package com.ideas.tetris.pacman.services.property.scorecard.dto;

import java.math.BigDecimal;
import java.util.Date;

public class PropertyScoreDTO {
    private Integer propertyId;
    private BigDecimal score;
    private Date generationDate;
    private Date startDate;
    private Date endDate;
    private Date futureScoreStartDate;
    private Date futureScoreEndDate;
    private BigDecimal futureScore;
    private Date earliestDecisionDate;

    public void setPropertyId(Integer propertyId) {
        this.propertyId = propertyId;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public void setGenerationDate(Date generationDate) {
        this.generationDate = generationDate;
    }

    public Integer getPropertyId() {
        return propertyId;
    }

    public BigDecimal getScore() {
        return score;
    }

    public Date getGenerationDate() {
        return generationDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEarliestDecisionDate(Date earliestDecisionDate) {
        this.earliestDecisionDate = earliestDecisionDate;
    }

    public Date getEarliestDecisionDate() {
        return earliestDecisionDate;
    }

    public Date getFutureScoreEndDate() {
        return futureScoreEndDate;
    }

    public void setFutureScoreEndDate(Date futureScoreEndDate) {
        this.futureScoreEndDate = futureScoreEndDate;
    }

    public Date getFutureScoreStartDate() {
        return futureScoreStartDate;
    }

    public BigDecimal getFutureScore() {
        return futureScore;
    }

    public void setFutureScore(BigDecimal futureScore) {
        this.futureScore = futureScore;
    }

    public void setFutureScoreStartDate(Date futureScoreStartDate) {
        this.futureScoreStartDate = futureScoreStartDate;
    }
}
