package com.ideas.tetris.pacman.services.security;

import com.ideas.infra.tetris.security.domain.Role;
import org.apache.log4j.Logger;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


@Component
@Transactional
public class MockRoleService {
    private static final Logger LOGGER = Logger.getLogger(MockRoleService.class);


    public void create(Role role, boolean isInternal) {
        if (!MockRoleTokenStore.isEnabled()) {
            LOGGER.debug("Creating mock roles is not enabled");
            return;
        }

        LOGGER.info("Creating mock role:" + role.getRoleName());
        MockRoleTokenStore.setMockRole(role);
    }

}
