package com.ideas.tetris.pacman.services.opera;

import com.google.common.collect.ImmutableMap;
import com.ideas.g3.integration.opera.dto.OperaDataLoadTypeCode;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Created by idnrbk on 4/28/2015.
 */
@OperaTransactionFeedValidationService.Qualifier
@Component
@Transactional
public class OperaTransactionFeedValidationService implements OperaFeedValidationService {

    private static final Logger LOGGER = Logger.getLogger(OperaTransactionFeedValidationService.class.getName());

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }

    @Autowired
    OperaUtilityService operaUtilityService;
    @TenantCrudServiceBean.Qualifier
	@Autowired
    @org.springframework.beans.factory.annotation.Qualifier("tenantCrudServiceBean")
    CrudService crudService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    public static final String QUERY_DUPLICATE_TRANSACTION_SUMMARY_RECORDS = "Select Reservation_Name_ID, Confirmation_number, Market_Code, Room_Type, Arrival_DT, " +
            "Departure_DT, Transaction_DT from opera.History_Transaction " +
            "where Data_Load_Metadata_ID in (:dlmIds) group by Reservation_Name_ID, Confirmation_number, Market_Code, Room_Type, Arrival_DT, Departure_DT, Transaction_DT " +
            "having count(*) > 1";

    public static final String QUERY_COUNT_TRANSACTION_MISSING_RES_ID = "Select count(*) from opera.History_Transaction " +
            "where Data_Load_Metadata_ID in (:dlmIds) AND Reservation_Name_ID = '' ";


    private static final String QUERY_TRANSACTION_MISSING_ROOM_TYPE = "Select Reservation_Name_ID from " +
            "opera.History_Transaction where Data_Load_Metadata_ID in (:dlmIds) and Reservation_Name_ID <> '' and Room_Type = '' ";

    private static final String QUERY_TRANSACTION_MISSING_ARRIVAL_DATE = "Select Reservation_Name_ID from " +
            "opera.History_Transaction where Data_Load_Metadata_ID in (:dlmIds) and Reservation_Name_ID <> '' and Arrival_DT = '' ";

    private static final String QUERY_TRANSACTION_MISSING_DEPARTURE_DATE = "Select Reservation_Name_ID from " +
            "opera.History_Transaction where Data_Load_Metadata_ID in (:dlmIds) and Reservation_Name_ID <> '' and Departure_DT = '' ";

    private static final String QUERY_TRANSACTION_MISSING_TRANSACTION_DATE = "Select Reservation_Name_ID from " +
            "opera.History_Transaction where Data_Load_Metadata_ID in (:dlmIds) and Reservation_Name_ID <> '' and Transaction_DT = '' ";

    public static final String PRIMARY_ATTRIBUTE = "Reservation ID";
    public static final String ENTITY = "Transaction";

    public static final String MSG_TRANSACTION_VALIDATION_FAILED = "Feed validation failed for " + ENTITY + ".\n";
    public static final String MSG_MISSING_PRIMARY_ATTR = ENTITY + " Record(s) have missing " + PRIMARY_ATTRIBUTE;
    public static final String MSG_DUPLICATE_RECORDS = "Duplicate Records (Reservation ID, Confirmation Number, Market Segment, Room Type, Arrival Date, Departure Date, Transaction Date)";
    public static final String MSG_DUPLICATE = " are present in " + ENTITY;
    public static final String MSG_INVALID_RT = " have invalid Room Type";
    public static final String MSG_INVALID_ARR_DT = " have invalid Arrival Date";
    public static final String MSG_INVALID_DEP_DT = " have invalid Departure Date";
    public static final String MSG_INVALID_TRANS_DT = " have invalid Transaction Date";

    private static Map<String, String> queryMap = ImmutableMap.<String, String>builder()
            .put(MSG_INVALID_RT, QUERY_TRANSACTION_MISSING_ROOM_TYPE)
            .put(MSG_INVALID_ARR_DT, QUERY_TRANSACTION_MISSING_ARRIVAL_DATE)
            .put(MSG_INVALID_DEP_DT, QUERY_TRANSACTION_MISSING_DEPARTURE_DATE)
            .put(MSG_INVALID_TRANS_DT, QUERY_TRANSACTION_MISSING_TRANSACTION_DATE)
            .build();

    private static Map<String, String> queryMapMissingCounts = ImmutableMap.<String, String>builder()
            .put(MSG_MISSING_PRIMARY_ATTR, QUERY_COUNT_TRANSACTION_MISSING_RES_ID)
            .build();


    protected static final String[] OPERA_DATALOAD_TYPE_CODE = new String[]{OperaDataLoadTypeCode.CTRANS.name(), OperaDataLoadTypeCode.PTRANS.name()};

    @Override
    public String[] getDataLoadTypes() {
        return OPERA_DATALOAD_TYPE_CODE;
    }

    @Override
    public String validateFeed(String correlationId, List<Integer> operaDataLoadTypeCodes) {
        boolean flagMissingPrimaryAttribute = isTransactionMissingPrimaryAttribute(operaDataLoadTypeCodes);
        boolean flagMissingRequiredFields = isTransactionMissingMandatoryFields(operaDataLoadTypeCodes);
        boolean flagDuplicateTransactions = isDuplicateTransactionPresent(operaDataLoadTypeCodes);

        if (flagMissingPrimaryAttribute || flagMissingRequiredFields || flagDuplicateTransactions) {
            return MSG_TRANSACTION_VALIDATION_FAILED;
        }
        return StringUtils.EMPTY;
    }

    private boolean isTransactionMissingPrimaryAttribute(List<Integer> operaDataLoadTypeCodes) {
        int result = 0;
        Set<String> keys = queryMapMissingCounts.keySet();
        for (String msg : keys) {
            List<Integer> queryResults = crudService.findByNativeQuery(queryMapMissingCounts.get(msg),
                    QueryParameter.with("dlmIds", operaDataLoadTypeCodes).parameters());
            Integer count = queryResults.get(0);
            if (count > 0) {
                LOGGER.error("OperaFeedValidationStep Failed: " + count + " " + msg);
                result++;
            }
        }
        return result > 0;
    }

    private boolean isTransactionMissingMandatoryFields(List<Integer> operaDataLoadTypeCodes) {
        boolean result = false;
        Set<String> keys = queryMap.keySet();
        for (String msg : keys) {
            if (StringUtils.equalsIgnoreCase(msg, MSG_INVALID_RT) && StringUtils.isNotBlank(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.MISSING_ROOM_TYPE_VALUE.value(Constants.OPERA)))) {
                continue;
            } else {
                List<String> queryResults = crudService.findByNativeQuery(queryMap.get(msg),
                        QueryParameter.with("dlmIds", operaDataLoadTypeCodes).parameters());
                result = logMissingDataError(queryResults, msg, PRIMARY_ATTRIBUTE) || result;
            }
        }
        return result;
    }

    private boolean isDuplicateTransactionPresent(List<Integer> operaDataLoadTypeCodes) {
        List<String> queryResults = crudService.findByNativeQuery(QUERY_DUPLICATE_TRANSACTION_SUMMARY_RECORDS,
                QueryParameter.with("dlmIds", operaDataLoadTypeCodes).parameters(), new RowMapper<String>() {
                    @Override
                    public String mapRow(Object[] row) {
                        return (new StringBuffer("{").append(row[0]).append(";").append(row[1]).append(";").append(row[2])
                                .append(";").append(row[3]).append(";").append(row[4]).append(";").append(row[5])
                                .append(";").append(row[6]).append("}")).toString();
                    }
                });
        return logMissingDataError(queryResults, MSG_DUPLICATE, MSG_DUPLICATE_RECORDS);
    }

    private boolean logMissingDataError(List<String> queryResults, String missingAttribute, String initialMessage) {
        if (!CollectionUtils.isEmpty(queryResults)) {
            LOGGER.error((new StringBuffer("OperaFeedValidationStep Failed: ").append(initialMessage).append(" : ").append(queryResults.toString()).append(missingAttribute)).toString());
            return true;
        }
        return false;
    }
}
