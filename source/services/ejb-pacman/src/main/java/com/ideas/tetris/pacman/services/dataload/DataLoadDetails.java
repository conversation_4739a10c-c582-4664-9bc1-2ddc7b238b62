package com.ideas.tetris.pacman.services.dataload;

import com.ideas.tetris.pacman.services.opera.OperaIncomingFile;
import com.ideas.tetris.pacman.services.opera.OperaIncomingFileType;
import com.ideas.tetris.pacman.services.opera.entity.DataLoadMetadata;
import org.joda.time.LocalDateTime;

public class DataLoadDetails {
    private LocalDateTime creationDate;
    private String fileDescription;
    private OperaIncomingFileType fileType;
    private String correlationId;

    public DataLoadDetails() {

    }

    public DataLoadDetails(DataLoadMetadata entity) {
        creationDate = entity.getCreateDate();
        //making sure that we fill in the fileDescription
        setFileType(OperaIncomingFile.getByFileTypeCode(entity.getIncomingFileTypeCode()));
        correlationId = entity.getCorrelationId();
    }

    public LocalDateTime getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(LocalDateTime creationDate) {
        this.creationDate = creationDate;
    }

    public OperaIncomingFileType getFileType() {
        return fileType;
    }

    public void setFileType(OperaIncomingFileType fileType) {
        this.fileType = fileType;
        if (fileType != null) {
            fileDescription = fileType.toString();
        }
    }

    public String getCorrelationId() {
        return correlationId;
    }

    public void setCorrelationId(String correlationId) {
        this.correlationId = correlationId;
    }

    public boolean isComplete() {
        return creationDate != null;
    }

    public String getFileDescription() {
        return fileDescription;
    }

    public void setFileDescription(String fileDescription) {
        this.fileDescription = fileDescription;
    }
}
