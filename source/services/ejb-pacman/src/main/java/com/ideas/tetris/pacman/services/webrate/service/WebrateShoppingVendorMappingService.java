package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.webrate.entity.Webrate;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateAccomType;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateChannel;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateSourceProperty;
import com.ideas.tetris.pacman.services.webrate.vo.WebrateAccomTypeVO;
import com.ideas.tetris.pacman.services.webrate.vo.WebrateChannelVO;
import com.ideas.tetris.pacman.services.webrate.vo.WebrateCompetitorsVO;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.LongAccumulator;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class WebrateShoppingVendorMappingService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;
    @Autowired
	protected WebrateShoppingCleanUpService webrateShoppingCleanUpService;
    @Autowired
	private SyncEventAggregatorService syncEventAggregatorService;

    public static final String ID_LIST = "idList";

    static final String PACE_WEBRATE_CHANNEL_COUNT_QUERY = "select count(*) from PACE_Webrate where Webrate_Source_Property_ID = :newWebrateSourcePropertyId and Webrate_Channel_ID =  :newWebrateChannelId";
    static final String PACE_WEBRATE_DIFFERENTIAL_CHANNEL_COUNT_QUERY = "select count(*) from PACE_Webrate_Differential where Webrate_Source_Property_ID = :newWebrateSourcePropertyId and Webrate_Channel_ID =  :newWebrateChannelId";
    static final String PACE_WEBRATE_CHANNEL_UPDATE_QUERY = "Update top(:chunkSize) PACE_Webrate set Webrate_Channel_ID = :oldWebrateChannelId where Webrate_Source_Property_ID = :newWebrateSourcePropertyId and Webrate_Channel_ID = :newWebrateChannelId";
    static final String PACE_WEBRATE_DIFFERENTIAL_CHANNEL_UPDATE_QUERY = "Update top(:chunkSize) PACE_Webrate_Differential set Webrate_Channel_ID = :oldWebrateChannelId where Webrate_Source_Property_ID = :newWebrateSourcePropertyId and Webrate_Channel_ID = :newWebrateChannelId";
    static final String PACE_WEBRATE_COMPETITOR_COUNT_QUERY = "select count(*) from PACE_Webrate where Webrate_Source_Property_ID = :newWebrateSourcePropertyId and Webrate_Competitors_ID =  :newWebrateCompetitorId";
    static final String PACE_WEBRATE_DIFFERENTIAL_COMPETITOR_COUNT_QUERY = "select count(*) from PACE_Webrate_Differential where Webrate_Source_Property_ID = :newWebrateSourcePropertyId and Webrate_Competitors_ID =  :newWebrateCompetitorId";
    static final String PACE_WEBRATE_COMPETITOR_UPDATE_QUERY = "Update top(:chunkSize) PACE_Webrate set Webrate_Competitors_ID = :oldWebrateCompetitorId where Webrate_Source_Property_ID = :newWebrateSourcePropertyId and Webrate_Competitors_ID = :newWebrateCompetitorId";
    static final String PACE_WEBRATE_DIFFERENTIAL_COMPETITOR_UPADATE_QUERY = "Update top(:chunkSize) PACE_Webrate_Differential set Webrate_Competitors_ID = :oldWebrateCompetitorId where Webrate_Source_Property_ID = :newWebrateSourcePropertyId and Webrate_Competitors_ID = :newWebrateCompetitorId";
    static final String PACE_WEBRATE_ACCOMTYPE_COUNT_QUERY = "select count(*) from PACE_Webrate where Webrate_Source_Property_ID = :newWebrateSourcePropertyId and Webrate_Accom_Type_ID =  :newWebrateAccomTypeId";
    static final String PACE_WEBRATE_DIFFERENTIAL_ACCOMTYPE_COUNT_QUERY = "select count(*) from PACE_Webrate_Differential where Webrate_Source_Property_ID = :newWebrateSourcePropertyId and Webrate_Accom_Type_ID =  :newWebrateAccomTypeId";
    static final String PACE_WEBRATE_ACCOMTYPE_UPDATE_QUERY = "Update top(:chunkSize) PACE_Webrate set Webrate_Accom_Type_ID = :oldWebrateAccomTypeId where Webrate_Source_Property_ID = :newWebrateSourcePropertyId and Webrate_Accom_Type_ID = :newWebrateAccomTypeId";
    static final String PACE_WEBRATE_DIFFERENTIAL_ACCOMTYPE_UPDATE_QUERY = "Update top(:chunkSize) PACE_Webrate_Differential set Webrate_Accom_Type_ID = :oldWebrateAccomTypeId where Webrate_Source_Property_ID = :newWebrateSourcePropertyId and Webrate_Accom_Type_ID = :newWebrateAccomTypeId";

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public void setWebrateShoppingCleanUpService(WebrateShoppingCleanUpService webrateShoppingCleanUpService) {
        this.webrateShoppingCleanUpService = webrateShoppingCleanUpService;
    }

    public List<WebrateAccomType> getAllActiveWebRateAccomTypes() {
        return tenantCrudService.findByNamedQuery(Webrate.ALL_ACTIVE_ACCOM_TYPES);
    }

    public List<WebrateAccomType> getInactiveWebRateAccomTypesOfMostRecentVendor() {
        return tenantCrudService.findByNamedQuery(Webrate.INACTIVE_ACCOM_TYPES_OF_MOST_RECENT_VENDOR);
    }

    public WebrateSourceProperty getActiveWebRateSourceProperty() {
        return (WebrateSourceProperty) tenantCrudService.findByNamedQuerySingleResult(WebrateSourceProperty.BY_PROPERTY_ID_AND_STATUS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("statusId", Constants.ACTIVE_STATUS_ID)
                        .parameters());
    }

    public WebrateSourceProperty getMostRecentInactivedWebRateSourceProperty() {
        return (WebrateSourceProperty) tenantCrudService.findByNamedQuerySingleResult(WebrateSourceProperty.GET_MOST_RECENT_INACTIVATED_RECORD,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .parameters());
    }

    public boolean mapWebrateAccomType(Map<WebrateAccomType, WebrateAccomType> webrateAccomTypeMap, WebrateSourceProperty newWebrateSourceProperty) {
        LongAccumulator totalModified = new LongAccumulator((x, y) -> x + y, 0L);
        cleanupWebrateAccomTypeDataAsynchronously(webrateAccomTypeMap, newWebrateSourceProperty, totalModified);
        return totalModified.get() != 0;
    }

    private void cleanupWebrateAccomTypeDataAsynchronously(Map<WebrateAccomType, WebrateAccomType> webrateAccomTypeMap, WebrateSourceProperty newWebrateSourceProperty, LongAccumulator totalModified) {
        List listOfAccomTypeToBeClean = new ArrayList();
        HashMap<WebrateAccomTypeVO, WebrateAccomTypeVO> webrateAccomTypesMapToUpdated = new HashMap<>();
        webrateAccomTypeMap.forEach((oldWebrateAccomType, newWebrateAccomType) -> {
                    if (null == newWebrateAccomType) {
                        listOfAccomTypeToBeClean.add(oldWebrateAccomType.getId().toString());
                        totalModified.accumulate(1);
                    } else if (!oldWebrateAccomType.getId().equals(newWebrateAccomType.getId())) {
                        listOfAccomTypeToBeClean.add(newWebrateAccomType.getId().toString());
                        tenantCrudService.flush();
                        webrateAccomTypesMapToUpdated.put(new WebrateAccomTypeVO(oldWebrateAccomType), new WebrateAccomTypeVO(newWebrateAccomType));
                        totalModified.accumulate(1);
                    }
                }
        );
        webrateShoppingCleanUpService.cleanupAndUpdateWebrateAccomTypesJob(listOfAccomTypeToBeClean, webrateAccomTypesMapToUpdated, newWebrateSourceProperty.getId());
    }

    public boolean mapWebrateCompetitors(Map<WebrateCompetitors, WebrateCompetitors> webrateCompetitorsMap, WebrateSourceProperty newWebrateSourceProperty) {
        LongAccumulator totalModified = new LongAccumulator((x, y) -> x + y, 0L);


        cleanupWebrateCompetitorsDataAsynchronously(webrateCompetitorsMap, newWebrateSourceProperty, totalModified);

        return totalModified.get() != 0;
    }

    private void cleanupWebrateCompetitorsDataAsynchronously(Map<WebrateCompetitors, WebrateCompetitors> webrateCompetitorsMap, WebrateSourceProperty newWebrateSourceProperty, LongAccumulator totalModified) {
        HashMap<WebrateCompetitorsVO, WebrateCompetitorsVO> webrateCompetitorsMapToUpdated = new HashMap<>();
        List listOfCompetitorIdsToBeClean = new ArrayList();
        webrateCompetitorsMap.forEach((oldWebrateCompetitors, newWebrateCompetitors) -> {
                    if (null == newWebrateCompetitors) {
                        listOfCompetitorIdsToBeClean.add(oldWebrateCompetitors.getId().toString());
                        totalModified.accumulate(1);
                    } else if (!oldWebrateCompetitors.getId().equals(newWebrateCompetitors.getId())) {
                        listOfCompetitorIdsToBeClean.add(newWebrateCompetitors.getId().toString());
                        webrateCompetitorsMapToUpdated.put(new WebrateCompetitorsVO(oldWebrateCompetitors), new WebrateCompetitorsVO(newWebrateCompetitors));
                        tenantCrudService.flush();
                        totalModified.accumulate(1);
                    }
                }
        );
        webrateShoppingCleanUpService.cleanupAndUpdateWebrateCompetitorsJob(listOfCompetitorIdsToBeClean, webrateCompetitorsMapToUpdated, newWebrateSourceProperty.getId());
    }


    public Map<WebrateCompetitors, WebrateCompetitorsVO> fetchWebRateCompetitorsDataByIds(Map<WebrateCompetitorsVO, WebrateCompetitorsVO> webRateIds) {
        Map<WebrateCompetitors, WebrateCompetitorsVO> webrateCompetitorsMap = new HashMap<>();
        List<Integer> parameters = webRateIds.keySet().stream().map(WebrateCompetitorsVO::getWebRateCompetitorsId).collect(Collectors.toList());
        List<WebrateCompetitors> webrateCompetitorsList = tenantCrudService.findByNamedQuery(WebrateCompetitors.BY_IDS, MapBuilder.with(ID_LIST, parameters).get());
        Map<Integer, WebrateCompetitors> webrateCompetitorsMap1 = webrateCompetitorsList.stream().collect(Collectors.toMap(WebrateCompetitors::getId, t -> t));
        webRateIds.forEach((oldWebRate, newWebRate) -> {
            WebrateCompetitors oldWebrateCompetitors = webrateCompetitorsMap1.get(oldWebRate.getWebRateCompetitorsId());
            webrateCompetitorsMap.put(oldWebrateCompetitors, newWebRate);
        });
        return webrateCompetitorsMap;
    }

    public Map<WebrateChannel, WebrateChannelVO> fetchWebRateChannelDetailsByIds(Map<WebrateChannelVO, WebrateChannelVO> webRateMapObject) {
        Map<WebrateChannel, WebrateChannelVO> webrateChannelHashMap = new HashMap<>();
        List<Integer> parameters = webRateMapObject.keySet().stream().map(WebrateChannelVO::getWebRateChannelId).collect(Collectors.toList());
        List<WebrateChannel> webrateChannelList = tenantCrudService.findByNamedQuery(WebrateChannel.BY_IDS, MapBuilder.with(ID_LIST, parameters).get());
        Map<Integer, WebrateChannel> webrateChannelHashMap1 = webrateChannelList.stream().collect(Collectors.toMap(WebrateChannel::getId, t -> t));
        webRateMapObject.forEach((oldWebRateId, newWebRateId) -> {
            WebrateChannel oldWebrateChannels = webrateChannelHashMap1.get(oldWebRateId.getWebRateChannelId());
            webrateChannelHashMap.put(oldWebrateChannels, newWebRateId);
        });
        return webrateChannelHashMap;
    }

    public Map<WebrateAccomType, WebrateAccomTypeVO> fetchWebRateAccomTypeByIds(Map<WebrateAccomTypeVO, WebrateAccomTypeVO> webRateMapObject) {
        Map<WebrateAccomType, WebrateAccomTypeVO> webrateAccomTypeHashMap = new HashMap<>();
        List<Integer> parameters = webRateMapObject.keySet().stream().map(WebrateAccomTypeVO::getWebRateAccomTypeId).collect(Collectors.toList());
        List<WebrateAccomType> webrateAccomTypesList = tenantCrudService.findByNamedQuery(WebrateAccomType.BY_IDS, MapBuilder.with(ID_LIST, parameters).get());
        Map<Integer, WebrateAccomType> webrateAccomTypeMap = webrateAccomTypesList.stream().collect(Collectors.toMap(WebrateAccomType::getId, t -> t));
        webRateMapObject.forEach((oldWebRateId, newWebRateId) -> {
            WebrateAccomType webrateAccomType = webrateAccomTypeMap.get(oldWebRateId.getWebRateAccomTypeId());
            webrateAccomTypeHashMap.put(webrateAccomType, newWebRateId);
        });
        return webrateAccomTypeHashMap;
    }

    public void updateOldWebrateAccomTypeWithNewWebrateAccomTypeDetails(WebrateAccomType oldWebrateAccomType, WebrateAccomTypeVO newWebrateAccomType) {
        oldWebrateAccomType.setWebrateAccomName(newWebrateAccomType.getWebrateAccomName());
        oldWebrateAccomType.setWebrateAccomAlias(newWebrateAccomType.getWebrateAccomAlias());
        oldWebrateAccomType.setShouldDelete(newWebrateAccomType.getShouldDelete());
        oldWebrateAccomType.setLastUpdatedByUserId(newWebrateAccomType.getLastUpdatedByUserId());
        oldWebrateAccomType.setLastUpdatedDate(newWebrateAccomType.getLastUpdatedDate());

        tenantCrudService.save(oldWebrateAccomType);
    }

    public WebrateAccomType updateOldWebrateAccomTypeWithNewWebrateAccomTypeDetails(WebrateAccomType oldWebrateAccomType, WebrateAccomType newWebrateAccomType) {
        oldWebrateAccomType.setWebrateAccomName(newWebrateAccomType.getWebrateAccomName());
        oldWebrateAccomType.setWebrateAccomAlias(newWebrateAccomType.getWebrateAccomAlias());
        oldWebrateAccomType.setShouldDelete(newWebrateAccomType.getShouldDelete());
        oldWebrateAccomType.setLastUpdatedByUserId(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        oldWebrateAccomType.setLastUpdatedDate(newWebrateAccomType.getLastUpdatedDate());

        tenantCrudService.save(oldWebrateAccomType);
        return newWebrateAccomType;
    }


    public List<WebrateCompetitors> getAllActiveWebRateCompetitors() {
        return tenantCrudService.findByNamedQuery(Webrate.ALL_ACTIVE_COMPETITORS);
    }

    public List<WebrateCompetitors> getInactiveWebRateCompetitorsOfMostRecentVendor() {
        return tenantCrudService.findByNamedQuery(Webrate.INACTIVE_COMPETITORS_OF_MOST_RECENT_VENDOR);
    }

    private void updateWebrateData(WebrateSourceProperty newWebrateSourceProperty, WebrateAccomType oldWebrateAccomType, WebrateAccomType newWebrateAccomType) {
        updateWebRateAccomTypeData(newWebrateSourceProperty.getId(), oldWebrateAccomType.getId(), newWebrateAccomType.getId());
    }

    public int updateWebRateAccomTypeData(Integer newWebrateSourcePropertyId, Integer oldWebrateAccomTypeId, Integer newWebrateAccomTypeId) {
        return tenantCrudService.executeUpdateByNativeQuery("Update Webrate set Webrate_Accom_Type_ID = " + oldWebrateAccomTypeId
                + " where Webrate_Source_Property_ID = " + newWebrateSourcePropertyId
                + " and Webrate_Accom_Type_ID = " + newWebrateAccomTypeId
        );
    }

    public void updateOldWebrateCompetitorsWithNewWebrateCompetitorsDetails(WebrateCompetitors oldWebrateCompetitors, WebrateCompetitorsVO webrateCompetitorsVO) {
        oldWebrateCompetitors.setWebrateHotelID(webrateCompetitorsVO.getWebrateHotelId());
        oldWebrateCompetitors.setWebrateCompetitorsName(webrateCompetitorsVO.getWebrateCompetitorsName());
        oldWebrateCompetitors.setWebrateCompetitorsAlias(webrateCompetitorsVO.getWebrateCompetitorsAlias());
        oldWebrateCompetitors.setWebrateCompetitorsDescription(webrateCompetitorsVO.getWebrateCompetitorsDescription());
        oldWebrateCompetitors.setShouldDelete(webrateCompetitorsVO.getShouldDelete());
        oldWebrateCompetitors.setLastUpdatedByUserId(webrateCompetitorsVO.getLastUpdatedByUserId());
        oldWebrateCompetitors.setLastUpdatedDate(webrateCompetitorsVO.getLastUpdatedDate());

        tenantCrudService.save(oldWebrateCompetitors);
    }

    public void updateOldWebrateCompetitorsWithNewWebrateCompetitorsDetails(WebrateCompetitors oldWebrateCompetitors, WebrateCompetitors newWebrateCompetitors) {
        oldWebrateCompetitors.setWebrateHotelID(newWebrateCompetitors.getWebrateHotelID());
        oldWebrateCompetitors.setWebrateCompetitorsName(newWebrateCompetitors.getWebrateCompetitorsName());
        oldWebrateCompetitors.setWebrateCompetitorsAlias(newWebrateCompetitors.getWebrateCompetitorsAlias());
        oldWebrateCompetitors.setWebrateCompetitorsDescription(newWebrateCompetitors.getWebrateCompetitorsDescription());
        oldWebrateCompetitors.setShouldDelete(newWebrateCompetitors.getShouldDelete());
        oldWebrateCompetitors.setLastUpdatedByUserId(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        oldWebrateCompetitors.setLastUpdatedDate(newWebrateCompetitors.getLastUpdatedDate());

        tenantCrudService.save(oldWebrateCompetitors);
    }


    public int updateWebRateCompetitorData(Integer newWebrateSourceProperty, Integer oldWebrateCompetitorsId, Integer newWebrateCompetitorsId) {
        return tenantCrudService.executeUpdateByNativeQuery("Update Webrate set Webrate_Competitors_ID = " + oldWebrateCompetitorsId
                + " where Webrate_Source_Property_ID = " + newWebrateSourceProperty
                + " and Webrate_Competitors_ID = " + newWebrateCompetitorsId
        );
    }

    public List<WebrateChannel> getAllActiveWebRateChannels() {
        return tenantCrudService.findByNamedQuery(Webrate.ALL_ACTIVE_CHANNELS);
    }

    public List<WebrateChannel> getInactiveWebRateChannelsOfMostRecentVendor() {
        return tenantCrudService.findByNamedQuery(Webrate.INACTIVE_CHANNELS_OF_MOST_RECENT_VENDOR);
    }

    public boolean mapWebrateChannels(Map<WebrateChannel, WebrateChannel> webrateChannelMap, WebrateSourceProperty newWebrateSourceProperty) {
        LongAccumulator totalModified = new LongAccumulator((x, y) -> x + y, 0L);

        cleanupWebrateChannelsDataAsynchronously(webrateChannelMap, newWebrateSourceProperty, totalModified);

        return totalModified.get() != 0;
    }

    private void cleanupWebrateChannelsDataAsynchronously(Map<WebrateChannel, WebrateChannel> webrateChannelMap, WebrateSourceProperty newWebrateSourceProperty, LongAccumulator totalModified) {
        List listOfChannelIdsToBeClean = new ArrayList();
        HashMap<WebrateChannelVO, WebrateChannelVO> webrateChannelsMapToUpdated = new HashMap<>();
        webrateChannelMap.forEach((oldWebrateChannel, newWebrateChannel) -> {
                    if (null == newWebrateChannel) {
                        listOfChannelIdsToBeClean.add(oldWebrateChannel.getId().toString());
                        totalModified.accumulate(1);
                    } else if (!oldWebrateChannel.getId().equals(newWebrateChannel.getId())) {
                        listOfChannelIdsToBeClean.add(newWebrateChannel.getId().toString());
                        webrateChannelsMapToUpdated.put(new WebrateChannelVO(oldWebrateChannel), new WebrateChannelVO(newWebrateChannel));
                        tenantCrudService.flush();
                        totalModified.accumulate(1);
                    }
                }
        );
        webrateShoppingCleanUpService.cleanupAndUpdateWebrateChannelsJob(listOfChannelIdsToBeClean, webrateChannelsMapToUpdated, newWebrateSourceProperty.getId());

    }


    public void updateOldWebrateChannelWithNewWebrateChannelDetails(WebrateChannel oldWebrateChannel, WebrateChannelVO newWebrateChannel) {
        oldWebrateChannel.setWebrateChannelName(newWebrateChannel.getWebrateChannelName());
        oldWebrateChannel.setWebrateChannelAlias(newWebrateChannel.getWebrateChannelAlias());
        oldWebrateChannel.setWebrateChannelDescription(newWebrateChannel.getWebrateChannelDescription());
        oldWebrateChannel.setShouldDelete(newWebrateChannel.getShouldDelete());
        oldWebrateChannel.setLastUpdatedByUserId(newWebrateChannel.getLastUpdatedByUserId());
        oldWebrateChannel.setLastUpdatedDate(newWebrateChannel.getLastUpdatedDate());
        oldWebrateChannel.setFileChannelID(newWebrateChannel.getFileChannelId());
        tenantCrudService.save(oldWebrateChannel);
    }

    public void updateOldWebrateChannelWithNewWebrateChannelDetails(WebrateChannel oldWebrateChannel, WebrateChannel newWebrateChannel) {
        oldWebrateChannel.setWebrateChannelName(newWebrateChannel.getWebrateChannelName());
        oldWebrateChannel.setWebrateChannelAlias(newWebrateChannel.getWebrateChannelAlias());
        oldWebrateChannel.setWebrateChannelDescription(newWebrateChannel.getWebrateChannelDescription());
        oldWebrateChannel.setShouldDelete(newWebrateChannel.getShouldDelete());
        oldWebrateChannel.setLastUpdatedByUserId(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        oldWebrateChannel.setLastUpdatedDate(newWebrateChannel.getLastUpdatedDate());
        oldWebrateChannel.setFileChannelID(newWebrateChannel.getFileChannelID());

        tenantCrudService.save(oldWebrateChannel);
    }

    private void updatePaceWebrateData(WebrateSourceProperty newWebrateSourceProperty, WebrateChannel oldWebrateChannel, WebrateChannel newWebrateChannel) {
        tenantCrudService.executeUpdateByNativeQuery("Update PACE_Webrate set Webrate_Channel_ID = " + oldWebrateChannel.getId()
                + " where Webrate_Source_Property_ID = " + newWebrateSourceProperty.getId()
                + " and Webrate_Channel_ID = " + newWebrateChannel.getId()
        );
    }

    private void updateWebrateData(WebrateSourceProperty newWebrateSourceProperty, WebrateChannel oldWebrateChannel, WebrateChannel newWebrateChannel) {
        updateWebRateChannelData(newWebrateSourceProperty.getId(), oldWebrateChannel.getId(), newWebrateChannel.getId());
    }

    public int updateWebRateChannelData(Integer newWebrateSourcePropertyId, Integer oldWebrateChannelId, Integer newWebrateChannelId) {
        return tenantCrudService.executeUpdateByNativeQuery("Update Webrate set Webrate_Channel_ID = " + oldWebrateChannelId
                + " where Webrate_Source_Property_ID = " + newWebrateSourcePropertyId
                + " and Webrate_Channel_ID = " + newWebrateChannelId
        );
    }

    public void clearAndRegisterSyncEvent() {
        // If there was a webrate config change that a sync isn't required for, it needs to be removed
        syncEventAggregatorService.clearSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);

        // Register Sync Event
        syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED);
    }

    public void updateOldWebratesDataWithNewWebrateSource(WebrateSourceProperty oldWebrateSourceProperty, WebrateSourceProperty newWebrateSourceProperty) {
        webrateShoppingCleanUpService.startWebrateSourceUpdateJob(oldWebrateSourceProperty, newWebrateSourceProperty);
    }

    public int updateWebrateSource(Integer oldWebrateSourcePropertyId, Integer newWebrateSourcePropertyId) {
        return tenantCrudService.executeUpdateByNativeQuery("Update Webrate set Webrate_Source_Property_ID = " + newWebrateSourcePropertyId
                + " where Webrate_Source_Property_ID = " + oldWebrateSourcePropertyId);
    }

    public Integer getTotalNumberOfRecordsFromPaceWebrateForChannelToUpdate(Integer newWebrateSourcePropertyId, Integer newWebrateChannelId) {
        return executeQueryToGetTotalNumberOfPaceWebrateRecords(newWebrateSourcePropertyId, newWebrateChannelId, PACE_WEBRATE_CHANNEL_COUNT_QUERY, "newWebrateChannelId");
    }

    public Integer getTotalNumberOfRecordsFromPaceWebrateDifferentialForChannelToUpdate(Integer newWebrateSourcePropertyId, Integer newWebrateChannelId) {
        return executeQueryToGetTotalNumberOfPaceWebrateRecords(newWebrateSourcePropertyId, newWebrateChannelId, PACE_WEBRATE_DIFFERENTIAL_CHANNEL_COUNT_QUERY, "newWebrateChannelId");
    }

    public Integer updatePaceWebrateChannelDataInChunk(Integer chunkSize, Integer newSourcePropertyId, Integer oldWebrateChannelId, Integer newWebrateChannelId) {
        return executeQueryToUpdatePaceWebrateData(chunkSize, newSourcePropertyId, oldWebrateChannelId, newWebrateChannelId, PACE_WEBRATE_CHANNEL_UPDATE_QUERY, "oldWebrateChannelId", "newWebrateChannelId");
    }

    public Integer updatePaceWebrateDifferentialChannelDataInChunk(Integer chunkSize, Integer newSourcePropertyId, Integer oldWebrateChannelId, Integer newWebrateChannelId) {
        return executeQueryToUpdatePaceWebrateData(chunkSize, newSourcePropertyId, oldWebrateChannelId, newWebrateChannelId, PACE_WEBRATE_DIFFERENTIAL_CHANNEL_UPDATE_QUERY, "oldWebrateChannelId", "newWebrateChannelId");
    }

    public Integer getTotalNumberOfRecordsFromPaceWebrateForCompetitorToUpdate(Integer newWebrateSourcePropertyId, Integer newWebrateCompetitorId) {
        return executeQueryToGetTotalNumberOfPaceWebrateRecords(newWebrateSourcePropertyId, newWebrateCompetitorId, PACE_WEBRATE_COMPETITOR_COUNT_QUERY, "newWebrateCompetitorId");
    }

    public Integer getTotalNumberOfRecordsFromPaceWebrateDifferentialForCompetitorToUpdate(Integer newWebrateSourcePropertyId, Integer newWebrateCompetitorId) {
        return executeQueryToGetTotalNumberOfPaceWebrateRecords(newWebrateSourcePropertyId, newWebrateCompetitorId, PACE_WEBRATE_DIFFERENTIAL_COMPETITOR_COUNT_QUERY, "newWebrateCompetitorId");
    }

    public Integer updatePaceWebrateCompetitorDataInChunk(Integer chunkSize, Integer newSourcePropertyId, Integer oldWebrateCompetitorId, Integer newWebrateCompetitorId) {
        return executeQueryToUpdatePaceWebrateData(chunkSize, newSourcePropertyId, oldWebrateCompetitorId, newWebrateCompetitorId, PACE_WEBRATE_COMPETITOR_UPDATE_QUERY, "oldWebrateCompetitorId", "newWebrateCompetitorId");
    }

    public Integer updatePaceWebrateDifferentialCompetitorDataInChunk(Integer chunkSize, Integer newSourcePropertyId, Integer oldWebrateCompetitorId, Integer newWebrateCompetitorId) {
        return executeQueryToUpdatePaceWebrateData(chunkSize, newSourcePropertyId, oldWebrateCompetitorId, newWebrateCompetitorId, PACE_WEBRATE_DIFFERENTIAL_COMPETITOR_UPADATE_QUERY, "oldWebrateCompetitorId", "newWebrateCompetitorId");
    }

    public Integer getTotalNumberOfRecordsFromPaceWebrateForAccomTypeToUpdate(Integer newWebrateSourcePropertyId, Integer newWebrateAccomTypeId) {
        return executeQueryToGetTotalNumberOfPaceWebrateRecords(newWebrateSourcePropertyId, newWebrateAccomTypeId, PACE_WEBRATE_ACCOMTYPE_COUNT_QUERY, "newWebrateAccomTypeId");
    }

    public Integer getTotalNumberOfRecordsFromPaceWebrateDifferentialForAccomTypeToUpdate(Integer newWebrateSourcePropertyId, Integer newWebrateAccomTypeId) {
        return executeQueryToGetTotalNumberOfPaceWebrateRecords(newWebrateSourcePropertyId, newWebrateAccomTypeId, PACE_WEBRATE_DIFFERENTIAL_ACCOMTYPE_COUNT_QUERY, "newWebrateAccomTypeId");
    }

    public Integer executeQueryToGetTotalNumberOfPaceWebrateRecords(Integer newWebrateSourcePropertyId, Integer newId, String countQuery, String newIdField) {
        return tenantCrudService.findByNativeQuerySingleResult(countQuery, MapBuilder.with("newWebrateSourcePropertyId", newWebrateSourcePropertyId).and(newIdField, newId).get());
    }

    public Integer updatePaceWebrateDifferentialAccomTypeDataInChunk(Integer chunkSize, Integer newSourcePropertyId, Integer oldWebrateAccomTypeId, Integer newWebrateAccomTypeId) {
        return executeQueryToUpdatePaceWebrateData(chunkSize, newSourcePropertyId, oldWebrateAccomTypeId, newWebrateAccomTypeId, PACE_WEBRATE_DIFFERENTIAL_ACCOMTYPE_UPDATE_QUERY, "oldWebrateAccomTypeId", "newWebrateAccomTypeId");
    }

    public Integer updatePaceWebrateAccomTypeDataInChunk(Integer chunkSize, Integer newSourcePropertyId, Integer oldWebrateAccomTypeId, Integer newWebrateAccomTypeId) {
        return executeQueryToUpdatePaceWebrateData(chunkSize, newSourcePropertyId, oldWebrateAccomTypeId, newWebrateAccomTypeId, PACE_WEBRATE_ACCOMTYPE_UPDATE_QUERY, "oldWebrateAccomTypeId", "newWebrateAccomTypeId");
    }

    public Integer executeQueryToUpdatePaceWebrateData(Integer chunkSize, Integer newSourcePropertyId, Integer oldId, Integer newId, String updateQuery, String oldIdField, String newIdField) {
        return tenantCrudService.executeUpdateByNativeQuery(updateQuery, MapBuilder.with("chunkSize", chunkSize).and(oldIdField, oldId)
                .and("newWebrateSourcePropertyId", newSourcePropertyId).and(newIdField, newId).get());
    }
}
