package com.ideas.tetris.pacman.services.webrate.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class WebrateDefaultChannelDto {
    private Integer webrateDefaultChannelId;
    private Integer mondayChannelId;
    private Integer tuesdayChannelId;
    private Integer wednesdayChannelId;
    private Integer thursdayChannelId;
    private Integer fridayChannelId;
    private Integer saturdayChannelId;
    private Integer sundayChannelId;
    private Integer productId;
    private Integer propertyId;
    private LocalDateTime createdDttm;
    private Integer createdByUserId;
    private Integer lastUpdatedByUserId;
    private LocalDateTime lastUpdatedDttm;
}

