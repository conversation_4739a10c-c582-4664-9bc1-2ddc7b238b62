package com.ideas.tetris.pacman.services.analytics.services;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.roa.processgroup.entity.ProcessGroupConfig;
import com.ideas.tetris.pacman.services.roa.processgroup.entity.ProcessGroupConfigurationDetails;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IpcfgProcessGroupService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    private CrudService tenantCrudService;

    public List<ProcessGroupConfigurationDetails> getProcessGroupConfigurationDetails(){
        return tenantCrudService.findByNamedQuery(ProcessGroupConfig.GET_PROCESS_GROUP_CONFIGURATION_DETAILS);
    }
}
