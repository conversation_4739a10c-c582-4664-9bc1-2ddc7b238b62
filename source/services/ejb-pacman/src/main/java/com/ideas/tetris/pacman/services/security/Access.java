package com.ideas.tetris.pacman.services.security;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;


// An enum representing Access rights
public enum Access {
    READONLY("readOnly"),
    READWRITE("readWrite");

    private static final Logger LOGGER = Logger.getLogger(Access.class);
    private final String accessCode;

    public String getAccessCode() {
        return accessCode;
    }

    Access(String accessCodeIn) {
        this.accessCode = accessCodeIn;
    }

    public static Access getAccessFromCode(String accessCodeIn) {
        Access accessRetVal = null;

        for (Access access : Access.values()) {
            if (StringUtils.equalsIgnoreCase(access.getAccessCode(), accessCodeIn)) {
                accessRetVal = access;
            }
        }
        if (accessRetVal == null) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Unable to translate accessCode of: " + accessCodeIn);
            }
        }
        return accessRetVal;
    }

    @Override
    public String toString() {
        return this.getAccessCode();
    }
}
