package com.ideas.tetris.pacman.services.decision;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.PaceOverbookingAccom;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.PaceOverbookingAccomUpload;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValueAccomType;
import com.ideas.tetris.pacman.services.fplos.entity.DecisionByRankFPLOS;
import com.ideas.tetris.pacman.services.fplos.entity.DecisionQualifiedFPLOS;
import com.ideas.tetris.pacman.services.fplos.entity.PaceDecisionByRankFPLOS;
import com.ideas.tetris.pacman.services.fplos.entity.PaceDecisionQualifiedFPLOS;
import com.ideas.tetris.pacman.services.hospitalityrooms.service.HospitalityRoomsService;
import com.ideas.tetris.pacman.services.lra.entity.DecisionLRAFPLOS;
import com.ideas.tetris.pacman.services.lra.entity.DecisionLRAMINLOS;
import com.ideas.tetris.pacman.services.lra.entity.PaceDecisionLRAFPLOS;
import com.ideas.tetris.pacman.services.lra.entity.PaceDecisionLRAMINLOS;
import com.ideas.tetris.pacman.services.minlos.entity.MinlosDecisions;
import com.ideas.tetris.pacman.services.overbooking.entity.DecisionOvrbkAccom;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class DeleteDecisionForZeroCapacityAccomTypesService {
    public static final String AND_DECISION_ID_MAX_DECISION_ID = " and Decision_ID = :maxDecisionId";
    private static final Logger LOGGER = Logger.getLogger(DeleteDecisionForZeroCapacityAccomTypesService.class);
    private static final String MAX_DECISION_ID = "maxDecisionId";
    private static final String LOG_MESSAGE_TEMPLATE = "Deleted zero capacity accom_types from %s = %d \n";
    private static final String GET_MAX_DECISION_ID_PACE_LRV_AT = "select MAX(decision_id) from PACE_LRV_AT";
    private static final String DELETE_PACE_LRV_AT_FOR_ZERO_CAPACITY_ACCOM = "Delete pla from PACE_LRV_AT pla " +
            " inner join Accom_Type as at on at.Accom_Type_ID = pla.Accom_Type_ID where pla.Occupancy_DT >= :caughtUpDate and at.Accom_Type_Capacity = 0 and at.accom_type_code in :accomTypeCodes " +
            AND_DECISION_ID_MAX_DECISION_ID;
    private static final String GET_MAX_DECISION_ID_PACE_FPLOS_BY_HIERARCHY = "select MAX(decision_id) from PACE_FPLOS_By_Hierarchy";
    private static final String DELETE_DECISION_FOR_DECISION_FPLOS_BY_HIERARCHY = "Delete dfbh from Decision_FPLOS_By_Hierarchy dfbh " +
            " inner join Accom_Type as at on at.Accom_Type_ID = dfbh.Accom_Type_ID where dfbh.Arrival_DT >= :caughtUpDate and at.Accom_Type_Capacity = 0 and at.accom_type_code in :accomTypeCodes";
    private static final String DELETE_DECISION_FOR_PACE_FPLOS_BY_HIERARCHY = "Delete pfbh from PACE_FPLOS_By_Hierarchy pfbh " +
            " inner join Accom_Type as at on at.Accom_Type_ID = pfbh.Accom_Type_ID where pfbh.Arrival_DT >= :caughtUpDate and at.Accom_Type_Capacity = 0 and at.accom_type_code in :accomTypeCodes " +
            AND_DECISION_ID_MAX_DECISION_ID;
    private static final String GET_MAX_DECISION_ID_PACE_MINLOS = "select MAX(decision_id) from PACE_MINLOS";
    private static final String DELETE_PACE_MINLOS_FOR_ZERO_CAPACITY_ACCOM = "Delete pmin from PACE_MINLOS pmin " +
            " inner join Accom_Type as at on at.Accom_Type_ID = pmin.Accom_Type_ID where pmin.Arrival_DT >= :caughtUpDate and at.Accom_Type_Capacity = 0 and at.accom_type_code in :accomTypeCodes " +
            AND_DECISION_ID_MAX_DECISION_ID;
    private static final String GET_MAX_DECISION_ID_PACE_FPLOS_BY_ROOM_TYPE = "select MAX(decision_id) from Pace_FPLOS_By_RoomType";
    private static final String DELETE_DECISION_FOR_DECISION_FPLOS_BY_ROOM_TYPE = "Delete dfbh from Decision_FPLOS_By_RoomType dfbh " +
            " inner join Accom_Type as at on at.Accom_Type_ID = dfbh.Accom_Type_ID where dfbh.Arrival_DT >= :caughtUpDate and at.Accom_Type_Capacity = 0 and at.accom_type_code in :accomTypeCodes";
    private static final String DELETE_DECISION_FOR_PACE_FPLOS_BY_ROOM_TYPE = "Delete pfbh from Pace_FPLOS_By_RoomType pfbh " +
            " inner join Accom_Type as at on at.Accom_Type_ID = pfbh.Accom_Type_ID where pfbh.Arrival_DT >= :caughtUpDate and at.Accom_Type_Capacity = 0 and at.accom_type_code in :accomTypeCodes " +
            AND_DECISION_ID_MAX_DECISION_ID;
    public static final String ACCOM_TYPE_CODES = "accomTypeCodes";
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;
    @Autowired
	private DateService dateService;
    @Autowired
	private HospitalityRoomsService hospitalityRoomsService;


    public String deleteDecisionsFor(List<String> accomTypeCodes) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("caughtUpDate", new LocalDate(dateService.getCaughtUpDate()));
        parameters.put(ACCOM_TYPE_CODES, accomTypeCodes);

        StringBuilder deletedDecisionsInfo = new StringBuilder();

        deletedDecisionsInfo.append(deleteDecisionsForQualifiedFplos(parameters));
        deletedDecisionsInfo.append(deleteDecisionsForOverBookingAccom(parameters));
        deletedDecisionsInfo.append(deleteDecisionsForBarFplosByRank(parameters));
        deletedDecisionsInfo.append(deleteDecisionsForLRVAtAccomType(parameters));
        deletedDecisionsInfo.append(deleteDecisionsForLRAFPLOS(parameters));
        deletedDecisionsInfo.append(deleteDecisionsForLRAMinLOS(parameters));
        deletedDecisionsInfo.append(deleteDecisionsForBarFplosByHierarchy(parameters));
        deletedDecisionsInfo.append(deleteDecisionsForMinMaxLOS(parameters));
        deletedDecisionsInfo.append(deleteDecisionForBarFplosByRoomType(parameters));

        return deletedDecisionsInfo.toString();
    }

    private String deleteDecisionsForQualifiedFplos(Map<String, Object> parameters) {
        StringBuilder deletedDecisionsInfo = new StringBuilder();
        int count = tenantCrudService.executeUpdateByNamedQuery(DecisionQualifiedFPLOS.DELETE_DECISION_FOR_DECISION_QUALIFIED_FPLOS, parameters);
        logMessage(deletedDecisionsInfo, "Decision_Qualified_FPLOS", count);
        BigInteger maxDecisionId = tenantCrudService.findByNamedQuerySingleResult(PaceDecisionQualifiedFPLOS.GET_MAX_DECISION_ID);
        if (maxDecisionId != null) {
            parameters.put(MAX_DECISION_ID, maxDecisionId);
            count = tenantCrudService.executeUpdateByNamedQuery(PaceDecisionQualifiedFPLOS.DELETE_DECISION_FOR_PACE_QUALIFIED_FPLOS, parameters);
            logMessage(deletedDecisionsInfo, "PACE_Qualified_FPLOS", count);
        }
        return deletedDecisionsInfo.toString();
    }

    private String deleteDecisionsForOverBookingAccom(Map<String, Object> commonParameters) {
        Map<String, Object> parameters = new HashMap<>(commonParameters);
        parameters.remove(MAX_DECISION_ID);
        parameters.remove(ACCOM_TYPE_CODES);
        StringBuilder deletedDecisionsInfo = new StringBuilder();
        int count = tenantCrudService.executeUpdateByNamedQuery(DecisionOvrbkAccom.DELETE_DECISION_FOR_DECISION_OVERBOOKING_ACCOM, parameters);
        logMessage(deletedDecisionsInfo, "Decision_Ovrbk_Accom", count);
        BigInteger maxDecisionId = tenantCrudService.findByNamedQuerySingleResult(PaceOverbookingAccomUpload.GET_MAX_DECISION_ID);
        if (maxDecisionId != null) {
            parameters.put(MAX_DECISION_ID, maxDecisionId);
            count = tenantCrudService.executeUpdateByNamedQuery(PaceOverbookingAccom.DELETE_DECISION_FOR_PACE_OVERBOOKING_ACCOM, parameters);
            logMessage(deletedDecisionsInfo, "Pace_Ovrbk_Accom", count);
            count = tenantCrudService.executeUpdateByNamedQuery(PaceOverbookingAccomUpload.DELETE_DECISION_FOR_PACE_OVERBOOKING_ACCOM_UPLOAD, parameters);
            logMessage(deletedDecisionsInfo, "PACE_Ovrbk_Accom_Upload", count);
        }
        return deletedDecisionsInfo.toString();
    }

    private String deleteDecisionsForBarFplosByRank(Map<String, Object> parameters) {
        StringBuilder deletedDecisionsInfo = new StringBuilder();
        parameters.remove(MAX_DECISION_ID);
        int count = tenantCrudService.executeUpdateByNamedQuery(DecisionByRankFPLOS.DELETE_FUTURE_DECISIONS_FOR_ZERO_CAPACITY_ROOM_TYPES, parameters);
        logMessage(deletedDecisionsInfo, "Decision_FPLOS_By_Rank", count);
        BigInteger maxDecisionId = tenantCrudService.findByNamedQuerySingleResult(PaceDecisionByRankFPLOS.GET_MAX_DECISION_ID);
        if (maxDecisionId != null) {
            parameters.put(MAX_DECISION_ID, maxDecisionId);
            count = tenantCrudService.executeUpdateByNamedQuery(PaceDecisionByRankFPLOS.DELETE_FUTURE_DECISIONS_FOR_ZERO_CAPACITY_ROOM_TYPES, parameters);
            logMessage(deletedDecisionsInfo, "PACE_FPLOS_By_Rank", count);
        }
        return deletedDecisionsInfo.toString();
    }

    private String deleteDecisionsForLRVAtAccomType(Map<String, Object> parameters) {
        StringBuilder deletedDecisionsInfo = new StringBuilder();
        parameters.remove(MAX_DECISION_ID);
        int count = tenantCrudService.executeUpdateByNamedQuery(LastRoomValueAccomType.DELETE_DECISIONS_FOR_ZERO_CAPACITY_ACCOM, parameters);
        logMessage(deletedDecisionsInfo, "Decision_LRV_AT", count);
        BigInteger maxDecisionId = tenantCrudService.findByNativeQuerySingleResult(GET_MAX_DECISION_ID_PACE_LRV_AT, null);
        if (maxDecisionId != null) {
            parameters.put(MAX_DECISION_ID, maxDecisionId);
            count = tenantCrudService.executeUpdateByNativeQuery(DELETE_PACE_LRV_AT_FOR_ZERO_CAPACITY_ACCOM, parameters);
            logMessage(deletedDecisionsInfo, "Pace_LRV_AT", count);
        }
        return deletedDecisionsInfo.toString();
    }

    private String deleteDecisionsForLRAFPLOS(Map<String, Object> parameters) {
        StringBuilder deletedDecisionsInfo = new StringBuilder();
        parameters.remove(MAX_DECISION_ID);
        int count = tenantCrudService.executeUpdateByNamedQuery(DecisionLRAFPLOS.DELETE_FUTURE_DECISIONS_FOR_ZERO_CAPACITY_ACCOM, parameters);
        logMessage(deletedDecisionsInfo, "Decision_LRA_FPLOS", count);
        BigInteger maxDecisionId = tenantCrudService.findByNamedQuerySingleResult(PaceDecisionLRAFPLOS.GET_MAX_DECISION_ID);
        if (maxDecisionId != null) {
            parameters.put(MAX_DECISION_ID, maxDecisionId);
            count = tenantCrudService.executeUpdateByNamedQuery(PaceDecisionLRAFPLOS.DELETE_FUTURE_DECISIONS_FOR_ZERO_CAPACITY_ACCOM, parameters);
            logMessage(deletedDecisionsInfo, "Pace_Decision_LRA_FPLOS", count);
        }
        return deletedDecisionsInfo.toString();
    }

    private String deleteDecisionsForLRAMinLOS(Map<String, Object> parameters) {
        StringBuilder deletedDecisionsInfo = new StringBuilder();
        parameters.remove(MAX_DECISION_ID);
        int count = tenantCrudService.executeUpdateByNamedQuery(DecisionLRAMINLOS.DELETE_FUTURE_DECISIONS_FOR_ZERO_CAPACITY_ACCOM, parameters);
        logMessage(deletedDecisionsInfo, "Decision_LRA_minLOS", count);
        BigInteger maxDecisionId = tenantCrudService.findByNamedQuerySingleResult(PaceDecisionLRAMINLOS.GET_MAX_DECISION_ID);
        if (maxDecisionId != null) {
            parameters.put(MAX_DECISION_ID, maxDecisionId);
            count = tenantCrudService.executeUpdateByNamedQuery(PaceDecisionLRAMINLOS.DELETE_FUTURE_DECISIONS_FOR_ZERO_CAPACITY_ACCOM, parameters);
            logMessage(deletedDecisionsInfo, "Pace_Decision_LRA_minLOS", count);
        }
        return deletedDecisionsInfo.toString();
    }

    private String deleteDecisionsForBarFplosByHierarchy(Map<String, Object> parameters) {
        StringBuilder deletedDecisionsInfo = new StringBuilder();
        parameters.remove(MAX_DECISION_ID);
        int count = tenantCrudService.executeUpdateByNativeQuery(DELETE_DECISION_FOR_DECISION_FPLOS_BY_HIERARCHY, parameters);
        logMessage(deletedDecisionsInfo, "Decision_FPLOS_By_Hierarchy", count);
        BigInteger maxDecisionId = tenantCrudService.findByNativeQuerySingleResult(GET_MAX_DECISION_ID_PACE_FPLOS_BY_HIERARCHY, null);
        if (maxDecisionId != null) {
            parameters.put(MAX_DECISION_ID, maxDecisionId);
            count = tenantCrudService.executeUpdateByNativeQuery(DELETE_DECISION_FOR_PACE_FPLOS_BY_HIERARCHY, parameters);
            logMessage(deletedDecisionsInfo, "PACE_FPLOS_By_Hierarchy", count);
        }
        return deletedDecisionsInfo.toString();
    }

    private String deleteDecisionsForMinMaxLOS(Map<String, Object> parameters) {
        StringBuilder deletedDecisionsInfo = new StringBuilder();
        parameters.remove(MAX_DECISION_ID);
        int count = tenantCrudService.executeUpdateByNamedQuery(MinlosDecisions.DELETE_FUTURE_DECISIONS_FOR_ZERO_CAPACITY_ROOM_TYPES, parameters);
        logMessage(deletedDecisionsInfo, "Decision_MINLOS", count);
        BigInteger maxDecisionId = tenantCrudService.findByNativeQuerySingleResult(GET_MAX_DECISION_ID_PACE_MINLOS, null);
        if (maxDecisionId != null) {
            parameters.put(MAX_DECISION_ID, maxDecisionId);
            count = tenantCrudService.executeUpdateByNativeQuery(DELETE_PACE_MINLOS_FOR_ZERO_CAPACITY_ACCOM, parameters);
            logMessage(deletedDecisionsInfo, "PACE_MINLOS", count);
        }
        return deletedDecisionsInfo.toString();
    }

    private String deleteDecisionForBarFplosByRoomType(Map<String, Object> parameters) {
        StringBuilder deletedDecisionsInfo = new StringBuilder();
        parameters.remove(MAX_DECISION_ID);
        int count = tenantCrudService.executeUpdateByNativeQuery(DELETE_DECISION_FOR_DECISION_FPLOS_BY_ROOM_TYPE, parameters);
        logMessage(deletedDecisionsInfo, "Decision_FPLOS_By_RoomType", count);
        BigInteger maxDecisionId = tenantCrudService.findByNativeQuerySingleResult(GET_MAX_DECISION_ID_PACE_FPLOS_BY_ROOM_TYPE, null);
        if (maxDecisionId != null) {
            parameters.put(MAX_DECISION_ID, maxDecisionId);
            count = tenantCrudService.executeUpdateByNativeQuery(DELETE_DECISION_FOR_PACE_FPLOS_BY_ROOM_TYPE, parameters);
            logMessage(deletedDecisionsInfo, "Pace_FPLOS_By_RoomType", count);
        }
        return deletedDecisionsInfo.toString();
    }

    private void logMessage(StringBuilder deletedDecisionsInfo, String table, int count) {
        final String message = String.format(LOG_MESSAGE_TEMPLATE, table, count);
        LOGGER.info(message);
        deletedDecisionsInfo.append(message);
    }

    @ForTesting


    public String deleteDecisions() {
        return deleteDecisionsFor(hospitalityRoomsService.getZeroCapacityRoomTypesExcludingHospitalityRooms());
    }
}
