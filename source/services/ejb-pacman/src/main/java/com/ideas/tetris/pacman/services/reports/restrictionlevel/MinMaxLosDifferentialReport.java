package com.ideas.tetris.pacman.services.reports.restrictionlevel;

import com.ideas.tetris.pacman.services.reports.restrictionlevel.dto.RestrictionLevelReportDTO;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Transactional
public class MinMaxLosDifferentialReport extends RestrictionLevelReport {

    @Override
    protected QueryParameter buildQueryParams(RestrictionLevelSpec rspec) {
        QueryParameter queryParameter = super.buildQueryParams(rspec);
        if (rspec.getChangesSince() != null) {
            queryParameter.and("business_date", rspec.getChangesSince());
            if (rspec.getRollingBusinessDate() != null) {
                queryParameter.and("rollingBusinessDate", rspec.getRollingBusinessDate());
            }
            queryParameter.and("isDecisionAtHotelLevel", rspec.isSrpFplosAtTotalLevelEnabled() ? 1 : 0);
        }
        return queryParameter;
    }

    @Override
    protected RestrictionLevelReportDTO getRowMappedFor(Object[] row) {
        return RestrictionLevelReportDTO.newMinLosReportFor(row);
    }

    @Override
    protected String getQuery() {
        return "select * from dbo.ufn_restriction_level_report_by_minlos_differential(:start_date, :end_date, :caught_date, :accom_type_id, :business_date, :isRollingDate, :rollingStartDate, :rollingEndDate, :rollingBusinessDate, :isDecisionAtHotelLevel) order by occupancy_date";
    }
}
