package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.MeetingPackageBaseProductPricingOverridesDTO;
import com.ideas.tetris.pacman.services.datafeed.entity.MeetingPackageBaseProductPricingOverrides;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.constants.Constants.END_DATE;
import static com.ideas.tetris.pacman.common.constants.Constants.START_DATE;

@Component
public class MeetingPackageBaseProductPricingOverridesService {

    @TenantCrudServiceBean.Qualifier
    @Qualifier("tenantCrudServiceBean")
    @Autowired
    private CrudService tenantCrudService;

    @Autowired
    private DateService dateService;

    public List<MeetingPackageBaseProductPricingOverridesDTO> getMeetingPackagePricingOverrideDto(DatafeedRequest datafeedRequest) {
        Map<String, Object> parameters = QueryParameter.with(START_DATE, datafeedRequest.getStartDate())
                .and(END_DATE, datafeedRequest.getEndDate())
                .parameters();

        List<MeetingPackageBaseProductPricingOverrides> meetingPackageOverrides = getMeetingPackageOverrides(datafeedRequest, parameters);
        String systemTimeZone = getSystemTimeZone();

        return meetingPackageOverrides == null ? Collections.emptyList() :
                meetingPackageOverrides.stream()
                        .flatMap(meetingPckOvr -> convertToDTOs(meetingPckOvr, systemTimeZone)).collect(Collectors.toList());
    }

    private List<MeetingPackageBaseProductPricingOverrides> getMeetingPackageOverrides(DatafeedRequest datafeedRequest, Map<String, Object> parameters) {
        return tenantCrudService.findByNamedQuery(MeetingPackageBaseProductPricingOverrides.GET_MEETING_PRICING_OVERRIDES,
                parameters, datafeedRequest.getStartPosition(), datafeedRequest.getSize());
    }

    private Stream<MeetingPackageBaseProductPricingOverridesDTO> convertToDTOs(MeetingPackageBaseProductPricingOverrides override, String systemTimeZone) {
        Double[] roomRentals = Arrays.stream(override.getOverridePrice().split(",")).map(Double::parseDouble).toArray(Double[]::new);
        Double offset = Optional.ofNullable(override.getTotalPackagePrice()).map(Double::parseDouble).orElse(0.0);

        String occupancyDate = override.getOccupancyDate().toString();
        String productName = override.getProductName();
        String meetingRoom = override.getMeetingRoom();
        String modifiedOn = override.getOverrideLastModifiedOn() + systemTimeZone;
        String modifiedBy = override.getOverrideLastModifiedBy();
        String overrideType = override.getOverrideType().toUpperCase();

        BiFunction<String, Double, MeetingPackageBaseProductPricingOverridesDTO> createDto = (type, rental) -> {
            MeetingPackageBaseProductPricingOverridesDTO dto = new MeetingPackageBaseProductPricingOverridesDTO();
            dto.setOccupancyDate(occupancyDate);
            dto.setProductName(productName);
            dto.setMeetingRoom(meetingRoom);
            dto.setOverrideType(type);
            dto.setOverrideRoomRental(rental);
            dto.setTotalPackageOverridePrice(rental + offset);
            dto.setOverrideLastModifiedOn(modifiedOn);
            dto.setOverrideLastModifiedBy(modifiedBy);
            return dto;
        };

        return getMeetingPackageBaseProductPricingOverridesDTOStream(roomRentals, overrideType, createDto);
    }

    private Stream<MeetingPackageBaseProductPricingOverridesDTO> getMeetingPackageBaseProductPricingOverridesDTOStream(Double[] roomRentals, String overrideType, BiFunction<String, Double, MeetingPackageBaseProductPricingOverridesDTO> createDto) {
        switch (overrideType) {
            case "FLOORANDCEIL":
                return Stream.of(createDto.apply("Floor", roomRentals[0]), createDto.apply("Ceiling", roomRentals[1]));
            case "FLOOR":
                return Stream.of(createDto.apply("Floor", roomRentals[0]));
            case "CEIL":
                return Stream.of(createDto.apply("Ceiling", roomRentals[0]));
            case "USER":
                return Stream.of(createDto.apply("Specific", roomRentals[0]));
            default:
                return Stream.empty();
        }
    }

    private String getSystemTimeZone() {
        TimeZone timeZone = dateService.getSystemDates().getTimeZone();
        boolean daylight = timeZone.inDaylightTime(new java.util.Date());
        return " " + timeZone.getDisplayName(daylight, TimeZone.SHORT);
    }
}