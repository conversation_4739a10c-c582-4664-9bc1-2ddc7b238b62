package com.ideas.tetris.pacman.services.dashboard.vo;

import java.io.Serializable;

public class RoomsSoldAndPickupVO implements Serializable {
    //Rooms Sold Details
    private String roomsSold;
    private int roomCapacity;
    private String roomsSoldPickup;

    //Budgeted Rooms Sold Details
    private String budgetedRoomsSold;
    private int budgetedRoomCapacity;

    public int getRoomCapacity() {
        return roomCapacity;
    }

    public void setRoomCapacity(int roomCapacity) {
        this.roomCapacity = roomCapacity;
    }

    public String getRoomsSoldPickup() {
        return roomsSoldPickup;
    }

    public void setRoomsSoldPickup(String roomsSoldPickup) {
        this.roomsSoldPickup = roomsSoldPickup;
    }

    public String getRoomsSold() {
        return roomsSold;
    }

    public void setRoomsSold(String roomsSold) {
        this.roomsSold = roomsSold;
    }

    public String getBudgetedRoomsSold() {
        return budgetedRoomsSold;
    }

    public void setBudgetedRoomsSold(String budgetedRoomsSold) {
        this.budgetedRoomsSold = budgetedRoomsSold;
    }

    public int getBudgetedRoomCapacity() {
        return budgetedRoomCapacity;
    }

    public void setBudgetedRoomCapacity(int budgetedRoomCapacity) {
        this.budgetedRoomCapacity = budgetedRoomCapacity;
    }


}
