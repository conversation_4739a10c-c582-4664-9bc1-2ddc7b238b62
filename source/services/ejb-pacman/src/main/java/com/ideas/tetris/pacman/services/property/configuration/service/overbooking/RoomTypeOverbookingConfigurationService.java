package com.ideas.tetris.pacman.services.property.configuration.service.overbooking;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingAccom;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingType;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.RoomTypeOverbookingLimitPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@RoomTypeOverbookingConfigurationService.Qualifier
@Component
@Transactional
public class RoomTypeOverbookingConfigurationService extends AbstractPropertyConfigurationService {

    private static final Logger LOGGER = Logger.getLogger(RoomTypeOverbookingConfigurationService.class.getName());

    private static final List<String> overbookingTypes = Arrays.asList("ROOMTYPE", "HOUSE", "NONE");

    @Override
    public PropertyConfigurationRecordType getPropertyConfigurationRecordType() {
        return PropertyConfigurationRecordType.ROBK;
    }

    @Override
    public List<PropertyConfigurationRecordFailure> doValidate(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId) {
        RoomTypeOverbookingLimitPropertyConfigurationDto rtolpcd = (RoomTypeOverbookingLimitPropertyConfigurationDto) propertyConfigurationDto;

        List<PropertyConfigurationRecordFailure> exceptions = new ArrayList<PropertyConfigurationRecordFailure>();

        // Validate Room Type
        String roomType = rtolpcd.getRoomType();
        if (StringUtils.isEmpty(roomType)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Room Type is required"));
        }

        // Validate AccomType
        AccomType accomType = findAccomType(propertyId, roomType);
        if (accomType == null) {
            exceptions.add(new PropertyConfigurationRecordFailure("Room Type: " + roomType + " not found"));
        }

        // If accomType is 1, we won't care about validating individual day data
        if (accomType.getRohType().intValue() == 1) {
            return exceptions;
        }

        // Validate Sunday
        validateOverbookingLimit(exceptions, rtolpcd.getOverbookingLimitSunday(), "Sunday");
        validateOverbookingType(exceptions, rtolpcd.getOverbookingTypeSunday(), "Sunday");

        // Validate Monday
        validateOverbookingLimit(exceptions, rtolpcd.getOverbookingLimitMonday(), "Monday");
        validateOverbookingType(exceptions, rtolpcd.getOverbookingTypeMonday(), "Monday");

        // Validate Tuesday
        validateOverbookingLimit(exceptions, rtolpcd.getOverbookingLimitTuesday(), "Tuesday");
        validateOverbookingType(exceptions, rtolpcd.getOverbookingTypeTuesday(), "Tuesday");

        // Validate Wednesday
        validateOverbookingLimit(exceptions, rtolpcd.getOverbookingLimitWednesday(), "Wednesday");
        validateOverbookingType(exceptions, rtolpcd.getOverbookingTypeWednesday(), "Wednesday");

        // Validate Thursday
        validateOverbookingLimit(exceptions, rtolpcd.getOverbookingLimitThursday(), "Thursday");
        validateOverbookingType(exceptions, rtolpcd.getOverbookingTypeThursday(), "Thursday");

        // Validate Overbooking Limit Friday
        validateOverbookingLimit(exceptions, rtolpcd.getOverbookingLimitFriday(), "Friday");
        validateOverbookingType(exceptions, rtolpcd.getOverbookingTypeFriday(), "Friday");

        // Validate Overbooking Limit Saturday
        validateOverbookingLimit(exceptions, rtolpcd.getOverbookingLimitSaturday(), "Saturday");
        validateOverbookingType(exceptions, rtolpcd.getOverbookingTypeSaturday(), "Saturday");

        return exceptions;
    }

    @Override
    public void doHandle(PropertyConfigurationDto pcd, Integer propertyId) {
        RoomTypeOverbookingLimitPropertyConfigurationDto rtolpcd = (RoomTypeOverbookingLimitPropertyConfigurationDto) pcd;

        AccomType accomType = findAccomType(propertyId, rtolpcd.getRoomType());

        // If accom type is ROH, set limit to -1 and type to "House"
        if (accomType.getRohType().intValue() == 1) {
            Integer limitWhenRoh = -1;
            String typeWhenRoh = "House";

            rtolpcd.setOverbookingLimitSunday(limitWhenRoh);
            rtolpcd.setOverbookingTypeSunday(typeWhenRoh);
            rtolpcd.setOverbookingLimitMonday(limitWhenRoh);
            rtolpcd.setOverbookingTypeMonday(typeWhenRoh);
            rtolpcd.setOverbookingLimitTuesday(limitWhenRoh);
            rtolpcd.setOverbookingTypeTuesday(typeWhenRoh);
            rtolpcd.setOverbookingLimitWednesday(limitWhenRoh);
            rtolpcd.setOverbookingTypeWednesday(typeWhenRoh);
            rtolpcd.setOverbookingLimitThursday(limitWhenRoh);
            rtolpcd.setOverbookingTypeThursday(typeWhenRoh);
            rtolpcd.setOverbookingLimitFriday(limitWhenRoh);
            rtolpcd.setOverbookingTypeFriday(typeWhenRoh);
            rtolpcd.setOverbookingLimitSaturday(limitWhenRoh);
            rtolpcd.setOverbookingTypeSaturday(typeWhenRoh);
        }

        OverbookingAccom overbookingAccom = findOverbookingAccom(accomType);
        if (overbookingAccom == null) {
            overbookingAccom = new OverbookingAccom();
            overbookingAccom.setAccomTypeId(accomType.getId());
            overbookingAccom.setPropertyId(propertyId);
            overbookingAccom.setCreatedByUserId(findUserId());
        }

        overbookingAccom.setSundayCeiling(rtolpcd.getOverbookingLimitSunday());
        overbookingAccom.setSundayOverbookingTypeId(findOverbookingTypeId(rtolpcd.getOverbookingTypeSunday()));
        overbookingAccom.setMondayCeiling(rtolpcd.getOverbookingLimitMonday());
        overbookingAccom.setMondayOverbookingTypeId(findOverbookingTypeId(rtolpcd.getOverbookingTypeMonday()));
        overbookingAccom.setTuesdayCeiling(rtolpcd.getOverbookingLimitTuesday());
        overbookingAccom.setTuesdayOverbookingTypeId(findOverbookingTypeId(rtolpcd.getOverbookingTypeTuesday()));
        overbookingAccom.setWednesdayCeiling(rtolpcd.getOverbookingLimitWednesday());
        overbookingAccom.setWednesdayOverbookingTypeId(findOverbookingTypeId(rtolpcd.getOverbookingTypeWednesday()));
        overbookingAccom.setThursdayCeiling(rtolpcd.getOverbookingLimitThursday());
        overbookingAccom.setThursdayOverbookingTypeId(findOverbookingTypeId(rtolpcd.getOverbookingTypeThursday()));
        overbookingAccom.setFridayCeiling(rtolpcd.getOverbookingLimitFriday());
        overbookingAccom.setFridayOverbookingTypeId(findOverbookingTypeId(rtolpcd.getOverbookingTypeFriday()));
        overbookingAccom.setSaturdayCeiling(rtolpcd.getOverbookingLimitSaturday());
        overbookingAccom.setSaturdayOverbookingTypeId(findOverbookingTypeId(rtolpcd.getOverbookingTypeSaturday()));

        if (overbookingAccom.getCreateDate() == null) {
            LOGGER.info("Creating OverbookingAccom for Property: " + pcd.getPropertyCode() + " and Room Type: " + rtolpcd.getRoomType());
            crudService.save(overbookingAccom);
        } else {
            LOGGER.info("Updating OverbookingAccom for Property: " + pcd.getPropertyCode() + " and Room Type: " + rtolpcd.getRoomType());
            crudService.save(overbookingAccom);
        }
    }

    public void validateOverbookingType(List<PropertyConfigurationRecordFailure> exceptions, String value, String day) {
        if (StringUtils.isEmpty(value)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Overbooking Type " + day + " is required"));
        } else if (!overbookingTypes.contains(value)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Overbooking Type " + day + ": " + value + " is not valid."));
        }
    }

    public void validateOverbookingLimit(List<PropertyConfigurationRecordFailure> exceptions, Integer value, String day) {
        if (value == null || value.intValue() < -1) {
            exceptions.add(new PropertyConfigurationRecordFailure("Overbooking Limit " + day + " must be -1 or greater"));
        }
    }

    public Integer findOverbookingTypeId(String name) {
        Map<String, Integer> overbookingTypeMap = null;
        if (overbookingTypeMap == null) {
            List<OverbookingType> overbookingTypes = crudService.findAll(OverbookingType.class);
            if (overbookingTypes != null) {

                overbookingTypeMap = new HashMap<String, Integer>();
                for (OverbookingType overbookingType : overbookingTypes) {
                    overbookingTypeMap.put(overbookingType.getOverbookingName().toUpperCase(), overbookingType.getId());
                }
            }
        }

        return overbookingTypeMap.get(determineOverbookingType(name.toUpperCase()).toUpperCase());
    }

    public String determineOverbookingType(String field) {
        if (StringUtils.equalsIgnoreCase("ROOMTYPE", field) || StringUtils.equalsIgnoreCase("Accommodation Type", field)) {
            return "Accommodation Type";
        } else if (StringUtils.equalsIgnoreCase("House", field)) {
            return "House";
        }

        return "None";
    }

    public OverbookingAccom findOverbookingAccom(AccomType accomType) {
        return (OverbookingAccom) crudService.findByNamedQuerySingleResult(OverbookingAccom.BY_ACCOMTYPEID, QueryParameter.with("accomTypeId", accomType.getId()).parameters());
    }

    public AccomType findAccomType(Integer propertyId, String name) {
        return (AccomType) crudService.findByNamedQuerySingleResult(AccomType.BY_PROPERTY_ID_TYPE, QueryParameter.with("propertyId", propertyId).and("accomTypeCode", name).parameters());
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
