package com.ideas.tetris.pacman.services.client.service;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Component
@Transactional
public class ClientConfigService {
    private static final Logger LOGGER = Logger.getLogger(ClientConfigService.class);

    public static final String ADD_USERS_TO_CLIENT = "if not exists (select 1 from Client_User where Client_ID = :clientId)\n" +
            "BEGIN\n" +
            "\tINSERT INTO Client_User (Client_ID, User_ID) SELECT :clientId, USER_ID FROM Users WHERE Internal = 1 AND User_ID != 11403\n" +
            "\tINSERT INTO Client_User (Client_ID, User_ID) VALUES (:clientId, 11403)\n" +
            "END\n";
    public static final String DELETE_USERS_FROM_CLIENT = "DELETE FROM Client_User WHERE Client_ID =  :clientId\n";

    public static final String DEACTIVATE_CLIENT_ONLY_USERS =
            "UPDATE global.dbo.Users SET Status_ID = :inactiveStatusId, Last_Updated_by_User_ID = :updateUserId, Last_Updated_DTTM = getdate()\n" +
                    "WHERE Status_ID = 1 AND Internal = 0 AND User_ID IN (\n" +
                    "  SELECT USER_ID FROM global.dbo.Client_User cu WHERE Client_ID = :clientId\n" +
                    ")";

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public Client getClientByName(String clientName) {
        return (Client) globalCrudService.findByNamedQuerySingleResult(Client.BY_NAME, QueryParameter.with(Client.NAME_CONSTANT, clientName).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<String> getClientNames() {
        return globalCrudService.findByNamedQuery(Client.GET_ALL_NAMES);
    }


    @SuppressWarnings("unchecked")
    public List<Client> getAllClientDetails() {
        return globalCrudService.findByNamedQuery(Client.ALL);
    }

    public Client persistClient(Client client) {

        return globalCrudService.save(client);
    }

    public void deleteClient(Integer clientId) {
        globalCrudService.delete(Client.class, clientId);
    }

    public Client updateClient(Client client) {
        return globalCrudService.save(client);
    }

    public Client getClientByCode(String clientCode) {
        return (Client) globalCrudService.findByNamedQuerySingleResult(Client.BY_CODE, QueryParameter.with("code", clientCode).parameters());
    }

    public Client createActiveClient(String code, String name) {
        Client client = new Client();
        client.setCode(code);
        client.setName(name);
        client.setStatus(Status.ACTIVE);
        return globalCrudService.save(client);
    }

    public void addUsersToNewClient(Integer clientId) {
        globalCrudService.executeUpdateByNativeQuery(ADD_USERS_TO_CLIENT, QueryParameter.with("clientId", clientId).parameters());
    }

    public void deleteUsersFromClient(Integer clientId) {
        String userId = PacmanWorkContextHelper.getUserId();

        int usersDeactivated = globalCrudService.executeUpdateByNativeQuery(DEACTIVATE_CLIENT_ONLY_USERS, QueryParameter
                .with("inactiveStatusId", Constants.INACTIVE_STATUS_ID)
                .and("updateUserId", userId)
                .and("clientId", clientId).parameters());
        LOGGER.info(String.format("Deactivated %d users from client %s", usersDeactivated, clientId));

        int usersRemoved = globalCrudService.executeUpdateByNativeQuery(DELETE_USERS_FROM_CLIENT, QueryParameter.
                with("clientId", clientId).parameters());
        LOGGER.info(String.format("Removed %d total users from client %s", usersRemoved, clientId));
    }

    public Client findClient(Integer clientId) {
        return globalCrudService.find(Client.class, clientId);
    }

    public List<Client> findAllActiveClients(List<String> clientCodes) {
        return globalCrudService.findByNamedQuery(Client.GET_ACTIVE_CLIENTS_FROM_LIST, QueryParameter.with("clientCodes", clientCodes).parameters());
    }

    public List<String> getClientCodes() {
        return globalCrudService.findByNamedQuery(Client.GET_ALL_CODES);
    }
}
