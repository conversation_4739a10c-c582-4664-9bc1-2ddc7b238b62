package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.services.datafeed.dto.rateshopping.TaxInclusiveConfigurationDTO;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class TaxInclusiveConfigurationService {

    @Autowired
    TaxService taxService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    public static final String YES = "Yes";
    public static final String NO = "No";
    public static final String DEFAULT = "Default";
    public static final String SEASONAL = "Seasonal";

    public List<TaxInclusiveConfigurationDTO> getTaxInclusiveConfiguration() {
        if (!isContinuousPricingEnabled()) {
            return Collections.emptyList();
        }

        List<Tax> taxes = taxService.findAllTaxes();
        return getZeroDefaultTax(taxes) != null ? Collections.singletonList(getDefaultTaxDTO()) : getTaxInclusiveConfigurationDTOS(taxes);
    }

    private List<TaxInclusiveConfigurationDTO> getTaxInclusiveConfigurationDTOS(List<Tax> taxes) {
        return taxes.stream()
                .map(this::getInclusiveConfigurationDTO).sorted(getTaxInclusiveConfigurationDTOComparator())
                .collect(Collectors.toList());
    }

    private Comparator<TaxInclusiveConfigurationDTO> getTaxInclusiveConfigurationDTOComparator() {
        return Comparator.comparing(TaxInclusiveConfigurationDTO::getSeasonStartDate, Comparator.nullsFirst(Comparator.naturalOrder()));
    }

    private TaxInclusiveConfigurationDTO getInclusiveConfigurationDTO(Tax tax) {
        if (isDefaultTax(tax)) {
            return getDefaultTaxDTO();
        } else {
            try {
                return getSeasonalTax(tax);
            } catch (ParseException e) {
                throw new TetrisException(e.getLocalizedMessage());
            }
        }
    }

    private boolean isDefaultTax(Tax tax) {
        return null == tax.getStartDate() || null == tax.getEndDate();
    }

    private Tax getZeroDefaultTax(List<Tax> taxes) {
        return taxes.stream()
                .filter(tax -> isDefaultTax(tax) && BigDecimal.ZERO.equals(tax.getRoomTaxRate()))
                .findFirst()
                .orElse(null);
    }

    private boolean isContinuousPricingEnabled() {
        return configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
    }

    private TaxInclusiveConfigurationDTO getDefaultTaxDTO() {
        Tax defaultTax = taxService.findTax();
        String defaultRoomRevenueTax = isNotDefaultRoomRevenueTax(defaultTax) ? NO : YES;
        return new TaxInclusiveConfigurationDTO(defaultRoomRevenueTax, DEFAULT, "", null, null, defaultTax.getRoomTaxRate());
    }

    private boolean isNotDefaultRoomRevenueTax(Tax defaultTax) {
        return defaultTax.getRoomTaxRate() == null || defaultTax.getRoomTaxRate().equals(BigDecimal.ZERO.setScale(defaultTax.getRoomTaxRate().scale(), RoundingMode.HALF_UP));
    }

    private TaxInclusiveConfigurationDTO getSeasonalTax(Tax tax) throws ParseException {
        return new TaxInclusiveConfigurationDTO(YES, SEASONAL, tax.getSeasonName(), getFormattedDate(tax.getStartDate()), getFormattedDate(tax.getEndDate()), tax.getRoomTaxRate());
    }

    private Date getFormattedDate(LocalDate localDate) throws ParseException {

        DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd");
        String formattedDate = formatter.print(localDate);
        return new SimpleDateFormat("yyyy-MM-dd").parse(formattedDate);
    }
}
