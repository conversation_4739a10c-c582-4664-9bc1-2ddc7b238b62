package com.ideas.tetris.pacman.services.vendor;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.ideas.tetris.platform.common.ngi.VendorCredentials;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class HotelConfigParams implements Serializable, VendorCredentialsAccessor {
    private static final long serialVersionUID = 3715003818963039275L;

    private String hotelCode;
    private String g3HotelCode;
    private String inboundHotelCode;
    private String propertyName;
    private String outgoingUrl;
    private String baseCurrencyCode;
    private Integer pastDays;
    private Integer futureDays;
    private String oxiInterfaceName;
    private BigDecimal taxAdjustmentValue;
    private VendorCredentials inboundCredentials;
    private VendorCredentials outboundCredentials;
    private Boolean assumeTaxIncluded;
    private Boolean assumePackageIncluded;
    private Boolean installMode;
    private Boolean scheduledDeferredDelivery;
    private Boolean calculateNonPickedUpBlocksUsingSummaryData;
    private String g2HotelCode;
    private Boolean inCatchup;
    private Boolean handlePreviouslyStraightMarketSegmentsInAms;
    private String defaultRoomType;
    private Boolean unqualifiedRatesDirectPopulationDisabled;
    private Boolean qualifiedRatesDirectPopulationDisabled;
    private String roomRevenuePackages;
    private Integer installationReservationsThreshold;
    private Boolean populatePackageDataEnabled;
    private Boolean useNetRoomRevenue;
    private Boolean useCurrencyInMessage;
    private Boolean includePseudoInRevenue;
    private Boolean includeDayUseInRevenue;
    private Boolean includeNoShowInRevenue;
    private Boolean oxiRoomStayReservationByDay;
    private Boolean htngRoomStayReservationByDay;
    private Boolean folsRoomStayReservationByDay;
    private Boolean htngUseBasicAuth;
    private Boolean preventPseudoDataInActivity;
    private Boolean includeRoomTypeHotelMarketSegmentActivity;
    private Boolean isComponentRoomsActivityEnabled;
    private Boolean useLegacyRoomStayHandling;
    private String oxiParkMessageTypes;
    private String clientEnvironmentName;
    private Boolean generateMarketSegmentStatsEnabled;
    private Boolean generateRateCodeStatsEnabled;
    private Boolean resetVirtualSuiteCounts;
    private Boolean generateHotelActivityFromRoomTypeActivity;
    private Boolean msrtSummaryPersistenceEnabled;
    private Boolean buildMSActivityUsingPMSMS;
    private Boolean summaryPersistenceEnabled;
    private String salesAndCateringStatus;
    private String salesAndCateringUnitOfMeasure;
    private String salesAndCateringRMSCurrencyCode;
    private String defaultMarketSegmentCode;
    private String defaultGroupMarketSegmentCode;
    private Boolean alternateHTNGGroupsFlow;
    private Boolean processSoftPickups;
    private Boolean mergeGroupBlocksRatesIfMissing;
    private Boolean mergeGroupBlocksForHeaderOnlyCancel;
    private String externalPropertyId;
    private String adjustReservationNetRates;
    private String inventoryLoadingCronScheduleInUTC;
    private String timeSliceDefinitionIds;
    private Boolean runGroupAutoWash;
    private Boolean folsUseCloudProcessing;
    private Boolean rraUseCloudProcessing;
    private Boolean adjustIdpSoldsUsingSkewingFactor;
    private Integer decisionsThreshold;
    private List<String> pseudoRoomTypes;
    private Boolean usePastInventoryForStatsAlways;
    private Boolean ignoreRateDetails;
    private Integer groupPastDays;
    private Boolean oxiUseCloudProcessing;
    private String cloudMigrationStatus;
    private Boolean htngUseCloudProcessing;
    private Boolean htngCallbackOnCloudDisabled;

    @Override
    public VendorCredentials getOutboundCredentials() {
        return outboundCredentials;
    }

    @Override
    public void setOutboundCredentials(VendorCredentials outboundCredentials) {
        this.outboundCredentials = outboundCredentials;
    }

    @Override
    public VendorCredentials getInboundCredentials() {
        return inboundCredentials;
    }

    @Override
    public void setInboundCredentials(VendorCredentials inboundCredentials) {
        this.inboundCredentials = inboundCredentials;
    }

    public void cleanUpEmptyHotelData() {
        if (getInboundCredentials() != null && StringUtils.isBlank(getInboundCredentials().getUsername()) && StringUtils.isBlank(getInboundCredentials().getPassword())) {
            setInboundCredentials(null);
        }
        if (getOutboundCredentials() != null && StringUtils.isBlank(getOutboundCredentials().getUsername()) && StringUtils.isBlank(getOutboundCredentials().getPassword())) {
            setOutboundCredentials(null);
        }
    }

    public Boolean getComponentRoomsActivityEnabled() {
        return isComponentRoomsActivityEnabled;
    }

    public void setComponentRoomsActivityEnabled(Boolean componentRoomsActivityEnabled) {
        this.isComponentRoomsActivityEnabled = componentRoomsActivityEnabled;
    }
}
