package com.ideas.tetris.pacman.services.property.dto;

import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;

import java.io.File;
import java.text.MessageFormat;
import java.util.regex.Matcher;

public class PacmanExtractForContextFileFilter extends PacmanExtractFileFilter {
    private LocalDate startDate;
    private LocalDate endDate;
    private WorkContextType workContext;

    public PacmanExtractForContextFileFilter(LocalDate startDate, LocalDate endDate, WorkContextType workContext) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.workContext = workContext;
    }

    @Override
    public boolean accept(File file) {
        // e.g. Hilton_DALMC_20110226_0111_T2SNAP.zip
        boolean shouldAccept = false;
        if (!file.isDirectory()) {
            String regex = MessageFormat.format(PacmanExtractDetails.T2SNAP_REGEX_PATTERN,
                    workContext.getClientCode().toLowerCase(),
                    workContext.getPropertyCode().toLowerCase());

            if (file.getName().toLowerCase().matches(regex)) {
                Matcher matcher = PacmanExtractDetails.T2SNAP_DATE_PATTERN.matcher(file.getName());
                if (matcher.find()) {
                    LocalDate fileDate = LocalDate.parse(matcher.group(),
                            DateTimeFormat.forPattern(PacmanExtractDetails.FILE_DATE_FORMAT));
                    shouldAccept = null != fileDate && !fileDate.isAfter(endDate) && !fileDate.isBefore(startDate);
                }
            }
        }
        return shouldAccept;
    }
}
