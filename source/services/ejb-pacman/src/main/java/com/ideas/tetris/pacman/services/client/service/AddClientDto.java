package com.ideas.tetris.pacman.services.client.service;

public class AddClientDto {

    private String name;
    private String code;
    private String externalSystem;
    private String externalSubSystem;
    private String opmsInstallationType;
    private String orsInstallationType;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getExternalSystem() {
        return externalSystem;
    }

    public void setExternalSystem(String externalSystem) {
        this.externalSystem = externalSystem;
    }

    public String getExternalSubSystem() {
        return externalSubSystem;
    }

    public void setExternalSubSystem(String externalSubSystem) {
        this.externalSubSystem = externalSubSystem;
    }

    public String getOpmsInstallationType() {
        return opmsInstallationType;
    }

    public void setOpmsInstallationType(String opmsInstallationType) {
        this.opmsInstallationType = opmsInstallationType;
    }

    public String getOrsInstallationType() {
        return orsInstallationType;
    }

    public void setOrsInstallationType(String orsInstallationType) {
        this.orsInstallationType = orsInstallationType;
    }

}
