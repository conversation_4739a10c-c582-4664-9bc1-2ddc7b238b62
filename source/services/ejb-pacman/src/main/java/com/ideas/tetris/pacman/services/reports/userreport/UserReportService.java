package com.ideas.tetris.pacman.services.reports.userreport;

import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.client.service.ClientAgentConfigService;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.userreport.dto.FilterDto;
import com.ideas.tetris.pacman.services.reports.userreport.dto.PropertyRoleAndLastAccessDTO;
import com.ideas.tetris.pacman.services.reports.userreport.dto.UserRolePermissionDetailsDTO;
import com.ideas.tetris.pacman.services.reports.userreport.dto.UsersAuthGroupPropertyDetails;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class UserReportService {

    private static final Logger LOGGER = Logger.getLogger(UserReportService.class.getName());
    private static final String HIPHEN = " -- ";
    public static final String ACTIVE = "Active";
    public static final String INACTIVE = "Inactive";
    private static final String DATE_FORMAT = "EEE dd-MMM-yyyy HH:mm:ss";
    public static final String EXCEL_DATE_FORMAT = "EEEE, dd-MMM-yyyy HH:mm:ss aaa";

    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService globalCrudService;

    @Autowired
	protected PacmanConfigParamsService configParamsService;

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public CrudService getGlobalCrudService() {
        return globalCrudService;
    }

    public void setConfigParamsService(PacmanConfigParamsService configParamsService) {
        this.configParamsService = configParamsService;
    }

    public PacmanConfigParamsService getConfigParamsService() {
        return configParamsService;
    }


    public List<UserRolePermissionDetailsDTO> getUsersData(final FilterDto userFilterDto, String clientCode, final Locale locale) {
        QueryParameter queryParameters = setQueryParameters(userFilterDto, clientCode)
                .and("useExitingTempTable", "false");
        StringBuilder sb = new StringBuilder();
        sb.append("exec dbo.usp_users_filterby_individualproperty_authgrp_role ")
                .append(":propertyIDs,")
                .append(":authGrpIDs,")
                .append(":roleIDs,")
                .append(":userIDs,")
                .append(":isActive,")
                .append(":isUserInternal,")
                .append(":client_code,")
                .append(":useExitingTempTable,")
                .append(":loggedInUserId");

        final TimeZone timeZone = Calendar.getInstance().getTimeZone();

        try {
            List<Object[]> resultList = getGlobalCrudService().findByNativeQuery(sb.toString(), queryParameters.parameters());
            List<UserRolePermissionDetailsDTO> userList = new ArrayList<>();
            UserRolePermissionDetailsDTO user;
            for (Object[] row : resultList) {
                user = new UserRolePermissionDetailsDTO();
                user.setId((Integer) row[0]);
                user.setLastName((String) row[2]);
                user.setFirstName((String) row[3]);
                user.setStatus((Integer) row[4] == 1 ? ACTIVE : INACTIVE);
                user.setEmail((String) row[5]);
                user.setCreatedDate((null != row[6]) ? getFormattedDateWithTimeZoneWithoutConversion((Date) row[6], timeZone, userFilterDto.getUserDefindedDateFormat(), locale) : HIPHEN);
                user.setLastLogin((null != row[7]) ? getFormattedDateWithTimeZoneWithoutConversion((Date) row[7], timeZone, userFilterDto.getUserDefindedDateFormat(), locale) : HIPHEN);
                user.setExcelCreatedDate((null != row[6]) ? getFormattedDateWithTimeZoneWithoutConversion((Date) row[6], timeZone, EXCEL_DATE_FORMAT, locale) : HIPHEN);
                user.setExcelLastLogin((null != row[7]) ? getFormattedDateWithTimeZoneWithoutConversion((Date) row[7], timeZone, EXCEL_DATE_FORMAT, locale) : HIPHEN);
                user.setUniqueUserID((String) row[8]);
                if (userFilterDto.getIsUserExternal() == 1) {
                    userList.add(user);
                } else if (isNotG3Agent(user)) {
                    userList.add(user);
                }
            }
            return userList;
        } catch (Exception e) {
            LOGGER.warn("No result found.", e);
            return new ArrayList<UserRolePermissionDetailsDTO>();
        }
    }

    private QueryParameter setQueryParameters(FilterDto userFilterDto, String clientCode) {
        return QueryParameter.with("propertyIDs", userFilterDto.isPropertiesAllSelected() == 1 ? "-1" : sqlFormatedList(userFilterDto.getPropertiesSelectedItems()))
                .and("authGrpIDs", userFilterDto.isAuthorizationGroupAllSelected() == 1 ? "-1" : sqlFormatedList(userFilterDto.getAuthorizationGroupSelectedItems()))
                .and("roleIDs", userFilterDto.isRolesAllSelected() == 1 ? "-1" : sqlFormatedList(userFilterDto.getRolesSelectedItems()))
                .and("userIDs", userFilterDto.isUsersAllSelected() == 1 ? "-1" : sqlFormatedList(userFilterDto.getUsersSelectedItems()))
                .and("isActive", userFilterDto.getStatus())
                .and("isUserInternal", userFilterDto.getIsUserExternal())
                .and("client_code", clientCode)
                .and("loggedInUserId", PacmanWorkContextHelper.getUserId());
    }

    private boolean isNotG3Agent(UserRolePermissionDetailsDTO user) {
        return !user.getEmail().equals(getAgentUserEmail(PacmanWorkContextHelper.getClientCode()));
    }

    private String getAgentUserEmail(String clientCode) {
        return MessageFormat.format(ClientAgentConfigService.AGENT_EMAIL_TMPL, clientCode);
    }

    public UsersAuthGroupPropertyDetails getUserDetailsData(Integer selectedUserId, String clientCode, Map<String, String> roleMap, boolean loggedInUserIsInternal, String userDefindedDateFormat, final Locale locale) {
        QueryParameter queryParameters =
                QueryParameter.with("userIDs", selectedUserId.toString())
                        .and("client_code", clientCode)
                        .and("isInternal", loggedInUserIsInternal ? 1 : 0);

        StringBuilder sb = new StringBuilder();
        sb.append("exec  dbo.usp_user_authgrp_property_role_details ")
                .append(":userIDs,")
                .append(":client_code,")
                .append(":isInternal");

        TimeZone timeZone = Calendar.getInstance().getTimeZone();

        try {
            String displayCodeOrName = configParamsService.getValue("pacman."
                            + PacmanWorkContextHelper.getClientCode(),
                    GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value());
            List<Object[]> resultList = globalCrudService.findByNativeQuery(sb.toString(), queryParameters.parameters());
            List<PropertyRoleAndLastAccessDTO> authGrpDetails = new ArrayList<PropertyRoleAndLastAccessDTO>();
            List<PropertyRoleAndLastAccessDTO> individualPropertyDetails = new ArrayList<PropertyRoleAndLastAccessDTO>();
            PropertyRoleAndLastAccessDTO propertyRoleAndLastAccessDTO;
            UsersAuthGroupPropertyDetails data = new UsersAuthGroupPropertyDetails();
            for (Object[] row : resultList) {
                if (null != row[1]) {
                    data.setAuthGrpName((String) row[1]);
                    data.setAuthGrpRole(roleMap.get(row[2]));
                    propertyRoleAndLastAccessDTO = new PropertyRoleAndLastAccessDTO();
                    propertyRoleAndLastAccessDTO.setPropertyName("Code".equalsIgnoreCase(displayCodeOrName)
                            ? (String) row[3] : (String) row[4]);
                    propertyRoleAndLastAccessDTO.setLastAccessed(null != row[7] ? getFormattedDateWithTimeZoneWithoutConversion((Date) row[7], timeZone, userDefindedDateFormat, locale) : HIPHEN);
                    authGrpDetails.add(propertyRoleAndLastAccessDTO);
                } else {
                    propertyRoleAndLastAccessDTO = new PropertyRoleAndLastAccessDTO();
                    propertyRoleAndLastAccessDTO.setPropertyName("Code".equalsIgnoreCase(displayCodeOrName) ?
                            (String) row[4] : (String) row[5]);
                    propertyRoleAndLastAccessDTO.setRoleName(roleMap.get(row[6]));
                    propertyRoleAndLastAccessDTO.setLastAccessed(null != row[7] ? getFormattedDateWithTimeZoneWithoutConversion((Date) row[7], timeZone, userDefindedDateFormat, locale) : HIPHEN);
                    individualPropertyDetails.add(propertyRoleAndLastAccessDTO);
                }
            }
            data.setAuthGrpDetails(authGrpDetails);
            data.setIndividualPropertyDetails(individualPropertyDetails);
            return data;

        } catch (Exception e) {
            LOGGER.warn("No result found.", e);
            return new UsersAuthGroupPropertyDetails();
        }
    }

    private String sqlFormatedList(Set<?> list) {
        if (null == list || list.isEmpty()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();

        for (Object i : list) {
            sb.append(i + ",");
        }

        sb.deleteCharAt(sb.length() - 1);

        return sb.toString();
    }

    public List<UserRolePermissionDetailsDTO> getUsersWithAllDetails(final FilterDto userFilterDto, String clientCode,
                                                                     final Map<String, String> roleMap, final Locale locale) {
        final String displayCodeOrName = configParamsService.getValue("pacman." + PacmanWorkContextHelper.getClientCode(),
                GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value());
        return getUserRolePermissionDetailsDTOS(userFilterDto, clientCode, roleMap, locale, displayCodeOrName);
    }

    public List<UserRolePermissionDetailsDTO> getUserRolePermissionDetailsDTOS(FilterDto userFilterDto, String clientCode, Map<String, String> roleMap, Locale locale, String displayCodeOrName) {
        QueryParameter queryParameters = setQueryParameters(userFilterDto, clientCode);

        try {
            final String queryStr = "exec dbo.usp_users_details_filterby_individualproperty_authgrp_role " +
                    ":propertyIDs, :authGrpIDs, :roleIDs, :userIDs, :isActive, :isUserInternal, :client_code, :loggedInUserId";
            List<Object[]> resultList = getGlobalCrudService().findByNativeQuery(queryStr, queryParameters.parameters());
            List<UserRolePermissionDetailsDTO> userList = new ArrayList<>();
            for (Object[] row : resultList) {
                UserRolePermissionDetailsDTO user = new UserRolePermissionDetailsDTO();
                user.setLastName((String) row[1]);
                user.setFirstName((String) row[0]);
                user.setStatus((Integer) row[2] == 1 ? ACTIVE : INACTIVE);
                user.setEmail((String) row[3]);
                user.setCreatedDate(getFormattedDate(row[4], locale, userFilterDto.getUserDefindedDateFormat()));
                user.setLastLogin(getFormattedDate(row[5], locale, userFilterDto.getUserDefindedDateFormat()));
                user.setAuthGroupName((String) row[6]);
                user.setAuthGroupRole(roleMap.getOrDefault(row[7], HIPHEN));
                user.setProperty(getProperty(displayCodeOrName, row));
                user.setPropertyRole(roleMap.getOrDefault(row[10], HIPHEN));
                user.setExcelCreatedDate(getFormattedDate(row[4], locale, EXCEL_DATE_FORMAT));
                user.setExcelLastLogin(getFormattedDate(row[5], locale, EXCEL_DATE_FORMAT));
                user.setUniqueUserID((String) row[11]);
                user.setPropertyCode((String) row[8]);
                if (userFilterDto.getIsUserExternal() == 1 || isNotG3Agent(user)) {
                    userList.add(user);
                }
            }
            LOGGER.info("getUsersWithAllDetails , ClientCode " + clientCode + ". Number Of Users Found =  " + userList.size());
            return userList;
        } catch (Exception e) {
            LOGGER.warn("No result found.", e);
            return Collections.emptyList();
        }
    }

    private String getFormattedDate(Object date, Locale locale, String userDefinedDateFormat) {
        return (null != date) ?
                getFormattedDateWithTimeZoneWithoutConversion((Date) date, Calendar.getInstance().getTimeZone(), userDefinedDateFormat, locale)
                : HIPHEN;
    }

    private String getProperty(String displayCodeOrName, Object[] row) {
        return (String) ("Code".equalsIgnoreCase(displayCodeOrName) ? row[8] : row[9]);
    }

    public String getFormattedDateWithTimeZoneWithoutConversion(Date date, TimeZone timeZone, String userPrefDateFormat, Locale locale) {
        DateFormat dateFormat = new SimpleDateFormat(null == userPrefDateFormat ? DATE_FORMAT : userPrefDateFormat, locale);
        String dateString = null;

        if (null != date) {
            boolean isPropertyInDayLightSavingForThisDate = timeZone.inDaylightTime(date);

            dateString = dateFormat.format(date) + " " + timeZone.getDisplayName(isPropertyInDayLightSavingForThisDate, 0);
        }

        return dateString;
    }
}
