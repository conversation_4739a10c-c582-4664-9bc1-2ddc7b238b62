package com.ideas.tetris.pacman.services.reports.operations.dto;

import java.time.LocalDate;
import java.util.Objects;

public class OperationsReportParameters {
    private LocalDate startDate;
    private LocalDate endDate;
    private String rollingEndDate;
    private String isRollingDate = "0";
    private String rollingStartDate;
    private String isPerPersonPricing;

    public String getIsPerPersonPricing() {
        return isPerPersonPricing;
    }

    public void setIsPerPersonPricing(String isPerPersonPricing) {
        this.isPerPersonPricing = isPerPersonPricing;
    }

    public String getIsRollingDate() {
        return isRollingDate;
    }

    public void setIsRollingDate(String isRollingDate) {
        this.isRollingDate = isRollingDate;
    }


    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }


    public String getRollingStartDate() {
        return rollingStartDate;
    }

    public void setRollingStartDate(String rollingStartDate) {
        this.rollingStartDate = rollingStartDate;
    }

    public String getRollingEndDate() {
        return rollingEndDate;
    }

    public void setRollingEndDate(String rollingEndDate) {
        this.rollingEndDate = rollingEndDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OperationsReportParameters that = (OperationsReportParameters) o;
        return isSpecificDatesEqual(that) &&
                Objects.equals(isRollingDate, that.isRollingDate) &&
                isRollingDatesEqual(that);
    }

    private boolean isRollingDatesEqual(OperationsReportParameters that) {
        return Objects.equals(rollingEndDate, that.rollingEndDate) &&
                Objects.equals(rollingStartDate, that.rollingStartDate);
    }

    private boolean isSpecificDatesEqual(OperationsReportParameters that) {
        return Objects.equals(startDate, that.startDate) &&
                Objects.equals(endDate, that.endDate);
    }

    @Override
    public int hashCode() {

        return Objects.hash(startDate, endDate, rollingEndDate, isRollingDate, rollingStartDate);
    }
}
