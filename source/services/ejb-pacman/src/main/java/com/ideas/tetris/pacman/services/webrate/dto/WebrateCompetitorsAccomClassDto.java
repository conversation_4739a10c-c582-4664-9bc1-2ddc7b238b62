package com.ideas.tetris.pacman.services.webrate.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class WebrateCompetitorsAccomClassDto {
    private Integer webrateCompetitorsClassId;
    private Integer webrateCompetitorsId;
    private Integer accomClassId;
    private Integer demandEnabled;
    private Integer rankEnabled;
    private Integer productId;
    private Integer dta;
    private Integer createdByUserId;
    private LocalDateTime createDttm;
    private Integer lastUpdatedByUserId;
    private LocalDateTime lastUpdatedDttm;
}
