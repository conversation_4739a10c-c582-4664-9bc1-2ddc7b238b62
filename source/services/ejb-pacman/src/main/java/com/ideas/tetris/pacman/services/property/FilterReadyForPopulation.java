package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ConsolidatedPropertyView;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecord;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecordStatus;
import com.ideas.tetris.pacman.services.property.dto.ExtractDetails;
import com.ideas.tetris.pacman.services.property.dto.PropertySearchCriteria;
import com.ideas.tetris.pacman.services.property.dto.WebRateExtractDetails;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.Stage;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class FilterReadyForPopulation {
    private static final int ZERO = 0;

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;
    @Autowired
	protected AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
	protected AuthorizationService authorizationService;
    @Autowired
	protected ExtractDetailsServiceLocal extractDetailsService;


    public List<ConsolidatedPropertyView> filter(final List<ConsolidatedPropertyView> properties, final PropertySearchCriteria searchCriteria) {
        List<ConsolidatedPropertyView> filteredProperties = properties;
        if (searchCriteria.isReadyForPopulation()) {
            filteredProperties = new ArrayList<ConsolidatedPropertyView>();
            boolean isReadyForPopulation;
            for (ConsolidatedPropertyView property : properties) {
                ExtractDetails extractDetails = null;
                WebRateExtractDetails webRateExtractDetails = null;
                extractDetails = extractDetailsService.getExtractDetails(property.getPropertyId());
                webRateExtractDetails = extractDetailsService.getWebRateExtractDetails(property.getPropertyId());

                isReadyForPopulation = checkIfNoExtractDetails(extractDetails, webRateExtractDetails);

                isReadyForPopulation = checkStage(isReadyForPopulation, property);

                isReadyForPopulation = checkUnProcessedExtracts(isReadyForPopulation, extractDetails, webRateExtractDetails);

                isReadyForPopulation = checkIfNoOOOPending(isReadyForPopulation, property);

                if (isReadyForPopulation) {
                    filteredProperties.add(property);
                }
            }
        }
        return filteredProperties;
    }

    private boolean checkIfNoExtractDetails(final ExtractDetails extractDetails, final WebRateExtractDetails webRateExtractDetails) {
        boolean result = true;
        if ((extractDetails == null) || (webRateExtractDetails == null)) {
            result = false;
        }
        return result;
    }

    private boolean checkStage(final boolean isReadyForPopulation, final ConsolidatedPropertyView property) {
        boolean result = isReadyForPopulation;
        if (isReadyForPopulation && !Stage.CATCHUP.equals(property.getStage())) {
            result = false;
        }
        return result;
    }

    private boolean checkUnProcessedExtracts(final boolean isReadyForPopulation, final ExtractDetails extractDetails, final WebRateExtractDetails webRateExtractDetails) {
        boolean result = isReadyForPopulation;
        if (isReadyForPopulation && (getIncomingExtractsSize(extractDetails) > ZERO || getWebRateExtractsSize(webRateExtractDetails) > ZERO)) {
            result = false;
        }
        return result;
    }

    private boolean checkIfNoOOOPending(final boolean isReadyForPopulation, final ConsolidatedPropertyView property) {
        boolean result = isReadyForPopulation;
        if (isReadyForPopulation && isOOPending(property)) {
            result = false;
        }
        return result;
    }

    private int getIncomingExtractsSize(final ExtractDetails extractDetails) {
        if (extractDetails == null) {
            return 0;
        }
        return extractDetails.getNumberOfIncomingExtracts();
    }

    private int getWebRateExtractsSize(final WebRateExtractDetails webRateExtractDetails) {
        if (webRateExtractDetails == null) {
            return 0;
        }
        return webRateExtractDetails.getNumberOfIncomingExtracts();
    }

    @SuppressWarnings("unchecked")
    private boolean isOOPending(final ConsolidatedPropertyView property) {
        boolean isOOPending = false;
        List<ConfigurationFileRecord> outOfOrderRecords = globalCrudService.findByNamedQuery(
                ConfigurationFileRecord.LATEST_BY_TYPE_AND_PROPERTY_AND_STATUS,
                QueryParameter.with("clientId", property.getClientId())
                        .and("propertyCode", property.getPropertyCode())
                        .and("recordType", PropertyConfigurationRecordType.OO.toString())
                        .and("configurationRecordStatus", ConfigurationFileRecordStatus.PENDING).parameters());
        if (outOfOrderRecords != null && !outOfOrderRecords.isEmpty()) {
            isOOPending = true;
        }

        return isOOPending;
    }
}
