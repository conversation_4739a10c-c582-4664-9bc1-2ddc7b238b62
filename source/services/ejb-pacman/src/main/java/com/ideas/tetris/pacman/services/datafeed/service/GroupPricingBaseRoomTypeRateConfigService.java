package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.services.datafeed.dto.grouppricing.BaseRoomTypeRateConfiguration;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by idnekp on 3/4/2016.
 */

@Component
@Transactional
public class GroupPricingBaseRoomTypeRateConfigService {

    private final String categorySeasonal = "Seasonal";
    private final String categoryDefault = "Default";
    @Autowired
    PricingConfigurationService pricingConfigurationService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;


    public List<BaseRoomTypeRateConfiguration> getGroupPricingBaseRoomTypeRateConfig(Date startDate) {
        List<BaseRoomTypeRateConfiguration> baseRoomTypeRateConfigurations = new ArrayList<>();
        List<PricingBaseAccomType> pricingBaseAccomTypes = pricingConfigurationService.getAllGroupPricingBaseAccomTypes();
        pricingBaseAccomTypes.stream().filter(pricingBaseAccomType -> pricingBaseAccomType.getEndDate() == null ||
                pricingBaseAccomType.getEndDate().isAfter(new org.joda.time.LocalDate(startDate).minusDays(1))).forEach(baseAccomType -> {
            BaseRoomTypeRateConfiguration baseRoomTypeRateConfiguration = new BaseRoomTypeRateConfiguration();
            baseRoomTypeRateConfiguration.setRoomTypeCode(baseAccomType.getAccomType().getAccomTypeCode());
            baseRoomTypeRateConfiguration.setGroupPricingCategory((baseAccomType.getStartDate() == null &&
                    baseAccomType.getEndDate() == null) ? categoryDefault : categorySeasonal);
            baseRoomTypeRateConfiguration.setSeasonName(baseAccomType.getSeasonName());
            if (pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)) {
                setFloorCeilingRatesForWeekdaysWithTax(baseAccomType, baseRoomTypeRateConfiguration);
            } else {
                setFloorCeilingRatesForWeekdaysWithoutTax(baseAccomType, baseRoomTypeRateConfiguration);
            }
            if (baseAccomType.getStartDate() != null) {
                baseRoomTypeRateConfiguration.setSeasonStartDate(baseAccomType.getStartDate().toDate());
                baseRoomTypeRateConfiguration.setSeasonEndDate(baseAccomType.getEndDate().toDate());
            }
            baseRoomTypeRateConfigurations.add(baseRoomTypeRateConfiguration);
        });
        return baseRoomTypeRateConfigurations;
    }

    private void setFloorCeilingRatesForWeekdaysWithTax(PricingBaseAccomType baseAccomType, BaseRoomTypeRateConfiguration baseRoomTypeRateConfiguration) {
        baseRoomTypeRateConfiguration.setSundayFloorRate(baseAccomType.getSundayFloorRateWithTax());
        baseRoomTypeRateConfiguration.setMondayFloorRate(baseAccomType.getMondayFloorRateWithTax());
        baseRoomTypeRateConfiguration.setTuesdayFloorRate(baseAccomType.getTuesdayFloorRateWithTax());
        baseRoomTypeRateConfiguration.setWednesdayFloorRate(baseAccomType.getWednesdayFloorRateWithTax());
        baseRoomTypeRateConfiguration.setThursdayFloorRate(baseAccomType.getThursdayFloorRateWithTax());
        baseRoomTypeRateConfiguration.setFridayFloorRate(baseAccomType.getFridayFloorRateWithTax());
        baseRoomTypeRateConfiguration.setSaturdayFloorRate(baseAccomType.getSaturdayFloorRateWithTax());

        baseRoomTypeRateConfiguration.setSundayCeilingRate(baseAccomType.getSundayCeilingRateWithTax());
        baseRoomTypeRateConfiguration.setMondayCeilingRate(baseAccomType.getMondayCeilingRateWithTax());
        baseRoomTypeRateConfiguration.setTuesdayCeilingRate(baseAccomType.getTuesdayCeilingRateWithTax());
        baseRoomTypeRateConfiguration.setWednesdayCeilingRate(baseAccomType.getWednesdayCeilingRateWithTax());
        baseRoomTypeRateConfiguration.setThursdayCeilingRate(baseAccomType.getThursdayCeilingRateWithTax());
        baseRoomTypeRateConfiguration.setFridayCeilingRate(baseAccomType.getFridayCeilingRateWithTax());
        baseRoomTypeRateConfiguration.setSaturdayCeilingRate(baseAccomType.getSaturdayCeilingRateWithTax());
    }

    private void setFloorCeilingRatesForWeekdaysWithoutTax(PricingBaseAccomType baseAccomType, BaseRoomTypeRateConfiguration baseRoomTypeRateConfiguration) {
        baseRoomTypeRateConfiguration.setSundayFloorRate(baseAccomType.getSundayFloorRate());
        baseRoomTypeRateConfiguration.setMondayFloorRate(baseAccomType.getMondayFloorRate());
        baseRoomTypeRateConfiguration.setTuesdayFloorRate(baseAccomType.getTuesdayFloorRate());
        baseRoomTypeRateConfiguration.setWednesdayFloorRate(baseAccomType.getWednesdayFloorRate());
        baseRoomTypeRateConfiguration.setThursdayFloorRate(baseAccomType.getThursdayFloorRate());
        baseRoomTypeRateConfiguration.setFridayFloorRate(baseAccomType.getFridayFloorRate());
        baseRoomTypeRateConfiguration.setSaturdayFloorRate(baseAccomType.getSaturdayFloorRate());

        baseRoomTypeRateConfiguration.setSundayCeilingRate(baseAccomType.getSundayCeilingRate());
        baseRoomTypeRateConfiguration.setMondayCeilingRate(baseAccomType.getMondayCeilingRate());
        baseRoomTypeRateConfiguration.setTuesdayCeilingRate(baseAccomType.getTuesdayCeilingRate());
        baseRoomTypeRateConfiguration.setWednesdayCeilingRate(baseAccomType.getWednesdayCeilingRate());
        baseRoomTypeRateConfiguration.setThursdayCeilingRate(baseAccomType.getThursdayCeilingRate());
        baseRoomTypeRateConfiguration.setFridayCeilingRate(baseAccomType.getFridayCeilingRate());
        baseRoomTypeRateConfiguration.setSaturdayCeilingRate(baseAccomType.getSaturdayCeilingRate());
    }
}
