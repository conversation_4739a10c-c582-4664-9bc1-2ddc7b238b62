package com.ideas.tetris.pacman.services.problem;

import com.ideas.tetris.pacman.services.problem.entity.ProblemView;
import com.ideas.tetris.pacman.services.problem.entity.SupportBulletin;
import com.ideas.tetris.pacman.services.problem.entity.SupportBulletinAction;
import com.ideas.tetris.platform.common.entity.AbstractCriteria;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import java.util.List;

public class SupportBulletinCriteria extends AbstractCriteria<SupportBulletin> {
    private boolean includeActiveBulletins;
    private boolean includeClosedBulletins;
    private Integer propertyId;
    private Integer errorCode;
    private String jobName;
    private String stepName;
    private List<SupportBulletinAction> actions;

    public SupportBulletinCriteria() {
        super(SupportBulletin.class);
    }

    public SupportBulletinCriteria(ProblemView problem) {
        this();
        includeActiveBulletins = true;
        includeClosedBulletins = false;
        errorCode = problem.getErrorCode().getId();
        jobName = problem.getJobName();
        stepName = problem.getStepName();
        propertyId = problem.getPropertyId();
    }

    public boolean isIncludeActiveBulletins() {
        return includeActiveBulletins;
    }

    public void setIncludeActiveBulletins(boolean includeActiveBulletins) {
        this.includeActiveBulletins = includeActiveBulletins;
    }

    public boolean isIncludeClosedBulletins() {
        return includeClosedBulletins;
    }

    public void setIncludeClosedBulletins(boolean includeClosedBulletins) {
        this.includeClosedBulletins = includeClosedBulletins;
    }

    public Integer getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Integer propertyId) {
        this.propertyId = propertyId;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getStepName() {
        return stepName;
    }

    public void setStepName(String stepName) {
        this.stepName = stepName;
    }

    public List<SupportBulletinAction> getActions() {
        return actions;
    }

    public void setActions(List<SupportBulletinAction> actions) {
        this.actions = actions;
    }

    @Override
    public DetachedCriteria getDetachedCriteria() {
        DetachedCriteria detachedCriteria = super.getDetachedCriteria();

        if (!includeActiveBulletins) {
            detachedCriteria.add(Restrictions.eq("active", false));
        }
        if (!includeClosedBulletins) {
            detachedCriteria.add(Restrictions.eq("active", true));
        }
        if (propertyId != null) {
            detachedCriteria.add(Restrictions.eq("propertyId", propertyId));
        }
        if (jobName != null) {
            detachedCriteria.add(Restrictions.eq("jobName", jobName));
        }
        if (stepName != null) {
            detachedCriteria.add(Restrictions.eq("stepName", stepName));
        }

        if (CollectionUtils.isNotEmpty(actions)) {
            detachedCriteria.add(Restrictions.in("action", actions));
        }

        return detachedCriteria;
    }

}
