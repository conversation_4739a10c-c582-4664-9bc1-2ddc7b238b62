package com.ideas.tetris.pacman.services.client.service;

import com.google.common.collect.Lists;
import com.ideas.infra.tetris.security.domain.AuthGroupRoleMapping;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.security.RoleService;
import com.ideas.tetris.pacman.services.security.UserService;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class ClientAgentConfigService {
    private static final Logger LOGGER = Logger.getLogger(ClientAgentConfigService.class);

    //TODO: should this be configurable?
    public static final String AGENT_ROLE = "G3Agent";
    public static final String AGENT_ROLE_DESCRIPTION = "Used to assign needed privileges for the G3 Agent to send and receive data from the G3 Server. Only minimal privileges are enabled for security reasons.";
    static final String AGENT_FIRST_NAME = "IDeaSAgent";
    public static final String AGENT_EMAIL_TMPL = "IDeaSAgent@{0}.com";
    static final String AGENT_PASSWORD = "GHJFhgjdei83^%$;";

    @Autowired
	private RoleService roleService;
    @Autowired
	private UserService userService;

    public Role addAgentRoleToNewClient(String clientCode) {
        // the role doesn't already exist since this can be run outside of the
        // context of "creating" a new client
        Role existingRole = roleService.findRoleByClientAndName(clientCode, AGENT_ROLE);
        if (existingRole == null) {
            LOGGER.info("Agent role not found for client " + clientCode + ", creating...");
            Role newRole = new Role();
            newRole.setRoleName(AGENT_ROLE);
            newRole.setDescription(AGENT_ROLE_DESCRIPTION);
            newRole.setClientCode(clientCode);
            newRole.setPermissions(new ArrayList<String>());
            existingRole = roleService.create(newRole, false);
        } else {
            LOGGER.info("Agent role found for client " + clientCode + ", updating...");
            existingRole.setCorporate(false);
            existingRole.setDescription(AGENT_ROLE_DESCRIPTION);
            existingRole.setRoleName(AGENT_ROLE);
            existingRole.setPermissions(new ArrayList<String>());
            existingRole = roleService.update(existingRole.getUniqueIdentifier(), existingRole);
        }
        return existingRole;
    }

    public Role deleteAgentRoleFromClient(String clientCode) {
        Role roleToDelete = roleService.findRoleByClientAndName(clientCode, AGENT_ROLE);
        if (roleToDelete != null) {
            LOGGER.info("Existing agent role found for client " + clientCode + ", deleting...");
            roleService.delete(roleToDelete.getUniqueIdentifier());
        }
        return roleToDelete;
    }

    public LDAPUser addAgentUserToNewClient(String clientCode) {
        String email = getAgentUserEmail(clientCode);

        Role agentRole = roleService.findRoleByClientAndName(clientCode, AGENT_ROLE);
        if (agentRole == null) {
            LOGGER.warn("Agent role was not found for client " + clientCode + " during user creation.  Creating the role before assigning to user.");
            agentRole = addAgentRoleToNewClient(clientCode);
        }

        LDAPUser existingUser = userService.getByEmailFromDB(email);
        if (existingUser == null) {
            LOGGER.info("Existing agent user not found for client " + clientCode + ", creating...");

            // FIXME: in order for user creation to work properly, we need to "simulate" the existence of a property this is necessary because of how property hierarchies work in pacman.
            boolean restore = setDummyPropertyCodeIfNeeded();

            LDAPUser newUser = new LDAPUser();
            updateUserDetail(newUser, email, clientCode, agentRole);
            existingUser = userService.create(newUser, false, AGENT_PASSWORD);

            //FIXME: continued
            restorePropertyCodeIfNeeded(restore);

        } else {
            LOGGER.info("Agent user already exists for client " + clientCode + ", updating...");
            updateUserDetail(existingUser, email, clientCode, agentRole);
            userService.update(existingUser.getUid(), existingUser);
        }

        return existingUser;
    }

    private boolean setDummyPropertyCodeIfNeeded() {
        if (PacmanWorkContextHelper.getPropertyCode() == null) {
            PacmanWorkContextHelper.setPropertyCode("DUMMY");
            return true;
        }
        return false;
    }

    private void restorePropertyCodeIfNeeded(boolean restoreNeeded) {
        if (restoreNeeded) {
            PacmanWorkContextHelper.setPropertyCode(null);
        }
    }

    private void updateUserDetail(LDAPUser user, String email, String clientCode, Role agentRole) {
        user.setAuthGroupRoles(Lists.newArrayList(new AuthGroupRoleMapping(agentRole.getUniqueIdentifier(), "-666")));
        user.setClient(clientCode);
        user.setActive(true);
        user.setIsCorporate(false);
        user.setGivenName(AGENT_FIRST_NAME);
        user.setMail(email);
        user.setHasSalesforceAccess(false);
        user.setCn(AGENT_FIRST_NAME + " " + clientCode);
        user.setSn(clientCode);
        user.setPasswordNeverExpire(true);
        user.setIntegrationUser(true);
        //FIXME this should not be necessary once password never expire is ok
        setLastPasswordChanged(user);
    }

    // set the last password changed to some future date to prevent it from ever expiring.
    // this should only be necessary as long as password expiration does not apply.
    private void setLastPasswordChanged(LDAPUser user) {
        Date lastPasswordChanged = DateUtils.addYears(new Date(), 100);
        DateFormat df = new SimpleDateFormat("yyyy-MMM-dd");
        user.setLastPasswordChanged(df.format(lastPasswordChanged));
    }

    private String getAgentUserEmail(String clientCode) {
        return MessageFormat.format(AGENT_EMAIL_TMPL, clientCode);
    }

    public LDAPUser deleteAgentUserFromClient(String clientCode) {
        LDAPUser existingUser = userService.getByEmailFromDB(getAgentUserEmail(clientCode));
        if (existingUser != null) {
            LOGGER.info("Agent user found for client " + clientCode + ", deleting...");
            userService.deleteById(existingUser.getUserId());
        }
        return existingUser;
    }

    @ForTesting
    public void setRoleService(RoleService roleService) {
        this.roleService = roleService;
    }

}
