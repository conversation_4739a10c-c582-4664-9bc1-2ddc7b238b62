package com.ideas.tetris.pacman.services.datafeed.dto.decisionconfiguration;

import lombok.*;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class FullDecisionScheduleDTO {
    private String propertyName;
    private String sellingSystemName;
    private String status;
    private String decisions;
    private String requestType;
    private String requestedBy;
    private String requestedOn;
    private Boolean isCancelScheduled;
}
