package com.ideas.tetris.pacman.services.security;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class UserSyncStats {
    private Integer totalUsersFromDB;
    private Integer totalUsersFromLDAP;
    private Integer passwordsUpdatedCount;
    private List<GlobalUser> missingGlobalUsers = new ArrayList<>();
    private Map<String, ClientWiseStats> clientWiseStats = new HashMap<>();
    private Set<String> migratedClient = new HashSet<>();

    public Map<String, ClientWiseStats> getClientWiseStats() {
        return clientWiseStats;
    }

    public List<GlobalUser> getMissingGlobalUsers() {
        return missingGlobalUsers;
    }

    public void addMissingGlobalUser(GlobalUser missingGlobalUserInLDAP) {
        missingGlobalUsers.add(missingGlobalUserInLDAP);
        updateClientStats(missingGlobalUserInLDAP);
    }

    public void addGlobalUserFoundInLDAP(GlobalUser userFoundInLDAP) {
        clientWiseStats.putIfAbsent(userFoundInLDAP.getClientCode(), new ClientWiseStats(userFoundInLDAP.getClientCode()));
        clientWiseStats.get(userFoundInLDAP.getClientCode()).setTotalUsersFoundInBoth(
                clientWiseStats.get(userFoundInLDAP.getClientCode()).getTotalUsersFoundInBoth() + 1
        );
    }

    private void updateClientStats(GlobalUser missingGlobalUserInLDAP) {
        clientWiseStats.putIfAbsent(missingGlobalUserInLDAP.getClientCode(), new ClientWiseStats(missingGlobalUserInLDAP.getClientCode()));
        clientWiseStats.get(missingGlobalUserInLDAP.getClientCode()).setMissingGlobalUsersInLDAP(
                clientWiseStats.get(missingGlobalUserInLDAP.getClientCode()).getMissingGlobalUsersInLDAP() + 1
        );
    }

    public Integer getTotalUsersFromDB() {
        return totalUsersFromDB;
    }

    public void setTotalUsersFromDB(Integer totalUsersFromDB) {
        this.totalUsersFromDB = totalUsersFromDB;
    }

    public Integer getTotalUsersFromLDAP() {
        return totalUsersFromLDAP;
    }

    public void setTotalUsersFromLDAP(Integer totalUsersFromLDAP) {
        this.totalUsersFromLDAP = totalUsersFromLDAP;
    }

    public Integer getPasswordsUpdatedCount() {
        return passwordsUpdatedCount;
    }

    public void setPasswordsUpdatedCount(Integer passwordsUpdatedCount) {
        this.passwordsUpdatedCount = passwordsUpdatedCount;
    }

    public void addToMigratedClient(String clientCode) {
        migratedClient.add(clientCode);
    }

    public Set<String> getMigratedClient() {
        return migratedClient;
    }

    public void setMigratedClient(Set<String> migratedClient) {
        this.migratedClient = migratedClient;
    }
}
