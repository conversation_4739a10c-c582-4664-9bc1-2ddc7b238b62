package com.ideas.tetris.pacman.services.forecast.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InhouseExtensionForecastDto {
    private Integer forecastGroupId;
    private Integer roomCategoryId;
    private LocalDate occupancyDt;
    private Integer inhouseExtensionSold;
    private Double inhouseExtensionRevenue;
}
