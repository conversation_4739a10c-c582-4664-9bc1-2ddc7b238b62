package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.services.property.dto.ExtractDetails;
import com.ideas.tetris.pacman.services.property.dto.PacmanExtractDetails;
import com.ideas.tetris.pacman.services.property.dto.WebRateExtractDetails;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;

import java.util.List;
import java.util.Map;

import org.springframework.aop.SpringProxy;
public interface ExtractMapperServiceLocal extends SpringProxy {

    Map<Integer, ExtractDetails> mapExtractsOnDiskAllProperties(List<Property> properties);

    Map<Integer, ExtractDetails> mapExtractsOnDisk(Integer propertyId);

    Map<Integer, ExtractDetails> mapExtractsOnDisk(Property property);

    Map<Integer, ExtractDetails> mapExtractsOnDisk(Integer propertyId, String clientCode, String propertyCode);  // used for newly added properties that won't show up in the ConsolidatedPropertyView yet

    Map<Integer, WebRateExtractDetails> mapWebRateExtractsOnDiskAllProperties(List<Property> properties);

    Map<Integer, WebRateExtractDetails> mapWebRateExtractsOnDisk(Integer propertyId);

    Map<Integer, WebRateExtractDetails> mapWebRateExtractsOnDisk(Property property);

    Map<Integer, WebRateExtractDetails> mapWebRateExtractsOnDisk(Integer propertyId, String clientCode, String propertyCode);  // used for newly added properties that won't show up in the ConsolidatedPropertyView yet

    Map<Integer, PacmanExtractDetails> mapPacmanExtractsOnDisk(Integer propertyId);
}
