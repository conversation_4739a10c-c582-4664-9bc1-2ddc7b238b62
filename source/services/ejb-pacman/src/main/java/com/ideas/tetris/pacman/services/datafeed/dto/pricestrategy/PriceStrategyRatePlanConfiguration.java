package com.ideas.tetris.pacman.services.datafeed.dto.pricestrategy;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by idnpda on 3/8/2016.
 */
public class PriceStrategyRatePlanConfiguration implements Serializable, Comparable<PriceStrategyRatePlanConfiguration> {

    private static final long serialVersionUID = 945379112629500948L;
    private String ratePlanName;
    private String roomTypeCode;
    private Date startDate;
    private Date endDate;
    private BigDecimal sunday;
    private BigDecimal monday;
    private BigDecimal tuesday;
    private BigDecimal wednesday;
    private BigDecimal thursday;
    private BigDecimal friday;
    private BigDecimal saturday;

    public String getRatePlanName() {
        return ratePlanName;
    }

    public void setRatePlanName(String ratePlanName) {
        this.ratePlanName = ratePlanName;
    }

    public String getRoomTypeCode() {
        return roomTypeCode;
    }

    public void setRoomTypeCode(String roomTypeCode) {
        this.roomTypeCode = roomTypeCode;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getSunday() {
        return sunday;
    }

    public void setSunday(BigDecimal sunday) {
        this.sunday = sunday;
    }

    public BigDecimal getMonday() {
        return monday;
    }

    public void setMonday(BigDecimal monday) {
        this.monday = monday;
    }

    public BigDecimal getTuesday() {
        return tuesday;
    }

    public void setTuesday(BigDecimal tuesday) {
        this.tuesday = tuesday;
    }

    public BigDecimal getWednesday() {
        return wednesday;
    }

    public void setWednesday(BigDecimal wednesday) {
        this.wednesday = wednesday;
    }

    public BigDecimal getThursday() {
        return thursday;
    }

    public void setThursday(BigDecimal thursday) {
        this.thursday = thursday;
    }

    public BigDecimal getFriday() {
        return friday;
    }

    public void setFriday(BigDecimal friday) {
        this.friday = friday;
    }

    public BigDecimal getSaturday() {
        return saturday;
    }

    public void setSaturday(BigDecimal saturday) {
        this.saturday = saturday;
    }

    @Override
    public int compareTo(PriceStrategyRatePlanConfiguration priceStrategyRatePlanConfiguration) {
        int ret = this.ratePlanName.compareTo(priceStrategyRatePlanConfiguration.getRatePlanName());
        if (ret != 0) {
            return ret;
        }
        ret = this.roomTypeCode.compareTo(priceStrategyRatePlanConfiguration.getRoomTypeCode());
        if (ret != 0) {
            return ret;
        }
        return this.startDate.compareTo(priceStrategyRatePlanConfiguration.getStartDate());
    }
}
