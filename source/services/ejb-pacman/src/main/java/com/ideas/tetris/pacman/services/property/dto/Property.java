package com.ideas.tetris.pacman.services.property.dto;

import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.property.configuration.dto.CatchupStatus;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.platform.common.entity.IdAwareEntity;
import com.ideas.tetris.platform.services.Stage;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@SuppressWarnings("serial")
public class Property extends IdAwareEntity<Integer> implements Comparable<Property> {

    private static final Logger LOGGER = Logger.getLogger(Property.class);

    private static final List<Stage> VALID_STAGES_FOR_CATCHUP =
            Arrays.asList(Stage.DATA_CAPTURE, Stage.CATCHUP, Stage.POPULATION, Stage.ONE_WAY);
    private static final String SYSTEM_MODE = ResourceUtil.getOptionalText("common.stage", Language.ENGLISH).orElse("System Mode");
    private Integer id;
    private String name;
    private String code;
    private String SFDCAcctNo;
    private String externalSystem;
    private String propertyTimeZone;
    private String crsTimeZone;
    private String webRateAlias;
    private String yieldCurrency;
    private String stage;
    private DateParameter srpAttributionExtractDate;
    private DateParameter srpAttributionSubmittedDate;
    private DateParameter setupCompletionDate;
    private DateParameter scheduledTwoWayDate;
    private DateParameter actualTwoWayDate;
    private DateParameter configFileLoadDate;
    private String configFileName;
    private boolean outOfOrderRecordsHaveBeenLoaded;
    private Integer pendingOORecordCount;
    private DateParameter systemDate;
    private CatchupData catchupData = new CatchupData();
    private boolean configurationComplete;
    private boolean eligibleForConfigure = true;
    private String reasonForConfigureNotEligible;
    private DateParameter stageChangedToCatchupDate;
    private DateParameter stageChangedToPopulationDate;
    private DateParameter stageChangedToOneWayDate;
    // previously from extractDetails
    private DateParameter historicalExtractDate;
    private List<DateParameter> missingDates = new ArrayList<DateParameter>();
    private int numberOfIncomingExtracts = 0;
    private int numberOfArchivedExtracts = 0;
    private DateParameter firstIncomingExtractDate;
    private DateParameter lastIncomingExtractDate;
    // previously from webRateExtractDetails
    private int numberOfIncomingWebRateExtracts = 0;
    private int numberOfArchivedWebRateExtracts = 0;
    private DateParameter firstIncomingWebRateExtractDate;
    private DateParameter lastIncomingWebRateExtractDate;

    private boolean ldb;
    private boolean isVirtualProperty;
    private String virtualPropertyDisplayCode;
    private String upsId;

    public Property() {
    }

    public Property(Integer id) {
        this.id = id;
    }

    public Property(Property property) {
        this.setId(property.getId());
        this.setName(property.getName());
        this.setCode(property.getCode());
        this.setSFDCAcctNo(property.getSFDCAcctNo());
        this.setExternalSystem(property.getExternalSystem());
        this.setPropertyTimeZone(property.getPropertyTimeZone());
        this.setCrsTimeZone(property.getCrsTimeZone());
        this.setWebRateAlias(property.getWebRateAlias());
        this.setYieldCurrency(property.getYieldCurrency());
        this.setStage(property.getStage());
        this.setSrpAttributionExtractDate(property.getSrpAttributionExtractDate());
        this.setSrpAttributionSubmittedDate(property.getSrpAttributionSubmittedDate());
        this.setSetupCompletionDate(property.getSetupCompletionDate());
        this.setScheduledTwoWayDate(property.getScheduledTwoWayDate());
        this.setActualTwoWayDate(property.getActualTwoWayDate());
        this.setConfigFileLoadDate(property.getConfigFileLoadDate());
        this.setConfigFileName(property.getConfigFileName());
        this.setOutOfOrderRecordsHaveBeenLoaded(property.isOutOfOrderRecordsHaveBeenLoaded());
        this.setPendingOORecordCount(property.getPendingOORecordCount());
        this.setSystemDate(property.getSystemDate());
        this.setCatchupData(new CatchupData(property.getCatchupData()));
        this.setConfigurationComplete(property.isConfigurationComplete());
        this.setEligibleForConfigure(property.isEligibleForConfigure());
        this.setReasonForConfigureNotEligible(property.getReasonForConfigureNotEligible());
        this.setStageChangedToCatchupDate(property.getStageChangedToCatchupDate());
        this.setStageChangedToPopulationDate(property.getStageChangedToPopulationDate());
        this.setStageChangedToOneWayDate(property.getStageChangedToOneWayDate());
        this.setHistoricalExtractDate(property.getHistoricalExtractDate());
        this.setMissingDates(property.getMissingDates());
        this.setNumberOfIncomingExtracts(property.getNumberOfIncomingExtracts());
        this.setNumberOfArchivedExtracts(property.getNumberOfArchivedExtracts());
        this.setFirstIncomingExtractDate(property.getFirstIncomingExtractDate());
        this.setLastIncomingExtractDate(property.getLastIncomingExtractDate());
        this.setNumberOfIncomingWebRateExtracts(property.getNumberOfIncomingWebRateExtracts());
        this.setNumberOfArchivedWebRateExtracts(property.getNumberOfArchivedWebRateExtracts());
        this.setFirstIncomingWebRateExtractDate(property.getFirstIncomingWebRateExtractDate());
        this.setLastIncomingWebRateExtractDate(property.getLastIncomingWebRateExtractDate());
        this.setLdb(property.isLdb());
        this.setVirtualProperty(property.isVirtualProperty());
        this.setVirtualPropertyDisplayCode(property.getVirtualPropertyDisplayCode());
        this.setUpsId(property.getUpsId());
    }

    public boolean isVirtualProperty() {
        return isVirtualProperty;
    }

    public void setVirtualProperty(boolean virtualProperty) {
        isVirtualProperty = virtualProperty;
    }

    public String getVirtualPropertyDisplayCode() {
        return virtualPropertyDisplayCode;
    }

    public void setVirtualPropertyDisplayCode(String virtualPropertyDisplayCode) {
        this.virtualPropertyDisplayCode = virtualPropertyDisplayCode;
    }

    public void setLdb(boolean ldb) {
        this.ldb = ldb;
    }

    public boolean isLdb() {
        return ldb;
    }

    @Override
    public Integer getId() {
        return id;
    }

    @Override
    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getSFDCAcctNo() {
        return SFDCAcctNo;
    }

    public void setSFDCAcctNo(String sFDCAcctNo) {
        SFDCAcctNo = sFDCAcctNo;
    }

    public DateParameter getSrpAttributionExtractDate() {
        return srpAttributionExtractDate;
    }

    public void setSrpAttributionExtractDate(DateParameter srpAttributionExtractDate) {
        this.srpAttributionExtractDate = srpAttributionExtractDate;
    }

    public DateParameter getSrpAttributionSubmittedDate() {
        return srpAttributionSubmittedDate;
    }

    public void setSrpAttributionSubmittedDate(
            DateParameter srpAttributionSubmittedDate) {
        this.srpAttributionSubmittedDate = srpAttributionSubmittedDate;
    }

    public DateParameter getSetupCompletionDate() {
        return setupCompletionDate;
    }

    public void setSetupCompletionDate(DateParameter setupCompletionDate) {
        this.setupCompletionDate = setupCompletionDate;
    }

    public DateParameter getScheduledTwoWayDate() {
        return scheduledTwoWayDate;
    }

    public void setScheduledTwoWayDate(DateParameter scheduledTwoWayDate) {
        this.scheduledTwoWayDate = scheduledTwoWayDate;
    }

    public DateParameter getActualTwoWayDate() {
        return actualTwoWayDate;
    }

    public void setActualTwoWayDate(DateParameter actualTwoWayDate) {
        this.actualTwoWayDate = actualTwoWayDate;
    }

    public DateParameter getConfigFileLoadDate() {
        return configFileLoadDate;
    }

    public void setConfigFileLoadDate(DateParameter configFileLoadDate) {
        this.configFileLoadDate = configFileLoadDate;
    }

    public String getExternalSystem() {
        return externalSystem;
    }

    public void setExternalSystem(String externalSystem) {
        this.externalSystem = externalSystem;
    }

    public String getPropertyTimeZone() {
        return propertyTimeZone;
    }

    public void setPropertyTimeZone(String propertyTimeZone) {
        this.propertyTimeZone = propertyTimeZone;
    }

    public String getWebRateAlias() {
        return webRateAlias;
    }

    public void setWebRateAlias(String webRateAlias) {
        this.webRateAlias = webRateAlias;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    public void verifyCatchupEligibility(ExtractDetails extractDetails, WebRateExtractDetails webRateExtractDetails) {
        deriveCatchupStartDate(extractDetails, webRateExtractDetails);
        deriveLatestPossibleCatchupEndDate(extractDetails, webRateExtractDetails);
    }

    public void verifyConfigureEligibility(ExtractDetails extractDetails, WebRateExtractDetails webRateExtractDetails) {
        if (stage == null) {
            setEligibleForConfigure(false);
            setReasonForConfigureNotEligible("Apply Configuration not allowed for null " + SYSTEM_MODE + " ");
        } else if (!stage.equals(Stage.CATCHUP.getCode())) {
            setEligibleForConfigure(false);
            String stageLabel = ResourceUtil.getOptionalText(stage.toLowerCase(), Language.ENGLISH).orElse(stage);
            setReasonForConfigureNotEligible("Apply Configuration not allowed for " + SYSTEM_MODE + " " + stageLabel);
        } else if (pendingOORecordCount > 0) {
            setEligibleForConfigure(false);
            setReasonForConfigureNotEligible("Out of Order records have not been processed for this property");
        } else if (getFirstIncomingExtractDate(extractDetails) != null) {
            setEligibleForConfigure(false);
            setReasonForConfigureNotEligible("This property has unprocessed CRS extracts");
        } else if (!isVirtualProperty && getFirstIncomingRSSExtractDate(webRateExtractDetails) != null) {
            setEligibleForConfigure(false);
            setReasonForConfigureNotEligible("This property has unprocessed RSS extracts");
        } else if (upsId == null) {
            setEligibleForConfigure(false);
            setReasonForConfigureNotEligible("This property does not have a UPS ID defined");
        } else {
            setEligibleForConfigure(true);
            setReasonForConfigureNotEligible(null);
        }
    }

    private void deriveCatchupStartDate(ExtractDetails extractDetails, WebRateExtractDetails webRateExtractDetails) {
        catchupData.setEligibleForCatchup(false);
        catchupData.setExtractStartDate(null);

        Stage deriveStage = deriveStage();

        // validate the stage
        if (deriveStage == null) {
            catchupData.setReasonForCatchupNotEligible("Could not detect the Stage. Cannot run Data Mapping.");
            return;
        }
        String stageLabel = ResourceUtil.getOptionalText(deriveStage.getCode().toLowerCase(), Language.ENGLISH).orElse(deriveStage.getCode());

        if (!VALID_STAGES_FOR_CATCHUP.contains(deriveStage)) {
            catchupData.setReasonForCatchupNotEligible("Cannot run Data Mapping when the stage is: " + stageLabel);
            return;
        }

        // make sure there are extracts to process
        Date firstIncomingDate = getFirstIncomingExtractDate(extractDetails);
        if (firstIncomingDate == null && getFirstIncomingRSSExtractDate(webRateExtractDetails) == null) {
            catchupData.setReasonForCatchupNotEligible("Could not find CRS or RSS extracts in the incoming folder. Cannot run Data Mapping.");
            return;
        }

        // validate the state of the history and mcat+ files
        if (validateHistoryAndMCatFiles(extractDetails, deriveStage, stageLabel, firstIncomingDate)) {
            return;
        }

        // everything is good so far...now see if a catchup is currently in progress
        validateIfDataMappingInProgress();

        // finally, derive the start date
        setStartDate(webRateExtractDetails, firstIncomingDate);
    }

    private boolean validateHistoryAndMCatFiles(ExtractDetails extractDetails, Stage deriveStage, String stageLabel, Date firstIncomingDate) {
        if (Stage.DATA_CAPTURE.equals(deriveStage)) {
            if (srpAttributionExtractDate == null) {
                catchupData.setReasonForCatchupNotEligible("When the stage is " + stageLabel +
                        " scheduled MCAT+ extract date needs to be set & the history extract should be present in incoming folder. Cannot run Data Mapping.");
                return true;
            }
            if (getHistoricalExtractDate() != null && firstIncomingDate != null && getHistoricalExtractDate().getMillis() != firstIncomingDate.getTime()) {
                catchupData.setReasonForCatchupNotEligible("When the stage is: " + stageLabel + " history extract is expected in the incoming folder.");
                return true;
            }
            if (extractDetails == null || !extractDetails.isSrpAttributeExtractAvailable()) {
                catchupData.setReasonForCatchupNotEligible("Could not find MCAT+ extract coresponding to date: " + srpAttributionExtractDate.toFormattedString());
                return true;
            }
        } else {
            if (getHistoricalExtractDate() != null && firstIncomingDate != null && getHistoricalExtractDate().getMillis() == firstIncomingDate.getTime()) {
                catchupData.setReasonForCatchupNotEligible("The current stage is: " + stageLabel + " and the historical extract is present in incoming folder. This is not expected. Something is wrong. Cannot run Data Mapping.");
                return true;
            }
        }
        return false;
    }

    private void validateIfDataMappingInProgress() {
        if (catchupData.getStatus() != null && catchupData.getStatus().equals(CatchupStatus.IN_PROGRESS)) {
            catchupData.setReasonForCatchupNotEligible("Data Mapping is already in progress for this property. Cannot run Data Mapping.");
            catchupData.setEligibilityOverridable(true);
        } else {
            catchupData.setEligibleForCatchup(true);
        }
    }

    private void setStartDate(WebRateExtractDetails webRateExtractDetails, Date firstIncomingDate) {
        if (firstIncomingDate == null) {
            catchupData.setExtractStartDate(getFirstIncomingRSSExtractDate(webRateExtractDetails));
        } else if (getFirstIncomingRSSExtractDate(webRateExtractDetails) == null) {
            catchupData.setExtractStartDate(firstIncomingDate);
        } else {
            catchupData.setExtractStartDate(firstIncomingDate.before(getFirstIncomingRSSExtractDate(webRateExtractDetails)) ? firstIncomingDate : getFirstIncomingRSSExtractDate(webRateExtractDetails));
        }
    }

    private void deriveLatestPossibleCatchupEndDate(ExtractDetails extractDetails, WebRateExtractDetails webRateExtractDetails) {
        catchupData.setExtractEndDate(null);
        if (catchupData.getExtractStartDate() == null) {
            return;
        } else {
            Date lastIncomingDate = getLastIncomingExtractDate(extractDetails);
            if (lastIncomingDate == null) {
                catchupData.setExtractEndDate(getLastIncomingRSSExtractDate(webRateExtractDetails));
            } else if (getLastIncomingRSSExtractDate(webRateExtractDetails) == null) {
                catchupData.setExtractEndDate(lastIncomingDate);
            } else {
                catchupData.setExtractEndDate(lastIncomingDate.after(getLastIncomingRSSExtractDate(webRateExtractDetails)) ? lastIncomingDate : getLastIncomingRSSExtractDate(webRateExtractDetails));
            }
        }
    }

    private Stage deriveStage() {
        Stage derivedStage = null;
        if (getStage() != null) {
            try {
                derivedStage = Stage.valueForCode(getStage());
            } catch (IllegalArgumentException e) {
                LOGGER.warn("Stage could not be derived", e);
            }
        }
        return derivedStage;
    }

    public DateParameter getHistoricalExtractDate() {
        return historicalExtractDate;
    }

    public void setHistoricalExtractDate(DateParameter historicalExtractDate) {
        this.historicalExtractDate = historicalExtractDate;
    }

    private Date getFirstIncomingExtractDate(ExtractDetails extractDetails) {
        if (extractDetails == null || extractDetails.getFirstIncomingExtractDate() == null) {
            return null;
        }
        return extractDetails.getFirstIncomingExtractDate().getTime();
    }

    private Date getLastIncomingExtractDate(ExtractDetails extractDetails) {
        if (extractDetails == null || extractDetails.getLastIncomingExtractDate() == null) {
            return null;
        }
        return extractDetails.getLastIncomingExtractDate().getTime();
    }

    private Date getFirstIncomingRSSExtractDate(WebRateExtractDetails webRateExtractDetails) {
        if (webRateExtractDetails == null || webRateExtractDetails.getFirstIncomingExtractDate() == null) {
            return null;
        }
        return webRateExtractDetails.getFirstIncomingExtractDate().getTime();
    }

    private Date getLastIncomingRSSExtractDate(WebRateExtractDetails webRateExtractDetails) {
        if (webRateExtractDetails == null || webRateExtractDetails.getLastIncomingExtractDate() == null) {
            return null;
        }
        return webRateExtractDetails.getLastIncomingExtractDate().getTime();
    }

    public boolean isOutOfOrderRecordsHaveBeenLoaded() {
        return outOfOrderRecordsHaveBeenLoaded;
    }

    public void setOutOfOrderRecordsHaveBeenLoaded(boolean outOfOrderRecordsHaveBeenLoaded) {
        this.outOfOrderRecordsHaveBeenLoaded = outOfOrderRecordsHaveBeenLoaded;
    }

    public Integer getPendingOORecordCount() {
        return pendingOORecordCount;
    }

    public void setPendingOORecordCount(Integer pendingOORecordCount) {
        this.pendingOORecordCount = pendingOORecordCount;
    }

    public String getConfigFileName() {
        return configFileName;
    }

    public void setConfigFileName(String configFileName) {
        this.configFileName = configFileName;
    }

    public DateParameter getSystemDate() {
        return systemDate;
    }

    public void setSystemDate(DateParameter systemDate) {
        this.systemDate = systemDate;
    }

    public CatchupData getCatchupData() {
        return catchupData;
    }

    public void setCatchupData(CatchupData catchupData) {
        if (catchupData == null) {
            this.catchupData = new CatchupData();
        } else {
            this.catchupData = catchupData;
        }
    }

    @Override
    public int compareTo(Property candidate) {
        return getCode().compareTo(candidate.getCode());
    }

    public boolean isConfigurationComplete() {
        return configurationComplete;
    }

    public void setConfigurationComplete(boolean configurationComplete) {
        this.configurationComplete = configurationComplete;
    }

    public boolean isEligibleForConfigure() {
        return eligibleForConfigure;
    }

    public void setEligibleForConfigure(boolean eligibleForConfigure) {
        this.eligibleForConfigure = eligibleForConfigure;
    }

    public String getReasonForConfigureNotEligible() {
        return reasonForConfigureNotEligible;
    }

    public void setReasonForConfigureNotEligible(String reasonForConfigureNotEligible) {
        this.reasonForConfigureNotEligible = reasonForConfigureNotEligible;
    }

    public DateParameter getStageChangedToCatchupDate() {
        return stageChangedToCatchupDate;
    }

    public void setStageChangedToCatchupDate(DateParameter stageChangedToCatchupDate) {
        this.stageChangedToCatchupDate = stageChangedToCatchupDate;
    }

    public DateParameter getStageChangedToPopulationDate() {
        return stageChangedToPopulationDate;
    }

    public void setStageChangedToPopulationDate(DateParameter stageChangedToPopulationDate) {
        this.stageChangedToPopulationDate = stageChangedToPopulationDate;
    }

    public DateParameter getStageChangedToOneWayDate() {
        return stageChangedToOneWayDate;
    }

    public void setStageChangedToOneWayDate(DateParameter stageChangedToOneWayDate) {
        this.stageChangedToOneWayDate = stageChangedToOneWayDate;
    }

    public List<DateParameter> getMissingDates() {
        return missingDates;
    }

    public void setMissingDates(List<DateParameter> missingDates) {
        this.missingDates = missingDates;
    }

    public int getNumberOfIncomingExtracts() {
        return numberOfIncomingExtracts;
    }

    public void setNumberOfIncomingExtracts(int numberOfIncomingExtracts) {
        this.numberOfIncomingExtracts = numberOfIncomingExtracts;
    }

    public int getNumberOfArchivedExtracts() {
        return numberOfArchivedExtracts;
    }

    public void setNumberOfArchivedExtracts(int numberOfArchivedExtracts) {
        this.numberOfArchivedExtracts = numberOfArchivedExtracts;
    }

    public int getNumberOfIncomingWebRateExtracts() {
        return numberOfIncomingWebRateExtracts;
    }

    public void setNumberOfIncomingWebRateExtracts(int numberOfIncomingWebRateExtracts) {
        this.numberOfIncomingWebRateExtracts = numberOfIncomingWebRateExtracts;
    }

    public int getNumberOfArchivedWebRateExtracts() {
        return numberOfArchivedWebRateExtracts;
    }

    public void setNumberOfArchivedWebRateExtracts(int numberOfArchivedWebRateExtracts) {
        this.numberOfArchivedWebRateExtracts = numberOfArchivedWebRateExtracts;
    }

    public DateParameter getFirstIncomingExtractDate() {
        return firstIncomingExtractDate;
    }

    public void setFirstIncomingExtractDate(DateParameter firstIncomingExtractDate) {
        this.firstIncomingExtractDate = firstIncomingExtractDate;
    }

    public DateParameter getLastIncomingExtractDate() {
        return lastIncomingExtractDate;
    }

    public void setLastIncomingExtractDate(DateParameter lastIncomingExtractDate) {
        this.lastIncomingExtractDate = lastIncomingExtractDate;
    }

    public DateParameter getFirstIncomingWebRateExtractDate() {
        return firstIncomingWebRateExtractDate;
    }

    public void setFirstIncomingWebRateExtractDate(
            DateParameter firstIncomingWebRateExtractDate) {
        this.firstIncomingWebRateExtractDate = firstIncomingWebRateExtractDate;
    }

    public DateParameter getLastIncomingWebRateExtractDate() {
        return lastIncomingWebRateExtractDate;
    }

    public void setLastIncomingWebRateExtractDate(
            DateParameter lastIncomingWebRateExtractDate) {
        this.lastIncomingWebRateExtractDate = lastIncomingWebRateExtractDate;
    }

    public String getCrsTimeZone() {
        return crsTimeZone;
    }

    public void setCrsTimeZone(String crsTimeZone) {
        this.crsTimeZone = crsTimeZone;
    }

    public String getYieldCurrency() {
        return yieldCurrency;
    }

    public void setYieldCurrency(String yieldCurrency) {
        this.yieldCurrency = yieldCurrency;
    }

    public String getUpsId() {
        return upsId;
    }

    public void setUpsId(String upsId) {
        this.upsId = upsId;
    }
}
