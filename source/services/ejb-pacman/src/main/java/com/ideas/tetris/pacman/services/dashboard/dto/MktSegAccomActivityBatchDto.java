package com.ideas.tetris.pacman.services.dashboard.dto;

import com.ideas.tetris.platform.common.crudservice.TableBatch;
import com.ideas.tetris.platform.common.crudservice.TableBatchAware;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.Date;
import java.util.Objects;

@Builder
@Data
public class MktSegAccomActivityBatchDto implements TableBatchAware {
    public static final String USP_ACTIVITY_RAW_INSERT = "usp_Mkt_Accom_Activity_Raw_Insert";
    public static final String USP_MKT_SEG_ACTIVITY_RAW_UPDATE = "USP_Mkt_Seg_Activity_Raw_Update";
    public static final String ACTIVITY_RAW_BATCH = "Mkt_Accom_Activity_Raw_Batch";

    private Integer mktSegAccomActivityId;
    private Integer accomTypeId;
    private Integer propertyId;
    private Integer fileMetadataId;
    private Integer mktSegId;
    private BigDecimal roomsSold;
    private BigDecimal arrivals;
    private BigDecimal departures;
    private BigDecimal cancellations;
    private BigDecimal noShows;
    private BigDecimal roomRevenue;
    private BigDecimal foodRevenue;
    private BigDecimal totalRevenue;
    private BigDecimal pseudoRoomRevenue;
    private BigDecimal totalProfit;
    private Date createDate;
    private Date occupancyDate;
    private Date snapShotDate;
    private Date lastUpdatedDate;
    private boolean isCPD;

    @Override
    public String getTableVariableName() {
        return ACTIVITY_RAW_BATCH;
    }

    @Override
    public void addTableBatchColumns(TableBatch tableBatch) {
        tableBatch.addColumn("Mkt_Accom_Activity_ID", Types.BIGINT);
        tableBatch.addColumn("Accom_Type_ID", Types.BIGINT);
        tableBatch.addColumn("Property_ID", Types.BIGINT);
        tableBatch.addColumn("File_Metadata_ID", Types.BIGINT);
        tableBatch.addColumn("Mkt_Seg_ID", Types.BIGINT);
        tableBatch.addColumn("Rooms_Sold", Types.NUMERIC);
        tableBatch.addColumn("Arrivals", Types.NUMERIC);
        tableBatch.addColumn("Departures", Types.NUMERIC);
        tableBatch.addColumn("Cancellations", Types.NUMERIC);
        tableBatch.addColumn("No_Shows", Types.NUMERIC);
        tableBatch.addColumn("Room_Revenue", Types.NUMERIC);
        tableBatch.addColumn("Food_Revenue", Types.NUMERIC);
        tableBatch.addColumn("Total_Revenue", Types.NUMERIC);
        tableBatch.addColumn("Pseudo_Room_Revenue", Types.NUMERIC);
        tableBatch.addColumn("Total_Profit", Types.NUMERIC);
        tableBatch.addColumn("CreateDate", Types.TIMESTAMP);
        tableBatch.addColumn("Occupancy_DT", Types.DATE);
        tableBatch.addColumn("SnapShot_DTTM", Types.TIMESTAMP);
        tableBatch.addColumn("Last_Updated_DTTM", Types.TIMESTAMP);
        tableBatch.addColumn("Is_Cpd", Types.VARCHAR);
    }

    @Override
    public Object[] toTableBatchRow() {
        return new Object[]{getMktSegAccomActivityId(),
                getAccomTypeId(),
                getPropertyId(),
                getFileMetadataId(),
                getMktSegId(),
                getRoomsSold(),
                getArrivals(),
                getDepartures(),
                getCancellations(),
                getNoShows(),
                getRoomRevenue(),
                getFoodRevenue(),
                getTotalRevenue(),
                getTotalProfit(),
                getPseudoRoomRevenue(),
                Objects.nonNull(getCreateDate()) ? new Timestamp(getCreateDate().getTime()) : new Timestamp(System.currentTimeMillis()),
                Objects.nonNull(getOccupancyDate()) ? new java.sql.Date(getOccupancyDate().getTime()) : null,
                Objects.nonNull(getSnapShotDate()) ? new Timestamp(getSnapShotDate().getTime()) : new Timestamp(System.currentTimeMillis()),
                Objects.nonNull(getLastUpdatedDate()) ? new Timestamp(getLastUpdatedDate().getTime()) : new Timestamp(System.currentTimeMillis()),
                isCPD() ? "1" : "0"
        };
    }

    @Override
    public String getInsertStoredProcedureName() {
        return USP_ACTIVITY_RAW_INSERT;
    }
}
