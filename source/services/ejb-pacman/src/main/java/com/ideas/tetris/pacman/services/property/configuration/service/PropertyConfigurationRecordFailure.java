package com.ideas.tetris.pacman.services.property.configuration.service;

import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecordStatus;

public class PropertyConfigurationRecordFailure {

    private ConfigurationFileRecordStatus status;

    private String message;

    public PropertyConfigurationRecordFailure(String message) {
        this(ConfigurationFileRecordStatus.FAILED, message);
    }

    public PropertyConfigurationRecordFailure(ConfigurationFileRecordStatus status, String message) {
        this.status = status;
        this.message = message;
    }

    public ConfigurationFileRecordStatus getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

}
