package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.tetris.pacman.services.bestavailablerate.entity.HotelMktSegAccomActivity;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import javax.inject.Inject;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class HotelMarketAccomActivityService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;


    public boolean isHotelMarketActivityPresent() {
        return tenantCrudService.findOne(HotelMktSegAccomActivity.class) != null;
    }


    public int updateNonLatestValuesToZero(int fileMetadataId) {
        Map<String, Object> params = QueryParameter.with("fileMetadataId", fileMetadataId).parameters();
        return tenantCrudService.executeUpdateByNamedQuery(HotelMktSegAccomActivity.ZERO_FILL_NON_LATEST_BY_FILE_METADATA, params);
    }

}
