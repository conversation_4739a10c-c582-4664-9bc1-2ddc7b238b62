package com.ideas.tetris.pacman.services.client.service;

public class RealmConfig {
    private Integer clientId;
    private String clientCode;
    private String clientName;
    private boolean usesOpenDS;
    private boolean openDSFeatureToggle;

    public Integer getClientId() {
        return clientId;
    }

    public void setClientId(Integer clientId) {
        this.clientId = clientId;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public boolean isUsesOpenDS() {
        return usesOpenDS;
    }

    public void setUsesOpenDS(boolean usesOpenDS) {
        this.usesOpenDS = usesOpenDS;
    }

    public boolean isOpenDSFeatureToggle() {
        return openDSFeatureToggle;
    }

    public void setOpenDSFeatureToggle(boolean openDSFeatureToggle) {
        this.openDSFeatureToggle = openDSFeatureToggle;
    }


}
