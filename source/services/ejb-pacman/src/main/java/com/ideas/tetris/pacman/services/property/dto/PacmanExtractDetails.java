package com.ideas.tetris.pacman.services.property.dto;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

@SuppressWarnings("serial")
public class PacmanExtractDetails implements Serializable {
    public static final int FILE_NAME_SEGMENTS = 6;
    public static final String FILE_PART_T2SNAP = "T2SNAP";
    public static final String FILE_PART_ZIP = "zip";
    public static final String FILE_DATE_FORMAT = "yyyyMMdd";

    public static final String T2SNAP_REGEX_PATTERN = "{0}_{1}_\\d{8}_\\d+_t2snap\\.zip";
    public static final Pattern T2SNAP_DATE_PATTERN = Pattern.compile("\\d{8}");

    private List<String> archivedExtractPaths = new ArrayList<String>();

    public PacmanExtractDetails() {
    }

    public List<String> getArchivedExtractPaths() {
        return archivedExtractPaths;
    }

    public List<String> getRecentExtracts(int numberOfExtracts) {
        Collections.sort(archivedExtractPaths);
        List<String> extracts = new ArrayList<String>();
        if (!archivedExtractPaths.isEmpty()) {
            int endIndex = archivedExtractPaths.size();
            int startIndex = 0;
            if (archivedExtractPaths.size() >= numberOfExtracts) {
                startIndex = endIndex - numberOfExtracts;
            }
            extracts.addAll(archivedExtractPaths.subList(startIndex, endIndex));
        }
        return extracts;
    }

    public void addExtract(File file) {
        addArchivedExtract(file);
    }

    public void addArchivedExtract(File file) {
        archivedExtractPaths.add(file.getPath());
    }

    public void copyAchivedExtractsToDirectory(File directory) throws IOException {
        for (String extract : getArchivedExtractPaths()) {
            FileUtils.copyFileToDirectory(new File(extract), directory);
        }
    }
}
