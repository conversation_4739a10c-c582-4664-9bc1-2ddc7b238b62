package com.ideas.tetris.pacman.services.reports.pricingoverridehistory.dto;

import com.ideas.tetris.pacman.services.scheduledreport.reportapi.annotations.ColumnHeader;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.datatypes.PropertyValueType;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Date;

public class PricingOverrideHistoryDTO {
    public static final String HYPHEN = " -- ";
    @ColumnHeader(titleKey = "property", order = 1)
    private String propertyName;
    @ColumnHeader(titleKey = "report.dow", order = 2, type = PropertyValueType.class)
    private String dow;
    @ColumnHeader(titleKey = "common.arrivalDate", order = 3, pattern = "dd-MMM-yyyy")
    private Date arrivalDate;
    @ColumnHeader(titleKey = "room.class", order = 4)
    private String accomClassName;
    @ColumnHeader(titleKey = "roomtype.table.header.roomtype", order = 7, condition = "isContinuousPricing")
    private String roomTypeName;
    @ColumnHeader(titleKey = "report.los", order = 6, condition = "isBarByLosProperty")
    private Integer los;
    private String newOverride;
    private String rateCodeName;
    @ColumnHeader(titleKey = "common.userName", order = 10)
    private String userName;
    @ColumnHeader(titleKey = "pricingOverrideHistoryReport.overrideLastModifiedOn", order = 11, pattern = "dd-MMM-yyyy HH:mm:ss z")
    private ZonedDateTime createDate;
    private String oldRateCodeName;
    private String oldOverride;
    @ColumnHeader(titleKey = "notes.label", order = 5)
    private String notes;
    private BigDecimal newBarRate;
    private BigDecimal oldBarRate;
    private String newCeilRateCodeName;
    private String oldCeilRateCodeName;
    private BigDecimal newCeilBarRate;
    private BigDecimal oldCeilBarRate;
    private BigDecimal newFloorRate;
    private BigDecimal oldFloorRate;
    @ColumnHeader(titleKey = "pricingOverrideHistoryReport.newDecision", order = 8)
    private String newDecisionDisplay;
    @ColumnHeader(titleKey = "pricingOverrideHistoryReport.oldDecision", order = 9)
    private String oldDecisionDisplay;

    public String getNewCeilRateCodeName() {
        return newCeilRateCodeName;
    }

    public void setNewCeilRateCodeName(String newCeilRateCodeName) {
        this.newCeilRateCodeName = newCeilRateCodeName;
    }

    public String getOldCeilRateCodeName() {
        return oldCeilRateCodeName;
    }

    public void setOldCeilRateCodeName(String oldCeilRateCodeName) {
        this.oldCeilRateCodeName = oldCeilRateCodeName;
    }

    public BigDecimal getNewCeilBarRate() {
        return newCeilBarRate.setScale(2, BigDecimal.ROUND_FLOOR);
    }

    public void setNewCeilBarRate(BigDecimal newCeilBarRate) {
        this.newCeilBarRate = newCeilBarRate;
    }

    public BigDecimal getOldCeilBarRate() {
        return oldCeilBarRate.setScale(2, BigDecimal.ROUND_FLOOR);
    }

    public void setOldCeilBarRate(BigDecimal oldCeilBarRate) {
        this.oldCeilBarRate = oldCeilBarRate;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public String getDow() {
        return dow;
    }

    public void setDow(String dow) {
        this.dow = dow;
    }

    public Date getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public String getAccomClassName() {
        return accomClassName;
    }

    public void setRoomTypeName(String roomTypeName) {
        this.roomTypeName = roomTypeName;
    }

    public String getRoomTypeName() {
        return roomTypeName;
    }

    public void setAccomClassName(String accomClassName) {
        this.accomClassName = accomClassName;
    }

    public Integer getLos() {
        return los;
    }

    public void setLos(Integer los) {
        this.los = los;
    }

    public String getNewOverride() {
        return newOverride;
    }

    public void setNewOverride(String newOverride) {
        this.newOverride = newOverride;
    }

    public String getRateCodeName() {
        return rateCodeName;
    }

    public void setRateCodeName(String rateCodeName) {
        this.rateCodeName = rateCodeName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public ZonedDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(ZonedDateTime createDate) {
        this.createDate = createDate;
    }

    public String getOldRateCodeName() {
        return oldRateCodeName;
    }

    public void setOldRateCodeName(String oldRateCodeName) {
        this.oldRateCodeName = oldRateCodeName;
    }

    public String getOldOverride() {
        return oldOverride;
    }

    public void setOldOverride(String oldOverride) {
        this.oldOverride = oldOverride;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getNewBarRate() {
        return newBarRate == null ? null : newBarRate.setScale(2, BigDecimal.ROUND_FLOOR);
    }

    public void setNewBarRate(BigDecimal newBarRate) {
        this.newBarRate = newBarRate;
    }

    public BigDecimal getOldFloorRate() {
        return oldFloorRate.setScale(2, BigDecimal.ROUND_FLOOR);
    }

    public void setOldFloorRate(BigDecimal oldFloorRate) {
        this.oldFloorRate = oldFloorRate;
    }

    public BigDecimal getNewFloorRate() {
        return newFloorRate.setScale(2, BigDecimal.ROUND_FLOOR);
    }

    public void setNewFloorRate(BigDecimal newFloorRate) {
        this.newFloorRate = newFloorRate;
    }

    public BigDecimal getOldBarRate() {
        return oldBarRate == null ? null : oldBarRate.setScale(2, BigDecimal.ROUND_FLOOR);
    }

    public void setOldBarRate(BigDecimal oldBarRate) {
        this.oldBarRate = oldBarRate;
    }

    public String getFloorDecisionForNewOverride() {
        return getRateCodeName() + " F" + "\n(" + getNewBarRate() + ")";
    }

    public String getCeilDecisionForNewOverride() {
        return getNewCeilRateCodeName() + " C" + "\n(" + getNewCeilBarRate() + ")";
    }

    public String getFloorAndCeilDecisionForNewOverride() {

        return getFloorDecisionForNewOverride() + ",\n" + getCeilDecisionForNewOverride();
    }

    public String getUserDecisionForNewOverride() {
        return getRateCodeName() + " S" + "\n(" + getNewBarRate() + ")";
    }

    public String getPendingDecisionForNewOverride() {
        return getRateCodeName() + " R" + "\n(" + getNewBarRate() + ")";
    }

    public String getDecisionForNewOverride() {
        if (getRateCodeName() == null && getNewCeilRateCodeName() == null) {
            return HYPHEN;
        }

        return "floorandceil".equalsIgnoreCase(getNewOverride()) ? getFloorAndCeilDecisionForNewOverride()
                : "user".equalsIgnoreCase(getNewOverride()) ? getUserDecisionForNewOverride()
                : "floor".equalsIgnoreCase(getNewOverride()) ? getFloorDecisionForNewOverride()
                : "ceil".equalsIgnoreCase(getNewOverride()) ? getCeilDecisionForNewOverride()
                : "pending".equalsIgnoreCase(getNewOverride()) ? getPendingDecisionForNewOverride()
                : "";
    }

    public String getDecisionForOldOverride() {
        if (getOldRateCodeName() == null && getOldCeilRateCodeName() == null) {
            return HYPHEN;
        }

        return "floorandceil".equalsIgnoreCase(getOldOverride()) ? getFloorAndCeilDecisionForOldOverride()
                : "user".equalsIgnoreCase(getOldOverride()) ? getUserDecisionForOldOverride()
                : "floor".equalsIgnoreCase(getOldOverride()) ? getFloorDecisionForOldOverride()
                : "ceil".equalsIgnoreCase(getOldOverride()) ? getCeilDecisionForOldOverride()
                : "pending".equalsIgnoreCase(getOldOverride()) ? getPendingDecisionForOldOverride()
                : "none".equalsIgnoreCase(getOldOverride()) ? getNoneDecisionForOldOverride()
                : "";
    }

    public String getDecisionForNewOverrideForCP() {
        return "floorandceil".equalsIgnoreCase(getNewOverride()) ? getNewFloorAndCeilForCP()
                : "user".equalsIgnoreCase(getNewOverride()) ? getNewBarRateForCP() + " - U"
                : "floor".equalsIgnoreCase(getNewOverride()) ? getNewFloorForCP()
                : "ceil".equalsIgnoreCase(getNewOverride()) ? getNewCeilForCP()
                : "pending".equalsIgnoreCase(getNewOverride()) ? getNewPendingForCp()
                : "";
    }

    public String getDecisionForOldOverrideForCP() {
        return "floorandceil".equalsIgnoreCase(getOldOverride()) ? getOldFloorAndCeilForCP()
                : "user".equalsIgnoreCase(getOldOverride()) ? getOldBarRateForCP() + " - U"
                : "floor".equalsIgnoreCase(getOldOverride()) ? getOldFloorForCP()
                : "ceil".equalsIgnoreCase(getOldOverride()) ? getOldCeilForCP()
                : "pending".equalsIgnoreCase(getOldOverride()) ? getOldBarRate() == null ? "" : "R(" + getOldBarRate() + ")"
                : "none".equalsIgnoreCase(getOldOverride()) ? getOldBarRateForCP()
                : "";
    }

    private String getOldBarRateForCP() {
        return getOldBarRate() == null ? "" : "" + getOldBarRate();
    }

    private String getNewBarRateForCP() {
        return getNewBarRate() == null ? "" : "" + getNewBarRate();
    }

    private String getOldFloorAndCeilForCP() {
        String s = "";
        s = s + getOldBarRateForCP();
        s = s + getOldFloorRate() == null ? "" : "\n" + getOldFloorRate() + " - F";
        s = s + getOldCeilBarRate() == null ? "" : "\n" + getOldCeilBarRate() + " - C";
        return s;
    }

    private String getNewFloorAndCeilForCP() {
        String s = "";
        s = s + getNewBarRateForCP();
        s = s + getNewFloorRate() == null ? "" : "\n" + getNewFloorRate() + " - F";
        s = s + getNewCeilBarRate() == null ? "" : "\n" + getNewCeilBarRate() + " - C";
        return s;
    }

    private String getOldFloorForCP() {
        String s = "";
        s = s + getOldBarRateForCP();
        s = s + getOldFloorRate() == null ? "" : "\n" + getOldFloorRate() + " - F";
        return s;
    }

    private String getNewFloorForCP() {
        String s = "";
        s = s + getNewBarRateForCP();
        s = s + getNewFloorRate() == null ? "" : "\n" + getNewFloorRate() + " - F";
        return s;
    }

    private String getOldCeilForCP() {
        String s = "";
        s = s + getOldBarRateForCP();
        s = s + getOldCeilBarRate() == null ? "" : "\n" + getOldCeilBarRate() + " - C";
        return s;
    }

    private String getNewCeilForCP() {
        String s = "";
        s = s + getNewBarRateForCP();
        s = s + getNewCeilBarRate() == null ? "" : "\n" + getNewCeilBarRate() + " - C";
        return s;
    }

    private String getNewPendingForCp() {
        return getOldBarRate() != null ? "R(" + getOldBarRate() + ")"
                : "floorandceil".equalsIgnoreCase(getOldOverride()) ? "" + getOldBarRateForCP() +
                getOldFloorRate() == null ? "" : "\nR(" + getOldFloorRate() + " - F)" +
                getOldCeilBarRate() == null ? "" : "\nR(" + getOldCeilBarRate() + " - C)"
                : "floor".equalsIgnoreCase(getOldOverride()) ?
                getOldFloorRate() == null ? "" : "\nR(" + getOldFloorRate() + " - F)"
                : "ceil".equalsIgnoreCase(getOldOverride()) ?
                getOldCeilBarRate() == null ? "" : "\nR(" + getOldCeilBarRate() + " - C)"
                : ""
                ;
    }

    private String getNoneDecisionForOldOverride() {
        return getOldRateCodeName() + "\n(" + getOldBarRate() + ")";
    }

    private String getPendingDecisionForOldOverride() {
        return getOldRateCodeName() + " R" + "\n(" + getOldBarRate() + ")";
    }

    private String getUserDecisionForOldOverride() {
        return getOldRateCodeName() + " S" + "\n(" + getOldBarRate() + ")";
    }

    private String getFloorAndCeilDecisionForOldOverride() {
        return getFloorDecisionForOldOverride() + ",\n" + getCeilDecisionForOldOverride();
    }

    private String getCeilDecisionForOldOverride() {
        return getOldCeilRateCodeName() + " C" + "\n(" + getOldCeilBarRate() + ")";
    }

    private String getFloorDecisionForOldOverride() {
        return getOldRateCodeName() + " F" + "\n(" + getOldBarRate() + ")";
    }

    public String getNewDecisionDisplay() {
        return newDecisionDisplay;
    }

    public void setNewDecisionDisplay(String newDecisionDisplay) {
        this.newDecisionDisplay = newDecisionDisplay;
    }

    public String getOldDecisionDisplay() {
        return oldDecisionDisplay;
    }

    public void setOldDecisionDisplay(String oldDecisionDisplay) {
        this.oldDecisionDisplay = oldDecisionDisplay;
    }
}
