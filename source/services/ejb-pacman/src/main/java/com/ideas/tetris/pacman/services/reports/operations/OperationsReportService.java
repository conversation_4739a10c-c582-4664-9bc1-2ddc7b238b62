package com.ideas.tetris.pacman.services.reports.operations;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.operations.dto.OperationsReportDTO;
import com.ideas.tetris.pacman.services.reports.operations.dto.OperationsReportParameters;
import com.ideas.tetris.pacman.services.reports.operations.dto.OperationsReportRowMapper;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.inject.Inject;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class OperationsReportService {
    private static final String ROLLING_DATE_VALUE_FALSE = "0";
    private static final Integer PER_PERSON_PRICING_TOGGLE_FALSE_DEFAULT_VALUE = -1;


    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    public List<OperationsReportDTO> generateReport(LocalDate startDate, LocalDate endDate) {
        List<OperationsReportRowMapper> rowMappers = getArrivalDepartureForecasts(startDate, endDate, ROLLING_DATE_VALUE_FALSE, StringUtils.EMPTY, StringUtils.EMPTY, isPerPersonPricingEnabled() ? 1 : 0);
        return populateOperationsReportDTO(rowMappers);
    }

    private List<OperationsReportDTO> populateOperationsReportDTO(List<OperationsReportRowMapper> rowMappers) {
        final List<OperationsReportDTO> reportResults = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rowMappers)) {
            rowMappers.forEach(row -> reportResults.add(mapRowValues(row, isPerPersonPricingEnabled())));
        }
        return reportResults;
    }

    public boolean isPerPersonPricingEnabled() {
        return configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED);
    }

    private OperationsReportDTO mapRowValues(OperationsReportRowMapper row, boolean isPerPersonEnabled) {
        OperationsReportDTO dto = new OperationsReportDTO();
        dto.setOnBooksOccupancy(row.getOccupancyOnBooks().intValue());
        dto.setOccupancyDate(row.getOccupancyDate());
        dto.setOnBooksArrivals(row.getArrivalsOnBooks());
        dto.setOnBooksDepartures(row.getDeparturesOnBooks());
        dto.setOnBooksStayThrus(row.getStayThruOnBooks());
        dto.setForecastArrivals(row.getArrivalsForecast());
        dto.setForecastDepartures(row.getDeparturesForecast());
        dto.setForecastStayThrus(row.getStayThruForecast());
        dto.setForecastOccupancy(row.getOccupancyForecast() == null ? 0 : row.getOccupancyForecast().intValue());
        dto.setCapacity(row.getTotalAccomCapacity() == null ? 0 : row.getTotalAccomCapacity().intValue());
        dto.setOutOfOrder(row.getOutOfOrder() == null ? 0 : row.getOutOfOrder().intValue());
        dto.setSpecialEvent(row.getSpecialEventName());
        dto.setNumberOfAdultsOnBooks(isPerPersonEnabled ? (null != row.getNumberOfAdultsOnBooks() ? row.getNumberOfAdultsOnBooks() : 0) : PER_PERSON_PRICING_TOGGLE_FALSE_DEFAULT_VALUE);
        dto.setNumberOfChildrenOnBooks(isPerPersonEnabled ? (null != row.getNumberOfChildrenOnBooks() ? row.getNumberOfChildrenOnBooks() : 0) : PER_PERSON_PRICING_TOGGLE_FALSE_DEFAULT_VALUE);
        dto.setNumberOfAdults(isPerPersonEnabled ? (null != row.getNumberOfAdults() ? row.getNumberOfAdults() : 0) : PER_PERSON_PRICING_TOGGLE_FALSE_DEFAULT_VALUE);
        dto.setNumberOfChildren(isPerPersonEnabled ? (null != row.getNumberOfChildren() ? row.getNumberOfChildren() : 0) : PER_PERSON_PRICING_TOGGLE_FALSE_DEFAULT_VALUE);
        dto.setDayOfWeek(DateUtil.getDayOfWeekShortName(dto.getOccupancyDate()));
        return dto;
    }


    public List<OperationsReportDTO> generateReport(OperationsReportParameters operationsReportParameters) {
        List<OperationsReportRowMapper> rowMappers = getArrivalDepartureForecasts(operationsReportParameters.getStartDate(), operationsReportParameters.getEndDate(), operationsReportParameters.getIsRollingDate(), operationsReportParameters.getRollingStartDate(), operationsReportParameters.getRollingEndDate(), isPerPersonPricingEnabled() ? 1 : 0);
        return populateOperationsReportDTO(rowMappers);
    }

    protected List<OperationsReportRowMapper> getArrivalDepartureForecasts(LocalDate startDate, LocalDate endDate, String isRollingDate, String rollingStartDate, String rollingEndDate, int isPerPersonPricing) {
        Map<String, Object> parameters = QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                .and("startDate", startDate)
                .and("endDate", endDate)
                .and("isRollingDate", isRollingDate)
                .and("rollingStartDate", rollingStartDate)
                .and("rollingEndDate", rollingEndDate)
                .and("isPerPersonPricing", isPerPersonPricing)
                .parameters();

        return tenantCrudService.findByNativeQuery(ARRIVAL_DEPARTURE_FORECAST, parameters, new OperationsReportRowMapper());
    }

    protected void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    protected static final String ARRIVAL_DEPARTURE_FORECAST = new StringBuilder()
            .append("SELECT * FROM dbo.ufn_get_operations_report")
            .append("(:propertyId,")
            .append(":startDate,")
            .append(":endDate,")
            .append(":isRollingDate,")
            .append(":rollingStartDate,")
            .append(":rollingEndDate,")
            .append(":isPerPersonPricing)")
            .append("order by Occupancy_DT ASC")
            .toString();
}