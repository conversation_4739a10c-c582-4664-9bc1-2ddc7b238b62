package com.ideas.tetris.pacman.services.security.login.vo;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

public class ModulePermissionVO {
    private String moduleName;
    private String modulePermission;
    private String moduleParent;

    public ModulePermissionVO() {
    }

    public ModulePermissionVO(String moduleName, String modulePermission) {
        this.moduleName = moduleName;
        this.modulePermission = modulePermission;
    }

    public ModulePermissionVO(String moduleName, String modulePermission, String moduleParent) {
        this.moduleName = moduleName;
        this.modulePermission = modulePermission;
        this.moduleParent = moduleParent;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getModulePermission() {
        return modulePermission;
    }

    public void setModulePermission(String modulePermission) {
        this.modulePermission = modulePermission;
    }

    public String getModuleParent() {
        return moduleParent;
    }

    public void setModuleParent(String moduleParent) {
        this.moduleParent = moduleParent;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ModulePermissionVO that = (ModulePermissionVO) o;

        return new EqualsBuilder()
                .append(moduleName, that.moduleName)
                .append(modulePermission, that.modulePermission)
                .append(moduleParent, that.moduleParent)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(moduleName)
                .append(modulePermission)
                .append(moduleParent)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "ModulePermissionVO{" +
                "moduleName='" + moduleName + '\'' +
                ", modulePermission='" + modulePermission + '\'' +
                '}';
    }
}
