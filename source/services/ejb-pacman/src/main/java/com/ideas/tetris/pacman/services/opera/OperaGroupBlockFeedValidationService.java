package com.ideas.tetris.pacman.services.opera;

import com.google.common.collect.ImmutableMap;
import com.ideas.g3.integration.opera.dto.OperaDataLoadTypeCode;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.List;
import java.util.Map;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Created by idnrbk on 4/22/2015.
 */

@OperaGroupBlockFeedValidationService.Qualifier
@Component
@Transactional
public class OperaGroupBlockFeedValidationService implements OperaFeedValidationService {

    private static final Logger LOGGER = Logger.getLogger(OperaGroupBlockFeedValidationService.class.getName());

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }

    @TenantCrudServiceBean.Qualifier
	@Autowired
    @org.springframework.beans.factory.annotation.Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    private static final String QUERY_DUPLICATE_GROUP_BLOCK_RECORDS = "Select Group_id,Room_Type,Block_DT from " +
            "opera.history_group_block where Data_Load_Metadata_ID in (:dlmIds) and room_type <> '' group by Group_id,Room_Type,Block_DT having count(Group_id) > 1";

    private static final String QUERY_COUNT_GROUP_BLOCK_WITH_MISSING_GROUP_ID = "Select count(*) from " +
            "opera.history_group_block where Data_Load_Metadata_ID in (:dlmIds) and Group_id = '' ";

    private static final String QUERY_GROUP_ID_WITH_MISSING_BLOCK_DATE = "Select Group_id from " +
            "opera.history_group_block where Data_Load_Metadata_ID in (:dlmIds) and Group_id <> '' and Block_DT = '' ";


    private static final String ENTITY = "Group Block";
    private static final String PRIMARY_ATTRIBUTE = "Group ID : ";

    public static final String MSG_GROUP_BLOCK_VALIDATION_FAILED = "Feed validation failed for " + ENTITY + ".\n";
    public static final String MSG_DUPLICATE_RECORDS = "Duplicate Records (Group ID, Room Type, Block Date) : ";
    public static final String MSG_DUPLICATE_GROUP_BLOCKS = " are present in " + ENTITY;
    public static final String MSG_INVALID_BLOCK_DATE = " have invalid Block Dates";
    public static final String MSG_MISSING_GROUP_ID = ENTITY + " Record(s) have missing Group ID";

    public final String[] OPERA_DATALOAD_TYPE_CODE = new String[]{OperaDataLoadTypeCode.PGB.name(), OperaDataLoadTypeCode.CGB.name()};

    public static Map<String, String> queryMap = ImmutableMap.<String, String>builder()
            .put(MSG_INVALID_BLOCK_DATE, QUERY_GROUP_ID_WITH_MISSING_BLOCK_DATE)
            .build();

    @Override
    public String[] getDataLoadTypes() {
        return OPERA_DATALOAD_TYPE_CODE;
    }

    @Override
    public String validateFeed(String correlationId, List<Integer> operaDataLoadTypeCodes) {
        boolean flagMissingGroupID = isGroupBlockMissingGroupID(correlationId, operaDataLoadTypeCodes);
        boolean flagMissingMandatoryAttribute = isGroupBlockMissingMandatoryAttribute(correlationId, operaDataLoadTypeCodes);
        boolean flagDuplicateGroupBlock = isDuplicateGroupBlockPresent(correlationId, operaDataLoadTypeCodes);

        if (flagMissingGroupID || flagMissingMandatoryAttribute || flagDuplicateGroupBlock) {
            return MSG_GROUP_BLOCK_VALIDATION_FAILED;
        }
        return StringUtils.EMPTY;
    }

    private boolean isGroupBlockMissingGroupID(String correlation_id, List<Integer> dataloadMetadatas) {
        List<Integer> queryResults = crudService.findByNativeQuery(QUERY_COUNT_GROUP_BLOCK_WITH_MISSING_GROUP_ID,
                QueryParameter.with("dlmIds", dataloadMetadatas).parameters());
        Integer count = queryResults.get(0);
        if (count > 0) {
            LOGGER.error("OperaFeedValidationStep Failed: " + count + " " + MSG_MISSING_GROUP_ID);
            return true;
        }
        return false;
    }

    private boolean isGroupBlockMissingMandatoryAttribute(String correlation_id, List<Integer> dataloadMetadatas) {
        boolean result = false;
        for (String msg : queryMap.keySet()) {
            List<String> queryResults = crudService.findByNativeQuery(queryMap.get(msg),
                    QueryParameter.with("dlmIds", dataloadMetadatas).parameters());
            result = logValidationMessage(queryResults, PRIMARY_ATTRIBUTE, msg) || result;
        }
        return result;
    }

    private boolean logValidationMessage(List<String> queryResults, String initialMessage, String missingAttribute) {
        if (!CollectionUtils.isEmpty(queryResults)) {
            LOGGER.error((new StringBuffer("OperaFeedValidationStep Failed: ").append(initialMessage).append(queryResults.toString()).append(missingAttribute)).toString());
            return true;
        }
        return false;
    }

    private boolean isDuplicateGroupBlockPresent(String correlation_id, List<Integer> dataloadMetadatas) {
        List<String> queryResults = crudService.findByNativeQuery(QUERY_DUPLICATE_GROUP_BLOCK_RECORDS,
                QueryParameter.with("dlmIds", dataloadMetadatas).parameters(), new RowMapper<String>() {
                    @Override
                    public String mapRow(Object[] row) {
                        return (new StringBuffer("{").append(row[0]).append(";").append(row[1]).append(";").append(row[2]).append("}")).toString();
                    }
                });
        return logValidationMessage(queryResults, MSG_DUPLICATE_RECORDS, MSG_DUPLICATE_GROUP_BLOCKS);
    }
}
