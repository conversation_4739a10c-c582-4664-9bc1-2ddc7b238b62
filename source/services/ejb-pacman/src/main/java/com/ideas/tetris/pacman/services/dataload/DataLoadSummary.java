package com.ideas.tetris.pacman.services.dataload;

import com.ideas.tetris.pacman.services.opera.OperaIncomingFile;
import com.ideas.tetris.pacman.services.opera.OperaIncomingMetadata;
import com.ideas.tetris.pacman.services.opera.entity.DataLoadMetadata;
import org.joda.time.LocalDateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.ArrayList;
import java.util.List;

public class DataLoadSummary {
    private List<DataLoadDetails> details = new ArrayList<DataLoadDetails>();
    private String correlationId;
    private LocalDateTime businessDate;
    private LocalDateTime preparedDate;
    private Integer pastDays;
    private Integer futureDays;
    private String inputType;
    private Integer inputProcessingId;
    private boolean populated;
    private Long jobId;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    private String status;


    public LocalDateTime getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(LocalDateTime businessDate) {
        this.businessDate = businessDate;
    }

    public LocalDateTime getPreparedDate() {
        return preparedDate;
    }

    public void setPreparedDate(LocalDateTime preparedDate) {
        this.preparedDate = preparedDate;
    }

    public Integer getPastDays() {
        return pastDays;
    }

    public void setPastDays(Integer pastDays) {
        this.pastDays = pastDays;
    }

    public Integer getFutureDays() {
        return futureDays;
    }

    public void setFutureDays(Integer futureDays) {
        this.futureDays = futureDays;
    }

    public List<DataLoadDetails> getDetails() {
        return details;
    }

    public void setDetails(List<DataLoadDetails> details) {
        this.details = details;
    }

    public boolean isPopulated() {
        return populated;
    }

    public void setPopulated(boolean populated) {
        this.populated = populated;
    }

    public void add(DataLoadMetadata entity) {
        DataLoadDetails detail = new DataLoadDetails(entity);
        details.add(detail);
    }

    public void add(OperaIncomingMetadata entity) {
        if (entity == null) {
            return;
        }
        try {
            pastDays = Integer.parseInt(entity.getPastDays());
        } catch (NumberFormatException e) {
        }
        try {
            futureDays = Integer.parseInt(entity.getFutureDays());
        } catch (NumberFormatException e) {
        }
        DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        if (entity.getPreparedDate() != null && entity.getPreparedTime() != null) {
            preparedDate = LocalDateTime.parse(entity.getPreparedDate() + " " + entity.getPreparedTime(), fmt);
        }
        if (entity.getBusinessDate() != null && entity.getBusinessTime() != null) {
            businessDate = LocalDateTime.parse(entity.getBusinessDate() + " " + entity.getBusinessTime(), fmt);
        }
    }

    public void fillInMissingDetails() {
        for (OperaIncomingFile type : OperaIncomingFile.values()) {
            if (getDetailsForType(type) == null) {
                DataLoadDetails missingDetails = new DataLoadDetails();
                missingDetails.setCorrelationId(correlationId);
                missingDetails.setFileType(type);
                details.add(missingDetails);
            }
        }
    }

    public DataLoadDetails getDetailsForType(OperaIncomingFile type) {
        for (DataLoadDetails candidate : details) {
            if (candidate.getFileType().getFileTypeCode().equals(type.getFileTypeCode())) {
                return candidate;
            }
        }
        return null;
    }

    public String getCorrelationId() {
        return correlationId;
    }

    public void setCorrelationId(String correlationId) {
        this.correlationId = correlationId;
    }

    public LocalDateTime getFirstActivityDate() {
        LocalDateTime firstDate = null;
        for (DataLoadDetails detail : details) {
            if (firstDate == null || (detail.getCreationDate() != null && detail.getCreationDate().isBefore(firstDate))) {
                firstDate = detail.getCreationDate();
            }
        }
        return firstDate;

    }

    public LocalDateTime getLastActivityDate() {
        LocalDateTime lastDate = null;
        for (DataLoadDetails detail : details) {
            if (lastDate == null || (detail.getCreationDate() != null && detail.getCreationDate().isAfter(lastDate))) {
                lastDate = detail.getCreationDate();
            }
        }
        return lastDate;

    }

    public int getCompletedActivityCount() {
        int count = 0;
        for (DataLoadDetails detail : details) {
            if (detail.isComplete()) {
                count++;
            }
        }
        return count;

    }

    public int getTotalActivityCount() {
        return details.size();
    }

    public String getCompletionStatus() {
        return "" + getCompletedActivityCount() + " of " + getTotalActivityCount();
    }

    public String getInputType() {
        return inputType;
    }

    public void setInputType(String inputType) {
        this.inputType = inputType;
    }

    public Integer getInputProcessingId() {
        return inputProcessingId;
    }

    public void setInputProcessingId(Integer inputProcessingId) {
        this.inputProcessingId = inputProcessingId;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }
}
