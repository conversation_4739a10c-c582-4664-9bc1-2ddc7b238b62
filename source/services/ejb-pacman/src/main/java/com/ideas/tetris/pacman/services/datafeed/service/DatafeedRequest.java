package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.activity.service.Pageable;
import com.ideas.tetris.platform.common.rest.annotation.DateFormat;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import javax.ws.rs.QueryParam;
import java.util.Date;

public class DatafeedRequest extends Pageable {

    @QueryParam("lastSuccessDate")
    @DateFormat(value = DateUtil.DATE_TIME_FORMAT)
    private Date lastSuccessDate;
    @QueryParam("includeHistoryData")
    private boolean includeHistoryData = Boolean.FALSE;
    @QueryParam("isOptixDatafeed")
    private boolean isOptixDatafeed = Boolean.FALSE;
    @QueryParam("datafeedName")
    private String datafeedName;

    private String dataFeedType;

    @QueryParam("useExtendedWindowDecision")
    private boolean useExtendedWindowDecision = Boolean.FALSE;

    @QueryParam("missingExtract")
    private boolean missingExtract = Boolean.FALSE;

    @QueryParam("systemDate")
    @DateFormat
    private Date systemDate;

    public DatafeedRequest() {
        super();
    }

    public DatafeedRequest(int page, int size, Date startDate, Date endDate, Date lastSuccessDate) {
        super(page, size, startDate, endDate);
        this.lastSuccessDate = lastSuccessDate;
    }

    public DatafeedRequest(int page, int size, Date startDate, Date endDate, Date lastSuccessDate, String datafeedName) {
        super(page, size, startDate, endDate);
        this.lastSuccessDate = lastSuccessDate;
        this.datafeedName = datafeedName;
    }

    public boolean isUseExtendedWindowDecision() {
        return useExtendedWindowDecision;
    }

    public void setUseExtendedWindowDecision(boolean useExtendedWindowDecision) {
        this.useExtendedWindowDecision = useExtendedWindowDecision;
    }

    public Date getLastSuccessDate() {
        return lastSuccessDate;
    }

    public void setLastSuccessDate(Date lastSuccessDate) {
        this.lastSuccessDate = lastSuccessDate;
    }

    public String getDataFeedType() {
        return dataFeedType;
    }

    public void setDataFeedType(String dataFeedType) {
        this.dataFeedType = dataFeedType;
    }

    public boolean isIncludeHistoryData() {
        return includeHistoryData;
    }

    public void setIncludeHistoryData(boolean includeHistoryData) {
        this.includeHistoryData = includeHistoryData;
    }

    public boolean isOptixDatafeed() {
        return isOptixDatafeed;
    }

    public void setOptixDatafeed(boolean optixDatafeed) {
        isOptixDatafeed = optixDatafeed;
    }

    public String getDatafeedName() {
        return datafeedName;
    }

    public void setDatafeedName(String datafeedName) {
        this.datafeedName = datafeedName;
    }

    public boolean isMissingExtract() {
        return missingExtract;
    }

    public void setMissingExtract(boolean missingExtract) {
        this.missingExtract = missingExtract;
    }

    public Date getSystemDate() {
        return systemDate;
    }

    public void setSystemDate(Date systemDate) {
        this.systemDate = systemDate;
    }
}
