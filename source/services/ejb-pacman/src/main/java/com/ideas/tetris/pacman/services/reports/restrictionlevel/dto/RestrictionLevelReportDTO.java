package com.ideas.tetris.pacman.services.reports.restrictionlevel.dto;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Date;
import java.util.LinkedHashMap;

import static java.lang.String.valueOf;

public class RestrictionLevelReportDTO {
    private static final String X = "X";
    private static final String O = "O";
    private static final String Y = "Y";
    public static final String EMPTY_STRING = "";

    private String dayOfWeek;
    private Date date;
    private String accomClassName;
    private String occupancyForecast;
    private String lrv;
    private String accomTypeName;
    private String restrictionCode;
    private String minlos;
    private String rateValue;
    private String maxlos;
    private boolean isMinlosChanged = true;
    private boolean isMaxlosChanged = true;
    private String rateQualifiedType;

    private LinkedHashMap<String, String> losWithValue;
    private Integer maxLosSize;

    public RestrictionLevelReportDTO() {
    }

    public RestrictionLevelReportDTO(Integer maxLosSize) {
        this.maxLosSize = maxLosSize;
    }

    public Integer getMaxLosSize() {
        return maxLosSize;
    }

    public void setMaxLosSize(Integer maxLosSize) {
        this.maxLosSize = maxLosSize;
    }

    public LinkedHashMap<String, String> getLosWithValue() {
        return losWithValue;
    }

    public void setLosWithValue(LinkedHashMap<String, String> losWithValue) {
        this.losWithValue = losWithValue;
    }


    public String getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(String dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = DateUtil.convertLocalDateToJavaUtilDate(date);
    }

    public String getAccomClassName() {
        return accomClassName;
    }

    public void setAccomClassName(String accomClassName) {
        this.accomClassName = accomClassName;
    }

    public String getOccupancyForecast() {
        return occupancyForecast;
    }

    public void setOccupancyForecast(String occupancyForecast) {
        this.occupancyForecast = occupancyForecast;
    }

    public String getLrv() {
        return lrv;
    }

    public void setLrv(String lrv) {
        this.lrv = lrv;
    }

    public String getAccomTypeName() {
        return accomTypeName;
    }

    public void setAccomTypeName(String accomTypeName) {
        this.accomTypeName = accomTypeName;
    }

    public String getRestrictionCode() {
        return restrictionCode;
    }

    public void setRestrictionCode(String restrictionCode) {
        this.restrictionCode = restrictionCode;
    }

    public String getMinlos() {
        return minlos;
    }

    public void setMinlos(String minlos) {
        this.minlos = minlos;
    }

    public String getRateValue() {
        return rateValue;
    }

    public void setRateValue(String rateValue) {
        this.rateValue = rateValue;
    }

    public static RestrictionLevelReportDTO newMinLosReportFor(Object[] row) {
        RestrictionLevelReportDTO reportDataOutput = newWith(row);
        if (row.length >= 12 && (null != row[11] && (Integer) row[11] != -1)) {
            reportDataOutput.setMinlosChanged(null != row[10] && (Integer) row[10] == 1 ? true : false);
            reportDataOutput.setMaxlosChanged(null != row[11] && (Integer) row[11] == 1 ? true : false);
            reportDataOutput.setRateQualifiedType((String) row[12]);
        } else {
            reportDataOutput.setRateQualifiedType((String) row[10]);
        }
        reportDataOutput.setMinlos(row[7] != null && reportDataOutput.isMinlosChanged() ? row[7].toString() : Constants.REPORT_HYPHEN);
        reportDataOutput.setMaxlos(row[9] != null && reportDataOutput.isMaxlosChanged() ? row[9].toString() : Constants.REPORT_HYPHEN);
        return reportDataOutput;
    }

    public RestrictionLevelReportDTO newFpLosReportFor(Object[] row) {
        RestrictionLevelReportDTO reportDataOutput = newWith(row);
        reportDataOutput.setLosWithValue(splitFplosIntoLosMap((String) row[7]));
        reportDataOutput.setMaxLosSize(getMaxLosSize());
        reportDataOutput.setRateQualifiedType((String) row[9]);
        return reportDataOutput;
    }

    private LinkedHashMap<String, String> splitFplosIntoLosMap(String fplos) {
        LinkedHashMap<String, String> losWithValueMap = new LinkedHashMap<String, String>();
        int i = 0;
        while (i < getMaxLosSize()) {
            String losValue = (null == fplos || fplos.length() <= i) ? Constants.REPORT_HYPHEN : valueOf(fplos.charAt(i)).equalsIgnoreCase(Y) ? O : X;
            i++;
            losWithValueMap.put(EMPTY_STRING + i, losValue);
        }
        return losWithValueMap;
    }

    private static RestrictionLevelReportDTO newWith(Object[] row) {
        RestrictionLevelReportDTO reportDataOutput = new RestrictionLevelReportDTO();
        reportDataOutput.setDayOfWeek((String) row[0]);
        reportDataOutput.setDate(DateUtil.convertJavaUtilDateToLocalDate((java.util.Date) row[1]));
        reportDataOutput.setAccomClassName(row[2] == null ? Constants.REPORT_HYPHEN : (String) row[2]);
        reportDataOutput.setOccupancyForecast(row[3] == null ? Constants.REPORT_HYPHEN : ((BigDecimal) row[3]).setScale(2, RoundingMode.HALF_EVEN).toString());
        reportDataOutput.setLrv(row[4] == null ? Constants.REPORT_HYPHEN : ((BigDecimal) row[4]).setScale(2, RoundingMode.HALF_EVEN).toString());
        reportDataOutput.setAccomTypeName(row[5] == null ? Constants.REPORT_HYPHEN : (String) row[5]);
        reportDataOutput.setRestrictionCode(row[6] == null ? Constants.REPORT_HYPHEN : (String) row[6]);
        reportDataOutput.setRateValue(row[8] == null ? Constants.REPORT_HYPHEN : ((BigDecimal) row[8]).setScale(2, RoundingMode.HALF_EVEN).toString());
        return reportDataOutput;
    }

    public String getMaxlos() {
        return maxlos;
    }

    public void setMaxlos(String maxlos) {
        this.maxlos = maxlos;
    }

    public boolean isMinlosChanged() {
        return isMinlosChanged;
    }

    public void setMinlosChanged(boolean isMinlosChanged) {
        this.isMinlosChanged = isMinlosChanged;
    }

    public boolean isMaxlosChanged() {
        return isMaxlosChanged;
    }

    public void setMaxlosChanged(boolean isMaxlosChanged) {
        this.isMaxlosChanged = isMaxlosChanged;
    }

    public String getRateQualifiedType() {
        return rateQualifiedType;
    }

    public void setRateQualifiedType(String rateQualifiedType) {
        this.rateQualifiedType = rateQualifiedType;
    }
}
