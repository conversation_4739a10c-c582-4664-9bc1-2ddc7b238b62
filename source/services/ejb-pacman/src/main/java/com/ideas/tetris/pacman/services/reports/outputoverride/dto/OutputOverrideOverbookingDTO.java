package com.ideas.tetris.pacman.services.reports.outputoverride.dto;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.annotations.ColumnHeader;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.datatypes.PropertyValueType;
import com.ideas.tetris.platform.common.rest.unmarshaller.DateTimeSerializer;

import java.math.BigDecimal;
import java.util.Date;

public class OutputOverrideOverbookingDTO {

    @ColumnHeader(titleKey = "common.propertyName", order = 1)
    private String propertyName;
    @ColumnHeader(titleKey = "roomType", order = 4)
    private String accomClassName;
    private String overbookingTypeName;
    @ColumnHeader(titleKey = "occupancyDate", order = 3)
    private Date occupancyDate;

    @ColumnHeader(titleKey = "report.dow", order = 2, type = PropertyValueType.class)
    private String dow;
    @ColumnHeader(titleKey = "report.overbookingValue", order = 7, useDashes = true)
    private BigDecimal overbookingOvr;
    @ColumnHeader(titleKey = "report.costOfWalkValue", order = 6, useDashes = true)
    private BigDecimal costofWalkValueOvr;
    @ColumnHeader(titleKey = "report.overbookingCeiling", order = 8, useDashes = true)
    private BigDecimal overBookingLimit;
    @ColumnHeader(titleKey = "overrideCategory", order = 5, type = PropertyValueType.class)
    private String overrideCategory;
    @ColumnHeader(titleKey = "report.overrideLastModifiedBy", order = 11)
    private String userName;
    private String userEmail;
    @ColumnHeader(titleKey = "report.overrideLastModifiedOn", order = 10)
    private Date createDate;
    @ColumnHeader(titleKey = "notes.label", order = 9)
    private String notes;

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    @JsonGetter("accomTypeName")
    public String getAccomClassName() {
        return accomClassName;
    }

    public void setAccomClassName(String accomClassName) {
        this.accomClassName = accomClassName;
    }

    public String getOverbookingTypeName() {
        return overbookingTypeName;
    }

    public void setOverbookingTypeName(String overbookingTypeName) {
        this.overbookingTypeName = overbookingTypeName;
    }

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public String getDow() {
        return dow;
    }

    public void setDow(String dow) {
        this.dow = dow;
    }

    public BigDecimal getOverbookingOvr() {
        return overbookingOvr;
    }

    public void setOverbookingOvr(BigDecimal overbookingOvr) {
        this.overbookingOvr = overbookingOvr;
    }

    public BigDecimal getCostofWalkValueOvr() {
        return costofWalkValueOvr;
    }

    public void setCostofWalkValueOvr(BigDecimal costofWalkValueOvr) {
        this.costofWalkValueOvr = costofWalkValueOvr;
    }

    public String getOverrideCategory() {
        return overrideCategory;
    }

    public void setOverrideCategory(String overrideCategory) {
        this.overrideCategory = overrideCategory;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @JsonSerialize(using = DateTimeSerializer.class)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getOverBookingLimit() {
        return overBookingLimit;
    }

    public void setOverBookingLimit(BigDecimal overBookingLimit) {
        this.overBookingLimit = overBookingLimit;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }
}
