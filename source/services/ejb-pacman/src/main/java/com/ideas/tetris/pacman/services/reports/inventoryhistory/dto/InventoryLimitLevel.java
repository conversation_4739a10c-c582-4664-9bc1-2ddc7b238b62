package com.ideas.tetris.pacman.services.reports.inventoryhistory.dto;

public enum InventoryLimitLevel {
    HOUSE(1, "houseProperty", "HOUSE");

    private int ordinal;
    private String caption;
    private String paramValue;

    InventoryLimitLevel(int ordinal, String caption, String paramValue) {
        this.ordinal = ordinal;
        this.caption = caption;
        this.paramValue = paramValue;
    }

    public String getCaption() {
        return caption;
    }

    public String getParamValue() {
        return paramValue;
    }
}
