package com.ideas.tetris.pacman.services.datafeed.dto.decisionconfiguration;

import com.ideas.tetris.platform.services.Stage;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
public class DecisionConfigurationDTO {

    // propertyStage field is changed from DecisionType Enum(vaadin ) to Stage Enum(Pacman) during redesign
    private Stage propertyStage;
    private boolean srpFPLOS;
    private String note;
    private String propertyType;
    private String operaPMSDailyBarRateCode;
    private String curtiscPropertyCode;
    private String curtiscDailyBarRateCode;
    private Integer optimizationWindowBDE;
    private Integer optimizationWindowCDP;
    private Integer decisionUploadWindowBDE;
    private Integer variableDecisionWindow;
    private boolean configNotAvailable;
    private Integer forecastWindowBDE;
    private String addTaxOption;
    private BigDecimal tax;
    private String scheduled2WayDate;
    private List<CdpScheduleDTO> idpSchedules = new ArrayList<>();
    private String zeroCapacityRTs = StringUtils.EMPTY;
    private String zeroCapacityRTsWithPricingAndRestrictions = StringUtils.EMPTY;

    @Override
    public boolean equals(Object dto) {
        if(!(dto instanceof DecisionConfigurationDTO)) {
            return false;
        }
        DecisionConfigurationDTO decisionConfigurationDTO = (DecisionConfigurationDTO) dto;

        return null != decisionConfigurationDTO.getPropertyStage() && (propertyStage != decisionConfigurationDTO.getPropertyStage() || (
                decisionConfigurationDTO.getPropertyStage().equals(Stage.TWO_WAY) && decisionConfigurationDTO.isSrpFPLOS() != srpFPLOS) ||
                !decisionConfigurationDTO.getOptimizationWindowBDE().equals(optimizationWindowBDE) ||
                !decisionConfigurationDTO.getDecisionUploadWindowBDE().equals(decisionUploadWindowBDE) ||
                !StringUtils.equals(decisionConfigurationDTO.getZeroCapacityRTs(), zeroCapacityRTs) ||
                !StringUtils.equals(decisionConfigurationDTO.getZeroCapacityRTsWithPricingAndRestrictions(), zeroCapacityRTsWithPricingAndRestrictions));
    }

    public boolean isValidCdpSchedules(DecisionConfigurationDTO originalConfig) {
        List<CdpScheduleDTO> originalItems = originalConfig.getIdpSchedules();
        boolean isEmpty = CollectionUtils.isEmpty(idpSchedules) && CollectionUtils.isEmpty(originalItems);
        if (!isEmpty) {
            if (idpSchedules.size() != originalItems.size()) {
                return true;
            } else {
                for(CdpScheduleDTO item : idpSchedules) {
                    if (originalItems.stream().anyMatch(originalItem -> Objects.equals(item.getId(), originalItem.getId()) &&
                            !item.getLocalTime().equals(originalItem.getLocalTime()))) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
}
