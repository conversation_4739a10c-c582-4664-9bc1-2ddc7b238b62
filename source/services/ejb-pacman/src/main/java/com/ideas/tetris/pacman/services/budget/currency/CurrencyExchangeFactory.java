package com.ideas.tetris.pacman.services.budget.currency;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CurrencyExchangeFactory {

    @Autowired
    private NgiCurrencyExchangeService ngiCurrencyExchangeService;

    @Autowired
    private OperaCurrencyExchangeService operaCurrencyExchangeService;


    @Autowired
    private HiltonCurrencyExchangeService hiltonCurrencyExchangeService;

    public AbstractCurrencyExchangeService getCurrencyExchangeService(ExternalSystemType externalSystem) {
        switch (externalSystem) {
            case NGI:
                return ngiCurrencyExchangeService;
            case OPERA:
                return operaCurrencyExchangeService;
            case PCRS:
            case HCRS:
            case HILSTAR:
                return hiltonCurrencyExchangeService;
            default:
                return null;
        }
    }
}
