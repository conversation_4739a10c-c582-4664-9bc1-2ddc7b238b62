package com.ideas.tetris.pacman.services.walkme;

public class WalkMeConstants {

    private WalkMeConstants() {

    }

    public static final String WEBRATE_ACCOM_NAME_STANDARD = "CompetitorRT_Standard";
    public static final String WEBRATE_ACCOM_NAME_STANDARD2 = "CompetitorRT_Standard 2";
    public static final String WEBRATE_ACCOM_NAME_DELUX = "CompetitorRT_Delux";

    public static final String WEBRATE_CHANNEL1 = "WalkMeChannel1";
    public static final String WEBRATE_CHANNEL2 = "WalkMeChannel2";

    public static final String WEBRATE_CHANNEL_ID1 = "WalkMeFileChannelId1";
    public static final String WEBRATE_CHANNEL_ID2 = "WalkMeFileChannelId2";

    public static final String WEBRATE_HOTEL_ID1 = "WalkMeWebrateHotelID1";
    public static final String WEBRATE_HOTEL_ID2 = "WalkMeWebrateHotelID2";
    public static final String WEBRATE_HOTEL_ID3 = "WalkMeWebrateHotelID3";
    public static final String WEBRATE_HOTEL_ID4 = "WalkMeWebrateHotelID4";

    public static final String WEBRATE_COMPETITOR1 = "WalkMeWebrateCompetitor1";
    public static final String WEBRATE_COMPETITOR2 = "WalkMeWebrateCompetitor2";
    public static final String WEBRATE_COMPETITOR3 = "WalkMeWebrateCompetitor3";
    public static final String WEBRATE_COMPETITOR4 = "WalkMeWebrateCompetitor4";

    public static final String RATE_UNQUALIFIED0 = "WalkMeLV0";
    public static final String RATE_UNQUALIFIED1 = "WalkMeLV1";
    public static final String RATE_UNQUALIFIED2 = "WalkMeLV2";
    public static final String RATE_UNQUALIFIED3 = "WalkMeLV3";
    public static final String RATE_UNQUALIFIED4 = "WalkMeLV4";
    public static final String RATE_UNQUALIFIED5 = "WalkMeLV5";
    public static final String RATE_UNQUALIFIED6 = "WalkMeLV6";
    public static final String RATE_UNQUALIFIED7 = "WalkMeLV7";
    public static final String RATE_UNQUALIFIED8 = "WalkMeLV8";

    public static final String ZERO = "0";
    public static final String ONE = "1";
    public static final String TWO = "2";
    public static final String THREE = "3";
    public static final String FOUR = "4";
    public static final String FIVE = "5";
    public static final String SIX = "6";
    public static final String SEVEN = "7";

    public static final String STATUS_ACT = "ACT";
    public static final String STATUS_CXL = "CXL";
    public static final String STATUS_DEF = "DEF";
    public static final String STATUS_EXP = "EXP";
    public static final String STATUS_LOS = "LOS";
    public static final String STATUS_TNT = "TNT";
    public static final String STATUS_UNC = "UNC";

    public static final String ACCOM_TYPE_NAME_DELUXE = "DELUXE";
    public static final String ACCOM_TYPE_NAME_SUITE = "SUITE";
    public static final String ACCOM_TYPE_NAME_DOUBLE = "DOUBLE";
    public static final String ACCOM_TYPE_NAME_QUEEN = "QUEEN";
    public static final String ACCOM_TYPE_NAME_KING = "KING";

    public static final String STANDARD = "Standard";

    public static final String ACCOM_TYPE_CODE_DLX = "DLX";
    public static final String ACCOM_TYPE_CODE_STE = "STE";
    public static final String ACCOM_TYPE_CODE_DBL = "DBL";
    public static final String ACCOM_TYPE_CODE_Q = "Q";
    public static final String ACCOM_TYPE_CODE_K = "K";

    public static final String DELUXE = "Deluxe Room";
    public static final String DOUBLE_ROOM = "DoubleRoom";
    public static final String QUEEN_SIZE = "Queen-Size Room";
    public static final String KING_SIZE = "King-Size Room";

    public static final String PALACE = "PALACE";
    public static final String TRV = "TRV";
    public static final String TRAVELLERS = "Travellers";
    public static final String WALK_ME_GROUP_MARKET_SEGMENT = "Walk Me Group Market Segment";
    public static final String G = "G";

    public static final String CORRELATION_ID = "1234WalkMeCorrelationId";
    public static final String WALK_ME_FILE = "WalkMeFile";

    public static final String GET_DATA_LOAD_METADATA_QUERY = "select data_load_metadata_id from opera.data_load_metadata where correlation_id = :correlationID";

    public static final String INSERT_INTO_RAW_TRANSACTION_QUERY = "INSERT INTO [opera].[Raw_Transaction] VALUES('1','Checked In',1,'None',GETDATE(),GETDATE(),GETDATE(),GETDATE(),GETDATE(),GETDATE(),:rateCode,'150',:marketSegment,'Room','150'\n" +
            "  ,'50','50','250','RoomType',null,null,null,null,null,null,null,'ReservationNameID',:dataLoadMetadataID,GETDATE(),null,GETDATE())";

    public static final String RATE_CODE_1 = "Rate Code 1";
    public static final String RATE_CODE_2 = "Rate Code 2";
    public static final String RATE_CODE_3 = "Rate Code 3";
    public static final String RATE_CODE_4 = "Rate Code 4";

    public static final String MARKET_CODE_1 = "Market Code 1";
    public static final String MARKET_CODE_2 = "Market Code 2";

    public static final String DATA_LOAD_METADATA_ID = "dataLoadMetadataID";
    public static final String RATE_CODE = "rateCode";
    public static final String MARKET_SEGMENT = "marketSegment";

    public static final String WALK_ME_BARTER = "Walk Me Barter";
    public static final String WALK_ME_DISCOUNT = "Walk Me Discount";
    public static final String WALK_ME_RACK = "Walk Me Rack";

    public static final String FORECAST_GROUP = "ForecastGroup";

    public static final String ROOM_TYPE_1 = "RoomType1";
    public static final String ROOM_TYPE_2 = "RoomType2";
}
