package com.ideas.tetris.pacman.services.budget.converter;


import com.ideas.tetris.pacman.services.budget.entity.UserForecastData;
import com.ideas.tetris.pacman.services.budget.model.BudgetUserForecastEntityModel;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Map;

public class UserForecastDataEntityConverter {
    private Map<String, Integer> businessGroupNameToIdMap;
    private Map<String, UserForecastData> currentEntities;
    private static final String DELIMETER = "_";

    public UserForecastDataEntityConverter(Map<String, UserForecastData> currentEntities, Map<String, Integer> businessGroupNameToIdMap) {
        this.currentEntities = currentEntities;
        this.businessGroupNameToIdMap = businessGroupNameToIdMap;
    }

    public UserForecastData convert(BudgetUserForecastEntityModel budgetUserForecastEntityModel) {

        UserForecastData entity = getCurrentEntity(budgetUserForecastEntityModel);
        if (entity == null) {
            entity = createNewEntity(budgetUserForecastEntityModel);
        }

        return entity;
    }

    private UserForecastData createNewEntity(BudgetUserForecastEntityModel budgetUserForecastEntityModel) {
        UserForecastData userForecastData = new UserForecastData();
        userForecastData.setBusinessGroupId(businessGroupNameToIdMap.get(budgetUserForecastEntityModel.getSegmentName().toLowerCase()));
        userForecastData.setOccupancyDate(JavaLocalDateUtils.toJodaLocalDate(budgetUserForecastEntityModel.getOccupancyDate()));
        userForecastData.setRoomsSold(budgetUserForecastEntityModel.getRooms());
        userForecastData.setRoomRevenue(budgetUserForecastEntityModel.getRevenue().setScale(5, BigDecimal.ROUND_DOWN));
        return userForecastData;
    }

    private UserForecastData getCurrentEntity(BudgetUserForecastEntityModel budgetUserForecastEntityModel) {
        UserForecastData currentEntity = currentEntities.get(getKey(budgetUserForecastEntityModel));
        if (currentEntity != null) {
            currentEntity.setRoomRevenue(budgetUserForecastEntityModel.getRevenue().setScale(5, BigDecimal.ROUND_DOWN));
            currentEntity.setRoomsSold(budgetUserForecastEntityModel.getRooms());
        }
        return currentEntity;
    }

    private String getKey(BudgetUserForecastEntityModel dataModel) {
        return dataModel.getOccupancyDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + DELIMETER + getBusinessGroupId(dataModel.getSegmentName());
    }

    private Integer getBusinessGroupId(String businessGroupName) {
        return businessGroupNameToIdMap.get(businessGroupName.toLowerCase());
    }
}
