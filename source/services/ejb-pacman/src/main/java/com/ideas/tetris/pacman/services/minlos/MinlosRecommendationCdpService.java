package com.ideas.tetris.pacman.services.minlos;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Component
@Transactional
public class MinlosRecommendationCdpService extends AbstractMinlosRecommendationService {


    @Override
    public void createMinlosDecisions() {
        super.createMinlosDecisions();
    }

    @Override
	public
    Date getOptimizationWindowEndDate() {
        return dateService.getOptimizationWindowEndDateCDP();
    }
}
