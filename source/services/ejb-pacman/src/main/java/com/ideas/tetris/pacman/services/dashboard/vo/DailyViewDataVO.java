package com.ideas.tetris.pacman.services.dashboard.vo;

public class DailyViewDataVO {

    private String occupancy; // For past day holds actual occupancy (AO) and for future day holds forecast occupancy(OF)
    private String occupancyPercentage;
    private String roomsSold; // For past day holds actual roomSold and for future day holds onTheBooks
    private String roomsSoldPercentage;
    private String occupancyLY;
    private String occupancyPercentageLY;
    private String roomsSoldLY;
    private String roomsSoldPercentageLY;
    private String roomsToSell;
    private String occupancyDate;

    private String revenue;
    private String ADR;
    private String revPAR;

    private String outOfOrder;

    private String lrv;

    private String barRateForLOS1;

    private String barValueForLOS1;

    private String overBooking;

    private String competitorRate;
    private String competitorRateConfiguration;

    public String getOccupancy() {
        return occupancy;
    }

    public void setOccupancy(String occupancy) {
        this.occupancy = occupancy;
    }

    public String getOccupancyPercentage() {
        return occupancyPercentage;
    }

    public void setOccupancyPercentage(String occupancyPercentage) {
        this.occupancyPercentage = occupancyPercentage;
    }

    public String getRoomsSold() {
        return roomsSold;
    }

    public void setRoomsSold(String roomsSold) {
        this.roomsSold = roomsSold;
    }

    public String getRoomsSoldPercentage() {
        return roomsSoldPercentage;
    }

    public void setRoomsSoldPercentage(String roomsSoldPercentage) {
        this.roomsSoldPercentage = roomsSoldPercentage;
    }

    public String getOccupancyLY() {
        return occupancyLY;
    }

    public void setOccupancyLY(String occupancyLY) {
        this.occupancyLY = occupancyLY;
    }

    public String getOccupancyPercentageLY() {
        return occupancyPercentageLY;
    }

    public void setOccupancyPercentageLY(String occupancyPercentageLY) {
        this.occupancyPercentageLY = occupancyPercentageLY;
    }

    public String getRoomsSoldLY() {
        return roomsSoldLY;
    }

    public void setRoomsSoldLY(String roomsSoldLY) {
        this.roomsSoldLY = roomsSoldLY;
    }

    public String getRoomsSoldPercentageLY() {
        return roomsSoldPercentageLY;
    }

    public void setRoomsSoldPercentageLY(String roomsSoldPercentageLY) {
        this.roomsSoldPercentageLY = roomsSoldPercentageLY;
    }

    public String getRoomsToSell() {
        return roomsToSell;
    }

    public void setRoomsToSell(String roomsToSell) {
        this.roomsToSell = roomsToSell;
    }

    public String getRevenue() {
        return revenue;
    }

    public void setRevenue(String revenue) {
        this.revenue = revenue;
    }

    public String getADR() {
        return ADR;
    }

    public void setADR(String aDR) {
        ADR = aDR;
    }

    public String getRevPAR() {
        return revPAR;
    }

    public void setRevPAR(String revPAR) {
        this.revPAR = revPAR;
    }

    public String getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(String occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public String getOverBooking() {
        return overBooking;
    }

    public void setOverBooking(String overBooking) {
        this.overBooking = overBooking;
    }

    public String getLrv() {
        return lrv;
    }

    public void setLrv(String lrv) {
        this.lrv = lrv;
    }

    public String getBarRateForLOS1() {
        return barRateForLOS1;
    }

    public void setBarRateForLOS1(String barRateForLOS1) {
        this.barRateForLOS1 = barRateForLOS1;
    }

    public String getBarValueForLOS1() {
        return barValueForLOS1;
    }

    public void setBarValueForLOS1(String barValueForLOS1) {
        this.barValueForLOS1 = barValueForLOS1;
    }

    public String getOutOfOrder() {
        return outOfOrder;
    }

    public void setOutOfOrder(String outOfOrder) {
        this.outOfOrder = outOfOrder;
    }

    public String getCompetitorRate() {
        return competitorRate;
    }

    public void setCompetitorRate(String competitorRate) {
        this.competitorRate = competitorRate;
    }

    public String getCompetitorRateConfiguration() {
        return competitorRateConfiguration;
    }

    public void setCompetitorRateConfiguration(String competitorRateConfiguration) {
        this.competitorRateConfiguration = competitorRateConfiguration;
    }

}
