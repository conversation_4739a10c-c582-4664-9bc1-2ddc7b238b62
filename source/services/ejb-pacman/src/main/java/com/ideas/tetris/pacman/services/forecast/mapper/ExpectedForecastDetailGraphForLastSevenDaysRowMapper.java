package com.ideas.tetris.pacman.services.forecast.mapper;

import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailGraphForPastSevenDaysDTO;
import com.ideas.tetris.platform.common.crudservice.RowMapper;

import java.util.Date;

public class ExpectedForecastDetailGraphForLastSevenDaysRowMapper implements RowMapper<ExpectedForecastDetailGraphForPastSevenDaysDTO> {

    @Override
    public ExpectedForecastDetailGraphForPastSevenDaysDTO mapRow(Object[] row) {

        ExpectedForecastDetailGraphForPastSevenDaysDTO expectedForecastDetailGraphForPastSevenDaysDTO = new ExpectedForecastDetailGraphForPastSevenDaysDTO();
        expectedForecastDetailGraphForPastSevenDaysDTO.setOccupancyDate((Date) row[0]);
        expectedForecastDetailGraphForPastSevenDaysDTO.setPacePoint(getValueFromRow(row[1]));
        expectedForecastDetailGraphForPastSevenDaysDTO.setTransientOnBooks(getValueFromRow(row[2]));
        expectedForecastDetailGraphForPastSevenDaysDTO.setStly(getValueFromRow(row[3]));
        expectedForecastDetailGraphForPastSevenDaysDTO.setSt2y(getValueFromRow(row[4]));
        expectedForecastDetailGraphForPastSevenDaysDTO.setAverageDow(getValueFromRow(row[5]));
        return expectedForecastDetailGraphForPastSevenDaysDTO;
    }

    private Integer getValueFromRow(Object row) {
        return null == row ? 0 : (Integer) row;
    }

}
