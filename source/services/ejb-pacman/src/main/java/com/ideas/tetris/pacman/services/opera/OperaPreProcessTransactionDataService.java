package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;

import javax.inject.Inject;

import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.DELETE_EARLIER_RECORDS_IN_STAGE_TRANSACTION;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.UPDATE_ANALYTICAL_MARKET_SEGMENTS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.UPDATE_ANALYTICAL_MARKET_SEGMENTS_DEFAULTS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.UPDATE_DEPARTURE_DT_IN_STAGE_TRANSACTION;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * Created by idnpak on 2/25/2015.
 */
@Component
public class OperaPreProcessTransactionDataService {
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    private static final Logger LOGGER = Logger.getLogger(OperaPreProcessTransactionDataService.class.getName());

    // DELETE_EARLIER_RECORDS_IN_STAGE_TRANSACTION
    public int deleteTrans() {
        int numRowsUpdated = 0;
        LOGGER.info("Started deleteTrans ");
        numRowsUpdated += crudService.executeUpdateByNativeQuery(DELETE_EARLIER_RECORDS_IN_STAGE_TRANSACTION);
        LOGGER.info(new StringBuilder().append("deleteTrans : ").append(numRowsUpdated).toString());
        return numRowsUpdated;
    }

    // UPDATE_DEPARTURE_DT_FOR_CHANGING_DEPARTURE_DT
    public int updateDepartureDt() {
        int numRowsUpdated = 0;
        LOGGER.info("Started updateDepartureDt ");
        numRowsUpdated += crudService.executeUpdateByNativeQuery(UPDATE_DEPARTURE_DT_IN_STAGE_TRANSACTION);
        LOGGER.info(new StringBuilder().append("updateDepartureDt : ").append(numRowsUpdated).toString());
        return numRowsUpdated;
    }

    public int transformPostDepartureTransactions(String correlationId) {
        LOGGER.info("Started updating MS , RT and RC for post departure transaction");
        String updatePostDeptTransMSRTandRC = new StringBuilder(" update xx set xx.Market_Code = y.market_code ,")
                .append(" xx.hotel_market_code = y.market_code ,xx.Room_Type = y.room_type,xx.Rate_Code = y.Rate_Code")
                .append(" from opera.stage_transaction as xx  join")
                .append("  (select z.reservation_name_id,z.transaction_dt,x.market_code,x.room_type,x.Rate_Code from")
                .append("	 (select transaction_dt,reservation_name_id from opera.stage_transaction where Transaction_DT >= Departure_DT) as Z 	 join")
                .append("		 (select A.reservation_name_id,a.transaction_dt,b.market_code,b.room_type,b.Rate_Code  			 from(")
                .append("					 (select min(transaction_dt)as Transaction_DT,reservation_name_id  						 from opera.stage_transaction ")
                .append("						 group by Reservation_Name_ID  					 ) as A 					 join opera.stage_transaction as B")
                .append("					 on A.reservation_name_id = B.reservation_name_id 					 and A.transaction_dt = B.transaction_dt 				 ) 		 ) As X")
                .append("	 on z.reservation_name_id = x.reservation_name_id  ) AS Y  on xx.Reservation_Name_ID = y.Reservation_Name_ID")
                .append(" and xx.Transaction_DT = y.Transaction_DT").toString();
        int noRowsUpdated = crudService.executeUpdateByNativeQuery(updatePostDeptTransMSRTandRC);
        LOGGER.info(new StringBuilder().append("Completed updating MS , RT and RC for post departure transaction : ").append(noRowsUpdated).toString());
        return noRowsUpdated;
    }

    public int updateAnalyticalMarketSegments(String correlationId) {
        LOGGER.info("Starting UPDATE_ANALYTICAL_MARKET_SEGMENTS");
        int noRowsUpdated = 0;
        noRowsUpdated = crudService.executeUpdateByNativeQuery(UPDATE_ANALYTICAL_MARKET_SEGMENTS);
        LOGGER.info("Completed UPDATE_ANALYTICAL_MARKET_SEGMENTS : " + noRowsUpdated);
        // now update defaults
        LOGGER.info("Starting UPDATE_ANALYTICAL_MARKET_SEGMENTS_DEFAULTS");
        noRowsUpdated += crudService.executeUpdateByNativeQuery(UPDATE_ANALYTICAL_MARKET_SEGMENTS_DEFAULTS);
        LOGGER.info("Completed UPDATE_ANALYTICAL_MARKET_SEGMENTS_DEFAULTS : " + noRowsUpdated);
        return noRowsUpdated;
    }

}
