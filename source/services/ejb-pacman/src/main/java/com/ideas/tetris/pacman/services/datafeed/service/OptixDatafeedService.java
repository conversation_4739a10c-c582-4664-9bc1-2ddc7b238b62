package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceTotalActivity;
import com.ideas.tetris.pacman.services.budget.BudgetDataService;
import com.ideas.tetris.pacman.services.budget.BudgetService;
import com.ideas.tetris.pacman.services.budget.entity.BudgetConfig;
import com.ideas.tetris.pacman.services.budget.entity.BudgetData;
import com.ideas.tetris.pacman.services.budget.entity.BudgetLevel;
import com.ideas.tetris.pacman.services.budget.entity.UserForecastData;
import com.ideas.tetris.pacman.services.businessgroup.service.BusinessGroupService;
import com.ideas.tetris.pacman.services.componentrooms.entity.CRAccomTypeMapping;
import com.ideas.tetris.pacman.services.componentrooms.services.ComponentRoomService;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.ComponentRoomMappingDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.GroupBlockDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.GroupMasterDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.*;
import com.ideas.tetris.pacman.services.datafeed.entity.PricingSensitivityCoefficient;
import com.ideas.tetris.pacman.services.datafeed.entity.PropertyLevelData;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.demand360.entity.Demand360BookingSummaryPace;
import com.ideas.tetris.pacman.services.demand360.entity.Demand360MarketSegmentHistoryCapacity;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockDetail;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;
import com.ideas.tetris.pacman.services.groupblock.PaceGroupBlock;
import com.ideas.tetris.pacman.services.groupblock.PaceGroupMaster;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup;
import com.ideas.tetris.pacman.services.pacealert.dto.PropertyOnBooksPaceAlertDTO;
import com.ideas.tetris.pacman.services.pacealert.entity.PropertyOnBooksPaceAlert;
import com.ideas.tetris.pacman.services.str.dto.STRDailyDTO;
import com.ideas.tetris.pacman.services.str.dto.STRMonthlyDTO;
import com.ideas.tetris.pacman.services.str.entity.STRDaily;
import com.ideas.tetris.pacman.services.str.entity.STRMonthly;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.round;
import static org.apache.commons.lang.StringUtils.EMPTY;
import static org.apache.commons.lang.StringUtils.isEmpty;

@Component
@Transactional
public class OptixDatafeedService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    private CrudService tenantCrudService;

    @Autowired
    DateService dateService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    BudgetService budgetService;

    @Autowired
    BudgetDataService budgetDataService;

    @Autowired
    BusinessGroupService businessGroupService;

    @Autowired
    ComponentRoomService componentRoomService;

    @Autowired
    private DatafeedEndpointService endpointService;

    public List<ReservationNightChangeDTO> getReservationChangeNight(DatafeedRequest datafeedRequest, boolean isFirstRun) {

        Date startDate = getStartDate(datafeedRequest, isFirstRun);
        String query = "select [Reservation_Identifier],[Individual_Status] ,[Arrival_DT] ,[Departure_DT] ,[Booking_DT] ,[Cancellation_DT] ," +
                "[Booked_Accom_Type_Code] ,at.[Accom_Type_Code],ms.[Mkt_Seg_Name] ,[Room_Revenue] ,[Food_Revenue],[Beverage_Revenue],[Telecom_Revenue]," +
                "[Other_Revenue],[Total_Revenue] ,[Source_Booking], [Nationality] ,[Rate_Code] ,[Rate_Value] ,[Room_Number] ,[Booking_type] ," +
                "[Number_Children],[Number_Adults],[Change_DTTM],[Confirmation_No],[Channel],[Occupancy_DT],[Inv_Block_Code], [Market_Code]," +
                "[Change_Type] from Reservation_Night_Change as rnc\n" +
                "left outer join Accom_Type at on rnc.Accom_Type_ID = at.Accom_Type_ID\n" +
                "left outer join Mkt_Seg ms on rnc.Mkt_Seg_ID = ms.Mkt_Seg_ID\n" +
                "where rnc.[Occupancy_DT] >=  :startDate ";

        if (pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED)) {
            query = query + " union " +
                    " SELECT post_dep.reservation_identifier, rn.Individual_Status, minMax.min_arrival_DT , rn.Departure_DT, rn.Booking_DT, rn.Cancellation_DT," +
                    "   rn.Booked_Accom_Type_Code, at.Accom_Type_Code ,ms.[Mkt_Seg_Name] , post_dep.room_revenue , post_dep.food_revenue , 0.00000 as beverrage_revenue, 0.00000 as telecom_revenue, " +
                    "   post_dep.other_revenue , post_dep.Total_Revenue , rn.Source_Booking, rn.Nationality, post_dep.rate_code, post_dep.rate_value, rn.Room_Number, rn.Booking_type, rn.Number_Children, " +
                    "   rn.Number_Adults, rn.Change_DTTM, rn.Confirmation_No, rn.Channel, post_dep.occupancy_dt, rn.Inv_Block_Code, post_dep.market_code, " +
                    "   rn.change_type " +
                    " FROM dbo.reservation_night_change AS rn " +
                    "   JOIN (SELECT t.reservation_identifier,  min(arrival_DT) AS min_arrival_DT,  max(departure_DT) AS max_departure_DT, max(file_metadata_id) as max_file_metadata_id " +
                    " FROM dbo.reservation_night_change AS t  GROUP BY t.reservation_identifier) minMax " +
                    "      ON rn.reservation_identifier = minMax.reservation_identifier " +
                    " JOIN post_departure_revenue post_dep on rn.reservation_identifier = post_dep.reservation_identifier " +
                    "   and rn.occupancy_DT = minMax.min_arrival_DT and rn.File_Metadata_ID = minMax.max_file_metadata_id" +
                    "   left outer join Accom_Type at on post_dep.Accom_Type_ID = at.Accom_Type_ID " +
                    "   left outer join Mkt_Seg ms on post_dep.Mkt_Seg_ID = ms.Mkt_Seg_ID " +
                    "   WHERE rn.Occupancy_DT >=  :startDate ";
        }

        String orderBy =
                " order by rnc.[Occupancy_DT], rnc.[Reservation_Identifier], rnc.[Rate_Value], rnc.[Booking_type], " +
                        " rnc.[Confirmation_No], rnc.[Change_DTTM], rnc.[Arrival_DT], rnc.[Booking_DT]";

        query = query + orderBy;
        return tenantCrudService.findByNativeQuery(query,
                QueryParameter.with("startDate", startDate).parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize(), reservationNightChangeDTORowMapper());
    }

    public List<BudgetDataDTO> getBudgetData(DatafeedRequest datafeedRequest, boolean isFirstRun) {

        Date startDate = getStartDate(datafeedRequest, isFirstRun);
        Date endDate = datafeedRequest.getEndDate();

        List<BudgetDataDTO> result = new ArrayList<>();
        BudgetConfig budgetConfig = budgetService.getBudgetConfig();

        if (budgetConfig != null) {
            String budgetLevel = budgetConfig.getBudgetLevel().getBudgetLevel();
            if (BudgetLevel.BUDGET_LEVEL.BUSINESS_TYPE.getValue().equals(budgetLevel)) {
                result = tenantCrudService.findByNamedQuery(
                        BudgetData.BUDGET_DATA_ON_BUSINESS_TYPE_BY_DATE_RANGE, QueryParameter.with(START_DATE, new LocalDate(startDate)).and(END_DATE, new LocalDate(endDate)).parameters(),
                        datafeedRequest.getStartPosition(), datafeedRequest.getSize());

            } else {
                result = tenantCrudService.findByNamedQuery(
                        BudgetData.BUDGET_DATA_ON_BUSINESS_GROUP_BY_DATE_RANGE, QueryParameter.with(START_DATE, new LocalDate(startDate)).and(END_DATE, new LocalDate(endDate)).parameters(),
                        datafeedRequest.getStartPosition(), datafeedRequest.getSize());

            }
        }
        return result;
    }

    public List<UserForecastDTO> getUserForecastData(DatafeedRequest datafeedRequest, boolean isFirstRun) {

        if (budgetService.isUserForecastEnabled()) {

            LocalDate startDate = new LocalDate(getStartDate(datafeedRequest, isFirstRun));
            LocalDate endDate = new LocalDate(datafeedRequest.getEndDate());

            return tenantCrudService.findByNamedQuery(UserForecastData.USER_FORECAST_DATA_BY_OCCUPANCY_DATE_RANGE,
                    QueryParameter
                            .with(START_DATE, startDate)
                            .and(END_DATE, endDate)
                            .and("budgetLevelId", budgetService.getUserForecastConfig().getBudgetLevel().getId()).parameters(),
                    datafeedRequest.getStartPosition(), datafeedRequest.getSize());
        }
        return Collections.EMPTY_LIST;
    }

    public List<OccupancyFCSTDTO> getOccupancyFCST(DatafeedRequest datafeedRequest, boolean isFirstRun) {

        Date startDate = getStartDate(datafeedRequest, isFirstRun);
        Date endDate = datafeedRequest.getEndDate();
        String query = "select  ms.MKT_Seg_CODE,at.Accom_Type_Code,ocf.Occupancy_DT,ocf.Occupancy_NBR,ocf.Revenue,ocf.CreateDate_DTTM  from Occupancy_FCST as ocf inner join Accom_type at on ocf.Accom_Type_ID = at.Accom_Type_ID \n" +
                " inner join Mkt_Seg ms on ocf.MKT_SEG_ID= ms.Mkt_Seg_ID \n" +
                " Where ocf.Occupancy_DT BETWEEN  :startDate AND :endDate order by ocf.Occupancy_DT";
        List<OccupancyFCSTDTO> result = tenantCrudService.findByNativeQuery(query,
                QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize(), occupancyFCSTDTORowMapper());
        return result != null ? result : Collections.emptyList();
    }

    private RowMapper<OccupancyFCSTDTO> occupancyFCSTDTORowMapper() {
        return row -> {
            OccupancyFCSTDTO occupancyFCSTDTO = new OccupancyFCSTDTO();
            occupancyFCSTDTO.setMarketCode((String) row[0]);
            occupancyFCSTDTO.setAccomTypeCode((String) row[1]);
            occupancyFCSTDTO.setOccupancyDate((Date) row[2]);
            occupancyFCSTDTO.setOccupancyNumber(getBigDecimalAt(row, 3));
            occupancyFCSTDTO.setRevenue(getBigDecimalAt(row, 4));
            occupancyFCSTDTO.setCreatedDate((Date) row[5]);
            return occupancyFCSTDTO;
        };
    }

    private RowMapper<ReservationNightChangeDTO> reservationNightChangeDTORowMapper() {

        return row -> {
            ReservationNightChangeDTO reservationNightChangeDTO = new ReservationNightChangeDTO();
            reservationNightChangeDTO.setReservationIdentifier((String) row[0]);
            reservationNightChangeDTO.setIndividualStatus((String) row[1]);
            reservationNightChangeDTO.setArrivalDate((Date) row[2]);
            reservationNightChangeDTO.setDepartureDate((Date) row[3]);
            reservationNightChangeDTO.setBookingDate((Date) row[4]);
            reservationNightChangeDTO.setCancellationDate((Date) row[5]);
            reservationNightChangeDTO.setBookedAccomTypeCode((String) row[6]);
            reservationNightChangeDTO.setAccomTypeCode((String) row[7]);
            reservationNightChangeDTO.setMarketSegCode((String) row[8]);
            reservationNightChangeDTO.setRoomRevenue(getBigDecimalAt(row, 9));
            reservationNightChangeDTO.setFoodRevenue(getBigDecimalAt(row, 10));
            reservationNightChangeDTO.setBeverageRevenue(getBigDecimalAt(row, 11));
            reservationNightChangeDTO.setTelecomRevenue(getBigDecimalAt(row, 12));
            reservationNightChangeDTO.setOtherRevenue(getBigDecimalAt(row, 13));
            reservationNightChangeDTO.setTotalRevenue(getBigDecimalAt(row, 14));
            reservationNightChangeDTO.setSourceBooking((String) row[15]);
            reservationNightChangeDTO.setNationality((String) row[16]);
            reservationNightChangeDTO.setRateCode((String) row[17]);
            reservationNightChangeDTO.setRateValue(getBigDecimalAt(row, 18));
            reservationNightChangeDTO.setRoomNumber((String) row[19]);
            reservationNightChangeDTO.setBookingType((String) row[20]);
            reservationNightChangeDTO.setNumberChildren(getIntegerAt(row, 21));
            reservationNightChangeDTO.setNumberAdults(getIntegerAt(row, 22));
            reservationNightChangeDTO.setChangeDate((Date) row[23]);
            reservationNightChangeDTO.setConfirmationNo((String) row[24]);
            reservationNightChangeDTO.setChannel((String) row[25]);
            reservationNightChangeDTO.setOccupancyDate((Date) row[26]);
            reservationNightChangeDTO.setInvBlockCode((String) row[27]);
            reservationNightChangeDTO.setMarketCode((String) row[28]);
            reservationNightChangeDTO.setChangeType(getIntegerAt(row, 29));
            return reservationNightChangeDTO;
        };
    }

    private RowMapper<PaceWebrateDTO> paceWebrateDTORowMapper() {
        return row -> {
            PaceWebrateDTO paceWebrateDTO = new PaceWebrateDTO();
            paceWebrateDTO.setWebrateSourceProperty((String) row[0]);
            paceWebrateDTO.setFirstWebrateGenerationDate((Date) row[1]);
            paceWebrateDTO.setWebrateGenerationDate((Date) row[2]);
            paceWebrateDTO.setCount(getIntegerAt(row, 3));
            paceWebrateDTO.setCompetitorsName((String) row[4]);
            paceWebrateDTO.setWebrateChannel((String) row[5]);
            paceWebrateDTO.setWebrateRoomType((String) row[6]);
            paceWebrateDTO.setWebrateTypeName((String) row[7]);
            paceWebrateDTO.setOccupancyDate((Date) row[8]);
            paceWebrateDTO.setLos(getIntegerAt(row, 9));
            paceWebrateDTO.setWebrateStatus((String) row[10]);
            paceWebrateDTO.setWebrateCurrency((String) row[11]);
            paceWebrateDTO.setWebrateRateValue(getBigDecimalValue(row, 12));
            paceWebrateDTO.setWebrateRank((String) row[13]);
            paceWebrateDTO.setWebrateRating((String) row[14]);
            paceWebrateDTO.setWebrateRateValueDisplay(getBigDecimalValue(row, 15));
            return paceWebrateDTO;
        };
    }

    private RowMapper<WebrateDTO> webrateDTORowMapper() {
        return row -> {
            WebrateDTO webrateDTO = new WebrateDTO();
            webrateDTO.setWebrateSourceProperty((String) row[0]);
            webrateDTO.setWebrateGenerationDate((Date) row[1]);
            webrateDTO.setCompetitorsName((String) row[2]);
            webrateDTO.setWebrateChannel((String) row[3]);
            webrateDTO.setWebrateRoomType((String) row[4]);
            webrateDTO.setWebrateTypeName((String) row[5]);
            webrateDTO.setOccupancyDate((Date) row[6]);
            webrateDTO.setLos(getIntegerAt(row, 7));
            webrateDTO.setWebrateStatus((String) row[8]);
            webrateDTO.setWebrateCurrency((String) row[9]);
            webrateDTO.setWebrateRateValue(getBigDecimalValue(row, 10));
            webrateDTO.setWebrateRank((String) row[11]);
            webrateDTO.setWebrateRating((String) row[12]);
            webrateDTO.setWebrateRateValueMax(getBigDecimalValue(row, 13));
            webrateDTO.setWebrateRateValueDisplay(getBigDecimalValue(row, 14));
            webrateDTO.setProductName((String) row[15]);
            return webrateDTO;
        };
    }

    private RowMapper<LRVPaceDTO> lrvPaceDTORowMapper() {
        return row -> {
            LRVPaceDTO lrvPaceDTO = new LRVPaceDTO();
            lrvPaceDTO.setBusinessDayEndDate((Date) row[0]);
            lrvPaceDTO.setOccupancyDate((Date) row[1]);
            lrvPaceDTO.setRoomClassCode((String) row[2]);
            lrvPaceDTO.setLrv(getBigDecimalAt(row, 3));
            return lrvPaceDTO;
        };
    }

    public static BigDecimal getBigDecimalAt(Object[] row, int i) {
        final String value = getStringAt(row, i);
        return isEmpty(value) ? null : round(new BigDecimal(value), 2);
    }

    public static BigDecimal getBigDecimalValue(Object[] row, int i) {
        final String value = getStringAt(row, i);
        return isEmpty(value) ? null : new BigDecimal(value);
    }

    public static Integer getIntegerAt(Object[] row, int i) {
        final String value = getStringAt(row, i);
        return isEmpty(value) ? null : Integer.valueOf(value);
    }

    public static String getStringAt(Object[] row, int i) {
        return Optional.ofNullable(row[i]).map(String::valueOf).orElse(EMPTY);
    }

    public List<ForecastGroupDTO> getForecastGroupInfo() {
        List<ForecastGroupDTO> forecastGroupDTOList = new ArrayList<>();
        List<ForecastGroup> forecastGroups = tenantCrudService.findAll(ForecastGroup.class);
        forecastGroups
                .forEach(forecastGroup -> {
                    ForecastGroupDTO forecastGroupDTO = new ForecastGroupDTO(forecastGroup);
                    forecastGroupDTOList.add(forecastGroupDTO);
                });
        return forecastGroupDTOList;
    }

    public List<MarketAccomActivityDTO> getMarketAccomActivity(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        Date startDate = getStartDate(datafeedRequest, isFirstRun);
        Date endDate = datafeedRequest.getEndDate();

        return tenantCrudService.findByNamedQuery(
                MktSegAccomActivity.GET_OCCUPANCY_DATE_RANGE, QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize());

    }

    public List<PaceGroupBlockDTO> getPaceGroupBlock(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        Date startDate = getStartDate(datafeedRequest, isFirstRun);
        Date endDate = datafeedRequest.getEndDate();

        return tenantCrudService.findByNamedQuery(
                PaceGroupBlock.GET_OCCUPANCY_DATE_RANGE, QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize());
    }

    public List<PaceTotalActivityDTO> getPaceTotalActivity(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        Date startDate = getStartDate(datafeedRequest, isFirstRun);
        Date endDate = datafeedRequest.getEndDate();

        List<PaceTotalActivity> paceTotalActivityList = tenantCrudService.findByNamedQuery(
                PaceTotalActivity.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID, QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).and("propertyId", PacmanWorkContextHelper.getPropertyId()).
                        parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize());

        return Optional.ofNullable(paceTotalActivityList)
                .orElse(Collections.emptyList()).stream()
                .map(PaceTotalActivityDTO::new)
                .collect(Collectors.toList());
    }

    public List<PaceWebrateDTO> getPaceWebrate(DatafeedRequest datafeedRequest, boolean isFirstRun) {

        Date startDate = getStartDate(datafeedRequest, isFirstRun);

        String query = "SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED \n " +
                "select ws.sourceName,pwd.First_Webrate_GenerationDate, pwd.Webrate_GenerationDate, pwd.Webrate_count, \n" +
                "wc.Webrate_Competitors_Name, webc.Webrate_Channel_Name, wat.Webrate_Accom_Name, wt.Webrate_Type_Name, \n" +
                "pwd.Occupancy_DT, pwd.LOS, pwd.Webrate_Status, pwd.Webrate_Currency, pwd.Webrate_RateValue, pwd.Webrate_Rank, pwd.Webrate_Rating, \n" +
                "pwd.Webrate_RateValue_Display from PACE_Webrate_Differential pwd \n" +
                "inner join (select wst.Webrate_Source_Name as sourceName, wsp.Webrate_Source_Property_ID  as sourcePropertyId from Webrate_Source wst \n" +
                "inner join Webrate_Source_Property wsp on wst.Webrate_Source_ID = wsp.Webrate_Source_ID) as ws on pwd.Webrate_Source_Property_ID =  ws.sourcePropertyID \n" +
                "inner join Webrate_Channel webc on pwd.Webrate_Channel_ID = webc.Webrate_Channel_ID \n" +
                "inner join Webrate_Competitors wc on pwd.Webrate_Competitors_ID = wc.Webrate_Competitors_ID \n" +
                "inner join Webrate_Accom_Type wat on pwd.Webrate_Accom_Type_ID = wat.Webrate_Accom_Type_ID \n" +
                "inner join Webrate_Type wt on pwd.Webrate_Type_ID = wt.Webrate_Type_ID \n" +
                "Where pwd.Occupancy_DT >=  :startDate order by pwd.Occupancy_DT,pwd.LOS,pwd.Webrate_GenerationDate,pwd.Webrate_count, \n" +
                "wc.Webrate_Competitors_Name,webc.Webrate_Channel_Name,wat.Webrate_Accom_Name,pwd.Webrate_RateValue, pwd.Webrate_RateValue_Display";

        List<PaceWebrateDTO> result = tenantCrudService.findByNativeQuery(query,
                QueryParameter.with("startDate", startDate).parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize(), paceWebrateDTORowMapper());

        return result != null ? result : Collections.emptyList();
    }

    public List<WebrateDTO> getWebrate(DatafeedRequest datafeedRequest) {

        Date startDate = datafeedRequest.getStartDate();
        String query = "SELECT ws.sourceName AS webrateSourceProperty\n" +
                ",pwd.Webrate_GenerationDate AS webrateGenerationDate\n" +
                ",wc.Webrate_Competitors_Name AS competitorsName\n" +
                ",webc.Webrate_Channel_Name AS webrateChannel\n" +
                ",wat.Webrate_Accom_Name AS webrateRoomType\n" +
                ",wt.Webrate_Type_Name AS webrateTypeName\n" +
                ",pwd.Occupancy_DT AS occupancyDate\n" +
                ",pwd.LOS AS los\n" +
                ",pwd.Webrate_Status AS webrateStatus\n" +
                ",pwd.Webrate_Currency AS webrateCurrency\n" +
                ",pwd.Webrate_RateValue AS webrateRateValue\n" +
                ",pwd.Webrate_Rank AS webrateRank\n" +
                ",pwd.Webrate_Rating AS webrateRating\n" +
                ",pwd.Webrate_RateValue_Max AS webrateRateValueMax\n" +
                ",pwd.Webrate_RateValue_Display\n" +
                ",pd.Name As productName" +
                " FROM Webrate pwd\n" +
                " INNER JOIN (\n" +
                " SELECT wst.Webrate_Source_Name AS sourceName\n" +
                ",wsp.Webrate_Source_Property_ID AS sourcePropertyId\n" +
                " FROM Webrate_Source wst\n" +
                " INNER JOIN Webrate_Source_Property wsp ON wst.Webrate_Source_ID = wsp.Webrate_Source_ID\n" +
                ") AS ws ON pwd.Webrate_Source_Property_ID = ws.sourcePropertyID\n" +
                " INNER JOIN Webrate_Channel webc ON pwd.Webrate_Channel_ID = webc.Webrate_Channel_ID\n" +
                " INNER JOIN Webrate_Competitors wc ON pwd.Webrate_Competitors_ID = wc.Webrate_Competitors_ID\n" +
                " INNER JOIN Webrate_Accom_Type wat ON pwd.Webrate_Accom_Type_ID = wat.Webrate_Accom_Type_ID\n" +
                " INNER JOIN Webrate_Type wt ON pwd.Webrate_Type_ID = wt.Webrate_Type_ID\n" +
                " LEFT JOIN Webrate_Accom_Class_Mapping wacm on wacm.Webrate_Accom_Type_ID = pwd.Webrate_Accom_Type_ID\n" +
                " LEFT JOIN Webrate_Competitors_Class cl ON cl.Webrate_Competitors_ID = pwd.Webrate_Competitors_ID and wacm.Accom_Class_ID = cl.Accom_Class_ID\n" +
                " LEFT JOIN Product pd ON cl.Product_ID = pd.Product_ID" +
                " WHERE pwd.Occupancy_DT >= :startDate " +
                " ORDER BY pwd.Occupancy_DT,pwd.LOS,webc.Webrate_Channel_Name, pwd.Webrate_Status,wat.Webrate_Accom_Name,wc.Webrate_Competitors_Name,pwd.Webrate_RateValue,pwd.Webrate_RateValue_Max, pwd.Webrate_RateValue_Display, pwd.Webrate_GenerationDate";

        List<WebrateDTO> result = tenantCrudService.findByNativeQuery(query,
                QueryParameter.with("startDate", startDate).parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize(), webrateDTORowMapper());

        return result != null ? result : Collections.emptyList();
    }

    public Stream<WebrateDTO> getWebrateStreaming(DatafeedRequest datafeedRequest) {

        Date startDate = datafeedRequest.getStartDate();
        Date lastSuccessDate = datafeedRequest.getLastSuccessDate();
        String baseQuery = "SELECT ws.sourceName AS webrateSourceProperty\n" +
                ",pwd.Webrate_GenerationDate AS webrateGenerationDate\n" +
                ",wc.Webrate_Competitors_Name AS competitorsName\n" +
                ",webc.Webrate_Channel_Name AS webrateChannel\n" +
                ",wat.Webrate_Accom_Name AS webrateRoomType\n" +
                ",wt.Webrate_Type_Name AS webrateTypeName\n" +
                ",pwd.Occupancy_DT AS occupancyDate\n" +
                ",pwd.LOS AS los\n" +
                ",pwd.Webrate_Status AS webrateStatus\n" +
                ",pwd.Webrate_Currency AS webrateCurrency\n" +
                ",pwd.Webrate_RateValue AS webrateRateValue\n" +
                ",pwd.Webrate_Rank AS webrateRank\n" +
                ",pwd.Webrate_Rating AS webrateRating\n" +
                ",pwd.Webrate_RateValue_Max AS webrateRateValueMax\n" +
                ",pwd.Webrate_RateValue_Display\n" +
                ",pd.Name As productName" +
                " FROM Webrate pwd\n" +
                " INNER JOIN (\n" +
                " SELECT wst.Webrate_Source_Name AS sourceName\n" +
                ",wsp.Webrate_Source_Property_ID AS sourcePropertyId\n" +
                " FROM Webrate_Source wst\n" +
                " INNER JOIN Webrate_Source_Property wsp ON wst.Webrate_Source_ID = wsp.Webrate_Source_ID\n" +
                ") AS ws ON pwd.Webrate_Source_Property_ID = ws.sourcePropertyID\n" +
                " INNER JOIN Webrate_Channel webc ON pwd.Webrate_Channel_ID = webc.Webrate_Channel_ID\n" +
                " INNER JOIN Webrate_Competitors wc ON pwd.Webrate_Competitors_ID = wc.Webrate_Competitors_ID\n" +
                " INNER JOIN Webrate_Accom_Type wat ON pwd.Webrate_Accom_Type_ID = wat.Webrate_Accom_Type_ID\n" +
                " INNER JOIN Webrate_Type wt ON pwd.Webrate_Type_ID = wt.Webrate_Type_ID\n" +
                " LEFT JOIN Webrate_Accom_Class_Mapping wacm on wacm.Webrate_Accom_Type_ID = pwd.Webrate_Accom_Type_ID\n" +
                " LEFT JOIN Webrate_Competitors_Class cl ON cl.Webrate_Competitors_ID = pwd.Webrate_Competitors_ID and wacm.Accom_Class_ID = cl.Accom_Class_ID\n" +
                " LEFT JOIN Product pd ON cl.Product_ID = pd.Product_ID" +
                " WHERE pwd.Occupancy_DT >= :startDate ";

        String dateFilter;
        QueryParameter queryParameter;

        if (lastSuccessDate != null) {
            dateFilter = " AND pwd.Webrate_GenerationDate >= :lastSuccessDate ";
            queryParameter = QueryParameter.with("startDate", startDate)
                    .and("lastSuccessDate", lastSuccessDate);
        } else {
            dateFilter = "";
            queryParameter = QueryParameter.with("startDate", startDate);
        }

        String orderBy = " ORDER BY pwd.Occupancy_DT,pwd.LOS,webc.Webrate_Channel_Name, pwd.Webrate_Status,wat.Webrate_Accom_Name," +
                "wc.Webrate_Competitors_Name,pwd.Webrate_RateValue,pwd.Webrate_RateValue_Max, pwd.Webrate_RateValue_Display, pwd.Webrate_GenerationDate";

        String query = baseQuery + dateFilter + orderBy;

        var result = tenantCrudService.findByNativeQueryStreaming(query,
                queryParameter.parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize(), webrateDTORowMapper());

        return result != null ? result : Stream.of();
    }

    public List<GroupBlockDTO> getGroupBlock(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        List<GroupBlockDetail> groupBlockDetails = tenantCrudService.findByNamedQuery(
                GroupBlockDetail.GET_BY_OCCUPANCY_DT_BETWEEN_STARTDATE_AND_ENDDATE,
                QueryParameter.with(START_DATE, new LocalDate(getStartDate(datafeedRequest, isFirstRun)))
                        .and(END_DATE, new LocalDate(datafeedRequest.getEndDate())).parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize());
        return Optional.ofNullable(groupBlockDetails)
                .orElse(Collections.emptyList()).stream()
                .map(GroupBlockDTO::new)
                .collect(Collectors.toList());
    }

    public Stream<GroupBlockDTO> getGroupBlockStreaming(DatafeedRequest datafeedRequest, boolean isFirstRun ) {

        Stream<GroupBlockDetail> groupBlockDetails = tenantCrudService.findByNamedQueryStreaming(
                GroupBlockDetail.GET_BY_OCCUPANCY_DT_BETWEEN_STARTDATE_AND_ENDDATE_DIFF,
                QueryParameter.with(START_DATE, new LocalDate(getStartDate(datafeedRequest, isFirstRun)))
                        .and(END_DATE, new LocalDate(datafeedRequest.getEndDate()))
                        .and(LAST_SUCCESS_DATE, DateUtil.convertJavaUtilDateToLocalDateTime(datafeedRequest.getLastSuccessDate()))
                        .parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize());
        return groupBlockDetails.map(GroupBlockDTO::new);
    }

    public List<GroupMasterDTO> getGroupMaster(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        List<GroupBlockMaster> groupMasterList = tenantCrudService.findByNamedQuery(GroupBlockMaster.GET_BY_BOOKING_DATE_BETWEEN_STARTDATE_AND_ENDDATE,
                QueryParameter.with(START_DATE, getStartDate(datafeedRequest, isFirstRun))
                        .and(END_DATE, datafeedRequest.getEndDate()).parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize());
        return Optional.ofNullable(groupMasterList)
                .orElse(Collections.emptyList()).stream()
                .map(GroupMasterDTO::new)
                .collect(Collectors.toList());
    }

    public Stream<GroupMasterDTO> getGroupMasterStreaming(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        var lastSuccessDate = datafeedRequest.getLastSuccessDate() == null ? null : DateUtil.convertJavaUtilDateToLocalDateTime(datafeedRequest.getLastSuccessDate());
        Stream<GroupBlockMaster> groupMasterList = tenantCrudService.findByNamedQueryStreaming(GroupBlockMaster.GET_BY_BOOKING_DATE_BETWEEN_STARTDATE_AND_ENDDATE_DIFF,
                QueryParameter.with(START_DATE, getStartDate(datafeedRequest, isFirstRun))
                        .and(END_DATE, datafeedRequest.getEndDate())
                        .and(LAST_UPDATED_DATE, lastSuccessDate).parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize());
        return Optional.ofNullable(groupMasterList)
                .orElse(Stream.of())
                .map(GroupMasterDTO::new);
    }

    public List<PaceGroupMasterDTO> getPaceGroupMaster(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        Date startDate = getStartDate(datafeedRequest, isFirstRun);
        Date endDate = datafeedRequest.getEndDate();

        List<PaceGroupMaster> paceGroupMasterList = tenantCrudService.findByNamedQuery(
                PaceGroupMaster.GET_BY_BOOKING_DATE_BETWEEN_STARTDATE_AND_ENDDATE, QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize());

        return Optional.ofNullable(paceGroupMasterList)
                .orElse(Collections.emptyList()).stream()
                .map(PaceGroupMasterDTO::new)
                .collect(Collectors.toList());
    }

    public List<LRVPaceDTO> getLRVPace(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        Date startDate = getStartDate(datafeedRequest, isFirstRun);
        Date endDate = datafeedRequest.getEndDate();

        String query = "SELECT d.Business_DT AS businessDayEndDate \n" +
                "   ,pl.Occupancy_DT AS occupancyDate \n" +
                "   ,ac.Accom_Class_Code AS roomClassCode \n" +
                "   ,pl.LRV AS lrv \n" +
                "FROM PACE_LRV pl \n" +
                "INNER JOIN Decision d ON pl.Decision_ID = d.Decision_ID \n" +
                "INNER JOIN Accom_Class ac ON pl.Accom_Class_ID = ac.Accom_Class_ID \n" +
                "WHERE pl.Occupancy_DT BETWEEN :startDate AND :endDate ";

        List<LRVPaceDTO> result = tenantCrudService.findByNativeQuery(query,
                QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize(), lrvPaceDTORowMapper());
        return result != null ? result : Collections.emptyList();
    }

    public List<ComponentRoomMappingDTO> getComponentRoomMapping() {
        List<CRAccomTypeMapping> crAccomTypeMappings = componentRoomService.getComponentRoomsMappings();
        return crAccomTypeMappings.stream().sorted(Comparator.comparing(CRAccomTypeMapping::getCrAccomType)).map(ComponentRoomMappingDTO::new).collect(Collectors.toList());
    }

    public List<STRDailyDTO> getSTRDailyData(Date startDate, Date endDate) {
        List<STRDaily> strs = tenantCrudService.findByNamedQuery
                (STRDaily.BY_DATE_RANGE, QueryParameter.with("startDate", DateUtil.convertDateToLocalDate(startDate)).and("endDate", DateUtil.convertDateToLocalDate(endDate)).parameters());
        return strs.stream().sorted(Comparator.comparing(STRDaily::getOccupancyDate)).map(STRDailyDTO::new).collect(Collectors.toList());
    }

    public List<STRMonthlyDTO> getSTRMonthlyData() {
        List<STRMonthly> strs = tenantCrudService.findAll(STRMonthly.class);
        return strs.stream().sorted(Comparator.comparing(STRMonthly::getDate)).map(STRMonthlyDTO::new).collect(Collectors.toList());
    }

    public List getHotelCapacityDemand360(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        List<Demand360MarketSegmentHistoryCapacity> compCapacities = tenantCrudService.findByNamedQuery
                (Demand360MarketSegmentHistoryCapacity.BY_DATE_RANGE, QueryParameter.with("startDate", DateUtil.convertDateToLocalDate(getStartDate(datafeedRequest, isFirstRun))).and("endDate", DateUtil.convertDateToLocalDate(datafeedRequest.getEndDate())).parameters());

        return compCapacities.stream().sorted(Comparator.comparing(Demand360MarketSegmentHistoryCapacity::getOccupancyDate)).map(D360CompCapacityDTO::new).collect(Collectors.toList());
    }

    public Stream<D360CompCapacityDTO> getHotelCapacityDemand360Streaming(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        Stream<Demand360MarketSegmentHistoryCapacity> compCapacities = tenantCrudService.findByNamedQueryStreaming
                (Demand360MarketSegmentHistoryCapacity.BY_DATE_RANGE_DIFF,
                        QueryParameter.with("startDate", DateUtil.convertDateToLocalDate(getStartDate(datafeedRequest, isFirstRun)))
                        .and("endDate", DateUtil.convertDateToLocalDate(datafeedRequest.getEndDate()))
                        .and("lastSuccessDate", DateUtil.convertJavaUtilDateToLocalDateTime(datafeedRequest.getLastSuccessDate()))
                        .parameters());

        return compCapacities.sorted(Comparator.comparing(Demand360MarketSegmentHistoryCapacity::getOccupancyDate)).map(D360CompCapacityDTO::new);
    }

    public List getBookingSummaryDemand360(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        List<Demand360BookingSummaryPace> bookingSummaryPaces = tenantCrudService.findByNamedQuery
                (Demand360BookingSummaryPace.BY_DATE_RANGE,
                        QueryParameter.with("startDate", DateUtil.convertDateToLocalDate(getStartDate(datafeedRequest, isFirstRun)))
                                .and("endDate", DateUtil.convertDateToLocalDate(datafeedRequest.getEndDate())).parameters(),
                        datafeedRequest.getStartPosition(), datafeedRequest.getSize());
        return bookingSummaryPaces.stream().map(D360BookingSummaryDTO::new).collect(Collectors.toList());
    }

    public Stream<D360BookingSummaryDTO> getBookingSummaryDemand360Streaming(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        Stream<Demand360BookingSummaryPace> bookingSummaryPaces = tenantCrudService.findByNamedQueryStreaming(
                Demand360BookingSummaryPace.BY_DATE_RANGE_DIFF,
                        QueryParameter.with("startDate", DateUtil.convertDateToLocalDate(getStartDate(datafeedRequest, isFirstRun)))
                                .and("endDate", DateUtil.convertDateToLocalDate(datafeedRequest.getEndDate()))
                                .and("lastUpdatedDate", DateUtil.convertJavaUtilDateToLocalDateTime(datafeedRequest.getLastSuccessDate())).parameters(),
                        datafeedRequest.getStartPosition(), datafeedRequest.getSize());
        return bookingSummaryPaces.map(D360BookingSummaryDTO::new);
    }

    private Integer getHistoryDataOffsetValue() {
        return pacmanConfigParamsService.getIntegerParameterValue(IntegrationConfigParamName.OPTIX_DATAFEED_HISTORY_DATA_OFFSET.value());
    }

    private Date getStartDate(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        Date startDate = datafeedRequest.getStartDate();
        Integer historyDataOffset = getHistoryDataOffsetValue();

        if ((isFirstRun && historyDataOffset > 0) || shouldIncludeHistoryData(datafeedRequest)) {
            startDate = dateService.getCaughtUpLocalDate().minusDays(historyDataOffset).toDate();
        }

        return startDate;
    }

    public boolean shouldIncludeHistoryData(DatafeedRequest datafeedRequest) {
        return datafeedRequest == null ? Boolean.FALSE : endpointService.shouldIncludeHistoryFor(datafeedRequest.getDataFeedType());
    }

    public List<PropertyOnBooksPaceAlertDTO> getPropertyOnBooksPaceAlerts() {
        List<PropertyOnBooksPaceAlert> propertyOnBooksPaceAlerts = tenantCrudService.findByNamedQuery(PropertyOnBooksPaceAlert.FIND_ALL_PROPERTY_ON_BOOKS_PACE_ALERTS_ORDER_BY_OCCUPANCY_DATE);
        return propertyOnBooksPaceAlerts.stream().map(alert -> {
            PropertyOnBooksPaceAlertDTO dto = new PropertyOnBooksPaceAlertDTO();
            dto.setOccupancyDate(alert.getOccupancyDate());
            dto.setPaceSeverityScore(alert.getPaceSeverityScore());
            dto.setActualOnBooks(alert.getActualOnBooks());
            dto.setExpectedOnBooks(alert.getExpectedOnBooks());
            dto.setExpectedOnBooksLowerBound(alert.getExpectedOnBooksLowerBound());
            dto.setExpectedOnBooksUpperBound(alert.getExpectedOnBooksUpperBound());
            dto.setCreateDateDTTM(alert.getCreateDateDTTM());
            return dto;
        }).collect(Collectors.toList());
    }

    public List<PropertyLevelDataDTO> getPropertyLevelData(DatafeedRequest datafeedRequest, int isDatafeedSpecialEventInstanceNameEnabled, boolean isFirstRun) {
        Date startDate = getStartDate(datafeedRequest, isFirstRun);
        List<PropertyLevelData> propertyLevelData = tenantCrudService.findByNamedQuery(PropertyLevelData.FIND_BY_DATES_BETWEEN,
                QueryParameter.with("startDate", startDate)
                        .and("endDate", datafeedRequest.getEndDate())
                        .and("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("isSpecialEventInstanceNameEnabled", isDatafeedSpecialEventInstanceNameEnabled).parameters());
        return propertyLevelData.stream().map(data -> {
            PropertyLevelDataDTO propertyLevelDataDTO = new PropertyLevelDataDTO();
            propertyLevelDataDTO.setOccupancyDate(data.getOccupancyDate());
            propertyLevelDataDTO.setComparisonDateLastYear(data.getComparisonDateLastYear());
            propertyLevelDataDTO.setPropertyOverbooking(data.getPropertyOverbooking());
            propertyLevelDataDTO.setSpecialEventNameThisYear(data.getSpecialEventNameThisYear());
            propertyLevelDataDTO.setSpecialEventNameLastYear(data.getSpecialEventNameLastYear());
            propertyLevelDataDTO.setBudgetRoomSold(data.getBudgetRoomSold());
            propertyLevelDataDTO.setBudgetRoomRevenue(data.getBudgetRoomRevenue());
            return propertyLevelDataDTO;
        }).collect(Collectors.toList());
    }

    public List getOptixPricingSensitivityCoefficientCfgs(DatafeedRequest datafeedRequest) {
        List<PricingSensitivityCoefficient> pricingSensitivityCoefficients = tenantCrudService.findByNamedQuery(PricingSensitivityCoefficient.GET_PRICING_SENSITIVITY,
                datafeedRequest.getStartPosition(), datafeedRequest.getSize());
        return Optional.ofNullable(pricingSensitivityCoefficients).orElse(Collections.emptyList());
    }
}
