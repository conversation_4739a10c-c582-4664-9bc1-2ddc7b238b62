package com.ideas.tetris.pacman.services.client.service;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.integration.ratchet.services.RatchetService;
import com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.RatchetClient;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientAttribute;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientAttributeValue;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientCriteria;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.RatchetCrudServiceBean;
import com.ideas.tetris.pacman.services.customattributeservice.service.CustomAttributeService;
import com.ideas.tetris.pacman.services.email.EmailService;
import com.ideas.tetris.pacman.services.remoteAgent.RemoteAgentConfigService;
import com.ideas.tetris.pacman.services.remoteAgent.entity.RemoteAgent;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.security.RoleService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.MessageFormat;
import java.util.*;

import static com.ideas.tetris.pacman.common.constants.Constants.*;

@Transactional(propagation = Propagation.REQUIRES_NEW)
@Component
public class ClientService {
    private static final Logger LOGGER = Logger.getLogger(ClientService.class);
    private static final String OPEN_AM_BATCH = "C:/Programs/OpenAM-9.5.4/bin/openamadm.bat {0} -u amadmin -f " +
            "C:/Programs/OpenAM-9.5.4/bin/pwd.txt -e {1}";
    private static final String TRAVELODGE = "TRAVELODGE";
    private static String CLIENT_CONTEXT = "pacman.%s";
    private static final String LDAP_DATA_STORE_NAME = "OpenDS";
    private static final String GLOBAL_DB_DATA_STORE_NAME = "GlobalDatabase";
    private static final String GLOBAL_DB_DATA_STORE_TYPE = "Database";
    public static final String IDEAS_REALM_NAME = "ideas";
    private static final String ERROR = "Error ";

    @Autowired
	public ClientConfigService clientConfigService;
    @Autowired
    private JobServiceLocal jobService;
    @GlobalCrudServiceBean.Qualifier
	@Qualifier("globalCrudServiceBean")
    @Autowired
    private CrudService globalCrudService;
    @Autowired
    private PacmanConfigParamsService configService;
    @Autowired
    private RatchetService ratchetService;
    @Autowired
    private ClientPropertyCacheService clientPropertyCacheService;
    @Autowired
    private EmailService emailService;
    @Autowired
    private CustomAttributeService customAttributeService;
    @Autowired
    private RemoteAgentConfigService remoteAgentConfigService;
    @Autowired
    private RoleService roleService;

    @RatchetCrudServiceBean.Qualifier
	@Qualifier("ratchetCrudServiceBean")
    @Autowired
    protected CrudService ratchetCrudService;

    public void addClient(AddClientDto dto) {
        Client client = getClientByCode(dto.getCode());
        if (client != null) {
            throw new TetrisException(ErrorCode.INVALID_REQUEST,
                    "Error: add client failed - existing client with client code " + dto.getCode());
        }
        String userId = PacmanWorkContextHelper.getUserId();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.CLIENT_CODE, dto.getCode());
        parameters.put(JobParameterKey.CLIENT_NAME, dto.getName());
        parameters.put(JobParameterKey.EXTERNAL_SYSTEM_NAME, dto.getExternalSystem());
        if (dto.getExternalSubSystem() != null) {
            parameters.put(JobParameterKey.EXTERNAL_SUB_SYSTEM_NAME, dto.getExternalSubSystem());
        }
        parameters.put(JobParameterKey.USER_ID, userId);
        parameters.put(JobParameterKey.DATE, (new Date()).toString());

        if (dto.getOpmsInstallationType() != null) {
            parameters.put(JobParameterKey.OPERA_AGENT_OPMS_INSTALL_TYPE, dto.getOpmsInstallationType());
        }
        if (dto.getOrsInstallationType() != null) {
            parameters.put(JobParameterKey.OPERA_AGENT_ORS_INSTALL_TYPE, dto.getOrsInstallationType());
        }
        jobService.startJob(JobName.AddClientJob, parameters);
    }

    public void addClientToOpenAm(String clientCode) {
        if (!SystemConfig.isOpenAmDisabled()) {
            // delete the realm, if it already exists
            removeClientFromOpenAm(clientCode); // if previously added, remove

            // add the realm
            String openAmCreateScript = constructOpenAmRealmCreationString(clientCode + DB_REALM_SUFFIX);
            runCommandLineAndWait(openAmCreateScript);

            // create the DataStore
            createDataStore(clientCode);
        }
    }

    public void createDataStore(String clientCode) {
        clientCode = clientCode + DB_REALM_SUFFIX;
        String existingDataStore = getDataStoreName(clientCode);
        String deleteDataStoreScript = constructOpenAmDeleteDataStoreString(clientCode, existingDataStore);
        runCommandLineAndWait(deleteDataStoreScript);
        File tmpOpenAmConfigfile = null;
        try {
            tmpOpenAmConfigfile = File.createTempFile(clientCode, "conf");
            FileUtils.writeStringToFile(tmpOpenAmConfigfile, GLOBAL_DATABASE_DS_CONFIG);
            String addDataStoreScript = constructOpenAmCreateDataStoreString(clientCode, GLOBAL_DB_DATA_STORE_NAME, tmpOpenAmConfigfile.getPath(), GLOBAL_DB_DATA_STORE_TYPE);
            runCommandLineAndWait(addDataStoreScript);
        } catch (IOException ioe) {
            throw new TetrisException(ErrorCode.ADD_G3_CLIENT,
                    "Looks like there was an file access issue when adding a client ", ioe);
        } finally {
            if (null != tmpOpenAmConfigfile) {
                FileUtils.deleteQuietly(tmpOpenAmConfigfile);
            }
        }

    }


    public String runCommandLineAndWait(String command) {
        StringBuilder output = new StringBuilder();
        Process process;
        try {
            LOGGER.info("Running command " + command);
            process = Runtime.getRuntime().exec(command);
            process.waitFor();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {

                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            LOGGER.info("Command Completed " + command + "\n Response : " + output.toString());
            return output.toString();
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.ADD_G3_CLIENT, "An issue occurred when ", e);
        }
    }

    /*
     * Can't seem to pass host to create-realm subcommand:
     * https://docs.oracle.com/cd/E19316-01/820-3886/ssoadm/index.html
     *
     * Looked at openam rest services but coud not establish connection:
     * http://openam.forgerock.org/openam-documentation/openam-doc-source/doc/webhelp/dev-guide/rest-api-serverinfo.html
     *
     * Think our version 9.5.4 only has some old web service implementation. 10 or 11 has the nice json rest services
     */
    protected String constructOpenAmRealmCreationString(final String clientCode) {
        return MessageFormat.format(OPEN_AM_BATCH, "create-realm", clientCode);
    }

    protected String constructOpenAmRealmDeletionString(final String clientCode) {
        return MessageFormat.format(OPEN_AM_BATCH, "delete-realm", clientCode);
    }


    private String constructOpenAmDeleteDataStoreString(final String clientCode, String existingDataStore) {
        return MessageFormat.format(OPEN_AM_BATCH, "delete-datastores", clientCode) + " -m " + existingDataStore;
    }

    private String constructOpenAmCreateDataStoreString(final String clientCode, String newDataStore, String filePath, String dataStoreType) {
        return MessageFormat.format(OPEN_AM_BATCH, "create-datastore", clientCode) + " -m " + newDataStore + " -D " + filePath + " -t " + dataStoreType;
    }

    private String constructOpenAmRealmListString() {
        return MessageFormat.format(OPEN_AM_BATCH, "list-realms", "/");
    }

    private String constructOpenAmListDataStoresString(final String clientCode) {
        return MessageFormat.format(OPEN_AM_BATCH, "list-datastores", clientCode);
    }

    protected static final String GLOBAL_DATABASE_DS_CONFIG =
            "sun-opensso-database-JDBCDbuser=" + SystemConfig.getDatabaseUsername() + "\n"
                    + "sun-opensso-database-UserIDAttr=User_ID\n"
                    + "sun-opensso-database-inactiveValue=2\n"
                    + "sun-opensso-database-UserPasswordAttr=Password\n"
                    + "sun-opensso-database-config-users-search-attribute=cn\n"
                    + "sun-opensso-database-activeValue=1\n"
                    + "sunIdRepoAttributeMapping=iplanet-am-user-password-reset-question-answer=iplanet_am_user_password_reset_question_answer\n"
                    + "sunIdRepoAttributeMapping=iplanet-am-user-failure-url=iplanet_am_user_failure_url\n"
                    + "sunIdRepoAttributeMapping=iplanet-am-user-password-reset-force-reset=iplanet_am_user_password_reset_force_reset\n"
                    + "sunIdRepoAttributeMapping=iplanet-am-user-password-resetoptions=iplanet_am_user_password_resetoptions\n"
                    + "sunIdRepoAttributeMapping=iplanet-am-user-auth-config=iplanet_am_user_auth_config\n"
                    + "sunIdRepoAttributeMapping=iplanet-am-user-alias-list=iplanet_am_user_alias_list\n"
                    + "sunIdRepoAttributeMapping=iplanet-am-user-success-url=iplanet_am_user_success_url\n"
                    + "sunIdRepoAttributeMapping=sn=Last_Name\n"
                    + "sunIdRepoAttributeMapping=mail=Email_Address\n"
                    + "sunIdRepoAttributeMapping=cn=User_name\n"
                    + "sunIdRepoAttributeMapping=givenname=First_Name\n"
                    + "sun-opensso-database-UserTableName=Users\n"
                    + "sun-opensso-database-DataSourceJndiName=java:comp/env/jdbc/Global\n"
                    + "sun-opensso-database-MembershipIDAttr=group_name\n"
                    + "sun-opensso-database-JDBCDriver=com.microsoft.sqlserver.jdbc.SQLServerDriver\n"
                    + "sun-opensso-database-config-max-result=1000\n"
                    + "sun-opensso-database-UserStatusAttr=Status_ID\n"
                    + "sun-opensso-database-sunIdRepoSupportedOperations=user=read,create,edit,delete,service\n"
                    + "sun-opensso-database-sunIdRepoSupportedOperations=group=read,create,edit,delete\n"
                    + "sun-opensso-database-membership-search-attribute=cn\n"
                    + "sun-opensso-database-dao-class-name=com.sun.identity.idm.plugins.database.JdbcSimpleUserDao\n"
                    + "sun-opensso-database-MembershipTableName=groups\n"
                    + "sun-opensso-database-JDBCDbpassword=" + SystemConfig.getDatabasePassword() + "\n"
                    + "RequiredValueValidator=com.sun.identity.sm.RequiredValueValidator\n"
                    + "sun-opensso-database-UserAttrs=User_ID\n"
                    + "sun-opensso-database-UserAttrs=Status_ID\n"
                    + "sun-opensso-database-UserAttrs=Password\n"
                    + "sun-opensso-database-UserAttrs=Last_Name\n"
                    + "sun-opensso-database-UserAttrs=First_Name\n"
                    + "sun-opensso-database-UserAttrs=Email_Address\n"
                    + "sun-opensso-database-UserAttrs=User_Name\n"
                    + "sun-opensso-database-JDBCUrl=jdbc:sqlserver://" + SystemConfig.getDbHost() + ":" + SystemConfig.getDbPort() + ";DatabaseName=" + SystemConfig.getDbName() + "\n"
                    + "sun-opensso-database-dao-JDBCConnectionType=JNDI\n"
                    + "sunIdRepoClass=com.sun.identity.idm.plugins.database.DatabaseRepo";


    public void deleteClient(String clientCode) {
        List<Property> properties = globalCrudService.findByNamedQuery(Property.GET_ACTIVE_PROPERTY_BY_CLIENT_CODE, QueryParameter.with(Property.PARAM_CLIENT_CODE, clientCode).parameters());
        if (!properties.isEmpty()) {
            return;
        }
        try {
            removeClientFromOpenAm(clientCode);
        } catch (Exception e) {
            LOGGER.error(ERROR, e);
        }
        List<Client> results = getClientsByCriteria(clientCode);
        if (!results.isEmpty()) {
            Client client = results.get(0);
            deleteClientReferencesFromRatchetDB(client);
            deleteGlobalParametersForClient(clientCode);
            deleteRemoteAgents(client);
            clientConfigService.deleteUsersFromClient(client.getId());
            deleteClientAttributes(client);
            clientPropertyCacheService.removeClientFromCache(client);
            deleteClientReferencesFromGlobalDB(client);
            globalCrudService.delete(client);
        }
    }

    private List<Client> getClientsByCriteria(String clientCode) {
        ClientCriteria criteria = new ClientCriteria();
        criteria.setCode(clientCode);
        return getClients(criteria);
    }

    public void deleteClientReferencesFromGlobalDB(Client client) {
        Map<String, Object> parametersClientID = QueryParameter.with("clientID", client.getId()).parameters();
        globalCrudService.executeUpdateByNativeQuery("delete from Announcement_Client where Client_ID = :clientID", parametersClientID);
        globalCrudService.executeUpdateByNativeQuery(new StringBuilder().append("delete from Client_Attribute_Value where Client_Attribute_ID in ")
                .append("(select Client_Attribute_ID from Client_Attribute where client_id =:clientID)")
                .toString(), parametersClientID);
        globalCrudService.executeUpdateByNativeQuery("delete from Client_Attribute where Client_ID = :clientID", parametersClientID);
        globalCrudService.executeUpdateByNativeQuery(new StringBuilder().append("delete from Client_Business_Group_Mkt_Seg_Code where Client_Business_Group_ID in ")
                .append("(select Client_Business_Group_ID from Client_Business_Group a inner join Client_Business_View b on a.Client_Business_View_ID = b.Client_Business_View_ID ")
                .append("where b.Client_ID = :clientID)").toString(), parametersClientID);
        globalCrudService.executeUpdateByNativeQuery("delete from [dbo].[Client_Business_Group] where [Client_Business_View_ID] in (select [Client_Business_View_ID] from [dbo].[Client_Business_View]  where [Client_ID] = :clientID)", parametersClientID);
        globalCrudService.executeUpdateByNativeQuery("delete from Client_Business_View where Client_ID = :clientID", parametersClientID);
        globalCrudService.executeUpdateByNativeQuery("delete from [dbo].[Grp_Evl_Multi] where [Property_Group_ID] in (select [Property_Group_ID] from [dbo].[Property_Group]  where [Client_ID] = :clientID)", parametersClientID);
        globalCrudService.executeUpdateByNativeQuery("delete from [dbo].[system_usage] where [Property_ID] in (select [Property_ID] from [dbo].[Property]  where [Client_ID] = :clientID)", parametersClientID);
        globalCrudService.executeUpdateByNativeQuery("delete from [dbo].[Property_Group] where [Client_ID] = :clientID", parametersClientID);
        globalCrudService.executeUpdateByNativeQuery("delete from [dbo].[Rule_Attribute_Value] where [Rule_ID] in (select [Rule_ID] from [dbo].[Rules]  where [Client_ID] = :clientID)", parametersClientID);
        globalCrudService.executeUpdateByNativeQuery("delete from Rules where Client_ID = :clientID", parametersClientID);
        globalCrudService.executeUpdateByNativeQuery("delete from Datafeed_Endpoint_Product_Code_Mapping where Datafeed_Endpoint_ID in (select Datafeed_Endpoint_ID from Datafeed_Endpoint where Client_ID = :clientID)", parametersClientID);
        globalCrudService.executeUpdateByNativeQuery("delete from Datafeed_Endpoint where Client_ID = :clientID", parametersClientID);
        roleService.deleteForClient(client.getCode());
        roleService.deleteUsersFromUserAuthGroupRolesForClient(client.getCode());
    }

    public void deleteClientReferencesFromRatchetDB(Client client) {
        RatchetClient ratchetClient = ratchetCrudService.findByNamedQuerySingleResult(RatchetClient.BY_CLIENT_CODE,
                QueryParameter.with(RatchetClient.PARAM_CLIENT_CODE, client.getCode().toUpperCase()).parameters());
        if (null != ratchetClient) {
            Map<String, Object> parametersClientID = QueryParameter.with("ratchetClientID", ratchetClient.getId()).parameters();
            ratchetCrudService.executeUpdateByNativeQuery("delete from Client_Srp_Mapping where Ratchet_Client_ID = :ratchetClientID", parametersClientID);
            ratchetCrudService.executeUpdateByNativeQuery("delete from [dbo].[Ratchet_Exploded_Mcat_Attributes] where Ratchet_Client_ID = :ratchetClientID", parametersClientID);
            ratchetService.deleteClient(client.getCode());
        }
    }

    private void deleteRemoteAgents(Client client) {
        deleteRemoteAgents(client.getId());
    }

    public void deleteRemoteAgents(Integer clientId) {
        List<RemoteAgent> remoteAgents = remoteAgentConfigService.getRemoteAgents(clientId);
        for (RemoteAgent remoteAgent : remoteAgents) {
            remoteAgentConfigService.deleteRemoteAgent(remoteAgent);
        }
    }

    public void deleteClientAttributes(Client client) {
        PacmanWorkContextHelper.setClientId(client.getId());
        List<ClientAttribute> attributes = customAttributeService.getAllClientAttribute();
        for (ClientAttribute attribute : attributes) {
            List<ClientAttributeValue> values = new ArrayList<>();
            values.addAll(attribute.getClientAttributeValues());
            customAttributeService.deleteClientAttributeValue(values);
            customAttributeService.deleteClientAttribute(attribute);
        }
    }

    public void removeClientFromOpenAm(String clientCode) {
        if (!SystemConfig.isOpenAmDisabled()) {
            String openAmDeletionScript = constructOpenAmRealmDeletionString(clientCode + DB_REALM_SUFFIX);
            runCommandLineAndWait(openAmDeletionScript);
        }
    }

    public void deleteGlobalParametersForClient(String clientCode) {
        String context = Constants.CONFIG_PARAMS_NODE_PREFIX + "." + clientCode;
        configService.deleteParameterValueNode(context);
    }

    public void deleteRolesForClient(String clientCode) {
        roleService.deleteForClient(clientCode);
    }

    public List<Client> getClients(ClientCriteria clientCriteria) {
        return globalCrudService.findByCriteria(clientCriteria);
    }

    public Client getClientByCode(String clientCode) {
        return clientConfigService.getClientByCode(clientCode);
    }

    public Client getClientByPropertyId(int propertyId) {
        return clientPropertyCacheService.getProperty(propertyId).getClient();
    }

    public boolean isTLUK() {
        return PacmanWorkContextHelper.getClientCode().equalsIgnoreCase(TRAVELODGE);
    }

    public boolean isScandicOrSgroup() {
        return StringUtils.equalsIgnoreCase(SCANDIC, PacmanWorkContextHelper.getClientCode()) || StringUtils.equalsIgnoreCase(SGROUP, PacmanWorkContextHelper.getClientCode());
    }

    public boolean isHilton() {
        return PacmanWorkContextHelper.isHiltonClientCode();
    }

    public boolean isHiltonByExternalSystem() {
        boolean isHilton = false;
        Object currentExternalSystem = configService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM);
        final List<String> hiltonExternalSystem = Arrays.asList(ExternalSystem.HILSTAR.getExternalSystemName().toLowerCase(), ExternalSystem.PCRS.getExternalSystemName().toLowerCase());
        if (Objects.nonNull(currentExternalSystem) && hiltonExternalSystem.contains(currentExternalSystem.toString().toLowerCase())) {
            isHilton = true;
        }
        return isHilton;
    }

    public Client findClient(Integer clientId) {
        return clientConfigService.findClient(clientId);
    }

    public Client findClientByUpsClientUuid(String upsClientUuid) {
        return globalCrudService.findByNamedQuerySingleResult(Client.BY_UPS_CLIENT_ID,
                QueryParameter.with(Client.CLIENT_UPS_ID_CONSTANT, upsClientUuid).parameters());
    }

    public List<Client> getClients(ReservationSystem externalSystem) {
        List<Client> allClients = getClients(new ClientCriteria());
        List<Client> filteredClients = new ArrayList<>();
        for (Client client : allClients) {
            String clientExternalSystem = configService.getValue(String.format(CLIENT_CONTEXT, client.getCode()), IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value());
            if (externalSystem.getConfigParameterValue().equalsIgnoreCase(clientExternalSystem)) {
                filteredClients.add(client);
            }
        }
        return filteredClients;
    }

    public List<RealmConfig> getRealmConfigs(String configToggleName) {
        List<RealmConfig> results = new ArrayList<>();
        List<String> realmNames = getRealmNames();
        for (String realmName : realmNames) {
            if (realmName.equalsIgnoreCase(IDEAS_REALM_NAME)) {
                results.add(createIdeasRealmConfig(configToggleName));
            } else {
                Client client = getClientByCode(realmName);
                String paramValue = configService.getValue(String.format(CLIENT_CONTEXT, realmName), configToggleName);
                boolean toggleValue = Boolean.parseBoolean(paramValue);
                if (client != null) {
                    String dataStoreName = getDataStoreName(realmName);
                    RealmConfig realmConfig = new RealmConfig();
                    realmConfig.setClientCode(client.getCode());
                    realmConfig.setClientName(client.getName());
                    realmConfig.setClientId(client.getId());
                    realmConfig.setUsesOpenDS(dataStoreName.equalsIgnoreCase(LDAP_DATA_STORE_NAME));
                    realmConfig.setOpenDSFeatureToggle(toggleValue);
                    results.add(realmConfig);
                }
            }
        }
        return results;
    }

    private RealmConfig createIdeasRealmConfig(String configToggleName) {
        String paramValue = configService.getValue("pacman", configToggleName);
        boolean toggleValue = Boolean.parseBoolean(paramValue);
        RealmConfig realmConfig = new RealmConfig();
        realmConfig.setClientCode(IDEAS_REALM_NAME);
        realmConfig.setClientName("IDeaS");
        realmConfig.setClientId(-1);
        String dataStoreName = getDataStoreName(IDEAS_REALM_NAME);
        realmConfig.setUsesOpenDS(dataStoreName.equalsIgnoreCase(LDAP_DATA_STORE_NAME));
        realmConfig.setOpenDSFeatureToggle(toggleValue);
        return realmConfig;
    }


    @SuppressWarnings("unchecked")
    public List<Integer> getActiveClientIds() {
        return globalCrudService.findByNamedQuery(Client.GET_ACTIVE_IDS);
    }

    public boolean usesOpenDS(String realmName) {
        String dataStoreName = getDataStoreName(realmName);
        return dataStoreName.equalsIgnoreCase(LDAP_DATA_STORE_NAME);
    }

    private String getDataStoreName(String realmName) {
        String openAmListScript = constructOpenAmListDataStoresString(realmName);
        String cmdOutput = runCommandLineAndWait(openAmListScript);
        String[] dataStoreNames = cmdOutput.split("[\n]");
        return dataStoreNames.length == 0 ? "" : dataStoreNames[dataStoreNames.length - 1].trim();
    }

    private List<String> getRealmNames() {
        List<String> results = new ArrayList<>();
        String openAmListScript = constructOpenAmRealmListString();
        String cmdOutput = runCommandLineAndWait(openAmListScript);
        String[] realmNames = cmdOutput.split("[\n]");
        for (String name : realmNames) {
            if (name.trim().length() > 0 && !name.contains("Search completed")) {
                results.add(name.trim());
            }
        }
        return results;
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    @ForTesting
    public void setRatchetService(RatchetService ratchetService) {
        this.ratchetService = ratchetService;
    }

    @ForTesting
    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigService) {
        this.configService = pacmanConfigService;
    }

    @ForTesting
    public void setRemoteAgentConfigService(RemoteAgentConfigService remoteAgentConfigService) {
        this.remoteAgentConfigService = remoteAgentConfigService;
    }

    @ForTesting
    public void setCustomAttributeService(CustomAttributeService customAttributeService) {
        this.customAttributeService = customAttributeService;
    }

    @ForTesting
    public void setRatchetCrudService(CrudService ratchetCrudService) {
        this.ratchetCrudService = ratchetCrudService;
    }

    @ForTesting
    public void setRoleService(RoleService roleService) {
        this.roleService = roleService;
    }

    public Client getClient(String clientCode) {
        return clientPropertyCacheService.getClient(clientCode);
    }

    public Client getClient(Integer clientId) {
        return clientPropertyCacheService.getClient(clientId);
    }

    @ForTesting
    public void setClientPropertyCacheService(ClientPropertyCacheService clientPropertyCacheService) {
        this.clientPropertyCacheService = clientPropertyCacheService;
    }

    public void persistClient(Client client) {
        globalCrudService.save(client);
        clientPropertyCacheService.reloadClient(client.getId());
    }
}
