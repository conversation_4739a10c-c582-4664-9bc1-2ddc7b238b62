package com.ideas.tetris.pacman.services.authgroup.services;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.authgroup.repositories.AuthGroupManagementRepository;
import com.ideas.tetris.pacman.services.fds.uas.UASService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.AuthorizationGroup;
import com.ideas.tetris.platform.services.daoandentities.entity.AuthorizationGroupPropertyMapping;

import javax.inject.Inject;
import java.util.List;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.util.Runner.runIfTrue;
import static java.util.Collections.emptyList;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class AuthGroupManagementService {

    @Autowired
	private AuthGroupManagementRepository authGroupManagementRepository;
    @Autowired
	private UASService uasService;
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;

    public List<Integer> save(AuthorizationGroup group) {
        return syncUsersAndProperties(group.getId(), group.getAuthGroupPropertyMappings().stream()
                .map(AuthorizationGroupPropertyMapping::getPropertyId)
                .collect(Collectors.toList()));
    }

    public void delete(int authGroupId) {
        AuthorizationGroup groupToBeDeleted = authGroupManagementRepository.getAuthorizationGroupById(authGroupId);
        String uasAuthGroupUuid = groupToBeDeleted != null ? groupToBeDeleted.getUasAuthGroupUuid() : null;
        runIfTrue(null != groupToBeDeleted, () -> {
            syncUsersAndProperties(groupToBeDeleted.getId(), emptyList());
            authGroupManagementRepository.delete(groupToBeDeleted.getId());
        });

        if (isFDSAuthGroupsEnabled()) {
            uasService.deleteAuthGroupInFDS(uasAuthGroupUuid);
        }
    }

    public boolean isFDSAuthGroupsEnabled() {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName()));
    }

    private List<Integer> syncUsersAndProperties(int authGroupId, List<Integer> properties) {
        return authGroupManagementRepository.syncUsersAndProperties(authGroupId, properties);
    }
}
