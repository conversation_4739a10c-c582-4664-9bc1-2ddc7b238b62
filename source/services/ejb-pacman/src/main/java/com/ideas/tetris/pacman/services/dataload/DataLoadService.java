package com.ideas.tetris.pacman.services.dataload;

import com.ideas.g3.integration.opera.dto.OperaCatchupDataWrapper;
import com.ideas.g3.integration.opera.dto.OperaDataLoadTypeCode;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.configparams.RemoteAgentConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.cdp.ConfigurationService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.CdpSchedule;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.contextholder.WorkContextRestEasyController;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.entity.JobExecutionParamsPK;
import com.ideas.tetris.pacman.services.job.entity.JobView;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.DailyProcessingService;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputProcessing;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputProcessingCriteria;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputType;
import com.ideas.tetris.pacman.services.ngi.dto.DataLoadResultMapper;
import com.ideas.tetris.pacman.services.opera.OperaIncomingFile;
import com.ideas.tetris.pacman.services.opera.OperaIncomingFileType;
import com.ideas.tetris.pacman.services.opera.OperaIncomingMetadata;
import com.ideas.tetris.pacman.services.opera.OperaTransaction;
import com.ideas.tetris.pacman.services.opera.OperaTransactionCriteria;
import com.ideas.tetris.pacman.services.opera.OperaTransactionHistory;
import com.ideas.tetris.pacman.services.opera.OperaTransactionHistoryCriteria;
import com.ideas.tetris.pacman.services.opera.OperaUtilityService;
import com.ideas.tetris.pacman.services.opera.entity.DataLoadMetadata;
import com.ideas.tetris.pacman.services.opera.entity.OperaPerformanceMonitor;
import com.ideas.tetris.pacman.services.opera.metric.OperaMetrics;
import com.ideas.tetris.pacman.services.property.PropertyRolloutService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.util.rest.G3RestClient;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.crudservice.TableBatchAware;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.rest.mapper.NGIStatisticsStatus;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.common.util.jdbc.JpaJdbcUtil;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.LocalTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.io.StringReader;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.PROCESS_STATUS_SUCCESSFUL;
import static com.ideas.tetris.platform.common.externalsystem.ExternalSystem.OPERA_AGENT;
import static com.ideas.tetris.platform.common.job.JobName.*;
import static com.ideas.tetris.platform.common.job.JobParameterKey.*;

@Component
@Transactional
public class DataLoadService {
    public static final String CORRELATION_ID = "correlationId";
    public static final String ZERO = "0";
    public static final String INPUT_PROCESSING_ID = "inputProcessingId";
    public static final OperaMetrics<DataLoadFeedMetricType> metricsFeedLoad = new OperaMetrics<>();

    protected static final String GET_HISTORY_BY_PROPERTY_ID = new StringBuilder("SELECT Past_Days, Future_Days, ")
            .append("Business_DT, Business_Time, Prepared_DT, Prepared_Time, Data_Load_Metadata_ID ")
            .append("FROM opera.History_Incoming_Metadata WHERE Property_Id = :propertyId ").toString();
    public static final String GET_RESORT_FOR_CORRELATION_ID = "select resort from opera.History_Occupancy_Summary where " +
            "History_Occupancy_Summary.Raw_Occupancy_Summary_ID = " +
            "(select MAX(Raw_Occupancy_Summary_ID) from opera.History_Occupancy_Summary) ";
    public static final String GET_LAST_SUCCESSFUL_OPERA_DATA_LOAD = "Select max(Snapshot_DT) " + "from File_Metadata where File_Name = 'OperaDataLoad'";
    protected static final String RETRIEVE_ALL_CORRELATION_IDS_FOR_FULL_FEEDS = " SELECT a.Correlation_ID FROM opera.Data_Load_Metadata odlm," +
            "   (SELECT COUNT(DISTINCT (dlm.Incoming_File_Type_Code)) AS NumFiles, dlm.Correlation_ID FROM opera.Data_Load_Metadata dlm GROUP BY dlm.Correlation_ID) a " +
            " WHERE a.NumFiles = :numberOfInputFileTypes AND odlm.Correlation_ID = a.Correlation_ID AND odlm.Incoming_File_Type_Code = 'YC' ORDER BY odlm.Create_DT ASC ";

    private static final String SQL_PARAM_DATE_START = "startDate";
    private static final String SQL_PARAM_DATE_END = "endDate";
    static final String SQL_ACTIVITY_DATA_CATCHUP_FOR_DATE_RANGE = new StringBuilder()
            .append("     SELECT DISTINCT correlation_id, create_dt FROM opera.Data_Load_Metadata odlm ")
            .append(" where (select COUNT(*) from opera.Data_Load_Metadata foo where foo.Correlation_ID = odlm.Correlation_ID) = 14 ")
            .append(" AND CAST(create_dt AS date) between :")
            .append(SQL_PARAM_DATE_START)
            .append("           AND :")
            .append(SQL_PARAM_DATE_END)
            .toString();
    private static final String UPDATE_PREPARED_DT_TIME_IN_DATA_LOAD_METADATA = "update opera.Data_Load_Metadata set Create_DT=(select (Prepared_DT + ' ' + Prepared_Time) " +
            "From opera.History_Incoming_Metadata where Data_Load_Metadata_ID= (select Data_Load_Metadata_ID from opera.Data_Load_Metadata where Incoming_File_Type_Code='metadata' " +
            "and Correlation_ID= :correlationId" + ")) where Correlation_ID= :correlationId";
    private static final String DEFAULT_OPERA_SCHEMA_NAME = "opera";
    private static final int NUM_RECORDS_IN_BATCH = 1000;
    private static final Logger LOGGER = Logger.getLogger(DataLoadService.class.getName());
    private static final Integer RECORD_TYPE_BDE = 3;
    @Autowired
    JpaJdbcUtil jpaJdbcUtil;
    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService globalCrudService;
    @Autowired
    FileMetadataService fileMetadataService;
    @Autowired
    JobServiceLocal jobService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    PropertyRolloutService propertyRolloutService;
    @Autowired
    PropertyService propertyService;
    @Autowired
    WorkContextRestEasyController workContextRestEasyController;
    @Autowired
    DateService dateService;
    @Autowired
    AlertService alertService;
    @Autowired
    ConfigurationService configurationService;
    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
    DailyProcessingService dailyProcessingService;
    @Autowired
    OperaUtilityService operaUtilityService;
    @Autowired
    RestClient restClient;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;
    @Autowired
	private ExternalSystemHelper externalSystemHelper;
    @Autowired
	private JobMonitorService jobMonitorService;
    @Autowired
	private G3RestClient g3RestClient;

    public CrudService getCrudService() {
        return crudService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }


    public Boolean loadPerformanceMetrics(BufferedReader incomingRow) {
        try {
            String row = incomingRow.readLine();
            return processPerformanceMonitorRow(row);
        } catch (IOException e) {
            LOGGER.error("Could not load opera monitor data.", e);
        }
        return Boolean.FALSE;
    }

    @ForTesting
    @Justification("Loading opera data for large amounts of data for testing.")
    @Transactional(timeout = 3600)
    public void loadFile(String incomingFile, String correlationId, String fileType) {
        Reader stringReader = new StringReader(incomingFile);
        BufferedReader reader = new BufferedReader(stringReader);
        OperaIncomingFileType operaIncomingFileType = OperaIncomingFile.getByFileTypeCode(fileType);
        loadFile(reader, operaIncomingFileType, correlationId, true);
    }

    @Transactional(timeout = 3600)
    @Justification("Loading opera data for large amounts of data. Would like to see a better approach to buffering / paging, but agent is deprecated anyway.")
    public void loadFile(BufferedReader incomingFile, OperaIncomingFileType operaFileType, String correlationId,
                         boolean isChunked) {
        int rowsProcessed = 0;
        try {
            // Process header rows
            // Ignore header row for now
            incomingFile.readLine();
            metricsFeedLoad.start(DataLoadFeedMetricType.READ_INCOMING_DATA);
            String row = incomingFile.readLine();
            metricsFeedLoad.stop(DataLoadFeedMetricType.READ_INCOMING_DATA);
            final boolean isTableBatchEnabled = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_OPERA_HISTORY_TABLE_BATCH);
            if (isTableBatchEnabled) {
                processTableBatch(incomingFile, operaFileType, correlationId, isChunked, row);
            } else {
                processBatch(incomingFile, operaFileType, correlationId, isChunked, row);
            }
        } catch (Exception e) {
            LOGGER.error("Could not load raw opera data.", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, correlationId + ": Could not load raw opera data.", e);
        } finally {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Finished loading feeds to history DB " + rowsProcessed + "  rows:\n" + metricsFeedLoad);
            }
        }
    }

    private Boolean processPerformanceMonitorRow(String monitorRow) {
        OperaPerformanceMonitor opm = null;
        try {
            opm = convertRowToEntity(monitorRow);
            crudService.save(opm);
            LOGGER.info("Successfully saved monitor feed data of Propertyid-" + opm.getPropertyId() + " correlation Id-" + opm.getCorrelationId() + " feedtype-" + opm.getFeedType());
            return Boolean.TRUE;
        } catch (Exception e) {
            LOGGER.error("Error while converting  opera monitor  data.", e);
        }
        return Boolean.FALSE;
    }

    private OperaPerformanceMonitor convertRowToEntity(String monitorRow) throws ParseException {
        LOGGER.info("monitor row received from agent is - " + monitorRow);

        OperaPerformanceMonitor operaPerformanceMonitor = new OperaPerformanceMonitor();
        String[] monitorFields = monitorRow.split("\\|");
        if (monitorFields.length > 11) {
            operaPerformanceMonitor.setPropertyId(Integer.parseInt(monitorFields[0]));
            String date = monitorFields[1];
            Date businessDate = new SimpleDateFormat("yyyy-MM-dd").parse(date);
            operaPerformanceMonitor.setBusinessDate(businessDate);
            operaPerformanceMonitor.setCorrelationId(monitorFields[2]);
            operaPerformanceMonitor.setFeedType(monitorFields[3]);
            operaPerformanceMonitor.setPopulationChunkSize(Integer.parseInt(monitorFields[4]));
            operaPerformanceMonitor.setNoOfTempTablePopulationChunks(Integer.parseInt(monitorFields[5]));
            operaPerformanceMonitor.setTempTablePopulationTime(Long.parseLong(monitorFields[6]));
            operaPerformanceMonitor.setTempTablePopulationChunkDays(Integer.parseInt(monitorFields[7]));
            operaPerformanceMonitor.setReadChunkSize(Integer.parseInt(monitorFields[8]));
            operaPerformanceMonitor.setNoOfReadChunks(Integer.parseInt(monitorFields[9]));
            operaPerformanceMonitor.setReadChunkTime(Long.parseLong(monitorFields[10]));
            operaPerformanceMonitor.setNoOfRows(Integer.parseInt(monitorFields[11]));
        }
        return operaPerformanceMonitor;
    }


    private void processTableBatch(BufferedReader incomingFile, OperaIncomingFileType operaFileType, String correlationId,
                                   boolean isChunked, String row) {
        DataLoadMetadata dataLoadMetadata = createDataLoadMetadataIfNotExists(operaFileType, correlationId,
                isChunked);
        boolean isFileTypeCSATOrPSAT = isFileTypeMatchesWithDataLoadFileType(operaFileType, OperaDataLoadTypeCode.CSAT, OperaDataLoadTypeCode.PSAT);
        List<TableBatchAware> dtosToInsert = new ArrayList<>();
        try {
            while (!StringUtils.isBlank(row)) {
                String[] field = row.split("\\|", -1);
                if (isFileTypeCSATOrPSAT && isRowHasZeroValue(field)) {
                    row = incomingFile.readLine();
                    continue;
                }
                metricsFeedLoad.start(DataLoadFeedMetricType.CREATE_BATCH_STATEMENT);
                operaFileType.fillHistoryDtos(dtosToInsert, field, dataLoadMetadata);
                metricsFeedLoad.stop(DataLoadFeedMetricType.CREATE_BATCH_STATEMENT);
                metricsFeedLoad.start(DataLoadFeedMetricType.READ_INCOMING_DATA);
                row = incomingFile.readLine();
                metricsFeedLoad.stop(DataLoadFeedMetricType.READ_INCOMING_DATA);
            }
            crudService.execute(operaFileType.getHistoryInsertProcedureName(), dtosToInsert);
        } catch (IOException e) {
            LOGGER.error("Could not load raw opera data.", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, correlationId + ": Could not load raw opera data.", e);
        }
    }

    private boolean isFileTypeMatchesWithDataLoadFileType(OperaIncomingFileType operaFileType, OperaDataLoadTypeCode code1, OperaDataLoadTypeCode code2) {
        return StringUtils.equalsIgnoreCase(code1.name(), operaFileType.getFileTypeCode())
                || StringUtils.equalsIgnoreCase(code2.name(),
                operaFileType.getFileTypeCode());
    }

    @SuppressWarnings({"resource", "squid:S3776"})
    private void processBatch(BufferedReader incomingFile, OperaIncomingFileType operaFileType, String correlationId,
                              boolean isChunked, String row) {
        int numRowsProcessed = 0;
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        try {

            connection = getConnection();
            preparedStatement = connection.prepareStatement(operaFileType.getInsertHistoryPreparedStatementSql());

            DataLoadMetadata dataLoadMetadata = createDataLoadMetadataIfNotExists(operaFileType, correlationId,
                    isChunked);
            boolean isFileTypePTRANSOrCTRANS = isFileTypeMatchesWithDataLoadFileType(operaFileType, OperaDataLoadTypeCode.PTRANS, OperaDataLoadTypeCode.CTRANS);
            boolean isFileTypeCSATOrPSAT = isFileTypeMatchesWithDataLoadFileType(operaFileType, OperaDataLoadTypeCode.CSAT, OperaDataLoadTypeCode.PSAT);
            while (!StringUtils.isBlank(row)) {
                String[] field = row.split("\\|", -1);
                if (isFileTypeCSATOrPSAT && isRowHasZeroValue(field)) {
                    row = incomingFile.readLine();
                    continue;
                }

                metricsFeedLoad.start(DataLoadFeedMetricType.CREATE_BATCH_STATEMENT);
                int fieldIndex = 0;
                for (; fieldIndex < field.length; fieldIndex++) {
                    preparedStatement.setString(fieldIndex + 1, field[fieldIndex]);
                }

                if (isFileTypePTRANSOrCTRANS && fieldIndex + 1 < operaFileType.getNumberOfFields()) {
                    preparedStatement.setString(fieldIndex + 1, null);
                    fieldIndex++;
                    preparedStatement.setString(fieldIndex + 1, null);
                    fieldIndex++;
                    preparedStatement.setString(fieldIndex + 1, null);
                    fieldIndex++;

                }

                // Set file type as possible metadata - in this case data load
                // metadata ID.
                if (fieldIndex < operaFileType.getNumberOfFields()) {
                    preparedStatement.setInt(fieldIndex + 1, dataLoadMetadata.getId());
                    fieldIndex++;
                }

                if (fieldIndex != operaFileType.getNumberOfFields()) {
                    throw new TetrisException(new StringBuilder().append(correlationId).append(": Error on line #")
                            .append(numRowsProcessed + 2).append(".  Row has [").append(fieldIndex)
                            .append("] fields, but expecting [").append(operaFileType.getNumberOfFields())
                            .append("].  Row is: [").append(row).append("].").toString());
                }

                preparedStatement.addBatch();
                metricsFeedLoad.stop(DataLoadFeedMetricType.CREATE_BATCH_STATEMENT);

                numRowsProcessed++;

                metricsFeedLoad.start(DataLoadFeedMetricType.READ_INCOMING_DATA);
                row = incomingFile.readLine();
                metricsFeedLoad.stop(DataLoadFeedMetricType.READ_INCOMING_DATA);

                if (numRowsProcessed % NUM_RECORDS_IN_BATCH == 0) {
                    writeBatch(preparedStatement);
                }

            }
            if (numRowsProcessed > 0) {
                writeBatch(preparedStatement);
            }
        } catch (Exception e) {
            LOGGER.error("Could not load raw opera data.", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, correlationId + ": Could not load raw opera data.", e);
        } finally {
            if (null != preparedStatement) {
                try {
                    preparedStatement.close();
                } catch (Exception ignore) {
                    LOGGER.error("Could not close prepared statement.", ignore);
                }
            }
            if (null != connection) {
                try {
                    jpaJdbcUtil.closeConnection(crudService, connection);
                } catch (Exception ignore) {
                    LOGGER.error("Could not close connection.", ignore);
                }
            }
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug(new StringBuilder().append("Finished loading feeds to history DB ")
                        .append(numRowsProcessed).append("  rows:\n").append(metricsFeedLoad.toString()).toString());
            }
        }
    }

    private DataLoadMetadata createDataLoadMetadataIfNotExists(OperaIncomingFileType operaFileType,
                                                               String correlationId, boolean isChunked) {
        DataLoadMetadata dataLoadMetadata;
        metricsFeedLoad.start(DataLoadFeedMetricType.CREATE_DATA_LOAD_METADATA);
        if (isChunked) {

            dataLoadMetadata = createOperaDataLoadMetadata(correlationId, operaFileType);
        } else {
            dataLoadMetadata = createDataLoadMetadata(correlationId, operaFileType);
        }
        metricsFeedLoad.stop(DataLoadFeedMetricType.CREATE_DATA_LOAD_METADATA);
        return dataLoadMetadata;
    }

    private boolean isRowHasZeroValue(String[] field) {
        for (int i = 4; i < 14; i++) {
            if (!isEmptyOrZero(field[i])) {
                return false;
            }
        }
        return true;
    }

    private boolean isEmptyOrZero(String valueToEvaluate) {
        return valueToEvaluate.equals(ZERO) || valueToEvaluate.isEmpty() || "null".equalsIgnoreCase(valueToEvaluate);
    }

    private Connection getConnection() {
        return jpaJdbcUtil.getJdbcConnection(crudService);
    }

    private String getPropertyStage() {
        Property property = propertyService.getPropertyById(workContextRestEasyController.getCurrentWorkContext().getPropertyId());
        Stage stage = property.getStage();

        if (stage != null) {
            return stage.getCode();
        }

        return null;
    }

    public OperaIncomingMetadata getIncomingMetaDataRecord(String correlationId) {
        return (OperaIncomingMetadata) crudService.findByNamedQuerySingleResult(
                OperaIncomingMetadata.GET_BY_CORRELATION, QueryParameter.with("correlationId", correlationId)
                        .parameters());
    }

    public OperaIncomingMetadata getIncomingMetaDataRecord(Integer dataLoadMetadataId) {
        return (OperaIncomingMetadata) crudService.findByNamedQuerySingleResult(
                OperaIncomingMetadata.GET_BY_DATA_LOAD_META_DATA_ID,
                QueryParameter.with("dataLoadMetadataId", dataLoadMetadataId).parameters());
    }

    public boolean isForcedFeedGeneration() {
        return pacmanConfigParamsService.getBooleanParameterValue(RemoteAgentConfigParamName.GENERATE_FEED_ON_DEMAND.value(Constants.OPERA));
    }

    public void setForceFeedGenerationToFalse() {
        pacmanConfigParamsService.updateParameterValue(
                pacmanConfigParamsService.propertyNode(PacmanWorkContextHelper.getWorkContext()),
                RemoteAgentConfigParamName.GENERATE_FEED_ON_DEMAND.value(Constants.OPERA), "false");
    }

    public DataLoadMetadata createOperaDataLoadMetadata(String correlationId, OperaIncomingFileType operaFileType) {
        DataLoadMetadata dataLoadMetadata = operaUtilityService.getDataLoadMetadataForFileType(correlationId,
                operaFileType.getFileTypeCode());
        if (null == dataLoadMetadata) {
            LOGGER.info("METADATA ENTRY NEEDS TO BE CREATED");
            return createDataLoadMetadata(correlationId, operaFileType);
        } else {
            return dataLoadMetadata;
        }
    }

    private DataLoadMetadata createDataLoadMetadata(String correlationId, OperaIncomingFileType operaFileType) {
        DataLoadMetadata operaDataLoadMetadata = new DataLoadMetadata();
        operaDataLoadMetadata.setCorrelationId(correlationId);
        operaDataLoadMetadata.setIncomingFileTypeCode(operaFileType.getFileTypeCode());
        operaDataLoadMetadata.setCreateDate(new LocalDateTime());
        return crudService.save(operaDataLoadMetadata);
    }

    public int setPreparedDtTimeInOperaDataLoadMetadata(String correlationId) {
        int executeUpdate = crudService.executeUpdateByNativeQuery(UPDATE_PREPARED_DT_TIME_IN_DATA_LOAD_METADATA,
                QueryParameter.with("correlationId", correlationId).parameters());
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("No of ROWS update by are UPDATE_PREPARED_DT_TIME_IN_DATA_LOAD_METADATA " + executeUpdate);
        }
        return executeUpdate;
    }

    /**
     * Used by 2015 Agent to fire the data load job, we know all feeds have completed
     */
    public Long verifyAndCallOperaDataLoadJob(String correlationId, InputType inputType, Integer inputProcessingId) {
        alertService.resolveAllAlerts(AlertType.CRSDataDidNotArrive, PacmanWorkContextHelper.getPropertyId());
        setPreparedDtTimeInOperaDataLoadMetadata(correlationId);
        return startOperaDataLoadJob(correlationId, inputType, inputProcessingId);
    }


    protected Long startJob(Map<String, Object> parameters, InputType inputType) {
        boolean isPreCovidOn = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POPULATE_ONLY_RESERVATIONS_FROM_FEED);
        if (isPreCovidOn) {
            LOGGER.info("Customized job being started");
            return jobService.startJob(OperaCustomizedDataLoad, parameters);
        } else if (inputType.equals(InputType.BDE)) {
            LOGGER.info("BDE job being started");
            Long jobId = jobService.startJob(OperaDataLoad, parameters);
            configurationService.updateLastRunTimes();
            return jobId;
        } else if (inputType.equals(InputType.CDP)) {
            LOGGER.info("CDP job being started");
            return jobService.startJob(OperaCdpDataLoad, parameters);
        } else {
            return null;
        }
    }

    private InputProcessing getInputProcessingForCdp(List<InputProcessing> inputProcessings, CdpSchedule cdp) {
        for (InputProcessing candidate : inputProcessings) {
            if (candidate.getCdpScheduleId().equals(cdp.getId())) {
                return candidate;
            }
        }
        return null;
    }

    public boolean isTimeToSendFullExtract(LocalDateTime operaBusinessDate) {
        // If the flag is set to force a full extract or the first extract,
        // always return true.
        if (isForcedFeedGeneration()) {
            LOGGER.info("Manually requested job being started");
            setForceFeedGenerationToFalse();
            return true;
        }
        if (doStartOperaJob(getPropertyStage()) && isThisTheFirstLoad()) {
            LOGGER.info("First load requested job being started");
            return true;
        }
        LocalDateTime serverBusinessDate = getCaughtUpDate();
        operaBusinessDate = operaBusinessDate.withHourOfDay(0);
        operaBusinessDate = operaBusinessDate.withMinuteOfHour(0);
        operaBusinessDate = operaBusinessDate.withMillisOfSecond(0);
        return serverBusinessDate == null || operaBusinessDate.isAfter(serverBusinessDate);
    }

    public boolean isTimeForCdp() {
        return configurationService.isTimeForCdp();
    }

    public Long startOperaDataLoadJob(String correlationId, InputType inputType, Integer inputProcessingId) {
        String propertyStage = getPropertyStage();
        Long response = null;
        if (doStartOperaJob(propertyStage)) {
            try {
                HashMap<String, Object> parameters = new HashMap<>();
                parameters.put("propertyId", PacmanWorkContextHelper.getPropertyId().toString());
                parameters.put(CORRELATION_ID, correlationId);
                if (inputProcessingId != null) {
                    parameters.put(INPUT_PROCESSING_ID, inputProcessingId);
                }
                parameters.put("timestamp", System.currentTimeMillis());
                parameters.put(EXTERNAL_SYSTEM_CODE, OPERA_AGENT.getCode());
                parameters.put(
                        JobParameterKey.DATE,
                        DateUtil.formatDate(DateUtil.getCurrentDate(), DateUtil.DATE_TIME_FORMAT));
                response = startJob(parameters, inputType);
            } catch (Exception e) {
                throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, correlationId
                        + ": Could not invoke spring batch processing.", e);
            }
        } else {
            Property property = propertyService.getPropertyById(workContextRestEasyController.getCurrentWorkContext()
                    .getPropertyId());
            String propertyCode = property.getCode();
            String clientCode = property.getClient().getCode();
            if (inputProcessingId != null) {
                dailyProcessingService.updatePropertyDailyProcessing(inputProcessingId, correlationId, null);
            } else {
                dailyProcessingService.inputReceived(propertyCode, clientCode, correlationId);
            }

            //The correlationId is probably still associated with the old input processingID
            InputProcessing inputProcessing = dailyProcessingService.inputCompleted(propertyCode, clientCode,
                    correlationId);

            dailyProcessingService.cancelPreviousOverdueInputs(inputProcessing, null);
            if (isForcedFeedGeneration()) {
                setForceFeedGenerationToFalse();
            }
            LOGGER.debug(new StringBuilder().append("Not starting opera data load job for property/correlationId ")
                    .append(PacmanWorkContextHelper.getPropertyId().toString()).append("/").append(correlationId)
                    .append(" as it is still in ").append(propertyStage).append(" stage.").toString());
        }
        return response;
    }

    public Long forceStartOperaDataLoadJob(String correlationId, String inputType, Integer inputProcessingId) {
        HashMap<String, Object> parameters = new HashMap<>();
        parameters.put("propertyId", PacmanWorkContextHelper.getPropertyId().toString());
        parameters.put(CORRELATION_ID, correlationId);
        parameters.put(INPUT_PROCESSING_ID, inputProcessingId);
        parameters.put(TIMESTAMP, System.currentTimeMillis());
        parameters.put(
                JobParameterKey.DATE,
                DateUtil.formatDate(DateUtil.getCurrentDate(), DateUtil.DATE_TIME_FORMAT));
        parameters.put(EXTERNAL_SYSTEM_CODE, OPERA_AGENT.getCode());
        return forceStartDataLoadJob(inputType, OperaDataLoad, OperaCdpDataLoad, parameters);
    }

    public Long forceStartNgiDataLoadJob(Long jobId, Integer inputProcessingId) {
        Map<String, Object> jobParameters = getJobParameters(jobMonitorService.getJobDetail(jobId));
        jobParameters.put(JobParameterKey.USER_ID, PacmanWorkContextHelper.getUserId());
        jobParameters.put(
                JobParameterKey.DATE,
                DateUtil.formatDate(DateUtil.getCurrentDate(), DateUtil.DATE_TIME_MILLIS_FORMAT));
        jobParameters.put(JobParameterKey.INPUT_PROCESSING_ID, inputProcessingId);
        return forceStartDataLoadJob(
                (String) jobParameters.get(JobParameterKey.INPUT_TYPE),
                NGIDeferredDeliveryJob,
                NGICdpDeferredDeliveryJob,
                jobParameters);
    }


    private Map<String, Object> getJobParameters(JobView jobView) {
        return jobView.getExecutionParameters()
                .stream()
                .collect(Collectors.toMap(JobExecutionParamsPK::getKeyName, JobExecutionParamsPK::getStringValue));
    }

    private Long forceStartDataLoadJob(String inputType,
                                       JobName bdeDataLoadJobName,
                                       JobName cdpDataLoadJobName,
                                       Map<String, Object> parameters) {
        Long response = null;
        try {
            if ("BDE".equals(inputType)) {
                response = jobService.startJob(bdeDataLoadJobName, parameters);
            } else if ("CDP".equals(inputType)) {
                response = jobService.startJob(cdpDataLoadJobName, parameters);
            }

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug(parameters.get(CORRELATION_ID) + ": Received response while invoking spring batch: " + response);
            }
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, parameters.get(CORRELATION_ID)
                    + ": Could not invoke spring batch processing.", e);
        }
        return response;
    }

    public String startOperaDataFeedJob(int propertyId, int agentId, boolean scheduleFirstStep, InputType inputType, Integer inputProcessingId, LocalDate overrideBusinessDate) {
        PacmanWorkContextHelper.setPropertyId(propertyId);
        Property property = propertyService.getPropertyById(propertyId);
        PacmanWorkContextHelper.setPropertyCode(property.getCode());
        PacmanWorkContextHelper.setClientCode(property.getClient().getCode());
        String response;
        try {
            HashMap<String, Object> parameters = new HashMap<>();
            parameters.put(PROPERTY_ID, propertyId);
            parameters.put(JobParameterKey.REMOTE_AGENT_ID, agentId);
            parameters.put(JobParameterKey.SCHEDULE_FIRST_STEP, scheduleFirstStep);
            parameters.put(INPUT_TYPE, inputType.toString());
            parameters.put(OPERATION_TYPE, inputType.toString());
            parameters.put(JobParameterKey.INPUT_PROCESSING_ID, inputProcessingId);
            parameters.put(JobParameterKey.PAST_DAYS, getPastDays());
            parameters.put(JobParameterKey.GROUP_PAST_DAYS, getGroupPastDays());
            parameters.put(JobParameterKey.FUTURE_DAYS, getFutureDays(inputType, overrideBusinessDate));
            parameters.put("timestamp", System.currentTimeMillis());
            parameters.put(JobParameterKey.EXTERNAL_SYSTEM_CODE, OPERA_AGENT.getCode());
            parameters.put(JobParameterKey.OVRRIDE_BUSINESS_DT, overrideBusinessDate);
            if (SystemConfig.isCreationOfJobByRestEnabled()) {
                parameters.put("jobName", JobName.OperaDataFeedJob);
                response = g3RestClient.get(RestEndpoints.CREATE_NEW_JOB, parameters);
                LOGGER.info("OperaDataFeedJob started through rest: " + response);
            } else {
                final Long jobExecutionId = jobService.startJob(JobName.OperaDataFeedJob, parameters);
                response = "Job ExecutionId: " + jobExecutionId;
            }
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Received response while invoking spring batch: " + response);
            }
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Could not invoke spring batch processing.", e);
        }
        return response;
    }

    private Integer getPastDays() {
        return pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.PAST_DAYS, OPERA_AGENT);
    }

    private Integer getGroupPastDays() {
        return pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.GROUP_PAST_DAYS, OPERA_AGENT);
    }

    private Integer getFutureDays(InputType inputType, LocalDate overrideBusinessDate) {
        Integer futureDaysBDE = pacmanConfigParamsService.getIntegerParameterValue(IntegrationConfigParamName.FUTURE_DAYS.value());
        if (InputType.CDP.equals(inputType)) {
            Integer futureDaysCDP = pacmanConfigParamsService.getIntegerParameterValue(IntegrationConfigParamName.FUTURE_DAYS_CDP.value());
            return futureDaysCDP > 0 ? futureDaysCDP : futureDaysBDE;
        } else {
            return futureDaysBDE + (overrideBusinessDate == null ? 0 : 1);
        }
    }

    protected boolean doStartOperaJob(String propertyStage) {
        return !(Stage.DATA_CAPTURE.getCode().equals(propertyStage) || Stage.CATCHUP.getCode().equals(propertyStage)
                || Stage.PAUSED.getCode().equals(propertyStage) || Stage.DORMANT.getCode().equals(propertyStage));
    }

    public LocalDateTime getCaughtUpDate() {
        return new LocalDateTime(dateService.getCaughtUpDate());
    }

    protected void writeBatch(@NotNull PreparedStatement preparedStatement) throws SQLException {
        metricsFeedLoad.start(DataLoadFeedMetricType.WRITE_BATCH_STATEMENT);
        preparedStatement.executeBatch();
        preparedStatement.clearBatch();
        metricsFeedLoad.stop(DataLoadFeedMetricType.WRITE_BATCH_STATEMENT);
    }


    public int getNumberOfRawTransactionRows() {
        return crudService.findByNativeQuerySingleResult("select count(*) from opera.Raw_Transaction rt", null,
                objects -> (Integer) objects[0]);
    }

    public int getNumberOfStageTransactionRows() {
        return crudService.findByNativeQuerySingleResult("select count(*) from opera.Stage_Transaction st", null,
                objects -> (Integer) objects[0]);
    }

    @SuppressWarnings("unchecked")
    public String getLastUploadedFile() {
        String sql = new StringBuilder()
                .append("SELECT Correlation_ID,Incoming_File_Type_Code from [opera].[Data_Load_Metadata] where Correlation_ID")
                .append(" in (select Correlation_ID from [opera].[Data_Load_Metadata] where [Data_Load_Metadata_ID] in")
                .append(" (SELECT Data_Load_Metadata_ID").append(" FROM opera.History_Incoming_Metadata))")
                .append(" order by Data_Load_Metadata_ID desc;").toString();
        List<Object[]> results = crudService.findByNativeQuery(sql);
        String allFileTypes = "";
        String corID = "";
        int cnt = 0;
        for (Object[] dataRow : results) {
            corID = dataRow[0].toString();
            if (cnt == 0) {
                allFileTypes = dataRow[1].toString();
            } else {
                allFileTypes = allFileTypes + "_" + dataRow[1].toString();
            }

            cnt++;
        }
        return corID + "@" + allFileTypes + "@" + getPreparedDateTime();
    }

    @SuppressWarnings("unchecked")
    public String getPreparedDateTime() {
        String sql = "  SELECT top 1 Prepared_DT,Prepared_Time from opera.History_Incoming_Metadata order by History_Incoming_MetaData_ID";
        String prepDtandTm = "";

        List<Object[]> results = crudService.findByNativeQuery(sql);
        for (Object[] dataRow : results) {
            prepDtandTm = dataRow[0] + " " + dataRow[1];
        }

        return prepDtandTm;
    }

    public void rebuildDatabase() {
        propertyRolloutService.cleanProperty();
        deleteOperaRawTables();
        deleteOperaStageTables();
        startAllJobs();
    }

    @SuppressWarnings("unchecked")
    private void startAllJobs() {
        List<String> results = crudService.findByNativeQuery(RETRIEVE_ALL_CORRELATION_IDS_FOR_FULL_FEEDS,
                QueryParameter.with("numberOfInputFileTypes", OperaIncomingFile.values().length).parameters());
        for (String correlationId : results) {
            HashMap<String, Object> parameters = new HashMap<>();
            parameters.put("propertyId", PacmanWorkContextHelper.getPropertyId().toString());
            parameters.put(CORRELATION_ID, correlationId);
            parameters.put("timestamp", System.currentTimeMillis());
            parameters.put(EXTERNAL_SYSTEM_CODE, OPERA_AGENT.getCode());
            parameters.put(
                    JobParameterKey.DATE,
                    DateUtil.formatDate(DateUtil.getCurrentDate(), DateUtil.DATE_TIME_FORMAT));
            Long response = startJob(parameters, InputType.BDE);
            LOGGER.debug(new StringBuilder().append(correlationId)
                    .append(": Received response while invoking spring batch: ").append(response).toString());
            try {
                Thread.sleep(1000);
            } catch (InterruptedException ignored) {
                //not sure why empty
            }
        }
    }

    protected void deleteOperaRawTables() {
        for (OperaIncomingFile operaIncomingFile : OperaIncomingFile.values()) {
            crudService.executeUpdateByNativeQuery(new StringBuilder().append("TRUNCATE TABLE opera.Raw_")
                    .append(operaIncomingFile.getBaseTableName()).toString());
        }
    }

    protected void deleteOperaStageTables() {
        for (OperaIncomingFile operaIncomingFile : OperaIncomingFile.values()) {
            crudService.executeUpdateByNativeQuery(new StringBuilder().append("TRUNCATE TABLE opera.Stage_")
                    .append(operaIncomingFile.getBaseTableName()).toString());
        }
    }

    protected void deleteOperaOtherTables() {
        String truncateQuery = "TRUNCATE TABLE opera.Adjusted_OOO; TRUNCATE TABLE opera.G3_Group_Master_Link;TRUNCATE TABLE opera.Filtered_Transaction;";
        crudService.executeUpdateByNativeQuery(truncateQuery);
    }

    public boolean isThisTheFirstLoad() {
        String sql = "select count(*) from File_Metadata where file_name= 'operaDataLoad' and process_status_id =13";
        return 0 == crudService.findByNativeQuerySingleResult(sql, null, new RowMapper<Integer>() {
            @Override
            public Integer mapRow(Object[] objects) {
                return (Integer) objects[0];
            }
        });
    }

    public void rollBackProperty() {
        propertyRolloutService.cleanProperty();
        deleteOperaRawTables();
        deleteOperaStageTables();
        deleteOperaOtherTables();
    }

    public void catchUp() {
        startAllJobs();
    }

    public String getLatestCorrelationId() {
        String sql = "SELECT TOP 1 Correlation_ID FROM [opera].[Data_Load_Metadata] ORDER BY Data_Load_Metadata_ID DESC";
        return crudService.findByNativeQuerySingleResult(sql, null, new RowMapper<String>() {
            @Override
            public String mapRow(Object[] objects) {
                return (String) objects[0];
            }
        });
    }

    @SuppressWarnings("unchecked")
    public List<DataLoadMetadata> getSuccessfulDataLoadMetadata() {
        return crudService.findByNamedQuery(DataLoadMetadata.COMPLETED_LOAD_DETAILS);
    }

    public DataLoadMetadata getSuccessfulDataLoadMetadata(String correlationId, OperaIncomingFile fileType) {
        return operaUtilityService.getDataLoadMetadataForFileType(correlationId, fileType.getFileTypeCode());
    }

    public String getInstanceIdByExecutionId(Long jobExecutionId) {
        return String.valueOf(jobService.getInstanceIdByExecutionId(jobExecutionId));
    }

    public List<OperaCatchupDataWrapper> getActivityDataForOperaCatchup(Integer propertyId, LocalDate startDate,
                                                                        LocalDate endDate) {
        List<OperaCatchupDataWrapper> ocdwList = new ArrayList<>();

        String sql = MessageFormat.format(SQL_ACTIVITY_DATA_CATCHUP_FOR_DATE_RANGE, OperaIncomingFile.values().length);
        List<Object[]> queryResults = multiPropertyCrudService.findByNativeQueryForSingleProperty(propertyId, sql,
                QueryParameter.with(SQL_PARAM_DATE_START, startDate.toDate()).and(SQL_PARAM_DATE_END, endDate.toDate())
                        .parameters());

        if (!queryResults.isEmpty()) {
            // Should be returning the correlationId and date only
            for (Object[] dataRow : queryResults) {
                String correlationId = (String) dataRow[0];
                Date date = (Date) dataRow[1];
                ocdwList.add(new OperaCatchupDataWrapper(date, null, correlationId, true));
            }
        }
        ocdwList = filterActivityData(ocdwList);
        Collections.sort(ocdwList, new OperaCatchupDataWrapperComparator());
        return ocdwList;
    }

    private List<OperaCatchupDataWrapper> filterActivityData(List<OperaCatchupDataWrapper> fullList) {
        List<OperaCatchupDataWrapper> results = new ArrayList<>();
        Map<LocalDate, List<OperaCatchupDataWrapper>> dateMap = buildDateMap(fullList);
        for (List<OperaCatchupDataWrapper> candidates : dateMap.values()) {
            OperaCatchupDataWrapper bde = findBde(candidates);
            if (bde != null) {
                results.add(bde);
            }
        }
        return results;
    }

    private OperaCatchupDataWrapper findBde(List<OperaCatchupDataWrapper> candidates) {
        if (candidates == null || candidates.isEmpty()) {
            return null;
        }
        if (candidates.size() == 1) {
            return candidates.get(0);
        }
        List<BdeCandidate> candidateList = new ArrayList<>();
        for (OperaCatchupDataWrapper ocdw : candidates) {
            BdeCandidate bdeCandidate = new BdeCandidate(ocdw);
            bdeCandidate.setPastDays(getPastDays(ocdw));
            candidateList.add(bdeCandidate);
        }
        Collections.sort(candidateList);
        return candidateList.get(0).getOcdw();
    }

    private int getPastDays(OperaCatchupDataWrapper ocdw) {
        OperaIncomingMetadata metadata = getIncomingMetaDataRecord(ocdw.getCorrelationId());
        try {
            return metadata == null ? 0 : Integer.parseInt(metadata.getPastDays());
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    private Map<LocalDate, List<OperaCatchupDataWrapper>> buildDateMap(List<OperaCatchupDataWrapper> fullList) {
        Map<LocalDate, List<OperaCatchupDataWrapper>> dateMap = new HashMap<>();
        for (OperaCatchupDataWrapper ocdw : fullList) {
            LocalDate localDate = LocalDate.fromDateFields(ocdw.getDateTime());
            List<OperaCatchupDataWrapper> dateValues = dateMap.computeIfAbsent(localDate, k -> new ArrayList<>());
            dateValues.add(ocdw);
        }
        return dateMap;
    }

    public List<OperaTransaction> findTransactionsByCriteria(OperaTransactionCriteria criteria) {
        return crudService.findByCriteria(criteria);
    }

    public int findCountByCriteria(OperaTransactionCriteria criteria) {
        return crudService.findCountByCriteria(criteria);
    }

    public int findCountByCriteria(OperaTransactionHistoryCriteria criteria) {
        return crudService.findCountByCriteria(criteria);
    }

    @SuppressWarnings("unchecked")
    public List<OperaTransactionHistory> findTransactionsHistory(OperaTransactionCriteria criteria) {
        Map<String, Object> parameters = QueryParameter.with("marketCode", criteria.getMarketCode())
                .and("rateCode", criteria.getRateCode()).parameters();
        return crudService.findByNamedQuery(
                OperaTransactionHistory.FIND_ALL_TRANSACTIONS_FOR_MARKET_RATE_CODE, parameters);
    }

    public List<OperaTransactionHistory> findTransactionsHistoryForProperty(OperaTransactionHistoryCriteria criteria) {
        return crudService.findByCriteria(criteria);
    }

    public List<DataLoadSummary> getRecentNGIDataLoads(String clientCode, String propertyCode, int maxCount) {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("clientCode", clientCode);
        parameters.put("propertyCode", propertyCode);
        if (maxCount < 0 && pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MULTIPLE_NGI_URL_CONFIGURATIONS)) {
            return restClient.getLimitedDataFromEndpoint(RestEndpoints.STATISTIC_CORRELATIONS_BY_CLIENT_AND_PROPERTY_AND_STATUSES, parameters, maxCount, new DataLoadResultMapper());
        }
        return restClient.getLimitedDataFromEndpoint(RestEndpoints.STATISTIC_CORRELATIONS_BY_CLIENT_AND_PROPERTY, parameters, maxCount, new DataLoadResultMapper());
    }

    public List<DataLoadSummary> getRecentDataLoads(Property property, int maxCount) {
        if (isNGI(property)) {
            return getRecentNGIDataLoads(property.getClient().getCode(), property.getCode(), maxCount);
        } else {
            return getRecentG3DataLoads(property.getId(), maxCount);
        }
    }

    public boolean isNGI(Property property) {
        return externalSystemHelper.isNGI(property.getClient().getCode(), property.getCode()) ||
                externalSystemHelper.isHiltonStreaming(property.getClient().getCode(), property.getCode());
    }

    @SuppressWarnings("unchecked")
    private List<DataLoadSummary> getRecentG3DataLoads(int propertyId, int maxCount) {
        PacmanWorkContextHelper.setPropertyId(propertyId);
        List<DataLoadSummary> results = new ArrayList<>();
        if (maxCount > 0) {
            results.addAll(fetchLimitedDataLoads(maxCount));
        } else {
            results.addAll(fetchAllDataLoads());
        }
        Collections.sort(results, new DataLoadSummaryComparator());
        if (maxCount > 0 && results.size() > maxCount) {
            results = results.subList(0, maxCount);
        }
        fillInMissingDetails(results);
        return results;
    }

    private List<DataLoadSummary> fetchLimitedDataLoads(int maxCount) {
        List<DataLoadMetadata> entities;
        int maxRowCount = maxCount * OperaIncomingFile.values().length;
        entities = crudService.findByNamedQuery(DataLoadMetadata.GET_RECENT, maxRowCount);
        Map<String, DataLoadSummary> summaries = new HashMap<>();
        for (DataLoadMetadata entity : entities) {
            String correlationId = entity.getCorrelationId();
            DataLoadSummary summary = summaries.computeIfAbsent(correlationId, k -> {
                DataLoadSummary s = new DataLoadSummary();
                s.setCorrelationId(correlationId);
                return s;
            });
            summary.add(entity);
            if (entity.getIncomingFileTypeCode().equals(OperaIncomingFile.INCOMING_METADATA.getFileTypeCode())) {
                OperaIncomingMetadata incomingMetaData = getIncomingMetaDataRecord(entity.getId());
                FileMetadata fileMetadata = fileMetadataService.findByFileLocationAndStatus(correlationId, PROCESS_STATUS_SUCCESSFUL);
                summary.setPopulated(fileMetadata != null);
                summary.add(incomingMetaData);
            }
        }
        return new ArrayList<>(summaries.values());
    }

    private List<DataLoadSummary> fetchAllDataLoads() {
        Map<String, DataLoadSummary> summaries = new HashMap<>();
        Map<String, Integer> correlationIdVsEntityId = new HashMap<>();
        prepareDataLoadSummary(summaries, correlationIdVsEntityId);
        List<String> processedFileMetadataCorrelationIds = fileMetadataService
                .findAllCorrelationIdsByStatusAndRecordType(PROCESS_STATUS_SUCCESSFUL, RECORD_TYPE_BDE);
        Map<Integer, OperaIncomingMetadata> dataLoadIdVsHistory = fetchAllOperaIncomingHistory();
        correlationIdVsEntityId.forEach((correlationId, entityId) -> {
            DataLoadSummary dataLoadSummary = summaries.get(correlationId);
            dataLoadSummary.setPopulated(processedFileMetadataCorrelationIds.contains(correlationId));
            dataLoadSummary.add(dataLoadIdVsHistory.get(entityId));
        });

        return new ArrayList<>(summaries.values());
    }

    private void prepareDataLoadSummary(Map<String, DataLoadSummary> summaries, Map<String, Integer> correlationIdVsEntityId) {
        List<DataLoadMetadata> entities = crudService.findByNamedQuery(DataLoadMetadata.GET_RECENT);
        for (DataLoadMetadata entity : entities) {
            String entityCorrelationId = entity.getCorrelationId();
            DataLoadSummary summary = summaries.computeIfAbsent(entityCorrelationId, k -> {
                DataLoadSummary s = new DataLoadSummary();
                s.setCorrelationId(entityCorrelationId);
                return s;
            });
            summary.add(entity);
            if (entity.getIncomingFileTypeCode().equals(OperaIncomingFile.INCOMING_METADATA.getFileTypeCode())) {
                correlationIdVsEntityId.put(entityCorrelationId, entity.getId());
            }
        }
    }

    public Map<Integer, OperaIncomingMetadata> fetchAllOperaIncomingHistory() {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        List<OperaIncomingMetadata> historyByDataLoadIds = crudService.findByNativeQuery(GET_HISTORY_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters(), new RowMapper<OperaIncomingMetadata>() {
                    @Override
                    public OperaIncomingMetadata mapRow(Object[] row) {
                        OperaIncomingMetadata history = new OperaIncomingMetadata(null, null,
                                propertyId.toString(), (String) row[0], (String) row[1], (String) row[2],
                                (String) row[3], (String) row[4], (String) row[5], populateDataLoadEntity((Integer) row[6]));
                        return history;
                    }
                });

        Map<Integer, OperaIncomingMetadata> dataLoadIdVsHistory = historyByDataLoadIds == null ? new HashMap<>() :
                historyByDataLoadIds.stream()
                        .collect(Collectors.toMap(history -> history.getDataLoadMetadata().getId(),
                                history -> history));
        return dataLoadIdVsHistory;
    }

    private DataLoadMetadata populateDataLoadEntity(Integer entityId) {
        DataLoadMetadata dataLoadMetadata = new DataLoadMetadata();
        dataLoadMetadata.setId(entityId);
        return dataLoadMetadata;
    }

    @SuppressWarnings("unchecked")
    private Map<String, DataLoadSummary> getDataLoadSummariesByQuery(String query) {
        Date systemDate = dateService.getCaughtUpDate(false);
        if (systemDate == null) {
            systemDate = new Date(0);
        }
        Map<String, Object> parameters = QueryParameter.with("businessDate", LocalDateTime.fromDateFields(systemDate))
                .parameters();
        List<DataLoadMetadata> entities = crudService.findByNamedQuery(
                query, parameters);
        if (entities.isEmpty()) {
            return null;
        }
        Map<String, DataLoadSummary> summaries = new HashMap<>();
        for (DataLoadMetadata entity : entities) {
            String correlationId = entity.getCorrelationId();
            DataLoadSummary summary = summaries.computeIfAbsent(correlationId, k -> {
                DataLoadSummary s = new DataLoadSummary();
                s.setCorrelationId(correlationId);
                return s;
            });
            summary.add(entity);
            if (entity.getIncomingFileTypeCode().equals(OperaIncomingFile.INCOMING_METADATA.getFileTypeCode())) {
                OperaIncomingMetadata incomingMetaData = getIncomingMetaDataRecord(entity.getId());
                summary.add(incomingMetaData);
            }
        }
        return summaries;
    }

    public DataLoadSummary getLatestProcessedDataLoad(Property property) {
        return getDataLoadSummary(
                property.getId(),
                fileMetadata -> dailyProcessingService.findInputProcessing(
                        property.getCode(),
                        property.getClient().getCode(),
                        fileMetadata.getFileLocation()));
    }

    public DataLoadSummary getOldestUnprocessedDataLoad(Property property) {
        return getDataLoadSummary(
                property.getId(),
                fileMetadata -> dailyProcessingService.findOldestInProgressInputProcessingSinceSnapshot(
                        property.getClient().getCode(),
                        property.getCode(),
                        fileMetadata.getFileLocation(),
                        LocalDateTime.fromDateFields(fileMetadata.getPreparedDtTm())));
    }

    private DataLoadSummary getDataLoadSummary(Integer propertyId, Function<FileMetadata, InputProcessing> inputProcessingFunction) {
        Optional<Integer> inputProcessingIdOptional = Optional.ofNullable(propertyId)
                .map(fileMetadataService::findByPropertyId)
                .map(inputProcessingFunction)
                .map(InputProcessing::getId);

        if (inputProcessingIdOptional.isEmpty()) {
            return null;
        }

        Optional<JobView> jobViewOptional = inputProcessingIdOptional
                .map(this::getJobView);

        if (jobViewOptional.isEmpty()) {
            return null;
        }

        Map<String, Object> jobParameters = getJobParameters(jobViewOptional.get());

        DataLoadSummary dataLoadSummary = null;

        Date fiscalDate = parseFiscalDate(jobParameters);
        Date dateTime = DateUtil.getDateTimeByTimeZone(DateUtil.convertISODate((String) jobParameters.get(JobParameterKey.TIMESTAMP)), dateService.getPropertyTimeZone());

        if (!jobParameters.isEmpty()) {
            dataLoadSummary = new DataLoadSummary();
            dataLoadSummary.setCorrelationId((String) jobParameters.get(JobParameterKey.CORRELATION_ID));
            dataLoadSummary.setBusinessDate(parseSnapShotDateTime(fiscalDate, dateTime));
            dataLoadSummary.setInputType((String) jobParameters.get(JobParameterKey.OPERATION_TYPE));
            dataLoadSummary.setInputProcessingId(inputProcessingIdOptional.get());
            dataLoadSummary.setJobId(jobViewOptional.get().getJobInstanceId());
        }
        return dataLoadSummary;
    }

    private Date parseFiscalDate(Map<String, Object> jobParameters) {
        Date fiscalDate;
        try {
            fiscalDate = DateUtil.parseDate((String) jobParameters.get(JobParameterKey.FISCAL_DATE), DateUtil.DEFAULT_DATE_FORMAT_FOR_SCHEDULED_REPORTS);
        } catch (ParseException e) {
            throw new TetrisException("fiscal date could not be parsed for reprocessing");
        }
        return fiscalDate;
    }

    protected JobView getJobView(Integer inputProcessingId) {
        return Optional.ofNullable(dailyProcessingService.getJobInstanceIds(inputProcessingId))
                .orElse(Collections.emptyList())
                .stream()
                .map(jobId -> jobMonitorService.getJobDetail(jobId))
                .filter(this::isProcessingJob)
                .findFirst()
                .orElse(null);

    }

    private boolean isProcessingJob(JobView jobView) {
        String jobName = jobView.getJobName();
        return JobName.NGIDeferredDeliveryJob.name().equals(jobName)
                || JobName.NGICdpDeferredDeliveryJob.name().equals(jobName);
    }

    private LocalDateTime parseSnapShotDateTime(Date date, Date time) {
        LocalTime snapShotTime = new LocalTime(time);
        LocalDate snapShotDate = new LocalDate(DateUtil.addDaysToDate(date, 1));
        return new LocalDateTime(snapShotDate.toDateTime(snapShotTime));
    }

    public DataLoadSummary getLatestProcessedOperaDataLoad(int propertyId) {
        PacmanWorkContextHelper.setPropertyId(propertyId);
        Map<String, DataLoadSummary> summaries = getDataLoadSummariesByQuery(DataLoadMetadata.BY_BUSINESS_DATE);
        if (summaries == null) {
            return null;
        }
        List<DataLoadSummary> results = new ArrayList<>();
        results.addAll(summaries.values());
        DataLoadSummary result = results.get(0);
        result.fillInMissingDetails();
        populateFieldsFromInputProcessing(result);
        return result;
    }

    public DataLoadSummary getFirstUnprocessedOperaDataLoad(int propertyId) {
        PacmanWorkContextHelper.setPropertyId(propertyId);
        Map<String, DataLoadSummary> summaries = getDataLoadSummariesByQuery(DataLoadMetadata.GET_UNPROCESSED);
        if (summaries == null) {
            return null;
        }
        List<DataLoadSummary> results = new ArrayList<>();
        results.addAll(summaries.values());
        Collections.sort(results, new DataLoadSummaryAscendingComparator());
        DataLoadSummary result = results.get(0);
        result.fillInMissingDetails();
        populateFieldsFromInputProcessing(result);
        return result;
    }

    private void populateFieldsFromInputProcessing(DataLoadSummary summary) {
        InputProcessingCriteria criteria = new InputProcessingCriteria();
        criteria.setInputId(summary.getCorrelationId());

        List<InputProcessing> ips = globalCrudService.findByCriteria(criteria);

        if (ips == null || ips.isEmpty()) {
            throw new IllegalStateException("Did not return any InputProcessings for correlationId:" + summary.getCorrelationId() + ", which right now we are assuming is impossible.");
        }
        InputProcessing inputProcessing = ips.get(0);
        summary.setInputProcessingId(inputProcessing.getId());
        summary.setInputType(inputProcessing.getInputType());
    }


    public String startDeleteFeedJob(int propertyId, List<String> correlationIds) {
        Long instanceId;
        try {
            HashMap<String, Object> parameters = new HashMap<>();
            parameters.put(PROPERTY_ID, propertyId);
            int count = 0;
            for (String correlationId : correlationIds) {
                parameters.put(JobParameterKey.CORRELATION_ID + count, correlationId);
                count++;
            }
            parameters.put(JobParameterKey.TIMESTAMP, System.currentTimeMillis());
            instanceId = jobService.startJob(JobName.DeleteOperaFeedJob, parameters);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Could not invoke spring batch processing.", e);
        }
        return "started " + JobName.DeleteOperaFeedJob.toString() + " with instance ID = " + instanceId.toString();
    }

    private List<Integer> getMetadataIdAsInt(String metadataIds) {
        List<Integer> list = new ArrayList<>();
        String[] strArr = metadataIds.split(",");
        for (String str : strArr) {
            list.add(Integer.parseInt(str));
        }
        return list;
    }

    @SuppressWarnings("unchecked")
    public List<String> getAllRoomTypesWithCapacity(String correlationId) {
        List<String> feedTypes = new ArrayList<>();
        feedTypes.add(OperaDataLoadTypeCode.PTAT.toString());
        feedTypes.add(OperaDataLoadTypeCode.CTAT.toString());
        feedTypes.add(OperaDataLoadTypeCode.PSAT.toString());
        feedTypes.add(OperaDataLoadTypeCode.CSAT.toString());
        String metadataIds = getDataLoadMetadataIds(correlationId, feedTypes);
        if (metadataIds == null) {
            return new ArrayList<>();
        }
        return crudService.findByNativeQuery("SELECT distinct(Room_Type) FROM opera.History_Occupancy_Summary WHERE Physical_Rooms != '0' AND Data_Load_Metadata_ID IN ( " + metadataIds + ")",
                null);
    }

    public List<String> getAllRoomTypesWithZeroCapacity(String correlationId, List<String> includeRoomTypes) {
        List<String> feedTypes = new ArrayList<>();
        feedTypes.add(OperaDataLoadTypeCode.PTAT.toString());
        feedTypes.add(OperaDataLoadTypeCode.CTAT.toString());
        feedTypes.add(OperaDataLoadTypeCode.PSAT.toString());
        feedTypes.add(OperaDataLoadTypeCode.CSAT.toString());
        String metadataIds = getDataLoadMetadataIds(correlationId, feedTypes);
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("includeRoomTypes", includeRoomTypes);
        if (metadataIds != null) {
            paramMap.put("dataLoadMetadaId", getMetadataIdAsInt(metadataIds));
        }
        return crudService.findByNamedQuery(OperaTransactionHistory.FIND_ALL_ZERO_CAPACITY_ROOMS_FROM_INCLUDE_ROOM_TYPE, paramMap);
    }

    public Date getBusinessDateInPropertyTimeZone(OperaIncomingMetadata metadata) {
        if (metadata == null || metadata.getBusinessDate() == null || metadata.getBusinessTime() == null) {
            return null;
        } else {
            TimeZone tz = dateService.getPropertyTimeZone();
            ZonedDateTime dt = java.time.LocalDate.parse(metadata.getBusinessDate())
                    .atTime(java.time.LocalTime.parse(metadata.getBusinessTime()))
                    .atZone(tz.toZoneId());
            return Date.from(dt.toInstant());
        }
    }

    @SuppressWarnings("unchecked")
    public Set<String> getAllRoomTypesInFeed(String correlationId) {
        List<String> feedTypes = new ArrayList<>();
        feedTypes.add(OperaDataLoadTypeCode.PTAT.toString());
        feedTypes.add(OperaDataLoadTypeCode.CTAT.toString());
        feedTypes.add(OperaDataLoadTypeCode.PSAT.toString());
        feedTypes.add(OperaDataLoadTypeCode.CSAT.toString());
        String metadataIds = getDataLoadMetadataIds(correlationId, feedTypes);
        Set<String> roomTypes = new HashSet<>();
        if (metadataIds != null) {
            roomTypes.addAll(crudService.findByNativeQuery("SELECT distinct(Room_Type) FROM opera.History_Occupancy_Summary WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")", null));
        }
        feedTypes = new ArrayList<>();
        feedTypes.add(OperaDataLoadTypeCode.PTRANS.toString());
        feedTypes.add(OperaDataLoadTypeCode.CTRANS.toString());
        metadataIds = getDataLoadMetadataIds(correlationId, feedTypes);
        if (metadataIds != null) {
            roomTypes.addAll(crudService.findByNativeQuery("SELECT distinct(Room_Type) FROM opera.History_Transaction WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")", null));
            roomTypes.addAll(crudService.findByNativeQuery("SELECT distinct(Booked_Room_Type) FROM opera.History_Transaction WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")", null));
        }
        return roomTypes;
    }

    public String deleteTransactions(String correlationId) {
        String metadataIds = getDataLoadMetadataIds(correlationId);
        int historyCount = 0;
        int rawCount = 0;
        int stageCount = 0;
        if (metadataIds != null) {
            historyCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.History_Transaction WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
            rawCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.Raw_Transaction WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
            stageCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.Stage_Transaction WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
            crudService.executeUpdateByNativeQuery("DELETE FROM opera.Filtered_Transaction WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
        }
        return "deleted " + historyCount + " opera.History_Transaction records, " + rawCount + " opera.Raw_Transaction records, " + stageCount + " opera.Stage_Transaction records";
    }

    public String deleteGroupBlock(String correlationId) {
        String metadataIds = getDataLoadMetadataIds(correlationId);
        int historyCount = 0;
        int rawCount = 0;
        int stageCount = 0;
        if (metadataIds != null) {
            historyCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.History_Group_Block WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
            rawCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.Raw_Group_Block WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
            stageCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.Stage_Group_Block WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
        }
        return "deleted " + historyCount + " opera.History_Group_Block records, " + rawCount + " opera.Raw_Group_Block records, " + stageCount + " opera.Stage_Group_Block records";
    }

    public String deleteGroupMaster(String correlationId) {
        String metadataIds = getDataLoadMetadataIds(correlationId);
        int historyCount = 0;
        int rawCount = 0;
        int stageCount = 0;
        if (metadataIds != null) {
            historyCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.History_Group_Master WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
            rawCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.Raw_Group_Master WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
            stageCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.Stage_Group_Master WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
        }
        return "deleted " + historyCount + " opera.History_Group_Master records, " + rawCount + " opera.Raw_Group_Master records, " + stageCount + " opera.Stage_Group_Master records";
    }

    public String deleteIncomingMetadata(String correlationId) {
        String metadataIds = getDataLoadMetadataIds(correlationId);
        int historyCount = 0;
        int rawCount = 0;
        int stageCount = 0;
        if (metadataIds != null) {
            historyCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.History_Incoming_Metadata WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
            rawCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.Raw_Incoming_Metadata WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
            stageCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.Stage_Incoming_Metadata WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
        }
        return "deleted " + historyCount + " opera.History_Incoming_Metadata records, " + rawCount + " opera.Raw_Incoming_Metadata records, " + stageCount + " opera.Stage_Incoming_Metadata records";
    }

    public String deleteOccupancySummary(String correlationId) {
        String metadataIds = getDataLoadMetadataIds(correlationId);
        int historyCount = 0;
        int rawCount = 0;
        int stageCount = 0;
        if (metadataIds != null) {
            historyCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.History_Occupancy_Summary WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
            rawCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.Raw_Occupancy_Summary WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
            stageCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.Stage_Occupancy_Summary WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
        }
        return "deleted " + historyCount + " opera.History_Occupancy_Summary records, " + rawCount + " opera.Raw_Occupancy_Summary records, " + stageCount + " opera.Stage_Occupancy_Summary records";
    }

    public String deleteYieldCurrency(String correlationId) {
        String metadataIds = getDataLoadMetadataIds(correlationId);
        int historyCount = 0;
        int rawCount = 0;
        int stageCount = 0;
        if (metadataIds != null) {
            historyCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.History_Yield_Currency WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
            rawCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.Raw_Yield_Currency WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
            stageCount = crudService.executeUpdateByNativeQuery("DELETE FROM opera.Stage_Yield_Currency WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
        }
        return "deleted " + historyCount + " opera.History_Yield_Currency records, " + rawCount + " opera.Raw_Yield_Currency records, " + stageCount + " opera.Stage_Yield_Currency records";
    }

    public String deleteDataLoadMetadata(String correlationId) {
        String metadataIds = getDataLoadMetadataIds(correlationId);
        int count = 0;
        if (metadataIds != null) {
            count = crudService.executeUpdateByNativeQuery("DELETE FROM opera.Data_Load_Metadata WHERE Data_Load_Metadata_ID IN ( " + metadataIds + ")");
        }
        return "deleted " + count + " opera.Data_Load_Metadata records";
    }

    @SuppressWarnings("unchecked")
    private String getDataLoadMetadataIds(String correlationId) {
        StringBuilder buffer = new StringBuilder();
        List<DataLoadMetadata> entities = crudService.findByNamedQuery(DataLoadMetadata.GET_BY_CORRELATION_ID
                , QueryParameter.with("correlationId", correlationId).parameters());
        if (entities.isEmpty()) {
            return null;
        }
        boolean first = true;
        for (DataLoadMetadata data : entities) {
            if (first) {
                first = false;
            } else {
                buffer.append(",");
            }
            buffer.append(data.getId());
        }
        return buffer.toString();

    }

    @SuppressWarnings("unchecked")
    private String getDataLoadMetadataIds(String correlationId, List<String> feedTypes) {
        StringBuilder buffer = new StringBuilder();
        List<DataLoadMetadata> entities = crudService.findByNamedQuery(DataLoadMetadata.GET_BY_CORRELATION_ID
                , QueryParameter.with("correlationId", correlationId).parameters());
        if (entities.isEmpty()) {
            return null;
        }
        boolean first = true;
        for (DataLoadMetadata data : entities) {
            if (feedTypes.contains(data.getIncomingFileTypeCode())) {
                if (first) {
                    first = false;
                } else {
                    buffer.append(",");
                }
                buffer.append(data.getId());
            }
        }
        return buffer.toString();

    }

    private void fillInMissingDetails(List<DataLoadSummary> summaries) {
        for (DataLoadSummary summary : summaries) {
            summary.fillInMissingDetails();
        }
    }

    public void invalidateNgiStatisticalCorrelation(List<String> correlationIds) {
        correlationIds.forEach(id -> updateStatusFor(id, RestEndpoints.UPDATE_STATISTICS_STATUS_INVALID, NGIStatisticsStatus.INVALID));
    }

    public void setNgiStatisticalCorrelationStatusComplete(List<String> correlationIds) {
        correlationIds.forEach(id -> updateStatusFor(id, RestEndpoints.UPDATE_STATISTICS_STATUS_COMPLETE, NGIStatisticsStatus.COMPLETE));
    }

    private void updateStatusFor(String statsCorrelationId, RestEndpoints updateRestEndPoint, NGIStatisticsStatus newStatus) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MULTIPLE_NGI_URL_CONFIGURATIONS)) {
            Map<String, Object> parameters = restClient.getClientPropertyMapWith(statsCorrelationId);
            parameters.put("status", newStatus.name());
            restClient.put(RestEndpoints.UPDATE_STATS_CORRELATION_STATUS,
                    Entity.entity(statsCorrelationId, MediaType.APPLICATION_JSON_TYPE),
                    parameters.get(CLIENT_CODE),
                    parameters.get(PROPERTY_CODE),
                    statsCorrelationId, newStatus.name());
        } else {
            restClient.put(updateRestEndPoint, statsCorrelationId,
                    Entity.entity(statsCorrelationId, MediaType.APPLICATION_JSON_TYPE));
        }
    }

    public enum DataLoadFeedMetricType {
        CREATE_DATA_LOAD_METADATA, READ_INCOMING_DATA, CREATE_BATCH_STATEMENT, WRITE_BATCH_STATEMENT
    }

    class DataLoadSummaryComparator implements Comparator<DataLoadSummary> {
        @Override
        public int compare(DataLoadSummary o1, DataLoadSummary o2) {
            // should not happen
            if (o1.getLastActivityDate() == null || o2.getLastActivityDate() == null) {
                return 0;
            }
            // sort in descending order, therefore o1 and o2 are reversed
            return o2.getLastActivityDate().compareTo(o1.getLastActivityDate());
        }
    }

    class DataLoadSummaryAscendingComparator implements Comparator<DataLoadSummary> {
        @Override
        public int compare(DataLoadSummary o1, DataLoadSummary o2) {
            // should not happen
            if (o1.getLastActivityDate() == null || o2.getLastActivityDate() == null) {
                return 0;
            }
            return o1.getLastActivityDate().compareTo(o2.getLastActivityDate());
        }
    }

    class OperaCatchupDataWrapperComparator implements Comparator<OperaCatchupDataWrapper> {
        @Override
        public int compare(OperaCatchupDataWrapper o1, OperaCatchupDataWrapper o2) {
            return o1.getDateTime().compareTo(o2.getDateTime());
        }
    }

    class BdeCandidate implements Comparable<BdeCandidate> {

        private OperaCatchupDataWrapper ocdw;
        private int pastDays;

        BdeCandidate() {
        }

        BdeCandidate(OperaCatchupDataWrapper ocdw) {
            this.ocdw = ocdw;
        }

        public OperaCatchupDataWrapper getOcdw() {
            return ocdw;
        }

        public void setOcdw(OperaCatchupDataWrapper ocdw) {
            this.ocdw = ocdw;
        }

        public int getPastDays() {
            return pastDays;
        }

        public void setPastDays(int pastDays) {
            this.pastDays = pastDays;
        }

        @Override
        public int compareTo(BdeCandidate o) {
            // sort in desc order
            int pastDaysCompare = Integer.compare(o.getPastDays(), this.pastDays);
            if (pastDaysCompare != 0) {
                return pastDaysCompare;
            }
            return this.ocdw.getDateTime().compareTo(o.getOcdw().getDateTime());
        }

    }
}
