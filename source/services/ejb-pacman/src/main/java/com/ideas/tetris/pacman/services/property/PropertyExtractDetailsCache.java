package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.services.property.dto.ExtractDetails;
import com.ideas.tetris.platform.common.cache.AbstractCache;

import java.util.Optional;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


@Component
@Transactional
public class PropertyExtractDetailsCache extends AbstractCache<Integer, ExtractDetails> {

    @Override
    protected boolean isAsync() {
        return false;
    }

    @Override
    protected Optional<Integer> getRedisLifespan() {
        return Optional.of(0);
    }
}
