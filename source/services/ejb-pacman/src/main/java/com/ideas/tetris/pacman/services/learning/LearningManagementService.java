package com.ideas.tetris.pacman.services.learning;


import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.infra.tetris.security.domain.PropertyRoleMapping;
import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.tetris.pacman.common.configparams.*;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.*;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.database.DBMaintainService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.RoleService;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.security.UserSynchronizationServiceLocal;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackDataBuilder;
import com.ideas.tetris.platform.common.businessservice.async.AsyncJobCallback;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.util.zip.ZipDirectory;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.compress.CompressUncompressService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.configparams.ParameterConversionUtility;
import com.ideas.tetris.platform.services.daoandentities.entity.*;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import com.ideas.tetris.platform.services.sas.log.SasDbQueryResult;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import com.ideas.tetris.platform.services.util.zip.PacmanZipDirectoryService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.OptimisticLockException;
import javax.persistence.PersistenceException;
import javax.persistence.Query;
import javax.ws.rs.QueryParam;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FilenameFilter;
import java.io.IOException;
import java.math.BigInteger;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.CONTEXT_KEY;
import static com.ideas.tetris.platform.services.compress.CompressionType.ZIP;
import static java.lang.Integer.valueOf;
import static org.apache.commons.lang.StringUtils.equalsIgnoreCase;

@Justification("CreatePropertiesBackupStep fails with newly added COASTAL property for transaction timeout as database size is large")
@Component
@Transactional(timeout = 18000)
public class LearningManagementService {

    private static final String PARAM_PROPERTY_NAME = "propertyName";

    private static final Logger LOGGER = Logger.getLogger(LearningManagementService.class);

    private static final String CONTEXT_PREFIX_PACMAN = "pacman.";

    private static final String LOG_LDF_EXT = "_log.ldf";
    private static final String LDF_EXT = ".ldf";
    private static final String MDF_EXT = ".mdf";

    private static final String PARAM_CLIENT_ID = "clientId";
    private static final String PARAM_CLIENT_USER_ID = "clientUserId";
    private static final String PARAM_CODE = "code";
    private static final String PARAM_CONTEXT = "context";
    private static final String PARAM_DATE = "date";
    private static final String PARAM_DB_LOC_ID = "dbLocId";
    private static final String SFDC_ACCOUNT_NUMBER = "sfdcAccountNo";
    private static final String PARAM_DB_NAME = "dbName";
    private static final String PARAM_NAME = "name";
    private static final String PARAM_PROPERTY_ID = "propertyId";
    private static final String PARAM_TEMPLATE_PROPERTY_NAME = "templatePropertyName";
    private static final String PARAM_USER_ID = "userId";

    public static final String TENANT_MAINTENANCE_FILENAME = "tenant.maintenance.sql";
    public static final String SAS_MAINTENANCE_FILENAME = "sas.maintenance.sql";

    public static final String SFDC_ACCOUNT_NO = "1885-0005";

    public static final String DISTINCT_PROPERTIES_FOR_CLIENT_AND_USER = "SELECT DISTINCT PPG.Property_ID FROM [dbo].[Property_Property_Group] ppg INNER JOIN [dbo].[Property_Group] pg ON ppg.Property_Group_ID = pg.Property_Group_ID WHERE pg.Client_ID = :clientId and pg.User_ID = :userId";
    public static final String STAGE = "stage";
    public static final String EXTERNAL_SYSTEM_OPERA = "opera";
    public static final String G3_SALES = "G3Sales";
    public static final String G3_LMS = "G3LMS";
    public static final String G3_LMSDEV = "G3LMSDEV";
    public static final String G3_CTS = "G3CTS";
    public static final String G3_CTSDEV = "G3CTSDEV";
    public static final String URBANCLUB = "UrbanClub";
    public static final String DELMAR = "DELMAR";
    public static final String COASTAL = "COASTAL";
    public static final String CITYSTAY = "CITYSTAY";
    public static final String CANYON = "CANYON";

    public static final String MAJESTICPINES = "MAJESTICPINES";

    protected static final String SAS_DATE_COLUMNS = "select libname label='schemaName', memname label='tableName', name label='columnName'  from dictionary.columns where libname <> 'SASHELP' and (UPPER(name) like '%_DTTM' OR UPPER(name) like '%_DT') order by libname, memname, name";

    public static final String TABLE_COLUMN_DATE_UPDATES =
            "SELECT TABLE_SCHEMA as schemaName, TABLE_NAME as tableName, COLUMN_NAME as columnName\n" +
                    "FROM INFORMATION_SCHEMA.COLUMNS\n" +
                    "WHERE TABLE_SCHEMA IN ('dbo','opera')\n" +
                    "AND DATA_TYPE IN ('date','datetime','datetime2','smalldatetime')\n" +
                    "AND TABLE_NAME NOT IN ('Year', 'Month', 'calendar_dim', 'date_list', 'Individual_Trans')\n" +
                    "AND TABLE_NAME NOT LIKE 'Vw_%'\n" +
                    "AND COLUMN_NAME not in ('Created_DTTM', 'Last_Updated_DTTM')\n" +
                    "ORDER BY TABLE_NAME, COLUMN_NAME;";
    public static final String SQL_ZIP_SUFFIX = "_sql.zip";
    public static final String SAS_ZIP_SUFFIX = "_sas.zip";

    protected static final Map<String, String> clientMap;

    static {
        clientMap = new HashMap<>();
        clientMap.put(G3_SALES, "30");
        clientMap.put(G3_LMS, "31");
        clientMap.put(G3_LMSDEV, "32");
        clientMap.put(G3_CTSDEV, "72");
        clientMap.put(G3_CTS, "70");
    }

    @GlobalCrudServiceBean.Qualifier
	@Qualifier("globalCrudServiceBean")
    @Autowired
	protected CrudService globalCrudService;
    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
    PropertyService propertyService;
    @Autowired
    DBMaintainService dbMaintainService;
    @Autowired
    UserService userService;
    @Autowired
    JobServiceLocal jobService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    RoleService roleService;
    @Autowired
    SasDbToolService sasDbToolService;
    @Autowired
    UserSynchronizationServiceLocal userSynchronizationService;
    @Autowired
    ClientPropertyCacheService clientPropertyCacheService;
    @Autowired
    @Qualifier("compressUncompressService")
    CompressUncompressService compressService;
    @Autowired
    DateService dateService;
    @TenantCrudServiceBean.Qualifier
	@Qualifier("tenantCrudServiceBean")
    @Autowired
    CrudService tenantCrudService;
    @PacmanZipDirectoryService.Qualifier
    @Qualifier("pacmanZipDirectoryService")
	@Autowired
    PacmanZipDirectoryService zipService; //TODO remove

    @SuppressWarnings("unchecked")
    @Async
    @AsyncJobCallback
    public Future<Object> copyLearningPropertiesAsync(JobStepContext jobStepContext, WorkContextType workContextType,
                                                      List<DBLoc> dbLocs, String[] templatePropertyNames, boolean useSnapshot) {
        String[] propertyIds = new String[dbLocs.size()];

        for (int i = 0; i < dbLocs.size(); i++) {
            propertyIds[i] = dbLocs.get(i).getDbName();
            copyLearningPropertyTemplate(dbLocs.get(i), templatePropertyNames[i], useSnapshot);
        }

        return AsyncCallbackDataBuilder.buildFuture(Arrays.toString(propertyIds));
    }

    public DBLoc getDBLoc(Integer propertyId) {
        String dbName = getDbName(propertyId);
        DBLoc dbLoc = getDbLocFrom(dbName);

        if (dbLoc == null) {
            String context = CONTEXT_PREFIX_PACMAN + PacmanWorkContextHelper.getClientCode() + "." + LMSProperty.getPropertyCode(propertyId);

            dbLoc = new DBLoc();
            dbLoc.setDbName(dbName);
            dbLoc.setDbtypeId(DBType.perProperty);
            dbLoc.setServerName(SystemConfig.getDbHost());
            dbLoc.setServerInst(SystemConfig.getDbInstance());
            dbLoc.setPortNumber(Integer.parseInt(SystemConfig.getDbPort()));
            dbLoc.setJndiName(pacmanConfigParamsService.getParameterValue(context, IntegrationConfigParamName.DEFAULT_JNDI).toString());
            dbLoc.setJndiNameForReports(pacmanConfigParamsService.getParameterValue(context, IntegrationConfigParamName.DEFAULT_JNDI_FOR_REPORTS).toString());
            dbLoc.setStatus(Status.ACTIVE);
            dbLoc = globalCrudService.save(dbLoc);
        }

        return dbLoc;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void copyLearningPropertyTemplate(DBLoc dbLoc, String basePropertyName, boolean useSnapshot) {
        try {
            String source = SystemConfig.getLmsSqlSource();
            String destination = SystemConfig.getLmsSqlDestination();

            File sourceDataFile = new File(source, basePropertyName + MDF_EXT);

            if (useSnapshot && hasSQLBackup(dbLoc.getDbName())) {
                dbMaintainService.restoreDatabase(dbLoc);
            } else {
                File dataFile = new File(destination, dbLoc.getDbName() + MDF_EXT);
                File logFile = new File(destination, dbLoc.getDbName() + LDF_EXT);

                FileUtils.copyFile(sourceDataFile, dataFile, true);
                File sourceLogFile = new File(source, basePropertyName + LDF_EXT);

                if (!sourceLogFile.exists()) {
                    sourceLogFile = new File(source, basePropertyName + LOG_LDF_EXT);

                    if (!sourceLogFile.exists()) {
                        throw new TetrisException("No log file found for " + dbLoc.getDbName());
                    }
                }

                FileUtils.copyFile(sourceLogFile, logFile, true);
            }
        } catch (IOException e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error copying tenant property template for LMS: "
                    + dbLoc.getDbName(), e);
        }
    }

    public DBLoc createLearningProperty(Integer propertyId, Integer clientId) {
        String dbName = getDbName(propertyId);

        try {
            LOGGER.info("Creating tenant property " + dbName);
            DBLoc dbLoc = getDbLocFrom(dbName);

            if (dbLoc == null) {
                throw new TetrisException("DBLoc not found for database: " + dbName);
            }

            String destination = SystemConfig.getLmsSqlDestination();
            if (SystemConfig.isLmsSqlLinux())
                destination = SystemConfig.getLmsSqlLinuxDestination();

            File dataFile = new File(destination, dbLoc.getDbName() + MDF_EXT);
            File logFile = new File(destination, dbLoc.getDbName() + LDF_EXT);
            Client client = globalCrudService.find(Client.class, clientId);
            String clientCode = (client != null) ? client.getCode() : null; // clientId is invalid when property is 'reset', so client is null.
            String propertyCode = LMSProperty.getPropertyCode(propertyId);
            dbMaintainService.attachDatabase(dbLoc, clientCode, propertyCode, dataFile, logFile);

            return dbLoc;
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error creating tenant property for LMS: " + propertyId, e);
        }
    }

    public DBLoc createOrUpdateLearningProperty(Integer propertyId, Integer clientId) {
        String dbName = getDbName(propertyId);

        LOGGER.info("Creating tenant property " + dbName);
        DBLoc dbLoc = getDbLocFrom(dbName);

        if (dbLoc == null) {
            throw new TetrisException("DBLoc not found for database: " + dbName);
        }

        Client client = globalCrudService.find(Client.class, clientId);
        String propertyCode = LMSProperty.getPropertyCode(propertyId);
        // update or create the property
        if (updateProperty(propertyId, dbLoc) < 1) {
            Property property = new Property(propertyId);
            property.setClient(client);
            property.setCode(propertyCode);
            property.setName(LMSProperty.getPropertyName(propertyId));
            property.setStatus(Status.ACTIVE);
            property.setCreateDate(LocalDateTime.now());
            property.setDbLocId(dbLoc.getId());
            property.setStage(LMSProperty.getStage(propertyId));
            property.setSfdcAccountNo(SFDC_ACCOUNT_NO);
            LOGGER.info("#ExtraLogsForDBLocID : LearningManagementService - Logging DBLocId : " + dbLoc.getId() + " from global property for property: " + property.getId());
            dbMaintainService.createProperty(property);
        }

        return dbLoc;
    }

    @SuppressWarnings("unchecked")
    @Async
    @AsyncJobCallback
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Future<Object> runPropertyMaintenanceAsync(JobStepContext jobStepContext, WorkContextType workContextType,
                                                      String basePropertyName, String dbName) {
        runPropertyMaintenance(basePropertyName, dbName);
        return AsyncCallbackDataBuilder.buildFuture(dbName);
    }


    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void runPropertyMaintenance(String basePropertyName, String dbName) {
        try {
            DBLoc dbLoc = getDbLocFrom(dbName);
            String scriptLocation = SystemConfig.getLmsSupplementalScriptLocation();
            File scriptDir = new File(scriptLocation);
            File tenantMaintFile = new File(scriptDir, TENANT_MAINTENANCE_FILENAME);

            if (!tenantMaintFile.exists()) {
                throw new FileNotFoundException(tenantMaintFile.getAbsolutePath());
            }

            String[] sql = readFile(tenantMaintFile);

            dbMaintainService.runPropertyMaintenance(sql, basePropertyName, dbLoc);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error running tenant property maintenance on " + dbName, e);
        }
    }

    public SASFileLoc createSasProperty(Integer propertyId, String basePropertyName, boolean useSnapshot) {
        try {
            File sourceFolder = new File(SystemConfig.getLmsSasSource(), basePropertyName);
            File destinationFolder = new File(SystemConfig.getLmsSasDestination(), propertyId.toString());

            if (destinationFolder.exists()) {
                FileUtils.cleanDirectory(destinationFolder);
            } else {
                destinationFolder.mkdirs();
            }

            SASFileLoc loc = globalCrudService.findByNamedQuerySingleResult(SASFileLoc.BY_PROPERTY_ID,
                    QueryParameter.with(PARAM_PROPERTY_ID, propertyId).parameters());

            if (useSnapshot && hasSASBackup(propertyId.toString())) {
                File snapshotFolder = new File(SystemConfig.getLmsSnapshotLocation());
                File zipFile = new File(snapshotFolder, propertyId + SAS_ZIP_SUFFIX);
                compressService.uncompress(ZIP, zipFile.getAbsolutePath(), destinationFolder.getAbsolutePath());
            } else {
                FileUtils.copyDirectory(sourceFolder, destinationFolder);
            }

            if (loc == null) {
                LOGGER.info("Creating SAS property " + propertyId);
                loc = new SASFileLoc();
                loc.setPropertyId(propertyId);

                loc.setAnalyticsDataSetPath(SystemConfig.getSasFileLoc() + propertyId);

                String context = "pacman." + PacmanWorkContextHelper.getClientCode() + "." + LMSProperty.getPropertyCode(propertyId);
                String serverName = pacmanConfigParamsService.getParameterValue(context, IntegrationConfigParamName.SAS_PROPERTY_NODE);
                if (StringUtils.isEmpty(serverName)) {
                    throw new TetrisException(ErrorCode.MISSING_DEFAULT_SERVER, IntegrationConfigParamName.SAS_PROPERTY_NODE.getParameterName() + " not configured");
                }
                loc.setSasServerName(serverName);

                loc.setStatusId(Status.ACTIVE.getId());
                loc.setCreateDate(LocalDateTime.now());
                globalCrudService.save(loc);
            }

            return loc;
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error creating SAS property for LMS: " + propertyId, e);
        }
    }

    public void associateUserWithProperties(String propertyGroupName, LDAPUser user, Integer clientId, Integer[] propertyIds) {
        LocalDateTime createDate = LocalDateTime.now();
        boolean needsSave = false;

        if (!user.isInternal()) {
            authorizeUserForProperties(user, propertyIds, user.getClient());
        }

        PropertyGroup propertyGroup = findPropertyGroup(propertyGroupName, clientId);

        if (propertyGroup == null) {
            LOGGER.info("Property group not found for user " + user.getUid() + ", client " + clientId + ". Creating it.");
            propertyGroup = new PropertyGroup();
            propertyGroup.setClientId(clientId);
            propertyGroup.setName(propertyGroupName);
            propertyGroup.setDescription(propertyGroupName);
            propertyGroup.setUserId(user.getUserId());
            propertyGroup.setCreatedByUserId(1);
            propertyGroup.setCreateDate(LocalDateUtils.toDate(createDate));
            propertyGroup.setPropertyPropertyGroups(new HashSet<PropertyPropertyGroup>());
            needsSave = true;
        }

        for (Integer propertyId : propertyIds) {
            Property property = globalCrudService.find(Property.class, propertyId);

            if (!associatedWithProperty(propertyGroup, propertyId)) {
                LOGGER.info("Property group association not found for property group " + propertyGroup.getId() + ", property " + propertyId + ". Creating it.");

                PropertyPropertyGroup propertyPropertyGroup = new PropertyPropertyGroup();
                propertyPropertyGroup.setPropertyGroup(propertyGroup);
                propertyPropertyGroup.setProperty(property);
                propertyPropertyGroup.setStatus(Status.ACTIVE);
                propertyPropertyGroup.setCreateDate(LocalDateUtils.toDate(createDate));
                propertyGroup.getPropertyPropertyGroups().add(propertyPropertyGroup);

                needsSave = true;
            }
        }

        if (needsSave) {
            globalCrudService.save(propertyGroup);
        }

        if (Constants.LEARNING_CLIENT_CODE.equals(user.getClient())) {
            LearningClientUser lcu = globalCrudService.findByNamedQuerySingleResult(
                    LearningClientUser.BY_USERID_AND_CLIENTID,
                    QueryParameter.with(PARAM_USER_ID, user.getUserId()).and(PARAM_CLIENT_ID, clientId).parameters());

            if (lcu == null) {
                LOGGER.info("LMS Client/User entry not found for user " + user.getUserId() +
                        ", client " + clientId + ". Creating it.");
                ClientUserMapping clientUser = globalCrudService.findByNamedQuerySingleResult(
                        ClientUserMapping.BY_USERID_AND_CLIENTID,
                        QueryParameter.with(ClientUserMapping.PARAM_USER_ID, user.getUserId()).and(ClientUserMapping.PARAM_CLIENT_ID, clientId).parameters());

                lcu = new LearningClientUser();
                lcu.setClientUser(clientUser);
                lcu.setCreatedDate(LocalDateUtils.toDate(createDate));
                lcu.setLastUpdatedTimestamp(createDate);
                lcu.setStatus(LearningClientUser.Status.AVAILABLE.toString());
                globalCrudService.save(lcu);
            }
        }
    }

    public LDAPUser createUser(String firstName, String lastName, String email, String clientCode) {

        LDAPUser ldapUser = userService.getByEmailFromDB(email);

        if (ldapUser == null) {
            LOGGER.info("User " + email + " not found in LDAP. Creating new one.");
            ldapUser = new LDAPUser();
            ldapUser.setMail(email);
            ldapUser.setDN("ou=users,o=" + clientCode.toLowerCase() + ",dc=ideas,dc=com");
            ldapUser.setGivenName(firstName);
            ldapUser.setSn(lastName);
            ldapUser.setCn(firstName + " " + lastName);
            ldapUser.setClient(clientCode);
            ldapUser = userService.create(ldapUser, false, "password");
        } else {
            LOGGER.info("User " + email + " already in LDAP: " + ldapUser.getUid());
        }

        Integer clientId = globalCrudService.findByNamedQuerySingleResult(
                Client.GET_ID_BY_CODE,
                QueryParameter.with(PARAM_CODE, clientCode).parameters());
        ClientUserMapping clientUser = globalCrudService.findByNamedQuerySingleResult(
                ClientUserMapping.BY_USERID_AND_CLIENTID,
                QueryParameter.with(ClientUserMapping.PARAM_USER_ID, ldapUser.getUserId()).and(ClientUserMapping.PARAM_CLIENT_ID, clientId).parameters());

        if (clientUser == null) {
            clientUser = new ClientUserMapping();
            clientUser.setClientID(clientId);
            clientUser.setUserID(ldapUser.getUserId());
            globalCrudService.save(clientUser);
        }

        clientPropertyCacheService.clearCaches();
        return ldapUser;
    }

    private void authorizeUserForProperties(LDAPUser ldapUser, Integer[] propertyIds, String clientCode) {
        Role role = roleService.findRoleByClientAndName(clientCode, "Regional Revenue Manager");
        List<PropertyRoleMapping> propertyRoleMappings = new ArrayList<>();
        List<String> propertyCodes = new ArrayList<>();

        for (Integer propertyId : propertyIds) {
            Property property = globalCrudService.find(Property.class, propertyId);
            propertyCodes.add(property.getCode());
            propertyRoleMappings.add(new PropertyRoleMapping(role.getUniqueIdentifier(), propertyId.toString()));
        }

        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY);
        workContext.setPropertyCode(propertyCodes.get(0));
        workContext.setUserId(ldapUser.getUid());

        ldapUser.setPropertyRoles(propertyRoleMappings);
        ldapUser.setDefaultPropertyOrGroup("Property");
        ldapUser.setDefaultLandingPage("information-manager");
        ldapUser.setDefaultProperty(propertyIds[0].toString());

        LOGGER.info("Updating user " + ldapUser.getUid() + " to add property role mappings for " + Arrays.toString(propertyIds));
        userService.update(ldapUser.getUid(), ldapUser);
    }

    public void startRefreshLearningDatabaseJob(Integer uid) {
        LearningClientUser lcu = globalCrudService.findByNamedQuerySingleResult(LearningClientUser.BY_USERID,
                QueryParameter.with(PARAM_USER_ID, uid).parameters());

        try {
            if (lcu != null) {
                lcu.setStatus(LearningClientUser.Status.REFRESHING.toString());
                lcu.setLastUpdatedTimestamp(LocalDateTime.now());
                globalCrudService.save(lcu);
                globalCrudService.flush();

                List<Integer> propertyIds = globalCrudService.findByNativeQuery(DISTINCT_PROPERTIES_FOR_CLIENT_AND_USER,
                        QueryParameter.with(PARAM_USER_ID, uid).and(PARAM_CLIENT_ID, lcu.getClientUser().getClientID()).parameters());
                if (CollectionUtils.isNotEmpty(propertyIds)) {
                    propertyIds.forEach((propertyId) -> {
                        HashMap<String, Object> parameters = new HashMap<>();
                        parameters.put(PARAM_DATE, Long.toString(System.currentTimeMillis()));
                        parameters.put(PARAM_PROPERTY_ID, propertyId.toString());
                        parameters.put(JobParameterKey.LEARNING_USER_ID, uid);

                        setPropertyStatus(propertyId, Status.ACTIVE);

                        parameters.put(PARAM_TEMPLATE_PROPERTY_NAME, LMSProperty.getTemplateName(propertyId));

                        jobService.startJob(JobName.RefreshLearningDatabaseJob, parameters);
                    });
                }
            }
        } catch (OptimisticLockException e) {
            // do nothing.  job already started for this user
            LOGGER.debug("Job already started for user: " + uid, e);
        }
    }

    public void setPropertyStatus(Integer propertyId, Status status) {
        Property property = propertyService.getPropertyById(propertyId, false);
        property.setStatus(status);

        // Need to set the decision type for the property
        LMSProperty lmsProperty = LMSProperty.getLMSProperty(propertyId);
        LMSPropertyParameters parameters = lmsProperty.getParameters();
        setConfigParameter(property, IPConfigParamName.BAR_BAR_DECISION.value(), parameters.getBarDecision());
        setConfigParameter(property, FeatureTogglesConfigParamName.WHAT_IF_ENABLED.value(), Boolean.TRUE.toString());
        setConfigParameter(property, FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value(), String.valueOf(parameters.isGroupPricingEnabled()));
        setConfigParameter(property, FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value(), String.valueOf(parameters.isFunctionSpaceEnabled()));
        setConfigParameter(property, IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value(), String.valueOf(parameters.isLDB()));
        setConfigParameter(property, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value(), String.valueOf(parameters.getPropertyTimeZone()));
        setConfigParameter(property, IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value(), String.valueOf(parameters.getExternalSystem()));
        setConfigParameter(property, IPConfigParamName.BAR_WEBRATE_STALENESS_ALERTS_ENABLED.value(), String.valueOf(parameters.isWebrateAlertsEnabled()));
        setConfigParameter(property, FeatureTogglesConfigParamName.ENABLE_MANUAL_BARUPLOAD.value(), String.valueOf(parameters.isManualBarUploadEnabled()));
        setConfigParameter(property, FeatureTogglesConfigParamName.RRAENABLED.value(), String.valueOf(parameters.isRRAEnabled()));
        setConfigParameter(property, FeatureTogglesConfigParamName.STRENABLED.value(), String.valueOf(parameters.isSTREnabled()));
        setConfigParameter(property, FeatureTogglesConfigParamName.MARKET_PERFORMANCE_ENABLED.value(), String.valueOf(parameters.isMarketPerformanceEnabled()));
        setConfigParameter(property, FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value(), String.valueOf(parameters.isComponentRoomsEnabled()));
        setConfigParameter(property, FeatureTogglesConfigParamName.DEMAND360_DEMAND360DASHBOARD_ENABLED.value(), String.valueOf(parameters.isDemand360Enabled()));
        setConfigParameter(property, FeatureTogglesConfigParamName.DEMAND_AND_WASH_MANAGEMENT_EXTENDED_STAY_ENABLED.value(), String.valueOf(parameters.isExtendedStayEnabled()));
        setConfigParameter(property, FeatureTogglesConfigParamName.IS_ADVANCED_PRICE_RANKING_ENABLED.value(), String.valueOf(parameters.isAdvancedPriceRankEnabled()));
        setConfigParameter(property, PreProductionConfigParamName.GROUP_PRICING_MULTI_PROPERTY_ENABLED.value(), String.valueOf(parameters.isGroupPricingMultiPropertyEnabled()));
        setConfigParameter(property, GUIConfigParamName.IS_PRICING_REDESIGN_ENABLED.value(), String.valueOf(parameters.isPricingRedesignEnabled()));
        setConfigParameter(property, FeatureTogglesConfigParamName.ANALYTICAL_AGILE_RATES_ROOM_CLASS.value(), String.valueOf(parameters.isAnalyticalAgileRateRCEnabled()));
        setConfigParameter(property, FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value(), String.valueOf(parameters.isAgileRatesEnabled()));
        setConfigParameter(property, PreProductionConfigParamName.ENABLE_NEW_PRICING_INVESTIGATOR.value(), String.valueOf(parameters.isNewPricingInvestigatorEnabled()));
        setConfigParameter(property, PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED.value(), String.valueOf(parameters.isIndependentProductsEnabled()));
        setConfigParameter(property, FeatureTogglesConfigParamName.INCLUDE_INDEPENDENT_PRODUCTS_IN_REPORT.value(), String.valueOf(parameters.isIncludeIndependentProductsInReportEnabled()));
        setConfigParameter(property, PreProductionConfigParamName.ENABLE_PRODUCT_DIMENSION_FOR_COMPETITIVE_NOTIFICATIONS.value(), String.valueOf(parameters.isProductDimensionForCompetitiveNotificationsEnabled()));
        setConfigParameter(property, PreProductionConfigParamName.USE_MKT_ACCOM_ACTIVITY_FOR_PAST_DATA.value(), String.valueOf(parameters.isUseMktAccomActivityForPastDataEnabled()));
        setConfigParameter(property, PreProductionConfigParamName.USE_OLD_PRODUCT_FAMILY_ID.value(), String.valueOf(parameters.isUseOldProductFamilyIdEnabled()));

        if (parameters.isAMSEnabled()) {
            setConfigParameter(property, FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value(), String.valueOf(parameters.isAMSEnabled()));
            setConfigParameter(property, GUIConfigParamName.POPULATION_ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE.value(), String.valueOf(parameters.isAMSEnabled()));
        }

        if (equalsIgnoreCase(Constants.BAR_DECISION_VALUE_RATEOFDAY, parameters.getBarDecision())) {
            setConfigParameter(property, IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value(), Boolean.FALSE.toString());
        }

        // Continuous Pricing properties need to have the config parameters set and the stage be greater than two way for
        // pretty pricing
        if (parameters.isContinuousPricing()) {
            setConfigParameter(property, GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value(), String.valueOf(parameters.isSupplementalEnabled()));
            setConfigParameter(property, GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value(), Boolean.TRUE.toString());
            setConfigParameter(property, GUIConfigParamName.IS_PRICING_CONFIGURATION_ENABLED.value(), Boolean.TRUE.toString());
            setConfigParameter(property, "pacman.feature.RatePlanConfigurationEnabled", Boolean.FALSE.toString());
            setConfigParameter(property, "pacman.feature.isPricingManagementEnabled", Boolean.FALSE.toString());
        }
        if (equalsIgnoreCase(URBANCLUB, property.getName()) || equalsIgnoreCase(DELMAR, property.getName())) {
            setConfigParameter(property, IPConfigParamName.USE_COMPACT_WEBRATE_PACE.value(), String.valueOf(parameters.isUseCompactWebratePace()));
        }
        if (equalsIgnoreCase(URBANCLUB, property.getName())) {
            setConfigParameter(property, FeatureTogglesConfigParamName.MAX_UPLOADED_AGILE_RATES.value(), "10");
            setConfigParameter(property, FeatureTogglesConfigParamName.MAX_ACTIVE_AGILE_RATES.value(), "10");
            setConfigParameter(property, IPConfigParamName.CHANNEL_DEMAND_FORECAST_ENABLED.value(), Boolean.TRUE.toString());
            setConfigParameter(property, FeatureTogglesConfigParamName.FORECAST_DASHBOARD_ENABLED.value(), Boolean.TRUE.toString());
            setConfigParameter(property, FeatureTogglesConfigParamName.CHANNEL_COST_V3.value(), Boolean.TRUE.toString());
        }
        if (equalsIgnoreCase("COASTAL", property.getName())) {
            setConfigParameter(property, FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value(), String.valueOf(parameters.isPerPersonPricingEnabled()));
            setConfigParameter(property, PreProductionConfigParamName.CP_CHILD_AGE_BUCKETS_ENABLED.value(), String.valueOf(parameters.isChildAgeBucketsEnabled()));
            setConfigParameter(property, IntegrationConfigParamName.UPLOAD_CHILD_AGE_BUCKETS.value(EXTERNAL_SYSTEM_OPERA), String.valueOf(parameters.isUploadChildAgeBucketsEnabled()));
            String context = "anyhtng." + property.getClient().getCode() + '.' + property.getCode();
            pacmanConfigParamsService.addParameterValue(context, ParameterConversionUtility.convertOldParameterToNew(IntegrationConfigParamName.UPLOAD_ADULTS_BEYOND_2.value("anyhtng")), String.valueOf(parameters.isUploadAdultsBeyond2Enabled()));
            setConfigParameter(property, "pacman.integration.UploadAdultsBeyond2", String.valueOf(parameters.isUploadAdultsBeyond2Enabled()));
            pacmanConfigParamsService.addParameterValue(context, ParameterConversionUtility.convertOldParameterToNew(IntegrationConfigParamName.UPLOAD_CHILDREN_BEYOND_EXTRA.value("anyhtng")), String.valueOf(parameters.isUploadChildrenBeyondExtraEnabled()));
            setConfigParameter(property, "pacman.integration.UploadChildrenBeyondExtra", String.valueOf(parameters.isUploadChildrenBeyondExtraEnabled()));
        }
        if (equalsIgnoreCase(DELMAR, property.getName())) {
            setConfigParameter(property, GUIConfigParamName.IS_FUNCTION_SPACE_PACKAGE_ENABLED.value(), Boolean.TRUE.toString());
        }
        if (equalsIgnoreCase(URBANCLUB, property.getName()) || equalsIgnoreCase(CANYON, property.getName()) || equalsIgnoreCase(COASTAL, property.getName())) {
            setConfigParameter(property, GUIConfigParamName.FIXED_ABOVE_BAR_PRODUCT_ENABLED.value(), Boolean.TRUE.toString());
            setConfigParameter(property, PreProductionConfigParamName.LINKED_PRODUCT_HIERARCHY_ENABLED.value(), Boolean.TRUE.toString());
        }

        if (equalsIgnoreCase(MAJESTICPINES, property.getName())) {
            setConfigParameter(property, FeatureTogglesConfigParamName.SCHEDULE_REPORT_ENABLED.value(), Boolean.TRUE.toString());
            setConfigParameter(property, FeatureTogglesConfigParamName.GENERATE_RESTRICTIONS_IN_ONE_WAY.value(), Boolean.TRUE.toString());
            setConfigParameter(property, FeatureTogglesConfigParamName.ENABLE_RESTRICTION_LEVEL_REPORT.value(), Boolean.TRUE.toString());
            setConfigParameter(property, FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED.value(), Boolean.TRUE.toString());
            setConfigParameter(property, FeatureTogglesConfigParamName.RATES_BASE_OCCUPANCY_NUMBER.value(), Constants.DOUBLE);
            setConfigParameter(property, PreProductionConfigParamName.PRICING_INVESTIGATOR_FETCH_COMPETITOR_PRICES_FROM_ALL_CHANNELS.value(), Boolean.TRUE.toString());
            setConfigParameter(property, FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.getParameterName(), Boolean.TRUE.toString());
            setConfigParameter(property, FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED.getParameterName(), Boolean.TRUE.toString());
            setConfigParameter(property, FeatureTogglesConfigParamName.CHANNEL_COST_V3.getParameterName(), Boolean.TRUE.toString());
        }
        if (equalsIgnoreCase(DELMAR, property.getName()) || equalsIgnoreCase(CITYSTAY, property.getName())) {
            setConfigParameter(property, FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE_DOW_AND_SEASONS.value(), Boolean.FALSE.toString());
        }
        if (equalsIgnoreCase(DELMAR, property.getName())){
            setConfigParameter(property, FeatureTogglesConfigParamName.GO_LIVE_ENABLED.value(), Boolean.TRUE.toString());
        }

        propertyService.updateProperty(property);
    }

    public void propertyCompleted(Integer propertyId) {
        if(Constants.LEARNING_CLIENT_CODE.equals(PacmanWorkContextHelper.getClientCode())) {
            PropertyPropertyGroup ppg = globalCrudService.findByNamedQuerySingleResult(PropertyPropertyGroup.BY_CLIENT_AND_PROPERTY, QueryParameter.with(PARAM_CLIENT_ID, PacmanWorkContextHelper.getClientId()).and(PARAM_PROPERTY_ID, propertyId).parameters());
            if (ppg == null) {
                throw new TetrisException("PropertyPropertyGroup not found for property ID " + propertyId);
            }

            PropertyGroup propertyGroup = ppg.getPropertyGroup();

            for (PropertyPropertyGroup current : propertyGroup.getPropertyPropertyGroups()) {
                if (!current.getProperty().getId().equals(propertyId) && !current.getProperty().getStatus().equals(Status.ACTIVE)) {
                    // not all properties are ready
                    return;
                }
            }

            LearningClientUser lcu = globalCrudService.findByNamedQuerySingleResult(LearningClientUser.BY_USERID,
                    QueryParameter.with(PARAM_USER_ID, propertyGroup.getUserId()).parameters());

            if (lcu != null) {
                lcu.setStatus(LearningClientUser.Status.AVAILABLE.toString());
                lcu.setLastUpdatedTimestamp(LocalDateTime.now());
                globalCrudService.save(lcu);
            }
        }
    }

    @Async
    @AsyncJobCallback    public Future<Object> runSasPropertyMaintenanceAsync(JobStepContext jobStepContext,
                                                         WorkContextType workContextType, Integer propertyId) {
        runSasPropertyMaintenance(propertyId);
        return AsyncCallbackDataBuilder.buildFuture(propertyId.toString());
    }

    public void runSasPropertyMaintenance(Integer propertyId) {
        Connection connection = null;

        try {
            connection = sasDbToolService.getTenantConnection(propertyId);

            String scriptLocation = SystemConfig.getLmsSupplementalScriptLocation();
            File scriptDir = new File(scriptLocation);
            File sasMaintFile = new File(scriptDir, SAS_MAINTENANCE_FILENAME);

            if (!sasMaintFile.exists()) {
                throw new FileNotFoundException(sasMaintFile.getAbsolutePath());
            }

            for (String sql : readFile(sasMaintFile)) {
                if (!StringUtils.isEmpty(sql) && !sql.startsWith("#")) {
                    LOGGER.info("Executing SQL on SAS property[" + propertyId + "]: " + sql);

                    if (SystemConfig.isSASRestClientDatasetEnabled()) {
                        sql = sql.replace("@toProp", propertyId.toString());
                        sasDbToolService.executeUpdate(propertyId, sasDbToolService.getTenantLibRef(propertyId), sql);
                    } else {
                        execSasPropertyUpdate(connection, sql, propertyId);
                    }
                }
            }
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Could not run SAS maintenance scripts", e);
        } finally {
            closeConnection(connection);
        }

        //Narendra
        try {
            String scriptLocation = SystemConfig.getLmsSupplementalScriptLocation();
            File scriptDir = new File(scriptLocation);
            File sasMaintFile = new File(scriptDir, getSasMaintenanceFilename(propertyId));
            if (sasMaintFile != null && sasMaintFile.exists()) {

                for (String sql : readFile(sasMaintFile)) {
                    if (!StringUtils.isEmpty(sql) && !sql.startsWith("#")) {
                        String[] els = sql.split("\\|");
                        String fgid = els[0];
                        String acid = els[1];
                        String query = els[2];
                        File fcstDataDir = new File(SystemConfig.getSasAnalyticsDataSetPath() + propertyId + "/fcst_data/" + fgid + "_" + acid);

                        if (!fcstDataDir.exists()) {
                            throw new TetrisException("Forecast dataset does not exist: " + fcstDataDir.getAbsolutePath());
                        }

                        String fcstlibref = "FCST '" + fcstDataDir.getAbsolutePath() + "';";


                        if (SystemConfig.isSASRestClientDatasetEnabled()) {
                            query = query.replace("@toProp", propertyId.toString());
                            sasDbToolService.executeUpdate(propertyId, fcstlibref, query);
                        } else {
                            connection = sasDbToolService.getConnection(propertyId, fcstlibref);
                            LOGGER.info("Executing SQL on SAS property[" + propertyId + "]: " + fgid + acid + query);
                            execSasPropertyUpdate(connection, query, propertyId);
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Could not run SAS maintenance scripts", e);
        } finally {
            closeConnection(connection);
        }
    }

    private void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
            } catch (Exception e) {
                LOGGER.warn("Couldn't close SAS connection", e);
            }
        }
    }

    private String getSasMaintenanceFilename(Integer propertyId) {
        return "sasfg" + LMSProperty.getPropertyName(propertyId).toLowerCase() + ".maintenance.sql";
    }

    public void associateInternalUsersWithProperties(Integer clientId, String propertyGroupName, Integer[] propertyIds) {
        for (LDAPUser ldapUser : userService.getAllUsers(true)) {
            ClientUserMapping clientUser = globalCrudService.findByNamedQuerySingleResult(ClientUserMapping.BY_USERID_AND_CLIENTID,
                    QueryParameter.with(ClientUserMapping.PARAM_USER_ID, ldapUser.getUserId()).and(ClientUserMapping.PARAM_CLIENT_ID, clientId).parameters());

            if (clientUser == null) {
                LOGGER.info("Creating client/user mapping for " + clientId + "/" + ldapUser.getUserId());
                clientUser = new ClientUserMapping();
                clientUser.setClientID(clientId);
                clientUser.setUserID(ldapUser.getUserId());
                globalCrudService.save(clientUser);
            }

            associateUserWithProperties(propertyGroupName, ldapUser, clientId, propertyIds);
        }
    }

    public void dropProperty(int propertyId) {
        String dbName = getDbName(propertyId);

        try {
            if (propertyExists(propertyId)) {
                LOGGER.info("Dropping database " + dbName);
                dbMaintainService.dropDatabase(propertyId);
            } else {
                DBLoc dbLoc = new DBLoc();
                dbLoc.setDbName(dbName);
                dbLoc.setServerName(SystemConfig.getDbHost());
                dbLoc.setPortNumber(Integer.parseInt(SystemConfig.getDbPort()));
                dbMaintainService.dropDatabase(dbLoc);
            }
            removeLocationReference(propertyId);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error copying tenant property template for LMS: " + propertyId, e);
        }
    }

    @SuppressWarnings("unchecked")
    public void removeClientUserMappings(Integer clientId, int start, int total) {
        Client client = globalCrudService.find(Client.class, clientId);
        List<ClientUserMapping> mappings = globalCrudService.findByNamedQuery(ClientUserMapping.BY_CLIENTID,
                QueryParameter.with(ClientUserMapping.PARAM_CLIENT_ID, clientId).parameters());
        List<String> emailsToDelete = new ArrayList<>();

        for (int i = start; i <= total; i++) {
            emailsToDelete.add(buildEmail(client.getCode(), i));
        }

        List<ClientUserMapping> mappingsToDelete = new ArrayList<>();

        for (ClientUserMapping clientUserMapping : mappings) {
            GlobalUser user = userService.getGlobalUser(clientUserMapping.getUserID(), false);
            if (emailsToDelete.contains(user.getEmail())) {
                List<LearningClientUser> users = globalCrudService.findByNamedQuery(LearningClientUser.BY_CLIENTUSERID,
                        QueryParameter.with(PARAM_CLIENT_USER_ID, clientUserMapping.getId()).parameters());
                globalCrudService.delete(users);
                mappingsToDelete.add(clientUserMapping);
            }
        }

        globalCrudService.delete(mappingsToDelete);
    }

    public Long startRefreshSalesPropertJob(Integer id) {
        String propertyId = id.toString();
        HashMap<String, Object> parameters = new HashMap<>();
        parameters.put(PARAM_DATE, Long.toString(System.currentTimeMillis()));
        parameters.put(PARAM_PROPERTY_ID, propertyId);
        parameters.put(PARAM_TEMPLATE_PROPERTY_NAME, LMSProperty.getTemplateName(id));

        return jobService.startJob(JobName.RefreshLearningDatabaseJob, parameters);
    }

    public Long startBuildLearningDatabaseJob(Map<String, Object> parameters) {
        return jobService.startJob(JobName.BuildLearningDatabasesJob, parameters);
    }

    public void setConfigParameter(String clientCode, String name, String value) {
        StringBuilder context = new StringBuilder(CONTEXT_PREFIX_PACMAN).append(clientCode);
        pacmanConfigParamsService.updateParameterValue(context.toString(), name, value);
    }

    public void setConfigParameter(Property property, String name, String value) {
        String context = CONTEXT_PREFIX_PACMAN + property.getClient().getCode() + '.' + property.getCode();
        String parameterValue = pacmanConfigParamsService.getValue(context, name);
        if (parameterValue != null && !StringUtils.isEmpty(parameterValue)) {
            pacmanConfigParamsService.updateParameterValue(context, name, value);
        } else {
            pacmanConfigParamsService.addParameterValue(context, name, value);
        }
    }

    public void disableSyncRequired(String clientCode) {
        // sets matching parameter values to false (1001)
        String sql = "update Config_Parameter_Value set Config_Parameter_Predefined_Value_ID = 1001 " +
                "from Config_Parameter_Value a inner join Config_Parameter b on a.config_parameter_id=b.Config_Parameter_ID " +
                "where b.Name  like '%sync%requ%' and a.context like 'pacman." + clientCode + "%'";
        Query query = globalCrudService.getEntityManager().createNativeQuery(sql);
        query.executeUpdate();
    }

    public int getMaxSalesUsersID() {
        return ((BigInteger) globalCrudService.findByNativeQuerySingleResult("select max(Sales_Users_ID) from Sales_Users;", null)).intValue();
    }

    public void executeSupplementaryScripts(String scriptLocation, Integer propertyId) {
        LMSProperty lmsProperty = LMSProperty.getLMSProperty(propertyId);

        File scriptDir = new File(scriptLocation);
        if (scriptDir.exists()) {
            File[] files = scriptDir.listFiles(new ScriptFileFilter(lmsProperty));

            for (File file : files) {
                try {
                    LOGGER.info("Executing supplemental script '" + file.getName() + "' on property " + propertyId);
                    String sql = FileUtils.readFileToString(file);
                    if (file.getName().contains("global")) {
                        globalCrudService.executeUpdateByNativeQuery(sql, propertyId == null ? null :
                                QueryParameter.with(PARAM_PROPERTY_ID, propertyId).parameters());
                    } else {
                        multiPropertyCrudService.executeNativeUpdateOnSingleProperty(propertyId, sql, null);
                    }
                } catch (PersistenceException e) {
                    throw new TetrisException(ErrorCode.UNEXPECTED_ERROR,
                            "Error executing supplementary script: " + file.getAbsolutePath(), e);
                } catch (IOException e) {
                    throw new TetrisException(ErrorCode.UNEXPECTED_ERROR,
                            "Error reading supplementary script: " + file.getAbsolutePath(), e);
                }
            }
        }

        if (lmsProperty != null) {
            String sql = "update Property set property_name=:propertyName where property_id = :propertyId";
            String propertyName = lmsProperty.name();
            multiPropertyCrudService.executeNativeUpdateOnSingleProperty(propertyId, sql,
                    QueryParameter.with(PARAM_PROPERTY_NAME, propertyName).and(PARAM_PROPERTY_ID, propertyId).parameters());
        }
    }

    public void synchronizePropertyUser(Integer propertyId) {
        List<PropertyGroup> propertyGroups = globalCrudService.findByNamedQuery(PropertyGroup.BY_PROPERTY_ID,
                QueryParameter.with(PARAM_PROPERTY_ID, propertyId).parameters());

        for (PropertyGroup propertyGroup : propertyGroups) {
            LDAPUser user = userService.getById(propertyGroup.getUserId());
            userSynchronizationService.createTenantUser(propertyId, user.getUserId(), user.getMail(), user.getCn(),
                    user.getActive(), user.isInternal());
        }
    }

    public List<Property> getPropertiesForUser(Integer userId) {
        List<Property> properties = new ArrayList<>();

        List<PropertyGroup> propertyGroups = globalCrudService.findByNamedQuery(
                PropertyGroup.BY_USER_ID, QueryParameter.with(PARAM_USER_ID, userId).parameters());

        for (PropertyGroup propertyGroup : propertyGroups) {
            for (PropertyPropertyGroup propertyPropertyGroup : propertyGroup.getPropertyPropertyGroups()) {
                properties.add(propertyPropertyGroup.getProperty());
            }
        }

        return properties;
    }

    public void disableSyncRequired(String clientCode, String propertyCode) {
        // sets matching parameter values to false (1001)
        String sql = "update Config_Parameter_Value set Config_Parameter_Predefined_Value_ID = 1001 " +
                "from Config_Parameter_Value a inner join Config_Parameter b on a.config_parameter_id=b.Config_Parameter_ID " +
                "where b.Name like '%sync%requ%' and a.context = :context ";
        Query query = globalCrudService.getEntityManager().createNativeQuery(sql);
        query.setParameter(PARAM_CONTEXT, CONTEXT_PREFIX_PACMAN + clientCode + "." + propertyCode);
        query.executeUpdate();
    }

    private void execSasPropertyUpdate(Connection connection, String sql, Integer propertyId) throws SQLException {
        PreparedStatement preparedStatement = null;
        try {
            sql = sql.replace("@toProp", propertyId.toString());
            preparedStatement = connection.prepareStatement(sql);
            preparedStatement.executeUpdate();
        } catch (SQLException e) {
            if (e.getLocalizedMessage().contains("does not exist")) {
                LOGGER.debug("Dataset does not exist", e);
            } else {
                throw e;
            }
        } finally {
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                } catch (Exception e) {
                    LOGGER.warn("Couldn't close SAS prepated statement.", e);
                }
            }
        }
    }

    private boolean associatedWithProperty(PropertyGroup propertyGroup, Integer propertyId) {
        for (PropertyPropertyGroup ppg : propertyGroup.getPropertyPropertyGroups()) {
            if (ppg.getProperty().getId().equals(propertyId)) {
                return true;
            }
        }
        return false;
    }

    private String getDbName(Integer propertyId) {
        return StringUtils.leftPad(propertyId.toString(), 6, "0");
    }

    private boolean propertyExists(Integer propertyId) {
        String sql = "SELECT 1 FROM Property WHERE Property_ID = :propertyId";
        List results = globalCrudService.findByNativeQuery(sql, QueryParameter.with(PARAM_PROPERTY_ID, propertyId).parameters());
        return !results.isEmpty();
    }

    private void removeLocationReference(Integer propertyId) {
        String sql = "UPDATE Property SET DBLoc_ID = null,"
                + "Status_ID = 2 "
                + "WHERE Property_ID = :propertyId";
        Query query = globalCrudService.getEntityManager().createNativeQuery(sql);
        query.setParameter(PARAM_PROPERTY_ID, propertyId);
        query.executeUpdate();
    }

    private int updateProperty(Integer propertyId, DBLoc dbLoc) {
        String sql = "UPDATE Property SET "
                + "DBLoc_ID = :dbLocId,"
                + "Property_Code = :code, "
                + "Property_Name = :name, "
                + "SFDC_Account_Number = :sfdcAccountNo, "
                + "Stage = :stage "
                + "WHERE Property_ID = :propertyId";
        Query propertyQuery = globalCrudService.getEntityManager().createNativeQuery(sql);
        propertyQuery.setParameter(PARAM_PROPERTY_ID, propertyId);
        propertyQuery.setParameter(PARAM_DB_LOC_ID, dbLoc.getId());
        propertyQuery.setParameter(PARAM_CODE, LMSProperty.getPropertyCode(propertyId));
        propertyQuery.setParameter(PARAM_NAME, LMSProperty.getPropertyName(propertyId));
        propertyQuery.setParameter(SFDC_ACCOUNT_NUMBER, SFDC_ACCOUNT_NO);
        propertyQuery.setParameter(STAGE, LMSProperty.getStage(propertyId).toString());
        return propertyQuery.executeUpdate();
    }

    private PropertyGroup findPropertyGroup(String name, Integer clientId) {
        List<PropertyGroup> groups = globalCrudService.findByNamedQuery(PropertyGroup.ALL,
                QueryParameter.with(PARAM_CLIENT_ID, clientId).parameters());

        for (PropertyGroup propertyGroup : groups) {
            if (propertyGroup.getName().equals(name)) {
                return propertyGroup;
            }
        }

        return null;
    }

    private String[] readFile(File file) throws IOException {
        String content = FileUtils.readFileToString(file);
        return content.split("\\r?\\n");
    }

    public String buildEmail(String clientCode, Integer loopCount) {
        if (clientCode.equals(Constants.LEARNING_CLIENT_CODE)) {
            return "user" + loopCount + ".<EMAIL>";
        }

        return "user" + loopCount + "." + clientCode.toLowerCase() + "@ideas.com";
    }

    public String backupProperties(Integer[] propertyIds) {
        try {
            File snapshotLocation = new File(SystemConfig.getLmsSnapshotLocation());

            if (!snapshotLocation.exists()) {
                snapshotLocation.mkdirs();
            }

            StringBuilder results = new StringBuilder();

            for (Integer propertyId : propertyIds) {
                String dbName = getDbName(propertyId);

                if (!hasSASBackup(dbName)) {
                    File zipFile = new File(snapshotLocation, propertyId + SAS_ZIP_SUFFIX);
                    deleteFile(zipFile);
                    File destinationFolder = new File(SystemConfig.getLmsSasDestination(), propertyId.toString());
                    ZipDirectory dirToZip = new ZipDirectory(destinationFolder, null, false);
                    LOGGER.info("Creating snapshot " + zipFile.getCanonicalPath());
                    results.append(zipService.zipDirectory(dirToZip, zipFile.getAbsolutePath()));
                    results.append("\n");
                }
                if (!hasSQLBackup(dbName)) {
                    DBLoc dbLoc = getDbLocFrom(dbName);
                    File zipFile = new File(snapshotLocation, dbLoc.getDbName() + SQL_ZIP_SUFFIX);
                    deleteFile(zipFile);
                    LOGGER.info("Creating snapshot " + zipFile.getCanonicalPath());
                    results.append(dbMaintainService.backupDatabase(dbLoc, zipFile));
                    results.append("\n");
                }
            }

            return results.toString();
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error backing up " + propertyIds, e);
        }
    }

    private void deleteFile(File zipFile) {
        if (zipFile.exists()) {
            zipFile.delete();
        }
    }

    @Async
    @AsyncJobCallback
    public Future<Object> backupPropertiesAsync(JobStepContext jobStepContext, WorkContextType workContextType,
                                                Integer[] propertyIds) {
        String zipFile = backupProperties(propertyIds);
        return AsyncCallbackDataBuilder.buildFuture(zipFile);
    }


    public boolean hasBackup(String dbName) {
        return hasSQLBackup(dbName) && hasSASBackup(dbName);
    }

    public boolean hasSASBackup(String dbName) {
        return isBackupAvailable(dbName + SAS_ZIP_SUFFIX);
    }

    public boolean hasSQLBackup(String dbName) {
        return isBackupAvailable(dbName + SQL_ZIP_SUFFIX);
    }

    private boolean isBackupAvailable(String fileName) {
        String snapshotLocation = SystemConfig.getLmsSnapshotLocation();
        File sasZipFile = new File(snapshotLocation, fileName);
        return sasZipFile.exists();
    }

    public void removeSnapshots(String propertyId) {
        String snapshotLocation = SystemConfig.getLmsSnapshotLocation();
        final String propertyId1 = propertyId;
        try {
            File dir = new File(snapshotLocation);

            if (dir.exists()) {
                File[] zipFiles = dir.listFiles(new FilenameFilter() {
                    @Override
                    public boolean accept(File dir, String name) {
                        return name.toLowerCase().endsWith(".zip") &&
                                name.startsWith(propertyId1);
                    }
                });

                deleteTheseZipFiles(zipFiles);
            }
        } catch (IOException e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error removing files for '" + propertyId1 + "' from '" + snapshotLocation + "'", e);
        }
    }

    public List<LearningClientStatusCount> getLmsUserStatus() {
        // Get all existing status counts
        List<LearningClientStatusCount> existingStatusCounts = globalCrudService.findByNamedQuery(LearningClientUser.COUNT_BY_STATUS);

        Map<String, Integer> statusCounts = new HashMap<>();
        if (CollectionUtils.isNotEmpty(existingStatusCounts)) {
            statusCounts = existingStatusCounts.stream().collect(Collectors.toMap(LearningClientStatusCount::getStatus, LearningClientStatusCount::getCount));
        }

        // Get the stale status count
        DateTime cutoff = DateTime.now().minusHours(SystemConfig.getLmsHoursUntilStale());
        LearningClientStatusCount staleStatusCount = globalCrudService.findByNamedQuerySingleResult(LearningClientUser.COUNT_OF_STALE, QueryParameter.with("date", cutoff.toDate()).parameters());

        // Since I always want to return all statuses - building the list to return separately
        List<LearningClientStatusCount> retVal = new ArrayList<>();
        int total = 0;
        for (LearningClientUser.Status status : LearningClientUser.Status.values()) {

            // Get the count for the status and add it to the total
            int count = 0;
            if (statusCounts.containsKey(status.name())) {
                count = statusCounts.get(status.name()).intValue();
            }
            total += count;

            // Subtract off any 'STALE' users from the IN_USE count
            if (LearningClientUser.Status.IN_USE.equals(status) && staleStatusCount != null && staleStatusCount.getCount().intValue() > 0) {
                count -= staleStatusCount.getCount().intValue();
            }

            // Add it to the list to be returned
            retVal.add(new LearningClientStatusCount(status.name(), count));
        }

        // Add the StaleStatus count to the list
        retVal.add(staleStatusCount);

        // Add a total record to the list
        retVal.add(new LearningClientStatusCount("TOTAL", total));

        return retVal;
    }

    public int getLmsUserStatus(String status) {
        return getLmsUserStatus().stream().filter(statusCount -> statusCount.getStatus().equals(status)).mapToInt(LearningClientStatusCount::getCount).findFirst().getAsInt();
    }

    public void refreshStaleUsers() {
        for (LearningClientUser lcu : userService.getStaleLmsUsers()) {
            startRefreshLearningDatabaseJob(lcu.getClientUser().getUserID());

            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                LOGGER.debug("Sleep interrupted", e);
            }
        }
    }

    public void deleteLearningProperty(String clientCode, String propertyCode) {
        List<Integer> propertyIds = (List<Integer>) (Object) globalCrudService.findByNamedQuery(Client.GET_PROP_IDS_FROM_PROP_NAME_AND_CLIENT_CODE,
                QueryParameter.with(PARAM_PROPERTY_NAME, propertyCode).and("clientCode", clientCode).parameters());
        for (Integer propertyId : propertyIds) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put(JobParameterKey.PROPERTY_ID, propertyId);
            parameters.put(JobParameterKey.SFDC_CASE_NUMBER, "0000-0000");
            parameters.put(JobParameterKey.USER_ID, PacmanWorkContextHelper.getUserId());
            parameters.put(JobParameterKey.DATE, new Date());
            jobService.startJob(JobName.DeleteProperty, parameters);

            removeSnapshots(propertyId.toString());
        }
    }

    public int removeSnapshotFor(String clientCode, String propertyCode, String fileTypes, String fileName) {
        LOGGER.info(String.format("Request received to delete snapshots for client - %s, property - %s, zip files - %s, file names - %s ", clientCode, propertyCode, fileTypes, fileName));
        if (!equalsIgnoreCase(fileName, "ALL")) {
            Integer propertyId = valueOf(fileName.substring(0, 6));
            return removeSnapshotFor(propertyId, fileName.endsWith(SAS_ZIP_SUFFIX), fileName.endsWith(SQL_ZIP_SUFFIX));
        }
        List<Integer> propertyIds = getPropertyIDsForSnapshotsToBeDeleted(clientCode, propertyCode);
        int count = 0;
        for (Integer propertyId : propertyIds) {
            boolean deleteSAS = false;
            boolean deleteSQL = false;
            switch (fileTypes) {
                case "Only SAS":
                    deleteSAS = true;
                    break;
                case "Only SQL":
                    deleteSQL = true;
                    break;
                default:
                    deleteSAS = true;
                    deleteSQL = true;
                    break;
            }
            count = count + removeSnapshotFor(propertyId, deleteSAS, deleteSQL);
        }
        return count;
    }

    private List<Integer> getPropertyIDsForSnapshotsToBeDeleted(String clientCode, String propertyCode) {
        Map<String, Integer> clientsMap = new HashMap<>();
        clientsMap.put(G3_SALES, 0);
        clientsMap.put(G3_LMS, 1);
        clientsMap.put(G3_LMSDEV, 2);
        clientsMap.put(G3_CTSDEV, 3);
        clientsMap.put(G3_CTS, 4);


        Map<String, Integer> propertyMap = new HashMap<>();
        propertyMap.put(URBANCLUB, 1);
        propertyMap.put(COASTAL, 4);
        propertyMap.put(CITYSTAY, 5);

        Integer clientIdentifier = clientsMap.get(clientCode);
        Integer propertyIdentifier = propertyMap.get(propertyCode);

        List<Integer> allPropertyIDs = getAllPropertyIDs();

        return getListOfPropertiesToBeDeleted(allPropertyIDs, clientIdentifier, propertyIdentifier);
    }

    protected List<Integer> getListOfPropertiesToBeDeleted(List<Integer> allPropertyIDs, Integer clientIdentifier, Integer propertyIdentifier) {
        List<Integer> listOfPropertiesToBeDeleted = new ArrayList<>();
        listOfPropertiesToBeDeleted.addAll(allPropertyIDs);
        for (Integer propertyId : allPropertyIDs) {
            if ((propertyIdentifier != null && propertyId % 10 != propertyIdentifier) || (clientIdentifier != null && (propertyId / 10000) % 10 != clientIdentifier)) {
                listOfPropertiesToBeDeleted.remove(propertyId);
            }
        }
        return listOfPropertiesToBeDeleted;
    }

    private List<Integer> getAllPropertyIDs() {
        String snapshotLocation = SystemConfig.getLmsSnapshotLocation();
        File dir = new File(snapshotLocation);
        List<Integer> propertyIDs = new ArrayList<>();

        if (dir.exists()) {
            File[] zipFiles = dir.listFiles(new FilenameFilter() {
                @Override
                public boolean accept(File dir, String name) {
                    return name.toLowerCase().endsWith(".zip");
                }
            });

            for (File file : zipFiles) {
                String propertyId = file.getName().replaceAll("_", "").replaceAll("sas", "").replaceAll("sql", "").replaceAll(".zip", "");
                Integer propertyID = valueOf(propertyId);
                if (!propertyIDs.contains(propertyID)) {
                    propertyIDs.add(propertyID);
                }
            }
        }
        return propertyIDs;
    }

    private int removeSnapshotFor(Integer propertyId, boolean deleteSAS, boolean deleteSQL) {
        String snapshotLocation = SystemConfig.getLmsSnapshotLocation();
        try {
            File dir = new File(snapshotLocation);

            if (dir.exists()) {
                File[] zipFiles = dir.listFiles(new FilenameFilter() {
                    @Override
                    public boolean accept(File dir, String name) {
                        return (deleteSAS && equalsIgnoreCase(name, propertyId.toString() + SAS_ZIP_SUFFIX)) ||
                                (deleteSQL && equalsIgnoreCase(name, propertyId.toString() + SQL_ZIP_SUFFIX));
                    }
                });

                deleteTheseZipFiles(zipFiles);
                return zipFiles.length;
            }
            return 0;
        } catch (IOException e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error removing backup files from '" + snapshotLocation + "'", e);
        }
    }

    private void deleteTheseZipFiles(File[] zipFiles) throws IOException {
        for (File file : zipFiles) {
            file.delete();
            LOGGER.warn("Removed " + file.getCanonicalPath());
        }
    }

    public List<LMSProperty> getLMSPropertiesForClient(String clientCode) {
        List<LMSProperty> properties = new ArrayList<>();
        List<String> propertyCodes = globalCrudService.findByNativeQuery("select distinct(Property_Code) from Learning_Client_Property_Mapping where Client_Code = '" + clientCode + "'");
        for (String propertyCode : propertyCodes) {
            //filter invalid property codes here
            properties.add(LMSProperty.valueOf(propertyCode.toUpperCase()));
        }
        return properties;
    }

    public List<String> getClients() {
        return globalCrudService.findByNativeQuery("select distinct Client_Code from Learning_Client_Property_Mapping");
    }

    public List<String> getLmsProperties() {
        return globalCrudService.findByNativeQuery("select distinct Property_Code from Learning_Client_Property_Mapping");
    }

    public Map<Integer, String> getAllSalesUsers() {
        final HashMap<Integer, String> salesUsersMap = new HashMap<>();
        globalCrudService.findByNativeQuery("select Sales_Users_ID, First_Name, Last_Name, Email_Address from Sales_Users where Sales_Users_ID in ( " +
                "                select min(Sales_Users_ID) as Sales_Users_ID from Sales_Users where Active = 1 group by Email_Address) order by Sales_Users_ID ", null, row -> {
            salesUsersMap.put(valueOf(row[0].toString()), row[1].toString() + ',' + row[2].toString() + ',' + row[3].toString());
            return row;
        });
        return salesUsersMap;
    }

    public boolean isSalesUserActive(Integer userId) {
        try {
            //sleep until previous transactions commit
            Thread.sleep(100);
        } catch (InterruptedException e) {
            LOGGER.error("Exception occurred during thread sleep.", e);
        }
        final List<Object> result = globalCrudService.findByNativeQuery("select Active from Sales_Users where Sales_Users_ID = " + userId);
        if (CollectionUtils.isEmpty(result)) {
            return false;
        }
        return (Integer) result.get(0) == 1;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void adjustSQLDatesToYear(int propertyId, String clientCode) {
        List<Integer> propertyIDs = new ArrayList<>();
        propertyIDs.add(propertyId);
        LocalDate caughtUpDate = new LocalDate(dateService.getCaughtUpDate(3, "SnapShot_DT", "SnapShot_TM", propertyIDs));
        int caughtupYearToBeChanged = caughtUpDate.getYear();
        int caughtupYearToBeChangedTo = fetchCaughtupYear(clientCode, propertyId);
        LOGGER.info("SQL : old year : " + caughtupYearToBeChanged + "   new year : " + caughtupYearToBeChangedTo);
        if (caughtupYearToBeChanged == caughtupYearToBeChangedTo) {
            return;
        }
        LocalDate newCaughtUpDate = caughtUpDate.plusYears(caughtupYearToBeChangedTo - caughtupYearToBeChanged);
        LocalDate dayOfWeekAdjustedDate = caughtUpDate.withWeekOfWeekyear(caughtUpDate.getWeekOfWeekyear()).withDayOfWeek(caughtUpDate.getDayOfWeek()).withWeekyear(caughtupYearToBeChangedTo);
        int numberOfDaysToIncrement = Days.daysBetween(caughtUpDate, dayOfWeekAdjustedDate).getDays();
        int numberOfDaysToIncrementForSpecialEvents = Days.daysBetween(caughtUpDate, newCaughtUpDate).getDays() - numberOfDaysToIncrement;
        // Adjust the Property's SQL Server database dates
        adjustDatabaseDates(propertyId, numberOfDaysToIncrement);
        adjustSpecialEventsDatesInPacman(propertyId, numberOfDaysToIncrementForSpecialEvents);
        String dbName = StringUtils.leftPad(Integer.toString(propertyId), 6, "0");
        DBLoc dbLoc = getDbLocFrom(dbName);
        dbMaintainService.shrinkPropertyLog(null, dbLoc);

        if (com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.hasLmsSnapshotFeatureEnabled()) {
            removeSnapshotFor(propertyId, false, true);
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void adjustSASDatesToYear(@QueryParam("propertyId") int propertyId, @QueryParam("clientCode") String clientCode) {
        String propertyCode = LMSProperty.getPropertyCode(propertyId);
        SasDbQueryResult sasDbQueryResult = sasDbToolService.executeQuery(clientCode, propertyId, propertyCode, "select max(snapshot_dt) from tenant.file_metadata where isbde=1");
        int sasCaughtupDate = BigDecimalUtil.valueOf(sasDbQueryResult.getData().get(0).get(0), false, true).intValue();
        LocalDate caughtUpDate = new LocalDate("1960-01-01").plusDays(sasCaughtupDate);
        int caughtupYearToBeChanged = caughtUpDate.getYear();
        int caughtupYearToBeChangedTo = fetchCaughtupYear(clientCode, propertyId);
        LOGGER.info("SAS : old year : " + caughtupYearToBeChanged + "   new year : " + caughtupYearToBeChangedTo);
        if (caughtupYearToBeChanged == caughtupYearToBeChangedTo) {
            return;
        }
        LocalDate dayOfWeekAdjustedDate = caughtUpDate.withWeekOfWeekyear(caughtUpDate.getWeekOfWeekyear()).withDayOfWeek(caughtUpDate.getDayOfWeek()).withWeekyear(caughtupYearToBeChangedTo);
        int numberOfDaysToIncrement = Days.daysBetween(caughtUpDate, dayOfWeekAdjustedDate).getDays();
        long numberOfMSToIncrement = dayOfWeekAdjustedDate.toDateMidnight().getMillis() - caughtUpDate.toDateMidnight().getMillis();
        long numberOfSecToIncrement = numberOfMSToIncrement / 1000;
        adjustSASDatasetDates(propertyId, numberOfDaysToIncrement, numberOfSecToIncrement);
        if (com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.hasLmsSnapshotFeatureEnabled()) {
            removeSnapshotFor(propertyId, true, false);
        }
    }

    private DBLoc getDbLocFrom(String dbName) {
        return globalCrudService.findByNamedQuerySingleResult(DBLoc.BY_DBNAME,
                QueryParameter.with(PARAM_DB_NAME, dbName).parameters());
    }

    protected int fetchCaughtupYear(String clientCode, int propertyId) {
        String propertyCode = LMSProperty.getLMSProperty(propertyId).name();
        StringBuilder fetchStatement = new StringBuilder();
        fetchStatement.append("select Caughtup_Year from dbo.Learning_Client_Property_Mapping where Client_Code = '")
                .append(clientCode)
                .append("' and Property_Code = '")
                .append(propertyCode)
                .append("'");
        return globalCrudService.findByNativeQuerySingleResult(fetchStatement.toString(), null);
    }

    protected void adjustSpecialEventsDatesInPacman(int propertyId, int numberOfDaysToIncrementForSpecialEvents) {
        StringBuilder updateStatement = new StringBuilder();
        updateStatement.append("update Property_Special_Event_Instance set Start_DTTM = DATEADD(day, ")
                .append(numberOfDaysToIncrementForSpecialEvents)
                .append(",Start_DTTM), End_DTTM = DATEADD(day, ")
                .append(numberOfDaysToIncrementForSpecialEvents)
                .append(",End_DTTM) where Event_Frequency_ID in ")
                .append("(select Frequency_ID from Frequency where Frequency_Pattern like '%OnSameStartDate%')");
        multiPropertyCrudService.executeNativeUpdateOnSingleProperty(propertyId, updateStatement.toString(), null);
        updateStatement = new StringBuilder();
        updateStatement.append("update Property_Special_Event set Start_DTTM = DATEADD(day, ")
                .append(numberOfDaysToIncrementForSpecialEvents)
                .append(",Start_DTTM), End_DTTM = DATEADD(day, ")
                .append(numberOfDaysToIncrementForSpecialEvents)
                .append(",End_DTTM) where Event_Frequency_ID in ")
                .append("(select Frequency_ID from Frequency where Frequency_Pattern like '%OnSameStartDate%')");
        multiPropertyCrudService.executeNativeUpdateOnSingleProperty(propertyId, updateStatement.toString(), null);
    }

    protected void adjustDatabaseDates(Integer propertyId, int numberOfDaysToIncrement) {
        LOGGER.info("Adjusting SQL Server Dates on Property: " + propertyId);

        // Get the column definitions that are date columns
        List<ColumnDefinition> tableAndColumnsWithDates = findColumnDefinitions(propertyId);

        // To limit the SQL to be executed, group the column definitions by schema/table
        Map<String, List<ColumnDefinition>> tablesAndColumnsBySchemaTable = tableAndColumnsWithDates.stream().collect(Collectors.groupingBy(o -> StringUtils.trimToEmpty(o.getSchemaName()) + "." + StringUtils.trimToEmpty(o.getTableName())));
        if (MapUtils.isNotEmpty(tablesAndColumnsBySchemaTable)) {

            // Create update statements for each table - some tables might have multiple columns
            tablesAndColumnsBySchemaTable.keySet().stream().forEach(table -> {
                LOGGER.info("Adjusting SQL Server Dates on Property: " + propertyId + ", Table: " + table);

                // Build the update statement
                StringBuilder updateStatement = new StringBuilder();
                updateStatement.append("UPDATE ");
                updateStatement.append(table);
                updateStatement.append(" SET ");

                // Get the columns for the table
                List<ColumnDefinition> tableColumns = tablesAndColumnsBySchemaTable.get(table);

                // For each table column add the set condition and be aware of when to add the ','
                for (int i = 0; i < tableColumns.size(); i++) {
                    if (i > 0) {
                        updateStatement.append(", ");
                    }

                    // Get the columnName
                    String column = StringUtils.trimToEmpty(tableColumns.get(i).getColumnName());
                    updateStatement.append(column);
                    updateStatement.append(" = DATEADD(day,");
                    updateStatement.append(numberOfDaysToIncrement);
                    updateStatement.append(",");
                    updateStatement.append(column);
                    updateStatement.append(")");
                }

                // Execute the update statement
                int updatedRows = multiPropertyCrudService.executeNativeUpdateOnSingleProperty(propertyId, updateStatement.toString(), null);
                LOGGER.info("Update statement for Property ID : " + updateStatement.toString());
                LOGGER.info("Updated number of rows : " + updatedRows + " for property : " + propertyId);
            });
        }
    }

    protected List<ColumnDefinition> findColumnDefinitions(Integer propertyId) {
        Integer contexPropertyId = PacmanWorkContextHelper.getPropertyId();
        LOGGER.info(String.format("propertyId : %s contexPropertyId : %s ", propertyId, contexPropertyId));
        return multiPropertyCrudService.findByNativeQueryForSingleProperty(propertyId, TABLE_COLUMN_DATE_UPDATES, null, new ColumnDefinition());
    }

    private void adjustSASDatasetDates(Integer propertyId, int numberOfDaysToIncrement, long numberOfMSToIncrement) {
        LOGGER.info("Adjusting SAS Dataset Dates on Property: " + propertyId);

        // Get the column definitions that are date columns
        List<ColumnDefinition> sasDatasetTableAndColumns = sasDbToolService.executeQueryNonClustered(propertyId, SAS_DATE_COLUMNS, ColumnDefinition.class);

        // To limit the SQL to be executed, group the column definitions by schema/table
        Map<String, List<ColumnDefinition>> tablesAndColumnsBySchemaTable = sasDatasetTableAndColumns.stream().sorted(Comparator.comparing(ColumnDefinition::getSchemaName))
                .collect(Collectors.groupingBy(o -> StringUtils.trimToEmpty(o.getSchemaName()) + "." + StringUtils.trimToEmpty(o.getTableName())));
        if (MapUtils.isNotEmpty(tablesAndColumnsBySchemaTable)) {


            // Create update statements for each table - some tables might have multiple columns
            tablesAndColumnsBySchemaTable.keySet().stream().forEach(table -> {
                try {
                    LOGGER.info("Adjusting SAS Dataset Dates on Property: " + propertyId + ", Table: " + table);

                    // Build the update statement
                    StringBuilder updateStatement = new StringBuilder();
                    updateStatement.append("UPDATE ");
                    updateStatement.append(table);
                    updateStatement.append(" SET ");

                    // Get the columns for the table
                    List<ColumnDefinition> tableColumns = tablesAndColumnsBySchemaTable.get(table);

                    // For each table column add the set condition and be aware of when to add the ','
                    for (int i = 0; i < tableColumns.size(); i++) {
                        if (i > 0) {
                            updateStatement.append(", ");
                        }

                        // Get the columnName
                        String column = StringUtils.trimToEmpty(tableColumns.get(i).getColumnName());
                        updateStatement.append(column);
                        updateStatement.append(" = ");
                        updateStatement.append(column);
                        updateStatement.append("+");

                        // If the column ends with DTTM, then the time in stored in MS
                        if (StringUtils.endsWithIgnoreCase(column, "_DTTM")) {
                            updateStatement.append(numberOfMSToIncrement);
                        } else {
                            updateStatement.append(numberOfDaysToIncrement);
                        }
                    }

                    // Execute the update statement
                    sasDbToolService.executeUpdateNonClustered(propertyId, updateStatement.toString());
                } catch (Exception e) {
                    LOGGER.error(String.format("ERROR occured while Adjusting SAS Data Set Dates on Property: %d, Table: %s", propertyId, table), e);
                    throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, String.format("ERROR occured while Adjusting SAS Data Set Dates on Property: %d, Table: %s", propertyId, table), e);
                }
            });
        }

        LOGGER.info("Completed Adjusting SAS Dataset Dates on Property: " + propertyId);
    }

    public List<String> getSnapshotFileNamesFor(String client, String property, String fileType) {
        String snapshotLocation = SystemConfig.getLmsSnapshotLocation();
        File dir = new File(snapshotLocation);
        File[] zipFiles = null;
        if (dir.exists()) {
            zipFiles = dir.listFiles(new FilenameFilter() {
                @Override
                public boolean accept(File dir, String name) {
                    return belongsToGivenSelection(client, property, fileType, name);
                }
            });
        }
        return getFileNames(zipFiles);
    }

    protected boolean belongsToGivenSelection(String client, String property, String fileType, String currentFileName) {
        boolean belongs = currentFileName.startsWith(getFileNameStartString(client));
        String propertyNumber = getPropertyNumber(property);

        switch (fileType) {
            case "Only SAS":
                belongs = belongs && (currentFileName.endsWith(propertyNumber + SAS_ZIP_SUFFIX));
                break;
            case "Only SQL":
                belongs = belongs && (currentFileName.endsWith(propertyNumber + SQL_ZIP_SUFFIX));
                break;
            default:
                belongs = belongs && (currentFileName.endsWith(propertyNumber + SAS_ZIP_SUFFIX) || currentFileName.endsWith(propertyNumber + SQL_ZIP_SUFFIX));
                break;
        }
        return belongs;
    }

    private String getPropertyNumber(String property) {
        for (LMSProperty lmsProperty : LMSProperty.values()) {
            if (equalsIgnoreCase(lmsProperty.name(), property)) {
                return lmsProperty.getEndsWith();
            }
        }
        return "";
    }

    protected String getFileNameStartString(String client) {
        return null == clientMap.get(client) ? "3" : clientMap.get(client);
    }

    private List<String> getFileNames(File[] zipFiles) {
        List<String> fileNames = new ArrayList<>();
        if (null == zipFiles) {
            return fileNames;
        }
        for (File zipFile : zipFiles) {
            fileNames.add(zipFile.getName());
        }
        return fileNames;
    }
}
