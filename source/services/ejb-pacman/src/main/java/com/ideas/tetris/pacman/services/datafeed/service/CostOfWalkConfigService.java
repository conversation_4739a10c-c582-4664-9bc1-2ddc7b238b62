package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.costofwalk.entity.CostofWalkDefault;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.CostOfWalkConfig;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.ideas.tetris.pacman.common.constants.Constants.PROPERTY_ID;

@Component
@Transactional
public class CostOfWalkConfigService {


    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;


    public List<CostOfWalkConfig> getCostOfWalkDetails(Integer propertyId, Date startDate) {
        Map RoomTypeIdCodeMap = getRoomAccumTypeCode(propertyId);
        List<CostOfWalkConfig> costOfWalkConfigList = new ArrayList<>();
        getCostOfWalkDefault(propertyId, costOfWalkConfigList, RoomTypeIdCodeMap);
        return costOfWalkConfigList;
    }

    private void getCostOfWalkDefault(Integer propertyId, List<CostOfWalkConfig> costOfWalkConfigList, Map<Integer, String> RoomTypeIdCodeMap) {
        List<CostofWalkDefault> costofWalkDefaultList = tenantCrudService.findByNamedQuery(CostofWalkDefault.BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());

        costofWalkDefaultList.forEach(costOfWalk -> {
            if (RoomTypeIdCodeMap.containsKey(costOfWalk.getAccomTypeId())) {
                CostOfWalkConfig costOfWalkConfig = new CostOfWalkConfig();
                costOfWalkConfig.setRoomTypeCode(RoomTypeIdCodeMap.get(costOfWalk.getAccomTypeId()));
                costOfWalkConfig.setCategory("Default");
                costOfWalkConfig.setMondayValue(costOfWalk.getMondayValue());
                costOfWalkConfig.setThursdayValue(costOfWalk.getThursdayValue());
                costOfWalkConfig.setWednesdayValue(costOfWalk.getWednesdayValue());
                costOfWalkConfig.setTuesdayValue(costOfWalk.getTuesdayValue());
                costOfWalkConfig.setFridayValue(costOfWalk.getFridayValue());
                costOfWalkConfig.setSaturdayValue(costOfWalk.getSaturdayValue());
                costOfWalkConfig.setSundayValue(costOfWalk.getSundayValue());
                costOfWalkConfigList.add(costOfWalkConfig);
            }
        });

    }

    private Map<Integer, String> getRoomAccumTypeCode(Integer propertyId) {
        List<AccomType> accomTypeList = tenantCrudService.findByNamedQuery(AccomType.BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        Map<Integer, String> accomIdCodeMap = new HashMap<>();
        accomTypeList.forEach(accomType -> {
            if (accomType.getStatusId() == 1) {
                accomIdCodeMap.put(accomType.getId(), accomType.getAccomTypeCode());
            }
        });
        return accomIdCodeMap;
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }
}
