package com.ideas.tetris.pacman.services.reports.dto;

import java.util.Map;
import java.util.TreeMap;

public class PropertyAttributeAssignmentDTO {

    private String propertyCode;
    private Map<String, AttributeDetails> attributeDetailsMap = new TreeMap<>();
    private String propertyName;

    public String getPropertyCode() {
        return propertyCode;
    }

    public void setPropertyCode(String propertyName) {
        this.propertyCode = propertyName;
    }

    public Map<String, AttributeDetails> getAttributeDetailsMap() {
        return attributeDetailsMap;
    }

    public void setAttributeDetailsMap(Map<String, AttributeDetails> attributeDetailsMap) {
        this.attributeDetailsMap = attributeDetailsMap;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public String getPropertyName() {
        return propertyName;
    }
}
