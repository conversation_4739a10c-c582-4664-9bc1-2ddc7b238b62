package com.ideas.tetris.pacman.services.reports.performancecomparison;

/**
 * Created by idnmal on 3/24/14.
 */
public enum PerformanceComparisonReportViewTypeEnum {


    TOTAL_PROPERTY(1, "common.total.hotel", "Performance_Comparison_Report_Total_Property"),
    TOTAL_TRANSIENT(2, "totaltransient", "Performance_Comparison_Report_Total_Transient"),
    TOTAL_GROUP(3, "totalgroup", "Performance_Comparison_Report_Total_Groups"),
    ROOM_CLASSES(4, "room.class", "Performance_Comparison_Report_Room_Classes"),
    FORECAST_GROUPS(5, "forecastgroups", "Performance_Comparison_Report_Forecast_Groups"),
    BUSINESS_VIEW(7, "businessview", "Performance_Comparison_Report_Business_View"),
    MARKET_SEGMENTS(6, "common.ms", "Performance_Comparison_Report_Market_Sengments");

    private String caption;
    private String type;
    private Integer id;

    PerformanceComparisonReportViewTypeEnum(Integer id, String caption, String type) {
        this.id = id;
        this.caption = caption;
        this.type = type;
    }

    public String getCaption() {
        return caption;
    }

    public Integer getId() {
        return id;
    }

    public String getType() {
        return type;
    }
}
