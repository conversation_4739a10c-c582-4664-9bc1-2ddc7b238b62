package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPOffsetConfigDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPOffsetConfigPeriodDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.grouppricing.GroupPricingRoomTypeOffsetConfiguration;
import com.ideas.tetris.pacman.services.perpersonpricing.OccupantBucketEntity;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationOffsetService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.security.User;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.EnumSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class GroupPricingRoomTypeOffsetConfigService {

    @Autowired
    PricingConfigurationOffsetService offsetService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
	private PricingConfigurationService pricingConfigurationService;

    @Autowired
    PropertyService propertyService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;


    public List<GroupPricingRoomTypeOffsetConfiguration> getGroupPricingRoomTypeOffsetConfiguration(final Date startDate) {
        if (!pricingConfigurationService.isBaseRoomTypeConfigurationComplete()) {
            return Collections.emptyList();
        }
        final CPOffsetConfigDTO cpOffsetConfigDTO = offsetService.retrieveOffsetConfigDTOForGroupPricing(PacmanWorkContextHelper.getPropertyId());
        return getPricingRoomTypeOffsetConfigurationsForDatafeed(startDate, cpOffsetConfigDTO);
    }

    public List<GroupPricingRoomTypeOffsetConfiguration> getCPPricingRoomTypeOffsetConfiguration(final Date startDate) {
        if (!pricingConfigurationService.isBaseRoomTypeConfigurationComplete()) {
            return Collections.emptyList();
        }
        final CPOffsetConfigDTO cpOffsetConfigDTO = offsetService.retrieveOffsetConfigDTO(PacmanWorkContextHelper.getPropertyId());
        return getPricingRoomTypeOffsetConfigurationsForDatafeed(startDate, cpOffsetConfigDTO);
    }

    private List<GroupPricingRoomTypeOffsetConfiguration> getPricingRoomTypeOffsetConfigurationsForDatafeed(Date startDate, CPOffsetConfigDTO cpOffsetConfigDTO) {
        final CPOffsetConfigPeriodDTO cpOffsetConfigPeriodDTO = cpOffsetConfigDTO.getDefaultOffsets();
        final List<GroupPricingRoomTypeOffsetConfiguration> groupPricingRoomTypeOffsetConfigurations = new ArrayList<>();
        groupPricingRoomTypeOffsetConfigurations.addAll(populateDefaultOffsetConfigurations(cpOffsetConfigDTO.getBaseOccupancyType(), cpOffsetConfigPeriodDTO.getOffsets()));
        groupPricingRoomTypeOffsetConfigurations.addAll(populateSeasonalOffsetConfigurations(cpOffsetConfigDTO.getBaseOccupancyType(), cpOffsetConfigDTO.getSeasonOffsets(), startDate));
        return groupPricingRoomTypeOffsetConfigurations;
    }

    private List<GroupPricingRoomTypeOffsetConfiguration> populateDefaultOffsetConfigurations(OccupancyType baseOccupancyType, LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> defaultOffsets) {
        return populateOffsetConfigurations(baseOccupancyType, defaultOffsets, true, Constants.DEFAULT);
    }

    private List<GroupPricingRoomTypeOffsetConfiguration> populateSeasonalOffsetConfigurations(OccupancyType baseOccupancyType, final Map<LocalDate, CPOffsetConfigPeriodDTO> seasonOffsets, final Date startDate) {
        List<GroupPricingRoomTypeOffsetConfiguration> seasonalOffsetConfigurations = new ArrayList<>();

        for (Map.Entry<LocalDate, CPOffsetConfigPeriodDTO> entry : seasonOffsets.entrySet()) {
            CPOffsetConfigPeriodDTO periodDTO = entry.getValue();
            LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets = periodDTO.getOffsets();
            final List<GroupPricingRoomTypeOffsetConfiguration> groupPricingRoomTypeOffsetConfigurations = populateOffsetConfigurations(baseOccupancyType, offsets, false, Constants.SEASONAL);
            addSeasonDates(periodDTO, groupPricingRoomTypeOffsetConfigurations);
            seasonalOffsetConfigurations.addAll(groupPricingRoomTypeOffsetConfigurations);
        }

        return seasonalOffsetConfigurations.stream().filter(
                roomTypeOffsetConfiguration -> LocalDate.fromDateFields(roomTypeOffsetConfiguration.getSeasonalEndDate()).isAfter(LocalDate.fromDateFields(startDate))
                        || LocalDate.fromDateFields(roomTypeOffsetConfiguration.getSeasonalEndDate()).isEqual(LocalDate.fromDateFields(startDate))).collect(Collectors.toList());
    }

    private void addSeasonDates(final CPOffsetConfigPeriodDTO periodDTO, final List<GroupPricingRoomTypeOffsetConfiguration> groupPricingRoomTypeOffsetConfigurations) {
        groupPricingRoomTypeOffsetConfigurations.stream().forEach(roomTypeOffsetConfiguration -> {
            roomTypeOffsetConfiguration.setSeasonalStartDate(periodDTO.getStartDate().toDate());
            roomTypeOffsetConfiguration.setSeasonalEndDate(periodDTO.getEndDate().toDate());
        });
    }

    private List<GroupPricingRoomTypeOffsetConfiguration> populateOffsetConfigurations(OccupancyType baseOccupancyType, LinkedHashMap<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> offsets,
                                                                                       Boolean defaultValuesToZero, String category) {
        List<GroupPricingRoomTypeOffsetConfiguration> offsetConfigurations = new ArrayList<>();
        final boolean isCPEnabled = pacmanConfigParamsService.getParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
        final boolean isPPPEnabled = pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED);
        final boolean isCPChildAgeBucketEnabled = pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.CP_CHILD_AGE_BUCKETS_ENABLED);
        Map<Integer, String> productsMap = fetchProductList();
        for (Map.Entry<PricingAccomClass, LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>>> accomClassLinkedHashMapEntry : offsets.entrySet()) {
            PricingAccomClass pricingAccomClass = accomClassLinkedHashMapEntry.getKey();
            LinkedHashMap<AccomType, LinkedList<CPConfigOffsetAccomType>> roomTypeMap = accomClassLinkedHashMapEntry.getValue();
            for (Map.Entry<AccomType, LinkedList<CPConfigOffsetAccomType>> accomTypeLinkedListEntry : roomTypeMap.entrySet()) {
                AccomType accomType = accomTypeLinkedListEntry.getKey();
                LinkedList<CPConfigOffsetAccomType> offsetTypes = accomTypeLinkedListEntry.getValue();
                populateOffsetsByOccupancyType(baseOccupancyType, defaultValuesToZero, category, offsetConfigurations, pricingAccomClass, accomType, offsetTypes, OccupancyType.SINGLE, isCPEnabled, isPPPEnabled, isCPChildAgeBucketEnabled, productsMap);
                //if Continuous Pricing is enabled then fetch all data by Occupancy Type
                if (isCPEnabled) {
                    fetchOccupancyTypesOnTheBasisOfCPAndPPPricingToggles(baseOccupancyType, defaultValuesToZero, category, offsetConfigurations, pricingAccomClass, accomType, offsetTypes, isPPPEnabled, isCPChildAgeBucketEnabled, productsMap);
                }
            }
        }

        return offsetConfigurations;
    }

    private void fetchOccupancyTypesOnTheBasisOfCPAndPPPricingToggles(OccupancyType baseOccupancyType, Boolean defaultValuesToZero, String category, List<GroupPricingRoomTypeOffsetConfiguration> offsetConfigurations, PricingAccomClass pricingAccomClass, AccomType accomType, LinkedList<CPConfigOffsetAccomType> offsetTypes, boolean isPPPEnabled, boolean isCPChildAgeBucketEnabled, Map<Integer, String> productsMap) {
        if (isPPPEnabled) {
            Set<OccupancyType> occupancyTypes = EnumSet.allOf(OccupancyType.class);
            occupancyTypes.remove(OccupancyType.SINGLE);
            for (OccupancyType occupancyType : occupancyTypes) {
                populateOffsetsByOccupancyType(baseOccupancyType, defaultValuesToZero, category, offsetConfigurations, pricingAccomClass, accomType, offsetTypes, occupancyType, true, true, isCPChildAgeBucketEnabled, productsMap);
            }
        } else {
            populateOffsetsByOccupancyType(baseOccupancyType, defaultValuesToZero, category, offsetConfigurations, pricingAccomClass, accomType, offsetTypes, OccupancyType.DOUBLE, true, false, isCPChildAgeBucketEnabled, productsMap);
            populateOffsetsByOccupancyType(baseOccupancyType, defaultValuesToZero, category, offsetConfigurations, pricingAccomClass, accomType, offsetTypes, OccupancyType.EXTRA_ADULT, true, false, isCPChildAgeBucketEnabled, productsMap);
            populateOffsetsByOccupancyType(baseOccupancyType, defaultValuesToZero, category, offsetConfigurations, pricingAccomClass, accomType, offsetTypes, OccupancyType.EXTRA_CHILD, true, false, isCPChildAgeBucketEnabled, productsMap);

            if (isCPChildAgeBucketEnabled) {
                populateOffsetsByOccupancyType(baseOccupancyType, defaultValuesToZero, category, offsetConfigurations, pricingAccomClass, accomType, offsetTypes, OccupancyType.CHILD_BUCKET_1, true, false, true, productsMap);
                populateOffsetsByOccupancyType(baseOccupancyType, defaultValuesToZero, category, offsetConfigurations, pricingAccomClass, accomType, offsetTypes, OccupancyType.CHILD_BUCKET_2, true, false, true, productsMap);
                populateOffsetsByOccupancyType(baseOccupancyType, defaultValuesToZero, category, offsetConfigurations, pricingAccomClass, accomType, offsetTypes, OccupancyType.CHILD_BUCKET_3, true, false, true, productsMap);
            }
        }
    }

    private void populateOffsetsByOccupancyType(OccupancyType baseOccupancyType, Boolean defaultValuesToZero, String category, List<GroupPricingRoomTypeOffsetConfiguration> offsetConfigurations, PricingAccomClass pricingAccomClass, AccomType accomType, LinkedList<CPConfigOffsetAccomType> offsetTypes, OccupancyType occupancyType, boolean isCPEnabled, boolean isPPPEnabled, boolean isCPChildAgeBucketEnabled, Map<Integer, String> productsMap) {
        final List<CPConfigOffsetAccomType> correspondingOffsetEntities = getCorrespondingOffsetEntities(pricingAccomClass, accomType, offsetTypes, defaultValuesToZero, occupancyType, isCPEnabled);
        if (correspondingOffsetEntities.isEmpty()) {
            return;
        }
        for (final CPConfigOffsetAccomType correspondingOffsetEntity : correspondingOffsetEntities) {
            GroupPricingRoomTypeOffsetConfiguration offsetConfiguration = createOffSetConfiguration(baseOccupancyType, pricingAccomClass, correspondingOffsetEntity, category, isPPPEnabled, isCPChildAgeBucketEnabled, productsMap);
            if (offsetConfiguration != null) {
                offsetConfigurations.add(offsetConfiguration);
            }
        }
    }

    public String getLastUpdatedDate(LocalDateTime localDateTime) {
        // convert LocalDateTime to ZonedDateTime, with default system zone id
        ZonedDateTime localDateTimeInDefaultZone = localDateTime.atZone(ZoneId.systemDefault());

        // convert LocalDateTime to ZonedDateTime, with specified zoneId
        ZonedDateTime lastUpdatedInPropertyTimeZone = localDateTimeInDefaultZone.withZoneSameInstant(ZoneId.of(propertyService.getPropertyTimeZone().getID()));
        return DateTimeFormatter.ofPattern("dd-MMM-yyyy HH:mm:ss z").format(lastUpdatedInPropertyTimeZone);
    }

    public String getUserEmail(Integer userId) {
        return getUserById(userId)
                .map(User::getEmail)
                .orElse("-");
    }

    public String getUserName(Integer userId) {
        return getUserById(userId)
                .map(User::getName)
                .orElse("-");
    }

    private Optional<User> getUserById(Integer userId) {
        return Optional.ofNullable(userId)
                .map(id -> tenantCrudService.find(User.class, id));
    }

    @ForTesting
	public
    GroupPricingRoomTypeOffsetConfiguration createOffSetConfiguration(OccupancyType baseOccupancyType, final PricingAccomClass pricingAccomClass, CPConfigOffsetAccomType cpConfigOffsetAccomType, final String category, boolean isPPPEnabled, boolean isCPChildAgeBucketEnabled, Map<Integer, String> productsMap) {
        GroupPricingRoomTypeOffsetConfiguration groupPricingRoomTypeOffsetConfiguration = new GroupPricingRoomTypeOffsetConfiguration();
        groupPricingRoomTypeOffsetConfiguration.setRoomTypeCode(cpConfigOffsetAccomType.getAccomType().getAccomTypeCode());

        final boolean isChildBucketOccupancyType = List.of(OccupancyType.CHILD_BUCKET_1.getId(), OccupancyType.CHILD_BUCKET_2.getId(), OccupancyType.CHILD_BUCKET_3.getId()).contains(cpConfigOffsetAccomType.getOccupancyType().getId());

        if (isChildBucketOccupancyType) {
            handleChildAgeBucket(cpConfigOffsetAccomType, isPPPEnabled, groupPricingRoomTypeOffsetConfiguration, isCPChildAgeBucketEnabled);
            if (StringUtils.isEmpty(groupPricingRoomTypeOffsetConfiguration.getOccupancyType())) {
                return null;
            }
        } else if (isPPPEnabled) {
            groupPricingRoomTypeOffsetConfiguration.setOccupancyType((ResourceUtil.getText(getKey(cpConfigOffsetAccomType), Locale.ENGLISH)).toUpperCase());
        } else {
            groupPricingRoomTypeOffsetConfiguration.setOccupancyType((ResourceUtil.getText(cpConfigOffsetAccomType.getOccupancyType().getKey(), Locale.ENGLISH)).toUpperCase());
        }

        if (pricingAccomClass.isPriceExcluded() || !(pricingAccomClass.getAccomType().equals(cpConfigOffsetAccomType.getAccomType()) && baseOccupancyType.equals(cpConfigOffsetAccomType.getOccupancyType()))) {
            groupPricingRoomTypeOffsetConfiguration.setOffsetMethod(getLocalizedOffsetMethod(cpConfigOffsetAccomType.getOffsetMethod()));
        }
        groupPricingRoomTypeOffsetConfiguration.setCategory(category);
        if (pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)) {
            setWeekdaysPricesWithTax(groupPricingRoomTypeOffsetConfiguration, cpConfigOffsetAccomType);
        } else {
            setWeekdaysPricesWithoutTax(groupPricingRoomTypeOffsetConfiguration, cpConfigOffsetAccomType);
        }

        if (null != cpConfigOffsetAccomType.getLastUpdatedByUserId()) {
            groupPricingRoomTypeOffsetConfiguration.setUpdatedBy(getUpdatedBy(cpConfigOffsetAccomType));
        }

        groupPricingRoomTypeOffsetConfiguration.setUpdatedOn(getLastUpdatedDateFor(cpConfigOffsetAccomType));

        if (null != cpConfigOffsetAccomType.getProductID()) {
            groupPricingRoomTypeOffsetConfiguration.setProductName(productsMap.get(cpConfigOffsetAccomType.getProductID()));
        }

        return groupPricingRoomTypeOffsetConfiguration;
    }

    private void handleChildAgeBucket(CPConfigOffsetAccomType cpConfigOffsetAccomType, boolean isPPPEnabled, GroupPricingRoomTypeOffsetConfiguration groupPricingRoomTypeOffsetConfiguration, boolean isCPChildAgeBucketEnabled) {
        if (isPPPEnabled || isCPChildAgeBucketEnabled) {
            OccupantBucketEntity obe = tenantCrudService.findByNamedQuerySingleResult(
                    OccupantBucketEntity.GET_ENTITY_BY_OCCUPANCY_TYPE,
                    QueryParameter.with("occupancyType", cpConfigOffsetAccomType.getOccupancyType()).parameters());

            groupPricingRoomTypeOffsetConfiguration.setOccupancyType(
                    (ResourceUtil.getText(cpConfigOffsetAccomType.getOccupancyType().getKey(), Locale.ENGLISH).toUpperCase())
                            + " " + obe.makeChildBucketString().toUpperCase());
        }
    }

    private Map<Integer, String> fetchProductList() {
        List<Product> productsList = tenantCrudService.findByNamedQuery(Product.GET_ALL);
        return productsList.stream().collect(Collectors.toMap(Product::getId, Product::getName));
    }

    private String getUpdatedBy(CPConfigOffsetAccomType cpConfigOffsetAccomType) {
        if (useUniqueUserIDInsteadOfEmailEnabled()) {
            return getUserName(cpConfigOffsetAccomType.getLastUpdatedByUserId());
        } else {
            return getUserEmail(cpConfigOffsetAccomType.getLastUpdatedByUserId());
        }
    }

    private String getKey(CPConfigOffsetAccomType cpConfigOffsetAccomType) {

        switch (cpConfigOffsetAccomType.getOccupancyType()) {
            case SINGLE:
                return "pricingConfiguration.oneAdult";
            case DOUBLE:
                return "pricingConfiguration.twoAdults";
            default:
                return cpConfigOffsetAccomType.getOccupancyType().getKey();
        }
    }

    private boolean useUniqueUserIDInsteadOfEmailEnabled() {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.USE_UNIQUE_USERID_INSTEADOF_EMAILID.getParameterName()));
    }

    public String getLastUpdatedDateFor(CPConfigOffsetAccomType cpConfigOffsetAccomType) {
        return Optional.ofNullable(cpConfigOffsetAccomType.getLastUpdatedDate()).map(this::getLastUpdatedDateInPropertyZone).orElse(StringUtils.EMPTY);
    }

    public String getLastUpdatedDateInPropertyZone(LocalDateTime localDateTime) {
        ZonedDateTime lastUpdatedInPropertyTimeZone = JavaLocalDateUtils.convertToZone(localDateTime, ZoneId.of(propertyService.getPropertyTimeZone().getID()));
        return DateTimeFormatter.ofPattern("dd-MMM-yyyy HH:mm:ss z").format(lastUpdatedInPropertyTimeZone);
    }

    private void setWeekdaysPricesWithTax(GroupPricingRoomTypeOffsetConfiguration groupPricingRoomTypeOffsetConfiguration, CPConfigOffsetAccomType cpConfigOffsetAccomType) {
        groupPricingRoomTypeOffsetConfiguration.setSunday(cpConfigOffsetAccomType.getSundayOffsetValueWithTax() == null ? cpConfigOffsetAccomType.getSundayOffsetValueWithTax() : cpConfigOffsetAccomType.getSundayOffsetValueWithTax().setScale(2));
        groupPricingRoomTypeOffsetConfiguration.setMonday(cpConfigOffsetAccomType.getMondayOffsetValueWithTax() == null ? cpConfigOffsetAccomType.getMondayOffsetValueWithTax() : cpConfigOffsetAccomType.getMondayOffsetValueWithTax().setScale(2));
        groupPricingRoomTypeOffsetConfiguration.setTuesday(cpConfigOffsetAccomType.getTuesdayOffsetValueWithTax() == null ? cpConfigOffsetAccomType.getTuesdayOffsetValueWithTax() : cpConfigOffsetAccomType.getTuesdayOffsetValueWithTax().setScale(2));
        groupPricingRoomTypeOffsetConfiguration.setWednesday(cpConfigOffsetAccomType.getWednesdayOffsetValueWithTax() == null ? cpConfigOffsetAccomType.getWednesdayOffsetValueWithTax() : cpConfigOffsetAccomType.getWednesdayOffsetValueWithTax().setScale(2));
        groupPricingRoomTypeOffsetConfiguration.setThursday(cpConfigOffsetAccomType.getThursdayOffsetValueWithTax() == null ? cpConfigOffsetAccomType.getThursdayOffsetValueWithTax() : cpConfigOffsetAccomType.getThursdayOffsetValueWithTax().setScale(2));
        groupPricingRoomTypeOffsetConfiguration.setFriday(cpConfigOffsetAccomType.getFridayOffsetValueWithTax() == null ? cpConfigOffsetAccomType.getFridayOffsetValueWithTax() : cpConfigOffsetAccomType.getFridayOffsetValueWithTax().setScale(2));
        groupPricingRoomTypeOffsetConfiguration.setSaturday(cpConfigOffsetAccomType.getSaturdayOffsetValueWithTax() == null ? cpConfigOffsetAccomType.getSaturdayOffsetValueWithTax() : cpConfigOffsetAccomType.getSaturdayOffsetValueWithTax().setScale(2));
    }

    private void setWeekdaysPricesWithoutTax(GroupPricingRoomTypeOffsetConfiguration groupPricingRoomTypeOffsetConfiguration, CPConfigOffsetAccomType cpConfigOffsetAccomType) {
        groupPricingRoomTypeOffsetConfiguration.setSunday(cpConfigOffsetAccomType.getSundayOffsetValue() == null ? cpConfigOffsetAccomType.getSundayOffsetValue() : cpConfigOffsetAccomType.getSundayOffsetValue().setScale(2));
        groupPricingRoomTypeOffsetConfiguration.setMonday(cpConfigOffsetAccomType.getMondayOffsetValue() == null ? cpConfigOffsetAccomType.getMondayOffsetValue() : cpConfigOffsetAccomType.getMondayOffsetValue().setScale(2));
        groupPricingRoomTypeOffsetConfiguration.setTuesday(cpConfigOffsetAccomType.getTuesdayOffsetValue() == null ? cpConfigOffsetAccomType.getTuesdayOffsetValue() : cpConfigOffsetAccomType.getTuesdayOffsetValue().setScale(2));
        groupPricingRoomTypeOffsetConfiguration.setWednesday(cpConfigOffsetAccomType.getWednesdayOffsetValue() == null ? cpConfigOffsetAccomType.getWednesdayOffsetValue() : cpConfigOffsetAccomType.getWednesdayOffsetValue().setScale(2));
        groupPricingRoomTypeOffsetConfiguration.setThursday(cpConfigOffsetAccomType.getThursdayOffsetValue() == null ? cpConfigOffsetAccomType.getThursdayOffsetValue() : cpConfigOffsetAccomType.getThursdayOffsetValue().setScale(2));
        groupPricingRoomTypeOffsetConfiguration.setFriday(cpConfigOffsetAccomType.getFridayOffsetValue() == null ? cpConfigOffsetAccomType.getFridayOffsetValue() : cpConfigOffsetAccomType.getFridayOffsetValue().setScale(2));
        groupPricingRoomTypeOffsetConfiguration.setSaturday(cpConfigOffsetAccomType.getSaturdayOffsetValue() == null ? cpConfigOffsetAccomType.getSaturdayOffsetValue() : cpConfigOffsetAccomType.getSaturdayOffsetValue().setScale(2));
    }

    private List<CPConfigOffsetAccomType> getCorrespondingOffsetEntities(PricingAccomClass pricingAccomClass, AccomType accomType, LinkedList<CPConfigOffsetAccomType> offsetTypes, Boolean defaultValuesToZero, OccupancyType occupancyType, boolean isCPEnabled) {
        if (offsetTypes != null) {
            final List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes = offsetTypes.stream().filter(offsetType -> occupancyType.equals(offsetType.getOccupancyType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cpConfigOffsetAccomTypes)) {
                return cpConfigOffsetAccomTypes;
            }
        }

        final CPConfigOffsetAccomType cpConfigOffsetAccomType = handleEmptyOffsetConfigs(pricingAccomClass, accomType, defaultValuesToZero, isCPEnabled);

        return cpConfigOffsetAccomType == null ? Collections.emptyList() : Collections.singletonList(cpConfigOffsetAccomType);
    }

    private CPConfigOffsetAccomType handleEmptyOffsetConfigs(PricingAccomClass pricingAccomClass, AccomType accomType, Boolean defaultValuesToZero, boolean isCPEnabled) {
        if (isCPEnabled) {
            // if its CP then we will get only records in DB and not extrapolate like seen on UI. extrapolate is only applicable in case of Group Pricing. hence returning null.
            return null;
        }
        //no existing CPConfigOffsetAccomType exists in the database so create a new one with some defaults set
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();
        cpConfigOffsetAccomType.setOccupancyType(OccupancyType.SINGLE);
        cpConfigOffsetAccomType.setAccomType(accomType);
        cpConfigOffsetAccomType.setPropertyId(accomType.getPropertyId());
        //Hard coded to BAR; Independent Products will not be supported at this time
        cpConfigOffsetAccomType.setProductID(1);

        //check to make sure we don't default to zero for base room type and a occupancy type of single
        if (accomType.equals(pricingAccomClass.getAccomType())) {
            defaultValuesToZero = false;
        }

        //default to zero
        if (defaultValuesToZero) {
            cpConfigOffsetAccomType.setSundayOffsetValue(BigDecimal.ZERO);
            cpConfigOffsetAccomType.setMondayOffsetValue(BigDecimal.ZERO);
            cpConfigOffsetAccomType.setTuesdayOffsetValue(BigDecimal.ZERO);
            cpConfigOffsetAccomType.setWednesdayOffsetValue(BigDecimal.ZERO);
            cpConfigOffsetAccomType.setThursdayOffsetValue(BigDecimal.ZERO);
            cpConfigOffsetAccomType.setFridayOffsetValue(BigDecimal.ZERO);
            cpConfigOffsetAccomType.setSaturdayOffsetValue(BigDecimal.ZERO);
        }

        //if the price for the accom class is excluded, the offset method should be defaulted to fixed price/set
        if (pricingAccomClass.isPriceExcluded()) {
            cpConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_PRICE);
            //make sure we don't default to zero as we want the user to fill in prices for a room class that has been price excluded
            cpConfigOffsetAccomType.setSundayOffsetValue(null);
            cpConfigOffsetAccomType.setMondayOffsetValue(null);
            cpConfigOffsetAccomType.setTuesdayOffsetValue(null);
            cpConfigOffsetAccomType.setWednesdayOffsetValue(null);
            cpConfigOffsetAccomType.setThursdayOffsetValue(null);
            cpConfigOffsetAccomType.setFridayOffsetValue(null);
            cpConfigOffsetAccomType.setSaturdayOffsetValue(null);
        } else {
            cpConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        }

        return cpConfigOffsetAccomType;
    }

    private String getLocalizedOffsetMethod(final OffsetMethod offsetMethod) {
        if (OffsetMethod.PERCENTAGE.equals(offsetMethod)) {
            return ResourceUtil.getText("percent", Locale.ENGLISH);
        } else if (OffsetMethod.FIXED_OFFSET.equals(offsetMethod)) {
            return ResourceUtil.getText("pricingConfiguration.fixed", Locale.ENGLISH);
        } else if (OffsetMethod.FIXED_PRICE.equals(offsetMethod)) {
            return ResourceUtil.getText("set", Locale.ENGLISH);
        }
        return offsetMethod.toString();
    }
}