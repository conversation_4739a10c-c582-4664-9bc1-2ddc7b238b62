package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.tetris.pacman.Repository;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.PaceAccomActivityDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.AccomAvailableCapacityDto;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Collections.emptyList;
import static org.apache.commons.collections.CollectionUtils.isEmpty;

@Repository
@Component
public class AccomActivityRepository {
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    public List<AccomActivity> findByDateRange(LocalDate startDate, LocalDate endDate) {
        Date startUtilDate = LocalDateUtils.toDate(startDate);
        Date endUtilDate = LocalDateUtils.toDate(endDate);
        return tenantCrudService.findByNamedQuery(AccomActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                Map.of("propertyId", PacmanWorkContextHelper.getPropertyId(), "startDate", startUtilDate, "endDate", endUtilDate));
    }

    public List<AccomActivity> findByDateRange(Date startDate, Date endDate) {
        return tenantCrudService.findByNamedQuery(AccomActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                Map.of("propertyId", PacmanWorkContextHelper.getPropertyId(), "startDate", startDate, "endDate", endDate));
    }

    public List<AccomActivity> findByDateRangeAndAccomType(LocalDate startDate, LocalDate endDate, AccomType accomType) {
        return findByDateRangeAndAccomTypeId(startDate, endDate, accomType.getId());
    }

    public List<AccomActivity> findByDateRangeAndAccomTypeId(LocalDate startDate, LocalDate endDate, Integer accomTypeId) {
        return tenantCrudService.findByNamedQuery(AccomActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID_AND_ACCOM_TYPE_ID,
                Map.of(
                        "propertyId", PacmanWorkContextHelper.getPropertyId(),
                        "startDate", LocalDateUtils.toDate(startDate),
                        "endDate", LocalDateUtils.toDate(endDate),
                        "accomTypeId", accomTypeId
                ));
    }

    public LocalDate getMaxOccupancyDate() {
        return Optional.ofNullable(getMaximumOccupancyDate()).map(LocalDateUtils::toJavaLocalDate).orElse(null);
    }

    public Date getMaximumOccupancyDate() {
        return tenantCrudService.findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE);
    }

    public List<OccupancyDateTotalCapacity> getRemainingCapacityForAccomClassList(List<Integer> accomClassIds,
                                                                                  Date startDate,
                                                                                  Date endDate) {
        if (isEmpty(accomClassIds)) {
            return emptyList();
        }

        List<Object[]> occupancyDateCapacityObjects = tenantCrudService.findByNamedQuery(AccomActivity.TOTAL_AVAILABLE_CAPACITY_BY_OCCUPANCY_DT_AND_ACCOM_CLASS_LIST,
                QueryParameter.with("accomClassIds", accomClassIds)
                        .and("endDate", endDate)
                        .and("startDate", startDate)
                        .parameters());

        return Optional.ofNullable(occupancyDateCapacityObjects)
                .orElse(emptyList())
                .stream()
                .map(occupancySummaryRowMapper())
                .collect(Collectors.toList());
    }

    public List<AccomAvailableCapacityDto> getAccomActivityByOccupancyDate(LocalDate startDate, LocalDate endDate, Integer page, Integer size) {
            List<Object[]> result = tenantCrudService.findByNamedQuery(AccomActivity.TOTAL_AVAILABLE_CAPACITY_BY_OCCUPANCY_DATE,
                QueryParameter.with("startDate", JavaLocalDateUtils.toDate(startDate))
                        .and("endDate", JavaLocalDateUtils.toDate(endDate))
                        .parameters(),
                page, size);
        return result.stream()
                .map(obj -> new AccomAvailableCapacityDto(
                        JavaLocalDateUtils.fromDate((Date) obj[0]),
                        (Integer) obj[1],
                        (BigDecimal) obj[2]
                ))
                .collect(Collectors.toList());
    }

    private static Function<Object[], OccupancyDateTotalCapacity> occupancySummaryRowMapper() {
        return row -> new OccupancyDateTotalCapacity((Date) row[0], ((BigDecimal) row[1]).intValue());
    }

    @Getter
    public static class OccupancyDateTotalCapacity {
        private final Date occupancyDate;
        private final Integer availableCapacity;

        public OccupancyDateTotalCapacity(Date occupancyDate, Integer availableCapacity) {
            this.occupancyDate = occupancyDate;
            this.availableCapacity = availableCapacity;
        }

    }
}
