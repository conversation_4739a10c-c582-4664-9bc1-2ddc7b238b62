package com.ideas.tetris.pacman.services.webrate.dto;

import com.ideas.tetris.pacman.services.webrate.entity.WebrateChannel;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateOverrideChannel;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;

import java.text.ParseException;
import java.text.SimpleDateFormat;

public class WebrateOverrideChannelDto {

    private Integer id;
    private String channelOverrideEndDT;
    private String channelOverrideStartDT;
    private String webrateOverrideName;
    private Integer propertyId;
    private Integer productID;
    private WebrateChannel webrateChannelMon;
    private WebrateChannel webrateChannelTues;
    private WebrateChannel webrateChannelWed;
    private WebrateChannel webrateChannelThurs;
    private WebrateChannel webrateChannelFri;
    private WebrateChannel webrateChannelSat;
    private WebrateChannel webrateChannelSun;
    private boolean shouldDelete;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getChannelOverrideEndDT() {
        return channelOverrideEndDT;
    }

    public void setChannelOverrideEndDT(String channelOverrideEndDT) {
        this.channelOverrideEndDT = channelOverrideEndDT;
    }

    public String getChannelOverrideStartDT() {
        return channelOverrideStartDT;
    }

    public void setChannelOverrideStartDT(String channelOverrideStartDT) {
        this.channelOverrideStartDT = channelOverrideStartDT;
    }

    public String getWebrateOverrideName() {
        return webrateOverrideName;
    }

    public void setWebrateOverrideName(String webrateOverrideName) {
        this.webrateOverrideName = webrateOverrideName;
    }

    public Integer getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Integer propertyId) {
        this.propertyId = propertyId;
    }

    public Integer getProductID() {
        return productID;
    }

    public void setProductID(Integer productID) {
        this.productID = productID;
    }

    public WebrateChannel getWebrateChannelMon() {
        return webrateChannelMon;
    }

    public void setWebrateChannelMon(WebrateChannel webrateChannelMon) {
        this.webrateChannelMon = webrateChannelMon;
    }

    public WebrateChannel getWebrateChannelTues() {
        return webrateChannelTues;
    }

    public void setWebrateChannelTues(WebrateChannel webrateChannelTues) {
        this.webrateChannelTues = webrateChannelTues;
    }

    public WebrateChannel getWebrateChannelWed() {
        return webrateChannelWed;
    }

    public void setWebrateChannelWed(WebrateChannel webrateChannelWed) {
        this.webrateChannelWed = webrateChannelWed;
    }

    public WebrateChannel getWebrateChannelThurs() {
        return webrateChannelThurs;
    }

    public void setWebrateChannelThurs(WebrateChannel webrateChannelThurs) {
        this.webrateChannelThurs = webrateChannelThurs;
    }

    public WebrateChannel getWebrateChannelFri() {
        return webrateChannelFri;
    }

    public void setWebrateChannelFri(WebrateChannel webrateChannelFri) {
        this.webrateChannelFri = webrateChannelFri;
    }

    public WebrateChannel getWebrateChannelSat() {
        return webrateChannelSat;
    }

    public void setWebrateChannelSat(WebrateChannel webrateChannelSat) {
        this.webrateChannelSat = webrateChannelSat;
    }

    public WebrateChannel getWebrateChannelSun() {
        return webrateChannelSun;
    }

    public void setWebrateChannelSun(WebrateChannel webrateChannelSun) {
        this.webrateChannelSun = webrateChannelSun;
    }

    public boolean isShouldDelete() {
        return shouldDelete;
    }

    public void setShouldDelete(boolean shouldDelete) {
        this.shouldDelete = shouldDelete;
    }

    public static WebrateOverrideChannelDto mapEntityToDto(WebrateOverrideChannel webrateOverrideChannel) {
        WebrateOverrideChannelDto webrateOverrideChannelDto = new WebrateOverrideChannelDto();
        webrateOverrideChannelDto.setId(webrateOverrideChannel.getId());
        webrateOverrideChannelDto.setProductID(webrateOverrideChannel.getProductID());
        webrateOverrideChannelDto.setPropertyId(webrateOverrideChannel.getPropertyId());
        webrateOverrideChannelDto.setWebrateOverrideName(webrateOverrideChannel.getWebrateOverrideName());
        webrateOverrideChannelDto.setChannelOverrideStartDT(webrateOverrideChannel.getChannelOverrideStartDT().toString());
        webrateOverrideChannelDto.setChannelOverrideEndDT(webrateOverrideChannel.getChannelOverrideEndDT().toString());
        webrateOverrideChannelDto.setWebrateChannelSun(webrateOverrideChannel.getWebrateChannelSun());
        webrateOverrideChannelDto.setWebrateChannelMon(webrateOverrideChannel.getWebrateChannelMon());
        webrateOverrideChannelDto.setWebrateChannelTues(webrateOverrideChannel.getWebrateChannelTues());
        webrateOverrideChannelDto.setWebrateChannelWed(webrateOverrideChannel.getWebrateChannelWed());
        webrateOverrideChannelDto.setWebrateChannelThurs(webrateOverrideChannel.getWebrateChannelThurs());
        webrateOverrideChannelDto.setWebrateChannelFri(webrateOverrideChannel.getWebrateChannelFri());
        webrateOverrideChannelDto.setWebrateChannelSat(webrateOverrideChannel.getWebrateChannelSat());
        return webrateOverrideChannelDto;
    }

    public static WebrateOverrideChannel mapDtoToEntity(WebrateOverrideChannel webrateOverrideChannel, WebrateOverrideChannelDto webrateOverrideChannelDto) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            webrateOverrideChannel.setId(webrateOverrideChannelDto.getId());
            webrateOverrideChannel.setProductID(webrateOverrideChannelDto.getProductID());
            webrateOverrideChannel.setPropertyId(webrateOverrideChannelDto.getPropertyId());
            webrateOverrideChannel.setWebrateOverrideName(webrateOverrideChannelDto.getWebrateOverrideName());
            webrateOverrideChannel.setChannelOverrideStartDT(dateFormat.parse(webrateOverrideChannelDto.getChannelOverrideStartDT()));
            webrateOverrideChannel.setChannelOverrideEndDT(dateFormat.parse(webrateOverrideChannelDto.getChannelOverrideEndDT()));
            webrateOverrideChannel.setWebrateChannelSun(webrateOverrideChannelDto.getWebrateChannelSun());
            webrateOverrideChannel.setWebrateChannelMon(webrateOverrideChannelDto.getWebrateChannelMon());
            webrateOverrideChannel.setWebrateChannelTues(webrateOverrideChannelDto.getWebrateChannelTues());
            webrateOverrideChannel.setWebrateChannelWed(webrateOverrideChannelDto.getWebrateChannelWed());
            webrateOverrideChannel.setWebrateChannelThurs(webrateOverrideChannelDto.getWebrateChannelThurs());
            webrateOverrideChannel.setWebrateChannelFri(webrateOverrideChannelDto.getWebrateChannelFri());
            webrateOverrideChannel.setWebrateChannelSat(webrateOverrideChannelDto.getWebrateChannelSat());
        } catch (ParseException e) {
            throw new TetrisException(e.getMessage());
        }
        return webrateOverrideChannel;
    }

}
