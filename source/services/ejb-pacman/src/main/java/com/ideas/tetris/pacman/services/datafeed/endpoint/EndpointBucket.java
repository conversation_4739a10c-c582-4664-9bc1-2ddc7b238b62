package com.ideas.tetris.pacman.services.datafeed.endpoint;

/* This is enum to defined Buckets. any addition/deletion should be done here
 * These bucket name value should match with value defined for configuration parameter - pacman.integration.datafeed.bucket */

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public enum EndpointBucket {
    OPTIX_CORE("optixCore"),
    CORE("core"),
    SYS_OVERRIDE("override"),
    USER_ACTIVITY("userActivity"),
    INFO_MANAGER("infoManager"),
    SYS_CONFIG("configuration"),
    MARS("mars");

    private final String bucketName;

    EndpointBucket(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getBucketName() {
        return bucketName;
    }

    public static Set<EndpointBucket> getBuckets(final String allowedBuckets) {
        //by default Core bucket should be part of every request.
        return getBuckets(allowedBuckets, true);
    }

    public static Set<EndpointBucket> getBuckets(final String allowedBuckets, boolean includeCore) {
        if (!includeCore && (allowedBuckets == null || allowedBuckets.length() == 0)) {
            return Collections.EMPTY_SET;
        }
        List<String> bucketNames = Arrays.asList(allowedBuckets.split(","));
        Set<EndpointBucket> buckets = new HashSet<>();
        if (includeCore) {
            buckets.add(EndpointBucket.CORE);
        }
        for (String bucket : bucketNames) {
            buckets.add(getEndpointBucket(bucket));
        }
        return buckets;
    }

    public static EndpointBucket getEndpointBucket(String bucketName) {
        for (EndpointBucket bucket : EndpointBucket.values()) {
            if (bucket.getBucketName().equalsIgnoreCase(bucketName)) {
                return bucket;
            }
        }
        return EndpointBucket.CORE;
    }
}
