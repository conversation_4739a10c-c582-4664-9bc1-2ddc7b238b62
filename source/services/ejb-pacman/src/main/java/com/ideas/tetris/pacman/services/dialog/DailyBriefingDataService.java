package com.ideas.tetris.pacman.services.dialog;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.budget.entity.BudgetConfig;
import com.ideas.tetris.pacman.services.businessanalysis.BusinessAnalysisDashboardService;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDataDetailsDto;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dashboard.DashboardMetric2;
import com.ideas.tetris.pacman.services.dashboard.DashboardService;
import com.ideas.tetris.pacman.services.dashboard.MetricRequest;
import com.ideas.tetris.pacman.services.dashboard.type.ActualEstimatedType;
import com.ideas.tetris.pacman.services.dashboard.type.GroupByType;
import com.ideas.tetris.pacman.services.dashboard.type.MetricType2;
import com.ideas.tetris.pacman.services.dashboard.type.YearType;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dialog.entity.DailyBriefing;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Component
@Transactional
public class DailyBriefingDataService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService crudService;

    @Autowired
	private BusinessAnalysisDashboardService businessAnalysisDashboardService;

    @Autowired
	private SpecialEventService specialEventService;

    @Autowired
	private DateService dateService;

    @Autowired
	private DashboardService dashboardService;

    @Autowired
	private PacmanConfigParamsService configService;

    public static final Logger LOGGER = Logger.getLogger(DailyBriefingDataService.class);


    public void saveDailyBriefingMonthlySummary() {
        try {
            deleteDailyBriefingAllData();
            saveDailyBriefingData(getDailyBriefingDtoList());
        } catch (TetrisException e) {
            String errorMsg = String.format("Encountered error saving Daily Briefing monthly data in DailyBriefingDataService:saveDailyBriefingMonthlySummary() - '%s' for User ID - %s with Client Code - %s and Property Code - %s. See below stack trace for more info:", e.getMessage(), PacmanWorkContextHelper.getUserId(), PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
            LOGGER.error(errorMsg, e);
            throw e;
        }
    }


    public DailyBriefing getDailyBriefingMonthlySummary(String startDateString, String endDateString) {
        return getDailyBriefingMonthlySummary(LocalDate.parse(startDateString), LocalDate.parse(endDateString));
    }

    public DailyBriefing getDailyBriefingMonthlySummary(LocalDate startDate, LocalDate endDate) {
        try {
            return crudService.findByNamedQuerySingleResult(DailyBriefing.BY_ARRIVAL_BETWEEN, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
        } catch (TetrisException e) {
            String errorMsg = String.format("Encountered error while fetching Daily Briefing monthly data in DailyBriefingDataService:getDailyBriefingMonthlySummary() - '%s' for User ID - %s with Client Code - %s and Property Code - %s. See below stack trace for more info:", e.getMessage(), PacmanWorkContextHelper.getUserId(), PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
            LOGGER.error(errorMsg, e);
            throw e;
        }
    }

    private List<DailyBriefing> getDailyBriefingDtoList() {
        LocalDate updatedDate = LocalDate.from(LocalDateUtils.toJavaLocalDate(dateService.getCaughtUpDate(PacmanWorkContextHelper.getPropertyId())).withDayOfMonth(1));
        int maxCount = 12;
        List<DailyBriefing> DailyBriefingList = new ArrayList<>();
        for (int count = 0; count < maxCount; count++) {
            LocalDate firstDayOfMonth = updatedDate;
            LocalDate lastDayOfMonth = updatedDate.plusMonths(1).plusDays(-1);
            DailyBriefingList.add(getDailyBriefingData(firstDayOfMonth, lastDayOfMonth));
            updatedDate = lastDayOfMonth.plusDays(1);
        }
        return DailyBriefingList;
    }

    private void saveDailyBriefingData(List<DailyBriefing> dailyBriefing) {
        crudService.save(dailyBriefing);
    }

    private void deleteDailyBriefingAllData() {
        crudService.deleteAll(DailyBriefing.class);
    }

    public DailyBriefing getDailyBriefingData(LocalDate startOfTheMonth, LocalDate endOfTheMonth) {
        Date startOfTheMonthDate = LocalDateUtils.toDate(startOfTheMonth);
        Date endOfTheMonthDate = LocalDateUtils.toDate(endOfTheMonth);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDtoList = businessAnalysisDashboardService.getPropertyBusinessAnalysisDataDetailDtos(startOfTheMonthDate, endOfTheMonthDate, true, 1, false, false);
        return populateSummaryLevelData(businessAnalysisDataDetailsDtoList, startOfTheMonth, endOfTheMonth);
    }

    private DailyBriefing populateDailyBriefingData(BusinessAnalysisDataDetailsDto bADto, Integer capacity, Integer lastYearCapacity, Integer stylCapacity) {
        DailyBriefing dailyBriefingDto = new DailyBriefing();

        dailyBriefingDto.setCapacity(capacity);
        dailyBriefingDto.setLastYearCapacity(lastYearCapacity);
        dailyBriefingDto.setStylCapacity(stylCapacity);

        if (null != bADto.getOnBooks()) {
            dailyBriefingDto.setOnBooksRoomSold(bADto.getOnBooks().intValue());
            dailyBriefingDto.setOnBooksRoomRevenue(bADto.getRevenue());
        }
        if (null != bADto.getOccupancyForecast()) {
            dailyBriefingDto.setForecastedRoomSold(bADto.getOccupancyForecast().intValue());
            dailyBriefingDto.setForecastedRoomRevenue(bADto.getRevenueForecast());
        }
        if (null != bADto.getBudgetRooms()) {
            dailyBriefingDto.setBudgetRoomSold(bADto.getBudgetRooms().intValue());
            dailyBriefingDto.setBudgetRoomRevenue(bADto.getBudgetRevenue());
        }
        if (null != bADto.getLastYearOnBooks()) {
            dailyBriefingDto.setStlyRoomSold(bADto.getLastYearOnBooks().intValue());
            dailyBriefingDto.setStlyRoomRevenue(bADto.getLastYearRevenue());
        }
        if (null != bADto.getLastYearOccupancyForecast()) {
            dailyBriefingDto.setLastYearRoomSold(bADto.getLastYearOccupancyForecast().intValue());
            dailyBriefingDto.setLastYearRoomRevenue(bADto.getLastYearRevenueForecast());
        }
        return dailyBriefingDto;
    }

    private DailyBriefing populateSummaryLevelData(List<BusinessAnalysisDataDetailsDto> badDTOs, LocalDate startDate, LocalDate endDate) {
        DashboardMetric2 dashboardMetric2 = dashboardService.getMetrics2(getMetricRequests(), LocalDateUtils.toJodaLocalDate(startDate), LocalDateUtils.toJodaLocalDate(endDate));
        List<BigDecimal> dateRangeCapacity = (List<BigDecimal>) dashboardMetric2.getMetricResponses().get(0).getMetricValuesByGroupByType().get(GroupByType.PROPERTY.name());
        ToIntFunction<BigDecimal> bigDecimalToIntFunction = (BigDecimal t) -> (null == t || t.intValue() == Integer.MIN_VALUE) ? 0 : t.intValue();

        List<BigDecimal> dateRangeOutOfOrder = (List<BigDecimal>) dashboardMetric2.getMetricResponses().get(3).getMetricValuesByGroupByType().get(GroupByType.PROPERTY.name());
        int capacity = dateRangeCapacity.stream().mapToInt(bigDecimalToIntFunction).sum();
        int outOfOrder = dateRangeOutOfOrder.stream().mapToInt(bigDecimalToIntFunction).sum();

        List<BigDecimal> lastYearCapacityList = (List<BigDecimal>) dashboardMetric2.getMetricResponses().get(1).getMetricValuesByGroupByType().get(GroupByType.PROPERTY.name());
        List<BigDecimal> lastYearOutOfOrderList = (List<BigDecimal>) dashboardMetric2.getMetricResponses().get(4).getMetricValuesByGroupByType().get(GroupByType.PROPERTY.name());
        int lastYearCapacity = lastYearCapacityList.stream().mapToInt(bigDecimalToIntFunction).sum();
        int lastYearOutOfOrder = lastYearOutOfOrderList.stream().mapToInt(bigDecimalToIntFunction).sum();

        List<BigDecimal> sameTimeLastYearCapacityList = (List<BigDecimal>) dashboardMetric2.getMetricResponses().get(2).getMetricValuesByGroupByType().get(GroupByType.PROPERTY.name());
        List<BigDecimal> sameTimeLastYearOutOfOrderList = (List<BigDecimal>) dashboardMetric2.getMetricResponses().get(5).getMetricValuesByGroupByType().get(GroupByType.PROPERTY.name());
        int sameTimeLastYearCapacity = sameTimeLastYearCapacityList.stream().mapToInt(bigDecimalToIntFunction).sum();
        int sameTimeLastYearOutOfOrder = sameTimeLastYearOutOfOrderList.stream().mapToInt(bigDecimalToIntFunction).sum();

        boolean enablePhysicalCapacityConsideration = configService.isEnablePhysicalCapacityConsideration();
        if (!enablePhysicalCapacityConsideration) {
            capacity = capacity - outOfOrder;
            lastYearCapacity = lastYearCapacity - lastYearOutOfOrder;
            sameTimeLastYearCapacity = sameTimeLastYearCapacity - sameTimeLastYearOutOfOrder;
        }
        DailyBriefing dailyBriefing = populateDailyBriefingData(badDTOs.get(0), capacity, lastYearCapacity, sameTimeLastYearCapacity);
        dailyBriefing.setSpecialEvents(getSpecialEvents(startDate, endDate));
        populatePickUpValue(dailyBriefing, startDate, endDate);
        dailyBriefing.setStartDate(startDate);
        dailyBriefing.setEndDate(endDate);

        return dailyBriefing;
    }

    private void populatePickUpValue(DailyBriefing dailyBriefing, LocalDate startDate, LocalDate endDate) {
        Date startDateDate = LocalDateUtils.toDate(startDate);
        Date endDateDate = LocalDateUtils.toDate(endDate);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDtoList = businessAnalysisDashboardService.getForecastGroupBusinessAnalysisDataDetailDtos(startDateDate, endDateDate, false, 7, false);
        if (null != businessAnalysisDataDetailsDtoList && !businessAnalysisDataDetailsDtoList.isEmpty()) {
            for (BusinessAnalysisDataDetailsDto businessAnalysisDataDetailsDto : businessAnalysisDataDetailsDtoList) {
                int pickUpvalue = businessAnalysisDataDetailsDto.getOnBooksPickUp().intValue();
                if ("Transient".equalsIgnoreCase(businessAnalysisDataDetailsDto.getName())) {
                    dailyBriefing.setTransientRoomSold(pickUpvalue);
                } else {
                    dailyBriefing.setGroupRoomSold(pickUpvalue);
                }
            }
        }
    }

    private String getSpecialEvents(LocalDate startDate, LocalDate endDate) {
        Date startDateDate = LocalDateUtils.toDate(startDate);
        Date endDateDate = LocalDateUtils.toDate(endDate);
        List<PropertySpecialEventInstance> specialEventInstancesForADateRange = specialEventService.getSpecialEventInstancesForADateRange(startDateDate, endDateDate, false, null, null);
        List<String> specialEventNameList = specialEventInstancesForADateRange.stream().filter(specialEventInstance -> specialEventInstance.isForecastImpacted()).map(specialEventVO -> specialEventVO.getPropertySpecialEvent().getName()).collect(Collectors.toList());
        if (!specialEventNameList.isEmpty()) {
            return StringUtils.join(specialEventNameList, ',');
        }
        return null;
    }

    public boolean isBudgetEnabled() {
        List<BudgetConfig> budgetConfigList = crudService.findAll(BudgetConfig.class);
        return budgetConfigList.isEmpty() ? false : true;
    }

    public List<MetricRequest> getMetricRequests() {
        List<MetricRequest> requests = new ArrayList<>();
        MetricType2 capacity = MetricType2.TOTAL_CAPACITY;
        requests.add(getMetricRequestFor(capacity, ActualEstimatedType.ACTUAL, YearType.CURRENT_YEAR, false));
        requests.add(getMetricRequestFor(capacity, ActualEstimatedType.ACTUAL, YearType.LAST_YEAR, false));
        requests.add(getMetricRequestFor(capacity, ActualEstimatedType.ACTUAL, YearType.LAST_YEAR, true));
        requests.add(getMetricRequestFor(MetricType2.OUT_OF_ORDER, ActualEstimatedType.ACTUAL, YearType.CURRENT_YEAR, false));
        requests.add(getMetricRequestFor(MetricType2.OUT_OF_ORDER, ActualEstimatedType.ACTUAL, YearType.LAST_YEAR, false));
        requests.add(getMetricRequestFor(MetricType2.OUT_OF_ORDER, ActualEstimatedType.ACTUAL, YearType.LAST_YEAR, true));
        return requests;
    }

    public MetricRequest getMetricRequestFor(MetricType2 metricType2, ActualEstimatedType actualEstimatedType, YearType yearType, Boolean dowAdjustedForLastYear) {
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setMetricType(metricType2);
        metricRequest.setActualEstimatedType(actualEstimatedType);
        metricRequest.setGroupByType(GroupByType.PROPERTY);
        metricRequest.setYearType(yearType);
        metricRequest.setDowAdjustForLastYear(dowAdjustedForLastYear);
        return metricRequest;
    }
}
