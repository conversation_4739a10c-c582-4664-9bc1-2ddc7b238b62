package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.opera.constants.OperaGroupServiceConstants;
import com.ideas.tetris.pacman.services.opera.metric.OperaMetrics;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.Date;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * Created by idnpak on 2/24/2015.
 */
@Component
public class OperaTransformGroupDataService {
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;
    @Autowired
    OperaUtilityService operaUtilityService;
    @Autowired
	private PacmanConfigParamsService configParamsService;

    private static final Logger LOGGER = Logger.getLogger(OperaTransformGroupDataService.class.getName());
    private static final String FUTURE_DAYS = "Future_Days";

    public enum TransformGroupMetricType {
        TRANSFORM_STAGE_DATA_GROUP
    }


    public static final OperaMetrics<TransformGroupMetricType> metricsTransformGroup = new OperaMetrics<>();

    public CrudService getCrudService() {
        return crudService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public int transformGroupData(String correlationId) {
        LOGGER.info("Started transforming group data for feed : " + correlationId);
        int numRows = 0;
        try {
            metricsTransformGroup.start(TransformGroupMetricType.TRANSFORM_STAGE_DATA_GROUP);
            //Groups with Group  Status Type = NONADJUST should not go to pacman.
            numRows += crudService.executeUpdateByNativeQuery(OperaGroupMaster.UPDATE_FOREIGN_KEYS_SQL);
            numRows += crudService.executeUpdateByNativeQuery(OperaGroupBlock.UPDATE_FOREIGN_KEYS_SQL);
            numRows += transformStageData(correlationId);
            metricsTransformGroup.stop(TransformGroupMetricType.TRANSFORM_STAGE_DATA_GROUP);
        } finally {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug(new StringBuilder().append("Finished transforming all stage group data ").append(numRows)
                        .append("  rows:\n").append(metricsTransformGroup.toString()).toString());
            }
        }
        LOGGER.info("Completed transforming group data for feed : " + correlationId);
        return numRows;
    }

    public int transformStageData(String correlationId) {
        int numRows = crudService
                .executeUpdateByNativeQuery(OperaGroupServiceConstants.CHANGE_NEGATIVE_RATE_VALUES_TO_ZERO);
        numRows += updateGroupRateValues();
        Date businessDate = operaUtilityService.getBusinessDate(correlationId);
        Date maxFutureDate = operaUtilityService.getMaxFutureDate(businessDate, FUTURE_DAYS, correlationId);

        numRows += crudService.executeUpdateByNativeQuery(
                OperaGroupServiceConstants.DELETE_RECORDS_ACCORDING_TO_FUTURE_WINDOW,
                QueryParameter.with(OperaGroupServiceConstants.MAX_WINDOW_SIZE, maxFutureDate).parameters());
        numRows += crudService.executeUpdateByNativeQuery(OperaGroupServiceConstants.DELETE_GB_WITHOUT_GM);
        numRows += crudService.executeUpdateByNativeQuery(OperaGroupServiceConstants.DELETE_GM_WITHOUT_GB);
        numRows += crudService.executeUpdateByNativeQuery(OperaGroupServiceConstants.UPDATE_GB_BUSINESS_DAY_END_DATE);
        return numRows;
    }

    protected int updateGroupRateValues() {
        boolean groupPerDOWcalculationEnabled = configParamsService.getParameterValue(FeatureTogglesConfigParamName.OPERA_GROUP_RATE_CALCULATION_PER_DOW_ENABLED);
        return groupPerDOWcalculationEnabled ? updateGroupRateValuesPerDOW() : updateGroupRateValueByAverage();
    }

    private int updateGroupRateValueByAverage() {
        return crudService.executeUpdateByNativeQuery(OperaGroupServiceConstants.UPDATE_GROUP_RATE_VALUE_SQL);
    }

    private int updateGroupRateValuesPerDOW() {
        String baseValue = configParamsService.getParameterValue(FeatureTogglesConfigParamName.RATES_BASE_OCCUPANCY_NUMBER);
        String mondayToThursdayRate = getParameterValue(FeatureTogglesConfigParamName.RATES_MONDAY_TO_THURSDAY, baseValue);
        String fridayToSunday = getParameterValue(FeatureTogglesConfigParamName.RATES_FRIDAY_TO_SUNDAY, baseValue);
        return crudService.executeUpdateByNativeQuery(getUpdateGroupRatePerDOWQuery().toString(), MapBuilder.with("mondayToThursday", mondayToThursdayRate).and("fridayToSunday", fridayToSunday).get());
    }

    private StringBuilder getUpdateGroupRatePerDOWQuery() {
        StringBuilder query = new StringBuilder();
        query.append(" UPDATE ");
        query.append("    opera.stage_group_block ");
        query.append(" SET ");
        query.append("    g3_rate_value = ");
        query.append("    CASE ");
        query.append("       WHEN ");
        query.append("          Datepart(dw, block_dt) > 1 AND Datepart(dw, block_dt) < 6 AND :mondayToThursday='single' ");
        query.append("      THEN ");
        query.append("          Single_Rate ");
        query.append(" 	  WHEN ");
        query.append("          Datepart(dw, block_dt) > 1 AND Datepart(dw, block_dt) < 6 ");
        query.append("       THEN ");
        query.append("          Double_Rate ");
        query.append(" 	  WHEN ");
        query.append("          :fridayToSunday='single' ");
        query.append("       THEN ");
        query.append("          Single_Rate ");
        query.append("       ELSE ");
        query.append("          Double_Rate ");
        query.append("    END");
        return query;
    }

    private String getParameterValue(FeatureTogglesConfigParamName ratesMondayToThursday, String baseValue) {
        String mondayToThursdayRate = configParamsService.getParameterValue(ratesMondayToThursday);
        mondayToThursdayRate = Constants.USE_BASE_OCCUPANCY.equalsIgnoreCase(mondayToThursdayRate) ? baseValue : mondayToThursdayRate;
        return mondayToThursdayRate;
    }

    public int updateAnalyticalMarketSegments(String correlationId) {
        int noRowsUpdated = 0;
        LOGGER.info("Starting UPDATE_ANALYTICAL_MARKET_SEGMENTS_DEFAULTS FOR GROUP");
        noRowsUpdated += crudService.executeUpdateByNativeQuery(
                OperaGroupServiceConstants.UPDATE_ANALYTICAL_MARKET_SEGMENTS_DEFAULTS,
                QueryParameter.with(OperaGroupServiceConstants.CORRELATION_ID, correlationId).parameters());
        LOGGER.info(new StringBuilder().append("Completed UPDATE_ANALYTICAL_MARKET_SEGMENTS_DEFAULTS FOR GROUP: ")
                .append(noRowsUpdated).toString());
        return noRowsUpdated;
    }

    public int preProcessGroupData(String correlationId) {
        // Delete Group Blocks with missing room type information from Stage Table
        int numRows = crudService.executeUpdateByNativeQuery(OperaGroupServiceConstants.DELETE_BLOCKS_WITH_MISSING_ROOM_TYPES);

        LOGGER.info(new StringBuilder().append("Updating group status according to G3 status map for ")
                .append(correlationId).toString());
        numRows += crudService.executeUpdateByNativeQuery(OperaGroupServiceConstants.UPDATE_GROUP_STATUS_CODE_SQL);
        return numRows;
    }
}
