package com.ideas.tetris.pacman.services.webrate.vo;


import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

public class WebRateDetailsVO implements Serializable {
    private List<String> webrateIdsList;
    private HashMap webRateMap;
    private Integer newWebrateSourcePropertyId;

    public List<String> getWebrateIdsList() {
        return webrateIdsList;
    }

    public void setWebrateIdsList(List<String> webrateIdsList) {
        this.webrateIdsList = webrateIdsList;
    }

    public HashMap getWebRateMap() {
        return webRateMap;
    }

    public void setWebRateMap(HashMap webRateMap) {
        this.webRateMap = webRateMap;
    }

    public Integer getNewWebrateSourcePropertyId() {
        return newWebrateSourcePropertyId;
    }

    public void setNewWebrateSourcePropertyId(Integer newWebrateSourcePropertyId) {
        this.newWebrateSourcePropertyId = newWebrateSourcePropertyId;
    }
}
