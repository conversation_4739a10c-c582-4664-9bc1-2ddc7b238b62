package com.ideas.tetris.pacman.services.budget.currency;

import com.ideas.tetris.pacman.services.budget.dto.CurrencyExchangeDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class NgiCurrencyExchangeService extends AbstractCurrencyExchangeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(NgiCurrencyExchangeService.class);

    @Override
    protected List<CurrencyExchangeDTO> getCurrencyExchangeRateForDateRange(String clientCode, String propertyCode, String revPlanCurrencyCode, String rmsCurrencyCode, LocalDate startDate, LocalDate endDate) {
        LOGGER.info("RevPlan currency conversion: {} -> {} for NGI external system", revPlanCurrencyCode, rmsCurrencyCode);
        return genericCurrencyExchangeService.getCurrencyExchangeRatesForDateRange(clientCode, propertyCode, revPlanCurrencyCode, rmsCurrencyCode, startDate, endDate);

    }

}
