package com.ideas.tetris.pacman.services.security;

import com.ideas.infra.tetris.security.EncryptionDecryption;
import com.ideas.infra.tetris.security.LDAPException;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.email.EmailService;
import com.ideas.tetris.pacman.services.twoFactAuthentication.service.TwoFactorAuthenticationService;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class ForgottenPasswordService {
    private static final Logger LOGGER = Logger.getLogger(ForgottenPasswordService.class);
    private static final String ERROR_NO_EMAIL = "No client found for email ";
    public static final String VERIFY_RESET_PASSWORD_LINK = "VerifyResetPasswordLink?token=";
    public static final String SSO_ENABLED_MESSAGE = "Single Sign On is enabled for your organization. Please use that to Log In.";

    @Autowired
    EmailService emailService;

    @Autowired
    PacmanConfigParamsService configParamsService;
    @Autowired
    UserGlobalDBService userGlobalDBService;

    @Autowired
    TwoFactorAuthenticationService twoFactorAuthenticationService;


    public String resetPasswordLink(String email) {
        // We do not know the client as user cannot login
        try {
            String client = null;
            String token = "";
            GlobalUser globalUserByEmail = userGlobalDBService.getGlobalUserByEmail(email);
            if (null != globalUserByEmail) {
                client = globalUserByEmail.getClientCode();
            }
            LOGGER.debug("Client for email (" + email + ") is : " + client);
            if (client == null) {
                throw new TetrisException(ErrorCode.SERVICE_ERROR, ERROR_NO_EMAIL + email);
            }

            if (Boolean.TRUE.toString().equalsIgnoreCase(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.SSOENABLED.value(), client))) {
                throw new TetrisException(ErrorCode.SSO_REST_PASSWORD_ERROR, SSO_ENABLED_MESSAGE);
            }

            String forgotPasswordLink;
            StringBuilder emailText;
            globalUserByEmail = userGlobalDBService.getGlobalUserByEmail(email);
            forgotPasswordLink = createResetPasswordLink(globalUserByEmail.getEmail(), globalUserByEmail.getId());
            if (Boolean.parseBoolean(configParamsService.getParameterValueByClientLevel(client, FeatureTogglesConfigParamName.ENABLE_TWO_FACTOR_AUTHENTICATION.value()))) {
                token = "\nYour Toke is: ";
                token += twoFactorAuthenticationService.sendTokenToUser(globalUserByEmail.getId());
            }
            emailText = generateResetPasswordEmailText(globalUserByEmail.getFullName(), globalUserByEmail.getEmail(), forgotPasswordLink, token);

            emailService.sendText(SystemConfig.getEmailCreateUserFrom(), email,
                    SystemConfig.getEmailPasswordResetSubject(), emailText.toString());
        } catch (LDAPException e) {
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "LDAP fail. Update password.", e);
        }
        return "Your password reset link has been sent to your email address";
    }


    private StringBuilder generateResetPasswordEmailText(String userCn, String mail, String forgotPasswordLink, String token) {
        StringBuilder emailText = new StringBuilder();
        emailText.append("Hello ");
        emailText.append(userCn);
        emailText.append("\nusername: ");
        emailText.append(mail);
        emailText.append(token);
        emailText.append("\nClick this link to reset your password: ");
        emailText.append("\"" + forgotPasswordLink + "\"");
        return emailText;
    }

    public String generateTwoFactorTokenMail(Integer userId) {
        try {
            String client = null;
            GlobalUser globalUserById = userGlobalDBService.getGlobalUserById(userId);
            if (null != globalUserById) {
                client = globalUserById.getClientCode();
            }
            if (client == null) {
                throw new TetrisException(ErrorCode.SERVICE_ERROR, ERROR_NO_EMAIL + userId);
            }
            if (Boolean.TRUE.toString().equalsIgnoreCase(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.SSOENABLED.value(), client))) {
                throw new TetrisException(ErrorCode.SSO_REST_PASSWORD_ERROR, SSO_ENABLED_MESSAGE);
            }
            StringBuilder emailText;
            String token = twoFactorAuthenticationService.sendTokenToUser(globalUserById.getId());
            emailText = generateTokenEmailText(globalUserById.getFullName(), globalUserById.getEmail(), token);
            emailService.sendText(SystemConfig.getEmailCreateUserFrom(), globalUserById.getEmail(),
                    SystemConfig.getEmailTwoFactorTokenResetSubject(), emailText.toString());
        } catch (LDAPException e) {
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "LDAP fail. Update token.", e);
        }
        return "";
    }

    private StringBuilder generateTokenEmailText(String userCn, String mail, String token) {
        StringBuilder emailText = new StringBuilder();
        emailText.append("Hello ");
        emailText.append(userCn);
        emailText.append("\nusername: ");
        emailText.append(mail);
        emailText.append("\nYour Token is: ");
        emailText.append(token);
        return emailText;
    }

    private String createResetPasswordLink(String mail, Integer userId) {
        String g3ClientLink = SystemConfig.getG3ClientLink() + VERIFY_RESET_PASSWORD_LINK;
        String encrypted = getEncryptedToken(mail, userId);
        String urlEncodedToken = null;
        try {
            urlEncodedToken = URLEncoder.encode(encrypted, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("Could not url encode the encrypted password token", e);
            throw new TetrisException(e.toString());
        }

        return g3ClientLink + urlEncodedToken;
    }

    public String getEncryptedToken(String mail, Integer userId) {
        String timeStamp = String.valueOf(new Date().getTime());
        StringBuilder textToEncode = new StringBuilder(
                mail)
                .append("--")
                .append(userId)
                .append("--")
                .append(timeStamp);
        return EncryptionDecryption.doStrongTextEncryption(textToEncode.toString());
    }

}

