package com.ideas.tetris.pacman.services.forecast.dto;

import java.util.Date;

public class ExpectedForecastDetailGraphForPastSevenDaysDTO {
    private Date occupancyDate;
    private Integer pacePoint;
    private Integer transientOnBooks;
    private Integer stly;
    private Integer st2y;
    private Integer averageDow;

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public Integer getPacePoint() {
        return pacePoint;
    }

    public void setPacePoint(Integer pacePoint) {
        this.pacePoint = pacePoint;
    }

    public Integer getTransientOnBooks() {
        return transientOnBooks;
    }

    public void setTransientOnBooks(Integer transientOnBooks) {
        this.transientOnBooks = transientOnBooks;
    }

    public Integer getStly() {
        return stly;
    }

    public void setStly(Integer stly) {
        this.stly = stly;
    }

    public Integer getSt2y() {
        return st2y;
    }

    public void setSt2y(Integer st2y) {
        this.st2y = st2y;
    }

    public Integer getAverageDow() {
        return averageDow;
    }

    public void setAverageDow(Integer averageDow) {
        this.averageDow = averageDow;
    }
}
