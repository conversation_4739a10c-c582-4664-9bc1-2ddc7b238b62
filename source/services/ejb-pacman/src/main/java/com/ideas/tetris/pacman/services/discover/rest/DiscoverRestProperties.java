package com.ideas.tetris.pacman.services.discover.rest;

import com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig;

public class DiscoverRestProperties {

    public DiscoverRestProperties() {
        url = SystemConfig.getLearningAccessUrl();
        apiKey = SystemConfig.getLearningAccessApiKey();
        privateKey = SystemConfig.getLearningAccessPrivateKey();
        learningAccessUID = SystemConfig.getLearningAccessUID();
        jwtClaimAudience = url.substring(0, url.length() - 1);
    }

    private String url;
    private String apiKey;
    private String privateKey;
    private String learningAccessUID;
    private String jwtClaimAudience;

    public String getUrl() {
        return url;
    }

    public String getApiKey() {
        return apiKey;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public String getLearningAccessUID() {
        return learningAccessUID;
    }

    public String getJwtClaimAudience() {
        return jwtClaimAudience;
    }
}
