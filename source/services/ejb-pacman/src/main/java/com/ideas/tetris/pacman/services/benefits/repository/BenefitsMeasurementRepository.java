package com.ideas.tetris.pacman.services.benefits.repository;

import com.ideas.tetris.pacman.Repository;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.PropertyGroup;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Benefits;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.getPropertyGroupId;
import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.getPropertyId;
import static com.ideas.tetris.pacman.util.Executor.addIfTrue;
import static com.ideas.tetris.pacman.util.Runner.runIfNotEmpty;
import static java.math.BigDecimal.ZERO;
import static java.util.Objects.nonNull;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang.StringUtils.EMPTY;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Repository
@Component
public class BenefitsMeasurementRepository {


    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService globalCrudService;

    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    private static final String FG_FGType_MAP = "select Forecast_Group_ID, Forecast_Type_ID " +
            "from forecast_group where Status_ID = 1 ";

    public String getPropertyGroupName(Integer id) {
        PropertyGroup group = globalCrudService.find(PropertyGroup.class, id);
        return nonNull(group) ? group.getName() : EMPTY;
    }

    public List<Benefits> getBenefits(int startMonthIndex, Integer endMonthIndex, Integer startYear, Integer endYear) {
        if (getPropertyGroupId() != null) {
            return Optional.ofNullable(globalCrudService.findByNativeQuery(
                    Benefits.GET_BENEFITS_FOR_PROPERTY_GROUP,
                    QueryParameter.with(Benefits.PROPERTY_GROUP_ID_PARAM_NAME, getPropertyGroupId())
                            .and(Benefits.START_YEAR_PARAM_NAME, startYear)
                            .and(Benefits.END_YEAR_PARAM_NAME, endYear)
                            .and(Benefits.START_MONTH_PARAM_NAME, startMonthIndex)
                            .and(Benefits.END_MONTH_PARAM_NAME, endMonthIndex)
                            .parameters(),
                    this::getBenefits)).orElse(Collections.emptyList());
        }
        return Optional.ofNullable(globalCrudService.findByNativeQuery(
                Benefits.GET_BENEFITS,
                QueryParameter.with(Benefits.PROPERTY_ID_PARAM_NAME, getPropertyId())
                        .and(Benefits.START_YEAR_PARAM_NAME, startYear)
                        .and(Benefits.END_YEAR_PARAM_NAME, endYear)
                        .and(Benefits.START_MONTH_PARAM_NAME, startMonthIndex)
                        .and(Benefits.END_MONTH_PARAM_NAME, endMonthIndex)
                        .parameters(),
                this::getBenefits)).orElse(Collections.emptyList());
    }

    public Map<Integer, List<Benefits>> getBenefitsForPropertiesUnderPropertyGroup(
            Integer propertyGroupId, Integer startMonthIndex, Integer endMonthIndex, Integer startYear, Integer endYear) {
        Map<Integer, List<Benefits>> result = new HashMap<>();
        List<Benefits> benefits = globalCrudService.findByNativeQuery(
                Benefits.GET_BENEFITS_FOR_PROPERTIES_UNDER_PROPERTY_GROUP,
                QueryParameter.with(Benefits.PROPERTY_GROUP_ID_PARAM_NAME, propertyGroupId)
                        .and(Benefits.START_YEAR_PARAM_NAME, startYear)
                        .and(Benefits.END_YEAR_PARAM_NAME, endYear)
                        .and(Benefits.START_MONTH_PARAM_NAME, startMonthIndex)
                        .and(Benefits.END_MONTH_PARAM_NAME, endMonthIndex)
                        .parameters(),
                this::getBenefits);
        runIfNotEmpty(benefits,
                () -> benefits.forEach(benefit -> result.computeIfAbsent(benefit.getPropertyId(), b -> new ArrayList<>()).add(benefit)));
        return result;
    }

    private Benefits getBenefits(Object[] row) {
        Benefits dto = new Benefits();
        dto.setPropertyId((Integer) row[0]);
        dto.setMonth((Integer) row[1]);
        dto.setYear((Integer) row[2]);
        dto.setHeuristicOccupancy((Integer) row[3]);
        dto.setHeuristicRevenue((BigDecimal) row[4]);
        dto.setHeuristicAdr((BigDecimal) row[5]);
        dto.setHeuristicRevpar((BigDecimal) row[6]);
        dto.setActualOccupancy((Integer) row[7]);
        dto.setActualRevenue((BigDecimal) row[8]);
        dto.setActualAdr((BigDecimal) row[9]);
        dto.setActualRevpar((BigDecimal) row[10]);
        dto.setBenefitOccupancy((BigDecimal) row[11]);
        dto.setBenefitRevenue((BigDecimal) row[12]);
        dto.setCapacity((Integer) row[13]);
        dto.setAncillaryRevenue((BigDecimal) row[14]);
        dto.setAncillaryRevenueWithoutRms((BigDecimal) row[15]);
        dto.setAncillaryRevenueGain((BigDecimal) row[16]);
        dto.setAncillaryProfit((BigDecimal) row[17]);
        dto.setAncillaryProfitWithoutRms((BigDecimal) row[18]);
        dto.setAncillaryProfitGain((BigDecimal) row[19]);
        dto.setActualProfit((BigDecimal) row[20]);
        dto.setHeuristicProfit((BigDecimal) row[21]);
        dto.setBenefitProfitInPercent((BigDecimal) row[22]);
        dto.setActualProPOR((BigDecimal) row[23]);
        dto.setHeuristicProPOR((BigDecimal) row[24]);
        dto.setActualProPAR((BigDecimal) row[25]);
        dto.setHeuristicProPAR((BigDecimal) row[26]);
        dto.setBenefitADR((BigDecimal) row[27]);
        dto.setBenefitRevpar((BigDecimal) row[28]);
        dto.setAncillaryRevenueGainInPercent((BigDecimal) row[29]);
        dto.setAncillaryProfitGainInPercentage((BigDecimal) row[30]);
        dto.setBenefitProPORInPercent((BigDecimal) row[31]);
        dto.setBenefitProPARInPercent((BigDecimal) row[32]);
        return dto;
    }

    public void saveBenefits(List<Benefits> benefits) {
        globalCrudService.save(benefits);
    }

    public List<Property> getProperties(Set<Integer> properties){
        return globalCrudService.findByNamedQuery(Property.BY_IDS, QueryParameter.with(Property.PARAM_PROPERTY_LIST_IDS, properties).parameters());
    }

    public Property getProperty(int propertyId){
        return globalCrudService.findByNamedQuerySingleResult(Property.BY_ID, Map.of("id", propertyId));
    }


    public List<Integer> filterPropertiesForMonth(int month, int year, List<Integer> targetProperties) {
        String sql =
                "WITH Property_With_Stage_Changes as (  " +
                        "    select    " +
                        "        P.Property_ID,   " +
                        "        (year(PA.Stage_Changed_Date_Time) * 100 + month(PA.Stage_Changed_Date_Time)) as TARGET,           " +
                        "        RANK() Over (Partition BY P.Property_ID ORDER BY PA.Stage_Changed_Date_Time ASC) AS rank   " +
                        "    from Property P   " +
                        "    join Property_Stage_Changes PA   " +
                        "    on P.Property_ID = PA.Property_ID   " +
                        "    where P.Stage = 'TWO_WAY'  " +
                        "    and  PA.New_Stage = 'TwoWay'  " +
                        "    and P.Property_ID in (:propertyIds) " +
                        "    and P.Status_ID = 1   " +
                        ") " +
                        "select P.Property_ID " +
                        "from Property_With_Stage_Changes P  " +
                        "where P.rank = 1  " +
                        "and ((:year * 100 + :month) - P.TARGET) > 1";
        return globalCrudService.findByNativeQuery(sql,
                QueryParameter
                        .with("propertyIds", targetProperties)
                        .and("year", year)
                        .and("month", month)
                        .parameters());
    }

    public List<Integer> getPropertyIdsToProcess(int month, int year, Integer maxProperties) {
        String sql =
                "WITH Property_With_Stage_Changes as (  " +
                        "    select    " +
                        "        P.Property_ID,  P.Property_Code, C.Client_Code, " +
                        "        (year(PA.Stage_Changed_Date_Time) * 100 + month(PA.Stage_Changed_Date_Time)) as TARGET,    " +
                        "        ((:year  * 100 + :month)) CURR_MONTH,      " +
                        "        RANK() Over (Partition BY P.Property_ID ORDER BY PA.Stage_Changed_Date_Time ASC) AS rank   " +
                        "    from Property P   " +
                        "    join Client C  " +
                        "    on P.Client_ID = C.Client_ID  " +
                        "    join Property_Stage_Changes PA   " +
                        "    on P.Property_ID = PA.Property_ID   " +
                        "    where P.Stage = 'TWO_WAY'  " +
                        "    and  PA.New_Stage = 'TwoWay'  " +
                        "    and P.Status_ID = 1   " +
                        "), " +
                        "Source_Properties as ( " +
                        "    select Property_Id, Property_Code, Client_Code, CURR_MONTH " +
                        "    from Property_With_Stage_Changes " +
                        "    where rank = 1  " +
                        "    and CURR_MONTH - TARGET > 1 " +
                        "), " +
                        "Benefits_Main as ( " +
                        "    select Property_ID, ((YEAR * 100 + MONTH)) BENEFIT_MONTH " +
                        "    from Benefits " +
                        "), " +
                        "Benefits_Final as ( " +
                        "    select Property_Id, MAX(BENEFIT_MONTH) MAX_BENEFIT_MONTH  " +
                        "    from Benefits_Main " +
                        "    group by Property_Id " +
                        "), " +
                        "Final_Results as ( " +
                        "    select  " +
                        "        P.Property_ID, P.Property_Code, P.Client_Code, P.CURR_MONTH, " +
                        "        B.MAX_BENEFIT_MONTH " +
                        "    from Source_Properties P " +
                        "    left join Benefits_Final B " +
                        "    on P.Property_ID = B.Property_Id " +
                        ") " +
                        "select Property_ID, Property_Code, Client_Code  " +
                        "from Final_Results " +
                        "where MAX_BENEFIT_MONTH is null or CURR_MONTH > MAX_BENEFIT_MONTH " +
                        "order by Property_ID ";
        List<Object[]> target = globalCrudService.findByNativeQuery(sql, QueryParameter.with("month", month).and("year", year).parameters());
        List<Integer> result = new ArrayList<>();
        if (isNotEmpty(target)) {
            for (Object[] columns : target) {
                boolean shouldAdd = !pacmanConfigParamsService.getBooleanParameterValue(
                        IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.getParameterName(),
                        (String) columns[2],
                        (String) columns[1]);
                addIfTrue(shouldAdd, result, (Integer) columns[0]);
                if (result.size() == maxProperties) {
                    break;
                }
            }
        }
        return result;
    }

    public Map<Integer, Integer> getFGForecastTypeMap(int propertyID) {
        return multiPropertyCrudService.findByNativeQueryForSingleProperty(propertyID, FG_FGType_MAP, null).stream().collect(Collectors.toMap(o -> (Integer) o[0], o -> (Integer) o[1]));
    }

    public void cleanBenefitsData(int month, int year, String propertyIds) {
        String sql = "delete from Benefits where Month = " + month + " and Year = " + year + " and Property_Id in (" + propertyIds + ")";
        globalCrudService.executeUpdateByNativeQuery(sql);
    }

    public void zeroInsert(int month, int year, Collection<Integer> propertyIds) {
        globalCrudService.save(propertyIds.stream()
                .map(propertyId -> getBenefitsWithZeroValue(month, year, propertyId))
                .collect(Collectors.toList()));
    }

    private Benefits getBenefitsWithZeroValue(int month, int year, Integer propertyId) {
        Benefits benefits = new Benefits();
        benefits.setPropertyId(propertyId);
        benefits.setMonth(month);
        benefits.setYear(year);
        benefits.setBenefitRevenue(ZERO);
        benefits.setBenefitOccupancy(ZERO);
        benefits.setHeuristicRevenue(ZERO);
        benefits.setHeuristicOccupancy(0);
        benefits.setHeuristicAdr(ZERO);
        benefits.setHeuristicRevpar(ZERO);
        benefits.setActualRevenue(ZERO);
        benefits.setActualOccupancy(0);
        benefits.setActualAdr(ZERO);
        benefits.setActualRevpar(ZERO);
        benefits.setGroupBenefitRevenue(ZERO);
        benefits.setGroupBenefitOccupancy(ZERO);
        benefits.setGroupHeuristicRevenue(ZERO);
        benefits.setGroupHeuristicOccupancy(0);
        benefits.setGroupHeuristicAdr(ZERO);
        benefits.setGroupActualRevenue(ZERO);
        benefits.setGroupActualOccupancy(0);
        benefits.setGroupActualAdr(ZERO);
        benefits.setTransientBenefitRevenue(ZERO);
        benefits.setTransientBenefitOccupancy(ZERO);
        benefits.setTransientHeuristicRevenue(ZERO);
        benefits.setTransientHeuristicOccupancy(0);
        benefits.setTransientHeuristicAdr(ZERO);
        benefits.setTransientActualRevenue(ZERO);
        benefits.setTransientActualOccupancy(0);
        benefits.setTransientActualAdr(ZERO);
        return benefits;
    }

    public Map<Integer, String> getCapacities(List<Integer> propertyIds, Date startDate, Date endDate) {
        HashMap<Integer, String> result = new HashMap<>();
        multiPropertyCrudService.findByNativeQuery(
                propertyIds,
                "select Property_ID, SUM(Total_Accom_Capacity) TOTAL,  SUM(Total_Accom_Capacity - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other) EFFECTIVE " +
                        "from Total_Activity where Occupancy_DT between :startDate and :endDate " +
                        "group by Property_ID;",
                QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters(),
                row -> {
                    result.put((Integer) row[0], row[1] + "," + row[2]);
                    return row;
                }
        );
        return result;
    }

    public Map<Integer, String> getContextsForProperties(List<Integer> propertyIds) {
        String sql =
                "select Property_ID, concat(Client_Code, ',', Property_Code) context " +
                        "from Vw_Client_Property " +
                        "where Stage = 'TWO_WAY' " +
                        "and Property_ID in (" + StringUtils.join(propertyIds, ",") + ")";
        HashMap<Integer, String> result = new HashMap<>();
        globalCrudService.findByNativeQuery(sql, new HashMap<>(),
                row -> {
                    result.put((Integer) row[0], (String) row[1]);
                    return null;
                });
        return result;
    }

    public Map<Integer, String> getContextsForPropertyGroup(Integer propertyGroupId) {
        String sql =
                "select VCP.Property_ID, concat(Client_Code, ',', Property_Code) context " +
                        " from Vw_Client_Property VCP " +
                        " inner join Property_Property_Group PPG " +
                        " on VCP.Property_ID=PPG.Property_ID " +
                        " and PPG.Property_Group_ID = " + propertyGroupId;
        HashMap<Integer, String> result = new HashMap<>();
        globalCrudService.findByNativeQuery(sql, new HashMap<>(),
                row -> {
                    result.put((Integer) row[0], (String) row[1]);
                    return null;
                });
        return result;
    }

    public List<Integer> getFirstTimers(List<Integer> propertyIds) {
        String sql =
                "select P.Property_ID " +
                        "from Property P " +
                        "left join Benefits B " +
                        "on P.Property_ID = B.Property_Id " +
                        "where P.Property_ID in (:propertyIds) " +
                        "and B.Property_Id is null " +
                        "group by P.Property_ID " +
                        "having count(B.Property_ID) = 0";
        return globalCrudService.findByNativeQuery(sql, QueryParameter.with("propertyIds", propertyIds).parameters());
    }

    public int deleteBenefitsForPropertyId(Integer propertyId) {
        String sql = "delete from Benefits where Property_Id = :propertyId";
        return globalCrudService.executeUpdateByNativeQuery(sql, QueryParameter.with("propertyId", propertyId).parameters());
    }
}
