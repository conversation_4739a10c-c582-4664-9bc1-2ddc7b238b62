package com.ideas.tetris.pacman.services.purge;

import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceDailyBarOutput;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datasourceswitching.DataSourceCacheService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.PropertyLock;
import com.ideas.tetris.pacman.services.opera.OperaIncomingFile;
import com.ideas.tetris.pacman.services.opera.entity.DataLoadMetadata;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.purge.dto.TablePurgeStrategyDto;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterPredefinedValue;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterPredefinedValueType;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterValue;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.util.metric.G3Metrics;
import io.micrometer.core.instrument.Timer;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE;
import static com.ideas.tetris.pacman.common.constants.Constants.DECISION_TYPE_DAILYBAR_FULL_REFRESH;
import static com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig.getPurgePaceDailyBarOutputBatchSize;
import static com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceDailyBarOutput.FIND_COUNT_GREATER_THAN_OR_EQUAL_DECISION_ID;
import static com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceDailyBarOutput.FIND_COUNT_LESS_THAN_DECISION_ID;
import static com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus.SUCCESSFUL;
import static com.ideas.tetris.pacman.services.purge.PurgeConstants.*;
import static com.ideas.tetris.pacman.services.purge.WhereClauseBuilder.OPERA;
import static com.ideas.tetris.pacman.services.purge.WhereClauseBuilder.*;
import static java.lang.String.format;
import static org.apache.commons.lang.StringUtils.equalsIgnoreCase;

@Component
@Transactional
public class TenantPurgeService {
    static final String MAX_DATA_LOAD_METADATA_ID_QUERY = "SELECT MAX(Data_Load_Metadata_ID)\n" +
            "FROM [opera].Data_Load_Metadata dlm\n" +
            "JOIN [dbo].[File_Metadata] fm ON dlm.Correlation_ID = fm.File_Location\n" +
            "WHERE SnapShot_DT < :snapshotDt\n" +
            "  AND fm.Record_Type_id = " + RecordType.T2SNAP_RECORD_TYPE_ID;
    static final String DB_SPACE_UTILIZATION = "SELECT \n" +
            "    db_name() as DBName,    \n" +
            "    CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS TotalSpaceMB,    \n" +
            "    CAST(ROUND(((SUM(a.used_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS UsedSpaceMB,     \n" +
            "    CAST(ROUND(((SUM(a.total_pages) - SUM(a.used_pages)) * 8) / 1024.00, 2) AS NUMERIC(36, 2)) AS UnusedSpaceMB\n" +
            "FROM \n" +
            "    sys.tables t\n" +
            "INNER JOIN      \n" +
            "    sys.indexes i ON t.OBJECT_ID = i.object_id\n" +
            "INNER JOIN \n" +
            "    sys.partitions p ON i.object_id = p.OBJECT_ID AND i.index_id = p.index_id\n" +
            "INNER JOIN \n" +
            "    sys.allocation_units a ON p.partition_id = a.container_id\n" +
            "LEFT OUTER JOIN \n" +
            "    sys.schemas s ON t.schema_id = s.schema_id\n" +
            "WHERE \n" +
            "    t.NAME NOT LIKE 'dt%' \n" +
            "    AND t.is_ms_shipped = 0\n" +
            "    AND i.OBJECT_ID > 255;";
    static final String DELETE_PACE_DAILYBAR_OUTPUT_QUERY = "EXEC usp_cleanup_pace_dailybar_output_table :decisionId, :totalFutureRecords, :batchSize, :caughtUpDate";
    static final Function<Property, String> GET_PROPERTY_LEVEL_CONTEXT = p1 -> "pacman." + p1.getClient().getCode() + "." + p1.getCode();
    private static final Logger LOGGER = Logger.getLogger(TenantPurgeService.class);
    private static final Function<Function<Property, Date>, Predicate<Property>> PURGED_BEFORE_TODAY = f -> p -> Optional.of(p).map(f).map(DateUtil::convertJavaUtilDateToLocalDate).get().isBefore(java.time.LocalDate.now());
    private static final int PURGE_WINDOW_START_TIME = 19;
    private static final int PURGE_WINDOW_END_TIME = 23;
    static final Predicate<ConfigParameterPredefinedValue> IS_WITHIN_PURGE_WINDOW = cfg -> {
        final LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of(cfg.getValue()));
        return localDateTime.getHour() >= PURGE_WINDOW_START_TIME && localDateTime.getHour() < PURGE_WINDOW_END_TIME;
    };
    private static final Function<List<Property>, List<Integer>> GET_PID_LIST = pl -> pl.stream().map(Property::getId).collect(Collectors.toList());
    static final String GET_TABLE_PURGE_STRATEGY_FROM_DB = "SELECT Tenant_Purge_Enum_Name, Category_Name, Number_Of_Days_To_Persist, Is_Purging_Enabled " +
            "FROM Table_Purge_Strategy as tps INNER JOIN Purge_Category as pc " +
            "ON tps.Purge_Category_Id = pc.Purge_Category_Id";
    static final String GET_TABLE_PURGE_STRATEGY ="SELECT Tenant_Purge_Enum_Name, Category_Name, Number_Of_Days_To_Persist, Table_To_Delete, Table_To_Compare, " +
            "Field_To_Compare, Where_Clause_Template, Is_Property_Id_Applied_To_Where, Schema_Names, Purge_Priority, Is_Purging_Enabled, Category " +
            "FROM Table_Purge_Strategy AS tps INNER JOIN Purge_Category AS pc " +
            "ON tps.Purge_Category_Id = pc.Purge_Category_Id " +
            "WHERE Category = :Category";

    @TenantCrudServiceBean.Qualifier
	@Qualifier("tenantCrudServiceBean")
    @Autowired
    CrudService crudService;
    @GlobalCrudServiceBean.Qualifier
	@Autowired
    CrudService globalCrudService;
    @Autowired
    JobServiceLocal jobService;
    @Autowired
    PropertyService propertyService;
    @Autowired
    JobMonitorService jobMonitorService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    DateService dateService;
    @Autowired
    WhereClauseBuilder whereClauseBuilder;
    @Autowired
    DataSourceCacheService dataSourceCacheService;

    public int purgeTable(String sql, Map<String, Object> parameters, boolean failOnException) {
        int totalRowsDeleted = 0;
        long start = System.currentTimeMillis();

        try {
            totalRowsDeleted += crudService.executeUpdateByNativeQuery(sql, parameters);

            long elapsed = System.currentTimeMillis() - start;
            LOGGER.info("Deleted " + totalRowsDeleted + " rows for: [" + sql + "] in " + elapsed + "ms");
        } catch (Exception e) {
            if (failOnException) {
                throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error occurred during purge operation", e);
            } else {
                LOGGER.warn("Purge operation failed, but ignored", e);
            }
        }

        return totalRowsDeleted;
    }

    @Transactional(propagation = Propagation.NEVER)
    public void purgeAll(Integer propertyId, Purgable[] purgables, LocalDateTime jobstartDttm) {
        Boolean useOptimizedProcedureForPurging = useOptimizedProcedureForPurging();
        if (isDynamicPurgingProcessEnabled()) {
            purgeTables(propertyId, purgables, jobstartDttm,useOptimizedProcedureForPurging);
	        return;
        }
        LOGGER.debug("Purge Job Starts.");
        LocalDateTime cutOffTime = jobstartDttm.plusSeconds(SystemConfig.tenantPurgeTimeoutSeconds());

        boolean isEnhancedG3PurgingProcessEnabled = isEnhancedG3PurgingProcessEnabled();
        List<TablePurgeStrategyDto> tablePurgeStrategyDtoList = null;
        if (isEnhancedG3PurgingProcessEnabled &&
             (purgables != null && purgables.length > 0 && !(purgables[0] instanceof OperaPurgeEnum))) {
            tablePurgeStrategyDtoList = globalCrudService.findByNativeQuery(GET_TABLE_PURGE_STRATEGY_FROM_DB,
                    null,
                    row -> new TablePurgeStrategyDto((String) row[0], (String) row[1], (Integer) row[2], (Boolean) row[3]));
        }
        for (Purgable purgable : purgables) {
	    AtomicBoolean skipPurge = new AtomicBoolean(false);
            Integer propId = (purgable.isPropertyIdAppliedToWhereClause() ? propertyId : null);
            Timer timer = G3Metrics.createPurgeTimer(purgable.getTableToDelete());
            List<TablePurgeStrategyDto> finalTablePurgeStrategyDtoList = tablePurgeStrategyDtoList;
            timer.record(() -> {
                TablePurgeStrategyDto purgableDto = isEnhancedG3PurgingProcessEnabled && CollectionUtils.isNotEmpty(finalTablePurgeStrategyDtoList) ?
                        finalTablePurgeStrategyDtoList.stream().filter(dto -> dto.getTenantPurgeEnumName().equalsIgnoreCase(purgable.toString()))
                                .findFirst().orElse(null) : null;
                if(Objects.nonNull(purgableDto) && !purgableDto.getIsPurgingEnabled()){
                    LOGGER.info("Purging is disabled for: " +purgableDto.getTenantPurgeEnumName());
                    skipPurge.set(true);
                }
                if(!skipPurge.get()){
                    purgeOneTableWithStoredProcedure(propId, purgable, purgableDto,useOptimizedProcedureForPurging);
                }
            });
            if (LocalDateTime.now().isAfter(cutOffTime)) {
                LOGGER.info("Purge Timeout Time Reached");
                break;
            }
        }
        DBSpace result = crudService.findByNativeQuerySingleResult(DB_SPACE_UTILIZATION,null,
                row -> new DBSpace((String) row[0], (BigDecimal) row[1], (BigDecimal) row[2], (BigDecimal) row[3]));
                LOGGER.info("DBSpace:: DBName: "+result.getDbName() + " TotalSpaceInMB: "+result.getTotalSpace() +" UsedSpacedInMB: "
                +result.getUsedSpace()+" UnUsedSpacedInMB: " +result.getUnUsedSpace());
    }

    private Boolean useOptimizedProcedureForPurging() {
      return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_OPTIMIZED_PROCEDURE_FOR_PURGING);
    }
    public String purgePaceDailyBarOutputTable() {
        String status = "";
        try {
            List<BigInteger> result = crudService.findByNamedQuery(Decision.GET_TOP_TWO_DECISION_IDS_BY_DECISION_TYPE_AND_PROCESS_STATUS_ID, QueryParameter.with("decisionTypeID", DECISION_TYPE_DAILYBAR_FULL_REFRESH).and("processStatusID", SUCCESSFUL).parameters());
            if (null != result && result.size() == 2) {
                int decisionId = result.get(1).intValue();
                java.time.LocalDate caughtUpDate = LocalDateUtils.toJavaLocalDate(dateService.getCaughtUpDate());
                LOGGER.info("Applicable decision id : " + decisionId);
                long totalFutureRecords = crudService.<Long>findByNamedQuerySingleResult(FIND_COUNT_GREATER_THAN_OR_EQUAL_DECISION_ID, QueryParameter.with("decisionId", decisionId).and("caughtUpDate", JavaLocalDateUtils.toJodaLocalDate(caughtUpDate)).parameters());
                int batchSize = getPurgePaceDailyBarOutputBatchSize();
                long start = System.currentTimeMillis();
                List<Object> resultSet = crudService.findByNativeQuery(DELETE_PACE_DAILYBAR_OUTPUT_QUERY, QueryParameter.with("decisionId", decisionId)
                        .and("totalFutureRecords", totalFutureRecords)
                        .and("batchSize", batchSize)
                        .and("caughtUpDate", caughtUpDate)
                        .parameters());
                long elapsed = System.currentTimeMillis() - start;
                String finalResult = (String) resultSet.get(0);
                if(finalResult.contains("Migrated total records :")) {
                    status = finalResult + " rows in " + elapsed + "ms";
                    LOGGER.info(status);
                } else {
                    LOGGER.info(finalResult);
                    throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Error from sql script : " + finalResult);
                }
            }
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error occurred during purge operation", e);
        }
        return status;
    }
    public String deletePaceDailyBarOutputData(int decisionId) {
        try {
            java.time.LocalDate caughtUpDate = LocalDateUtils.toJavaLocalDate(dateService.getCaughtUpDate());
            int numberOfFutureRecordsDeleted = crudService.executeUpdateByNamedQuery(PaceDailyBarOutput.DELETE_FUTURE_BY_DECISION_ID, Map.of( "decisionId", decisionId, "date", caughtUpDate));
            int numberOfPastRecordsDeleted = crudService.executeUpdateByNamedQuery(PaceDailyBarOutput.DELETE_PAST_DECISIONS, Map.of( "date", caughtUpDate));
            return "Deleted future records : " + numberOfFutureRecordsDeleted + " and past records : " + numberOfPastRecordsDeleted;
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error occurred during purge operation", e);
        }
    }

    public boolean isDeleteRecordsRequired() {
        int decisionId = getApplicableDecisionID();
        if(decisionId != 0) {
            java.time.LocalDate caughtUpDate = LocalDateUtils.toJavaLocalDate(dateService.getCaughtUpDate());
            LOGGER.info("Applicable decision id : " + decisionId);
            return crudService.<Long>findByNamedQuerySingleResult(FIND_COUNT_LESS_THAN_DECISION_ID, QueryParameter.with("decisionId", decisionId).and("caughtUpDate", JavaLocalDateUtils.toJodaLocalDate(caughtUpDate)).parameters()) > 0L;
        }
        return false;
    }

    public int getApplicableDecisionID() {
        List<BigInteger> result = crudService.findByNamedQuery(Decision.GET_TOP_TWO_DECISION_IDS_BY_DECISION_TYPE_AND_PROCESS_STATUS_ID, QueryParameter.with("decisionTypeID", DECISION_TYPE_DAILYBAR_FULL_REFRESH).and("processStatusID", SUCCESSFUL).parameters());
        if (null != result && result.size() == 2) {
            return result.get(1).intValue();
        }
        return 0;
    }
    public boolean isPurgeDataFromPACEDailybarOutputEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.PURGE_PACE_DAILYBAR_OUTPUT_DATA_ENABLED);
    }
    public boolean isContinuousPricingEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
    }

    public void purgeTables(Integer propertyId, Purgable[] purgables, LocalDateTime jobstartDttm, Boolean useOptimizedProcedureForPurging) {
        String purgeEnumCategory = getPurgeEnumCategory(purgables);
        if (purgeEnumCategory.isEmpty()) {
            return;
        }
        LocalDateTime cutOffTime = jobstartDttm.plusSeconds(SystemConfig.tenantPurgeTimeoutSeconds());
        List<TablePurgeStrategyDto> tablePurgeStrategies = getTablePurgeStrategies(purgeEnumCategory);

        for (TablePurgeStrategyDto purgable : tablePurgeStrategies) {
            AtomicBoolean skipPurge = new AtomicBoolean(false);
            Integer propId = purgable.getIsPropertyAppliedToWhereClause() ? propertyId : null;
            Timer timer = G3Metrics.createPurgeTimer(purgable.getTableToDelete());
            timer.record(() -> {
                if(!purgable.getIsPurgingEnabled()){
                    LOGGER.info("Purging is disabled for: " +purgable.getTenantPurgeEnumName() +" property Code: "+ PacmanWorkContextHelper.getPropertyCode());
                    skipPurge.set(true);
                }
                if(!skipPurge.get()){
                    purgeTable(propId,purgable,useOptimizedProcedureForPurging);
                }
            });
            if (LocalDateTime.now().isAfter(cutOffTime)) {
                LOGGER.info("Purge Timeout Time Reached");
                break;
            }
        }
        DBSpace result = crudService.findByNativeQuerySingleResult(DB_SPACE_UTILIZATION,null,
                row -> new DBSpace((String) row[0], (BigDecimal) row[1], (BigDecimal) row[2], (BigDecimal) row[3]));
        LOGGER.info("DBSpace:: DBName: "+result.getDbName() + " TotalSpaceInMB: "+result.getTotalSpace() +" UsedSpacedInMB: "
                +result.getUsedSpace()+" UnUsedSpacedInMB: " +result.getUnUsedSpace());
    }

    private String getPurgeEnumCategory(Purgable[] purgables) {
        if (purgables == null || purgables.length <= 0) {
            return StringUtils.EMPTY;
        } else if (purgables[0] instanceof TenantPurgeEnum) {
            return TENANT;
        } else if (purgables[0] instanceof InfoMgrPurgeEnum) {
            return INFO_MGR;
        } else if (purgables[0] instanceof DecisionPurgeEnum) {
            return DECISION;
        } else if (purgables[0] instanceof OperaPurgeEnum) {
            return OPERA;
        }
        return StringUtils.EMPTY;
    }

    private List<TablePurgeStrategyDto> getTablePurgeStrategies(String purgeEnumCategory) {
        List<TablePurgeStrategyDto> tablePurgeStrategyDtos = globalCrudService.findByNativeQuery(GET_TABLE_PURGE_STRATEGY,
                Map.of("Category",purgeEnumCategory), this::getTablePurgeStrategyDto);
        return tablePurgeStrategyDtos.stream().sorted(Comparator.comparing(TablePurgeStrategyDto::getPurgePriority)).collect(Collectors.toList());
    }

    private TablePurgeStrategyDto getTablePurgeStrategyDto(Object[] row) {
        return new TablePurgeStrategyDto((String) row[0], (String) row[1], (Integer) row[2], (String) row[3], (String) row[4], (String) row[5], (String) row[6],
                (Boolean) row[7], (String) row[8], (Integer) row[9], (Boolean) row[10],(String) row[11]);
    }

    private boolean isDynamicPurgingProcessEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_DYNAMIC_PURGING_PROCESS);
    }

    private boolean isEnhancedG3PurgingProcessEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_ENHANCED_G3_PURGING_PROCESS);
    }

    private void purgeOneTableWithStoredProcedure(Integer propertyId, Purgable purgable, TablePurgeStrategyDto tablePurgeStrategyDto, Boolean useOptimizedProcedureForPurging) {

        try {
            if (purgable == null) {
                return;
            }

            String sqlWhereClause = whereClauseBuilder.createWhereClause(propertyId, purgable, tablePurgeStrategyDto);
            if (purgable instanceof InfoMgrPurgeEnum && StringUtils.isEmpty(sqlWhereClause)) {
                return;
            }
            long start = System.currentTimeMillis();
            String proc;
            if(useOptimizedProcedureForPurging && equalsIgnoreCase(tablePurgeStrategyDto.getCategory(),("TENANT"))){
                proc = "exec dbo.usp_Delete_TableDataByBatch_V1 :dryRun,:batchSize,:schemaName,:tableName,:whereClause,:useRowLockHint";
            }else {
                proc = "exec dbo.usp_Delete_TableDataByBatch :dryRun,:batchSize,:schemaName,:tableName,:whereClause,:useRowLockHint";
            }
            Query nativeQuery = crudService.getEntityManager().createNativeQuery(proc);
            nativeQuery.setParameter("dryRun",false);
            nativeQuery.setParameter("batchSize",purgable.getBatchSize());
            nativeQuery.setParameter("schemaName",purgable.getSchema());
            nativeQuery.setParameter("tableName",purgable.getTableToDelete());
            nativeQuery.setParameter("whereClause",sqlWhereClause);
            nativeQuery.setParameter("useRowLockHint",purgable.isUseRowLock());
            List resultList = nativeQuery.getResultList();

            Object result = null;
            Object deletedRowCount = null;
            for (Object o : resultList) {
                if (o instanceof Object[]) {
                    Object[] array = (Object[]) o;
                    result = array[0];
                    deletedRowCount = array.length > 13 ? array[13] : null;
                }
            }
            long elapsed = System.currentTimeMillis() - start;
            String debugInfo = String.format(
                    "BatchSize %s for [%s].%s.%s with where clause of %s deleted total records %s in %s ms with a result of %s",
                    purgable.getBatchSize(),
                    PacmanWorkContextHelper.getPropertyId(),
                    purgable.getSchema(),
                    purgable.getTableToDelete(),
                    sqlWhereClause,
                    deletedRowCount,
                    elapsed,
                    result);


        LOGGER.debug(debugInfo);
        //logging to have some patterns for dashboard. we will delete above existing info in 9.6.1 once this pattern
        // gets configured on DD, else will revert this below logger.
        LOGGER.info(String.format(
                "BatchSize : %s; TableName : %s; DeletedRecords : %s; Duration : %s ms; Result : %s;",
                purgable.getBatchSize(),purgable.getTableToDelete(),deletedRowCount,elapsed,result));

            if (!"COMPLETE".equals(result) && !purgable.isFailedSilently()) {
                throw new TetrisException(String.format("Purge for '%s' failed where clause '%s'", purgable.getTableToDelete(),
                        sqlWhereClause));
            }
        }catch(Exception e) {
            LOGGER.error("Exception occurred while purging table : " + purgable.getTableToDelete(), e);
            e.printStackTrace();
        }
    }
    private void purgeTable(Integer propertyId, TablePurgeStrategyDto purgeDto, Boolean useOptimizedProcedureForPurging) {
        try {
            boolean isWhereClauseValid = isWhereClauseValid(purgeDto);
            if (!isWhereClauseValid) {
                LOGGER.info("Enum: " + purgeDto.getTenantPurgeEnumName() + " has invalid where clause: " + purgeDto.getWhereClauseTemplate());
                return;
            }
            String sqlWhereClause = whereClauseBuilder.createWhereClauseForDeletion(propertyId, purgeDto);
            long start = System.currentTimeMillis();
            String proc;
            if(Boolean.TRUE.equals(useOptimizedProcedureForPurging) && equalsIgnoreCase(purgeDto.getCategory(),("TENANT"))){
                proc = "exec dbo.usp_Delete_TableDataByBatch_V1 :dryRun,:batchSize,:schemaName,:tableName,:whereClause,:useRowLockHint";
            }else{
               proc = "exec dbo.usp_Delete_TableDataByBatch :dryRun,:batchSize,:schemaName,:tableName,:whereClause,:useRowLockHint";
            }
            Query nativeQuery = crudService.getEntityManager().createNativeQuery(proc);
            nativeQuery.setParameter("dryRun",false);
            nativeQuery.setParameter("batchSize",getBatchSize());
            nativeQuery.setParameter("schemaName",purgeDto.getSchemaNames());
            nativeQuery.setParameter("tableName",purgeDto.getTableToDelete());
            nativeQuery.setParameter("whereClause",sqlWhereClause);
            nativeQuery.setParameter("useRowLockHint",isUseRowLock());
            List resultList = nativeQuery.getResultList();

            Object result = null;
            Object deletedRowCount = null;
            for (Object o : resultList) {
                if (o instanceof Object[]) {
                    Object[] array = (Object[]) o;
                    result = array[0];
                    deletedRowCount = array.length > 13 ? array[13] : null;
                }
            }
            long elapsed = System.currentTimeMillis() - start;
            String debugInfo = String.format(
                    "BatchSize %s for [%s].%s.%s with where clause of %s deleted total records %s in %s ms with a result of %s",
                    getBatchSize(),
                    PacmanWorkContextHelper.getPropertyId(),
                    purgeDto.getSchemaNames(),
                    purgeDto.getTableToDelete(),
                    sqlWhereClause,
                    deletedRowCount,
                    elapsed,
                    result);


        LOGGER.info(debugInfo);
        //logging to have some patterns for dashboard. we will delete above existing info in 9.6.1 once this pattern
        // gets configured on DD, else will revert this below logger.
        LOGGER.info(String.format(
                "BatchSize : %s; TableName : %s; DeletedRecords : %s; Duration : %s ms; Result : %s;",
                getBatchSize(),purgeDto.getTableToDelete(),deletedRowCount,elapsed,result));

            if (!"COMPLETE".equals(result) && !isFailedSilently(purgeDto)) {
                throw new TetrisException(String.format("Purge for '%s' failed where clause '%s'", purgeDto.getTableToDelete(),
                        sqlWhereClause));
            }
        }catch(Exception e) {
            LOGGER.error("Exception occurred while purging table : " + purgeDto.getTableToDelete(), e);
            e.printStackTrace();
        }
    }

    private boolean isWhereClauseValid(TablePurgeStrategyDto purgeDto) {
        String whereClauseTemplate = purgeDto.getWhereClauseTemplate();
        if(StringUtils.isEmpty(whereClauseTemplate)){
            return false;
        }
        boolean clauseCriteria = whereClauseTemplate.contains(PLACEHOLDER_PROPERTY_ID_CONDITIONAL) || whereClauseTemplate.contains(PLACEHOLDER_FIELD_TO_COMPARE)
                || whereClauseTemplate.contains(PLACEHOLDER_CAUGHT_UP_DATE) || whereClauseTemplate.contains(PLACEHOLDER_MIN_DATE);

        if (DECISION.equalsIgnoreCase(purgeDto.getCategory())) {
             return clauseCriteria;
        } else if (OPERA.equalsIgnoreCase(purgeDto.getCategory())) {
            return whereClauseTemplate.contains(PLACEHOLDER_TABLE_NAME) ||  whereClauseTemplate.contains(PLACEHOLDER_FIELD_TO_COMPARE) || whereClauseTemplate.contains(PLACEHOLDER_FILE_METADATA_IDS);
        } else if (TENANT.contains(purgeDto.getCategory())) {
            return clauseCriteria;
        } else if (INFO_MGR.equalsIgnoreCase(purgeDto.getCategory())) {
           return whereClauseTemplate.contains(PLACEHOLDER_FIELD_TO_COMPARE) || whereClauseTemplate.contains(PLACEHOLDER_INSTANCE_IDS);
        }
        return false;
    }

    private boolean isFailedSilently(TablePurgeStrategyDto purgeDto) {
        return purgeDto.getTableToDelete().equalsIgnoreCase("Decision");
    }

    int getBatchSize() {
        return SystemConfig.getStoredProcedureBatchSize();
    }

    boolean isUseRowLock() {
        return SystemConfig.useStoredProcedureRowLock();
    }

    public void updateLastPurgeDate(int propertyId) {
        String sql = "UPDATE Property with(rowlock) SET Last_Purged_Date = CURRENT_TIMESTAMP WHERE Property_Id = :propertyId";
        int rows = globalCrudService.executeUpdateByNativeQuery(sql, QueryParameter.with("propertyId", propertyId)
                .parameters());
        if (rows != 1) {
            throw new TetrisException("Could not set last purged date for property: " + propertyId);
        }
    }

    public int startPurgeJobs() {
        return startPurgeJobsByTimeZone(SystemConfig.getPurgePropertiesBatchSize(), SystemConfig.getPurgeSasPropertiesBatchSize());
    }

    public int startPurgeJobsByTimeZone(int dbPurgeCount, int sasPurgeCount) {
        // Get context of properties that are in favourable Timezones to delete
        final Set<String> withinTimezoneContexts = getPropertyContextsValidTimezones();
        // Get context of properties that have LDB enabled (These are to be ignored)
        final Set<String> ldbPropertyContexts = getLdbPropertyContexts();

        Predicate<Property> isEligibleForPurge = p -> Optional.ofNullable(p).map(GET_PROPERTY_LEVEL_CONTEXT)
                .filter(withinTimezoneContexts::contains).filter(s -> !ldbPropertyContexts.contains(s)).isPresent();

        // Start Tenant Purge Properties
        Function<List<Property>, String> aggregateIds = l -> l.stream().map(Property::getId).map(String::valueOf).collect(Collectors.joining(","));

        final List<Property> propertiesToBePurged = getPropertiesThrottledByDbServer(dbPurgeCount, isEligibleForPurge, Property::getLastPurgedDate, Property.ALL_ACTIVE_BY_STAGE_ORDER_BY_LAST_PURGED_DATE, SystemConfig.getPurgePropertiesDbServerLimit());
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("properties to be purged" + aggregateIds.apply(propertiesToBePurged));
        }
        startPurgeJobs(propertiesToBePurged, JobName.PurgeOldPropertyDataJob);

        // Start Sas Purge On Properties

        final List<Property> sasPropertiesToBePurged = getPropertiesThrottledByDbServer(sasPurgeCount, isEligibleForPurge, Property::getLastSasPurgedDate, Property.ALL_ACTIVE_BY_STAGE_ORDER_BY_LAST_SAS_PURGED_DATE, SystemConfig.getPurgeSasDatasetsDbServerLimit());
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("SAS properties to be purged" + aggregateIds.apply(sasPropertiesToBePurged));
        }
        startPurgeJobs(sasPropertiesToBePurged, JobName.PurgeOldPropertySASDatasetsJob);

        // Update the Property Table after invoking all the jobs
        updateLastPurgedDate(Property.UPDATE_LAST_PURGE_DATE_BY_PROPERTY_IDS, GET_PID_LIST.apply(propertiesToBePurged));
        updateLastPurgedDate(Property.UPDATE_LAST_SAS_PURGE_DATE_BY_PROPERTY_IDS, GET_PID_LIST.apply(sasPropertiesToBePurged));

        return propertiesToBePurged.size() + sasPropertiesToBePurged.size();
    }

    private List<Property> getPropertiesThrottledByDbServer(int purgeCount, Predicate<Property> eligibilityCriteria, Function<Property, Date> purgeDateFunction, String getPropertiesNamedQuery, Integer throttleByDbServer) {
        final Map<String, List<Property>> dbServerPropertyMap = new HashMap<>();
        Function<Property, String> getDbServerName = p -> dataSourceCacheService.getDBLoc(p.getId()).getServerName();
        Predicate<String> needNotThrottle = s -> !dbServerPropertyMap.containsKey(s) || dbServerPropertyMap.get(s).size() < throttleByDbServer;
        Predicate<Property> withinThrottleLimit = p -> Optional.ofNullable(p).map(getDbServerName).filter(needNotThrottle).isPresent();
        final List<Property> properties = globalCrudService.findByNamedQuery(getPropertiesNamedQuery, QueryParameter.with("stage", Stage.TWO_WAY).parameters());
        final Consumer<Property> addToThrottleMap = p -> {
            final String serverName = getDbServerName.apply(p);
            final List<Property> getValue = dbServerPropertyMap.getOrDefault(serverName, new ArrayList<>());
            getValue.add(p);
            dbServerPropertyMap.put(serverName, getValue);
        };
        return properties.stream().filter(eligibilityCriteria)
                .filter(PURGED_BEFORE_TODAY.apply(purgeDateFunction))
                .filter(withinThrottleLimit)
                .peek(addToThrottleMap)
                .limit(purgeCount).collect(Collectors.toList());
    }

    public void updateLastPurgedDate(String query, List<Integer> propertyIds) {
        if (CollectionUtils.isEmpty(propertyIds)) {
            return;
        }
        Map<String, Object> params = QueryParameter.with("propertyIds", propertyIds).parameters();
        globalCrudService.executeUpdateByNativeQuery(query, params);
    }


    private Function<JobName, Consumer<Property>> startPurgeJobs(List<Property> propertiesToBePurged, JobName jobName2) {
        final String startDateTime = DateTime.now().toString(ISODateTimeFormat.basicDateTime());
        Function<JobName, Consumer<Property>> startPurge = (jobName) -> p -> startJob(startDateTime, p.getId(), jobName);
        propertiesToBePurged.stream().forEach(startPurge.apply(jobName2));
        return startPurge;
    }

    public List<Property> getProperties(int purgeCount, Predicate<Property> eligibilityCriteria, Function<Property, Date> purgeDateFunction, String getPropertiesNamedQuery) {
        final List<Property> properties = globalCrudService.findByNamedQuery(getPropertiesNamedQuery, QueryParameter.with("stage", Stage.TWO_WAY).parameters());
        return properties.stream().filter(eligibilityCriteria)
                .filter(PURGED_BEFORE_TODAY.apply(purgeDateFunction))
                .limit(purgeCount).collect(Collectors.toList());
    }

    public Set<String> getLdbPropertyContexts() {
        List<ConfigParameterValue> configValueList = globalCrudService.findByNamedQuery(ConfigParameterValue.PRE_DEFINED_VALUE_BY_PARAM_NAME_AND_VALUE_AND_VALUE_TYPE,
                QueryParameter.with("paramName", IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.getParameterName())
                        .and("predefinedValue", "true")
                        .and("predefinedValueTypeCode", "boolean").parameters());
        return configValueList.stream().map(ConfigParameterValue::getContext).collect(Collectors.toSet());
    }

    public Set<String> getPropertyContextsValidTimezones() {
        ConfigParameterPredefinedValueType timezoneValueType = globalCrudService.findByNamedQuerySingleResult(ConfigParameterPredefinedValueType.BY_CODE, QueryParameter.with("code", "timezone").parameters());
        final List<ConfigParameterPredefinedValue> timezoneParamValues = globalCrudService.findByNamedQuery(ConfigParameterPredefinedValue.BY_TYPE, QueryParameter.with("type", timezoneValueType).parameters());
        final List<ConfigParameterPredefinedValue> timeZonesInWindow = timezoneParamValues.stream().filter(a -> !"Canada/East-Saskatchewan".equalsIgnoreCase(a.getValue())).filter(IS_WITHIN_PURGE_WINDOW).collect(Collectors.toList());
        final Set<Integer> validPredefinedIds = timeZonesInWindow.stream().map(ConfigParameterPredefinedValue::getId).collect(Collectors.toSet());
        final List<String> contextTimeZoneValues = globalCrudService.findByNamedQuery(ConfigParameterValue.CONTEXTS_BY_PARAM_NAME_PREDEFINED_VALUE_IDS, QueryParameter.with("paramName", CORE_PROPERTY_TIME_ZONE.getParameterName()).and("validPredefinedIds", validPredefinedIds).parameters());
        return new HashSet<>(contextTimeZoneValues);
    }

    public void startJob(String date, Integer propertyId, JobName jobName) {
        HashMap<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.PROPERTY_ID, propertyId);
        parameters.put(JobParameterKey.DATE, date);
        jobService.startJob(jobName, parameters);
    }

    public boolean isLDB(Integer propertyId) {
        Property property = propertyService.getPropertyById(propertyId);
        WorkContextType workContext = PacmanWorkContextHelper.getWorkContext();
        if (workContext == null) {
            workContext = new WorkContextType();
            PacmanWorkContextHelper.setWorkContext(workContext);
        }
        PacmanWorkContextHelper.setClientCode(property.getClient().getCode());
        PacmanWorkContextHelper.setPropertyCode(property.getCode());
        return pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED);
    }

    public Long startPurgeJob(Integer propertyId) {
        return startPurgeJob(propertyId, SystemConfig.isPurgeOperaHistoryJobEnabled(), SystemConfig
                .getPurgeOperaHistoryJobDaysToDelete());
    }

    public Long startPurgeJob(Integer propertyId, Boolean purgeOperaHistoryJob, Integer purgeOperaHistoryJobDaysToDelete) {
        String date = DateTime.now().toString(ISODateTimeFormat.basicDateTimeNoMillis());

        HashMap<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.PROPERTY_ID, propertyId);
        parameters.put(JobParameterKey.DATE, date);
        parameters.put(JobParameterKey.PURGE_OPERA_HISTORY_JOB, purgeOperaHistoryJob);
        parameters.put(JobParameterKey.PURGE_OPERA_HISTORY_JOB_DAYS_TO_DELETE, purgeOperaHistoryJobDaysToDelete);

        return jobService.startJob(JobName.PurgeOldPropertyDataJob, parameters);
    }

    public List<String> getLockedPropertyCodes() {
        List<String> codes = new ArrayList<>();
        codes.add("-1");
        List<PropertyLock> locks = jobMonitorService.getPropertyLocks();

        for (PropertyLock propertyLock : locks) {
            codes.add(propertyLock.getPropertyCode());
        }

        return codes;
    }

    @SuppressWarnings("unchecked")
    public Integer findMaxDataloadMetadataIdBefore(LocalDate businessDate) {
        Map<String, Object> params = QueryParameter.with("snapshotDt", businessDate.toDate()).parameters();
        return crudService.findByNativeQuerySingleResult(MAX_DATA_LOAD_METADATA_ID_QUERY, params);
    }

    public Integer updateOperaYieldCurrencyEntriesOlderThan(int numberOfDays) {
        final Integer maxMetadataIdToDelete = findMaxDataloadMetadataIdBefore(new LocalDate(dateService.getCaughtUpDate()).minusDays(numberOfDays));
        if (maxMetadataIdToDelete == null) {
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info(format("Less than %d days of data present", numberOfDays));
            }
            return 0;
        }
        final Map<String, Object> findParams = QueryParameter.with("metaDataId", maxMetadataIdToDelete).and("IncomingFileType", OperaIncomingFile.YIELD_CURRENCY.getFileTypeCode()).parameters();
        final Integer minIdRetained = crudService.findByNamedQuerySingleResult(DataLoadMetadata.GET_MIN_DATA_LOAD_METADATA_ID_GREATER_THAN_ID, findParams);
        if (minIdRetained == null) {
            LOGGER.warn("Minimum Data_Load_Metadata_Id To Be Retained is null");
            return 0;
        }
        final String updateRawYieldCurrency = "update [opera].Raw_Yield_Currency set Data_Load_Metadata_ID = :minIdRetained where Data_Load_Metadata_ID <= (:maxIdDeleted);";
        final String updateStageYieldCurrency = "update [opera].Stage_Yield_Currency set Data_Load_Metadata_ID = :minIdRetained where Data_Load_Metadata_ID <= (:maxIdDeleted);";
        final Map<String, Object> updateParams = QueryParameter.with("minIdRetained", minIdRetained).and("maxIdDeleted", maxMetadataIdToDelete).parameters();
        return crudService.executeUpdateByNativeQuery(updateRawYieldCurrency.concat(updateStageYieldCurrency), updateParams);
    }

    public static class DBSpace {
        private final String dbName;
        private final BigDecimal totalSpace;
        private final BigDecimal usedSpace;
        private final BigDecimal unUsedSpace;

        public DBSpace(String dbName, BigDecimal totalSpace, BigDecimal usedSpace, BigDecimal unUsedSpace) {
            this.dbName = dbName;
            this.totalSpace = totalSpace;
            this.usedSpace = usedSpace;
            this.unUsedSpace = unUsedSpace;
        }

        public String getDbName() {return dbName;}

        public BigDecimal getTotalSpace() {return totalSpace;}

        public BigDecimal getUsedSpace() {return usedSpace;}

        public BigDecimal getUnUsedSpace() {return unUsedSpace;}
    }
}
