package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.OperationsForecastArrivalsDeparturesDTO;
import com.ideas.tetris.pacman.services.reports.operations.dto.OperationsReportRowMapper;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class OperationsForecastArrivalsDeparturesDatafeedService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
	private PacmanConfigParamsService configParamsService;


    public List<OperationsForecastArrivalsDeparturesDTO> getForecastArrivalsDepartures(Date startDate, Date endDate) {
        boolean isPerPersonPricing = configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED);

        Map<String, Object> parameters = QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                .and("startDate", startDate)
                .and("endDate", endDate)
                .and("isRollingDate", "0")
                .and("rollingStartDate", "")
                .and("rollingEndDate", "")
                .and("isPerPersonPricing", isPerPersonPricing)
                .parameters();
        List<OperationsReportRowMapper> operationsRecords = tenantCrudService.findByNativeQuery(ARRIVAL_DEPARTURE_FORECAST, parameters, new OperationsReportRowMapper());
        return prepareOperationsForecastArrivalsDeparturesDTO(operationsRecords, isPerPersonPricing);
    }

    private List<OperationsForecastArrivalsDeparturesDTO> prepareOperationsForecastArrivalsDeparturesDTO(List<OperationsReportRowMapper> rowMappers, boolean isPerPersonPricing) {
        List<OperationsForecastArrivalsDeparturesDTO> listDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rowMappers)) {
            rowMappers.forEach(row -> listDtos.add(mapValuesForDto(row, isPerPersonPricing)));
        }
        return listDtos;
    }

    private OperationsForecastArrivalsDeparturesDTO mapValuesForDto(OperationsReportRowMapper row, boolean isPerPersonPricing) {
        OperationsForecastArrivalsDeparturesDTO dto = new OperationsForecastArrivalsDeparturesDTO();
        dto.setOccupancyDate(DateUtil.convertLocalDateToJavaUtilDate(row.getOccupancyDate()));
        dto.setOnBooksArrivals(row.getArrivalsOnBooks());
        dto.setOnBooksDepartures(row.getDeparturesOnBooks());
        dto.setOnBooksStayThrus(row.getStayThruOnBooks());
        dto.setForecastArrivals(row.getArrivalsForecast());
        dto.setForecastDepartures(row.getDeparturesForecast());
        dto.setForecastStayThrus(row.getStayThruForecast());
        if (isPerPersonPricing) {
            dto.setOnBooksAdults(getAnInt(row.getNumberOfAdultsOnBooks()));
            dto.setOnBooksChildren(getAnInt(row.getNumberOfChildrenOnBooks()));
            dto.setForecastAdults(getAnInt(row.getNumberOfAdults()));
            dto.setForecastChildren(getAnInt(row.getNumberOfChildren()));
        }
        return dto;
    }

    private int getAnInt(Integer numberOfAdultsOnBooks) {
        return Optional.ofNullable(numberOfAdultsOnBooks).orElse(0);
    }

    protected static final String ARRIVAL_DEPARTURE_FORECAST = new StringBuilder()
            .append("SELECT * FROM dbo.ufn_get_operations_report")
            .append("(:propertyId,")
            .append(":startDate,")
            .append(":endDate,")
            .append(":isRollingDate,")
            .append(":rollingStartDate,")
            .append(":rollingEndDate,")
            .append(":isPerPersonPricing)")
            .append("order by Occupancy_DT ASC")
            .toString();

}