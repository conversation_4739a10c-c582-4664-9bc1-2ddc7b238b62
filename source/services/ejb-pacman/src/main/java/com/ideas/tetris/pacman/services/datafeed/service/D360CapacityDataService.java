package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.D360CompCapacityDTO;
import com.ideas.tetris.platform.common.crudservice.CrudService;

import javax.inject.Inject;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class D360CapacityDataService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;


    public Boolean loadStrDailyDataIntoPacman(List<D360CompCapacityDTO> data) {

        boolean isDataSaved = false;
        StringBuilder query = new StringBuilder();

        data.forEach(d360CapacityDto -> {
            query.append("INSERT into [dbo].[D360_MKT_Hist_Capacity] ([D360_Comp_Set_ID], [Occupancy_DT], [Capacity], [Property_ID], [Created_DTTM], [Last_Updated_DTTM]) VALUES ( ");
            query.append("99999," + "CAST(N'" + d360CapacityDto.getOccupancyDate() + "' AS Date)");
            query.append("," + d360CapacityDto.getCapacity() + ",010027,CAST(N'2015-11-17T20:43:11.690' AS DateTime), CAST(N'2015-11-17T20:43:11.690' AS DateTime));");

        });

        if (!query.toString().isEmpty()) {
            tenantCrudService.executeUpdateByNativeQuery(query.toString());
            isDataSaved = true;
        }
        return isDataSaved;
    }


    public Boolean deleteData() {
        tenantCrudService.executeUpdateByNativeQuery("Delete from [dbo].[D360_MKT_Hist_Capacity] where D360_Comp_Set_ID=99999 ;");
        return true;
    }

}
