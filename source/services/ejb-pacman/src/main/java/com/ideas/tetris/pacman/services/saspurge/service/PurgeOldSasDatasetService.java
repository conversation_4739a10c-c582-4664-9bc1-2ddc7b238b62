package com.ideas.tetris.pacman.services.saspurge.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.purge.TenantPurgeService;
import com.ideas.tetris.pacman.services.sas.entity.SasPurgableDatasets;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Component
public class PurgeOldSasDatasetService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    CrudService tenantCrudService;

    @Autowired
    private SasDbToolService sasDbToolService;

    @Autowired
    private DateService dateService;

    private static final org.apache.log4j.Logger LOGGER = Logger.getLogger(TenantPurgeService.class);

    public void purgeAll(List<String> queries) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
         List<String> results = queries.stream()
                .map(query -> {
                    try {
                        sasDbToolService.executeQueryTenant(propertyId, query);
                        return "Successfully executed: " + query;
                    } catch (Exception e) {
                        return "Error executing query: " + query + " for propertyId: " + propertyId + ". " + e.getMessage();
                    }
                }).collect(Collectors.toList());
        results.forEach(LOGGER::info);
    }

    public List<String> buildTenantDatasetPurgeQueries() {
        List<SasPurgableDatasets> purgableDatasets = tenantCrudService.findAll(SasPurgableDatasets.class);

        if (purgableDatasets == null || purgableDatasets.isEmpty()) {
            return new ArrayList<>();
        }

        LocalDate snapshotDate = dateService.getCaughtUpJavaLocalDate();

        return purgableDatasets.stream()
                .map(e -> {
                    String columnName = e.getColumnName().toLowerCase().contains("dttm")
                            ? new StringBuilder("datepart(").append(e.getColumnName()).append(")").toString()
                            : e.getColumnName();

                    String retentionDate = LocalDateUtils.getSasDate(snapshotDate.minusDays(Math.abs(e.getRetentionPeriod())));

                    return new StringBuilder("DELETE FROM tenant.")
                            .append(e.getDatasetName())
                            .append(" WHERE ")
                            .append(columnName)
                            .append(" < '")
                            .append(retentionDate)
                            .append("'d;")
                            .toString();
                })
                .collect(Collectors.toList());
    }
}
