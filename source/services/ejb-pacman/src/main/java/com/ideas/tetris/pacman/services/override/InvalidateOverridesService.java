package com.ideas.tetris.pacman.services.override;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.CPManagementService;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionOverrideType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.groupflooroverride.GroupFloorOverrideService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Transactional
public class InvalidateOverridesService {

    private static final String OVERRIDE = "override";
    private static final String IN_ACTIVE_STATUS = "inActiveStatus";
    private static final String ACCOM_CLASS_ID = "accomClassId";
    private static final String ACTIVE_STATUS = "activeStatus";
    private static final String ACCOM_CLASS_IDS = "accomClassIds";
    private static final String CAUGHT_UP_DATE = "caughtUpDate";
    private static final String PROPERTY_ID = "propertyId";
    private static final String ACCOM_TYPE_IDS = "accomTypeIds";

    private static final Logger LOGGER = Logger.getLogger(InvalidateOverridesService.class);

    @Autowired
	private DateService dateService;

    @Autowired
	private PacmanConfigParamsService configService;

    @Autowired
	private DecisionService decisionService;

    @Autowired
	private PricingConfigurationService pricingConfigurationService;

    @Autowired
	private CPManagementService cpManagementService;

    @Autowired
	private GroupFloorOverrideService groupFloorOverrideService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    public boolean hasOverrides(Integer accomClassId, Date caughtUpDate) {
        // It's hard to look for overrides if you don't provide an Accom Class
        if (accomClassId == null) {
            return false;
        }

        // Check to see if there are Arrival Demand Forecast Overrides
        if (hasArrivalDemandForecastOverrides(accomClassId, caughtUpDate)) {
            return true;
        }

        // Check to see if there are Occupancy Demand Forecast Overrides
        if (hasOccupancyDemandForecastOverrides(accomClassId, caughtUpDate)) {
            return true;
        }

        // Check to see if there are any Decision Bar Overrides
        return hasDecisionBAROutputOverrides(accomClassId, caughtUpDate);
    }

    public void invalidateOverrides(List<AccomClass> accomClasses) {
        if (accomClasses == null || accomClasses.isEmpty()) {
            return;
        }

        List<Integer> accomClassIds = new ArrayList<Integer>();
        List<Integer> accomTypeIds = new ArrayList<>();

        try {
            LOGGER.info("Accom Classes to invalidate are " + accomClasses.size());
            // All of the property ids should be the same, so just get the first one
            Integer propertyId = accomClasses.get(0).getPropertyId();

            List<AccomClass> affectedAccomClasses = accomClasses;

            // If an AccomClass is the master class, all accom classes are considered 'dirty'
            if (configService.getBooleanParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()) && isMasterClassIncludedInList(affectedAccomClasses)) {
                affectedAccomClasses = findAllAccomClassesForProperty(propertyId);
            }
            LOGGER.info("Affected Accom Classes are " + affectedAccomClasses.size());
            // Add affected accom classes to accom class id list
            accomClassIds.addAll(affectedAccomClasses.stream().map(AccomClass::getId).collect(Collectors.toList()));
            affectedAccomClasses.stream().map(AccomClass::getAccomTypes).forEach(accomTypes -> accomTypeIds.addAll(accomTypes.stream().map(AccomType::getId).collect(Collectors.toList())));

            Date caughtUpDate = dateService.getCaughtUpDate();

            // Invalidate Arrival Demand Forecast Overrides
            invalidateArrivalDemandForecastOverrides(accomClassIds, caughtUpDate);

            // Invalidate Occupancy Demand Forecast Overrides
            invalidateOccupancyDemandForecastOverrides(accomClassIds, caughtUpDate);

            // Invalidate Decision LRV
            invalidateDecisionLRV(accomClassIds, caughtUpDate);

            if (configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())) {
                invalidateCPOverrides(accomClassIds);
            } else {
                // Invalidate Demand BAR Output
                invalidateDecisionBarOutput(accomClassIds, caughtUpDate);

                // Invalidate Demand BAR Output Overrides
                invalidateDecisionBAROutputOverride(accomClassIds, caughtUpDate);

                // Invalidate PACE Bar Output
                invalidatePACEBAROutput(accomClassIds, caughtUpDate);
            }

            // Invalidate PACE LRV
            invalidatePACELRV(accomClassIds, caughtUpDate);

            // Invalidate Wash Forecast Group Overrides
            invalidateWashForecastGroupFCSTOverrides(propertyId, caughtUpDate);

            // Invalidate Overbooking Overrides
            invalidateOverbookingOverrides(propertyId, accomTypeIds, caughtUpDate);
        } catch (Exception e) {
            LOGGER.fatal("Unable to Invalidate Overrides for AccomClasses: " + accomClassIds, e);
        }
    }

    public void invalidateCPOverrides(List<Integer> accomClassIds) {
        LocalDate caughtUpDate = dateService.getCaughtUpLocalDate();

        // Find any overrides in the future and remove them
        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService.findByNamedQuery(CPDecisionBAROutput.GET_OVERRIDES_FOR_ACCOM_CLASSES_WITH_DATES_BETWEEN, CPDecisionBAROutput.params(LocalDate.fromDateFields(caughtUpDate.toDate()), accomClassIds));

        if (CollectionUtils.isNotEmpty(cpDecisionBAROutputs)) {
            Set<Integer> accomTypeIds = cpDecisionBAROutputs.stream().map(cpOutput -> cpOutput.getAccomType().getId()).collect(Collectors.toSet());
            removeCPOverrides(new ArrayList<>(accomTypeIds), caughtUpDate);
        }

        // Invalidate PACE Bar Output
        invalidateCPPACEBAROutput(accomClassIds, caughtUpDate.toDate());
        groupFloorOverrideService.removeGroupFloorOverrides(accomClassIds, caughtUpDate);
    }

    public void invalidateCPOverridesOfAccomTypes(List<Integer> accomTypeIds) {
        LocalDate caughtUpDate = dateService.getCaughtUpLocalDate();

        List<CPDecisionBAROutput> cpDecisionBAROutputs = tenantCrudService.findByNamedQuery(CPDecisionBAROutput.GET_OVERRIDES_FOR_ACCOM_TYPES_WITH_DATES_BETWEEN,
                CPDecisionBAROutput.paramsWithArrivalDateAndAccomTypeIds(LocalDate.fromDateFields(caughtUpDate.toDate()), accomTypeIds));
        if (CollectionUtils.isNotEmpty(cpDecisionBAROutputs)) {
            removeCPOverrides(accomTypeIds, caughtUpDate);
        }

        invalidateAccomTypesOfCPPACEBAROutput(accomTypeIds, caughtUpDate.toDate());
    }

    private void removeCPOverrides(List<Integer> accomTypeIds, LocalDate caughtUpDate) {
        Query query = tenantCrudService.getEntityManager().createQuery("delete from CPDecisionBAROutputOverride where accomType.id in (:accomTypeIds) and arrivalDate >= :caughtUpDate");
        query.setParameter("accomTypeIds", accomTypeIds);
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        query.executeUpdate();
    }

    public boolean isMasterClassIncludedInList(List<AccomClass> accomClasses) {
        for (AccomClass accomClass : accomClasses) {
            if (accomClass.isMstClsDefined()) {
                return true;
            }
        }

        return false;
    }

    @SuppressWarnings("unchecked")
	public
    List<AccomClass> findAllAccomClassesForProperty(Integer propertyId) {
        return tenantCrudService.findByNamedQuery(AccomClass.BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public boolean hasArrivalDemandForecastOverrides(Integer accomClassId, Date caughtUpDate) {
        Query query = tenantCrudService.getEntityManager().createQuery("select count(*) from ArrivalDemandOverride where accomClassID = :accomClassId and statusId = :activeStatus and arrivalDate >= :caughtUpDate");
        query.setParameter(ACTIVE_STATUS, Constants.ACTIVE_STATUS_ID);
        query.setParameter(ACCOM_CLASS_ID, accomClassId);
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);

        Long count = (Long) query.getSingleResult();
        return count != null && count.longValue() > 0;
    }

    public boolean hasOccupancyDemandForecastOverrides(Integer accomClassId, Date caughtUpDate) {
        Query query = tenantCrudService.getEntityManager().createQuery("select count(*) from OccupancyDemandOverride where accomClassID = :accomClassId and statusId = :activeStatus and occupancyDate >= :caughtUpDate");
        query.setParameter(ACTIVE_STATUS, Constants.ACTIVE_STATUS_ID);
        query.setParameter(ACCOM_CLASS_ID, accomClassId);
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);

        Long count = (Long) query.getSingleResult();
        return count != null && count.longValue() > 0;
    }

    public boolean hasDecisionBAROutputOverrides(Integer accomClassId, Date caughtUpDate) {
        Query query = tenantCrudService.getEntityManager().createQuery("select count(*) from DecisionBAROutputOverride where accomClassId = :accomClassId and arrivalDate >= :caughtUpDate and newOverride != :override");
        query.setParameter(ACCOM_CLASS_ID, accomClassId);
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        query.setParameter(OVERRIDE, Constants.BARDECISIONOVERRIDE_PENDING);

        Long count = (Long) query.getSingleResult();
        return count != null && count.longValue() > 0;
    }

    public void invalidateArrivalDemandForecastOverrides(List<Integer> accomClassIds, Date caughtUpDate) {
        Query query = tenantCrudService.getEntityManager().createQuery("update ArrivalDemandOverride set statusId = :inActiveStatus where accomClassID in (:accomClassIds) and arrivalDate >= :caughtUpDate");
        query.setParameter(IN_ACTIVE_STATUS, Constants.INACTIVE_STATUS_ID);
        query.setParameter(ACCOM_CLASS_IDS, accomClassIds);
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        query.executeUpdate();
    }

    public void invalidateOccupancyDemandForecastOverrides(List<Integer> accomClassIds, Date caughtUpDate) {
        Query query = tenantCrudService.getEntityManager().createQuery("update OccupancyDemandOverride set statusId = :inActiveStatus where accomClassID in (:accomClassIds) and occupancyDate >= :caughtUpDate");
        query.setParameter(IN_ACTIVE_STATUS, Constants.INACTIVE_STATUS_ID);
        query.setParameter(ACCOM_CLASS_IDS, accomClassIds);
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        query.executeUpdate();
    }

    public void invalidateDecisionLRV(List<Integer> accomClassIds, Date caughtUpDate) {
        Query query = tenantCrudService.getEntityManager().createQuery("delete from LastRoomValue where accomClassID in (:accomClassIds) and occupancyDate > :caughtUpDate");
        query.setParameter(ACCOM_CLASS_IDS, accomClassIds);
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        query.executeUpdate();
    }

    public void invalidateDecisionBarOutput(List<Integer> accomClassIds, Date caughtUpDate) {
        Query query = tenantCrudService.getEntityManager().createQuery(
                "update DecisionBAROutput set " +
                        "override = :override, " +
                        "floorRateUnqualified = NULL, " +
                        "ceilingRateUnqualified = NULL " +
                        "where accomClassId in (:accomClassIds) and arrivalDate >= :caughtUpDate");
        query.setParameter(OVERRIDE, Constants.BARDECISIONOVERRIDE_PENDING);
        query.setParameter(ACCOM_CLASS_IDS, accomClassIds);
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        query.executeUpdate();
    }

    public void invalidateDecisionBAROutputOverride(List<Integer> accomClassIds, Date caughtUpDate) {
        Query query = tenantCrudService.getEntityManager().createQuery(
                "update DecisionBAROutputOverride set " +
                        "oldOverride = :override, " +
                        "newOverride = :override, " +
                        "newFloorRateUnqualified = NULL, " +
                        "newCeilingRateUnqualified = NULL " +
                        "where accomClassId in (:accomClassIds) and arrivalDate >= :caughtUpDate");
        query.setParameter(OVERRIDE, Constants.BARDECISIONOVERRIDE_PENDING);
        query.setParameter(ACCOM_CLASS_IDS, accomClassIds);
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        query.executeUpdate();
    }

    public void invalidatePACELRV(List<Integer> accomClassIds, Date caughtUpDate) {
        Query query = tenantCrudService.getEntityManager().createNativeQuery("delete from PACE_LRV where Accom_Class_ID in (:accomClassIds) and Occupancy_Dt >= :caughtUpDate");
        query.setParameter(ACCOM_CLASS_IDS, accomClassIds);
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        query.executeUpdate();
    }

    public void invalidatePACEBAROutput(List<Integer> accomClassIds, Date caughtUpDate) {
        Query query = tenantCrudService.getEntityManager().createQuery(
                "update PaceBAROutput set override = :override, floorRateUnqualified = NULL, ceilingRateUnqualified = NULL " +
                        " where accomClassId in (:accomClassIds) and arrivalDate >= :caughtUpDate");
        query.setParameter(OVERRIDE, Constants.BARDECISIONOVERRIDE_PENDING);
        query.setParameter(ACCOM_CLASS_IDS, accomClassIds);
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        query.executeUpdate();
    }

    public void invalidateCPPACEBAROutput(List<Integer> accomClassIds, Date caughtUpDate) {

        Query queryDifferential = tenantCrudService.getEntityManager().createNativeQuery("exec usp_invalidate_cp_overrides_for_accomClass_accomtypes_differential :override, :accomTypeIds, :accomClassIds");
        queryDifferential.setParameter(OVERRIDE, DecisionOverrideType.PENDING.name());
        queryDifferential.setParameter(ACCOM_TYPE_IDS, "-1");
        queryDifferential.setParameter(ACCOM_CLASS_IDS, accomClassIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        queryDifferential.executeUpdate();


    }

    public void invalidateAccomTypesOfCPPACEBAROutput(List<Integer> accomTypeIds, Date caughtUpDate) {

            Query queryDifferential = tenantCrudService.getEntityManager().createNativeQuery("exec usp_invalidate_cp_overrides_for_accomClass_accomtypes_differential :override,:accomTypeIds, :accomClassIds");
            queryDifferential.setParameter(OVERRIDE, DecisionOverrideType.PENDING.name());
            queryDifferential.setParameter(ACCOM_TYPE_IDS, accomTypeIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            queryDifferential.setParameter(ACCOM_CLASS_IDS, "-1");
            queryDifferential.executeUpdate();

    }

    public void invalidateWashForecastGroupFCSTOverrides(Integer propertyId, Date caughtUpDate) {
        // Set the override statuses to inactive
        Query washOverrideQuery = tenantCrudService.getEntityManager().createQuery("update WashOverride set statusId = :inActiveStatus where propertyID = :propertyId and occupancyDate >= :caughtUpDate");
        washOverrideQuery.setParameter(IN_ACTIVE_STATUS, Constants.INACTIVE_STATUS_ID);
        washOverrideQuery.setParameter(PROPERTY_ID, propertyId);
        washOverrideQuery.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        washOverrideQuery.executeUpdate();
    }

    public void invalidateOverbookingOverrides(Integer propertyId, List<Integer> accomTypeIds, Date caughtUpDate) {
        if (CollectionUtils.isEmpty(accomTypeIds)) {
            return;
        }
        // NOTE: We want to invalidate all overbooking overrides for the property, not just the ones
        Query accomOverbookingQuery = tenantCrudService.getEntityManager().createQuery("update DecisionOvrbkAccomOVR set statusId = :inActiveStatus where propertyId = :propertyId and occupancyDate >= :caughtUpDate and accomTypeId in :accomTypeIds");
        accomOverbookingQuery.setParameter(IN_ACTIVE_STATUS, Constants.INACTIVE_STATUS_ID);
        accomOverbookingQuery.setParameter(PROPERTY_ID, propertyId);
        accomOverbookingQuery.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        accomOverbookingQuery.setParameter(ACCOM_TYPE_IDS, accomTypeIds);
        accomOverbookingQuery.executeUpdate();
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    public void setConfigService(PacmanConfigParamsService configService) {
        this.configService = configService;
    }
}
