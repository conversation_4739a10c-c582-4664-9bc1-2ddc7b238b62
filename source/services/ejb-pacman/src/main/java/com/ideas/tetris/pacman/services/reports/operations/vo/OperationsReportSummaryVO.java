package com.ideas.tetris.pacman.services.reports.operations.vo;

import java.io.Serializable;
import java.util.Set;

public class OperationsReportSummaryVO implements Serializable {
    private Set<OperationsReportVO> specialEventsData;
    private Set<OperationsReportVO> PMSOnBooksData;
    private Set<OperationsReportVO> forecastData;
    private Set<OperationsReportVO> operationalForecastData;
    private String message;

    public Set<OperationsReportVO> getSpecialEventsData() {
        return specialEventsData;
    }

    public void setSpecialEventsData(Set<OperationsReportVO> specialEventsData) {
        this.specialEventsData = specialEventsData;
    }

    public Set<OperationsReportVO> getPMSOnBooksData() {
        return PMSOnBooksData;
    }

    public void setPMSOnBooksData(Set<OperationsReportVO> PMSOnBooksData) {
        this.PMSOnBooksData = PMSOnBooksData;
    }

    public Set<OperationsReportVO> getForecastData() {
        return forecastData;
    }

    public void setForecastData(Set<OperationsReportVO> forecastData) {
        this.forecastData = forecastData;
    }

    public Set<OperationsReportVO> getOperationalForecastData() {
        return operationalForecastData;
    }

    public void setOperationalForecastData(Set<OperationsReportVO> operationalForecastData) {
        this.operationalForecastData = operationalForecastData;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

}
