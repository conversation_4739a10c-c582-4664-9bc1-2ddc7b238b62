package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.threadpool.NamedDefaultThreadFactory;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.property.dto.AbstractExtractDetails;
import com.ideas.tetris.pacman.services.property.dto.ExtractDetails;
import com.ideas.tetris.pacman.services.property.dto.WebRateExtractDetails;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.log4j.Logger;

import javax.annotation.PostConstruct;


import java.text.MessageFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executors;

import static com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig.isExtractPathCleanUpFromCacheEnabled;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Component
@Transactional
public class ExtractDetailsCacheService {

    private static final Logger LOGGER = Logger.getLogger(ExtractDetailsCacheService.class);

    @Autowired
    PropertyExtractDetailsCache propertyExtractDetailsCache;

    @Autowired
    PropertyWebRateExtractDetailsCache propertyWebRateExtractDetailsCache;

    @Autowired
    ExtractMapperServiceLocal mapperService; // not private only to support unit tests

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;

    static boolean INVOKE_USING_SEPARATE_THREADS = true;

    @PostConstruct
    public void init() {
        if (SystemConfig.loadExtractCacheOnStartup()) {
            loadCaches();
        }
    }


    public void refresh() {
        propertyExtractDetailsCache.clear();
        propertyWebRateExtractDetailsCache.clear();
        loadCaches();
    }


    public ExtractDetails getExtractDetails(Integer propertyId) {
        return propertyExtractDetailsCache.get(propertyId);
    }


    public void putExtractDetails(Integer propertyId, ExtractDetails extractDetails) {
        clearExtractPathMaps(extractDetails);
        putOnCache(propertyId, extractDetails);
    }

    public void putOnCache(Integer propertyId, ExtractDetails extractDetails) {
        propertyExtractDetailsCache.put(propertyId, extractDetails);
    }


    public WebRateExtractDetails getWebRateExtractDetails(Integer propertyId) {
        WebRateExtractDetails details = propertyWebRateExtractDetailsCache.get(propertyId);
        if (details != null) {
            LOGGER.info(MessageFormat.format("WebRate Cache: details for property id - {0} :" +
                            "incomingFolder size - {1}, firstDate - {2}, lastDate - {3}, pathSize - {4}  ::" +
                            "archivedFolder size  - {5}, pathSize - {6} ",
                    propertyId, details.getNumberOfIncomingExtracts(), details.getFirstIncomingExtractDate(),
                    details.getLastIncomingExtractDate(), details.getIncomingExtracts().size(),
                    details.getNumberOfArchivedExtracts(), details.getArchivedExtracts().size()));
        } else {
            LOGGER.info(MessageFormat.format("WebRate Cache: details for property id - {0} : is null", propertyId));
        }
        return details;
    }


    public void putWebRateExtractDetails(Integer propertyId, WebRateExtractDetails webRateExtractDetails) {
        clearExtractPathMaps(webRateExtractDetails);
        propertyWebRateExtractDetailsCache.put(propertyId, webRateExtractDetails);
    }

    protected void clearExtractPathMaps(AbstractExtractDetails details) {
        if (isExtractPathCleanUpFromCacheEnabled()) {
            details.cleanExtractMaps();
        }
    }

    private void loadCaches() {
        // Get all properties
        List<Property> properties = globalCrudService.findAll(Property.class);

        setDefaultWorkContextIfEmpty();

        // Load the caches
        if (SystemConfig.loadExtractCacheInNewThreads()) {
            Executors.newSingleThreadExecutor(new NamedDefaultThreadFactory("extract-detail")).execute(() -> loadExtractDetailsCache(properties));
            Executors.newSingleThreadExecutor(new NamedDefaultThreadFactory("extract-detail-webrate")).execute(() -> loadWebRateExtractDetailsCache(properties));
        } else {
            loadExtractDetailsCache(properties);
            loadWebRateExtractDetailsCache(properties);
        }
    }

    private void setDefaultWorkContextIfEmpty() {
        WorkContextType workContext = PlatformThreadLocalContextHolder.getWorkContext();
        if (workContext == null) {
            PlatformThreadLocalContextHolder.setWorkContext(new WorkContextType());
        }
    }

    private void loadExtractDetailsCache(List<Property> properties) {
        LOGGER.info("Loading extract details cache...");
        long start = System.currentTimeMillis();
        Map<Integer, ExtractDetails> map = mapperService.mapExtractsOnDiskAllProperties(properties);
        Set<Integer> propertyIds = map.keySet();
        for (Integer propertyId : propertyIds) {
            ExtractDetails extractDetails = map.get(propertyId);
            setSrpAttributeDateAvailability(extractDetails);
            clearExtractPathMaps(extractDetails);
            putOnCache(propertyId, extractDetails);
        }
        LOGGER.info(String.format("Finished loading extract details cache...took %d ms", (System.currentTimeMillis() - start)));
    }

    private void loadWebRateExtractDetailsCache(List<Property> properties) {
        LOGGER.info("Loading webrate extract details cache...");
        long start = System.currentTimeMillis();
        Map<Integer, WebRateExtractDetails> map = mapperService.mapWebRateExtractsOnDiskAllProperties(properties);
        Set<Integer> propertyIds = map.keySet();
        for (Integer propertyId : propertyIds) {
            WebRateExtractDetails webRateExtractDetails = map.get(propertyId);
            clearExtractPathMaps(webRateExtractDetails);
            propertyWebRateExtractDetailsCache.put(propertyId, webRateExtractDetails);
        }
        LOGGER.info(String.format("Finished loading webrate extract details cache...took %d ms", (System.currentTimeMillis() - start)));
    }

    private void setSrpAttributeDateAvailability(ExtractDetails extractDetails) {
        Date srpAttributeDate = configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_SCHEDULED_EXTRACT_DATE);
        if (srpAttributeDate != null) {
            extractDetails.setSrpAttributeExtractDateAvailability(srpAttributeDate);
        }
    }

}
