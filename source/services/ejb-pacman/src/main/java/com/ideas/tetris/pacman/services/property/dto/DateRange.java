package com.ideas.tetris.pacman.services.property.dto;

import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;

import java.util.Date;

@Deprecated
public final class DateRange {
    private DateParameter startDate;
    private DateParameter endDate;

    public DateRange() {

    }

    public DateRange(DateParameter startDate, DateParameter endDate) {
        setStartDate(startDate);
        setEndDate(endDate);
    }

    public DateRange(Date startDate, Date endDate) {
        setStartDate(startDate);
        setEndDate(endDate);
    }

    public DateParameter getStartDate() {
        return startDate;
    }

    public void setStartDate(DateParameter startDate) {
        this.startDate = startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = DateParameter.fromDate(startDate);
    }

    public DateParameter getEndDate() {
        return endDate;
    }

    public void setEndDate(Date sendDate) {
        this.endDate = DateParameter.fromDate(sendDate);
    }

    public void setEndDate(DateParameter endDate) {
        this.endDate = endDate;
    }

    public boolean isEmpty() {
        return (startDate == null && endDate == null);
    }
}
