package com.ideas.tetris.pacman.services.reports.outputoverride.dto;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.annotations.ColumnHeader;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.datatypes.PropertyValueType;
import com.ideas.tetris.platform.common.rest.unmarshaller.DateTimeSerializer;

import java.math.BigDecimal;
import java.util.Date;

public class OutputOverrideInventoryLimitDTO {

    @ColumnHeader(titleKey = "common.propertyName", order = 1)
    private String propertyName;
    @ColumnHeader(titleKey = "report.dow", order = 2, type = PropertyValueType.class)
    private String dow;
    @ColumnHeader(titleKey = "occupancyDate", order = 3)
    private Date occupancyDate;
    @ColumnHeader(titleKey = "roomType", order = 4)
    private String accomClassName;
    @ColumnHeader(titleKey = "overrideCategory", order = 5, type = PropertyValueType.class)
    private String overrideCategory;
    @ColumnHeader(titleKey = "override.value", order = 6, useDashes = true)
    private BigDecimal overrideValue;
    @ColumnHeader(titleKey = "notes.label", order = 7)
    private String notes;
    @ColumnHeader(titleKey = "report.overrideLastModifiedOn", order = 8)
    private Date createDate;
    @ColumnHeader(titleKey = "report.overrideLastModifiedBy", order = 9)
    private String userName;
    private String userEmail;

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    @JsonGetter("accomTypeName")
    public String getAccomClassName() {
        return accomClassName;
    }

    public void setAccomClassName(String accomClassName) {
        this.accomClassName = accomClassName;
    }

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public String getDow() {
        return dow;
    }

    public void setDow(String dow) {
        this.dow = dow;
    }

    public BigDecimal getOverrideValue() {
        return overrideValue;
    }

    public void setOverrideValue(BigDecimal overrideValue) {
        this.overrideValue = overrideValue;
    }

    public String getOverrideCategory() {
        return overrideCategory;
    }

    public void setOverrideCategory(String overrideCategory) {
        this.overrideCategory = overrideCategory;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @JsonSerialize(using = DateTimeSerializer.class)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }
}
