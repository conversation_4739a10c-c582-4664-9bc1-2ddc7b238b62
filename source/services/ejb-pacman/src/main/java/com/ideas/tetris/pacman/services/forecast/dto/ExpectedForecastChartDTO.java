package com.ideas.tetris.pacman.services.forecast.dto;

import java.util.Date;
import java.util.List;
import java.util.Map;

public class ExpectedForecastChartDTO {
    private Date occupanyStartDate;
    private Date occupancyEndDate;
    private List<Date> occupanyDates;
    private Map<Date, Integer> actualOnBooks;
    private Map<Date, Integer> expectedOnbooks;
    private Map<Date, Integer> lowerConfidenceIntervals;
    private Map<Date, Integer> upperConfidenceIntervals;

    public Date getOccupanyStartDate() {
        return occupanyStartDate;
    }

    public void setOccupanyStartDate(Date occupanyStartDate) {
        this.occupanyStartDate = occupanyStartDate;
    }

    public Date getOccupancyEndDate() {
        return occupancyEndDate;
    }

    public void setOccupancyEndDate(Date occupancyEndDate) {
        this.occupancyEndDate = occupancyEndDate;
    }

    public List<Date> getOccupanyDates() {
        return occupanyDates;
    }

    public void setOccupanyDates(List<Date> occupanyDates) {
        this.occupanyDates = occupanyDates;
    }

    public Map<Date, Integer> getActualOnBooks() {
        return actualOnBooks;
    }

    public void setActualOnBooks(Map<Date, Integer> actualOnBooks) {
        this.actualOnBooks = actualOnBooks;
    }

    public Map<Date, Integer> getExpectedOnbooks() {
        return expectedOnbooks;
    }

    public void setExpectedOnbooks(Map<Date, Integer> expectedOnbooks) {
        this.expectedOnbooks = expectedOnbooks;
    }

    public Map<Date, Integer> getLowerConfidenceIntervals() {
        return lowerConfidenceIntervals;
    }

    public void setLowerConfidenceIntervals(Map<Date, Integer> lowerConfidenceIntervals) {
        this.lowerConfidenceIntervals = lowerConfidenceIntervals;
    }

    public Map<Date, Integer> getUpperConfidenceIntervals() {
        return upperConfidenceIntervals;
    }

    public void setUpperConfidenceIntervals(Map<Date, Integer> upperConfidenceIntervals) {
        this.upperConfidenceIntervals = upperConfidenceIntervals;
    }
}
