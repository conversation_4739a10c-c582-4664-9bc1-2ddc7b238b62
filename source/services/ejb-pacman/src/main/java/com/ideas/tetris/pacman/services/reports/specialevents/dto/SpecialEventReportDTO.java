package com.ideas.tetris.pacman.services.reports.specialevents.dto;

import com.ideas.tetris.pacman.services.scheduledreport.reportapi.annotations.ColumnHeader;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.datatypes.PropertyValueType;

import java.util.Date;

public class SpecialEventReportDTO {
    @ColumnHeader(titleKey = "special.event.name", order = 1)
    private String name;
    @ColumnHeader(titleKey = "description", order = 2)
    private String description;
    @ColumnHeader(titleKey = "preDays", order = 3, showBlank = false)
    private Integer preEventDays;
    @ColumnHeader(titleKey = "common.dow", order = 4, type = PropertyValueType.class)
    private String startDateDOW;
    @ColumnHeader(titleKey = "startDate", order = 5)
    private Date startDate;
    @ColumnHeader(titleKey = "common.dow", order = 6, type = PropertyValueType.class)
    private String endDateDOW;
    @ColumnHeader(titleKey = "endDate", order = 7)
    private Date endDate;
    @ColumnHeader(titleKey = "postEventDaysLabel", order = 8, showBlank = false)
    private Integer postEventDays;
    @ColumnHeader(titleKey = "informationOnly", order = 9, type = PropertyValueType.class)
    private String informationOnlyEvent;
    @ColumnHeader(titleKey = "common.category", order = 10)
    private String category;
    @ColumnHeader(titleKey = "createdBy", order = 11)
    private String createdByUser;
    @ColumnHeader(titleKey = "report.createdOn", order = 12)
    private Date createdDate;
    @ColumnHeader(titleKey = "common.updatedBy", order = 13)
    private String updatedByUser;
    @ColumnHeader(titleKey = "updatedOn", order = 14)
    private Date updatedDate;

    public SpecialEventReportDTO() {
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public String getStartDateDOW() {
        return startDateDOW;
    }

    public String getEndDateDOW() {
        return endDateDOW;
    }

    public Date getStartDate() {
        return startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public String getInformationOnlyEvent() {
        return informationOnlyEvent;
    }

    public String getCategory() {
        return category;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setStartDateDOW(String startDateDOW) {
        this.startDateDOW = startDateDOW;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public void setEndDateDOW(String endDateDOW) {
        this.endDateDOW = endDateDOW;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public void setInformationOnlyEvent(String informationOnlyEvent) {
        this.informationOnlyEvent = informationOnlyEvent;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCreatedByUser() {
        return createdByUser;
    }

    public void setCreatedByUser(String createdByUser) {
        this.createdByUser = createdByUser;
    }

    public String getUpdatedByUser() {
        return updatedByUser;
    }

    public void setUpdatedByUser(String updatedByUser) {
        this.updatedByUser = updatedByUser;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public void setPreEventDays(Integer preEventDays) {
        this.preEventDays = preEventDays;
    }

    public void setPostEventDays(Integer postEventDays) {
        this.postEventDays = postEventDays;
    }

    public Integer getPreEventDays() {
        return preEventDays;
    }

    public Integer getPostEventDays() {
        return postEventDays;
    }
}
