package com.ideas.tetris.pacman.services.reports.pricing;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.pricing.dto.PricingByDay;
import com.ideas.tetris.pacman.services.reports.pricing.dto.PricingByLos;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ReportParamEnum;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PricingService {

    private static final Logger LOGGER = Logger.getLogger(PricingService.class.getName());

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    private CrudService getCrudServiceBean() {
        return crudService;
    }

    public void setCrudServiceBean(CrudService crudService) {
        this.crudService = crudService;
    }


    private QueryParameter getQueryParameters(LocalDate startDate, LocalDate endDate, String accomClassIdStr, Integer isRollingDate, String rollingStartDate, String rollingEndDate) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("start_date", java.sql.Date.valueOf(startDate))
                .and("end_date", java.sql.Date.valueOf(endDate))
                .and("RoomClasses", accomClassIdStr)
                .and("isRollingDate", isRollingDate)
                .and("rolling_start_date", rollingStartDate)
                .and("rolling_end_date", rollingEndDate);

        return queryParameters;
    }

    @SuppressWarnings("squid:S3776")
    public List<PricingByDay> getPricingByDayData(LocalDate startDate, LocalDate endDate, String accomClassIdStr, List<Integer> competitorIds, Integer isRollingDate, String rollingStartDate, String rollingEndDate, final boolean isPhysicalCapacityEnabled) {

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = getQueryParameters(startDate, endDate, accomClassIdStr, isRollingDate, rollingStartDate, rollingEndDate);

        StringBuilder compIdPramString = new StringBuilder();
        String compIdAttribute;

        if (competitorIds != null) {
            for (int i = 0; i < 15; i++) {
                compIdAttribute = "comp_id_" + (i + 1);
                compIdPramString.append(":").append(compIdAttribute).append(",");
                queryParameters.and(compIdAttribute, competitorIds.get(i) == null ? -1 : competitorIds.get(i));
            }
        }

        try {

            boolean isContinuousPricing = configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value());

            String function = isContinuousPricing ? "dbo.ufn_get_pricing_by_day_cp_report" : "dbo.ufn_get_pricing_by_day_report";

            String sql = "select * from " + function + "(:property_id, :start_date, :end_date, " + compIdPramString + ":RoomClasses, :isRollingDate, :rolling_start_date, :rolling_end_date)  order by Arrival_DT";
            if (isContinuousPricing) {
                sql = sql + ", Accom_Class_Name, Accom_Type_Name";
            }
            return getCrudServiceBean().findByNativeQuery(sql,
                    queryParameters.parameters(), new RowMapper<PricingByDay>() {
                        @Override
                        public PricingByDay mapRow(Object[] row) {
                            PricingByDay data = new PricingByDay();

                            data.setArrivalDate((Date) row[0]);
                            data.setOverride((String) row[1]);
                            data.setDow((String) row[2]);
                            data.setRoomSold((BigDecimal) row[3]);
                            data.setOutOfOrder((BigDecimal) row[4]);
                            data.setOccupancyForecast((BigDecimal) row[5]);
                            if (isPhysicalCapacityEnabled) {
                                data.setOccupancyForecastPercent((BigDecimal) row[48]);
                                data.setPropertyOccupancyForecastPercent((BigDecimal) row[49]);
                            } else {
                                data.setOccupancyForecastPercent((BigDecimal) row[6]);
                                data.setPropertyOccupancyForecastPercent((BigDecimal) row[8]);
                            }

                            data.setPropertyOccupancyForecast((BigDecimal) row[7]);
                            data.setRateCodeName((String) row[9]);
                            data.setPrice((BigDecimal) row[10]);
                            data.setAccomClassName((String) row[11]);
                            data.setNotes((String) row[12]);
                            data.setCompRate1((BigDecimal) row[13]);
                            data.setCompName1((String) row[14]);
                            data.setCompRate2((BigDecimal) row[15]);
                            data.setCompName2((String) row[16]);
                            data.setCompRate3((BigDecimal) row[17]);
                            data.setCompName3((String) row[18]);
                            data.setCompRate4((BigDecimal) row[19]);
                            data.setCompName4((String) row[20]);
                            data.setCompRate5((BigDecimal) row[21]);
                            data.setCompName5((String) row[22]);
                            data.setCompRate1((BigDecimal) row[13]);
                            data.setCompName1((String) row[14]);

                            data.setCompRate2((BigDecimal) row[15]);
                            data.setCompName2((String) row[16]);

                            data.setCompRate3((BigDecimal) row[17]);
                            data.setCompName3((String) row[18]);

                            data.setCompRate4((BigDecimal) row[19]);
                            data.setCompName4((String) row[20]);

                            data.setCompRate5((BigDecimal) row[21]);
                            data.setCompName5((String) row[22]);

                            data.setCompRate6((BigDecimal) row[23]);
                            data.setCompName6((String) row[24]);

                            data.setCompRate7((BigDecimal) row[25]);
                            data.setCompName7((String) row[26]);

                            data.setCompRate8((BigDecimal) row[27]);
                            data.setCompName8((String) row[28]);

                            data.setCompRate9((BigDecimal) row[29]);
                            data.setCompName9((String) row[30]);

                            data.setCompRate10((BigDecimal) row[31]);
                            data.setCompName10((String) row[32]);

                            data.setCompRate11((BigDecimal) row[33]);
                            data.setCompName11((String) row[34]);

                            data.setCompRate12((BigDecimal) row[35]);
                            data.setCompName12((String) row[36]);

                            data.setCompRate13((BigDecimal) row[37]);
                            data.setCompName13((String) row[38]);

                            data.setCompRate14((BigDecimal) row[39]);
                            data.setCompName14((String) row[40]);

                            data.setCompRate15((BigDecimal) row[41]);
                            data.setCompName15((String) row[42]);

                            data.setLRV((BigDecimal) row[43]);
                            data.setTotalRooms((BigDecimal) row[44]);
                            data.setUserId((Integer) row[45]);
                            if (row[46] != null) {
                                data.setCreateDateTime(DateUtil.convertJavaUtilDateToZonedDateTime((Date) row[46]));
                            }
                            data.setUserName((String) row[47]);

                            if (isContinuousPricing) {
                                data.setAccomTypeName((String) row[50]);
                            }

                            return data;
                        }
                    });
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<PricingByDay>();
        }
    }

    @SuppressWarnings("squid:S3776")
    public List<PricingByLos> getPricingByLosData(LocalDate startDate, LocalDate endDate, String accomClassIdStr, List<Integer> competitorIds, Integer isRollingDate, String rollingStartDate, String rollingEndDate, final boolean isPhysicalCapacityEnabled) {

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = getQueryParameters(startDate, endDate, accomClassIdStr, isRollingDate, rollingStartDate, rollingEndDate);

        final boolean isLRAEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LRAENABLED.value());
        final boolean isSingleBarDecisionEnabled = configParamsService.getBooleanParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value());
        final String isLV0ClosedStr = configParamsService.getParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value());
        final boolean isHiltonSpecificEnabled = Boolean.parseBoolean(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.DISPLAY_OPTIMIZATION_SETTINGS_TAB.value()));
        final boolean isCustomPricingReportEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.CUSTOM_PRICING_REPORT_ENABLED);
        queryParameters.and("isLV0Closed", isLV0ClosedStr)
                .and(ReportParamEnum.isCustomPricingReportEnabled.getCorrespondingAttributeName(), isCustomPricingReportEnabled);

        StringBuilder compIdPramString = new StringBuilder();
        String compIdAttribute;

        if (competitorIds != null) {
            for (int i = 0; i < 15; i++) {
                compIdAttribute = "comp_id_" + (i + 1);
                compIdPramString.append(":").append(compIdAttribute).append(",");
                queryParameters.and(compIdAttribute, competitorIds.get(i) == null ? -1 : competitorIds.get(i));
            }
        }

        try {
            List<PricingByLos> pricingByLosList = getCrudServiceBean().findByNativeQuery("exec dbo.usp_get_pricing_by_los_report :property_id, :start_date, :end_date, " + compIdPramString + ":RoomClasses, :isRollingDate, :rolling_start_date, :rolling_end_date, :isLV0Closed,:isCustomPricingReportEnabled",
                    queryParameters.parameters(), new RowMapper<PricingByLos>() {
                        @Override
                        public PricingByLos mapRow(Object[] row) {
                            PricingByLos data = new PricingByLos();

                            data.setArrivalDate((Date) row[0]);
                            data.setDow((String) row[1]);
                            data.setRoomSold((BigDecimal) row[2]);
                            data.setOutOfOrder((BigDecimal) row[3]);
                            data.setOccupancyForecast((BigDecimal) row[4]);
                            if (isPhysicalCapacityEnabled) {
                                data.setOccupancyForecastPercent((BigDecimal) row[76]);
                                data.setPropertyOccupancyForecastPercent((BigDecimal) row[77]);
                            } else {
                                data.setOccupancyForecastPercent((BigDecimal) row[5]);
                                data.setPropertyOccupancyForecastPercent((BigDecimal) row[7]);
                            }
                            data.setPropertyOccupancyForecast((BigDecimal) row[6]);
                            data.setAccomClassName((String) row[8]);
                            data.setNotes((String) row[9]);

                            data.setCompRate1((BigDecimal) row[10]);
                            data.setCompName1((String) row[11]);
                            data.setCompRate2((BigDecimal) row[12]);
                            data.setCompName2((String) row[13]);
                            data.setCompRate3((BigDecimal) row[14]);
                            data.setCompName3((String) row[15]);
                            data.setCompRate4((BigDecimal) row[16]);
                            data.setCompName4((String) row[17]);
                            data.setCompRate5((BigDecimal) row[18]);
                            data.setCompName5((String) row[19]);
                            data.setCompRate6((BigDecimal) row[20]);
                            data.setCompName6((String) row[21]);
                            data.setCompRate7((BigDecimal) row[22]);
                            data.setCompName7((String) row[23]);
                            data.setCompRate8((BigDecimal) row[24]);
                            data.setCompName8((String) row[25]);
                            data.setCompRate9((BigDecimal) row[26]);
                            data.setCompName9((String) row[27]);
                            data.setCompRate10((BigDecimal) row[28]);
                            data.setCompName10((String) row[29]);
                            data.setCompRate11((BigDecimal) row[30]);
                            data.setCompName11((String) row[31]);
                            data.setCompRate12((BigDecimal) row[32]);
                            data.setCompName12((String) row[33]);
                            data.setCompRate13((BigDecimal) row[34]);
                            data.setCompName13((String) row[35]);
                            data.setCompRate14((BigDecimal) row[36]);
                            data.setCompName14((String) row[37]);
                            data.setCompRate15((BigDecimal) row[38]);
                            data.setCompName15((String) row[39]);

                            data.setLRV((BigDecimal) row[40]);
                            data.setTotalRooms((BigDecimal) row[41]);
                            if (row[42] != null) {
                                data.setCreateDateTime(DateUtil.convertJavaUtilDateToZonedDateTime((Date) row[42]));
                            }
                            data.setUserName((String) row[43]);

                            data.setOverride1((String) row[44]);
                            data.setLOS1((Integer) row[45]);
                            data.setRateCodeName1((String) row[46]);
                            data.setLOSPrice1((BigDecimal) row[47]);
                            data.setOverride2((String) row[48]);
                            data.setLOS2((Integer) row[49]);
                            data.setRateCodeName2((String) row[50]);
                            data.setLOSPrice2((BigDecimal) row[51]);
                            data.setOverride3((String) row[52]);
                            data.setLOS3((Integer) row[53]);
                            data.setRateCodeName3((String) row[54]);
                            data.setLOSPrice3((BigDecimal) row[55]);
                            data.setOverride4((String) row[56]);
                            data.setLOS4((Integer) row[57]);
                            data.setRateCodeName4((String) row[58]);
                            data.setLOSPrice4((BigDecimal) row[59]);
                            data.setOverride5((String) row[60]);
                            data.setLOS5((Integer) row[61]);
                            data.setRateCodeName5((String) row[62]);
                            data.setLOSPrice5((BigDecimal) row[63]);
                            data.setOverride6((String) row[64]);
                            data.setLOS6((Integer) row[65]);
                            data.setRateCodeName6((String) row[66]);
                            data.setLOSPrice6((BigDecimal) row[67]);
                            data.setOverride7((String) row[68]);
                            data.setLOS7((Integer) row[69]);
                            data.setRateCodeName7((String) row[70]);
                            data.setLOSPrice7((BigDecimal) row[71]);
                            data.setOverride8((String) row[72]);
                            data.setLOS8((Integer) row[73]);
                            data.setRateCodeName8((String) row[74]);
                            data.setLOSPrice8((BigDecimal) row[75]);
                            data.setFloor_Ovr_Rate_Code_Name1((String) row[78]);
                            data.setFloor_Ovr_Rate_Code_Name2((String) row[79]);
                            data.setFloor_Ovr_Rate_Code_Name3((String) row[80]);
                            data.setFloor_Ovr_Rate_Code_Name4((String) row[81]);
                            data.setFloor_Ovr_Rate_Code_Name5((String) row[82]);
                            data.setFloor_Ovr_Rate_Code_Name6((String) row[83]);
                            data.setFloor_Ovr_Rate_Code_Name7((String) row[84]);
                            data.setFloor_Ovr_Rate_Code_Name8((String) row[85]);
                            data.setDecision_Reason_Type_ID1((Integer) row[86]);
                            data.setDecision_Reason_Type_ID2((Integer) row[87]);
                            data.setDecision_Reason_Type_ID3((Integer) row[88]);
                            data.setDecision_Reason_Type_ID4((Integer) row[89]);
                            data.setDecision_Reason_Type_ID5((Integer) row[90]);
                            data.setDecision_Reason_Type_ID6((Integer) row[91]);
                            data.setDecision_Reason_Type_ID7((Integer) row[92]);
                            data.setDecision_Reason_Type_ID8((Integer) row[93]);

                            data.setLV0_Closed_RoomTypes_LOS1(row[94] == null ? null : ((String) row[94]).trim());
                            data.setLV0_Closed_RoomTypes_LOS2(row[95] == null ? null : ((String) row[95]).trim());
                            data.setLV0_Closed_RoomTypes_LOS3(row[96] == null ? null : ((String) row[96]).trim());
                            data.setLV0_Closed_RoomTypes_LOS4(row[97] == null ? null : ((String) row[97]).trim());
                            data.setLV0_Closed_RoomTypes_LOS5(row[98] == null ? null : ((String) row[98]).trim());
                            data.setLV0_Closed_RoomTypes_LOS6(row[99] == null ? null : ((String) row[99]).trim());
                            data.setLV0_Closed_RoomTypes_LOS7(row[100] == null ? null : ((String) row[100]).trim());
                            data.setLV0_Closed_RoomTypes_LOS8(row[101] == null ? null : ((String) row[101]).trim());
                            return data;
                        }
                    });
            if (CollectionUtils.isNotEmpty(pricingByLosList)) {
                pricingByLosList.stream().forEach(pricingByLos -> prepareBarLOSDetailsFor(pricingByLos, isLRAEnabled, isSingleBarDecisionEnabled, isHiltonSpecificEnabled));
            }
            return pricingByLosList;
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<PricingByLos>();
        }
    }

    private void prepareBarLOSDetailsFor(PricingByLos pricingByLosDTO, boolean isLRAEnabled, boolean isSingleBarDecisionEnabled, boolean isHiltonSpecificEnabled) {
        pricingByLosDTO.setBarDetailsLOS1(prepareBarDetailsPerLOS(pricingByLosDTO.getRateCodeName1(), pricingByLosDTO.getLOSPrice1(), pricingByLosDTO.getOverride1(),
                pricingByLosDTO.getDecision_Reason_Type_ID1(), pricingByLosDTO.getFloor_Ovr_Rate_Code_Name1(), isLRAEnabled, isSingleBarDecisionEnabled, isHiltonSpecificEnabled));

        pricingByLosDTO.setBarDetailsLOS2(prepareBarDetailsPerLOS(pricingByLosDTO.getRateCodeName2(), pricingByLosDTO.getLOSPrice2(), pricingByLosDTO.getOverride2(),
                pricingByLosDTO.getDecision_Reason_Type_ID2(), pricingByLosDTO.getFloor_Ovr_Rate_Code_Name2(), isLRAEnabled, isSingleBarDecisionEnabled, isHiltonSpecificEnabled));

        pricingByLosDTO.setBarDetailsLOS3(prepareBarDetailsPerLOS(pricingByLosDTO.getRateCodeName3(), pricingByLosDTO.getLOSPrice3(), pricingByLosDTO.getOverride3(),
                pricingByLosDTO.getDecision_Reason_Type_ID3(), pricingByLosDTO.getFloor_Ovr_Rate_Code_Name3(), isLRAEnabled, isSingleBarDecisionEnabled, isHiltonSpecificEnabled));

        pricingByLosDTO.setBarDetailsLOS4(prepareBarDetailsPerLOS(pricingByLosDTO.getRateCodeName4(), pricingByLosDTO.getLOSPrice4(), pricingByLosDTO.getOverride4(),
                pricingByLosDTO.getDecision_Reason_Type_ID4(), pricingByLosDTO.getFloor_Ovr_Rate_Code_Name4(), isLRAEnabled, isSingleBarDecisionEnabled, isHiltonSpecificEnabled));

        pricingByLosDTO.setBarDetailsLOS5(prepareBarDetailsPerLOS(pricingByLosDTO.getRateCodeName5(), pricingByLosDTO.getLOSPrice5(), pricingByLosDTO.getOverride5(),
                pricingByLosDTO.getDecision_Reason_Type_ID5(), pricingByLosDTO.getFloor_Ovr_Rate_Code_Name5(), isLRAEnabled, isSingleBarDecisionEnabled, isHiltonSpecificEnabled));

        pricingByLosDTO.setBarDetailsLOS6(prepareBarDetailsPerLOS(pricingByLosDTO.getRateCodeName6(), pricingByLosDTO.getLOSPrice6(), pricingByLosDTO.getOverride6(),
                pricingByLosDTO.getDecision_Reason_Type_ID6(), pricingByLosDTO.getFloor_Ovr_Rate_Code_Name6(), isLRAEnabled, isSingleBarDecisionEnabled, isHiltonSpecificEnabled));

        pricingByLosDTO.setBarDetailsLOS7(prepareBarDetailsPerLOS(pricingByLosDTO.getRateCodeName7(), pricingByLosDTO.getLOSPrice7(), pricingByLosDTO.getOverride7(),
                pricingByLosDTO.getDecision_Reason_Type_ID7(), pricingByLosDTO.getFloor_Ovr_Rate_Code_Name7(), isLRAEnabled, isSingleBarDecisionEnabled, isHiltonSpecificEnabled));

        pricingByLosDTO.setBarDetailsLOS8(prepareBarDetailsPerLOS(pricingByLosDTO.getRateCodeName8(), pricingByLosDTO.getLOSPrice8(), pricingByLosDTO.getOverride8(),
                pricingByLosDTO.getDecision_Reason_Type_ID8(), pricingByLosDTO.getFloor_Ovr_Rate_Code_Name8(), isLRAEnabled, isSingleBarDecisionEnabled, isHiltonSpecificEnabled));
    }

    public String prepareBarDetailsPerLOS(String rateCode, BigDecimal price, String override, Integer overrideDecisionReasonType, String floorOverrideRateCode,
                                   boolean isLRAEnabled, boolean isSingleBarDecisionEnabled, boolean isHiltonSpecificEnabled) {
        if (rateCode == null) {
            return null;
        }

        final boolean isLV0RestrictedDueToLRV = (overrideDecisionReasonType == 2) && isSingleBarDecisionEnabled;
        final boolean isLV0RestrictedDueToLRA = (overrideDecisionReasonType == 6) && isLRAEnabled && isSingleBarDecisionEnabled;
        final boolean isOverrideTypeSpecific = StringUtils.equalsIgnoreCase(override, Constants.BARDECISIONOVERRIDE_USER);
        final boolean isOverrideTypeFloor = StringUtils.equalsIgnoreCase(override, Constants.BARDECISIONOVERRIDE_FLOOR);

        StringBuilder barDetailsBuilder = new StringBuilder(String.format("(%s) %s", rateCode, price.setScale(2, java.math.BigDecimal.ROUND_FLOOR)));

        if (isOverrideTypeSpecific) {
            barDetailsBuilder.append("\n").append("S");
        }

        String details = null;
        if (isHiltonSpecificEnabled) {
            if (isLV0RestrictedDueToLRV) { //R-LRV
                details = String.format("%s-%s", Constants.BARDECISIONOVERRIDE_INDICATOR_PENDING, Constants.LRV_IMPACTED_DECISION);
            } else if (isLV0RestrictedDueToLRA) { //R-LRA
                details = String.format("%s-%s", Constants.BARDECISIONOVERRIDE_INDICATOR_PENDING, Constants.LRA_IMPACTED_DECISION);
            } else {
                if (isOverrideTypeFloor && (floorOverrideRateCode != null)) {
                    details = String.format("%s-%s", Constants.BARDECISIONOVERRIDE_INDICATOR_FLOOR, floorOverrideRateCode);
                }
            }
        } else {
            if (isOverrideTypeFloor) {
                details = Constants.BARDECISIONOVERRIDE_INDICATOR_FLOOR;
            }
        }

        if (details != null) {
            return String.format("%s\n%s", barDetailsBuilder.toString(), details);
        } else {
            return barDetailsBuilder.toString();
        }
    }

    public void setConfigParamsService(PacmanConfigParamsService configParamsService) {
        this.configParamsService = configParamsService;
    }

}
