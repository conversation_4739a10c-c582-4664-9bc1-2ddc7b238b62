package com.ideas.tetris.pacman.services.purge;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.purge.dto.TablePurgeStrategyDto;
import com.ideas.tetris.pacman.util.CollectionUtils;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.COMMA;
import static com.ideas.tetris.pacman.services.informationmanager.dto.AlertCategory.*;
import static com.ideas.tetris.pacman.services.purge.PurgeConstants.*;
import static org.apache.commons.collections.CollectionUtils.isEmpty;

@Component
@Transactional
public class WhereClauseBuilder {
    private static final String ALL_NOTIFICATION = "(type.Alert_Category='Exception' AND Occupancy_Date < " + PLACEHOLDER_DATE_ADD_FUNCTION + ")";
    private static final String ALL_EXCEPTIONS = "(type.Alert_Category='System Exception' AND Occupancy_Date < " + PLACEHOLDER_DATE_ADD_FUNCTION + ")";
    private static final String RESOLVED_ALERT_CONDITION = "(type.Alert_Category='Alert' AND Created_Date < " + PLACEHOLDER_DATE_ADD_FUNCTION + " AND Info_Mgr_Status_Id=4)";

    private static final String DATEADD_FUNCTION = "DATEADD(yy, -2, '" + PLACEHOLDER_CAUGHT_UP_DATE + "')";
    private static final Logger LOGGER = Logger.getLogger(WhereClauseBuilder.class);

    static final String DATA_LOAD_METADATA_ID_QUERY = " SELECT distinct(Data_Load_Metadata_ID) " +
            " FROM " +
            "   (SELECT dlm.correlation_id, " +
            "           count(dlm.Correlation_ID) AS countDlm " +
            "    FROM [opera].Data_Load_Metadata dlm " +
            "    GROUP BY dlm.correlation_id " +
            "    HAVING count(dlm.Correlation_ID)= 14 ) D " +
            " INNER JOIN opera.data_load_metadata d1 ON D.Correlation_ID = d1.Correlation_ID " +
            " INNER JOIN dbo.File_Metadata fm1 ON d1.Correlation_ID = fm1.File_Location " +
            " WHERE SnapShot_DT < :snapshotDt\n" +
            "  AND fm1.Record_Type_id = " + RecordType.T2SNAP_RECORD_TYPE_ID +
            "  AND SnapShot_DT < " +
            "     (SELECT DATEADD(DAY, %d, min(SnapShot_DT)) AS min_snapshot_DT " +
            "      FROM [opera].Data_Load_Metadata dlm " +
            "      JOIN [dbo].[File_Metadata] fm ON dlm.Correlation_ID = fm.File_Location " +
            "      WHERE SnapShot_DT < :snapshotDt   and Incoming_File_Type_Code='YC')";
    public static final String DECISION = "DECISION";
    public static final String OPERA = "OPERA";
    public static final String TENANT = "TENANT";
    public static final String INFO_MGR = "INFO_MGR";

    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;
    @Autowired
    DateService dateService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    public WhereClauseBuilder() {
        super();
    }

    @VisibleForTesting
	public
    WhereClauseBuilder(CrudService crudService, DateService dateService, PacmanConfigParamsService configParamsService) {
        this.crudService = crudService;
        this.dateService = dateService;
        this.pacmanConfigParamsService = configParamsService;
    }

    @Transactional(propagation = Propagation.NEVER)
    public String createWhereClause(Integer propertyId, Purgable purgable, TablePurgeStrategyDto tablePurgeStrategyDto) {

        if (purgable instanceof DecisionPurgeEnum) {
            return createDecisionWhereClause((DecisionPurgeEnum) purgable);
        } else if (purgable instanceof OperaPurgeEnum) {
            return createOperaWhereClause((OperaPurgeEnum) purgable);
        } else if (purgable instanceof TenantPurgeEnum) {
            return createTenantWhereClause(propertyId, (TenantPurgeEnum) purgable, tablePurgeStrategyDto);
        } else if (purgable instanceof InfoMgrPurgeEnum) {
            return createInfoMgrWhereClause((InfoMgrPurgeEnum) purgable, tablePurgeStrategyDto);
        } else {
            throw new IllegalArgumentException("Unexpected purgable: " + purgable);
        }
    }

    @Transactional(propagation = Propagation.NEVER)
    public String createWhereClauseForDeletion(Integer propertyId,TablePurgeStrategyDto tablePurgeStrategyDto) {

        if (DECISION.equalsIgnoreCase(tablePurgeStrategyDto.getCategory())) {
            return createWhereClauseForDecision(tablePurgeStrategyDto);
        } else if (OPERA.equalsIgnoreCase(tablePurgeStrategyDto.getCategory())) {
            return createWhereClauseForOpera(tablePurgeStrategyDto);
        } else if (TENANT.equalsIgnoreCase(tablePurgeStrategyDto.getCategory())) {
            return createWhereClauseForTenant(propertyId,tablePurgeStrategyDto);
        } else if (INFO_MGR.equalsIgnoreCase(tablePurgeStrategyDto.getCategory())) {
            return createWhereClauseForInfoMgr(tablePurgeStrategyDto);
        } else {
            throw new IllegalArgumentException("Unexpected purgable: " + tablePurgeStrategyDto.getTenantPurgeEnumName());
        }
    }

    private String createWhereClauseForDecision(TablePurgeStrategyDto purgeDto) {
        int days = -(365 + dateService.getForecastWindowOffsetBDE());
        String caughtUpDate = DateUtil.formatDate(dateService.getCaughtUpLocalDate().toDate(), DateUtil.DEFAULT_DATE_FORMAT);

        return purgeDto.getWhereClauseTemplate()
                .replace("%s",Integer.toString(days))
                .replaceFirst(PLACEHOLDER_PROPERTY_ID_CONDITIONAL, "").replaceFirst(PLACEHOLDER_FIELD_TO_COMPARE, purgeDto.getFieldToCompare())
                .replaceFirst(PLACEHOLDER_CAUGHT_UP_DATE, caughtUpDate)
                .replaceFirst(PLACEHOLDER_MIN_DATE, getMinDate(purgeDto));
    }
    private String createWhereClauseForTenant(Integer propertyId, TablePurgeStrategyDto purgeDto) {
        String caughtUpDate = DateUtil.formatDate(dateService.getCaughtUpLocalDate().toDate(), DateUtil.DEFAULT_DATE_FORMAT);
        StringBuilder whereClause = new StringBuilder(purgeDto.getWhereClauseTemplate());

        replaceDateAddFunctionWithCriteria(purgeDto, whereClause);
        return whereClause.toString()
                .replaceAll(PLACEHOLDER_PROPERTY_ID_CONDITIONAL, createPropertyIdConditional(propertyId))
                .replaceAll(PLACEHOLDER_FIELD_TO_COMPARE, purgeDto.getFieldToCompare())
                .replaceAll(PLACEHOLDER_CAUGHT_UP_DATE, caughtUpDate)
                .replaceAll(PLACEHOLDER_MIN_DATE, getMinDate(purgeDto));
    }
    private String createWhereClauseForOpera(TablePurgeStrategyDto purgeDto) {
        LocalDate businessDate = dateService.getCaughtUpLocalDate().minusDays(SystemConfig.getPurgeOperaHistoryJobDaysToDelete());
        List<Integer> dataLoadMetaIds = findDataLoadMetadataIdsBefore(businessDate);
        List<String> strings = dataLoadMetaIds.stream().map(Object::toString).collect(Collectors.toList());
        String ids = dataLoadMetaIds.isEmpty() ? EMPTY : String.join(COMMA, strings);

        return purgeDto.getWhereClauseTemplate()
                .replaceAll(PLACEHOLDER_TABLE_NAME, purgeDto.getTableToCompare())
                .replaceAll(PLACEHOLDER_FIELD_TO_COMPARE, purgeDto.getFieldToCompare())
                .replaceAll(PLACEHOLDER_FILE_METADATA_IDS, ids);
    }
    private String createWhereClauseForInfoMgr(TablePurgeStrategyDto purgeDto) {
        StringBuilder dateAddWhereClause = new StringBuilder(DATEADD_FUNCTION);

        if (purgeDto != null) {
            replaceDateAddFunctionWithCriteria(purgeDto, dateAddWhereClause);
        }
        java.time.LocalDate businessDate = JavaLocalDateUtils.toJavaLocalDate(dateService.getCaughtUpLocalDate());
        String instanceIdQuery = getInstanceIdsToBePurged()
                .replace(PLACEHOLDER_DATE_ADD_FUNCTION, dateAddWhereClause)
                .replace(PLACEHOLDER_CAUGHT_UP_DATE, businessDate.toString());
        if (instanceIdQuery.isEmpty()) {
            return StringUtils.EMPTY;
        }
        List<Map<Integer, Integer>> instanceIds = crudService.findByNativeQuery(instanceIdQuery, null, row -> Map.of((Integer) row[0], (Integer) row[1]));
        String ids = getPurgeIds(purgeDto,instanceIds);

        // delete script allows max 8000 chars so considering Added 7880 constant for instance ids
        int endIndex = Math.min(ids.length(), MAX_CHARS_FOR_WHERE_CLAUSE);
        if (endIndex != ids.length()) {
            ids = StringUtils.substringBeforeLast(ids.substring(0, endIndex), COMMA);
        }

        return purgeDto.getWhereClauseTemplate()
                .replaceAll(PLACEHOLDER_TABLE_NAME, purgeDto.getTableToCompare())
                .replaceAll(PLACEHOLDER_FIELD_TO_COMPARE, purgeDto.getFieldToCompare())
                .replaceAll(PLACEHOLDER_INSTANCE_IDS, ids);
    }

    private void replaceDateAddFunctionWithCriteria(TablePurgeStrategyDto tablePurgeStrategyDto, StringBuilder whereClause) {
        Pattern pattern = Pattern.compile("DATEADD\\((YY|dd){1},[\\s]*%s,[\\s]*'[%a-zA-Z_]+'\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(whereClause);
        if (matcher.find()) {
            String dateFunction = matcher.group();
            int columnStartIndex = dateFunction.lastIndexOf(',') + 1;
            int columnEndIndex = dateFunction.lastIndexOf(')');
            String columnName = dateFunction.substring(columnStartIndex, columnEndIndex);
            String dateFunctionFromDto = "DATEADD(dd,-" +
                    tablePurgeStrategyDto.getNumberOfDaysToPersist() +
                    "," + columnName +
                    ")";
            whereClause.replace(matcher.start(), matcher.end(), dateFunctionFromDto);
        }
    }
    private String createDecisionWhereClause(DecisionPurgeEnum purgePolicy) {
        int days = -(365 + dateService.getForecastWindowOffsetBDE());
        String caughtUpDate = DateUtil.formatDate(dateService.getCaughtUpLocalDate().toDate(), DateUtil.DEFAULT_DATE_FORMAT);

        return PurgeConstants.day(days)
                .replaceFirst(PLACEHOLDER_PROPERTY_ID_CONDITIONAL, "").replaceFirst(PLACEHOLDER_FIELD_TO_COMPARE, purgePolicy.getFieldToCompare())
                .replaceFirst(PLACEHOLDER_CAUGHT_UP_DATE, caughtUpDate)
                .replaceFirst(PLACEHOLDER_MIN_DATE, getMinDate(purgePolicy));
    }

    private String createOperaWhereClause(OperaPurgeEnum purgePolicy) {
        LocalDate businessDate = dateService.getCaughtUpLocalDate().minusDays(SystemConfig.getPurgeOperaHistoryJobDaysToDelete());
        List<Integer> dataLoadMetaIds = findDataLoadMetadataIdsBefore(businessDate);
        List<String> strings = dataLoadMetaIds.stream().map(Object::toString).collect(Collectors.toList());
        String ids = dataLoadMetaIds.isEmpty() ? EMPTY : String.join(COMMA, strings);

        return purgePolicy.getWhereClauseTemplate()
                .replaceAll(PLACEHOLDER_TABLE_NAME, purgePolicy.getTableToCompare())
                .replaceAll(PLACEHOLDER_FIELD_TO_COMPARE, purgePolicy.getFieldToCompare())
                .replaceAll(PLACEHOLDER_FILE_METADATA_IDS, ids);
    }

    private String createTenantWhereClause(Integer propertyId, TenantPurgeEnum purgePolicy, TablePurgeStrategyDto tablePurgeStrategyDto) {
        String caughtUpDate = DateUtil.formatDate(dateService.getCaughtUpLocalDate().toDate(), DateUtil.DEFAULT_DATE_FORMAT);
        StringBuilder whereClause = new StringBuilder(purgePolicy.getWhereClauseTemplate());

        if (tablePurgeStrategyDto != null) {
            replaceDateAddFunctionWithCriteriaFromDto(tablePurgeStrategyDto, whereClause);
        }
        return whereClause.toString()
                .replaceAll(PLACEHOLDER_PROPERTY_ID_CONDITIONAL, createPropertyIdConditional(propertyId))
                .replaceAll(PLACEHOLDER_FIELD_TO_COMPARE, purgePolicy.getFieldToCompare())
                .replaceAll(PLACEHOLDER_CAUGHT_UP_DATE, caughtUpDate)
                .replaceAll(PLACEHOLDER_MIN_DATE, getMinDate(purgePolicy));
    }

    private String createInfoMgrWhereClause(InfoMgrPurgeEnum purgePolicy, TablePurgeStrategyDto tablePurgeStrategyDto) {
        StringBuilder dateAddWhereClause = new StringBuilder(DATEADD_FUNCTION);

        if (tablePurgeStrategyDto != null) {
            replaceDateAddFunctionWithCriteriaFromDto(tablePurgeStrategyDto, dateAddWhereClause);
        }
        java.time.LocalDate businessDate = JavaLocalDateUtils.toJavaLocalDate(dateService.getCaughtUpLocalDate());
        String instanceIdQuery = getInstanceIdsToBePurged()
                .replace(PLACEHOLDER_DATE_ADD_FUNCTION, dateAddWhereClause)
                .replace(PLACEHOLDER_CAUGHT_UP_DATE, businessDate.toString());
        if (instanceIdQuery.isEmpty()) {
            return StringUtils.EMPTY;
        }
        List<Map<Integer, Integer>> instanceIds = crudService.findByNativeQuery(instanceIdQuery, null, row -> Map.of((Integer) row[0], (Integer) row[1]));
        String ids = getIdsToPurge(purgePolicy, instanceIds);

        // delete script allows max 8000 chars so considering Added 7880 constant for instance ids
        int endIndex = Math.min(ids.length(), MAX_CHARS_FOR_WHERE_CLAUSE);
        if (endIndex != ids.length()) {
            ids = StringUtils.substringBeforeLast(ids.substring(0, endIndex), COMMA);
        }

        return purgePolicy.getWhereClauseTemplate()
                .replaceAll(PLACEHOLDER_TABLE_NAME, purgePolicy.getTableToCompare())
                .replaceAll(PLACEHOLDER_FIELD_TO_COMPARE, purgePolicy.getFieldToCompare())
                .replaceAll(PLACEHOLDER_INSTANCE_IDS, ids);
    }

    private static String getIdsToPurge(InfoMgrPurgeEnum purgePolicy, List<Map<Integer, Integer>> dataToBeDeleted) {
        String ids;
        if (isEmpty(dataToBeDeleted)) {
            ids = EMPTY;
            LOGGER.info("There are no info_mgr_instances to delete for table " + purgePolicy.getTableToCompare());
        } else {
            List<Integer> instanceIdsToDelete = dataToBeDeleted.stream()
                    .flatMap(map -> map.keySet().stream())
                    .collect(Collectors.toList());
            Map<Integer, Long> infoMgrTypeToInstances = dataToBeDeleted.stream().
                    flatMap(map -> map.entrySet().stream()).
                    collect(Collectors.groupingBy(Map.Entry::getValue, Collectors.counting()));

            LOGGER.info("Purging table " + purgePolicy.getTableToCompare() + " [Info_Mgr_Instance_Id:Info_Mgr_Type_Id] are " + infoMgrTypeToInstances);
            ids = instanceIdsToDelete.stream().map(Object::toString).collect(Collectors.joining(COMMA));
        }
        return ids;
    }
    private String getPurgeIds(TablePurgeStrategyDto purgeDto, List<Map<Integer, Integer>> dataToBeDeleted) {
        String ids;
        if (isEmpty(dataToBeDeleted)) {
            ids = EMPTY;
            LOGGER.info("There are no info_mgr_instances to delete for table " + purgeDto.getTableToCompare());
        } else {
            List<Integer> instanceIdsToDelete = dataToBeDeleted.stream()
                    .flatMap(map -> map.keySet().stream())
                    .collect(Collectors.toList());
            Map<Integer, Long> infoMgrTypeToInstances = dataToBeDeleted.stream().
                    flatMap(map -> map.entrySet().stream()).
                    collect(Collectors.groupingBy(Map.Entry::getValue, Collectors.counting()));

            LOGGER.info("Purging table " + purgeDto.getTableToCompare() + " [Info_Mgr_Instance_Id:Info_Mgr_Type_Id] are " + infoMgrTypeToInstances);
            ids = instanceIdsToDelete.stream().map(Object::toString).collect(Collectors.joining(COMMA));
        }
        return ids;
    }

    private String getInstanceIdsToBePurged() {
        String excludedInfoMgrTypes = pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.EXCLUDE_INFO_MGR_TYPE_IDS_PURGING);
        String infoMgrAlertCategories = pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.INFO_MGR_ALERT_CATEGORIES_TO_PURGE);
        if (StringUtils.isAnyBlank(excludedInfoMgrTypes, infoMgrAlertCategories)) {
            LOGGER.info("InfoMgrTypeIdsExcludeInPurging and InfoMgrAlertCategoriesToPurge config parameter can not be blank");
            return StringUtils.EMPTY;
        }
        String whereClauseForAlertCategories = createWhereClauseForAlertCategories(infoMgrAlertCategories);
        if (StringUtils.isBlank(whereClauseForAlertCategories)) {
            LOGGER.info("InfoMgrAlertCategoriesToPurge config parameter must be Alert,Exception,Notification");
            return StringUtils.EMPTY;
        }
        return "SELECT TOP " + SystemConfig.getMaxInfoMgrRecordsToBeFetched() + " Info_Mgr_Instance_Id, imi.Info_Mgr_Type_Id" +
                " FROM Info_Mgr_Instance imi" +
                " inner join Info_Mgr_Type type on imi.Info_Mgr_Type_Id=type.Info_Mgr_Type_Id" +
                " WHERE imi.Info_Mgr_Type_Id not in (" + excludedInfoMgrTypes + ")" +
                whereClauseForAlertCategories +
                " ORDER BY imi.Info_Mgr_Instance_ID DESC";
    }

    private String createWhereClauseForAlertCategories(String alertCategories) {
        List<String> alertCategoryClauses = new ArrayList<>();
        List<String> alertCategoriesToBePurged = Arrays.asList(alertCategories.toUpperCase().replaceAll("\\s", "").split(COMMA));

        if (alertCategoriesToBePurged.contains(ALERT.toString())) {
            alertCategoryClauses.add(RESOLVED_ALERT_CONDITION);
        }
        if (alertCategoriesToBePurged.contains(NOTIFICATION.toString())) {
            alertCategoryClauses.add(ALL_NOTIFICATION);
        }
        if (alertCategoriesToBePurged.contains(EXCEPTION.toString())) {
            alertCategoryClauses.add(ALL_EXCEPTIONS);
        }

        if (!alertCategoryClauses.isEmpty()) {
            return " and (" + StringUtils.join(alertCategoryClauses, " or ") + ")";
        }
        return StringUtils.EMPTY;
    }

    private void replaceDateAddFunctionWithCriteriaFromDto(TablePurgeStrategyDto tablePurgeStrategyDto, StringBuilder whereClause) {
        Pattern pattern = Pattern.compile("DATEADD\\((YY|dd){1},[\\s]*-\\d+,[\\s]*'[%a-zA-Z_]+'\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(whereClause);
        if (matcher.find()) {
            String dateFunction = matcher.group();
            int columnStartIndex = dateFunction.lastIndexOf(',') + 1;
            int columnEndIndex = dateFunction.lastIndexOf(')');
            String columnName = dateFunction.substring(columnStartIndex, columnEndIndex);
            String dateFunctionFromDto = "DATEADD(dd,-" +
                    tablePurgeStrategyDto.getNumberOfDaysToPersist() +
                    "," + columnName +
                    ")";
            whereClause.replace(matcher.start(), matcher.end(), dateFunctionFromDto);
        }
    }

    @Transactional(propagation = Propagation.NEVER)
    public String getMinDate(Purgable purgePolicy) {
        String select = MIN_DATE_SELECT
                .replaceAll(PLACEHOLDER_FIELD_TO_COMPARE, purgePolicy.getFieldToCompare())
                .replaceAll(PLACEHOLDER_TABLE_NAME, purgePolicy.getTableToCompare())
                .replaceAll(PLACEHOLDER_DAYS_AT_A_TIME, Integer.toString(SystemConfig.getTenantPurgeMaxDaysToDelete()));
        java.util.Date date = crudService.findByNativeQuerySingleResult(select, new HashMap<>());
        return date == null ? Constants.EMPTY_STRING : String.format(MAX_DAYS_AT_A_TIME, purgePolicy.getFieldToCompare(), date);
    }
    @Transactional(propagation = Propagation.NEVER)
    public String getMinDate(TablePurgeStrategyDto purgePolicy) {
            String select = MIN_DATE_SELECT
                    .replaceAll(PLACEHOLDER_FIELD_TO_COMPARE, purgePolicy.getFieldToCompare())
                    .replaceAll(PLACEHOLDER_TABLE_NAME, purgePolicy.getTableToCompare())
                    .replaceAll(PLACEHOLDER_DAYS_AT_A_TIME, Integer.toString(SystemConfig.getTenantPurgeMaxDaysToDelete())).toUpperCase();
            java.util.Date date = crudService.findByNativeQuerySingleResult(select, new HashMap<>());
            return date == null ? Constants.EMPTY_STRING : String.format(MAX_DAYS_AT_A_TIME, purgePolicy.getFieldToCompare(), date);
    }

    @SuppressWarnings("unchecked")
    private List<Integer> findDataLoadMetadataIdsBefore(LocalDate businessDate) {
        final int tenantPurgeMaxDaysToDelete = SystemConfig.getTenantPurgeMaxDaysToDelete();
        Map<String, Object> params = QueryParameter.with("snapshotDt", businessDate.toDate()).parameters();
        return crudService.findByNativeQuery(String.format(DATA_LOAD_METADATA_ID_QUERY, tenantPurgeMaxDaysToDelete), params);
    }

    static public String createPropertyIdConditional(Integer propertyId) {
        return (propertyId != null) ? String.format("(PROPERTY_ID = %d) AND ", propertyId) : "";
    }
}
