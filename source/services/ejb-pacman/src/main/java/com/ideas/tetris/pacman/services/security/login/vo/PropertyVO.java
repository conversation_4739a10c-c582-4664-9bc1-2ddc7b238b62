package com.ideas.tetris.pacman.services.security.login.vo;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PropertyVO implements Serializable {

    private String propertyName;
    private Integer propertyID;
    private String systemMode;
    private String systemToday;
    private Integer capacity;
    private String highOccCutOff;
    private String lowOccCutOff;
    private Boolean isDefaultProperty;

    public PropertyVO() {
    }

    public String getPropertyName() {
        return propertyName;
    }

    public Integer getPropertyID() {
        return propertyID;
    }

    public Integer getCapacity() {
        return capacity;
    }

    public String getSystemMode() {
        return systemMode;
    }

    public String getSystemToday() {
        return systemToday;
    }

    public String getHighOccCutOff() {
        return highOccCutOff;
    }

    public String getLowOccCutOff() {
        return lowOccCutOff;
    }

    public Boolean isDefaultProperty() {
        return isDefaultProperty;
    }

    public void setDefaultProperty(Boolean defaultProperty) {
        isDefaultProperty = defaultProperty;
    }

    public PropertyVO changePropertyNameByCode(boolean displayPropertyByCode, String propertyCode, String propertyName) {
        this.propertyName = (displayPropertyByCode) ? propertyCode : propertyName;
        return this;
    }

    public PropertyVO(Property property, String systemToday, Integer capacity, String highOccCutOff, String lowOccCutOff) {
        this.propertyName = property.getName();
        this.propertyID = property.getId();
        this.systemMode = "FULLVERSION";
        this.systemToday = systemToday;
        this.capacity = capacity;
        this.highOccCutOff = highOccCutOff;
        this.lowOccCutOff = lowOccCutOff;
    }

    public PropertyVO(Property property, String systemToday, Integer capacity, String highOccCutOff, String lowOccCutOff, Boolean isDefaultProperty) {
        this.propertyName = property.getName();
        this.propertyID = property.getId();
        this.systemMode = "FULLVERSION";
        this.systemToday = systemToday;
        this.capacity = capacity;
        this.highOccCutOff = highOccCutOff;
        this.lowOccCutOff = lowOccCutOff;
        this.isDefaultProperty = isDefaultProperty;
    }

    public PropertyVO(Property property) {
        this.propertyName = property.getName();
        this.propertyID = property.getId();
    }
}
