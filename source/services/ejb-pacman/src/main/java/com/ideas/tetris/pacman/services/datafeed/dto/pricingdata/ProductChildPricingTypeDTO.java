package com.ideas.tetris.pacman.services.datafeed.dto.pricingdata;

import java.io.Serializable;

public class ProductChildPricingTypeDTO implements Serializable {

    private String productName;

    private String childPricingType;

    public ProductChildPricingTypeDTO(String productName, String childPricingType) {
        this.productName = productName;
        this.childPricingType = childPricingType;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getChildPricingType() {
        return childPricingType;
    }

    public void setChildPricingType(String childPricingType) {
        this.childPricingType = childPricingType;
    }

}
