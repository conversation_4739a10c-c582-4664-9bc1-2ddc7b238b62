package com.ideas.tetris.pacman.services.reports.specialevents;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.reports.specialevents.dto.SpecialEventReportDTO;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.JasperReportDataConverter;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.SpecialEventsReportCriteria;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportData;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.service.JasperReportService;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.joda.time.DateTime;

import javax.inject.Inject;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class SpecialEventsReportService extends JasperReportService<ScheduledReportData, SpecialEventsReportCriteria> {
    @Autowired
    SpecialEventService specialEventService;

    @Autowired
	private UserService userService;

    @Override
    protected JasperReportDataConverter getJasperReportDataConverter() {
        return null;
    }

    @Override
    protected ScheduledReportData retrieveData(ScheduledReport<SpecialEventsReportCriteria> scheduledReport) {
        populateReportCriteria(scheduledReport);
        SpecialEventsReportCriteria reportCriteria = scheduledReport.getReportCriteria();
        List<SpecialEventReportDTO> dataList = getSpecialEventsForDateRange(reportCriteria.getStartDate(),
                reportCriteria.getEndDate(), reportCriteria.isRollingDate(), reportCriteria.getRollingStartDate(),
                reportCriteria.getRollingEndDate(), reportCriteria.getEventType());
        ScheduledReportSheet sheet = new ScheduledReportSheet("special-events-report", dataList, SpecialEventReportDTO.class);
        List<ScheduledReportSheet> sheetList = new ArrayList<>(1);
        sheetList.add(sheet);
        return new ScheduledReportData("special-events-report", sheetList);
    }

    private void populateReportCriteria(ScheduledReport<SpecialEventsReportCriteria> scheduledReport) {
        if (null != scheduledReport) {
            Integer propertyId = Integer.valueOf(scheduledReport.getReportCriteria().getPropertyId().toString());
            Property property = getGlobalCrudService().find(Property.class, propertyId);
            TimeZone timeZone = getAlertService().getPropertyTimeZone(property);
            scheduledReport.getReportCriteria().setCreatedOn(ScheduledReportUtils.convertDateTimeToTimeZone(new DateTime(), timeZone));
        }
    }

    public List<SpecialEventReportDTO> getSpecialEventsForDateRange(Date startDate, Date endDate,
                                                                    boolean isRollingDate,
                                                                    String rollingStartDate,
                                                                    String rollingEndDate,
                                                                    SpecialDisplayEventType eventType) {
        List<PropertySpecialEventInstance> allSpecialEvents = specialEventService.getSpecialEventInstancesForADateRange(
                startDate, endDate, isRollingDate, rollingStartDate, rollingEndDate);
        List<SpecialEventReportDTO> dataList = getSpecialEventsToDisplay(eventType, allSpecialEvents);
        return dataList;
    }

    private List<SpecialEventReportDTO> getSpecialEventsToDisplay(SpecialDisplayEventType eventType, List<PropertySpecialEventInstance> allSpecialEvents) {
        List<SpecialEventReportDTO> dataList = new ArrayList<>();
        List<PropertySpecialEventInstance> specialEventsToDisplay = isShowInformationOnlyEventsRequested(eventType)
                ? getInformationOnlyEvents(allSpecialEvents) : allSpecialEvents;
        specialEventsToDisplay.forEach(psei -> {
            SpecialEventReportDTO wrapper = createDTO(psei);
            dataList.add(wrapper);
        });
        return dataList;
    }

    private boolean isShowInformationOnlyEventsRequested(SpecialDisplayEventType specialEventType) {
        return SpecialDisplayEventType.INFORMATION_ONLY.equals(specialEventType);
    }

    private List<PropertySpecialEventInstance> getInformationOnlyEvents(List<PropertySpecialEventInstance> specialEvents) {
        List<PropertySpecialEventInstance> informationOnlyEvents = specialEvents.stream()
                .filter(psei -> psei.getPropertySpecialEvent().isInfoOnly())
                .collect(Collectors.toList());
        return informationOnlyEvents;
    }

    private SpecialEventReportDTO createDTO(PropertySpecialEventInstance psei) {
        SpecialEventReportDTO specialEventReportDTO = new SpecialEventReportDTO();
        specialEventReportDTO.setName(psei.getEventNameWithInstanceName());
        specialEventReportDTO.setDescription(psei.getPropertySpecialEvent().getDescription());
        specialEventReportDTO.setPreEventDays(psei.getPreEventDays());
        specialEventReportDTO.setStartDateDOW(getDOW(psei.getStartDate()));
        specialEventReportDTO.setStartDate(psei.getStartDate());
        specialEventReportDTO.setEndDateDOW(getDOW(psei.getEndDate()));
        specialEventReportDTO.setEndDate(psei.getEndDate());
        specialEventReportDTO.setPostEventDays(psei.getPostEventDays());
        specialEventReportDTO.setInformationOnlyEvent(setInformationOnlyMarker(psei));
        specialEventReportDTO.setCategory(psei.getPropertySpecialEvent().getSpecialEventType().getName());
        specialEventReportDTO.setCreatedByUser(userService.getUserForProperty(psei.getCreatedByUserId()).getName());
        specialEventReportDTO.setUpdatedByUser(userService.getUserForProperty(psei.getLastUpdatedByUserId()).getName());
        specialEventReportDTO.setCreatedDate(LocalDateUtils.toDate(psei.getCreateDate()));
        specialEventReportDTO.setUpdatedDate(LocalDateUtils.toDate(psei.getLastUpdatedDate()));
        return specialEventReportDTO;
    }

    private String getDOW(Date date) {
        return DateTimeFormatter.ofPattern("EEEE").format(DateUtil.convertJavaUtilDateToLocalDate(date, true));
    }

    private String setInformationOnlyMarker(PropertySpecialEventInstance psei) {
        return Constants.SPECIAL_EVENT_INFORMATION_ONLY == psei.getPropertySpecialEvent().getImpactOnForcast()
                ? "common.yes" : "no";
    }
}
