package com.ideas.tetris.pacman.services.datafeed.service;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.grouppricing.AccomTypeMetrics;
import com.ideas.tetris.pacman.services.datafeed.dto.grouppricing.ArrivalDateMetrics;
import com.ideas.tetris.pacman.services.datafeed.dto.grouppricing.GroupEvaluationData;
import com.ideas.tetris.pacman.services.functionspace.evaluation.results.adjustedoutput.AdjustmentOutputUtil;
import com.ideas.tetris.pacman.services.functionspace.evaluation.results.adjustedoutput.UserAdjustmentArrivalDateWrapper;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingEvaluationMethod;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.ConferenceAndBanquetService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.GroupEvaluationArrivalDateDisplacementAndForecastDetail;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluation;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationArrivalDate;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationArrivalDateAccomClass;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationArrivalDateForecastGroupDateAC;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationArrivalDateGuestRoomRates;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationArrivalDateUserAdjustment;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationCost;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationCostType;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationFunctionSpaceConfAndBanq;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationRoomType;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.AuditableEntity;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.USE_NATIVE_QUERY_FOR_SAVED_GROUP_EVALUATION;
import static com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluation.FIND_GE_BY_IDS;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.ONE_HUNDRED;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.add;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.divide;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.multiply;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.negativeValueToZero;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.subtract;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.zeroIfNull;
import static java.math.BigDecimal.ZERO;
import static java.util.Collections.emptyList;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class GroupEvaluationDataFeedService {

    private static final String EVALUATION_METHOD_ROH = "ROH";
    private static final String EVALUATION_METHOD_ROOM_CLASS = "Room Class";
    private static final String GROUP_EVALUATION_IDS = "groupEvaluationIds";
    public static final String GROUP_EVALUATION_ARRIVAL_DATE_ID = "groupEvaluationArrivalDateIds";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;
    @GlobalCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("globalCrudServiceBean")
    private CrudService globalCrudService;

    @Autowired
    private GroupEvaluationService groupEvaluationService;
    @Autowired
    private PacmanConfigParamsService configParamsService;
    @Autowired
    private ConferenceAndBanquetService conferenceAndBanquetService;

    public List<GroupEvaluationData> getGroupEvaluationDataFeed(Date startDate, int startPosition, int size) {
        if (shouldUseNativeQueryForGroupEvaluationDatafeed()) {
            return getGroupEvaluationDataWithNativeQuery(startDate, startPosition, size);
        }
        return getGroupEvaluationData(startDate, startPosition, size);
    }

    private boolean shouldUseNativeQueryForGroupEvaluationDatafeed() {
        return configParamsService.getBooleanParameterValue(USE_NATIVE_QUERY_FOR_SAVED_GROUP_EVALUATION);
    }

    private List<GroupEvaluationData> getGroupEvaluationDataWithNativeQuery(Date startDate, int startPosition, int size) {
        final List<GroupEvaluationData> groupEvaluationsDataList = getGroupEvaluationsWithBaseData(startDate, startPosition, size);

        if (CollectionUtils.isEmpty(groupEvaluationsDataList)) {
            return Collections.emptyList();
        }
        final Set<Integer> groupEvaluationIds = groupEvaluationsDataList.stream().map(GroupEvaluationData::getUniqueGroupId).collect(Collectors.toSet());
        final Map<Integer, GroupEvaluation> groupEvaluationById = getGroupEvaluationByIds(groupEvaluationIds).stream()
                .collect(Collectors.toMap(GroupEvaluation::getId, Function.identity(), (grpEvaluation1, grpEvaluation2) -> grpEvaluation1));

        final List<ArrivalDateMetrics> displacedRooms = getArrivalDateMetricsFor(groupEvaluationIds, getNativeQueryToFetchDisplacedRooms());
        final Map<Integer, String> userIdToFullNameMap = groupEvaluationService.getUserIdToFullNameMapForGroupEvaluationIDs(getEvaluationUserIds(groupEvaluationIds));
        final List<AccomTypeMetrics> noOfRooms = getAccomTypeMetricsFor(groupEvaluationIds, getNativeQueryToFetchNoOfRooms());
        final List<ArrivalDateMetrics> incrementalRoomsByArrivalDate = getArrivalDateMetricsFor(groupEvaluationIds, getNativeQueryToFetchIncrementalRoomsByArrivalDate());
        final List<GroupEvaluationArrivalDate> groupEvaluationsArrivalDates = getGroupEvaluationsArrivalDatesFor(groupEvaluationIds);
        final Map<Integer, GroupEvaluation> groupEvaluationIDMap = getGroupEvaluationIDMap(groupEvaluationIds);
        final Map<Integer, List<GroupEvaluationArrivalDateAccomClass>> geadIDToGeadACMap = getgeadIDToGeadACMap(groupEvaluationIds);
        final List<AccomTypeMetrics> noOfNights = getAccomTypeMetricsFor(groupEvaluationIds, getNativeQueryToFetchNoOfNights());
        final List<ArrivalDateMetrics> totalCostCAndB = calculateGPConferenceAndBanquetTotalCost(groupEvaluationIds, groupEvaluationById);
        final List<ArrivalDateMetrics> totalCost = getArrivalDateMetricsFor(groupEvaluationIds, getNativeQueryToFetchTotalCost());
        final List<ArrivalDateMetrics> grossRevenueTotal = getArrivalDateMetricsFor(groupEvaluationIds, getNativeQueryToFetchGrossRevenueTotal());
        final List<ArrivalDateMetrics> fsCommissionRevenue = calculateFSConferenceAndBanquetCommission(groupEvaluationIds, groupEvaluationById);
        final List<ArrivalDateMetrics> cAndBCommissionRevenue = calculateGPConferenceAndBanquetCommission(groupEvaluationIds, groupEvaluationById);
        final Map<AccomClass, BigDecimal> perRoomServicingCostByRoomType = groupEvaluationService.getPerRoomServicingCostByRoomType();

        final List<Integer> groupEvaluationsArrivalDateIds = groupEvaluationsArrivalDates.stream().map(GroupEvaluationArrivalDate::getId).collect(Collectors.toList());
        Map<Integer, List<GroupEvaluationArrivalDateUserAdjustment>> groupEvaluationArrivalDateUserAdjustmentsMap = getGroupEvaluationArrivalDateUserAdjustmentsMap(groupEvaluationsArrivalDateIds);
        Map<Integer, List<GroupEvaluationArrivalDateGuestRoomRates>> groupEvaluationArrivalDateGuestRoomRatesMap = getGroupEvaluationArrivalDateGuestRoomRatesMap(groupEvaluationsArrivalDateIds);
        Map<Integer, List<GroupEvaluationFunctionSpaceConfAndBanq>> groupEvaluationFunctionSpaceConfAndBanqsMap = getGroupEvaluationFunctionSpaceConfAndBanqsMap(groupEvaluationIds);
        List<GroupEvaluationCost> costs = tenantCrudService.findByNamedQuery(GroupEvaluationCost.FIND_BY_GROUP_EVALUATION_ID, getQueryParamWith(GROUP_EVALUATION_IDS, groupEvaluationIds));
        Map<Integer, List<GroupEvaluationCost>> costsMap = costs.stream().collect(Collectors.groupingBy(e -> e.getGroupEvaluation().getId()));
        updateGroupEvaluationData(groupEvaluationsDataList, displacedRooms, userIdToFullNameMap, noOfRooms, incrementalRoomsByArrivalDate, groupEvaluationsArrivalDates, groupEvaluationIDMap, geadIDToGeadACMap, noOfNights, totalCostCAndB, totalCost, grossRevenueTotal, fsCommissionRevenue, cAndBCommissionRevenue, perRoomServicingCostByRoomType, groupEvaluationArrivalDateUserAdjustmentsMap, groupEvaluationArrivalDateGuestRoomRatesMap, groupEvaluationFunctionSpaceConfAndBanqsMap, costsMap);
        return groupEvaluationsDataList;
    }

    private List<ArrivalDateMetrics> calculateGPConferenceAndBanquetTotalCost(Set<Integer> groupEvaluationIds,
                                                                              Map<Integer, GroupEvaluation> groupEvaluationById) {
        if (conferenceAndBanquetService.shouldUseFSRevenueStreams()) {
            String queryToFetchConfAndBanqCommission = getNativeQueryWithNoLockToFetchFunctionSpaceConfAndBanqCommission();
            List<ArrivalDateMetrics> conferenceBanquetCommissionMetrics = getArrivalDateMetricsFor(groupEvaluationIds, queryToFetchConfAndBanqCommission);
            return conferenceBanquetCommissionMetrics.stream()
                    .filter(arrivalDateMetric -> {
                        GroupEvaluation groupEvaluation = groupEvaluationById.get(arrivalDateMetric.getUniqueGroupId());
                        return groupEvaluation.isEvaluationCategoryGroupPricing();
                    })
                    .collect(Collectors.toList());
        }

        return getArrivalDateMetricsFor(groupEvaluationIds, getNativeQueryToFetchTotalCostCAndBByArrivalDate());
    }

    private List<ArrivalDateMetrics> calculateFSConferenceAndBanquetCommission(Set<Integer> groupEvaluationIds,
                                                                               Map<Integer, GroupEvaluation> groupEvaluationById) {
        String queryToFetchConfAndBanqCommission = getNativeQueryToFetchFunctionSpaceConfAndBanqCommission();
        final List<ArrivalDateMetrics> conferenceBanquetCommissionMetrics = getArrivalDateMetricsFor(groupEvaluationIds, queryToFetchConfAndBanqCommission);
        if (conferenceAndBanquetService.shouldUseFSRevenueStreams()) {
            return conferenceBanquetCommissionMetrics.stream()
                    .filter(arrivalDateMetric -> {
                        GroupEvaluation groupEvaluation = groupEvaluationById.get(arrivalDateMetric.getUniqueGroupId());
                        return groupEvaluation.isNotGroupPricingEvaluation();
                    })
                    .collect(Collectors.toList());
        }

        return conferenceBanquetCommissionMetrics;
    }

    private List<ArrivalDateMetrics> calculateGPConferenceAndBanquetCommission(Set<Integer> groupEvaluationIds,
                                                                               Map<Integer, GroupEvaluation> groupEvaluationById) {
        if (conferenceAndBanquetService.shouldUseFSRevenueStreams()) {
            String queryToFetchConfAndBanqCommission = getNativeQueryToFetchFunctionSpaceConfAndBanqCommission();
            List<ArrivalDateMetrics> conferenceBanquetCommissionMetrics = getArrivalDateMetricsFor(groupEvaluationIds, queryToFetchConfAndBanqCommission);
            return conferenceBanquetCommissionMetrics.stream()
                    .filter(arrivalDateMetric -> {
                        GroupEvaluation groupEvaluation = groupEvaluationById.get(arrivalDateMetric.getUniqueGroupId());
                        return groupEvaluation.isEvaluationCategoryGroupPricing();
                    })
                    .collect(Collectors.toList());
        }

        return getArrivalDateMetricsFor(groupEvaluationIds, getNativeQueryToFetchConferenceAndBanquetCommissionRevenue());
    }

    private void updateGroupEvaluationData(List<GroupEvaluationData> groupEvaluationsDataList, List<ArrivalDateMetrics> displacedRooms, Map<Integer, String> userIdToFullNameMap, List<AccomTypeMetrics> noOfRooms, List<ArrivalDateMetrics> incrementalRoomsByArrivalDate, List<GroupEvaluationArrivalDate> groupEvaluationsArrivalDates, Map<Integer, GroupEvaluation> groupEvaluationIDMap, Map<Integer, List<GroupEvaluationArrivalDateAccomClass>> geadIDToGeadACMap, List<AccomTypeMetrics> noOfNights, List<ArrivalDateMetrics> totalCostCAndB, List<ArrivalDateMetrics> totalCost, List<ArrivalDateMetrics> grossRevenueTotal, List<ArrivalDateMetrics> fsCommissionRevenue, List<ArrivalDateMetrics> cAndBCommissionRevenue, Map<AccomClass, BigDecimal> perRoomServicingCostByRoomType, Map<Integer, List<GroupEvaluationArrivalDateUserAdjustment>> groupEvaluationArrivalDateUserAdjustmentsMap, Map<Integer, List<GroupEvaluationArrivalDateGuestRoomRates>> groupEvaluationArrivalDateGuestRoomRatesMap, Map<Integer, List<GroupEvaluationFunctionSpaceConfAndBanq>> groupEvaluationFunctionSpaceConfAndBanqsMap, Map<Integer, List<GroupEvaluationCost>> costsMap) {
        groupEvaluationsDataList.forEach(groupEvaluation -> {
            final GroupEvaluationArrivalDate gead = findGroupEvaluationArrivalDateFor(groupEvaluation, groupEvaluationsArrivalDates);
            final GroupEvaluation ge = groupEvaluationIDMap.get(groupEvaluation.getUniqueGroupId());
            final List<GroupEvaluationArrivalDateAccomClass> geadAccomClasses = geadIDToGeadACMap.get(gead.getId());
            final BigDecimal totalCostCAndBValue = getArrivalDateMetricValueFor(groupEvaluation, totalCostCAndB);
            if (GroupPricingEvaluationMethod.RC.equals(ge.getEvaluationMethod())) {
                ge.setPerRoomServingCostByRoomType(perRoomServicingCostByRoomType);
            }
            final BigDecimal displacedRoomsValue = getArrivalDateMetricValueFor(groupEvaluation, displacedRooms);
            groupEvaluation.setSalesperson(userIdToFullNameMap.get(ge.getLastUpdatedByUserId()));
            groupEvaluation.setDisplacedRooms(getDisplacedRooms(gead, displacedRoomsValue));
            groupEvaluation.setNumberOfRooms(getNoOfRooms(groupEvaluation, noOfRooms));
            groupEvaluation.setIncrementalRooms(getArrivalDateMetricValueFor(groupEvaluation, incrementalRoomsByArrivalDate));
            Set<GroupEvaluationFunctionSpaceConfAndBanq> groupEvaluationFunctionSpaceConfAndBanqs = !groupEvaluationFunctionSpaceConfAndBanqsMap.isEmpty()
                    ? new HashSet<>(groupEvaluationFunctionSpaceConfAndBanqsMap.getOrDefault(ge.getId(), Collections.emptyList()))
                    : Collections.emptySet();
            List<GroupEvaluationArrivalDateGuestRoomRates> groupEvaluationArrivalDateGuestRoomRates = !groupEvaluationArrivalDateGuestRoomRatesMap.isEmpty() ? groupEvaluationArrivalDateGuestRoomRatesMap.get(gead.getId()) : emptyList();
            List<GroupEvaluationArrivalDateUserAdjustment> groupEvaluationArrivalDateUserAdjustments = !groupEvaluationArrivalDateUserAdjustmentsMap.isEmpty() ? groupEvaluationArrivalDateUserAdjustmentsMap.get(gead.getId()) : emptyList();
            groupEvaluation.setCostRooms(getCostRooms(ge, gead, geadAccomClasses, getTotalNumberOfRooms(groupEvaluation, noOfRooms), groupEvaluationFunctionSpaceConfAndBanqs, groupEvaluationArrivalDateGuestRoomRates, groupEvaluationArrivalDateUserAdjustments, costsMap));
            groupEvaluation.setNumberOfNights(getNoOfNights(groupEvaluation, noOfNights));
            groupEvaluation.setTotalCostConferenceAndBanquet(totalCostCAndBValue);
            groupEvaluation.setNetRevenueConferenceAndBanquet(subtract(groupEvaluation.getGrossRevenueConferenceAndBanquet(), totalCostCAndBValue));
            groupEvaluation.setGrossRevenueTotal(getGrossRevenueTotal(groupEvaluation, grossRevenueTotal));
            groupEvaluation.setCostTotal(getCostTotal(groupEvaluation, totalCost));
            updateTotalMetrics(groupEvaluation, gead, ge, fsCommissionRevenue, cAndBCommissionRevenue, costsMap);
            updateUserAdjustedMetrics(groupEvaluation, gead, ge, geadAccomClasses, groupEvaluationFunctionSpaceConfAndBanqs, groupEvaluationArrivalDateGuestRoomRates, groupEvaluationArrivalDateUserAdjustments);

            if (GroupPricingEvaluationMethod.ROH.name().equals(groupEvaluation.getEvaluationMethod())) {
                groupEvaluation.setRecommendedRate(round(gead.getSuggestedRate()));
                groupEvaluation.setAdjustedRate(round(gead.getUserAdjustedRoomRate()));
            }
        });
    }

    private Map<Integer, List<GroupEvaluationFunctionSpaceConfAndBanq>> getGroupEvaluationFunctionSpaceConfAndBanqsMap(Set<Integer> groupEvaluationIds) {
        List<GroupEvaluationFunctionSpaceConfAndBanq> groupEvaluationFunctionSpaceConfAndBanqs = tenantCrudService.findByNamedQuery(GroupEvaluationFunctionSpaceConfAndBanq.FIND_BY_GROUP_EVALUATION_ID_IN, getQueryParamWith("geId", groupEvaluationIds));
        return groupEvaluationFunctionSpaceConfAndBanqs.stream().collect(Collectors.groupingBy(e -> e.getGroupEvaluation().getId()));
    }

    private Map<Integer, List<GroupEvaluationArrivalDateGuestRoomRates>> getGroupEvaluationArrivalDateGuestRoomRatesMap(List<Integer> groupEvaluationsArrivalDateIds) {
        List<GroupEvaluationArrivalDateGuestRoomRates> groupEvaluationArrivalDateGuestRoomRates = tenantCrudService.findByNamedQuery(GroupEvaluationArrivalDateGuestRoomRates.FIND_BY_ARRIVAL_DATE_IN, getQueryParamWith(GROUP_EVALUATION_ARRIVAL_DATE_ID, groupEvaluationsArrivalDateIds));
        return groupEvaluationArrivalDateGuestRoomRates.stream().collect(Collectors.groupingBy(e -> e.getGroupEvaluationArrivalDate().getId()));
    }

    private Map<Integer, List<GroupEvaluationArrivalDateUserAdjustment>> getGroupEvaluationArrivalDateUserAdjustmentsMap(List<Integer> groupEvaluationsArrivalDateIds) {
        List<GroupEvaluationArrivalDateUserAdjustment> groupEvaluationArrivalDateUserAdjustments = tenantCrudService.findByNamedQuery(GroupEvaluationArrivalDateUserAdjustment.FIND_BY_ARRIVAL_DATE_IN, getQueryParamWith(GROUP_EVALUATION_ARRIVAL_DATE_ID, groupEvaluationsArrivalDateIds));
        return groupEvaluationArrivalDateUserAdjustments.stream().collect(Collectors.groupingBy(e -> e.getGroupEvaluationArrivalDate().getId()));
    }

    private List<Integer> getEvaluationUserIds(Set<Integer> groupEvaluationIds) {
        final List<GroupEvaluation> groupEvaluations = getGroupEvaluationByIds(groupEvaluationIds);
        return groupEvaluations.stream().map(AuditableEntity::getLastUpdatedByUserId).collect(Collectors.toList());
    }

    private List<GroupEvaluation> getGroupEvaluationByIds(Set<Integer> groupEvaluationIds) {
        return tenantCrudService.findByNamedQuery(FIND_GE_BY_IDS, QueryParameter.with(GROUP_EVALUATION_IDS, groupEvaluationIds).parameters());
    }

    private BigDecimal getTotalNumberOfRooms(GroupEvaluationData ged, List<AccomTypeMetrics> noOfRooms) {
        return Optional.ofNullable(noOfRooms).orElse(emptyList()).stream()
                .filter(dr -> dr.getUniqueGroupId() == ged.getUniqueGroupId())
                .map(AccomTypeMetrics::getMetrics)
                .reduce(Integer::sum).map(BigDecimal::new)
                .orElse(ZERO);
    }

    private Map<Integer, List<GroupEvaluationArrivalDateAccomClass>> getgeadIDToGeadACMap(Set<Integer> groupEvaluationIds) {
        final List<GroupEvaluationArrivalDateAccomClass> geadacList = tenantCrudService.findByNamedQuery(GroupEvaluationArrivalDateAccomClass.FIND_BY_GEAD_IDS, getQueryParamWith(GROUP_EVALUATION_IDS, groupEvaluationIds));
        return geadacList.stream().collect(Collectors.groupingBy(g -> g.getGroupEvaluationArrivalDate().getId()));
    }

    private BigDecimal getDisplacedRooms(GroupEvaluationArrivalDate gead, BigDecimal displacedRoomsValue) {
        return round(add(displacedRoomsValue, negativeValueToZero(gead.getPreDisplacedRooms()), negativeValueToZero(gead.getPostDisplacedRooms())));
    }

    private BigDecimal getGrossRevenueTotal(GroupEvaluationData ged, List<ArrivalDateMetrics> grossRevenueTotal) {
        return getArrivalDateMetricValueFor(ged, grossRevenueTotal);
    }

    private BigDecimal getCostTotal(GroupEvaluationData ged, List<ArrivalDateMetrics> totalCost) {
        return round(add(getArrivalDateMetricValueFor(ged, totalCost), ged.getCostRooms()));
    }

    private Map<Integer, GroupEvaluation> getGroupEvaluationIDMap(Set<Integer> groupEvaluationIds) {
        return getGroupEvaluationsFor(groupEvaluationIds).stream().collect(Collectors.toMap(GroupEvaluation::getId, Function.identity(), (a, b) -> b));
    }

    private List<GroupEvaluation> getGroupEvaluationsFor(Set<Integer> groupEvaluationIds) {
        return tenantCrudService.findByNamedQuery(GroupEvaluation.FIND_BY_IDS, getQueryParamWith(GROUP_EVALUATION_IDS, groupEvaluationIds));
    }

    @VisibleForTesting
	public
    List<GroupEvaluationArrivalDate> getGroupEvaluationsArrivalDatesFor(Set<Integer> groupEvaluationIds) {
        return tenantCrudService.findByNamedQuery(GroupEvaluationArrivalDate.FIND_BY_GROUP_EVALUATION_IDS_FETCH_GEAD_GUEST_ROOM_RATES, getQueryParamWith(GROUP_EVALUATION_IDS, groupEvaluationIds));
    }

    private void updateTotalMetrics(GroupEvaluationData ged, GroupEvaluationArrivalDate gead, GroupEvaluation ge, List<ArrivalDateMetrics> fsCommissionRevenue, List<ArrivalDateMetrics> conferenceAndBanquetCommissionRevenue, Map<Integer, List<GroupEvaluationCost>> costsMap) {
        final boolean fSIncluded = ge.isFunctionSpaceIncluded();

        ged.setNetRevenueTotal(getNetRevenueTotal(ged, gead, fsCommissionRevenue, conferenceAndBanquetCommissionRevenue, costsMap, fSIncluded));
        ged.setGrossProfitTotal(getTotalGrossProfit(gead, fSIncluded));
        ged.setDisplacedRevenueTotal(getDisplacedRevenueTotal(gead, fSIncluded));
        ged.setDisplacedProfitTotal(getTotalDisplacedProfit(gead, fSIncluded));
        ged.setNetProfitTotal(getTotalNetProfit(ged, gead));
        ged.setNetProfitPercent(getTotalNetProfitPercentage(ged, gead, fsCommissionRevenue, fSIncluded));
    }

    private void updateUserAdjustedMetrics(GroupEvaluationData ged, GroupEvaluationArrivalDate gead, GroupEvaluation ge,
                                           List<GroupEvaluationArrivalDateAccomClass> groupEvaluationArrivalDateAccomClasses,
                                           Set<GroupEvaluationFunctionSpaceConfAndBanq> groupEvaluationFunctionSpaceConfAndBanqs, List<GroupEvaluationArrivalDateGuestRoomRates> groupEvaluationArrivalDateGuestRoomRates,
                                           List<GroupEvaluationArrivalDateUserAdjustment> groupEvaluationArrivalDateUserAdjustments) {
        if (gead.hasUserAdjustedOutput()) {
            final UserAdjustmentArrivalDateWrapper wrapper = new UserAdjustmentArrivalDateWrapper(gead, ge,
                    groupEvaluationFunctionSpaceConfAndBanqs,
                    groupEvaluationArrivalDateGuestRoomRates,
                    groupEvaluationArrivalDateUserAdjustments,
                    groupEvaluationArrivalDateAccomClasses);

            ged.setAdjustGrossRevenueTotal(round(wrapper.getUserAdjustedTotalGrossRevenue()));
            ged.setAdjustGrossProfitTotal(round(wrapper.getTotalUserAdjustedGrossProfit()));
            ged.setAdjustNetProfitTotal(round(wrapper.getUserAdjustedNetProfit()));
            ged.setAdjustNetProfitPercentTotal(round(wrapper.getUserAdjustedProfitPercentage()));
        } else {
            ged.setAdjustNetProfitPercentTotal(ged.getNetProfitPercent());
            ged.setAdjustNetProfitTotal(ged.getNetProfitTotal());
            ged.setAdjustGrossProfitTotal(ged.getGrossProfitTotal());
            ged.setAdjustGrossRevenueTotal(ged.getGrossRevenueTotal());
        }
    }

    private BigDecimal getTotalGrossProfit(GroupEvaluationArrivalDate gead, boolean fSIncluded) {
        return round(add(gead.getRoomGrossProfit(), gead.getAncillaryGrossProfit(), getConfAndBanqGrossProfit(gead, fSIncluded)));
    }

    private BigDecimal getConfAndBanqGrossProfit(GroupEvaluationArrivalDate gead, boolean fSIncluded) {
        return fSIncluded ? gead.getFunctionSpaceProfit() : gead.getConferenceAndBanquetGrossProfit();
    }

    private BigDecimal getDisplacedRevenueTotal(GroupEvaluationArrivalDate gead, boolean fSIncluded) {
        return round(add(getRoomDisplacedRevenue(gead), gead.getDisplacedAncillaryRevenue(), getDisplacedFunctionSpaceRevenue(gead, fSIncluded)));
    }

    private BigDecimal getTotalDisplacedProfit(GroupEvaluationArrivalDate gead, boolean fSIncluded) {
        return round(add(getRoomDisplacedProfit(gead), gead.getDisplacedAncillaryProfit(), getDisplacedFunctionSpaceProfit(gead, fSIncluded)));
    }

    private BigDecimal getTotalNetProfit(GroupEvaluationData ge, GroupEvaluationArrivalDate gead) {
        return round(subtract(ge.getGrossProfitTotal(), ge.getDisplacedProfitTotal(), gead.getCostOfWalk()));
    }

    private BigDecimal getTotalNetProfitPercentage(GroupEvaluationData ge, GroupEvaluationArrivalDate gead, List<ArrivalDateMetrics> fsCommissionRevenue, boolean fSIncluded) {
        return multiply(divide(ge.getNetProfitTotal(), getTotalGrossRevenue(ge, gead, fsCommissionRevenue, fSIncluded), 4), ONE_HUNDRED, 2);
    }

    private List<GroupEvaluationData> getGroupEvaluationsWithBaseData(Date startDate, int startPosition, int size) {
        return tenantCrudService.findByNativeQuery(getNativeQueryToFetchGroupEvaluations(),
                getQueryParamWith("startDate", startDate),
                startPosition, size,
                GroupEvaluationData::getGroupEvaluationDataRowMapper);
    }

    private List<AccomTypeMetrics> getAccomTypeMetricsFor(Set<Integer> groupEvaluationIds, String nativeQueryToFetchNoOfNights) {
        return tenantCrudService.findByNativeQuery(nativeQueryToFetchNoOfNights,
                getQueryParamWith(GROUP_EVALUATION_IDS, groupEvaluationIds),
                AccomTypeMetrics::mapRow);
    }

    private List<ArrivalDateMetrics> getArrivalDateMetricsFor(Set<Integer> groupEvaluationIds, String query) {
        List<ArrivalDateMetrics> arrivalDateMetricsList =  tenantCrudService.findByNativeQuery(query,
                getQueryParamWith(GROUP_EVALUATION_IDS, groupEvaluationIds),
                ArrivalDateMetrics::mapRow);
        return Optional.ofNullable(arrivalDateMetricsList).orElseGet(Collections::emptyList);
    }

    private BigDecimal round(BigDecimal bigDecimal) {
        return BigDecimalUtil.round(bigDecimal, 2);
    }

    private Map<String, Object> getQueryParamWith(String name, Object value) {
        return QueryParameter.with(name, value).parameters();
    }

    public BigDecimal getDisplacedFunctionSpaceProfit(GroupEvaluationArrivalDate gead, boolean fSIncluded) {
        return fSIncluded ? round(subtract(zeroIfNull(gead.getFunctionSpaceProfit()), zeroIfNull(gead.getFunctionSpaceIncrementalProfit()))) : round(ZERO);
    }

    public BigDecimal getRoomDisplacedProfit(GroupEvaluationArrivalDate gead) {
        return round(subtract(gead.getRoomGrossProfit(), gead.getIncrementalRoomProfit(), gead.getCostOfWalk()));
    }

    public BigDecimal getDisplacedFunctionSpaceRevenue(GroupEvaluationArrivalDate gead, boolean fSIncluded) {
        return round(fSIncluded ? subtract(gead.getFunctionSpaceNetRevenue(), gead.getFunctionSpaceNetIncrementalRevenue()) : ZERO);
    }

    public BigDecimal getRoomDisplacedRevenue(GroupEvaluationArrivalDate gead) {
        return round(subtract(gead.getRoomNetRevenue(), gead.getNetIncrementalRoomRevenue()));
    }

    private BigDecimal getNetRevenueTotal(GroupEvaluationData ge, GroupEvaluationArrivalDate gead, List<ArrivalDateMetrics> fsCommissionRevenue, List<ArrivalDateMetrics> conferenceAndBanquetCommissionRevenue, Map<Integer, List<GroupEvaluationCost>> costsMap, boolean fSIncluded) {
        BigDecimal totalGrossRevenueValue = getTotalGrossRevenue(ge, gead, fsCommissionRevenue, fSIncluded);
        BigDecimal totalCommissionRevenueValue = getTotalCommissionRevenue(ge, gead, fsCommissionRevenue, conferenceAndBanquetCommissionRevenue);
        return round(subtract(totalGrossRevenueValue, gead.getRoomConcessionRevenue(), totalCommissionRevenueValue, getFunctionSpaceCost(ge, costsMap)));
    }

    private BigDecimal getFunctionSpaceCost(GroupEvaluationData ged, Map<Integer, List<GroupEvaluationCost>> costsMap) {
        final List<GroupEvaluationCost> costs = costsMap.get(ged.getUniqueGroupId());
        final BigDecimal fsCost = Optional.ofNullable(costs).orElse(emptyList()).stream()
                .filter(cost -> GroupEvaluationCostType.FUNCTION_SPACE == cost.getGroupEvaluationCostType() && cost.getCost() != null)
                .findFirst()
                .map(GroupEvaluationCost::getCost)
                .orElse(ZERO);
        return round(fsCost);
    }

    public BigDecimal getTotalCommissionRevenue(GroupEvaluationData ge, GroupEvaluationArrivalDate gead,
                                                List<ArrivalDateMetrics> confNBanqCommissionRevenue, List<ArrivalDateMetrics> fsCommissionRevenue) {
        return add(gead.getRoomCommissionRevenue(), getConferenceAndBanquetCommissionRevenue(ge, confNBanqCommissionRevenue), getArrivalDateMetricValueFor(ge, fsCommissionRevenue));
    }

    private BigDecimal getConferenceAndBanquetCommissionRevenue(GroupEvaluationData ge, List<ArrivalDateMetrics> conferenceAndBanquetCommissionRevenue) {
        return getArrivalDateMetricValueFor(ge, conferenceAndBanquetCommissionRevenue);
    }

    public BigDecimal getTotalGrossRevenue(GroupEvaluationData ge, GroupEvaluationArrivalDate gead, List<ArrivalDateMetrics> fsCommissionRevenue, boolean fSIncluded) {
        return add(ge.getGrossRevenueRooms(), ge.getGrossRevenueAncillary(), geConfAndBanqGrossRevenue(ge, gead, fsCommissionRevenue, fSIncluded));
    }

    private BigDecimal geConfAndBanqGrossRevenue(GroupEvaluationData ge, GroupEvaluationArrivalDate gead, List<ArrivalDateMetrics> fsCommissionRevenue, boolean fSIncluded) {
        return fSIncluded ? getFunctionSpaceTotalGrossRevenue(ge, gead, fsCommissionRevenue) : gead.getConferenceAndBanquetGrossRevenue();
    }

    public BigDecimal getFunctionSpaceTotalGrossRevenue(GroupEvaluationData ge, GroupEvaluationArrivalDate gead, List<ArrivalDateMetrics> fsCommissionRevenue) {
        return add(gead.getFunctionSpaceGrossRevenue(), getArrivalDateMetricValueFor(ge, fsCommissionRevenue));
    }

    private BigDecimal getArrivalDateMetricValueFor(GroupEvaluationData ge, List<ArrivalDateMetrics> arrivalDateMetrics) {
        final BigDecimal metrics = Optional.ofNullable(arrivalDateMetrics).orElse(emptyList()).stream()
                .filter(dr -> dr.getUniqueGroupId() == ge.getUniqueGroupId())
                .filter(d -> d.getArrivalDate() == null || d.getArrivalDate().equals(ge.getArrivalDate()))
                .findFirst()
                .map(ArrivalDateMetrics::getMetrics)
                .orElse(ZERO);
        return round(metrics);
    }

    private int getNoOfNights(GroupEvaluationData ge, List<AccomTypeMetrics> noOfNights) {
        return Optional.ofNullable(noOfNights).orElse(emptyList()).stream()
                .filter(dr -> dr.getUniqueGroupId() == ge.getUniqueGroupId())
                .findFirst()
                .map(AccomTypeMetrics::getMetrics)
                .orElse(0);
    }

    private BigDecimal getCostRooms(GroupEvaluation ge, GroupEvaluationArrivalDate gead, List<GroupEvaluationArrivalDateAccomClass> groupEvaluationArrivalDateAccomClasses, BigDecimal totalNoOfRooms, Set<GroupEvaluationFunctionSpaceConfAndBanq> groupEvaluationFunctionSpaceConfAndBanqs, List<GroupEvaluationArrivalDateGuestRoomRates> groupEvaluationArrivalDateGuestRoomRates,
                                    List<GroupEvaluationArrivalDateUserAdjustment> groupEvaluationArrivalDateUserAdjustments, Map<Integer, List<GroupEvaluationCost>> costsMap) {
        List<GroupEvaluationCost> groupEvaluationCosts = costsMap.get(ge.getId());
        if (null == groupEvaluationCosts) {
            groupEvaluationCosts = Collections.emptyList();
        }
        final UserAdjustmentArrivalDateWrapper wrapper = new UserAdjustmentArrivalDateWrapper(gead, ge,
                groupEvaluationFunctionSpaceConfAndBanqs,
                groupEvaluationArrivalDateGuestRoomRates,
                groupEvaluationArrivalDateUserAdjustments,
                totalNoOfRooms,
                groupEvaluationCosts,
                groupEvaluationArrivalDateAccomClasses);
        return round(AdjustmentOutputUtil.getTotalCostWithoutPerRoomServicingCostBeforeAdjustment(wrapper));
    }

    private GroupEvaluationArrivalDate findGroupEvaluationArrivalDateFor(GroupEvaluationData ge, List<GroupEvaluationArrivalDate> groupEvaluationsArrivalDates) {
        return groupEvaluationsArrivalDates.stream()
                .filter(groupEvaluationArrivalDate -> Objects.equals(groupEvaluationArrivalDate.getGroupEvaluation().getId(), ge.getUniqueGroupId()) && groupEvaluationArrivalDate.getArrivalDate().toDate().equals(ge.getArrivalDate()))
                .findFirst().orElse(null);
    }

    private int getNoOfRooms(GroupEvaluationData ge, List<AccomTypeMetrics> noOfRooms) {
        return Optional.ofNullable(noOfRooms).orElse(emptyList()).stream()
                .filter(dr -> dr.getUniqueGroupId() == ge.getUniqueGroupId())
                .filter(d -> d.getAccomTypeCode() == null || StringUtils.isEmpty(ge.getRoomTypeCode()) || d.getAccomTypeCode().equals(ge.getRoomTypeCode()))
                .findFirst()
                .map(AccomTypeMetrics::getMetrics)
                .orElse(0);
    }

    private String getNativeQueryToFetchConferenceAndBanquetCommissionRevenue() {
        return "select ge.Grp_Evl_ID, gead.Arrival_Date, sum(case when gecb.Revenue is null or gecb.Commission_Percentage is null then 0 else \n" +
                "\t\tgecb.Revenue * gecb.Commission_Percentage end) / 100 as Commission\n" +
                "\t\tfrom Grp_Evl_Conf_Banq gecb\n" +
                "\t\tjoin Grp_Evl ge on ge.Grp_Evl_ID = gecb.Grp_Evl_ID \n" +
                "\t\tjoin Grp_Evl_Arr_DT gead on gead.Grp_Evl_ID = ge.Grp_Evl_ID\n" +
                " where ge.Grp_Evl_ID in :groupEvaluationIds" +
                "\t\tgroup by  ge.Grp_Evl_ID, gead.Arrival_Date";
    }

    private String getNativeQueryToFetchFunctionSpaceConfAndBanqCommission() {
        return "select ge.Grp_Evl_ID, gead.Arrival_Date, sum(case when gefscb.Commission_Percentage is null then 0\n" +
                "\t\t\telse gefscb.Revenue * gefscb.Commission_Percentage\tend ) as Commission\n" +
                "\t\t from Grp_Evl_Fct_Spc_Conf_Banq gefscb\n" +
                "\t\t\tjoin Grp_Evl ge on ge.Grp_Evl_ID = gefscb.Grp_Evl_ID\n" +
                "\t\t\tjoin Grp_Evl_Arr_DT gead on gead.Grp_Evl_ID = ge.Grp_Evl_ID\n" +
                " where ge.Grp_Evl_ID in :groupEvaluationIds" +
                "\t\t group by  ge.Grp_Evl_ID, gead.Arrival_Date";
    }

    private String getNativeQueryWithNoLockToFetchFunctionSpaceConfAndBanqCommission() {
        return "select ge.Grp_Evl_ID, gead.Arrival_Date, sum(case when gefscb.Commission_Percentage is null then 0\n" +
                "\t\t\telse gefscb.Revenue * gefscb.Commission_Percentage\tend ) as Commission\n" +
                "\t\t from Grp_Evl_Fct_Spc_Conf_Banq gefscb WITH (NOLOCK) \n" +
                "\t\t\tjoin Grp_Evl ge WITH (NOLOCK) on ge.Grp_Evl_ID = gefscb.Grp_Evl_ID\n" +
                "\t\t\tjoin Grp_Evl_Arr_DT gead WITH (NOLOCK) on gead.Grp_Evl_ID = ge.Grp_Evl_ID\n" +
                " where ge.Grp_Evl_ID in :groupEvaluationIds" +
                "\t\t group by  ge.Grp_Evl_ID, gead.Arrival_Date";
    }

    private String getNativeQueryToFetchTotalCost() {
        return "select base.Grp_Evl_ID, base.Arrival_Date, sum(ISNULL(Commission,0)) as Cost from \n" +
                "\t(select ge.Grp_Evl_ID, gead.Arrival_Date, sum(case when gefscb.Commission_Percentage is null then 0\n" +
                "\t\t\telse gefscb.Revenue * gefscb.Commission_Percentage\tend ) as Commission\n" +
                "\t\t from Grp_Evl_Fct_Spc_Conf_Banq gefscb\n" +
                "\t\t\tjoin Grp_Evl ge on ge.Grp_Evl_ID = gefscb.Grp_Evl_ID\n" +
                "\t\t\tjoin Grp_Evl_Arr_DT gead on gead.Grp_Evl_ID = ge.Grp_Evl_ID\n" +
                "\t\t group by  ge.Grp_Evl_ID, gead.Arrival_Date\n" +
                "\tUnion All\n" +
                "\tselect ge.Grp_Evl_ID, gead.Arrival_Date, sum(case when gecb.Revenue is null or gecb.Commission_Percentage is null then 0 else \n" +
                "\t\tgecb.Revenue * gecb.Commission_Percentage end) / 100 as Commission\n" +
                "\t\tfrom Grp_Evl_Conf_Banq gecb\n" +
                "\t\tjoin Grp_Evl ge on ge.Grp_Evl_ID = gecb.Grp_Evl_ID \n" +
                "\t\tjoin Grp_Evl_Arr_DT gead on gead.Grp_Evl_ID = ge.Grp_Evl_ID\n" +
                "\t\tgroup by  ge.Grp_Evl_ID, gead.Arrival_Date\n" +
                "\tUnion All\n" +
                "\t\tselect \tgec.grp_evl_id,null, gec.cost from Grp_Evl_Cost gec\n" +
                "\t\twhere gec.Cost_Type = 10 and gec.Cost is not null\n" +
                "\t\t) base\n" +
                " where base.Grp_Evl_ID in :groupEvaluationIds" +
                " group by  base.Grp_Evl_ID, base.Arrival_Date";
    }

    private String getNativeQueryToFetchGrossRevenueTotal() {
        return "select ge.Grp_Evl_ID, gead.Arrival_Date,\n" +
                "\tgead.Gross_Room_Revenue + gead.Ancillary_Revenue + \n" +
                "\tcase when ge.Evaluation_Method in (2,3) \n" +
                "\t\tthen gead.Fct_Spc_Gross_Revenue + fscommisionRevenue.FunctionSpaceCommissionRevenue\n" +
                "\t\telse  gead.Conference_And_Banquet_Revenue end\n" +
                "\tas grossRevenueTotal\n" +
                "from Grp_Evl_Arr_DT gead\n" +
                "inner join Grp_Evl ge on ge.Grp_Evl_ID = gead.Grp_Evl_ID\n" +
                "left join (\n" +
                "\tselect ge.Grp_Evl_ID, gead.Arrival_Date, sum(case when gefscb.Commission_Percentage is null then 0\n" +
                "\t\telse gefscb.Revenue * gefscb.Commission_Percentage\tend ) as FunctionSpaceCommissionRevenue\n" +
                "\t from Grp_Evl_Fct_Spc_Conf_Banq gefscb\n" +
                "\t\tjoin Grp_Evl ge on ge.Grp_Evl_ID = gefscb.Grp_Evl_ID\n" +
                "\t\tjoin Grp_Evl_Arr_DT gead on gead.Grp_Evl_ID = ge.Grp_Evl_ID\n" +
                "\t group by  ge.Grp_Evl_ID, gead.Arrival_Date ) fscommisionRevenue\n" +
                "\ton ge.Grp_Evl_ID = fscommisionRevenue.Grp_Evl_ID\n" +
                "where ge.Grp_Evl_ID in :groupEvaluationIds";
    }

    private String getNativeQueryToFetchTotalCostCAndBByArrivalDate() {
        return "select ge.Grp_Evl_ID,gead.Arrival_Date, sum(case when gecb.Revenue is null or gecb.Commission_Percentage is null then 0 else \n" +
                "\t\tgecb.Revenue * gecb.Commission_Percentage end) / 100 as Commission\n" +
                "\tfrom Grp_Evl_Conf_Banq gecb WITH (NOLOCK)\n" +
                "\t\tjoin Grp_Evl ge WITH (NOLOCK) on ge.Grp_Evl_ID = gecb.Grp_Evl_ID \n" +
                "\t\tjoin Grp_Evl_Arr_DT gead WITH (NOLOCK) on gead.Grp_Evl_ID = ge.Grp_Evl_ID\n" +
                " where ge.Grp_Evl_ID in :groupEvaluationIds " +
                "\tgroup by ge.Grp_Evl_ID,gead.Arrival_Date";
    }

    private String getNativeQueryToFetchNoOfNights() {
        return "select Grp_Evl_ID,'', max(NoOfNights) as NoOfNights from (\n" +
                "\tselect ge.Grp_Evl_Id, max(gertdos.Day_Of_Stay) as NoOfNights \n" +
                "\tfrom Grp_Evl_Room_Type gert\n" +
                "\tjoin Grp_Evl ge on ge.Grp_Evl_ID = gert.Grp_Evl_ID\n" +
                "\tjoin Grp_Evl_Room_Type_Day_Of_Stay gertdos on gert.Grp_Evl_Room_Type_ID = gertdos.Grp_Evl_Room_Type_ID\n" +
                " where ge.Evaluation_Method = 1 " +
                "\tGroup By ge.Grp_Evl_Id\n" +
                "\n" +
                "\tUnion All\n" +
                "\n" +
                "\tselect ge.Grp_Evl_Id, max(gedos.Day_Of_Stay) as NoOfNights \n" +
                "\tfrom Grp_Evl_Day_Of_Stay gedos\n" +
                "\tjoin Grp_Evl ge on ge.Grp_Evl_ID = gedos.Grp_Evl_ID\n" +
                " where ge.Evaluation_Method = 0" +
                "\tGroup By ge.Grp_Evl_Id\n" +
                "\tUnion All\n" +
                "\tselect ge.Grp_Evl_Id, Datediff(day,min(gefs.start_Time), Max(gefs.End_Time)) as NoOfNights \n" +
                "\tfrom Grp_Evl_Fct_Spc gefs\n" +
                "\tjoin Grp_Evl ge on gefs.Grp_Evl_ID = ge.Grp_Evl_ID\n" +
                "\tgroup by ge.Grp_Evl_Id\n" +
                ") as base\n" +
                "\twhere base.Grp_Evl_ID in :groupEvaluationIds\n" +
                "group by Grp_Evl_Id";
    }

    private String getNativeQueryToFetchIncrementalRoomsByArrivalDate() {
        return " select DisplacedRooms.grp_evl_id, DisplacedRooms.Arrival_Date, SUM(TotalRooms.NoOfRooms - DisplacedRooms.Displaced_Rooms) as incremental_Rooms from \n" +
                " \t ( select gead.grp_evl_id as grp_evl_id, gead.Arrival_Date as Arrival_Date,geadfgdt.Occupancy_DT as Occupancy_Date, sum(geadfgdt.displaced_rooms) as Displaced_Rooms\n" +
                " \t\t\t\t\tfrom Grp_Evl_Arr_DT_FG_DT geadfgdt\n" +
                " \t\t\t\t\tjoin Grp_Evl_Arr_DT_FG geadfg on geadfg.Grp_Evl_Arr_DT_FG_ID = geadfgdt.Grp_Evl_Arr_DT_FG_ID \n" +
                " \t\t\t\t\tjoin Grp_Evl_Arr_DT gead on geadfg.grp_evl_arr_dt_id = gead.grp_evl_arr_dt_id\n" +
                " \t\t\t\t\tjoin Grp_Evl ge on ge.grp_Evl_Id = gead.grp_Evl_Id\n" +
                " \t\t where ge.Evaluation_Method = 0\n" +
                " \t\tgroup by gead.grp_evl_id,gead.Arrival_Date, geadfgdt.Occupancy_DT \n" +
                " \t\tunion all\n" +
                " \t\tselect gead.grp_evl_id, gead.Arrival_Date, geadfgac.Occupancy_DT, sum(geadfgac.displaced_rooms) as Displaced_Rooms \n" +
                " \t\t\t\t\t\tfrom Grp_Evl_Arr_DT_FG_DT_AC geadfgac\n" +
                " \t\t\t\t\t\tjoin Grp_Evl_Arr_DT_FG geadfg on geadfg.Grp_Evl_Arr_DT_FG_ID = geadfgac.Grp_Evl_Arr_DT_FG_ID \n" +
                " \t\t\t\t\t\tjoin Grp_Evl_Arr_DT gead on geadfg.grp_evl_arr_dt_id = gead.grp_evl_arr_dt_id\n" +
                " \t\t\t\t\t\tjoin Grp_Evl ge on ge.grp_Evl_Id = gead.grp_Evl_Id\n" +
                " \t\t where ge.Evaluation_Method = 1\n" +
                " \t\tgroup by gead.grp_evl_id,gead.Arrival_Date, geadfgac.Occupancy_DT\n" +
                " \t) DisplacedRooms left join \n" +
                "  \t( select gead.Grp_Evl_ID as Grp_Evl_ID, gead.Arrival_Date as Arrival_Date, DATEADD(DAY, gertdos.Day_Of_Stay-1, gead.Arrival_Date) as Occupancy_Date, sum(gertdos.Number_Of_Rooms) as NoOfRooms\n" +
                " \t\tfrom Grp_Evl_Room_Type_Day_Of_Stay gertdos \n" +
                " \t\tjoin Grp_Evl_Room_Type gert on gert.Grp_Evl_Room_Type_ID = gertdos.Grp_Evl_Room_Type_ID\n" +
                " \t\tjoin Grp_Evl_Arr_DT gead on gert.Grp_Evl_ID = gead.Grp_Evl_ID\n" +
                " \t\tjoin Grp_Evl ge on ge.Grp_Evl_ID = gead.Grp_Evl_ID\n" +
                " \t\twhere ge.Evaluation_Method = 1 \n" +
                " \t\tgroup by gead.Grp_Evl_ID, gead.Arrival_Date, DATEADD(DAY, gertdos.Day_Of_Stay-1, gead.Arrival_Date) \n" +
                " \t\tunion all \n" +
                " \t\tselect gead.Grp_Evl_ID, gead.Arrival_Date, DATEADD(DAY, gedos.Day_Of_Stay-1, gead.Arrival_Date) as Occupancy_Date, gedos.Number_Of_Rooms as NoOfRooms \n" +
                " \t\tfrom Grp_Evl_Day_Of_Stay gedos\n" +
                " \t\tjoin Grp_Evl_Arr_DT gead on gedos.Grp_Evl_ID = gead.Grp_Evl_ID\n" +
                " \t\tjoin Grp_Evl ge on ge.Grp_Evl_ID = gead.Grp_Evl_ID\n" +
                " \t\twhere ge.Evaluation_Method = 0 \n" +
                " \t)TotalRooms\n" +
                " \t\ton TotalRooms.Grp_Evl_ID = DisplacedRooms.Grp_Evl_ID\n" +
                " \t\tand TotalRooms.Arrival_Date = DisplacedRooms.Arrival_Date\n" +
                " \t\tand TotalRooms.Occupancy_Date = DisplacedRooms.Occupancy_Date \n" +
                " \tleft join \n" +
                " \t( select Grp_Evl_ID, max(NoOfNights) as NoOfNights \n" +
                " \t\tfrom (\n" +
                " \t\t\t\tselect ge.Grp_Evl_Id, max(gertdos.Day_Of_Stay) as NoOfNights \n" +
                " \t\t\t\tfrom Grp_Evl_Room_Type gert\n" +
                " \t\t\t\tjoin Grp_Evl ge on ge.Grp_Evl_ID = gert.Grp_Evl_ID\n" +
                " \t\t\t\tjoin Grp_Evl_Room_Type_Day_Of_Stay gertdos on gert.Grp_Evl_Room_Type_ID = gertdos.Grp_Evl_Room_Type_ID\n" +
                " \t\t\t\twhere ge.Evaluation_Method = 1 \n" +
                " \t\t\t\tGroup By ge.Grp_Evl_Id\n" +
                " \t\t\t\tUnion All\n" +
                " \t\t\t\tselect ge.Grp_Evl_Id, max(gedos.Day_Of_Stay) as NoOfNights \n" +
                " \t\t\t\tfrom Grp_Evl_Day_Of_Stay gedos\n" +
                " \t\t\t\tjoin Grp_Evl ge on ge.Grp_Evl_ID = gedos.Grp_Evl_ID\n" +
                " \t\t\t\twhere ge.Evaluation_Method = 0\n" +
                " \t\t\t\tGroup By ge.Grp_Evl_Id\n" +
                " \t\t\t\tUnion All\n" +
                " \t\t\t\tselect ge.Grp_Evl_Id, Datediff(day,min(gefs.start_Time), Max(gefs.End_Time)) as NoOfNights \n" +
                " \t\t\t\tfrom Grp_Evl_Fct_Spc gefs\n" +
                " \t\t\t\tjoin Grp_Evl ge on gefs.Grp_Evl_ID = ge.Grp_Evl_ID\n" +
                " \t\t\t\tgroup by ge.Grp_Evl_Id\n" +
                " \t\t\t) as base\n" +
                " \t\t\tgroup by Grp_Evl_Id\n" +
                " \t) Nights on Nights.grp_evl_id = DisplacedRooms.grp_evl_id\n" +
                " where DisplacedRooms.grp_evl_id in :groupEvaluationIds and\n" +
                " DisplacedRooms.Occupancy_Date >= DisplacedRooms.Arrival_Date and \n" +
                " DisplacedRooms.Occupancy_Date <= DATEADD(DAY, Nights.NoOfNights, DisplacedRooms.Arrival_Date) \n" +
                " Group By DisplacedRooms.grp_evl_id, DisplacedRooms.Arrival_Date";
    }

    private String getNativeQueryToFetchNoOfRooms() {
        return "select gert.Grp_Evl_ID, at.Accom_Type_Code, sum(gertdos.Number_Of_Rooms) as NoOfRooms \n" +
                "from Grp_Evl_Room_Type_Day_Of_Stay gertdos \n" +
                "\t\tjoin Grp_Evl_Room_Type gert on gert.Grp_Evl_Room_Type_ID = gertdos.Grp_Evl_Room_Type_ID\n" +
                "\t\tjoin Accom_Type at on at.Accom_type_id = gert.Accom_Type_ID\n" +
                "\t\tjoin Grp_Evl ge on ge.Grp_Evl_ID = gert.Grp_Evl_ID\n" +
                "    where gert.grp_evl_id in :groupEvaluationIds and\n" +
                "\t\tge.Evaluation_Method = 1\n" +
                "\tgroup by gert.Grp_Evl_ID , at.Accom_Type_Code\n" +
                "union all\n" +
                "select gedos.grp_evl_id,null, sum(Number_Of_Rooms) as NoOfRooms from Grp_Evl_Day_Of_Stay gedos\n" +
                "\t\t\tjoin Grp_Evl ge on ge.Grp_Evl_ID = gedos.Grp_Evl_ID\n" +
                "\t\twhere gedos.grp_evl_id in :groupEvaluationIds and \n" +
                "\t\t\t\tge.Evaluation_Method = 0\n" +
                "\t\tgroup by gedos.grp_evl_id";
    }

    private String getNativeQueryToFetchDisplacedRooms() {
        return "select gead.grp_evl_id, gead.Arrival_Date,   \n" +
                "  sum(case when (sign(geadfgac.displaced_rooms) = -1 and (cast(geadfgac.Occupancy_DT as date) < gead.Arrival_Date or  DATEADD(day, NoOfNights.nights -1 , gead.Arrival_Date) < cast(geadfgac.Occupancy_DT as date))) then 0 else geadfgac.displaced_rooms end) as Displaced_Rooms   \n" +
                "  from Grp_Evl_Arr_DT_FG_DT_AC geadfgac  \n" +
                "  join Grp_Evl_Arr_DT_FG geadfg on geadfg.Grp_Evl_Arr_DT_FG_ID = geadfgac.Grp_Evl_Arr_DT_FG_ID   \n" +
                "  join Grp_Evl_Arr_DT gead on geadfg.grp_evl_arr_dt_id = gead.grp_evl_arr_dt_id  \n" +
                "  join Grp_Evl ge on ge.Grp_Evl_ID = gead.Grp_Evl_ID  \n" +
                "  join (select distinct ge.Grp_Evl_ID, count (gertdos.Day_Of_Stay) nights  \n" +
                "     from Grp_Evl_Room_Type_Day_Of_Stay gertdos  \n" +
                "     join Grp_Evl_Room_Type gert on gert.Grp_Evl_Room_Type_ID = gertdos.Grp_Evl_Room_Type_ID  \n" +
                "     join Grp_Evl ge on ge.Grp_Evl_ID = gert.Grp_Evl_ID  \n" +
                "     group by ge.Grp_Evl_ID, gertdos.Grp_Evl_Room_Type_ID  \n" +
                "   ) as NoOfNights on NoOfNights.Grp_Evl_ID = ge.Grp_Evl_ID  \n" +
                "  where ge.grp_evl_id in :groupEvaluationIds   \n" +
                "  group by gead.grp_evl_id,gead.Arrival_Date  \n" +
                "  union all  \n" +
                "    select gead.grp_evl_id, gead.Arrival_Date, sum(case when ( sign(geadfgdt.displaced_rooms) = -1 and (cast(geadfgdt.Occupancy_DT as date) < gead.Arrival_Date or  DATEADD(day, genos.noOfDays -1 , gead.Arrival_Date) < cast(geadfgdt.Occupancy_DT as date))) then 0 else geadfgdt.displaced_rooms end)  \n" +
                "     from Grp_Evl_Arr_DT_FG_DT geadfgdt  \n" +
                "     join Grp_Evl_Arr_DT_FG geadfg on geadfg.Grp_Evl_Arr_DT_FG_ID = geadfgdt.Grp_Evl_Arr_DT_FG_ID   \n" +
                "     join Grp_Evl_Arr_DT gead on geadfg.grp_evl_arr_dt_id = gead.grp_evl_arr_dt_id  \n" +
                "     join Grp_Evl ge on ge.Grp_Evl_ID = gead.Grp_Evl_ID  \n" +
                "     join ( select ge.Grp_Evl_ID, count(*) noOfDays from Grp_Evl_Day_Of_Stay gedos   \n" +
                "        join Grp_Evl ge on ge.Grp_Evl_ID = gedos.Grp_Evl_ID  \n" +
                "        group by ge.Grp_Evl_ID ) as genos on ge.Grp_Evl_ID = genos.Grp_Evl_ID  \n" +
                "   where ge.Grp_Evl_ID in :groupEvaluationIds  \n" +
                "   group by gead.grp_evl_id,gead.Arrival_Date  ";
    }

    private String getNativeQueryToFetchGroupEvaluations() {
        return "  select \n" +

                "  ge.Group_Name,\n" +
                "  ge.Grp_Evl_ID,\n" +
                "  ge.Evaluation_Date,\n" +
                "  ge.Materialization_Status, \n" +

                "  case when ge.Grp_Evl_Multi_ID is null then 'false' else 'true' end as MultiPropertyEvaluation,\n" +
                "  case when gead.Preferred_Date = 1 then 'true' else 'false' end as Preferred_Date,\n" +
                "  ms.Mkt_Seg_Code,\n" +
                "  gead.Arrival_Date,\n" +
                "  case when ge.evaluation_method = 0 then 'ROH' else 'Room Class' end Evaluation_Method,\n" +

                "  at.Accom_type_Code,\n" +
                "  geadat.Rate,\n" +
                "  geadat.User_Adjusted_Rate,\n" +
                "  gead.Break_Even_Rate,\n" +
                "  gead.Average_Weighted_MAR,\n" +

                "  gead.Gross_Room_Revenue,\n" +
                "  gead.Net_Room_Revenue,\n" +
                "  gead.Room_Profit,\n" +
                "  gead.Net_Room_Revenue - ISNULL(gead.Net_Incremental_Room_Revenue,0) as displacedRevenueRooms,\n" +
                "  gead.Room_Profit - ISNULL(gead.Incremental_Room_Profit,0) - ISNULL(gead.Cost_Of_Walk,0) as displacedProfitRooms,\n" +

                "  gead.Cost_Of_Walk as costOfWalk,\n" +
                "  gead.Incremental_Room_Profit as NetProfit,\n" +
                "  case when gead.Gross_Room_Revenue is null or gead.Gross_Room_Revenue = 0 then 0 else (gead.Incremental_Room_Profit / gead.Gross_Room_Revenue) * 100 end as NetProfitPercentRooms,\n" +
                "  gead.Ancillary_Revenue as grossRevenueAncillary,\n" +
                "  gead.Ancillary_Revenue as netRevenueAncillary,\n" +

                "  gead.Ancillary_Profit AS Ancillary_Profit,\n" +
                "  gead.Displaced_Ancillary_Revenue as Displaced_Ancillary_Revenue,\n" +
                "  gead.Displaced_Ancillary_Profit as Displaced_Ancillary_Profit,\n" +
                "  case when gead.Displaced_Ancillary_Profit is null then gead.Ancillary_Profit else gead.Ancillary_Profit - ISNULL(gead.Displaced_Ancillary_Profit,0) end as netProfitAncillary,\n" +
                "  case when gead.Ancillary_Revenue <=0 then 0 else\n" +
                "  ((case when gead.Displaced_Ancillary_Profit is null then gead.Ancillary_Profit else gead.Ancillary_Profit - ISNULL(gead.Displaced_Ancillary_Profit,0) end) / gead.Ancillary_Revenue) * 100 end as netProfitPercentAncillary,\n" +

                "  gead.Conference_And_Banquet_Revenue,\n" +
                "  gead.Conference_And_Banquet_Profit as grossProfitConferenceAndBanquet,\n" +
                "  gead.Conference_And_Banquet_Profit as netProfitConferenceAndBanquet,\n" +
                "  case when gead.Conference_And_Banquet_Revenue is null or gead.Conference_And_Banquet_Revenue = 0 \n" +
                "    then 0 else (gead.Conference_And_Banquet_Profit / Conference_And_Banquet_Revenue) *100\n" +
                "    end  as netProfitPercentConferenceAndBanquet,\n" +

                "  ge.Contracted_Rate,\n" +
                "  ge.Notes as Notes, ge.sales_catering_booking_id \n" +

                "  from Grp_Evl_Arr_DT gead\n" +
                "  inner join Grp_Evl ge on ge.Grp_Evl_ID = gead.Grp_Evl_ID\n" +
                "  left join Grp_Evl_Arr_DT_AC geadac on geadac.Grp_Evl_Arr_DT_ID = gead.Grp_Evl_Arr_DT_ID \n" +
                "  left join Grp_Evl_Arr_DT_AT geadat on geadat.Grp_Evl_Arr_DT_AC_ID = geadac.Grp_Evl_Arr_DT_AC_ID\n" +
                "  left join Accom_Type at on at.Accom_Type_ID = geadat.Accom_Type_ID\n" +
                "  left join Mkt_Seg ms on ge.Mkt_Seg_ID = ms.Mkt_Seg_ID" +
                "  where ge.Evaluation_Date > :startDate";
    }

    private List<GroupEvaluationData> getGroupEvaluationData(Date startDate, int startPosition, int size) {
        List<GroupEvaluation> groupEvaluations = groupEvaluationService.getSavedGroupEvaluationsForDateRange(startDate, startPosition, size);
        Map<Integer, String> users = groupEvaluationService.getUserIdToFullnameMapForGroupEvaluations(groupEvaluations);

        Map<Integer, List<GroupEvaluationArrivalDateForecastGroupDateAC>> forecastGroupACDetails = getGroupEvaluationArrivalDateForecastGroupDateACList(groupEvaluations);

        final List<GroupEvaluationData> groupEvaluationsForDataFeed = new ArrayList<>();
        groupEvaluations.forEach(groupEvaluation -> {
                    String username = users.get(groupEvaluation.getLastUpdatedByUserId());
                    groupEvaluationsForDataFeed.addAll(transformGroupEvaluation(groupEvaluation, username, forecastGroupACDetails));
                }
        );
        return groupEvaluationsForDataFeed;
    }

    private Map<Integer, List<GroupEvaluationArrivalDateForecastGroupDateAC>> getGroupEvaluationArrivalDateForecastGroupDateACList(List<GroupEvaluation> groupEvaluations) {
        List<Integer> arrivalDateIds = new ArrayList<>();
        groupEvaluations.stream()
                .filter(GroupEvaluation::isRoomTypeEvaluation)
                .forEach(groupEvaluation ->
                        arrivalDateIds.addAll(
                                groupEvaluation.getGroupEvaluationArrivalDates().stream()
                                        .map(GroupEvaluationArrivalDate::getId)
                                        .collect(Collectors.toList())));

        if (arrivalDateIds.isEmpty()) {
            return Collections.emptyMap();
        }

        List<GroupEvaluationArrivalDateForecastGroupDateAC> list = tenantCrudService.
                findByNamedQuery(GroupEvaluationArrivalDateForecastGroupDateAC.FIND_BY_FORECAST_GROUP_ARRIVAL_DATE, getQueryParamWith("arrivalDateIds", arrivalDateIds));
        Map<Integer, List<GroupEvaluationArrivalDateForecastGroupDateAC>> forecastGroupsACForArrivalDate = new HashMap<>();
        arrivalDateIds.forEach(id -> forecastGroupsACForArrivalDate.put(id, new ArrayList<>()));
        list.forEach(entry -> {
                    int key = entry.getGroupEvaluationArrivalDateForecastGroup().getGroupEvaluationArrivalDate().getId();
                    forecastGroupsACForArrivalDate.get(key).add(entry);
                }
        );
        return forecastGroupsACForArrivalDate;
    }

    private List<GroupEvaluationData> transformGroupEvaluation(GroupEvaluation groupEvaluation, String username, Map<Integer, List<GroupEvaluationArrivalDateForecastGroupDateAC>> forecastGroupACDetails) {
        final List<GroupEvaluationData> groupEvaluationsForDataFeed = new ArrayList<>();
        if (isRoomClassEvaluationMethod(groupEvaluation)) {
            List<GroupEvaluationData> roomClassDataFeed = getDataFeedForRoomClass(groupEvaluation, username, forecastGroupACDetails);
            groupEvaluationsForDataFeed.addAll(roomClassDataFeed);
        } else { // this is ROH evaluation
            List<GroupEvaluationData> rohDataFeed = getDataFeedForROH(groupEvaluation, username);
            groupEvaluationsForDataFeed.addAll(rohDataFeed);
        }
        return groupEvaluationsForDataFeed;
    }

    private boolean isRoomClassEvaluationMethod(GroupEvaluation groupEvaluation) {
        return GroupPricingEvaluationMethod.RC == groupEvaluation.getEvaluationMethod();
    }

    private List<GroupEvaluationData> getDataFeedForROH(GroupEvaluation groupEvaluation, String username) {
        return groupEvaluation.getGroupEvaluationArrivalDates().stream().map(
                arrivalDateDto -> {
                    List<GroupEvaluationArrivalDateDisplacementAndForecastDetail> displacementAndForecastDetails =
                            GroupEvaluationData.computeDisplacedAndForecastDetailsForROH(arrivalDateDto);
                    GroupEvaluationData dataFeedRecord = createGroupEvaluationData(EVALUATION_METHOD_ROH, arrivalDateDto, username);
                    dataFeedRecord.setRecommendedRate(round(arrivalDateDto.getSuggestedRate()));
                    dataFeedRecord.setAdjustedRate(round(arrivalDateDto.getUserAdjustedRoomRate()));
                    dataFeedRecord.computeDisplacedRooms(displacementAndForecastDetails);
                    dataFeedRecord.computeIncrementalRooms(displacementAndForecastDetails);
                    return dataFeedRecord;
                }
        ).collect(Collectors.toList());
    }

    private List<GroupEvaluationData> getDataFeedForRoomClass(GroupEvaluation groupEvaluation, String username, Map<Integer, List<GroupEvaluationArrivalDateForecastGroupDateAC>> forecastGroupACDetails) {
        final List<GroupEvaluationData> dataFeedList = new ArrayList<>();
        Map<AccomType, Integer> accomTypeNumberOfRooms = new HashMap<>();
        groupEvaluation.getGroupEvaluationArrivalDates().forEach(
                arrivalDateDto -> {
                    List<GroupEvaluationArrivalDateDisplacementAndForecastDetail> displacementAndForecastDetails =
                            GroupEvaluationData.computeDisplacedAndForecastDetailsForRC(arrivalDateDto, forecastGroupACDetails.get(arrivalDateDto.getId()));
                    arrivalDateDto.getGroupEvaluationArrivalDateAccomClasses().forEach(
                            groupEvaluationArrivalDateAccomClass -> groupEvaluationArrivalDateAccomClass.getGroupEvaluationArrivalDateAccomTypes().forEach(
                                    groupEvaluationArrivalDateAccomType -> {
                                        GroupEvaluationData dataFeedRecord = createGroupEvaluationData(EVALUATION_METHOD_ROOM_CLASS, arrivalDateDto, username);
                                        dataFeedRecord.setRoomTypeCode(groupEvaluationArrivalDateAccomType.getAccomType().getAccomTypeCode());
                                        dataFeedRecord.setNumberOfRooms(getTotalNumberOfRooms(groupEvaluationArrivalDateAccomType.getAccomType(), groupEvaluation, accomTypeNumberOfRooms));
                                        dataFeedRecord.setRecommendedRate(round(groupEvaluationArrivalDateAccomType.getRate()));
                                        dataFeedRecord.setAdjustedRate(round(groupEvaluationArrivalDateAccomType.getUserAdjustedRate()));
                                        dataFeedRecord.computeDisplacedRooms(displacementAndForecastDetails);
                                        dataFeedRecord.computeIncrementalRooms(displacementAndForecastDetails);
                                        dataFeedList.add(dataFeedRecord);
                                    } // room type end
                            ) // room class end
                    );
                }  // date end
        );
        return dataFeedList;
    }

    private GroupEvaluationData createGroupEvaluationData(String evaluationMethod, GroupEvaluationArrivalDate groupEvaluationArrivalDate, String username) {
        GroupEvaluationData dataFeedRecord = new GroupEvaluationData(groupEvaluationArrivalDate, username);
        dataFeedRecord.setEvaluationMethod(evaluationMethod);
        return dataFeedRecord;
    }

    private int getTotalNumberOfRooms(AccomType accomType, GroupEvaluation groupEvaluation, Map<AccomType, Integer> accomTypeNumberOfRooms) {
        if (accomTypeNumberOfRooms.containsKey(accomType)) {
            return accomTypeNumberOfRooms.get(accomType);
        }
        accomTypeNumberOfRooms.put(accomType, getTotalNumberOfRoomsByAccomType(accomType, groupEvaluation));
        return accomTypeNumberOfRooms.get(accomType);
    }

    private int getTotalNumberOfRoomsByAccomType(AccomType accomType, GroupEvaluation groupEvaluation) {
        return groupEvaluation.getGroupEvaluationRoomTypes()
                .stream()
                .filter(e -> e.getRoomType().equals(accomType))
                .findFirst()
                .map(GroupEvaluationRoomType::getTotalNumberOfRooms).orElse(0);
    }
}