package com.ideas.tetris.pacman.services.forecast;


import org.springframework.aop.SpringProxy;
public interface ExpectedForecastConstant extends SpringProxy {

    String HIPHEN = "--";
    String PROPERTY_ID = "propertyId";
    String OCCUPANCY_START_DATE = "occupancyStartDate";
    String OCCUPANCY_END_DATE = "occupancyEndDate";
    String BUSINESS_END_DATE = "businessEndDate";
    String OCCUPANCY_DATE = "occupancyDate";
    String DOW_END_DATE = "dowEndDate";
    String PACE = "pace";
    String START_DATE = "startDate";
    String END_DATE = "endDate";
    String DAY_OF_WEEK = "dayOfWeek";
    String TRANSIENT_ON_BOOKS = "forecast.investigator.transient.on.books.table";
    String EXPECTED_ON_BOOKS = "forecast.investigator.expected.on.books.table";
    String STLY = "forecast.investigator.stly.on.books.table";
    String ST2Y = "forecast.investigator.st2y.on.books.table";
    String AVG_SAME_DOW_THIS_YEAR = "forecast.investigator.avgdow.on.books.table";
    String TRANSIENT_ON_BOOKS_OTHER = "forecast.investigator.transient.others.on.books.table";
    String GROUP_ON_BOOKS = "forecast.investigator.group.on.books.table";
    String TOTAL_FORECAST = "forecast.investigator.forecast.on.books.table";
    String TOTAL_ON_BOOKS = "forecast.investigator.total.on.books.table";
    String MY_FORECAST = "forecast.investigator.myforecast.on.books.table";
    String TRANSIENT_ON_BOOKS_PAST_DATA = "common.transient";
    String TRANSIENT_ON_BOOKS_OTHER_PAST_DATA = "forecast.investigator.transient.other.on.books.table.past.seven.days";
    String GROUPS_PAST_DATA = "common.header.group";
    String LABEL_PAST_DATA = "forecast.investigator.on.books.label.past.seven.days";
    Integer PAST_DAYS = 6;
    String DATE_FORMAT = "dd-MMM-yyyy";
    String OCCUPANCY_DEMAND_OVERRIDE = "forecast.investigator.occupancy.demand.override";
    String ARRIVAL_DEMAND_OVERRIDE = "forecast.investigator.arrival.demand.override";

}
