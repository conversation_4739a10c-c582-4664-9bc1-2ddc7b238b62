package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductHierarchy;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;

import javax.inject.Inject;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Component
@Transactional
public class PricingDataProductHierarchyDataService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;


    public Boolean loadProductHierarchyDataIntoPacman(List<ProductHierarchy> data) {
        StringBuilder queryToInsertData = new StringBuilder();

        data.forEach(productHierarchyDTO -> {
            queryToInsertData.append("INSERT INTO [dbo].[Product_Hierarchy] VALUES (" + productHierarchyDTO.getFromProduct().getId() + "," + productHierarchyDTO.getToProduct().getId() + ",'2021-07-15 07:27:44.920',11403,11403,'2021-07-15 07:27:44.920')");
        });

        if (!queryToInsertData.toString().isEmpty()) {
            tenantCrudService.executeUpdateByNativeQuery(queryToInsertData.toString());
            return true;
        }
        return false;
    }


    public Boolean deleteData() {

        StringBuilder dataToDelete = new StringBuilder("delete Product_Hierarchy where From_Product_ID = 1");
        tenantCrudService.executeUpdateByNativeQuery(dataToDelete.toString());
        return true;
    }

}
