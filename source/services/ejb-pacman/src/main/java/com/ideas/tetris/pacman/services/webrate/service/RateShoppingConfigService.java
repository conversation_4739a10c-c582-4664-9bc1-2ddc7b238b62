package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.pricing.ProductManagementService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.rateshoppingadjustment.entity.RateShoppingAdjustment;
import com.ideas.tetris.pacman.services.rateshoppingadjustment.service.RateShoppingAdjustmentService;
import com.ideas.tetris.pacman.services.webrate.dto.*;
import com.ideas.tetris.pacman.services.webrate.entity.*;
import com.ideas.tetris.pacman.services.webrate.entity.dcmpc.DcmpcCfg;
import com.ideas.tetris.pacman.services.webrate.entity.dcmpc.DcmpcCfgMapping;
import com.ideas.tetris.pacman.services.webrate.enums.RateShoppingConfigExcelEnum;
import com.ideas.tetris.pacman.services.webrate.seasonenum.SeasonFilterType;
import com.ideas.tetris.pacman.util.CollectionUtils;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.DEFAULT;
import static com.ideas.tetris.pacman.common.constants.Constants.SEASONAL;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.*;

@Component
public class RateShoppingConfigService {

    @Autowired
    private AccommodationMappingService accommodationMappingService;
    @Autowired
    WebrateChannelIgnoreService webrateChannelIgnoreService;
    @Autowired
    private WebrateShoppingDataService webrateShoppingDataService;
    @Autowired
    private ProductManagementService productManagementService;
    @Autowired
    private DynamicCMPCService dynamicCMPCService;
    public static final String D_MMM_YYYY = "d-MMM-yyyy";

    @Autowired
    WebrateDataSchedulingService webrateDataSchedulingService;
    @Autowired
    RateShoppingAdjustmentService rateShoppingAdjustmentService;
    @Autowired
    private PacmanConfigParamsService pacmanConfigParamsService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    private CrudService tenantCrudService;

    private List<WebrateCompetitors> webrateCompetitors;
    Map<Integer, WebrateCompetitors> webrateCompetitorsMap;
    private Map<Integer, String> productMap;

    public List<RoomClassMappingConfigExcelDto> getRateShoppingConfigDataFor(RateShoppingConfigExcelEnum rateShoppingConfigExcelEnum) {
        if (rateShoppingConfigExcelEnum == RateShoppingConfigExcelEnum.ROOM_CLASS_MAPPING) {
            return getRoomClassMappingConfigs();
        }
        return new ArrayList<>();
    }

    public List<OccupancyBasedCMPCConfigExcelDto> getOccupancyBasedConfigData(boolean shouldSetAdditionalFields) {
        Map<String, OccupancyBasedCMPCConfigExcelDto> mapUniqueConfig = new HashMap<>();
        productMap = new HashMap<>();
        List<DcmpcCfgMapping> mappings = dynamicCMPCService.getAllDcmpcCfgMapping();
        setProductMap(shouldSetAdditionalFields, mappings);

        AtomicInteger index = new AtomicInteger();
        mappings.forEach(mapping -> {
            String uniqueDTOId = mapping.getDcmpcCfg().getProductId() + ":" + mapping.getDcmpcCfg().getAccomClass().getId() + ":" + mapping.getDcmpcCfg().getStartDate() + ":" + mapping.getDcmpcCfg().getEndDate() + ":" + index;
            OccupancyBasedCMPCConfigExcelDto dto = mapUniqueConfig.computeIfAbsent
                    (uniqueDTOId, dtoId -> getOccupancyBasedCMPCConfigExcelDto(mapping, shouldSetAdditionalFields));

            String percentile = String.valueOf(mapping.getDcmpcCfgDetail().getPercentile());
            String onBooksThreshold = String.valueOf(mapping.getDcmpcCfgDetail().getOnBooksThreshold());
            dto = setDowOccupancyBasedCmpcCfgs(index, mapping, dto, percentile, onBooksThreshold, shouldSetAdditionalFields);

            uniqueDTOId = mapping.getDcmpcCfg().getProductId() + ":" + mapping.getDcmpcCfg().getAccomClass().getId() + ":" + mapping.getDcmpcCfg().getStartDate() + ":" + mapping.getDcmpcCfg().getEndDate() + ":" + index;
            mapUniqueConfig.put(uniqueDTOId, dto);
        });

        return mapUniqueConfig.values().stream().sorted(Comparator.comparing(OccupancyBasedCMPCConfigExcelDto::getStartDate, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(OccupancyBasedCMPCConfigExcelDto::getAccomClass, Comparator.nullsFirst(Comparator.naturalOrder()))).collect(Collectors.toList());
    }

    private void setProductMap(boolean shouldSetAdditionalFields, List<DcmpcCfgMapping> mappings) {
        if (shouldSetAdditionalFields) {
            List<Product> productList = tenantCrudService.findAll(Product.class);
            Set<Integer> productIds = mappings.stream().map(mapping -> mapping.getDcmpcCfg().getProductId()).collect(Collectors.toSet());
            productMap = getProductMap(productList, productIds);
        }
    }

    private Map<Integer, String> getProductMap(List<Product> productList, Set<Integer> productIds) {
        return productList.stream().filter(product -> productIds.contains(product.getId()))
                .collect(Collectors.toMap(Product::getId, Product::getName));
    }

    private void setAdditionalDtoFields(OccupancyBasedCMPCConfigExcelDto dto, DcmpcCfgMapping dcmpcCfgMapping) {
        dto.setProductName(productMap.get(dcmpcCfgMapping.getDcmpcCfg().getProductId()));
        dto.setCategory(getOccupancyBasedCategory(dcmpcCfgMapping));
    }

    private String getOccupancyBasedCategory(DcmpcCfgMapping mapping) {
        DcmpcCfg dcmpcCfg = mapping.getDcmpcCfg();
        return (dcmpcCfg.getStartDate() == null && dcmpcCfg.getEndDate() == null) ? DEFAULT : SEASONAL;
    }

    private OccupancyBasedCMPCConfigExcelDto setDowOccupancyBasedCmpcCfgs(AtomicInteger index, DcmpcCfgMapping mapping, OccupancyBasedCMPCConfigExcelDto dto, String percentile, String onBooksThreshold, boolean shouldSetAdditionalFields) {
        switch (mapping.getDowId()) {
            case 1:
                dto = getOccupancyBasedCMPCConfigExcelDto(dto.getMobSunday(), dto.getMmpSunday(), index, mapping, dto, shouldSetAdditionalFields);
                dto.setMmpSunday(percentile);
                dto.setMobSunday(onBooksThreshold);
                break;
            case 2:
                dto = getOccupancyBasedCMPCConfigExcelDto(dto.getMobMonday(), dto.getMmpMonday(), index, mapping, dto, shouldSetAdditionalFields);
                dto.setMmpMonday(percentile);
                dto.setMobMonday(onBooksThreshold);
                break;
            case 3:
                dto = getOccupancyBasedCMPCConfigExcelDto(dto.getMobTuesday(), dto.getMmpTuesday(), index, mapping, dto, shouldSetAdditionalFields);
                dto.setMmpTuesday(percentile);
                dto.setMobTuesday(onBooksThreshold);
                break;
            case 4:
                dto = getOccupancyBasedCMPCConfigExcelDto(dto.getMobWednesday(), dto.getMmpWednesday(), index, mapping, dto, shouldSetAdditionalFields);
                dto.setMmpWednesday(percentile);
                dto.setMobWednesday(onBooksThreshold);
                break;
            case 5:
                dto = getOccupancyBasedCMPCConfigExcelDto(dto.getMobThursday(), dto.getMmpThursday(), index, mapping, dto, shouldSetAdditionalFields);
                dto.setMmpThursday(percentile);
                dto.setMobThursday(onBooksThreshold);
                break;
            case 6:
                dto = getOccupancyBasedCMPCConfigExcelDto(dto.getMobFriday(), dto.getMmpFriday(), index, mapping, dto, shouldSetAdditionalFields);
                dto.setMmpFriday(percentile);
                dto.setMobFriday(onBooksThreshold);
                break;
            case 7:
                dto = getOccupancyBasedCMPCConfigExcelDto(dto.getMobSaturday(), dto.getMmpSaturday(), index, mapping, dto, shouldSetAdditionalFields);
                dto.setMmpSaturday(percentile);
                dto.setMobSaturday(onBooksThreshold);
                break;
        }
        return dto;
    }

    private OccupancyBasedCMPCConfigExcelDto getOccupancyBasedCMPCConfigExcelDto(String dowOnBooksThreshold, String dowPercentile, AtomicInteger index, DcmpcCfgMapping mapping, OccupancyBasedCMPCConfigExcelDto dto, boolean shouldSetAdditionalFields) {
        if (dowOnBooksThreshold != null && dowPercentile != null) {
            index.getAndIncrement();
            dto = getOccupancyBasedCMPCConfigExcelDto(mapping, shouldSetAdditionalFields);
        }
        return dto;
    }

    private OccupancyBasedCMPCConfigExcelDto getOccupancyBasedCMPCConfigExcelDto(DcmpcCfgMapping mapping, boolean shouldSetAdditionalFields) {
        OccupancyBasedCMPCConfigExcelDto dto;
        dto = new OccupancyBasedCMPCConfigExcelDto();
        if (shouldSetAdditionalFields) {
            setAdditionalDtoFields(dto, mapping);
        }
        dto.setAccomClass(mapping.getDcmpcCfg().getAccomClass().getName());
        dto.setStartDate(mapping.getDcmpcCfg().getStartDate() != null ? JavaLocalDateUtils.toDate(mapping.getDcmpcCfg().getStartDate()) : null);
        dto.setEndDate(mapping.getDcmpcCfg().getEndDate() != null ? JavaLocalDateUtils.toDate(mapping.getDcmpcCfg().getEndDate()) : null);
        return dto;
    }

    private List<RoomClassMappingConfigExcelDto> getRoomClassMappingConfigs(){
        List<RoomClassMappingConfigExcelDto> roomClassMappingConfigExcelDtos = new ArrayList<>();
        List<WebrateAccomType> webrateAccomTypes = accommodationMappingService.getAccomodationMappingByProperty();
        webrateAccomTypes.forEach(at -> Optional.ofNullable(at.getWebrateAccomClassMappings()).ifPresent(webrateAccomClassMappings -> {
            webrateAccomClassMappings.forEach(ac -> {
                RoomClassMappingConfigExcelDto roomClassMappingConfigExcelDto = RoomClassMappingConfigExcelDto.builder()
                        .competitiveRoomType(at.getWebrateAccomName())
                        .webrateAccomAlias(at.getWebrateAccomAlias())
                        .accomClass(ac.getAccomClass().getName()).build();
                roomClassMappingConfigExcelDtos.add(roomClassMappingConfigExcelDto);
            });
        }));
        return roomClassMappingConfigExcelDtos;
    }

    public List<IgnoreCompetitorDataExcelDTO> getIgnoreCompetitorData(RateShoppingConfigExcelEnum rateShoppingConfigExcelEnum, List<WebrateCompetitors> competitors) {
        if(rateShoppingConfigExcelEnum == RateShoppingConfigExcelEnum.IGNORE_COMPETITOR_DATA) {
            boolean isNewUiEnabled = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_UI_IGNORE_COMPETITOR);
            return isNewUiEnabled ? getIgnoreCompetitorDataExcelNewUi(competitors) : getIgnoreCompetitorDataExcelOldUi(competitors);
        }
        return new ArrayList<>();
    }

    public List<IgnoreCompetitorDataExcelDTO> getIgnoreCompetitorDataExcelOldUi(List<WebrateCompetitors> competitors) {
        List<IgnoreCompetitorDataExcelDTO> ignoreCompetitorDataExcelDtos = new ArrayList<>();
        List<Product> allEligibleProducts = productManagementService.getAllEligibleProductsForRateShopping();
        Map<Integer, List<WebrateChannel>> detaillsToChannelMap = getWebrateShoppingCompMappings();
        Set<WebrateCompetitors> activeDemandOrRankingEnabledWebrateCompetitorsExcludingSelf = allEligibleProducts.stream()
                .flatMap(product -> getAllActiveDemandOrRankingEnabledWebrateCompetitorsExcludingSelf(product, competitors).stream())
                .collect(Collectors.toSet());
        List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetails = getWebrateOvrCompetitorDetails(activeDemandOrRankingEnabledWebrateCompetitorsExcludingSelf);
        webrateOverrideCompetitorDetails.forEach(competitor -> {
            IgnoreCompetitorDataExcelDTO excelDTO = new IgnoreCompetitorDataExcelDTO();
            excelDTO.setCompetitorName(competitor.getWebrateCompetitorsAccomClass().getWebrateCompetitor().getWebrateCompetitorsName());
            excelDTO.setProduct(getProductName(allEligibleProducts, competitor.getProductID()));
            excelDTO.setAccomClass(competitor.getWebrateCompetitorsAccomClass().getAccomClass().getName());
            populateChannelNameIntoExcel(detaillsToChannelMap, excelDTO, competitor);
            excelDTO.setStartDate(competitor.getWebrateOverrideCompetitor().getCompetitorOverrideStartDT());
            excelDTO.setEndDate(competitor.getWebrateOverrideCompetitor().getCompetitorOverrideEndDT());
            excelDTO.setSunday(competitor.getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnSunday() == 1 ? "TRUE" : "FALSE");
            excelDTO.setMonday(competitor.getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnMonday() == 1 ? "TRUE" : "FALSE");
            excelDTO.setTuesday(competitor.getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnTuesday() == 1 ? "TRUE" : "FALSE");
            excelDTO.setWednesday(competitor.getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnWednesday() == 1 ? "TRUE" : "FALSE");
            excelDTO.setThursday(competitor.getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnThursday() == 1 ? "TRUE" : "FALSE");
            excelDTO.setFriday(competitor.getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnFriday() == 1 ? "TRUE" : "FALSE");
            excelDTO.setSaturday(competitor.getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnSaturday() == 1 ? "TRUE" : "FALSE");
            ignoreCompetitorDataExcelDtos.add(excelDTO);
        });
        return ignoreCompetitorDataExcelDtos;
    }

    private List<WebrateOverrideCompetitorDetails> getWebrateOvrCompetitorDetails(Set<WebrateCompetitors> webrateCompetitors) {
        return tenantCrudService.findByNamedQuery(WebrateOverrideCompetitorDetails.BY_COMPETITOR,
                        QueryParameter.with("competitors", webrateCompetitors).parameters());
    }

    public List<IgnoreCompetitorDataExcelDTO> getIgnoreCompetitorDataExcelNewUi(List<WebrateCompetitors> allCompetitors) {
        List<IgnoreCompetitorDataExcelDTO> ignoreCompetitorDataExcelDtos = new ArrayList<>();
        List<Product> allEligibleProducts = productManagementService.getAllEligibleProductsForRateShopping();
        for(Product product : allEligibleProducts) {
            List<WebrateCompetitors> allCompetitorsExcludingSelf = getAllActiveDemandOrRankingEnabledWebrateCompetitorsExcludingSelf(product, allCompetitors);
            List<IgnoreCompetitorDataDTO> ignoreCompetitorDataDTOS = getAllIgnoreCompetitorDataDtosForUi(product,null, allCompetitorsExcludingSelf).stream()
                    .map(dto -> (IgnoreCompetitorDataDTO)dto).collect(Collectors.toList());
            for(IgnoreCompetitorDataDTO dto : ignoreCompetitorDataDTOS) {
                List<String> channels = dto.getWebrateChannels().stream()
                        .map(WebrateChannel::getWebrateChannelAlias).collect(Collectors.toList());
                List<String> accomClasses = dto.getAccomClasses().stream()
                        .map(AccomClass::getName).collect(Collectors.toList());
                Set<WebrateCompetitors> competitors = dto.getWebrateCompetitors();
                for(WebrateCompetitors competitor: competitors) {
                    for (String accomClass : accomClasses) {
                        setDataToExcel(ignoreCompetitorDataExcelDtos, product, dto, channels, accomClass, competitor.getWebrateCompetitorsAlias());
                    }
                }
            }
        }
        return ignoreCompetitorDataExcelDtos;
    }

    public void setDataToExcel(List<IgnoreCompetitorDataExcelDTO> ignoreCompetitorDataExcelDtos,
                                Product product,
                                IgnoreCompetitorDataDTO dto,
                                List<String> channels,
                                String accomClass,
                                String competitorAlias) {
        boolean isNewUiEnabled = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_UI_IGNORE_COMPETITOR);
        if (isNewUiEnabled) {
            for (String channel : channels) {
                IgnoreCompetitorDataExcelDTO excelDTO = setValuesToExcelDTO(product, dto, accomClass, competitorAlias);
                excelDTO.setChannel(channel);
                ignoreCompetitorDataExcelDtos.add(excelDTO);
            }
        } else {
            IgnoreCompetitorDataExcelDTO excelDTO = setValuesToExcelDTO(product, dto, accomClass, competitorAlias);
            ignoreCompetitorDataExcelDtos.add(excelDTO);
        }
    }

    private IgnoreCompetitorDataExcelDTO setValuesToExcelDTO(Product product, IgnoreCompetitorDataDTO dto, String accomClass, String competitorAlias) {
        IgnoreCompetitorDataExcelDTO excelDTO = new IgnoreCompetitorDataExcelDTO();
        excelDTO.setProduct(product.getName());
        excelDTO.setCompetitorName(competitorAlias);
        excelDTO.setStartDate(DateUtil.convertLocalDateToJavaUtilDate(dto.getStartDate()));
        excelDTO.setEndDate(DateUtil.convertLocalDateToJavaUtilDate(dto.getEndDate()));
        setSelectedDOW(dto, excelDTO);
        excelDTO.setAccomClass(accomClass);
        return excelDTO;
    }

    private static void setSelectedDOW(IgnoreCompetitorDataDTO dto, IgnoreCompetitorDataExcelDTO excelDTO) {
        setDefaultValuesOfDOW(excelDTO);
        dto.getSelectedDaysOfWeek().forEach(dow -> {
            Integer dayOfWeek = dow.getCalendarDayOfWeek();
            switch (dayOfWeek) {
                case 1: excelDTO.setMonday("TRUE");
                break;
                case 2: excelDTO.setTuesday("TRUE");
                break;
                case 3: excelDTO.setWednesday("TRUE");
                break;
                case 4: excelDTO.setThursday("TRUE");
                break;
                case 5: excelDTO.setFriday("TRUE");
                break;
                case 6: excelDTO.setSaturday("TRUE");
                break;
                case 7: excelDTO.setSunday("TRUE");
                break;
            }
        });
    }

    private static void setDefaultValuesOfDOW(IgnoreCompetitorDataExcelDTO excelDTO) {
        excelDTO.setMonday("FALSE");
        excelDTO.setTuesday("FALSE");
        excelDTO.setWednesday("FALSE");
        excelDTO.setThursday("FALSE");
        excelDTO.setFriday("FALSE");
        excelDTO.setSaturday("FALSE");
        excelDTO.setSunday("FALSE");
    }

    public Map<Integer, List<WebrateChannel>> getWebrateShoppingCompMappings() {
        Map<Integer, List<WebrateChannel>> detaillsToChannelMap;
        boolean isNewUiEnabled = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_UI_IGNORE_COMPETITOR);
        if(isNewUiEnabled) {
            List<WebrateCompChannelMapping> webrateCompChannelMappings = webrateShoppingDataService.getWebrateCompChannelMappings();
            detaillsToChannelMap = webrateCompChannelMappings.stream()
                    .collect(Collectors.groupingBy(WebrateCompChannelMapping::getId, Collectors.mapping(WebrateCompChannelMapping::getWebrateChannel, Collectors.toList())));
        } else {
            detaillsToChannelMap = new HashMap<>();
        }
        return detaillsToChannelMap;
    }

    private void populateChannelNameIntoExcel(Map<Integer, List<WebrateChannel>> detaillsToChannelMap, IgnoreCompetitorDataExcelDTO excelDTO, WebrateOverrideCompetitorDetails competitor) {
        boolean isNewUiEnabled = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_UI_IGNORE_COMPETITOR);
        if(isNewUiEnabled) {
            List<WebrateChannel> webrateChannel = detaillsToChannelMap.get(competitor.getId());
            if (CollectionUtils.isNotEmpty(webrateChannel)) {
                String channel = webrateChannel.stream()
                        .map(WebrateChannel::getWebrateChannelName)
                        .collect(Collectors.joining(", "));
                excelDTO.setChannel(channel);
            } else {
                excelDTO.setChannel("");
            }
        }
    }

    public List<DisplayChannelConfigExcelDTO> getDisplayChannelConfigs() {
        List<DisplayChannelConfigExcelDTO> displayChannelConfigExcelDTOs = new ArrayList<>();
        List<WebrateChannel> webrateChannels = accommodationMappingService.getWebRateChannelsList();

        webrateChannels.forEach(at -> {
            DisplayChannelConfigExcelDTO displayChannelConfigExcelDTO = DisplayChannelConfigExcelDTO.builder()
                    .channelName(at.getWebrateChannelName())
                    .displayName(at.getWebrateChannelAlias())
                    .usedForDisplay(at.getStatusId() == 1 ? "TRUE" : "FALSE")
                    .build();
            displayChannelConfigExcelDTOs.add(displayChannelConfigExcelDTO);
        });

        return displayChannelConfigExcelDTOs;
    }

    public List<IgnoreChannelConfigDTO> getIgnoreChannelConfigs(boolean isDatafeedEnabled) {
        List<IgnoreChannelConfigDTO> ignoreChannelConfigDTOs = new ArrayList<>();
        List<WebrateChannelIgnoreConfig> webrateChannelIgnoreConfigs = webrateChannelIgnoreService.getAllWebrateChannelsIgnored();

        webrateChannelIgnoreConfigs.forEach(at -> {
            IgnoreChannelConfigDTO ignoreChannelConfigDTO = IgnoreChannelConfigDTO.builder()
                    .channelName(at.getWebrateChannel().getWebrateChannelName())
                    .sunday(formatIgnoreValue(at.getIgnoreChannelOnSunday(), isDatafeedEnabled))
                    .monday(formatIgnoreValue(at.getIgnoreChannelOnMonday(), isDatafeedEnabled))
                    .tuesday(formatIgnoreValue(at.getIgnoreChannelOnTuesday(), isDatafeedEnabled))
                    .wednesday(formatIgnoreValue(at.getIgnoreChannelOnWednesday(), isDatafeedEnabled))
                    .thursday(formatIgnoreValue(at.getIgnoreChannelOnThursday(), isDatafeedEnabled))
                    .friday(formatIgnoreValue(at.getIgnoreChannelOnFriday(), isDatafeedEnabled))
                    .saturday(formatIgnoreValue(at.getIgnoreChannelOnSaturday(), isDatafeedEnabled))
            .build();
            ignoreChannelConfigDTOs.add(ignoreChannelConfigDTO);
        });

        return ignoreChannelConfigDTOs;
    }

    private String formatIgnoreValue(Boolean ignoreValue, boolean isDatafeedEnabled) {
        String value = ignoreValue.toString().toUpperCase();
        return isDatafeedEnabled ? ("TRUE".equals(value) ? "Yes" : "No") : value;
    }


    public List<DefaultChannelSettingsConfigDTO> getDefaultChannelSettingsConfigs() {
        List<DefaultChannelSettingsConfigDTO> defaultChannelSettingsConfigDTOs = new ArrayList<>();
        addWebrateDefaultChannelInDTO(defaultChannelSettingsConfigDTOs);
        addWebrateOverrideChannelInDTO(defaultChannelSettingsConfigDTOs);
        return defaultChannelSettingsConfigDTOs;
    }

    private void addWebrateOverrideChannelInDTO(List<DefaultChannelSettingsConfigDTO> defaultChannelSettingsConfigDTOs) {
        List<Object[]> webrateOverrideChannels = webrateChannelIgnoreService.getAllWebrateOverrideChannel();
        if (Objects.nonNull(webrateOverrideChannels) && !webrateOverrideChannels.isEmpty()) {
            webrateOverrideChannels.stream()
                    .filter(record -> Objects.nonNull(record[0]))
                    .forEach(record -> {
                        WebrateOverrideChannel channel = (WebrateOverrideChannel) record[0];
                        String productName = (String) record[1];
                        DefaultChannelSettingsConfigDTO dto = DefaultChannelSettingsConfigDTO.builder()
                                .product(productName)
                                .seasonName(channel.getWebrateOverrideName())
                                .startDate(new SimpleDateFormat(D_MMM_YYYY).format(channel.getChannelOverrideStartDT()))
                                .endDate(new SimpleDateFormat(D_MMM_YYYY).format(channel.getChannelOverrideEndDT()))
                                .sunday(channel.getWebrateChannelSun().getWebrateChannelAlias())
                                .monday(channel.getWebrateChannelMon().getWebrateChannelAlias())
                                .tuesday(channel.getWebrateChannelTues().getWebrateChannelAlias())
                                .wednesday(channel.getWebrateChannelWed().getWebrateChannelAlias())
                                .thursday(channel.getWebrateChannelThurs().getWebrateChannelAlias())
                                .friday(channel.getWebrateChannelFri().getWebrateChannelAlias())
                                .saturday(channel.getWebrateChannelSat().getWebrateChannelAlias())
                                .build();
                        defaultChannelSettingsConfigDTOs.add(dto);
                    });
        }
    }

    private void addWebrateDefaultChannelInDTO(List<DefaultChannelSettingsConfigDTO> defaultChannelSettingsConfigDTOs) {
        List<Object[]> webrateDefaultChannels = webrateChannelIgnoreService.getAllWebrateDefaultChannel();
        if (Objects.nonNull(webrateDefaultChannels) && !webrateDefaultChannels.isEmpty()) {
            webrateDefaultChannels.stream()
                    .filter(record -> Objects.nonNull(record[0]))
                    .forEach(record -> {
                        WebrateDefaultChannel channel = (WebrateDefaultChannel) record[0];
                        String productName = (String) record[1];
                        DefaultChannelSettingsConfigDTO dto = DefaultChannelSettingsConfigDTO.builder()
                                .product(productName)
                                .sunday(channel.getWebrateChannelSun().getWebrateChannelAlias())
                                .monday(channel.getWebrateChannelMon().getWebrateChannelAlias())
                                .tuesday(channel.getWebrateChannelTues().getWebrateChannelAlias())
                                .wednesday(channel.getWebrateChannelWed().getWebrateChannelAlias())
                                .thursday(channel.getWebrateChannelThurs().getWebrateChannelAlias())
                                .friday(channel.getWebrateChannelFri().getWebrateChannelAlias())
                                .saturday(channel.getWebrateChannelSat().getWebrateChannelAlias())
                                .build();
                        defaultChannelSettingsConfigDTOs.add(dto);
                    });
        }
    }

    public List<StandardCMPCConfigExcelDTO> getStandardCMPCConfig() {
        List<StandardCMPCConfigExcelDTO> standardCMPCConfigExcelDTOS = new ArrayList<>();
        standardCMPCConfigExcelDTOS.addAll(getStandardDefaultCMPCConfigs());
        standardCMPCConfigExcelDTOS.addAll(getStandardSeasonsCMPCConfigs());
        return standardCMPCConfigExcelDTOS;
    }

    private List<StandardCMPCConfigExcelDTO> getStandardSeasonsCMPCConfigs() {
        List<WebrateRankingAccomClassOverride> standardSeasonsCMPCConfig = accommodationMappingService.getStandardSeasonCMPCConfig();
        List<Product> allEligibleProducts = productManagementService.getAllEligibleProductsForRateShopping();
        return standardSeasonsCMPCConfig.stream()
                .filter(seasonsCMPCConfig -> isProductEligible(allEligibleProducts,seasonsCMPCConfig.getProductID()))
                .map(seasonsCMPCConfig -> getStandardSeasonsCMPCConfigExcelDto(allEligibleProducts,seasonsCMPCConfig))
                .collect(Collectors.toList());
    }

    public StandardCMPCConfigExcelDTO getStandardSeasonsCMPCConfigExcelDto(List<Product> allEligibleProducts,WebrateRankingAccomClassOverride standardSeasonsCMPC){
        return StandardCMPCConfigExcelDTO.builder()
                .product(getProductName(allEligibleProducts, standardSeasonsCMPC.getProductID()))
                .accomClass(standardSeasonsCMPC.getAccomClass().getName())
                .startDate(new SimpleDateFormat(D_MMM_YYYY).format(standardSeasonsCMPC.getWebrateRankingStartDT()))
                .endDate(new SimpleDateFormat(D_MMM_YYYY).format(standardSeasonsCMPC.getWebrateRankingEndDT()))
                .sunday(standardSeasonsCMPC.getWebrateRankingOvrSunday().getWebrateRankingName())
                .monday(standardSeasonsCMPC.getWebrateRankingOvrMonday().getWebrateRankingName())
                .tuesday(standardSeasonsCMPC.getWebrateRankingOvrTuesday().getWebrateRankingName())
                .wednesday(standardSeasonsCMPC.getWebrateRankingOvrWednesday().getWebrateRankingName())
                .thursday(standardSeasonsCMPC.getWebrateRankingOvrThursday().getWebrateRankingName())
                .friday(standardSeasonsCMPC.getWebrateRankingOvrFriday().getWebrateRankingName())
                .saturday(standardSeasonsCMPC.getWebrateRankingOvrSaturday().getWebrateRankingName())
                .build();
    }

    private List<StandardCMPCConfigExcelDTO> getStandardDefaultCMPCConfigs() {
        List<WebrateRankingAccomClass> standardDefaultCMPCConfig = accommodationMappingService.getStandardDefaultCMPCConfig();
        List<Product> allEligibleProducts = productManagementService.getAllEligibleProductsForRateShopping();
        return standardDefaultCMPCConfig.stream()
                .filter(defaultCMPCConfig -> isProductEligible(allEligibleProducts,defaultCMPCConfig.getProductID()))
                .map(defaultCMPCConfig -> getStandardDefaultCMPCConfigExcelDto(allEligibleProducts,defaultCMPCConfig))
                .collect(Collectors.toList());
    }

    public StandardCMPCConfigExcelDTO getStandardDefaultCMPCConfigExcelDto(List<Product> allEligibleProducts,WebrateRankingAccomClass standardDefaultCMPC){
        return StandardCMPCConfigExcelDTO.builder()
                .product(getProductName(allEligibleProducts, standardDefaultCMPC.getProductID()))
                .accomClass(standardDefaultCMPC.getAccomClass().getName())
                .sunday(standardDefaultCMPC.getWebrateRankingSunday().getWebrateRankingName())
                .monday(standardDefaultCMPC.getWebrateRankingMonday().getWebrateRankingName())
                .tuesday(standardDefaultCMPC.getWebrateRankingTuesday().getWebrateRankingName())
                .wednesday(standardDefaultCMPC.getWebrateRankingWednesday().getWebrateRankingName())
                .thursday(standardDefaultCMPC.getWebrateRankingThursday().getWebrateRankingName())
                .friday(standardDefaultCMPC.getWebrateRankingFriday().getWebrateRankingName())
                .saturday(standardDefaultCMPC.getWebrateRankingSaturday().getWebrateRankingName())
                .build();
    }
    private boolean isProductEligible(List<Product> allEligibleProducts, Integer productID) {
        return allEligibleProducts.stream()
                .anyMatch(product -> product.getId().equals(productID));
    }
    public String getProductName(List<Product> allProductsList, Integer productID) {
        return Optional.ofNullable(productID)
                .flatMap(id -> allProductsList.stream()
                        .filter(product -> id.equals(product.getId()))
                        .map(Product::getName)
                        .findFirst())
                .orElse("");
    }

    public List<CompetitorSettingConfigExcelDto> getCompetitorSettingData(){
        return webrateShoppingDataService.getALlCompetitorSettingsForExportToExcel();
    }

    public List<RateShoppingScheduleConfigExcelDto> getRateShoppingScheduleConfigs() {
        List<WebrateShoppingConfig> webrateShoppingConfigs = webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty();
        return webrateShoppingConfigs.stream()
                .map(this::getRateShoppingScheduleConfigExcelDto)
                .collect(Collectors.toList());
    }
    private RateShoppingScheduleConfigExcelDto getRateShoppingScheduleConfigExcelDto(WebrateShoppingConfig config) {
        return RateShoppingScheduleConfigExcelDto.builder()
                .daysInTheShop(Optional.ofNullable(config.getRollingDaysToShop()).orElse(0))
                .numberOfDaysUntilTheNextShop(Optional.ofNullable(config.getWebrateShoppingFrequency()).orElse(0))
                .numberOfDaysBeforeShopIsOverdue(Optional.ofNullable(config.getWebrateShoppingThreshold()).orElse(0))
                .build();
    }

    public RateAdjustmentSeasonAndDefaultDtos getRateAdjustmentConfigs() {
        List<RateShoppingAdjustment> rateShoppingAdjustments = rateShoppingAdjustmentService.getAllRateShoppingAdjustmentConfig();
        List<WebrateCompetitors> allCompetitors = getAllCompetitorsByProperty();
        List<RateAdjustmentConfigExcelDto> defaultRateAdjustmentConfigExcelDto = new ArrayList<>();
        List<RateAdjustmentConfigExcelDto> seasonRateAdjustmentConfigExcelDto = new ArrayList<>();
        rateShoppingAdjustments.stream()
                .map(rateAdjustment -> getRateAdjustmentConfigExcelDto(rateAdjustment, allCompetitors))
                .forEach(dto -> (dto.getSeasonName().isEmpty() ? defaultRateAdjustmentConfigExcelDto : seasonRateAdjustmentConfigExcelDto).add(dto));
        return new RateAdjustmentSeasonAndDefaultDtos(defaultRateAdjustmentConfigExcelDto, seasonRateAdjustmentConfigExcelDto);
    }
    private RateAdjustmentConfigExcelDto getRateAdjustmentConfigExcelDto(RateShoppingAdjustment rateAdjustment, List<WebrateCompetitors> allCompetitors) {
        SimpleDateFormat dateFormatter = new SimpleDateFormat(D_MMM_YYYY);
        return RateAdjustmentConfigExcelDto.builder()
                .competitorName(Optional.ofNullable(rateAdjustment.getCompetitorId()).map(competitorId -> getCompetitorName(competitorId, allCompetitors)).orElse("Default"))
                .seasonName(Optional.ofNullable(rateAdjustment.getName()).orElse(""))
                .startDate(Optional.ofNullable(rateAdjustment.getStartDate()).map(date -> dateFormatter.format(date.toDate())).orElse(""))
                .endDate(Optional.ofNullable(rateAdjustment.getEndDate()).map(date -> dateFormatter.format(date.toDate())).orElse(""))
                .taxType(Optional.ofNullable(rateAdjustment.getTaxOffsetType()).orElse(""))
                .taxValue(Optional.ofNullable(rateAdjustment.getTaxOffsetValue()).map(taxOffsetValue -> String.valueOf(taxOffsetValue.negate())).orElse(""))
                .otherType(Optional.ofNullable(rateAdjustment.getOtherOffsetType()).orElse(""))
                .otherValue(Optional.ofNullable(rateAdjustment.getOtherOffsetValue()).map(otherOffsetValue -> String.valueOf(otherOffsetValue.negate())).orElse(""))
                .build();
    }

    private String getCompetitorName(Integer competitorId, List<WebrateCompetitors> allCompetitors){
        return allCompetitors.stream()
                .filter(competitor -> competitor.getId().equals(competitorId))
                .map(WebrateCompetitors::getWebrateCompetitorsName)
                .findFirst()
                .orElse("");
    }

    public List<WebrateCompetitors> getAllCompetitorsByProperty() {
        return webrateShoppingDataService.getAllCompetitorsByProperty();
    }

    public List<IgnoreCompetitorDataDTO> getAllIgnoreCompetitorDataDtosForUi(Product product, LocalDate systemDateAsLocalDate, List<WebrateCompetitors> allCompetitorsExcludingSelf) {
        IgnoreCompetitorDataDTO ignoreCompetitorData = new IgnoreCompetitorDataDTO();
        Set<Integer> competitorIds = allCompetitorsExcludingSelf.stream().map(WebrateCompetitors::getId).collect(Collectors.toSet());
        Map<WebrateOverrideCompetitorDetails, List<WebrateChannel>> filteredMap = new HashMap<>(
                webrateShoppingDataService.getWebrateOvrCompetitorDetailsChannelMap(product.getId(), competitorIds));

        if (filteredMap.isEmpty()) {
            return new ArrayList<>();
        }
        List<IgnoreCompetitorDataDTO> dtoList = setIgnoreCompetitorDtos(filteredMap, systemDateAsLocalDate);

        return ignoreCompetitorData.getGroupedIgnoreCompetitorDTO(dtoList);
    }

    private List<WebrateCompetitors> getAllActiveDemandOrRankingEnabledWebrateCompetitorsExcludingSelf(Product product, List<WebrateCompetitors> competitors) {
        if (null != competitors) {
            return competitors.stream()
                    .filter(c -> c.getStatusId().equals(Constants.ACTIVE_STATUS_ID) && 1 != c.getIsSelfCompetitor())
                    .filter(c -> c.getWebrateCompetitorsAccomClasses()
                            .stream().anyMatch(webrateCompetitorsAccomClass -> webrateCompetitorsAccomClass.getProductID().equals(product.getId()) && (webrateCompetitorsAccomClass.getDemandEnabled().equals(1) || webrateCompetitorsAccomClass.getRankingEnabled().equals(1))))
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private List<IgnoreCompetitorDataDTO> setIgnoreCompetitorDtos(Map<WebrateOverrideCompetitorDetails, List<WebrateChannel>> filteredMap, java.time.LocalDate systemDateAsLocalDate) {
        List<IgnoreCompetitorDataDTO> dtoList = new ArrayList<>();
        for (Map.Entry<WebrateOverrideCompetitorDetails, List<WebrateChannel>> entry : filteredMap.entrySet()) {
            WebrateOverrideCompetitorDetails competitorDetails = entry.getKey();
            IgnoreCompetitorDataDTO dto = new IgnoreCompetitorDataDTO();
            WebrateOverrideCompetitor webrateOverrideCompetitor = competitorDetails.getWebrateOverrideCompetitor();
            java.time.LocalDate startDate = convertJavaUtilDateToLocalDate(toDate(webrateOverrideCompetitor.getStartDate().toFormattedString(DEFAULT_DATE_FORMAT)));
            java.time.LocalDate endDate = convertJavaUtilDateToLocalDate(toDate(webrateOverrideCompetitor.getEndDate().toFormattedString(DEFAULT_DATE_FORMAT)));
            dto.setWebrateOverrideCompetitorDetails(getWebrateOverrideCompetitorDetails(competitorDetails, dto));
            dto.setAccomClasses(getAccomClasses(competitorDetails, dto));
            dto.setWebrateCompetitors(getWebrateCompetitors(competitorDetails, dto));
            dto.setStartDate(startDate);
            dto.setOriginalStartDate(startDate);
            dto.setEndDate(endDate);
            dto.setOriginalEndDate(endDate);
            dto.setNotes(webrateOverrideCompetitor.getNotes());
            dto.setOriginalNote(webrateOverrideCompetitor.getNotes());
            dto.setDaysOfWeek(webrateOverrideCompetitor);
            dto.setWebrateChannels(new HashSet<>(entry.getValue()));
            if(systemDateAsLocalDate != null) {
                dto.setSeasonFilterType(getSeasonFilterTypeForIgnoreCompetitorDto(dto.getStartDate(), dto.getEndDate(), systemDateAsLocalDate));
            }
            dto.setDtoGroupKey(dto);
            dtoList.add(dto);
        }
        return dtoList;
    }

    public SeasonFilterType getSeasonFilterTypeForIgnoreCompetitorDto(java.time.LocalDate startDate, java.time.LocalDate endDate, java.time.LocalDate systemDateAsLocalDate) {
        if (endDate.isBefore(systemDateAsLocalDate)) {
            return SeasonFilterType.PAST;
        } else if (startDate.isAfter(systemDateAsLocalDate)) {
            return SeasonFilterType.FUTURE;
        } else {
            return SeasonFilterType.PRESENT;
        }
    }

    public Set<AccomClass> getAccomClasses(WebrateOverrideCompetitorDetails competitorDetails, IgnoreCompetitorDataDTO dto) {
        Set<AccomClass> accomClasses = Optional.ofNullable(dto.getAccomClasses()).orElseGet(HashSet::new);
        accomClasses.add(getAccomClassFromOverrideCompetitorDetails(competitorDetails));
        return accomClasses;
    }

    private AccomClass getAccomClassFromOverrideCompetitorDetails(WebrateOverrideCompetitorDetails details) {
        return details.getWebrateCompetitorsAccomClass().getAccomClass();
    }

    public Set<WebrateCompetitors> getWebrateCompetitors(WebrateOverrideCompetitorDetails competitorDetails, IgnoreCompetitorDataDTO dto) {
        Set<WebrateCompetitors> competitors = Optional.ofNullable(dto.getWebrateCompetitors()).orElseGet(HashSet::new);
        competitors.add(getWebrateCompetitorFromOverrideCompetitorDetails(competitorDetails));
        return competitors;
    }

    private WebrateCompetitors getWebrateCompetitorFromOverrideCompetitorDetails(WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails) {
        return webrateOverrideCompetitorDetails.getWebrateCompetitorsAccomClass().getWebrateCompetitor();
    }

    public List<WebrateOverrideCompetitorDetails> getWebrateOverrideCompetitorDetails(WebrateOverrideCompetitorDetails competitorDetails, IgnoreCompetitorDataDTO dto) {
        List<WebrateOverrideCompetitorDetails> webrateOvrCompetitorDetails =
                org.apache.commons.collections.CollectionUtils.isEmpty(dto.getWebrateOverrideCompetitorDetails()) ? new ArrayList<>() : dto.getWebrateOverrideCompetitorDetails();
        webrateOvrCompetitorDetails.add(competitorDetails);
        return webrateOvrCompetitorDetails;
    }
}
