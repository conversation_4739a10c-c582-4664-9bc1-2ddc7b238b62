package com.ideas.tetris.pacman.services.forecast.controller;

import com.ideas.tetris.pacman.services.forecast.ExpectedForecastService;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastChartDTO;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailForPastSevenDaysDTO;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailGraphPastSevenDaysChartDTO;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailOnBooksDTO;
import com.ideas.tetris.platform.common.rest.annotation.DateFormat;

import javax.inject.Inject;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class ExpectedForecastController {

    @Autowired
	private ExpectedForecastService expectedForecastService;

    private static final String OCCUPANCY_DATE = "occupancyDate";


    public ExpectedForecastChartDTO getExpectedForecastChartDetails() {

        return expectedForecastService.fetchExpectedForecastDetails();
    }


    public List<ExpectedForecastDetailOnBooksDTO> getExpectedForecastOnBooksDetails(@DateFormat Date occupancyDt) {

        return expectedForecastService.fetchExpectedForecastOnBooksDetails(occupancyDt);
    }


    public List<ExpectedForecastDetailForPastSevenDaysDTO> getExpectedForecastOnBooksDetailsForPastDays(@DateFormat Date occupancyDtStr) {
        return expectedForecastService.fetchExpectedForecastDetailForPastSevenDays(occupancyDtStr);
    }


    public ExpectedForecastDetailGraphPastSevenDaysChartDTO getExpectedForecastOnBooksDetailsGraph(@DateFormat Date occupancyDtStr) {
        return expectedForecastService.fetchExpectedForecastDetailGraphForPastSevenDays(occupancyDtStr);
    }

}
