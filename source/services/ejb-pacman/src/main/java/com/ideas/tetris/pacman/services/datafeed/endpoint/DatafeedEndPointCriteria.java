package com.ideas.tetris.pacman.services.datafeed.endpoint;


import com.ideas.tetris.platform.services.daoandentities.entity.Client;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

public class DatafeedEndPointCriteria {

    private final boolean clientLevelRequest;
    private final String clientCode;
    private Set<EndpointFrequencyType> frequencies = new HashSet<>();
    private Set<EndpointBucket> clientBuckets = new HashSet<>();
    private final boolean continuousPricingEnabled;
    private final boolean groupPricingEnabled;
    private final boolean groupPricingMinProfitEnabled;
    private final boolean functionSpaceEnabled;
    private final boolean systemHealthEnabled;
    private final boolean supplementsEnabled;
    private final boolean agileRatesEnabled;
    private final boolean ldbProjectionEnabled;
    private final boolean groupEvaluationEnabled;
    private final boolean groupEvaluationIncludesBookingId;
    private final boolean agileRatesProductConfigurationEnabled;
    private final boolean sendDecisionAdjustmentEnabled;
    private final boolean groupFinalForecastEnabled;
    private final boolean propertySpecificNewColumnsEnabled;
    private final boolean propertySpecificUpdatedColumnsEnabled;
    private final boolean profitPopulationEnabled;
    private final boolean profitMetricsDatafeedEnabled;
    private final boolean outOfOrderOverridesEnabled;
    private final boolean taxInclusiveConfigurationEnabled;
    private final boolean firstUpload;
    private final boolean paceWebRateEnabled;
    private final boolean agileProductOptimizationFilesEnabled;
    private final boolean agileIndependentProductHierarchyColumnEnabled;
    private final boolean isChannelColumnEnabledInIgnoreCompetitor;
    private final boolean enhancedInventoryHistoryEnabled;
    private final boolean strEnabled;
    private final boolean demand360Enabled;
    private final boolean rateProtectEnabled;
    private final boolean extendedStayUnqualifiedRateManagementEnabled;
    private final boolean useUniqueUserIdInsteadOfEmail;
    private final boolean virtualPropertyMappingForHiltonEnabled;
    private final boolean st19ForDatafeedMSRTEnabled;
    private final boolean ageBasedPricingForHiltonEnabled;
    private final boolean enhancedProductPackageElementDFEnabled;
    private final boolean productRateShopDefinitionEnabled;
    private final boolean productRateShopDefinitionEnhancedEnabled;
    private final boolean productClassificationEnabled;
    private final boolean benefitMeasurementEnabled;
    private final boolean benefitMeasurementWithProfitColumnsEnabled;
    private final boolean scheduledReportsEnabled;
    private final boolean channelForecastEnabled;
    private final boolean lastLDBUpdateColumnEnabled;
    private final boolean hiltonConsortiaFreeNightEnabled;
    private final boolean productFreeNightDefinitionEnabled;
    private final boolean productGroupProductDefinitionEnabled;
    private final boolean groupPricingSCMarketSegmentMappingEnabled;
    private final boolean groupPricingSCRoomTypeMappingEnabled;
    private final boolean rolePermissionWithRankColumnEnabled;
    private Boolean isPercentageColumnForCPSupplementOfDatafeedEnabled;
    private final boolean isIndependentProductsEnabled;

    private final boolean isSpecialUseRoomTypesDatafeedEnabled;

    private final boolean isInventoryLimitEnabled;
    private boolean isRateShoppingOccupancyBasedCMPCEnabled;
    private boolean isPropertyCodeEnabledInUserReportDataFeed;
    private boolean isRateShoppingIgnoreChannelConfigEnabled;
    private final boolean isDiscontinuedRTEnabledInRoomClassConfigDataFeed;
    private final boolean isMeetingPackagePricingCfgDataFeedEnabled;
    private final boolean isWindowSettingsEnabledInInformationalDataFeed;

    public boolean isAgileRatesProductConfigurationEnabled() {
        return agileRatesProductConfigurationEnabled;
    }

    public boolean isAgileRatesEnabled() {
        return agileRatesEnabled;
    }

    public boolean isSendDecisionAdjustmentEnabled() {
        return sendDecisionAdjustmentEnabled;
    }

    public boolean isClientLevelRequest() {
        return clientLevelRequest;
    }

    public String getClientCode() {
        return clientCode;
    }

    public Boolean isHiltonClientCode() {
        return Client.HILTON.equalsIgnoreCase(clientCode) ||
                Client.HILTON_TEST.equalsIgnoreCase(clientCode);
    }

    public Set<EndpointFrequencyType> getFrequencies() {
        return frequencies;
    }

    public boolean isContinuousPricingEnabled() {
        return continuousPricingEnabled;
    }

    public boolean isSystemHealthEnabled() {
        return systemHealthEnabled;
    }

    public boolean isSupplementsEnabled() {
        return supplementsEnabled;
    }

    public Set<EndpointBucket> getClientBuckets() {
        return clientBuckets;
    }

    public boolean isPropertySpecificNewColumnsEnabled() {
        return propertySpecificNewColumnsEnabled;
    }

    public boolean isPropertySpecificUpdatedColumnsEnabled() {
        return propertySpecificUpdatedColumnsEnabled;
    }

    public boolean isLDBProjectionEnabled() {
        return ldbProjectionEnabled;
    }

    public boolean isAgeBasedPricingForHiltonEnabled() {
        return ageBasedPricingForHiltonEnabled;
    }

    public boolean isEnhancedProductPackageElementDFEnabled() {
        return enhancedProductPackageElementDFEnabled;
    }

    public boolean isGroupEvaluationEnabled() {
        return groupEvaluationEnabled;
    }

    public boolean isGroupEvaluationIncludesBookingId() {
        return groupEvaluationIncludesBookingId;
    }

    public boolean isGroupPricingEnabled() {
        return groupPricingEnabled;
    }

    public boolean isGroupPricingMinProfitEnabled() {
        return groupPricingMinProfitEnabled;
    }

    public boolean isFunctionSpaceEnabled() {
        return functionSpaceEnabled;
    }

    public boolean isGroupFinalForecastEnabled() {
        return groupFinalForecastEnabled;
    }

    public boolean isOutOfOrderOverridesEnabled() {
        return outOfOrderOverridesEnabled;
    }

    public boolean isTaxInclusiveConfigurationEnabled() {
        return taxInclusiveConfigurationEnabled;
    }

    public boolean isProfitPopulationEnabled() {
        return profitPopulationEnabled;
    }

    public boolean isProfitMetricsDatafeedEnabled() {
        return profitMetricsDatafeedEnabled;
    }

    public boolean isFirstUpload() {
        return firstUpload;
    }

    public boolean isPaceWebRateEnabled() {
        return paceWebRateEnabled;
    }

    public boolean isAgileProductOptimizationFilesEnabled() {
        return agileProductOptimizationFilesEnabled;
    }

    public boolean isAgileIndependentProductHierarchyColumnEnabled() {
        return agileIndependentProductHierarchyColumnEnabled;
    }

    public boolean isEnhancedInventoryHistoryEnabled() {
        return enhancedInventoryHistoryEnabled;
    }

    public boolean isStrEnabled() {
        return strEnabled;
    }

    public boolean isDemand360Enabled() {
        return demand360Enabled;
    }

    public boolean isRateProtectEnabled() {
        return rateProtectEnabled;
    }

    public boolean isAdditionalInformationalFieldsLastLDBUpdateEnabled() {
        return lastLDBUpdateColumnEnabled;
    }

    public boolean isExtendedStayUnqualifiedRateManagementEnabled() {
        return extendedStayUnqualifiedRateManagementEnabled;
    }

    public boolean isVirtualPropertyMappingForHiltonEnabled() {
        return virtualPropertyMappingForHiltonEnabled;
    }

    public boolean isSt19ForDatafeedMSRTEnabled() {
        return st19ForDatafeedMSRTEnabled;
    }

    public boolean isProductRateShopDefinitionEnabled() {
        return productRateShopDefinitionEnabled;
    }

    public boolean isProductRateShopDefinitionEnhancedEnabled() {
        return productRateShopDefinitionEnhancedEnabled;
    }

    public boolean isProductClassificationEnabled() {
        return productClassificationEnabled;
    }

    public boolean isBenefitMeasurementEnabled() {
        return benefitMeasurementEnabled;
    }

    public boolean isBenefitMeasurementWithProfitColumnsEnabled() {
        return benefitMeasurementWithProfitColumnsEnabled;
    }

    public boolean isScheduledReportsEnabled() {
        return scheduledReportsEnabled;
    }

    public boolean isChannelForecastEnabled() {
        return channelForecastEnabled;
    }

    public boolean isHiltonConsortiaFreeNightEnabled() {
        return hiltonConsortiaFreeNightEnabled;
    }

    public boolean isProductFreeNightDefinitionEnabled() {
        return productFreeNightDefinitionEnabled;
    }

    public boolean isProductGroupProductDefinitionEnabled() {
        return productGroupProductDefinitionEnabled;
    }

    public Boolean isGroupPricingSCMarketSegmentMappingEnabled() {
        return groupPricingSCMarketSegmentMappingEnabled;
    }

    public Boolean isGroupPricingSCRoomTypeMappingEnabled() {
        return groupPricingSCRoomTypeMappingEnabled;
    }

    public boolean isRolePermissionWithRankColumnEnabled() {
        return rolePermissionWithRankColumnEnabled;
    }

    private DatafeedEndPointCriteria(DatafeedEndPointCriteriaBuilder datafeedEndPointCriteriaBuilder) {
        this.clientLevelRequest = datafeedEndPointCriteriaBuilder.clientLevelRequest;
        this.clientCode = datafeedEndPointCriteriaBuilder.clientCode;
        this.frequencies = datafeedEndPointCriteriaBuilder.frequencies;
        this.clientBuckets = datafeedEndPointCriteriaBuilder.clientBuckets;
        this.continuousPricingEnabled = datafeedEndPointCriteriaBuilder.continuousPricingEnabled;
        this.systemHealthEnabled = datafeedEndPointCriteriaBuilder.systemHealthEnabled;
        this.supplementsEnabled = datafeedEndPointCriteriaBuilder.supplementsEnabled;
        this.agileRatesEnabled = datafeedEndPointCriteriaBuilder.agileRatesEnabled;
        this.ldbProjectionEnabled = datafeedEndPointCriteriaBuilder.ldbProjectionEnabled;
        this.groupEvaluationEnabled = datafeedEndPointCriteriaBuilder.groupEvaluationEnabled;
        this.groupEvaluationIncludesBookingId = datafeedEndPointCriteriaBuilder.groupEvaluationIncludesBookingId;
        this.agileRatesProductConfigurationEnabled = datafeedEndPointCriteriaBuilder.agileRatesProductConfigurationEnabled;
        this.sendDecisionAdjustmentEnabled = datafeedEndPointCriteriaBuilder.sendDecisionAdjustmentEnabled;
        this.groupPricingEnabled = datafeedEndPointCriteriaBuilder.groupPricingEnabled;
        this.groupPricingMinProfitEnabled = datafeedEndPointCriteriaBuilder.groupPricingMinProfitEnabled;
        this.functionSpaceEnabled = datafeedEndPointCriteriaBuilder.functionSpaceEnabled;
        this.groupFinalForecastEnabled = datafeedEndPointCriteriaBuilder.groupFinalForecastEnabled;
        this.propertySpecificNewColumnsEnabled = datafeedEndPointCriteriaBuilder.propertySpecificNewColumnsEnabled;
        this.propertySpecificUpdatedColumnsEnabled = datafeedEndPointCriteriaBuilder.propertySpecificUpdatedColumnsEnabled;
        this.profitPopulationEnabled = datafeedEndPointCriteriaBuilder.profitPopulationEnabled;
        this.profitMetricsDatafeedEnabled = datafeedEndPointCriteriaBuilder.profitMetricsDatafeedEnabled;
        this.outOfOrderOverridesEnabled = datafeedEndPointCriteriaBuilder.outOfOrderOverridesEnabled;
        this.taxInclusiveConfigurationEnabled = datafeedEndPointCriteriaBuilder.taxInclusiveConfigurationEnabled;
        this.firstUpload = datafeedEndPointCriteriaBuilder.firstUpload;
        this.paceWebRateEnabled = datafeedEndPointCriteriaBuilder.paceWebRateEnabled;
        this.agileProductOptimizationFilesEnabled = datafeedEndPointCriteriaBuilder.agileProductOptimizationFilesEnabled;
        this.agileIndependentProductHierarchyColumnEnabled = datafeedEndPointCriteriaBuilder.agileIndependentProductHierarchyColumnEnabled;
        this.enhancedInventoryHistoryEnabled = datafeedEndPointCriteriaBuilder.enhancedInventoryHistoryEnabled;
        this.strEnabled = datafeedEndPointCriteriaBuilder.strEnabled;
        this.demand360Enabled = datafeedEndPointCriteriaBuilder.demand360Enabled;
        this.rateProtectEnabled = datafeedEndPointCriteriaBuilder.rateProtectEnabled;
        this.extendedStayUnqualifiedRateManagementEnabled = datafeedEndPointCriteriaBuilder.extendedStayUnqualifiedRateManagementEnabled;
        this.useUniqueUserIdInsteadOfEmail = datafeedEndPointCriteriaBuilder.useUniqueUserIdInsteadOfEmail;
        this.virtualPropertyMappingForHiltonEnabled = datafeedEndPointCriteriaBuilder.virtualPropertyMappingForHiltonEnabled;
        this.st19ForDatafeedMSRTEnabled = datafeedEndPointCriteriaBuilder.st19ForDatafeedMSRTEnabled;
        this.ageBasedPricingForHiltonEnabled = datafeedEndPointCriteriaBuilder.ageBasedPricingForHiltonEnabled;
        this.enhancedProductPackageElementDFEnabled = datafeedEndPointCriteriaBuilder.enhancedProductPackageElementDFEnabled;
        this.productRateShopDefinitionEnabled = datafeedEndPointCriteriaBuilder.productRateShopDefinitionEnabled;
        this.productRateShopDefinitionEnhancedEnabled = datafeedEndPointCriteriaBuilder.productRateShopDefinitionEnhancedEnabled;
        this.productClassificationEnabled = datafeedEndPointCriteriaBuilder.productClassificationEnabled;
        this.benefitMeasurementEnabled = datafeedEndPointCriteriaBuilder.benefitMeasurementEnabled;
        this.benefitMeasurementWithProfitColumnsEnabled = datafeedEndPointCriteriaBuilder.benefitMeasurementWithProfitColumnsEnabled;
        this.scheduledReportsEnabled = datafeedEndPointCriteriaBuilder.scheduledReportsEnabled;
        this.channelForecastEnabled = datafeedEndPointCriteriaBuilder.channelForecastEnabled;
        this.hiltonConsortiaFreeNightEnabled = datafeedEndPointCriteriaBuilder.hiltonConsortiaFreeNightEnabled;
        this.productFreeNightDefinitionEnabled = datafeedEndPointCriteriaBuilder.productFreeNightDefinitionEnabled;
        this.productGroupProductDefinitionEnabled = datafeedEndPointCriteriaBuilder.productGroupProductDefinitionEnabled;
        this.groupPricingSCMarketSegmentMappingEnabled = datafeedEndPointCriteriaBuilder.groupPricingSCMarketSegmentMappingEnabled;
        this.groupPricingSCRoomTypeMappingEnabled = datafeedEndPointCriteriaBuilder.groupPricingSCRoomTypeMappingEnabled;
        this.lastLDBUpdateColumnEnabled = datafeedEndPointCriteriaBuilder.lastLDBUpdateColumnEnabled;
        this.rolePermissionWithRankColumnEnabled = datafeedEndPointCriteriaBuilder.rolePermissionWithRankColumnEnabled;
        this.isPercentageColumnForCPSupplementOfDatafeedEnabled = datafeedEndPointCriteriaBuilder.percentageColumnForCPSupplementOfDatafeedEnabled;
        this.isIndependentProductsEnabled = datafeedEndPointCriteriaBuilder.isIndependentProductsEnabled;
        this.isInventoryLimitEnabled = datafeedEndPointCriteriaBuilder.isInventoryLimitEnabled;
        this.isSpecialUseRoomTypesDatafeedEnabled = datafeedEndPointCriteriaBuilder.isSpecialUseRoomTypesDatafeedEnabled;
        this.isRateShoppingOccupancyBasedCMPCEnabled = datafeedEndPointCriteriaBuilder.isRateShoppingOccupancyBasedCMPCDatafeedEnabled;
        this.isChannelColumnEnabledInIgnoreCompetitor =datafeedEndPointCriteriaBuilder.isChannelColumnEnabledInIgnoreCompetitor;
        this.isPropertyCodeEnabledInUserReportDataFeed = datafeedEndPointCriteriaBuilder.isPropertyCodeEnabledInUserReportDataFeed;
        this.isRateShoppingIgnoreChannelConfigEnabled = datafeedEndPointCriteriaBuilder.isRateShoppingIgnoreChannelConfigDatafeedEnabled;
        this.isDiscontinuedRTEnabledInRoomClassConfigDataFeed = datafeedEndPointCriteriaBuilder.isDiscontinuedRoomTypeEnabledInRoomClassConfig;
        this.isMeetingPackagePricingCfgDataFeedEnabled = datafeedEndPointCriteriaBuilder.isMeetingPackagePricingDataFeedEnabled;
        this.isWindowSettingsEnabledInInformationalDataFeed = datafeedEndPointCriteriaBuilder.isWindowSettingsEnabledInInformationalDataFeed;
    }

    public boolean isUseUniqueIDEnabled() {
        return this.useUniqueUserIdInsteadOfEmail;
    }

    public Boolean isPercentageColumnForCPSupplementOfDatafeedEnabled() {
        return Objects.requireNonNullElse(isPercentageColumnForCPSupplementOfDatafeedEnabled, false);
    }

    public boolean isIndependentProductsEnabled() {
        return isIndependentProductsEnabled;
    }

    public boolean isEnabledInventoryLimit() {
        return isInventoryLimitEnabled;
    }
    public boolean isSpecialUseRoomTypesDatafeedEnabled() {
        return isSpecialUseRoomTypesDatafeedEnabled;
    }

    public Boolean isRateShoppingOccupancyBasedCMPCEnabled() {
        return isRateShoppingOccupancyBasedCMPCEnabled;
    }
    public Boolean isChannelColumnEnabledInIgnoreCompetitor() {
        return isChannelColumnEnabledInIgnoreCompetitor;
    }

    public Boolean isPropertyCodeEnabledInUserReportDataFeed() {
        return isPropertyCodeEnabledInUserReportDataFeed;
    }

    public Boolean isRateShoppingIgnoreChannelConfigEnabled() { return isRateShoppingIgnoreChannelConfigEnabled; }

    public Boolean isDiscontinuedRTEnabledInRoomClassConfigDataFeed() {
        return isDiscontinuedRTEnabledInRoomClassConfigDataFeed;
    }

    public Boolean isMeetingPackagePricingDataFeedEnabled() {
        return isMeetingPackagePricingCfgDataFeedEnabled;
    }

    public Boolean isWindowSettingsEnabledInInformationalDataFeed() {
        return isWindowSettingsEnabledInInformationalDataFeed;
    }

    public static class DatafeedEndPointCriteriaBuilder {

        // mandatory parameters
        private boolean clientLevelRequest;
        private boolean lastLDBUpdateColumnEnabled;
        private String clientCode;
        private Set<EndpointFrequencyType> frequencies;
        private Set<EndpointBucket> clientBuckets;

        // optional parameters when client level request is true
        private boolean continuousPricingEnabled;
        private boolean systemHealthEnabled;
        private boolean supplementsEnabled;
        private boolean agileRatesEnabled;
        private boolean ldbProjectionEnabled;
        private boolean agileRatesProductConfigurationEnabled;
        private boolean sendDecisionAdjustmentEnabled;
        private boolean groupEvaluationEnabled;
        private boolean groupEvaluationIncludesBookingId;
        private boolean groupPricingEnabled;
        private boolean groupPricingMinProfitEnabled;
        private boolean functionSpaceEnabled;
        private boolean groupFinalForecastEnabled;
        private boolean propertySpecificNewColumnsEnabled;
        private boolean propertySpecificUpdatedColumnsEnabled;
        private boolean profitPopulationEnabled;
        private boolean profitMetricsDatafeedEnabled;
        private boolean outOfOrderOverridesEnabled;
        private boolean taxInclusiveConfigurationEnabled;
        private boolean firstUpload;
        private boolean paceWebRateEnabled;
        private boolean productColumnNotificationsDatafeedFilesEnabled;
        private boolean agileProductOptimizationFilesEnabled;
        private boolean agileIndependentProductHierarchyColumnEnabled;
        private boolean enhancedInventoryHistoryEnabled;
        private boolean strEnabled;
        private boolean demand360Enabled;
        private boolean rateProtectEnabled;
        private boolean extendedStayUnqualifiedRateManagementEnabled;
        private boolean useUniqueUserIdInsteadOfEmail;
        private boolean virtualPropertyMappingForHiltonEnabled;
        private boolean st19ForDatafeedMSRTEnabled;
        private boolean ageBasedPricingForHiltonEnabled;
        private boolean enhancedProductPackageElementDFEnabled;
        private boolean productRateShopDefinitionEnabled;
        private boolean productRateShopDefinitionEnhancedEnabled;
        private boolean productClassificationEnabled;
        private boolean benefitMeasurementEnabled;
        private boolean benefitMeasurementWithProfitColumnsEnabled;
        private boolean scheduledReportsEnabled;
        private boolean channelForecastEnabled;
        private boolean hiltonConsortiaFreeNightEnabled;
        private boolean productFreeNightDefinitionEnabled;
        private boolean productGroupProductDefinitionEnabled;
        private boolean groupPricingSCMarketSegmentMappingEnabled;
        private boolean groupPricingSCRoomTypeMappingEnabled;
        private boolean rolePermissionWithRankColumnEnabled;

        public Boolean percentageColumnForCPSupplementOfDatafeedEnabled;

        public boolean isIndependentProductsEnabled;
        private boolean isSpecialUseRoomTypesDatafeedEnabled;

        private boolean isInventoryLimitEnabled;
        private boolean isRateShoppingOccupancyBasedCMPCDatafeedEnabled;
        private boolean isPropertyCodeEnabledInUserReportDataFeed;
        private boolean isRateShoppingIgnoreChannelConfigDatafeedEnabled;
        private boolean isChannelColumnEnabledInIgnoreCompetitor;
        private boolean isDiscontinuedRoomTypeEnabledInRoomClassConfig;
        public boolean isMeetingPackagePricingDataFeedEnabled;
        public boolean isWindowSettingsEnabledInInformationalDataFeed;

        public DatafeedEndPointCriteriaBuilder() {
        }

        public DatafeedEndPointCriteriaBuilder(final boolean clientLevelRequest, final String clientCode, final Set<EndpointFrequencyType> frequencies, final Set<EndpointBucket> clientBuckets) {
            this.clientLevelRequest = clientLevelRequest;
            this.clientCode = clientCode;
            this.frequencies = frequencies;
            this.clientBuckets = clientBuckets;
        }

        public DatafeedEndPointCriteriaBuilder(final String clientCode, final Set<EndpointFrequencyType> frequencies, final Set<EndpointBucket> clientBuckets, final boolean continuousPricingEnabled, final boolean systemHealthEnabled, final boolean supplementsEnabled, final boolean percentageColumnForCPSupplementEnabled, final boolean isIndependentProductsEnabled, final boolean isSpecialUseRoomTypesDatafeedEnabled, final boolean isInventoryLimitEnabled, final boolean isAdditionalInformationalFieldsLastLDBUpdateColumnEnabled) {
            this.clientCode = clientCode;
            this.frequencies = frequencies;
            this.clientBuckets = clientBuckets;
            this.continuousPricingEnabled = continuousPricingEnabled;
            this.systemHealthEnabled = systemHealthEnabled;
            this.supplementsEnabled = supplementsEnabled;
            this.percentageColumnForCPSupplementOfDatafeedEnabled = percentageColumnForCPSupplementEnabled;
            this.isIndependentProductsEnabled = isIndependentProductsEnabled;
            this.isInventoryLimitEnabled = isInventoryLimitEnabled;
            this.isSpecialUseRoomTypesDatafeedEnabled = isSpecialUseRoomTypesDatafeedEnabled;
            this.lastLDBUpdateColumnEnabled= isAdditionalInformationalFieldsLastLDBUpdateColumnEnabled;
        }

        public DatafeedEndPointCriteriaBuilder clientLevelRequest(boolean clientLevelRequest) {
            this.clientLevelRequest = clientLevelRequest;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder clientCode(String clientCode) {
            this.clientCode = clientCode;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder frequencies(Set<EndpointFrequencyType> frequencies) {
            this.frequencies = frequencies;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder continuousPricingEnabled(boolean continuousPricingEnabled) {
            this.continuousPricingEnabled = continuousPricingEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setGroupFinalForecastEnabled(boolean groupFinalForecastEnabled) {
            this.groupFinalForecastEnabled = groupFinalForecastEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder propertySpecificNewColumnsEnabled(boolean propertySpecificNewColumnsEnabled) {
            this.propertySpecificNewColumnsEnabled = propertySpecificNewColumnsEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder systemHealthEnabled(boolean systemHealthEnabled) {
            this.systemHealthEnabled = systemHealthEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder supplementsEnabled(boolean supplementsEnabled) {
            this.supplementsEnabled = supplementsEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder clientBuckets(Set<EndpointBucket> clientBuckets) {
            this.clientBuckets = clientBuckets;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setSendDecisionAdjustmentEnabled(boolean sendDecisionAdjustmentEnabled) {
            this.sendDecisionAdjustmentEnabled = sendDecisionAdjustmentEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder agileRatesEnabled(boolean agileRatesEnabled) {
            this.agileRatesEnabled = agileRatesEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder isLDBProjectionEnabled(boolean isLDBProjectionEnabled) {
            this.ldbProjectionEnabled = isLDBProjectionEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setAgeBasedPricingForHiltonEnabled(boolean ageBasedPricingEnabledForHilton) {
            this.ageBasedPricingForHiltonEnabled = ageBasedPricingEnabledForHilton;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setEnhancedProductPackageElementDFEnabled(boolean enhancedProductPackageElementDFEnabled) {
            this.enhancedProductPackageElementDFEnabled = enhancedProductPackageElementDFEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setGroupEvaluationEnabled(boolean groupEvaluationEnabled) {
            this.groupEvaluationEnabled = groupEvaluationEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setGroupEvaluationIncludesBookingId(boolean groupEvaluationIncludesBookingId) {
            this.groupEvaluationIncludesBookingId = groupEvaluationIncludesBookingId;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setPropertySpecificNewColumnsEnabled(boolean propertySpecificNewColumnsEnabled) {
            return propertySpecificNewColumnsEnabled(propertySpecificNewColumnsEnabled);
        }

        public DatafeedEndPointCriteriaBuilder setPropertySpecificUpdatedColumnsEnabled(boolean propertySpecificUpdatedColumnsEnabled) {
            this.propertySpecificUpdatedColumnsEnabled = propertySpecificUpdatedColumnsEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setAgileRatesProductConfigurationEnabled(boolean agileRatesProductConfigurationEnabled) {
            this.agileRatesProductConfigurationEnabled = agileRatesProductConfigurationEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setGroupPricingEnabled(boolean groupPricingEnabled) {
            this.groupPricingEnabled = groupPricingEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setGroupPricingMinProfitEnabled(boolean groupPricingMinProfitEnabled) {
            this.groupPricingMinProfitEnabled = groupPricingMinProfitEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setFunctionSpaceEnabled(boolean functionSpaceEnabled) {
            this.functionSpaceEnabled = functionSpaceEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setProfitPopulationEnabled(boolean profitPopulationEnabled) {
            this.profitPopulationEnabled = profitPopulationEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setProfitMetricsDatafeedEnabled(boolean profitMetricsDatafeedEnabled) {
            this.profitMetricsDatafeedEnabled = profitMetricsDatafeedEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setOutOfOrderOverridesEnabled(boolean outOfOrderOverridesEnabled) {
            this.outOfOrderOverridesEnabled = outOfOrderOverridesEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setTaxInclusiveConfigurationEnabled(boolean taxInclusiveConfigurationEnabled) {
            this.taxInclusiveConfigurationEnabled = taxInclusiveConfigurationEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setFirstUpload(boolean isFirstUpload) {
            this.firstUpload = isFirstUpload;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setPaceWebRateEnabled(boolean paceWebRateEnabled) {
            this.paceWebRateEnabled = paceWebRateEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setProductColumnNotificationsDatafeedFilesEnabled(boolean productColumnNotificationsDatafeedFilesEnabled) {
            this.productColumnNotificationsDatafeedFilesEnabled = productColumnNotificationsDatafeedFilesEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setAgileProductOptimizationFilesEnabled(boolean agileProductOptimizationFilesEnabled) {
            this.agileProductOptimizationFilesEnabled = agileProductOptimizationFilesEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setAgileIndependentProductHierarchyColumnEnabled(boolean agileIndependentProductHierarchyColumnEnabled) {
            this.agileIndependentProductHierarchyColumnEnabled = agileIndependentProductHierarchyColumnEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setEnhancedInventoryHistoryEnabled(boolean enhancedInventoryHistoryEnabled) {
            this.enhancedInventoryHistoryEnabled = enhancedInventoryHistoryEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setStrEnabled(boolean strEnabled) {
            this.strEnabled = strEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setDemand360Enabled(boolean demand360Enabled) {
            this.demand360Enabled = demand360Enabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setRateProtectEnabled(boolean rateProtectEnabled) {
            this.rateProtectEnabled = rateProtectEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setExtendedStayUnqualifiedRateManagementEnabled(boolean extendedStayUnqualifiedRateManagementEnabled) {
            this.extendedStayUnqualifiedRateManagementEnabled = extendedStayUnqualifiedRateManagementEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setUseUniqueUserIdInsteadOfEmail(boolean useUniqueUserIdInsteadOfEmail) {
            this.useUniqueUserIdInsteadOfEmail = useUniqueUserIdInsteadOfEmail;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setVirtualPropertyMappingForHiltonEnabled(boolean virtualPropertyMappingForHiltonEnabled) {
            this.virtualPropertyMappingForHiltonEnabled = virtualPropertyMappingForHiltonEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setSt19ForDatafeedMSRTEnabled(boolean st19ForDatafeedMSRTEnabled) {
            this.st19ForDatafeedMSRTEnabled = st19ForDatafeedMSRTEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setProductRateShopDefinitionEnabled(boolean productRateShopDefinitionEnabled) {
            this.productRateShopDefinitionEnabled = productRateShopDefinitionEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setProductRateShopDefinitionEnhancedEnabled(boolean productRateShopDefinitionEnhancedEnabled) {
            this.productRateShopDefinitionEnhancedEnabled = productRateShopDefinitionEnhancedEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setProductClassificationEnabled(boolean productClassificationEnabled) {
            this.productClassificationEnabled = productClassificationEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setBenefitMeasurementEnabled(boolean benefitMeasurementEnabled) {
            this.benefitMeasurementEnabled = benefitMeasurementEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setBenefitMeasurementWithProfitColumnsEnabled(boolean benefitMeasurementWithProfitColumnsEnabled) {
            this.benefitMeasurementWithProfitColumnsEnabled = benefitMeasurementWithProfitColumnsEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setScheduledReportsEnabled(boolean scheduledReportsEnabled) {
            this.scheduledReportsEnabled = scheduledReportsEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setChannelForecastEnabled(boolean channelForecastEnabled) {
            this.channelForecastEnabled = channelForecastEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setHiltonConsortiaFreeNightEnabled(boolean hiltonConsortiaFreeNightEnabled) {
            this.hiltonConsortiaFreeNightEnabled = hiltonConsortiaFreeNightEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setProductFreeNightDefinitionEnabled(boolean productFreeNightDefinitionEnabled) {
            this.productFreeNightDefinitionEnabled = productFreeNightDefinitionEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setProductGroupProductDefinitionEnabled(boolean productGroupProductDefinitionEnabled) {
            this.productGroupProductDefinitionEnabled = productGroupProductDefinitionEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setGroupPricingSCMarketSegmentMappingEnabled(boolean groupPricingSCMarketSegmentMappingEnabled) {
            this.groupPricingSCMarketSegmentMappingEnabled = groupPricingSCMarketSegmentMappingEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setGroupPricingSCRoomTypeMappingEnabled(boolean groupPricingSCRoomTypeMappingEnabled) {
            this.groupPricingSCRoomTypeMappingEnabled = groupPricingSCRoomTypeMappingEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setRolePermissionWithRankColumnEnabled(boolean rolePermissionWithRankColumnEnabled) {
            this.rolePermissionWithRankColumnEnabled = rolePermissionWithRankColumnEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setPercentageColumnForCPSupplementOfDatafeedEnabled(Boolean percentageColumnForCPSupplementOfDatafeedEnabled) {
            this.percentageColumnForCPSupplementOfDatafeedEnabled = percentageColumnForCPSupplementOfDatafeedEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setIsIndependentProductsEnabled(boolean isIndependentProductsEnabled) {
            this.isIndependentProductsEnabled = isIndependentProductsEnabled;
            return this;
        }
        public DatafeedEndPointCriteriaBuilder setIsInventoryLimitEnabled(boolean isInventoryLimitEnabled) {
            this.isInventoryLimitEnabled = isInventoryLimitEnabled;
            return this;
        }
        public DatafeedEndPointCriteriaBuilder setIsSpecialUseRoomTypesEnabled(boolean specialUseRoomTypesEnabled) {
            this.isSpecialUseRoomTypesDatafeedEnabled = specialUseRoomTypesEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setLastLDBUpdateColumnForPropertyBasicInformation(boolean lastLDBUpdateColumnEnabled) {
            this.lastLDBUpdateColumnEnabled = lastLDBUpdateColumnEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setIsRateShoppingOccupancyBasedCMPCDatafeedEnabled(boolean isOccupancyBasedCMPCDatafeedEnabled) {
            this.isRateShoppingOccupancyBasedCMPCDatafeedEnabled = isOccupancyBasedCMPCDatafeedEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setIsRateShoppingIgnoreChannelConfigDatafeedEnabled(boolean isRateShoppingIgnoreChannelConfigDatafeedEnabled) {
            this.isRateShoppingIgnoreChannelConfigDatafeedEnabled = isRateShoppingIgnoreChannelConfigDatafeedEnabled;
            return this;
        }
        public DatafeedEndPointCriteriaBuilder setIsChannelColumnEnabledInIgnoreCompetitor(boolean isChannelColumnEnabledInIgnoreCompetitor) {
            this.isChannelColumnEnabledInIgnoreCompetitor = isChannelColumnEnabledInIgnoreCompetitor;
            return this;
        }

        public void setIsPropertyCodeEnabledInUserReportDatafeed(boolean isPropertyCodeEnabledInUserReportDataFeed) {
            this.isPropertyCodeEnabledInUserReportDataFeed = isPropertyCodeEnabledInUserReportDataFeed;
        }

        public DatafeedEndPointCriteriaBuilder setIsDiscontinuedRoomTypeInRoomClassDatafeedEnabled(boolean isDiscontinuedRTEnabledInRoomClassConfig) {
            this.isDiscontinuedRoomTypeEnabledInRoomClassConfig = isDiscontinuedRTEnabledInRoomClassConfig;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setIsMeetingPackagePricingDataFeedEnabled(boolean isMeetingPackagePricingDataFeedEnabled) {
            this.isMeetingPackagePricingDataFeedEnabled = isMeetingPackagePricingDataFeedEnabled;
            return this;
        }

        public DatafeedEndPointCriteriaBuilder setIsWindowSettingsEnabledInInformationalDataFeed(boolean isWindowSettingsEnabledInInformationalDataFeed) {
            this.isWindowSettingsEnabledInInformationalDataFeed = isWindowSettingsEnabledInInformationalDataFeed;
            return this;
        }

        public DatafeedEndPointCriteria build() {
            return new DatafeedEndPointCriteria(this);
        }
    }
}
