package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;

import javax.inject.Inject;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class CopyClientInfoFromGlobalToTenantDBService {

    @Autowired
    JobServiceLocal jobServiceLocal;

    public void copyClientInfoFromGlobalToTenant(String clientCode) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put(JobParameterKey.CLIENT_CODE, clientCode);
        parameters.put(JobParameterKey.DATE, new Date());

        jobServiceLocal.startJob(JobName.CopyClientInfoToTenantJob, parameters);
    }
}
