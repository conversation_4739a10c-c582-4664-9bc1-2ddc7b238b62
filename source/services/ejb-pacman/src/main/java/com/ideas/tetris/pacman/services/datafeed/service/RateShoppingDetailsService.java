package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.datafeed.dto.rateshopping.RateChannel;
import com.ideas.tetris.pacman.services.datafeed.dto.rateshopping.RateCompetitor;
import com.ideas.tetris.pacman.services.datafeed.dto.rateshopping.RateShoppingSchedule;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateChannel;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitorsAccomClass;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateShoppingConfig;
import com.ideas.tetris.pacman.services.webrate.service.WebrateDataSchedulingService;
import com.ideas.tetris.pacman.services.webrate.service.WebrateShoppingDataService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.NO;
import static com.ideas.tetris.pacman.common.constants.Constants.YES;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class RateShoppingDetailsService {

    @Autowired
    WebrateDataSchedulingService webrateDataSchedulingService;

    @Autowired
    WebrateShoppingDataService webrateShoppingDataService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    public List getAllRateCompetitorDetails() {
        List<WebrateCompetitors> rateShoppingData = webrateShoppingDataService.getAllCompetitorsByProperty();

        return getRateCompetitorDetails(rateShoppingData);
    }

    public List getAllRateChannelDetails() {
        List<WebrateChannel> channels = webrateShoppingDataService.getAllChannelsByProperty();
        return channels.stream().map(channel -> new RateChannel(
                        channel.getWebrateChannelName(),
                        channel.getWebrateChannelAlias(),
                        getChannelDisabledStatus(channel.getStatusId())))
                .collect(Collectors.toList());
    }

    public List getRateShoppingScheduleDetails() {
        List<WebrateShoppingConfig> shoppingConfigs = webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty();
        return shoppingConfigs.stream().map(config -> new RateShoppingSchedule() {
            {
                setScheduleCompetitorForTheNextXDays(config.getRollingDaysToShop());
                setScheduleDataArrivesEveryXDays(config.getWebrateShoppingFrequency());
                setScheduleStopUsingDataAfterXDays(config.getWebrateShoppingThreshold());
            }
        }).collect(Collectors.toList());
    }

    private List<RateCompetitor> getRateCompetitorDetails(List<WebrateCompetitors> webrateCompetitors) {
        List<RateCompetitor> rateCompetitorDetails = new ArrayList<>();
        Map<Integer, String> allProductsMap = Collections.emptyMap();

        List<Product> allProducts = webrateShoppingDataService.findAllProducts();
        allProductsMap = allProducts.stream().collect(
                Collectors.toMap(Product::getId, Product::getName));

        for (WebrateCompetitors webrateCompetitor : webrateCompetitors) {
            RateCompetitor rateCompetitor;
            for (WebrateCompetitorsAccomClass accomClass : webrateCompetitor.getWebrateCompetitorsAccomClasses()) {
                rateCompetitor = new RateCompetitor();
                rateCompetitor.setCompetitorPropertyName(webrateCompetitor.getWebrateCompetitorsName());
                rateCompetitor.setCompetitorDisplayName(webrateCompetitor.getWebrateCompetitorsAlias());
                rateCompetitor.setRoomClassCode(accomClass.getAccomClass().getCode());
                rateCompetitor.setSelfHotelIndicator(webrateCompetitor.getIsSelfCompetitor() == 1 ? YES : NO);
                setToggles(webrateCompetitor, rateCompetitor, accomClass);
                rateCompetitor.setProductName(allProductsMap.get(accomClass.getProductID()));
                rateCompetitorDetails.add(rateCompetitor);
            }
        }
        return rateCompetitorDetails;
    }

    private void setToggles(WebrateCompetitors webrateCompetitor, RateCompetitor rateCompetitor, WebrateCompetitorsAccomClass accomClass) {
        rateCompetitor.setIsUseInDemandModelEnabled(getStatus(accomClass.getDemandEnabled()));
        rateCompetitor.setIsUseInCompetetiveModelEnabled(getStatus(accomClass.getRankingEnabled()));
        rateCompetitor.setIsDisabled(getCompetitorDisabledStatus(accomClass.getDemandEnabled(), accomClass.getRankingEnabled()));
    }

    //Different ways to deduce disable status to match UI data
    private String getCompetitorDisabledStatus(Integer demandEnabled, Integer rankingEnabled) {
        return demandEnabled == 0 && rankingEnabled == 0 ? YES : NO;
    }

    private String getChannelDisabledStatus(Integer statusId) {
        return statusId == 2 ? YES : NO;
    }

    private String getStatus(Integer statusId) {
        return statusId == 1 ? YES : NO;
    }

}
