package com.ideas.tetris.pacman.services.saspurge.service;

import com.ideas.tetris.pacman.common.xml.schema.ISASRequest;
import com.ideas.tetris.pacman.common.xml.schema.purge.request.v2.PurgeRequestV2Type;
import com.ideas.tetris.pacman.common.xml.schema.purge.request.v2.RequestHeaderType;
import com.ideas.tetris.pacman.common.xml.schema.purge.request.v2.SASPurgeRequestV2;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.sasinvocationbase.AbstractSasInvocation;
import com.ideas.tetris.platform.common.util.jaxb.JAXBUtilLocal;
import com.ideas.tetris.platform.common.util.xml.XmlHelper;
import com.ideas.tetris.platform.common.util.xml.XmlHelperImpl;
import com.ideas.tetris.platform.common.utils.systemconfig.SASSettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Date;

import static com.ideas.tetris.pacman.util.jaxb.purge.request.PurgeRequestV2JAXBUtil.PurgeRequestV2Qualifier;
import static com.ideas.tetris.pacman.util.jaxb.purge.response.PurgeResponseV2JAXBUtil.PurgeResponseV2Qualifier;

@Component
public class PurgeOldSasDatasetServiceV2 extends AbstractSasInvocation<PurgeRequestV2Type> {

    private static final String OPERATION_NAME = "sasdatapurgeV2";

    @PurgeRequestV2Qualifier
    @Autowired
    @Qualifier("purgeRequestV2JAXBUtil")
    private JAXBUtilLocal requestJaxbUtil;

    @PurgeResponseV2Qualifier
    @Autowired
    @Qualifier("purgeResponseV2JAXBUtil")
    private JAXBUtilLocal responseJaxbUtil;

    @Autowired
    private DateService dateService;

    @Override
    protected String getOperationName() {
        return OPERATION_NAME;
    }

    @Override
    protected JAXBUtilLocal getJaxbUtil() {
        return requestJaxbUtil;
    }

    public void setRequestJaxbUtil(JAXBUtilLocal requestJaxbUtil) {
        this.requestJaxbUtil = requestJaxbUtil;
    }

    @Override
    protected JAXBUtilLocal getResponseJaxbUtil() {
        return responseJaxbUtil;
    }

    public void setResponseJaxbUtil(JAXBUtilLocal responseJaxbUtil) {
        this.responseJaxbUtil = responseJaxbUtil;
    }

    @Override
    protected String getStoredProc() {
        return SASSettings.getDefaultStoredProcName();
    }

    @Override
    protected String getRequestMap() {
        return SASSettings.getMapFileLocation().concat("purgeRequestV2.map");
    }

    @SuppressWarnings("rawtypes")
    @Override
    protected ISASRequest getSASRequest(PurgeRequestV2Type requestType, Decision decision) {
        SASPurgeRequestV2 sasRequest = new SASPurgeRequestV2();
        sasRequest.setRequestHeader(new RequestHeaderType());
        sasRequest.setPurgeRequestV2(requestType);
        return sasRequest;
    }

    public void execute() {
        Date snapshotDate = dateService.getCaughtUpDate();
        PurgeRequestV2Type requestType = createPurgeRequest(snapshotDate);
        execute(requestType);
    }

    private PurgeRequestV2Type createPurgeRequest(Date snapshotDate) {
        XmlHelper xmlHelper = new XmlHelperImpl();
        PurgeRequestV2Type requestType = new PurgeRequestV2Type();
        requestType.setSnapshotDate(xmlHelper.convertDateToXMLGregorian(snapshotDate));
        return requestType;
    }
}
