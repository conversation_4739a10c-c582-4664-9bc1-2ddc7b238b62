package com.ideas.tetris.pacman.services.opera;


import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.CREATE_HASH_VALUE_INDEX;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.CREATE_NC_INDEX_SHARERS_ONLY_SHARED_TRANS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.CREATE_SHARERS_ONLY_CNX_NO_SHOW_INDEX;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.DROP_HAS_VALUE_INDEX;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.DROP_NC_INDEX_SHARERS_ONLY_SHARED_TRANS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.DROP_SHARERS_ONLY_CNX_NO_SHOW_INDEX;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.GET_ALL_SHARED_TRANSACTIONS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.GET_CANCELLED_AND_NS_TRX_FROM_SHARERS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.GET_CANCELLED_AND_NS_TRX_WITH_REVENUE_FROM_SHARERS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.GET_ONLY_SHARED_TRANSACTIONS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Service
@Justification("Until (if ever) updates performed by this service are made more performant, a 60 minute timeout is required")
public class OperaTransformTransactionDataUtilityService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    private static final Logger LOGGER = Logger.getLogger(OperaTransformTransactionDataUtilityService.class.getName());

    @Transactional(propagation = Propagation.REQUIRES_NEW, timeout = 3600)
    public void dropNonClusteredIndexes() {
        crudService.executeUpdateByNativeQuery(DROP_HAS_VALUE_INDEX);
        crudService.executeUpdateByNativeQuery(DROP_NC_INDEX_SHARERS_ONLY_SHARED_TRANS);
        LOGGER.info("Dropping Non Clustered Indexes on Sharers_OnlySharedTrans completed");
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, timeout = 3600)
    public int getOnlySharedTransactionsWithNewTransaction() {
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(GET_ONLY_SHARED_TRANSACTIONS));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, timeout = 3600)
    public void truncateTable(final String tableName) {
        String queryStr = "if object_id('" + tableName + "') is not NULL TRUNCATE TABLE " + tableName;
        crudService.executeUpdateByNativeQuery(queryStr);
        LOGGER.info(String.format("Truncate table %s completed", tableName));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, timeout = 3600)
    public void createNonClusteredIndexes() {
        crudService.executeUpdateByNativeQuery(CREATE_HASH_VALUE_INDEX);
        crudService.executeUpdateByNativeQuery(CREATE_NC_INDEX_SHARERS_ONLY_SHARED_TRANS);
        LOGGER.info("Creating Non Clustered Indexes on Sharers_OnlySharedTrans completed");
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, timeout = 3600)
    public int getAllSharedTransactionsWithNewTransaction() {
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(GET_ALL_SHARED_TRANSACTIONS));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, timeout = 3600)
    public void dropSharersOnlyCnxOrNoShowIndex() {
        crudService.executeUpdateByNativeQuery(DROP_SHARERS_ONLY_CNX_NO_SHOW_INDEX);
        LOGGER.info("DROP_SHARERS_ONLY_CNX_NO_SHOW_INDEX completed");
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, timeout = 3600)
    public void getCanceledAndNsTrxFromSharers(boolean isIncludeNoShowCancellationRevenue) {
        crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(isIncludeNoShowCancellationRevenue ? GET_CANCELLED_AND_NS_TRX_WITH_REVENUE_FROM_SHARERS : GET_CANCELLED_AND_NS_TRX_FROM_SHARERS));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, timeout = 3600)
    public void createSharersOnlyCnxOrNoShowIndex() {
        crudService.executeUpdateByNativeQuery(CREATE_SHARERS_ONLY_CNX_NO_SHOW_INDEX);
        LOGGER.info("CREATE_SHARERS_ONLY_CNX_NO_SHOW_INDEX completed");
    }

    protected String forceLegacyCardinalityEstimator(String sqlString) {
        if (!isRefactorTransactionSQL()) {
            return sqlString;
        }

        sqlString = sqlString.trim();
        if (sqlString.endsWith(";")) {
            sqlString = sqlString.substring(0, sqlString.length() - 1);
        }

        return sqlString + " \nOPTION (USE HINT ('FORCE_LEGACY_CARDINALITY_ESTIMATION'))";
    }

    private boolean isRefactorTransactionSQL() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.REFACTOR_TRANSFORM_TRANSACTION_SQL);
    }
}