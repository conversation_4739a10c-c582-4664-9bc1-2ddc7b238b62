package com.ideas.tetris.pacman.services.property.dto;

import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.regex.Pattern;

@SuppressWarnings("serial")
public class PacmanExtractFile extends File {
    //SandBox_RATVDW_20110705_2319_T2SNAP.zip
    private static final String DELIMITER = "_";
    private static final String DATE_FORMAT = "yyyyMMdd";
    private static final String DATE_TIME_FORMAT = "yyyyMMddHHmm";
    private static final String FOLDER_FORMAT = "yyyy_MM";

    private String clientCode;

    private String propertyCode;
    private String dateString;
    private String timeString;
    private Date date;
    private Date dateTime;
    private String folderName;

    public PacmanExtractFile(String filename) {
        super(filename);
        init(getName());
    }

    public PacmanExtractFile(File parent, String child) {
        super(parent, child);
        init(getName());
    }

    private void init(String filename) {
        // a valid extract file name is :
        //SandBox_RATVDW_20110705_2319_T2SNAP.zip
        // TESTPROP.201207.2315.tar.Z
        if (filename != null) {
            String[] segments = filename.split("[" + DELIMITER + "]");
            if (segments.length == 5 && segments[segments.length - 1].equals("T2SNAP.zip")) {

                clientCode = segments[0];
                propertyCode = segments[1];
                dateString = segments[2];
                timeString = segments[3];

                if (Pattern.matches("\\d{8}", dateString)) {
                    try {
                        date = new SimpleDateFormat(DATE_FORMAT).parse(dateString);
                        folderName = new SimpleDateFormat(FOLDER_FORMAT).format(date);
                    } catch (ParseException e) {
                    }
                }
                if (Pattern.matches("\\d{12}", dateString + timeString)) {
                    try {
                        dateTime = new SimpleDateFormat(DATE_TIME_FORMAT).parse(dateString + timeString);
                    } catch (ParseException e) {
                    }
                }
            }
        }
    }

    public String getClientCode() {
        return clientCode;
    }

    public String getPropertyCode() {
        return propertyCode;
    }

    public String getDateString() {
        return dateString;
    }

    public String getTimeString() {
        return timeString;
    }

    public Date getDate() {
        return date;
    }

    public Date getDateTime() {
        return dateTime;
    }

    /**
     * For this class a valid ExtractFile will end in _T2SNAP.zip, have a non-empty property code, and parseable date/time segments.
     *
     * @return
     */
    public boolean isValid() {
        return !StringUtils.isEmpty(propertyCode) && date != null && dateTime != null;
    }


    /**
     * Return the name of the folder we would expect this file to be in
     *
     * @return
     */
    public String getFolderName() {
        return folderName;
    }

    /**
     * Returns filename without the _T2SNAP.zip
     *
     * @return
     */
    public String getBaseFilename() {
        return StringUtils.join(Arrays.asList(clientCode, propertyCode, dateString, timeString), DELIMITER);
    }
}
