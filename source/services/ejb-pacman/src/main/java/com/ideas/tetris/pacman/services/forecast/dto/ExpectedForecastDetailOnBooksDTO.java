package com.ideas.tetris.pacman.services.forecast.dto;

import java.util.Date;

public class ExpectedForecastDetailOnBooksDTO {

    private Date occupancyDate;
    private String transientOnBooks;
    private String expectedOnBooks;
    private String averageDOWThisYear;
    private String stly;
    private String st2y;
    private String transientOnBooksOthers;
    private String groupOnBooks;
    private String totalOnBooks;
    private String totalForecast;
    private String myForecast;

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public String getTransientOnBooks() {
        return transientOnBooks;
    }

    public void setTransientOnBooks(String transientOnBooks) {
        this.transientOnBooks = transientOnBooks;
    }

    public String getExpectedOnBooks() {
        return expectedOnBooks;
    }

    public void setExpectedOnBooks(String expectedOnBooks) {
        this.expectedOnBooks = expectedOnBooks;
    }

    public String getAverageDOWThisYear() {
        return averageDOWThisYear;
    }

    public void setAverageDOWThisYear(String averageDOWThisYear) {
        this.averageDOWThisYear = averageDOWThisYear;
    }

    public String getStly() {
        return stly;
    }

    public void setStly(String stly) {
        this.stly = stly;
    }

    public String getSt2y() {
        return st2y;
    }

    public void setSt2y(String st2y) {
        this.st2y = st2y;
    }

    public String getTransientOnBooksOthers() {
        return transientOnBooksOthers;
    }

    public void setTransientOnBooksOthers(String transientOnBooksOthers) {
        this.transientOnBooksOthers = transientOnBooksOthers;
    }

    public String getGroupOnBooks() {
        return groupOnBooks;
    }

    public void setGroupOnBooks(String groupOnBooks) {
        this.groupOnBooks = groupOnBooks;
    }

    public String getTotalOnBooks() {
        return totalOnBooks;
    }

    public void setTotalOnBooks(String totalOnBooks) {
        this.totalOnBooks = totalOnBooks;
    }

    public String getTotalForecast() {
        return totalForecast;
    }

    public void setTotalForecast(String totalForecast) {
        this.totalForecast = totalForecast;
    }

    public String getMyForecast() {
        return myForecast;
    }

    public void setMyForecast(String myForecast) {
        this.myForecast = myForecast;
    }
}
