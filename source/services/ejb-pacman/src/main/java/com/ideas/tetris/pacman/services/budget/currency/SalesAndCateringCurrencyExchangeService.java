package com.ideas.tetris.pacman.services.budget.currency;

 
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.budget.dto.CurrencyExchangeDTO;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.pacman.util.CollectionUtils;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class SalesAndCateringCurrencyExchangeService {

    @Autowired
	protected PacmanConfigParamsService configParamsService;

    @Autowired
    private CurrencyExchangeFactory currencyExchangeFactory;

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesAndCateringCurrencyExchangeService.class);

    private String getExternalSystemType() {
        return configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM);
    }


    public CurrencyExchangeDTO getCurrencyExchangeDTO(String clientCode, String propertyCode, String salesAndCateringCurrencyCode) {
        AbstractCurrencyExchangeService currencyExchangeEvaluator = getCurrencyExchangeEvaluator();
        String rmsCurrencyCode = currencyExchangeEvaluator.getRMSCurrencyCode(salesAndCateringCurrencyCode);
        List<CurrencyExchangeDTO> currencyExchangeDTOs = currencyExchangeEvaluator.getCurrencyExchangeDTOS(clientCode, propertyCode, java.time.LocalDate.now(), java.time.LocalDate.now(), salesAndCateringCurrencyCode, rmsCurrencyCode);
        return CollectionUtils.isNotEmpty(currencyExchangeDTOs) ? currencyExchangeDTOs.iterator().next() : new CurrencyExchangeDTO();
    }

    public boolean isCurrencyConversionRequired(String salesAndCateringCurrencyCode) {
        AbstractCurrencyExchangeService currencyExchangeEvaluator = getCurrencyExchangeEvaluator();
        return currencyExchangeEvaluator.isCurrencyConversionRequired(salesAndCateringCurrencyCode);
    }

    public AbstractCurrencyExchangeService getCurrencyExchangeEvaluator() {
        ExternalSystemType externalSystemType = ExternalSystemType.valueOf(getExternalSystemType().toUpperCase());
        AbstractCurrencyExchangeService currencyExchangeService = currencyExchangeFactory.getCurrencyExchangeService(externalSystemType);
        if (currencyExchangeService == null) {
            TetrisException tetrisException = new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Unable to obtained Currency Exchange Evaluator for external system " + externalSystemType);
            LOGGER.error(String.valueOf(tetrisException));
            throw tetrisException;
        }

        return currencyExchangeService;
    }

    public BigDecimal getConvertedValueUsingExchangeRate(BigDecimal revenueValue, BigDecimal exchangeRate) {
        if (Objects.isNull(revenueValue) || Objects.isNull(exchangeRate)) {
            return revenueValue;
        }
        BigDecimal v = BigDecimalUtil.multiply(revenueValue, exchangeRate, 0);
        return v.setScale(2, RoundingMode.HALF_UP);
    }

    public Map<LocalDate, BigDecimal> getCurrencyExchangeFactorForYearUsingPreferredDate(String salesAndCateringCurrencyCode, LocalDate departureDate, String rmsCurrencyCode, AbstractCurrencyExchangeService currencyExchangeEvaluator) {
        LocalDate startDate = departureDate.with(TemporalAdjusters.firstDayOfYear());
        LocalDate endDate = getEndDate(startDate, departureDate);

        List<CurrencyExchangeDTO> currencyExchangeDTOs = currencyExchangeEvaluator.getCurrencyExchangeDTOS(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode(), startDate, endDate, salesAndCateringCurrencyCode, rmsCurrencyCode);

        return CollectionUtils.isNotEmpty(currencyExchangeDTOs) ? createDateWiseMapForExchangeRate(currencyExchangeDTOs, startDate, endDate) : new HashMap<>();
    }

    protected LocalDate getEndDate(LocalDate startDate, LocalDate departureDate) {
        LocalDate currentDate = LocalDate.now();
        LocalDate endDate = departureDate.with(TemporalAdjusters.lastDayOfYear());
        if (startDate.isBefore(currentDate) && endDate.isAfter(currentDate)) {
            endDate = currentDate;
        }
        return endDate;
    }

    private Map<LocalDate, BigDecimal> createDateWiseMapForExchangeRate(List<CurrencyExchangeDTO> currencyExchangeDTOs, LocalDate startDate, LocalDate endDate) {
        Map<LocalDate, BigDecimal> dateWiseExchangeRateMap = new HashMap<>();
        currencyExchangeDTOs.forEach(currencyExchangeDTO -> {
            if (Objects.equals(currencyExchangeDTO.getFromDate().getYear(), startDate.getYear())) {
                dateWiseExchangeRateMap.put(currencyExchangeDTO.getFromDate(), currencyExchangeDTO.getExchangeRate());
            }
        });
        CurrencyExchangeDTO lastDateDto = currencyExchangeDTOs.stream().reduce((first, second) -> second).get();

        if (!Objects.equals(lastDateDto.getFromDate().getYear(), startDate.getYear())) {
            startDate.plusDays(1).datesUntil(endDate.plusDays(1)).forEach(date -> {
                dateWiseExchangeRateMap.computeIfAbsent(date, e -> lastDateDto.getExchangeRate());
            });
        } else if (!Objects.equals(lastDateDto.getFromDate(), endDate)) {
            lastDateDto.getFromDate().plusDays(1).datesUntil(endDate.plusDays(1)).forEach(date -> {
                dateWiseExchangeRateMap.computeIfAbsent(date, e -> lastDateDto.getExchangeRate());
            });
        }

        return dateWiseExchangeRateMap;
    }

    public boolean isConversionRequired(String currencyCode, String rmsCurrencyCode) {
        return !StringUtils.equals(currencyCode, rmsCurrencyCode);
    }
}
