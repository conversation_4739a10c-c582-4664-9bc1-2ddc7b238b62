package com.ideas.tetris.pacman.services.benefits.controller;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.benefits.dto.BenefitsDto;
import com.ideas.tetris.pacman.services.benefits.service.BenefitsMeasurementService;

import lombok.extern.slf4j.Slf4j;
import javax.validation.constraints.NotNull;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;


@Component
@Transactional
@Slf4j
public class BenefitsMeasurementEjbController {

    @Autowired
	private BenefitsMeasurementService service;

    
    
    public void startBenefitMeasurementJob( String monthYear,  String propertyIds) {
        cleanBenefitsData(monthYear, propertyIds);
        log.info("Processing BenefitMeasurementJob through API for | Month : {} | Properties : {}", monthYear, propertyIds);
        service.startBenefitMeasurementJob(monthYear, propertyIds);
    }

    
    public void cleanBenefitsData( String monthYear,  String propertyIds) {
        service.cleanBenefitsData(monthYear, propertyIds);
    }

    public List<String> startBenefitMeasurementBackDateJob(String propertyIds) {
        log.info("Processing BenefitMeasurementBackDateJob through API for | Properties : {}", propertyIds);
         return service.startBenefitMeasurementBackDateJob( propertyIds);
    }



    public List<BenefitsDto> getBenefitMeasurements(@NotNull   String startMonth, @NotNull   String endMonth ) {
        return service.getBenefits(startMonth, endMonth);
    }



    public void deleteBenefits() {
        service.deleteBenefits(PacmanWorkContextHelper.getPropertyId());
    }

}
