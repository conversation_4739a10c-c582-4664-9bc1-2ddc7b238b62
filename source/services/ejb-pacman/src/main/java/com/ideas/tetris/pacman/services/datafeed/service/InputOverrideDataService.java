package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.datafeed.dto.InputOverride;
import com.ideas.tetris.pacman.services.reports.inputoverride.InputOverrideService;
import com.ideas.tetris.pacman.services.reports.inputoverride.dto.InputOverrideCategoryEnum;
import com.ideas.tetris.pacman.services.reports.inputoverride.dto.InputOverrideDTO;
import com.ideas.tetris.pacman.services.reports.inputoverride.dto.InputOverrideFilterDTO;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class InputOverrideDataService {

    @Autowired
    InputOverrideService inputOverrideService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    public List<InputOverride> getInputOverrideForDatafeed(final Date startDate, final Date endDate) {
        final Map<String, String> overrideCategoryTypeMap = new HashMap<>(3);
        overrideCategoryTypeMap.put("common.wash", InputOverrideCategoryEnum.WASH.getCaption());
        overrideCategoryTypeMap.put("Demand.Arrival.By.LOS", InputOverrideCategoryEnum.DEMAND_ARRIVAL_BY_LOS.getCaption());
        overrideCategoryTypeMap.put("Demand.Occupancy.Date", InputOverrideCategoryEnum.DEMAND_OCCUPANCY_DATE.getCaption());

        final InputOverrideFilterDTO inputOverrideFilterDTO = populateInputOverrideFilterDTO(DateUtil.convertJavaUtilDateToLocalDate(startDate), DateUtil.convertJavaUtilDateToLocalDate(endDate));
        final List<InputOverrideDTO> inputOverrideDTOList = inputOverrideService.getOverrideData(inputOverrideFilterDTO);
        if (!CollectionUtils.isEmpty(inputOverrideDTOList)) {
            final List<InputOverride> inputOverrideList = new ArrayList<>();
            inputOverrideDTOList.stream().forEach(inputOverrideDTO -> {
                InputOverride inputOverride = new InputOverride();
                inputOverride.setRoomClassCode(inputOverrideDTO.getAccomClassName());
                inputOverride.setForecastGroupCode(inputOverrideDTO.getForecstGroupCode());
                inputOverride.setLos(formatLos(inputOverrideDTO.getLos()));
                inputOverride.setNotes(inputOverrideDTO.getNotes());
                inputOverride.setOccupancyDate(
                        (inputOverrideDTO.getOccupancyDate() == null) ? null : DateUtil.convertLocalDateToJavaUtilDate(inputOverrideDTO.getOccupancyDate()));
                inputOverride.setOverrideCategory(overrideCategoryTypeMap.get(inputOverrideDTO.getOverrideCategory()));
                inputOverride.setOverrideLastModifiedOn(inputOverrideDTO.getOverrideLastModifiedOn());
                inputOverride.setUserDemandOverride(formatUserDemandOverride(inputOverrideDTO.getUserDemandOverride()));
                inputOverride.setOverrideLastModifiedBy(inputOverrideDTO.getUserEmail());
                if (useUniqueUserIDInsteadOfEmailEnabled()) {
                    inputOverride.setOverrideLastModifiedBy(inputOverrideDTO.getUserName());
                }
                inputOverride.setUserWashExpirationDate(
                        (inputOverrideDTO.getUserWashExpirationDate() == null) ? null : formatUserWashExpirationDate(inputOverrideDTO.getUserWashExpirationDate()));
                inputOverride.setUserWashOverridePercent(formatUserDemandOverride(inputOverrideDTO.getUserWashOverridePerc()));
                inputOverrideList.add(inputOverride);

            });
            return inputOverrideList;
        }
        return Collections.<InputOverride>emptyList();
    }

    private boolean useUniqueUserIDInsteadOfEmailEnabled() {
        return Boolean.parseBoolean(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.USE_UNIQUE_USERID_INSTEADOF_EMAILID.getParameterName()));
    }

    private InputOverrideFilterDTO populateInputOverrideFilterDTO(final LocalDate startDate, final LocalDate endDate) {
        final InputOverrideFilterDTO inputOverrideFilterDTO = new InputOverrideFilterDTO();
        inputOverrideFilterDTO.setStartDate(startDate);
        inputOverrideFilterDTO.setEndDate(endDate);

        inputOverrideFilterDTO.setIsWashEnable(true);
        inputOverrideFilterDTO.setIsDemandArrivalByLosEnable(true);
        inputOverrideFilterDTO.setIsDemandOccupancyDateEnable(true);
        inputOverrideFilterDTO.setGffOverrideFetch(false);
        inputOverrideFilterDTO.setIsIncludeNoteEnable(true);
        inputOverrideFilterDTO.setIsRollingDate(0);
        return inputOverrideFilterDTO;
    }

    private Integer formatLos(final Integer los) {
        return (los == -1) ? null : los;
    }

    private BigDecimal formatUserDemandOverride(final BigDecimal userDemandOverride) {
        if (userDemandOverride == null || userDemandOverride.intValue() == -1) {
            return null;
        }
        return userDemandOverride;
    }

    private Date formatUserWashExpirationDate(final LocalDate userWashExpirationDate) {
        final LocalDate nullDate = LocalDate.of(1971, 1, 1);
        return (userWashExpirationDate.compareTo(nullDate) == 0) ? null : DateUtil.convertLocalDateToJavaUtilDate(userWashExpirationDate);
    }

    public void setInputOverrideService(InputOverrideService inputOverrideService) {
        this.inputOverrideService = inputOverrideService;
    }

    public void setConfigParamsService(PacmanConfigParamsService configParamsService) {
        this.configParamsService = configParamsService;
    }
}
