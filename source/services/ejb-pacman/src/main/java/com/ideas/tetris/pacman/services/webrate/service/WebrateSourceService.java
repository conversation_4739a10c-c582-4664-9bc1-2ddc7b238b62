package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.webrate.dto.WebrateSourceDto;
import com.ideas.tetris.pacman.services.webrate.entity.Webrate;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateSource;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateSourceProperty;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import javax.inject.Inject;
import javax.ws.rs.NotFoundException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.webrate.entity.WebrateSource.PARAM_ID;
import static com.ideas.tetris.pacman.services.webrate.entity.WebrateSource.WEBRATE_SOURCE_NAME;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class WebrateSourceService {
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    public static final String DELETE_FROM_PACE_WEBRATE_DIFFERENTIAL_BY_SOURCE_PROPERTY_IDS = "delete from PACE_Webrate_Differential where Webrate_Source_Property_ID in (:webrateSourcePropertyIds)";

    public List<WebrateSourceDto> getWebrateSources() {
        return crudService.findAll(WebrateSource.class).stream().map(this::convertToDto).collect(Collectors.toList());
    }

    public WebrateSourceDto getWebrateSourceById(Integer id) {
        WebrateSource webrateSource = crudService.find(WebrateSource.class, id);

        if (null == webrateSource) {
            throw new NotFoundException("No webrate-source found with id : " + id);
        }
        return convertToDto(webrateSource);
    }

    public void createWebrateSource(WebrateSourceDto webrateSourceDto) {
        if (null == webrateSourceDto.getWebrateSourceName()) {
            throw new IllegalArgumentException("Webrate-source name cannot be null");
        }
        WebrateSource webrateSource = crudService.findByNamedQuerySingleResult(WebrateSource.BY_SOURCE_NAME,
                QueryParameter.with(WEBRATE_SOURCE_NAME, webrateSourceDto.getWebrateSourceName().trim()).parameters());

        crudService.save(convertToEntity(webrateSourceDto, webrateSource));
    }

    public void deleteWebrateSourceById(Integer id) {
        List<WebrateSourceProperty> webrateSourceProperties = crudService.findByNamedQuery(WebrateSourceProperty.BY_PROPERTY_ID_AND_WEBRATE_SOURCE_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("webrateSourceId", id).parameters());
        Set<Integer> webrateSourcePropertyIds = webrateSourceProperties.stream().map(WebrateSourceProperty::getId).collect(Collectors.toSet());

        if (isNotEmpty(webrateSourcePropertyIds)) {
            crudService.executeUpdateByNativeQuery(DELETE_FROM_PACE_WEBRATE_DIFFERENTIAL_BY_SOURCE_PROPERTY_IDS, QueryParameter.with("webrateSourcePropertyIds", webrateSourcePropertyIds).parameters());
            crudService.executeUpdateByNamedQuery(Webrate.DELETE_BY_SOURCE_PROPERTY_IDS, QueryParameter.with("webrateSourcePropertyIds", webrateSourcePropertyIds).parameters());
            crudService.executeUpdateByNamedQuery(WebrateSourceProperty.DELETE_BY_WEBRATE_SOURCE_ID, QueryParameter.with("webrateSourceId", id).parameters());
        }
        crudService.executeUpdateByNamedQuery(WebrateSource.DELETE_BY_ID, QueryParameter.with(PARAM_ID, id).parameters());
    }

    private WebrateSourceDto convertToDto(WebrateSource webrateSource) {
        WebrateSourceDto webrateSourceDto = new WebrateSourceDto();
        webrateSourceDto.setWebrateSourceId(webrateSource.getId());
        webrateSourceDto.setWebrateSourceName(webrateSource.getWebrateSourceName());
        webrateSourceDto.setWebrateSourceDescription(webrateSource.getWebrateSourceDescription());
        webrateSourceDto.setCreateDate(webrateSource.getCreateDate());

        return webrateSourceDto;
    }

    private WebrateSource convertToEntity(WebrateSourceDto webrateSourceDto, WebrateSource webrateSource) {
        if (null == webrateSource) {
            webrateSource = new WebrateSource();
        }
        webrateSource.setWebrateSourceName(webrateSourceDto.getWebrateSourceName());
        webrateSource.setWebrateSourceDescription(webrateSourceDto.getWebrateSourceDescription());

        return webrateSource;
    }
}
