package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.tetris.platform.common.rest.annotation.DateFormat;

import javax.ws.rs.DefaultValue;
import javax.ws.rs.QueryParam;
import java.util.Date;

public class Pageable {

    @QueryParam("page")
    @DefaultValue("0")
    private int page;

    @QueryParam("size")
    @DefaultValue("100")
    private int size;

    @QueryParam("startDate")
    @DateFormat
    private Date startDate;

    @QueryParam("endDate")
    @DateFormat
    private Date endDate;

    public Pageable() {
    }

    public Pageable(int page, int size, Date startDate, Date endDate) {
        this.page = page;
        this.size = size;
        this.startDate = startDate;
        this.endDate = endDate;

    }

    public int getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public int getStartPosition() {
        return page * size;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }


}
