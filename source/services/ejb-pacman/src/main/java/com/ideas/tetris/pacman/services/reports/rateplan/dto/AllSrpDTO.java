package com.ideas.tetris.pacman.services.reports.rateplan.dto;

import java.math.BigDecimal;

public class AllSrpDTO {

    private String ratePlanName;
    private Integer roomSold;
    private BigDecimal roomRevenue;
    private String forecastGroupName;
    private String marketSegmentName;

    public String getRatePlanName() {
        return ratePlanName;
    }

    public void setRatePlanName(String ratePlanName) {
        this.ratePlanName = ratePlanName;
    }

    public Integer getRoomSold() {
        return roomSold;
    }

    public void setRoomSold(Integer roomSold) {
        this.roomSold = roomSold;
    }

    public BigDecimal getRoomRevenue() {
        return roomRevenue;
    }

    public void setRoomRevenue(BigDecimal roomRevenue) {
        this.roomRevenue = roomRevenue;
    }

    public String getForecastGroupName() {
        return forecastGroupName;
    }

    public void setForecastGroupName(String forecastGroupName) {
        this.forecastGroupName = forecastGroupName;
    }

    public String getMarketSegmentName() {
        return marketSegmentName;
    }

    public void setMarketSegmentName(String marketSegmentName) {
        this.marketSegmentName = marketSegmentName;
    }

}
