package com.ideas.tetris.pacman.services.reports.operations.vo;

public class OperationsReportVO implements Comparable {
    private Integer propertyID;
    private String occupancyDate;
    private String shortName;
    private Integer occupancyforecast;
    private Integer departureForecast;
    private Integer arrivalForecast;
    private Integer roomsNotAvailOther;
    private Integer roomDepartures;
    private Integer roomsSold;
    private Integer roomsNotAvailMaint;
    private Integer roomCapacity;
    private Integer ooo;
    private Integer roomArrivals;
    private Integer onBooksStayThrus;
    private Integer forecastStayThrus;

    public Integer getOnBooksStayThrus() {
        return onBooksStayThrus;
    }

    public void setOnBooksStayThrus(Integer onBooksStayThrus) {
        this.onBooksStayThrus = onBooksStayThrus;
    }

    public Integer getForecastStayThrus() {
        return forecastStayThrus;
    }

    public void setForecastStayThrus(Integer forecastStayThrus) {
        this.forecastStayThrus = forecastStayThrus;
    }

    public Integer getRoomArrivals() {
        return roomArrivals;
    }

    public void setRoomArrivals(Integer roomArrivals) {
        this.roomArrivals = roomArrivals;
    }

    public Integer getPropertyID() {
        return propertyID;
    }

    public void setPropertyID(Integer propertyID) {
        this.propertyID = propertyID;
    }

    public String getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(String occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public Integer getOccupancyforecast() {
        return occupancyforecast;
    }

    public void setOccupancyforecast(Integer occupancyforecast) {
        this.occupancyforecast = occupancyforecast;
    }

    public Integer getDepartureForecast() {
        return departureForecast;
    }

    public void setDepartureForecast(Integer departureForecast) {
        this.departureForecast = departureForecast;
    }

    public Integer getArrivalForecast() {
        return arrivalForecast;
    }

    public void setArrivalForecast(Integer arrivalForecast) {
        this.arrivalForecast = arrivalForecast;
    }

    public Integer getRoomsNotAvailOther() {
        return roomsNotAvailOther;
    }

    public void setRoomsNotAvailOther(Integer roomsNotAvailOther) {
        this.roomsNotAvailOther = roomsNotAvailOther;
    }

    public Integer getRoomDepartures() {
        return roomDepartures;
    }

    public void setRoomDepartures(Integer roomDepartures) {
        this.roomDepartures = roomDepartures;
    }

    public Integer getRoomsSold() {
        return roomsSold;
    }

    public void setRoomsSold(Integer roomsSold) {
        this.roomsSold = roomsSold;
    }

    public Integer getRoomsNotAvailMaint() {
        return roomsNotAvailMaint;
    }

    public void setRoomsNotAvailMaint(Integer roomsNotAvailMaint) {
        this.roomsNotAvailMaint = roomsNotAvailMaint;
    }

    public Integer getRoomCapacity() {
        return roomCapacity;
    }

    public void setRoomCapacity(Integer roomCapacity) {
        this.roomCapacity = roomCapacity;
    }

    public Integer getOoo() {
        return ooo;
    }

    public void setOoo(Integer ooo) {
        this.ooo = ooo;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        OperationsReportVO that = (OperationsReportVO) o;

        return occupancyDate != null ? occupancyDate.equals(that.occupancyDate) : that.occupancyDate == null;
    }

    @Override
    public int hashCode() {
        return occupancyDate != null ? occupancyDate.hashCode() : 0;
    }

    @Override
    public int compareTo(Object o) {
        OperationsReportVO vo = (OperationsReportVO) o;
        return this.occupancyDate.compareTo(vo.getOccupancyDate());
    }
}
