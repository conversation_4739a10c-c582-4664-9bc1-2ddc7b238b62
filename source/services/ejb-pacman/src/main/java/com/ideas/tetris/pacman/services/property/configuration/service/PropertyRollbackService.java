package com.ideas.tetris.pacman.services.property.configuration.service;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.datafeed.service.DecisionConfigurationService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.property.ExtractDetailsServiceLocal;
import com.ideas.tetris.pacman.services.property.PropertyBuildType;
import com.ideas.tetris.pacman.services.property.PropertyRolloutService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.property.dto.Property;
import com.ideas.tetris.pacman.services.reservation.service.ReservationDataService;
import com.ideas.tetris.pacman.services.rollback.EmailNotificationService;
import com.ideas.tetris.pacman.services.rollback.NewFeatureDatasetBackupService;
import com.ideas.tetris.pacman.services.rollback.NewFeatureDatasetRestoreService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.jmsclient.TetrisEventManager;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import static com.ideas.tetris.pacman.services.reservation.service.ReservationDataService.RESERVATION_DATA_VERSION_AFTER_RESERVATION_NIGHT_CHANGE_SUPPORT;
import static java.util.Collections.emptyMap;

@Transactional(propagation = Propagation.NEVER)
@Component
public class PropertyRollbackService {
    private static final Logger LOGGER = Logger.getLogger(PropertyRollbackService.class.getName());

    @Autowired
	protected PropertyRolloutService propertyRolloutService;
    @Autowired
	protected PropertyConfigurationLoaderService propertyConfigurationLoaderService;
    @Autowired
	protected TetrisEventManager tetrisEventManager;
    @Autowired
	protected ExtractDetailsServiceLocal extractDetailsService;
    @Autowired
	protected NewFeatureDatasetBackupService newFeatureDatasetBackupService;
    @Autowired
	protected NewFeatureDatasetRestoreService newFeatureDatasetRestoreService;
    @Autowired
	protected EmailNotificationService emailNotificationService;
    @Autowired
	protected DecisionConfigurationService decisionConfigurationService;
    @Autowired
	protected ReservationDataService reservationDataService;
    @Autowired
	protected PropertyService propertyService;

    /**
     * @deprecated (legacy for Hilton)
     */
    @Deprecated
    public Property rollbackProperty(Property property) {
        Integer propertyId = property.getId();
        String propertyCode = property.getCode();
        LOGGER.info("Beginning rollback for property: " + propertyCode + "," + propertyId);
        propertyRolloutService.inactivateProperty(property);
        LOGGER.info("Complete rollback inactivate for property: " + propertyCode + "," + propertyId);
        PacmanWorkContextHelper.setPropertyId(propertyId);
        Integer reservationDataVersion = propertyService.getReservationDataVersion();
        if (reservationDataVersion == null) {
            reservationDataVersion = 0;
        }
        try {
            newFeatureDatasetBackupService.backup(propertyId);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error Backing Up New Feature Dataset", e);
        }

        emailAnalyticalOverrides(property);
        // need to save the condition before propertyRolloutService.cleanProperty(property) because after that all the config parameters are deleted.
        boolean shouldScheduledTwoWayDateParameterBeReset = decisionConfigurationService.shouldScheduledTwoWayParameterBeReset();

        try {
            propertyRolloutService.cleanProperty(property);
        } catch (Exception e) {
            throwTetrisException(ErrorCode.ROLLBACK_REMOVE_PROPERTY_FAILED,
                    "Error occured removing property", e);
        }

        LOGGER.info("Complete rollback rollout for property: " + propertyCode + "," + propertyId);
        try {
            propertyConfigurationLoaderService.resetConfiguration(propertyCode);
            propertyConfigurationLoaderService.addProperty(propertyCode, true, false, emptyMap(), PropertyBuildType.STANDARD);
        } catch (Exception e) {
            throwTetrisException(ErrorCode.ROLLBACK_ADD_PROPERTY_FAILED,
                    "Error occured re-adding property", e);
        }


        try {
            newFeatureDatasetRestoreService.restore(propertyId);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error Restoring New Feature Dataset", e);
        }

        LOGGER.info("Complete rollback configuration for property: " + propertyCode + "," + propertyId);
        try {
            extractDetailsService.moveArchivedExtractsToIncoming(propertyId);
            extractDetailsService.moveArchivedWebRateExtractsToIncoming(propertyId);
        } catch (Exception e) {
            throwTetrisException(ErrorCode.ROLLBACK_MOVE_FILES_FAILED,
                    "Error occured moving archived extracts to incoming folder", e);
        }
        if (shouldScheduledTwoWayDateParameterBeReset) {
            decisionConfigurationService.resetScheduledTwoWayDate(PacmanWorkContextHelper.getClientCode(), propertyCode);
            decisionConfigurationService.createAlert(AlertType.ScheduledDecisionDeliveryDateCancelled.getName(), "scheduled.decision.delivery.date.cancelled.alert.details", "");
        }

        if (reservationDataVersion >= RESERVATION_DATA_VERSION_AFTER_RESERVATION_NIGHT_CHANGE_SUPPORT) {
            reservationDataService.initializeReservationNightChangeTable();
            propertyService.updateReservationDataVersion(reservationDataVersion);
        }
        LOGGER.info("Complete rollback extracts for property: " + propertyCode + "," + propertyId);
        createBookkeepingEvent(propertyId);
        return property;
    }

    private void emailAnalyticalOverrides(Property property) {
        PacmanWorkContextHelper.setPropertyId(property.getId());
        PacmanWorkContextHelper.setPropertyCode(property.getCode());
        emailNotificationService.triggerNotification();
    }

    private String getUserId() {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        return workContext != null ? workContext.getUserId() : "";
    }

    private void throwTetrisException(ErrorCode code, String msg, Exception e) {
        throw new TetrisException(code, msg, e);
    }

    protected void createBookkeepingEvent(int propertyId) {
        try {
            tetrisEventManager.raiseEvent(tetrisEventManager.buildRollbackInitiatedEvent(propertyId, getUserId()));
        } catch (Exception e) {
            LOGGER.error("Error occured creating bookkeeping event during rollback", e);
        }
    }
}
