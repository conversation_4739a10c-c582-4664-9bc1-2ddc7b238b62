package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.webrate.dto.WebrateCompetitorsAccomClassDto;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitorsAccomClass;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateOverrideCompetitorDetails;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.NotFoundException;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Transactional
public class WebrateCompetitorsAccomClassService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Autowired
    WebrateShoppingCleanUpService webrateShoppingCleanUpService;


    public List<WebrateCompetitorsAccomClassDto> getWebrateCompetitorsAccomClassDtos() {
        return crudService.findAll(WebrateCompetitorsAccomClass.class).stream().map(this::convertToDto).collect(Collectors.toList());
    }

    public WebrateCompetitorsAccomClassDto getWebrateCompetitorsAccomClassById(Integer id) {
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = crudService.find(WebrateCompetitorsAccomClass.class, id);

        if (null == webrateCompetitorsAccomClass) {
            throw new NotFoundException("No webrate-competitors-accom-class found with id " + id);
        }

        return convertToDto(webrateCompetitorsAccomClass);
    }

    public void createWebrateCompetitorsAccomClass(WebrateCompetitorsAccomClassDto dto) {
        crudService.save(convertToEntity(dto));
    }

    public void updateWebrateCompetitorAccomClass(Integer id, WebrateCompetitorsAccomClassDto dto) {
        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = crudService.find(WebrateCompetitorsAccomClass.class, id);

        if (null == webrateCompetitorsAccomClass) {
            throw new NotFoundException("No webrate-competitors-accom-class found with id " + id);
        }
        crudService.save(updateEntity(dto, webrateCompetitorsAccomClass));
    }

    public void deleteWebrateCompetitorAccomClassById(Integer id) {
        webrateShoppingCleanUpService.cleanupWebrateCompetitorChannelMappingsBasedOnCompAccomClassId(id);
        crudService.executeUpdateByNamedQuery(WebrateOverrideCompetitorDetails.DELETE_BY_ACCOMMAPPING_IDS,
                QueryParameter.with("compAccomClassIds", Set.of(id)).parameters());
        crudService.executeUpdateByNamedQuery(WebrateCompetitorsAccomClass.DELETE_BY_ID,
                QueryParameter.with("id", id).parameters());
    }

    private WebrateCompetitorsAccomClass updateEntity(WebrateCompetitorsAccomClassDto dto, WebrateCompetitorsAccomClass webrateCompetitorsAccomClass) {
        Optional.ofNullable(dto.getWebrateCompetitorsId())
                .ifPresent(id -> webrateCompetitorsAccomClass.setWebrateCompetitor(crudService.find(WebrateCompetitors.class, id)));
        Optional.ofNullable(dto.getAccomClassId())
                .ifPresent(id -> webrateCompetitorsAccomClass.setAccomClass(crudService.find(AccomClass.class, id)));
        Optional.ofNullable(dto.getDemandEnabled())
                .ifPresent(webrateCompetitorsAccomClass::setDemandEnabled);
        Optional.ofNullable(dto.getRankEnabled())
                .ifPresent(webrateCompetitorsAccomClass::setRankingEnabled);
        Optional.ofNullable(dto.getProductId())
                .ifPresent(webrateCompetitorsAccomClass::setProductID);
        Optional.ofNullable(dto.getDta())
                .ifPresent(webrateCompetitorsAccomClass::setDaysToArrival);

        return webrateCompetitorsAccomClass;
    }

    private WebrateCompetitorsAccomClass convertToEntity(WebrateCompetitorsAccomClassDto dto) {

        WebrateCompetitorsAccomClass webrateCompetitorsAccomClass = new WebrateCompetitorsAccomClass();

        webrateCompetitorsAccomClass.setWebrateCompetitor(crudService.find(WebrateCompetitors.class, dto.getWebrateCompetitorsId()));
        webrateCompetitorsAccomClass.setAccomClass(crudService.find(AccomClass.class, dto.getAccomClassId()));
        webrateCompetitorsAccomClass.setDemandEnabled(dto.getDemandEnabled());
        webrateCompetitorsAccomClass.setRankingEnabled(dto.getRankEnabled());
        webrateCompetitorsAccomClass.setProductID(dto.getProductId());
        webrateCompetitorsAccomClass.setDaysToArrival(dto.getDta());

        return webrateCompetitorsAccomClass;
    }

    private WebrateCompetitorsAccomClassDto convertToDto(WebrateCompetitorsAccomClass webrateCompetitorsAccomClass) {
        WebrateCompetitorsAccomClassDto webrateCompetitorsAccomClassDto = new WebrateCompetitorsAccomClassDto();
        webrateCompetitorsAccomClassDto.setWebrateCompetitorsClassId(webrateCompetitorsAccomClass.getId());
        webrateCompetitorsAccomClassDto.setWebrateCompetitorsId(webrateCompetitorsAccomClass.getWebrateCompetitor().getId());
        webrateCompetitorsAccomClassDto.setAccomClassId(webrateCompetitorsAccomClass.getAccomClass().getId());
        webrateCompetitorsAccomClassDto.setDemandEnabled(webrateCompetitorsAccomClass.getDemandEnabled());
        webrateCompetitorsAccomClassDto.setRankEnabled(webrateCompetitorsAccomClass.getRankingEnabled());
        webrateCompetitorsAccomClassDto.setProductId(webrateCompetitorsAccomClass.getProductID());
        webrateCompetitorsAccomClassDto.setDta(webrateCompetitorsAccomClass.getDaysToArrival());
        webrateCompetitorsAccomClassDto.setCreatedByUserId(webrateCompetitorsAccomClass.getCreatedByUserId());
        webrateCompetitorsAccomClassDto.setCreateDttm(webrateCompetitorsAccomClass.getCreateDate());
        webrateCompetitorsAccomClassDto.setLastUpdatedByUserId(webrateCompetitorsAccomClass.getLastUpdatedByUserId());
        webrateCompetitorsAccomClassDto.setLastUpdatedDttm(webrateCompetitorsAccomClass.getLastUpdatedDate());

        return webrateCompetitorsAccomClassDto;
    }
}
