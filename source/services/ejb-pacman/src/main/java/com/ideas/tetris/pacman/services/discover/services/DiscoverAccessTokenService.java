package com.ideas.tetris.pacman.services.discover.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.services.discover.DiscoverConstants;
import com.ideas.tetris.pacman.services.discover.rest.DiscoverRestEndPoints;
import com.ideas.tetris.pacman.services.discover.rest.DiscoverRestProperties;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.log4j.Logger;
import org.jboss.resteasy.specimpl.ResteasyUriBuilder;
import org.jboss.resteasy.util.HttpResponseCodes;
import org.json.JSONObject;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriBuilder;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Instant;
import java.util.*;

@Component
@Transactional
public class DiscoverAccessTokenService {

    static final String PARAMS_OAUTH_GRANT_TYPE = "urn:ietf:params:oauth:grant-type:jwt-bearer";
    private static final Logger LOGGER = Logger.getLogger(DiscoverAccessTokenService.class);
    private final Client client = ClientBuilder.newBuilder().build();
    private final DiscoverRestProperties discoverRestProperties = new DiscoverRestProperties();

    private static final String PKCS8_START = "-----BEGIN PRIVATE KEY-----";
    private static final String PKCS8_END = "-----END PRIVATE KEY-----";
    private static final String PKCS1_START = "-----BEGIN RSA PRIVATE KEY-----";
    private static final String PKCS1_END = "-----END RSA PRIVATE KEY-----";

    @SuppressWarnings("unchecked")
    @VisibleForTesting
	public
    String generateAccessToken() {
        Entity<?> e = Entity.entity(buildPayload(), MediaType.APPLICATION_JSON_TYPE);
        WebTarget target = client.target(buildUrl());
        Response response = null;
        try {
            response = target.request().headers(buildHeaders(false)).post(e);
            if (response.getStatusInfo().getStatusCode() >= HttpResponseCodes.SC_BAD_REQUEST) {
                if (Boolean.parseBoolean(System.getProperty("iat.logging.enabled", "true"))) {
                    JSONObject errorObject = new JSONObject(response.readEntity(String.class));
                    String errorPhrase = errorObject.getString("error");
                    String errorDescription = errorObject.getString("error_description");
                    LOGGER.error("Error requesting access token for Discover with status code: " + response.getStatusInfo().getStatusCode() +
                            " and reason: " + response.getStatusInfo().getReasonPhrase() +
                            " and errorPhrase: " + errorPhrase +
                            " and errorDescription: " + errorDescription
                    );
                }
                throw new TetrisException(ErrorCode.DISCOVER_REST_CLIENT_FAILURE,
                        "Error while requesting access token[" + response.getStatusInfo().getStatusCode() + "] "
                                + response.getStatusInfo().getReasonPhrase());
            }

            JSONObject responseObject = new JSONObject(response.readEntity(String.class));
            LOGGER.debug("Generated discover access token");
            return "Bearer " + responseObject.getString("access_token");
        } catch (Exception ce) {
            LOGGER.error("Error while generating access token from Discover");
            throw new TetrisException(ErrorCode.DISCOVER_REST_CLIENT_FAILURE, "Error while generating access token from Discover", ce);
        } finally {
            if (Objects.nonNull(response)) {
                response.close();
            }
        }
    }

    @SuppressWarnings("unchecked")
    public MultivaluedHashMap buildHeaders(boolean addAuthTokenHeader) {
        MultivaluedHashMap headers = new MultivaluedHashMap();
        headers.add("Content-Type", "application/json");
        headers.add("Accept", "application/json");
        if (addAuthTokenHeader) {
            headers.add("Authorization", generateAccessToken());
        }
        return headers;
    }

    private String buildUrl() {
        String urlToCall = discoverRestProperties.getUrl() + DiscoverRestEndPoints.GET_ACCESS_TOKEN_V3.getEndpoint();
        ResteasyUriBuilder uriBuilder = (ResteasyUriBuilder) UriBuilder.fromUri(urlToCall);
        return uriBuilder.buildFromMap(Collections.emptyMap()).toString();
    }

    @VisibleForTesting
	public
    String buildPayload() {
        Map<String, String> finalMap = new HashMap<>();
        finalMap.put(DiscoverConstants.GRANT_TYPE, PARAMS_OAUTH_GRANT_TYPE);
        finalMap.put(DiscoverConstants.ASSERTION, generateJwtToken());
        try {
            return new ObjectMapper().writeValueAsString(finalMap);
        } catch (JsonProcessingException e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error while creating payload for getting access token", e);
        }
    }

    private PrivateKey readPrivateKey() {
        String privateKeyStr = discoverRestProperties.getPrivateKey();
        privateKeyStr = privateKeyStr.replaceAll("\\s", "");
        if (privateKeyStr.contains(PKCS8_START)) {
            privateKeyStr = privateKeyStr.replace(PKCS8_START, "");
            privateKeyStr = privateKeyStr.replace(PKCS8_END, "");
            return readPkcs8PrivateKey(Base64.getDecoder().decode(privateKeyStr));
        }
        privateKeyStr = privateKeyStr.replace(PKCS1_START, "");
        privateKeyStr = privateKeyStr.replace(PKCS1_END, "");
        return readPkcs1PrivateKey(Base64.getDecoder().decode(privateKeyStr));
    }

    private PrivateKey readPkcs8PrivateKey(byte[] pkcs8Bytes) {
        return getPrivateKey(new PKCS8EncodedKeySpec(pkcs8Bytes));
    }

    /* Reference:
    https://stackoverflow.com/questions/7216969/getting-rsa-private-key-from-pem-base64-encoded-private-key-file/55339208#55339208
     */
    private PrivateKey readPkcs1PrivateKey(byte[] pkcs1Bytes) {
        // We can't use Java internal APIs to parse ASN.1 structures, so we build a PKCS#8 key Java can understand
        int pkcs1Length = pkcs1Bytes.length;
        int totalLength = pkcs1Length + 22;
        byte[] pkcs8Header = new byte[]{
                0x30, (byte) 0x82, (byte) ((totalLength >> 8) & 0xff), (byte) (totalLength & 0xff), // Sequence + total length
                0x2, 0x1, 0x0, // Integer (0)
                0x30, 0xD, 0x6, 0x9, 0x2A, (byte) 0x86, 0x48, (byte) 0x86, (byte) 0xF7, 0xD, 0x1, 0x1, 0x1, 0x5, 0x0, // Sequence: 1.2.840.113549.1.1.1, NULL
                0x4, (byte) 0x82, (byte) ((pkcs1Length >> 8) & 0xff), (byte) (pkcs1Length & 0xff) // Octet string + length
        };
        byte[] pkcs8bytes = join(pkcs8Header, pkcs1Bytes);
        return readPkcs8PrivateKey(pkcs8bytes);
    }

    private byte[] join(byte[] byteArray1, byte[] byteArray2) {
        byte[] bytes = new byte[byteArray1.length + byteArray2.length];
        System.arraycopy(byteArray1, 0, bytes, 0, byteArray1.length);
        System.arraycopy(byteArray2, 0, bytes, byteArray1.length, byteArray2.length);
        return bytes;
    }

    private PrivateKey getPrivateKey(KeySpec keySpec) {
        KeyFactory factory;
        try {
            factory = KeyFactory.getInstance("RSA");
        } catch (NoSuchAlgorithmException e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "RSA Algorithm not found in key factory", e);
        }
        try {
            return factory.generatePrivate(keySpec);
        } catch (InvalidKeySpecException e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR,
                    "Invalid  key spec found while generating private key from factory", e);
        }
    }

    @VisibleForTesting
	public
    String generateJwtToken() {
        Map<String, Object> jwtClaims = getJwtClaims();
        return Jwts.builder()
                .setClaims(jwtClaims)
                .signWith(SignatureAlgorithm.RS256, readPrivateKey())
                .compact();
    }

    @VisibleForTesting
	public
    Map<String, Object> getJwtClaims() {
        Map<String, Object> jwtClaims = new HashMap<>();
        jwtClaims.put("iss", discoverRestProperties.getLearningAccessUID());
        jwtClaims.put("scope", "admin_read admin_write");
        jwtClaims.put("aud", discoverRestProperties.getJwtClaimAudience());
        long iat;
        if (Boolean.parseBoolean(System.getProperty("iat.fix.enabled", "true"))) {
            iat = Instant.now().minusSeconds(30).getEpochSecond();
        } else {
            LOGGER.info("Not using iat fix");
            iat = Instant.now().getEpochSecond();
        }
        long exp = Instant.now().plusSeconds(60).getEpochSecond();
        jwtClaims.put("iat", iat);
        jwtClaims.put("exp", exp);
        return jwtClaims;
    }
}
