package com.ideas.tetris.pacman.services.reports.bookingpace.dto;

import java.math.BigDecimal;
import java.util.Date;

public class TotalGroupAndTransientDTO {
	/*
	 *  Occupancy_DT	date	no	3	10   	0    
		businessdate	date	no	3	10   	0    
		daystoArrival	int	no	4	10   	0    
		dow	varchar	no	10	     	     
		Group_RoomSold	numeric	no	9	18   	0    
		Tran_RoomSold	numeric	no	9	18   	0    
		Total_RoomSold	numeric	no	9	18   	0    
		Total_Accom_Capacity	numeric	no	9	18   	0    
		availableCapacity	numeric	no	9	18   	0    
		Tran_Occupancy_NBR	numeric	no	5	8    	2    
		Group_Occupancy_NBR	numeric	no	5	8    	2    
		Tran_Occupancy_NBR_Perc	numeric	no	5	8    	2    
		Group_Occupancy_NBR_Perc	numeric	no	5	8    	2    
		Occupancy_NBR	numeric	no	5	8    	2    
		Occupancy_NBR_Perc	numeric	no	5	8    	2    
	 * 
	 * */


    /*select * from dbo.ufn_get_booking_pace_total_trans_group_report (14,'2011-07-29',10)*/

    private Date occupancyDate;
    private Date businessDate;
    private Integer paceDays;
    private String dow;
    private BigDecimal groupRoomSold;
    private BigDecimal transientRoomSold;
    private BigDecimal totalRoomSold;
    private BigDecimal totalAccomCapacity;
    private BigDecimal availableCapacity;
    private BigDecimal transientForecast;
    private BigDecimal transientForecastPercentage;
    private BigDecimal groupForecast;
    private BigDecimal groupForecastPercentage;
    private BigDecimal hotelForecast;
    private BigDecimal hotelForecastPercentage;
    private Integer inventoryLimit;

    public Integer getInventoryLimit() {
        return inventoryLimit;
    }

    public void setInventoryLimit(Integer inventoryLimit) {
        this.inventoryLimit = inventoryLimit;
    }

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public Integer getPaceDays() {
        return paceDays;
    }

    public void setPaceDays(Integer paceDays) {
        this.paceDays = paceDays;
    }

    public String getDow() {
        return dow;
    }

    public void setDow(String dow) {
        this.dow = dow;
    }

    public BigDecimal getGroupRoomSold() {
        return groupRoomSold;
    }

    public void setGroupRoomSold(BigDecimal row) {
        this.groupRoomSold = row;
    }

    public BigDecimal getTransientRoomSold() {
        return transientRoomSold;
    }

    public void setTransientRoomSold(BigDecimal transientRoomSold) {
        this.transientRoomSold = transientRoomSold;
    }

    public BigDecimal getTotalRoomSold() {
        return totalRoomSold;
    }

    public void setTotalRoomSold(BigDecimal totalRoomSold) {
        this.totalRoomSold = totalRoomSold;
    }

    public BigDecimal getTotalAccomCapacity() {
        return totalAccomCapacity;
    }

    public void setTotalAccomCapacity(BigDecimal totalAccomCapacity) {
        this.totalAccomCapacity = totalAccomCapacity;
    }

    public BigDecimal getAvailableCapacity() {
        return availableCapacity;
    }

    public void setAvailableCapacity(BigDecimal availableCapacity) {
        this.availableCapacity = availableCapacity;
    }

    public BigDecimal getTransientForecast() {
        return transientForecast;
    }

    public void setTransientForecast(BigDecimal transientForecast) {
        this.transientForecast = transientForecast;
    }

    public BigDecimal getGroupForecast() {
        return groupForecast;
    }

    public void setGroupForecast(BigDecimal groupForecast) {
        this.groupForecast = groupForecast;
    }

    public BigDecimal getTransientForecastPercentage() {
        return transientForecastPercentage;
    }

    public void setTransientForecastPercentage(BigDecimal transientForecastPercentage) {
        this.transientForecastPercentage = transientForecastPercentage;
    }

    public BigDecimal getGroupForecastPercentage() {
        return groupForecastPercentage;
    }

    public void setGroupForecastPercentage(BigDecimal groupForecastPercentage) {
        this.groupForecastPercentage = groupForecastPercentage;
    }

    public BigDecimal getHotelForecast() {
        return hotelForecast;
    }

    public void setHotelForecast(BigDecimal hotelForecast) {
        this.hotelForecast = hotelForecast;
    }

    public BigDecimal getHotelForecastPercentage() {
        return hotelForecastPercentage;
    }

    public void setHotelForecastPercentage(BigDecimal hotelForecastPercentage) {
        this.hotelForecastPercentage = hotelForecastPercentage;
    }


}
