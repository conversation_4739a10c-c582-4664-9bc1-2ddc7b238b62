package com.ideas.tetris.pacman.services.forecast.dto;

import java.util.Date;

public class ExpectedForecastDetailForPastSevenDaysDTO {
    private Date occupancyDate;
    private String groups;
    private String transientOnBooks;
    private String otherTransientOnBooks;

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public String getGroups() {
        return groups;
    }

    public void setGroups(String groups) {
        this.groups = groups;
    }

    public String getTransientOnBooks() {
        return transientOnBooks;
    }

    public void setTransientOnBooks(String transientOnBooks) {
        this.transientOnBooks = transientOnBooks;
    }

    public String getOtherTransientOnBooks() {
        return otherTransientOnBooks;
    }

    public void setOtherTransientOnBooks(String otherTransientOnBooks) {
        this.otherTransientOnBooks = otherTransientOnBooks;
    }
}