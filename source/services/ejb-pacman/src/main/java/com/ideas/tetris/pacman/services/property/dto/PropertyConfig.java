package com.ideas.tetris.pacman.services.property.dto;

import java.util.ArrayList;
import java.util.List;

public class PropertyConfig {
    private List<Integer> propertyIds = new ArrayList<Integer>();
    private List<GlobalParameter> globalParameters = new ArrayList<GlobalParameter>();
    private List<PropertyExcludedDates> excludedDates = new ArrayList<PropertyExcludedDates>();

    public List<Integer> getPropertyIds() {
        return propertyIds;
    }

    public Integer getPropertyId() {
        if (propertyIds.isEmpty()) {
            return null;
        }
        return propertyIds.get(0);
    }

    public void setPropertyId(Integer propertyId) {
        propertyIds.clear();
        addPropertyId(propertyId);
    }

    public void setPropertyIds(List<Integer> propertyIds) {
        if (propertyIds != null) {
            this.propertyIds = propertyIds;
        }
    }

    public void addPropertyId(Integer propertyId) {
        if (propertyId != null) {
            propertyIds.add(propertyId);
        }
    }

    public List<GlobalParameter> getGlobalParameters() {
        return globalParameters;
    }

    public void setGlobalParameters(List<GlobalParameter> globalParameters) {
        if (globalParameters != null) {
            this.globalParameters = globalParameters;
        }
    }

    public void addGlobalParameter(GlobalParameter parameter) {
        if (parameter != null) {
            globalParameters.add(parameter);
        }
    }

    public List<PropertyExcludedDates> getExcludedDates() {
        return excludedDates;
    }

    public void setExcludedDates(List<PropertyExcludedDates> excludedDates) {
        if (excludedDates != null) {
            this.excludedDates = excludedDates;
        }
    }

    public void addExcludedDates(PropertyExcludedDates excludedDates) {
        if (excludedDates != null) {
            this.excludedDates.add(excludedDates);
        }
    }

}
