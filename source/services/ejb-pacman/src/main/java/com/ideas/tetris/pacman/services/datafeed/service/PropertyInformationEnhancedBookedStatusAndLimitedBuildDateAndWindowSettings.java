package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.datafeed.dto.PropertyBasicInformationEnhancedBookedStatus;
import com.ideas.tetris.pacman.services.datafeed.dto.PropertyBasicInformationEnhancedLimitedBuildDate;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PropertyInformationEnhancedBookedStatusAndLimitedBuildDateAndWindowSettings extends PropertyBasicInformationEnhancedLimitedBuildDate {
    Integer bdeForecastWindow;
    Integer bdeDecisionWindow;
    Integer idpForecastWindow;
    Integer idpOptimizationWindow;
    Integer groupPricingExtendedWindow;
    Integer variableDecisionWindow;

    public PropertyInformationEnhancedBookedStatusAndLimitedBuildDateAndWindowSettings(PropertyBasicInformationEnhancedBookedStatus propertyBasicInformationEnhancedBookedStatus) {
        super(propertyBasicInformationEnhancedBookedStatus);
    }
}