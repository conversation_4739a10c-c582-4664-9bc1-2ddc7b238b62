package com.ideas.tetris.pacman.services.rollback;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.sas.log.SASNodeLocator;

import javax.inject.Inject;
import java.io.File;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class RollbackHelper {

    private static final String TEMP = "Temp";

    @Autowired
	protected SASNodeLocator sasNodeLocator;

    public File getAnalyticsDatasetDirectory(Integer propertyId) {
        String dataSetPath = sasNodeLocator.determineSasAnalyticsDataSetLocationForJBossNodes(propertyId);
        File dataSetDirectory = new File(dataSetPath);
        if (!dataSetDirectory.exists() || !dataSetDirectory.isDirectory()) {
            throw new TetrisException(ErrorCode.CONFIGURATION_MISSING_EXCEPTION,
                    "ERROR: configured Analytics Data Set Path (" + dataSetPath + ") is not valid");
        }

        return dataSetDirectory;
    }

    public String getAnalyticalDatasetDirectory(Integer propertyId) {
        return SystemConfig.getSasAnalyticsDataSetPath() + propertyId;
    }

    public String getNewFeatureDatasetBackupFolder(Integer propertyId) {
        return SystemConfig.getSASTempFolder() + Constants.NEW_FEATURE_DATASET_BACKUP_FOLDER
                + File.separator + propertyId.toString();
    }

    public String getPropertyAttrDatasetBackupFolder(Integer propertyId) {
        return SystemConfig.getSASTempFolder() + Constants.PROPERTY_ATTR_DATASET_BACKUP_FOLDER
                + File.separator + propertyId.toString();
    }

    public File getDemand360BackupFolder(String clientCode, String propertyCode) {
        return new File(SystemConfig.getG3DataFolder() + TEMP + File.separator + Constants.DEMAND360_TABLES_BACKUP_FOLDER
                + File.separator + clientCode + File.separator + propertyCode);
    }
}
