package com.ideas.tetris.pacman.services.rates;

import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractDetail;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractRate;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.lang.time.DateUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Rate transformation and population has way too much interdependent logic so this is encapsulating the dtos, objects,
 * and other information needed at different points in the population of rates.
 */

public class RateHeaderAndDetail {

    private Integer fileMetadataId;

    private Date caughtUpDate;
    private Date minimumDate;
    private Date maximumDate;

    private AbstractRate header;

    private final Map<String, Object> headerDto;

    private final List<Map<String, Object>> detailDtos;

    public RateHeaderAndDetail(Map<String, Object> headerDto, List<Map<String, Object>> detailDtos) {
        this.headerDto = headerDto;
        this.detailDtos = detailDtos;
    }

    public Integer getFileMetadataId() {
        return fileMetadataId;
    }

    public void setFileMetadataId(Integer fileMetadataId) {
        this.fileMetadataId = fileMetadataId;
    }

    public Date getCaughtUpDate() {
        return caughtUpDate;
    }

    public void setCaughtUpDate(Date caughtUpDate) {
        this.caughtUpDate = caughtUpDate;
        setMaximumDate();
        setMinimumDate();
    }

    public AbstractRate getHeader() {
        return header;
    }

    public void setHeader(AbstractRate header) {
        this.header = header;
    }

    public Map<String, Object> getHeaderDto() {
        return headerDto;
    }

    public List<Map<String, Object>> getDetailDtos() {
        return detailDtos;
    }

    public Date getMinimumDate() {
        return minimumDate;
    }

    private void setMinimumDate() {
        minimumDate = DateUtil.removeTimeFromDate(caughtUpDate);
    }

    public Date getMaximumDate() {
        return maximumDate;
    }

    //TODO this logic should go away
    private void setMaximumDate() {
        maximumDate = DateUtil.removeTimeFromDate(DateUtils.addYears(caughtUpDate, 3));
    }

    public boolean rateHeaderIsOutsideOfAcceptableRange() {
        return header != null && (minimumDate.after(header.getEndDate())
                || maximumDate.before(header.getStartDate()));
    }

    public boolean rateDetailsAreOutsideOfAcceptableRange(AbstractDetail rateDetails) {
        return rateDetails.getEndDate().before(minimumDate)
                || rateDetails.getStartDate().after(maximumDate);
    }
}
