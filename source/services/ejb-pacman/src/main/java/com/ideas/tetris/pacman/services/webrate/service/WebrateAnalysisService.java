package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.rdl.entity.RDLShopAttribute;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DATE_TIME_MILLIS_FORMAT_NGI_DATE;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class WebrateAnalysisService {

    static Logger log = LoggerFactory.getLogger(WebrateAnalysisService.class);
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService crudService;
    @Autowired
	protected DateService dateService;
    @Autowired
	protected PacmanConfigParamsService configService;
    @Autowired
	protected WebrateDataSchedulingService webrateDataSchedulingService;

    public void createErrorLoggerForRDL() {
        try {
            List<RDLShopAttribute> shopAttributes = crudService.findByNamedQuery(RDLShopAttribute.ALL);
            int stalenessThreshold = getStalenessThreshold();
            for (RDLShopAttribute rdlShopAttribute : shopAttributes) {
                addLogger(stalenessThreshold, rdlShopAttribute);
            }
        } catch (ParseException e) {
            log.error("Error parsing last shopped dttm of RDL shop attribute", e);
        }
    }

    public Integer getStalenessThreshold() {
        Integer stalenessThreshold;
        stalenessThreshold = webrateDataSchedulingService.getConfiguredMinThreshold();

        if (stalenessThreshold == 0) {
            stalenessThreshold = configService.getIntegerParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.getParameterName());
        }
        return stalenessThreshold;
    }

    @ForTesting
    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    private void addLogger(Integer stalenessThreshold, RDLShopAttribute rdlShopAttribute) throws ParseException {
        if(isLastShoppedDttmStale(stalenessThreshold, rdlShopAttribute)) {
            String context = String.format("RDL_DATA_IS_STALE :No recent webrate data has been found for %s : %s UPSID : %s for competitor UPS_ID : %s, LOS : %s, max occ : %s and channel %s", PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode(), PacmanWorkContextHelper.getUpsId(), rdlShopAttribute.getWebrateCompetitor().getUpsId(), rdlShopAttribute.getLos(), rdlShopAttribute.getOccupancyNumber(), rdlShopAttribute.getWebrateChannel().getWebrateChannelName());
            log.warn(context);
        }
    }

    public boolean isLastShoppedDttmStale(Integer stalenessThreshold, RDLShopAttribute rdlShopAttribute) throws ParseException {
        String lastShoppedDttmStr = rdlShopAttribute.getLastShoppedDttm();
        if(StringUtils.isBlank(lastShoppedDttmStr)) {
            return false;
        }
        Date lastShoppedDttm = DateUtil.parseDate(lastShoppedDttmStr, DATE_TIME_MILLIS_FORMAT_NGI_DATE);
        return lastShoppedDttm.before(DateUtil.addDaysToDate(dateService.getCaughtUpDate(), -stalenessThreshold));
    }
}
