package com.ideas.tetris.pacman.services.property.dataextraction;


public class DatabaseExtractionInfo {

    protected static final String SCHEDULED_REPORTS = "ScheduledReports";
    protected static final String[] OPERA_TABLES_TO_EXTRACT = {
            "opera.G3_Group_Status_Type_Map",
            "opera.Adjusted_OOO",
            "opera.Stage_Yield_Currency",
            "opera.History_Occupancy_Summary",
            "opera.Raw_Group_Block",
            "opera.Stage_Transaction",
            "opera.Raw_Occupancy_Summary",
            "opera.Stage_Group_Block",
            "opera.Stage_Incoming_Metadata",
            "opera.Stage_Occupancy_Summary",
            "opera.Raw_Group_Master",
            "opera.Filtered_Transaction",
            "opera.Stage_Group_Master",
            "opera.Raw_Yield_Currency",
            "opera.Data_Load_Metadata",
            "opera.Raw_Transaction",
            "opera.Stage_Transaction_Change",
            "opera.Decision_Update_Date_To_External_System",
            "opera.Rate_Category_Code_Mapping",
            "opera.Analytic_Mkt_Accom_Los_Inv",
            "opera.Sharers_AdjustedCnxAndNSSharedTrx",
            "opera.Sharers_AdjustedShareTransactions",
            "opera.History_Group_Block",
            "opera.Sharers_ShareWithFullSharers",
            "opera.Sharers_OnlyCnxOrNoShow",
            "opera.Sharers_OnlySharedTrans",
            "opera.History_Group_Master",
            "opera.Trans_RollUp_NonPace",
            "opera.History_Yield_Currency",
            "opera.History_Transaction",
            "opera.All_Full_Sorted_Sharers",
            "opera.Raw_Incoming_Metadata",
            "opera.History_Incoming_Metadata",
            "opera.Opera_Performance_Monitor",
            "opera.Yield_Category_Rule",
            "opera.G3_Group_Status_Map",
            "opera.G3_Group_Master_Link",
            "opera.G3_Group_Status_Type"};

    protected static final String[] PACE_TABLES_TO_EXTRACT = {
            "Pace_Group_Master",
            "PACE_Ovrbk_Property_Upload",
            "Temp_Webrate_Pace",
            "PACE_FPLOS_By_RoomType",
            "PACE_Mkt_Occupancy_FCST",
            "PACE_Mkt_Activity",
            "PACE_LRV_AT",
            "PACE_LRV",
            "PACE_Webrate",
            "PACE_Total_Activity",
            "PACE_Qualified_FPLOS",
            "PACE_FPLOS_By_Rank",
            "PACE_Bar_Output",
            "PACE_Accom_Occupancy_FCST",
            "PACE_Dailybar_Output",
            "PACE_Accom_Activity",
            "Pace_Decision_LRA_FPLOS",
            "FS_Booking_Pace",
            "Pace_Group_Block",
            "Pace_Decision_LRA_minLOS",
            "PACE_FPLOS_By_Hierarchy",
            "PACE_Manual_Restriction_Property_OVR",
            "PACE_Webrate_Differential",
            "PACE_Manual_Restriction_Accom_OVR",
            "CP_Pace_Decision_Bar_Output",
            "CP_Pace_Decision_Bar_Output_differential",
            "PACE_Ovrbk_Property",
            "PACE_Ovrbk_Accom",
            "PACE_CR_Accom_Activity",
            "PACE_Bar_Output_Upload",
            "PACE_MINLOS",
            "D360_Booking_Summary_Pace",
            "PACE_Ovrbk_Accom_Upload"
    };

    protected static final String[] REVENUE_STREAM_TABLES_TO_EXTRACT = {
            "Revenue_Stream", "Revenue_Stream_Cost", "Revenue_Stream_Detail"};

    protected static final String[] BASE_TABLES_TO_EXTRACT = {
            "PACE_LRV_NOTIFICATION",
            "PACE_Bar_Output_NOTIFICATION",
            "Pace_Ovrbk_Accom_NOTIFICATION",
            "pace_Ovrbk_Property_NOTIFICATION",
            "PACE_Mkt_Occupancy_FCST_NOTIFICATION",
            "PACE_Accom_Occupancy_FCST_NOTIFICATION",
            "property_onbooks_pace_alert",
            "LDB_Build_History",
            "Posting_Rule_Type",
            "Budget_Data",
            "Decision_FPLOS_By_RoomType",
            "Grp_Evl_Fct_Spc_Fct_Room",
            "Mkt_Seg_Details_Proposed",
            "IP_Cfg_Season_Group",
            "ES_Mkt_Seg_Product_Mapping",
            "Accom_Type_Supplement",
            "Decision_Dailybar_Output",
            "OVR_Overbooking_Type",
            "RRA_Source",
            "Group_Evaluation_Request",
            "Grp_Evl_Day_Of_Stay",
            "Mkt_Seg_Details",
            "Agile_Rates_Product_Group",
            "FS_Cfg_Event_Type",
            "Product_Rate_Offset_OVR",
            "Property",
            "Overbooking_Type",
            "FS_Fcst_Eval_Override",
            "Business_Group",
            "RRA_Competitor",
            "Mkt_Seg_Business_Group",
            "Offset_Type",
            "Wash_Ind_Group_Fcst",
            "Accom_Class_Sharing_Group",
            "Product",
            "IP_Cfg_Season_Group_Detail",
            "Net_Value_Type",
            "CR_Accom_Type_Mapping_History",
            "Info_Mgr_Steps",
            "Decision_LRV_AT",
            "RRA_Source_Property",
            "Grp_Evl_Room_Type_Day_Of_Stay",
            "Month",
            "Occupancy_Demand_FCST",
            "Accom_Class",
            "Info_Mgr_Instance_Step_state",
            "IP_Cfg_Task_Category_Type",
            "LDB_Projection",
            "Linked_SRP_Mappings",
            "Temp_CR_Accom_Type_Mapping",
            "File_Metadata",
            "Decision_Ack_Status",
            "MP_Decision_Ack_Status",
            "Heat_Map_Range_and_Colors_Config_Type",
            "CR_Mapping_Room_Numbers_History",
            "RRA_Channel",
            "Decision",
            "los_values",
            "Linked_SRP_Header",
            "Grp_Evl_Arr_DT",
            "Info_Mgr_History_Type",
            "Heat_Map_Range_and_Colors_Config",
            "Tax",
            "FS_Cfg_Mkt_Seg",
            "Grp_Prc_Cfg_Accom_Type",
            "Forecast_Type",
            "Grp_Prc_Cfg_Base_AT",
            "PP_Occupancy_FCST",
            "Linked_SRP_Details",
            "RRA_Score",
            "FS_KPI",
            "Group_Floor_OVR_Constraining_BAR",
            "Heat_Map_Range_and_Colors",
            "Decision_Reason_Type",
            "Grp_Evl_Fct_Spc_Conf_Banq",
            "Rate_Shopping_Adjustment",
            "Info_Mgr_History",
            "Rate_Code_Vendor_Mapping",
            "Benefit_Occupancy_Results",
            "IP_Cfg_Runtime_Param_Name_List",
            "Main_Circuit_Breaker_Overbooking_Stats",
            "LOS_Range_AC",
            "Linked_SRP_Adjustment",
            "STR_Daily",
            "FS_Room_Utilization",
            "IP_Cfg_Process_Group_Type",
            "Group_Final_Forecast_OVR",
            "Property_Special_Event",
            "calendar_dim",
            "FS_OTB",
            "Info_Mgr_Comments",
            "FS_MS_PERFORMANCE",
            "FS_Cfg_Booking_Type",
            "Group_Master",
            "STR_Monthly",
            "ES_Rate_Unqualified",
            "Business_Type",
            "Grp_Prc_Cfg_Conf_Banq",
            "Grp_Evl_Arr_DT_FG",
            "Occ_FCST_Org",
            "Webrate_Override_Competitor",
            "Agile_Product_Restriction_Association",
            "LRA_Restriction_Mapping",
            "Out_Of_Order_Override",
            "CR_Mkt_Accom_Activity",
            "Immediate_Full_Decisions",
            "Info_Mgr_Email_Notification",
            "Webrate_Competitors",
            "Webrate_Channel",
            "FS_Booking",
            "FS_Booking_Sub_Block_Code",
            "CP_Cfg_Base_AT",
            "flyway_schema_history",
            "Linked_SRP_Details_Output",
            "Accom_Class_PriceRank_Path",
            "Overbooking_Accom_Season",
            "Info_Mgr_Excep_Notif_Skip_dates",
            "Decision_LRA_FPLOS",
            "Grp_Evl_Arr_DT_AC",
            "Group_Block",
            "ES_Rate_Unqualified_Details",
            "Manual_Restriction_Type",
            "Webrate_Accom_Type",
            "Analytic_Mkt_Accom_Los_Inv",
            "Ovrd_Process_Group_Config",
            "Overbooking_Accom",
            "FS_Cfg_Price_Tier",
            "Arr_Dep_FCST",
            "Channel_Cost",
            "Sync_Flags",
            "Grp_Evl_Conf_Banq",
            "Webrate_Source_Property",
            "Occupancy_FCST",
            "Occupancy_FCST_NOVR",
            "Occupancy_Demand_FCST_OVR",
            "Info_Mgr_Eval_Execs",
            "Agile_Rates_Package",
            "Grp_Evl_Fct_Spc_Arr_DT",
            "Daily_Briefing",
            "Grp_Evl_Arr_DT_FG_DT",
            "Mkt_Accom_Activity",
            "Ovrd_Noshows",
            "Temp_stage_occupancy_summary_PSAT_CSAT",
            "Webrate_Ranking_Accom_Class",
            "Room_Type_Recoding_Config",
            "reservation_status",
            "IP_Cfg_TS_Class_Config",
            "Reservation_Night",
            "Decision_LRA_minLOS",
            "Channel_Cost_Excluded_Rate",
            "Forecast_Activity_Type",
            "Webrate_Accom_Class_Mapping",
            "CP_Cfg_Offset_AT",
            "Grp_Prc_Cfg_Limit",
            "Decision_Bar_Output_OVR",
            "AMS_Composition_Change",
            "date_list",
            "IP_Cfg_Property_Attribute",
            "Grp_Evl_Arr_DT_AT",
            "Webrate",
            "ES_LongLos_Cfg",
            "Room_Type_Recoding_Config_Mapping",
            "Remote_Task",
            "Decision_Bar_Output",
            "Ovrd_Rate_Forecast",
            "Wash_Property_FCST",
            "Info_Mgr_Job_Lookup",
            "Task_Parameter",
            "Decision_Qualified_FPLOS",
            "FS_Event",
            "ovrd_property_attribute",
            "Manual_Restriction_Property_OVR",
            "Wash_Forecast_Group_FCST_OVR",
            "Product_Package",
            "Business_Accom_FCST",
            "Info_Mgr_Costly_Ooo_Excep_Snooze",
            "Grp_Evl_Fct_Spc_Arr_DT_Day_Part",
            "Module_Type_To_Budget_Level",
            "Ovrd_Reference_Price",
            "Wash_Forecast_Group_FCST",
            "Vendor_Warning_Filter",
            "ES_LongLos_Price_Season",
            "Arrival_Demand_FCST_OVR",
            "Lrv_Drop_Restriction_Cfg",
            "LS_LOS_Group_UI_Mapping",
            "Current_Mkt_Accom_activity",
            "Status",
            "Wash_FCST",
            "D360_MKT_Seg_Detail",
            "Ovrd_Occ_Reference_Price",
            "Mkt_Seg_Process",
            "FS_Cfg_Guest_Room_Category",
            "Linked_SRP_Details_Offset_Seasons",
            "Special_Event_Type",
            "Linked_SRP_Details_Raw",
            "Total_Activity",
            "Grp_Prc_Cfg_MAR_Season",
            "ES_LongLos_Price_MinDiff",
            "ip_cfg_mark_date_data_type",
            "Ovrd_Booking_Curve_Pattern",
            "Manual_Restriction_Accom_OVR",
            "Webrate_Default_Channel",
            "CP_Unqualified_Demand_FCST_Price",
            "Accom_Type_ADR_Final",
            "LDB_Hybrid_Accom_Type",
            "Accom_Type_OverBooking",
            "Webrate_Ranking",
            "Accom_Class_MinDiff",
            "Grp_Evl_Arr_DT_FG_DT_AC",
            "Webrate_Competitors_Class",
            "Accom_Activity",
            "Run_Task_Macros",
            "CDP_Schedule",
            "Fiscal_Calendar",
            "Yield_Type",
            "Max_Allowed_Occupancy_AT",
            "User_Forecast_Data",
            "Webrate_Override_Channel",
            "CostofWalk_Season",
            "Monitor_Process_Scores",
            "Product_Group",
            "ES_LongLos_Price_Cfg",
            "Year",
            "CostofWalk_Default",
            "Unexpected_Demand_Notification_Details",
            "Webrate_Type",
            "Ovrd_Special_Event_Pattern",
            "Property_Special_Event_Instance",
            "IP_Cfg_Process_Group_Fcst_Type",
            "Decision_Upload_Date_To_External_System",
            "Accom_Class_MinDiff_Season",
            "FS_Cfg_Func_Room_Limit",
            "Decision_Ovrbk_Accom_OVR",
            "CP_Decision_Bar_Output",
            "CP_Decision_Bar_NOVR",
            "Webrate_Source",
            "Rate_Qualified",
            "RATE_QUALIFIED_FIXED",
            "CR_Total_Activity",
            "Ip_Cfg_Execution_Position",
            "Decision_Ovrbk_Accom",
            "FS_Booking_Guest_Room",
            "IP_Cfg_Fcst_Task_name_List",
            "time",
            "TetrisRevisionEntity",
            "Main_Circuit_Breaker_Fcst_Calib_Sync_State",
            "Decision_FPLOS_By_Rank",
            "IP_Cfg_Property_Task",
            "Decision_COW_Value_OVR",
            "Hospitality_rooms_config_UI",
            "Info_Mgr_Excep_Notif_Snooze_Params_Values",
            "Frequency",
            "Channel_Cost_Simplified",
            "Channel_Cost_Hierarchical",
            "Rate_Unqualified",
            "Info_Mgr_Sys_Health_Condition",
            "Rate_Qualified_Mkt_Seg",
            "CP_Cfg",
            "Decision_Type",
            "Occupancy_Bucket",
            "Rate_Qualified_Details",
            "RATE_QUALIFIED_FIXED_DETAILS",
            "Analytical_Mkt_Seg",
            "Linked_SRP_Delta",
            "Info_Mgr_Sys_Health_Summary",
            "CP_Decision_Bar_Output_OVR",
            "Benefit_Mkt_Accom_Results",
            "Group_Floor_OVR_Reason",
            "Record_Type",
            "Rate_Qualified_Adjustment",
            "ES_Product_Definition",
            "Group_Floor_OVR",
            "FS_Cfg_Status",
            "analytics_trans",
            "Ams_Occupancy_Summary",
            "Arrival_Demand_FCST",
            "FS_Cfg_Revenue_Type",
            "Mkt_Seg_Master",
            "FS_Cfg_Resource_Type",
            "Grp_Evl_Arr_DT_GR_Rates",
            "Accom_Type_Vendor_Mapping",
            "Inventory_Group",
            "Info_Mgr_Sys_Health_Extended_Details_Name_Lookup",
            "CR_Out_Of_Order",
            "Grp_Prc_Cfg_Min_Profit",
            "Accom_Class_PriceRank_Network_Arrow",
            "Info_Mgr_Excep_Notif_SubType_Level_Mapping",
            "Users",
            "Rate_Unqualified_Accom_Class",
            "Decision_LRV",
            "Info_Mgr_Sys_Health_Details",
            "Product_Rate_Code",
            "Property_Special_Event_Instance_Notes",
            "FS_Cfg_Revenue_Group",
            "BM_Status",
            "Decision_Ovrbk_Property_OVR",
            "Grp_Prc_Cfg_Anc_Stream",
            "GFF_Cfg",
            "Webrate_OVR_Competitor_DTLS",
            "BM_Policy",
            "Decision_Ovrbk_Property",
            "Info_Mgr_Sys_Health_Extended_Details",
            "Grp_Evl_Approval",
            "Grp_Evl_Room_Type",
            "LDB_Clone_Pattern_Source",
            "Rate_Unqualified_Mkt_Seg",
            "BM_Request",
            "Accom_Type",
            "FS_Cfg_Func_Room_Type",
            "FS_Cfg_Func_Room_MAR_Season",
            SCHEDULED_REPORTS,
            "ES_Recommended_Price",
            "Group_Floor_OVR_Alert_Details",
            "Rate_Unqualified_Details",
            "Inventory_Group_Details",
            "Info_Mgr_Excep_Notif_Config",
            "CR_PROBABLE_ACCOM_TYPES",
            "Daily_Bar_Config",
            "Unqualified_Demand_FCST_Price",
            "BM_OVRBK_Benefit",
            "Accom_Class_Inventory_Sharing",
            "Decision_MINLOS",
            "FS_Cfg_Func_Room",
            "CR_Accom_Activity",
            "Grp_Prc_Cfg_Anc_Assgn_Season",
            "DQI_Master",
            "Rate_Unqualified_User_Override",
            "Info_Mgr_Excep_Notif_Level",
            "CP_Cfg_AC",
            "Group_Floor_Accom_Class_Cfg",
            "Decision_Restrict_Highest_Bar",
            "Wash_Ind_Group_Fcst_OVR",
            "Daily_Bar_Rate_Chart",
            "Rate_Unqualified_LOS_Override",
            "BM_Results",
            "Hospitality_rooms_config",
            "Overbooking_Property",
            "Product_AT",
            "Rate_Unqualified_Defaults",
            "FS_Event_Revenue",
            "IP_Cfg_Runtime_Param_Override",
            "Info_Mgr_Excep_Notif_Sub_Level",
            "Overbooking_Property_Season",
            "Hotel_Mkt_Accom_Activity",
            "BM_Results_Busy",
            "Decision_Anomaly_Smoke_Test_Result",
            "Grp_Evl_Arr_DT_User_Adj",
            "Rate_Unqualified_Closed",
            "Group_Floor_MFN_Cfg",
            "Notes",
            "CommandLog",
            "Last_Optimization_Total_Activity",
            "IP_Cfg_Process_Group_Config",
            "Decision_Bar_Output_OVR_Details",
            "Info_Mgr_Excep_Notif_Metric_Type",
            "Mkt_Seg",
            "LDB_Config",
            "Decisions_Delivered",
            "BM_Results_FG",
            "IP_cfg_Process_Group_Stat",
            "Group_Floor_Mkt_Seg_Cfg",
            "Mkt_Seg_Template_Grouping",
            "Webrate_Shopping_Config",
            "Info_Mgr_Excep_Notif_Sub_Type",
            "FS_Cfg_Func_Room_Part",
            "CR_Accom_Type_Mapping",
            "Grp_Prc_Cfg_Anc_Assgn",
            "IP_Cfg_Process_Group_Scope",
            "IP_Cfg_Task_Runtime_Param_Map",
            "CR_Orphan_Mapping",
            "Grp_Evl_Cost",
            "IP_Cfg_Los_Group_Detail",
            "Forecast_Group",
            "Decision_Restrict_Highest_Bar_OVR",
            "Main_Circuit_Breaker_Solds_Rev_Stats",
            "Agile_Rates_DTA_Range",
            "Linked_SRP_Details_LV0",
            "D360_MKT_Hist_Capacity",
            "IP_Cfg_DOW_Group",
            "FS_Cfg_Func_Room_Setup",
            "Pretty_Pricing",
            "IP_Cfg_Horizon_Group_Detail",
            "Product_Hierarchy",
            "CR_Mapping_Room_Numbers",
            "Webrate_Ranking_AC_OVR",
            "CP_Recommended_Floor_Ceiling",
            "Main_Circuit_Breaker_Fcst_Solds_Rev_Stats",
            "GFF_FG_OVR",
            "Last_Optimization_Accom_Activity",
            "Info_Mgr_Excep_Notif_Type_SubType_Mapping",
            "IP_Cfg_Fcst_Group_Ovrd_Detail",
            "FS_Fcst",
            "FS_Booking_Revenue",
            "Main_Circuit_Breaker_Lrv_Stats",
            "Forecast_Group_Proposed",
            "FS_Cfg_Day_Part",
            "Rate_Qualified_Type",
            "Linked_SRP_RT_Threshold_Value",
            "PMS_Migration_Mapping",
            "IP_Cfg_DOW_Group_Detail",
            "Budget_Level",
            "LDB_DOW_Projections",
            "IP_Cfg_Fcst_Group_Ovrd",
            "G3_Group_Status_Type",
            "ES_Competitor_Mapping",
            "Grp_Evl_Anc",
            "Main_Circuit_Breaker_Bar_Stats",
            "Competitor_Price_Change_Notification",
            "Occupancy_Type",
            "G3_Group_Status_Type_Map",
            "Grp_Prc_Cfg",
            "Grp_Evl_Fct_Spc",
            "Info_Mgr_Type",
            "Budget_Config",
            "LDB_Monthly_Projections",
            "LDB_ADR_BY_RC_Projections",
            "IP_Cfg_Los_Group",
            "Product_Rate_Offset",
            "Grp_Evl",
            "FS_Fcst_Override",
            "Last_Optimization_Mkt_Accom_Activity",
            "Forecast_Group_Mkt_Seg_Proposed",
            "Servicing_Cost_Cfg",
            "Channel",
            "IP_Cfg_Process_Group",
            "LDB_Generic_Booking_Curve",
            "ES_Rate_Unqualified_Override",
            "Reservation_Night_Change",
            "Channel_Cost_Cfg",
            "FS_Cfg_Fcst_Level",
            "LDB_Generic_Booking_Curve_Point",
            "IP_Cfg_Mark_Property_Date",
            "Decision_FPLOS_By_Hierarchy",
            "Limit_Total_Rate_Qualified",
            "Info_Mgr_Status",
            "dbmaintain_scripts",
            "Inventory_Sharing_Rank",
            "Process_Status",
            "Info_Mgr_Instance",
            "PMS_Migration_Rate_code_mkt_seg_Mapping",
            "IP_Cfg_Horizon_Group",
            "LDB_Generic_Pattern_Data",
            "Mkt_Seg_Forecast_Group"
    };

    private DatabaseExtractionInfo() throws IllegalAccessException {
        throw new IllegalAccessException("Utility class cannot be instantiated");
    }
}
