package com.ideas.tetris.pacman.services.reports.dataextraction;

import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionForecastGroup;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionReportDto;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionType;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.ReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.DataExtractionReportCriteria;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil.getText;


public class DataExtractionForecastGroupConverter {
    private static List<Object> getForecastGroupHeaderList(Language language, DataExtractionReportCriteria reportCriteria) {
        List<Object> headers = new ArrayList<>();
        if (reportCriteria.isShowLastYearData()) {
            headers.add(getText("common.propertyName", language));
            headers.add(getText("report.dow", language));
            headers.add(getText("occupancyDate", language));
            headers.add(getText("common.comparisonDateLastYear", language));
            headers.add(getText("forecast.group", language));

            if (reportCriteria.isForecastGroupRoomsSold() && reportCriteria.isForecastGroupRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isForecastGroupRoomsSold() && !reportCriteria.isForecastGroupRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
            }

            if (!reportCriteria.isForecastGroupRoomsSold() && reportCriteria.isForecastGroupRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isForecastGroupArrivals()) {
                headers.add(getText("arrivals", language) + " " + getText("common.thisYear", language));
                headers.add(getText("arrivals", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isForecastGroupDepartures()) {
                headers.add(getText("common.departures", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.departures", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isForecastGroupCancellations()) {
                headers.add(getText("cancelled", language) + " " + getText("common.thisYear", language));
                headers.add(getText("cancelled", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isForecastGroupNoShow()) {
                headers.add(getText("noshow", language) + " " + getText("common.thisYear", language));
                headers.add(getText("noshow", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isForecastGroupRevenue() && reportCriteria.isForecastGroupRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isForecastGroupRevenue() && !reportCriteria.isForecastGroupRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
            }

            if (!reportCriteria.isForecastGroupRevenue() && reportCriteria.isForecastGroupRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isForecastGroupOccupancyForecast()) {
                headers.add(getText("common.occupancyForecast", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.occupancyForecast", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isForecastGroupSystemUnconstrainedDemand()) {
                headers.add(getText("systemUnconstrainedDemand", language) + " " + getText("common.thisYear", language));
                headers.add(getText("systemUnconstrainedDemand", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isForecastGroupUserUnconstrainedDemand()) {
                headers.add(getText("report.column.userDemand", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.userDemand", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isForecastGroupSystemGroupWashPerFG()) {
                headers.add(getText("common.systemWashPercentage", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.systemWashPercentage", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("common.userWashPercentage", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.userWashPercentage", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isForecastGroupADR()) {
                headers.add(getText("report.column.bookedAdr", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedAdr", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.forecastedAdr", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedAdr", language) + " " + getText("common.lastYearActual", language));
            }

        } else {
            headers.add(getText("common.propertyName", language));
            headers.add(getText("report.dow", language));
            headers.add(getText("occupancyDate", language));
            headers.add(getText("forecast.group", language));

            if (reportCriteria.isForecastGroupRoomsSold() && reportCriteria.isForecastGroupRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isForecastGroupRoomsSold() && !reportCriteria.isForecastGroupRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
            }

            if (!reportCriteria.isForecastGroupRoomsSold() && reportCriteria.isForecastGroupRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isForecastGroupArrivals()) {
                headers.add(getText("arrivals", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isForecastGroupDepartures()) {
                headers.add(getText("common.departures", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isForecastGroupCancellations()) {
                headers.add(getText("cancelled", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isForecastGroupNoShow()) {
                headers.add(getText("noshow", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isForecastGroupRevenue() && reportCriteria.isForecastGroupRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isForecastGroupRevenue() && !reportCriteria.isForecastGroupRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
            }

            if (!reportCriteria.isForecastGroupRevenue() && reportCriteria.isForecastGroupRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isForecastGroupOccupancyForecast()) {
                headers.add(getText("common.occupancyForecast", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isForecastGroupSystemUnconstrainedDemand()) {
                headers.add(getText("systemUnconstrainedDemand", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isForecastGroupUserUnconstrainedDemand()) {
                headers.add(getText("report.column.userDemand", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isForecastGroupSystemGroupWashPerFG()) {
                headers.add(getText("common.systemWashPercentage", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.userWashPercentage", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isForecastGroupADR()) {
                headers.add(getText("report.column.bookedAdr", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedAdr", language) + " " + getText("common.thisYear", language));
            }

        }

        return headers;
    }

    public static ReportSheet getForecastGroupReportSheet(Map<DataExtractionType, List<DataExtractionReportDto>> records, ScheduledReport<DataExtractionReportCriteria> scheduledReport) {

        Language language = scheduledReport.getLanguage();
        DecimalFormat decimalFormat = ScheduledReportUtils.getLocaleDecimalFormat(language.getLocale());
        ReportSheet forecastGroupSheet = new ReportSheet(getText("forecast.group", scheduledReport.getLanguage()));
        forecastGroupSheet.setReportTitle(getText("dataExtractionReport.title.at.forecast.group.level", scheduledReport.getLanguage()));
        List<DataExtractionReportDto> dataExtractionReportDtoList = records.get(DataExtractionType.FORECAST_GROUP);
        Object[] headerArray = getForecastGroupHeaderList(scheduledReport.getLanguage(), scheduledReport.getReportCriteria()).toArray();
        DataExtractionReportCriteria reportCriteria = scheduledReport.getReportCriteria();
        for (int i = 0; i < headerArray.length; i++) {
            forecastGroupSheet.addColumn(String.class);
        }
        forecastGroupSheet.addHeaderRow(headerArray);
        dataExtractionReportDtoList.forEach(dto -> {
            List<Object> dataList = new ArrayList<Object>();
            DataExtractionForecastGroup data = (DataExtractionForecastGroup) dto;

            if (reportCriteria.isShowLastYearData()) {
                dataList.add(DataExtractionReportUtil.getStringValue(data.getPropertyName())); //  Property Name
                if (data.getDayOfWeek() != null && !data.getDayOfWeek().isEmpty()) {
                    dataList.add(getText(DataExtractionReportUtil.getStringValue(data.getDayOfWeek()).toLowerCase(), language)); // Day of Week
                } else {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getDayOfWeek())); // Day of Week
                }
                dataList.add(ScheduledReportUtils.getDateString(data.getOccupancyDate())); //  Occupancy Date
                dataList.add(ScheduledReportUtils.getDateString(data.getComparisonDateLastYear())); //  Comparison Date Last Year
                dataList.add(DataExtractionReportUtil.getStringValue(data.getForecastGroupName())); //  Forecast Group

                if (reportCriteria.isForecastGroupRoomsSold() && reportCriteria.isForecastGroupRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); //  Occupancy On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }
                if (reportCriteria.isForecastGroupRoomsSold() && !reportCriteria.isForecastGroupRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); //  Occupancy On Books Last Year Actual
                }
                if (!reportCriteria.isForecastGroupRoomsSold() && reportCriteria.isForecastGroupRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); //  Occupancy On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }

                if (reportCriteria.isForecastGroupArrivals()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsThisYear())); //  Arrivals This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsLastYear())); //  Arrivals Last Year Actual
                }

                if (reportCriteria.isForecastGroupDepartures()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesThisYear())); //  Departures This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesLastYear())); //  Departures Last Year Actual
                }

                if (reportCriteria.isForecastGroupCancellations()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledThisYear())); //  Cancelled This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledLastYear())); //  Cancelled Last Year Actual
                }

                if (reportCriteria.isForecastGroupNoShow()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowThisYear())); //  No Show This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowLastYear())); //  No Show Last Year Actual
                }

                if (reportCriteria.isForecastGroupRevenue() && reportCriteria.isForecastGroupRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); //  Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueLastYear(), decimalFormat)); //  Forecasted Room Revenue Last Year Actual
                }
                if (reportCriteria.isForecastGroupRevenue() && !reportCriteria.isForecastGroupRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); //  Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueLastYear(), decimalFormat)); //  Forecasted Room Revenue Last Year Actual
                }
                if (!reportCriteria.isForecastGroupRevenue() && reportCriteria.isForecastGroupRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); //  Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                }

                if (reportCriteria.isForecastGroupOccupancyForecast()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOccupancyForecastThisYear(), decimalFormat)); //  Occupancy Forecast This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOccupancyForecastLastYear(), decimalFormat)); //  Occupancy Forecast Last Year Actual
                }

                if (reportCriteria.isForecastGroupSystemUnconstrainedDemand()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getSystemDemandThisYear(), decimalFormat)); //  System Unconstrained Demand This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getSystemDemandLastYear(), decimalFormat)); //  System Unconstrained Demand Last Year Actual
                }

                if (reportCriteria.isForecastGroupUserUnconstrainedDemand()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserDemandThisYear(), decimalFormat)); //  User Demand This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserDemandLastYear(), decimalFormat)); //  User Demand Last Year Actual
                }

                if (reportCriteria.isForecastGroupSystemGroupWashPerFG()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getSystemWashThisYear(), decimalFormat)); //  System Wash % This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getSystemWashLastYear(), decimalFormat)); //  System Wash % Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserWashThisYear(), decimalFormat)); //  User Wash % This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserWashLastYear(), decimalFormat)); //  User Wash % Last Year Actual
                }

                if (reportCriteria.isForecastGroupADR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRThisYear(), decimalFormat)); //  ADR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRLastYear(), decimalFormat)); //  ADR On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrThisYear(), decimalFormat)); //  ADR Forecast This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrLastYear(), decimalFormat)); //  ADR Forecast Last Year Actual
                }

            } else {

                dataList.add(DataExtractionReportUtil.getStringValue(data.getPropertyName())); //  Property Name
                if (data.getDayOfWeek() != null && !data.getDayOfWeek().isEmpty()) {
                    dataList.add(getText(DataExtractionReportUtil.getStringValue(data.getDayOfWeek()).toLowerCase(), language)); // Day of Week
                } else {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getDayOfWeek())); // Day of Week
                }
                dataList.add(ScheduledReportUtils.getDateString(data.getOccupancyDate())); //  Occupancy Date
                dataList.add(DataExtractionReportUtil.getStringValue(data.getForecastGroupName())); //  Forecast Group

                if (reportCriteria.isForecastGroupRoomsSold() && reportCriteria.isForecastGroupRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }
                if (reportCriteria.isForecastGroupRoomsSold() && !reportCriteria.isForecastGroupRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                }
                if (!reportCriteria.isForecastGroupRoomsSold() && reportCriteria.isForecastGroupRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }

                if (reportCriteria.isForecastGroupArrivals()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsThisYear())); //  Arrivals This Year
                }

                if (reportCriteria.isForecastGroupDepartures()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesThisYear())); //  Departures This Year
                }

                if (reportCriteria.isForecastGroupCancellations()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledThisYear())); //  Cancelled This Year
                }

                if (reportCriteria.isForecastGroupNoShow()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowThisYear())); //  No Show This Year
                }

                if (reportCriteria.isForecastGroupRevenue() && reportCriteria.isForecastGroupRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                }
                if (reportCriteria.isForecastGroupRevenue() && !reportCriteria.isForecastGroupRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                }
                if (!reportCriteria.isForecastGroupRevenue() && reportCriteria.isForecastGroupRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                }

                if (reportCriteria.isForecastGroupOccupancyForecast()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOccupancyForecastThisYear(), decimalFormat)); //  Occupancy Forecast This Year
                }

                if (reportCriteria.isForecastGroupSystemUnconstrainedDemand()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getSystemDemandThisYear(), decimalFormat)); //  System Unconstrained Demand This Year
                }

                if (reportCriteria.isForecastGroupUserUnconstrainedDemand()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserDemandThisYear(), decimalFormat)); //  User Demand This Year
                }

                if (reportCriteria.isForecastGroupSystemGroupWashPerFG()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getSystemWashThisYear(), decimalFormat)); //  System Wash % This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserWashThisYear(), decimalFormat)); //  User Wash % This Year

                }

                if (reportCriteria.isForecastGroupADR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRThisYear(), decimalFormat)); //  ADR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrThisYear(), decimalFormat)); //  ADR Forecast This Year
                }

            }

            forecastGroupSheet.addRow(dataList.toArray());
        });
        return forecastGroupSheet;

    }
}
