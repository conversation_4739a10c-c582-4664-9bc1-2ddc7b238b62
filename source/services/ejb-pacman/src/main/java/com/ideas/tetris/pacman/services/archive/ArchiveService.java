package com.ideas.tetris.pacman.services.archive;

import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.extract.CrsFileUtil;
import com.ideas.tetris.pacman.services.property.PropertyConfigParamService;
import com.ideas.tetris.pacman.services.s3.S3Operations;
import com.ideas.tetris.pacman.services.s3.dto.S3Inputs;
import com.ideas.tetris.pacman.services.virtualproperty.service.VirtualPropertyMappingService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileFilter;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig.getNumberOfMonthsToLookupInS3;
import static com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig.getS3ArchiveBucketName;

@Component
@Transactional
public class ArchiveService {

    private final SimpleDateFormat archiveMonthFolderFormat = new SimpleDateFormat("yyyy_MM");
    @Autowired
    S3Operations s3Operations;

    @Autowired
    VirtualPropertyMappingService virtualPropertyMappingService;

    @Autowired
    PropertyConfigParamService propertyConfigParamService;

    @Autowired
    DateService dateService;

    private static final Logger LOGGER = Logger.getLogger(ArchiveService.class.getName());


    public File[] listFilesFromCloud(String folderPath, FileFilter fileFilter, boolean useS3RefreshableConnection) {
        return CrsFileUtil.applyFilter(s3Operations.listFiles(S3Inputs.builder()
                .key(folderPath)
                .bucketName(getS3ArchiveBucketName())
                .isS3ConnectionRefreshable(useS3RefreshableConnection)
                .build()), fileFilter).toArray(File[]::new);
    }

    public File downloadExtract(String extractPath, boolean useS3RefreshableConnection) {
        File extract = new File(extractPath);
        if (!extract.getParentFile().exists()) {
            extract.getParentFile().mkdirs();
        }
        return s3Operations.download(S3Inputs.builder()
                .key(extract.getAbsolutePath())
                .bucketName(getS3ArchiveBucketName())
                .downloadToPath(extract.getAbsolutePath())
                .isS3ConnectionRefreshable(useS3RefreshableConnection)
                .build());

    }

    public File[] getLastNMonthsPath(String rootDir, Integer propertyId) {
        int numberOfMonthsToLookUp = getNumberOfMonthsToLookupInS3();
        File[] lastNFolderMonths = new File[numberOfMonthsToLookUp];
        WorkContextType workContext = PacmanThreadLocalContextHolder.getWorkContext();
        if (workContext == null || workContext.getPropertyId() == null) { // This condition occurs in CrsExtract job where context is not build
            return new File[0];
        }
        Date caughtUpDate;
        try {
            caughtUpDate = dateService.getCaughtUpDate(propertyId);
        } catch (Exception e) {
            LOGGER.info("No DB found for property:- " + propertyId + ", returning current date to query Cloud", e);
            caughtUpDate = dateService.getCurrentDate();
        }
        for (int i = 0; i < numberOfMonthsToLookUp; i++) {
            lastNFolderMonths[i] = new File(rootDir
                    + File.separator
                    + archiveMonthFolderFormat.format(DateUtil.addMonthsToDate(caughtUpDate, -i)));
        }
        return lastNFolderMonths;
    }
}
