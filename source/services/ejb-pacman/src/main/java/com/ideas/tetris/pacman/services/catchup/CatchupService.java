package com.ideas.tetris.pacman.services.catchup;

import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.ngi.NGICatchupService;
import com.ideas.tetris.pacman.services.opera.OperaCatchupService;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService;
import com.ideas.tetris.pacman.services.property.ExtractDetailsServiceLocal;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.property.dto.ExtractDetails;
import com.ideas.tetris.pacman.services.property.dto.WebRateExtractDetails;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import com.ideas.tetris.platform.common.job.CatchupMode;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.HILTON_STREAMING_POPULATION_ENABLED;
import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.isHiltonClientCode;
import static com.ideas.tetris.platform.common.externalsystem.ReservationSystem.isHilstarOrPCRS;

@Component
@Transactional
@Slf4j
public class CatchupService {

    @Autowired
	protected PropertyService propertyService;
    @Autowired
	protected ExtractDetailsServiceLocal extractDetailsService;
    @Autowired
	protected OperaCatchupService operaCatchupService;
    @Autowired
	protected NGICatchupService ngiCatchupService;
    @Autowired
	protected CatchupHelper catchupHelper;
    @Autowired
	protected JobServiceLocal jobService;
    @Autowired
    private PMSMigrationService pmsMigrationService;
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;

    public boolean isEligibleForCatchup(Integer propertyId) {
        Property property = propertyService.getPropertyById(propertyId);
        ExtractDetails extractDetails = extractDetailsService.getExtractDetails(propertyId);
        boolean activityCondition = extractDetails != null && extractDetails.getNumberOfIncomingExtracts() > 0;

        return catchupHelper.isProperStage(property)
                && catchupHelper.hasActivityOrWebRates(property, activityCondition);

    }

    public CatchupParameters getCatchupParameters(Integer propertyId) {
        ReservationSystem reservationSystem = propertyService.getReservationSystem(propertyId);
        return getCatchupParameters(propertyId, reservationSystem);
    }

    public boolean isHiltonStreamingPopulationEnabled(Integer propertyId) {
        final Property property = propertyService.getPropertyById(propertyId);
        return pacmanConfigParamsService.getBooleanParameterValue(
                HILTON_STREAMING_POPULATION_ENABLED.getParameterName(),
                property.getClient().getCode(), property.getCode());
    }

    private CatchupParameters getCatchupParameters(Integer propertyId, ReservationSystem reservationSystem) {
        LocalDate firstActivityDate = null;
        LocalDate lastActivityDate = null;
        if (pmsMigrationService.isHybridCatchupEnabledForMigratedProperty(propertyId)) {
            firstActivityDate = convert(operaCatchupService.getFirstPossibleCatchupDate(propertyId));
            if (firstActivityDate == null) {
                firstActivityDate = ngiCatchupService.getCatchupStartDate(propertyId);
            }
            lastActivityDate = ngiCatchupService.getCatchupEndDate(propertyId);
        } else if (reservationSystem.equals(ReservationSystem.OPERA)) {
            firstActivityDate = convert(operaCatchupService.getFirstPossibleCatchupDate(propertyId));
            lastActivityDate = convert(operaCatchupService.getLastPossibleCatchupDate(propertyId));
        } else if (reservationSystem.equals(ReservationSystem.NGI)) {
            firstActivityDate = ngiCatchupService.getCatchupStartDate(propertyId);
            lastActivityDate = ngiCatchupService.getCatchupEndDate(propertyId);
        } else if (isHilstarOrPCRS(reservationSystem) && isHiltonStreamingPopulationEnabled(propertyId)) {
            firstActivityDate = ngiCatchupService.getCatchupStartDate(propertyId);
            lastActivityDate = ngiCatchupService.getCatchupEndDate(propertyId);
        } else {
            ExtractDetails extractDetails = extractDetailsService.rebuildExtractDetails(propertyId);
            firstActivityDate = convert(extractDetails.getFirstIncomingExtractDate());
            lastActivityDate = convert(extractDetails.getLastIncomingExtractDate());
        }
        CatchupParameters parameters = new CatchupParameters();
        WebRateExtractDetails webRateExtractDetails = extractDetailsService.rebuildWebRateExtractDetails(propertyId);
        LocalDate firstRateShoppingDate = convert(webRateExtractDetails.getFirstIncomingExtractDate());
        LocalDate lastRateShoppingDate = convert(webRateExtractDetails.getLastIncomingExtractDate());
        parameters.setRateShoppingFirstDate(firstRateShoppingDate);
        parameters.setRateShoppingLastDate(lastRateShoppingDate);
        parameters.setActivityFirstDate(firstActivityDate);
        parameters.setActivityLastDate(lastActivityDate);
        LocalDate startDate = null;
        LocalDate endDate = null;
        if (firstActivityDate != null) {
            parameters.setCatchupMode(CatchupMode.ACTIVITY_ONLY);
            startDate = firstActivityDate;
            endDate = lastActivityDate;
        } else {
            parameters.setCatchupMode(CatchupMode.RATE_SHOPPING_ONLY);
            startDate = firstRateShoppingDate;
            endDate = lastRateShoppingDate;
        }
        parameters.setEndDate(endDate);
        parameters.setStartDate(startDate);
        return parameters;
    }

    private LocalDate convert(DateParameter input) {
        if (input == null) {
            return null;
        }
        return LocalDate.fromDateFields(input.getTime());
    }

    private LocalDate convert(Date input) {
        if (input == null) {
            return null;
        }
        return LocalDate.fromDateFields(input);
    }

    public void catchup(Integer propertyId, CatchupParameters parameters) {
        ReservationSystem reservationSystem = propertyService.getReservationSystem(propertyId);
        if (pmsMigrationService.isHybridCatchupEnabledForMigratedProperty(propertyId)) {
            pmsMigrationService.startCatchupJob(propertyId, parameters);
        } else if (reservationSystem.equals(ReservationSystem.OPERA)) {
            operaCatchupService.catchup(propertyId, parameters.getStartDate(), parameters.getEndDate(), parameters.getCatchupMode().toString());
        } else if (reservationSystem.equals(ReservationSystem.NGI)) {
            ngiCatchupService.catchup(propertyId, parameters);
        } else if (isHiltonClientCode() && isHiltonStreamingPopulationEnabled(propertyId)) {
            ngiCatchupService.catchup(propertyId, parameters);
        }
        else {
            log.warn("Catchup job is not started");
        }
    }
}
