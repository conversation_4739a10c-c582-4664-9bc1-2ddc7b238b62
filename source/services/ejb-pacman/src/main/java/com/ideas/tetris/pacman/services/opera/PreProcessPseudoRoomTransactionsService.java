package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.componentrooms.entity.ProbableAccomTypesCR;
import com.ideas.tetris.pacman.services.componentrooms.services.ComponentRoomService;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dataload.DataLoadService;
import com.ideas.tetris.pacman.services.opera.predicate.ReservationIDPredicate;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.CONTEXT_KEY;

/**
 * Created by idnpak on 11/6/2014.
 */
@Component
@Transactional
public class PreProcessPseudoRoomTransactionsService {
    private static final Logger LOGGER = Logger.getLogger(PreProcessPseudoRoomTransactionsService.class.getName());

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;
    @Autowired
	protected PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
	protected ComponentRoomService componentRoomService;
    @Autowired
	protected DataLoadService dataLoadService;
    @Autowired
	protected OperaFilterStageDataService operaFilterStageDataService;

    public int preProcessUnwantedRoomTransactions(String correlationId) {
        LOGGER.info("Processing reservations with unwanted rooms for feed : " + correlationId);
        int numberOfRowsUpdated = 0;
        boolean isIncludeList = true;
        List<String> roomList = getIncludeRoomList();
        if (roomList.isEmpty()) {
            roomList = getPseudoRoomList();
            isIncludeList = false;
        }
        if (!roomList.isEmpty()) {
            List<OperaTransaction> transRecList = findAnyReservationsSpiltByUnwantedRooms(roomList, isIncludeList);
            HashMap<Integer, LinkedList<OperaTransaction>> fullMap = createPerResIdTransList(transRecList);
            LinkedList<OperaTransaction> correctedTransactions = getCorrectedTransaction(isIncludeList, roomList, fullMap);
            numberOfRowsUpdated += updateResvDataWithCorrectedUnwantedRoomSplit(correctedTransactions);
            crudService.flush();
        }
        LOGGER.info("Completed processing reservations with unwanted rooms for feed : " + correlationId + " updated rows : " + numberOfRowsUpdated);
        return numberOfRowsUpdated;
    }

    protected LinkedList<OperaTransaction> getCorrectedTransaction(boolean isIncludeList, List<String> roomList, HashMap<Integer, LinkedList<OperaTransaction>> fullMap) {
        LinkedList<OperaTransaction> correctedTransactions;
        if (!isIncludeList && isIncludePseudoInRevenueEnabled()) {
            correctedTransactions = correctPseudoRoomTransactionsByAllowingStartingAndEnding(fullMap, roomList, isIncludeList);
        } else {
            HashMap<Integer, LinkedList<OperaTransaction>> correctedTransEndingWithUnwantedRoom = correctTransactionsEndingWithUnwantedRoom(fullMap, roomList, isIncludeList);
            correctedTransactions = correctTransactionsStartingWithUnwantedRoomAndCreatingSplit(
                    correctedTransEndingWithUnwantedRoom, roomList, isIncludeList);
        }
        return correctedTransactions;
    }

    public String updatePseudoRoomList(String correlationId) {
        boolean hasCRLicense = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED);
        List<String> includRoomTypeList = getIncludeRoomList();
        if (!includRoomTypeList.isEmpty()) {
            if (hasCRLicense) {
                List<String> zeroCapacityRooms = dataLoadService.getAllRoomTypesWithZeroCapacity(correlationId, includRoomTypeList);
                populateProbableCRs(zeroCapacityRooms);
            }
            return "include room list provided, therefore pseudo room list not updated";
        }
        List<String> updatedPseudoRooms = new ArrayList<String>();
        List<String> origPseudoRooms = getPseudoRoomList();
        List<String> allRoomTypesWithCapacity = dataLoadService.getAllRoomTypesWithCapacity(correlationId);
        for (String roomType : origPseudoRooms) {
            if (!allRoomTypesWithCapacity.contains(roomType)) {
                updatedPseudoRooms.add(roomType);
            }
        }
        Set<String> allRoomTypesInFeed = dataLoadService.getAllRoomTypesInFeed(correlationId);
        for (String roomType : allRoomTypesInFeed) {
            if (!StringUtils.isBlank(roomType) && !origPseudoRooms.contains(roomType) && !allRoomTypesWithCapacity.contains(roomType)) {
                if (!isRoomTypeInPacman(roomType)) {
                    updatedPseudoRooms.add(roomType);
                }
            }
        }

        filterZeroCapacityRooms(updatedPseudoRooms);

        if (hasCRLicense) {
            populateProbableCRs(updatedPseudoRooms);
        }
        if (!areListEquivalent(origPseudoRooms, updatedPseudoRooms)) {
            savePseudoRoomList(updatedPseudoRooms);
        }
        return buildResults(origPseudoRooms, updatedPseudoRooms);
    }

    private void filterZeroCapacityRooms(List<String> updatedPseudoRooms) {
        List<String> zeroCapacityRoomsTypesToInclude = getZeroCapacityRoomsTypesToInclude();
        updatedPseudoRooms.removeAll(zeroCapacityRoomsTypesToInclude);
    }

    public List<ProbableAccomTypesCR> populateProbableCRs(List<String> roomTypes) {
        List<ProbableAccomTypesCR> updatedProbableAccomTypesCRList = new ArrayList<>();
        List<String> crList = new ArrayList<>();
        List<ProbableAccomTypesCR> probableAccomTypesCRList = componentRoomService.getProbableComponentRooms();
        if (probableAccomTypesCRList == null) {
            probableAccomTypesCRList = new ArrayList<>();
        }
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY);
        int propertyId = workContext.getPropertyId();

        for (String roomType : roomTypes) {
            boolean modified = false;
            ProbableAccomTypesCR pact = checkIfAlreadyExistsInProbableCRs(roomType, probableAccomTypesCRList);
            // Does not exists already. create new entry.
            if (pact == null) {
                pact = new ProbableAccomTypesCR();
                pact.setPropertyId(propertyId);
                pact.setAccomTypeCode(roomType);
                pact.setConfiguredAsCr(null);
                modified = true;
            } else if ("true".equalsIgnoreCase(pact.getConfiguredAsCr())) {
                crList.add(pact.getAccomTypeCode());
            }
            if (modified) {
                updatedProbableAccomTypesCRList.add(pact);
            }
        }
        roomTypes.removeAll(crList);
        if (!updatedProbableAccomTypesCRList.isEmpty()) {
            crudService.save(updatedProbableAccomTypesCRList);
        }
        return updatedProbableAccomTypesCRList;
    }

    private ProbableAccomTypesCR checkIfAlreadyExistsInProbableCRs(String updatedPseudoRoom, List<ProbableAccomTypesCR> probableAccomTypesCRList) {
        for (ProbableAccomTypesCR pact : probableAccomTypesCRList) {
            if (pact.getAccomTypeCode().equalsIgnoreCase(updatedPseudoRoom)) {
                return pact;
            }
        }
        return null;
    }

    // lists may be ordered differently
    private boolean areListEquivalent(List<String> origList, List<String> updatedList) {
        if (origList.size() != updatedList.size()) {
            return false;
        }
        for (String data : origList) {
            if (!updatedList.contains(data)) {
                return false;
            }
        }
        return true;
    }

    private String buildResults(List<String> origPseudoRooms, List<String> updatedPseudoRooms) {
        StringBuilder buffer = new StringBuilder();
        buffer.append("orig: ");
        for (String roomType : origPseudoRooms) {
            buffer.append(roomType).append(" ");
        }
        buffer.append("  ; updated: ");
        for (String roomType : updatedPseudoRooms) {
            buffer.append(roomType).append(" ");
        }
        return buffer.toString();
    }

    protected boolean isRoomTypeInPacman(String roomType) {
        AccomType accomType = crudService.findByNamedQuerySingleResult(AccomType.BY_CODE_AND_STATUS_NOT_IN, QueryParameter.with("code", roomType).and("statusId", 6).parameters());
        return accomType != null;
    }


    public List<OperaTransaction> findAnyReservationsSpiltByUnwantedRooms(List<String> roomList, boolean isIncludeList) {
        List<Integer> resIdList = findReservationIdsWithUnwantedRoomSplit(roomList, isIncludeList);
        return findFullReservationsWithUnwantedRoomSplit(resIdList);
    }

    private List<OperaTransaction> findFullReservationsWithUnwantedRoomSplit(List<Integer> resIdList) {
        List<OperaTransaction> transRecList = new ArrayList<OperaTransaction>();
        if (!resIdList.isEmpty()) {
            LOGGER.info("There are : " + resIdList.size() + " reservations split by RT or MS change");
            List<List<Integer>> resIdSubList = createSubListsOfReservationIds(resIdList);
            for (List<Integer> aResIdSubList : resIdSubList) {
                transRecList.addAll(fetchAllTransactionsInBatch(aResIdSubList));
            }
        } else {
            LOGGER.info("There is no reservation split by RT or MS change");
        }
        return transRecList;
    }

    public List<List<Integer>> createSubListsOfReservationIds(List<Integer> resIdList) {
        List<List<Integer>> resIdSubList = new ArrayList<List<Integer>>();
        int totalResIds = resIdList.size();
        int startPosition = 0;
        int endPosition = 0;
        int batchSize = 1500;
        int numberOfBatches = totalResIds / batchSize;
        for (int counter = 1; counter <= numberOfBatches; counter++) {
            startPosition = endPosition;
            endPosition = endPosition + batchSize;
            resIdSubList.add(createTransactionIdSubLists(resIdList, startPosition, endPosition));
        }
        if (endPosition < totalResIds) {
            resIdSubList.add(createTransactionIdSubLists(resIdList, endPosition, totalResIds));
        }
        return resIdSubList;
    }

    private List<Integer> createTransactionIdSubLists(List<Integer> resIdList, int startPosition, int endPosition) {
        LOGGER.info(" startPosition : " + startPosition + " endPosition: " + endPosition);
        List<Integer> resIdSubList = resIdList.subList(startPosition, endPosition);
        LOGGER.info("resIdSubList.size() : " + resIdSubList.size());
        return resIdSubList;
    }

    @SuppressWarnings("unchecked")
    private List<OperaTransaction> fetchAllTransactionsInBatch(List<Integer> resIdSubList) {
        return crudService.findByNamedQuery(OperaTransaction.FIND_BY_RESID_NO_POST_DEPARTURES
                , QueryParameter.with("resIdList", resIdSubList).parameters());
    }

    @SuppressWarnings("unchecked")
    private List<Integer> findReservationIdsWithUnwantedRoomSplit(List<String> roomList, boolean isIncludeList) {
        String criteria = isIncludeList ? " not in " : " in ";
        String findAnyReservationsSpiltByUnwantedRoomsQuery = new StringBuilder(" select distinct Reservation_Name_ID from")
                .append("   (select distinct ot1.room_type as  room_type ,ot1.Reservation_Name_ID as Reservation_Name_ID")
                .append("       from opera.Stage_Transaction ot1")
                .append("       where ot1.Reservation_Name_ID in")
                .append("           (select distinct ot2.Reservation_Name_ID")
                .append("               from opera.Stage_Transaction ot2")
                .append("               where ot2.Room_Type ").append(criteria).append(" (:roomList)")
                .append("            )")
                .append("    ) AS A ")
                .append("   group by Reservation_Name_ID")
                .append("   having COUNT(room_type) > 1")
                .toString();
        return crudService.findByNativeQuery(findAnyReservationsSpiltByUnwantedRoomsQuery
                , QueryParameter.with("roomList", roomList).parameters());
    }

    @SuppressWarnings("unchecked")
	public
    HashMap<Integer, LinkedList<OperaTransaction>> createPerResIdTransList(List<OperaTransaction> transRecList) {
        HashMap<Integer, LinkedList<OperaTransaction>> fullMap = new HashMap<Integer, LinkedList<OperaTransaction>>();
        while (!transRecList.isEmpty()) {
            OperaTransaction trans = transRecList.get(0);
            int resID = trans.getReservationNameId();
            ReservationIDPredicate reservationIDPredicate = new ReservationIDPredicate(resID);
            LinkedList<OperaTransaction> selectList = new LinkedList<OperaTransaction>();
            CollectionUtils.select(transRecList, reservationIDPredicate, selectList);
            fullMap.put(resID, selectList);
            transRecList = (List<OperaTransaction>) CollectionUtils.selectRejected(transRecList, reservationIDPredicate);
        }
        return fullMap;
    }

    public HashMap<Integer, LinkedList<OperaTransaction>> correctTransactionsEndingWithUnwantedRoom(
            HashMap<Integer, LinkedList<OperaTransaction>> fullMap, List<String> roomList, boolean isIncludeList) {
        LinkedList<OperaTransaction> transListToUpdate;
        HashMap<Integer, LinkedList<OperaTransaction>> correctedMap = new HashMap<Integer, LinkedList<OperaTransaction>>();
        for (Map.Entry<Integer, LinkedList<OperaTransaction>> transaction : fullMap.entrySet()) {
            LinkedList<OperaTransaction> perResIdList = transaction.getValue();
            Integer resId = transaction.getKey();
            transListToUpdate = adjustTransactionsEndingWithUnwantedRoom(perResIdList, roomList, isIncludeList);
            correctedMap.put(resId, transListToUpdate);
        }
        return correctedMap;
    }

    public boolean isIncludePseudoInRevenueEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INCLUDE_PSEUDO_IN_REVENUE);
    }

    public LinkedList<OperaTransaction> correctTransactionsStartingWithUnwantedRoomAndCreatingSplit(
            HashMap<Integer, LinkedList<OperaTransaction>> correctedTransEndingWithUnwantedRoom, List<String> roomList, boolean isIncludeList) {

        // Since Collections cannot be modified while iterating, we need these lists to maintain updated reservation objects
        LinkedList<OperaTransaction> finalCorrectedReservations = new LinkedList<OperaTransaction>();
        correctedTransEndingWithUnwantedRoom.entrySet().stream().forEach(transaction -> {
            LinkedList<OperaTransaction> perResIdList = transaction.getValue();
            finalCorrectedReservations.addAll(
                    adjustTransactionsStartingWithUnwantedRoomAndCreatingSplit(perResIdList, roomList, isIncludeList)
            );
        });
        return finalCorrectedReservations;
    }

    public int updateResvDataWithCorrectedUnwantedRoomSplit(LinkedList<OperaTransaction> finalList) {
        int numOfRecordUpdated = 0;
        for (OperaTransaction operaTransaction : finalList) {
            crudService.save(operaTransaction);
            numOfRecordUpdated++;
        }
        return numOfRecordUpdated;
    }

    private LinkedList<OperaTransaction> adjustTransactionsEndingWithUnwantedRoom(LinkedList<OperaTransaction> perResIdList
            , List<String> roomList, boolean isIncludeList) {
        LinkedList<OperaTransaction> transListToUpdate = new LinkedList<OperaTransaction>();
        LocalDate correctedDepartureDate = null;
        //If the first trans in backward parsing is a non-pseudo room, respect it's departure
        //  - Condition for future resv where only part of resv has come in current feed
        OperaTransaction operaTransaction = perResIdList.get(perResIdList.size() - 1);
        if ((isNonPseudo(roomList, isIncludeList, operaTransaction)) || (isIncludeList && roomList.contains(operaTransaction.getRoomType()))) {
            correctedDepartureDate = operaTransaction.getDepartureDate();
        }
        for (java.util.ListIterator<OperaTransaction> lit = perResIdList.listIterator(perResIdList.size()); lit.hasPrevious(); ) {
            OperaTransaction trans = lit.previous();
            if ((isNonPseudo(roomList, isIncludeList, trans)) || (isIncludeList && roomList.contains(trans.getRoomType()))) {
                if (transListToUpdate.isEmpty() && null == correctedDepartureDate) {
                    correctedDepartureDate = trans.getTransactionDate().plusDays(1);
                }
                trans.setDepartureDate(correctedDepartureDate);
                transListToUpdate.addFirst(trans);
            }
            if (((!isIncludeList && roomList.contains(trans.getRoomType())) || (isIncludeList && !roomList.contains(trans.getRoomType()))) && !transListToUpdate.isEmpty()) {
                trans.setDepartureDate(correctedDepartureDate);
                transListToUpdate.addFirst(trans);
            }
        }
        return transListToUpdate;
    }

    private LinkedList<OperaTransaction> adjustTransactionsStartingWithUnwantedRoomAndCreatingSplit(LinkedList<OperaTransaction> perResIdList
            , List<String> roomList, boolean isIncludeList) {
        LinkedList<OperaTransaction> transListToUpdate = new LinkedList<OperaTransaction>();
        LocalDate correctedArrivalDate = null;
        String correctedRoomType = null;
        String correctedMarketCode = null;
        String correctedHotelMarketCode = null;
        for (java.util.ListIterator<OperaTransaction> lit = perResIdList.listIterator(0); lit.hasNext(); ) {
            OperaTransaction trans = lit.next();
            if ((isNonPseudo(roomList, isIncludeList, trans)) || (isIncludeList && roomList.contains(trans.getRoomType()))) {
                correctedRoomType = trans.getRoomType();
                correctedMarketCode = trans.getMarketCode();
                correctedHotelMarketCode = trans.getHotelMarketCode();
                if (transListToUpdate.isEmpty()) {
                    correctedArrivalDate = trans.getTransactionDate();
                }
                trans.setArrivalDate(correctedArrivalDate);
                transListToUpdate.add(trans);
            }
            if (((!isIncludeList && roomList.contains(trans.getRoomType())) || (isIncludeList && !roomList.contains(trans.getRoomType()))) && !transListToUpdate.isEmpty()) {
                trans.setArrivalDate(correctedArrivalDate);
                trans.setRoomType(correctedRoomType);
                trans.setHotelMarketCode(correctedHotelMarketCode);
                trans.setMarketCode(correctedMarketCode);
                transListToUpdate.add(trans);
            }
        }
        return transListToUpdate;
    }

    public LinkedList<OperaTransaction> correctPseudoRoomTransactionsByAllowingStartingAndEnding(
            HashMap<Integer, LinkedList<OperaTransaction>> pseudoRoomTransactions, List<String> roomList, boolean isIncludeList) {

        // Since Collections cannot be modified while iterating, we need these lists to maintain updated reservation objects
        LinkedList<OperaTransaction> finalCorrectedReservations = new LinkedList<OperaTransaction>();
        pseudoRoomTransactions.entrySet().stream().forEach(transactions -> {
            LinkedList<OperaTransaction> perResIdList = transactions.getValue();
            finalCorrectedReservations.addAll(
                    correctPseudoRoomTransactionsOccurringInTheMiddleOfTransactions(perResIdList, roomList, isIncludeList)
            );
        });
        return finalCorrectedReservations;
    }

    private LinkedList<OperaTransaction> correctPseudoRoomTransactionsOccurringInTheMiddleOfTransactions(
            LinkedList<OperaTransaction> perResIdList
            , List<String> roomList, boolean isIncludeList) {

        List<OperaTransaction> nonPseudoTransactions = filterNonPseudoTransactions(perResIdList, roomList, isIncludeList);

        if (null != nonPseudoTransactions && !nonPseudoTransactions.isEmpty() && nonPseudoTransactions.size() > 1) {
            OperaTransaction firstNonPseudoTransaction = nonPseudoTransactions.get(0);
            OperaTransaction lastNonPseudoTransaction = nonPseudoTransactions.get(nonPseudoTransactions.size() - 1);
            LinkedList<OperaTransaction> transactionsBetweenFirstAndLastNonPseudo =
                    getTransactionsBetweenFirstAndLastNonPseudo(perResIdList, firstNonPseudoTransaction, lastNonPseudoTransaction);
            return correctPseudoRTTransactionByPrevious(roomList, isIncludeList, transactionsBetweenFirstAndLastNonPseudo);
        }
        return new LinkedList<>();
    }

    private List<OperaTransaction> filterNonPseudoTransactions(LinkedList<OperaTransaction> perResIdList, List<String> roomList, boolean isIncludeList) {
        return perResIdList
                .stream()
                .filter(operaTransaction -> isNonPseudo(roomList, isIncludeList, operaTransaction)).collect(Collectors.toList());
    }

    private LinkedList<OperaTransaction> correctPseudoRTTransactionByPrevious(List<String> roomList, boolean isIncludeList, LinkedList<OperaTransaction> skippedPseudoTransactionWithStartsAndEnds) {
        LinkedList<OperaTransaction> finalList = new LinkedList<>();
        String correctedRoomType = null;
        String correctedMarketCode = null;
        String correctedHotelMarketCode = null;
        for (ListIterator<OperaTransaction> lit = skippedPseudoTransactionWithStartsAndEnds.listIterator(0); lit.hasNext(); ) {
            OperaTransaction trans = lit.next();
            if (isNonPseudo(roomList, isIncludeList, trans)) {
                correctedRoomType = trans.getRoomType();
                correctedMarketCode = trans.getMarketCode();
                correctedHotelMarketCode = trans.getHotelMarketCode();
            }
            if ((!isIncludeList && roomList.contains(trans.getRoomType())) && null != correctedRoomType && null != correctedMarketCode) {
                trans.setRoomType(correctedRoomType);
                trans.setHotelMarketCode(correctedHotelMarketCode);
                trans.setMarketCode(correctedMarketCode);
                finalList.add(trans);
            }
        }
        return finalList;
    }

    private LinkedList<OperaTransaction> getTransactionsBetweenFirstAndLastNonPseudo(
            LinkedList<OperaTransaction> perResIdList,
            OperaTransaction firstNonPseudoTransaction,
            OperaTransaction lastNonPseudoTransaction) {
        LinkedList<OperaTransaction> skippedPseudoTransactionWithStartsAndEnds;

        skippedPseudoTransactionWithStartsAndEnds = perResIdList.stream().filter(trans ->
                trans.getTransactionDate().compareTo(firstNonPseudoTransaction.getTransactionDate()) >= 0 &&
                        trans.getTransactionDate().compareTo(lastNonPseudoTransaction.getTransactionDate()) <= 0
        ).collect(Collectors.toCollection(LinkedList::new));
        return skippedPseudoTransactionWithStartsAndEnds;
    }

    private boolean isNonPseudo(List<String> roomList, boolean isIncludeList, OperaTransaction trans) {
        return !isIncludeList && !roomList.contains(trans.getRoomType());
    }

    public List<String> getPseudoRoomList() {
        return getParameterValueAsListOfStrings(IntegrationConfigParamName.PSEUDO_ROOM_TYPE_CODES.value());
    }

    public List<String> getIncludeRoomList() {
        return getParameterValueAsListOfStrings(IntegrationConfigParamName.ROOM_TYPES_TO_INCLUDE.value(Constants.OPERA));
    }

    public List<String> getZeroCapacityRoomsTypesToInclude() {
        return getParameterValueAsListOfStrings(IntegrationConfigParamName.ZERO_CAPACITY_ROOM_TYPES_TO_INCLUDE.value(Constants.OPERA));
    }

    public List<String> getZeroCapacityRTRequiringPricingAndRestrictions() {
        return getParameterValueAsListOfStrings(IntegrationConfigParamName.ZERO_CAPACITY_ROOM_TYPES_REQUIRING_PRICING_AND_RESTRICTIONS.value(Constants.OPERA));
    }

    private List<String> getParameterValueAsListOfStrings(String parameterKey) {
        List<String> list = new ArrayList<String>();
        String value = pacmanConfigParamsService.getParameterValue(parameterKey);
        if (!StringUtils.isBlank(value)) {
            String[] individualValues = value.split(",");
            for (String individualValue : individualValues) {
                if (individualValue.trim().length() > 0) {
                    list.add(individualValue.trim());
                }
            }
        }
        return list;
    }

    public void savePseudoRoomList(List<String> list) {
        StringBuilder buffer = new StringBuilder();
        boolean firstTime = true;
        for (String roomType : list) {
            if (firstTime) {
                firstTime = false;
            } else {
                buffer.append(",");
            }
            buffer.append(roomType);
        }
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY);
        String context = "pacman." + workContext.getClientCode() + "." + workContext.getPropertyCode();
        pacmanConfigParamsService.addParameterValue(context, IntegrationConfigParamName.PSEUDO_ROOM_TYPE_CODES.value(), buffer.toString());

    }
}
