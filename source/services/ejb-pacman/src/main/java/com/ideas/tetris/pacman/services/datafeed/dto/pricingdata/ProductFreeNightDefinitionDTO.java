package com.ideas.tetris.pacman.services.datafeed.dto.pricingdata;

public class ProductFreeNightDefinitionDTO {

    private String productName;
    private String freeNight;
    private String freeUpgrade;

    public ProductFreeNightDefinitionDTO() {
    }

    public ProductFreeNightDefinitionDTO(String productName, boolean freeNight, boolean freeUpgrade) {
        this.productName = productName;
        this.freeNight = freeNight ? "Yes" : "No";
        this.freeUpgrade = freeUpgrade ? "Yes" : "No";
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getFreeNight() {
        return freeNight;
    }

    public void setFreeNight(String freeNight) {
        this.freeNight = freeNight;
    }

    public String getFreeUpgrade() {
        return freeUpgrade;
    }

    public void setFreeUpgrade(String freeUpgrade) {
        this.freeUpgrade = freeUpgrade;
    }
}
