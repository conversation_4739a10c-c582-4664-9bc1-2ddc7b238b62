package com.ideas.tetris.pacman.services.property.dto;

import java.io.File;
import java.io.FileFilter;

public class PacmanExtractFileFilter implements FileFilter {
    @Override
    public boolean accept(File candidate) {
        // e.g. Hilton_DALMC_20110226_0111_T2SNAP.zip
        boolean shouldAccept = false;
        if (!candidate.isDirectory()) {
            String[] segments = candidate.getName().split("[_.]");
            shouldAccept = segments.length == PacmanExtractDetails.FILE_NAME_SEGMENTS &&
                    PacmanExtractDetails.FILE_PART_T2SNAP.equals(segments[PacmanExtractDetails.FILE_NAME_SEGMENTS - 2]) &&
                    PacmanExtractDetails.FILE_PART_ZIP.equals(segments[PacmanExtractDetails.FILE_NAME_SEGMENTS - 1]);
        }
        return shouldAccept;
    }
}
