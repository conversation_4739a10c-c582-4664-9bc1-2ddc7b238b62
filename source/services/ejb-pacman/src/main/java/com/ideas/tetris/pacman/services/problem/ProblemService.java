package com.ideas.tetris.pacman.services.problem;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.threadpool.NamedDefaultThreadFactory;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.entity.ExecutionStatus;
import com.ideas.tetris.pacman.services.job.entity.JobExecution;
import com.ideas.tetris.pacman.services.job.entity.JobView;
import com.ideas.tetris.pacman.services.problem.dto.Bulletin;
import com.ideas.tetris.pacman.services.problem.dto.Note;
import com.ideas.tetris.pacman.services.problem.dto.NoteType;
import com.ideas.tetris.pacman.services.problem.dto.ProblemDetails;
import com.ideas.tetris.pacman.services.problem.dto.ProblemStatistics;
import com.ideas.tetris.pacman.services.problem.dto.Resolution;
import com.ideas.tetris.pacman.services.problem.entity.Problem;
import com.ideas.tetris.pacman.services.problem.entity.ProblemNote;
import com.ideas.tetris.pacman.services.problem.entity.ProblemView;
import com.ideas.tetris.pacman.services.problem.entity.SupportBulletinAction;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.salesforce.SalesForceEmailContactService;
import com.ideas.tetris.pacman.services.salesforce.entity.EmailEntityType;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.util.rest.ProblemRequest;
import com.ideas.tetris.pacman.util.rest.ProblemResponse;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.remoting.regulator.entities.RegulatorRequest;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.DBLoc;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.SASFileLoc;
import com.ideas.tetris.platform.services.regulator.service.RegulatorService;
import com.ideas.tetris.platform.services.regulator.service.spring.RegulatorSpringService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.OptimisticLockException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.platform.common.job.JobName.NGICdpDeferredDeliveryJob;
import static com.ideas.tetris.platform.common.job.JobName.NGIDeferredDeliveryJob;
import static com.ideas.tetris.platform.common.job.JobName.OperaCdpDataLoad;
import static com.ideas.tetris.platform.common.job.JobName.OperaDataLoad;
import org.springframework.beans.factory.annotation.Qualifier;

@Service
public class ProblemService {
    private static final Integer INITIAL_SCORE = 1;
    private static final String NOTE_CLAIMED = "claimed";
    private static final String NOTE_RELEASED = "released";
    public static final String SYSTEM = "System";
    private static final Logger LOGGER = Logger.getLogger(ProblemService.class.getName());
    private static List<SupportBulletinAction> NON_MANUAL_ACTIONS = SupportBulletinAction.nonManualActions();

    @JobCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("jobCrudServiceBean")
	protected CrudService jobCrudService;
    @Autowired
	private SupportBulletinService supportBulletinService;
    @Autowired
	protected PropertyService propertyService;
    @Autowired
	private ExternalSystemHelper externalSystemHelper;
    @Autowired
	private JobMonitorService jobMonitorService;
    @Autowired
	private SalesForceEmailContactService salesForceEmailContactService;
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;
    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;
    @Autowired
	private RegulatorService regulatorService;

    @Autowired
    private RegulatorSpringService regulatorSpringService;

    @Autowired
	private JobServiceLocal jobService;
    @Autowired
	private UserService userService;
    @Autowired
	private ClientService clientService;


    public List<ProblemView> getProblems(ProblemViewCriteria criteria) {
        List<ProblemView> problemViews = jobCrudService.findByCriteria(criteria);
        Map<Integer, Integer> emailCountByIssueIdMap = new HashMap<>();
        if (salesForceEmailContactService.isMonitorByExceptionSendEmailEnabled()) {
            List<Integer> issueIds = problemViews.stream().map(ProblemView::getProblemId).map(Long::intValue).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(issueIds)) {
                emailCountByIssueIdMap = salesForceEmailContactService.getEmailCountByWithPartition(issueIds, EmailEntityType.PROBLEM);
            }
        }
        for (ProblemView problemView : problemViews) {
            updateProblemView(problemView, emailCountByIssueIdMap);
        }
        return problemViews;
    }

    public List<ProblemView> getProblemsForSummary(ProblemViewCriteria criteria) {
        return jobCrudService.findByCriteria(criteria);
    }

    public ProblemStatistics getProblemStatistics(ProblemViewCriteria criteria) {
        return new ProblemStatistics(jobCrudService.findByCriteria(criteria));
    }

    public Long createProblem(Long jobInstanceId, Long jobExecutionId, Long stepExecutionId, String description,
                              ErrorCode errorCode, Integer propertyId) {
        return createProblem(jobInstanceId, jobExecutionId, stepExecutionId, description, errorCode, propertyId, true);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void createClosedProblem(Long jobInstanceId, Long jobExecutionId, Long stepExecutionId, String description,
                                    ErrorCode errorCode, Integer propertyId) {
        createProblem(jobInstanceId, jobExecutionId, stepExecutionId, description, errorCode, propertyId, false);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Long createProblem(Long jobInstanceId, Long jobExecutionId, Long stepExecutionId, String description, ErrorCode errorCode, Integer propertyId, boolean isActive) {
        Problem problem = new Problem();
        problem.setActive(isActive);
        problem.setCreationDate(new Date());

        if (description != null && description.length() > Problem.DESCRIPTION_LENGTH) {
            description = description.substring(0, Problem.DESCRIPTION_LENGTH);
        }

        problem.setDescription(description);
        problem.setStepExecutionId(stepExecutionId);
        problem.setScore(INITIAL_SCORE);
        problem.setErrorCode(errorCode);
        problem.setJobInstanceId(jobInstanceId);
        problem.setJobExecutionId(jobExecutionId);
        problem.setPropertyId(propertyId);
        problem = jobCrudService.save(problem);

        // Increment the Problem_Count in the JOB_STATE table (backing table of the Job_View)
        jobCrudService.executeUpdateByNativeQuery(
                "update JOB_STATE set PROBLEM_COUNT = PROBLEM_COUNT + 1 where JOB_INSTANCE_ID = :jobInstanceId",
                QueryParameter.with("jobInstanceId", jobInstanceId).parameters());

        // Get the ProblemView to get access to the job/step names
        ProblemView problemView = getProblemView(problem.getId());

        handleAutoSupportBulletin(problem, problemView);

        if (problemView != null && isAutoUnlockFeatureEnabled(problemView)) {
            autoUnlockConfigurationScreen(problemView);
        }

        return problem.getProblemId();
    }

    private void autoUnlockConfigurationScreen(ProblemView problemView) {

        if (problemView.getJobName() != null) {
            JobName jobNameEnum = JobName.valueOf(problemView.getJobName());
            if (jobNameEnum.isLocksUI()) {
                propertyService.setPropertyReadOnlyOverride(true, problemView.getPropertyId());
            }
        }
    }

    private boolean isAutoUnlockFeatureEnabled(ProblemView problemView) {
        return pacmanConfigParamsService.getParameterValue(problemView.getClientCode(), problemView.getPropertyCode(), GUIConfigParamName.IS_AUTO_UNLOCK_CONFIG_UI_ENABLED);
    }


    public List<ProblemView> assignProblems(List<ProblemView> problems, String userId, String userName) {
        List<Problem> uniqueProblems = getProblems(problems);
        uniqueProblems.forEach(p -> assignProblem(p, userId, userName));
        return getProblemViews(uniqueProblems);
    }


    public ProblemView assignProblem(long problemId, String userId, String userName) {
        Problem problem = jobCrudService.find(Problem.class, problemId);
        if (problem == null) {
            return null;
        }
        LDAPUser user = userService.getById(userId);
        if (null == user || (!StringUtils.equals(userName, user.getCn()))) {
            return null;
        }
        assignProblem(problem, userId, userName);
        return getProblemView(problemId);
    }

    private void assignProblem(Problem problem, String userId, String userName) {
        // don't assign it if it is closed or the user already owns it
        if (isClosed(problem) || isOwnedByUser(problem, userId)) {
            return;
        }

        String currentUserName = PacmanThreadLocalContextHolder.getPrincipal().getDisplayName();
        // Check if release necessary
        if (problem.getOwnerId() != null) {
            releaseProblem(problem, currentUserName);
        }

        problem.setOwnerId(userId);
        problem.setOwnerName(userName);
        problem.addNote(createProblemNote(currentUserName, NoteType.ASSIGN, "assigned to " + userName));
        saveProblem(problem);
    }

    public List<ProblemView> claimProblems(List<ProblemView> problems) {
        List<Problem> uniqueProblems = getProblems(problems);
        uniqueProblems.forEach(this::tryClaimProblem);
        return getProblemViews(uniqueProblems);
    }


    public ProblemView claimProblem(long problemId) {
        Problem problem = jobCrudService.find(Problem.class, problemId);
        if (problem == null) {
            return null;
        }
        tryClaimProblem(problem);
        return getProblemView(problemId);
    }

    private void tryClaimProblem(Problem problem) {
        if (claimProblem(problem)) {
            saveProblem(problem);
        }
    }

    private boolean claimProblem(Problem problem) {
        // don't claim already closed problems
        if (!isClosed(problem)) {
            String userId = PacmanWorkContextHelper.getWorkContext() != null ? PacmanWorkContextHelper.getWorkContext().getUserId() : SYSTEM;
            String userName = PacmanThreadLocalContextHolder.getPrincipal() != null ? PacmanThreadLocalContextHolder.getPrincipal().getDisplayName() : SYSTEM;
            // Check if release necessary
            if (isOwnedByDifferentUser(problem)) {
                releaseProblem(problem, userName);
            }
            // Did not claim what i already own
            if (problem.getOwnerId() == null) {
                problem.setOwnerId(userId);
                problem.setOwnerName(userName);
                problem.addNote(createProblemNote(userName, NoteType.CLAIM, NOTE_CLAIMED));
                return true;
            }
        }
        return false;
    }

    public List<ProblemView> releaseProblems(List<ProblemView> problems) {
        List<Problem> uniqueProblems = getProblems(problems);
        uniqueProblems.forEach(this::releaseProblem);
        return getProblemViews(uniqueProblems);
    }


    public ProblemView releaseProblem(long problemId) {
        Problem problem = jobCrudService.find(Problem.class, problemId);
        if (problem == null) {
            return null;
        }
        releaseProblem(problem);
        return getProblemView(problemId);
    }

    private void releaseProblem(Problem problem) {
        if (problem.isActive() && problem.getOwnerId() != null) {
            String userName = PacmanThreadLocalContextHolder.getPrincipal().getDisplayName();
            releaseProblem(problem, userName);
            saveProblem(problem);
        }
    }

    private void releaseProblem(Problem problem, String userName) {
        problem.setOwnerId(null);
        problem.setOwnerName(null);
        problem.addNote(createProblemNote(userName, NoteType.RELEASE, NOTE_RELEASED));
    }

    private void closeProblem(Problem problem, String resolutionText) {
        claimProblem(problem);

        problem.setActive(false);
        problem.addNote(createProblemNote(problem.getOwnerName(), NoteType.CLOSE, resolutionText));
        if (saveProblem(problem) && problem.getPropertyId() != null && problem.getPropertyId() > 0) {
            propertyService.setPropertyReadOnlyOverride(false, problem.getPropertyId());
        }
    }

    private void disableIgnoreInRegulator(Problem problem) {
        JobView jobView = jobCrudService.find(JobView.class, problem.getJobInstanceId());

        // Check to see if there is a regulatorRequestId on the job's execution context
        Integer regulatorRequestId = jobView != null ? (Integer) jobView.getFromExecutionContext("regulatorRequestId") : null;
        if (regulatorRequestId != null) {
            globalCrudService.executeUpdateByNativeQuery(
                    "update [Regulator_Request] with(ROWLOCK) set Ignore_In_Throttler = 0, ModifiedDate = GETDATE() where [Request_ID] = :regulatorRequestId",
                    QueryParameter.with("regulatorRequestId", regulatorRequestId).parameters());

        }
    }

    public int resumeFailedJobs(List<ProblemView> selectedProblems) {
        if (CollectionUtils.isEmpty(selectedProblems)) {
            return 0;
        }
        List<Problem> uniqueProblems = getProblems(selectedProblems);
        if (CollectionUtils.isEmpty(uniqueProblems)) {
            return 0;
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Attempting to resume " + uniqueProblems.size() + " of " + selectedProblems.size() + " problems.");
        }
        uniqueProblems.forEach(this::resumeFailedJob);
        return uniqueProblems.size();
    }


    public void resumeFailedJob(long problemId) {
        resumeFailedJob(problemId, "");
    }

    public void resumeFailedJob(long problemId, String interventionType) {
        Problem problem = jobCrudService.find(Problem.class, problemId);
        if (StringUtils.isNotBlank(interventionType)) {
            problem.setInterventionType(interventionType);
        }
        resumeFailedJob(problem);
    }

    private void resumeFailedJob(Problem problem) {
        if (problem != null && !isClosed(problem) && !isOwnedByDifferentUser(problem)) {
            closeProblem(problem, Resolution.RESUME.getNoteText());
            disableIgnoreInRegulator(problem);
            jobMonitorService.resumeJobExecution(problem.getJobExecutionId());
        }
    }

    private List<Problem> getProblems(List<ProblemView> selectedProblems) {
        if (CollectionUtils.isEmpty(selectedProblems)) {
            return Collections.emptyList();
        }

        Set<Long> uniqueProblemIds = selectedProblems.stream().map(ProblemView::getProblemId).collect(Collectors.toSet());
        return jobCrudService.findByNamedQuery(Problem.FIND_ALL_BY_PROBLEM_IDS, QueryParameter.with("problemIds", uniqueProblemIds).parameters());
    }

    public int continueFailedJobs(List<ProblemView> problems) {
        if (CollectionUtils.isEmpty(problems)) {
            return 0;
        }
        List<Problem> uniqueProblems = getProblems(problems);
        if (CollectionUtils.isEmpty(uniqueProblems)) {
            return 0;
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Attempting to continue " + uniqueProblems.size() + " of " + problems.size() + " problems.");
        }
        uniqueProblems.forEach(this::continueFailedJob);
        return uniqueProblems.size();
    }


    public void continueFailedJob(long problemId) {
        continueFailedJob(problemId, "");
    }

    public void continueFailedJob(long problemId, String interventionType) {
        Problem problem = jobCrudService.find(Problem.class, problemId);
        if (null != problem && StringUtils.isNotBlank(interventionType)) {
            problem.setInterventionType(interventionType);
        }
        continueFailedJob(problem);
    }

    private void continueFailedJob(Problem problem) {
        if (problem != null && !isClosed(problem) && !isOwnedByDifferentUser(problem)) {
            closeProblem(problem, Resolution.CONTINUE.getNoteText());
            disableIgnoreInRegulator(problem);
            jobMonitorService.continueJobExecution(problem.getJobExecutionId());
        }
    }

    public int abandonFailedJobs(List<ProblemView> problems) {
        if (CollectionUtils.isEmpty(problems)) {
            return 0;
        }
        List<Problem> uniqueProblems = getProblems(problems);
        if (CollectionUtils.isEmpty(uniqueProblems)) {
            return 0;
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Attempting to abandon " + uniqueProblems.size() + " of " + problems.size() + " problems.");
        }
        uniqueProblems.forEach(this::abandonFailedJob);
        return uniqueProblems.size();
    }


    public Problem abandonFailedJob(Long problemId) {
        return abandonFailedJob(problemId, "");
    }

    public Problem abandonFailedJob(Long problemId, String interventionType) {
        if (problemId == null) {
            return null;
        }
        Problem problem = jobCrudService.find(Problem.class, problemId);
        if (null != problem && StringUtils.isNotBlank(interventionType)) {
            problem.setInterventionType(interventionType);
        }
        abandonFailedJob(problem);
        handlePropertyReadOnlyDuringIDP(problem);
        return problem;
    }

    private void handlePropertyReadOnlyDuringIDP(Problem problem) {
        if (problem == null) {
            return;
        }
        JobView jobDetail = jobMonitorService.getJobDetail(problem.getJobInstanceId());
        String jobName = jobDetail.getJobName();
        Stream.of(OperaCdpDataLoad.name(), NGICdpDeferredDeliveryJob.name(),
                        OperaDataLoad.name(), NGIDeferredDeliveryJob.name())
                .filter(job -> job.equals(jobName))
                .findAny()
                .ifPresent(s -> propertyService.updateIsReadOnly(0, problem.getPropertyId()));
    }

    private void abandonFailedJob(Problem problem) {
        if (problem != null) {
            LOGGER.info("Abandon process started: " + problem.getProblemId() + " for propertyId: " + problem.getPropertyId());
        }
        if (problem != null && !isOwnedByDifferentUser(problem)) {
            closeProblem(problem, Resolution.ABANDON.getNoteText());
            LOGGER.info("Job abandon process started: " + problem.getJobInstanceId() + " Owner Name:" + problem.getOwnerName() + " for propertyId: " + problem.getPropertyId());
            jobMonitorService.abandonJobInstance(problem.getJobInstanceId(), problem.getOwnerName());
            LOGGER.info("Job abandon process completed: " + problem.getJobInstanceId() + " Owner Name:" + problem.getOwnerName() + " for propertyId: " + problem.getPropertyId());
        }
    }

    public ProblemDetails getDetails(long problemId) {
        Problem entity = jobCrudService.find(Problem.class, problemId);
        if (entity == null) {
            return null;
        }
        ProblemDetails details = new ProblemDetails(entity);
        ProblemView view = getProblemView(problemId);
        if (view == null) {
            return details;
        }
        List<Bulletin> bulletins = supportBulletinService.getSupportBulletins(new ProblemBulletinCriteria(view));
        details.setBulletins(bulletins);
        return details;
    }

    public Note addNote(long problemId, String text) {
        Problem problem = jobCrudService.find(Problem.class, problemId);
        if (problem == null) {
            return null;
        }
        return addNote(text, problem);
    }

    public Note addNote(String text, Problem problem) {
        String userName = PacmanThreadLocalContextHolder.getPrincipal().getDisplayName();
        ProblemNote note = createProblemNote(userName, NoteType.GENERAL, text);
        problem.addNote(note);
        return saveProblem(problem) ? new Note(note) : null;
    }

    private ProblemNote createProblemNote(String authorName, NoteType type, String text) {
        ProblemNote note = new ProblemNote();
        note.setAuthorName(authorName);
        note.setType(type.toString());
        note.setCreationDate(new Date());
        if (text != null && text.length() > ProblemNote.TEXT_LENGTH) {
            text = text.substring(0, ProblemNote.TEXT_LENGTH);
        }
        note.setText(text);
        return note;
    }


    public int getUnclaimedProblemCount() {
        ProblemViewCriteria criteria = new ProblemViewCriteria();
        criteria.setIncludeActiveProblems(true);
        criteria.setIncludeClosedProblems(false);
        criteria.setOwnerId(ProblemViewCriteria.UNCLAIMED_ONLY);
        criteria.setPropertyId(ProblemViewCriteria.ALL_PROPERTIES_OR_NO_PROPERTY);
        return jobCrudService.findCountByCriteria(criteria);
    }

    public boolean isProblemAlreadyClaimed(List<ProblemView> problems) {
        if (problems != null) {
            String userId = PacmanWorkContextHelper.getWorkContext().getUserId();

            Set<Long> problemIds = Optional.ofNullable(problems).orElse(Collections.emptyList())
                    .stream().map(ProblemView::getProblemId).collect(Collectors.toSet());

            List<String> owners = jobCrudService.findByNamedQuery(Problem.GET_OWNERS,
                    QueryParameter.with("problemIds", problemIds).parameters());

            if (CollectionUtils.isNotEmpty(owners)) {
                // look for any owners that are not null and not equal to the current user
                return owners.stream().anyMatch(owner -> owner != null && !owner.equals(userId));
            }
        }
        return false;
    }

    public ProblemView getProblemView(long problemId) {
        ProblemView entity = jobCrudService.findByNamedQuerySingleResult(ProblemView.GET_BY_ID,
                QueryParameter.with("problemId", problemId).parameters());
        Map<Integer, Integer> emailCountByIssueIdMap = new HashMap<>();
        if (salesForceEmailContactService.isMonitorByExceptionSendEmailEnabled() && entity != null && entity.getProblemId() != null) {
            emailCountByIssueIdMap = salesForceEmailContactService.getEmailCountByWithPartition(Collections.singletonList(entity.getProblemId().intValue()), EmailEntityType.PROBLEM);
        }
        updateProblemView(entity, emailCountByIssueIdMap);
        return entity;
    }

    public ProblemView getProblemViewWithNote(long problemId) {
        ProblemView problemView = getProblemView(problemId);
        Hibernate.initialize(problemView.getNotes());
        return problemView;
    }

    public List<ProblemNote> getProblemNote(long problemId) {
        return jobCrudService.findByNamedQuery(ProblemNote.GET_BY_PROBLEM_ID,
                QueryParameter.with("problemId", problemId).parameters());
    }

    private void updateProblemView(ProblemView problemView, Map<Integer, Integer> emailCountByIssueIdMap) {
        if (problemView != null && problemView.getPropertyId() != null) {
            Property property = propertyService.getPropertyById(problemView.getPropertyId());
            problemView.setPropertyName(property == null ? "" : property.getName());
            problemView.setClientName(property == null ? "" : property.getClient().getName());
            problemView.setExternalSystem(property == null ? ""
                    : externalSystemHelper.getExternalSystemAsString(property.getClient().getCode(), property.getCode()));
            problemView.setEmailCount(emailCountByIssueIdMap.get(problemView.getProblemId().intValue()));
        }
    }


    public List<ProblemView> getActiveProblems() {
        ProblemViewCriteria criteria = new ProblemViewCriteria();
        criteria.setIncludeActiveProblems(true);
        criteria.setIncludeNotes(true);
        criteria.setPropertyId(ProblemViewCriteria.ALL_PROPERTIES_OR_NO_PROPERTY);
        List<ProblemView> problems = jobCrudService.findByCriteria(criteria);
        return problems;
    }

    private boolean isClosed(Problem problem) {
        return !problem.isActive();
    }

    private boolean isOwnedByDifferentUser(Problem problem) {
        String userId = PacmanWorkContextHelper.getWorkContext() != null ? PacmanWorkContextHelper.getWorkContext().getUserId() : SYSTEM;
        return (problem.getOwnerId() != null && !problem.getOwnerId().equals(userId));
    }

    private boolean isOwnedByUser(Problem problem, String userId) {
        return (problem.getOwnerId() != null && problem.getOwnerId().equals(userId));
    }

    boolean saveProblem(Problem problem) {
        try {
            jobCrudService.save(problem);
        } catch (OptimisticLockException e) {
            LOGGER.warn("Failed to save Problem: " + problem.getProblemId(), e);
            return false;
        }
        return true;
    }

    void handleAutoSupportBulletin(Problem problem, ProblemView problemView) {

        // Search for a matching SupportBulletin
        SupportBulletinCriteria supportBulletinCriteria = new SupportBulletinCriteria();
        supportBulletinCriteria.setJobName(problemView.getJobName());
        supportBulletinCriteria.setStepName(problemView.getStepName());
        supportBulletinCriteria.setIncludeActiveBulletins(true);
        supportBulletinCriteria.setIncludeClosedBulletins(false);
        supportBulletinCriteria.setActions(NON_MANUAL_ACTIONS);

        // Query for the bulletins
        List<Bulletin> supportBulletins = supportBulletinService.getSupportBulletins(supportBulletinCriteria);
        LOGGER.info("Auto support bulletin handle started for problem: " + problem.getProblemId() + " for propertyId: " + problem.getPropertyId());

        if (CollectionUtils.isNotEmpty(supportBulletins)) {
            LOGGER.info("Total Support Bulletins: " + supportBulletins.size() + " for propertyId: " + problem.getPropertyId());
            // Get the action for the associated SupportBulletin
            Bulletin supportBulletin = supportBulletins.get(0);

            // We need to execute the action in a new thread - this will allow the job that is currently running to
            // end it's execution.  We'll poll within the thread to wait for the job to end.
            Executors.newSingleThreadExecutor(new NamedDefaultThreadFactory("problem-service")).execute(() -> actionProblemWithSupportBulletin(problem, supportBulletin));
        }
    }

    void actionProblemWithSupportBulletin(Problem problem, Bulletin supportBulletin) {
        // Wait for the job to completely stop - since we are creating a problem the job hasn't stopped yet
        JobView jobView = jobMonitorService.getJobViewWaitUntilStatus(problem.getJobInstanceId(), true, JobMonitorService.TIMEOUT_MILLIS, Arrays.asList(ExecutionStatus.FAILED, ExecutionStatus.STOPPED));

        // Create local variables for easier access
        Long problemId = problem.getId();
        SupportBulletinAction action = supportBulletin.getAction();
        List<ProblemView> problems = jobView.getProblems();
        LOGGER.info("Bulletin action: " + action.getName() + " Problem Id:" + problemId + " for propertyId: " + problem.getPropertyId());
        boolean hasProblemOnStep = CollectionUtils.isNotEmpty(problems) && problems.stream().filter(p -> !p.getId().equals(problem.getId())).anyMatch(p -> p.getStepName().equals(jobView.getStepName()));

        // If the action is to resume the problem, only do it when the problem count is 0 - this provides a circuit breaker to stop the actions from being put into an infinite loop
        if (SupportBulletinAction.RESUME.equals(action) && !hasProblemOnStep) {
            resumeFailedJob(problemId);
        } else if (SupportBulletinAction.ABANDON.equals(action)) {
            abandonFailedJob(problemId);
        } else if (SupportBulletinAction.CONTINUE.equals(action)) {
            continueFailedJob(problemId);
        }
    }

    private List<ProblemView> getProblemViews(List<Problem> problems) {
        ProblemViewCriteria criteria = new ProblemViewCriteria();
        criteria.setProblemIds(problems.stream().map(Problem::getProblemId).collect(Collectors.toList()));
        return getProblems(criteria);
    }

    public void closeActiveProblemsIn(JobExecution jobExecution) {
        jobExecution.getProblems().stream()
                .filter(ProblemView::isActive)
                .map(ProblemView::getProblemId)
                .forEach(activeProblemId -> {
                    Problem activeProblem = jobCrudService.find(Problem.class, activeProblemId);
                    closeProblem(activeProblem, Resolution.RESUME.getNoteText());
                });
    }


    public ProblemResponse resumeJob(Long problemId, ProblemRequest request) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Invoking /resumeJob REST endpoint for problemId: " + problemId);
        }
        Problem problem = jobCrudService.find(Problem.class, request.getProblemId());
        if (problem == null) {
            return new ProblemResponse();
        }
        if (StringUtils.isNotBlank(request.getInterventionType())) {
            problem.setInterventionType(request.getInterventionType());
        }
        resumeProblemJob(problem);

        return new ProblemResponse(problem);
    }

    protected void resumeProblemJob(Problem problem) {
        RegulatorRequest existingRegulatorRequest = getRegulatorRequest(problem.getJobInstanceId());
        if (existingRegulatorRequest != null) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Preparing to resume problem job thru Regulator");
            }
            // This Job normally runs in the Regulator, therefore re-run it in the Regulator to distribute load on resources
            resumeJobAsProxiedProblemResumeJob(problem);

        } else {
            // Either the RegulatorRequest has been deleted or this Job does not participate in the Regulator.
            // In this case fall back to resuming Job as would normally would happen
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Preparing to resume problem job");
            }
            resumeFailedJob(problem);
        }
    }

    protected RegulatorRequest getRegulatorRequest(Long jobInstanceId) {
        if (regulatorService.isSpringTXEnableRegulatorService()) {
            List<RegulatorRequest> regulatorRequests = regulatorSpringService.getRequestsByJobInstanceId(jobInstanceId);
            if (CollectionUtils.isNotEmpty(regulatorRequests)) {
                return regulatorRequests.get(0);
            }
        } else {
            List<RegulatorRequest> regulatorRequests = regulatorService.getRequestsByJobInstanceId(jobInstanceId);
            if (CollectionUtils.isNotEmpty(regulatorRequests)) {
                return regulatorRequests.get(0);
            }
        }
        return null;
    }

    protected Long resumeJobAsProxiedProblemResumeJob(Problem problem) {
        // Claim and close out the problem
        closeProblem(problem);

        // Get the existing Job details
        JobExecution existingJobExecution = jobMonitorService.getJobExecution(problem.getJobExecutionId());
        Map<String, Object> jobParameters = buildJobParams(existingJobExecution);

        // Create new JobInstance and JobExecution
        Long jobExecutionId = jobService.startGuaranteedNewInstance(JobName.ProblemResumeJob, jobParameters);

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Created new ProblemResumeJob for Proxied JobExecution(" + jobExecutionId + ")");
        }
        return jobExecutionId;
    }

    protected Map<String, Object> buildJobParams(JobExecution existingJobExecution) {
        Map<String, Object> jobParameters = new HashMap<>();
        jobParameters.put(JobParameterKey.PROBLEM_RESUME_JOB_PROXIED_JOB_INSTANCE_ID, existingJobExecution.getJobView().getJobInstanceId());
        jobParameters.put(JobParameterKey.PROBLEM_RESUME_JOB_PROXIED_JOB_EXECUTION_ID, existingJobExecution.getJobExecutionId());
        return jobParameters;
    }


    protected void closeProblem(Problem problem) {
        if (problem != null && !isClosed(problem) && !isOwnedByDifferentUser(problem)) {
            closeProblem(problem, Resolution.RESUME.getNoteText());
        }
    }

    public List<ProblemView> getProblemsByCriteria(ProblemViewCriteria criteria) {
        validateFilters(criteria.getClientCodes(), criteria.getPropertyCodes());
        return getProblems(criteria);
    }

    @VisibleForTesting
    protected void validateFilters(Set<String> clientCode, Set<String> propertyCodes) {
        Client client = null;
        if (clientCode != null) {
            client = clientService.getClient(clientCode.iterator().next());
            if (client == null) {
                throw new TetrisException(ErrorCode.INVALID_INPUT, clientCode + " : is not valid Client Code");
            }
        }
        Set<Property> properties = propertyService.getProperties(client, propertyCodes);
        if (propertyCodes != null && (properties == null || propertyCodes.size() != properties.size())) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "One or more properties are not part of client " + clientCode);
        }
    }

    public Note addNote(Long problemId, String emailId, String text) {
        validateInputs(emailId, text);
        LDAPUser user = getUserByEmail(emailId);
        Problem problem = getProblem(problemId);
        setUserIdOnWorkContent(user.getUid());
        setPrincipal(user.getCn());
        claimProblem(problem);
        Note note = addNote(text, problem);
        if (note == null) {
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "Unable to add Note, Please try again !");
        }
        return note;
    }

    public void setUserIdOnWorkContent(String userId) {
        WorkContextType workContext = new WorkContextType();
        workContext.setUserId(userId);
        PacmanWorkContextHelper.setWorkContext(workContext);
    }

    public Problem getProblem(Long problemId) {
        Problem problem = jobCrudService.find(Problem.class, problemId);
        if (problem == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Problem not found with given problem id : " + problemId);
        }
        return problem;
    }

    public LDAPUser getUserByEmail(String emailId) {
        Set<LDAPUser> users = userService.getGlobalUsersByEmailIds(Arrays.asList(emailId));
        LDAPUser user = null;
        if (CollectionUtils.isNotEmpty(users)) {
            user = users.iterator().next();
        } else {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "User not found with given mail id : " + emailId);
        }
        return user;
    }

    private void validateInputs(String emailId, String text) {
        if (StringUtils.isBlank(emailId) || StringUtils.isBlank(text)) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Mandatory inputs are empty : emailId =  " + emailId + " ,text = " + text + " ");
        }
    }

    protected void setPrincipal(String userName) {
        TetrisPrincipal principal = new TetrisPrincipal();
        principal.setDisplayName(userName);
        PacmanThreadLocalContextHolder.setPrincipal(principal);
    }

    public List<String> getDistinctDbServerNames() {
        return globalCrudService.findByNamedQuery(DBLoc.FIND_DISTINCT_SERVER_NAMES);
    }

    public List<String> getDistinctSasServerNames() {
        return globalCrudService.findByNamedQuery(SASFileLoc.FIND_DISTINCT_SAS_SERVER_NAMES);
    }
}