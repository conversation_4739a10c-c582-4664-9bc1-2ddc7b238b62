package com.ideas.tetris.pacman.services.reports.bookingbylos;

import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedHashMap;

public class BookingByLOSUiDTO {

    private String dayOfWeek;
    private Date arrivalDate;
    private Integer dl;
    private Integer transSold;
    private Integer groupSold;
    private Integer totalSold;
    private BigDecimal stayBookings;
    private LinkedHashMap<String, Integer> losWithValue;

    public String getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(String dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public Date getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public Integer getDl() {
        return dl;
    }

    public void setDl(Integer dl) {
        this.dl = dl;
    }

    public Integer getTransSold() {
        return transSold;
    }

    public void setTransSold(Integer transSold) {
        this.transSold = transSold;
    }

    public Integer getGroupSold() {
        return groupSold;
    }

    public void setGroupSold(Integer groupSold) {
        this.groupSold = groupSold;
    }

    public Integer getTotalSold() {
        return totalSold;
    }

    public void setTotalSold(Integer totalSold) {
        this.totalSold = totalSold;
    }

    public BigDecimal getStayBookings() {
        return stayBookings;
    }

    public void setStayBookings(BigDecimal stayBookings) {
        this.stayBookings = stayBookings;
    }

    public LinkedHashMap<String, Integer> getLosWithValue() {
        return losWithValue;
    }

    public void setLosWithValue(LinkedHashMap<String, Integer> losWithValue) {
        this.losWithValue = losWithValue;
    }
}
