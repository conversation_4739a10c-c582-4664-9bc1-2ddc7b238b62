package com.ideas.tetris.pacman.services.reports.inputoverride.dto;

public enum InputOverrideCategoryEnum {

    DEMAND_OCCUPANCY_DATE(1, "Demand-Occupancy Date", "DEMAND_OCCUPANCY_DATE"),
    DEMAND_ARRIVAL_BY_LOS(2, "Demand-Arrival by LOS", "DEMAND_ARRIVAL_BY_LOS"),
    WASH(3, "Wash", "WASH"),
    NOTES(4, "Notes", "NOTES"),
    GFFOVERRIDE(5, "Group Final Forecast Override", "GFF_OVERRIDE");

    private Integer id;
    private String caption;
    private String type;

    public Integer getId() {
        return id;
    }

    public String getCaption() {
        return caption;
    }

    public String getType() {
        return type;
    }

    InputOverrideCategoryEnum(Integer id, String caption, String type) {
        this.id = id;
        this.caption = caption;
        this.type = type;
    }

}
