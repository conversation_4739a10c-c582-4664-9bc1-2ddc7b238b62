package com.ideas.tetris.pacman.services.learning;

import com.ideas.tetris.platform.services.Stage;
import lombok.Data;

@Data
public class LMSPropertyParameters {

    private String barDecision;
    private Stage stage = Stage.ONE_WAY;
    private boolean isContinuousPricing;
    private boolean isGroupPricingEnabled;
    private boolean isFunctionSpaceEnabled;
    private boolean isLDB;
    private String propertyTimeZone;
    private String externalSystem;
    private boolean isWebrateAlertsEnabled;
    private boolean isManualBarUploadEnabled;
    private boolean isSTREnabled;
    private boolean isMarketPerformanceEnabled;
    private boolean isComponentRoomsEnabled;
    private boolean isDemand360Enabled;
    private boolean isAMSEnabled;
    private boolean isExtendedStayEnabled;
    private boolean isAdvancedPriceRankEnabled;
    private boolean isUseCpPaceDiffrentialTableEnabled;
    private boolean isGroupPricingMultiPropertyEnabled;
    private boolean isRRAEnabled;
    private boolean isAgileRatesEnabled;
    private boolean isPopulateAnalyticsTransEnabled;
    private boolean isPerPersonPricingEnabled;
    private boolean isChildAgeBucketsEnabled;
    private boolean isUploadChildAgeBucketsEnabled;
    private boolean isUploadAdultsBeyond2Enabled;
    private boolean isUploadChildrenBeyondExtraEnabled;
    private boolean isUseCompactWebratePace;
    private boolean isPricingRedesignEnabled;
    private boolean isSupplementalEnabled;
    private boolean isQualifiedRatePlanConfigurationEnabled;
    private boolean isAnalyticalAgileRateRCEnabled;
    private boolean isNewPricingInvestigatorEnabled;
    private boolean isIndependentProductsEnabled;
    private boolean isIncludeIndependentProductsInReportEnabled;
    private boolean isIndependentAndLinkedForAtAGlanceEnabled;
    private boolean isProductDimensionForCompetitiveNotificationsEnabled;
    private boolean isUseMktAccomActivityForPastDataEnabled;
    private boolean isUseOldProductFamilyIdEnabled;
    private boolean isFixedAboveBARProductEnabled;
    private boolean isFunctionSpacePackageEnabled;
    private boolean isLinkedProductHierarchyEnabled;
    private boolean isPricingInvestigatorFetchCompetitorPricesFromAllChannels;
    private boolean isChannelCostV3;
    private boolean isChannelCostEnabled;
    private boolean isEnablePriceDropRestrictionsForDOWAndSeasons;
}
