package com.ideas.tetris.pacman.services.discover;

public class DiscoverConstants {
    public static final String USER_ID = "user_id";
    public static final String USER_EMAIL = "user_email";
    public static final String USER_LOCALE = "locale";
    public static final String USER_IS_ACTIVE = "is_active";
    public static final String USER_PRODUCT = "custom_c";
    public static final String USER = "user";
    public static final String SUCCESS = "Success";
    public static final String FAILURE = "Failure";
    public static final String GRANT_TYPE = "grant_type";
    public static final String ASSERTION = "assertion";
    public static final String PRODUCT_SEPARATOR = " & ";
    public static final String REVPLAN_PRODUCT_NAME = "RevPlan";
    public static final String G3_PRODUCT_NAME = "G3";
    public static final String SMARTSPACE_PRODUCT_NAME = "SmartSpace";
    public static final String ELEVATE_PRODUCT_NAME = "Elevate";
    public static final String RMS_PRODUCT_NAME = "RMS";
    public static final String PRODUCT_NAME_RESPONSE_KEY = "product_name";
    public static final String IS_ACTIVE_RESPONSE_KEY = "is_active";

    private DiscoverConstants() {
        // Comment to remove Sonar violation
    }
}
