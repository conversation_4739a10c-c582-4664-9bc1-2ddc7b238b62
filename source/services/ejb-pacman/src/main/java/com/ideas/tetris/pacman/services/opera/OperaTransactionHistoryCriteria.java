package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.platform.common.entity.AbstractCriteria;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import java.util.List;

public class OperaTransactionHistoryCriteria extends AbstractCriteria<OperaTransactionHistory> {

    private List<String> reservationNameIds;

    public OperaTransactionHistoryCriteria() {
        super(OperaTransactionHistory.class);
    }


    public OperaTransactionHistoryCriteria(List<String> reservationNameIds) {
        this();
        this.reservationNameIds = reservationNameIds;
    }

    @Override
    public DetachedCriteria getDetachedCriteria() {
        DetachedCriteria detachedCriteria = super.getDetachedCriteria();

        if (reservationNameIds != null && !reservationNameIds.isEmpty()) {
            detachedCriteria.add(Restrictions.in("reservationNameId", reservationNameIds));           //reservationNameId
        }
        return detachedCriteria;
    }


    public List<String> getReservationNameIds() {
        return reservationNameIds;
    }


    public void setReservationNameIds(List<String> reservationNameIds) {
        this.reservationNameIds = reservationNameIds;
    }
}
