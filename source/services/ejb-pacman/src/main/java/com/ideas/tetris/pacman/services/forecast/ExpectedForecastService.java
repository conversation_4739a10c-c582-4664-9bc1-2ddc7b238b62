package com.ideas.tetris.pacman.services.forecast;

import com.ideas.tetris.pacman.Service;
import com.ideas.tetris.pacman.services.dashboard.builder.SpecialEventsBuilder;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.demandoverride.entity.ArrivalDemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.entity.OccupancyDemandOverride;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastChartDTO;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailDTO;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailForPastSevenDaysDTO;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailGraphForPastSevenDaysDTO;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailGraphPastSevenDaysChartDTO;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailOnBooksDTO;
import com.ideas.tetris.pacman.services.forecast.repository.ExpectedForecastRepository;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.inject.Inject;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.forecast.ExpectedForecastConstant.HIPHEN;
import static com.ideas.tetris.pacman.services.forecast.ExpectedForecastConstant.PAST_DAYS;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Service
@Component
public class ExpectedForecastService {

    @Autowired
    ExpectedForecastRepository repository;

    @Autowired
	public DateService dateService;

    @Autowired
	private SpecialEventsBuilder specialEventsBuilder;

    /**
     * Use to get the data for Expected ForeCast Chart.
     *
     * @return
     */
    public ExpectedForecastChartDTO fetchExpectedForecastDetails() {

        List<ExpectedForecastDetailDTO> expectedForecastDetailDTOS = repository.fetchExpectedForecastDetails();
        ExpectedForecastChartDTO expectedForecastChartDTO = new ExpectedForecastChartDTO();

        if (CollectionUtils.isNotEmpty(expectedForecastDetailDTOS)) {
            populateExpectedForecastChartDTO(expectedForecastDetailDTOS, expectedForecastChartDTO);
        }
        return expectedForecastChartDTO;
    }

    public List<ExpectedForecastDetailOnBooksDTO> fetchExpectedForecastOnBooksDetails(Date occupancyDt) {

        Date occupancyDate = DateUtil.removeTimeFromDate(occupancyDt);
        Date startDate = DateUtil.addDaysToDate(occupancyDate, -1);
        Date endDate = DateUtil.addDaysToDate(occupancyDate, 1);
        Date businessEndDate = dateService.getCaughtUpDate();

        return repository.fetchExpectedForecastOnBooksDetails(startDate, endDate, businessEndDate);
    }

    private void populateExpectedForecastChartDTO(List<ExpectedForecastDetailDTO> expectedForecastDetailDTOS, ExpectedForecastChartDTO expectedForecastChartDTO) {
        List<Date> occupanyDates = new ArrayList<>();
        Map<Date, Integer> actualOnBooks = new HashMap<>();
        Map<Date, Integer> expectedOnbooks = new HashMap<>();
        Map<Date, Integer> expectedOnbooksLowerBound = new HashMap<>();
        Map<Date, Integer> expectedOnbooksUpperBound = new HashMap<>();

        for (ExpectedForecastDetailDTO expectedForecastDetailDTO : expectedForecastDetailDTOS) {
            occupanyDates.add(expectedForecastDetailDTO.getOccupancyDate());
            actualOnBooks.put(expectedForecastDetailDTO.getOccupancyDate(), expectedForecastDetailDTO.getActualOnbooks());
            expectedOnbooks.put(expectedForecastDetailDTO.getOccupancyDate(), expectedForecastDetailDTO.getExpectedOnbooks());
            expectedOnbooksLowerBound.put(expectedForecastDetailDTO.getOccupancyDate(), expectedForecastDetailDTO.getExpectedOnbooksLowerBound());
            expectedOnbooksUpperBound.put(expectedForecastDetailDTO.getOccupancyDate(), expectedForecastDetailDTO.getExpectedOnbooksUpperBound());
        }
        expectedForecastChartDTO.setActualOnBooks(actualOnBooks);
        expectedForecastChartDTO.setExpectedOnbooks(expectedOnbooks);
        expectedForecastChartDTO.setLowerConfidenceIntervals(expectedOnbooksLowerBound);
        expectedForecastChartDTO.setUpperConfidenceIntervals(expectedOnbooksUpperBound);
        setDateDetails(expectedForecastChartDTO, occupanyDates);
    }

    private void setDateDetails(ExpectedForecastChartDTO expectedForecastChartDTO, List<Date> occupanyDates) {
        Optional<Date> tempOccupancyStartDate = occupanyDates.stream().min(Date::compareTo);
        Optional<Date> tempOccupancyEndDate = occupanyDates.stream().max(Date::compareTo);
        expectedForecastChartDTO.setOccupanyStartDate(tempOccupancyStartDate.isPresent() ? tempOccupancyStartDate.get() : null);
        expectedForecastChartDTO.setOccupancyEndDate(tempOccupancyEndDate.isPresent() ? tempOccupancyEndDate.get() : null);
        expectedForecastChartDTO.setOccupanyDates(occupanyDates);
    }

    public String getAvgDOWOfOnBooks(Date occupancyDt) {
        String avgRoomSoldDOW = HIPHEN;
        String dayOfWeek = DateUtil.convertJavaUtilDateToLocalDate(occupancyDt).getDayOfWeek().toString();

        Date occupancyDate = DateUtil.removeTimeFromDate(occupancyDt);
        Date caughtUpDate = dateService.getCaughtUpDate();
        Date businessEndDate = DateUtil.removeTimeFromDate(DateUtil.addDaysToDate(caughtUpDate, -1));
        LocalDate calculatedDOW = DateUtil.convertJavaUtilDateToLocalDate(calculateDOWDate(occupancyDate, caughtUpDate));
        int pacePoint = (int) DateUtil.getDateDiffDays(occupancyDate.getTime(), businessEndDate.getTime());

        List<Object> result = repository.getAvgDOWOfOnBooks(calculatedDOW, dayOfWeek, calculatedDOW.minusDays(pacePoint));
        if (!result.isEmpty()) {
            avgRoomSoldDOW = null == result.get(0) ? HIPHEN : result.get(0) + StringUtils.EMPTY;
        }

        return avgRoomSoldDOW;
    }

    public List<ExpectedForecastDetailForPastSevenDaysDTO> fetchExpectedForecastDetailForPastSevenDays(Date occupancyDt) {

        Date occupancyDate = DateUtil.removeTimeFromDate(occupancyDt);
        Date startDate = DateUtil.addDaysToDate(occupancyDate, -1);
        Date endDate = DateUtil.addDaysToDate(occupancyDate, 1);
        Date businessStartDate = DateUtil.removeTimeFromDate(getMeTheBusinessStartDate());

        return repository.fetchExpectedForecastDetailForPastSevenDays(startDate, endDate, businessStartDate);
    }

    private Date getMeTheBusinessStartDate() {
        return DateUtil.addDaysToDate(DateUtil.addDaysToDate(dateService.getCaughtUpDate(), -1), -PAST_DAYS);
    }

    public Map<Date, String> getAllSpecialEvents(LocalDate selectedOccupancyDate) {
        return specialEventsBuilder.buildSpecialEventsResultsMap(getStartDate(selectedOccupancyDate), getEndDate(selectedOccupancyDate));
    }

    public List<Date> getOccupancyDemandOverrideDates(LocalDate selectedOccupancyDate) {
        List<Date> demandOverrideDatesList = new ArrayList<>();

        demandOverrideDatesList.addAll(getOccupancyDemandOverrides(getStartDate(selectedOccupancyDate), getEndDate(selectedOccupancyDate)));

        demandOverrideDatesList.sort(Comparator.naturalOrder());
        return demandOverrideDatesList.stream().distinct().collect(Collectors.toList());
    }

    public List<Date> getArrivalDemandOverrideDates(LocalDate selectedOccupancyDate) {
        List<Date> demandOverrideDatesList = new ArrayList<>();

        demandOverrideDatesList.addAll(getArrivalDemandOverrides(getStartDate(selectedOccupancyDate), getEndDate(selectedOccupancyDate)));

        demandOverrideDatesList.sort(Comparator.naturalOrder());
        return demandOverrideDatesList.stream().distinct().collect(Collectors.toList());
    }

    private List<Date> getArrivalDemandOverrides(Date startDate, Date endDate) {
        Map<Date, Object> summaryMap = new TreeMap<>();
        List<ArrivalDemandOverride> arrivalDemandOverrides = repository.getArrivalDemandOverrides(startDate, endDate);
        if (arrivalDemandOverrides != null && !arrivalDemandOverrides.isEmpty()) {
            for (ArrivalDemandOverride arrivalDemandOverride : arrivalDemandOverrides) {
                if (!summaryMap.containsKey(arrivalDemandOverride.getArrivalDate())) {
                    summaryMap.put(arrivalDemandOverride.getArrivalDate(), arrivalDemandOverride);
                }
            }
        }
        return new ArrayList<>(summaryMap.keySet());
    }

    private List<Date> getOccupancyDemandOverrides(Date startDate, Date endDate) {
        Map<Date, Object> summaryMap = new TreeMap<>();
        List<OccupancyDemandOverride> occupancyDemandOverrides = repository.getOccupancyDemandOverrides(startDate, endDate);
        // Summarize by removing duplicates for the same day
        if (occupancyDemandOverrides != null && !occupancyDemandOverrides.isEmpty()) {
            for (OccupancyDemandOverride occupancyDemandOverride : occupancyDemandOverrides) {
                if (!summaryMap.containsKey(occupancyDemandOverride.getOccupancyDate())) {
                    summaryMap.put(occupancyDemandOverride.getOccupancyDate(), occupancyDemandOverride);
                }
            }
        }
        return new ArrayList<>(summaryMap.keySet());
    }

    private Date getStartDate(LocalDate date) {
        return com.ideas.tetris.platform.common.utils.dateutil.DateUtil.addDaysToDate(getDateWithoutTime(date), -1);
    }

    private Date getEndDate(LocalDate date) {
        return com.ideas.tetris.platform.common.utils.dateutil.DateUtil.addDaysToDate(getDateWithoutTime(date), 1);
    }

    private Date getDateWithoutTime(LocalDate date) {
        return com.ideas.tetris.platform.common.utils.dateutil.DateUtil.removeTimeFromDate(DateUtil.convertLocalDateToJavaUtilDate(date));
    }

    public ExpectedForecastDetailGraphPastSevenDaysChartDTO fetchExpectedForecastDetailGraphForPastSevenDays(Date occupancyDt) {

        ExpectedForecastDetailGraphPastSevenDaysChartDTO graphDto = new ExpectedForecastDetailGraphPastSevenDaysChartDTO();
        Date occupancyDate = DateUtil.removeTimeFromDate(occupancyDt);
        Date businessEndDate = DateUtil.removeTimeFromDate(DateUtil.addDaysToDate(dateService.getCaughtUpDate(), -1));
        Date dowDate = calculateDOWDate(occupancyDate, dateService.getCaughtUpDate());
        int pacePoint = (int) DateUtil.getDateDiffDays(occupancyDate.getTime(), businessEndDate.getTime());

        populateExpectedForecastDetailGraphPastSevenDaysChartDTO
                (repository.fetchExpectedForecastDetailGraphForPastSevenDays(occupancyDate, pacePoint, dowDate), graphDto, pacePoint);
        return graphDto;
    }

    private Date calculateDOWDate(Date dateToBeConvertedToDow, Date systemDate) {
        if (dateToBeConvertedToDow.before(systemDate)) {
            return dateToBeConvertedToDow;
        } else {
            while (dateToBeConvertedToDow.after(systemDate) || DateUtil.isDateEqualIgnoreTime(dateToBeConvertedToDow, systemDate)) {
                dateToBeConvertedToDow = DateUtil.addDaysToDate(dateToBeConvertedToDow, -7);
            }
        }
        return dateToBeConvertedToDow;
    }

    void populateExpectedForecastDetailGraphPastSevenDaysChartDTO(List<ExpectedForecastDetailGraphForPastSevenDaysDTO> graphDtos, ExpectedForecastDetailGraphPastSevenDaysChartDTO graphDto, int pacePoint) {

        List<Integer> pacePoints = getThePacePoints(pacePoint);
        Map<Integer, Integer> transientOnBooks = new HashMap<>();
        Map<Integer, Integer> stly = new HashMap<>();
        Map<Integer, Integer> st2y = new HashMap<>();
        Map<Integer, Integer> averageDows = new HashMap<>();

        for (ExpectedForecastDetailGraphForPastSevenDaysDTO graphForPastSevenDaysDTO : graphDtos) {
            if (null != graphForPastSevenDaysDTO.getPacePoint()) {
                Integer paceP = graphForPastSevenDaysDTO.getPacePoint();
                transientOnBooks.put(paceP, graphForPastSevenDaysDTO.getTransientOnBooks());
                stly.put(paceP, graphForPastSevenDaysDTO.getStly());
                st2y.put(paceP, graphForPastSevenDaysDTO.getSt2y());
                averageDows.put(paceP, graphForPastSevenDaysDTO.getAverageDow());
            }
        }
        finalizeTheData(pacePoints, transientOnBooks, stly, st2y, averageDows);
        graphDto.setPacePoints(pacePoints);
        graphDto.setTransientOnBooks(transientOnBooks);
        graphDto.setStly(stly);
        graphDto.setSt2y(st2y);
        graphDto.setAverageDow(averageDows);
        graphDto.setOccupancyDate(graphDtos.get(0).getOccupancyDate());
    }

    private void finalizeTheData(List<Integer> pacePoints, Map<Integer, Integer> transientOnBooks, Map<Integer, Integer> stly, Map<Integer, Integer> st2y, Map<Integer, Integer> averageDows) {

        pacePoints.forEach((Integer name) -> {

            if (!transientOnBooks.containsKey(name)) {
                transientOnBooks.put(name, 0);
            }
            if (!stly.containsKey(name)) {
                stly.put(name, 0);
            }
            if (!st2y.containsKey(name)) {
                st2y.put(name, 0);
            }
            if (!averageDows.containsKey(name)) {
                averageDows.put(name, 0);
            }
        });
    }

    private List<Integer> getThePacePoints(int pacePoint) {
        List<Integer> pacePoints = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            pacePoints.add(pacePoint++);
        }
        return pacePoints;
    }
}
