package com.ideas.tetris.pacman.services.forecast.repository;

import com.ideas.tetris.pacman.services.forecast.dto.InhouseExtensionForecastDto;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class InhouseExtensionForecastRepository {
    @Autowired
    private SasDbToolService sasDbToolService;
    public List<InhouseExtensionForecastDto> retrieveInhouseExtensionForecasts(Integer propertyId, LocalDate startDate, LocalDate endDate) {
        String startDt = LocalDateUtils.getSasDate(startDate);
        String endDt = LocalDateUtils.getSasDate(endDate);
        var query = "select forecast_group_id as forecastGroupId, room_category_id as roomCategoryId, occupancy_dt as occupancyDt format best12. , inhouse_extension_sold as inhouseHouseExtensionSold, inhouse_extension_revenue as inhouseExtensionRevenue tenant.inhouse_extension_fcst where occupancyDt between '"+startDt+"'d and '"+endDt+"'d order by forecastGrpId, roomCategoryId, occupancyDt;";
        var sasDbQueryResult = sasDbToolService.executeQueryTenant(propertyId,query);
        if (Objects.isNull(sasDbQueryResult)) {
            return List.of();
        }
        var data = sasDbQueryResult.getData();
        if (Objects.isNull(data)) {
            return List.of();
        }
        return data.stream()
                .map(list -> new InhouseExtensionForecastDto(
                        ((Double)list.get(0)).intValue(),
                        ((Double)list.get(1)).intValue(),
                        LocalDateUtils.toJavaLocalDateFromSasDate(((Double) list.get(2)).longValue()),
                        ((Double)list.get(3)).intValue(),
                        (Double) list.get(4)))
                .collect(Collectors.toList());
    }
}
