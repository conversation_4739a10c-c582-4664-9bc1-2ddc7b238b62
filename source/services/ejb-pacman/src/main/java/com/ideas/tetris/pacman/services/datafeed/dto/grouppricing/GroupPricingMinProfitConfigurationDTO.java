package com.ideas.tetris.pacman.services.datafeed.dto.grouppricing;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ideas.tetris.platform.common.rest.unmarshaller.DateSerializer;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class GroupPricingMinProfitConfigurationDTO implements Serializable {

    private String daysToArrival;
    private String category;
    private BigDecimal minProfitSunday;
    private BigDecimal minProfitMonday;
    private BigDecimal minProfitTuesday;
    private BigDecimal minProfitWednesday;
    private BigDecimal minProfitThursday;
    private BigDecimal minProfitFriday;
    private BigDecimal minProfitSaturday;
    private String seasonName;
    @JsonSerialize(using = DateSerializer.class)
    private Date seasonalStartDate;
    @JsonSerialize(using = DateSerializer.class)
    private Date seasonalEndDate;


    public String getDaysToArrival() {
        return daysToArrival;
    }

    public void setDaysToArrival(String daysToArrival) {
        this.daysToArrival = daysToArrival;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public BigDecimal getMinProfitSunday() {
        return minProfitSunday;
    }

    public void setMinProfitSunday(BigDecimal minProfitSunday) {
        this.minProfitSunday = minProfitSunday;
    }

    public BigDecimal getMinProfitMonday() {
        return minProfitMonday;
    }

    public void setMinProfitMonday(BigDecimal minProfitMonday) {
        this.minProfitMonday = minProfitMonday;
    }

    public BigDecimal getMinProfitTuesday() {
        return minProfitTuesday;
    }

    public void setMinProfitTuesday(BigDecimal minProfitTuesday) {
        this.minProfitTuesday = minProfitTuesday;
    }

    public BigDecimal getMinProfitWednesday() {
        return minProfitWednesday;
    }

    public void setMinProfitWednesday(BigDecimal minProfitWednesday) {
        this.minProfitWednesday = minProfitWednesday;
    }

    public BigDecimal getMinProfitThursday() {
        return minProfitThursday;
    }

    public void setMinProfitThursday(BigDecimal minProfitThursday) {
        this.minProfitThursday = minProfitThursday;
    }

    public BigDecimal getMinProfitFriday() {
        return minProfitFriday;
    }

    public void setMinProfitFriday(BigDecimal minProfitFriday) {
        this.minProfitFriday = minProfitFriday;
    }

    public BigDecimal getMinProfitSaturday() {
        return minProfitSaturday;
    }

    public void setMinProfitSaturday(BigDecimal minProfitSaturday) {
        this.minProfitSaturday = minProfitSaturday;
    }

    public String getSeasonName() {
        return seasonName;
    }

    public void setSeasonName(String seasonName) {
        this.seasonName = seasonName;
    }

    public Date getSeasonalStartDate() {
        return seasonalStartDate;
    }

    public void setSeasonalStartDate(Date seasonalStartDate) {
        this.seasonalStartDate = seasonalStartDate;
    }

    public Date getSeasonalEndDate() {
        return seasonalEndDate;
    }

    public void setSeasonalEndDate(Date seasonalEndDate) {
        this.seasonalEndDate = seasonalEndDate;
    }
}
