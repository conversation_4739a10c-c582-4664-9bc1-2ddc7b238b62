package com.ideas.tetris.pacman.services.utility;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.roa.attribute.service.ROAPropertyAttributeService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.sas.log.SasDbQueryResult;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import static com.ideas.tetris.platform.services.sas.log.SasDbToolService.NO_RESULT_LIMIT;


@Component
@Transactional
public class SasPacmanUtility {

    static final String UPDATE_NEW_FEATURE =
            "UPDATE TENANT.NEW_FEATURE SET feature_status_id = %s WHERE feature_name = ('%s')";
    static final String DELETE_BY_MKT_SEG_IDS = "Delete from {0} where mkt_seg_id in ({1}) ";
    private static final Logger LOGGER = Logger.getLogger(SasPacmanUtility.class);
    private static final String ORIGINAL_INFO = "ORIGINAL_INFO";
    private static final String ORG_RESERVATION_INFO = "ORG_RESERVATION_INFO";
    private static final String[] UPDATE_QUERRIES = new String[]{
            "update tenant.mkt_accom_los_inventory set los = :migrationDate-arrival_DT where arrival_DT " +
                    "<:migrationDate and (arrival_DT + los) > :migrationDate;",
            "update tenant.bde_mkt_accom_los_inventory set los = :migrationDate-arrival_DT where arrival_DT " +
                    "<:migrationDate and (arrival_DT + los) > :migrationDate ;",
            "update tenant.original_info set los = :migrationDate-arrival_DT where :migrationDate between arrival_DT " +
                    "and arrival_DT + los -1 AND RESERVATION_IDENTIFIER LIKE '%000' ;",
            "update tenant.org_reservation_info set los = :migrationDate-arrival_DT where :migrationDate between " +
                    "arrival_DT and arrival_DT + los -1 AND RESERVATION_IDENTIFIER LIKE '%000';",
            "delete from tenant.org_reservation_info where occupancy_DT >= :migrationDate AND RESERVATION_IDENTIFIER " +
                    "LIKE '%000';",
            "update tenant.los_extension_info set los = :migrationDate - arrival_DT where :migrationDate between " +
                    "arrival_DT and arrival_DT + los -1 AND RESERVATION_IDENTIFIER LIKE '%000';"
    };
    @Autowired
	private PropertyService propertyService;
    @Autowired
	private PacmanConfigParamsService configParamsService;
    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;
    @Autowired
	private SasDbToolService sasDbToolService;
    @Autowired
	private ExternalSystemHelper externalSystemHelper;
    @Autowired
	private ROAPropertyAttributeService propertyAttributeService;


    public List<String> setAllPropertyAutoDetectSufficientBookedRTPaceWhenUseBookDataParamIsTrueAndExtSystemOperaOrNGI() {
        List<String> result = new ArrayList<>();
        List<Client> clients = globalCrudService.findByNamedQuery(Client.GET_ACTIVE_CLIENTS);

        clients.forEach(client -> {
            String clientCode = client.getCode();
            List<Property> properties = globalCrudService.findByNamedQuery(Property.GET_ACTIVE_PROPERTY_BY_CLIENT_CODE,
                    QueryParameter.with(Property.PARAM_CLIENT_CODE, clientCode).parameters());

            handlePropertiesForSetAllPropertyAutoDetectSufficientBookedRTPaceWhenUseBookDataParamIsTrueAndExtSystemOperaOrNGI(
                    result, clientCode, properties);
        });
        return result;
    }

    public int updateSasDatasetsPostMigration(String clientCode, Integer propertyId, String propertyCode,
                                              LocalDate date) {
        String sasDate = "'" + LocalDateUtils.getSasDate(date) + "'d";
        return Stream.of(UPDATE_QUERRIES).map(s -> s.replace(":migrationDate", sasDate))
                .peek(s -> LOGGER.info("executing sas query " + s))
                .mapToInt(q -> sasDbToolService.executeUpdate(clientCode, propertyId, propertyCode, q))
                .sum();
    }


    public List<String> setAllPropertyAutoDetectSufficientBookedRTPaceForClient(String clientCode) {
        List<String> result = new ArrayList<>();
        List<Property> properties = globalCrudService.findByNamedQuery(Property.GET_ACTIVE_PROPERTY_BY_CLIENT_CODE,
                QueryParameter.with(Property.PARAM_CLIENT_CODE, clientCode).parameters());
        handlePropertiesForSetAllPropertyAutoDetectSufficientBookedRTPaceWhenUseBookDataParamIsTrueAndExtSystemOperaOrNGI(
                result, clientCode, properties);
        return result;
    }

    public void handlePropertiesForSetAllPropertyAutoDetectSufficientBookedRTPaceWhenUseBookDataParamIsTrueAndExtSystemOperaOrNGI(List<String> result, String clientCode, List<Property> properties) {
        for (Property property : properties) {
            try {
                boolean isExternalSystemOperaOrNGI = isExternalSystemOperaOrNGI(clientCode, property.getCode());
                boolean isLDB = isLimitedDataBuild(clientCode, property.getCode());
                boolean isPropertyOneWayOrAbove = propertyService.isPropertyOneWayAndAbove(property);

                if (!isExternalSystemOperaOrNGI || isLDB || !isPropertyOneWayOrAbove) {
                    continue;
                }

                if (setAutoDetectSufficientBookedRTPaceWhenUseBookDataParamIsTrue(clientCode, property.getId(), property.getCode())) {
                    LOGGER.info("setAllPropertyAutoDetectSufficientBookedRTPace() done for Property:" + property.getCode());
                    result.add(property.getCode());
                }
            } catch (Exception ex) {
                LOGGER.error("Unable to setAllPropertyAutoDetectSufficientBookedRTPaceWhenUseBookDataParamIsTrueAndExtSystemOperaOrNGI for property: " + property.getCode(), ex);
            }
        }
    }

    public boolean isLimitedDataBuild(String clientCode, String propertyCode) {
        return configParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value(), clientCode, propertyCode);
    }

    public boolean isExternalSystemOperaOrNGI(String clientCode, String propertyCode) {
        return externalSystemHelper.isNGI(clientCode, propertyCode) || externalSystemHelper.isOpera(clientCode, propertyCode);
    }


    public boolean setAutoDetectSufficientBookedRTPaceWhenUseBookDataParamIsTrue(String clientCode, Integer propertyId, String propertyCode) {
        boolean autoDetectSufficientBookedRTPace = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AMS_AUTO_DETECT_SUFFICIENT_BOOKED_RTPACE.value(), clientCode, propertyCode);
        if (checkUseBookDataParamIsTrue(clientCode, propertyId, propertyCode) && !autoDetectSufficientBookedRTPace) {
            setAutoDetectSufficientBookedRTPace(clientCode, propertyCode, true);
            return true;
        }
        return false;
    }


    public boolean checkUseBookDataParamIsTrue(String clientCode, Integer propertyId, String propertyCode) {
        final String ipCfgValue = propertyAttributeService.getAttributeValueByAttributeName("use_book_data");

        if (StringUtils.isNotEmpty(ipCfgValue)) {
            return "1".equals(ipCfgValue.trim());
        }
        String query = "select feature_name, feature_status_id from TENANT.NEW_FEATURE " +
                "where (feature_name EQ 'use_book_data'  AND feature_status_id EQ 1 ) ";
        SasDbQueryResult result = sasDbToolService.executeQuery(clientCode, propertyId, propertyCode, query);
        return result.getData() != null && !result.getData().isEmpty();
    }

    public void setAutoDetectSufficientBookedRTPace(String clientCode, String propertyCode, boolean value) {
        String propertyContext = "pacman." + clientCode + "." + propertyCode;
        if (value) {
            configParamsService.addParameterValue(propertyContext, FeatureTogglesConfigParamName.AMS_AUTO_DETECT_SUFFICIENT_BOOKED_RTPACE.value(), "true");
        } else {
            configParamsService.addParameterValue(propertyContext, FeatureTogglesConfigParamName.AMS_AUTO_DETECT_SUFFICIENT_BOOKED_RTPACE.value(), "false");
        }
    }


    public List<String> getPropertiesWithAutoDetectSufficientBookedRTPaceDiscrepancy() {
        List<String> result = new ArrayList<>();
        List<Client> clients = globalCrudService.findByNamedQuery(Client.GET_ACTIVE_CLIENTS);

        clients.forEach(client -> {
            String clientCode = client.getCode();
            List<Property> properties = globalCrudService.findByNamedQuery(Property.GET_ACTIVE_PROPERTY_BY_CLIENT_CODE, QueryParameter.with(Property.PARAM_CLIENT_CODE, clientCode).parameters());

            handlePropertiesForGetPropertiesWithAutoDetectSufficientBookedRTPaceDiscrepancy(result, clientCode, properties);
        });
        return result;
    }


    public List<String> getPropertiesWithAutoDetectSufficientBookedRTPaceDiscrepancyForClient(String clientCode) {
        List<String> result = new ArrayList<>();
        List<Property> properties = globalCrudService.findByNamedQuery(Property.GET_ACTIVE_PROPERTY_BY_CLIENT_CODE, QueryParameter.with(Property.PARAM_CLIENT_CODE, clientCode).parameters());
        handlePropertiesForGetPropertiesWithAutoDetectSufficientBookedRTPaceDiscrepancy(result, clientCode, properties);
        return result;
    }

    public void handlePropertiesForGetPropertiesWithAutoDetectSufficientBookedRTPaceDiscrepancy(List<String> result, String clientCode, List<Property> properties) {
        for (Property property : properties) {
            try {
                boolean isExternalSystemOperaOrNGI = isExternalSystemOperaOrNGI(clientCode, property.getCode());
                boolean isLDB = isLimitedDataBuild(clientCode, property.getCode());
                boolean isPropertyOneWayOrAbove = propertyService.isPropertyOneWayAndAbove(property);
                if (!isExternalSystemOperaOrNGI || isLDB || !isPropertyOneWayOrAbove) {
                    continue;
                }

                boolean autoDetectSufficientBookedRTPace = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AMS_AUTO_DETECT_SUFFICIENT_BOOKED_RTPACE.value(), clientCode, property.getCode());

                if (checkUseBookDataParamIsTrue(clientCode, property.getId(), property.getCode()) && !autoDetectSufficientBookedRTPace) {
                    LOGGER.info("getPropertiesWithAutoDetectSufficientBookedRTPaceDiscrepancy() done for Property:" + property.getCode());
                    result.add(property.getCode());
                }
            } catch (Exception ex) {
                LOGGER.error("Unable to getPropertiesWithAutoDetectSufficientBookedRTPaceDiscrepancy for property: " + property.getCode(), ex);
            }
        }
    }


    public void updateNewFeature(final String clientCode, final int propertyId, final String propertyCode, final String featureName, final String updateValue) {
        if (StringUtils.isNotEmpty(propertyAttributeService.getAttributeValueByAttributeName(featureName))) {
            propertyAttributeService.saveOverrideByAttributeName(featureName, updateValue);
        }
        sasDbToolService.executeUpdate(clientCode, propertyId, propertyCode, String.format(UPDATE_NEW_FEATURE, updateValue, featureName));
    }

    public void updateNewFeatures(final String clientCode, final int propertyId, final String propertyCode, final List<String> featureNames, final String updateValue) {
        for (String featureName : featureNames) {
            updateNewFeature(clientCode, propertyId, propertyCode, featureName, updateValue);
        }
    }

    public int deleteOriginalDatasetsByMarketSegmentIds(final List<Integer> mktSegids, final Integer propertyId) {
        String mktSegs = mktSegids.stream().map(String::valueOf).collect(Collectors.joining(","));
        return deleteOriginalDatasetsByMarketSegmentIds(propertyId, mktSegs);
    }


    public int deleteOriginalDatasetsByMarketSegmentIds(final Integer propertyId, final String mktSegs) {
        final int updatedOriginalInfoRecords = deleteMktSegsFromTableIfTablePresent(mktSegs, propertyId, ORIGINAL_INFO);
        final int updatedOrgReservationRecords = deleteMktSegsFromTableIfTablePresent(mktSegs, propertyId, ORG_RESERVATION_INFO);
        return updatedOriginalInfoRecords + updatedOrgReservationRecords;
    }

    private int deleteMktSegsFromTableIfTablePresent(final String mktSegs, final Integer propertyId,
                                                     final String tableName) {
        if (!isTablePresent(propertyId, tableName)) {
            return 0;
        }
        String deleteQuery = MessageFormat.format(DELETE_BY_MKT_SEG_IDS, SasDbToolService.TENANT_SAS_LIB + "." + tableName, mktSegs);
        return sasDbToolService.executeUpdate(PacmanWorkContextHelper.getClientCode(), propertyId, PacmanWorkContextHelper.getPropertyCode(), deleteQuery);
    }

    private boolean isTablePresent(final Integer propertyId, final String memname) {
        String findTableQuery = "select COUNT(*) as count from dictionary.tables where libname in ('" + SasDbToolService.TENANT_SAS_LIB + "') and memname = '" + memname + "'";
        final SasDbQueryResult countOfTables = sasDbToolService.executeQuery(propertyId, sasDbToolService.getTenantLibRef(propertyId), findTableQuery, NO_RESULT_LIMIT);
        return ((Double) countOfTables.getData().get(0).get(0)) > 0;
    }

    public Integer deleteOriginalDatasetsByMarketSegmentIds(final List<Integer> mktSegs) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        return deleteOriginalDatasetsByMarketSegmentIds(mktSegs, propertyId);
    }
}
