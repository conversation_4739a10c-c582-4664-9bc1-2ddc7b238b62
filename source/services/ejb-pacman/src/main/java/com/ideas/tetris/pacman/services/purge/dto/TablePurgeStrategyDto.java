package com.ideas.tetris.pacman.services.purge.dto;

public class TablePurgeStrategyDto {
    private String tenantPurgeEnumName;
    private String purgeCategoryName;
    private Integer numberOfDaysToPersist;
    private String tableToDelete;
    private String tableToCompare;
    private String fieldToCompare;
    private String whereClauseTemplate;
    private Boolean isPropertyAppliedToWhereClause;
    private String schemaNames;
    private Integer purgePriority;
    private Boolean isPurgingEnabled;
    private String category;

    public TablePurgeStrategyDto(String tenantPurgeEnumName, String purgeCategoryName, Integer numberOfDaysToPersist, Boolean isPurgingEnabled) {
        this.tenantPurgeEnumName = tenantPurgeEnumName;
        this.purgeCategoryName = purgeCategoryName;
        this.numberOfDaysToPersist = numberOfDaysToPersist;
        this.isPurgingEnabled = isPurgingEnabled;
    }

    public TablePurgeStrategyDto(String tenantPurgeEnumName, String purgeCategoryName, Integer numberOfDaysToPersist, String tableToDelete,
                              String tableToCompare, String fieldToCompare, String whereClauseTemplate, Boolean isPropertyAppliedToWhereClause,
                              String schemaNames, Integer purgePriority, Boolean isPurgingEnabled,String category) {
        this.tenantPurgeEnumName = tenantPurgeEnumName;
        this.purgeCategoryName = purgeCategoryName;
        this.numberOfDaysToPersist = numberOfDaysToPersist;
        this.tableToDelete = tableToDelete;
        this.tableToCompare = tableToCompare;
        this.fieldToCompare = fieldToCompare;
        this.whereClauseTemplate = whereClauseTemplate;
        this.isPropertyAppliedToWhereClause = isPropertyAppliedToWhereClause;
        this.schemaNames = schemaNames;
        this.purgePriority = purgePriority;
        this.isPurgingEnabled = isPurgingEnabled;
        this.category = category;
    }

    public TablePurgeStrategyDto() {

    }

    public String getTenantPurgeEnumName() {
        return tenantPurgeEnumName;
    }

    public void setTenantPurgeEnumName(String tenantPurgeEnumName) {
        this.tenantPurgeEnumName = tenantPurgeEnumName;
    }

    public Integer getNumberOfDaysToPersist() {
        return numberOfDaysToPersist;
    }

    public void setNumberOfDaysToPersist(Integer numberOfDaysToPersist) {
        this.numberOfDaysToPersist = numberOfDaysToPersist;
    }

    public String getPurgeCategoryName() {
        return purgeCategoryName;
    }

    public void setPurgeCategoryName(String purgeCategoryName) {
        this.purgeCategoryName = purgeCategoryName;
    }
    public Boolean getIsPurgingEnabled() {return isPurgingEnabled;}

    public void setIsPurgingEnabled(Boolean purgingEnabled) {isPurgingEnabled = purgingEnabled;}

    public String getTableToDelete() {
        return tableToDelete;
    }

    public void setTableToDelete(String tableToDelete) {
        this.tableToDelete = tableToDelete;
    }

    public String getTableToCompare() {
        return tableToCompare;
    }

    public void setTableToCompare(String tableToCompare) {
        this.tableToCompare = tableToCompare;
    }

    public String getFieldToCompare() {
        return fieldToCompare;
    }

    public void setFieldToCompare(String fieldToCompare) {
        this.fieldToCompare = fieldToCompare;
    }

    public String getWhereClauseTemplate() {
        return whereClauseTemplate;
    }

    public void setWhereClauseTemplate(String whereClauseTemplate) {
        this.whereClauseTemplate = whereClauseTemplate;
    }

    public Boolean getIsPropertyAppliedToWhereClause() {
        return isPropertyAppliedToWhereClause;
    }

    public void setPropertyAppliedToWhereClause(Boolean propertyAppliedToWhereClause) {
        isPropertyAppliedToWhereClause = propertyAppliedToWhereClause;
    }

    public String getSchemaNames() {
        return schemaNames;
    }

    public void setSchemaNames(String schemaNames) {
        this.schemaNames = schemaNames;
    }

    public Integer getPurgePriority() {
        return purgePriority;
    }

    public void setPurgePriority(Integer purgePriority) {
        this.purgePriority = purgePriority;
    }

    public Boolean getPurgingEnabled() {
        return isPurgingEnabled;
    }

    public void setPurgingEnabled(Boolean purgingEnabled) {
        isPurgingEnabled = purgingEnabled;
    }

    public String getCategory() {return category;}

    public void setCategory(String category) {this.category = category;}

}
