package com.ideas.tetris.pacman.services.pricing;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.*;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.*;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPBARDecisionDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPRoomClassDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CompetitorInfo;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.PricingCompetitorRates;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.*;
import com.ideas.tetris.pacman.services.businessanalysis.BusinessAnalysisDashboardService;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDailyDataDto;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDailyIndicatorDto;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValue;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.extendedstay.ratemanagement.dto.ProductDTO;
import com.ideas.tetris.pacman.services.pricing.dto.*;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationLTBDEService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.Supplement;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.services.webrate.dto.CompetitorRateInfo;
import com.ideas.tetris.pacman.services.webrate.dto.DCMPCGenericValuesDTO;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateView;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateViewPK;
import com.ideas.tetris.pacman.services.webrate.service.CompetitorRateInfoService;
import com.ideas.tetris.pacman.services.webrate.service.DynamicCMPCService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.HttpStatus;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.core.Response;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED;
import static com.ideas.tetris.pacman.services.webrate.entity.WebrateView.PARAM_COMPETITOR_IDS;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.toJavaLocalDate;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;

@Component
@Transactional
public class ProductManagementService {

    private static final Logger LOGGER = Logger.getLogger(ProductManagementService.class.getName());

    private static final String PROPERTY_ID = "propertyId";
    private static final String START_DATE = "startDate";
    private static final String END_DATE = "endDate";
    private static final String ROOM_CLASS_ID = "roomClassId";
    private static final String IS_PHYSICAL_CAPACITY_ENABLED = "isPhysicalCapacityEnabled";
    private static final String INCLUDE_ZERO_CAPACITY_ROOM_TYPE = "includeZeroCapacityRT";
    public  static final String DEFAULT = "DEFAULT_VALUES";
    private static final Response SUCCESS_RESPONSE = Response.status(HttpStatus.SC_OK)
            .entity("SUCCESS")
            .build();
    public static final String VW_QUERY_TO_FETCH_PRODUCT_ID = "select WebRate_ID, Product_ID from Vw_Webrate_full " +
            "where Occupancy_DT = :selectedDate and Product_Id in (:productIds)";

    public static final String VW_QUERY_V2_TO_FETCH_COMPETITOR_RATE_FOR_PRODUCT = "SELECT vw.* from VW_Webrate_V2_full vw " +
            "JOIN Vw_Webrate_Channel_Product_ID_col wcp ON vw.Product_ID = wcp.Product_ID and vw.Occupancy_DT = wcp.Occupancy_DT " +
            "WHERE vw.Product_ID in (:productIds) AND vw.Webrate_Competitors_ID in (:competitorIds) \n" +
            "AND vw.Occupancy_DT BETWEEN :startDate AND :endDate " +
            "AND vw.Webrate_Channel_ID = wcp.Channel_ID";

    public static final String GET_ALL_CHILD_LINKED_PRODUCTS = ";WITH DependentProducts AS (\n" +
            "    SELECT *\n" +
            "    FROM product p\n" +
            "    WHERE p.Product_id = :productId  \n" +
            "    UNION ALL\n" +
            "    SELECT  child.*\n" +
            "    FROM product child\n" +
            "    INNER JOIN DependentProducts parent ON child.Dependent_Product_ID = parent.Product_id\n" +
            ")\n" +
            "SELECT * \n" +
            "FROM DependentProducts\n" +
            "WHERE Product_id != :productId \n" +
            "ORDER BY  Product_id;\n";

    public static final String SELECTED_DATE = "selectedDate";
    public static final String PRODUCT_IDS = "productIds";

    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;
    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;

    @Autowired
    AccommodationService accommodationService;

    @Autowired
	private CompetitorRateInfoService competitorRateInfoService;

    @Autowired
	private DateService dateService;

    @Autowired
	private BusinessAnalysisDashboardService businessAnalysisDashboardService;

    @Autowired
	private BarDecisionService barDecisionService;

    @Autowired
	private SyncEventAggregatorService syncEventAggregatorService;

    @Autowired
	private DecisionService decisionService;

    @Autowired
	private AgileRatesConfigurationService agileRatesConfigurationService;

    @Autowired
	private PricingConfigurationService pricingConfigurationService;

    @Autowired
	private PricingConfigurationLTBDEService pricingConfigurationLTBDEService;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
	private TaxService taxService;

    @Autowired
	private AccomTypeSupplementService accomTypeSupplementService;

    @Autowired
    private DynamicCMPCService dynamicCMPCService;

    @Autowired
    PrettyPricingService prettyPricingService;

    public List<CPDecisionBAROutput> getDecisionBarOutputForDate(LocalDate date) {
        Product product = crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        return crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_FOR_DATE, CPDecisionBAROutput.params(product, date));
    }

    @SuppressWarnings("unchecked")
    public List<CPDecisionBAROutput> getCPDecisionsBetweenDatesForProductAndRoomTypes(Set<Product> products, LocalDate startDate, LocalDate endDate, List<AccomType> accomTypes) {
        return crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES_PRODUCTS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("products", products)
                        .and("startDate", startDate)
                        .and("endDate", endDate)
                        .and("accomTypes", accomTypes).parameters());
    }

    public LocalDate getMaxDecisionDate() {
        return crudService.findByNamedQuerySingleResult(CPDecisionBAROutput.MAX_DECISION_DATE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<CPDecisionBARNOVRDetails> getCPDecisionsNOVRBetweenDatesForProductAndRoomTypes(Set<Product> products, LocalDate startDate, LocalDate endDate, List<AccomType> accomTypes) {
        return crudService.findByNamedQuery(CPDecisionBARNOVRDetails.GET_NOVR_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES_PRODUCTS,
                QueryParameter.with("products", products)
                        .and("startDate", startDate)
                        .and("endDate", endDate)
                        .and("accomTypes", accomTypes).parameters());
    }

    public List<ProductRateOffsetOverride> getProductRateOffsetOverrides(Set<Product> products, LocalDate startDate, LocalDate endDate) {
        return crudService.findByNamedQuery(ProductRateOffsetOverride.BY_PRODUCT_BETWEEN_DATES, QueryParameter.with("products", products).and("startDate", startDate).and("endDate", endDate).parameters());
    }

    public List<ProductRateOffsetOverride> getActiveProductRateOffsetOverrides(Product product, LocalDate startDate, LocalDate endDate) {
        return crudService.findByNamedQuery(ProductRateOffsetOverride.ACTIVE_BY_PRODUCT_BETWEEN_DATES, QueryParameter.with("product", product).and("startDate", startDate).and("endDate", endDate).parameters());
    }

    public List<AccomClass> getSortedAccomClasses(boolean isPricingScreenOptimizationEnabled) {
        if(isPricingScreenOptimizationEnabled){
            return accommodationService.getAssignedAccomClassesByViewOrderWithAccomTypes();
        }
        return accommodationService.getAssignedAccomClassesByViewOrder();
    }

    public List<AccomType> getBaseAccomTypes() {
        List<PricingAccomClass> pricingBaseAccomTypes = crudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        return pricingBaseAccomTypes.stream().map(PricingAccomClass::getAccomType).collect(toList());
    }

    public List<Product> findAllActiveProducts() {
        return crudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_AGILE_RATES_INDEPENDENT_SMALL_GROUP_PRODUCTS_BY_STATUS, QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
    }

    public List<Product> findAllAgileProductsWithAdvancePurchase() {
        return crudService.findByNamedQuery(Product.GET_ACTIVE_AGILE_PRODUCTS_WITH_ADVANCE_PURCHASE);
    }

    public List<Product> findSystemDefaultAndAllActiveLinkedAndSmallGroupProducts() {
        return crudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_AGILE_RATES_SMALL_GROUP_PRODUCTS_BY_STATUS, QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
    }

    public List<Product> findActiveSmallGroupProducts(Integer propertyId) {
        return multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, Product.GET_BY_CODE, QueryParameter.with("code", Product.GROUP_PRODUCT_CODE).parameters());
    }

    public List<Product> findSystemDefaultAndAllActiveLinkedAndIndependentProducts() {
        return crudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_AGILE_RATES_INDEPENDENT_PRODUCTS_BY_STATUS, QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
    }

    public List<Product> findSystemDefaultAndAllActiveLinkedAndIndependentProducts(Integer propertyId) {
        return (List<Product>) multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, Product.GET_SYSTEM_DEFAULT_AGILE_RATES_INDEPENDENT_PRODUCTS_BY_STATUS,
                QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
    }

    public List<Product> findSystemDefaultAndAllActiveLinkedProducts() {
        return crudService.findByNamedQuery(Product.GET_ACTIVE_SYSTEM_DEFAULT_OR_AGILE_RATES, QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
    }

    public List<Product> findSystemDefaultAndAllActiveLinkedProducts(Integer propertyId) {
        return (List<Product>) multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, Product.GET_ACTIVE_SYSTEM_DEFAULT_OR_AGILE_RATES,
                QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
    }

    public List<LastRoomValue> getLastRoomValues(Date startDate, Date endDate) {
        return crudService.findByNamedQuery(LastRoomValue.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(START_DATE, startDate).and(END_DATE, endDate).parameters());
    }

    public List<CompetitorRateInfo> getCompetitorRates(Date occupancyDate, int accomClass, boolean isCompetitiveMarketPositioningEnabled,
                                                       List<Integer> productIds) {
        boolean isDCMPCEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_OCCUPANCY_BASED_DCMPC_TAB);
        boolean isShowOccupancyBasedSettingOnCompetitorUIEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.SHOW_OCCUPANCY_BASED_SETTING_ON_COMPETITOR_UI);

        List<Integer> accomClasses = buildAccomClassIDs(accomClass);

        List<CompetitorRateInfo> competitorRateInfoList = competitorRateInfoService.getCompetitorRatesForAccomClassList(occupancyDate, accomClasses, isCompetitiveMarketPositioningEnabled, productIds);
        String userPreferredDateFormat = dateService.getUserPreferredDateFormat();
        List<DCMPCGenericValuesDTO> dynamicCMPCValues;
        int dynamicCMPCDOW;
        BigDecimal occupancyPercentage;
        if(isShowOccupancyBasedSettingOnCompetitorUIEnabled && isDCMPCEnabled){
            occupancyPercentage = accommodationService.getOccupancyPercentage(JavaLocalDateUtils.fromDate(occupancyDate));
            dynamicCMPCValues =  dynamicCMPCService.fetchDefaultAndSeasonValuesFirstHigherToOccupancyPercentage(productIds, accomClasses, JavaLocalDateUtils.fromDate(occupancyDate), occupancyPercentage);
            dynamicCMPCDOW = JavaLocalDateUtils.getDayOfWeek(occupancyDate);
        } else {
            occupancyPercentage = null;
            dynamicCMPCDOW = -1;
            dynamicCMPCValues = null;
        }

        competitorRateInfoList.forEach(rateInfo -> {
            rateInfo.setRate(null != rateInfo.getRate() ? rateInfo.getRate().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            rateInfo.setLastUpdatedDate(DateUtil.formatDate(rateInfo.getWebrateGenerationDate().getTime(), userPreferredDateFormat));
            if(isShowOccupancyBasedSettingOnCompetitorUIEnabled && isDCMPCEnabled && dynamicCMPCValues != null){
                Optional<DCMPCGenericValuesDTO> matchedRecord = dynamicCMPCValues.stream().filter(dto -> (dto.getAccomClassName() != null && dto.getAccomClassName().equalsIgnoreCase(rateInfo.getAccomClassName()) && dto.getDowId() == dynamicCMPCDOW)).findFirst();
                matchedRecord.ifPresent(dcmpcGenericValuesDTO -> {
                    rateInfo.setDcmpcMaxMarketPercentile(dcmpcGenericValuesDTO.getMaxPercentile());
                    rateInfo.setDcmpcOnBooksThresholdPercent(dcmpcGenericValuesDTO.getOnBooksThresholdPercent());
                });
            }
            rateInfo.setOccupancyPercentage(occupancyPercentage);
        });

        return competitorRateInfoList;
    }

    public List<CompetitorRateInfo> getCompetitorRatesForDateRange(Date fromDate, Date toDate, int accomClass, boolean isCompetitiveMarketPositioningEnabled) {
        List<Integer> accomClasses = buildAccomClassIDs(accomClass);

        List<CompetitorRateInfo> competitorRateInfoList = competitorRateInfoService.getCompetitorRatesForAccomClassListForDateRange(fromDate, toDate, accomClasses, isCompetitiveMarketPositioningEnabled);
        String userPreferredDateFormat = dateService.getUserPreferredDateFormat();

        competitorRateInfoList.forEach(rateInfo -> {
            rateInfo.setRate(null != rateInfo.getRate() ? rateInfo.getRate().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            rateInfo.setLastUpdatedDate(DateUtil.formatDate(rateInfo.getWebrateGenerationDate().getTime(), userPreferredDateFormat));
        });

        return competitorRateInfoList;
    }

    public List<CPBARDecisionDTO> searchForBarDecisionDTO(boolean isCpBaseRoomTypeOnly, PricingManagementCPSearchCriteria searchCriteria, boolean isAllRoomClassesWithBaseRoomType, PricingAccomClass masterPricingAccomClass, Date caughtUpDate) {
        List<CPBARDecisionDTO> dtos = new ArrayList<>();
        boolean isBaseRoomTypeOnly = isCpBaseRoomTypeOnly || isAllRoomClassesWithBaseRoomType;
        Map<LocalDate, List<CPDecisionBAROutput>> dateMap = new LinkedHashMap<>();
        List<BusinessAnalysisDailyDataDto> businessAnalysisDailyDataDtos;
        List<BusinessAnalysisDailyIndicatorDto> businessAnalysisDailyIndicatorDtos;
        Map<Date, CompetitorInfo> competitorInfoMap = new HashMap<>();
        List<LastRoomValue> lastRoomValues = new ArrayList<>();

        List<CPDecisionBAROutput> outputs = search(searchCriteria);
        Date startDate = searchCriteria.getStartDate().toDate();
        Date endDate = searchCriteria.getEndDate().toDate();

        outputs.forEach(output -> {
            LocalDate arrivalDate = output.getArrivalDate();
            if (dateMap.get(arrivalDate) == null) {
                List<CPDecisionBAROutput> barcpOutputs = new ArrayList<>();
                barcpOutputs.add(output);
                dateMap.put(arrivalDate, barcpOutputs);
            } else {
                dateMap.get(arrivalDate).add(output);
            }
        });

        if (!dateMap.isEmpty()) {
            businessAnalysisDailyDataDtos = getBusinessAnalysisDailyDataDtos(masterPricingAccomClass, caughtUpDate, startDate, endDate);
            businessAnalysisDailyIndicatorDtos = businessAnalysisDashboardService.getBusinessAnalysisDailyIndicatorDtos(startDate, endDate);

            if (searchCriteria.getRoomClass() != null) {
                Integer accomClassId = searchCriteria.getRoomClass().getId();
                lastRoomValues = barDecisionService.getLastRoomValues(accomClassId, startDate, endDate);
                //Length of stay of 1
                competitorInfoMap = barDecisionService.getCompetitorInfo(accomClassId, 1, startDate, endDate);
            }

            dtos.addAll(createCpBarDecisionDtos(isBaseRoomTypeOnly, dateMap, businessAnalysisDailyDataDtos, businessAnalysisDailyIndicatorDtos, competitorInfoMap, lastRoomValues));
        }

        if (isBaseRoomTypeOnly) {
            addLrvAndCompetitorRateInfoToBaseRoomType(outputs, startDate, endDate, lastRoomValues);
        }

        return dtos;
    }

    public List<BusinessAnalysisDailyDataDto> getBusinessAnalysisDailyDataDtos(PricingAccomClass masterPricingAccomClass, Date caughtUpDate, Date startDate, Date endDate) {
        List<BusinessAnalysisDailyDataDto> businessAnalysisDailyDataDtos;
        if (SystemConfig.usePerformanceImprovementChangesPricingMultiProductDisplayChanges()) {
            businessAnalysisDailyDataDtos = businessAnalysisDashboardService.getBusinessAnalysisDailyDataDtos(startDate, endDate, masterPricingAccomClass, caughtUpDate);
        } else {
            businessAnalysisDailyDataDtos = businessAnalysisDashboardService.getBusinessAnalysisDailyDataDtos(startDate, endDate);
        }
        return businessAnalysisDailyDataDtos;
    }
    public PricingAccomClass getMasterPricingAccomClass() {
        AccomClass masterAccomClass = businessAnalysisDashboardService.getMasterAccomClass();
        return businessAnalysisDashboardService.getPricingAccomClass(masterAccomClass);
    }

    public Map<LocalDate, CPRoomClassDTO> getRoomClassDetails(Integer roomClassId, LocalDate startDate, LocalDate endDate, boolean optimizationEnabled) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        int isPhysicalCapacityEnabled = isPhysicalCapacityEnabled();

        String procedure = "[dbo].[" + (optimizationEnabled ? "usp_pricing_get_by_rc_optimized" : "usp_pricing_get_by_rc") + "] :propertyId, :roomClassId, :startDate, :endDate, :isPhysicalCapacityEnabled, :includeZeroCapacityRT";

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(PROPERTY_ID, propertyId);
        parameters.put(ROOM_CLASS_ID, roomClassId);
        parameters.put(START_DATE, startDate.toDate());
        parameters.put(END_DATE, endDate.toDate());
        parameters.put(IS_PHYSICAL_CAPACITY_ENABLED, isPhysicalCapacityEnabled);
        parameters.put(INCLUDE_ZERO_CAPACITY_ROOM_TYPE, 0);

        RowMapper<CPRoomClassDTO> rowMapper = row -> {
            CPRoomClassDTO dto = new CPRoomClassDTO();
            dto.setDate(new LocalDate(row[0]));
            dto.setOutOfOrder((BigDecimal) row[1]);
            dto.setRoomsOnBooks((BigDecimal) row[2]);
            dto.setOccupancyForecast((BigDecimal) row[3]);
            dto.setOverbooking((BigDecimal) row[4]);
            dto.setCapacity((BigDecimal) row[5]);
            return dto;
        };

        List<CPRoomClassDTO> dtos = crudService.findByNativeQuery(procedure, parameters, rowMapper);
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyMap();
        }
        return dtos.stream().collect(Collectors.toMap(CPRoomClassDTO::getDate, dto -> dto));
    }

    public Map<LocalDate, Map<Integer, CPRoomClassDTO>> getAllRoomClassDetails(String roomClassId, LocalDate startDate, LocalDate endDate) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        int isPhysicalCapacityEnabled = isPhysicalCapacityEnabled();

        String procedure = "[dbo].[usp_pricing_get_by_all_rc] :propertyId, :roomClassId, :startDate, :endDate, :isPhysicalCapacityEnabled, :includeZeroCapacityRT";

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(PROPERTY_ID, propertyId);
        parameters.put(ROOM_CLASS_ID, roomClassId);
        parameters.put(START_DATE, startDate.toDate());
        parameters.put(END_DATE, endDate.toDate());
        parameters.put(IS_PHYSICAL_CAPACITY_ENABLED, isPhysicalCapacityEnabled);
        parameters.put(INCLUDE_ZERO_CAPACITY_ROOM_TYPE, 0);

        RowMapper<CPRoomClassDTO> rowMapper = row -> {
            CPRoomClassDTO dto = new CPRoomClassDTO();
            dto.setDate(new LocalDate(row[0]));
            dto.setOutOfOrder((BigDecimal) row[1]);
            dto.setRoomsOnBooks((BigDecimal) row[2]);
            dto.setOccupancyForecast((BigDecimal) row[3]);
            dto.setOverbooking((BigDecimal) row[4]);
            dto.setCapacity((BigDecimal) row[5]);
            dto.setAccomClassId((Integer) row[6]);
            return dto;
        };

        List<CPRoomClassDTO> dtos = crudService.findByNativeQuery(procedure, parameters, rowMapper);
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyMap();
        }

        return dtos.stream().collect(Collectors.groupingBy(
                CPRoomClassDTO::getDate,
                Collectors.toMap(CPRoomClassDTO::getAccomClassId, dto -> dto)));

    }

    public Map<LocalDate, Map<Integer, CPRoomClassDTO>> getAllRoomTypeDetails(Integer roomClassId, LocalDate startDate, LocalDate endDate) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        int isPhysicalCapacityEnabled = isPhysicalCapacityEnabled();

        String procedure = "[dbo].[usp_pricing_get_by_all_rt] :propertyId, :roomClassId, :startDate, :endDate, :isPhysicalCapacityEnabled, :includeZeroCapacityRT";

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(PROPERTY_ID, propertyId);
        parameters.put(ROOM_CLASS_ID, roomClassId);
        parameters.put(START_DATE, startDate.toDate());
        parameters.put(END_DATE, endDate.toDate());
        parameters.put(IS_PHYSICAL_CAPACITY_ENABLED, isPhysicalCapacityEnabled);
        parameters.put(INCLUDE_ZERO_CAPACITY_ROOM_TYPE, 0);

        RowMapper<CPRoomClassDTO> rowMapper = row -> {
            CPRoomClassDTO dto = new CPRoomClassDTO();
            dto.setDate(new LocalDate(row[0]));
            dto.setOutOfOrder((BigDecimal) row[1]);
            dto.setRoomsOnBooks((BigDecimal) row[2]);
            dto.setOverbooking((BigDecimal) row[3]);
            dto.setCapacity((BigDecimal) row[4]);
            dto.setAccomClassId((Integer) row[5]);
            dto.setAccomTypeId((Integer) row[6]);
            return dto;
        };

        List<CPRoomClassDTO> dtos = crudService.findByNativeQuery(procedure, parameters, rowMapper);
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyMap();
        }

        return dtos.stream().collect(Collectors.groupingBy(
                CPRoomClassDTO::getDate,
                Collectors.toMap(CPRoomClassDTO::getAccomTypeId, dto -> dto)));
    }

    public int isPhysicalCapacityEnabled() {
        return configParamsService.isEnablePhysicalCapacityConsideration() ? 1 : 0;
    }


    public Product getSystemDefaultProduct() {
        return crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
    }

    private List<CPBARDecisionDTO> createCpBarDecisionDtos(boolean isBaseRoomTypeOnly, Map<LocalDate, List<CPDecisionBAROutput>> dateMap, List<BusinessAnalysisDailyDataDto> businessAnalysisDailyDataDtos, List<BusinessAnalysisDailyIndicatorDto> businessAnalysisDailyIndicatorDtos, Map<Date, CompetitorInfo> competitorInfoMap, List<LastRoomValue> lastRoomValues) {
        List<CPBARDecisionDTO> dtos = new ArrayList<>();

        dateMap.keySet().forEach(date -> {
            CPBARDecisionDTO decisionCPDTO = new CPBARDecisionDTO();
            decisionCPDTO.setDate(date);
            decisionCPDTO.setDecisions(dateMap.get(date));

            BusinessAnalysisDailyDataDto dailyDataDto = getDailyDataDTO(date, businessAnalysisDailyDataDtos);
            if (dailyDataDto != null) {
                decisionCPDTO.setOccupancyForecast(dailyDataDto.getOccupancyForecast());
                decisionCPDTO.setOccupancyForecastPercentage(dailyDataDto.getOccupancyForecastPerc());
                decisionCPDTO.setOutOfOrder(dailyDataDto.getOutOfOrder());
                decisionCPDTO.setRoomsOnBooks(dailyDataDto.getOnBooks());
                decisionCPDTO.setOnBooksPerc(dailyDataDto.getOnBooksPerc());
                decisionCPDTO.setAvailableCapacityToSell(calculateCapacityToSell(dailyDataDto));
                decisionCPDTO.setEffectiveCapacity(dailyDataDto.getEffectiveCapacity());
                decisionCPDTO.setAuthorizedCapacity(dailyDataDto.getAuthorizedCapacity());
            }

            if (!isBaseRoomTypeOnly) {
                LastRoomValue lastRoomValue = lastRoomValues.stream().filter(lrv -> lrv.getOccupancyDate().equals(date.toDate())).findFirst().orElse(null);
                if (lastRoomValue != null) {
                    decisionCPDTO.setLrv(lastRoomValue.getValue());
                }

                decisionCPDTO.setCompetitorRate(getCompetitorRate(competitorInfoMap, date));
            }

            addSpecialEvents(businessAnalysisDailyIndicatorDtos, date, decisionCPDTO);

            dtos.add(decisionCPDTO);
        });

        return dtos;
    }

    public void addLrvAndCompetitorRateInfoToBaseRoomType(List<CPDecisionBAROutput> cpBARDecisionOutputs, Date startDate, Date endDate, List<LastRoomValue> lastRoomValues) {
        Set<Integer> roomClassIds = new HashSet<>();

        roomClassIds.addAll(cpBARDecisionOutputs.stream().map(cpDecisionBAROutput -> cpDecisionBAROutput.getAccomType().getAccomClass().getId()).collect(Collectors.toSet()));
        Map<Integer, Map<Date, CompetitorInfo>> allCompetitorInfoMap = barDecisionService.getCompetitorInfoMap(1, startDate, endDate);
        // Build up LRV and Rate Maps per Room Class
        for (Integer roomClassId : roomClassIds) {
            List<CPDecisionBAROutput> outputsPerRoomClass = new ArrayList<>();
            Map<Date, BigDecimal> lrvMap = getLastRoomValue(roomClassId, lastRoomValues);
            Map<Date, CompetitorInfo> competitorInfoMap = allCompetitorInfoMap.get(roomClassId);

            // Get All Decision Bar Outputs for Room Class
            outputsPerRoomClass.addAll(cpBARDecisionOutputs.stream().filter(outPut -> (outPut.getAccomType().getAccomClass()).getId().intValue() == roomClassId.intValue()).collect(toList()));

            // Add LRV and Competitor Price to CP Decision Bar Output by date
            for (CPDecisionBAROutput barOutput : outputsPerRoomClass) {
                barOutput.setLrv(lrvMap.get(barOutput.getArrivalDate().toDate()));
                if (competitorInfoMap != null && competitorInfoMap.get(barOutput.getArrivalDate().toDate()) != null) {
                    barOutput.setCompetitorRate(competitorInfoMap.get(barOutput.getArrivalDate().toDate()).getCompetitorPrice());
                }
            }
        }
    }

    public void addLRVInfoToDecisions(List<CPDecisionBAROutput> cpBARDecisionOutputs, List<LastRoomValue> lastRoomValues, Set<Integer> roomClassIds) {
        // Build up LRV Maps per Room Class
        for (Integer roomClassId : roomClassIds) {
            List<CPDecisionBAROutput> outputsPerRoomClass = new ArrayList<>();
            Map<Date, BigDecimal> lrvMap = getLastRoomValue(roomClassId, lastRoomValues);

            // Get All Decision Bar Outputs for Room Class
            outputsPerRoomClass.addAll(cpBARDecisionOutputs.stream().filter(outPut -> (outPut.getAccomType().getAccomClass()).getId().intValue() == roomClassId.intValue()).collect(toList()));

            // Add LRV and Competitor Price to CP Decision Bar Output by date
            for (CPDecisionBAROutput barOutput : outputsPerRoomClass) {
                barOutput.setLrv(lrvMap.get(barOutput.getArrivalDate().toDate()));
            }
        }
    }

    public void addCompetitorRateInfoToDecisions(List<CPDecisionBAROutput> cpBARDecisionOutputs, Date startDate, Date endDate, Set<Integer> roomClassIds) {
        Map<Integer, Map<Date, Map<Integer, CompetitorInfo>>> allCompetitorInfoMap = barDecisionService.getCompetitorInfoMapDayCardLayout(1, startDate, endDate);
        // Build up Rate Map per Room Class
        for (Integer roomClassId : roomClassIds) {
            List<CPDecisionBAROutput> outputsPerRoomClass = new ArrayList<>();
            Map<Date, Map<Integer, CompetitorInfo>> competitorInfoMapAccomClass = allCompetitorInfoMap.get(roomClassId);

            // Get All Decision Bar Outputs for Room Class
            outputsPerRoomClass.addAll(cpBARDecisionOutputs.stream()
                    .filter(outPut -> (outPut.getAccomType().getAccomClass()).getId().intValue() == roomClassId.intValue())
                    .collect(toList()));

            // Add LRV and Competitor Price to CP Decision Bar Output by date
            for (CPDecisionBAROutput barOutput : outputsPerRoomClass) {
                Integer productId = barOutput.getProduct().getId();
                if (competitorInfoMapAccomClass != null &&
                        competitorInfoMapAccomClass.get(barOutput.getArrivalDate().toDate()) != null &&
                        competitorInfoMapAccomClass.get(barOutput.getArrivalDate().toDate()).get(productId) != null) {
                    CompetitorInfo competitorInfo = competitorInfoMapAccomClass.get(barOutput.getArrivalDate().toDate()).get(productId);
                    if (competitorInfo != null && productId.equals(competitorInfo.getProductId().intValue())) {
                        barOutput.setCompetitorRate(competitorInfo.getCompetitorPrice());
                    }
                }
            }
        }
    }

    public void addCompetitorRateInfoToDecisions(List<CPDecisionBAROutput> cpBARDecisionOutputs, Date startDate, Date endDate, Set<Integer> roomClassIds, Set<Integer> lengthOfStays) {
        Map<Integer, Map<Date, Map<Integer, CompetitorInfo>>> allCompetitorInfoMap = barDecisionService.getCompetitorInfoMapDayCardLayout(lengthOfStays, startDate, endDate);
        // Build up Rate Map per Room Class
        for (Integer roomClassId : roomClassIds) {
            List<CPDecisionBAROutput> outputsPerRoomClass = new ArrayList<>();
            Map<Date, Map<Integer, CompetitorInfo>> competitorInfoMapAccomClass = allCompetitorInfoMap.get(roomClassId);

            // Get All Decision Bar Outputs for Room Class
            outputsPerRoomClass.addAll(cpBARDecisionOutputs.stream()
                    .filter(outPut -> (outPut.getAccomType().getAccomClass()).getId().intValue() == roomClassId.intValue())
                    .collect(toList()));

            // Add LRV and Competitor Price to CP Decision Bar Output by date
            for (CPDecisionBAROutput barOutput : outputsPerRoomClass) {
                Integer productId = barOutput.getProduct().getId();
                if (competitorInfoMapAccomClass != null &&
                        competitorInfoMapAccomClass.get(barOutput.getArrivalDate().toDate()) != null &&
                        competitorInfoMapAccomClass.get(barOutput.getArrivalDate().toDate()).get(productId) != null) {
                    CompetitorInfo competitorInfo = competitorInfoMapAccomClass.get(barOutput.getArrivalDate().toDate()).get(productId);
                    if (competitorInfo != null && productId.equals(competitorInfo.getProductId().intValue())) {
                        barOutput.setCompetitorRate(competitorInfo.getCompetitorPrice());
                    }
                }
            }
        }
    }

    private Map<Date, BigDecimal> getLastRoomValue(Integer roomClassId, List<LastRoomValue> lastRoomValues) {
        List<LastRoomValue> lrvs = lastRoomValues.stream().filter(lastRoomValue -> lastRoomValue.getAccomClassID().equals(roomClassId)).collect(toList());
        return lrvs.stream().collect(Collectors.toMap(key -> key.getOccupancyDate(), value -> value.getValue(), (val1, val2) -> val2));
    }

    private BusinessAnalysisDailyDataDto getDailyDataDTO(LocalDate date, List<BusinessAnalysisDailyDataDto> dtos) {
        BusinessAnalysisDailyDataDto dto = null;

        Date javaDate = date.toDate();
        for (BusinessAnalysisDailyDataDto businessAnalysisDailyDataDto : dtos) {
            if (DateParameter.fromDate(javaDate).equals(businessAnalysisDailyDataDto.getDate())) {
                dto = businessAnalysisDailyDataDto;
                break;
            }
        }

        return dto;
    }

    private BusinessAnalysisDailyIndicatorDto getDailyIndicatorDTO(LocalDate date, List<BusinessAnalysisDailyIndicatorDto> dtos) {
        BusinessAnalysisDailyIndicatorDto dto = null;

        Date javaDate = date.toDate();
        for (BusinessAnalysisDailyIndicatorDto businessAnalysisDailyIndicatorDto : dtos) {
            if (DateParameter.fromDate(javaDate).equals(businessAnalysisDailyIndicatorDto.getDate())) {
                dto = businessAnalysisDailyIndicatorDto;
                break;
            }
        }

        return dto;
    }

    public List<CPDecisionBAROutput> search(PricingManagementCPSearchCriteria searchCriteria) {
        return crudService.findByCriteria(searchCriteria);
    }

    public void save(List<CPDecisionBAROutput> cpDecisionBAROutputs) {
        crudService.save(cpDecisionBAROutputs);
    }


    public void saveOverrideForCPDecision(CPDecisionContext cpDecisionContext, CPDecisionBAROutputOverride override, CPDecisionBAROutput decisionBARCPOutput, boolean applyBaseOverrideAcrossRoomTypes) {
        // Create a new Decision for the override
        Integer decisionId = decisionService.createBAROverrideDecision().getId();

        saveOverrideForCPDecision(cpDecisionContext, override, decisionBARCPOutput, decisionId, applyBaseOverrideAcrossRoomTypes);
    }

    public void saveOverrideForCPDecision(CPDecisionContext cpDecisionContext, CPDecisionBAROutputOverride override, CPDecisionBAROutput decisionBARCPOutput, boolean applyBaseOverrideAcrossRoomTypes, Date caughtUpDate, Date businessDate, Date rateUnqualifiedCaughtUpDate, Date webRateShoppingDate) {
        // Create a new Decision for the override
        Integer decisionId = decisionService.createBAROverrideDecision(caughtUpDate, businessDate, rateUnqualifiedCaughtUpDate, webRateShoppingDate).getId();

        saveOverrideForCPDecision(cpDecisionContext, override, decisionBARCPOutput, decisionId, applyBaseOverrideAcrossRoomTypes);
    }

    public void saveOverrideForCPDecisions(CPDecisionContext cpDecisionContext,
                                           Map<CPDecisionBAROutput, CPDecisionBAROutputOverride> overrideMap,
                                           Date caughtUpDate, Date businessDate,
                                           Date rateUnqualifiedCaughtUpDate, Date webRateShoppingDate) {
        // Create a new Decision for the override
        Collection<Decision> barOverrideDecision = decisionService.createBAROverrideDecisions(caughtUpDate, businessDate, rateUnqualifiedCaughtUpDate, webRateShoppingDate, overrideMap.size());

        List<CPDecisionBAROutput> cpDecisionBAROutputList = overrideMap
                .keySet()
                .stream()
                .collect(toList());
        int index = 0;
        boolean floorCeilingOverrideChangedSync = false;
        for (Decision decision : barOverrideDecision) {
            CPDecisionBAROutput cpDecisionBAROutput = cpDecisionBAROutputList.get(index);
            CPDecisionBAROutputOverride override = overrideMap.get(cpDecisionBAROutput);

            // If it's a new floor/ceiling override, we need to trigger a sync
            if (override.getNewFloorRate() != null || override.getNewCeilingRate() != null) {
                floorCeilingOverrideChangedSync = true;
            }
            updateCPOverridesForCPDecision(cpDecisionContext, override, cpDecisionBAROutput, decision.getId());
            pricingConfigurationLTBDEService.enableOverrideForExtendedWindowIfApplicable(
                    toJavaLocalDate(cpDecisionBAROutput.getArrivalDate()),
                    dateService.getCaughtUpJavaLocalDate());

            index++;
        }

        //Bulk save after final operation
        if (CollectionUtils.isNotEmpty(overrideMap.values())) {
            boolean fireEventForFloorCeilingOverrideChanged = false;
            for (CPDecisionBAROutput cpDecisionBAROutput : cpDecisionBAROutputList) {
                if (isDateInOptimizationWindow(cpDecisionBAROutput, dateService.getCaughtUpJavaLocalDate())) {
                    fireEventForFloorCeilingOverrideChanged = true;
                    break;
                }
            }
            if (fireEventForFloorCeilingOverrideChanged) {
                registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED, floorCeilingOverrideChangedSync);
            }
            crudService.save(overrideMap.keySet());
            crudService.save(overrideMap.values());
        }
    }

    @VisibleForTesting
	public
    boolean isDateInOptimizationWindow(CPDecisionBAROutput cpDecisionBAROutput, java.time.LocalDate caughtUpLocalDate) {
        return isDateInOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(cpDecisionBAROutput.getArrivalDate()), caughtUpLocalDate);
    }

    @VisibleForTesting
	public
    boolean isDateInOptimizationWindow(ProductRateOffsetOverride productRateOffsetOverride, java.time.LocalDate caughtUpLocalDate) {
        return isDateInOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(productRateOffsetOverride.getOccupancyDate()), caughtUpLocalDate);
    }

    private boolean isDateInOptimizationWindow(java.time.LocalDate occupancyDate, java.time.LocalDate caughtUpLocalDate) {
        return !pricingConfigurationLTBDEService.isDateInExtendedWindowForOverrides(occupancyDate, caughtUpLocalDate);
    }

    public CPDecisionBAROutputOverride saveOverrideForCPDecisionWithoutCascade(CPDecisionContext cpDecisionContext, CPDecisionBAROutputOverride override, CPDecisionBAROutput decisionBARCPOutput, Integer decisionId) {
        return saveOverrideForCPDecision(cpDecisionContext, override, decisionBARCPOutput, decisionId);
    }

    public CPDecisionBAROutputOverride saveOverrideForCPDecisionWithCascade(CPDecisionContext cpDecisionContext, CPDecisionBAROutputOverride override, CPDecisionBAROutput cpDecisionBAROutput, Integer decisionId) {
        CPDecisionBAROutputOverride cpDecisionBAROutputOverride = saveOverrideForCPDecision(cpDecisionContext, override, cpDecisionBAROutput, decisionId);

        applyBaseOverrideAcrossRoomTypes(cpDecisionContext, override, cpDecisionBAROutput, decisionId);

        //we only return the base RT override
        return cpDecisionBAROutputOverride;
    }

    private void saveOverrideForCPDecision(CPDecisionContext cpDecisionContext, CPDecisionBAROutputOverride override, CPDecisionBAROutput cpDecisionBAROutput, Integer decisionId, boolean applyBaseOverrideAcrossRoomTypes) {
        saveOverrideForCPDecision(cpDecisionContext, override, cpDecisionBAROutput, decisionId);
        // We only cascade specific overrides, not ceiling or floor
        if (applyBaseOverrideAcrossRoomTypes && override.getNewCeilingRate() == null && override.getNewFloorRate() == null) {
            applyBaseOverrideAcrossRoomTypes(cpDecisionContext, override, cpDecisionBAROutput, decisionId);
        }
    }

    private void applyBaseOverrideAcrossRoomTypes(CPDecisionContext cpDecisionContext, CPDecisionBAROutputOverride override, CPDecisionBAROutput cpDecisionBAROutput, Integer decisionId) {
        List<CPDecisionBAROutput> allRoomTypeDecisionsForDate = findCPDecisionsBetweenDates(cpDecisionBAROutput.getArrivalDate(), cpDecisionBAROutput.getArrivalDate());
        for (CPDecisionBAROutput decision : allRoomTypeDecisionsForDate) {
            if (!cpDecisionBAROutput.getAccomType().equals(decision.getAccomType()) && cpDecisionBAROutput.getAccomType().getAccomClass().equals(decision.getAccomType().getAccomClass())) {
                CPDecisionBAROutputOverride newRoomTypeOverride = new CPDecisionBAROutputOverride();
                newRoomTypeOverride.setPropertyId(override.getPropertyId());
                newRoomTypeOverride.setProduct(override.getProduct());
                newRoomTypeOverride.setUser(override.getUser());
                newRoomTypeOverride.setDecisionId(decisionId);
                newRoomTypeOverride.setLengthOfStay(override.getLengthOfStay());
                newRoomTypeOverride.setAccomType(decision.getAccomType());
                newRoomTypeOverride.setArrivalDate(override.getArrivalDate());
                newRoomTypeOverride.setNewOverrideType(override.getNewOverrideType());
                newRoomTypeOverride.setNewUserOverride(override.getNewUserOverride());
                newRoomTypeOverride.setNewFloorRate(override.getNewFloorRate());
                newRoomTypeOverride.setNewCeilingRate(override.getNewCeilingRate());
                newRoomTypeOverride.setCreateDate(new Date());

                newRoomTypeOverride.setNewUserOverride(cpDecisionContext.applyOffset(decision, newRoomTypeOverride.getNewUserOverride()));
                newRoomTypeOverride.setNewFloorRate(cpDecisionContext.applyOffset(decision, newRoomTypeOverride.getNewFloorRate()));
                newRoomTypeOverride.setNewCeilingRate(cpDecisionContext.applyOffset(decision, newRoomTypeOverride.getNewCeilingRate()));

                newRoomTypeOverride.setOldOverrideType(decision.getOverrideType());
                //Is this correct? Do we need to lookup the old override to determine old user override?
                newRoomTypeOverride.setOldUserOverride(decision.getFinalBAR());
                newRoomTypeOverride.setOldFloorRate(decision.getFloorOverride());
                newRoomTypeOverride.setOldCeilingRate(decision.getCeilingOverride());
                saveOverrideForCPDecision(cpDecisionContext, newRoomTypeOverride, decision, decisionId, false);
            }
        }
    }

    protected CPDecisionBAROutputOverride saveOverrideForCPDecision(CPDecisionContext cpDecisionContext, CPDecisionBAROutputOverride override, CPDecisionBAROutput cpDecisionBAROutput, Integer decisionId) {
        // Set the Output and the Override
        cpDecisionBAROutput.setDecisionId(decisionId);
        override.setDecisionId(decisionId);
        // Get the single supplement for the arrivalDate/accomType

        AccomTypeSupplementValue accomTypeSupplementValue = cpDecisionContext.getAccomTypeSupplementValue(cpDecisionBAROutput);
        BigDecimal specificSupplementValue;
        BigDecimal ceilingSupplementValue;
        BigDecimal floorSupplementValue;

        BigDecimal finalPriceValue = cpDecisionContext.calculateRoundedRate(cpDecisionBAROutput);
        BigDecimal ceilingOverrideValue = cpDecisionContext.getCeilingOverrideRate() != null ? cpDecisionContext.getCeilingOverrideRate() : BigDecimal.ZERO;
        BigDecimal floorOverrideValue = cpDecisionContext.getFloorOverrideRate() != null ? cpDecisionContext.getFloorOverrideRate() : BigDecimal.ZERO;

        if (accomTypeSupplementValue != null && OffsetMethod.PERCENTAGE.equals(accomTypeSupplementValue.getOffsetMethod())) {
            specificSupplementValue = Supplement.calculateSupplementInPercentage(finalPriceValue, cpDecisionContext.getSupplement(cpDecisionBAROutput));
            ceilingSupplementValue = Supplement.calculateSupplementInPercentage(ceilingOverrideValue, cpDecisionContext.getSupplement(cpDecisionBAROutput));
            floorSupplementValue = Supplement.calculateSupplementInPercentage(floorOverrideValue, cpDecisionContext.getSupplement(cpDecisionBAROutput));
        } else {
            specificSupplementValue = cpDecisionContext.getSupplement(cpDecisionBAROutput);
            ceilingSupplementValue = cpDecisionContext.getSupplement(cpDecisionBAROutput);
            floorSupplementValue = cpDecisionContext.getSupplement(cpDecisionBAROutput);
        }

        cpDecisionBAROutput.setSpecificOverride(BigDecimalUtil.addReturnsNull(override.getNewUserOverride(), specificSupplementValue));
        cpDecisionBAROutput.setFloorOverride(BigDecimalUtil.addReturnsNull(override.getNewFloorRate(), floorSupplementValue));
        cpDecisionBAROutput.setCeilingOverride(BigDecimalUtil.addReturnsNull(override.getNewCeilingRate(), ceilingSupplementValue));
        cpDecisionBAROutput.setOverrideType(override.getNewOverrideType());

        if (override.getNewUserOverride() != null) {
            cpDecisionBAROutput.setPrettyBAR(BigDecimalUtil.addReturnsNull(override.getNewUserOverride(), specificSupplementValue));
            cpDecisionBAROutput.setRoomsOnlyBAR(override.getNewUserOverride());
            cpDecisionBAROutput.setFinalBAR(BigDecimalUtil.addReturnsNull(override.getNewUserOverride(), specificSupplementValue));
        } else {
            // Calculate the prettyBAR
            BigDecimal prettyBAR = cpDecisionContext.calculateRoundedRate(cpDecisionBAROutput);

            cpDecisionBAROutput.setPrettyBAR(prettyBAR);
            cpDecisionBAROutput.setRoomsOnlyBAR(BigDecimalUtil.subtract(prettyBAR, specificSupplementValue));
            cpDecisionBAROutput.setFinalBAR(prettyBAR);
        }

        if (override.getCreateDate() == null) {
            override.setCreateDate(new Date());
        }

        // If it's a new floor/ceiling override, we need to trigger a sync
        if (override.getNewFloorRate() != null || override.getNewCeilingRate() != null) {
            if (isDateInOptimizationWindow(cpDecisionBAROutput, dateService.getCaughtUpJavaLocalDate())) {
                syncEventAggregatorService.registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
            }
        }

        // It's possible that the user was still in the UI after executing a sync.  The sync will have written new
        // CPDecisionBarOutput objects and the ones that were in the user's session will no longer exist.  Thus, those
        // objects will look like they are 'new' trying to be saved and will cause a unique key exception to be thrown.
        // This logic will prevent the duplicate key exceptions from happening.
        CPDecisionBAROutput foundCPDecisionBarOutput = crudService.findByNamedQuerySingleResult(CPDecisionBAROutput.GET_DECISION_BAR_OUTPUT,
                CPDecisionBAROutput.params(cpDecisionBAROutput.getProduct(), cpDecisionBAROutput.getArrivalDate(), cpDecisionBAROutput.getAccomType()));
        if (foundCPDecisionBarOutput != null && !foundCPDecisionBarOutput.equals(cpDecisionBAROutput)) {
            cpDecisionBAROutput.setId(foundCPDecisionBarOutput.getId());
        }

        crudService.save(cpDecisionBAROutput);
        pricingConfigurationLTBDEService.enableOverrideForExtendedWindowIfApplicable(
                toJavaLocalDate(cpDecisionBAROutput.getArrivalDate()),
                dateService.getCaughtUpJavaLocalDate());
        return crudService.save(override);
    }

    private void updateCPOverridesForCPDecision(CPDecisionContext cpDecisionContext, CPDecisionBAROutputOverride override, CPDecisionBAROutput cpDecisionBAROutput, Integer decisionId) {
        // Set the Output and the Override
        cpDecisionBAROutput.setDecisionId(decisionId);
        override.setDecisionId(decisionId);
        // Get the single supplement for the arrivalDate/accomType
        AccomTypeSupplementValue accomTypeSupplementValue = cpDecisionContext.getAccomTypeSupplementValue(cpDecisionBAROutput);
        BigDecimal specificSupplementValue;
        BigDecimal ceilingSupplementValue;
        BigDecimal floorSupplementValue;

        BigDecimal finalPriceValue = cpDecisionContext.calculateRoundedRate(cpDecisionBAROutput);
        BigDecimal ceilingOverrideValue = cpDecisionContext.getCeilingOverrideRate() != null ? cpDecisionContext.getCeilingOverrideRate() : BigDecimal.ZERO;
        BigDecimal floorOverrideValue = cpDecisionContext.getFloorOverrideRate() != null ? cpDecisionContext.getFloorOverrideRate() : BigDecimal.ZERO;

        if (accomTypeSupplementValue != null && OffsetMethod.PERCENTAGE.equals(accomTypeSupplementValue.getOffsetMethod())) {
            specificSupplementValue = Supplement.calculateSupplementInPercentage(finalPriceValue, cpDecisionContext.getSupplement(cpDecisionBAROutput));
            ceilingSupplementValue = Supplement.calculateSupplementInPercentage(ceilingOverrideValue, cpDecisionContext.getSupplement(cpDecisionBAROutput));
            floorSupplementValue = Supplement.calculateSupplementInPercentage(floorOverrideValue, cpDecisionContext.getSupplement(cpDecisionBAROutput));
        } else {
            specificSupplementValue = cpDecisionContext.getSupplement(cpDecisionBAROutput);
            ceilingSupplementValue = cpDecisionContext.getSupplement(cpDecisionBAROutput);
            floorSupplementValue = cpDecisionContext.getSupplement(cpDecisionBAROutput);
        }

        cpDecisionBAROutput.setSpecificOverride(BigDecimalUtil.addReturnsNull(override.getNewUserOverride(), specificSupplementValue));
        cpDecisionBAROutput.setFloorOverride(BigDecimalUtil.addReturnsNull(override.getNewFloorRate(), floorSupplementValue));
        cpDecisionBAROutput.setCeilingOverride(BigDecimalUtil.addReturnsNull(override.getNewCeilingRate(), ceilingSupplementValue));
        cpDecisionBAROutput.setOverrideType(override.getNewOverrideType());

        if (override.getNewUserOverride() != null) {
            cpDecisionBAROutput.setPrettyBAR(BigDecimalUtil.addReturnsNull(override.getNewUserOverride(), specificSupplementValue));
            cpDecisionBAROutput.setRoomsOnlyBAR(override.getNewUserOverride());
            cpDecisionBAROutput.setFinalBAR(BigDecimalUtil.addReturnsNull(override.getNewUserOverride(), specificSupplementValue));
        } else {
            // Calculate the prettyBAR
            BigDecimal prettyBAR = cpDecisionContext.calculateRoundedRate(cpDecisionBAROutput);

            cpDecisionBAROutput.setPrettyBAR(prettyBAR);
            cpDecisionBAROutput.setRoomsOnlyBAR(BigDecimalUtil.subtract(prettyBAR, specificSupplementValue));
            cpDecisionBAROutput.setFinalBAR(prettyBAR);
        }

        if (override.getCreateDate() == null) {
            override.setCreateDate(new Date());
        }

        // It's possible that the user was still in the UI after executing a sync.  The sync will have written new
        // CPDecisionBarOutput objects and the ones that were in the user's session will no longer exist.  Thus, those
        // objects will look like they are 'new' trying to be saved and will cause a unique key exception to be thrown.
        // This logic will prevent the duplicate key exceptions from happening.
        CPDecisionBAROutput foundCPDecisionBarOutput = crudService.findByNamedQuerySingleResult(CPDecisionBAROutput.GET_DECISION_BAR_OUTPUT,
                CPDecisionBAROutput.params(cpDecisionBAROutput.getProduct(), cpDecisionBAROutput.getArrivalDate(), cpDecisionBAROutput.getAccomType()));
        if (foundCPDecisionBarOutput != null && !foundCPDecisionBarOutput.equals(cpDecisionBAROutput)) {
            cpDecisionBAROutput.setId(foundCPDecisionBarOutput.getId());
        }
    }

    @SuppressWarnings("unchecked")
    public List<CPDecisionBAROutput> findCPDecisionsBetweenDates(LocalDate startDate, LocalDate endDate) {
        Product product = crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        return crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN, CPDecisionBAROutput.params(product, startDate, endDate));
    }


    public void removeOverride(CPDecisionContext cpDecisionContext, CPDecisionBAROutput decisionBARCPOutput, Integer decisionId, Integer userId, boolean removeOverrideAcrossRoomTypes, boolean finalBarRequiresCalculation, boolean subtractSupplementFromOldRates) {
        CPDecisionBAROutputOverride decisionBARCPOutputOverride = CPDecisionBAROutputOverride.removeOverride(decisionBARCPOutput, userId);

        // Set the Output and the Override
        decisionBARCPOutput.setDecisionId(decisionId);
        decisionBARCPOutputOverride.setDecisionId(decisionId);

        // Need to subtract off the supplement value from the old override values
        BigDecimal specificSupplementValue;
        BigDecimal ceilingSupplementValue;
        BigDecimal floorSupplementValue;
        AccomTypeSupplementValue accomTypeSupplementValue = cpDecisionContext.getAccomTypeSupplementValue(decisionBARCPOutput);

        if (accomTypeSupplementValue != null && OffsetMethod.PERCENTAGE.equals(accomTypeSupplementValue.getOffsetMethod())) {
            specificSupplementValue = Supplement.calculateSupplementInPercentage(decisionBARCPOutput.getSpecificOverride() != null ? decisionBARCPOutput.getSpecificOverride() : BigDecimal.ZERO, cpDecisionContext.getSupplement(decisionBARCPOutput));
            ceilingSupplementValue = Supplement.calculateSupplementInPercentage(decisionBARCPOutput.getCeilingOverride() != null ? decisionBARCPOutput.getCeilingOverride() : BigDecimal.ZERO, cpDecisionContext.getSupplement(decisionBARCPOutput));
            floorSupplementValue = Supplement.calculateSupplementInPercentage(decisionBARCPOutput.getFloorOverride() != null ? decisionBARCPOutput.getFloorOverride() : BigDecimal.ZERO, cpDecisionContext.getSupplement(decisionBARCPOutput));
        } else {
            specificSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
            ceilingSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
            floorSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
        }

        if (subtractSupplementFromOldRates) {
            decisionBARCPOutputOverride.setOldCeilingRate(BigDecimalUtil.subtractReturnsNull(decisionBARCPOutputOverride.getOldCeilingRate(), ceilingSupplementValue));
            decisionBARCPOutputOverride.setOldFloorRate(BigDecimalUtil.subtractReturnsNull(decisionBARCPOutputOverride.getOldFloorRate(), floorSupplementValue));
            decisionBARCPOutputOverride.setOldUserOverride(BigDecimalUtil.subtractReturnsNull(decisionBARCPOutputOverride.getOldUserOverride(), specificSupplementValue));
        }

        //before we reset overrides, check to see what we're removing so we can toss up a sync flag
        if (decisionBARCPOutput.getFloorOverride() != null || decisionBARCPOutput.getCeilingOverride() != null) {
            if (isDateInOptimizationWindow(decisionBARCPOutput, dateService.getCaughtUpJavaLocalDate())) {
                syncEventAggregatorService.registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
            }
        }

        if (decisionBARCPOutput.getSpecificOverride() != null) {
            if (isDateInOptimizationWindow(decisionBARCPOutput, dateService.getCaughtUpJavaLocalDate())) {
                syncEventAggregatorService.registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_SPECIFIC_OVERRIDE_REMOVED);
            }
        }

        decisionBARCPOutput.setSpecificOverride(null);
        decisionBARCPOutput.setFloorOverride(null);
        decisionBARCPOutput.setCeilingOverride(null);
        decisionBARCPOutput.setOverrideType(DecisionOverrideType.NONE);

        if (finalBarRequiresCalculation) {
            // Calculate the rounded bar and set it as final
            decisionBARCPOutput.setFinalBAR(cpDecisionContext.calculateRoundedRate(decisionBARCPOutput));
            decisionBARCPOutput.setPrettyBAR(decisionBARCPOutput.getFinalBAR());

            AccomTypeSupplementValue newAccomTypeSupplementValue = cpDecisionContext.getAccomTypeSupplementValue(decisionBARCPOutput);
            BigDecimal supplement;

            if (accomTypeSupplementValue != null && OffsetMethod.PERCENTAGE.equals(newAccomTypeSupplementValue.getOffsetMethod())) {
                supplement = Supplement.calculateSupplementInPercentage(decisionBARCPOutput.getFinalBAR(), cpDecisionContext.getSupplement(decisionBARCPOutput));
            } else {
                supplement = cpDecisionContext.getSupplement(decisionBARCPOutput);
            }

            // Subtract the 'supplement' from the finalBAR to set the roomsOnlyBAR
            decisionBARCPOutput.setRoomsOnlyBAR(BigDecimalUtil.subtract(decisionBARCPOutput.getFinalBAR(), supplement));
        }

        if (decisionBARCPOutputOverride.getCreateDate() == null) {
            decisionBARCPOutputOverride.setCreateDate(new Date());
        }

        crudService.save(decisionBARCPOutput);
        crudService.save(decisionBARCPOutputOverride);

        pricingConfigurationLTBDEService.enableOverrideForExtendedWindowIfApplicable(
                toJavaLocalDate(decisionBARCPOutput.getArrivalDate()),
                dateService.getCaughtUpJavaLocalDate());

        if (removeOverrideAcrossRoomTypes) {
            List<CPDecisionBAROutput> allRoomTypeDecisionsForDate = findCPDecisionsBetweenDates(decisionBARCPOutput.getArrivalDate(), decisionBARCPOutput.getArrivalDate());
            allRoomTypeDecisionsForDate.stream()
                    .filter(decision -> !decisionBARCPOutput.getAccomType().equals(decision.getAccomType()) && decisionBARCPOutput.getAccomType().getAccomClass().equals(decision.getAccomType().getAccomClass()))
                    .forEach(decision -> removeOverride(cpDecisionContext, decision, decisionId, userId, false, true, true));
        }
    }

    public void removeOverrides(CPDecisionContext cpDecisionContext, List<CPDecisionBAROutput> decisionBARCPOutputList, Integer decisionId, Integer userId,
                                List<CPDecisionBAROutput> finalNonBaseRoomTypeDecisions, boolean subtractSupplementFromOldRates) {
        List<CPDecisionBAROutputOverride> decisionBAROutputOverridesToSave = new ArrayList<>();
        boolean floorCeilingOverrideChanged = false;
        boolean specificOverrideRemoved = false;
        for (CPDecisionBAROutput decisionBARCPOutput : decisionBARCPOutputList) {
            CPDecisionBAROutputOverride decisionBARCPOutputOverride = CPDecisionBAROutputOverride.removeOverride(decisionBARCPOutput, userId);
            decisionBAROutputOverridesToSave.add(decisionBARCPOutputOverride);

            // Set the Output and the Override
            decisionBARCPOutput.setDecisionId(decisionId);
            decisionBARCPOutputOverride.setDecisionId(decisionId);

            // Need to subtract off the supplement value from the old override values
            BigDecimal specificSupplementValue;
            BigDecimal ceilingSupplementValue;
            BigDecimal floorSupplementValue;
            AccomTypeSupplementValue accomTypeSupplementValue = cpDecisionContext.getAccomTypeSupplementValue(decisionBARCPOutput);

            if (accomTypeSupplementValue != null && OffsetMethod.PERCENTAGE.equals(accomTypeSupplementValue.getOffsetMethod())) {
                specificSupplementValue = Supplement.calculateSupplementInPercentage(decisionBARCPOutput.getSpecificOverride() != null ? decisionBARCPOutput.getSpecificOverride() : BigDecimal.ZERO, cpDecisionContext.getSupplement(decisionBARCPOutput));
                ceilingSupplementValue = Supplement.calculateSupplementInPercentage(decisionBARCPOutput.getCeilingOverride() != null ? decisionBARCPOutput.getCeilingOverride() : BigDecimal.ZERO, cpDecisionContext.getSupplement(decisionBARCPOutput));
                floorSupplementValue = Supplement.calculateSupplementInPercentage(decisionBARCPOutput.getFloorOverride() != null ? decisionBARCPOutput.getFloorOverride() : BigDecimal.ZERO, cpDecisionContext.getSupplement(decisionBARCPOutput));
            } else {
                specificSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
                ceilingSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
                floorSupplementValue = cpDecisionContext.getSupplement(decisionBARCPOutput);
            }

            if (subtractSupplementFromOldRates) {
                decisionBARCPOutputOverride.setOldCeilingRate(BigDecimalUtil.subtractReturnsNull(decisionBARCPOutputOverride.getOldCeilingRate(), ceilingSupplementValue));
                decisionBARCPOutputOverride.setOldFloorRate(BigDecimalUtil.subtractReturnsNull(decisionBARCPOutputOverride.getOldFloorRate(), floorSupplementValue));
                decisionBARCPOutputOverride.setOldUserOverride(BigDecimalUtil.subtractReturnsNull(decisionBARCPOutputOverride.getOldUserOverride(), specificSupplementValue));
            }

            //before we reset overrides, check to see what we're removing so we can toss up a sync flag
            if (decisionBARCPOutput.getFloorOverride() != null || decisionBARCPOutput.getCeilingOverride() != null) {
                floorCeilingOverrideChanged = true;
            }

            if (decisionBARCPOutput.getSpecificOverride() != null) {
                specificOverrideRemoved = true;
            }

            decisionBARCPOutput.setSpecificOverride(null);
            decisionBARCPOutput.setFloorOverride(null);
            decisionBARCPOutput.setCeilingOverride(null);
            decisionBARCPOutput.setOverrideType(DecisionOverrideType.NONE);

            boolean finalBarRequiresCalculation = !finalNonBaseRoomTypeDecisions.contains(decisionBARCPOutput);
            if (finalBarRequiresCalculation) {
                // Calculate the rounded bar and set it as final
                decisionBARCPOutput.setFinalBAR(cpDecisionContext.calculateRoundedRate(decisionBARCPOutput));
                decisionBARCPOutput.setPrettyBAR(decisionBARCPOutput.getFinalBAR());

                AccomTypeSupplementValue newAccomTypeSupplementValue = cpDecisionContext.getAccomTypeSupplementValue(decisionBARCPOutput);
                BigDecimal supplement;

                if (accomTypeSupplementValue != null && OffsetMethod.PERCENTAGE.equals(newAccomTypeSupplementValue.getOffsetMethod())) {
                    supplement = Supplement.calculateSupplementInPercentage(decisionBARCPOutput.getFinalBAR(), cpDecisionContext.getSupplement(decisionBARCPOutput));
                } else {
                    supplement = cpDecisionContext.getSupplement(decisionBARCPOutput);
                }

                // Subtract the 'supplement' from the finalBAR to set the roomsOnlyBAR
                decisionBARCPOutput.setRoomsOnlyBAR(BigDecimalUtil.subtract(decisionBARCPOutput.getFinalBAR(), supplement));
            }

            if (decisionBARCPOutputOverride.getCreateDate() == null) {
                decisionBARCPOutputOverride.setCreateDate(new Date());
            }

            pricingConfigurationLTBDEService.enableOverrideForExtendedWindowIfApplicable(
                    toJavaLocalDate(decisionBARCPOutput.getArrivalDate()),
                    dateService.getCaughtUpJavaLocalDate());
        }

        boolean overrideChangedInOptimizationWindow = false;
        if (CollectionUtils.isNotEmpty(decisionBARCPOutputList)) {
            for (CPDecisionBAROutput cpDecisionBarOutput : decisionBARCPOutputList) {
                if (isDateInOptimizationWindow(cpDecisionBarOutput, dateService.getCaughtUpJavaLocalDate())) {
                    overrideChangedInOptimizationWindow = true;
                    break;
                }
            }
            if (overrideChangedInOptimizationWindow) {
                registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED, floorCeilingOverrideChanged);
                registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_SPECIFIC_OVERRIDE_REMOVED, specificOverrideRemoved);
            }
            crudService.save(decisionBARCPOutputList);
            crudService.save(decisionBAROutputOverridesToSave);
        }
    }

    private void registerSyncEvent(SyncEvent syncEvent, boolean fireEvent) {
        if (fireEvent) {
            syncEventAggregatorService.registerSyncEvent(syncEvent);
        }
    }

    private void addSpecialEvents(List<BusinessAnalysisDailyIndicatorDto> businessAnalysisDailyIndicatorDtos, LocalDate date, CPBARDecisionDTO decisionCPDTO) {
        BusinessAnalysisDailyIndicatorDto dailyIndicatorDto = getDailyIndicatorDTO(date, businessAnalysisDailyIndicatorDtos);
        if (dailyIndicatorDto != null) {
            boolean hasSpecialEvent = dailyIndicatorDto.isSpecialEventImpactFCST() || dailyIndicatorDto.isSpecialEventInfoOnly();
            if (hasSpecialEvent) {
                decisionCPDTO.setSpecialEvents(businessAnalysisDashboardService.getBusinessAnalysisSpecialEventDtos(date.toDate()));
            }
        }
    }

    private String getCompetitorRate(Map<Date, CompetitorInfo> competitorInfoMap, LocalDate date) {
        CompetitorInfo competitorInfo = competitorInfoMap.get(date.toDate());
        return competitorInfo != null ? competitorInfo.getCompetitorPrice() : null;
    }

    private List<Integer> buildAccomClassIDs(int accomClass) {
        List<Integer> accomClasses = new ArrayList<>();

        if (accomClass == -1) {
            List<AccomClass> roomClasses = competitorRateInfoService.getAccomClassesHavingWebrateAccomTypes();
            accomClasses = roomClasses.stream().map(AccomClass::getId).collect(toList());
        } else {
            accomClasses.add(accomClass);
        }
        return accomClasses;
    }

    public Long calculateCapacityToSell(BusinessAnalysisDailyDataDto dailyDataDto) {
        return dailyDataDto.getCapacity()
                + ofNullable(dailyDataDto.getOverbookings()).orElse(0L)
                - ofNullable(dailyDataDto.getOutOfOrder()).orElse(0L)
                - ofNullable(dailyDataDto.getOnBooks()).orElse(0L);
    }

    public CPDecisionBAROutput getProductDecision(LocalDate date, Product product, AccomType accomType) {
        return crudService.findByNamedQuerySingleResult(CPDecisionBAROutput.GET_DECISION_BAR_OUTPUT, QueryParameter.with("product", product).and("arrivalDate", date).and("accomType", accomType).and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<ProductRateOffset> getProductDiscount(Product product) {
        return agileRatesConfigurationService.findProductRateOffsetsByProduct(product);
    }

    public List<ProductRateOffset> getProductDiscountsFor(Set<Product> products) {
        return agileRatesConfigurationService.findProductRateOffsetFor(products);
    }

    public Set<AccomClass> findRoomClassesByProduct(Product product) {
        return agileRatesConfigurationService.findRoomTypesByProduct(product)
                .stream()
                .map(AccomType::getAccomClass)
                .collect(Collectors.toSet());
    }


    private javax.ws.rs.core.Response getInvalidResponse(String errorMessage) {
        return javax.ws.rs.core.Response.status(HttpStatus.SC_BAD_REQUEST)
                .entity(errorMessage)
                .build();
    }

    @ForTesting


    public javax.ws.rs.core.Response saveNonOptimizedAgileProductOverrides(int productId,
                                                                           int accomClassId,
                                                                           String startDateStr,
                                                                           String endDateStr,
                                                                           double newOffsetValue) {
        AccomClass accomClass = crudService.find(AccomClass.class, accomClassId);
        Product product = crudService.find(Product.class, productId);
        javax.ws.rs.core.Response errorResponse = validateInputParameters(productId, accomClassId, false, accomClass, product);
        if (null != errorResponse) {
            return errorResponse;
        }
        LocalDate startDate = LocalDate.parse(startDateStr);
        LocalDate endDate = LocalDate.parse(endDateStr);
        deleteExistingOverridesForNonOptimized(accomClassId, product, startDate, endDate);

        BigDecimal offsetValue = BigDecimal.valueOf(newOffsetValue);
        List<ProductRateOffsetOverride> newOverrides = createOverridesByDimensions(product, accomClass, startDate, endDate, offsetValue, offsetValue, new HashMap<LocalDate, BigDecimal>());
        saveProductRateOffsetOverride(newOverrides);

        return SUCCESS_RESPONSE;
    }

    @ForTesting


    public javax.ws.rs.core.Response saveOptimizedAgileProductOverrides(int productId,
                                                                        int accomClassId,
                                                                        String startDateStr,
                                                                        String endDateStr,
                                                                        double floorValue,
                                                                        double ceilingValue) {
        AccomClass accomClass = crudService.find(AccomClass.class, accomClassId);
        Product product = crudService.find(Product.class, productId);
        javax.ws.rs.core.Response errorResponse = validateInputParameters(productId, accomClassId, true, accomClass, product);
        if (null != errorResponse) {
            return errorResponse;
        }
        Set<AccomClass> accomClasses = OptimizationLevel.SAME_FOR_ALL_ROOM_CLASSES.equals(getOptimizationLevel())
                ? findRoomClassesByProduct(product)
                : Collections.singleton(accomClass);
        LocalDate startDate = LocalDate.parse(startDateStr);
        LocalDate endDate = LocalDate.parse(endDateStr);
        List<ProductRateOffsetOverride> productRateOffsetOverrides = getActiveProductRateOffsetOverrides(product, startDate, endDate)
                .stream()
                .filter(pro -> accomClasses.contains(pro.getAccomClass()))
                .collect(toList());
        Map<LocalDate, BigDecimal> existingAdjustments = productRateOffsetOverrides
                .stream()
                .collect(Collectors.toMap(ProductRateOffsetOverride::getOccupancyDate, ProductRateOffsetOverride::getOffsetValue, (value1, value2) -> value1));
        List<ProductRateOffsetOverride> newOverrides = accomClasses
                .stream()
                .flatMap(ac ->
                        createOverridesByDimensions(product, ac, startDate, endDate, BigDecimal.valueOf(floorValue), BigDecimal.valueOf(ceilingValue), existingAdjustments).stream())
                .collect(toList());

        productRateOffsetOverrides.forEach(productRateOffsetOverride -> productRateOffsetOverride.setStatusId(Status.INACTIVE.getId()));
        crudService.save(productRateOffsetOverrides);

        saveProductRateOffsetOverride(newOverrides);
        return SUCCESS_RESPONSE;
    }

    @ForTesting


    public javax.ws.rs.core.Response deleteOptimizedAgileProductOverrides(int productId,
                                                                          int accomClassId,
                                                                          String startDateStr,
                                                                          String endDateStr) {
        AccomClass accomClass = crudService.find(AccomClass.class, accomClassId);
        Product product = crudService.find(Product.class, productId);
        javax.ws.rs.core.Response errorResponse = validateInputParameters(productId, accomClassId, true, accomClass, product);
        if (null != errorResponse) {
            return errorResponse;
        }

        deleteExistingOverridesForOptimized(accomClass, product, LocalDate.parse(startDateStr), LocalDate.parse(endDateStr));

        return SUCCESS_RESPONSE;
    }

    public void deleteExistingOverridesForOptimized(AccomClass accomClass, Product product, LocalDate startDate, LocalDate endDate) {
        Set<AccomClass> accomClasses = OptimizationLevel.SAME_FOR_ALL_ROOM_CLASSES.equals(getOptimizationLevel())
                ? findRoomClassesByProduct(product)
                : Collections.singleton(accomClass);
        List<ProductRateOffsetOverride> productRateOffsetOverrides = getActiveProductRateOffsetOverrides(product, startDate, endDate)
                .stream()
                .filter(pro -> accomClasses.contains(pro.getAccomClass()))
                .collect(toList());
        deleteProductRateOffsetOverride(productRateOffsetOverrides);
    }

    public void deleteExistingOverridesForNonOptimized(Integer accomClassId, Product product, LocalDate startDate, LocalDate endDate) {
        List<ProductRateOffsetOverride> productRateOffsetOverrides = getActiveProductRateOffsetOverrides(product, startDate, endDate);
        List<ProductRateOffsetOverride> existingOverridesForRoomClass = productRateOffsetOverrides.stream().filter(override -> accomClassId.equals(override.getAccomClass().getId())).collect(toList());
        existingOverridesForRoomClass.forEach(productRateOffsetOverride -> productRateOffsetOverride.setStatusId(Status.INACTIVE.getId()));
        crudService.save(productRateOffsetOverrides);
    }

    @ForTesting


    public javax.ws.rs.core.Response deleteNonOptimizedAgileProductOverrides(int productId,
                                                                             int accomClassId,
                                                                             String startDateStr,
                                                                             String endDateStr) {
        AccomClass accomClass = crudService.find(AccomClass.class, accomClassId);
        Product product = crudService.find(Product.class, productId);
        javax.ws.rs.core.Response errorResponse = validateInputParameters(productId, accomClassId, false, accomClass, product);
        if (null != errorResponse) {
            return errorResponse;
        }
        deleteExistingOverridesForNonOptimized(accomClassId, product, LocalDate.parse(startDateStr), LocalDate.parse(endDateStr));
        if (isPricingOverrideIDPFlowBasedSyncEnabled()) {
            registerPricingOverrideSyncEvent();
        } else {
            registerAgileRateChangeSyncEvent();
        }
        return SUCCESS_RESPONSE;
    }

    private javax.ws.rs.core.Response validateInputParameters(Integer productId, Integer accomClassId, boolean productOptimizedState, AccomClass accomClass, Product product) {
        if (null == product) {
            return getInvalidResponse(String.format("[%d] is not a valid productId.", productId));
        }
        if (null == accomClass) {
            return getInvalidResponse(String.format("[%d] is not a valid roomClassId.", accomClassId));
        }

        if (productOptimizedState != product.isOptimized()) {
            return getInvalidResponse(String.format("This API should only used for %s products.", productOptimizedState ? "optimized" : "non-optimized"));
        }

        boolean isRCMappedToProduct = findRoomClassesByProduct(product).stream().map(ac -> ac.getId()).anyMatch(accomClassId1 -> accomClassId1.equals(accomClassId));
        if (!isRCMappedToProduct) {
            return getInvalidResponse(String.format("accomClassId [%d] is not a mapped to productId [%d].", accomClassId, productId));
        }

        return null;
    }

    public List<ProductRateOffsetOverride> createOverridesByDimensions(Product product, AccomClass roomClass, LocalDate startDate, LocalDate endDate, BigDecimal floorValue, BigDecimal ceilingValue, Map<LocalDate, BigDecimal> existingAdjustments) {
        List<AgileRatesDTARange> dtaRanges = finalDTARangesForProduct(product);
        List<ProductRateOffsetOverride> productRateOffsetOverrides = new ArrayList<>();
        LocalDate occupancyDate = new LocalDate(startDate);
        while (!occupancyDate.isAfter(endDate)) {
            BigDecimal adjustmentValue = floorValue;
            if (product.isOptimized()) {
                BigDecimal adjustmentForADate = existingAdjustments.get(occupancyDate);
                adjustmentValue = null != adjustmentForADate ? adjustmentForADate : (null != floorValue ? floorValue : ceilingValue);
            }
            for (AgileRatesDTARange range : dtaRanges) {
                ProductRateOffsetOverride override = createProductRateOffsetOverride(product, roomClass, floorValue, ceilingValue, adjustmentValue);
                override.setAgileRatesDTARange(range);
                override.setOccupancyDate(occupancyDate);
                productRateOffsetOverrides.add(override);
            }
            occupancyDate = occupancyDate.plusDays(1);
        }
        return productRateOffsetOverrides;
    }


    private ProductRateOffsetOverride createProductRateOffsetOverride(Product product, AccomClass roomClass, BigDecimal floorValue, BigDecimal ceilingValue, BigDecimal offsetValue) {
        ProductRateOffsetOverride override = new ProductRateOffsetOverride();
        override.setProduct(product);
        override.setAccomClass(roomClass);
        override.setStatusId(Status.ACTIVE.getId());
        override.setOffsetMethod(product.getOffsetMethod());
        override.setOffsetValue(offsetValue);
        override.setFloorValue(floorValue);
        override.setCeilingValue(ceilingValue);
        return override;
    }

    public List<AgileRatesDTARange> findAllDTARanges() {
        return agileRatesConfigurationService.findAllDTARanges();
    }

    public List<AgileRatesDTARange> finalDTARangesForProduct(Product product) {
        List<AgileRatesDTARange> allDTARanges = agileRatesConfigurationService.findAllDTARanges();
        return agileRatesConfigurationService.getAdjustedDTARanges(product, allDTARanges);
    }


    public void saveProductRateOffsetOverride(List<ProductRateOffsetOverride> overrides) {
        crudService.save(overrides);
        if (shouldAgileRatesSyncBeFire(overrides)) {
            if (isPricingOverrideIDPFlowBasedSyncEnabled()) {
                registerPricingOverrideSyncEvent();
            } else {
                registerAgileRateChangeSyncEvent();
            }
        }
    }


    public void deleteProductRateOffsetOverride(List<ProductRateOffsetOverride> overrides) {
        overrides.forEach(productRateOffsetOverride -> productRateOffsetOverride.setStatusId(Status.INACTIVE.getId()));
        crudService.save(overrides);
        if (shouldAgileRatesSyncBeFire(overrides)) {
            if (isPricingOverrideIDPFlowBasedSyncEnabled()) {
                registerPricingOverrideSyncEvent();
            } else {
                registerAgileRateChangeSyncEvent();
            }
        }
    }

    protected boolean shouldAgileRatesSyncBeFire(List<ProductRateOffsetOverride> overrides) {
        boolean pricingDisableAgileRatesSyncForOptimizedProductOverrides = isPricingDisableAgileRatesSyncForOptimizedProductOverrides();
        boolean pricingDisableAgileRatesSyncForNonOptimizedProductOverrides = isPricingDisableAgileRatesSyncForNonOptimizedProductOverrides();
        boolean hasOptimizedOverrides = CollectionUtils.isNotEmpty(overrides) && overrides.stream().anyMatch(proo -> proo.getProduct().isOptimized());
        boolean hasNonOptimizedOverrides = CollectionUtils.isNotEmpty(overrides) && overrides.stream().anyMatch(proo -> !proo.getProduct().isOptimized());

        if (CollectionUtils.isEmpty(overrides)) {
            return false;
        } else if (!pricingDisableAgileRatesSyncForOptimizedProductOverrides && !pricingDisableAgileRatesSyncForNonOptimizedProductOverrides) {
            return true;
        } else if (pricingDisableAgileRatesSyncForOptimizedProductOverrides && pricingDisableAgileRatesSyncForNonOptimizedProductOverrides) {
            return false;
        } else if (!pricingDisableAgileRatesSyncForOptimizedProductOverrides && pricingDisableAgileRatesSyncForNonOptimizedProductOverrides) {
            return hasOptimizedOverrides;
        } else if (pricingDisableAgileRatesSyncForOptimizedProductOverrides && !pricingDisableAgileRatesSyncForNonOptimizedProductOverrides) {
            return hasNonOptimizedOverrides;
        }
        return true;
    }

    protected boolean isPricingDisableAgileRatesSyncForOptimizedProductOverrides() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES);
    }

    protected boolean isPricingDisableAgileRatesSyncForNonOptimizedProductOverrides() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_NON_OPTIMIZED_PRODUCT_OVERRIDES);
    }

    @ForTesting


    public void deleteAllProductRateOffsetOverrides() {
        crudService.deleteAll(ProductRateOffsetOverride.class);
    }

    private void registerAgileRateChangeSyncEvent() {
        syncEventAggregatorService.registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
    }

    public void registerPricingOverrideSyncEvent() {
        syncEventAggregatorService.registerSyncEvent(SyncEvent.AGILE_RATES_OVERRIDE_CHANGED);
    }
    public boolean isPricingOverrideIDPFlowBasedSyncEnabled() {
        return configParamsService.getBooleanParameterValue(
                PreProductionConfigParamName.PRICING_OVERRIDE_IDP_FLOW_BASED_SYNC_ENABLED);
    }
    public boolean isPriceBetweenFloorAndCeilingMultiDay(Product primaryProduct, LocalDate startDate, LocalDate endDate,
                                                         AccomType accomType, BigDecimal upperBoundOverride,
                                                         BigDecimal lowerBoundOverride, Set<DayOfWeek> daysOfTheWeek) {

        List<CPUnqualifedDemandForecastPrice> demandForecastBetweenDatesForAccomClass =
                findDemandForecastBetweenDatesForAccomClass(primaryProduct, accomType.getAccomClass(), startDate, endDate);
        if (!demandForecastBetweenDatesForAccomClass.isEmpty()) {

            CeilingFloor ceilingFloor = calculateCeilingAndFloor(primaryProduct.getId(), startDate, endDate, accomType, daysOfTheWeek, demandForecastBetweenDatesForAccomClass);

            return isOverrideBetween(upperBoundOverride, ceilingFloor.getCeiling(), ceilingFloor.getFloor())
                    && isOverrideBetween(lowerBoundOverride, ceilingFloor.getCeiling(), ceilingFloor.getFloor());

        }
        return true;
    }

    public boolean isPriceBetweenFloorAndCeilingMultiDay(List<CPUnqualifedDemandForecastPrice> demandForecastBetweenDatesForAccomClass, Integer primaryProductId,
                                                         AccomType accomType, BigDecimal upperBoundOverride,
                                                         BigDecimal lowerBoundOverride, Set<DayOfWeek> daysOfTheWeek, Map<LocalDate, Tax> taxesByDate,
                                                         Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplement,
                                                         Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetsForDates,
                                                         java.time.LocalDate startDate, java.time.LocalDate endDate) {
        if (CollectionUtils.isNotEmpty(demandForecastBetweenDatesForAccomClass)) {
            OccupancyType baseOccupancyType = pricingConfigurationService.getBaseOccupancyType();
            CeilingFloor ceilingFloor = getCeilingFloor(primaryProductId, accomType, daysOfTheWeek, demandForecastBetweenDatesForAccomClass, taxesByDate, supplement, offsetsForDates, baseOccupancyType);

            if (baseOccupancyType.isApplyRoundingRules()) {
                CPDecisionContext cpDecisionContext = pricingConfigurationService.getCPDecisionContext(new LocalDate(startDate.toString()), new LocalDate(endDate.toString()), false, baseOccupancyType);
                ceilingFloor.setCeiling(cpDecisionContext.calculatePrettyPrice(primaryProductId, ceilingFloor.getCeiling()));
                ceilingFloor.setFloor(cpDecisionContext.calculatePrettyPrice(primaryProductId, ceilingFloor.getFloor()));
            }

            return isOverrideBetween(upperBoundOverride, ceilingFloor.getCeiling(), ceilingFloor.getFloor())
                    && isOverrideBetween(lowerBoundOverride, ceilingFloor.getCeiling(), ceilingFloor.getFloor());

        }
        return true;
    }

    public List<CPUnqualifedDemandForecastPrice> findDemandForecastBetweenDatesForAccomClass(Product product, AccomClass accomClass,
                                                                                             LocalDate startDate,
                                                                                             LocalDate endDate) {
        return crudService.findByNamedQuery(CPUnqualifedDemandForecastPrice.GET_DEMAND_FORECAST_FOR_ACCOM_CLASS,
                CPUnqualifedDemandForecastPrice.params(product, accomClass, startDate, endDate));
    }

    private CeilingFloor calculateCeilingAndFloor(Integer productId, LocalDate startDate, LocalDate endDate, AccomType
            accomType, Set<DayOfWeek> daysOfTheWeek, List<CPUnqualifedDemandForecastPrice> demandForecastBetweenDatesForAccomClass) {
        Map<LocalDate, Tax> taxesByDate = getTaxesForDateRange(startDate, endDate);
        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplement = isSupplementEnabled() ? getSupplement(startDate, endDate) : null;
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetsForDates = getOffsetsForDates(startDate, endDate);

        return getCeilingFloor(productId, accomType, daysOfTheWeek, demandForecastBetweenDatesForAccomClass, taxesByDate, supplement, offsetsForDates);
    }

    public Map<LocalDate, Tax> getTaxesForDateRange(LocalDate startDate, LocalDate endDate) {
        return taxService.findTaxesForDateRange(startDate, endDate);
    }

    public Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> getOffsetsForDates(LocalDate startDate, LocalDate endDate) {
        return pricingConfigurationService.findOffsetsForDates(startDate, endDate);
    }

    private CeilingFloor getCeilingFloor(Integer productId, AccomType accomType, Set<DayOfWeek> daysOfTheWeek, List<CPUnqualifedDemandForecastPrice> demandForecastBetweenDatesForAccomClass, Map<LocalDate, Tax> taxesByDate, Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplement, Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetsForDates) {
        OccupancyType baseOccupancyType = pricingConfigurationService.getBaseOccupancyType();
        return getCeilingFloor(productId, accomType, daysOfTheWeek, demandForecastBetweenDatesForAccomClass, taxesByDate, supplement, offsetsForDates, baseOccupancyType);
    }

    private CeilingFloor getCeilingFloor(Integer productId, AccomType accomType, Set<DayOfWeek> daysOfTheWeek, List<CPUnqualifedDemandForecastPrice> demandForecastBetweenDatesForAccomClass, Map<LocalDate, Tax> taxesByDate, Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplement, Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetsForDates, OccupancyType baseOccupancyType) {
        CeilingFloor ceilingFloor = new CeilingFloor();
        ceilingFloor.setCeiling(BigDecimal.valueOf(Integer.MAX_VALUE));
        ceilingFloor.setFloor(BigDecimal.ZERO);

        //group them by arrival date
        Map<LocalDate, List<CPUnqualifedDemandForecastPrice>> groupByArrivalDate =
                demandForecastBetweenDatesForAccomClass.stream()
                        .filter(cpUnqualifiedDemandForecastPrice -> daysOfTheWeek.contains
                                (DayOfWeek.valueOf(cpUnqualifiedDemandForecastPrice.getArrivalDate().getDayOfWeek())))
                        .collect(Collectors.groupingBy
                                (CPUnqualifedDemandForecastPrice::getArrivalDate));

        groupByArrivalDate.forEach((arrivalDate, cpUnqualifedDemandForecastPrices) -> {
            CPConfigMergedOffset cpConfigMergedOffset =
                    offsetsForDates.get(new CPConfigMergedOffsetPK(arrivalDate, productId, accomType.getId(), baseOccupancyType));
            Tax tax = taxesByDate.get(arrivalDate);
            CPUnqualifedDemandForecastPrice floorDemandForecast = cpUnqualifedDemandForecastPrices.get(0);
            CPUnqualifedDemandForecastPrice ceilingDemandForecast = cpUnqualifedDemandForecastPrices.get(9);

            BigDecimal ceilingValue = applyOffsetTaxAndSupplement(ceilingDemandForecast.getProduct().getId(), baseOccupancyType, tax, supplement, cpConfigMergedOffset,
                    ceilingDemandForecast,
                    accomType.getId());
            BigDecimal floorValue = applyOffsetTaxAndSupplement(floorDemandForecast.getProduct().getId(), baseOccupancyType, tax, supplement, cpConfigMergedOffset,
                    floorDemandForecast,
                    accomType.getId());

            if (ceilingValue.compareTo(ceilingFloor.getCeiling()) < 0) {
                ceilingFloor.setCeiling(ceilingValue);
            }

            if (floorValue.compareTo(ceilingFloor.getFloor()) > 0) {
                ceilingFloor.setFloor(floorValue);
            }
        });
        return ceilingFloor;
    }

    protected BigDecimal applyOffsetTaxAndSupplement(
            Integer productId,
            OccupancyType baseOccupancyType,
            Tax tax,
            Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplementValueMap,
            CPConfigMergedOffset offset,
            CPUnqualifedDemandForecastPrice forecastPrice,
            Integer accomTypeId) {
        BigDecimal value = forecastPrice.getRate();
        if (offset != null) {
            if (offset.getOffsetMethod().equals(OffsetMethod.FIXED_OFFSET)) {
                // we need to apply tax rate to room type rate before adding the offset. The offset should
                // already have tax applied
                value = tax.applyRoomTaxRate(value);
                value = value.add(offset.getOffsetValueForPrice(value));
            } else if (offset.getOffsetMethod().equals(OffsetMethod.PERCENTAGE)) {
                value = value.add(offset.getOffsetValueForPrice(value));
                if (tax != null) {
                    value = tax.applyRoomTaxRate(value);
                }
            }
        } else if (tax != null) {
            value = tax.applyRoomTaxRate(value);
        }

        boolean isSupplementPercentageEnabled =
                configParamsService.getBooleanParameterValue(
                        PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT);
        if (supplementValueMap != null) {
            if (!isSupplementPercentageEnabled) {
                BigDecimal supplementValue =
                        getSupplementValue(
                                productId,
                                supplementValueMap,
                                forecastPrice.getArrivalDate(),
                                accomTypeId,
                                baseOccupancyType);
                value = value.add(supplementValue);
            } else {
                AccomTypeSupplementValue accomTypeSupplement =
                        supplementValueMap.get(
                                new AccomTypeSupplementValuePK(
                                        productId, forecastPrice.getArrivalDate(), accomTypeId, baseOccupancyType));
                value = Supplement.addSupplementTo(value, accomTypeSupplement);
            }
        }
        return value;
    }

    protected BigDecimal getSupplementValue
            (Integer productId, Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplements, LocalDate arrivalDate, Integer
                    accomTypeId, OccupancyType occupancyType) {
        // Get the supplement value for the date, accomType/occupancyType (already tax-inclusive)
        BigDecimal supplement = BigDecimal.ZERO;
        AccomTypeSupplementValue supplementValue = supplements.get(new AccomTypeSupplementValuePK(productId, arrivalDate, accomTypeId, occupancyType));
        if (supplementValue != null) {
            supplement = supplementValue.getValue();
        }

        return supplement;
    }

    private boolean isOverrideBetween(BigDecimal value, BigDecimal upperBound, BigDecimal lowerBound) {
        return value == null || (lowerBound.doubleValue() <= value.doubleValue() && value.doubleValue() <= upperBound.doubleValue());
    }

    protected Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> getSupplement(LocalDate startDate, LocalDate
            endDate) {
        if (isSupplementEnabled()) {
            return accomTypeSupplementService.getSupplementValueMap(startDate, endDate);
        }
        return null;
    }

    public OptimizationLevel getOptimizationLevel() {
        return agileRatesConfigurationService.getOptimizationLevel();
    }


    public Map<Integer, Set<String>> getWebRateToProductMap(Map<Integer, Product> allProducts, Date occupancyDate) {
        Map<Integer, Set<String>> webRateProductMap = new HashMap<>();
        List<Object[]> webRateIdsWithProductIds = crudService.findByNativeQuery(VW_QUERY_TO_FETCH_PRODUCT_ID,
                QueryParameter.with(SELECTED_DATE, occupancyDate).and(PRODUCT_IDS, allProducts.keySet()).parameters());

        for (Object[] webRateIdsWithProductId : webRateIdsWithProductIds) {
            Integer webRateId = (Integer) webRateIdsWithProductId[0];
            BigInteger productId = (BigInteger) webRateIdsWithProductId[1];
            String productName = allProducts.get(productId.intValue()).getName();
            Set<String> productNamesInWebRateExisting = new HashSet<>();
            if (ObjectUtils.isNotEmpty(webRateProductMap.get(webRateId))) {
                productNamesInWebRateExisting = webRateProductMap.get(webRateId);
            }
            productNamesInWebRateExisting.add(productName);
            webRateProductMap.put(webRateId, productNamesInWebRateExisting);
        }
        return webRateProductMap;
    }

    public Map<Integer, Product> getAllProducts() {
        List<Product> allProducts = crudService.findByNamedQuery(Product.GET_ALL);
        return allProducts.stream().collect(Collectors.toMap(Product::getId, Function.identity()));
    }

    public Map<Integer, AccomType> getAllAccomTypes() {
        return accommodationService.getAllAccomTypes().stream()
                .collect(Collectors.toMap(AccomType::getId, Function.identity()));
    }

    public List<ProductDetailsDTO> getBaseProductDetails() {
        List<Product> allBaseProducts = findAllBaseProducts();
        if (allBaseProducts.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Integer, List<String>> productToRateCodesMap = getProductToRateCodesMap(allBaseProducts);
        Map<Integer, List<Integer>> productToAccomTypesMap = getProductsToAccomTypeMap(allBaseProducts);

        return allBaseProducts.stream()
                .map(product -> buildProductDetailsDTO(product, productToRateCodesMap, productToAccomTypesMap))
                .collect(Collectors.toList());
    }

    public ProductDetailsDTO buildProductDetailsDTO(Product product,
                                                     Map<Integer, List<String>> productToRateCodesMap,
                                                     Map<Integer, List<Integer>> productToAccomTypesMap) {
        ProductDetailsDTO productDetailsDTO = new ProductDetailsDTO();
        populateProductDetails(product, productDetailsDTO, productToRateCodesMap, productToAccomTypesMap);
        return productDetailsDTO;
    }

    public void populateProductDetails(Product product, ProductDetailsDTO dto,
                                      Map<Integer, List<String>> productToRateCodesMap,
                                      Map<Integer, List<Integer>> productToAccomTypesMap) {
        dto.setId(product.getId());
        dto.setName(product.getName());
        dto.setDescription(product.getDescription());
        dto.setCode(product.getCode());
        dto.setType(product.getType());
        dto.setDisplayOrder(product.getDisplayOrder());
        dto.setStatus(product.getStatus());
        dto.setDecisionUploadEnabled(product.isSystemDefault() ? true : product.isUpload());
        dto.setOverridableType(product.getIsOverridable());
        dto.setMinimumLOS(product.getMinLOS());
        dto.setMaximumLOS(product.getMaxLOS());
        dto.setRateCodes(productToRateCodesMap.getOrDefault(product.getId(), new ArrayList<>()));
        dto.setRoomTypeIds(productToAccomTypesMap.getOrDefault(product.getId(), new ArrayList<>()));
        dto.setRoundingRules(getRoundingRulesDTO(product));
        dto.setHierarchy(getProductHierarchyList(product));
        dto.setFloorCeilDetails(getFloorCeiling(product));
        dto.setOffsetDetails(getProductOffsets(product));
        dto.setSupplement(getProductSupplements(product));
        dto.setPriceChangeRange(getPriceChangeRange(product));
        dto.setRateShoppingMinimumLOS(product.getRateShoppingLOSMin());
        dto.setRateShoppingMaximumLOS(product.getRateShoppingLOSMax());
        dto.setLinkProducts(getLinkedProducts(product));
        dto.setRateShoppingAllLOS(isRateShoppingAllLOSApplicable(product));
        dto.setPrimaryPriced(product.isSystemDefault());
    }

    public boolean isRateShoppingAllLOSApplicable(Product product){
        return  (product.getRateShoppingLOSMax() != null && product.getRateShoppingLOSMax() == -1) &&
                (product.getRateShoppingLOSMin() != null && product.getRateShoppingLOSMin() == -1);
    }

    public List<LinkProductsDTO> getLinkedProducts(Product product) {
        List<Product> childLinkedProducts = getAllChildLinkedProducts(product.getId());
        if(childLinkedProducts.isEmpty()){
            return Collections.emptyList();
        }
        Map<Integer, List<String>> linkedProductToRateCodesMap = getProductToRateCodesMap(childLinkedProducts);
        Map<Integer, List<Integer>> linkedProductToAccomTypesMap = getProductsToAccomTypeMap(childLinkedProducts);

        return childLinkedProducts.stream()
                .map(child -> buildLinkProductDTO(child, linkedProductToRateCodesMap, linkedProductToAccomTypesMap))
                .collect(Collectors.toList());
    }

    public LinkProductsDTO buildLinkProductDTO(Product product,
                                                Map<Integer, List<String>> linkedRateCodesMap,
                                                Map<Integer, List<Integer>> linkedAccomTypesMap) {
        LinkProductsDTO dto = new LinkProductsDTO();
        dto.setId(product.getId());
        dto.setName(product.getName());
        dto.setDescription(product.getDescription());
        dto.setCode(product.getCode());
        dto.setType(product.getType());
        dto.setDisplayOrder(product.getDisplayOrder());
        dto.setStatus(product.getStatus());
        dto.setDecisionUploadEnabled(product.isUpload());
        dto.setOverridableType(product.getIsOverridable());
        dto.setMinimumLOS(product.getMinLOS());
        dto.setMaximumLOS(product.getMaxLOS());
        dto.setRateCodes(linkedRateCodesMap.getOrDefault(product.getId(), new ArrayList<>()));
        dto.setRoomTypeIds(linkedAccomTypesMap.getOrDefault(product.getId(), new ArrayList<>()));
        dto.setRoundingRules(getRoundingRulesDTO(product));
        dto.setDependentProductId(product.getDependentProductId());
        dto.setMinimumDaysToArrival(product.getMinDTA());
        dto.setMaximumDaysToArrival(product.getMaxDTA());
        dto.setMinimumPriceChange(product.getMinimumPriceChange());
        dto.setFloorType(product.getFloorType());
        dto.setFloorRate(product.getFloor());
        dto.setFloorPercentage(product.getFloorPercentage());
        dto.setOffsetMethod(product.getOffsetMethod());
        dto.setOffsetForExtraAdult(product.isOffsetForExtraAdult());
        dto.setOffsetForExtraChild(product.isOffsetForExtraChild());
        dto.setDecisionsSentBy(product.getDecisionsSentBy());
        dto.setPackages(getProductPackage(product));
        dto.setProductRates(getProductRateOffsets(product));
        dto.setOptimized(product.isOptimized());
        dto.setSeasonalProductOnly(product.isDefaultInactive());

        return dto;
    }


    public List<PriceChangeRangeDTO> getPriceChangeRange(Product product) {

        List<ProductFlatRateSeason> productFlatRateSeason = crudService.findByNamedQuery(ProductFlatRateSeason.GET_ALL_SEASONS_BY_PRODUCT_ID, QueryParameter.with("productId", product.getId()).parameters());
        if(productFlatRateSeason.isEmpty()){
            return Collections.emptyList();
        }
        return productFlatRateSeason.stream()
                .map(this::createPriceChangeRangeDTO)
                .collect(Collectors.toList());
    }

    public PriceChangeRangeDTO createPriceChangeRangeDTO(ProductFlatRateSeason season) {
        PriceChangeRangeDTO dto = new PriceChangeRangeDTO();
        dto.setStartDate(season.getStartDate() != null ? season.getStartDate() : null);
        dto.setEndDate(season.getEndDate() != null ? season.getEndDate() : null);
        dto.setFrequency(season.getFrequency());
        return dto;
    }


    public List<SupplementDTO> getProductSupplements(Product product) {

        List<AccomTypeSupplement> supplements = fetchSupplements(product);
        if(supplements.isEmpty()){
            return Collections.emptyList();
        }

        Map<String, List<AccomTypeSupplement>> groupedBySeason = supplements.stream()
                .collect(Collectors.groupingBy(supplement ->
                        (supplement.getName() == null) ? DEFAULT : supplement.getName()));

        return groupedBySeason.entrySet().stream()
                .map(entry -> createSupplementDTO(entry.getKey(), entry.getValue()))
                .sorted(Comparator.comparing(SupplementDTO::getSeasonName, Comparator.nullsFirst(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }

    public List<AccomTypeSupplement> fetchSupplements(Product product) {
        return crudService.findByNamedQuery(
                AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT,
                QueryParameter.with("productID", product.getId()).parameters());
    }

    public SupplementDTO createSupplementDTO(String seasonName, List<AccomTypeSupplement> seasonData) {
        SupplementDTO dto = new SupplementDTO();
        dto.setSeasonName(seasonName.equals(DEFAULT) ? null : seasonName);
        dto.setStartDate(seasonData.get(0).getStartDate() == null ? null : DateUtil.convertJodaToJavaLocalDate(seasonData.get(0).getStartDate()));
        dto.setEndDate(seasonData.get(0).getEndDate() == null ? null : DateUtil.convertJodaToJavaLocalDate(seasonData.get(0).getEndDate()));

        Map<Integer, List<AccomTypeSupplement>> groupedByRoomType = seasonData.stream()
                .collect(Collectors.groupingBy(supplement -> supplement.getAccomType().getId()));

        List<SupplementByRoomTypeDTO> roomTypeDTOs = groupedByRoomType.entrySet().stream()
                .map(this::createSupplementByRoomTypeDTO)
                .collect(Collectors.toList());

        dto.setSupplementByRoomTypes(roomTypeDTOs);
        return dto;
    }

    public SupplementByRoomTypeDTO createSupplementByRoomTypeDTO(Map.Entry<Integer, List<AccomTypeSupplement>> roomEntry) {
        SupplementByRoomTypeDTO roomDTO = new SupplementByRoomTypeDTO();
        roomDTO.roomTypeId = roomEntry.getKey();

        roomDTO.supplementValues = roomEntry.getValue().stream()
                .map(this::createSupplementValueDTO)
                .collect(Collectors.toList());
        return roomDTO;
    }

    public SupplementValueDTO createSupplementValueDTO(AccomTypeSupplement supplement) {
        SupplementValueDTO valueDTO = new SupplementValueDTO();
        valueDTO.setOffsetMethod(supplement.getOffsetMethod());
        valueDTO.setOccupancyType(supplement.getOccupancyType());
        valueDTO.setSundaySupplementValue(supplement.getSundaySupplementValue());
        valueDTO.setMondaySupplementValue(supplement.getMondaySupplementValue());
        valueDTO.setTuesdaySupplementValue(supplement.getTuesdaySupplementValue());
        valueDTO.setWednesdaySupplementValue(supplement.getWednesdaySupplementValue());
        valueDTO.setThursdaySupplementValue(supplement.getThursdaySupplementValue());
        valueDTO.setFridaySupplementValue(supplement.getFridaySupplementValue());
        valueDTO.setSaturdaySupplementValue(supplement.getSaturdaySupplementValue());
        return valueDTO;
    }

    public List<OffsetDetailsDTO> getProductOffsets(Product product) {
        List<CPConfigOffsetAccomType> offsets = crudService.findByNamedQuery(CPConfigOffsetAccomType.GET_OFFSET_CONFIG_FOR_PROPERTY,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and("productID", product.getId()).parameters());
        if (offsets.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, List<CPConfigOffsetAccomType>> offsetsGroupedBySeasons = offsets.stream()
                .collect(Collectors.groupingBy(entry -> (entry.getName() == null) ? DEFAULT : entry.getName()));
        return offsetsGroupedBySeasons.entrySet().stream().map(this::createOffsetDetails)
                .sorted(Comparator.comparing(OffsetDetailsDTO::getSeasonName, Comparator.nullsFirst(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }

    public OffsetDetailsDTO createOffsetDetails(Map.Entry<String, List<CPConfigOffsetAccomType>> entry) {
        String seasonName = entry.getKey();
        List<CPConfigOffsetAccomType> seasonData = entry.getValue();

        OffsetDetailsDTO dto = new OffsetDetailsDTO();
        dto.setSeasonName(seasonName.equals(DEFAULT) ? null : seasonName);
        dto.setStartDate(seasonData.get(0).getStartDate() == null ? null : DateUtil.convertJodaToJavaLocalDate(seasonData.get(0).getStartDate()));
        dto.setEndDate(seasonData.get(0).getEndDate() == null ? null : DateUtil.convertJodaToJavaLocalDate(seasonData.get(0).getEndDate()));

        Map<Integer, List<CPConfigOffsetAccomType>> groupedByRoomType = seasonData.stream()
                .collect(Collectors.groupingBy(offset -> offset.getAccomType().getId()));

        List<OffsetByRoomTypeDTO> roomTypeDTOs = groupedByRoomType.entrySet().stream()
                .map(this::createOffsetByRoomTypeDTO)
                .collect(Collectors.toList());

        dto.setOffsetByRoomType(roomTypeDTOs);
        return dto;
    }
    public OffsetByRoomTypeDTO createOffsetByRoomTypeDTO(Map.Entry<Integer, List<CPConfigOffsetAccomType>> roomEntry) {
        OffsetByRoomTypeDTO roomDTO = new OffsetByRoomTypeDTO();
        roomDTO.setRoomTypeId(roomEntry.getKey());

        List<OffsetValueDTO> offsetValues = roomEntry.getValue().stream()
                .map(this::createOffsetValueDTO)
                .collect(Collectors.toList());

        roomDTO.setOffsetValues(offsetValues);
        return roomDTO;
    }

    public OffsetValueDTO createOffsetValueDTO(CPConfigOffsetAccomType offset) {
        OffsetValueDTO valueDTO = new OffsetValueDTO();
        valueDTO.setOffsetMethod(offset.getOffsetMethod());
        valueDTO.setOccupancyType(offset.getOccupancyType());
        valueDTO.setSundayValueWithTax(offset.getSundayOffsetValueWithTax());
        valueDTO.setMondayValueWithTax(offset.getMondayOffsetValueWithTax());
        valueDTO.setTuesdayValueWithTax(offset.getTuesdayOffsetValueWithTax());
        valueDTO.setWednesdayValueWithTax(offset.getWednesdayOffsetValueWithTax());
        valueDTO.setThursdayValueWithTax(offset.getThursdayOffsetValueWithTax());
        valueDTO.setFridayValueWithTax(offset.getFridayOffsetValueWithTax());
        valueDTO.setSaturdayValueWithTax(offset.getSaturdayOffsetValueWithTax());
        return valueDTO;
    }

    public List<FloorCeilDetailsDTO> getFloorCeiling(Product product) {
        List<TransientPricingBaseAccomType> floorCeiling = crudService.findByNamedQuery(
                TransientPricingBaseAccomType.FIND_ALL_BY_PRODUCT_ID, QueryParameter.with("productID", product.getId()).parameters());

        if (floorCeiling.isEmpty()) {
            return Collections.emptyList();
        }

        Map<String, List<TransientPricingBaseAccomType>> groupedBySeason = groupFloorCeilingBySeason(floorCeiling);

        return groupedBySeason.entrySet().stream()
                .map(entry -> buildFloorCeilDetailsDTO(entry.getKey(), entry.getValue()))
                .sorted(Comparator.comparing(FloorCeilDetailsDTO::getSeasonName, Comparator.nullsFirst(String::compareTo)))
                .collect(Collectors.toList());
    }

    public Map<String, List<TransientPricingBaseAccomType>> groupFloorCeilingBySeason(List<TransientPricingBaseAccomType> floorCeiling) {
        return floorCeiling.stream()
                .collect(Collectors.groupingBy(entry ->
                        Optional.ofNullable(entry.getSeasonName()).orElse(DEFAULT)
                ));
    }

    public FloorCeilDetailsDTO buildFloorCeilDetailsDTO(String seasonName, List<TransientPricingBaseAccomType> seasonData) {
        FloorCeilDetailsDTO dto = new FloorCeilDetailsDTO();
        dto.setSeasonName(DEFAULT.equals(seasonName) ? null : seasonName);
        dto.setStartDate(seasonData.get(0).getStartDate() == null ? null : DateUtil.convertJodaToJavaLocalDate(seasonData.get(0).getStartDate()));
        dto.setEndDate(seasonData.get(0).getEndDate() == null ? null : DateUtil.convertJodaToJavaLocalDate(seasonData.get(0).getEndDate()));
        dto.setFloorCeilValues(buildFloorCeilValuesList(seasonData));

        return dto;
    }

    public List<FloorCeilValuesDTO> buildFloorCeilValuesList(List<TransientPricingBaseAccomType> seasonData) {
        return seasonData.stream()
                .map(this::buildFloorCeilValuesDTO)
                .collect(Collectors.toList());
    }

    public FloorCeilValuesDTO buildFloorCeilValuesDTO(TransientPricingBaseAccomType floorCeilingDTO) {
        FloorCeilValuesDTO valuesDTO = new FloorCeilValuesDTO();
        valuesDTO.setRoomTypeId(floorCeilingDTO.getAccomType().getId());
        valuesDTO.setSundayCeilingRateWithTax(floorCeilingDTO.getSundayCeilingRateWithTax());
        valuesDTO.setSundayFloorRateWithTax(floorCeilingDTO.getSundayFloorRateWithTax());
        valuesDTO.setMondayCeilingRateWithTax(floorCeilingDTO.getMondayCeilingRateWithTax());
        valuesDTO.setMondayFloorRateWithTax(floorCeilingDTO.getMondayFloorRateWithTax());
        valuesDTO.setTuesdayCeilingRateWithTax(floorCeilingDTO.getTuesdayCeilingRateWithTax());
        valuesDTO.setTuesdayFloorRateWithTax(floorCeilingDTO.getTuesdayFloorRateWithTax());
        valuesDTO.setWednesdayCeilingRateWithTax(floorCeilingDTO.getWednesdayCeilingRateWithTax());
        valuesDTO.setWednesdayFloorRateWithTax(floorCeilingDTO.getWednesdayFloorRateWithTax());
        valuesDTO.setThursdayCeilingRateWithTax(floorCeilingDTO.getThursdayCeilingRateWithTax());
        valuesDTO.setThursdayFloorRateWithTax(floorCeilingDTO.getThursdayFloorRateWithTax());
        valuesDTO.setFridayCeilingRateWithTax(floorCeilingDTO.getFridayCeilingRateWithTax());
        valuesDTO.setFridayFloorRateWithTax(floorCeilingDTO.getFridayFloorRateWithTax());
        valuesDTO.setSaturdayCeilingRateWithTax(floorCeilingDTO.getSaturdayCeilingRateWithTax());
        valuesDTO.setSaturdayFloorRateWithTax(floorCeilingDTO.getSaturdayFloorRateWithTax());
        return valuesDTO;
    }

    public List<HierarchyDTO> getProductHierarchyList(Product product) {
        List<ProductHierarchy> productHierarchy = crudService.findByNamedQuery(ProductHierarchy.BY_PRODUCT_ID, QueryParameter.with("productId", product.getId()).parameters());
        if(productHierarchy.isEmpty()){
            return Collections.emptyList();
        }
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        AccomClass masterAccomClass = accommodationService.findMasterClass(propertyId);

        return  buildHierarchyDTOList(productHierarchy, masterAccomClass);
    }

    public List<HierarchyDTO> buildHierarchyDTOList(List<ProductHierarchy> hierarchies, AccomClass masterAccomClass) {
        return hierarchies.stream()
                .map(hierarchy -> {
                    BigDecimal sundayMinPriceDiff = getSundayMinPriceDiff(hierarchy, masterAccomClass);
                    HierarchyDTO dto = new HierarchyDTO();
                    dto.setFromProductId(hierarchy.getFromProduct().getId());
                    dto.setToProductId(hierarchy.getToProduct().getId());
                    dto.setMinimumPriceDifference(sundayMinPriceDiff);
                    return dto;
                })
                .collect(Collectors.toList());
    }
    public BigDecimal getSundayMinPriceDiff(ProductHierarchy hierarchy, AccomClass masterAccomClass) {
        return hierarchy.getProductMinPriceDiffList().stream()
                .filter(diff -> diff.getAccomClass().getId().equals(masterAccomClass.getId()))
                .map(ProductMinPriceDiff::getSundayDiffWithTax)
                .findFirst()
                .orElse(BigDecimal.ZERO);
    }

    public List<ProductRateOffsetsDTO> getProductRateOffsets(Product product) {
        List<ProductRateOffset> productRateOffsets = agileRatesConfigurationService.findProductRateOffsetsByProduct(product);
        if(productRateOffsets.isEmpty()){
            return Collections.emptyList();
        }

        return  productRateOffsets.stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }


    public ProductRateOffsetsDTO mapToDto(ProductRateOffset offset) {
        ProductRateOffsetsDTO dto = new ProductRateOffsetsDTO();
        dto.setRoomClassId(offset.getAccomClass() != null ? offset.getAccomClass().getId() : null);
        dto.setSeasonName(offset.getSeasonName());
        dto.setSeasonDowOffset(offset.isSeasonDowOffset());
        dto.setStartDate(offset.getStartDate() == null ? null : DateUtil.convertJodaToJavaLocalDate(offset.getStartDate()));
        dto.setEndDate(offset.getEndDate() == null ? null : DateUtil.convertJodaToJavaLocalDate(offset.getEndDate()));
        dto.setMinimumDaysToArrival(offset.getAgileRatesDTARange() == null ? null : offset.getAgileRatesDTARange().getMinDaysToArrival());
        dto.setMaximumDaysToArrival(offset.getAgileRatesDTARange() == null ? null : offset.getAgileRatesDTARange().getMaxDaysToArrival());
        dto.setOffsetMethod(offset.getOffsetMethod());
        dto.setSundayOffsetValueFloor(offset.getSundayOffsetValueFloor());
        dto.setSundayOffsetValueCeiling(offset.getSundayOffsetValueCeiling());
        dto.setMondayOffsetValueFloor(offset.getMondayOffsetValueFloor());
        dto.setMondayOffsetValueCeiling(offset.getMondayOffsetValueCeiling());
        dto.setTuesdayOffsetValueFloor(offset.getTuesdayOffsetValueFloor());
        dto.setTuesdayOffsetValueCeiling(offset.getTuesdayOffsetValueCeiling());
        dto.setWednesdayOffsetValueFloor(offset.getWednesdayOffsetValueFloor());
        dto.setWednesdayOffsetValueCeiling(offset.getWednesdayOffsetValueCeiling());
        dto.setThursdayOffsetValueFloor(offset.getThursdayOffsetValueFloor());
        dto.setThursdayOffsetValueCeiling(offset.getThursdayOffsetValueCeiling());
        dto.setFridayOffsetValueFloor(offset.getFridayOffsetValueFloor());
        dto.setFridayOffsetValueCeiling(offset.getFridayOffsetValueCeiling());
        dto.setSaturdayOffsetValueFloor(offset.getSaturdayOffsetValueFloor());
        dto.setSaturdayOffsetValueCeiling(offset.getSaturdayOffsetValueCeiling());
        return dto;
    }

    public List<ProductPackageDTO> getProductPackage(Product product) {

        List<AgileRatesPackage> agileRatesPackages =  crudService.findByNamedQuery(AgileRatesPackage.GET_AGILE_RATES_PACKAGES_BY_PRODUCT_ID, QueryParameter.with("productId", product.getId()).parameters());
        if(agileRatesPackages.size() == 0){
            return Collections.emptyList();
        }
        return agileRatesPackages.stream()
                .map(agileRatesPackage -> {
                    ProductPackageDTO dto = new ProductPackageDTO();
                    dto.setPackageName(agileRatesPackage.getName());
                    dto.setDescription(agileRatesPackage.getDescription());
                    dto.setChargeType(agileRatesPackage.getChargeType());
                    dto.setOffsetMethod(agileRatesPackage.getOffsetMethod());
                    dto.setOffsetValue(agileRatesPackage.getOffsetValue());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    public RoundingRulesDTO getRoundingRulesDTO(Product product) {
        RoundingRulesDTO roundingRules = new RoundingRulesDTO();
        List<PrettyPricingRulesDTO> prettyPricingRulesDTOList = getRoundingRules(product.getId());
        roundingRules.setRoundingRule(product.getRoundingRule());
        roundingRules.setPrettyPricingRules(prettyPricingRulesDTOList);
        return roundingRules;
    }

    public List<PrettyPricingRulesDTO> getRoundingRules(Integer productId) {
        PricingRule pricingRule = prettyPricingService.getPricingRule(productId);
        if (pricingRule == null || pricingRule.getRules() == null) {
            return Collections.emptyList();
        }
        List<PrettyPricingRulesDTO> dtoList = new ArrayList<>();
        for (Map.Entry<PricingDigit, PrettyPricingRuleRow> entry : pricingRule.getRules().entrySet()) {
            PrettyPricingRulesDTO dto = new PrettyPricingRulesDTO();
            dto.setDigit(entry.getKey());
            dto.setRuleNumbers(entry.getValue().getRuleNumbers());
            dtoList.add(dto);
        }
        return dtoList;
    }

    public List<Product> getAllChildLinkedProducts(Integer productId) {
        return crudService. findByNativeQuery(GET_ALL_CHILD_LINKED_PRODUCTS,QueryParameter.with("productId", productId).parameters(), Product.class);
    }

    public Map<Integer, List<Integer>> getProductsToAccomTypeMap(List<Product> products) {
        if(products.isEmpty()){
            return Collections.emptyMap();
        }
        List<ProductAccomType> productAccomType = crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS,  QueryParameter.with("products", products).parameters());
        return productAccomType.stream()
                .collect(Collectors.groupingBy(
                        productAccomTypes -> productAccomTypes.getProduct().getId(),
                        Collectors.mapping(productAccomTypes -> productAccomTypes.getAccomType().getId(), Collectors.toList())
                ));
    }

    public Map<Integer, List<String>> getProductToRateCodesMap(List<Product> products) {
        if(products.isEmpty()){
            return Collections.emptyMap();
        }
        List<ProductRateCode> productRateCodes = crudService.findByNamedQuery(ProductRateCode.BY_PRODUCTS,  QueryParameter.with("products", products).parameters());
        return productRateCodes.stream()
                .collect(Collectors.groupingBy(
                        productRateCode -> productRateCode.getProduct().getId(),
                        Collectors.mapping(ProductRateCode::getRateCode, Collectors.toList())
                ));
    }

    public List<Product> findAllBaseProducts() {
        return crudService.findByNamedQuery(Product.GET_ALL_BASE_PRODUCTS);
    }

    public void registerSyncForAgileProducts(List<ProductRateOffsetOverride> productRateOffsetOverride) {
        if (productRateOffsetOverride == null || productRateOffsetOverride.isEmpty()) {
            return;
        }

        if (shouldAgileRatesSyncBeFire(productRateOffsetOverride)) {
            if (isPricingOverrideIDPFlowBasedSyncEnabled()) {
                registerPricingOverrideSyncEvent();
            } else {
                registerAgileRateChangeSyncEvent();
            }
        }
    }

    public List<ProductRateOffsetOverride> deletePricingOverridesForOptimizedProducts(AccomClass accomClass, Product product, LocalDate startDate, LocalDate endDate) {
        Set<AccomClass> accomClasses = OptimizationLevel.SAME_FOR_ALL_ROOM_CLASSES.equals(getOptimizationLevel())
                ? findRoomClassesByProduct(product) : Collections.singleton(accomClass);
        List<ProductRateOffsetOverride> overrides = Optional.ofNullable(getActiveProductRateOffsetOverrides(product, startDate, endDate))
                .orElse(Collections.emptyList())
                .stream().filter(pro -> pro != null && accomClasses.contains(pro.getAccomClass()))
                .collect(Collectors.toList());

        if (!overrides.isEmpty()) {
            deleteProductRateOffsetOverride(overrides);
        }
        return overrides;
    }

    public List<ProductRateOffsetOverride> deletePricingOverridesForOverridesForNonOptimized(Integer accomClassId, Product product, LocalDate startDate, LocalDate endDate) {
        List<ProductRateOffsetOverride> productRateOffsetOverrides = Optional.ofNullable(getActiveProductRateOffsetOverrides(product, startDate, endDate))
                .orElse(Collections.emptyList());
        if (productRateOffsetOverrides.isEmpty()) {
            return productRateOffsetOverrides;
        }
        List<ProductRateOffsetOverride> existingOverridesForRoomClass = productRateOffsetOverrides.stream().filter(override -> accomClassId.equals(override.getAccomClass().getId())).collect(toList());
        if (!existingOverridesForRoomClass.isEmpty()) {
            existingOverridesForRoomClass.forEach(o -> o.setStatusId(Status.INACTIVE.getId()));
            crudService.save(existingOverridesForRoomClass);
        }
        return existingOverridesForRoomClass;
    }

    private class CeilingFloor {
        BigDecimal ceiling;
        BigDecimal floor;

        public BigDecimal getCeiling() {
            return ceiling;
        }

        public void setCeiling(BigDecimal ceiling) {
            this.ceiling = ceiling;
        }

        public BigDecimal getFloor() {
            return floor;
        }

        public void setFloor(BigDecimal floor) {
            this.floor = floor;
        }
    }

    private boolean isSupplementEnabled() {
        return configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value());
    }

    public List<CPPaceDecisionBAROutput> getDecisionsFromLastBDE(Set<Product> products, LocalDate startDate, LocalDate endDate, List<AccomType> accomTypes) {
        Integer decisionId = crudService.findByNamedQuerySingleResult(Decision.GET_LAST_BDE, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        Map<String, Object> parameters = QueryParameter.with("products", products).and("decisionId", decisionId).and("startDate", startDate).and("endDate", endDate).and("accomTypes", accomTypes).and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters();
        return getCpPaceDecisionBAROutputs(parameters);
    }

    public List<CPPaceDecisionBAROutput> getDecisionsFromLastCDP(Set<Product> products, LocalDate startDate, LocalDate endDate, List<AccomType> accomTypes) {
        Integer decisionId = crudService.findByNamedQuerySingleResult(Decision.GET_LAST_CDP, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        Map<String, Object> parameters = QueryParameter.with("products", products).and("decisionId", decisionId).and("startDate", startDate).and("endDate", endDate).and("accomTypes", accomTypes).and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters();
        return getCpPaceDecisionBAROutputs(parameters);
    }

    public List<CPPaceDecisionBAROutput> getDecisionsFromSpecifiedDate(Set<Product> products, LocalDate startDate, LocalDate endDate, List<AccomType> accomTypes, Date filterChangesSinceDate) {
        Integer decisionId = crudService.findByNamedQuerySingleResult(Decision.GET_FIRST_BEFORE_DATE, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and("selectedDate", filterChangesSinceDate).parameters());
        if (decisionId != null) {
            Map<String, Object> parameters = QueryParameter.with("products", products).and("decisionId", decisionId).and("startDate", startDate).and("endDate", endDate).and("accomTypes", accomTypes).and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters();
            return getCpPaceDecisionBAROutputs(parameters);
        }
        return new ArrayList<>();
    }


    private List<CPPaceDecisionBAROutput> getCpPaceDecisionBAROutputs(Map<String, Object> parameters) {
        return getPaceDecisionBAROutputsDifferentialTable(parameters);
    }

    private List<CPPaceDecisionBAROutput> getPaceDecisionBAROutputsDifferentialTable(Map<String, Object> parameters) {
        parameters.remove("propertyId");
        Object dID = parameters.get("decisionId");
        Integer decisionId = null;
        if (dID != null) {
            decisionId = Integer.parseInt(String.valueOf(parameters.get("decisionId")));
        }
        final Integer decisionIdFinal = decisionId;
        Map<Integer, Product> products = getAllProducts();
        Map<Integer, AccomType> accomTypes = getAllAccomTypes();
        parameters.put("products", idsInString(products.keySet()));
        parameters.put("accomTypes", idsInString(accomTypes.keySet()));
        parameters.put("startDate", ((LocalDate) parameters.get("startDate")).toDate());
        parameters.put("endDate", ((LocalDate) parameters.get("endDate")).toDate());
        return Optional.ofNullable(crudService.findByNativeQuery(CPPaceDecisionBAROutputDifferential.GET_BY_SELECTED_DATES, parameters, row -> CPPaceDecisionBAROutput.map(row, decisionIdFinal, products, accomTypes)))
                .orElse(Collections.emptyList());
    }

    private String idsInString(Set<Integer> ids) {
        String idsStr = ids.toString();
        idsStr = idsStr.substring(1, idsStr.length() - 1);
        return idsStr;
    }

    public List<PricingCompetitorRates> getPricingCompetitorRates(LocalDate startDate, LocalDate endDate, String accomClassIdStr, String accomTypeIdStr, List<Integer> competitorIds) {
        //competitorIds should always have 7 entries or it will fail
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("start_date", new java.sql.Date(startDate.toDate().getTime()))
                .and("end_date", new java.sql.Date(endDate.toDate().getTime()))
                .and("RoomClasses", accomClassIdStr)
                .and("RoomTypes", accomTypeIdStr);

        StringBuilder compIdPramString = new StringBuilder();
        String compIdAttribute;

        if (competitorIds != null) {
            for (int i = 0; i < 7; i++) {
                compIdAttribute = "comp_id_" + (i + 1);
                compIdPramString.append(":").append(compIdAttribute).append(",");
                queryParameters.and(compIdAttribute, competitorIds.get(i) == null ? -1 : competitorIds.get(i));
            }
        }

        String sql = "select * from dbo.ufn_get_pricing_competitor_rates (:property_id, :start_date, :end_date, " + compIdPramString + ":RoomClasses, :RoomTypes) order by Arrival_DT, Accom_Class_Name, Accom_Type_Name";
        try {
            return crudService.findByNativeQuery(sql,
                    queryParameters.parameters(), row -> {
                        PricingCompetitorRates data = new PricingCompetitorRates();
                        data.setArrivalDate((Date) row[0]);
                        data.setAccomClassName((String) row[1]);
                        data.setCompRate1((BigDecimal) row[2]);
                        data.setCompName1((String) row[3]);
                        data.setCompStatus1((String) row[4]);

                        data.setCompRate2((BigDecimal) row[5]);
                        data.setCompName2((String) row[6]);
                        data.setCompStatus2((String) row[7]);

                        data.setCompRate3((BigDecimal) row[8]);
                        data.setCompName3((String) row[9]);
                        data.setCompStatus3((String) row[10]);

                        data.setCompRate4((BigDecimal) row[11]);
                        data.setCompName4((String) row[12]);
                        data.setCompStatus4((String) row[13]);

                        data.setCompRate5((BigDecimal) row[14]);
                        data.setCompName5((String) row[15]);
                        data.setCompStatus5((String) row[16]);

                        data.setCompRate6((BigDecimal) row[17]);
                        data.setCompName6((String) row[18]);
                        data.setCompStatus6((String) row[19]);

                        data.setCompRate7((BigDecimal) row[20]);
                        data.setCompName7((String) row[21]);
                        data.setCompStatus7((String) row[22]);

                        data.setAccomTypeName((String) row[23]);

                        return data;
                    });
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<PricingCompetitorRates>();
        }
    }

    public List<WebrateView> getWebrateViewForProductsInDateRange(List<Integer> productIds, List<Integer> competitorIds, LocalDate startDate, LocalDate endDate) {
        return crudService.findByNamedQuery(WebrateView.BY_PRODUCT_AND_COMPETITOR_FOR_DATE_RANGE,
                QueryParameter.with(WebrateView.PARAM_PRODUCT_IDS, productIds)
                        .and(PARAM_COMPETITOR_IDS, competitorIds)
                        .and(WebrateView.PARAM_START_DATE, DateUtil.convertJodaToJavaLocalDate(startDate))
                        .and(WebrateView.PARAM_END_DATE, DateUtil.convertJodaToJavaLocalDate(endDate))
                        .parameters());
    }

    public List<WebrateView> getWebrateViewV2ForProductsInDateRangeForRDLProperties(List<Integer> productIds, List<Integer> competitorIds, java.time.LocalDate startDate, java.time.LocalDate endDate) {
        List<Object[]> result = crudService.findByNativeQuery(VW_QUERY_V2_TO_FETCH_COMPETITOR_RATE_FOR_PRODUCT,
                QueryParameter.with(WebrateView.PARAM_PRODUCT_IDS, productIds)
                        .and(PARAM_COMPETITOR_IDS, competitorIds)
                        .and(WebrateView.PARAM_START_DATE, startDate)
                        .and(WebrateView.PARAM_END_DATE, endDate)
                        .parameters());

        return convertResultObjectOfWebrateViewV2ToWebrateView(CollectionUtils.isNotEmpty(result) ? result : new ArrayList<>());
    }

    private List<WebrateView> convertResultObjectOfWebrateViewV2ToWebrateView(List<Object[]> result) {
        List<WebrateView> webrateViewV2List = new ArrayList<>();
        for (Object[] webrateV2 : result) {
            WebrateView view = new WebrateView();

            WebrateViewPK pk = new WebrateViewPK();
            pk.setWebrateId((Integer) webrateV2[0]);
            pk.setProductId(((BigInteger) webrateV2[22]).intValue());
            view.setId(pk);

            view.setWebrateSourcePropertyId((Integer) webrateV2[1]);
            view.setWebrateCompetitorsId((Integer) webrateV2[3]);
            view.setWebrateChannelId((Integer) webrateV2[4]);
            view.setWebrateAccomTypeId((Integer) webrateV2[5]);
            view.setOccupancyDate(JavaLocalDateUtils.fromDate((Date) webrateV2[6]));
            view.setLos(((BigDecimal) webrateV2[7]).toBigInteger().intValue());
            view.setWebrateRemark((String) webrateV2[8]);
            view.setWebrateStatus((String) webrateV2[9]);
            view.setWebrateCurrency((String) webrateV2[10]);
            view.setWebrateRateValue((BigDecimal) webrateV2[11]);
            view.setOriginalWebrateRateValue((BigDecimal) webrateV2[12]);
            view.setWebrateRateValueWithoutTax((BigDecimal) webrateV2[13]);
            view.setWebrateRateValueMax((BigDecimal) webrateV2[18]);
            view.setWebrateCompetitorsAlias((String) webrateV2[20]);
            view.setAccomClassId((Integer) webrateV2[21]);

            webrateViewV2List.add(view);
        }
        return webrateViewV2List;
    }

    public List<WebrateView> getWebrateViewForProducts(List<Integer> productIds) {
        return crudService.findByNamedQuery(WebrateView.BY_PRODUCT,
                QueryParameter.with(WebrateView.PARAM_PRODUCT_IDS, productIds)
                        .parameters());
    }

    public List<WebrateView> getWebrateViewForDateRange(LocalDate startDate, LocalDate endDate) {
        return crudService.findByNamedQuery(WebrateView.BY_DATE_RANGE,
                QueryParameter.with(WebrateView.PARAM_START_DATE, DateUtil.convertJodaToJavaLocalDate(startDate))
                        .and(WebrateView.PARAM_END_DATE, DateUtil.convertJodaToJavaLocalDate(endDate))
                        .parameters());
    }

    public List<WebrateView> getAllWebrateView() {
        return crudService.findByNamedQuery(WebrateView.ALL_ROWS);
    }


    @ForTesting

    public void deleteModifiedBARProductOverridesForCPGP02() {
        Integer decisionId = decisionService.createBAROverrideDecision().getId();
        String queryStr = "SELECT * FROM CP_Decision_Bar_Output WHERE Property_ID = 11033 AND Arrival_DT > '2017-10-09' AND ((Floor_Rate IS NOT NULL) OR (Ceil_Rate IS NOT NULL) OR (User_Specified_Rate IS NOT NULL))";
        List<CPDecisionBAROutput> modifiedCPDecisionBAROutputs = crudService.findByNativeQuery(queryStr, null, CPDecisionBAROutput.class);
        if (CollectionUtils.isNotEmpty(modifiedCPDecisionBAROutputs)) {
            LocalDate startDate = modifiedCPDecisionBAROutputs.stream().min(Comparator.comparing(CPDecisionBAROutput::getArrivalDate)).get().getArrivalDate();
            LocalDate endDate = modifiedCPDecisionBAROutputs.stream().max(Comparator.comparing(CPDecisionBAROutput::getArrivalDate)).get().getArrivalDate();
            CPDecisionContext cpDecisionContext = pricingConfigurationService.getCPDecisionContext(startDate, endDate, false);

            modifiedCPDecisionBAROutputs.forEach(cpDecisionBAROutput -> {
                removeOverride(cpDecisionContext, cpDecisionBAROutput, decisionId, 11403, false, true, true);
            });
        }
    }

    public List<Product> getUploadEnabledIndependentProducts() {
        return crudService.findByNamedQuery(Product.GET_PRODUCTS_BY_CODE_STATUS_UPLOAD,
                QueryParameter.with("code", Product.INDEPENDENT_PRODUCT_CODE).and("status", TenantStatusEnum.ACTIVE)
                        .and("isUpload", true).parameters());
    }

    public List<AccomType> getRoomTypesForSelectedProduct(Product product) {
        return agileRatesConfigurationService.findProductRoomTypesByProduct(product)
                .stream()
                .map(ProductAccomType::getAccomType)
                .collect(toList());
    }


    private void registerSmallGroupChangeSyncEvent() {
        syncEventAggregatorService.registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);
    }
    private void registerSmallGroupOverrideChangeSyncEvent() {
        syncEventAggregatorService.registerSyncEvent(SyncEvent.GROUP_PRODUCT_OVERRIDE_CHANGED);
    }

    protected boolean shouldSmallGroupSyncBeFire(List<ProductRateOffsetOverride> overrides) {
        boolean pricingDisableAgileRatesSyncForOptimizedProductOverrides = isPricingDisableAgileRatesSyncForOptimizedProductOverrides();

        if (CollectionUtils.isEmpty(overrides)) {
            return false;
        } else if (!pricingDisableAgileRatesSyncForOptimizedProductOverrides) {
            java.time.LocalDate caughtUpJavaLocalDate = dateService.getCaughtUpJavaLocalDate();
            return overrides.stream()
                    .anyMatch(override -> isDateInOptimizationWindow(override, caughtUpJavaLocalDate));
        } else if (pricingDisableAgileRatesSyncForOptimizedProductOverrides) {
            return false;
        }

        return true;
    }

    public void saveSmallGroupProductRateOffsetOverride(List<ProductRateOffsetOverride> overrides) {
        crudService.save(overrides);
        enableOverrideForExtendedWindowIfApplicable(overrides);
        if (shouldSmallGroupSyncBeFire(overrides)) {
            if (isPricingOverrideIDPFlowBasedSyncEnabled()) {
                registerSmallGroupOverrideChangeSyncEvent();
            } else {
                registerSmallGroupChangeSyncEvent();
            }
        }
    }

    public void deleteSmallGroupProductRateOffsetOverride(List<ProductRateOffsetOverride> overrides) {
        overrides.forEach(smallGroupProductRateOffsetOverride -> smallGroupProductRateOffsetOverride.setStatusId(Status.INACTIVE.getId()));
        crudService.save(overrides);
        enableOverrideForExtendedWindowIfApplicable(overrides);
        if (shouldSmallGroupSyncBeFire(overrides)) {
            if (isPricingOverrideIDPFlowBasedSyncEnabled()) {
                registerSmallGroupOverrideChangeSyncEvent();
            } else {
                registerSmallGroupChangeSyncEvent();
            }
        }
    }

    private void enableOverrideForExtendedWindowIfApplicable(List<ProductRateOffsetOverride> overrides) {
        java.time.LocalDate caughtUpJavaLocalDate = dateService.getCaughtUpJavaLocalDate();
        List<java.time.LocalDate> occupancyDates = overrides.stream()
                .map(ProductRateOffsetOverride::getOccupancyDate)
                .map(JavaLocalDateUtils::toJavaLocalDate)
                .collect(toList());

        pricingConfigurationLTBDEService.enableOverrideForExtendedWindowIfApplicable(occupancyDates, caughtUpJavaLocalDate);
    }

    public List<ProductDTO> getProductDetails(){
        Map<Integer, Product> allProducts = getAllProducts();
        List<Product> products = new ArrayList<>(allProducts.values());
        return products.stream().filter(x->x.getStatus().equals(TenantStatusEnum.ACTIVE)).map(this::mapToProductDTO).collect(Collectors.toList());
    }

    private ProductDTO mapToProductDTO(Product product) {
        ProductDTO productDTO = new ProductDTO();
        if ((product.isSystemDefault())) {
            productDTO.setProductCodeName("PRIMARY PRICED PRODUCT");
        } else {
            productDTO.setProductCodeName(product.getProductCode().getProductCodeName());

        }
        productDTO.setProductName(product.getName());
        return productDTO;
    }

    public List<Product> getAllEligibleProductsForRateShopping() {
        List<Product> allProductsList = agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts();
        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(INDEPENDENT_PRODUCTS_ENABLED);
        boolean isRDLEnabled= configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);

        List<Product> allProducts;
        if (isIndependentProductsEnabled && isRDLEnabled) {
            allProducts = allProductsList;
        } else if (isIndependentProductsEnabled) {
            allProducts = getProducts(allProductsList, product -> product.isSystemDefault() || product.isIndependentProduct());

        } else if (isRDLEnabled) {
            allProducts = getProducts(allProductsList, product -> product.isSystemDefault() || product.isAgileRatesProduct());
        } else {
            allProducts = getProducts(allProductsList, product -> product.isSystemDefault());
        }
        allProducts.sort(Comparator.comparing(Product::getDisplayOrder));

        return allProducts;
    }

    private static List<Product> getProducts(List<Product> allProductsList, Predicate<Product> productPredicate) {
        return allProductsList.stream()
                .filter(productPredicate)
                .collect(Collectors.toList());
    }


    public Response updateProductConfiguration(Integer productId, String productName, String fieldName, String newValue) {
        Product product = null;
        if (Objects.nonNull(productId)) {
            product = crudService.findByNamedQuerySingleResult(Product.GET_BY_ID_ACTIVE_PRODUCT,
                    QueryParameter.with("productId", productId).parameters());
        } else if (Objects.nonNull(productName)) {
            product = crudService.findByNamedQuerySingleResult(Product.GET_BY_NAME,
                    QueryParameter.with("name", productName).parameters());
        }
        if (Objects.isNull(product)) {
            return getInvalidResponse("ProductId or Product Name is invalid.");
        }
        try {
            Field field = Product.class.getDeclaredField(fieldName);
            field.setAccessible(true);
            setProductFieldValue(newValue, product, field);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            return getInvalidResponse("Invalid field or fieldValue");
        }

        crudService.save(product);

        return SUCCESS_RESPONSE;
    }

    public Product getProductByName(String productName) {

        return crudService.findByNamedQuerySingleResult(Product.GET_BY_NAME, QueryParameter.with("name", productName).parameters());

    }

    private static void setProductFieldValue(String newValue, Product product, Field field) throws IllegalAccessException {
        if (field.getType().equals(String.class)) {
            field.set(product, newValue);
        } else if (field.getType().equals(Integer.class)) {
            field.set(product, Integer.parseInt(newValue));
        } else if (field.getType().equals(BigDecimal.class)) {
            field.set(product, new BigDecimal(newValue));
        } else if (field.getType().equals(boolean.class) || field.getType().equals(Boolean.class)) {
            field.set(product, Boolean.parseBoolean(newValue));
        }
    }


}