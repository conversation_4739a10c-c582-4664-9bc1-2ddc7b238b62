package com.ideas.tetris.pacman.services.property.dto;

import org.apache.log4j.Logger;

import java.io.File;
import java.io.FileFilter;

public class MonthFolderFileFilter implements FileFilter {
    private static final Logger LOGGER = Logger.getLogger(MonthFolderFileFilter.class.getName());

    @Override
    public boolean accept(File candidate) {
        boolean shouldAccept = false;
        if (candidate.isDirectory()) {
            String[] segments = candidate.getName().split("[_]");
            // Should be of form YYYY_mm
            if (segments.length == 2) {
                shouldAccept = areFileDatePartsValid(segments, candidate.getName());
            }
        }
        return shouldAccept;
    }

    private boolean areFileDatePartsValid(String[] dateParts, String fileName) {
        boolean areValid = false;
        try {
            int year = Integer.valueOf(dateParts[0]);
            int month = Integer.valueOf(dateParts[1]);
            if (year >= 2000 && year <= 3000 && month >= 1 && month <= 12) {
                areValid = true;
            }
        } catch (NumberFormatException e) {
            LOGGER.info("Could not parse year or month from possible month folder: " + fileName, e);
        }
        return areValid;
    }
}
