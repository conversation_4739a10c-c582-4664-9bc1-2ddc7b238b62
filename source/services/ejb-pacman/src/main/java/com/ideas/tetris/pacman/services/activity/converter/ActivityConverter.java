package com.ideas.tetris.pacman.services.activity.converter;

import com.google.common.collect.Lists;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.RoomTypeCache;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ActivityEntity;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataCache;
import com.ideas.tetris.pacman.services.marketsegment.MarketSegmentCache;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.repository.MarketSegmentRepository;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.ngi.NGIConvertUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.G3_NGI_DIFFERENT_CLIENT_CODE_PROPERTY_CODE_MAPPING_ENABLED;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * The ActivityConverter can be extended to provide a basic copy of properties
 * from a Map DTO to an ActivityEntity. It utilizes Apache ConvertUtilsBean to
 * help some of the basic properties to be copied.
 */
@Transactional
@Component
public abstract class ActivityConverter<E extends ActivityEntity> {
    public static final String ID = "id";
    public static final String CLIENT_CODE = "clientCode";
    public static final String PROPERTY_CODE = "propertyCode";
    public static final String PROPERTY_ID = "propertyId";
    public static final String OCCUPANCY_DATE = "occupancyDate";
    public static final String ROOM_TYPE_CODE = "roomTypeCode";
    public static final String ROOM_TYPE_NAME = "roomTypeName";
    public static final String ROOM_CLASS_NAME = "roomClassName";
    public static final String MARKET_SEGMENT_NAME = "marketSegmentName";
    public static final String BUSINESS_DAY_END_DATE = "businessDayEndDate";
    public static final String MARKET_SEGMENT_CODE = "marketSegmentCode";
    public static final String ARRIVAL_DATE = "arrivalDate";
    public static final String ACCOM_TYPE_ID = "accomTypeId";
    public static final String MKT_SEG_ID = "mktSegId";
    public static final String CANCELLATIONS = "cancellations";
    public static final String ARRIVALS = "arrivals";
    public static final String TOTAL_REVENUE = "totalRevenue";
    public static final String ROOMS_SOLD = "roomsSold";
    public static final String ROOM_REVENUE = "roomRevenue";
    public static final String NO_SHOWS = "noShows";
    public static final String FOOD_REVENUE = "foodRevenue";
    public static final String DEPARTURES = "departures";
    static final String OCCUPANCY_DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
    static final String ACCOM_CAPACITY = "accomCapacity";
    static final String TOTAL_ACCOM_CAPACITY = "totalAccomCapacity";
    static final String ROOMS_NOT_AVAILABLE_MAINTENANCE = "roomsNotAvailableMaintenance";
    static final String ROOMS_NOT_AVAILABLE_OTHER = "roomsNotAvailableOther";
    private static final Logger LOGGER = Logger.getLogger(ActivityConverter.class);
    private static final int EDITABLE = 1;
    private static final String CONVERTING_TO_ENTITY = "Converting: %s to Entity";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    @Autowired
	protected PropertyService propertyService;
    @Autowired
	protected MarketSegmentRepository marketSegmentRepository;
    @Autowired
    RoomTypeCache roomTypeCache;
    @Autowired
	private MarketSegmentCache marketSegmentCache;
    @Autowired
	private AccommodationService accommodationService;
    @Autowired
	private FileMetadataCache fileMetadataCache;
    @Autowired
	private DateService dateService;
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;

    /**
     * Converts a DTO into an ActivityEntity
     */
    public E convertFromMap(Map<String, Object> dto, String correlationId, boolean isPast) {
        LOGGER.debug(String.format(CONVERTING_TO_ENTITY, dto));

        // If the DTO is null, don't make any assumptions and return a null Entity
        if (dto == null) {
            return null;
        }

        // Determine propertyId
        Integer propertyId = determinePropertyId(dto);

        // Look for an existing ActivityEntity for the DTO, if it doesn't exist, create a new one
        E entity = findExistingOrCreateNewActivity(propertyId, dto, correlationId, isPast);

        // Get a reference to the ID, there is a possibility that the DTO won't have one even though
        // the entity does have one and it could get lost in the property copy
        Integer id = entity.getId();

        // Using copy all of the variables that can be copied by naming convention
        populate(entity, dto);

        // Reset the ID if there was one and if the copy properties removed it
        if (entity.getId() == null && id != null) {
            entity.setId(id);
        }

        // Set the propertyId
        entity.setPropertyId(propertyId);
        return entity;
    }

    public List<E> convertFromMapAll(List<Map<String, Object>> dtos, String correlationId) {
        LOGGER.debug(String.format(CONVERTING_TO_ENTITY, dtos));
        return findExistingOrCreateNewActivity(determinePropertyId(dtos.get(0)), dtos, correlationId);
    }

    /**
     * Converts an ActivityEntity into a DTO
     */
    public Map<String, Object> convertFromEntity(ActivityEntity entity) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Converting: " + entity + " to DTO");
        }

        // If the Entity is null, don't make any assumptions and return a null DTO
        if (entity == null) {
            return null;
        }

        // Construct a new DTO object
        Map<String, Object> dto = new LinkedHashMap<>();

        // Using copy all of the variables that can be copied by naming convention
        copyPropertiesToDto(dto, entity);

        // Add the client/property codes for the object
        addClientAndPropertyCode(dto, entity);

        // Remove the Property_ID from the DTO
        dto.remove(PROPERTY_ID);

        return dto;
    }

    /**
     * Convert a list of Entities to a list of DTOs
     */
    public List<Map<String, Object>> convertFromEntities(List<? extends ActivityEntity> entities) {

        if (entities == null) {
            return Lists.newArrayList();
        }

        LOGGER.info("Starting convert of entities: " + entities.size());

        List<Map<String, Object>> dtos = new ArrayList<>();
        for (ActivityEntity entity : entities) {
            dtos.add(convertFromEntity(entity));
        }

        LOGGER.info("Completed convert of entities: " + dtos.size());

        return dtos;
    }

    /**
     * Copy the properties from one bean to another
     */
    private void copyPropertiesToDto(Map<String, Object> dto, ActivityEntity entity) {
        try {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Copying properties from Dto: " + dto + " to " + entity);
            }

            List<Class<?>> classHierarchy = getEntityClassHierarchy(entity);
            for (Class<?> clazz : classHierarchy) {
                for (Field field : clazz.getDeclaredFields()) {
                    field.setAccessible(true);
                    copyFieldsToDto(dto, entity, field);
                }
            }
        } catch (Exception e) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Unable to copy properties from DTO: " + e.getMessage(), e);
            }
            throw new TetrisException(ErrorCode.BEAN_PROPERTY_COPY_FAILED, "Unable to copy properties from DTO: " + dto + " to " + entity);
        }
    }

    private void copyFieldsToDto(Map<String, Object> dto, ActivityEntity entity, Field field) throws IllegalAccessException {
        if (!Modifier.isFinal(field.getModifiers())) {
            if ("snapShotDate".equals(field.getName())) {
                Date date = DateUtil.addDaysToDate((Date) field.get(entity), 1);
                Date dateTimeByTimeZone = DateUtil.getDateTimeByTimeZone(date, dateService.getPropertyTimeZone());
                dto.put(field.getName(), dateTimeByTimeZone);
            } else if (ActivityConverter.OCCUPANCY_DATE.equals(field.getName()) || ActivityConverter.BUSINESS_DAY_END_DATE.equals(field.getName())) {
                dto.put(field.getName(), DateUtil.formatDate((Date) field.get(entity), DateUtil.DEFAULT_DATE_FORMAT));
            } else {
                dto.put(field.getName(), field.get(entity));
            }
        }
    }

    /**
     * Copy the properties from one bean to another
     */
    protected void populate(E entity, Map<String, Object> properties) {
        try {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Populating DTO from  properties: " + properties + " into: " + entity);
            }
            if (properties != null) {
                List<Class<?>> classHierarchy = getEntityClassHierarchy(entity);
                for (String key : properties.keySet()) {
                    populateField(entity, properties, classHierarchy, key);
                }
            }
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.BEAN_PROPERTY_COPY_FAILED, "Unable to populate DTO: " + entity + " from properties: " + properties, e);
        }
    }

    private void populateField(E entity, Map<String, Object> properties, List<Class<?>> classHierarchy, String key) throws IllegalAccessException {
        Field field = getField(classHierarchy, key);
        if (field != null) {
            field.setAccessible(true);
            Object value = properties.get(key);
            convertValue(entity, key, field, value);
        }
    }

    private void convertValue(E entity, String key, Field field, Object value) throws IllegalAccessException {
        if (!"snapShotDate".equals(key)) {
            if (field.getType().isAssignableFrom(BigDecimal.class)) {
                field.set(entity, NGIConvertUtils.convert(value, field.getType(), BigDecimal.ZERO));
            } else {
                field.set(entity, NGIConvertUtils.convert(value, field.getType()));
            }
        }
    }

    public String findRoomTypeCodeForRoomTypeId(Integer roomTypeId) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Looking for Room Type with ID: " + roomTypeId);
        }
        return tenantCrudService.find(AccomType.class, roomTypeId).getAccomTypeCode();
    }

    protected AccomType findOrCreateRoomTypeForCode(Integer propertyId, String roomTypeCode) {
        return accommodationService.findOrCreateRoomTypeForCode(propertyId, roomTypeCode);
    }

    protected List<AccomType> findOrCreateRoomTypesForCodes(Integer propertyId, Set<String> roomTypeCodes) {
        return accommodationService.findOrCreateRoomTypeForCodes(propertyId, roomTypeCodes);
    }

    public String findMarketSegmentCodeForMarketSegmentId(Integer marketSegmentId) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Looking for Market Segment with ID: " + marketSegmentId);
        }
        return tenantCrudService.find(MktSeg.class, marketSegmentId).getCode();
    }

    public String findMarketSegmentNameForCode(String marketSegmentCode) {
        MktSeg marketSegment = marketSegmentCache.get(PacmanWorkContextHelper.getPropertyId(), marketSegmentCode);
        return marketSegment.getName();
    }

    private Integer determinePropertyId(Map<String, Object> dto) {
        if (Boolean.TRUE.equals(pacmanConfigParamsService.getParameterValue(G3_NGI_DIFFERENT_CLIENT_CODE_PROPERTY_CODE_MAPPING_ENABLED))) {
            return PacmanWorkContextHelper.getPropertyId();
        }
        String clientCode = getString(dto, CLIENT_CODE);
        if (StringUtils.isEmpty(clientCode)) {
            throw new TetrisException(ErrorCode.NO_CLIENT_CODE, "Need to have a clientCode to determine PropertyId");
        }

        String propertyCode = getString(dto, PROPERTY_CODE);
        if (StringUtils.isEmpty(propertyCode)) {
            throw new TetrisException(ErrorCode.NO_PROPERTY_CODE, "Need to have a propertyCode to determine PropertyId");
        }
        Property property = propertyService.getPropertyByCode(clientCode, propertyCode);
        if (property == null) {
            throw new TetrisException(ErrorCode.PROPERTY_NOT_FOUND, "Unable to find property for Client Code: " + clientCode + " and Property Code: " + propertyCode);
        }

        return property.getId();
    }

    private void addClientAndPropertyCode(Map<String, Object> dto, ActivityEntity entity) {
        // If there is a propertyId, use it to look up the Property
        Integer propertyId = entity.getPropertyId();
        if (propertyId != null) {

            // If the Property exists, set the clientCode and propertyCode
            Property property = propertyService.getPropertyById(propertyId);
            if (property != null) {
                dto.put(CLIENT_CODE, property.getClient().getCode());
                dto.put(PROPERTY_CODE, property.getCode());
            }
        }
    }

    @SuppressWarnings("squid:S1166")
    private Field getField(List<Class<?>> classHierarchy, String fieldName) {
        for (Class<?> clazz : classHierarchy) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException nsfe) {
                // Don't need to do anything, check the next Class
            }
        }

        return null;
    }

    private List<Class<?>> getEntityClassHierarchy(ActivityEntity entity) {
        List<Class<?>> classHierarchy = new ArrayList<>();
        Class<?> currentClass = entity.getClass();
        while (!currentClass.equals(Object.class)) {
            classHierarchy.add(currentClass);
            currentClass = currentClass.getSuperclass();
        }
        return classHierarchy;
    }

    public FileMetadata findExistingFileMetadata(String correlationId) {
        FileMetadata fileMetadata = fileMetadataCache.get(PacmanWorkContextHelper.getPropertyId(), correlationId);
        if (fileMetadata == null) {
            fileMetadata = tenantCrudService.findByNamedQuerySingleResult(FileMetadata.BY_FILE_LOCATION, QueryParameter.with("location", correlationId).parameters());
        }
        return fileMetadata;
    }

    public BigDecimal getBigDecimal(Map<String, Object> dto, String field) {
        return get(dto, field, BigDecimal.class);
    }

    public BigDecimal getBigDecimalDefault(Map<String, Object> dto, String field, BigDecimal defaultValue) {
        BigDecimal potentialValue = get(dto, field, BigDecimal.class);
        return potentialValue != null ? potentialValue : defaultValue;
    }

    public Integer getInteger(Map<String, Object> dto, String field) {
        return get(dto, field, Integer.class);
    }

    public String getString(Map<String, Object> dto, String field) {
        return StringUtils.trim(get(dto, field, String.class));
    }

    public Date getDate(Map<String, Object> dto, String field) {
        return get(dto, field, Date.class);
    }

    private <T> T get(Map<String, Object> dto, String field, Class<T> type) {
        if (dto == null || field == null) {
            return null;
        }

        return NGIConvertUtils.convert(dto.get(field), type);
    }

    /**
     * Implement this method to either find an existing version of the Entity or create a new one.
     */
    public abstract E findExistingOrCreateNewActivity(Integer propertyId, Map<String, Object> dto, String correlationId, boolean isPast);

    public abstract List<E> findExistingOrCreateNewActivity(Integer propertyId, List<Map<String, Object>> dtos, String correlationId);
}
