package com.ideas.tetris.pacman.services.rates;

import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractDetail;

import java.math.BigDecimal;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.Deque;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.daysBetween;

public class SeasonMergeService {
    private static final int DAYS_WEEK = 7;
    private Map<Integer, Deque<AbstractDetail>> detailByAccomTypes;
    private static final BigDecimal VALUE_NEGATIVE_ONE = new BigDecimal("-1.0");

    public SeasonMergeService(Map<Integer, Deque<AbstractDetail>> detailByAccomTypes) {
        this.detailByAccomTypes = detailByAccomTypes;
    }

    public SeasonMergeService() {

    }

    public Map<Integer, Deque<AbstractDetail>> mergeSeasonsUptoSevenDays() {
        Map<Integer, Deque<AbstractDetail>> mergedSeasonMap = new HashMap<>();
        detailByAccomTypes.forEach((id, abstractDetails) -> {
            List<AbstractDetail> mergedSeason = mergeSeasonsUptoSevenDays(new ArrayList<>(abstractDetails));
            mergedSeasonMap.put(id, new ArrayDeque<>(mergedSeason));
        });
        return mergedSeasonMap;
    }

    List<AbstractDetail> mergeSeasonsUptoSevenDays(List<AbstractDetail> abstractDetails) {
        List<AbstractDetail> detailsToSave = new ArrayList<>();
        abstractDetails.sort(Comparator.comparing(AbstractDetail::getStartDate));
        List<AbstractDetail> eligibleForMerge = new ArrayList<>();
        int seasonLength = 0;
        for (AbstractDetail currentDetail : abstractDetails) {
            int differenceDays = getDifferenceDays(currentDetail.getStartDate(), currentDetail.getEndDate());
            if (differenceDays < DAYS_WEEK) {
                seasonLength = differenceDays + seasonLength;
                boolean datesAreConsistent = datesAreConsistent(eligibleForMerge, currentDetail);
                if (seasonLength <= DAYS_WEEK && datesAreConsistent) {
                    eligibleForMerge.add(currentDetail);  //merge with existing season
                } else {
                    addEligibleForMergeToSaveList(detailsToSave, eligibleForMerge);
                    eligibleForMerge = new ArrayList<>();
                    eligibleForMerge.add(currentDetail);
                    seasonLength = differenceDays;
                }
            } else {
                addEligibleForMergeToSaveList(detailsToSave, eligibleForMerge);
                detailsToSave.add(currentDetail);
                eligibleForMerge = new ArrayList<>();
                seasonLength = differenceDays;
            }
        }
        addEligibleForMergeToSaveList(detailsToSave, eligibleForMerge);
        return detailsToSave;
    }

    private boolean datesAreConsistent(List<AbstractDetail> eligibleForMerge, AbstractDetail currentDetail) {
        return eligibleForMerge.isEmpty() ||
                daysBetween(eligibleForMerge.get(eligibleForMerge.size() - 1).getEndDate(), currentDetail.getStartDate()) == 1;
    }

    private void addEligibleForMergeToSaveList(List<AbstractDetail> detailsToSave, List<AbstractDetail> eligibleForMerge) {
        if (!eligibleForMerge.isEmpty()) {
            detailsToSave.add(getMergedSeason(eligibleForMerge));
        }
    }

    private AbstractDetail getMergedSeason(List<AbstractDetail> eligibleForMerge) {
        AbstractDetail mergedDetail = eligibleForMerge.get(0);
        mergedDetail.setEndDate(eligibleForMerge.get(eligibleForMerge.size() - 1).getEndDate());
        for (AbstractDetail abstractDetail : eligibleForMerge) {
            updateValue(mergedDetail::setMonday, abstractDetail.getMonday());
            updateValue(mergedDetail::setTuesday, abstractDetail.getTuesday());
            updateValue(mergedDetail::setWednesday, abstractDetail.getWednesday());
            updateValue(mergedDetail::setThursday, abstractDetail.getThursday());
            updateValue(mergedDetail::setFriday, abstractDetail.getFriday());
            updateValue(mergedDetail::setSaturday, abstractDetail.getSaturday());
            updateValue(mergedDetail::setSunday, abstractDetail.getSunday());

        }
        return mergedDetail;
    }


    private boolean isCriteriaMet(BigDecimal rate) {
        return null != rate && VALUE_NEGATIVE_ONE.compareTo(rate) != 0;
    }

    private void updateValue(Consumer<BigDecimal> setterMethod, BigDecimal value) {
        if (isCriteriaMet(value)) {
            setterMethod.accept(value);
        }
    }

    public static int getDifferenceDays(Date d1, Date d2) {
        long diff = d2.getTime() - d1.getTime();
        return (int) TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS) + 1;
    }
}
