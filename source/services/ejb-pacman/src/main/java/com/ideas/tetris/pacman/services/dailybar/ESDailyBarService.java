package com.ideas.tetris.pacman.services.dailybar;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dailybar.constants.DailyBarConstants;
import com.ideas.tetris.pacman.services.datafeed.service.DecisionConfigurationService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.extendedstay.ratemanagement.ExtendedStayOverrideStatus;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;

import javax.inject.Inject;

import static com.ideas.tetris.pacman.common.constants.Constants.ES_DAILY_BAR_FULL_RATE_SYNC;
import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.getPropertyId;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class ESDailyBarService {
    private static final Logger LOGGER = Logger.getLogger(ESDailyBarService.class.getName());

    private static final String UPDATE_ES_RATE_UNQUALIFIED_WITH_OVERRIDDEN_VALUES = " UPDATE esrd\n" +
            "SET    esrd.rate_value = esrdo.rate_value\n" +
            "FROM   es_rate_unqualified_details esrd\n" +
            "       JOIN es_rate_unqualified_override esrdo\n" +
            "         ON esrd.es_rate_unqualified_id = esrdo.es_rate_unqualified_id\n" +
            "            AND esrd.accom_type_id = esrdo.accom_type_id\n" +
            "            AND esrd.occupancy_date = esrdo.occupancy_date\n" +
            "WHERE  esrdo.override_status = 'ACKNOWLEDGEMENT_PENDING'\n" +
            "       AND esrdo.last_updated_dttm < (SELECT Max(created_dttm)\n" +
            "                                      FROM\n" +
            "           decision_upload_date_to_external_system\n" +
            "                                      WHERE  decision_name = 'ES dailyBar'\n" +
            "                                             AND status = 'SUC')  ";

    private static final String INSERT_ES_RATE_UNQUALIFIED_WITH_OVERRIDDEN_VALUES_IF_NOT_PRESENT = "insert into ES_Rate_Unqualified_Details (ES_Rate_Unqualified_ID,accom_type_id, Occupancy_Date,rate_value,created_dttm, Last_Updated_DTTM)\n" +
            "select ES_Rate_Unqualified_ID,accom_type_id, Occupancy_Date,rate_value,created_dttm, Last_Updated_DTTM from ES_Rate_Unqualified_Override  eruo\n" +
            "where eruo.override_status = 'ACKNOWLEDGEMENT_PENDING' and Last_Updated_DTTM < (SELECT Max(created_dttm)\n" +
            "                                      FROM\n" +
            "           decision_upload_date_to_external_system\n" +
            "                                      WHERE  decision_name = 'ES dailyBar'\n" +
            "                                             AND status = 'SUC') and not exists (select 1 from ES_Rate_Unqualified_Details esud where esud.Occupancy_Date = eruo.Occupancy_Date and esud.Accom_Type_ID = eruo.Accom_Type_ID\n" +
            "\t\t\t\t\t\t\t\t\t\t\t and esud.ES_Rate_Unqualified_ID = eruo.ES_Rate_Unqualified_ID)";
    private static final String DELETE_ES_DECISION_OVERRIDES =
            "delete from es_rate_unqualified_override where last_updated_dttm < (select max(created_dttm) from " +
                    "decision_upload_date_to_external_system where decision_name = 'ES dailyBar' and status = " +
                    "'SUC' )";
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService crudService;
    @Autowired
	protected DecisionService decisionService;
    @Autowired
    DateService dateService;
    @Autowired
    DecisionConfigurationService decisionConfigurationService;
    @Autowired
    PropertyService propertyService;

    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public CrudService getCrudService() {
        return crudService;
    }

    public int updateMainTableAndDeleteOverrides() {
        var updatedRecordsFromMainTable =
                crudService.executeUpdateByNativeQuery(UPDATE_ES_RATE_UNQUALIFIED_WITH_OVERRIDDEN_VALUES);
        var insertedRecords = crudService.executeUpdateByNativeQuery(INSERT_ES_RATE_UNQUALIFIED_WITH_OVERRIDDEN_VALUES_IF_NOT_PRESENT);
        var deletedFromOverrideTable = crudService.executeUpdateByNativeQuery(
                DELETE_ES_DECISION_OVERRIDES);
        LOGGER.info("ESDailyBarService updatedRates : " + updatedRecordsFromMainTable + " deletedOverrides : " +
                deletedFromOverrideTable + " insertedRecords: " + insertedRecords);
        return deletedFromOverrideTable;
    }

    public void createESDailyBarDecisions() {
        LOGGER.debug("ESDailyBarService createESDailyBarDecisions ");
        Decision decisionRecord = decisionService.createESDailyBarDecision();
        LOGGER.info("Creating ES DailyBar Decisions for decisionRecord: " + decisionRecord);

        String sync = getESDailyBarDecisionSync();

        StringBuilder query = new StringBuilder();
        if (ES_DAILY_BAR_FULL_RATE_SYNC.equals(sync)) {
            query.append(DailyBarConstants.GET_ES_RATE_UNQUALIFIED_DETAILS_FULL);
        } else {
            query.append(DailyBarConstants.GET_ES_RATE_UNQUALIFIED_OVERRIDE_DIFFERENTIAL);
        }
        query.append(DailyBarConstants.REMOVE_DATA_FOR_ACCOM_TYPES_WITH_NO_CAPACITY)
                .append(DailyBarConstants.GET_ES_DAILYBAR_RATE_OFFSET_MAP_FOR_ALL_ACCOMS)
                .append(DailyBarConstants.CALCULATE_DAILYBAR_RATES_WITHOUT_DOUBLE)
                .append(DailyBarConstants.CALCULATE_ES_DAILYBAR_RATES)
                .append(DailyBarConstants.INSERT_ES_DECISON_DAILYBAR_OUTPUT);

        int decisionDailybarOutputCount = crudService.executeUpdateByNativeQuery(query.toString()
                , QueryParameter.with("decisions_id", decisionRecord.getId())
                        .and("decisionStartDate", dateService.getOptimizationWindowStartDate())
                        .and("decisionEndDate", dateService.getDecisionUploadWindowEndDate())
                        .parameters());

        LOGGER.info("Number of records inserted into Decision_Dailybar_Output table are " + decisionDailybarOutputCount +
                " for decision id: " + decisionRecord.getId());

        int paceDecisionDailybarOutputCount = crudService.executeUpdateByNativeQuery(DailyBarConstants.INSERT_PACE_DECISON_DAILYBAR_OUTPUT, QueryParameter.with("decisionId", decisionRecord.getId()).parameters());
        LOGGER.info("Number of records inserted into PACE_Dailybar_Output table are " + paceDecisionDailybarOutputCount);
        decisionService.updateDescisionProcessStatus(decisionRecord.getId(), Constants.PROCESS_STATUS_SUCCESSFUL);
        crudService.executeUpdateByNativeQuery("update es_rate_unqualified_override set Override_Status = :ackPending where Override_Status = :uploadPending "
                , QueryParameter.with("ackPending", ExtendedStayOverrideStatus.ACKNOWLEDGEMENT_PENDING.name()).and("uploadPending", ExtendedStayOverrideStatus.UPLOAD_PENDING.name()).parameters());
        LOGGER.info("Updated Pace Daily Bar Decisions and decision process status");
    }

    private String getESDailyBarDecisionSync() {
        Integer propertyId = getPropertyId();
        if (decisionConfigurationService.shouldSendFullDecisions(propertyId)) {
            LOGGER.info("ENABLE_FORCE_FULL_DECISIONS:Property=" + propertyId + ". Sending one-time Full Decisions for Decision Type ES_DAILY_BAR");
            LOGGER.info("Setting " + IntegrationConfigParamName.ES_DAILY_BAR_DECISION_SYNC.value() + "to FullRateSync");
            return ES_DAILY_BAR_FULL_RATE_SYNC;
        }
        return pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.ES_DAILY_BAR_DECISION_SYNC);
    }

}
