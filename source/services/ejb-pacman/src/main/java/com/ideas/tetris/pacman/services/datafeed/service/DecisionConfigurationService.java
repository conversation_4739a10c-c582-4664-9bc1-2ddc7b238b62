package com.ideas.tetris.pacman.services.datafeed.service;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.tetris.pacman.common.configparams.*;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.cdp.ConfigurationService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.CdpSchedule;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.DecisionConfiguration;
import com.ideas.tetris.pacman.services.datafeed.dto.decisionconfiguration.*;
import com.ideas.tetris.pacman.services.datafeed.entity.ImmediateFullDecisions;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decisionconfig.DecisionConfigCoreSettingService;
import com.ideas.tetris.pacman.services.decisionconfig.angular.SellingSystemDTO;
import com.ideas.tetris.pacman.services.decisiondelivery.DecisionDeliveryService;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.DecisionUploadDateToExternalSystem;
import com.ideas.tetris.pacman.services.eventaggregator.PropertyStateService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.forcefulldecisions.ForceFullDecisionsUpdateReasons;
import com.ideas.tetris.pacman.services.forcefulldecisions.entity.ForceFullDecisions;
import com.ideas.tetris.pacman.services.forcefulldecisions.service.ForceFullDecisionsService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.DailyProcessingService;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputProcessing;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputProcessingStatus;
import com.ideas.tetris.pacman.services.outbounddecisiondelivery.ProcessOutboundDecisions;
import com.ideas.tetris.pacman.services.property.PropertyAuditDto;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputType;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.PropertyDailyProcessing;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.property.PropertyStageChangeService;
import com.ideas.tetris.pacman.services.property.dto.PropertyStageChange;
import com.ideas.tetris.pacman.services.ratepopulation.QualifiedRateService;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.syncflags.service.SyncDisplayNameService;
import com.ideas.tetris.pacman.util.CustomizedDisplayName;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.*;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.PropertyAudit;
import com.ideas.tetris.platform.services.daoandentities.entity.PropertyAuditCriteria;
import com.ideas.tetris.platform.services.jmsclient.TetrisEventManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName.*;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.FORCEFULLDECISIONS_OUTBOUND_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.FULL_DECISIONS_ALL_OUTBOUND_NAMES_DISPLAY_ENABLED;
import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig.shouldUseCRHGGROUPFeatureForRHGROW;
import static com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType.T2SNAP_RECORD_TYPE_ID;
import static com.ideas.tetris.platform.common.externalsystem.ExternalSystem.OPERA_AGENT;
import static com.ideas.tetris.platform.services.configparams.ParameterConversionUtility.convertOldParameterToNew;

/**
 * Created by idnpda on 12/7/2015.
 */
@Component
@Transactional
public class DecisionConfigurationService {
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    PropertyService propertyService;

    @Autowired
    AlertService alertService;

    @Autowired
    TetrisEventManager tetrisEventManager;

    @Autowired
    DecisionDeliveryService decisionDeliveryService;

    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService crudService;

    @Autowired
    DailyProcessingService dailyProcessingService;

    @Autowired
    PropertyStateService propertyStateService;

    @Autowired
    DecisionConfigCoreSettingService decisionConfigCoreSettingService;

    @Autowired
    ProcessOutboundDecisions processOutboundDecisions;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
    ForceFullDecisionsService forceFullDecisionsService;
    @Autowired
    PropertyStageChangeService propertyStageChangeService;

    @Autowired
    DateService dateService;

    @Autowired
    private ConfigurationService configurationService;

    @Autowired
    private SyncEventAggregatorService syncEventAggregatorService;


    @Autowired
    private SyncDisplayNameService syncDisplayNameService;

    @Autowired
    private UserService userService;


    @Autowired
    ExternalSystemHelper externalSystemHelper;

    @Autowired
    private QualifiedRateService qualifiedRateService;

    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;

    public static final String DEFAULT_TIME_FORMAT_12HR = "hh:mm a";
    private static final String CARLSON_CLIENT_CODE = "CRHGROUP";
    private static final String RHGROW_CLIENT_CODE = "RHGROW";
    private static final String NOT_APPLICABLE_STRING = "N/A";
    private static final Logger LOGGER = Logger.getLogger(DecisionConfigurationService.class);

    private static final String LOG_STR_ENABLE_FORCE_FULL_DECISIONS_WITH_PROPERTY = "ENABLE_FORCE_FULL_DECISIONS:Property=";
    private static final String LOG_STR_FORCE_FULL_DECISIONS_OUTBOUNDS_WITH_PROPERTY = "FORCE_FULL_DECISIONS_OUTBOUNDS:Property=";
    private static final String LOG_STR_CLIENT_ID = " ClientId=";
    private static final String LOG_STR_INPUT_PROCESSING_STATUS = " with InputProcessing Status :";

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    @SuppressWarnings("unchecked")

    public int getForceFullDecisionsCount(int clientId) {
        List<Property> properties = crudService.findByNamedQuery(Property.FORCE_FULL_DECISIONS_BY_CLIENT_ID, QueryParameter.with(Property.PARAM_CLIENT_ID, clientId).parameters());
        int totalCountOfOutboundsConfigured = 0;
        if (pacmanConfigParamsService.getBooleanParameterValue(FORCEFULLDECISIONS_OUTBOUND_ENABLED.getParameterName())) {
            final Integer forceFullDecisionsAtOutboundCount = forceFullDecisionsService.getForceFullDecisionsAtOutboundCount();
            totalCountOfOutboundsConfigured = forceFullDecisionsAtOutboundCount;
        }
        return showFullDecisionsAllOutboundNames() ? totalCountOfOutboundsConfigured : properties.size() + totalCountOfOutboundsConfigured;
    }

    public boolean showFullDecisionsAllOutboundNames() {
        return pacmanConfigParamsService.getBooleanParameterValue(FULL_DECISIONS_ALL_OUTBOUND_NAMES_DISPLAY_ENABLED);
    }

    public int getImmediateFullDecisionUploadMaxCount() {
        return pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.IMMEDIATE_FULL_DECISION_UPLOAD_MAX_COUNT.value());
    }

    public boolean immediateFullDecisionsEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IMMEDIATE_FULL_DECISIONS_ENABLED);
    }


    public int getImmediateFullDecisionUploadCurrentCount() {
        Date startDate = DateUtil.removeTimeFromDate(new Date());
        return tenantCrudService.findByNamedQuerySingleResult(ImmediateFullDecisions.GET_COUNT_OF_DATE,QueryParameter.with("createdDate",startDate).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<Property> getPropertiesWithFullDecisions(int clientId) {
        List<Property> properties = crudService.findByNamedQuery(Property.FORCE_FULL_DECISIONS_BY_CLIENT_ID, QueryParameter.with(Property.PARAM_CLIENT_ID, clientId).parameters());
        return properties;
    }

    public boolean isPropertyScheduledForFullDecisions(int propertyId) {
        return propertyService.isForceFullDecisions(propertyId);
    }

    public void setForceFullDecisions(int propertyId, boolean force) {
        propertyService.setForceFullDecisions(propertyId, force);
    }

    public String getPropertyCode(String externalSystem) {
        return getParameterValue(externalSystem, IntegrationConfigParamName.PROPERTYCODE);
    }

    private String getParameterValue(String externalSystem, IntegrationConfigParamName propertycode) {
        Optional<ExternalSystem> optionalFromCode = ExternalSystem.getOptionalFromCode(externalSystem);
        if (optionalFromCode.isPresent() && ExternalSystem.getHtngPartners().contains(optionalFromCode.get())) {
            return pacmanConfigParamsService.getParameterValue(propertycode, optionalFromCode.get());
        }
        return null;
    }

    public String getDailyBARRateCode(String externalSystem) {
        return getParameterValue(externalSystem, DAILY_BAR_DAILYBAR_RATE_CODE);
    }

    public String getDailyBarUploadType(String externalSystem) {
        return getParameterValue(externalSystem, IntegrationConfigParamName.DAILY_BAR_UPLOADTYPE);
    }


    public boolean isCurrentProcessBDE(int propertyId) {
        if (isNotBDE(propertyId))
            return false;
        Property property = propertyService.getPropertyById(propertyId);
        TimeZone propertyTimeZone = TimeZone.getTimeZone(propertyService.getPropertyTimeZone(propertyId));
        LocalDate currentPropertyDate = new LocalDate(DateTimeZone.forTimeZone(propertyTimeZone));
        Optional<InputProcessing> inputProcessingBDERecord = dailyProcessingService.getLatestBDEByDate(property.getClient().getCode(), property.getCode(), currentPropertyDate);
        if (isBDEInProgress(inputProcessingBDERecord)) {
            LOGGER.info(LOG_STR_ENABLE_FORCE_FULL_DECISIONS_WITH_PROPERTY + propertyId + " isBDEInProgress=TRUE, Input Processing Record with id :" + inputProcessingBDERecord.get().getInputId() + "with currentPropertyDate :" + currentPropertyDate + LOG_STR_INPUT_PROCESSING_STATUS + inputProcessingBDERecord.get().getStatus());
            return true;
        }
        if (inputProcessingBDERecord.isPresent()) {
            LOGGER.info(LOG_STR_ENABLE_FORCE_FULL_DECISIONS_WITH_PROPERTY + propertyId + " FALSE.Input Processing Record with id :" + inputProcessingBDERecord.get().getInputId() + "with currentPropertyDate :" + currentPropertyDate + LOG_STR_INPUT_PROCESSING_STATUS + inputProcessingBDERecord.get().getStatus());
        }
        return false;

    }

    @VisibleForTesting
	public
    boolean isCurrentProcessBDEWithInputProcessingId(int propertyId, int inputProcessingId) {
        if (isNotBDE(propertyId))
            return false;
        Optional<InputProcessing> inputProcessingBDERecord = Optional.ofNullable(crudService.find(InputProcessing.class, inputProcessingId));
        if (isBDEInProgress(inputProcessingBDERecord)) {
            LOGGER.info(LOG_STR_ENABLE_FORCE_FULL_DECISIONS_WITH_PROPERTY + propertyId + " isBDEInProgress=TRUE, Input Processing Record with id :" +
                    inputProcessingId + LOG_STR_INPUT_PROCESSING_STATUS + inputProcessingBDERecord.get().getStatus());
            return true;
        }

        if (inputProcessingBDERecord.isPresent()) {
            LOGGER.info(LOG_STR_ENABLE_FORCE_FULL_DECISIONS_WITH_PROPERTY + propertyId + " isBDEInProgress=FALSE.Input Processing Record with id :" +
                    inputProcessingId + LOG_STR_INPUT_PROCESSING_STATUS + inputProcessingBDERecord.get().getStatus());
        }
        return false;
    }

    private boolean isNotBDE(int propertyId) {
        FileMetadata fileMetadata = getLatestFileMetadataForRecordTypeT2Snap(propertyId);
        if (null == fileMetadata) {
            LOGGER.info(LOG_STR_ENABLE_FORCE_FULL_DECISIONS_WITH_PROPERTY + propertyId + " FALSE. Filemetadata is NULL");
            return true;
        }
        if (1 != fileMetadata.getBde()) {
            LOGGER.info(LOG_STR_ENABLE_FORCE_FULL_DECISIONS_WITH_PROPERTY + propertyId + " FALSE. Filemetadata with id :" + fileMetadata.getId() + " is Not BDE");
            return true;
        }
        return false;
    }

    private boolean isForceFullDecisionsToggleEnabled() {
        return Boolean.TRUE.equals(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.ENABLE_FORCE_FULL_DECISIONS));
    }

    private boolean isForceFullDecisionsOutboundsToggleEnabled() {
        return Boolean.TRUE.equals(pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.FORCEFULLDECISIONS_OUTBOUND_ENABLED));
    }

    private boolean isForceFullDecisionSetAtOutBoundsLevel() {
        ExternalSystem externalSystem = PacmanWorkContextHelper.getExternalSystem();
        if (null != externalSystem) {
            LOGGER.info(LOG_STR_FORCE_FULL_DECISIONS_OUTBOUNDS_WITH_PROPERTY + PacmanWorkContextHelper.getPropertyId() + LOG_STR_CLIENT_ID + PacmanWorkContextHelper.getClientId() + " externalSystemInContext=" + externalSystem + " At:isForceFullDecisionSetAtOutBoundsLevel");
            String externalSystemCode = externalSystem.getCode();
            if (externalSystem == ExternalSystem.OXI || externalSystem == OPERA_AGENT) {
                externalSystemCode = OPERA_AGENT.getCode();
            }
            return forceFullDecisionsService.isForceFullDecisionAtOutBounds(PacmanWorkContextHelper.getClientId(), PacmanWorkContextHelper.getPropertyId(), externalSystemCode);
        }
        LOGGER.info(LOG_STR_FORCE_FULL_DECISIONS_OUTBOUNDS_WITH_PROPERTY + PacmanWorkContextHelper.getPropertyId() + LOG_STR_CLIENT_ID + PacmanWorkContextHelper.getClientId() + " FALSE At:isForceFullDecisionSetAtOutBoundsLevel");
        return false;
    }

    private boolean isOnDemandDecisionsRunning(){
        if(SystemConfig.shouldCheckOnDemandDecisionsRunning()) {
            String clientCode = PacmanWorkContextHelper.getClientCode();
            String propertyCode = PacmanWorkContextHelper.getPropertyCode();
            PropertyDailyProcessing pdp = dailyProcessingService.findCurrentPropertyDailyProcessing(clientCode, propertyCode);
            if (pdp == null) {
                LOGGER.info("Got PDP as Null");
                return false;
            }
            Optional<InputProcessing> currentInputProcessing = pdp.getInputProcessings().stream().max(Comparator.comparing(InputProcessing::getId));
            if (currentInputProcessing.isPresent()) {
                LOGGER.info("currentInputProcessing -> " + currentInputProcessing.get().getInputProcessingId());
                return InputType.DECISIONS_ON_DEMAND.toString().equals(currentInputProcessing.get().getInputType());
            }
            return false;
        }
        return false;
    }
    public boolean shouldSendFullDecisions(Integer propertyId) {
        if (isForceFullDecisionsToggleEnabled() && isForceFullDecisionsOutboundsToggleEnabled() && isForceFullDecisionSetAtOutBoundsLevel() && (isCurrentProcessBDE(propertyId) || isOnDemandDecisionsRunning())) {
            LOGGER.info(LOG_STR_FORCE_FULL_DECISIONS_OUTBOUNDS_WITH_PROPERTY + propertyId + LOG_STR_CLIENT_ID + PacmanWorkContextHelper.getClientId() + " TRUE At:shouldSendFullDecisions");
            return true;
        }
        return isForceFullDecisionsToggleEnabled() && isPropertyScheduledForFullDecisions(propertyId) && (isCurrentProcessBDE(propertyId) || isOnDemandDecisionsRunning());
    }

    public boolean shouldSendFullDecisionsByExternalSystemAndDecisionType(Integer propertyId, String externalSystem, String decisionName) {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        if (isForceFullDecisionsToggleEnabled() && isForceFullDecisionsOutboundsToggleEnabled() && (isCurrentProcessBDE(propertyId) || isOnDemandDecisionsRunning())) {
            ForceFullDecisions forceFullDecisions = forceFullDecisionsService.getForceFullDecisionsByClientIdAndPropertyIdAndOutBoundName(clientId, propertyId, externalSystem);
            if (forceFullDecisions != null && isDecisionTypeSelected(decisionName, forceFullDecisions)) {
                LOGGER.info(LOG_STR_FORCE_FULL_DECISIONS_OUTBOUNDS_WITH_PROPERTY + propertyId + LOG_STR_CLIENT_ID + clientId + " TRUE At:shouldSendFullDecisions");
                return true;
            }
        }
        return isForceFullDecisionsToggleEnabled() && isPropertyScheduledForFullDecisions(propertyId) && (isCurrentProcessBDE(propertyId) || isOnDemandDecisionsRunning());
    }

    private boolean isDecisionTypeSelected(String decisionName, ForceFullDecisions forceFullDecisions) {
        return ALL_DECISION_TYPES.equalsIgnoreCase(forceFullDecisions.getDecisionTypes())
                || Arrays.stream(forceFullDecisions.getDecisionTypes().split(",")).anyMatch(dt -> dt.equalsIgnoreCase(decisionName));
    }

    public boolean isImmediateDecisionDeliveryByDecisionType(Integer propertyId, String decisionName) {
        if (isForceFullDecisionsToggleEnabled() && isForceFullDecisionsOutboundsToggleEnabled() && isForceFullDecisionSetAtOutBoundsLevel() && (isCurrentProcessBDE(propertyId) || isOnDemandDecisionsRunning())) {
            LOGGER.info(LOG_STR_FORCE_FULL_DECISIONS_OUTBOUNDS_WITH_PROPERTY + propertyId + LOG_STR_CLIENT_ID + PacmanWorkContextHelper.getClientId() + " TRUE At:shouldSendFullDecisions");
            return true;
        }
        return isForceFullDecisionsToggleEnabled() && isPropertyScheduledForFullDecisions(propertyId) && (isCurrentProcessBDE(propertyId) || isOnDemandDecisionsRunning());
    }

    public boolean shouldSendFullDecisions(Integer propertyId, Integer inputProcessingId) {
        if (isForceFullDecisionsToggleEnabled() && isForceFullDecisionsOutboundsToggleEnabled() && isForceFullDecisionSetAtOutBoundsLevel() &&
                (isCurrentProcessBDEWithInputProcessingId(propertyId, inputProcessingId) || isOnDemandDecisionsRunning())) {
            LOGGER.info(LOG_STR_FORCE_FULL_DECISIONS_OUTBOUNDS_WITH_PROPERTY + propertyId + LOG_STR_CLIENT_ID + PacmanWorkContextHelper.getClientId() +
                    " InputProcessingId=" + inputProcessingId + " TRUE At:shouldSendFullDecisions");
            return true;
        }
        return isForceFullDecisionsToggleEnabled() && isPropertyScheduledForFullDecisions(propertyId) &&
                (isCurrentProcessBDEWithInputProcessingId(propertyId, inputProcessingId) || isOnDemandDecisionsRunning());
    }

    private FileMetadata getLatestFileMetadataForRecordTypeT2Snap(int propertyId) {
        return tenantCrudService.findByNamedQuerySingleResult(
                FileMetadata.BY_RECORD_TYPE_AND_PROPERTY_ORDER_BY_SNAPSHOTDT_DESC,
                QueryParameter.with("propertyId", propertyId)
                        .and("recordTypeId", T2SNAP_RECORD_TYPE_ID).parameters());
    }


    private boolean isBDEInProgress(Optional<InputProcessing> inputProcessingBDERecord) {
        return inputProcessingBDERecord.isPresent() && inputProcessingBDERecord
                .map(inputProcessing -> inputProcessing.getStatus())
                .filter(status -> InputProcessingStatus.IN_PROGRESS.toString().equals(status) || InputProcessingStatus.DECISIONS_IN_PROGRESS.toString().equals(status))
                .isPresent();
    }

    public List<DecisionConfiguration> getDecisionConfigurations() {
        return getDecisionConfigurationsFromGlobalDB();
    }

    private List<DecisionConfiguration> getDecisionConfigurationsFromGlobalDB() {
        List<PropertyStageChange> propertyStageChanges = propertyStageChangeService.getPropertyStageChangesWithStageChangeFlag();
        return Collections.singletonList(getDecisionConfigurationsDTo(propertyStageChanges));
    }

    private DecisionConfiguration getDecisionConfigurationsDTo(List<PropertyStageChange> propertyStageChanges) {
        return CollectionUtils.isEmpty(propertyStageChanges) ? createDecisionConfiguration() : createDecisionConfigurationFromGlobalDB(propertyStageChanges.get(0));
    }

    private DecisionConfiguration createDecisionConfigurationFromGlobalDB(PropertyStageChange propertyStageChange) {
        DecisionConfiguration configurationDTO = createDecisionConfiguration();
        configurationDTO.setUserEmail(getUserEmail(propertyStageChange.getInitiatedBy()));
        configurationDTO.setAction(getStageAsShownOnUI(propertyStageChange.getNewStage()));
        configurationDTO.setNotes(propertyStageChange.getNotes());
        configurationDTO.setUpdatedOn(
                DateUtil.getDateTimeByTimeZone(propertyStageChange.getStageChangedDateTime(),
                        Calendar.getInstance().getTimeZone(),
                        propertyService.getPropertyTimeZone()));
        return configurationDTO;
    }

    private DecisionConfiguration createDecisionConfiguration() {
        DecisionConfiguration configurationDTO = new DecisionConfiguration();
        configurationDTO.setDecisionWindow(optimizationWindowBDE());
        configurationDTO.setPropertyType(getParameterValue(IntegrationConfigParamName.RECEIVING_SYSTEMS.value()));
        String decision = getStageAsShownOnUI(getDecisionType());
        configurationDTO.setDecisionType(decision);
        configurationDTO.setSrpFplosAtTotalLevel(srpFplosAtTotalLevel());
        return configurationDTO;
    }

    private String getUserEmail(String userName) {
        GlobalUser user = crudService.findByNamedQuerySingleResult(GlobalUser.BY_USER_NAME,
                QueryParameter.with("userName", userName).parameters());
        if (null == user) {
            return "NA";
        }
        if (Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.USE_UNIQUE_USERID_INSTEADOF_EMAILID.getParameterName()))) {
            return user.getFullName();
        } else {
            return user.getEmail();
        }
    }

    private String getStageAsShownOnUI(String stage) {
        if (StringUtils.isNotBlank(stage)) {
            return ResourceUtil.getOptionalText(stage.toLowerCase(), Language.ENGLISH).orElse(stage);
        }
        return stage;
    }

    private String srpFplosAtTotalLevel() {
        return "true".equalsIgnoreCase(getParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value())) ? "Yes" : "No";
    }

    private String getParameterValue(String param) {
        return pacmanConfigParamsService.getParameterValue(param);
    }

    private Integer optimizationWindowBDE() {
        final String parameterValue = getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value());
        return null != parameterValue ? Integer.parseInt(parameterValue) : 0;
    }

    private String getDecisionType() {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Stage stage = propertyService.getPropertyStage(propertyId);
        return null == stage ? "" : stage.getCode();
    }

    public void createAlert(String name, String description, String details) {
        alertService.createAlert(name, description, details);
    }

    public void raiseStageChangeEvent(Property property, String user, Stage oldStage, Stage newStage, String notes, Boolean isSrpFPLOS) {
        tetrisEventManager.raiseEvent(tetrisEventManager.buildPropertyStageChangedEvent(property.getId(), user,
                oldStage, newStage, notes, getDecisions(property), getScheduledTwoWayDateValueString(), true, isSrpFPLOS));
    }

    public String getDecisions(Property property) {
        Map<String, String> decisionJson = new HashMap<>();
        if (showDecisionConfigurationGenerallyAvailableFields()) {
            decisionJson.put("optionalFieldValues", getOptionalFieldValues(property));
            decisionJson.put("decisionConfigurations", getDecisionConfigurations(property));
            return new JSONObject(decisionJson).toString();
        }
        return null;
    }

    private String getDecisionConfigurations(Property property) {
        List<com.ideas.tetris.pacman.services.decisiondelivery.DecisionConfiguration> configurations = decisionDeliveryService.getDecisionConfigurations(property.getId());
        return CollectionUtils.isNotEmpty(configurations) ? configurations.stream().map(this::getDecisionString).collect(Collectors.joining(" , ")) : null;
    }

    private String getDecisionString(com.ideas.tetris.pacman.services.decisiondelivery.DecisionConfiguration config) {
        return config.getDestination() != null && config.getDeliveryType() != null ? config.getDestination().getLocalizationKey() + " : " + config.getDeliveryType().getLocalizationKey() : null;
    }

    private String getOptionalFieldValues(Property property) {
        String optionalFieldValues = null;
        if (isClientCarlson(property.getClient().getCode())) {
            optionalFieldValues = isHideCurtisCFields() ? getOptionalFieldValuesWithoutCurtiscFields(): getOptionalFieldValues();
        }
        return optionalFieldValues;
    }

    private String getOptionalFieldValuesWithoutCurtiscFields() {
        return String.join(" , ", Arrays.asList(getAddTaxOptions(), getTaxValueText(), getOperaDailyBarRateCodeValueText()));
    }

    private String getOptionalFieldValues() {
        return String.join(" , ", Arrays.asList(getAddTaxOptions(), getTaxValueText(), getOperaDailyBarRateCodeValueText(), getCurtiscDailyBarRateCodeValueText(), getCurtiscPropertyCodeValueText()));
    }

    private String getAddTaxOptions() {
        String parameterValue = getParameterValue(IntegrationConfigParamName.APPLY_TAX.value(OPERA));
        String applyTax = YES;
        if (FALSE.equalsIgnoreCase(parameterValue)) {
            applyTax = NO;
        }
        return "decisionConfiguration.addTaxOption" + " : " + applyTax;
    }

    private String getOperaDailyBarRateCodeValueText() {
        return "decisionConfiguration.operaPMSDailyBarRateCodeLbl" + " : " + getParameterValue(IntegrationConfigParamName.DAILYBAR_PMSRATE_CODE.value(OPERA));
    }

    private String getCurtiscDailyBarRateCodeValueText() {
        return "decisionConfiguration.curtiscDailyBarRateCodeLbl" + " : " +
                pacmanConfigParamsService.getParameterValue(DAILY_BAR_DAILYBAR_RATE_CODE, ExternalSystem.CURTISC);
    }

    private String getCurtiscPropertyCodeValueText() {
        return "decisionConfiguration.curtiscPropertyCodeLbl" + " : " +
                pacmanConfigParamsService.getParameterValue(PROPERTYCODE, ExternalSystem.CURTISC);
    }

    private String getTaxValueText() {
        String parameterValue = getParameterValue(IntegrationConfigParamName.TAX_ADJUSTMENT_VALUE.value(OPERA));
        String tax = StringUtils.isNotEmpty(parameterValue) ? parameterValue : "-";
        if (getParameterValue(IntegrationConfigParamName.APPLY_TAX.value(OPERA)).equalsIgnoreCase(FALSE)) {
            tax = "-";
        }
        return "decisionConfiguration.tax" + " : " + tax;
    }

    private boolean showDecisionConfigurationGenerallyAvailableFields() {
        String parameterValue = getParameterValue(FeatureTogglesConfigParamName.SHOW_DECISION_CONFIGURATION_GAFIELDS.value());
        return StringUtils.isNotEmpty(parameterValue) && "true".equalsIgnoreCase(parameterValue);
    }

    @ForTesting
	public
    boolean isClientCarlson(String clientCode) {
        return CARLSON_CLIENT_CODE.equals(clientCode) || (shouldUseCRHGGROUPFeatureForRHGROW() && RHGROW_CLIENT_CODE.equals(clientCode));
    }

    public String getOptionalFieldValueString(String decisions) {
        JSONObject decisionsJson = getJSONDecisions(decisions);
        return (String) decisionsJson.get("optionalFieldValues");
    }

    public String getDecisionConfigurationString(String decisions) {
        JSONObject decisionsJson = getJSONDecisions(decisions);
        return (String) decisionsJson.get("decisionConfigurations");
    }

    private JSONObject getJSONDecisions(String decisions) {
        JSONParser jsonParser = new JSONParser();
        try {
            return (JSONObject) jsonParser.parse(decisions);
        } catch (ParseException e) {
            LOGGER.error("Error parsing configuration history records", e);
            return new JSONObject();
        }
    }

    public String getScheduledTwoWayDateValueString() {
        return getParameterValue(CORE_PROPERTY_SCHEDULED_TWO_WAY_DATE.value());
    }

    public Date getScheduledTwoWayDate() {
        String scheduledTwoWayDate = getScheduledTwoWayDateValueString();
        if (StringUtils.isNotEmpty(scheduledTwoWayDate) && !NOT_APPLICABLE_STRING.equalsIgnoreCase(scheduledTwoWayDate)) {
            try {
                return new SimpleDateFormat(GLOBAL_DATE_PARAM_FORMAT).parse(scheduledTwoWayDate);
            } catch (java.text.ParseException e) {
                LOGGER.error("Could not parse Scheduled 2-Way Date: " + scheduledTwoWayDate, e);
                return null;
            }
        }
        return null;
    }

    private String getContext(String clientCode, String propertyCode) {
        return pacmanConfigParamsService.propertyNode(clientCode, propertyCode);
    }

    private String getContext() {
        return pacmanConfigParamsService.propertyNode(getClientCode(), getPropertyCode());
    }

    public void createAlertAndResetScheduledTwoWayDate(String clientCode, String propertyCode) {
        createAlert(AlertType.SystemModeChangedToDecisionDelivery.getName(), "system.mode.changed.to.decision.delivery.alert.details", "");
        resetScheduledTwoWayDate(clientCode, propertyCode);
    }

    public boolean isScheduledTwoWayDateToggleEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_SCHEDULED_TWO_WAY_DATE_ENABLED.value());
    }

    public void resetScheduledTwoWayDate(String clientCode, String propertyCode) {
        pacmanConfigParamsService.deleteParameterValue(getContext(clientCode, propertyCode), CORE_PROPERTY_SCHEDULED_TWO_WAY_DATE.value(), true);

    }

    public boolean shouldScheduledTwoWayParameterBeReset() {
        return isScheduledTwoWayDateToggleEnabled() && StringUtils.isNotBlank(getParameterValue(CORE_PROPERTY_SCHEDULED_TWO_WAY_DATE.value()));
    }

    public void saveScheduled2WayDateField(Date scheduled2WayDate) {
        if (isScheduledTwoWayDateToggleEnabled()) {
            if (null != scheduled2WayDate) {
                pacmanConfigParamsService.addParameterValue(getContext(), CORE_PROPERTY_SCHEDULED_TWO_WAY_DATE.value(), getGlobalDateFormat().format(scheduled2WayDate));
            } else {
                resetScheduledTwoWayDate(getClientCode(), getPropertyCode());
            }
        }
    }

    public void saveScheduled2WayDate(Date scheduled2WayDate, Stage propertyStage) {
        if (Stage.ONE_WAY.equals(propertyStage) && scheduled2WayDate != null) {
            pacmanConfigParamsService.addParameterValue(getContext(), CORE_PROPERTY_SCHEDULED_TWO_WAY_DATE.value(), getGlobalDateFormat().format(scheduled2WayDate));
        } else if (getScheduledTwoWayDate() != null) {
            resetScheduledTwoWayDate(getClientCode(), getPropertyCode());
        }
    }

    private SimpleDateFormat getGlobalDateFormat() {
        return new SimpleDateFormat(Constants.GLOBAL_DATE_PARAM_FORMAT);
    }

    private String getPropertyCode() {
        return PacmanWorkContextHelper.getPropertyCode();
    }

    private String getClientCode() {
        return PacmanWorkContextHelper.getClientCode();
    }

    public boolean isGoLiveEnabled() {
        Stage propertyStage = propertyService.getPropertyStage(PacmanWorkContextHelper.getPropertyId());
        boolean goLiveEnabled = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GO_LIVE_ENABLED);
        return Stage.ONE_WAY.equals(propertyStage) && goLiveEnabled;
    }

    public boolean isPropertyMovingToTwoWayFirstTime(){
        int count = tenantCrudService.findByNamedQuerySingleResult(DecisionUploadDateToExternalSystem.FIND_COUNT);
        return count==0;
    }
    public void updateImmediateFullDecisionsCount(List<String> outboundNames) {
        List<ImmediateFullDecisions> list = new ArrayList<>();
        Date createdDttm = new Date();
        outboundNames.stream().forEach(outboundName -> {
            ImmediateFullDecisions imfd = new ImmediateFullDecisions();
            imfd.setOutboundName(outboundName);
            imfd.setCreatedDttm(createdDttm);
            imfd.setCreatedByUserId(Integer.valueOf(PacmanWorkContextHelper.getUserId()));
            list.add(imfd);
        });
        tenantCrudService.save(list);
    }

    public DecisionConfigurationDTO loadConfigInfo() {
        DecisionConfigurationDTO configurationDTO = new DecisionConfigurationDTO();
        setPropertyStageInfo(configurationDTO);
        loadSRPFPLOS(configurationDTO);
        loadZeroCapacityRTs(configurationDTO);
        loadZeroCapacityRTsWithPricingAndRestrictions(configurationDTO);
        loadReceivingSystem(configurationDTO);
        loadForecastWindow(configurationDTO);
        loadOptimizationWindowBDE(configurationDTO);
        loadVariableDecisionWindow(configurationDTO);
        loadOptimizationWindowCDP(configurationDTO);
        loadDecisionUploadWindowBDE(configurationDTO);
        loadOptionalFields(configurationDTO);
        loadSchedule2WayDate(configurationDTO);
        fetchIDPConfiguration(configurationDTO);
        return configurationDTO;
    }

    private void loadForecastWindow(DecisionConfigurationDTO configurationDTO) {
        final Integer parameterValue = pacmanConfigParamsService.getIntegerParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());
        configurationDTO.setForecastWindowBDE(null != parameterValue ? parameterValue : 0);
    }

    private void loadVariableDecisionWindow(DecisionConfigurationDTO configurationDTO) {
        final Integer parameterValue = pacmanConfigParamsService.getIntegerParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value());
        configurationDTO.setVariableDecisionWindow(parameterValue != null ? parameterValue : 0);
    }

    private void loadOptimizationWindowCDP(DecisionConfigurationDTO configurationDTO) {
        final Integer parameterValue = pacmanConfigParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_CDP.value());
        configurationDTO.setOptimizationWindowCDP(null != parameterValue ? parameterValue : 0);
    }

    private void setPropertyStageInfo(DecisionConfigurationDTO configurationDTO) {
        final Stage propertyStage = propertyService.getPropertyStage(PacmanWorkContextHelper.getPropertyId());
        configurationDTO.setConfigNotAvailable(null == propertyStage);

        configurationDTO.setPropertyStage(propertyStage);
    }

    private void loadSRPFPLOS(DecisionConfigurationDTO configurationDTO) {
        final boolean parameterValue = pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL);
        configurationDTO.setSrpFPLOS(parameterValue);
    }

    private void loadZeroCapacityRTs(DecisionConfigurationDTO configurationDTO) {
        String zeroCapacityRTs = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.ZERO_CAPACITY_ROOM_TYPES_TO_INCLUDE.value(Constants.OPERA));
        if (null != zeroCapacityRTs) {
            configurationDTO.setZeroCapacityRTs(zeroCapacityRTs);
        }
    }

    private void loadZeroCapacityRTsWithPricingAndRestrictions(DecisionConfigurationDTO configurationDTO) {
        String zeroCapacityRTsWithPricingAndRestrictions = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.ZERO_CAPACITY_ROOM_TYPES_REQUIRING_PRICING_AND_RESTRICTIONS, Constants.OPERA);
        if (null != zeroCapacityRTsWithPricingAndRestrictions) {
            configurationDTO.setZeroCapacityRTsWithPricingAndRestrictions(zeroCapacityRTsWithPricingAndRestrictions);
        }
    }

    private void loadReceivingSystem(DecisionConfigurationDTO configurationDTO) {
        String parameterValue = getParameterValue(IntegrationConfigParamName.RECEIVING_SYSTEMS.value());
        if (showDecisionConfigurationGenerallyAvailableFields() && isClientAccor(getClientCode())) {
            parameterValue = getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM.value());
            configurationDTO.setPropertyType("None".equalsIgnoreCase(parameterValue) ? "Opera Agent" : parameterValue);
        } else {
            configurationDTO.setPropertyType((String) ObjectUtils.defaultIfNull(parameterValue, ""));
        }
    }
    private void loadOptimizationWindowBDE(DecisionConfigurationDTO configurationDTO) {
        final Integer parameterValue = pacmanConfigParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value());
        configurationDTO.setOptimizationWindowBDE(null != parameterValue ? parameterValue : 0);
    }

    private void loadDecisionUploadWindowBDE(DecisionConfigurationDTO configurationDTO) {
        final Integer parameterValue = pacmanConfigParamsService.getIntegerParameterValue(IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value());
        configurationDTO.setDecisionUploadWindowBDE(null != parameterValue ? parameterValue : 0);
    }

    private void loadOptionalFields(DecisionConfigurationDTO configurationDTO) {
        loadOPMSDailyBarRateCode(configurationDTO);
        loadCurtiscDailyBarRateCode(configurationDTO);
        loadCurtiscPropertyCode(configurationDTO);
        loadAddTaxOption(configurationDTO);
        loadTax(configurationDTO);
    }

    private void loadOPMSDailyBarRateCode(DecisionConfigurationDTO configurationDTO) {
        final String parameterValue = getParameterValue(IntegrationConfigParamName.DAILYBAR_PMSRATE_CODE.value(OPERA));
        configurationDTO.setOperaPMSDailyBarRateCode((String) ObjectUtils.defaultIfNull(parameterValue, ""));
    }

    private void loadCurtiscDailyBarRateCode(DecisionConfigurationDTO configurationDTO) {
        String parameterValue = pacmanConfigParamsService.getParameterValue(DAILY_BAR_DAILYBAR_RATE_CODE, ExternalSystem.CURTISC);
        configurationDTO.setCurtiscDailyBarRateCode(Optional.ofNullable(parameterValue).orElse(EMPTY_STRING));
    }

    private void loadCurtiscPropertyCode(DecisionConfigurationDTO configurationDTO) {
        String parameterValue = pacmanConfigParamsService.getParameterValue(PROPERTYCODE, ExternalSystem.CURTISC);
        configurationDTO.setCurtiscPropertyCode(Optional.ofNullable(parameterValue).orElse(EMPTY_STRING));
    }


    private void loadAddTaxOption(DecisionConfigurationDTO configurationDTO) {
        final boolean parameterValue = pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.APPLY_TAX.value(OPERA));
        configurationDTO.setAddTaxOption(parameterValue ? YES : NO);
    }

    private void loadTax(DecisionConfigurationDTO configurationDTO) {
        if (isContinuousPricingEnabled() && configurationDTO.getAddTaxOption().equalsIgnoreCase(YES)) {
            configurationDTO.setTax(BigDecimal.ZERO);
        } else {
            configurationDTO.setTax(pacmanConfigParamsService.getBigDecimalParameterValue(IntegrationConfigParamName.TAX_ADJUSTMENT_VALUE.value(OPERA)));
        }
    }

    private void loadSchedule2WayDate(DecisionConfigurationDTO configurationDTO) {
        final Date parameterValue = getScheduledTwoWayDate();
        configurationDTO.setScheduled2WayDate(parameterValue == null ? null : DateFormatUtils.format(parameterValue, DateUtil.DEFAULT_DATE_FORMAT));
    }

    private boolean isContinuousPricingEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
    }

    private void fetchIDPConfiguration(DecisionConfigurationDTO configurationDTO) {
        List<CdpSchedule> cdpSchedules = configurationService.getCdpSchedules();
        List<CdpScheduleDTO> cdpScheduleDTOS = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DEFAULT_TIME_FORMAT_12HR);
        cdpSchedules.sort(Comparator.comparing(CdpSchedule::getCreateDate));
        for (CdpSchedule cdpSchedule : cdpSchedules) {
            LocalTime localTime = JavaLocalDateUtils.jodaToJavaLocalTime(cdpSchedule.getCdpTime());
            cdpScheduleDTOS.add(new CdpScheduleDTO(cdpSchedule.getId(), localTime.format(formatter)));
        }
        configurationDTO.setIdpSchedules(cdpScheduleDTOS);
    }

    public boolean isClientAccor(String clientCode) {
        return CLIENT_ACCORHG.equals(clientCode);
    }

    public String saveDecisionConfiguration(DecisionConfigurationDTO newConfig) {
        DecisionConfigurationDTO originalConfig = loadConfigInfo();
        if (!isValidConfiguration(newConfig, originalConfig)) {
            saveScheduled2WayDateAndOptionalFields(newConfig, originalConfig);
        } else {
            // todo reprocessing confirmation message
            saveDecisionConfiguration(newConfig, originalConfig);
        }
        return "Successfully saved";

    }

    private void saveScheduled2WayDateAndOptionalFields(DecisionConfigurationDTO newConfig, DecisionConfigurationDTO originalConfig) {
        String newScheduledDate = newConfig.getScheduled2WayDate();
        if(scheduled2WayDateHasChanges(newScheduledDate, originalConfig.getScheduled2WayDate()) ||
                optionalFieldHasChanges(newConfig, originalConfig)) {
            saveScheduled2WayDate(newScheduledDate);
            saveOptionalParams(newConfig);
            raiseStageChangeEvent(newConfig, originalConfig);
        }
    }

    private void saveOptionalParams(DecisionConfigurationDTO newConfig) {
        if (!showDecisionConfigurationGenerallyAvailableFields()) {
            return;
        }
        if (isClientCarlson(getClientCode())) {
            addParameterValue(IntegrationConfigParamName.DAILYBAR_PMSRATE_CODE.value(OPERA), newConfig.getOperaPMSDailyBarRateCode());

            pacmanConfigParamsService.addParameterValue(getNewStyleContextFor(CURTISC),
                    convertOldParameterToNew(DAILY_BAR_DAILYBAR_RATE_CODE.value(CURTISC)), newConfig.getCurtiscDailyBarRateCode());
            pacmanConfigParamsService.addParameterValue(getNewStyleContextFor(CURTISC),
                    convertOldParameterToNew(PROPERTYCODE.value(CURTISC)), newConfig.getCurtiscPropertyCode());

            if (newConfig.getAddTaxOption().equals(YES)) {
                pacmanConfigParamsService.addParameterValue(getNewStyleContextFor(CURTISC),
                        convertOldParameterToNew(DAILY_BAR_TAX_ADJUSTMENT_VALUE.value(CURTISC)), newConfig.getTax().toPlainString());
                pacmanConfigParamsService.addParameterValue(getNewStyleContextFor(CURTISC),
                        convertOldParameterToNew(LRVAT_ROOM_CLASS_TAX_ADJUSTMENT_VALUE.value(CURTISC)), newConfig.getTax().toPlainString());
                addParameterValue(IntegrationConfigParamName.TAX_ADJUSTMENT_VALUE.value(OPERA), newConfig.getTax().toPlainString());
                addParameterValue(IntegrationConfigParamName.APPLY_TAX.value(OPERA), String.valueOf(Boolean.TRUE));
            } else {
                pacmanConfigParamsService.deleteParameterValue(getNewStyleContextFor(CURTISC),
                        convertOldParameterToNew(DAILY_BAR_TAX_ADJUSTMENT_VALUE.value(CURTISC)), Boolean.TRUE);
                pacmanConfigParamsService.deleteParameterValue(getNewStyleContextFor(CURTISC),
                        convertOldParameterToNew(LRVAT_ROOM_CLASS_TAX_ADJUSTMENT_VALUE.value(CURTISC)), Boolean.TRUE);
                deleteParamValue(IntegrationConfigParamName.TAX_ADJUSTMENT_VALUE.value(OPERA));
                addParameterValue(IntegrationConfigParamName.APPLY_TAX.value(OPERA), String.valueOf(Boolean.FALSE));
            }
        } else if (isClientAccor(getClientCode()) && externalSystemHelper.isFOLS()) {
            addParameterValue(IntegrationConfigParamName.ROOM_TYPE_OVERBOOKING_UPLOAD_TYPE.value(OPERA), DecisionUploadType.NONE.getConfigParamValue());
            addParameterValue(IntegrationConfigParamName.HOTEL_OVERBOOKING_UPLOADTYPE.value(OPERA), DecisionUploadType.NONE.getConfigParamValue());
        }
    }

    private String getNewStyleContextFor(String vendor) {
        return vendor + "." + getClientCode() + "." + getPropertyCode();
    }

    private void addParameterValue(String paramName, String configParamValue) {
        pacmanConfigParamsService.addParameterValue(getContext(), paramName, configParamValue);
    }

    private void deleteParamValue(String paramName) {
        pacmanConfigParamsService.deleteParameterValue(getContext(), paramName, Boolean.TRUE);
    }



    private boolean optionalFieldHasChanges(DecisionConfigurationDTO newConfig, DecisionConfigurationDTO originalConfig) {
        return !StringUtils.equals(newConfig.getOperaPMSDailyBarRateCode(), originalConfig.getOperaPMSDailyBarRateCode()) ||
                !StringUtils.equals(newConfig.getCurtiscDailyBarRateCode(), originalConfig.getCurtiscDailyBarRateCode()) ||
                !StringUtils.equals(newConfig.getCurtiscPropertyCode(), originalConfig.getCurtiscPropertyCode()) ||
                !StringUtils.equals(newConfig.getAddTaxOption(), originalConfig.getAddTaxOption()) ||
                (newConfig.getTax() == null ? originalConfig.getTax() != null : newConfig.getTax().compareTo(originalConfig.getTax()) != 0);
    }

    protected void saveZeroCapacityRTsRequiringDecisions(DecisionConfigurationDTO newConfig, DecisionConfigurationDTO originalConfig) {
        if (!originalConfig.getZeroCapacityRTs().trim().equalsIgnoreCase(newConfig.getZeroCapacityRTs().trim())) {
            pacmanConfigParamsService.updateParameterValue(IntegrationConfigParamName.ZERO_CAPACITY_ROOM_TYPES_TO_INCLUDE.value(Constants.OPERA), newConfig.getZeroCapacityRTs());
        }
    }

    protected void saveZeroCapacityRTsWithPricingAndRestrictions(DecisionConfigurationDTO newConfig, DecisionConfigurationDTO originalConfig) {
        if (!originalConfig.getZeroCapacityRTsWithPricingAndRestrictions().trim().equalsIgnoreCase(newConfig.getZeroCapacityRTsWithPricingAndRestrictions().trim())) {
            pacmanConfigParamsService.updateParameterValue(IntegrationConfigParamName.ZERO_CAPACITY_ROOM_TYPES_REQUIRING_PRICING_AND_RESTRICTIONS.value(Constants.OPERA), newConfig.getZeroCapacityRTsWithPricingAndRestrictions());
            List<String> parameterValueAsListOfStrings = pacmanConfigParamsService.getParameterValueAsListOfStrings(IntegrationConfigParamName.ZERO_CAPACITY_ROOM_TYPES_REQUIRING_PRICING_AND_RESTRICTIONS, OPERA);
            qualifiedRateService.setLV0OffsetToZeroForAccomTypes(parameterValueAsListOfStrings);
        }
    }

    private void saveDecisionConfiguration(DecisionConfigurationDTO newConfig, DecisionConfigurationDTO originalConfig) {
        saveWindowBDE(newConfig, originalConfig);
        if (shouldDisplayZeroCapacityRTField()) {
            saveZeroCapacityRTsRequiringDecisions(newConfig, originalConfig);
            saveZeroCapacityRTsWithPricingAndRestrictions(newConfig, originalConfig);
        }
        saveOptionalParams(newConfig);
        String scheduled2WayDate = newConfig.getScheduled2WayDate();
        saveScheduled2WayDate(scheduled2WayDate);

        saveStage(newConfig);
        if (allowExternalIDPConfiguration() && newConfig.isValidCdpSchedules(originalConfig)) {
            saveIDPSchedules(newConfig.getIdpSchedules());
        }
    }

    public boolean allowExternalIDPConfiguration() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ALLOW_EXTERNAL_IDP_CONFIGURATION);
    }

    private void saveScheduled2WayDate(String scheduled2WayDate) {
        try {
            saveScheduled2WayDateField(scheduled2WayDate == null ? null : DateUtils.parseDate(scheduled2WayDate, DateUtil.DEFAULT_DATE_FORMAT));
        } catch (java.text.ParseException e) {
            LOGGER.error("Invalid Scheduled Two Way Date parsed" + scheduled2WayDate);
            saveScheduled2WayDateField(null);
        }
    }

    private void saveStage(DecisionConfigurationDTO newConfig) {
        Stage stage = Stage.valueForCode(newConfig.getPropertyStage().getCode());
        propertyStageChangeService.changeStage(PacmanWorkContextHelper.getPropertyId(), stage,
                newConfig.isSrpFPLOS(), newConfig.getNote());
    }

    private void saveIDPSchedules(List<CdpScheduleDTO> items) {
        List<CdpSchedule> cdpSchedules = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DEFAULT_TIME_FORMAT_12HR);
        for (CdpScheduleDTO item : items) {
            CdpSchedule cdpSchedule = new CdpSchedule();
            cdpSchedule.setId(item.getId());
            LocalTime time = LocalTime.parse(item.getLocalTime(), formatter);
            cdpSchedule.setCdpTime(JavaLocalDateUtils.toJodaLocalTime(time));
            cdpSchedules.add(cdpSchedule);
        }

        configurationService.saveCdpSchedules(cdpSchedules);
        pacmanConfigParamsService.updateParameterValue(getContext(),
                IntegrationConfigParamName.CORE_PROPERTY_CDP_DAILYMAX.value(),
                Integer.toString(cdpSchedules.size()));
    }

    private void saveWindowBDE(DecisionConfigurationDTO configurationDTO, DecisionConfigurationDTO originalConfig) {
        if (configurationDTO.getDecisionUploadWindowBDE() != null &&
                (originalConfig.getDecisionUploadWindowBDE() == null ||
                        originalConfig.getDecisionUploadWindowBDE().intValue() != configurationDTO.getDecisionUploadWindowBDE().intValue())) {
            pacmanConfigParamsService.addParameterValue(getContext(), IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value(), configurationDTO.getDecisionUploadWindowBDE().toString());
            if (syncEventAggregatorService.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)) {
                syncDisplayNameService.addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                        CustomizedDisplayName.DECISION_CONFIGURATION_CHANGED);
            }
        }
    }

    private static boolean scheduled2WayDateHasChanges(String newScheduledDate, String oldScheduledDate) {
        return !Objects.equals(oldScheduledDate, newScheduledDate);
    }

    void raiseStageChangeEvent(DecisionConfigurationDTO newConfig, DecisionConfigurationDTO originalConfig) {
        Stage newStage = newConfig.getPropertyStage();
        Stage oldStage = originalConfig.getPropertyStage();
        raiseStageChangeEvent(propertyService.getPropertyById(PacmanWorkContextHelper.getPropertyId()), getCurrentUser(), oldStage, newStage, newConfig.getNote(), newConfig.isSrpFPLOS());
    }

    public String getCurrentUser() {
        return PacmanThreadLocalContextHolder.getPrincipal() != null ?
                PacmanThreadLocalContextHolder.getPrincipal().getDisplayName() : "NA";
    }


    private boolean isValidConfiguration(DecisionConfigurationDTO newConfig, DecisionConfigurationDTO originalConfig) {
        return newConfig.equals(originalConfig) || (allowExternalIDPConfiguration() && newConfig.isValidCdpSchedules(originalConfig));
    }

    private LDAPUser getLDAPUser(){
        return userService.getExistingUserByUID(PacmanWorkContextHelper.getUserId());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<DecisionConfigurationHistoryDTO> getDecisionConfigurationHistory(){
        LDAPUser ldapUser = getLDAPUser();
        Language language = Language.getLanguageFromString(ldapUser.getLanguage());
        String userPreferredDatePattern = getUserPreferredDateFormat(ldapUser);
        List<PropertyStageChange> propertyStageChanges = propertyStageChangeService.getPropertyStageChangesWithStageChangeFlag();
        return propertyStageChanges.stream().map(s->createDecisionConfigurationHistoryDTOFromGlobalDBData(s, userPreferredDatePattern, language)).collect(Collectors.toList());
    }

    private String getUserPreferredDateFormat(LDAPUser ldapUser) {
        String defaultDateFormat = ldapUser.getDefaultDateFormat();
        return StringUtils.replaceChars(defaultDateFormat, 'm', 'M');
    }

    private DecisionConfigurationHistoryDTO createDecisionConfigurationHistoryDTOFromGlobalDBData(PropertyStageChange propertyStageChange, String userPreferredDatePattern, Language language) {
        String action = ResourceUtil.getText(Stage.valueForCode(propertyStageChange.getNewStage()).getCode().toLowerCase(), language);
        Date stageChangeDate = propertyStageChange.getStageChangedDateTime();
        String date = DateFormatUtils.format(stageChangeDate, userPreferredDatePattern, language.getLocale());
        String time = DateFormatUtils.format(stageChangeDate, DecisionConfigurationHistoryDTO.TIME_FORMAT) + " " + getPropertyTimeZoneFor(stageChangeDate);
        String scheduledTwoWayDate = Optional.ofNullable(propertyStageChange.getScheduledTwoWayDate()).map(t -> DateUtil.formatDate(t, GLOBAL_DATE_PARAM_FORMAT)).orElse(null);
        String decisionsString = getFormatedDecisionsString(propertyStageChange.getDecisions(), propertyStageChange.issRPFPLOSTotalLevel(), scheduledTwoWayDate, language);
        return new DecisionConfigurationHistoryDTO(propertyStageChange.getInitiatedBy(), action, propertyStageChange.getNotes(), date+" "+time, decisionsString);
    }


    private String getPropertyTimeZoneFor(Date date) {
        TimeZone propertyTimeZone = propertyService.getPropertyTimeZone();
        return propertyTimeZone.getDisplayName(propertyTimeZone.inDaylightTime(date), 0);
    }

    private String getFormatedDecisionsString(String decisionsString, boolean srpFPLOSAtTotalLevelFlag, String scheduledTwoWayDate, Language language) {
        String decisions = getFormattedDecisionStringFor(decisionsString, srpFPLOSAtTotalLevelFlag, language);
        if (scheduledTwoWayDate != null) {
            return ResourceUtil.getText("decisionConfiguration.scheduled2WayDate",language) + " : " + getScheduledDateForConfigHistory(scheduledTwoWayDate) + ",\n" + decisions;
        }
        return decisions;
    }

    private String getFormattedDecisionStringFor(String decisionsString, boolean srpFPLOSAtTotalLevelFlag, Language language) {
        if (showDecisionConfigurationGenerallyAvailableFields()) {
            return Optional.ofNullable(decisionsString).filter(StringUtils::isNotEmpty).map(s -> formatDecisions(s, language)).orElse("-");
        }
        return srpFPLOSAtTotalLevelFlag ? ResourceUtil.getText("srpFPLOSDecisionAtTotalLevel", language) :
                ResourceUtil.getText("srpFPLOSDecisionAtRoomTypeLevel", language);
    }

    private String getScheduledDateForConfigHistory(String scheduledDateValue) {
        try {
            return DateFormatUtils.format(new SimpleDateFormat(GLOBAL_DATE_PARAM_FORMAT).parse(scheduledDateValue),DecisionConfigurationHistoryDTO.DATE_FORMAT);
        } catch (java.text.ParseException e) {
            LOGGER.error("Could not parse date: " + scheduledDateValue, e);
            return "-";
        }
    }

    private String formatDecisions(String decisions, Language language) {
        if (decisions.contains("optionalFieldValues")) {
            String optionalFieldValueString = getOptionalFieldValueString(decisions);
            String decisionConfigurationString = getDecisionConfigurationString(decisions);
            if (StringUtils.isNotEmpty(decisionConfigurationString)) {
                decisions = formatDecisionConfigurationString(decisionConfigurationString, language);
                if (StringUtils.isNotEmpty(optionalFieldValueString)) {
                    decisions = formatOptionalFieldValueString(optionalFieldValueString, language) + ", \n" + decisions;
                }
                return decisions;
            } else {
                return "-";
            }
        } else {
            List<String> decisionsList = Arrays.asList(decisions.split(" , "));
            return String.join(", \n", decisionsList);
        }
    }

    private String formatDecisionConfigurationString(String decisions, Language language) {
        String[] decisionsList = decisions.split(" , ");
        List<String> transulatedDecisionsList = new ArrayList<>();
        for (String decision : decisionsList) {
            String translatedDecision = Stream.of(decision.split(" : "))
                    .map(s->getDecisionConfigurationText(s, language))
                    .collect(Collectors.joining(" : "));
            transulatedDecisionsList.add(translatedDecision);
        }
        return String.join(", \n", transulatedDecisionsList);
    }

    private String formatOptionalFieldValueString(String optionalFieldValueString, Language language) {
        String[] optionalFieldValueList = optionalFieldValueString.split(" , ");
        List<String> transulatedoptionalFieldValuesList = new ArrayList<>();
        for (String optionalField : optionalFieldValueList) {
            String[] fields = optionalField.split(" : ");
            String transulatedOptionalField = fields.length == 2 ? ResourceUtil.getText(fields[0],language) + " : " + fields[1]
                    :  ResourceUtil.getText(fields[0],language) + " : - ";
            transulatedoptionalFieldValuesList.add(transulatedOptionalField);
        }
        return String.join(", \n", transulatedoptionalFieldValuesList);
    }

    private String getDecisionConfigurationText(String decisionConfig, Language language) {
        return ResourceUtil.getText(decisionConfig, language).replaceFirst(" " + ResourceUtil.getText("system",language), "");
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<SellingSystemDTO> getSellingSystems() {
        LDAPUser ldapUser = getLDAPUser();
        Language lan = Language.getLanguageFromString(ldapUser.getLanguage());
        return decisionDeliveryService.getDecisionConfigurations(PacmanWorkContextHelper.getPropertyId())
                .stream().map(decisionConfiguration -> prepareSellingSystemDTO(decisionConfiguration,lan))
                .collect(Collectors.toList());
    }

    private SellingSystemDTO prepareSellingSystemDTO(com.ideas.tetris.pacman.services.decisiondelivery.DecisionConfiguration decisionConfiguration, Language lan){
       return new SellingSystemDTO(getOutBoundDisplayName(decisionConfiguration.getDestination(),lan),decisionConfiguration.getDestination().name(), getDecisionTypeName(decisionConfiguration,lan));
    }
    private String getDecisionTypeName(com.ideas.tetris.pacman.services.decisiondelivery.DecisionConfiguration configuration, Language lan){
        return ResourceUtil.getText(configuration.getDeliveryType().getLocalizationKey(), lan);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public ComponentStatusDTO getComponentStatus() {
        ComponentStatusDTO statusDTO = new ComponentStatusDTO();
        boolean showDecisionConfigurationGenerallyAvailableFields = showDecisionConfigurationGenerallyAvailableFields();
        statusDTO.setSrpFPLOSVisible(!showDecisionConfigurationGenerallyAvailableFields);
        statusDTO.setDecisionUploadWindowBDEEnabled(!showDecisionConfigurationGenerallyAvailableFields);
        statusDTO.setIDPScheduleVisible(allowExternalIDPConfiguration());
        statusDTO.setZeroCapacityRTFieldVisible(shouldDisplayZeroCapacityRTField());
        statusDTO.setZeroCapacityRTRestrictionVisible(shouldDisplayZeroCapacityRTField());
//        statusDTO.setPropertyTypeVisible(!showDecisionConfigurationGenerallyAvailableFields);
        statusDTO.setPropertyTypeVisible(showDecisionConfigurationGenerallyAvailableFields && isClientAccor(PacmanWorkContextHelper.getClientCode()));
        boolean gaFieldsForCarlsonClient = showDecisionConfigurationGenerallyAvailableFields && isClientCarlson(PacmanWorkContextHelper.getClientCode());
        statusDTO.setOperaDailyBARRateVisible(gaFieldsForCarlsonClient);
        statusDTO.setTaxVisible(gaFieldsForCarlsonClient);
        statusDTO.setAddTaxOptionVisible(gaFieldsForCarlsonClient);
        statusDTO.setCurtiscBARRateVisible(gaFieldsForCarlsonClient && !isHideCurtisCFields());
        statusDTO.setCurtisPropertyCodeVisible(gaFieldsForCarlsonClient && !isHideCurtisCFields());
        statusDTO.setContinuousPricingEnabled(isContinuousPricingEnabled());

        statusDTO.setRequestFullUploadVisible(isForceFullDecisionsEnabled());
        statusDTO.setUploadBySellingSystemEnabled(isForceFullDecisionsAtOutboundsEnabled());
        statusDTO.setUploadAtBDEIsVisible(isForceFullDecisionsEnabled());
        statusDTO.setUploadAtBDEIsEnabled(!isForceFullDecisions());
        statusDTO.setUploadNowIsVisible(isImmediateFullDecisionsEnabled());
        statusDTO.setUploadNowIsEnabled(isImmediateFullDecisionsEditable());
        statusDTO.setScheduleDecisionDeliveryByDecisionTypeEnabled(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SCHEDULE_DECISION_DELIVERY_BY_DECISION_TYPE_ENABLE));
        statusDTO.setImmediateUploadDisabledReason(getImmediateUploadDisabledReason());
        statusDTO.setScheduledTwoWayDateEnabled(isScheduledTwoWayDateEnabled());

        return statusDTO;
    }

    private String getImmediateUploadDisabledReason() {
        LDAPUser ldapUser = getLDAPUser();
        Language lan = Language.getLanguageFromString(ldapUser.getLanguage());
        if (isProcessingRunningOrPlannedToRun()) {
            return ResourceUtil.getText("immediateFullDecisionsDisabled.processing", lan);
        } else if (isDecisionUploadInProgress()) {
            return ResourceUtil.getText("immediateFullDecisionsDisabled.decisionDeliveryInProgress", lan);
        } else if (isForceFullDecisions()) {
            return ResourceUtil.getText("immediateFullDecisionsDisabled.deliveryAlreadyScheduled", lan);
        } else if (hasImmediateDecisionsCountReachedDailyLimit()) {
            return ResourceUtil.getText("immediateFullDecisionsDisabled.dailyCountLimitReached", lan);
        }
        return "";
    }


    public boolean isHideCurtisCFields(){
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.HIDE_CURTIS_C_FIELDS_ON_DECISION_CONFIGURATION);
    }
    public boolean shouldDisplayZeroCapacityRTField() {
        return isContinuousPricingEnabled() && isDisplayZeroCapacityRTEnabled();
    }

    public boolean isDisplayZeroCapacityRTEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DISPLAY_ZERO_CAPACITY_RT_ON_DECISION_CONFIGURATION.value());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<FullDecisionScheduleDTO> getCurrentFullDecisionSchedule() {
        List<FullDecisionScheduleDTO> fullDecisionScheduleDTOS = new ArrayList<>();

        List<Property> properties = getPropertiesWithFullDecisions(PacmanWorkContextHelper.getClientId());
        final List<ForceFullDecisions> forceFullDecisionsAtOutbound = forceFullDecisionsService.getForceFullDecisionsAtOutbound();
        if (CollectionUtils.isEmpty(properties) && CollectionUtils.isEmpty(forceFullDecisionsAtOutbound)) {
            return new ArrayList<>();
        }
        Map<Integer, String> usersMap = getUserNameMapByClientIdAndInternalUsers();
        Language language = getLanguage();
        if (!showFullDecisionsAllOutboundNames()) {
            fullDecisionScheduleDTOS.addAll(toFullDecisionScheduleDTO(properties, usersMap, language));
        }
        updateOutboundNameWithPropertyCode(forceFullDecisionsAtOutbound, language);
        fullDecisionScheduleDTOS.addAll(toFullDecisionScheduleDTOFromForceFullDecision(forceFullDecisionsAtOutbound, usersMap, language,false));

        return fullDecisionScheduleDTOS;
    }

    private List<FullDecisionScheduleDTO> toFullDecisionScheduleDTO(List<Property> properties, Map<Integer, String> usersMap, Language language) {
        List<FullDecisionScheduleDTO> fullDecisionScheduleDTOS = new ArrayList<>();
        String propertyCode = PacmanWorkContextHelper.getPropertyCode();
        String scheduled = getFormattedString("scheduled",language);
        String allOutbound = getFormattedString("all.outbound",language);
        String decisions = getFormattedString("decisions.all",language);
        properties.forEach(property -> {
            FullDecisionScheduleDTO fullDecisionScheduleDTO = new FullDecisionScheduleDTO();
            fullDecisionScheduleDTO.setPropertyName(property.getCode());
            fullDecisionScheduleDTO.setSellingSystemName(allOutbound);
            fullDecisionScheduleDTO.setStatus(scheduled);
            fullDecisionScheduleDTO.setDecisions(decisions);
            fullDecisionScheduleDTO.setRequestedOn(property.getLastUpdatedDate().toLocalDate().toString());
            fullDecisionScheduleDTO.setRequestedBy(usersMap.get(property.getLastUpdatedByUserId()));
            fullDecisionScheduleDTO.setIsCancelScheduled(propertyCode.equals(property.getCode()) && !isDecisionUploadInProgress());
            fullDecisionScheduleDTOS.add(fullDecisionScheduleDTO);
        });

        return fullDecisionScheduleDTOS;
    }

    private List<FullDecisionScheduleDTO> toFullDecisionScheduleDTOFromForceFullDecision(List<ForceFullDecisions> forceFullDecisions, Map<Integer, String> usersMap,
                                                                                         Language  language, Boolean isPastFullDecision) {
        List<FullDecisionScheduleDTO> fullDecisionScheduleDTOS = new ArrayList<>();
        String propertyCode = PacmanWorkContextHelper.getPropertyCode();
        String scheduled = getFormattedString("scheduled", language);
        String cancelled = getFormattedString("cancelled", language);
        String fullDecisionsCompleted = getFormattedString("fullDecisionsCompleted", language);
        String jobAbandoned = getFormattedString("jobStatus.abandoned", language);
        String decisionUploadInProgress = getFormattedString("client.dashboard.decision.inprogress", language);

        forceFullDecisions.forEach(fullDecisions -> {
            FullDecisionScheduleDTO fullDecisionScheduleDTO = new FullDecisionScheduleDTO();
            fullDecisionScheduleDTO.setSellingSystemName(fullDecisions.getOutboundName());
            fullDecisionScheduleDTO.setPropertyName(fullDecisions.getProperty().getCode());
            fullDecisionScheduleDTO.setDecisions(fullDecisions.getDecisionTypes().equals(ALL_DECISION_TYPES) ? ALL_DECISION_TYPES : getSelectedDecisionsToDisplayByLanguage(fullDecisions.getDecisionTypes(), language));
            fullDecisionScheduleDTO.setRequestedOn(fullDecisions.getLastUpdatedDate().toLocalDate().toString());
            fullDecisionScheduleDTO.setRequestedBy(usersMap.get(fullDecisions.getLastUpdatedByUserId()));
            fullDecisionScheduleDTO.setIsCancelScheduled(!isPastFullDecision && propertyCode.equals(fullDecisions.getProperty().getCode()) && !isDecisionUploadInProgress() && !fullDecisions.getImmediateDecisionUpload());
            String requestType = Boolean.TRUE.equals((fullDecisions.getImmediateDecisionUpload())) ?
                    "Now" : "Next Nightly Processing";
            fullDecisionScheduleDTO.setRequestType(requestType);

            boolean wasForceFullDecisionTriggeredBySystemOrCancelledByUser = Objects.equals(fullDecisions.getIsForceFullDecision(), ForceFullDecisionsUpdateReasons.FFD_UPLOADED.getReasonID()) ||
                    Objects.equals(fullDecisions.getIsForceFullDecision(), ForceFullDecisionsUpdateReasons.JOB_ABANDONED.getReasonID()) ||
                    Objects.equals(fullDecisions.getIsForceFullDecision(), ForceFullDecisionsUpdateReasons.FFD_CANCELLED_FROM_UI.getReasonID());

            if (Boolean.TRUE.equals(fullDecisions.getImmediateDecisionUpload()) && !(wasForceFullDecisionTriggeredBySystemOrCancelledByUser)) {
                fullDecisionScheduleDTO.setStatus(decisionUploadInProgress);
            } else if (isCurrentProcessBDE(PacmanWorkContextHelper.getPropertyId()) && !(wasForceFullDecisionTriggeredBySystemOrCancelledByUser)) {
                fullDecisionScheduleDTO.setStatus(decisionUploadInProgress);
            } else if (isForceFullDecisionsSetAtOutbounds(fullDecisions)) {
                fullDecisionScheduleDTO.setStatus(scheduled);
            } else if (Objects.equals(fullDecisions.getIsForceFullDecision(), ForceFullDecisionsUpdateReasons.FFD_UPLOADED.getReasonID())) {
                fullDecisionScheduleDTO.setStatus(fullDecisionsCompleted);
            } else if (Objects.equals(fullDecisions.getIsForceFullDecision(), ForceFullDecisionsUpdateReasons.JOB_ABANDONED.getReasonID())) {
                fullDecisionScheduleDTO.setStatus(jobAbandoned);
            } else {
                fullDecisionScheduleDTO.setStatus(cancelled);
            }
            fullDecisionScheduleDTOS.add(fullDecisionScheduleDTO);
        });
        return fullDecisionScheduleDTOS;
    }

    private String getSelectedDecisionsToDisplayByLanguage(String decisions, Language language) {
        return Arrays.stream(decisions.split(",\\s*"))
                .map(DecisionDeliveryType::fromCode)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(deliveryType -> getFormattedString(deliveryType.getLocalizationKey(), language))
                .collect(Collectors.joining(", "));
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<FullDecisionScheduleDTO> getPastFullDecisionSchedule(String requestedStartDate, String requestedEndDate) {
        List<FullDecisionScheduleDTO> fullDecisionScheduleDTOS = new ArrayList<>();

        Date startDate = getStartDate(requestedStartDate);
        Date endDate = getEndDate(requestedEndDate);

        Map<Integer, String> usersMap = getUserNameMapByClientIdAndInternalUsers();
        Language language = getLanguage();

        List<ForceFullDecisions> outboundFullDecisionsForAudit = getOutboundLevelForceFullDecisionsForAudit(startDate, endDate);
        updateOutboundNameWithPropertyCode(outboundFullDecisionsForAudit, language);

        if (!showFullDecisionsAllOutboundNames()) {
            List<PropertyAuditDto> propertyAuditDtos = getPropertyLevelForceFullDecisionsForAudit(startDate, endDate);
            fullDecisionScheduleDTOS.addAll(toFullDecisionScheduleDTOFormPropertyDto(propertyAuditDtos, language));
        }
        fullDecisionScheduleDTOS.addAll(toFullDecisionScheduleDTOFromForceFullDecision(outboundFullDecisionsForAudit, usersMap, language,true));

        return fullDecisionScheduleDTOS;
    }

    private  Date getEndDate(String endDate) {
        if (Objects.nonNull(endDate)) {
            return DateUtil.convertLocalDateTimeToJavaUtilDate(setEndDateTimeAtEndOfTheDay(endDate)); // include changes through the entire day specified by endDate
        }
        return null;
    }

    private LocalDateTime setEndDateTimeAtEndOfTheDay(String endDate) {
        return java.time.LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay().plusDays(1).minusSeconds(1);
    }

    private Date getStartDate(String startDate) {
        if (Objects.nonNull(startDate) && !startDate.contains("null")) {
            return DateUtil.addDaysToDate(getFormattedDate(startDate), -7); // go back 7 days prior to date range start to make sure that full decision change is captured
        } else {
            return DateUtil.convertLocalDateToJavaUtilDate(java.time.LocalDate.now().minusDays(7));
        }
    }

    private List<FullDecisionScheduleDTO> toFullDecisionScheduleDTOFormPropertyDto(List<PropertyAuditDto> properties, Language language) {
        List<FullDecisionScheduleDTO> fullDecisionScheduleDTOS = new ArrayList<>();
        String scheduled = getFormattedString("scheduled", language);
        String cancelled = getFormattedString("cancelled", language);
        String fullDecisionsCompleted = getFormattedString("fullDecisionsCompleted", language);
        String decisions = getFormattedString("decisions.all",language);
        String allOutbound = getFormattedString("all.outbound",language);
        String TECH_OPS = "Tech Ops";

        properties.forEach(propertyAuditDto -> {
            FullDecisionScheduleDTO fullDecisionScheduleDTO = new FullDecisionScheduleDTO();
            fullDecisionScheduleDTO.setPropertyName(propertyAuditDto.getPropertyCode());
            fullDecisionScheduleDTO.setSellingSystemName(allOutbound);
            fullDecisionScheduleDTO.setDecisions(decisions);
            fullDecisionScheduleDTO.setRequestedOn(propertyAuditDto.getModifiedDate().toLocalDate().toString());
            fullDecisionScheduleDTO.setRequestedBy(propertyAuditDto.getModifiedBy());
            fullDecisionScheduleDTO.setIsCancelScheduled(false);
            if (propertyAuditDto.getForceFullDecisions()) {
                fullDecisionScheduleDTO.setStatus(scheduled);
            } else if ((propertyAuditDto.getModifiedBy().equals(PropertyAuditDto.DUMMY_USER_DISPLAY_NAME) ||
                    propertyAuditDto.getModifiedBy().trim().equalsIgnoreCase(TECH_OPS))) {
                fullDecisionScheduleDTO.setStatus(fullDecisionsCompleted);
            } else {
                fullDecisionScheduleDTO.setStatus(cancelled);
            }
            fullDecisionScheduleDTOS.add(fullDecisionScheduleDTO);
        });

        return fullDecisionScheduleDTOS;
    }


    private void updateOutboundNameWithPropertyCode(List<ForceFullDecisions> outboundFullDecisionsForAudit,Language language) {
        outboundFullDecisionsForAudit.forEach(forceFullDecisions -> {
            String outboundName = forceFullDecisions.getOutboundName();
            Optional<DecisionDestination> decisionDestination = DecisionDestination.getOptionalFromCode(outboundName);
            decisionDestination.ifPresent(system ->
                    forceFullDecisions.setOutboundName(getOutBoundDisplayName(system,language).toUpperCase()));
        });
    }

    public String getOutBoundDisplayName(DecisionDestination system, Language language) {
        String dailyBARRateCode = "";
        String propertyCode = getPropertyCode(system.getConfigParamNode());
        String dailyBarUploadType = getDailyBarUploadType(system.getConfigParamNode());
        if (StringUtils.isNotBlank(dailyBarUploadType) && !Constants.NONE.equalsIgnoreCase(dailyBarUploadType)) {
            dailyBARRateCode = getDailyBARRateCode(system.getConfigParamNode());
        }

        String sellingSystemName = (system.isSynxis() || system.isIHotelier())
                && StringUtils.isNotBlank(dailyBARRateCode) ? ResourceUtil.getText(system.getLocalizationKey(),language).concat(" - " + dailyBARRateCode) : ResourceUtil.getText(system.getLocalizationKey(), language);
        return propertyCode==null ? sellingSystemName : sellingSystemName.replace("{0}", propertyCode);
    }
    private Map<Integer, String> getUserNameMapByClientIdAndInternalUsers() {
        List<GlobalUser> globalUsers = userService.listDatabaseUsersForClient(PacmanWorkContextHelper.getClientId());
        globalUsers.addAll(userService.getAllInternalUsersForScheduledReports());
        return globalUsers.stream().distinct().collect(Collectors.toMap(GlobalUser::getId, GlobalUser::getFullName));
    }

    private boolean isForceFullDecisionsSetAtOutbounds(ForceFullDecisions forceFullDecisions) {
        if (Objects.nonNull(forceFullDecisions.getREVTYPE())) {
            return forceFullDecisions.getREVTYPE().intValue() == PropertyAudit.ADDED_ACTION;
        }
        return Integer.valueOf(ForceFullDecisionsUpdateReasons.FFD_SET.getReasonID()).equals(forceFullDecisions.getIsForceFullDecision());
    }

    public Boolean cancelFullDecision(String outboundName) {
        if (showFullDecisionsAllOutboundNames()) {
            if (propertyService.isForceFullDecisions(PacmanWorkContextHelper.getPropertyId())) {
                cancelFullDecisionsAtOutbounds(outboundName);
                setForceFullDecisions(PacmanWorkContextHelper.getPropertyId(), false);
            } else {
                cancelFullDecisionsAtOutbounds(outboundName);
            }
        } else {
            if (propertyService.isForceFullDecisions(PacmanWorkContextHelper.getPropertyId())) {
                setForceFullDecisions(PacmanWorkContextHelper.getPropertyId(), false);
            } else {
                cancelFullDecisionsAtOutbounds(outboundName);
            }
        }
        return true;
    }

    private void cancelFullDecisionsAtOutbounds(String outboundName) {
        forceFullDecisionsService.deleteOutBoundsEntryByClientIdAndPropertyIdAndOutBoundName(PacmanWorkContextHelper.getClientId(), PacmanWorkContextHelper.getPropertyId(), outboundName,
                ForceFullDecisionsUpdateReasons.FFD_CANCELLED_FROM_UI.getReasonID());
    }


    private String getFormattedString(String key, Language language) {
        return ResourceUtil.getText(key, language);
    }

    private Language getLanguage() {
        LDAPUser ldapUser = getLDAPUser();
        return Language.getLanguageFromString(ldapUser.getLanguage());
    }

    private List<ForceFullDecisions> getOutboundLevelForceFullDecisionsForAudit(Date startDate, Date endDate) {
        return forceFullDecisionsService.getOutboundAuditHistory(startDate, endDate);
    }

    private List<PropertyAuditDto> getPropertyLevelForceFullDecisionsForAudit(Date startDate, Date endDateUtil) {
        PropertyAuditCriteria criteria = new PropertyAuditCriteria();
        criteria.setClientId(PacmanWorkContextHelper.getClientId());
        criteria.setDateRangeStart(startDate);
        criteria.setDateRangeEnd(endDateUtil);
        List<PropertyAuditDto> allChanges = propertyService.getPropertyAuditInfo(criteria);
        List<PropertyAuditDto> results = getFullDecisionChangesMultipleProperties(allChanges, false);
        if (startDate != null) { // since we went back further than start date, need to filter out any changes prior to start date
            List<PropertyAuditDto> filteredResults = new ArrayList<PropertyAuditDto>();
            for (PropertyAuditDto change : results) {
                if (change.getModifiedDate().toLocalDate().isAfter(DateUtil.convertJavaUtilDateToLocalDate(startDate)) || change.getModifiedDate().toLocalDate().isEqual(DateUtil.convertJavaUtilDateToLocalDate(startDate))) {
                    filteredResults.add(change);
                }
            }
            return filteredResults;
        } else {
            return results;
        }
    }

    private List<PropertyAuditDto> getFullDecisionChangesMultipleProperties(List<PropertyAuditDto> allChanges, boolean latestOnly) {
        List<PropertyAuditDto> results = new ArrayList<>();
        Collection<List<PropertyAuditDto>> propertyLists = splitByProperty(allChanges);
        for (List<PropertyAuditDto> propertyList : propertyLists) {
            List<PropertyAuditDto> propertyChanges = getFullDecisionChangesSingleProperty(propertyList);
            if (latestOnly && !propertyChanges.isEmpty()) {
                results.add(propertyChanges.get(propertyChanges.size() - 1));
            } else {
                results.addAll(propertyChanges);
            }
        }
        if (CollectionUtils.isNotEmpty(results)) {
            results.sort(Comparator.comparing(PropertyAuditDto::getModifiedDate));
        }
        return results;
    }

    private Collection<List<PropertyAuditDto>> splitByProperty(List<PropertyAuditDto> allChanges) {
        Map<Integer, List<PropertyAuditDto>> map = new HashMap<>();
        for (PropertyAuditDto change : allChanges) {
            Integer propertyId = change.getPropertyId();
            List<PropertyAuditDto> propertyChanges = map.get(propertyId);
            if (CollectionUtils.isEmpty(propertyChanges)) {
                propertyChanges = new ArrayList<>();
                map.put(propertyId, propertyChanges);
            }
            propertyChanges.add(change);
        }
        return map.values();
    }

    private List<PropertyAuditDto> getFullDecisionChangesSingleProperty(List<PropertyAuditDto> allChanges) {
        List<PropertyAuditDto> results = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(allChanges)) {
            allChanges.sort(Comparator.comparing(PropertyAuditDto::getModifiedDate));
            Boolean previousForceFullDecisions = null;
            for (PropertyAuditDto change : allChanges) {
                if (results.isEmpty() && Boolean.TRUE.equals(change.getForceFullDecisions())) {
                    results.add(change);
                } else if (Boolean.TRUE.equals(change.getForceFullDecisions()) && !Boolean.TRUE.equals(previousForceFullDecisions)) {
                    results.add(change);
                } else if (Boolean.FALSE.equals(change.getForceFullDecisions()) && Boolean.TRUE.equals(previousForceFullDecisions)) {
                    results.add(change);
                }
                previousForceFullDecisions = change.getForceFullDecisions();
            }
        }
        return results;
    }

    private Date getFormattedDate(String date){
        try {
            return DateUtils.parseDate(date, DateUtil.DEFAULT_DATE_FORMAT);
        } catch (java.text.ParseException e) {
            return null;
        }
    }

    public boolean isForceFullDecisionsAtOutboundsEnabled() {
        final Stage propertyStage = propertyService.getPropertyStage(getPropertyId());
        return isForceFullDecisionsToggleEnabled()
                && pacmanConfigParamsService.getBooleanParameterValue(FORCEFULLDECISIONS_OUTBOUND_ENABLED)
                && Stage.TWO_WAY.equals(propertyStage) && isOutboundSetForProperty();
    }


    private boolean isOutboundSetForProperty() {
        return !getConfiguredExternalSystems().isEmpty();
    }

    public List<DecisionDestination> getConfiguredExternalSystems() {
        List<String> notAllowedExternalSystems = Arrays.asList(DecisionDestination.HILSTAR.getConfigParamNode(), DecisionDestination.PCRS.getConfigParamNode());
        return decisionConfigCoreSettingService.getConfiguredExternalSystems().stream()
                .filter(obj -> !notAllowedExternalSystems.contains(obj.getConfigParamNode())).collect(Collectors.toList());
    }


    private boolean isDecisionUploadInProgress(){
        return propertyStateService.getPropertyState().isDecisionUploadInProgress();
    }

    public boolean isForceFullDecisions() {
        return propertyService.isForceFullDecisions(getPropertyId()) ||
                forceFullDecisionsService.isForceFullDecisionAtAnyOutBounds(PacmanWorkContextHelper.getClientId(),
                        PacmanWorkContextHelper.getPropertyId());
    }

    public boolean isProcessingRunningOrPlannedToRun(){
        String clientCode = PacmanWorkContextHelper.getClientCode();
        String propertyCode = PacmanWorkContextHelper.getPropertyCode();
        boolean bdeWithinNextFewMinutes = dailyProcessingService.isBDEWithinNextFewMinutes(clientCode, propertyCode, 60);
        boolean cdpWithinNextFewMinutes = dailyProcessingService.isCdpWithinNextFewMinutes(clientCode, propertyCode, PacmanWorkContextHelper.getPropertyId(), 60);
        return bdeWithinNextFewMinutes || cdpWithinNextFewMinutes;
    }

    private boolean isImmediateFullDecisionsEnabled() {
        return isForceFullDecisionsEnabled() && immediateFullDecisionsEnabled();
    }

    public boolean isForceFullDecisionsEnabled() {
        String context = APPLICATION_NAME + "." + getClientCode() + "." + getPropertyCode();
        String featureEnabled = pacmanConfigParamsService.getValue(context, FeatureTogglesConfigParamName.ENABLE_FORCE_FULL_DECISIONS.value());
        return "true".equals(featureEnabled) ;
    }


    public boolean isImmediateFullDecisionsEditable() {
        return isImmediateFullDecisionsEnabled() && !isForceFullDecisions() && !isProcessingRunningOrPlannedToRun() && !isDecisionUploadInProgress() && !hasImmediateDecisionsCountReachedDailyLimit();
    }

    public boolean isScheduledTwoWayDateEnabled(){
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_SCHEDULED_TWO_WAY_DATE_ENABLED);
    }

    public boolean hasImmediateDecisionsCountReachedDailyLimit() {
        int immediateFullDecisionUploadMaxCount = getImmediateFullDecisionUploadMaxCount();
        int immediateFullDecisionUploadCurrentCount = getImmediateFullDecisionUploadCurrentCount();
        return (immediateFullDecisionUploadCurrentCount >= immediateFullDecisionUploadMaxCount);
    }

    private Integer getPropertyId() {
        return PacmanWorkContextHelper.getPropertyId();
    }

    public void startImmediateFullDecisions(Set<SellingSystemDTO> sellingSystemDTOS,Boolean immediateDecisionUpload){
        Set<DecisionDestination> forceFullDecisionOutbounds = getDecisionDestinationFromStringList(sellingSystemDTOS);
        List<SellingSystemDTO> currentSellingSystems = getSellingSystems();
        updateImmediateFullDecisionsCount(forceFullDecisionOutbounds, Boolean.TRUE.equals(currentSellingSystems.size() == sellingSystemDTOS.size()));
        scheduleFullDecisions(sellingSystemDTOS,immediateDecisionUpload);
        processImmediateFullDecisions(forceFullDecisionOutbounds);
    }

    public void updateImmediateFullDecisionsCount(Set<DecisionDestination> forceFullDecisionOutbounds, boolean isAllOutboundSelected){
        if (isAllOutboundSelected) {
            updateImmediateFullDecisionsCount(List.of("ALL_OUTBOUNDS"));
        } else if (!forceFullDecisionOutbounds.isEmpty()) {
            updateImmediateDecisionsCount(forceFullDecisionOutbounds);
        } else {
            if (showFullDecisionsAllOutboundNames()) {
                updateImmediateDecisionsCount(new HashSet<>(getConfiguredExternalSystems()));
            } else {
                updateImmediateFullDecisionsCount(List.of("ALL_OUTBOUNDS"));
            }
        }
    }

    public void updateImmediateDecisionsCount(Set<DecisionDestination> forceFullDecisionOutbounds) {
        List<String> outboundNames = new ArrayList<>();
        forceFullDecisionOutbounds.forEach(forceFullDecisionOutbound -> outboundNames.add(forceFullDecisionOutbound.getConfigParamNode()));
        updateImmediateFullDecisionsCount(outboundNames);
    }
    public void processImmediateFullDecisions(Set<DecisionDestination> forceFullDecisionOutbounds) {
        if(forceFullDecisionOutbounds.isEmpty()){
            forceFullDecisionOutbounds.addAll(getConfiguredExternalSystems());
        }
        processOutboundDecisions.startImmediateDecisionDelivery(extractSelectedOutboundNames(forceFullDecisionOutbounds));
    }

    public Set<String> extractSelectedOutboundNames(Set<DecisionDestination> forceFullDecisionOutbounds) {
        List<String> outbounds = forceFullDecisionOutbounds.stream().map(DecisionDestination::name).map(String::toUpperCase).collect(Collectors.toList());
        List<String> decisionOutboundSteps = Arrays.stream(LastKnowGoodDecisionOutboundSteps.values()).map(LastKnowGoodDecisionOutboundSteps::name).map(String::toUpperCase).collect(Collectors.toList());
        List<String> htngPartners = Arrays.stream(HtngPartner.valuesConfiguredOutboundsAware()).map(HtngPartner::getName).map(String::toUpperCase)
                .filter(partner -> !decisionOutboundSteps.contains(partner)).collect(Collectors.toList());

        Set<String> selectedOutboundSteps = outbounds.stream().filter(decisionOutboundSteps::contains)
                .map(LastKnowGoodDecisionOutboundSteps::valueOf).map(LastKnowGoodDecisionOutboundSteps::getSteps)
                .flatMap(List::stream).collect(Collectors.toSet());
        outbounds.stream().filter(htngPartners::contains).findFirst().ifPresent(outbound -> selectedOutboundSteps.addAll(LastKnowGoodDecisionOutboundSteps.HTNG.getSteps()));
        return selectedOutboundSteps;
    }
    public void scheduleFullDecisions(Set<SellingSystemDTO> sellingSystemDTOS, Boolean immediateDecisionUpload){
        LDAPUser ldapUser = getLDAPUser();
        Language lan = Language.getLanguageFromString(ldapUser.getLanguage());
        Set<DecisionDestination> forceFullDecisionOutbounds = getDecisionDestinationFromStringList(sellingSystemDTOS);
        List<SellingSystemDTO> currentSellingSystems = getSellingSystems();
        Map<String, String> decisionTypesGroupedBySystem = mapSellingSystemsToDecisionDeliveryType(sellingSystemDTOS, forceFullDecisionOutbounds, lan);
        if (currentSellingSystems.size() == sellingSystemDTOS.size()) {
            propertyService.setForceFullDecisions(PacmanWorkContextHelper.getPropertyId(), true);
            if (showFullDecisionsAllOutboundNames()) {
                saveFullDecisionsAtOutbounds(forceFullDecisionOutbounds, decisionTypesGroupedBySystem,immediateDecisionUpload);
            }
        } else if (!forceFullDecisionOutbounds.isEmpty()) {
            saveFullDecisionsAtOutbounds(forceFullDecisionOutbounds, decisionTypesGroupedBySystem,immediateDecisionUpload);
        } else {
            if (showFullDecisionsAllOutboundNames()) {
                propertyService.setForceFullDecisions(PacmanWorkContextHelper.getPropertyId(), true);
                saveFullDecisionsAtOutbounds(new HashSet<>(getConfiguredExternalSystems()), new HashMap<>(), immediateDecisionUpload);
            } else {
                forceFullDecisions();
            }
        }
    }

    public void saveFullDecisionsAtOutbounds(Set<DecisionDestination> forceFullDecisionOutbounds, Map<String, String> selectedDecisionTypes, Boolean immediateDecisionUpload) {
        forceFullDecisionsService.saveForceFullDecisions(forceFullDecisionOutbounds, selectedDecisionTypes, immediateDecisionUpload);
    }
    private boolean isScheduleDeliveryByDecisionTypeEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SCHEDULE_DECISION_DELIVERY_BY_DECISION_TYPE_ENABLE);
    }

    public void forceFullDecisions() {
        propertyService.setForceFullDecisions(getPropertyId(), true);
    }

    private Set<DecisionDestination> getDecisionDestinationFromStringList(Set<SellingSystemDTO> sellingSystemDTOS) {
        Set<String> outboundVendorNames = sellingSystemDTOS.stream().map(dto->dto.getDecisionDestination().split("-")[0].trim()).collect(Collectors.toSet());
        return Arrays.stream(DecisionDestination.valuesConfiguredOutboundsAware()).filter(dd-> outboundVendorNames.contains(dd.name())).collect(Collectors.toSet());
    }
    public Map<String, String> mapSellingSystemsToDecisionDeliveryType(Set<SellingSystemDTO> sellingSystemDTOS, Set<DecisionDestination> forceFullDecisionOutbounds, Language language) {
        Set<DecisionDeliveryType> decisionDeliveryTypes = getSelectedDecisionDeliveryType(sellingSystemDTOS, language);
        Map<String, String> decisionTypesGroupedBySystem;
        if (isScheduleDeliveryByDecisionTypeEnabled()) {
            Map<String, String> allDecisionTypesMap = sellingSystemDTOS.stream()
                    .filter(dto -> !dto.getDecisionDestination().isEmpty())
                    .filter(dto -> dto.getDecisionType().equalsIgnoreCase(ALL_DECISION_TYPES))
                    .collect(Collectors.toMap(
                            SellingSystemDTO::getDecisionDestination,
                            dto -> ALL_DECISION_TYPES,
                            (a, b) -> a
                    ));

            Map<String, String> matchedDecisionTypesMap = sellingSystemDTOS.stream()
                    .filter(dto -> !dto.getDecisionDestination().isEmpty())
                    .filter(dto -> !dto.getDecisionType().equalsIgnoreCase(ALL_DECISION_TYPES))
                    .flatMap(dto -> decisionDeliveryTypes.stream()
                            .filter(type -> getFormattedString(type.getLocalizationKey(), language).equals(dto.getDecisionType()))
                            .map(type -> new AbstractMap.SimpleEntry<>(dto.getDecisionDestination(), type.getConfigParamNode()))
                            .findFirst().stream()
                    )
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (existing, newValue) -> existing + "," + newValue
                    ));

            decisionTypesGroupedBySystem = new HashMap<>(matchedDecisionTypesMap);
            allDecisionTypesMap.forEach(decisionTypesGroupedBySystem::putIfAbsent);

        } else {
            decisionTypesGroupedBySystem = sellingSystemDTOS.stream()
                    .collect(Collectors.toMap(
                            SellingSystemDTO::getDecisionDestination,
                            dto -> ALL_DECISION_TYPES,
                            (a, b) -> a
                    ));
        }


        return decisionTypesGroupedBySystem;
    }
    private Set<DecisionDeliveryType> getSelectedDecisionDeliveryType(Set<SellingSystemDTO> sellingSystemDTOS, Language language) {
        List<String> selectedDecisionTypes = sellingSystemDTOS.stream()
                .map(SellingSystemDTO::getDecisionType)
                .collect(Collectors.toList());

        List<com.ideas.tetris.pacman.services.decisiondelivery.DecisionConfiguration> decisionConfigurations =
                decisionDeliveryService.getDecisionConfigurations(PacmanWorkContextHelper.getPropertyId());

        return decisionConfigurations.stream()
                .map(com.ideas.tetris.pacman.services.decisiondelivery.DecisionConfiguration::getDeliveryType)
                .filter(deliveryType -> selectedDecisionTypes.contains(
                        getFormattedString(deliveryType.getLocalizationKey(), language)))
                .collect(Collectors.toSet());
    }

    public boolean shouldSkipDecisionTypeIfNotSelectedInForceFullForImmediateDelivery(String propertyId, String decisionName, String externalSystem) {
        if (!isScheduleDeliveryByDecisionTypeEnabled()) {
            return false;
        }
        Integer clientId = PacmanWorkContextHelper.getClientId();
        ForceFullDecisions forceFullDecisions = forceFullDecisionsService.getForceFullDecisionsByClientIdAndPropertyIdAndOutBoundName(clientId, Integer.valueOf(propertyId), externalSystem);
        return Objects.nonNull(forceFullDecisions) && forceFullDecisions.getImmediateDecisionUpload() && !isDecisionTypeSelected(decisionName, forceFullDecisions);
    }
}