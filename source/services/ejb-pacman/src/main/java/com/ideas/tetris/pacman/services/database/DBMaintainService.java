package com.ideas.tetris.pacman.services.database;

import com.ideas.database.DataSourceWriter;
import com.ideas.database.FlywayDatabaseService;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.datasourceswitching.DataSourceCacheBean;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.concurrent.MultiThreadedExecutor;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.compress.CompressUncompressService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.*;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import thirdparty.org.apache.commons.dbutils.DbUtils;
import thirdparty.org.apache.commons.dbutils.QueryRunner;

import javax.persistence.Query;
import java.io.File;
import java.sql.*;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.ExecutionException;

import static com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.getBackupFolderLinux;
import static com.ideas.tetris.platform.services.compress.CompressionType.ZIP;

@Component
@Transactional
public class DBMaintainService {
    private static final Logger LOGGER = Logger.getLogger(DBMaintainService.class.getName());

    private static final String SQL_DB_EXISTS =
            "IF EXISTS(SELECT name FROM sys.databases WHERE name =N''{0}'') BEGIN USE master {1} database [{0}]";

    private static final String SQL_DB_VALIDATION = "SELECT 1 FROM Property WHERE property_id = ?";
    private static final String TEST_QUALIFIER = "test";
    private static final String SQL_DB_CREATE = "EXECUTE [dbautils].[dbo].[usp_Create_Database] ?";
    private static final String SQL_DB_BACKUP_RESTORE = "EXECUTE [dbo].[usp_backup_and_restore_database] ?,?,?,?";
    private static final String SQL_MASK_DATA = "EXECUTE [dbo].[usp_masking_data] ?,?";
    private static final String SQL_UPDATE_PROPERTY_ID = "EXECUTE [dbo].[usp_update_property_id] ?,?";
    private static final String LDF_EXT = ".ldf";
    private static final String MDF_EXT = ".mdf";
    private static final boolean REBUILD_IN_PROPERTY_UPGRADE = false;
    private static final String PROBLEM_CLEANING_UP_DB_RESOURCES = "Problem cleaning up db resources";
    private static final String JDBC_SQLSERVER = "jdbc:sqlserver://";
    public static final String PROPERTY_ID = "propertyId";
    static final Integer PER_PROPERTY_DB = 4;
    static final Integer TEST_PROPERTY_DB = 5;

    // One test leveraged first two so left package scope. Would prefer mocking and make private. Not sure risk.
    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService globalCrudService;
    @Autowired
    DBInstanceDeterminer dbInstanceDeterminer;
    @Autowired
    PacmanConfigParamsService configParamsService;
    @Autowired
    PropertyService propertyService;
    @Autowired
	private CompressUncompressService compressUncompressService;
    @Autowired
	private DataSourceCacheBean dataSourceCache;

    public static final int CAP_ON_NUMBER_OF_PARALLEL_THREADS = 50;

    public void createDatabase(Integer propertyId, DBLoc dbLoc) {
        dropDatabase(dbLoc); // if there is an old database lying around, delete it
        String dbName = getDatabaseName(propertyId);
        LOGGER.info("Creating new Database for Property " + propertyId + " with name " + dbName);

        // Get a list of all PerProperty Databases for includes / excludes
        List<String> perPropertyDatabaseNames = findAllDBLocsWithDBTypePerProperty();

        // Create Empty Database
        LOGGER.info("Creating New Empty Database for Property " + propertyId + " with name " + dbName);
        createNewDatabase(dbName, dbLoc);

        LOGGER.info("Creating CMT Datasource File at JBoss SOA Node for Property " + propertyId + " and database " + dbName);
        String jndiName = getJndiName(propertyId);
        createCmtDatasourceFile(dbName, dbLoc, jndiName);

        // Execute DBMaintain scripts against new DB
        LOGGER.info("Executing DBMaintain Scripts for Property " + propertyId + " and database " + dbName);
        executeDBScripts(dbLoc, false, perPropertyDatabaseNames);
    }

    public void executeDBScripts(DBLoc dbLoc, boolean rebuild, List<String> perPropertyDatabaseNames) {
        String driver = SystemConfig.getDatabaseDriver();
        String url = JDBC_SQLSERVER + dbLoc.getServerName() + ":" + dbLoc.getPortNumber() + ";DatabaseName=" + dbLoc.getDbName();
        String username = SystemConfig.getDatabaseUsername();
        String password = SystemConfig.getDatabasePassword();
        String schemas = SystemConfig.getFlywaySchemas();
        String locations = SystemConfig.getFlywayLocations();
        boolean baselineOnMigrate = SystemConfig.getFlywayBaselineOnMigrate();
        String baselineVersion = SystemConfig.getFlywayBaselineVersion();

        getFlywayDatabaseService().migrateTenant(driver, url, schemas, username, password, locations, baselineOnMigrate, baselineVersion, rebuild);
    }

    private FlywayDatabaseService getFlywayDatabaseService() {
        return new FlywayDatabaseService();
    }

    public void recreateDatabase(Integer propertyId) {
        DBLoc dbLoc = getDbLocForPropertyId(propertyId);

        // Get a list of all PerProperty Databases for includes / excludes
        List<String> perPropertyDatabaseNames = findAllDBLocsWithDBTypePerProperty();
        executeDBScripts(dbLoc, true, perPropertyDatabaseNames);
    }

    public void dropDatabase(Integer propertyId) {
        LOGGER.info("Removing Database for Property: " + propertyId);
        DBLoc dbLoc = getDbLocForPropertyId(propertyId);

        if (dbLoc == null) {
            return;
        }

        // Delete the datasource file
        try {
            DataSourceWriter dataSourceWriter = getDataSourceWriter();
            LOGGER.info("Deleting CMT-Datasource and Local-TX-Datasource file for property: " + propertyId);
            if (SystemConfig.isCmtDatasourceCreationEnabled()) {
                dataSourceWriter.deleteCmtDatasourceFile(SystemConfig.getCMTJBossDatasourceDir(), getJndiName(propertyId));
            }
        } catch (Exception e) {
            String message = "Failed to delete CMT-Datasource and Local-TX-Datasource file for property " + propertyId;
            LOGGER.error(message);
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, message, e);
        }

        // Drop the database
        LOGGER.info("Dropping Database Property: " + propertyId);
        dropDatabase(dbLoc);

        // Drop the DBLoc record
        LOGGER.info("Deleting DBLoc record for Property: " + propertyId);
        globalCrudService.delete(dbLoc);
        globalCrudService.getEntityManager().flush();
    }

    private void alterDatabaseSetSingleUser(DBLoc dbLoc) {
        Connection connection = null;
        Statement statement = null;
        String databaseName = dbLoc.getDbName();
        TetrisException cleanupException = null;
        try {
            connection = getConnectionBasedOnDBLoc(dbLoc);
            statement = connection.createStatement();
            String sql = MessageFormat.format(SQL_DB_EXISTS, databaseName, "alter") +
                    " set single_user with rollback immediate END";
            statement.execute(sql);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Unable to alter Database: " + databaseName, e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException sqle) {
                    cleanupException = new TetrisException(ErrorCode.DATABASE_EXCEPTION,
                            "Unable to close statement after altering database: " + databaseName, sqle);
                }
            }

            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException sqle) {
                    cleanupException = new TetrisException(ErrorCode.DATABASE_EXCEPTION,
                            "Unable to close connection after altering database: " + databaseName, sqle);
                }
            }
        }

        if (null != cleanupException) {
            LOGGER.error(PROBLEM_CLEANING_UP_DB_RESOURCES, cleanupException);
            throw cleanupException;
        }
    }

    public void createCmtDatasourceFile(String databaseName, DBLoc dbLoc, String jndiName) {
        if (SystemConfig.isCmtDatasourceCreationEnabled()) {
            try {
                getDataSourceWriter().writeOldSchoolJbossDatasource(SystemConfig.getCMTJBossDatasourceDir(), databaseName, jndiName,
                        dbLoc.getServerName(), dbLoc.getPortNumber().toString(),
                        SystemConfig.getCmtDatabaseUsername(), SystemConfig.getCmtDatabasePassword());
            } catch (Exception e) {
                throw new TetrisException(ErrorCode.FILE_CREATION_FAILED, "Unable to Create CMT Datasource for Database: " + databaseName, e);
            }
        } else {
            LOGGER.info("Not creating the CMT Datasource since SystemConfig.isCmtDatasourceCreationEnabled() is not true");
        }
    }

    @SuppressWarnings("squid:S2095")
    public void validateDatabase(Integer propertyId, DBLoc dbLoc) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        try {
            connection = getPropertyConnection(dbLoc);
            statement = connection.prepareStatement(SQL_DB_VALIDATION);
            statement.setInt(1, propertyId);
            resultSet = statement.executeQuery();
            if (!resultSet.next()) {
                throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "New property database is not configured correctly");
            }
        } catch (ClassNotFoundException | SQLException e) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Could not connect to property database", e);
        } finally {
            closeQuietly(resultSet);
            closeQuietly(statement);
            closeQuietly(connection);
        }
    }

    private void closeQuietly(AutoCloseable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception e) {
                LOGGER.warn("Could not close connection", e);
            }
        }
    }

    private void createNewDatabase(String databaseName, DBLoc dbLoc) {
        Connection connection = null;
        PreparedStatement statement = null;
        TetrisException cleanupException = null;
        try {
            connection = getConnection(dbLoc);
            statement = connection.prepareStatement(SQL_DB_CREATE);
            statement.setString(1, databaseName.trim());
            statement.execute();
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Unable to create new database: " +
                    databaseName + "\nScript is : " + SQL_DB_CREATE, e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException sqle) {
                    cleanupException = new TetrisException(ErrorCode.DATABASE_EXCEPTION,
                            "Unable to close statement after Creating New Database: " + databaseName, sqle);
                }
            }

            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException sqle) {
                    cleanupException = new TetrisException(ErrorCode.DATABASE_EXCEPTION,
                            "Unable to close connection after Creating New Database: " + databaseName, sqle);
                }
            }
        }

        if (null != cleanupException) {
            LOGGER.error(PROBLEM_CLEANING_UP_DB_RESOURCES, cleanupException);
            throw cleanupException;
        }
    }

    public void dropDatabase(DBLoc dbLoc) {
        Connection connection = null;
        Statement statement = null;
        String databaseName = dbLoc.getDbName();
        TetrisException cleanupException = null;

        LOGGER.info("Changing database to single user in preparation of dropping it: " + databaseName);
        alterDatabaseSetSingleUser(dbLoc);

        try {
            connection = getConnectionBasedOnDBLoc(dbLoc);
            statement = connection.createStatement();
            String sql = MessageFormat.format(SQL_DB_EXISTS, databaseName, "drop") + " END";
            statement.execute(sql);
            statement.execute(sql);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Unable to Drop Database: " + databaseName, e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException sqle) {
                    cleanupException = new TetrisException(ErrorCode.DATABASE_EXCEPTION,
                            "Unable to close statement after Dropping Database: " + databaseName, sqle);
                }
            }

            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException sqle) {
                    cleanupException = new TetrisException(ErrorCode.DATABASE_EXCEPTION,
                            "Unable to close connection after Dropping Database: " + databaseName, sqle);
                }
            }
        }

        if (null != cleanupException) {
            LOGGER.error(PROBLEM_CLEANING_UP_DB_RESOURCES, cleanupException);
            throw cleanupException;
        }
    }

    public DBLoc createDBLoc(Integer propertyId) {
        String dbName = getDatabaseName(propertyId);
        DBLoc dbLoc = dbInstanceDeterminer.determineInstance();
        dbLoc.setDbName(dbName);
        dbLoc.setStatus(Status.ACTIVE);
        dbLoc.setDbtypeId(DBType.perProperty);
        return globalCrudService.save(dbLoc);
    }

    public DBLoc createDBLoc(Integer propertyId, DBHostRef ref) {
        String dbName = getDatabaseName(propertyId);
        DBLoc dbLoc = dbInstanceDeterminer.determineInstance();
        dbLoc.setDbName(dbName);
        dbLoc.setStatus(Status.ACTIVE);
        dbLoc.setDbtypeId(DBType.perProperty);

        //get servername and jndi names from ref
        dbLoc.setServerName(ref.getServerName());
        dbLoc.setJndiName(ref.getJndiName());
        dbLoc.setJndiNameForReports(ref.getJndiNameForReports());
        dbLoc.setDbHostRef(ref);
        return globalCrudService.save(dbLoc);
    }

    public void attachDatabase(DBLoc dbLoc, String clientCode, String propertyCode, File dataFile, File logFile) {
        try {
            String databaseUrl = JDBC_SQLSERVER + dbLoc.getServerName() + ":" + dbLoc.getPortNumber() + ";databaseName=Master;";
            Connection conMaster = DriverManager.getConnection(databaseUrl, SystemConfig.getDatabaseUsername(), SystemConfig.getDatabasePassword());

            try {
                QueryRunner runner = new QueryRunner(true);
                String sqlCreate = "CREATE DATABASE [" + dbLoc.getDbName() + "] " +
                        "ON (filename='" + dataFile.getAbsolutePath() + "'), " +
                        "(filename='" + logFile.getAbsolutePath() + "') FOR ATTACH";
                runner.update(conMaster, sqlCreate);
            } finally {
                DbUtils.commitAndCloseQuietly(conMaster);
            }

            Integer propertyId = Integer.parseInt(dbLoc.getDbName());
            String jndiName = createJndiName(clientCode, propertyCode);
            createCmtDatasourceFile(dbLoc.getDbName(), dbLoc, jndiName);

            String sqlUpdateProperty = "update Property set DBLoc_ID = :dbLocId where Property_ID = :propertyId";
            Query query = globalCrudService.getEntityManager().createNativeQuery(sqlUpdateProperty);
            query.setParameter("dbLocId", dbLoc.getId());
            query.setParameter(PROPERTY_ID, propertyId);
            query.executeUpdate();

            List<String> perPropertyDatabaseNames = findAllDBLocsWithDBTypePerProperty();
            executeDBScripts(dbLoc, false, perPropertyDatabaseNames);
        } catch (SQLException sqle) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Unable to attach database: " + dataFile, sqle);
        }
    }

    public String backupDatabase(DBLoc dbLoc, File zipFile) {
        try {
            String destination = SystemConfig.getLmsSqlDestination();

            File dataFile = new File(destination, dbLoc.getDbName() + MDF_EXT);
            File logFile = new File(destination, dbLoc.getDbName() + LDF_EXT);

            alterDatabaseSetSingleUser(dbLoc);
            String databaseUrl = JDBC_SQLSERVER + dbLoc.getServerName() + ":" +
                    dbLoc.getPortNumber() + ";databaseName=Master;";
            Connection conMaster = DriverManager.getConnection(databaseUrl,
                    SystemConfig.getDatabaseUsername(), SystemConfig.getDatabasePassword());

            try {
                QueryRunner runner = new QueryRunner(true);
                String sql = "EXEC sp_detach_db ?, 'true';";
                runner.update(conMaster, sql, dbLoc.getDbName());

                compressUncompressService.compress(
                        ZIP,
                        Arrays.asList(logFile.getAbsolutePath(), dataFile.getAbsolutePath()),
                        zipFile.getAbsolutePath());
                zipFile.setReadable(true, false);
                zipFile.setWritable(true, false);

                if (SystemConfig.isLmsSqlLinux()) {
                    destination = SystemConfig.getLmsSqlLinuxDestination();
                    dataFile = new File(destination, dbLoc.getDbName() + MDF_EXT);
                    logFile = new File(destination, dbLoc.getDbName() + LDF_EXT);
                }

                String sqlCreate = "CREATE DATABASE [" + dbLoc.getDbName() + "] " +
                        "ON (filename='" + dataFile.getAbsolutePath() + "'), " +
                        "(filename='" + logFile.getAbsolutePath() + "') FOR ATTACH";
                runner.update(conMaster, sqlCreate);
            } finally {
                DbUtils.commitAndCloseQuietly(conMaster);
            }

            return zipFile.getAbsolutePath();
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error backing up database: " + dbLoc.getDbName(), e);
        }
    }

    public String restoreDatabase(DBLoc dbLoc) {
        try {
            String snapshotLocation = SystemConfig.getLmsSnapshotLocation();
            String destination = SystemConfig.getLmsSqlDestination();
            File zipFile = new File(snapshotLocation, dbLoc.getDbName() + "_sql.zip");
            compressUncompressService.uncompress(ZIP, zipFile.getAbsolutePath(), destination);
            return zipFile.getAbsolutePath();
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error restoring database: " + dbLoc.getDbName(), e);
        }
    }

    public void createProperty(Property property) {
        String sqlCreate = "SET IDENTITY_INSERT [dbo].[Property] ON \n"
                + "INSERT INTO [dbo].[Property](Property_ID,Client_ID,Property_Code,Property_Name,Status_ID,Created_DTTM,DBLoc_ID,SFDC_Account_Number,Stage) "
                + "VALUES(:propertyId,:clientId,:propertyCode,:propertyName,:statusId,:createdDTTM,:dbLocId,:sfdcAccountNo,:stage) \n"
                + "SET IDENTITY_INSERT [dbo].[Property] OFF";
        Query query = globalCrudService.getEntityManager().createNativeQuery(sqlCreate);
        query.setParameter(PROPERTY_ID, property.getId());
        query.setParameter("clientId", property.getClient().getId());
        query.setParameter("propertyCode", property.getCode());
        query.setParameter("propertyName", property.getName());
        query.setParameter("statusId", property.getStatus().getId());
        query.setParameter("createdDTTM", property.getCreateDate());
        query.setParameter("dbLocId", property.getDbLocId());
        query.setParameter("sfdcAccountNo", property.getSfdcAccountNo());
        query.setParameter("stage", property.getStage().toString());

        if (query.executeUpdate() != 1) {
            throw new TetrisException("Property Code-'" + property.getCode() + "' , client - '" + property.getClient().getCode() + "' not inserted.");
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void runPropertyMaintenance(String[] maintenanceSql, String fromProp, DBLoc dbLoc) {
        QueryRunner runner = new QueryRunner(true);
        String toProp = dbLoc.getDbName();

        try (Connection propertyConnection = getPropertyConnection(dbLoc)) {
            propertyConnection.setAutoCommit(true);
            runner.update(propertyConnection, "EXEC sp_msforeachtable \"ALTER TABLE ? NOCHECK CONSTRAINT all\"");

            List<List<String>> chunks = getQueryChunks(maintenanceSql);
            String propertyUpdateStatement = runTenantMaintainanceScripts(fromProp, dbLoc, propertyConnection, toProp, chunks, runner);

            LOGGER.info("Running update for Property Table on DB : " + fromProp);
            runner.update(propertyConnection, propertyUpdateStatement.replace("@fromProp", "'" + fromProp + "'").replace("@toProp", "'" + toProp + "'"));
            runner.update(propertyConnection, "EXEC sp_msforeachtable @command1=\"print '?'\", @command2=\"ALTER TABLE ? WITH CHECK CHECK CONSTRAINT all\"");
            shrinkPropertyLog(propertyConnection, dbLoc);
        } catch (Exception e) {
            LOGGER.error(String.format("Error running maintenance script on property '%s'.%s", toProp, e.getMessage()));
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, String.format("Error running maintenance script on property '%s'.%s", toProp, e));
        }
    }

    public String runTenantMaintainanceScripts(String fromProp, DBLoc dbLoc, Connection propertyConnection, String toProp, List<List<String>> chunks, QueryRunner runner) throws ClassNotFoundException, SQLException {
        String propertyUpdateStatement = "";
        for (int i = 0; i < chunks.size(); i++) {
            List<TenantMaintainanceCallable> tenantMaintainanceCallables = new ArrayList<>();
            for (String sql : chunks.get(i)) {
                if (sql.startsWith("update property set")) {
                    propertyUpdateStatement = sql;
                } else if (!StringUtils.isEmpty(sql) && !sql.startsWith("#")) {
                    Connection connection = getPropertyConnection(dbLoc);
                    propertyConnection.setAutoCommit(true);

                    Object[][] params = new Object[][]{{toProp, fromProp}};
                    String sqlStatement = sql.replace("@fromProp", "?").replace("@toProp", "?");

                    TenantMaintainanceCallable tenantMaintainanceCallable = new TenantMaintainanceCallable(connection, runner, sqlStatement, params);
                    tenantMaintainanceCallables.add(tenantMaintainanceCallable);
                }
            }
            runSQL(tenantMaintainanceCallables);
        }
        return propertyUpdateStatement;
    }

    public List<List<String>> getQueryChunks(String[] maintenanceSql) {
        List<List<String>> chunks = new ArrayList<>();
        List<String> currentChunk = new ArrayList<>();
        for (int i = 0; i < maintenanceSql.length; i++) {
            if (!currentChunk.isEmpty() && i % CAP_ON_NUMBER_OF_PARALLEL_THREADS == 0) {
                chunks.add(currentChunk);
                currentChunk = new ArrayList<>();
            }
            currentChunk.add(maintenanceSql[i]);
        }
        chunks.add(currentChunk);
        return chunks;
    }

    public List runSQL(List<TenantMaintainanceCallable> tenantMaintainanceCallables) {
        String threadNamePrefix = tenantMaintainanceCallables.get(0).getClass().getSimpleName();
        MultiThreadedExecutor executor = new MultiThreadedExecutor(threadNamePrefix, tenantMaintainanceCallables.size());
        try {
            return executor.submit(tenantMaintainanceCallables);
        } catch (InterruptedException | ExecutionException e) {
            LOGGER.error("Exception in updating property IDs from tenant DB");
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, String.format("Error running maintenance script! %s", e));
        }
    }

    public void shrinkPropertyLog(Connection propertyConnection, DBLoc dbLoc) {
        QueryRunner runner = new QueryRunner(true);
        LOGGER.info(String.format("Started shrinking log for property %s.", dbLoc.getDbName()));
        try {
            if (null == propertyConnection) {
                propertyConnection = getPropertyConnection(dbLoc);
                propertyConnection.setAutoCommit(true);
            }
            Integer fileId = runner.query(propertyConnection,
                    "select FILE_ID from sys.database_files where type_desc = 'LOG'",
                    rs -> {
                        if (rs.next()) {
                            return rs.getInt(1);
                        }
                        return null;
                    });

            if (fileId != null) {
                runner.query(propertyConnection, "DBCC SHRINKFILE (?)", rs -> null, fileId);
            }
            LOGGER.info(String.format("Completed shrinking log for property %s.", dbLoc.getDbName()));
        } catch (ClassNotFoundException | SQLException e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error shrinking logs for '" + dbLoc.getDbName() + "'.", e);
        } finally {
            closeConnection(propertyConnection, dbLoc);
        }
    }

    private void closeConnection(Connection propertyConnection, DBLoc dbLoc) {
        try {
            if (null != propertyConnection && !propertyConnection.isClosed()) {
                propertyConnection.close();
            }
        } catch (SQLException sqle) {
            LOGGER.error(String.format("Error shrinking Log for property '%s'.%s", dbLoc.getDbName(), sqle));
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, String.format("Error shrinking Log for property '%s'.%s", dbLoc.getDbName(), sqle));
        }
    }

    private String getDatabaseName(Integer propertyId) {
        return StringUtils.leftPad(propertyId.toString(), 6, "0");
    }

    public String getJndiName(Integer propertyId) {
        Property property = propertyService.getPropertyById(propertyId);
        if (property != null) {
            String propertyCode = property.getCode();
            String clientCode = property.getClient().getCode();
            return createJndiName(clientCode, propertyCode);
        }
        return propertyId.toString();
    }

    private String createJndiName(String clientCode, String propertyCode) {
        return String.format("%s-%s", clientCode, propertyCode);
    }

    public String getExcludeQualifiers(List<String> propertyDatabaseNames, String currentProperty) {
        List<String> properties = new ArrayList<>();
        properties.add(TEST_QUALIFIER);
        for (String propertyDatabaseName : propertyDatabaseNames) {
            if (!propertyDatabaseName.equalsIgnoreCase(currentProperty)) {
                properties.add(propertyDatabaseName);
            }
        }
        String propertyQualifiers = StringUtils.join(properties, ",");
        return SystemConfig.getDatabaseBaseExcludeQualifiers().isEmpty() ? propertyQualifiers : SystemConfig.getDatabaseBaseExcludeQualifiers() + "," + propertyQualifiers;
    }

    private String getQualifiers(List<String> propertyDatabaseNames) {
        List<String> properties = new ArrayList<>(propertyDatabaseNames);
        return SystemConfig.getDatabaseBaseQualifiers() + "," + StringUtils.join(properties, ",");
    }

    private List<String> findAllDBLocsWithDBTypePerProperty() {
        // TODO hard code to "PerProperty" and "Test Property" to prevent db contention
        return globalCrudService.findByNamedQuery(DBLoc.BY_DBTYPE_NAME, QueryParameter.with("dbTypeIds",
                Arrays.asList(DBType.perProperty, DBType.testProperty)).parameters());
    }

    public Properties buildDatabasePropertiesForProperty(DBLoc dbLoc) {
        Properties props = new Properties();
        props.setProperty("database.driverClassName", SystemConfig.getDatabaseDriver());
        props.setProperty("database.schemaNames", SystemConfig.getDatabaseSchema());
        props.setProperty("database.dialect", SystemConfig.getDatabaseDialect());
        props.setProperty("database.userName", SystemConfig.getDatabaseUsername());
        props.setProperty("database.password", SystemConfig.getDatabasePassword());
        props.setProperty("dbMaintainer.disableConstraints", "false");
        props.setProperty("database.storedIndentifierCase." + SystemConfig.getDatabaseDialect(), "auto");
        props.setProperty("database.identifierQuoteString." + SystemConfig.getDatabaseDialect(), "auto");
        props.setProperty("org.dbmaintain.database.Database.implClassName", "org.dbmaintain.database.impl.MsSqlDatabase");
        props.setProperty("dbMaintainer.script.encoding", "unicode");
        props.setProperty("dbMaintainer.autoCreateDbMaintainScriptsTable", "true");
        props.setProperty("dbMaintainer.cleanDb", "false");
        props.setProperty("dbMaintainer.script.locations", SystemConfig.getDatabaseScriptsJar());
        String databaseUrl = JDBC_SQLSERVER + dbLoc.getServerName() + ":" + dbLoc.getPortNumber() + ";DatabaseName=" + dbLoc.getDbName();
        props.setProperty("database.url", databaseUrl);
        return props;
    }

    public DBLoc getDbLocForPropertyId(Integer propertyId) {
        String sql = "SELECT DBLoc_ID FROM Property WHERE Property_ID = :propertyId";
        Integer dbLocId = globalCrudService.findByNativeQuerySingleResult(sql, QueryParameter.with(PROPERTY_ID, propertyId).parameters(),
                row -> (Integer) row[0]);

        if (dbLocId != null) {
            return globalCrudService.find(DBLoc.class, dbLocId);
        }

        return null;
    }

    // The following are package scope for testing purposes - allowed to ditch stateful variables
    public Connection getConnectionBasedOnDBLoc(DBLoc dbLoc) throws ClassNotFoundException, SQLException {
        LOGGER.warn("Managing own database connection outside of connection pool using dbloc in global db");
        Class.forName(SystemConfig.getDatabaseDriver());
        String databaseUrl = JDBC_SQLSERVER + dbLoc.getServerName() + ":" + dbLoc.getPortNumber();
        return DriverManager.getConnection(databaseUrl, SystemConfig.getDatabaseUsername(), SystemConfig.getDatabasePassword());
    }

    public Connection getConnection(DBLoc dbLoc) throws ClassNotFoundException, SQLException {
        LOGGER.warn("Managing own database connection outside of connection pool using timezone");
        Class.forName(SystemConfig.getDatabaseDriver());
        String databaseUrl = JDBC_SQLSERVER + dbLoc.getServerName() + ":" + dbLoc.getPortNumber();
        return DriverManager.getConnection(databaseUrl, SystemConfig.getDatabaseUsername(), SystemConfig.getDatabasePassword());
    }

    private Connection getPropertyConnection(DBLoc dbLoc) throws ClassNotFoundException, SQLException {
        LOGGER.warn("Managing own database connection outside of connection pool using timezone");
        Class.forName(SystemConfig.getDatabaseDriver());
        String databaseUrl = JDBC_SQLSERVER + dbLoc.getServerName() + ":" + dbLoc.getPortNumber() + ";databaseName=" + dbLoc.getDbName();
        return DriverManager.getConnection(databaseUrl, SystemConfig.getDatabaseUsername(), SystemConfig.getDatabasePassword());
    }

    public DataSourceWriter getDataSourceWriter() {
        return new DataSourceWriter();
    }

    public DBLoc moveDatabase(Integer propertyId, DBLoc propertyDbLoc, String destinationServer) {

        DBLoc referenceDbLoc = globalCrudService.findByNamedQuerySingleResult(DBLoc.BY_SERVERNAME,
                QueryParameter.with("serverName", destinationServer).parameters());
        if (referenceDbLoc == null) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "No reference DBLoc found for server " + destinationServer);
        }

        propertyDbLoc.setServerName(destinationServer);
        propertyDbLoc.setServerInst(referenceDbLoc.getServerInst());
        propertyDbLoc.setJndiName(referenceDbLoc.getJndiName());
        propertyDbLoc.setJndiNameForReports(referenceDbLoc.getJndiNameForReports());
        propertyDbLoc.setPortNumber(referenceDbLoc.getPortNumber());

        validateDatabase(propertyId, propertyDbLoc);

        DBLoc updatedDbLoc = globalCrudService.save(propertyDbLoc);
        dataSourceCache.refresh(propertyId);

        return updatedDbLoc;
    }

    public void backupAndRestoreProperty(String refPropertyId, String trainingPropertyId) {
        Connection connection = null;
        PreparedStatement statement = null;
        try {
            connection = getPropertyConnection(getDbLocForPropertyId(Integer.parseInt(refPropertyId)));
            statement = connection.prepareStatement(SQL_DB_BACKUP_RESTORE);
            statement.setString(1, refPropertyId.trim());
            statement.setString(2, trainingPropertyId.trim());
            statement.setString(3, getBackupFolderLinux()+"/"+refPropertyId+".bak");
            statement.setString(4, getBackupFolderLinux());
            statement.execute();
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Unable to backup and restore new database: " +
                    refPropertyId + "\nScript is : " + SQL_DB_BACKUP_RESTORE, e);
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException sqle) {
                    throw new TetrisException(ErrorCode.DATABASE_EXCEPTION,
                            "Unable to close connection after Creating New Database: " + trainingPropertyId, sqle);
                }
            }
        }
    }

    public void maskTenantData(String strPropertyId) {
        Integer propertyId= Integer.parseInt(strPropertyId);
        String sql = "SELECT property_code FROM Property WHERE Property_ID = :propertyId";
        String propertyCode = globalCrudService.findByNativeQuerySingleResult(sql, QueryParameter.with(PROPERTY_ID, propertyId).parameters(),
                row -> (String) row[0]);
        sql = "SELECT property_name FROM Property WHERE Property_ID = :propertyId";
        String propertyName = globalCrudService.findByNativeQuerySingleResult(sql, QueryParameter.with(PROPERTY_ID, propertyId).parameters(),
                row -> (String) row[0]);

        Connection connection = null;
        PreparedStatement statement = null;
        try {
            connection = getPropertyConnection(getDbLocForPropertyId(propertyId));
            statement = connection.prepareStatement(SQL_MASK_DATA);
            statement.setString(1, propertyCode);
            statement.setString(2, propertyName);
            statement.execute();
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Unable to mask data on database: " +
                    strPropertyId + "\nScript is : " + SQL_MASK_DATA, e);
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException sqle) {
                    throw new TetrisException(ErrorCode.DATABASE_EXCEPTION,
                            "Unable to close connection after Masking data on database : " + strPropertyId, sqle);
                }
            }
        }
    }

    public void updateSqlTablesPropertyId(String refPropertyId, String trainingPropertyId) {
        {
            Connection connection = null;
            PreparedStatement statement = null;
            try {
                connection = getPropertyConnection(getDbLocForPropertyId(Integer.parseInt(trainingPropertyId)));
                statement = connection.prepareStatement(SQL_UPDATE_PROPERTY_ID);
                statement.setInt(1, Integer.parseInt(refPropertyId));
                statement.setInt(2, Integer.parseInt(trainingPropertyId));
                statement.execute();
            } catch (Exception e) {
                throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Unable to update property id in database: " +
                        refPropertyId + "\nScript is : " + SQL_UPDATE_PROPERTY_ID, e);
            } finally {
                if (connection != null) {
                    try {
                        connection.close();
                    } catch (SQLException sqle) {
                        throw new TetrisException(ErrorCode.DATABASE_EXCEPTION,
                                "Unable to close connection after updating property id: " + trainingPropertyId, sqle);
                    }
                }
            }
        }
    }
}
