package com.ideas.tetris.pacman.services.license.feature;

import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.license.LicenseService;
import com.ideas.tetris.platform.common.license.entities.LicensePackage;
import com.ideas.tetris.platform.common.license.migration.actions.LicenseFeatureUpgradable;
import com.ideas.tetris.platform.common.license.util.LicenseFeatureConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.*;

import static com.ideas.tetris.platform.common.job.JobParameterKey.DELETE_LINKED_PRODUCTS;

@Component
public class AgileRatesFeature extends LicenseFeatureUpgradable {

    @Autowired
    LicenseService licenseService;
    @Autowired
    AgileRatesConfigurationService agileRatesConfigurationService;
    private static final Logger logger = LoggerFactory.getLogger(AgileRatesFeature.class);

    @Override
    public String getFeatureCode() {
        return LicenseFeatureConstants.AGILE_RATES;
    }

    @Override
    protected boolean isCleanUpRequired(LicensePackage currentLicensePackage, LicensePackage newLicensePackage, Map<String, Object> featuresInputMapToDowngrade) {
        if (featuresInputMapToDowngrade != null) {
            String value = (String) featuresInputMapToDowngrade.get(JobParameterKey.DELETE_LINKED_PRODUCTS);
            if (Boolean.parseBoolean(value)) {
                return true;
            }
        }
        return super.isCleanUpRequired(currentLicensePackage, newLicensePackage, featuresInputMapToDowngrade);
    }

    @Override
    protected void cleanUp(int propertyId, LicensePackage currentLicensePackage, LicensePackage newLicensePackage, Map<String, Object> migrationParameterMap) {
        logger.info("Cleaning up Agile Rates features...");
        String value = (String) migrationParameterMap.get(JobParameterKey.DELETE_LINKED_PRODUCTS);
        boolean deleteAllLinkedProducts = Boolean.parseBoolean(value);
        int numberOfProductsToRetain = licenseService.getLicenseFeatureValue(LicenseFeatureConstants.MAX_ACTIVE_AGILE_RATES, newLicensePackage);
        agileRatesConfigurationService.cleanAgileRatesFeatures(numberOfProductsToRetain, deleteAllLinkedProducts, currentLicensePackage.isAddOn());
    }
}