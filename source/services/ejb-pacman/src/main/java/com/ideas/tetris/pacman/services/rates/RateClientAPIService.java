package com.ideas.tetris.pacman.services.rates;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.api.client.invoker.ApiClient;
import com.ideas.api.client.rate.RateApi;
import com.ideas.api.client.rate.RateV2Api;
import com.ideas.api.client.rate.model.RatePlanDto;
import com.ideas.api.client.rate.model.ResourceOfQualifiedRatePlan;
import com.ideas.api.client.rate.model.SlicedResourcesOfResourceOfQualifiedRatePlan;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.currency.CurrencyService;
import com.ideas.tetris.pacman.services.pmsinbound.PmsInboundApiCompatabilityMapper;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.rest.mapper.PlatformNGIRestClient;
import org.apache.log4j.Logger;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;

import javax.inject.Inject;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class RateClientAPIService {
    private static final Logger LOGGER = Logger.getLogger(RateClientAPIService.class);

    @Autowired
	private PlatformNGIRestClient platformNgiRestClient;
    @Autowired
	private CurrencyService currencyService;
    @Autowired
    private PmsInboundApiCompatabilityMapper pmsInboundApiCompatabilityMapper;


    public void updateQualified(String clientCode, String propertyCode, String name, RatePlanDto dto) {
        try {
            if (SystemConfig.isPmsInboundV2()) {
                getApiV2Instance(clientCode, propertyCode).updateQualified(clientCode, propertyCode, name, dto);
            } else {
                getApiInstance(clientCode, propertyCode).updateQualified(clientCode, propertyCode, name, pmsInboundApiCompatabilityMapper.toV1(dto));
            }
        } catch (HttpClientErrorException e) {
            if (HttpStatus.NOT_FOUND.equals(e.getStatusCode())) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("No qualified rate code was found for updating status", e);
                }
            } else {
                throw e;
            }
        }
    }

    public @Valid List<ResourceOfQualifiedRatePlan> getRates(String clientCode, String propertyCode, List<String> rateCodes) {
        String yieldCurrency = currencyService.getYieldCurrency();
        boolean applyYieldCurrency = currencyService.applyYieldCurrency() && yieldCurrency != null;
        var response =
                getRateProtectQualifiedRatePlans(clientCode, propertyCode, rateCodes,
                        applyYieldCurrency ? yieldCurrency : null);
        if (response.getStatusCode().is2xxSuccessful()) {
            SlicedResourcesOfResourceOfQualifiedRatePlan body = response.getBody();
            return body.getContent();
        }
        LOGGER.error("Error occured while retrieving rates from inbound");
        return new ArrayList<>();
    }

    public ResponseEntity<SlicedResourcesOfResourceOfQualifiedRatePlan> getRateProtectQualifiedRatePlans(String clientCode, String propertyCode, List<String> rateCodes, String toCurrencyCode) {
        int maxPageSize = SystemConfig.getMaxRateCodeRetrievalPageSize();
        if (rateCodes.size() > maxPageSize) {
            throw new TetrisException("Rate codes exceed max size, shorten the rate code or increase page size");
        }
        RateV2Api apiV2Instance = getApiV2Instance(clientCode, propertyCode);
        ApiClient apiClient = apiV2Instance.getApiClient();
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("clientCode", clientCode);
        uriVariables.put("propertyCode", propertyCode);
        String path = apiClient.expandPath("/api/v2/rates/qualified/rateprotect/{clientCode}/{propertyCode}", uriVariables);
        MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
        queryParams.addAll("rateCodes", new ArrayList<>(rateCodes));
        if (toCurrencyCode != null) {
            queryParams.add("toCurrencyCode", toCurrencyCode);
        }
        queryParams.add("page", "0");
        queryParams.add("size", String.valueOf(maxPageSize));

        HttpHeaders headerParams = new HttpHeaders();
        MultiValueMap<String, String> cookieParams = new LinkedMultiValueMap();
        MultiValueMap<String, Object> formParams = new LinkedMultiValueMap();
        String[] localVarAccepts = new String[]{"application/json"};
        List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        String[] contentTypes = new String[0];
        MediaType contentType = apiClient.selectHeaderContentType(contentTypes);
        String[] authNames = new String[0];
        ParameterizedTypeReference<SlicedResourcesOfResourceOfQualifiedRatePlan> returnType = new ParameterizedTypeReference<>() {
        };
        return apiClient.invokeAPI(path, HttpMethod.GET, queryParams, null, headerParams, cookieParams, formParams, localVarAccept, contentType, authNames, returnType);
    }

    @VisibleForTesting
	public
    RateApi getApiInstance(String clientCode, String propertyCode) {
        return new RateApi(platformNgiRestClient.getClientAPI(platformNgiRestClient.getPMSInboundNGIBaseUrl(clientCode, propertyCode)));
    }

    @VisibleForTesting
	public
    RateV2Api getApiV2Instance(String clientCode, String propertyCode) {
        return new RateV2Api(platformNgiRestClient.getClientV2API(platformNgiRestClient.getPMSInboundNGIBaseUrl(clientCode, propertyCode)));
    }
}
