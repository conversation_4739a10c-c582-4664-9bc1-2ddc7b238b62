package com.ideas.tetris.pacman.services.datafeed.dto.grouppricing;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ideas.tetris.platform.common.rest.unmarshaller.DateSerializer;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by idnekp on 3/10/2016.
 */
public class AncillaryConfiguration {

    private String revenueStreamName;
    private BigDecimal profitPercentage;
    private String ancillaryCategory;
    @JsonSerialize(using = DateSerializer.class)
    private Date startDate;
    @JsonSerialize(using = DateSerializer.class)
    private Date endDate;
    private String marketSegmentCode;
    private BigDecimal revenuePerRoomNight;


    public String getRevenueStreamName() {
        return revenueStreamName;
    }

    public void setRevenueStreamName(String revenueStreamName) {
        this.revenueStreamName = revenueStreamName;
    }

    public BigDecimal getProfitPercentage() {
        return profitPercentage;
    }

    public void setProfitPercentage(BigDecimal profitPercentage) {
        this.profitPercentage = profitPercentage;
    }

    public String getAncillaryCategory() {
        return ancillaryCategory;
    }

    public void setAncillaryCategory(String ancillaryCategory) {
        this.ancillaryCategory = ancillaryCategory;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getMarketSegmentCode() {
        return marketSegmentCode;
    }

    public void setMarketSegmentCode(String marketSegmentCode) {
        this.marketSegmentCode = marketSegmentCode;
    }

    public BigDecimal getRevenuePerRoomNight() {
        return revenuePerRoomNight;
    }

    public void setRevenuePerRoomNight(BigDecimal revenuePerRoomNight) {
        this.revenuePerRoomNight = revenuePerRoomNight;
    }
}
