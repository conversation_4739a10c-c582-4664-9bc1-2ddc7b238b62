package com.ideas.tetris.pacman.services.budget.currency;


import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.services.budget.dto.CurrencyExchangeDTO;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.sas.log.SasDbQueryResult;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import static com.ideas.tetris.platform.common.errorhandling.ErrorCode.UNEXPECTED_ERROR;
import static java.lang.String.format;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class HiltonCurrencyExchangeService extends AbstractCurrencyExchangeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HiltonCurrencyExchangeService.class);

    public static final String RATCHET_YIELD_CURRENCY_CODE = "pacman.integration.ratchet.yieldCurrencyCode";

    @Autowired
	private SasDbToolService sasDbToolService;

    @Autowired
	private PropertyService propertyService;

    @Override
    protected List<CurrencyExchangeDTO> getCurrencyExchangeRateForDateRange(String clientCode, String propertyCode, String revPlanCurrencyCode, String rmsCurrencyCode, LocalDate startDate, LocalDate endDate) {
        LOGGER.info("RevPlan currency conversion for {} external system", getExternalSystemType());

        if (isHiltonStreamingPopulationEnabled()) {
            LOGGER.info("RevPlan currency conversion: {} -> {}", revPlanCurrencyCode, rmsCurrencyCode);
            LOGGER.info("Fetching the currency conversion factor from NGI when streaming enabled");
            return genericCurrencyExchangeService.getCurrencyExchangeRatesForDateRange(clientCode, propertyCode, revPlanCurrencyCode, rmsCurrencyCode, startDate, endDate);
        }

        final String ratchetYieldCurrencyCode = getRatchetYieldCurrencyCode();

        final CurrencyExchangeDTO currencyExchangeDTO = fetchCurrencyConversionFactorFromRatchet(clientCode, propertyCode, revPlanCurrencyCode, ratchetYieldCurrencyCode, startDate);

        return List.of(currencyExchangeDTO);
    }

    private boolean isHiltonStreamingPopulationEnabled() {
        return configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_POPULATION_ENABLED);
    }

    private String getRatchetYieldCurrencyCode() {
        return configService.getParameterValue(RATCHET_YIELD_CURRENCY_CODE);
    }

    private String getExternalSystemType() {
        return configService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM);
    }

    private CurrencyExchangeDTO fetchCurrencyConversionFactorFromRatchet(String clientCode, String propertyCode, String revPlanCurrencyCode, String ratchetYieldCurrency, LocalDate startDate) {
        LOGGER.info("RevPlan currency conversion: {} -> {}", revPlanCurrencyCode, ratchetYieldCurrency);
        LOGGER.info("Fetching the currency conversion factor from Ratchet.currex dataset");
        final Integer propertyId = propertyService.getPropertyId(clientCode, propertyCode);
        SasDbQueryResult currexEffectiveConversionFactor = sasDbToolService.executeQuery(clientCode, propertyId, propertyCode, format("select effectiveConversionFactor from RATCHET.currex where currencyId ='%s'", revPlanCurrencyCode));
        if (currexEffectiveConversionFactor.isEmpty()) {
            throw new TetrisException(UNEXPECTED_ERROR, "Unable to find exchange rate from Ratchet.currex dataset");
        }
        final BigDecimal exchangeRate = BigDecimalUtil.valueOf(currexEffectiveConversionFactor.getData().get(0).get(0), false, true);
        return getCurrencyExchangeDTO(clientCode, propertyCode, revPlanCurrencyCode, ratchetYieldCurrency, startDate, exchangeRate);
    }


    private CurrencyExchangeDTO getCurrencyExchangeDTO(String clientCode, String propertyCode, String fromCurrency, String toCurrency, LocalDate startDate, BigDecimal exchangeRate) {
        return CurrencyExchangeDTO.builder()
                .clientCode(clientCode)
                .propertyCode(propertyCode)
                .fromCurrency(fromCurrency)
                .toCurrency(toCurrency)
                .fromDate(startDate)
                .toDate(startDate)
                .exchangeRate(exchangeRate)
                .build();
    }

}
