package com.ideas.tetris.pacman.services.dashboard.dto;

import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.MultiPropertyAggregate;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Sum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

@MultiPropertyAggregate
public class TotalActivityDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private static final int CALCULATED_SCALE = 2;

    @Sum(scale = 0)
    private BigDecimal availableCapacity;


    @Sum(scale = 1)
    private BigDecimal occupancy;

    @Sum(scale = 0)
    private BigDecimal revenue;

    public BigDecimal getPhysicalCapacity() {
        return physicalCapacity;
    }

    public void setPhysicalCapacity(BigDecimal physicalCapacity) {
        this.physicalCapacity = physicalCapacity;
    }

    @Sum(scale = 0)
    private BigDecimal physicalCapacity;
    @Sum(scale = 2)
    private BigDecimal profit;
    private BigDecimal profitPAR;

    public BigDecimal getProfit() {
        return profit;
    }

    public void setProfit(BigDecimal profit) {
        this.profit = profit;
    }

    public BigDecimal getProfitPAR() {
        return profitPAR;
    }

    public void setProfitPAR(BigDecimal profitPAR) {
        this.profitPAR = profitPAR;
    }

    public BigDecimal getProfitSTLYPAR(boolean usePhysicalCapacity) {
        if (profit == null || isNotValidCapacity(usePhysicalCapacity)) {
            return BigDecimal.ZERO;
        }
        BigDecimal capacityToUse = usePhysicalCapacity ? physicalCapacity : availableCapacity;
        return BigDecimal.valueOf(profit.doubleValue() / capacityToUse.doubleValue()).setScale(CALCULATED_SCALE, RoundingMode.HALF_UP);
    }

    private boolean isNotValidCapacity(boolean usePhysicalCapacity) {
        boolean physicalCapacityNotValid = usePhysicalCapacity && isZeroCapacity(physicalCapacity);
        boolean availableCapacityNotValid = !usePhysicalCapacity && isZeroCapacity(availableCapacity);
        return availableCapacityNotValid || physicalCapacityNotValid;
    }

    private boolean isZeroCapacity(BigDecimal physicalCapacity) {
        return physicalCapacity == null || physicalCapacity.compareTo(BigDecimal.ZERO) == 0;
    }

    public BigDecimal getAvailableCapacity() {
        return availableCapacity;
    }

    public void setAvailableCapacity(BigDecimal availableCapacity) {
        this.availableCapacity = availableCapacity;
    }

    public BigDecimal getOccupancy() {
        return occupancy;
    }

    public void setOccupancy(BigDecimal occupancy) {
        this.occupancy = occupancy;
    }

    public BigDecimal getRevenue() {
        return revenue;
    }

    public void setRevenue(BigDecimal revenue) {
        this.revenue = revenue;
    }


    public BigDecimal getOccupancyPercent() {
        if (availableCapacity == null || availableCapacity.compareTo(BigDecimal.ZERO) == 0 || occupancy == null || occupancy.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(occupancy.doubleValue() * 100.0 / availableCapacity.doubleValue()).setScale(CALCULATED_SCALE, RoundingMode.HALF_UP);
    }

    public BigDecimal getADR() {
        if (revenue == null || occupancy == null || occupancy.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(revenue.doubleValue() / occupancy.doubleValue()).setScale(CALCULATED_SCALE, RoundingMode.HALF_UP);
    }

    public BigDecimal getREVPAR(boolean usePhysicalCapacity) {
        BigDecimal revPAR;
        boolean physicalCapacityCheck = usePhysicalCapacity && (revenue == null || physicalCapacity == null || physicalCapacity.compareTo(BigDecimal.ZERO) == 0);
        boolean availableCapacityCheck = !usePhysicalCapacity && (revenue == null || availableCapacity == null || availableCapacity.compareTo(BigDecimal.ZERO) == 0);
        if (availableCapacityCheck || physicalCapacityCheck) {
            revPAR = BigDecimal.ZERO;
        } else {
            BigDecimal capacityToUse = usePhysicalCapacity ? physicalCapacity : availableCapacity;
            revPAR = BigDecimal.valueOf(revenue.doubleValue() / capacityToUse.doubleValue()).setScale(CALCULATED_SCALE, RoundingMode.HALF_UP);
        }
        return revPAR;
    }
}