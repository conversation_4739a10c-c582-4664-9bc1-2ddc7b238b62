package com.ideas.tetris.pacman.services.security;

public enum PermissionKeys {

    NO_ACCESS(1, "noAccess"),
    READ_ONLY_ACCESS(2, "readOnly"),
    READ_WRITE_ACCESS(3, "readWrite");

    private final Integer weight;

    public Integer getWeight() {
        return weight;
    }

    private final String label;

    public String getLabel() {
        return label;
    }

    PermissionKeys(Integer weight, String label) {
        this.weight = weight;
        this.label = label;
    }
}
