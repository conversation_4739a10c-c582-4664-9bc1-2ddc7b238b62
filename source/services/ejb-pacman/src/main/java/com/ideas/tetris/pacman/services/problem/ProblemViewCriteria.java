package com.ideas.tetris.pacman.services.problem;

import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.tetris.pacman.services.job.JobViewCriteria;
import com.ideas.tetris.pacman.services.job.entity.ExecutionStatus;
import com.ideas.tetris.pacman.services.job.entity.JobView;
import com.ideas.tetris.pacman.services.problem.dto.Resolution;
import com.ideas.tetris.pacman.services.problem.entity.ProblemView;
import com.ideas.tetris.platform.common.entity.AbstractCriteria;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.ErrorType;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.util.G3Restrictions;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.Stage;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Restrictions;
import org.hibernate.sql.JoinType;
import org.hibernate.type.LongType;
import org.hibernate.type.Type;
import org.joda.time.Duration;
import org.joda.time.LocalDateTime;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@SuppressWarnings("unused")
public class ProblemViewCriteria extends AbstractCriteria<ProblemView> {
    public static final Integer CLAIMED_ONLY = -1;
    public static final Integer UNCLAIMED_ONLY = -2;
    public static final Integer ALL_PROPERTIES = -1;
    public static final Integer ALL_PROPERTIES_OR_NO_PROPERTY = -2;
    private static final Integer ALL_CLIENTS = -1;
    public static final Integer ALL_CLIENTS_OR_NO_CLIENT = -2;
    private Integer ownerId; // null selects claimed and unclaimed problems
    private Integer clientId = ALL_CLIENTS_OR_NO_CLIENT; // null selects problems not associated with a client
    private Integer propertyId; // null selects problems not associated with a property
    private String jobName; // null selects all job types
    private String stepName; // null selects all steps, is ignored if jobName is null
    private boolean includeActiveProblems;
    private boolean includeClosedProblems;
    private LocalDateTime dateRangeStart;
    private LocalDateTime dateRangeEnd;
    private LocalDateTime activeDate;
    private List<Long> problemIds = new ArrayList<>();
    private JobView jobView;
    private ErrorCode errorCode;
    private Resolution resolution;
    private Duration duration;
    private List<JobName> jobNames = new ArrayList<>();
    private List<LDAPUser> ownerIds; //null include all, empty problems with no owners
    private List<Stage> stages; //null include all, empty problems with no stage
    private Set<String> clientCodes;
    private Set<String> propertyCodes;
    private LocalDateTime closedDateRangeStart;
    private LocalDateTime closedDateRangeEnd;
    private ErrorType problemType;
    private boolean excludeStep;
    private boolean includeNotes;
    private String notes;
    private String dbServerName;
    private String sasServerName;
    private List<String> nodes;


    public String getSasServerName() {
        return sasServerName;
    }

    public void setSasServerName(String sasServerName) {
        this.sasServerName = sasServerName;
    }


    public String getDbServerName() {
        return dbServerName;
    }

    public void setDbServerName(String dbServerName) {
        this.dbServerName = dbServerName;
    }


    public ProblemViewCriteria() {
        super(ProblemView.class);
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public Duration getDuration() {
        return duration;
    }

    public void setDuration(Duration duration) {
        this.duration = duration;
    }

    public Integer getClientId() {
        return clientId;
    }

    public void setClientId(Integer clientId) {
        this.clientId = clientId;
    }

    public Integer getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Integer propertyId) {
        this.propertyId = propertyId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getStepName() {
        return stepName;
    }

    public void setStepName(String stepName) {
        this.stepName = stepName;
    }

    public boolean isIncludeActiveProblems() {
        return includeActiveProblems;
    }

    public void setIncludeActiveProblems(boolean includeActiveProblems) {
        this.includeActiveProblems = includeActiveProblems;
    }

    public boolean isIncludeClosedProblems() {
        return includeClosedProblems;
    }

    public void setIncludeClosedProblems(boolean includeClosedProblems) {
        this.includeClosedProblems = includeClosedProblems;
    }

    public LocalDateTime getActiveDate() {
        return activeDate;
    }

    public void setActiveDate(LocalDateTime activeDate) {
        this.activeDate = activeDate;
    }

    public LocalDateTime getDateRangeStart() {
        return dateRangeStart;
    }

    public void setDateRangeStart(LocalDateTime dateRangeStart) {
        this.dateRangeStart = dateRangeStart;
    }

    public LocalDateTime getDateRangeEnd() {
        return dateRangeEnd;
    }

    public void setDateRangeEnd(LocalDateTime dateRangeEnd) {
        this.dateRangeEnd = dateRangeEnd;
    }

    public List<Long> getProblemIds() {
        return problemIds;
    }

    public void setProblemIds(List<Long> problemIds) {
        this.problemIds = problemIds;
    }

    public void addProblemId(Long problemId) {
        if (problemIds == null) {
            problemIds = new ArrayList<>();
        }
        problemIds.add(problemId);
    }

    public JobView getJobView() {
        return jobView;
    }

    public void setJobView(JobView jobView) {
        this.jobView = jobView;
    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(ErrorCode errorCode) {
        this.errorCode = errorCode;
    }

    public Resolution getResolution() {
        return resolution;
    }

    public void setResolution(Resolution resolution) {
        this.resolution = resolution;
    }

    public List<JobName> getJobNames() {
        return jobNames;
    }

    public void setJobNames(List<JobName> jobNames) {
        this.jobNames = jobNames;
    }

    public void addJobName(JobName name) {
        if (jobNames == null) {
            jobNames = new ArrayList<>();
        }
        jobNames.add(name);
    }

    public List<LDAPUser> getOwnerIds() {
        return ownerIds;
    }

    public void setOwnerIds(List<LDAPUser> ownerIds) {
        this.ownerIds = ownerIds;
    }

    public void addOwnerId(LDAPUser user) {
        if (ownerIds == null) {
            ownerIds = new ArrayList<>();
        }
        ownerIds.add(user);
    }

    public List<Stage> getStages() {
        return stages;
    }

    public void setStages(List<Stage> stages) {
        this.stages = stages;
    }

    public void addStage(Stage stage) {
        if (stages == null) {
            stages = new ArrayList<>();
        }
        stages.add(stage);
    }

    public LocalDateTime getClosedDateRangeStart() {
        return closedDateRangeStart;
    }

    public void setClosedDateRangeStart(LocalDateTime closedDateRangeStart) {
        this.closedDateRangeStart = closedDateRangeStart;
    }

    public LocalDateTime getClosedDateRangeEnd() {
        return closedDateRangeEnd;
    }

    public void setClosedDateRangeEnd(LocalDateTime closedDateRangeEnd) {
        this.closedDateRangeEnd = closedDateRangeEnd;
    }

    public Set<String> getClientCodes() {
        return clientCodes;
    }

    public void setClientCodes(Set<String> clientCodes) {
        this.clientCodes = clientCodes;
    }

    public void setPropertyCodes(Set<String> propertyCodes) {
        this.propertyCodes = propertyCodes;
    }

    public Set<String> getPropertyCodes() {
        return propertyCodes;
    }

    @Override
    public DetachedCriteria getDetachedCriteria() {

        DetachedCriteria detachedCriteria = super.getDetachedCriteria();

        if (!includeActiveProblems) {
            detachedCriteria.add(Restrictions.eq("active", false));
        }
        if (!includeClosedProblems) {
            detachedCriteria.add(Restrictions.eq("active", true));
        }
        if (Objects.equals(ownerId, CLAIMED_ONLY)) {
            detachedCriteria.add(Restrictions.isNotNull("ownerId"));
        } else if (Objects.equals(ownerId, UNCLAIMED_ONLY)) {
            detachedCriteria.add(Restrictions.isNull("ownerId"));
        } else if (ownerId != null) {
            detachedCriteria.add(Restrictions.eq("ownerId", ownerId.toString()));
        }
        if (Objects.equals(clientId, ALL_CLIENTS_OR_NO_CLIENT)) {
            // do nothing
        } else if (Objects.equals(clientId, ALL_CLIENTS)) {
            detachedCriteria.add(Restrictions.isNotNull("clientId"));
        } else if (clientId == null) {
            detachedCriteria.add(Restrictions.isNull("clientId"));
        } else {
            detachedCriteria.add(Restrictions.eq("clientId", clientId));
        }
        if (Objects.equals(propertyId, ALL_PROPERTIES_OR_NO_PROPERTY)) {
            // do nothing
        } else if (Objects.equals(propertyId, ALL_PROPERTIES)) {
            detachedCriteria.add(Restrictions.isNotNull("propertyId"));
        } else if (propertyId == null) {
            detachedCriteria.add(Restrictions.isNull("propertyId"));
        } else {
            detachedCriteria.add(Restrictions.eq("propertyId", propertyId));
        }
        if (jobName != null) {
            detachedCriteria.add(Restrictions.eq("jobName", jobName));
        }
        if (stepName != null) {
            if (excludeStep) {
                detachedCriteria.add(Restrictions.ne("stepName", stepName));
            } else {
                detachedCriteria.add(Restrictions.eq("stepName", stepName));
            }
        }
        if (activeDate != null) {
            detachedCriteria.add(Restrictions.le("creationDate", activeDate));
            detachedCriteria.add(Restrictions.or(
                    Restrictions.isNull("closedDate"),
                    Restrictions.ge("closedDate", activeDate)));
        }
        if (dateRangeStart != null) {
            detachedCriteria.add(Restrictions.ge("creationDate", dateRangeStart));
        }
        if (dateRangeEnd != null) {
            detachedCriteria.add(Restrictions.le("creationDate", dateRangeEnd));
        }
        if (closedDateRangeStart != null) {
            detachedCriteria.add(Restrictions.ge("closedDate", closedDateRangeStart));
        }
        if (closedDateRangeEnd != null) {
            detachedCriteria.add(Restrictions.le("closedDate", closedDateRangeEnd));
        }
        if (problemIds != null && !problemIds.isEmpty()) {
            detachedCriteria.add(G3Restrictions.in("problemId", problemIds));
        }
        if (jobNames != null && !jobNames.isEmpty()) {
            List<String> names = jobNames.stream().map(Enum::toString).collect(Collectors.toList());
            detachedCriteria.add(G3Restrictions.in("jobName", names));
        }
        if (ownerIds != null) {
            if (ownerIds.isEmpty()) {
                detachedCriteria.add(Restrictions.isNull("ownerId"));
            } else {
                List<String> ids = ownerIds.stream().map(value -> value.getUserId().toString()).collect(Collectors.toList());
                detachedCriteria.add(G3Restrictions.in("ownerId", ids));
            }
        }
        if (stages != null) {
            if (stages.isEmpty()) {
                detachedCriteria.add(Restrictions.isNull("propertyStage"));
            } else {
                List<String> stageCodes = stages.stream().map(Stage::getCode).collect(Collectors.toList());
                detachedCriteria.add(G3Restrictions.in("propertyStage", stageCodes));
            }
        }
        if (clientCodes != null && !clientCodes.isEmpty()) {
            detachedCriteria.add(Restrictions.or(
                    G3Restrictions.in("clientCode", clientCodes),
                    Restrictions.isNull("clientCode")));
        }

        if (propertyCodes != null && !propertyCodes.isEmpty()) {
            detachedCriteria.add(Restrictions.or(
                    G3Restrictions.in("propertyCode", propertyCodes),
                    Restrictions.isNull("propertyCode")));
        }

        if (jobView != null) {
            detachedCriteria.createAlias("jobExecution.jobView", "jobViewAlias");
            detachedCriteria.add(Restrictions.eq("jobViewAlias.jobInstanceId", jobView.getJobInstanceId()));
        }
        if (errorCode != null) {
            detachedCriteria.add(Restrictions.eq("errorCode", errorCode.getId()));
        }
        if (resolution != null) {
            detachedCriteria.add(Restrictions.eq("resolutionText", resolution.getNoteText()));
        }
        if (duration != null) {
            long seconds = duration.getMillis() / DateUtil.SECOND_IN_MILLISECONDS;
            detachedCriteria.add(Restrictions.sqlRestriction("({alias}.CLOSED_DATE is not null and DATEDIFF(s, {alias}.CREATION_DATE, {alias}.CLOSED_DATE) > ?)" +
                            " or ({alias}.CLOSED_DATE is null and DATEDIFF(s, {alias}.CREATION_DATE, GETDATE()) > ?)",
                    new Long[]{seconds, seconds}, new Type[]{LongType.INSTANCE, LongType.INSTANCE}));
        }
        if (problemType != null) {
            detachedCriteria.add(Restrictions.eq("problemType", problemType));
        }
        if (includeNotes) {
            detachedCriteria.createAlias("notes", "notes", JoinType.LEFT_OUTER_JOIN);
            if (StringUtils.isNotBlank(notes)) {
                detachedCriteria.add(Restrictions.like("notes.text", notes, MatchMode.ANYWHERE));
            }
        }
        if (dbServerName != null) {
            detachedCriteria.add(Restrictions.eq("dbServerName", dbServerName));
        }
        if (sasServerName != null) {
            detachedCriteria.add(Restrictions.eq("sasServerName", sasServerName));
        }
        if (CollectionUtils.isNotEmpty(nodes)) {
            addNodes(detachedCriteria);
        }
        return detachedCriteria;
    }

    private void addNodes(DetachedCriteria detachedCriteria) {
        if (nodes.size() == 1) {
            detachedCriteria.add(Restrictions.eq("hostname", nodes.get(0)));
        } else {
            detachedCriteria.add(Restrictions.in("hostname", nodes));
        }
    }

    public static ProblemViewCriteria fromJobViewCriteria(JobViewCriteria criteria) {
        //build Problem View Criteria
        ProblemViewCriteria problemViewCriteria = new ProblemViewCriteria();
        problemViewCriteria.setDateRangeStart(criteria.getDateRangeStart());
        problemViewCriteria.setDateRangeEnd(criteria.getDateRangeEnd());

        if (!CollectionUtils.isEmpty(criteria.getPropertyStages())) {
            problemViewCriteria.setStages(criteria.getPropertyStages());
        }

        if (!CollectionUtils.isEmpty(criteria.getJobNames())) {
            List<JobName> jobNames = criteria.getJobNames().stream().map(s -> JobName.valueOf(s)).collect(Collectors.toList());
            problemViewCriteria.setJobNames(jobNames);
        }

        problemViewCriteria.setClientId(null == criteria.getClientId() ? ALL_CLIENTS_OR_NO_CLIENT : criteria.getClientId());
        problemViewCriteria.setPropertyId(criteria.getPropertyId() == null ? ALL_PROPERTIES_OR_NO_PROPERTY : criteria.getPropertyId());

        //default to false
        problemViewCriteria.setIncludeClosedProblems(true);
        problemViewCriteria.setIncludeActiveProblems(true);

        if (!CollectionUtils.isEmpty(criteria.getStatuses())) {
            if (criteria.getStatuses().contains(ExecutionStatus.ABANDONED) ||
                    criteria.getStatuses().contains(ExecutionStatus.COMPLETED)) {
                problemViewCriteria.setIncludeActiveProblems(false);

            }
            if (criteria.getStatuses().contains(ExecutionStatus.RUNNING) ||
                    criteria.getStatuses().contains(ExecutionStatus.STOPPING) ||
                    criteria.getStatuses().contains(ExecutionStatus.STOPPED) ||
                    criteria.getStatuses().contains(ExecutionStatus.FAILED)) {
                problemViewCriteria.setIncludeClosedProblems(false);

            }
        }
        return problemViewCriteria;
    }

    public ErrorType getProblemType() {
        return problemType;
    }

    public void setProblemType(ErrorType problemType) {
        this.problemType = problemType;
    }

    public void setExcludeStep(Boolean excludeStep) {
        this.excludeStep = excludeStep;
    }

    public boolean isExcludeStep() {
        return excludeStep;
    }

    public boolean isIncludeNotes() {
        return includeNotes;
    }

    public void setIncludeNotes(boolean includeNotes) {
        this.includeNotes = includeNotes;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public List<String> getNodes() {
        return nodes;
    }

    public void setNodes(List<String> nodes) {
        this.nodes = nodes;
    }
}
