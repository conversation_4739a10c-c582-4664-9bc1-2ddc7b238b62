package com.ideas.tetris.pacman.services.dashboard.vo;


import java.io.Serializable;

public class GMDashboardVO implements Serializable {

    private RoomsSoldAndPickupVO roomsSoldBudgetTodayVO;
    private RoomsSoldAndPickupVO roomsSoldBudgetYesterdayVO;
    private RoomsSoldAndPickupVO roomsSoldBudgetWeeklyVO;
    private RoomsSoldAndPickupVO roomsSoldBudgetMonthlyVO;
    private RoomsSoldAndPickupVO roomsSoldBudgetYearlyVO;
    private String onBooksOccupancyPercentage;
    private String onBooksRoomRevenue;
    private String onBooksADR;
    private String onBooksRevPAR;
    private String expectedOccupancyPercentage;
    private String expectedRoomRevenue;
    private String expectedADR;
    private String expectedRevPAR;
    private String budgetOccupancyPercentage;
    private String budgetRoomRevenue;
    private String budgetADR;
    private String budgetRevPAR;
    private String stlyOccupancyPercentage;
    private String stlyRoomRevenue;
    private String stlyADR;
    private String stlyRevPAR;
    private String lyOccupancyPercentage;
    private String lyRoomRevenue;
    private String lyADR;
    private String lyRevPAR;

    public RoomsSoldAndPickupVO getRoomsSoldBudgetTodayVO() {
        return roomsSoldBudgetTodayVO;
    }

    public void setRoomsSoldBudgetTodayVO(
            RoomsSoldAndPickupVO roomsSoldBudgetTodayVO) {
        this.roomsSoldBudgetTodayVO = roomsSoldBudgetTodayVO;
    }

    public RoomsSoldAndPickupVO getRoomsSoldBudgetYesterdayVO() {
        return roomsSoldBudgetYesterdayVO;
    }

    public void setRoomsSoldBudgetYesterdayVO(
            RoomsSoldAndPickupVO roomsSoldBudgetYesterdayVO) {
        this.roomsSoldBudgetYesterdayVO = roomsSoldBudgetYesterdayVO;
    }

    public RoomsSoldAndPickupVO getRoomsSoldBudgetWeeklyVO() {
        return roomsSoldBudgetWeeklyVO;
    }

    public void setRoomsSoldBudgetWeeklyVO(
            RoomsSoldAndPickupVO roomsSoldBudgetWeeklyVO) {
        this.roomsSoldBudgetWeeklyVO = roomsSoldBudgetWeeklyVO;
    }

    public RoomsSoldAndPickupVO getRoomsSoldBudgetMonthlyVO() {
        return roomsSoldBudgetMonthlyVO;
    }

    public void setRoomsSoldBudgetMonthlyVO(
            RoomsSoldAndPickupVO roomsSoldBudgetMonthlyVO) {
        this.roomsSoldBudgetMonthlyVO = roomsSoldBudgetMonthlyVO;
    }

    public RoomsSoldAndPickupVO getRoomsSoldBudgetYearlyVO() {
        return roomsSoldBudgetYearlyVO;
    }

    public void setRoomsSoldBudgetYearlyVO(
            RoomsSoldAndPickupVO roomsSoldBudgetYearlyVO) {
        this.roomsSoldBudgetYearlyVO = roomsSoldBudgetYearlyVO;
    }

    public String getOnBooksOccupancyPercentage() {
        return onBooksOccupancyPercentage;
    }

    public void setOnBooksOccupancyPercentage(String onBooksOccupancyPercentage) {
        this.onBooksOccupancyPercentage = onBooksOccupancyPercentage;
    }

    public String getOnBooksRoomRevenue() {
        return onBooksRoomRevenue;
    }

    public void setOnBooksRoomRevenue(String onBooksRoomRevenue) {
        this.onBooksRoomRevenue = onBooksRoomRevenue;
    }

    public String getOnBooksADR() {
        return onBooksADR;
    }

    public void setOnBooksADR(String onBooksADR) {
        this.onBooksADR = onBooksADR;
    }

    public String getOnBooksRevPAR() {
        return onBooksRevPAR;
    }

    public void setOnBooksRevPAR(String onBooksRevPAR) {
        this.onBooksRevPAR = onBooksRevPAR;
    }

    public String getExpectedOccupancyPercentage() {
        return expectedOccupancyPercentage;
    }

    public void setExpectedOccupancyPercentage(String expectedOccupancyPercentage) {
        this.expectedOccupancyPercentage = expectedOccupancyPercentage;
    }

    public String getExpectedRoomRevenue() {
        return expectedRoomRevenue;
    }

    public void setExpectedRoomRevenue(String expectedRoomRevenue) {
        this.expectedRoomRevenue = expectedRoomRevenue;
    }

    public String getExpectedADR() {
        return expectedADR;
    }

    public void setExpectedADR(String expectedADR) {
        this.expectedADR = expectedADR;
    }

    public String getExpectedRevPAR() {
        return expectedRevPAR;
    }

    public void setExpectedRevPAR(String expectedRevPAR) {
        this.expectedRevPAR = expectedRevPAR;
    }

    public String getBudgetOccupancyPercentage() {
        return budgetOccupancyPercentage;
    }

    public void setBudgetOccupancyPercentage(String budgetOccupancyPercentage) {
        this.budgetOccupancyPercentage = budgetOccupancyPercentage;
    }

    public String getBudgetRoomRevenue() {
        return budgetRoomRevenue;
    }

    public void setBudgetRoomRevenue(String budgetRoomRevenue) {
        this.budgetRoomRevenue = budgetRoomRevenue;
    }

    public String getBudgetADR() {
        return budgetADR;
    }

    public void setBudgetADR(String budgetADR) {
        this.budgetADR = budgetADR;
    }

    public String getBudgetRevPAR() {
        return budgetRevPAR;
    }

    public void setBudgetRevPAR(String budgetRevPAR) {
        this.budgetRevPAR = budgetRevPAR;
    }

    public String getStlyOccupancyPercentage() {
        return stlyOccupancyPercentage;
    }

    public void setStlyOccupancyPercentage(String stlyOccupancyPercentage) {
        this.stlyOccupancyPercentage = stlyOccupancyPercentage;
    }

    public String getStlyRoomRevenue() {
        return stlyRoomRevenue;
    }

    public void setStlyRoomRevenue(String stlyRoomRevenue) {
        this.stlyRoomRevenue = stlyRoomRevenue;
    }

    public String getStlyADR() {
        return stlyADR;
    }

    public void setStlyADR(String stlyADR) {
        this.stlyADR = stlyADR;
    }

    public String getStlyRevPAR() {
        return stlyRevPAR;
    }

    public void setStlyRevPAR(String stlyRevPAR) {
        this.stlyRevPAR = stlyRevPAR;
    }

    public String getLyOccupancyPercentage() {
        return lyOccupancyPercentage;
    }

    public void setLyOccupancyPercentage(String lyOccupancyPercentage) {
        this.lyOccupancyPercentage = lyOccupancyPercentage;
    }

    public String getLyRoomRevenue() {
        return lyRoomRevenue;
    }

    public void setLyRoomRevenue(String lyRoomRevenue) {
        this.lyRoomRevenue = lyRoomRevenue;
    }

    public String getLyADR() {
        return lyADR;
    }

    public void setLyADR(String lyADR) {
        this.lyADR = lyADR;
    }

    public String getLyRevPAR() {
        return lyRevPAR;
    }

    public void setLyRevPAR(String lyRevPAR) {
        this.lyRevPAR = lyRevPAR;
    }

}
