package com.ideas.tetris.pacman.services.rollback;

import com.ideas.sas.service.SASClientService;
import com.ideas.tetris.pacman.common.constants.Constants;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

import static com.ideas.tetris.pacman.common.constants.Constants.COPY_FILE;
import static com.ideas.tetris.pacman.common.constants.Constants.DELETE_FILE_QUIETLY;
import static com.ideas.tetris.pacman.common.constants.Constants.DELETE_PATH;
import static com.ideas.tetris.pacman.common.constants.Constants.DESC_PATH;
import static com.ideas.tetris.pacman.common.constants.Constants.SRC_PATH;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class NewFeatureDatasetBackupService {

    private static final Logger LOGGER = Logger.getLogger(NewFeatureDatasetBackupService.class);

    @Autowired
	protected RollbackHelper rollbackHelper;
    @Autowired
	private SASClientService sasClientService;

    public void backup(Integer propertyId) {
        String newFeatureDatasetPath = rollbackHelper.getAnalyticalDatasetDirectory(propertyId) + File.separator + Constants.NEW_FEATURE_DATASET_FILENAME;

        String backupFolder = rollbackHelper.getNewFeatureDatasetBackupFolder(propertyId);
        sasClientService.executeFileOps(DELETE_FILE_QUIETLY, new HashMap<>(Map.of(DELETE_PATH, backupFolder)));

        sasClientService.executeFileOps(COPY_FILE, new HashMap<>(Map.of(SRC_PATH, newFeatureDatasetPath, DESC_PATH, backupFolder)));
        LOGGER.debug("New Feature Dataset backup path :" + backupFolder);
    }
}
