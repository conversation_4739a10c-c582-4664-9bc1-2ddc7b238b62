package com.ideas.tetris.pacman.services.datafeed.dto.pricingdata;


import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesPackageChargeType;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

public class PricingDataProductPackageElementsDowDTO {

    private String packageName;
    private String packageDescription;
    private String occupancyType;
    private String category;
    private Date seasonalStartDate;
    private Date seasonalEndDate;
    private String adjustmentType;
    private BigDecimal sunday;
    private BigDecimal monday;
    private BigDecimal tuesday;
    private BigDecimal wednesday;
    private BigDecimal thursday;
    private BigDecimal friday;
    private BigDecimal saturday;

    public PricingDataProductPackageElementsDowDTO(AgileRatesPackageChargeType agileRatesPackageChargeType) {
        this.packageName = agileRatesPackageChargeType.getAgileRatesPackage().getName();
        this.packageDescription = agileRatesPackageChargeType.getAgileRatesPackage().getDescription();
        this.occupancyType = agileRatesPackageChargeType.getOccupancyType().name();
        this.category = Optional.ofNullable(agileRatesPackageChargeType.getStartDate()).isPresent() ? Constants.SEASONAL : Constants.DEFAULT;
        this.seasonalStartDate = agileRatesPackageChargeType.getStartDate() == null ? null : agileRatesPackageChargeType.getStartDate().toDate();
        this.seasonalEndDate = agileRatesPackageChargeType.getEndDate() == null ? null : agileRatesPackageChargeType.getEndDate().toDate();
        this.adjustmentType = AgileRatesOffsetMethod.PERCENTAGE.equals(agileRatesPackageChargeType.getAgileRatesPackage().getOffsetMethod()) ? "Percentage" : agileRatesPackageChargeType.getAgileRatesPackage().getOffsetMethod().getCaption();
        this.sunday = agileRatesPackageChargeType.getSundayOffsetValue();
        this.monday = agileRatesPackageChargeType.getMondayOffsetValue();
        this.tuesday = agileRatesPackageChargeType.getTuesdayOffsetValue();
        this.wednesday = agileRatesPackageChargeType.getWednesdayOffsetValue();
        this.thursday = agileRatesPackageChargeType.getThursdayOffsetValue();
        this.friday = agileRatesPackageChargeType.getFridayOffsetValue();
        this.saturday = agileRatesPackageChargeType.getSaturdayOffsetValue();
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getPackageDescription() {
        return packageDescription;
    }

    public void setPackageDescription(String packageDescription) {
        this.packageDescription = packageDescription;
    }

    public String getOccupancyType() {
        return occupancyType;
    }

    public void setOccupancyType(String occupancyType) {
        this.occupancyType = occupancyType;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Date getSeasonalStartDate() {
        return seasonalStartDate;
    }

    public void setSeasonalStartDate(Date seasonalStartDate) {
        this.seasonalStartDate = seasonalStartDate;
    }

    public Date getSeasonalEndDate() {
        return seasonalEndDate;
    }

    public void setSeasonalEndDate(Date seasonalEndDate) {
        this.seasonalEndDate = seasonalEndDate;
    }

    public String getAdjustmentType() {
        return adjustmentType;
    }

    public void setAdjustmentType(String adjustmentType) {
        this.adjustmentType = adjustmentType;
    }

    public BigDecimal getSunday() {
        return sunday;
    }

    public void setSunday(BigDecimal sunday) {
        this.sunday = sunday;
    }

    public BigDecimal getMonday() {
        return monday;
    }

    public void setMonday(BigDecimal monday) {
        this.monday = monday;
    }

    public BigDecimal getTuesday() {
        return tuesday;
    }

    public void setTuesday(BigDecimal tuesday) {
        this.tuesday = tuesday;
    }

    public BigDecimal getWednesday() {
        return wednesday;
    }

    public void setWednesday(BigDecimal wednesday) {
        this.wednesday = wednesday;
    }

    public BigDecimal getThursday() {
        return thursday;
    }

    public void setThursday(BigDecimal thursday) {
        this.thursday = thursday;
    }

    public BigDecimal getFriday() {
        return friday;
    }

    public void setFriday(BigDecimal friday) {
        this.friday = friday;
    }

    public BigDecimal getSaturday() {
        return saturday;
    }

    public void setSaturday(BigDecimal saturday) {
        this.saturday = saturday;
    }
}
