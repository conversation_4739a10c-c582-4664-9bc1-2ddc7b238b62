package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.opera.dto.MissingDayInformation;
import com.ideas.tetris.pacman.services.opera.entity.DataLoadMetadata;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.NEW_LINE;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class OperaCorrectMissingTransactionRawService {
    private static final Logger LOGGER = Logger.getLogger(OperaCorrectMissingTransactionRawService.class);

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService crudService;

    @Autowired
	protected OperaUtilityService operaUtilityService;

    @Autowired
	protected PacmanConfigParamsService configParamsService;

    @Autowired
	protected OperaFilterStageDataService operaFilterStageDataService;

    private static final String PAST_DAYS = "Past_Days";
    private static final String FUTURE_DAYS = "Future_Days";

    public static final String FIND_MISSING_DATES = new StringBuilder()
            .append("select distinct Reservation_Name_ID, Confirmation_Number, ")
            .append("CAST(Transaction_DT as DATE) as Transaction_DT, ")
            .append("CAST(Arrival_DT as DATE) as Arrival_DT, CAST(Departure_DT as DATE) as Departure_DT ")
            .append("into #RawTrans ")
            .append("from opera.Raw_Transaction ")
            .append("where Reservation_Status not in (:reservationStatusToSkip) ")
            .append("and Data_Load_Metadata_ID IN (:dataLoadMetaDataIds)")
            .append("select c.* from ")
            .append("( ")
            .append("   select distinct calendar_date, CAST(Arrival_DT as DATE) as Arrival_DT, CAST(Departure_DT as DATE) as Departure_DT, Reservation_Name_ID, Confirmation_Number ")
            .append("   from calendar_dim as a ")
            .append("   join ")
            .append("   #RawTrans as b ")
            .append("   on a.calendar_date >= b.Arrival_DT and a.calendar_date < b.Departure_DT ")
            .append("   and a.calendar_date >= :startDayOfWindow and a.calendar_date <= :endDayOfWindow ")
            .append(") as c ")
            .append("left outer join ")
            .append("#RawTrans as x  ")
            .append("on x.Transaction_DT = c.calendar_date ")
            .append("and x.Reservation_Name_ID = c.Reservation_Name_ID ")
            .append("and x.Confirmation_Number = c.Confirmation_Number ")
            .append("where  x.Transaction_DT is null ").toString();

    public String correctMissingRawTransaction(String correlationId) {
        Date businessDate = operaUtilityService.getBusinessDate(correlationId);
        Date windowStartDate = operaUtilityService.getMaxPastDate(businessDate, PAST_DAYS, correlationId);
        Date windowEndDate = operaUtilityService.getMaxFutureDate(businessDate, FUTURE_DAYS, correlationId);
        LocalDate startDayOfWindow = LocalDate.fromDateFields(windowStartDate);
        LocalDate endDayOfWindow = LocalDate.fromDateFields(windowEndDate);
        List<MissingDayInformation> missingDayInformationWithinWindow = findMissingDatesInRawTransaction(correlationId, startDayOfWindow, endDayOfWindow);
        LOGGER.info(String.format("Start date of window : %s. ", startDayOfWindow));
        LOGGER.info(String.format("Missing transaction dates of Raw Transaction within window are : %s. ", extractMissingDates(missingDayInformationWithinWindow)));
        LOGGER.info(String.format("Correct missing raw transaction for the correlation id : %s", correlationId));
        Integer allowedNumberOfMissingDates = configParamsService.getIntegerParameterValue(IntegrationConfigParamName.OPERA_CORRECT_MISSING_TRANSACTION_NUMBER.value());

        if (missingDayInformationWithinWindow.size() > allowedNumberOfMissingDates) {
            throw new TetrisException(String.format("Missing transaction dates in Raw Transaction is %d which is more than the configurable parameter (OperaCorrectMissingTransactionNumber) %d. The extract contains %d missing transaction. ",
                    missingDayInformationWithinWindow.size(), allowedNumberOfMissingDates, missingDayInformationWithinWindow.size()));
        }
        Integer latestCTransDataloadMetadataIdBeforeGivenCorrelationId = findLatestDataloadMetadataIdBeforeGivenCorrelationId(correlationId,
                OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        LOGGER.info(String.format("Latest CTRANS dataload metadata id before the given correlation id : %d", latestCTransDataloadMetadataIdBeforeGivenCorrelationId));
        Integer latestPTransDataloadMetadataIdBeforeGivenCorrelationId = findLatestDataloadMetadataIdBeforeGivenCorrelationId(correlationId,
                OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        LOGGER.info(String.format("Latest PTRANS dataload metadata id before the given correlation id : %d", latestPTransDataloadMetadataIdBeforeGivenCorrelationId));
        List<Integer> cTransPTransOfLastCorrelation = Arrays.asList(latestCTransDataloadMetadataIdBeforeGivenCorrelationId, latestPTransDataloadMetadataIdBeforeGivenCorrelationId);

        StringBuilder result = getResultString(missingDayInformationWithinWindow, allowedNumberOfMissingDates);
        missingDayInformationWithinWindow.stream().forEach(missingInfo -> {
            LocalDate missingDate = missingInfo.getMissingDate();
            LOGGER.info(String.format("Correcting Raw transaction for the missing date : %s, Reservation Id : %s, Confirmation Number : %s, Arrival Date : %s, Departure Date : %s, Ctrans and Ptrans : %s",
                    missingDate, missingInfo.getReservationId(), missingInfo.getConfirmationNumber(), missingInfo.getArrivalDate(), missingInfo.getDepartureDate(), cTransPTransOfLastCorrelation.toArray()));
            int updatedRowsForCTransPTrans = updateRawTransactionFor(missingDate, missingInfo.getReservationId(), missingInfo.getConfirmationNumber(),
                    missingInfo.getArrivalDate(), missingInfo.getDepartureDate(), cTransPTransOfLastCorrelation);
            if (updatedRowsForCTransPTrans <= 0) {
                throw new TetrisException(String.format("No Information found in the History for the missing date : %s in Raw Transaction. %s", missingDate, result.toString()));
            }
        });

        result.append("Total number of missing transactions fixed are : " + missingDayInformationWithinWindow.size());
        return result.toString();
    }

    private StringBuilder getResultString(List<MissingDayInformation> missingDayInformationWithinWindow, Integer allowedNumberOfMissingDates) {
        StringBuilder result = new StringBuilder();
        result.append("Total number of missing transactions are : ").append(missingDayInformationWithinWindow.size());
        result.append(NEW_LINE);
        result.append("OperaCorrectMissingTransactionNumber is : ").append(allowedNumberOfMissingDates);
        result.append(NEW_LINE);
        return result;
    }

    private List<String> extractMissingDates(List<MissingDayInformation> missingDays) {

        return missingDays.stream().filter(Objects::nonNull).map(missingDay ->
                new StringBuilder()
                        .append("Date : ")
                        .append(missingDay.getMissingDate())
                        .append(", Reservation Name Id : ")
                        .append(missingDay.getReservationId())
                        .append(", Confirmation Number : ")
                        .append(missingDay.getConfirmationNumber())
                        .append(NEW_LINE).toString()
        ).collect(Collectors.toList());
    }

    public List<MissingDayInformation> findMissingDatesInRawTransaction(String correlationId, LocalDate startDayOfWindow, LocalDate endDayOfWindow) {
        DataLoadMetadata cTransDataLoadMetadataId = operaUtilityService.getDataLoadMetadataForFileType(correlationId, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata pTransDataLoadMetadataId = operaUtilityService.getDataLoadMetadataForFileType(correlationId, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        List<Integer> dataLoadMetaDataIds = Arrays.asList(
                null != cTransDataLoadMetadataId ? cTransDataLoadMetadataId.getId() : -1,
                null != pTransDataLoadMetadataId ? pTransDataLoadMetadataId.getId() : -1
        );
        List<String> reservationStatusToSkip = new ArrayList<>(Arrays.asList(OperaReservationStatus.CANCELLED.getCode(), OperaReservationStatus.NO_SHOW.getCode()));

        List<String> reservationStatusesToFilter = operaFilterStageDataService.getReservationStatusesToFilter();
        if (isNotEmpty(reservationStatusesToFilter)) {
            reservationStatusToSkip.addAll(reservationStatusesToFilter);
        }
        List<Object[]> results = crudService.findByNativeQuery(FIND_MISSING_DATES,
                QueryParameter.with("dataLoadMetaDataIds", dataLoadMetaDataIds)
                        .and("startDayOfWindow", startDayOfWindow)
                        .and("endDayOfWindow", endDayOfWindow)
                        .and("reservationStatusToSkip", reservationStatusToSkip)
                        .parameters());
        return results.stream().map(MissingDayInformation::new).collect(Collectors.toList());
    }

    private Integer findLatestDataloadMetadataIdBeforeGivenCorrelationId(String correlationId, String incomingFileTypeCode) {
        String queryStr = new StringBuilder()
                .append("select t1.Data_Load_Metadata_ID from opera.Data_Load_Metadata as t1 ")
                .append("inner join opera.Data_Load_Metadata as t2 ")
                .append("on t1.Incoming_File_Type_Code = t2.Incoming_File_Type_Code ")
                .append("and t2.Incoming_File_Type_Code = :incomingFileTypeCode and t2.Correlation_ID = :correlationId ")
                .append("where t1.Correlation_ID != :correlationId ")
                .append("and t1.Incoming_File_Type_Code = :incomingFileTypeCode ")
                .append("and t1.Create_DT < t2.Create_DT ")
                .append("order by t1.Create_DT desc ").toString();
        List<Object> dataLoadMetadataIds = crudService.findByNativeQuery(
                queryStr, QueryParameter.with("incomingFileTypeCode", incomingFileTypeCode).and("correlationId", correlationId).parameters());
        Integer latestDataloadMetadataId = -1;
        if (isNotEmpty(dataLoadMetadataIds)) {
            latestDataloadMetadataId = (Integer) dataLoadMetadataIds.get(0);
        }
        return latestDataloadMetadataId;
    }

    private int updateRawTransactionFor(LocalDate missingDate, String reservationId, String confirmationNumber, LocalDate arrivalDate, LocalDate departureDate, List<Integer> dataloadMetadataIds) {
        return crudService.executeUpdateByNativeQuery("insert into opera.Raw_Transaction \n" +
                        "select Confirmation_Number, Reservation_Status, Is_Shared, Sharers, Transaction_DT, Arrival_DT, Departure_DT, Checkout_DT, Cancellation_DT, Booking_DT, Rate_Code, Rate_Amount, \n" +
                        "Market_Code, Room, Room_Revenue, Food_Beverage_Revenue, Other_Revenue, Total_Revenue, Room_Type, Source_Code, Channel, Booked_Room_Type, Nationality, Reservation_Type, \n" +
                        "Number_Children, Number_Adults, Reservation_Name_ID, Data_Load_Metadata_ID, Update_DTTM, Rate_Category, Booking_TM \n" +
                        "from  opera.History_Transaction where cast(Transaction_DT as date) = '" + missingDate + "' and Reservation_Name_ID = " + reservationId + " and Confirmation_Number = "
                        + confirmationNumber + " and cast(Arrival_DT as date) = '" + arrivalDate + "' and cast(Departure_DT as date) = '" + departureDate + "' and Data_Load_Metadata_ID IN (:dataLoadMetadataIds)",
                QueryParameter.with("dataLoadMetadataIds", dataloadMetadataIds).parameters());
    }
}
