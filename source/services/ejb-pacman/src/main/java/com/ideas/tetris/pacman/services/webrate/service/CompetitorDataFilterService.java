package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.eventaggregator.SystemComponent;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.webrate.entity.*;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Component
@Transactional
public class CompetitorDataFilterService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
	private SyncEventAggregatorService syncEventAggregatorService;

    @Autowired
	private PropertyService propertyService;

    @Autowired
    private WebrateShoppingCleanUpService webrateShoppingCleanUpService;

    @SuppressWarnings("unchecked")
    public List<WebrateCompetitors> getAllCompetitorsByProperty(Integer propertyId) {

        String webRateHotelId = configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        List<WebrateCompetitors> competitorsList = crudService.
                findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID_AND_STATUS_NOT,
                        QueryParameter.with(WebrateCompetitors.PARAM_PROP_ID, propertyId).and(WebrateCompetitors.PARAM_STATUS_ID, 2).parameters());
        Property property = propertyService.getPropertyById(propertyId);
        for (WebrateCompetitors webrateCompetitor : competitorsList) {
            if (webrateCompetitor.getWebrateCompetitorsAccomClasses().isEmpty()) {
                List<AccomClass> accomClassList = crudService.findByNamedQuery(
                        WebrateAccomClassMapping.BY_PROPERTY_ID,
                        QueryParameter.with("propertyId", propertyId).parameters());
                webrateCompetitor.setAccomClassList(accomClassList);
            }
            //To Check for the self Competitor
            try {
                if ((webRateHotelId != null && webRateHotelId.equalsIgnoreCase(webrateCompetitor.getWebrateHotelID())) || isRDLSelfProperty(webrateCompetitor, property)) {
                    webrateCompetitor.setIsSelfCompetitor(1);
                }
            } catch (NumberFormatException e) {
                // This will occur if WebRate Hotel Id is not properly set either in parameter_value table or in
                // web rate competitors table
            }
        }
        return competitorsList;
    }

    private boolean isRDLSelfProperty(WebrateCompetitors webrateCompetitor, Property property) {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED) &&
                StringUtils.isNotEmpty(property.getUpsId()) && property.getUpsId().equals(webrateCompetitor.getUpsId());
    }

    public boolean deleteOverrideCompetitor(Integer webrateOverrideCompetitorId) {
        crudService.delete(WebrateOverrideCompetitor.class, webrateOverrideCompetitorId);
        // Deleting an Override Competitor required a sync to occur
        if (!syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
        }

        return true;
    }
    @SuppressWarnings("unchecked")
    public List<WebrateOverrideCompetitor> getAllOverrideCompetitorByProperty(Integer propertyId) {
        List<WebrateOverrideCompetitor> webCompDetailsList;
        List<WebrateOverrideCompetitor> webCompDetailsUpdatedList = new ArrayList<WebrateOverrideCompetitor>();
        boolean activeOvrFlag = false;
        webCompDetailsList = crudService.findByNamedQuery(
                WebrateOverrideCompetitor.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());

        if (webCompDetailsList != null && !webCompDetailsList.isEmpty()) {
            for (WebrateOverrideCompetitor overrideCompetitor : webCompDetailsList) {
                activeOvrFlag = false;
                Set<WebrateOverrideCompetitorDetails> webOverCompDtlsSet = overrideCompetitor.getWebrateOverrideCompetitorDetails();
                for (WebrateOverrideCompetitorDetails overrideCompDetails : webOverCompDtlsSet) {
                    WebrateCompetitorsAccomClass wbCompAcClass = overrideCompDetails.getWebrateCompetitorsAccomClass();
                    WebrateCompetitors wc = wbCompAcClass.getWebrateCompetitor();
                    if (wc.getStatusId().intValue() == 1 && wbCompAcClass.getDemandEnabled().intValue() == 1) {
                        activeOvrFlag = true;
                    }
                }
                if (activeOvrFlag) {
                    webCompDetailsUpdatedList.add(overrideCompetitor);

                }
            }
        }

        return webCompDetailsUpdatedList;
    }

    public boolean saveCompetitorFilterData(List<WebrateOverrideCompetitor> overrideCompetitorList,
                                            Integer propertyId, Product product) {
        for (WebrateOverrideCompetitor overrideCompetitor : overrideCompetitorList) {
            overrideCompetitor.setPropertyId(propertyId);
            overrideCompetitor.setProductID(product.getId());
            Set<WebrateOverrideCompetitorDetails> overrideCompDetails = overrideCompetitor.getWebrateOverrideCompetitorDetails();
            List<WebrateOverrideCompetitorDetails> overrideCompetitorDetailsToBeDeleted = overrideCompDetails.stream()
                    .filter(competitorDetails -> (competitorDetails.getIsDeleted() != null && competitorDetails.getIsDeleted() == 1))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(overrideCompetitorDetailsToBeDeleted)) {
                webrateShoppingCleanUpService.cleanupWebrateCompetitorChannelMappings(overrideCompetitorDetailsToBeDeleted);
            }
            for (WebrateOverrideCompetitorDetails overrideDetails : overrideCompDetails) {
                if (overrideDetails.getIsDeleted() != null && overrideDetails.getIsDeleted().intValue() == 1) {
                    deleteOverrideCompetitor(overrideCompetitor.getId());
                }
            }
        }
        saveCompetitorFilterDataHelper(overrideCompetitorList, propertyId, product);
        return true;
    }

    @SuppressWarnings("squid:S3776")
    public boolean saveCompetitorFilterDataHelper(List<WebrateOverrideCompetitor> overrideCompetitorList, Integer propertyId, Product product) {
        boolean syncRequired = false;
        for (WebrateOverrideCompetitor overrideCompetitor : overrideCompetitorList) {
            overrideCompetitor.setPropertyId(propertyId);
            overrideCompetitor.setProductID(product.getId());

            if (!syncRequired && isSyncRequired(overrideCompetitor)) {
                syncRequired = true;
            }

            Set<WebrateOverrideCompetitorDetails> overrideCompDetails = overrideCompetitor.getWebrateOverrideCompetitorDetails();
            for (WebrateOverrideCompetitorDetails overrideDetails : overrideCompDetails) {
                if (overrideDetails.getId() == null) {
                    AccomClass accomClass = overrideDetails.getWebrateCompetitorsAccomClass().getAccomClass();
                    WebrateCompetitors webrateComp = overrideDetails.getWebrateCompetitorsAccomClass().getWebrateCompetitor();
                    WebrateCompetitorsAccomClass compAccomClass = crudService.findByNamedQuerySingleResult(WebrateCompetitorsAccomClass.BY_ACCOM_ID_COMP_ID_AND_PRODUCT_ID, QueryParameter.with("accomClassId", accomClass.getId()).and("compId", webrateComp.getId()).and("productId", product.getId()).parameters());

                    overrideDetails.setProductID(product.getId());
                    overrideDetails.setWebrateCompetitorsAccomClass(compAccomClass);
                    overrideDetails.setWebrateOverrideCompetitor(overrideCompetitor);
                }
                if (overrideDetails.getIsDeleted() != null && overrideDetails.getIsDeleted().intValue() != 1) {
                    if (null == overrideCompetitor.getCreateDate()) {
                        overrideCompetitor.setCreateDate(new Date());
                    }
                    if (null == overrideCompetitor.getCreatedByUserId() && StringUtils.isNotEmpty(PacmanWorkContextHelper.getUserId())) {
                        try {
                            overrideCompetitor.setCreatedByUserId(Integer.valueOf(PacmanWorkContextHelper.getUserId()));
                        } catch (NumberFormatException nfe) {
                            overrideCompetitor.setCreatedByUserId(Integer.valueOf(1));
                        }

                    }
                    crudService.save(overrideCompetitor);
                }
            }
        }

        if (syncRequired && !syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
        }

        return true;
    }

    /**
     * @param overrideCompetitor
     * @param overrideDetails
     */
    public void validateFeatureToggle(WebrateOverrideCompetitor overrideCompetitor,
                               WebrateOverrideCompetitorDetails overrideDetails) {
        if (overrideDetails.getIsDeleted() != null && overrideDetails.getIsDeleted().intValue() != 1) {
            if (StringUtils.isNotEmpty(PacmanWorkContextHelper.getUserId())) {
                try {
                    overrideCompetitor.setCreatedByUserId(Integer.valueOf(PacmanWorkContextHelper.getUserId()));
                } catch (NumberFormatException nfe) {
                    overrideCompetitor.setCreatedByUserId(Integer.valueOf(1));
                }

            }
            crudService.save(overrideCompetitor);
        }
    }

    public boolean isSyncRequired(WebrateOverrideCompetitor webrateOverrideCompetitor) {
        // If there is a new WebrateOverrideCompetitor a sync is required
        if (webrateOverrideCompetitor.getId() == null) {
            return true;
        }

        WebrateOverrideCompetitor existingWebrateOverrideCompetitor = crudService.find(WebrateOverrideCompetitor.class, webrateOverrideCompetitor.getId());
        if (existingWebrateOverrideCompetitor == null) {
            return false;
        }


        // If the state or end dates have changed, a sync is required
        EqualsBuilder startOrEndDateEqualsBuilder = new EqualsBuilder();
        startOrEndDateEqualsBuilder.append(webrateOverrideCompetitor.getCompetitorOverrideStartDT(), existingWebrateOverrideCompetitor.getCompetitorOverrideStartDT());
        startOrEndDateEqualsBuilder.append(webrateOverrideCompetitor.getCompetitorOverrideEndDT(), existingWebrateOverrideCompetitor.getCompetitorOverrideEndDT());
        if (!startOrEndDateEqualsBuilder.isEquals()) {
            return true;
        }

        // If the DOW level configuration have changed, a sync is required
        EqualsBuilder dowConfigurationEqualsBuilder = new EqualsBuilder();
        dowConfigurationEqualsBuilder.append(webrateOverrideCompetitor.getIgnoreCompetitorDataOnMonday(), existingWebrateOverrideCompetitor.getIgnoreCompetitorDataOnMonday());
        dowConfigurationEqualsBuilder.append(webrateOverrideCompetitor.getIgnoreCompetitorDataOnTuesday(), existingWebrateOverrideCompetitor.getIgnoreCompetitorDataOnTuesday());
        dowConfigurationEqualsBuilder.append(webrateOverrideCompetitor.getIgnoreCompetitorDataOnWednesday(), existingWebrateOverrideCompetitor.getIgnoreCompetitorDataOnWednesday());
        dowConfigurationEqualsBuilder.append(webrateOverrideCompetitor.getIgnoreCompetitorDataOnThursday(), existingWebrateOverrideCompetitor.getIgnoreCompetitorDataOnThursday());
        dowConfigurationEqualsBuilder.append(webrateOverrideCompetitor.getIgnoreCompetitorDataOnFriday(), existingWebrateOverrideCompetitor.getIgnoreCompetitorDataOnFriday());
        dowConfigurationEqualsBuilder.append(webrateOverrideCompetitor.getIgnoreCompetitorDataOnSaturday(), existingWebrateOverrideCompetitor.getIgnoreCompetitorDataOnSaturday());
        dowConfigurationEqualsBuilder.append(webrateOverrideCompetitor.getIgnoreCompetitorDataOnSunday(), existingWebrateOverrideCompetitor.getIgnoreCompetitorDataOnSunday());
        if (!dowConfigurationEqualsBuilder.isEquals()) {
            return true;
        }

        // Check to see if it has a new or changed detail record
        Set<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetails = webrateOverrideCompetitor.getWebrateOverrideCompetitorDetails();
        for (WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetail : webrateOverrideCompetitorDetails) {

            // If it's new, a sync is required
            if (webrateOverrideCompetitorDetail.getId() == null) {
                return true;
            }

            WebrateOverrideCompetitorDetails existingWebrateOverrideCompetitorDetail = crudService.find(WebrateOverrideCompetitorDetails.class, webrateOverrideCompetitorDetail.getId());

            EqualsBuilder competitorDetail = new EqualsBuilder();
            competitorDetail.append(webrateOverrideCompetitorDetail.getWebrateCompetitorsAccomClass(), existingWebrateOverrideCompetitorDetail.getWebrateCompetitorsAccomClass());
            competitorDetail.append(webrateOverrideCompetitorDetail.getWebrateOverrideCompetitor(), existingWebrateOverrideCompetitorDetail.getWebrateOverrideCompetitor());

            // If competitorDetail is not equal a sync is required
            if (!competitorDetail.isEquals()) {
                return true;
            }
        }

        return false;
    }

    public void setSyncEventAggregatorService(SyncEventAggregatorService syncEventAggregatorService) {
        this.syncEventAggregatorService = syncEventAggregatorService;
    }

    public void setConfigParamsService(PacmanConfigParamsService configParamsService) {
        this.configParamsService = configParamsService;
    }

    public void setPropertyService(PropertyService propertyService) {
        this.propertyService = propertyService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }
}
