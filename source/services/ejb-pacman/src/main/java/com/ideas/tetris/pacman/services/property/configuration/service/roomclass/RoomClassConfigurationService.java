package com.ideas.tetris.pacman.services.property.configuration.service.roomclass;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.RoomClassPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecordStatus;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.List;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@RoomClassConfigurationService.Qualifier
@Component
@Transactional
public class RoomClassConfigurationService extends AbstractPropertyConfigurationService {

    private static final Logger LOGGER = Logger.getLogger(RoomClassConfigurationService.class.getName());

    private static final int ROOM_CLASS_MAX_LENGTH = 50;
    private static final int ROOM_CLASS_DESCRIPTION_MAX_LENGTH = 150;


    @Override
    public PropertyConfigurationRecordType getPropertyConfigurationRecordType() {
        return PropertyConfigurationRecordType.RC;
    }

    @Override
    public List<PropertyConfigurationRecordFailure> doValidate(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId) {
        RoomClassPropertyConfigurationDto roomClassPropertyConfigurationDto = (RoomClassPropertyConfigurationDto) propertyConfigurationDto;

        List<PropertyConfigurationRecordFailure> exceptions = new ArrayList<>();

        // Validate Room Class Name
        String roomClassName = roomClassPropertyConfigurationDto.getRoomClassName();
        if (StringUtils.isEmpty(roomClassName)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Room Class Name is required"));
        } else if (roomClassName.length() > ROOM_CLASS_MAX_LENGTH) {
            roomClassPropertyConfigurationDto.setRoomClassName(StringUtils.left(roomClassName, ROOM_CLASS_MAX_LENGTH));
            exceptions.add(new PropertyConfigurationRecordFailure(ConfigurationFileRecordStatus.WARNING, "Room Class Name cannot be longer than " + ROOM_CLASS_MAX_LENGTH + " characters.  The value has been trimmed."));
        }

        // Validate Room Class Description
        String roomClassDescription = roomClassPropertyConfigurationDto.getRoomClassDescription();
        if (StringUtils.isNotEmpty(roomClassDescription) && roomClassDescription.length() > ROOM_CLASS_DESCRIPTION_MAX_LENGTH) {
            roomClassPropertyConfigurationDto.setRoomClassDescription(StringUtils.left(roomClassDescription, ROOM_CLASS_DESCRIPTION_MAX_LENGTH));
            exceptions.add(new PropertyConfigurationRecordFailure(ConfigurationFileRecordStatus.WARNING, "Room Class Description cannot be longer than " + ROOM_CLASS_DESCRIPTION_MAX_LENGTH + " characters.  The value has been trimmed."));
        }

        // Validate Display Order
        Integer displayOrder = roomClassPropertyConfigurationDto.getDisplayOrder();
        if (displayOrder == null || displayOrder < 0) {
            exceptions.add(new PropertyConfigurationRecordFailure("Display Order must contain a positive number"));
        }

        // Validate Master Accomodation Class
        if (roomClassPropertyConfigurationDto.isMasterAccommodationClass() == null) {
            exceptions.add(new PropertyConfigurationRecordFailure("Master Accommodation Class must be 'Y' or 'N'"));
        } else if (roomClassPropertyConfigurationDto.isMasterAccommodationClass()) {
            AccomClass existingMasterClass = getMasterClass(propertyId);
            if (existingMasterClass != null && !existingMasterClass.getCode().equals(roomClassPropertyConfigurationDto.getRoomClassName())) {
                exceptions.add(new PropertyConfigurationRecordFailure("Multiple Master Classes for the same Property"));
            }
        }

        return exceptions;
    }

    @SuppressWarnings("squid:S1166")
    private AccomClass getMasterClass(Integer propertyId) {
        try {
            return crudService.findByNamedQuerySingleResult(AccomClass.GET_MASTER_CLASS, QueryParameter.with("propertyId", propertyId)
                    .parameters());
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public void doHandle(PropertyConfigurationDto pcd, Integer propertyId) {
        RoomClassPropertyConfigurationDto roomClassPropertyConfigurationDto = (RoomClassPropertyConfigurationDto) pcd;
        if (!accomClassExistsForProperty(roomClassPropertyConfigurationDto.getRoomClassName(), propertyId)) {
            AccomClass accomClass = new AccomClass();
            accomClass.setPropertyId(propertyId);
            accomClass.setCode(roomClassPropertyConfigurationDto.getRoomClassName());
            accomClass.setName(roomClassPropertyConfigurationDto.getRoomClassName());
            accomClass.setStatusId(Constants.ACTIVE_STATUS_ID);
            accomClass.setSystemDefault(0);
            accomClass.setDescription(roomClassPropertyConfigurationDto.getRoomClassDescription());
            accomClass.setMasterClass(BooleanUtils.toIntegerObject(roomClassPropertyConfigurationDto.isMasterAccommodationClass()));
            accomClass.setViewOrder(roomClassPropertyConfigurationDto.getDisplayOrder());
            Integer rankOrder = getAccomClassRankOrder(propertyId);
            accomClass.setRankOrder(rankOrder + 1);
            LOGGER.info("Creating AccomClass: " + roomClassPropertyConfigurationDto.getRoomClassName() + " for Property: " + pcd.getPropertyCode());
            LOGGER.debug("EntityManager: " + crudService.getEntityManager());
            crudService.save(accomClass);
        }
    }

    private Integer getAccomClassRankOrder(Integer propertyId) {
        Integer maxRank = crudService.findByNamedQuerySingleResult(AccomClass.MAX_RANK, QueryParameter.with("propertyId", propertyId).parameters());
        if (maxRank != null) {
            return maxRank;
        } else {
            return 0;
        }
    }

    @SuppressWarnings("unchecked")
    private boolean accomClassExistsForProperty(String code, Integer propertyId) {
        List<AccomClass> results = crudService.findByNamedQuery(AccomClass.BY_CODE, QueryParameter.with("propertyId", propertyId).and("code", code).parameters());
        return results != null && !results.isEmpty();
    }

    public AccomClass findAccomClass(Integer propertyId, String name) {
        return (AccomClass) crudService.findByNamedQuerySingleResult(AccomClass.BY_NAME, QueryParameter.with("propertyId", propertyId).and("name", name).parameters());
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
