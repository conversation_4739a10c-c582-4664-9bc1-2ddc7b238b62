package com.ideas.tetris.pacman.services.budget;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.budget.converter.BudgetDataEntityConverter;
import com.ideas.tetris.pacman.services.budget.converter.UserForecastDataEntityConverter;
import com.ideas.tetris.pacman.services.budget.dto.BudgetDataDto;
import com.ideas.tetris.pacman.services.budget.dto.BudgetDataValidationError;
import com.ideas.tetris.pacman.services.budget.entity.BudgetConfig;
import com.ideas.tetris.pacman.services.budget.entity.BudgetData;
import com.ideas.tetris.pacman.services.budget.entity.BudgetLevel;
import com.ideas.tetris.pacman.services.budget.entity.UserForecastData;
import com.ideas.tetris.pacman.services.budget.model.BudgetUserForecastEntityModel;
import com.ideas.tetris.pacman.services.businessgroup.service.BusinessGroupService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantProperty;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dashboard.DashboardMetrics2Cache;
import com.ideas.tetris.pacman.services.datafeed.dto.BudgetDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.UserForecastDataDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.UserForecastDTO;
import com.ideas.tetris.pacman.services.datafeed.service.DatafeedRequest;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessType;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.services.budget.entity.BudgetData.BUDGET_DATA_BY_DATE_RANGE_FOR_BUSINESS_GROUP;
import static com.ideas.tetris.pacman.services.budget.entity.BudgetData.BUDGET_DATA_BY_DATE_RANGE_FOR_BUSINESS_TYPE;

@Component
@Transactional
public class BudgetDataService {
    private static final Logger LOGGER = Logger.getLogger(BudgetDataService.class);
    private static final double MAX_ROOM_REVENUE_VALUE = 99999999999999.99;

    private static final String IS_NOT_A_VALID_NUMBER = " is not a valid number";
    private static final String IS_NOT_A_VALID_DATE = " is not a valid date";
    private static final String START_DATE = "startDate";
    private static final String END_DATE = "endDate";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    @Autowired
    AccommodationService accommodationService;

    @Autowired
	private DashboardMetrics2Cache dashboardMetrics2Cache;

    @Autowired
	private BudgetService budgetService;

    @Autowired
	protected BusinessGroupService businessGroupService;

    public List<BudgetDataDto> getBudgetDataFromWorkbook(InputStream inputStream) {
        return getBudgetUserForecastDataFromWorkbook(inputStream, Constants.BUDGET);
    }

    public List<BudgetDataDto> getBudgetUserForecastDataFromWorkbook(InputStream inputStream, String sheetName) {
        Workbook workbook = getWorkbook(inputStream);
        List<BudgetDataDto> results = new ArrayList<>();

        Sheet budgetUserForecastSheet = workbook.getSheet(sheetName);
        if (budgetUserForecastSheet == null) {
            return results;
        }
        Iterator<Row> rowIterator = budgetUserForecastSheet.iterator();
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            if (ignoreRows(row)) {
                continue;  // ignore bad data
            }

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
            LocalDate occupancyDate = LocalDate.parse(row.getCell(INDEX_OCCUPANCY_DATE).toString().trim(), formatter);
            String marketSegment = row.getCell(INDEX_SEGMENT).toString().trim();
            Integer roomsSold = new BigDecimal(row.getCell(INDEX_ROOM_SOLD).toString().trim()).intValue();
            BigDecimal roomRevenue = new BigDecimal(row.getCell(INDEX_ROOM_REVENUE).toString().trim());
            createBudgetDataDto(results, occupancyDate, marketSegment, roomsSold, roomRevenue);
        }
        return results;
    }

    private void createBudgetDataDto(List<BudgetDataDto> results, LocalDate occupancyDate, String marketSegment, Integer roomsSold, BigDecimal roomRevenue) {
        BudgetDataDto budgetDataDto = new BudgetDataDto();
        budgetDataDto.setOccupancyDate(occupancyDate);
        budgetDataDto.setSegment(marketSegment);
        budgetDataDto.setRoomsSold(roomsSold);
        budgetDataDto.setRoomRevenue(roomRevenue);
        results.add(budgetDataDto);
    }

    private boolean ignoreRows(Row row) {
        return row.getRowNum() == 0 || isEmptyRow(row) || rowNotValid(row);
    }

    private Workbook getWorkbook(InputStream inputStream) {
        Workbook workbook;
        try {
            workbook = new XSSFWorkbook(inputStream);
        } catch (Exception e) {
            LOGGER.error("Error while reading BudgetData file using stream");
            IOUtils.closeQuietly(inputStream);
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Exception reading BudgetData file using stream ", e);
        }
        return workbook;
    }

    private boolean rowNotValid(Row row) {
        if (isBlank(row.getCell(INDEX_OCCUPANCY_DATE)) || isBlank(row.getCell(INDEX_SEGMENT)) || isBlank(row.getCell(INDEX_ROOM_SOLD)) || isBlank(row.getCell(INDEX_ROOM_REVENUE))) {
            return true;
        }
        try {
            row.getCell(INDEX_OCCUPANCY_DATE).getDateCellValue();
        } catch (IllegalStateException e) {
            LOGGER.error(row.getCell(INDEX_OCCUPANCY_DATE).toString().trim() + " is not a date cell", e);
            return true;
        } catch (NumberFormatException e) {
            LOGGER.error(row.getCell(INDEX_OCCUPANCY_DATE).toString().trim() + IS_NOT_A_VALID_NUMBER, e);
            return true;
        }
        try {
            new BigDecimal(row.getCell(INDEX_ROOM_SOLD).toString());
        } catch (NumberFormatException e) {
            LOGGER.error(row.getCell(INDEX_ROOM_SOLD).toString().trim() + IS_NOT_A_VALID_NUMBER, e);
            return true;
        }
        try {
            new BigDecimal(row.getCell(INDEX_ROOM_REVENUE).toString());
        } catch (NumberFormatException e) {
            LOGGER.error(row.getCell(INDEX_ROOM_REVENUE).toString().trim() + IS_NOT_A_VALID_NUMBER, e);
            return true;
        }
        return false;
    }

    private boolean isBlank(Cell cell) {
        return cell == null || StringUtils.isEmpty(cell.toString());
    }

    private boolean isEmptyRow(Row row) {
        for (Cell cell : row) {
            if ((cell.toString() != null) && cell.toString().trim().length() > 0) {
                return false;
            }
        }
        return true;
    }


    public String loadBudgetDataIntoPacman(List<BudgetDataDto> data) {
        List<BudgetData> entities = new ArrayList<BudgetData>();
        int newCount = 0;
        int existingCount = 0;
        Map<String, Integer> businessTypeMap = getBusinessTypeMap();
        Map<LocalDate, Set<BudgetData>> budgetDateSetMap = buildExistingDataMap(tenantCrudService.findAll(BudgetData.class));
        for (BudgetDataDto dto : data) {
            BudgetData entity = checkForExistingData(new BudgetData(dto, businessTypeMap.get(dto.getSegment().toLowerCase())), budgetDateSetMap);
            if (entity.getId() == null) {
                newCount++;
            } else {
                entity.setRoomsSold(dto.getRoomsSold());
                entity.setRoomRevenue(dto.getRoomRevenue());
                existingCount++;
            }
            entities.add(entity);
        }
        tenantCrudService.save(entities);

        dashboardMetrics2Cache.handleRemoveKeysForProperty(PacmanWorkContextHelper.getPropertyId());

        StringBuilder buffer = new StringBuilder().append("Saved ").append(newCount).append(" new rows and updated ").append(existingCount).append(" existing rows in Budget Data table");
        return buffer.toString();
    }


    public void loadUserForecastIntoPacman(List<UserForecastDTO> data) {
        List<UserForecastData> entities = new ArrayList<>();
        BudgetLevel budgetLevel = new BudgetLevel();
        budgetLevel.setId(2);
        for (UserForecastDTO dto : data) {
            UserForecastData entity = new UserForecastData();
            entity.setBudgetLevel(budgetLevel);
            entity.setBusinessGroupId(dto.getGroupBusinessId());
            entity.setOccupancyDate(new org.joda.time.LocalDate(dto.getOccupancyDate()));
            entity.setRoomsSold(dto.getForecastRooms());
            entity.setRoomRevenue(dto.getRoomRevenue());
            entities.add(entity);
        }
        saveForeCastData(entities);
    }

    public void loadBudgetDataFromRevplanIntoG3(List<BudgetUserForecastEntityModel> data) {
        if (CollectionUtils.isNotEmpty(data)) {
            Map<String, BudgetData> currentEntities = getLocalDateAndSegmentIdToBudgetDataMap();
            BudgetDataEntityConverter converter = new BudgetDataEntityConverter(currentEntities, getBusinessGroupNameToIds());
            List<BudgetData> entities = data.stream().map(converter::convert).collect(Collectors.toList());
            saveBudgetData(entities);
        }
    }

    public void loadUserForecastDataFromRevplanIntoG3(List<BudgetUserForecastEntityModel> data) {
        if (CollectionUtils.isNotEmpty(data)) {
            Map<String, UserForecastData> currentEntities = getLocalDateAndSegmentIdToUserForecastDataMap();
            UserForecastDataEntityConverter converter = new UserForecastDataEntityConverter(currentEntities, getBusinessGroupNameToIds());
            BudgetLevel budgetLevel = budgetService.getUserForecastConfig().getBudgetLevel();
            List<UserForecastData> entities = data.stream().map(input -> {
                UserForecastData entity = converter.convert(input);
                entity.setBudgetLevel(budgetLevel);
                return entity;
            }).collect(Collectors.toList());
            saveForeCastData(entities);
        }
    }


    public void deleteUserForecast() {
        tenantCrudService.deleteAll(UserForecastData.class);
    }


    public void deleteBudgetData() {
        tenantCrudService.deleteAll(BudgetData.class);
    }

    public List<BudgetDTO> getBudgetDetails(DatafeedRequest datafeedRequest) {
        BudgetConfig budgetConfig = budgetService.getBudgetConfig();
        if (budgetConfig == null) {
            return Collections.emptyList();
        }
        return isBusinessTypeLevel(budgetConfig) ? getDataFor(BUDGET_DATA_BY_DATE_RANGE_FOR_BUSINESS_TYPE, datafeedRequest)
                : getDataFor(BUDGET_DATA_BY_DATE_RANGE_FOR_BUSINESS_GROUP, datafeedRequest);
    }

    public List<UserForecastDataDTO> getForecastDetails(DatafeedRequest datafeedRequest) {
        BudgetConfig budgetConfig = budgetService.getUserForecastConfig();
        if (budgetConfig == null) {
            return Collections.emptyList();
        }
        return isBusinessTypeLevel(budgetConfig) ? getUserForecastData(UserForecastData.USER_FORECAST_DATA_BY_BUSINESS_TYPE_BETWEEN_STARTDATE_AND_ENDDATE, datafeedRequest)
                : getUserForecastData(UserForecastData.USER_FORECAST_DATA_BETWEEN_STARTDATE_AND_ENDDATE, datafeedRequest);
    }

    private List<BudgetDTO> getDataFor(String query, DatafeedRequest datafeedRequest) {
        return tenantCrudService.findByNamedQuery(query, getParameters(datafeedRequest),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize());
    }

    private List<UserForecastDataDTO> getUserForecastData(String query, DatafeedRequest datafeedRequest) {
        return tenantCrudService.findByNamedQuery(query, getParameters(datafeedRequest),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize());
    }

    private Map<String, Object> getParameters(DatafeedRequest datafeedRequest) {
        return QueryParameter.with(START_DATE, DateUtil.convertJavaUtilDateToLocalDate(datafeedRequest.getStartDate(), true))
                .and(END_DATE, DateUtil.convertJavaUtilDateToLocalDate(datafeedRequest.getEndDate(), true))
                .parameters();
    }

    private boolean isBusinessTypeLevel(BudgetConfig budgetConfig) {
        final String budgetLevel = budgetConfig.getBudgetLevel().getBudgetLevel();
        return BudgetLevel.BUDGET_LEVEL.BUSINESS_TYPE.getValue().equals(budgetLevel);
    }

    private Map<String, Integer> getBusinessTypeMap() {
        Map<String, Integer> businessTypeMap = new HashMap<>();
        List<BusinessType> businessTypes = tenantCrudService.findAll(BusinessType.class);
        for (BusinessType businessType : businessTypes) {
            businessTypeMap.put(businessType.getName().toLowerCase(), businessType.getId());
        }
        return businessTypeMap;
    }

    private BudgetData checkForExistingData(BudgetData entity, Map<LocalDate, Set<BudgetData>> existingDataMap) {
        LocalDate date = entity.getOccupancyDate();
        Set<BudgetData> dailyResults = existingDataMap.get(date);
        if (dailyResults == null) {
            return entity;
        }
        for (BudgetData existingEntity : dailyResults) {
            if (existingEntity.getSegmentID().equals(entity.getSegmentID())) {
                return existingEntity;
            }
        }
        return entity;
    }

    private Map<LocalDate, Set<BudgetData>> buildExistingDataMap(List<BudgetData> entities) {
        Map<LocalDate, Set<BudgetData>> map = new HashMap<LocalDate, Set<BudgetData>>();
        for (BudgetData entity : entities) {
            LocalDate date = entity.getOccupancyDate();
            Set<BudgetData> data = map.computeIfAbsent(date, localDate -> new HashSet<>());
            data.add(entity);
        }
        return map;
    }

    public List<UserForecastData> getUserForecastData(LocalDate startDate, LocalDate endDate) {
        return tenantCrudService.findByNamedQuery(UserForecastData.DATA_BETWEEN_STARTDATE_AND_ENDDATE,
                QueryParameter
                        .with(START_DATE, JavaLocalDateUtils.toJodaLocalDate(startDate))
                        .and(END_DATE, JavaLocalDateUtils.toJodaLocalDate(endDate))
                        .and("budgetLevelId", budgetService.getUserForecastConfig().getBudgetLevel().getId()).parameters());
    }

    public List<BudgetData> getBudgetDataForBusinessViews(LocalDate startDate, LocalDate endDate) {
        return tenantCrudService.findByNamedQuery(BudgetData.BUDGETDATA_BETWEEN_STARTDATE_AND_ENDDATE,
                QueryParameter
                        .with(START_DATE, startDate)
                        .and(END_DATE, endDate).parameters());
    }


    public List<BudgetData> getBudgetData(LocalDate startDate, LocalDate endDate) {
        List<BudgetData> results = new ArrayList<BudgetData>();
        Map<LocalDate, Set<BudgetData>> budgetDateSetMap = buildExistingDataMap(tenantCrudService.findByNamedQuery(BudgetData.BUDGETDATA_BETWEEN_STARTDATE_AND_ENDDATE, QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).parameters()));
        List<BusinessType> businessTypes = getBusinessType();
        LocalDate occupancyDate = startDate;
        while (!occupancyDate.isAfter(endDate)) {
            for (BusinessType businessType : businessTypes) {
                BudgetData entity = new BudgetData();
                entity.setSegmentID(businessType.getId());
                entity.setOccupancyDate(occupancyDate);
                entity.setRoomRevenue(BigDecimal.ZERO);
                entity.setRoomsSold(0);
                entity = checkForExistingData(entity, budgetDateSetMap);
                results.add(entity);
            }
            occupancyDate = occupancyDate.plusDays(1);
        }
        return results;
    }

    public List<UserForecastData> getForecastData(LocalDate startDate, LocalDate endDate) {
        List<UserForecastData> results = new ArrayList<>();
        Map<LocalDate, Set<UserForecastData>> budgetDateSetMap = buildExistingUserForecastDataMap(getUserForecastData(startDate, endDate));
        List<BusinessType> businessTypes = getBusinessType();
        LocalDate occupancyDate = startDate;
        while (!occupancyDate.isAfter(endDate)) {
            for (BusinessType businessType : businessTypes) {
                UserForecastData entity = new UserForecastData();
                entity.setBusinessGroupId(businessType.getId());
                entity.setOccupancyDate(JavaLocalDateUtils.toJodaLocalDate(occupancyDate));
                entity.setRoomRevenue(BigDecimal.ZERO);
                entity.setRoomsSold(0);
                entity = checkForExistingUserForecastData(entity, budgetDateSetMap);
                results.add(entity);
            }
            occupancyDate = occupancyDate.plusDays(1);
        }
        return results;
    }

    public List<BusinessType> getBusinessType() {
        return tenantCrudService.findAll(BusinessType.class);
    }

    public TenantProperty getProperty() {
        return tenantCrudService.find(TenantProperty.class, PacmanWorkContextHelper.getPropertyId());
    }


    public List<BudgetDataValidationError> validateBudgetData(List<BudgetDataValidationError> results, InputStream inputStream, List<String> columnOrder) {
        return validateBudgetData(results, inputStream, columnOrder, Constants.BUDGET);
    }

    public List<BudgetDataValidationError> validateBudgetData(List<BudgetDataValidationError> results, InputStream inputStream, List<String> columnOrder, String sheetName) {
        Workbook workbook = getWorkbook(inputStream);
        Sheet budgetSheet = workbook.getSheet(sheetName);
        if (budgetSheet == null) {
            results.add(new BudgetDataValidationError(null, null, "Worksheet " + sheetName + " does not exist", null));
            return results;
        }
        LocalDate startDate = null;
        LocalDate endDate = null;
        Set<String> segments = getSegments();
        Map<LocalDate, List<BudgetDataDto>> dateMap = new HashMap<LocalDate, List<BudgetDataDto>>();
        Map<LocalDate, Integer> occupancyDateIndexMap = new HashMap<LocalDate, Integer>();
        Set<String> budgetSegments = new HashSet<String>();
        List<BudgetDataDto> budgetDataDtos = new ArrayList<BudgetDataDto>();
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("dd-MMM-yyyy");
        Iterator<Row> rowIterator = budgetSheet.iterator();
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            if (isEmptyRow(row) || row.getRowNum() == 0) {
                continue; // ignore empty rows
            }
            if (row.getRowNum() == 1) {
                if (!validatePropertyId(row.getCell(1))) {
                    results.add(new BudgetDataValidationError(null, null, "Property ID does not match", getIncrementVal(row.getRowNum())));
                    return results;
                } else {
                    continue;
                }
            }

            if (row.getRowNum() == 2) {
                if (!validateColumnOrder(row, columnOrder)) {
                    results.add(new BudgetDataValidationError(null, null, "Columns  order does not match", getIncrementVal(row.getRowNum())));
                    return results;
                } else {
                    continue;
                }
            }
            if (!validateRow(row, results)) {
                continue;
            }

            LocalDate occupancyDate = LocalDate.parse(row.getCell(INDEX_OCCUPANCY_DATE).toString().trim(), fmt);
            String segment = row.getCell(INDEX_SEGMENT).toString().trim();
            Integer roomsSold = new BigDecimal(row.getCell(INDEX_ROOM_SOLD).toString().trim()).intValue();
            BigDecimal roomRevenue = new BigDecimal(row.getCell(INDEX_ROOM_REVENUE).toString().trim());
            BudgetDataDto budgetDataDto = new BudgetDataDto();
            budgetDataDto.setOccupancyDate(occupancyDate);
            budgetDataDto.setSegment(segment);
            budgetDataDto.setRoomsSold(roomsSold);
            budgetDataDto.setRoomRevenue(roomRevenue);
            validateBudgetDataDto(budgetDataDto, results, segments, getIncrementVal(row.getRowNum()));
            budgetDataDtos.add(budgetDataDto);
            budgetSegments.add(segment);
            List<BudgetDataDto> budgetDataDtoForDate = dateMap.computeIfAbsent(occupancyDate, localDate -> {
                ArrayList budgetDataForDate = new ArrayList();
                occupancyDateIndexMap.put(occupancyDate, getIncrementVal(row.getRowNum()));
                return budgetDataForDate;
            });
            budgetDataDtoForDate.add(budgetDataDto);
            if (startDate == null || occupancyDate.isBefore(startDate)) {
                startDate = occupancyDate;
            }
            if (endDate == null || occupancyDate.isAfter(endDate)) {
                endDate = occupancyDate;
            }
        }

        if (budgetDataDtos.isEmpty()) {
            results.add(new BudgetDataValidationError(new BudgetDataDto(), "No data found", null));
            return results;
        }
        checkDates(startDate, endDate, dateMap, segments, results, occupancyDateIndexMap);

        return results;
    }

    public Map<String, UserForecastData> getLocalDateAndSegmentIdToUserForecastDataMap() {
        List<UserForecastData> entities = tenantCrudService.findByNamedQuery(UserForecastData.DATA_FOR_GIVEN_BUDGET_LEVEL,
                QueryParameter.with("budgetLevelId", budgetService.getUserForecastConfig().getBudgetLevel().getId()).parameters());
        return entities.stream()
                .collect(Collectors.toMap(e -> e.getOccupancyDate().toString("yyyyMMdd") + "_" + e.getBusinessGroupId(), e -> e));
    }

    public Map<String, BudgetData> getLocalDateAndSegmentIdToBudgetDataMap() {
        return tenantCrudService.findAll(BudgetData.class).stream()
                .collect(Collectors.toMap(e -> DateTimeFormatter.ofPattern("yyyyMMdd").format(e.getOccupancyDate()) + "_" + e.getSegmentID(), e -> e));
    }

    public void saveForeCastData(List<UserForecastData> entities) {
        tenantCrudService.save(entities);
        dashboardMetrics2Cache.removeUserForecastRelatedKeys();
    }


    public void saveBudgetData(List<BudgetData> entities) {
        tenantCrudService.save(entities);
        dashboardMetrics2Cache.handleRemoveKeysForProperty(PacmanWorkContextHelper.getPropertyId());
    }

    public Map<String, Integer> getBusinessGroupNameToIds() {
        return businessGroupService.getAllBusinessGroupDetails()
                .stream()
                .collect(Collectors.toMap(group -> group.getName().toLowerCase(), BusinessGroup::getId));
    }

    public Set<String> getSegments() {
        Set<String> results = new HashSet<String>();
        for (BusinessType businessType : getBusinessType()) {
            results.add(businessType.getName());
        }
        return results;
    }

    private boolean validateRow(Row row, List<BudgetDataValidationError> errors) {
        boolean pass = true;
        LocalDate occupancyDate = null;
        String segment = null;
        if (isBlank(row.getCell(INDEX_OCCUPANCY_DATE))) {
            errors.add(new BudgetDataValidationError(null, null, "Blank occupancy date", getIncrementVal(row.getRowNum())));
            pass = false;
        } else {
            try {
                occupancyDate = DateUtil.convertJavaUtilDateToLocalDate(row.getCell(INDEX_OCCUPANCY_DATE).getDateCellValue(), true);
            } catch (IllegalStateException e) {
                errors.add(new BudgetDataValidationError(null, null, row.getCell(INDEX_OCCUPANCY_DATE).toString().trim() + " is not a date column", getIncrementVal(row.getRowNum())));
                LOGGER.error(row.getCell(INDEX_OCCUPANCY_DATE).toString().trim() + IS_NOT_A_VALID_DATE, e);
                pass = false;
            } catch (NumberFormatException e) {
                errors.add(new BudgetDataValidationError(null, null, row.getCell(INDEX_OCCUPANCY_DATE).toString().trim() + IS_NOT_A_VALID_DATE, getIncrementVal(row.getRowNum())));
                LOGGER.error(row.getCell(INDEX_OCCUPANCY_DATE).toString().trim() + IS_NOT_A_VALID_DATE, e);
                pass = false;
            }
        }
        if (isBlank(row.getCell(INDEX_SEGMENT))) {
            errors.add(new BudgetDataValidationError(occupancyDate, null, "Blank market segment", getIncrementVal(row.getRowNum())));
            pass = false;
        } else {
            segment = row.getCell(INDEX_SEGMENT).toString().trim();
        }
        if (!validateRoomSold(errors, row.getCell(INDEX_ROOM_SOLD), occupancyDate, segment)) {
            pass = false;
        }
        if (!validateRoomRevenue(errors, row.getCell(INDEX_ROOM_REVENUE), occupancyDate, segment)) {
            pass = false;
        }

        return pass;
    }


    private boolean validateRoomSold(List<BudgetDataValidationError> errors, Cell roomSoldCell, LocalDate occupancyDate, String segment) {
        if (isBlank(roomSoldCell)) {
            errors.add(new BudgetDataValidationError(occupancyDate, segment, "Blank rooms sold", getIncrementVal(roomSoldCell == null ? 0 : roomSoldCell.getRowIndex())));
            return false;
        } else {
            try {

                BigDecimal roomsSold = new BigDecimal(roomSoldCell.toString());
                if (roomsSold.compareTo(BigDecimal.ZERO) < 0) {
                    errors.add(new BudgetDataValidationError(occupancyDate, segment, roomSoldCell.toString().trim() + " is not a non-negative number", getIncrementVal(roomSoldCell.getRowIndex())));
                } else if (roomsSold.compareTo(BigDecimal.valueOf(MAX_ROOM_REVENUE_VALUE)) > 0) {
                    errors.add(new BudgetDataValidationError(occupancyDate, segment, "Room  solds cannot be greater than 99999999999999.99", getIncrementVal(roomSoldCell.getRowIndex())));
                }
            } catch (NumberFormatException e) {
                errors.add(new BudgetDataValidationError(occupancyDate, segment, roomSoldCell.toString().trim() + IS_NOT_A_VALID_NUMBER, getIncrementVal(roomSoldCell.getRowIndex())));
                LOGGER.error(roomSoldCell.toString().trim() + IS_NOT_A_VALID_NUMBER, e);
                return false;
            }
        }
        return true;
    }

    private boolean validateRoomRevenue(List<BudgetDataValidationError> errors, Cell roomRevenueCell, LocalDate occupancyDate, String segment) {
        if (isBlank(roomRevenueCell)) {
            errors.add(new BudgetDataValidationError(occupancyDate, segment, "Blank room revenue", getIncrementVal(roomRevenueCell == null ? 0 : roomRevenueCell.getRowIndex())));
            return false;
        } else {
            try {
                BigDecimal revenue = BigDecimal.valueOf(Double.parseDouble(roomRevenueCell.toString()));
                if (revenue.compareTo(BigDecimal.valueOf(SystemConfig.getMinRoomRevenueValue())) < 0) {
                    errors.add(new BudgetDataValidationError(occupancyDate, segment, "Room revenue cannot be less than " + new DecimalFormat("#").format(SystemConfig.getMinRoomRevenueValue()), getIncrementVal(roomRevenueCell.getRowIndex())));
                } else if (revenue.compareTo(BigDecimal.valueOf(MAX_ROOM_REVENUE_VALUE)) > 0) {
                    errors.add(new BudgetDataValidationError(occupancyDate, segment, "Room revenue cannot be greater than 100000000000000", getIncrementVal(roomRevenueCell.getRowIndex())));
                }
            } catch (NumberFormatException e) {
                errors.add(new BudgetDataValidationError(occupancyDate, segment, roomRevenueCell.toString().trim() + IS_NOT_A_VALID_NUMBER, getIncrementVal(roomRevenueCell.getRowIndex())));
                LOGGER.error(roomRevenueCell.toString().trim() + IS_NOT_A_VALID_NUMBER, e);
                return false;
            }
        }
        return true;
    }


    private void validateBudgetDataDto(BudgetDataDto budgetDataDto, List<BudgetDataValidationError> errors, Set<String> segments, Integer rowNum) {
        if (!containsCaseInsensitive(segments, budgetDataDto.getSegment())) {
            errors.add(new BudgetDataValidationError(budgetDataDto, "Segment does not exist", rowNum));
        }
    }

    private boolean containsCaseInsensitive(Set<String> set, String data) {
        for (String candidate : set) {
            if (candidate.equalsIgnoreCase(data)) {
                return true;
            }
        }
        return false;
    }

    private boolean validatePropertyId(Cell propertyIdcell) {
        if (propertyIdcell != null && propertyIdcell.getCellType() == Cell.CELL_TYPE_STRING && PacmanWorkContextHelper.getPropertyId().toString().equalsIgnoreCase(propertyIdcell.getStringCellValue())) {
            return true;
        }
        return propertyIdcell != null && propertyIdcell.getCellType() == Cell.CELL_TYPE_NUMERIC && PacmanWorkContextHelper.getPropertyId() == Double.valueOf(propertyIdcell.getNumericCellValue()).intValue();
    }

    private void checkDates(LocalDate startDate, LocalDate endDate, Map<LocalDate, List<BudgetDataDto>> dateMap,
                            Set<String> segments, List<BudgetDataValidationError> errors, Map<LocalDate, Integer> occupancyDateIndexMap) {
        if (startDate != null && endDate != null) {
            checkMaxBudgetPeriod(startDate, endDate, errors);
            LocalDate occupancyDate = startDate;
            int capacity = getTotalHotelCapacity();
            while (!occupancyDate.isAfter(endDate)) {
                List<BudgetDataDto> budgetDataDtos = dateMap.get(occupancyDate);
                if (budgetDataDtos == null) {
                    errors.add(new BudgetDataValidationError(occupancyDate, null, "Missing occupancy date", occupancyDateIndexMap.get(occupancyDate)));
                } else {
                    checkForDuplicateSegments(budgetDataDtos, errors, occupancyDateIndexMap);
                    checkForMissingSegments(budgetDataDtos, segments, errors, occupancyDateIndexMap);
                    checkSoldsVsCapacity(budgetDataDtos, capacity, errors, occupancyDateIndexMap);
                }
                occupancyDate = occupancyDate.plusDays(1);
            }
        }
    }

    private void checkMaxBudgetPeriod(LocalDate startDate, LocalDate endDate, List<BudgetDataValidationError> errors) {
        int numberOfOccupancyDays = JavaLocalDateUtils.daysBetween(startDate, endDate) + 1;
        if (numberOfOccupancyDays > 730) {
            errors.add(new BudgetDataValidationError(null, null, "Number of occupancy days (" + numberOfOccupancyDays + ") greater than 730", null));
        }
    }

    public Integer getTotalHotelCapacity() {
        int capacity = 0;
        List<AccomType> accomTypes = accommodationService.getAllActiveAccomTypesWithCapacityForBudget();
        for (AccomType accomType : accomTypes) {
            capacity += accomType.getAccomTypeCapacity();
        }
        return capacity;
    }

    private void checkSoldsVsCapacity(List<BudgetDataDto> budgetDataDtos, int capacity, List<BudgetDataValidationError> errors, Map<LocalDate, Integer> occupancyDateIndexMap) {
        int roomsSold = 0;
        roomsSold = budgetDataDtos.stream().mapToInt(BudgetDataDto::getRoomsSold).sum();
        if (roomsSold > capacity) {
            errors.add(new BudgetDataValidationError(budgetDataDtos.get(0).getOccupancyDate(), null, "Total rooms sold (" + roomsSold + ") greater than hotel capacity (" + capacity + ")", occupancyDateIndexMap.get(budgetDataDtos.get(0).getOccupancyDate())));
        }
    }

    private boolean validateColumnOrder(Row row, List<String> columnOrder) {
        return row.getCell(INDEX_OCCUPANCY_DATE).getStringCellValue().equalsIgnoreCase(columnOrder.get(0)) &&
                row.getCell(INDEX_DOW).getStringCellValue().equalsIgnoreCase(columnOrder.get(1)) &&
                row.getCell(INDEX_SEGMENT).getStringCellValue().equalsIgnoreCase(columnOrder.get(2)) &&
                isRoomSoldsAndRevenueValid(row, columnOrder);
    }

    private boolean isRoomSoldsAndRevenueValid(Row row, List<String> columnOrder) {
        return row.getCell(INDEX_ROOM_SOLD).getStringCellValue().equalsIgnoreCase(columnOrder.get(3)) &&
                row.getCell(INDEX_ROOM_REVENUE).getStringCellValue().equalsIgnoreCase(columnOrder.get(4));
    }

    private void checkForDuplicateSegments(List<BudgetDataDto> budgetDataDtos, List<BudgetDataValidationError> errors, Map<LocalDate, Integer> occupancyDateIndexMap) {
        Set<String> segments = new HashSet<String>();
        for (BudgetDataDto budgetDataDto : budgetDataDtos) {
            if (containsCaseInsensitive(segments, budgetDataDto.getSegment())) {
                errors.add(new BudgetDataValidationError(budgetDataDto, "Duplicate segment", occupancyDateIndexMap.get(budgetDataDto.getOccupancyDate())));
            } else {
                segments.add(budgetDataDto.getSegment());
            }
        }
    }

    private void checkForMissingSegments(List<BudgetDataDto> budgetDataDtos, Set<String> segments, List<BudgetDataValidationError> errors, Map<LocalDate, Integer> occupancyDateIndexMap) {
        for (String segment : segments) {
            boolean found = false;
            found = budgetDataDtos.stream().anyMatch(budgetDataDto -> budgetDataDto.getSegment().equalsIgnoreCase(segment));
            if (!found) {
                errors.add(new BudgetDataValidationError(budgetDataDtos.get(0).getOccupancyDate(), segment, "Missing segment", occupancyDateIndexMap.get(budgetDataDtos.get(0).getOccupancyDate())));
            }
        }

    }

    private Integer getIncrementVal(int val) {
        return ++val;
    }


    public String loadUserForecastDataIntoPacman(List<BudgetDataDto> data) {
        LOGGER.info("Saving BusinessType data into User_Forecast_Data table");
        BudgetLevel budgetLevel = new BudgetLevel();
        budgetLevel.setId(1);
        List<UserForecastData> entities = new ArrayList<>();
        int newCount = 0;
        int existingCount = 0;
        Map<String, Integer> businessTypeMap = getBusinessTypeMap();
        Map<LocalDate, Set<UserForecastData>> budgetDateSetMap = buildExistingUserForecastDataMap(tenantCrudService.findAll(UserForecastData.class));
        for (BudgetDataDto dto : data) {
            UserForecastData userForecastData = new UserForecastData();
            userForecastData.setBusinessGroupId(businessTypeMap.get(dto.getSegment().toLowerCase()));
            userForecastData.setBudgetLevel(budgetLevel);
            userForecastData.setOccupancyDate(JavaLocalDateUtils.toJodaLocalDate(dto.getOccupancyDate()));
            userForecastData.setRoomsSold(dto.getRoomsSold());
            userForecastData.setRoomRevenue(dto.getRoomRevenue());
            UserForecastData entity = checkForExistingUserForecastData(userForecastData, budgetDateSetMap);
            if (entity.getId() == null) {
                newCount++;
            } else {
                entity.setRoomsSold(dto.getRoomsSold());
                entity.setRoomRevenue(dto.getRoomRevenue());
                existingCount++;
            }
            entities.add(entity);
        }
        tenantCrudService.save(entities);

        dashboardMetrics2Cache.handleRemoveKeysForProperty(PacmanWorkContextHelper.getPropertyId());

        return "Saved " + newCount + " new rows and updated " + existingCount + " existing rows in UserForecast Data table";
    }

    private UserForecastData checkForExistingUserForecastData(UserForecastData entity, Map<LocalDate, Set<UserForecastData>> existingDataMap) {
        LocalDate date = JavaLocalDateUtils.toJavaLocalDate(entity.getOccupancyDate());
        Set<UserForecastData> dailyResults = existingDataMap.get(date);
        if (dailyResults == null) {
            return entity;
        }
        for (UserForecastData existingEntity : dailyResults) {
            if (existingEntity.getBusinessGroupId().equals(entity.getBusinessGroupId())) {
                return existingEntity;
            }
        }
        return entity;
    }

    private Map<LocalDate, Set<UserForecastData>> buildExistingUserForecastDataMap(List<UserForecastData> entities) {
        Map<LocalDate, Set<UserForecastData>> map = new HashMap<>();
        for (UserForecastData entity : entities) {
            LocalDate date = JavaLocalDateUtils.toJavaLocalDate(entity.getOccupancyDate());
            Set<UserForecastData> data = map.computeIfAbsent(date, localDate -> new HashSet<>());
            data.add(entity);
        }
        return map;
    }

}




