package com.ideas.tetris.pacman.services.budget.validator;

import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.budget.dto.BudgetConfigType;
import com.ideas.tetris.pacman.services.budget.model.BudgetUserForecastEntityModel;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class RevPlanBudgetForecastValidator {

    private static final double MAX_ROOM_REVENUE_VALUE = 99999999999999.99;
    private static final double MAX_ROOM_SOLD_VALUE = 9999;
    public static final int MAX_LIMIT_FOR_VALIDATION_MESSAGES = 100;

    public static List<BudgetUserForecastEntityModel> validateBudgetForecastData(List<BudgetUserForecastEntityModel> entityModels, List<String> errorMessages, String configType) {
        entityModels.removeIf(model -> isBudgetForecastDataInvalid(model, errorMessages, configType));
        return entityModels;
    }

    private static boolean isBudgetForecastDataInvalid(BudgetUserForecastEntityModel model, List<String> errorMessages, String configType) {
        if (isOccupancyDateInvalid(model.getOccupancyDate()) || isSegmentNameInvalid(model.getSegmentName()) || isSegmentNameInvalid(model.getSegmentCode()) || isRoomSoldInvalid(model.getRooms()) || isRoomRevenueInvalid(model.getRevenue(), configType)) {
            if (errorMessages.size() < MAX_LIMIT_FOR_VALIDATION_MESSAGES) {
                errorMessages.add(model.getValidationMessage());
            }
            return true;
        }
        return false;
    }

    private static boolean isOccupancyDateInvalid(LocalDate occupancyDate) {
        return ObjectUtils.isEmpty(occupancyDate);
    }

    private static boolean isRoomSoldInvalid(Integer roomSold) {
        return (ObjectUtils.isEmpty(roomSold) || notInRange(0, MAX_ROOM_SOLD_VALUE, roomSold));
    }

    private static boolean isRoomRevenueInvalid(BigDecimal roomRevenue, String configType) {
        return (ObjectUtils.isEmpty(roomRevenue) ||
                notInRange(BudgetConfigType.LDB.getKey().equalsIgnoreCase(configType) ? 0 : SystemConfig.getMinRoomRevenueValue(), MAX_ROOM_REVENUE_VALUE, roomRevenue.doubleValue()));
    }

    private static boolean isSegmentNameInvalid(String segmentName) {
        return StringUtils.isBlank(segmentName);
    }

    private static boolean notInRange(double min, double max, double value) {
        return (value < min) || (value > max);
    }

    public static List<BudgetUserForecastEntityModel> validateBudgetForecastDataForHotelCapacity(List<BudgetUserForecastEntityModel> entityModels, List<String> errorMessages, Integer maxDailyCapacity) {

        return entityModels.stream()
                .collect(Collectors.groupingBy(BudgetUserForecastEntityModel::getOccupancyDate))
                .values().stream()
                .filter(budgetUserForecastEntityModels -> isValidCapacity(budgetUserForecastEntityModels, errorMessages, maxDailyCapacity))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private static boolean isValidCapacity(List<BudgetUserForecastEntityModel> entityModels, List<String> errorMessages, Integer maxDailyCapacity) {
        if (entityModels.stream().mapToLong(BudgetUserForecastEntityModel::getRooms).sum() > maxDailyCapacity) {
            errorMessages.add("Total rooms sold is greater than hotel capacity(" + maxDailyCapacity + ") for :" + entityModels.get(0).getOccupancyDate());
            return false;
        }
        return true;
    }

}
