package com.ideas.tetris.pacman.services.reports.pricing;


import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.pricing.dto.PricingByDay;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.JasperReportDataConverter;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.PricingBarByDayConverter;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.PricingReportCriteria;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportData;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.scheduledreport.service.JasperReportService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;

import javax.inject.Inject;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PricingBarByDayService extends JasperReportService<ScheduledReportData, PricingReportCriteria> {

    @PricingBarByDayConverter.Qualifier
    @Autowired
	@Qualifier("pricingBarByDayConverter")
	private JasperReportDataConverter<List<PricingByDay>, PricingReportCriteria> pricingReportConverter;

    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService tenantCrudService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    PricingService pricingService;

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    @Override
    public String getReportTitle(ScheduledReport<PricingReportCriteria> scheduledReport) {
        return ResourceUtil.getText("pricingReport.barByDay.title", scheduledReport.getLanguage());
    }

    @Override
    protected ScheduledReportData retrieveData(ScheduledReport<PricingReportCriteria> scheduledReport) {

        List<PricingByDay> reportData = null;
        PricingReportCriteria reportCriteria = scheduledReport.getReportCriteria();

        LocalDate startDate = DateUtil.convertJodaToJavaLocalDate(reportCriteria.getStartDate());
        LocalDate endDate = DateUtil.convertJodaToJavaLocalDate(reportCriteria.getEndDate());
        Integer rolling = reportCriteria.getIsRollingDate();
        String rollingStartDate = reportCriteria.getRollingStartDate();
        String rollingEndDate = reportCriteria.getRollingEndDate();
        String roomClasses = reportCriteria.getRoomClasses();

        boolean isPhysicalCapacityEnabled = pacmanConfigParamsService.isEnablePhysicalCapacityConsideration();
        boolean isContinuousPricing = pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);

        if (isContinuousPricing) {
            roomClasses = reportCriteria.getRoomTypes();
        }

        ArrayList<Integer> competitorIds = new ArrayList<Integer>();
        if (reportCriteria.getCompList() != null) {
            String competitorId[] = reportCriteria.getCompList().replace("[", "").replace("]", "").split(",");
            if (competitorId.length != 15) {
                throw new RuntimeException("Competitor length should be 15, it is now " + competitorId.length);
            }
            for (int i = 0; i < 15; i++) {
                competitorIds.add(Integer.parseInt(competitorId[i].trim()));
            }
        }

        reportData = pricingService.getPricingByDayData(startDate, endDate, roomClasses, competitorIds, rolling, rollingStartDate, rollingEndDate, isPhysicalCapacityEnabled);

        populateReportCriteria(reportCriteria);

        ScheduledReportSheet pricingReportBarByDaySheet = new ScheduledReportSheet("pricingReport.barByLos.fileName", reportData, PricingByDay.class);
        List<ScheduledReportSheet> reportSheets = new ArrayList<>(1);
        reportSheets.add(pricingReportBarByDaySheet);

        return new ScheduledReportData("pricingReport.barByDay.title", reportSheets);
    }


    public void populateReportCriteria(PricingReportCriteria reportCriteria) {

        String propertyId = reportCriteria.getPropertyId();
        String userId = reportCriteria.getUserId();
        String baseCurrency = reportCriteria.getCurrency();

        LocalDate startDate = DateUtil.convertJodaToJavaLocalDate(reportCriteria.getStartDate());
        LocalDate endDate = DateUtil.convertJodaToJavaLocalDate(reportCriteria.getEndDate());
        Integer rolling = reportCriteria.getIsRollingDate();
        String rollingStartDate = reportCriteria.getRollingStartDate();
        String rollingEndDate = reportCriteria.getRollingEndDate();

        String sql = " select * from dbo.ufn_get_filter_selection " +
                "('" + propertyId + "'," + userId + ",'" + baseCurrency + "'," +
                "'" + rolling + "'," +
                "'" + startDate + "'," +
                "'" + endDate + "','','','','','',''," +
                "'" + rollingStartDate + "'," +
                "'" + rollingEndDate + "','','','','','','' )";

        List<Object[]> resultList = tenantCrudService.findByNativeQuery(sql);
        if (resultList != null) {

            Property property = getGlobalCrudService().find(Property.class, Integer.parseInt(reportCriteria.getPropertyId()));
            TimeZone pTimeZone = getAlertService().getPropertyTimeZone(property);
            ZonedDateTime createDateTime = JavaLocalDateUtils.toZonedDateTime(ScheduledReportUtils.convertDateTimeToTimeZone(reportCriteria.getCreatedOn(), pTimeZone));

            Object[] result = resultList.get(0);
            reportCriteria.setPropertyName((String) result[0]);
            reportCriteria.setCreatedBy((String) result[1]);
            reportCriteria.setCreatedOn(JavaLocalDateUtils.toJodaDateTime(createDateTime));
            reportCriteria.setStartDate(JavaLocalDateUtils.toJodaLocalDate(DateUtil.convertJavaUtilDateToLocalDate((Date) result[3], true)));
            reportCriteria.setEndDate(JavaLocalDateUtils.toJodaLocalDate(DateUtil.convertJavaUtilDateToLocalDate((Date) result[4], true)));
        }
    }

    @Override
    protected JasperReportDataConverter<ScheduledReportData, PricingReportCriteria> getJasperReportDataConverter() {
        return null;
    }
}
