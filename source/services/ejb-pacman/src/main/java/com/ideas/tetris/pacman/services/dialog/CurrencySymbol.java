package com.ideas.tetris.pacman.services.dialog;

import org.apache.log4j.Logger;

import java.util.Currency;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import org.springframework.stereotype.Component;

@Component
public class CurrencySymbol {

    private static final Logger LOGGER = Logger.getLogger(CurrencySymbol.class);
    private static final Map<Currency, Locale> localeMap = getCurrencyLocaleMap();
    private static final String USD_CURRENCY_SYMBOL_CODE = "CA";

    public String getCurrencySymbol(String currencyCode) {
        try {
            Currency currency = Currency.getInstance(new Locale("EN", parseCurrencyCode(currencyCode)));
            return currency.getSymbol(localeMap.get(currency));
        } catch (IllegalArgumentException e) {
            LOGGER.info("Currency Symbol not found..." + e);
            return currencyCode;
        }
    }

    private static Map<Currency, Locale> getCurrencyLocaleMap() {
        Map<Currency, Locale> map = new HashMap<>();
        for (Locale locale : Locale.getAvailableLocales()) {
            try {
                map.put(Currency.getInstance(locale), locale);
            } catch (IllegalArgumentException e) {
                LOGGER.info("Unable to create locale Map..." + e);
            }
        }
        return map;
    }

    private String parseCurrencyCode(String currencyCode) {
        return getUSDCurrencySymbolCode(getFirstTwoLetterOfCurrencyCode(currencyCode));
    }

    private String getFirstTwoLetterOfCurrencyCode(String currencyCode) {
        final Optional<String> currencyCodeOptional = Optional.ofNullable(currencyCode);
        if (currencyCodeOptional.isPresent() && currencyCodeOptional.get().length() > 2) {
            return currencyCodeOptional.get().substring(0, 2);
        }
        return currencyCode;
    }

    private String getUSDCurrencySymbolCode(String currencyCode) {
        if ("US".equalsIgnoreCase(currencyCode)) {
            return USD_CURRENCY_SYMBOL_CODE;
        }
        return currencyCode;
    }
}