package com.ideas.tetris.pacman.services.property.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ideas.tetris.platform.services.Stage;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PropertySearchCriteria {
    private Integer propertyId;
    private String propertyCode;
    private List<Stage> stages = new ArrayList<Stage>();
    private int minimumNumberOfExtracts;
    private boolean uploadedPropertiesOnly = false;
    private DateRange handoffDueDateRange;
    private DateRange scheduledTwoWayDateRange;
    private DateRange actualTwoWayDateRange;
    private boolean readyForCatchup = false;
    private boolean readyForPopulation = false;

    public String getPropertyCode() {
        return propertyCode;
    }

    public void setPropertyCode(String propertyCode) {
        this.propertyCode = propertyCode;
    }

    public List<Stage> getStages() {
        if (readyForCatchup) {
            return Arrays.asList(Stage.DATA_CAPTURE);
        }
        if (readyForPopulation) {
            return Arrays.asList(Stage.CATCHUP);
        }
        return stages;
    }

    public void setStages(List<Stage> stages) {
        this.stages = stages;
    }

    public int getMinimumNumberOfExtracts() {
        return minimumNumberOfExtracts;
    }

    public void setMinimumNumberOfExtracts(int minimumNumberOfExtracts) {
        this.minimumNumberOfExtracts = minimumNumberOfExtracts;
    }

    public Integer getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Integer propertyId) {
        this.propertyId = propertyId;
    }

    public boolean isReadyForCatchup() {
        return readyForCatchup;
    }

    public void setReadyForCatchup(boolean readyForCatchup) {
        this.readyForCatchup = readyForCatchup;
    }

    public boolean isReadyForPopulation() {
        return readyForPopulation;
    }

    public void setReadyForPopulation(boolean readyForPopulation) {
        this.readyForPopulation = readyForPopulation;
    }

    public boolean isEmpty() {
        if (propertyId != null) {
            return false;
        }
        if (propertyCode != null && propertyCode.length() > 0) {
            return false;
        }
        if (stages != null && !stages.isEmpty()) {
            return false;
        }
        if (uploadedPropertiesOnly) {
            return false;
        }
        if (handoffDueDateRange != null && !handoffDueDateRange.isEmpty()) {
            return false;
        }
        if (scheduledTwoWayDateRange != null && !scheduledTwoWayDateRange.isEmpty()) {
            return false;
        }
        if (actualTwoWayDateRange != null && !actualTwoWayDateRange.isEmpty()) {
            return false;
        }
        if (readyForCatchup || readyForPopulation) {
            return false;
        }
        return true;
    }

    public boolean isUploadedPropertiesOnly() {
        return uploadedPropertiesOnly;
    }

    public void setUploadedPropertiesOnly(boolean uploadedPropertiesOnly) {
        this.uploadedPropertiesOnly = uploadedPropertiesOnly;
    }

    @JsonIgnore
    public DateRange getHandoffDueDateRange() {
        return handoffDueDateRange;
    }

    public void setHandoffDueDateRange(DateRange handoffDueDateRange) {
        this.handoffDueDateRange = handoffDueDateRange;
    }

    @JsonIgnore
    public DateRange getScheduledTwoWayDateRange() {
        return scheduledTwoWayDateRange;
    }

    public void setScheduledTwoWayDateRange(DateRange scheduledTwoWayDateRange) {
        this.scheduledTwoWayDateRange = scheduledTwoWayDateRange;
    }

    @JsonIgnore
    public DateRange getActualTwoWayDateRange() {
        return actualTwoWayDateRange;
    }

    public void setActualTwoWayDateRange(DateRange actualTwoWayDateRange) {
        this.actualTwoWayDateRange = actualTwoWayDateRange;
    }

    public List<String> parsePropertyCodes() {
        List<String> propertyCodes = new ArrayList<String>();
        if (StringUtils.isNotBlank(propertyCode)) {
            if (propertyCode.contains(",")) {
                String[] array = propertyCode.split("[,]");
                for (String code : array) {
                    if (code != null && code.trim().length() > 0) {
                        propertyCodes.add(code.trim());
                    }
                }
            } else {
                propertyCodes.add(propertyCode.trim());
            }
        }
        return propertyCodes;
    }

}
