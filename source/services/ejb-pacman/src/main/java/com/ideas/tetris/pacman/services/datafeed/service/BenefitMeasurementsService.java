package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.benefits.dto.BenefitsDto;
import com.ideas.tetris.pacman.services.benefits.service.BenefitsMeasurementService;
import com.ideas.tetris.pacman.services.datafeed.dto.BenefitMeasurementDTO;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import static java.math.RoundingMode.HALF_UP;
import static java.util.Objects.nonNull;

@Component
@Transactional
public class BenefitMeasurementsService {

    @Autowired
    BenefitsMeasurementService benefitsMeasurementService;

    @Autowired
    DateService dateService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    public List<BenefitMeasurementDTO> getBenefits() {
        LocalDate processDate = dateService.getCaughtUpJavaLocalDate().minusMonths(2);
        String monthYearString = processDate.getMonth().getDisplayName(TextStyle.SHORT, Locale.US) + "-" + processDate.getYear();
        return getBenefitMeasurementDTO(benefitsMeasurementService.getBenefits(monthYearString, monthYearString), monthYearString);
    }

    protected List<BenefitMeasurementDTO> getBenefitMeasurementDTO(List<BenefitsDto> benefitsDtoList, String monthYearString) {
        boolean isProfitOptimizationEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED);
        BenefitsDto benefitsDto = !benefitsDtoList.isEmpty() && !benefitsDtoList.get(0).getChildren().isEmpty() ? benefitsDtoList.get(0).getChildren().get(0) : null;
        List<BenefitMeasurementDTO> benefitMeasurementDTOList = new ArrayList<>();

        if (benefitsDto != null) {
            BenefitMeasurementDTO benefitMeasurementDTO = new BenefitMeasurementDTO();
            benefitMeasurementDTO.setMonthDate(monthYearString.replace('-', ' '));

            benefitMeasurementDTO.setHeuristicOccupancy(benefitsDto.getHeuristicOccupancy().setScale(0, RoundingMode.HALF_UP));
            benefitMeasurementDTO.setHeuristicRevenue(scale(benefitsDto.getHeuristicRevenue()));
            benefitMeasurementDTO.setHeuristicAdr(scale(benefitsDto.getHeuristicAdr()));
            benefitMeasurementDTO.setHeuristicRevpar(scale(benefitsDto.getHeuristicRevpar()));

            benefitMeasurementDTO.setActualOccupancy(benefitsDto.getActualOccupancy().setScale(0, RoundingMode.HALF_UP));
            benefitMeasurementDTO.setActualRevenue(scale(benefitsDto.getActualRevenue()));
            benefitMeasurementDTO.setActualAdr(scale(benefitsDto.getActualAdr()));
            benefitMeasurementDTO.setActualRevpar(scale(benefitsDto.getActualRevpar()));

            benefitMeasurementDTO.setBenefitsRevenue(scale(benefitsDto.getBenefitsRevenue()));
            benefitMeasurementDTO.setBenefitsOccupancy(scale(benefitsDto.getBenefitsOccupancy()));
            benefitMeasurementDTO.setBenefitsADR(scale(benefitsDto.getBenefitsADR()));
            benefitMeasurementDTO.setBenefitsRevpar(scale(benefitsDto.getBenefitsRevpar()));

            benefitMeasurementDTO.setEstimatedRevenue(scale(benefitsDto.getActualRevenue().subtract(benefitsDto.getHeuristicRevenue())));
            benefitMeasurementDTO.setEstimatedOccupancy((benefitsDto.getActualOccupancy().subtract(benefitsDto.getHeuristicOccupancy())).setScale(0, RoundingMode.HALF_UP));
            benefitMeasurementDTO.setEstimatedAdr(scale(benefitsDto.getActualAdr().subtract(benefitsDto.getHeuristicAdr())));
            benefitMeasurementDTO.setEstimatedRevPar(scale(benefitsDto.getActualRevpar().subtract(benefitsDto.getHeuristicRevpar())));

            benefitMeasurementDTO.setAncillaryRevenueActual(scale(benefitsDto.getAncillaryRevenue()));
            benefitMeasurementDTO.setAncillaryRevenueWithoutRms(scale(benefitsDto.getAncillaryRevenueWithoutRms()));
            benefitMeasurementDTO.setAncillaryRevenueGain(scale(benefitsDto.getAncillaryRevenueGainInPercent()));
            benefitMeasurementDTO.setAncillaryRevenueEstimatedBenefits(scale(benefitsDto.getAncillaryRevenueGain()));

            benefitMeasurementDTO.setAncillaryProfitActual(scale(benefitsDto.getAncillaryProfit()));
            benefitMeasurementDTO.setAncillaryProfitWithoutRms(scale(benefitsDto.getAncillaryProfitWithoutRms()));
            benefitMeasurementDTO.setAncillaryProfitGain(scale(benefitsDto.getAncillaryProfitGainInPercentage()));
            benefitMeasurementDTO.setAncillaryProfitEstimatedBenefits(scale(benefitsDto.getAncillaryProfitGain()));

            benefitMeasurementDTO.setProfitEstimatedBenefit(scale(benefitsDto.getActualProfit().subtract(benefitsDto.getHeuristicProfit())));
            benefitMeasurementDTO.setProfitSimulated(scale(benefitsDto.getHeuristicProfit()));
            benefitMeasurementDTO.setProfitActual(scale(benefitsDto.getActualProfit()));
            benefitMeasurementDTO.setProfitGain(scale(benefitsDto.getBenefitProfitInPercent()));

            benefitMeasurementDTO.setProPOREstimatedBenefit(scale(benefitsDto.getActualProPOR().subtract(benefitsDto.getHeuristicProPOR())));
            benefitMeasurementDTO.setProPORSimulated(scale(benefitsDto.getHeuristicProPOR()));
            benefitMeasurementDTO.setProPORActual(scale(benefitsDto.getActualProPOR()));
            benefitMeasurementDTO.setProPORGain(scale(benefitsDto.getBenefitProPORInPercent()));

            benefitMeasurementDTO.setProPAREstimatedBenefit(scale(benefitsDto.getActualProPAR().subtract(benefitsDto.getHeuristicProPAR())));
            benefitMeasurementDTO.setProPARSimulated(scale(benefitsDto.getHeuristicProPAR()));
            benefitMeasurementDTO.setProPARActual(scale(benefitsDto.getActualProPAR()));
            benefitMeasurementDTO.setProPARGain(scale(benefitsDto.getBenefitProPARInPercent()));

            benefitMeasurementDTOList.add(benefitMeasurementDTO);
            if (isProfitOptimizationEnabled && benefitMeasurementDTO.getBenefitsRevenue().add(benefitMeasurementDTO.getAncillaryProfitGain()).compareTo(BigDecimal.ZERO) < 0) {
                benefitMeasurementDTOList.remove(0);
            } else if (!isProfitOptimizationEnabled && (benefitMeasurementDTO.getBenefitsRevenue().compareTo(BigDecimal.ZERO) < 0 ||
                    benefitMeasurementDTO.getEstimatedRevPar().compareTo(BigDecimal.ZERO) < 0)) {
                benefitMeasurementDTOList.remove(0);
            }
        }
        return benefitMeasurementDTOList;
    }

    private BigDecimal scale(BigDecimal value) {
        return nonNull(value) ? value.setScale(2, HALF_UP) : null;
    }

}
