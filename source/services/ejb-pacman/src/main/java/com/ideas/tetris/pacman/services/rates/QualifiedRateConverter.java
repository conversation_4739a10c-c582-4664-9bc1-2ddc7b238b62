package com.ideas.tetris.pacman.services.rates;

import com.ideas.api.client.rate.model.RateAdjustment.AdjustmentEnum;
import com.ideas.api.client.rate.model.RateAdjustment.PeriodEnum;
import com.ideas.api.client.rate.model.RateAdjustment.TypeEnum;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetaDataBuffer;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.*;
import com.ideas.tetris.pacman.services.ratepopulation.dtos.RateQualifiedDetailsDto;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractDetail;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractRate;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedAdjustment.DELETE_OLD;
import static com.ideas.tetris.pacman.util.streamutils.StreamUtils.keepOnly;

@Component
@Transactional
public class QualifiedRateConverter extends RateConverter {

    private static final Logger LOGGER = Logger.getLogger(QualifiedRateConverter.class);

    static final String MONETARY_VALUE_ADJUSTMENT_TYPE = "monetaryvalue";

    @Override
    protected void deleteDetailsStartingFrom(List<Integer> ids, Date minStartDate) {
        tenantCrudService.executeUpdateByNamedQuery(RateQualifiedDetails.DELETE_BY_RATE_QUALIFIED_IDS_AND_START_DATE, QueryParameter.with("rateQualifiedIds", ids).and("date", minStartDate).parameters());
    }

    @Override
    protected AbstractRate getHeader(String name) {
        return findEligibleRate(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, name);
    }


    @Override
    protected List<AbstractDetail> getDetails(List<Integer> ids) {
        return tenantCrudService.findByNamedQuery(RateQualifiedDetails.BY_RATE_QUALIFIED_IDS, QueryParameter.with("rateQualifiedIds", ids).parameters());
    }

    @Override
    protected AbstractRate createNewHeader() {
        return new RateQualified();
    }

    @Override
    protected AbstractDetail createNewDetail() {
        return new RateQualifiedDetails();
    }

    @Override
    protected AbstractDetail createNewDetail(AbstractDetail detail) {
        return detail.cloneDetail();
    }

    @Override
    protected void convertSpecificFields(Map<String, Object> dto, AbstractRate rateHeader) {
        ((RateQualified) rateHeader).setReferenceRateCode((String) dto.get("referenceRateCode"));
        ((RateQualified) rateHeader).setRateQualifiedTypeId(getRateQualifiedTypeId(dto));
        ((RateQualified) rateHeader).setPostingRules(getPostingRulesForThisRecord(dto));
        ((RateQualified) rateHeader).setYieldDeltaAmount(getYieldDeltaAmount(dto.get("yieldDeltaAmount")));
        ((RateQualified) rateHeader).setSrpGroupName((String) (dto.getOrDefault("srpGroupId", dto.get("SRPGroupID"))));
        ((RateQualified) rateHeader).setInventoryType((String) dto.get("inventoryType"));
        ((RateQualified) rateHeader).setYieldType((String) dto.get("yieldType"));
        ((RateQualified) rateHeader).setYieldValue((String) dto.get("yieldValue"));
    }

    @Override
    protected void setHeaderReferenceOnDetails(AbstractRate rate, AbstractDetail detail) {
        ((RateQualifiedDetails) detail).setRateQualifiedId(rate.getId());
    }

    @Override
    protected void softDeleteHeader(Integer fileMetaDataId, AbstractRate rateHeader, Date caughtUpDate) {
        if (rateHeader != null) {
            rateHeader.setStatusId(INACTIVE);
        }
    }

    @Override
    protected void saveHeaders(final List<RateHeaderAndDetail> completeRecords) {
        final Map<String, PostingRulesType> allPostingRulesTypes = getAllPostingRulesTypes();
        final NetValueType netValueType = getNetValueType();
        final List<RateQualifiedAdjustment> rateQualifiedAdjustmentList = new ArrayList<>();

        tenantCrudService.save(completeRecords.stream().map(RateHeaderAndDetail::getHeader).collect(Collectors.toList()));

        completeRecords.forEach(rateHeaderAndDetail ->
                extractRateAdjustments(
                        rateQualifiedAdjustmentList,
                        rateHeaderAndDetail,
                        allPostingRulesTypes.get(((RateQualified) rateHeaderAndDetail.getHeader()).getPostingRules()),
                        netValueType)
        );

        if (CollectionUtils.isNotEmpty(rateQualifiedAdjustmentList)) {
            List<RateQualifiedAdjustment> resolvedRateAdjustments = resolveNewAdjustments(rateQualifiedAdjustmentList);
            tenantCrudService.save(resolvedRateAdjustments);
        }
    }

    public List<RateQualifiedAdjustment> resolveNewAdjustments(List<RateQualifiedAdjustment> newRateAdjustments) {
        return newRateAdjustments
                .stream()
                .filter(rateAdjustment -> rateAdjustment.getAdjustmentType() != null)
                .filter(rateAdjustment -> rateAdjustment.getRateQualifiedId() != null)
                .filter(rateAdjustment -> rateAdjustment.getStartDate() != null)
                // Keeping last adjustment from the repeated same adjustment having different values
                .collect(Collectors.toMap(
                        rateAdjustment -> Triple.of(
                                rateAdjustment.getAdjustmentType(),
                                rateAdjustment.getRateQualifiedId(),
                                rateAdjustment.getStartDate()),
                        Function.identity(),
                        (left, right) -> right))
                .values()
                .stream()
                // Grouping adjustments to resolve overlapping for the same qualified rate
                .collect(Collectors.groupingBy(
                        rateAdjustment -> Pair.of(
                                rateAdjustment.getAdjustmentType(),
                                rateAdjustment.getRateQualifiedId())))
                .values()
                .stream()
                .peek(this::resolveOverlapping)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private void resolveOverlapping(final List<RateQualifiedAdjustment> rateAdjustments) {
        rateAdjustments.sort(Comparator.comparing(RateQualifiedAdjustment::getStartDate).reversed());

        LocalDate previousStartDate = LocalDate.MAX;
        for (RateQualifiedAdjustment rateQualifiedAdjustment : rateAdjustments) {
            LocalDate endDate = LocalDateUtils.toJavaLocalDate(rateQualifiedAdjustment.getEndDate());
            if (endDate.isAfter(previousStartDate) || endDate.isEqual(previousStartDate)) {
                Date resolvedEndDate = LocalDateUtils.toDate(previousStartDate.minusDays(1));
                rateQualifiedAdjustment.setEndDate(resolvedEndDate);
            }
            previousStartDate = LocalDateUtils.toJavaLocalDate(rateQualifiedAdjustment.getStartDate());
        }

        Collections.reverse(rateAdjustments);
    }

    @SuppressWarnings("unchecked")
    protected void extractRateAdjustments(final List<RateQualifiedAdjustment> rateQualifiedAdjustmentList,
                                          final RateHeaderAndDetail record,
                                          final PostingRulesType postingRules,
                                          final NetValueType netValueType) {
        final Map<String, Object> headerDto = record.getHeaderDto();
        final List<RateQualifiedAdjustment> rateQualifiedAdjustments =
                headerDto.containsKey("rateAdjustments") && CollectionUtils.isNotEmpty((List<Map<String, Object>>) headerDto.get("rateAdjustments"))
                        ? createRateQualifiedAdjustmentsFromDto(record)
                        : createRateQualifiedAdjustment(record.getHeader(), netValueType, postingRules);

        if (CollectionUtils.isNotEmpty(rateQualifiedAdjustments)) {
            rateQualifiedAdjustmentList.addAll(rateQualifiedAdjustments);
        }
    }

    protected List<RateQualifiedAdjustment> createRateQualifiedAdjustmentsFromDto(final RateHeaderAndDetail rateHeaderAndDetail) {
        final RateQualified ratePlan = (RateQualified) rateHeaderAndDetail.getHeader();
        final Integer rateQualifiedId = ratePlan.getId();
        Optional.ofNullable(rateQualifiedId).ifPresent(this::deleteOldAdjustments);

        return Stream.concat(
                        yieldableCostRateAdjustments(rateHeaderAndDetail, rateQualifiedId),
                        yieldableValueRateAdjustment(ratePlan))
                .collect(Collectors.groupingBy(RateQualifiedAdjustment::getAdjustmentType, LinkedHashMap::new, Collectors.toList()))
                .entrySet().stream()
                // if for some reason the first yieldableCostRateAdjustments stream will return YieldableValue adjustments because of changed requirements
                // we will skip all YieldableValue adjustments except the last one from yieldableValueRateAdjustment stream,
                // because we delete all YieldableValue adjustments before creating new one by yieldableValueRateAdjustment method
                .flatMap(QualifiedRateConverter::streamLastYieldableValueRateAdjustmentOrAny)
                .collect(Collectors.toList());
    }

    private static Stream<RateQualifiedAdjustment> streamLastYieldableValueRateAdjustmentOrAny(Map.Entry<String, List<RateQualifiedAdjustment>> entry) {
        return Constants.ADJUSTMENT_TYPE.YieldableValue.name().equals(entry.getKey())
                ? entry.getValue().stream().skip(entry.getValue().size() - 1L)
                : entry.getValue().stream();
    }

    protected List<RateQualifiedAdjustment> createRateQualifiedAdjustment(AbstractRate rate, NetValueType netValueType, PostingRulesType postingRulesType) {
        List<RateQualifiedAdjustment> existingRateQualifiedAdjustment = getExistingRateQualifiedAdjustment(rate.getId(), Optional.ofNullable(postingRulesType).map(PostingRulesType::getId).orElse(null));
        Optional.ofNullable(rate.getId()).ifPresent(this::deleteOldAdjustments);

        return Stream.concat(
                        yieldableDeltaRateAdjustment((RateQualified) rate, netValueType, postingRulesType, existingRateQualifiedAdjustment),
                        yieldableValueRateAdjustment((RateQualified) rate))
                .collect(Collectors.groupingBy(RateQualifiedAdjustment::getAdjustmentType, LinkedHashMap::new, Collectors.toList()))
                .entrySet().stream()
                // if both yieldableDeltaRateAdjustment and yieldableValueRateAdjustment of a same type YieldableValue,
                // then keep only yieldableValueRateAdjustment, because we delete all YieldableValue adjustments before creating new one
                // by yieldableValueRateAdjustment method
                .flatMap(QualifiedRateConverter::streamLastYieldableValueRateAdjustmentOrAny)
                .collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    private Stream<RateQualifiedAdjustment> yieldableCostRateAdjustments(RateHeaderAndDetail rateHeaderAndDetail, Integer rateQualifiedId) {
        final String adjustmentType = Constants.ADJUSTMENT_TYPE.YieldableCost.name();

        return ((List<Map<String, Object>>) rateHeaderAndDetail.getHeaderDto().get("rateAdjustments"))
                .stream()
                .map(rateAdjustment -> {
                    final AdjustmentEnum adjustment = AdjustmentEnum.fromValue((String) rateAdjustment.get("adjustment"));
                    final TypeEnum type = TypeEnum.fromValue((String) rateAdjustment.get("type"));
                    final PeriodEnum period = PeriodEnum.fromValue((String) rateAdjustment.get("period"));
                    final BigDecimal amount = (BigDecimal) rateAdjustment.get("amount");
                    final Date startDate = LocalDateUtils.toDate(LocalDate.parse((String) rateAdjustment.get("startDate")));
                    final int postingRuleTypeId = PeriodEnum.PERNIGHT.equals(period) ? 2 : 1;

                    final RateQualifiedAdjustment rateQualifiedAdjustment = new RateQualifiedAdjustment();

                    rateQualifiedAdjustment.setRateQualifiedId(rateQualifiedId);
                    rateQualifiedAdjustment.setStartDate(startDate);
                    rateQualifiedAdjustment.setEndDate(LocalDateUtils.toDate(LocalDate.parse((String) rateAdjustment.get("endDate"))));
                    rateQualifiedAdjustment.setPostingRuleTypeId(postingRuleTypeId);
                    rateQualifiedAdjustment.setNetValue(AdjustmentEnum.DECREASE.equals(adjustment) ? amount.negate().floatValue() : amount.floatValue());

                    if (AdjustmentEnum.SET.equals(adjustment)) {
                        rateQualifiedAdjustment.setNetValueTypeId(3);
                    } else if (TypeEnum.ACTUAL.equals(type)) {
                        rateQualifiedAdjustment.setNetValueTypeId(1);
                    } else if (TypeEnum.PERCENTAGE.equals(type)) {
                        rateQualifiedAdjustment.setNetValueTypeId(2);
                    }

                    rateQualifiedAdjustment.setAdjustmentType(adjustmentType);

                    LOGGER.debug("Adjustment: " + rateQualifiedAdjustment);

                    return rateQualifiedAdjustment;
                });
    }

    private Stream<RateQualifiedAdjustment> yieldableValueRateAdjustment(RateQualified ratePlan) {
        final String adjustmentType = Constants.ADJUSTMENT_TYPE.YieldableValue.name();

        final BigDecimal yieldValue = tryParseYieldValue(ratePlan);
        if (yieldValue == null) {
            return Stream.empty();
        }

        final RateQualifiedAdjustment rateQualifiedAdjustment = new RateQualifiedAdjustment();
        rateQualifiedAdjustment.setRateQualifiedId(ratePlan.getId());
        rateQualifiedAdjustment.setAdjustmentType(adjustmentType);
        rateQualifiedAdjustment.setStartDate(ratePlan.getStartDate());
        rateQualifiedAdjustment.setEndDate(ratePlan.getEndDate());
        final int postingRulePerNightTypeId = 2;
        rateQualifiedAdjustment.setPostingRuleTypeId(postingRulePerNightTypeId);
        rateQualifiedAdjustment.setNetValue(yieldValue.floatValue());
        final String yieldType = ratePlan.getYieldType();
        final int netValueTypeId = yieldType == null || MONETARY_VALUE_ADJUSTMENT_TYPE.equalsIgnoreCase(yieldType) ? 1 : 2;
        rateQualifiedAdjustment.setNetValueTypeId(netValueTypeId);

        LOGGER.debug("Adjustment: " + rateQualifiedAdjustment);

        return Stream.of(rateQualifiedAdjustment);
    }

    private static BigDecimal tryParseYieldValue(RateQualified ratePlan) {
        try {
            return NumberUtils.isParsable(ratePlan.getYieldValue()) ? new BigDecimal(ratePlan.getYieldValue()) : null;
        } catch (Exception e) {
            LOGGER.error("Error parsing qualified rate plan " + ratePlan.getName() + " yield value: " + ratePlan.getYieldValue(), e);
            return null;
        }
    }


    private void deleteOldAdjustments(int rateQualifiedId) {
        Map<String, Object> parameters = Map.of(
                "rateQualifiedId", rateQualifiedId);

        tenantCrudService.executeUpdateByNamedQuery(DELETE_OLD, parameters);
        tenantCrudService.flushAndClear();
    }

    private Stream<RateQualifiedAdjustment> yieldableDeltaRateAdjustment(RateQualified rate, NetValueType netValueType, PostingRulesType postingRulesType,
                                                                         List<RateQualifiedAdjustment> existingRateQualifiedAdjustmentList) {
        Float yieldDeltaAmount = rate.getYieldDeltaAmount();
        if (yieldDeltaAmount != null) {
            float yieldDeltaSignum = Math.signum(yieldDeltaAmount);
            String adjustmentType = yieldDeltaSignum > 0 ? Constants.ADJUSTMENT_TYPE.YieldableValue.name() : Constants.ADJUSTMENT_TYPE.YieldableCost.name();

            if (Math.round(yieldDeltaSignum) != 0) {
                RateQualifiedAdjustment rateQualifiedAdjustment = existingRateQualifiedAdjustmentList.stream()
                        .filter(existingRateAdjustment -> adjustmentType.equals(existingRateAdjustment.getAdjustmentType()))
                        .findFirst()
                        .orElse(new RateQualifiedAdjustment());
                rateQualifiedAdjustment.setRateQualifiedId(rate.getId());
                rateQualifiedAdjustment.setAdjustmentType(adjustmentType);
                rateQualifiedAdjustment.setNetValueTypeId(netValueType.getId());
                rateQualifiedAdjustment.setNetValue(yieldDeltaAmount);
                rateQualifiedAdjustment.setStartDate(rate.getStartDate());
                rateQualifiedAdjustment.setEndDate(rate.getEndDate());
                rateQualifiedAdjustment.setPostingRuleTypeId(postingRulesType.getId());
                LOGGER.debug("Adjustment: " + rateQualifiedAdjustment);

                return Stream.of(rateQualifiedAdjustment);
            }
        }

        return Stream.empty();
    }

    private List<RateQualifiedAdjustment> getExistingRateQualifiedAdjustment(final Integer rateQualifiedId, final Integer postingRuleTypeId) {

        return tenantCrudService.findByNamedQuery(RateQualifiedAdjustment.BY_RATE_PLAN_AND_POSTING_RULE,
                QueryParameter.with("rateQualifiedId", rateQualifiedId)
                        .and("postingRuleTypeId", postingRuleTypeId).parameters());
    }

    @Override
    protected void truncateEndDateOfSeasonsWitinRangeOfGivenDate(Integer headerId, Date dateToTruncateDetailsFrom) {
        var parameters = QueryParameter.with("rateQualifiedId", headerId).and("date", dateToTruncateDetailsFrom).parameters();
        if (SystemConfig.updatePastRateDetailsInSingleOperation()) {
            tenantCrudService.executeUpdateByNamedQuery(RateQualifiedDetails.UPDATE_PAST_RATE_QUALIFIED_DETAILS, parameters);
        } else {
            List<RateQualifiedDetails> seasonsToUpdate =
                    tenantCrudService.findByNamedQuery(RateQualifiedDetails.BY_RATE_PLAN_ID_AND_ACTIVE_ON_DATE, parameters);
            seasonsToUpdate
                    .forEach(rateQualifiedDetails -> {
                        if (!dateToTruncateDetailsFrom.equals(rateQualifiedDetails.getStartDate())) {
                            rateQualifiedDetails.setEndDate(DateUtil.addDaysToDate(dateToTruncateDetailsFrom, -1));
                            rateQualifiedDetails.clearOutOfRangeDOWsWithRangeCheck();
                            tenantCrudService.save(rateQualifiedDetails);
                        }
                    });
        }
    }

    private Float getYieldDeltaAmount(Object amount) {
        String stringDeltaAmount = amount == null ? null : String.valueOf(amount);
        return stringDeltaAmount == null ? null : Float.valueOf(stringDeltaAmount);
    }

    private static Integer getRateQualifiedTypeId(Map<String, Object> dto) {
        return Optional.ofNullable((Integer) dto.get("rateQualifiedTypeId"))
                .or(() -> Optional.ofNullable(RateQualifiedType.rateQualifiedTypeId((String) dto.get("rateQualifiedType"))))
                .orElse(Constants.QUALIFIED_RATE_PLAN_FIXED_TYPE);
    }

    @Override
    protected FileMetadata createFileMetadata(Map<String, Object> dto) {
        Date dateTimeByTimeZone = DateUtil.getDateTimeByTimeZone(DateUtil.getCurrentDate(), dateService.getPropertyTimeZone());
        String today = DateUtil.formatDate(dateTimeByTimeZone, "yyyyMMdd_HHmm");
        String fileName = String.format("%s_%s_%s_%s_Rates", "NGI", dto.get("clientCode"), dto.get("propertyCode"), today);
        FileMetaDataBuffer metaDataBuffer = new FileMetaDataBuffer(fileName, Constants.QUALIFIED_RATE_PLAN, (String) dto.get("correlationId"));
        metaDataBuffer.eventDate(dateTimeByTimeZone);
        FileMetadata metaData = metaDataBuffer.value();
        return tenantCrudService.save(metaData);
    }

    private NetValueType getNetValueType() {
        return tenantCrudService.findByNamedQuerySingleResult(
                NetValueType.BY_TYPE_NAME,
                QueryParameter.with("name", AMOUNT)
                        .parameters());
    }

    private Map<String, PostingRulesType> getAllPostingRulesTypes() {
        return tenantCrudService.<PostingRulesType>findByNamedQuery(PostingRulesType.ALL_TYPE)
                .stream()
                .collect(
                        Collectors.toMap(
                                PostingRulesType::getName,
                                postingRulesType -> postingRulesType,
                                (a, b) -> b));
    }

    private String getPostingRulesForThisRecord(Map<String, Object> dto) {
        return (String) dto.get(POSTING_RULES);
    }

    @Override
    protected void saveRateDetailsInBatch(List<AbstractDetail> detailsToSave) {
        final List<RateQualifiedDetailsDto> tableBatch = detailsToSave.stream()
                .flatMap(keepOnly(RateQualifiedDetails.class))
                .map(RateQualifiedDetailsDto::fromEntity)
                .collect(Collectors.toList());

        if (!tableBatch.isEmpty()) {
            tenantCrudService.execute(RateQualifiedDetailsDto.USP_RATE_QUALIFIED_DETAILS_INSERT, tableBatch);
        }
    }
}
