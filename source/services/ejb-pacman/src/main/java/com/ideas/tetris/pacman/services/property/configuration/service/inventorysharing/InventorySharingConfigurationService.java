package com.ideas.tetris.pacman.services.property.configuration.service.inventorysharing;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassInventorySharing;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassSharingGroup;
import com.ideas.tetris.pacman.services.accommodation.entity.InventorySharingRank;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.property.configuration.RoomClassCapacityMap;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.InventorySharingPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecordStatus;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.List;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@InventorySharingConfigurationService.Qualifier
@Component
@Transactional
public class InventorySharingConfigurationService extends AbstractPropertyConfigurationService {

    private static final String PROPERTY_ID = "propertyId";

    private static final Logger LOGGER = Logger.getLogger(InventorySharingConfigurationService.class.getName());

    private static final int INVENTORY_SHARE_NAME_MAX_LENGTH = 50;
    private static final int INVENTORY_SHARE_DESCRIPTION_MAX_LENGTH = 50;
    private static final int ADDITIONAL_EXISTING_GROUPS = 3;
    private static final int MAX_PERCENT = 100;

    @Autowired
	private AccommodationService accommodationService;

    @Override
    public PropertyConfigurationRecordType getPropertyConfigurationRecordType() {
        return PropertyConfigurationRecordType.IS;
    }

    @Override
    public List<PropertyConfigurationRecordFailure> doValidate(PropertyConfigurationDto pcd, Integer propertyId) {
        InventorySharingPropertyConfigurationDto ispcd = (InventorySharingPropertyConfigurationDto) pcd;

        List<PropertyConfigurationRecordFailure> exceptions = new ArrayList<PropertyConfigurationRecordFailure>();
        // Validate Inventory Share Name
        String inventoryShareName = ispcd.getInventoryShareName();
        if (StringUtils.isEmpty(inventoryShareName)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Inventory Share Name is required"));
        } else if (inventoryShareName.length() > INVENTORY_SHARE_NAME_MAX_LENGTH) {
            ispcd.setInventoryShareDescription(StringUtils.left(inventoryShareName, INVENTORY_SHARE_NAME_MAX_LENGTH));
            exceptions.add(new PropertyConfigurationRecordFailure(ConfigurationFileRecordStatus.WARNING, "Inventory Share Name cannot be longer than " + INVENTORY_SHARE_NAME_MAX_LENGTH + " characters.  The value has been trimmed."));
        }

        // Validate Inventory Share Description
        String inventoryShareDescription = ispcd.getInventoryShareDescription();

        if (StringUtils.isEmpty(inventoryShareDescription)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Inventory Share Description is required"));
        } else if (inventoryShareDescription.length() > INVENTORY_SHARE_DESCRIPTION_MAX_LENGTH) {
            ispcd.setInventoryShareDescription(StringUtils.left(inventoryShareDescription, INVENTORY_SHARE_DESCRIPTION_MAX_LENGTH));
            exceptions.add(new PropertyConfigurationRecordFailure(ConfigurationFileRecordStatus.WARNING, "Inventory Share Description cannot be longer than " + INVENTORY_SHARE_DESCRIPTION_MAX_LENGTH + " characters.  The value has been trimmed."));
        }
        String roomClassName = ispcd.getRoomClassName();
        String sharedRoomClassName = ispcd.getSharedRoomClassName();
        AccomClass accomClass = findAccomClass(propertyId, roomClassName);
        AccomClass sharedAccomClass = findAccomClass(propertyId, sharedRoomClassName);
        AccomClassSharingGroup accomClassSharingGroup = findAccomClassSharingGroup(propertyId, ispcd.getInventoryShareName());
        AccomClassInventorySharing existingInventorySharing = findAccomClassInventorySharing(accomClass, accomClassSharingGroup);
        if (existingInventorySharing != null && !existingInventorySharing.getSharedAccomClass().getName().equals(sharedRoomClassName)) {
            existingInventorySharing = null;
        }

        // Validate Room Class Name
        if (StringUtils.isEmpty(roomClassName)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Room Class Name is required"));
        } else if (accomClass == null) {
            exceptions.add(new PropertyConfigurationRecordFailure("Room Class Name: " + roomClassName + " is not valid"));
        } else if (existingInventorySharing == null &&
                (roomClassIsAlreadySharedTo(accomClass.getId(), accomClassSharingGroup) || roomClassIsAlreadyShared(accomClass.getId(), null))) {
            exceptions.add(new PropertyConfigurationRecordFailure("Room Class Name: " + roomClassName + " is already shared"));
        }


        // Validate Shared Room Class Name
        if (StringUtils.isEmpty(sharedRoomClassName)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Shared Room Class Name is required"));
        } else if (sharedAccomClass == null) {
            exceptions.add(new PropertyConfigurationRecordFailure("Shared Room Class Name: " + sharedRoomClassName + " is not valid"));
        } else if (existingInventorySharing == null &&
                (roomClassIsAlreadySharedTo(sharedAccomClass.getId(), null) || roomClassIsAlreadyShared(sharedAccomClass.getId(), accomClassSharingGroup))) {
            exceptions.add(new PropertyConfigurationRecordFailure("Shared Room Class Name: " + sharedRoomClassName + " is already shared"));
        }

        if (exceptions.isEmpty() && existingInventorySharing == null &&
                accomClassSharingGroup != null && isDisjoint(accomClassSharingGroup, accomClass, sharedAccomClass)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Disjoint sharing in iventory share " + inventoryShareName));
        }

        int numberOfAccomClasses = getNumberOfAccomClasses(propertyId);
        List<AccomClassInventorySharing> existingShares = getExistingAccomClassInventorySharing(propertyId);
        if (existingInventorySharing == null && existingShares.size() >= (numberOfAccomClasses - 1)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Maximum number of Inventory Shares exceeded "));
        }
        if (accomClassSharingGroup == null) { // attempt to create a new sharing group
            List<AccomClassSharingGroup> existingGroups = getExistingSharingGroups(propertyId);
            if (!existingGroups.isEmpty()) {
                if (numberOfAccomClasses <= existingGroups.size() + 2) {
                    exceptions.add(new PropertyConfigurationRecordFailure("Maximum number of Inventory Shares exceeded "));
                } else if (numberOfAccomClasses == existingGroups.size() + ADDITIONAL_EXISTING_GROUPS && existingShares.size() > 1) {
                    exceptions.add(new PropertyConfigurationRecordFailure("Maximum number of Inventory Shares exceeded "));
                }
            }
        }
        // Validate Number of Shared Rooms
        String percentOfCapacityShared = ispcd.getPercentOfCapacityShared();
        if (percentOfCapacityShared == null) {
            exceptions.add(new PropertyConfigurationRecordFailure("Invalid percent of of capacity shared: null"));
        }
        try {
            Integer percent = Integer.valueOf(percentOfCapacityShared);
            if (percent <= 0 || percent > MAX_PERCENT) {
                exceptions.add(new PropertyConfigurationRecordFailure("Invalid percent of of capacity shared:" + percent));
            }
            if (percent > 0 && percent < MAX_PERCENT) {
                Integer capacity = RoomClassCapacityMap.getRoomClassCapacity(pcd.getPropertyCode(), roomClassName);
                if (capacity == null || capacity.intValue() == 0) {
                    exceptions.add(new PropertyConfigurationRecordFailure("Inventory Sharing has been defined for a room class with zero or unspecified capacity:" + percentOfCapacityShared));
                }
            }

        } catch (NumberFormatException e) {
            exceptions.add(new PropertyConfigurationRecordFailure("Invalid percent of of capacity shared:" + percentOfCapacityShared));
        }
        return exceptions;
    }

    @SuppressWarnings("unchecked")
    private List<AccomClassInventorySharing> getExistingAccomClassInventorySharing(Integer propertyId) {
        return crudService.findByNamedQuery(AccomClassInventorySharing.ALL_BY_PROPERTY,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    @SuppressWarnings("unchecked")
    private int getNumberOfAccomClasses(Integer propertyId) {
        List<AccomClass> accomClasses = crudService.findByNamedQuery(AccomClass.BY_SYSTEMDEFAULT_AND_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, propertyId).and("systemDefault", 0).parameters());
        return accomClasses.size();
    }

    @SuppressWarnings("unchecked")
    private List<AccomClassSharingGroup> getExistingSharingGroups(Integer propertyId) {
        return crudService.findByNamedQuery(AccomClassSharingGroup.BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    @SuppressWarnings("unchecked")
    private boolean isDisjoint(AccomClassSharingGroup accomClassSharingGroup, AccomClass accomClass, AccomClass sharedAccomClass) {
        if (accomClass == null || sharedAccomClass == null) {
            return false;
        }
        List<AccomClassInventorySharing> records = crudService.findByNamedQuery(AccomClassInventorySharing.BY_GROUP_ID, QueryParameter.with("id", accomClassSharingGroup.getId()).parameters());
        for (AccomClassInventorySharing record : records) {
            if (record.getAccomClass().getId().equals(sharedAccomClass.getId()) || record.getSharedAccomClass().getId().equals(accomClass.getId())) {
                return false;
            }
        }
        return true;
    }

    @SuppressWarnings("unchecked")
    private boolean roomClassIsAlreadySharedTo(Integer sharedRoomClassId, AccomClassSharingGroup excludeGroup) {
        List<AccomClassInventorySharing> records = crudService.findByNamedQuery(AccomClassInventorySharing.BY_SHARED_ACCOM_CLASS_ID, QueryParameter.with("id", sharedRoomClassId).parameters());
        if (excludeGroup == null) {
            return !records.isEmpty();
        }
        for (AccomClassInventorySharing record : records) {
            if (!record.getAccomClassSharingGrp().getId().equals(excludeGroup.getId())) {
                return true;
            }
        }
        return false;
    }

    @SuppressWarnings("unchecked")
    private boolean roomClassIsAlreadyShared(Integer roomClassId, AccomClassSharingGroup excludeGroup) {
        List<AccomClassInventorySharing> records = crudService.findByNamedQuery(AccomClassInventorySharing.BY_ACCOM_CLASS_ID, QueryParameter.with("id", roomClassId).parameters());
        if (excludeGroup == null) {
            return !records.isEmpty();
        }
        for (AccomClassInventorySharing record : records) {
            if (!record.getAccomClassSharingGrp().getId().equals(excludeGroup.getId())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void doHandle(PropertyConfigurationDto pcd, Integer propertyId) {
        InventorySharingPropertyConfigurationDto ispcd = (InventorySharingPropertyConfigurationDto) pcd;

        AccomClassSharingGroup accomClassSharingGroup = findCreateOrUpdateAccomClassSharingGroup(propertyId, ispcd);

        AccomClass roomClass = findAccomClass(propertyId, ispcd.getRoomClassName());
        AccomClass sharedRoomClass = findAccomClass(propertyId, ispcd.getSharedRoomClassName());

        findCreateOrUpdateAccomClassInventorySharing(accomClassSharingGroup, roomClass, sharedRoomClass, ispcd, pcd.getPropertyCode());

        InventorySharingRank inventorySharingRank = findInventorySharingRank(roomClass, accomClassSharingGroup);
        if (inventorySharingRank == null) {
            inventorySharingRank = new InventorySharingRank();
            inventorySharingRank.setAccomClass(roomClass);
            inventorySharingRank.setAccomClassSharingGrp(accomClassSharingGroup);
            inventorySharingRank.setRank(0);
            LOGGER.info("Creating InventorySharingRank for AccomClass: " + inventorySharingRank.getAccomClass().getName() + " for Property: " + pcd.getPropertyCode());
            inventorySharingRank = crudService.save(inventorySharingRank);
        }
        inventorySharingRank = findInventorySharingRank(sharedRoomClass, accomClassSharingGroup);
        if (inventorySharingRank == null) {
            inventorySharingRank = new InventorySharingRank();
            inventorySharingRank.setAccomClass(sharedRoomClass);
            inventorySharingRank.setAccomClassSharingGrp(accomClassSharingGroup);
            inventorySharingRank.setRank(0);
            LOGGER.info("Creating InventorySharingRank for AccomClass: " + sharedRoomClass.getName() + " for Property: " + pcd.getPropertyCode());
            inventorySharingRank = crudService.save(inventorySharingRank);
        }
        orderInventorySharingRankRecords(accomClassSharingGroup);

    }

    @SuppressWarnings("unchecked")
    private void orderInventorySharingRankRecords(AccomClassSharingGroup accomClassSharingGroup) {
        List<InventorySharingRank> records = crudService.findByNamedQuery(InventorySharingRank.BY_GROUP_ID_SORTED_BY_VIEW_ORDER, QueryParameter.with("grpId", accomClassSharingGroup.getId()).parameters());
        int rank = 1;
        for (InventorySharingRank record : records) {
            record.setRank(rank);
            crudService.save(record);
            rank++;
        }
    }

    public AccomClassInventorySharing findCreateOrUpdateAccomClassInventorySharing(AccomClassSharingGroup accomClassSharingGroup, AccomClass roomClass, AccomClass sharedRoomClass, InventorySharingPropertyConfigurationDto ispcd, String propertyCode) {
        AccomClassInventorySharing inventorySharing = findAccomClassInventorySharing(roomClass, accomClassSharingGroup);
        if (inventorySharing == null) {
            inventorySharing = new AccomClassInventorySharing();
            inventorySharing.setAccomClassSharingGrp(accomClassSharingGroup);
            inventorySharing.setAccomClass(roomClass);
            inventorySharing.setRank(0);
        }

        inventorySharing.setSharedAccomClass(sharedRoomClass);
        int percentOfCapacityShared = ispcd.getPercentOfCapacitySharedAsInt();
        if (percentOfCapacityShared == 0) { // this should be prevented by validation
            inventorySharing.setUseMax(false);
            inventorySharing.setSharedRoomCount(0);
        } else if (percentOfCapacityShared == MAX_PERCENT) {
            inventorySharing.setUseMax(true);
            inventorySharing.setSharedRoomCount(0);
        } else {
            inventorySharing.setUseMax(false);
            Integer capacity = getCapacity(propertyCode, roomClass.getName());
            if (capacity == 0) { // this should be prevented by validation
                inventorySharing.setSharedRoomCount(0);
            } else {
                float roomCountDecimal = ((float) percentOfCapacityShared * (float) capacity) / (float) MAX_PERCENT;
                int roomCount = Math.round(roomCountDecimal);
                if (roomCount == 0) {
                    roomCount = 1;
                }
                inventorySharing.setSharedRoomCount(roomCount);
            }
        }

        if (inventorySharing.getCreateDate() == null) {
            LOGGER.info("Creating AccomClassInventorySharing with Shared Class: " + inventorySharing.getSharedAccomClass().getName() + " for Property: " + ispcd.getPropertyCode() + " and Accom Class:" + roomClass.getName());
            inventorySharing = crudService.save(inventorySharing);
        } else {
            LOGGER.info("Updating AccomClassInventorySharing with Shared Class: " + inventorySharing.getSharedAccomClass().getName() + " for Property: " + ispcd.getPropertyCode() + " and Accom Class:" + roomClass.getName());
            inventorySharing = crudService.save(inventorySharing);
        }

        return inventorySharing;
    }


    private Integer getCapacity(String propertyCode, String roomClass) {
        Integer capacity = RoomClassCapacityMap.getRoomClassCapacity(propertyCode, roomClass);
        if (capacity == null) {
            return 0;
        }
        return capacity;
    }

    public AccomClassSharingGroup findCreateOrUpdateAccomClassSharingGroup(Integer propertyId, InventorySharingPropertyConfigurationDto ispcd) {
        AccomClassSharingGroup accomClassSharingGroup = findAccomClassSharingGroup(propertyId, ispcd.getInventoryShareName());
        if (accomClassSharingGroup == null) {
            accomClassSharingGroup = new AccomClassSharingGroup();
            accomClassSharingGroup.setPropertyId(propertyId);
            accomClassSharingGroup.setName(ispcd.getInventoryShareName());
        }
        accomClassSharingGroup.setDescription(ispcd.getInventoryShareDescription());

        if (accomClassSharingGroup.getCreateDate() == null) {
            LOGGER.info("Creating AccomClassSharingGroup: " + accomClassSharingGroup.getName() + " for Property: " + ispcd.getPropertyCode());
            accomClassSharingGroup = crudService.save(accomClassSharingGroup);
        } else {
            LOGGER.info("Updating AccomClassSharingGroup: " + accomClassSharingGroup.getName() + " for Property: " + ispcd.getPropertyCode());
            accomClassSharingGroup = crudService.save(accomClassSharingGroup);
        }

        return accomClassSharingGroup;
    }

    public InventorySharingRank findInventorySharingRank(AccomClass accomClass, AccomClassSharingGroup accomSharingGroup) {
        return (InventorySharingRank) crudService.findByNamedQuerySingleResult(InventorySharingRank.BY_ACCOM_CLASS_ID_AND_GROUP_ID, QueryParameter.with("accomClassId", accomClass.getId()).and("grpId", accomSharingGroup.getId()).parameters());
    }

    public AccomClassInventorySharing findAccomClassInventorySharing(AccomClass accomClass, AccomClassSharingGroup accomClassSharingGroup) {
        if (accomClass == null || accomClassSharingGroup == null) {
            return null;
        }
        return (AccomClassInventorySharing) crudService.findByNamedQuerySingleResult(AccomClassInventorySharing.BY_ACCOM_CLASS_ID_AND_SHARE_ID, QueryParameter.with("accomClassId", accomClass.getId()).and("shareId", accomClassSharingGroup.getId()).parameters());
    }

    public AccomClassSharingGroup findAccomClassSharingGroup(Integer propertyId, String inventoryShareName) {
        return (AccomClassSharingGroup) crudService.findByNamedQuerySingleResult(AccomClassSharingGroup.BY_NAME, QueryParameter.with(PROPERTY_ID, propertyId).and("name", inventoryShareName).parameters());
    }

    public AccomClass findAccomClass(Integer propertyId, String name) {
        return (AccomClass) crudService.findByNamedQuerySingleResult(AccomClass.BY_NAME, QueryParameter.with(PROPERTY_ID, propertyId).and("name", name).parameters());
    }

    public void setAccommodationService(AccommodationService accommodationService) {
        this.accommodationService = accommodationService;
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
