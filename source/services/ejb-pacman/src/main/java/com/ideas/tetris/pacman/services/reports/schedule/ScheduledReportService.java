package com.ideas.tetris.pacman.services.reports.schedule;

import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import javax.inject.Inject;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class ScheduledReportService {

    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService globalCrudService;

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public CrudService getGlobalCrudService() {
        return globalCrudService;
    }

    public List<ScheduledReport> getSchedules(Integer propertyId) {
        List<ScheduledReport> schedules = globalCrudService.findByNamedQuery(ScheduledReport.FIND_BY_PROPERTY, QueryParameter.with("propertyId", propertyId).parameters());
        return schedules;
    }
}
