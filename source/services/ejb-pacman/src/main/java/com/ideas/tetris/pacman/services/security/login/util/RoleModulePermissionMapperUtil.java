package com.ideas.tetris.pacman.services.security.login.util;


import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class RoleModulePermissionMapperUtil {

    public static final String PERMISSION_READ_ONLY = "Read Only";
    public static final String PERMISSION_READ_WRITE = "Read/Write";
    public static final String PERMISSION_WRITE = "WRITE";
    public static final String PERMISSION_READ = "READ";
    private static final Map<String, String> ACCESS_LABEL_MAP;

    static {
        Map<String, String> map = new HashMap<>();
        map.put(PERMISSION_READ_WRITE, PERMISSION_WRITE);
        map.put(PERMISSION_READ_ONLY, PERMISSION_READ);
        ACCESS_LABEL_MAP = Collections.unmodifiableMap(map);

    }

    public static Map<String, String> getAccessLabelMap() {
        return ACCESS_LABEL_MAP;
    }
}
