package com.ideas.tetris.pacman.services.activesrp.repository;

import com.ideas.tetris.pacman.Repository;
import com.ideas.tetris.pacman.services.activesrp.entity.ActiveSrp;
import com.ideas.tetris.pacman.services.crudservice.RatchetCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Repository
@Component
public class ActiveSrpsRepository {


    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    @RatchetCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("ratchetCrudServiceBean")
	protected CrudService ratchetCrudService;


    public Map<String, List<ActiveSrp>> getActiveWindowsForFixedHeaders(List<String> srpCodes) {
        Map<String, List<ActiveSrp>> result = new HashMap<>();
        String sql = new StringBuilder("select SRP_Code, Start_Date, End_Date from Active_Srps ")
                .append("where SRP_Code in (:srpCodes)").toString();

        tenantCrudService.findByNativeQuery(sql,
                QueryParameter.with("srpCodes", srpCodes).parameters(),
                row -> {
                    ActiveSrp activeSrp = ActiveSrp.builder()
                            .srpCode((String) row[0])
                            .startDate(new LocalDate(row[1]))
                            .endDate(new LocalDate(row[2]))
                            .build();
                    result.computeIfAbsent((String) row[0], a -> new ArrayList<>()).add(activeSrp);
                    return null;
                });
        return result;
    }
}
