package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.tetris.pacman.services.activity.converter.PaceActivityConverter;
import com.ideas.tetris.pacman.services.activity.converter.TotalActivityConverter;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceTotalActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.dashboard.dto.TotalActivityBatchDto;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.TableBatchAware;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Provides REST access to the TotalActivity summary information. It extends the
 * ActivityService to provide the standard set of endpoints used by the activity
 * data.
 */
@Component
@Transactional
public class TotalActivityService extends PaceActivityService<TotalActivity, PaceTotalActivity> {
    private static final Logger LOGGER = Logger.getLogger(TotalActivityService.class);
    @TotalActivityConverter.Qualifier
    @Autowired
	@Qualifier("totalActivityConverter")
	private TotalActivityConverter totalActivityConverter;

    @Override
    protected Comparator<Map<String, Object>> getComparator() {
        return null;
    }

    @Override
    protected Class<TotalActivity> getEntityClass() {
        return TotalActivity.class;
    }

    @Override
    protected String getDateRangeQuery() {
        return TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID;
    }

    @Override
    protected String deleteDateRangeQuery() {
        return TotalActivity.DELETE_BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID;
    }

    @Override
    protected String getPaceDateRangeQuery() {
        return PaceTotalActivity.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID;
    }

    @Override
    protected String deletePaceDateRangeQuery() {
        return PaceTotalActivity.DELETE_BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID;
    }

    @Override
    protected List<TableBatchAware> convertToTableBatch(List<TotalActivity> totalActivities, boolean isCDP) {
        return totalActivities.stream().map(activity ->
                TotalActivityBatchDto.builder()
                        .totalActivityId(activity.getId())
                        .propertyId(activity.getPropertyId())
                        .fileMetadataId(activity.getFileMetadataId())
                        .totalAccomCapacity(activity.getTotalAccomCapacity())
                        .roomsSold(activity.getRoomsSold())
                        .roomsNotAvailableMaintenance(activity.getRoomsNotAvailableMaintenance())
                        .roomsNotAvailableOther(activity.getRoomsNotAvailableOther())
                        .arrivals(activity.getArrivals())
                        .departures(activity.getDepartures())
                        .cancellations(activity.getCancellations())
                        .noShows(activity.getNoShows())
                        .roomRevenue(activity.getRoomRevenue())
                        .foodRevenue(activity.getFoodRevenue())
                        .totalRevenue(activity.getTotalRevenue())
                        .totalProfit(activity.getTotalProfit())
                        .createDate(activity.getCreateDate())
                        .occupancyDate(activity.getOccupancyDate())
                        .snapShotDate(activity.getSnapShotDate())
                        .lastUpdatedDate(activity.getLastUpdatedDate())
                        .isCPD(isCDP)
                        .build()
        ).collect(Collectors.toList());
    }

    @Override
    protected PaceActivityConverter<TotalActivity, PaceTotalActivity> getConverter() {
        return totalActivityConverter;
    }

    @Override
    protected List<TotalActivity> saveEntities(List<Map<String, Object>> dtos, String correlationId, boolean isPast) {
        int count = dtos.size();
        LOGGER.info("Starting save of entities: " + count);
        List<TotalActivity> entities = convertDtosToEntities(dtos, correlationId);

        tenantCrudService.save(entities);

        LOGGER.info("Completed save of entities: " + count);
        return entities;
    }

    private List<TotalActivity> convertDtosToEntities(List<Map<String, Object>> dtos, String correlationId) {
        return getConverter().convertFromMapAll(dtos, correlationId);
    }

    public Integer updatePace(String propertyId, Date startDate, Date endDate, FileMetadata fileMetadata) {

        int numberOfUpdatedRecords = tenantCrudService.executeUpdateByNamedQuery(PaceTotalActivity.MERGE_PACE,
                QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and("propertyId", propertyId)
                        .and("snapshotDt", fileMetadata.getSnapshotDt())
                        .and("snapshotDtTm", fileMetadata.getSnapshotDtTm())
                        .and(FILE_META_DATA_ID_CAPTIALIZED_D, fileMetadata.getId())
                        .parameters());

        LOGGER.info("Finished save of pace, new pace records: " + numberOfUpdatedRecords);
        return numberOfUpdatedRecords;
    }

    public Integer updateNonPace(Integer propertyId, Date startDate, Date endDate, FileMetadata fileMetadata) {

        int numberOfUpdatedRecords = tenantCrudService.executeUpdateByNamedQuery(TotalActivity.UPDATE_TOTAL_ACTIVITY_FROM_MKT_ACCOM_ACTIVITY,
                QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and("propertyId", propertyId)
                        .and(FILE_META_DATA_ID_CAPTIALIZED_D, fileMetadata.getId())
                        .parameters());

        LOGGER.info("Finished updating total_activity. Records updated: " + numberOfUpdatedRecords);
        return numberOfUpdatedRecords;
    }

    public Integer insertNonPaceForLostData(Integer propertyId, Date startDate, Date endDate, FileMetadata fileMetadata) {

        int numberOfUpdatedRecords = tenantCrudService.executeUpdateByNamedQuery(TotalActivity.INSERT_TOTAL_ACTIVITY_FROM_ACCOM_ACTIVITY,
                QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and("propertyId", propertyId)
                        .and(FILE_META_DATA_ID_CAPTIALIZED_D, fileMetadata.getId())
                        .and("snapshotDt", fileMetadata.getSnapshotDt())
                        .parameters());

        LOGGER.info("Finished updating total_activity. Records updated: " + numberOfUpdatedRecords);
        return numberOfUpdatedRecords;
    }

    public boolean hasActivityForFileMetadataId(Integer fileMetadataId) {
        return 0 < (Long) tenantCrudService.findByNamedQuerySingleResult(TotalActivity.COUNT_BY_FILE_METADATA_ID, QueryParameter.with("fileMetadataId", fileMetadataId).parameters());
    }
}
