package com.ideas.tetris.pacman.services.reports.dto;

public class InputOverrideParamsDTO extends ReportParamsDTO {
    private String startDate;
    private String endDate;
    private String filterParamStartDate;
    private String filterParamEndDate;
    private Boolean occupancyDateCategory = true;
    private Boolean arrivalByLOSCategory = true;
    private Boolean washCategory = true;
    private Boolean notes = true;

    public InputOverrideParamsDTO() {
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getFilterParamStartDate() {
        return filterParamStartDate;
    }

    public void setFilterParamStartDate(String filterParamStartDate) {
        this.filterParamStartDate = filterParamStartDate;
    }

    public String getFilterParamEndDate() {
        return filterParamEndDate;
    }

    public void setFilterParamEndDate(String filterParamEndDate) {
        this.filterParamEndDate = filterParamEndDate;
    }

    public Boolean getOccupancyDateCategory() {
        return occupancyDateCategory;
    }

    public void setOccupancyDateCategory(Boolean occupancyDateCategory) {
        this.occupancyDateCategory = occupancyDateCategory;
    }

    public Boolean getArrivalByLOSCategory() {
        return arrivalByLOSCategory;
    }

    public void setArrivalByLOSCategory(Boolean arrivalByLOSCategory) {
        this.arrivalByLOSCategory = arrivalByLOSCategory;
    }

    public Boolean getWashCategory() {
        return washCategory;
    }

    public void setWashCategory(Boolean washCategory) {
        this.washCategory = washCategory;
    }

    public Boolean getNotes() {
        return notes;
    }

    public void setNotes(Boolean notes) {
        this.notes = notes;
    }

}
