package com.ideas.tetris.pacman.services.vendor;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.ideas.tetris.platform.common.ngi.VendorCredentials;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChainConfigParams implements VendorCredentialsAccessor {
    private String chainCode;
    private String g3ChainCode;
    private String baseCurrencyCode;
    private VendorCredentials inboundCredentials;
    private VendorCredentials outboundCredentials;
    private List<HotelConfigParams> hotels = new ArrayList<>();
    private String clientEnvironmentName;
    private Boolean unqualifiedRatesDirectPopulationDisabled;
    private Boolean qualifiedRatesDirectPopulationDisabled;
    private Integer installationReservationsThreshold;
    private Boolean populatePackageDataEnabled;
    private Boolean useNetRoomRevenue;
    private Boolean useCurrencyInMessage;
    private Boolean includePseudoInRevenue;
    private Boolean includeDayUseInRevenue;
    private Boolean includeNoShowInRevenue;
    private Boolean oxiRoomStayReservationByDay;
    private Boolean htngRoomStayReservationByDay;
    private Boolean folsRoomStayReservationByDay;
    private Boolean htngUseBasicAuth;
    private Boolean sendHTNGCallbackRequest;
    private Boolean includeRoomTypeHotelMarketSegmentActivity;
    private Boolean preventPseudoDataInActivity;
    private Boolean isComponentRoomsActivityEnabled;
    private Boolean useLegacyRoomStayHandling;
    private String oxiParkMessageTypes;
    private Boolean generateMarketSegmentStatsEnabled;
    private Boolean generateRateCodeStatsEnabled;
    private Boolean resetVirtualSuiteCounts;
    private Boolean generateHotelActivityFromRoomTypeActivity;
    private Boolean msrtSummaryPersistenceEnabled;
    private Boolean buildMSActivityUsingPMSMS;
    private Boolean summaryPersistenceEnabled;
    private String salesAndCateringUnitOfMeasure;
    private String salesAndCateringRMSCurrencyCode;
    private String defaultMarketSegmentCode;
    private String defaultGroupMarketSegmentCode;
    private Boolean alternateHTNGGroupsFlow;
    private Boolean processSoftPickups;
    private Boolean mergeGroupBlocksRatesIfMissing;
    private Boolean mergeGroupBlocksForHeaderOnlyCancel;
    private String adjustReservationNetRates;
    private String webhookToken;
    private Boolean runGroupAutoWash;
    private Boolean folsUseCloudProcessing;
    private Boolean rraUseCloudProcessing;
    private Boolean adjustIdpSoldsUsingSkewingFactor;
    private Integer decisionsThreshold;
    private List<String> pseudoRoomTypes;
    private Boolean usePastInventoryForStatsAlways;
    private Boolean ignoreRateDetails;
    private Integer groupPastDays;
    private Boolean oxiUseCloudProcessing;
    private String cloudMigrationStatus;
    private Boolean htngUseCloudProcessing;
    private Boolean htngCallbackOnCloudDisabled;

    @Override
    public VendorCredentials getOutboundCredentials() {
        return outboundCredentials;
    }

    @Override
    public void setOutboundCredentials(VendorCredentials outboundCredentials) {
        this.outboundCredentials = outboundCredentials;
    }

    @Override
    public VendorCredentials getInboundCredentials() {
        return inboundCredentials;
    }

    @Override
    public void setInboundCredentials(VendorCredentials inboundCredentials) {
        this.inboundCredentials = inboundCredentials;
    }

    public void cleanUpEmptyChainData() {
        if (getInboundCredentials() != null && StringUtils.isBlank(getInboundCredentials().getUsername()) && StringUtils.isBlank(getInboundCredentials().getPassword())) {
            setInboundCredentials(null);
        }
        if (getOutboundCredentials() != null && StringUtils.isBlank(getOutboundCredentials().getUsername()) && StringUtils.isBlank(getOutboundCredentials().getPassword())) {
            setOutboundCredentials(null);
        }

        cleanUpEmptyHotelData();
    }

    private void cleanUpEmptyHotelData() {
        for (HotelConfigParams hotel : getHotels()) {
            hotel.cleanUpEmptyHotelData();
        }
    }

    public Boolean getComponentRoomsActivityEnabled() {
        return isComponentRoomsActivityEnabled;
    }

    public void setComponentRoomsActivityEnabled(Boolean componentRoomsActivityEnabled) {
        this.isComponentRoomsActivityEnabled = componentRoomsActivityEnabled;
    }
}
