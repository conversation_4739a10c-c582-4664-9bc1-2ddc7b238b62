package com.ideas.tetris.pacman.services.walkme;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.services.opera.OperaTransactionHistory;
import com.ideas.tetris.pacman.services.opera.entity.HistoryGroupBlock;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.CORRELATION_ID;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.DATA_LOAD_METADATA_ID;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.GET_DATA_LOAD_METADATA_QUERY;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.INSERT_INTO_RAW_TRANSACTION_QUERY;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.MARKET_CODE_1;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.MARKET_CODE_2;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.MARKET_SEGMENT;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ONE;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.RATE_CODE;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.RATE_CODE_1;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.RATE_CODE_2;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.RATE_CODE_3;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.RATE_CODE_4;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ROOM_TYPE_1;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ROOM_TYPE_2;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.THREE;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.TWO;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.WALK_ME_BARTER;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.WALK_ME_DISCOUNT;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.WALK_ME_GROUP_MARKET_SEGMENT;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.WALK_ME_RACK;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.getCurrentDate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class WalkMeCookDataForMarketSegmentConfigurationService {

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    @Autowired
    WalkMeCookDataService walkMeCookDataService;

    public void createDataForMarketSegmentConfiguration() {
        enableAnalyticalMarketSegment();
        addRequirementsForMSC();
        addMarketSegments();
        addGroupMarketSegment();
        addHistoryGroupBlocks();
        addOperaRawTransactions();
    }

    public void addGroupMarketSegment() {
        tenantCrudService.executeUpdateByNativeQuery("insert into mkt_seg ([Property_ID] ,[Mkt_Seg_Code]  ,[Mkt_Seg_Name]  ,[Mkt_Seg_Description] ,[Status_ID],[Last_Updated_DTTM],[Is_Editable],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID]) values(" + PacmanWorkContextHelper.getPropertyId() + ", '" + WALK_ME_GROUP_MARKET_SEGMENT + "', '" + WALK_ME_GROUP_MARKET_SEGMENT + "', '" + WALK_ME_GROUP_MARKET_SEGMENT + "', 1, GETDATE(), 1, 1, GETDATE(), 1)");
    }

    public void addOperaRawTransactions() {
        tenantCrudService.executeUpdateByNativeQuery(INSERT_INTO_RAW_TRANSACTION_QUERY,
                QueryParameter.with(DATA_LOAD_METADATA_ID, getDataLoadMetadataID()).and(RATE_CODE, RATE_CODE_1).and(MARKET_SEGMENT, MARKET_CODE_1).parameters());
        tenantCrudService.executeUpdateByNativeQuery(INSERT_INTO_RAW_TRANSACTION_QUERY,
                QueryParameter.with(DATA_LOAD_METADATA_ID, getDataLoadMetadataID()).and(RATE_CODE, RATE_CODE_2).and(MARKET_SEGMENT, MARKET_CODE_1).parameters());
        tenantCrudService.executeUpdateByNativeQuery(INSERT_INTO_RAW_TRANSACTION_QUERY,
                QueryParameter.with(DATA_LOAD_METADATA_ID, getDataLoadMetadataID()).and(RATE_CODE, RATE_CODE_3).and(MARKET_SEGMENT, MARKET_CODE_2).parameters());
        tenantCrudService.executeUpdateByNativeQuery(INSERT_INTO_RAW_TRANSACTION_QUERY,
                QueryParameter.with(DATA_LOAD_METADATA_ID, getDataLoadMetadataID()).and(RATE_CODE, RATE_CODE_4).and(MARKET_SEGMENT, MARKET_CODE_2).parameters());
    }

    public void enableAnalyticalMarketSegment() {
        configParamsService.updateParameterValue(walkMeCookDataService.getContextPath(), FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value(), Constants.TRUE);
    }

    public void addRequirementsForMSC() {
        Integer dataLoadMetadataID = getDataLoadMetadataID();
        OperaTransactionHistory operaTransactionHistory = new OperaTransactionHistory();
        operaTransactionHistory.setArrivalDate(getCurrentDate().toString());
        operaTransactionHistory.setDataLoadMetadataId(dataLoadMetadataID);
        tenantCrudService.save(operaTransactionHistory);
    }

    public void addMarketSegments() {
        MarketSegmentSummary marketSegmentSummaryBarter = createMarketSegmentSummary(WALK_ME_BARTER);
        MarketSegmentSummary marketSegmentSummaryDiscount = createMarketSegmentSummary(WALK_ME_DISCOUNT);
        MarketSegmentSummary marketSegmentSummaryRack = createMarketSegmentSummary(WALK_ME_RACK);
        tenantCrudService.save(Arrays.asList(marketSegmentSummaryBarter, marketSegmentSummaryDiscount, marketSegmentSummaryRack));
    }

    private MarketSegmentSummary createMarketSegmentSummary(String marketSegmentName) {
        MarketSegmentSummary marketSegmentSummary = new MarketSegmentSummary();
        marketSegmentSummary.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        marketSegmentSummary.setName(marketSegmentName);
        marketSegmentSummary.setCode(marketSegmentName);
        marketSegmentSummary.setDescription(marketSegmentName);
        marketSegmentSummary.setStatusId(1);
        marketSegmentSummary.setEditable(1);
        return marketSegmentSummary;
    }

    public void addHistoryGroupBlocks() {
        List<HistoryGroupBlock> historyGroupBlockList = new ArrayList<>();
        historyGroupBlockList.add(addDataIntoOperaHistoryGroupBlock(ONE, ROOM_TYPE_1));
        historyGroupBlockList.add(addDataIntoOperaHistoryGroupBlock(TWO, ROOM_TYPE_2));
        tenantCrudService.save(historyGroupBlockList);
    }

    private HistoryGroupBlock addDataIntoOperaHistoryGroupBlock(String groupMasterID, String roomType) {
        Integer dataLoadMetadataID = getDataLoadMetadataID();
        HistoryGroupBlock historyGroupBlock = new HistoryGroupBlock();
        historyGroupBlock.setGroupId(groupMasterID);
        historyGroupBlock.setBlock(THREE);
        String date = DateUtil.formatDate(getCurrentDate(), DateUtil.DEFAULT_DATE_FORMAT);
        historyGroupBlock.setBlockDate(date);
        historyGroupBlock.setPickup(ONE);
        historyGroupBlock.setRoomType(roomType);
        historyGroupBlock.setDataLoadMetadataId(dataLoadMetadataID);
        return historyGroupBlock;
    }

    private Integer getDataLoadMetadataID() {
        return tenantCrudService.findByNativeQuerySingleResult
                (GET_DATA_LOAD_METADATA_QUERY, QueryParameter.with("correlationID", CORRELATION_ID).parameters());
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }
}
