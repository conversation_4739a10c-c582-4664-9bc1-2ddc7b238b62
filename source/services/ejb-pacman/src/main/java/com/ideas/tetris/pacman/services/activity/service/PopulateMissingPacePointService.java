package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceMktSegActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceTotalActivity;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import javax.inject.Inject;
import java.sql.Time;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PopulateMissingPacePointService {

    public static final String MISSING_SNAPSHOT_PACE_POINT = "PopulateMissingSnapshotPacePoint";
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;
    @Autowired
    FileMetadataService fileMetadataService;


    public int populateMissingPacePointMktSegActivity() {
        return tenantCrudService.executeUpdateByNamedQuery(PaceMktSegActivity.POPULATE_MISSING_PACE_POINT,
                getParameters());
    }


    public int populateMissingPacePointAccomActivity() {
        return tenantCrudService.executeUpdateByNamedQuery(PaceAccomActivity.POPULATE_MISSING_PACE_POINT,
                getParameters());
    }


    public int populateMissingPacePointTotalActivity() {
        return tenantCrudService.executeUpdateByNamedQuery(PaceTotalActivity.POPULATE_MISSING_PACE_POINT,
                getParameters());
    }


    public Collection<FileMetadata> addMissingSnapShots() {
        List<Object[]> missingSnapShots = getMissingSnapShots();
        FileMetadata lastSuccessfulProcessingRecord = fileMetadataService.getLastSuccessfulProcessingRecord(PacmanWorkContextHelper.getPropertyId(), RecordType.T2SNAP_RECORD_TYPE_ID);
        List<FileMetadata> fileMetadataList = new ArrayList<>();
        for (Object[] date : missingSnapShots) {
            FileMetadata fileMetadata = new FileMetadata();
            fileMetadata.setSnapshotDt((Date) date[0]);
            fileMetadata.setSnapshotTm((Time) date[1]);
            fileMetadata.setPreparedDt((Date) date[0]);
            fileMetadata.setPreparedTm((Time) date[1]);
            fileMetadata.setBde(lastSuccessfulProcessingRecord.getBde());
            fileMetadata.setRecordTypeId(lastSuccessfulProcessingRecord.getRecordTypeId());
            fileMetadata.setFileLocation(MISSING_SNAPSHOT_PACE_POINT);
            fileMetadata.setFileName(MISSING_SNAPSHOT_PACE_POINT);
            fileMetadata.setProcessStatusId(lastSuccessfulProcessingRecord.getProcessStatusId());
            fileMetadata.setTenantPropertyId(lastSuccessfulProcessingRecord.getTenantPropertyId());
            fileMetadata.setPastWindowSize(lastSuccessfulProcessingRecord.getPastWindowSize());
            fileMetadata.setFutureWindowSize(lastSuccessfulProcessingRecord.getFutureWindowSize());
            fileMetadataList.add(fileMetadata);
        }
        return tenantCrudService.save(fileMetadataList);
    }

    protected List<Object[]> getMissingSnapShots() {
        return tenantCrudService.findByNativeQuery("SELECT r.calendar_date AS missing_snapshot_dt, " +
                "       miss.snapshot_tm AS missing_snapshot_tm " +
                "FROM   (SELECT DISTINCT calendar_date " +
                "        FROM   calendar_dim AS caldm " +
                "               LEFT JOIN file_metadata AS fm " +
                "                      ON caldm.calendar_date = snapshot_dt " +
                "        WHERE  caldm.calendar_date BETWEEN :startDate  and :endDate " +
                "               AND snapshot_dt IS NULL) r " +
                "       CROSS apply (SELECT TOP 1 Dateadd(day, -1, fmd.snapshot_dt) AS " +
                "                                 previous_date, " +
                "                                 fmd.snapshot_tm                   AS " +
                "                                 Snapshot_TM " +
                "                    FROM   file_metadata AS fmd " +
                "                    WHERE  fmd.snapshot_dt <= Dateadd(day, -1, r.calendar_date) " +
                "                    ORDER  BY fmd.snapshot_dt DESC) AS miss", getParameters());
    }

    private Map<String, Object> getParameters() {
        Date lastBDEDate = fileMetadataService.getLastBDEDate();
        Date endDate = DateUtil.addDaysToDate(lastBDEDate, -1);
        Date startDate = DateUtil.addDaysToDate(lastBDEDate, -4);
        return QueryParameter.with("startDate", DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT))
                .and("endDate", DateUtil.formatDate(endDate, DateUtil.DEFAULT_DATE_FORMAT)).parameters();
    }
}
