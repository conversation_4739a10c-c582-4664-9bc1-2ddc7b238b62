package com.ideas.tetris.pacman.services.analytics.services;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.analytics.entity.AnalyticsConfig;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;

@Component
@Transactional
public class AnalyticsConfigService {

    private static final Integer DEFAULT_USER_ID = 1;

    public static final String NEXT_IDP_WINDOW_OVERRIDE = "NEXT_IDP_WINDOW_OVERRIDE";

    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    public CrudService tenantCrudService;

    @Autowired
    public PacmanConfigParamsService pacmanConfigParamsService;

    public void updateNextIdpWindowOverride(String value){
        updateAnalyticsConfigParamValue(NEXT_IDP_WINDOW_OVERRIDE, value);
    }

    public Integer getNextIdpWindowOverride(){
        Boolean useAnalyticsConfigParam = pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.USE_ANALYTICS_CONFIG_ENABLED);
        return useAnalyticsConfigParam ?
                getIntegerParameterValue(NEXT_IDP_WINDOW_OVERRIDE) :
                (Integer)pacmanConfigParamsService.getParameterValue(IPConfigParamName.NEXT_IDP_WINDOW_OVERRIDE);
    }

    public Integer getNextClientIdpWindowOverride(){
        Boolean useAnalyticsConfigParam = pacmanConfigParamsService.getBooleanParameterValueByClientLevel(IPConfigParamName.USE_ANALYTICS_CONFIG_ENABLED.getParameterName());
        return useAnalyticsConfigParam ?
                getIntegerParameterValue(NEXT_IDP_WINDOW_OVERRIDE) :
                (Integer)pacmanConfigParamsService.getIntegerParameterValueByClientLevel(IPConfigParamName.NEXT_IDP_WINDOW_OVERRIDE);
    }

    public void updateAnalyticsConfigParamValue(String configParamName, String value) {
        AnalyticsConfig configParam = tenantCrudService.findByNamedQuerySingleResult(
                AnalyticsConfig.FIND_BY_CONFIG_PARAM_NAME,
                QueryParameter.with("name", configParamName).parameters());

        Date now = new Date();
        Integer userId = getUserId();

        if (configParam == null) {
            configParam = new AnalyticsConfig();
            configParam.setCreatedByUserId(userId);
            configParam.setCreateDate(now);
        }

        configParam.setValue(null != value ? value : configParam.getValue());
        configParam.setLastUpdatedDate(now);
        configParam.setLastUpdatedByUserId(userId);
    }

    public AnalyticsConfig getAnalyticsConfigParam(String configParamName) {
        return tenantCrudService.findByNamedQuerySingleResult(
                    AnalyticsConfig.FIND_BY_CONFIG_PARAM_NAME,
                    QueryParameter.with("name", configParamName).parameters());

    }

    public Integer getIntegerParameterValue(String configParamName) {
        return Optional.ofNullable(getAnalyticsConfigParam(configParamName))
                .map(AnalyticsConfig::getValue)
                .map(Integer::parseInt)
                .orElse(-1);

    }

    private Integer getUserId() {
        String userId = PacmanWorkContextHelper.getUserId();
        if (userId == null || userId.isEmpty()) {
            return DEFAULT_USER_ID;
        }
        try {
            return Integer.parseInt(userId);
        }
        catch (Exception e) {
            return DEFAULT_USER_ID;
        }
    }
}
