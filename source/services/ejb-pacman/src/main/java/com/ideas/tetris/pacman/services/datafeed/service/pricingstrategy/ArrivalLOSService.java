package com.ideas.tetris.pacman.services.datafeed.service.pricingstrategy;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.entity.ArrivalConfiguration;
import com.ideas.tetris.pacman.services.datafeed.entity.LOSConfiguration;
import com.ideas.tetris.pacman.services.unqualifiedrate.serivce.RateUnqualifiedService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;

import javax.inject.Inject;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class ArrivalLOSService {

    public static final String MAX = "Max";
    public static final String MIN = "Min";
    @Autowired
	private RateUnqualifiedService rateService;
    @Autowired
	private PacmanConfigParamsService paramsService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    public List<ArrivalConfiguration> getValidatedArrivalConfigurations(Date date) {
        return isConfiguredFor(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value()) ? getArrivalConfigurations(date) : Collections.emptyList();
    }

    public List<LOSConfiguration> getValidatedLosConfigurations(Date date) {
        return isConfiguredFor(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value()) ? getLosConfigurations(date) : Collections.emptyList();
    }

    private boolean isConfiguredFor(String parameterName) {
        return paramsService.getBooleanParameterValue(parameterName);
    }

    private List<ArrivalConfiguration> getArrivalConfigurations(Date date) {
        return tenantCrudService.findByNamedQuery(ArrivalConfiguration.FIND_BY_DATE,
                QueryParameter.with("startDate", DateUtil.formatDate(date, DateUtil.DEFAULT_DATE_FORMAT)).parameters());
    }

    private List<LOSConfiguration> getLosConfigurations(Date date) {
        return tenantCrudService.findByNamedQuery(LOSConfiguration.FIND_MIN_MAX_BY_DATE,
                QueryParameter.with("startDate", DateUtil.formatDate(date, DateUtil.DEFAULT_DATE_FORMAT)).parameters());
    }
}

