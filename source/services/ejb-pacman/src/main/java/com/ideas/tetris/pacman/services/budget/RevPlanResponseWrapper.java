package com.ideas.tetris.pacman.services.budget;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@NoArgsConstructor
public class RevPlanResponseWrapper {
    List<RevPlanResponse> result;

    @Getter
    @NoArgsConstructor
    public static class RevPlanResponse {
        String occupancyDate;
        String segmentCode;
        String segmentName;
        Double rn;
        Double revenue;

    }
}
