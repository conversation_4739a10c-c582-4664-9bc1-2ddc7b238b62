package com.ideas.tetris.pacman.services.dashboard.vo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class MonthlyDataVO implements Serializable {

    private Map<String, BasicPricingDailyCalendarVO> basicPricingDailyCalendarVO;
    private BasicPricingSummaryVO basicPricingSummaryVO;
    private Map<String, List<BasicPricingSpecialEventVO>> specialEventsMap;
    private String systemToday;
    private boolean isBARUploadButtonEnabled;
    private boolean isBARUploadButtonConfigured;
    private boolean isExcludeCompHouseEnabled;
    private boolean isCeilingEnabled;
    private boolean isContinuousPricingEnabled;
    private boolean isForceSyncCalibrationFlagActive;
    private boolean forceSyncCalibrationJobStatus;
    private boolean propertyReadOnlyStatus;

    public Map<String, BasicPricingDailyCalendarVO> getBasicPricingDailyCalendarVO() {
        return basicPricingDailyCalendarVO;
    }

    public void setBasicPricingDailyCalendarVO(
            Map<String, BasicPricingDailyCalendarVO> basicPricingDailyCalendarVO) {
        this.basicPricingDailyCalendarVO = basicPricingDailyCalendarVO;
    }

    public boolean isCeilingEnabled() {
        return isCeilingEnabled;
    }

    public void setCeilingEnabled(boolean ceilingEnabled) {
        isCeilingEnabled = ceilingEnabled;
    }

    public BasicPricingSummaryVO getBasicPricingSummaryVO() {
        return basicPricingSummaryVO;
    }

    public void setBasicPricingSummaryVO(BasicPricingSummaryVO basicPricingSummaryVO) {
        this.basicPricingSummaryVO = basicPricingSummaryVO;
    }

    public void setSpecialEventsMap(Map<String, List<BasicPricingSpecialEventVO>> specialEventsMap) {
        this.specialEventsMap = specialEventsMap;
    }

    public Map<String, List<BasicPricingSpecialEventVO>> getSpecialEventsMap() {
        return specialEventsMap;
    }

    public void setSystemToday(String systemToday) {
        this.systemToday = systemToday;
    }

    public String getSystemToday() {
        return systemToday;
    }

    public boolean isBARUploadButtonEnabled() {
        return isBARUploadButtonEnabled;
    }

    public void setBARUploadButtonEnabled(boolean isBARUploadButtonEnabled) {
        this.isBARUploadButtonEnabled = isBARUploadButtonEnabled;
    }

    public boolean isBARUploadButtonConfigured() {
        return isBARUploadButtonConfigured;
    }

    public void setBARUploadButtonConfigured(boolean isBARUploadButtonConfigured) {
        this.isBARUploadButtonConfigured = isBARUploadButtonConfigured;
    }

    public boolean isExcludeCompHouseEnabled() {
        return isExcludeCompHouseEnabled;
    }

    public void setExcludeCompHouseEnabled(boolean isExcludeCompHouseEnabled) {
        this.isExcludeCompHouseEnabled = isExcludeCompHouseEnabled;
    }

    public boolean isContinuousPricingEnabled() {
        return isContinuousPricingEnabled;
    }

    public void setContinuousPricingEnabled(boolean continuousPricingEnabled) {
        isContinuousPricingEnabled = continuousPricingEnabled;
    }

    public boolean isForceSyncCalibrationJobStatus() {
        return forceSyncCalibrationJobStatus;
    }

    public void setForceSyncCalibrationJobStatus(boolean forceSyncCalibrationJobStatus) {
        this.forceSyncCalibrationJobStatus = forceSyncCalibrationJobStatus;
    }

    public boolean isForceSyncCalibrationFlagActive() {
        return isForceSyncCalibrationFlagActive;
    }

    public void setForceSyncCalibrationFlagActive(boolean forceSyncCalibrationFlagActive) {
        isForceSyncCalibrationFlagActive = forceSyncCalibrationFlagActive;
    }

    public boolean isPropertyReadOnlyStatus() {
        return propertyReadOnlyStatus;
    }

    public void setPropertyReadOnlyStatus(boolean propertyReadOnlyStatus) {
        this.propertyReadOnlyStatus = propertyReadOnlyStatus;
    }
}
