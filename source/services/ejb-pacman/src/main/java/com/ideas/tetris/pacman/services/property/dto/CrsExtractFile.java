package com.ideas.tetris.pacman.services.property.dto;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.regex.Pattern;

@SuppressWarnings("serial")
public class CrsExtractFile extends File {

    private static final Logger LOGGER = Logger.getLogger(CrsExtractFile.class.getName());

    private static final String DELIMITER = ".";
    private static final String DATE_FORMAT = "yyyyMMdd";
    private static final String DATE_TIME_FORMAT = "yyyyMMddHHmm";
    private static final String FOLDER_FORMAT = "yyyy_MM";

    private String propertyCode;
    private String dateString;
    private String timeString;
    private Date date;
    private Date dateTime;
    private boolean isHistoryFile;
    private String folderName;

    public CrsExtractFile(String filename) {
        super(filename);
        init(getName());
    }

    public CrsExtractFile(File parent, String child) {
        super(parent, child);
        init(getName());
    }

    private void init(String filename) {
        // a valid extract file name is of one of two formats:
        // PROPCODE.[date formatted as yyyymm].[time formatted as hhmm].tar.Z
        //    or
        // PROPCODE.[date formatted as yyyymm].[time formatted as hhmm>.history.tar.Z
        //
        // i.e. TESTPROP.201207.2315.tar.Z or TESTPROP.201207.2315.history.tar.Z
        if (filename != null) {
            String[] segments = filename.split("[" + DELIMITER + "]");
            if ((segments.length == 5 || (segments.length == 6 && "history".equalsIgnoreCase(segments[3])))
                    && "tar".equals(segments[segments.length - 2])
                    && ExtractFileFilter.lastSegmentZipOrZ(segments[segments.length - 1])) {

                isHistoryFile = segments.length == 6;

                propertyCode = segments[0];
                dateString = segments[1];
                timeString = segments[2];

                if (Pattern.matches("\\d{8}", dateString)) {
                    try {
                        date = new SimpleDateFormat(DATE_FORMAT).parse(dateString);
                        folderName = new SimpleDateFormat(FOLDER_FORMAT).format(date);
                    } catch (ParseException e) {
                        LOGGER.warn("Could not parse string: " + dateString + " or folderName:" + folderName + " using specified formats.");
                    }
                }
                if (Pattern.matches("\\d{12}", dateString + timeString)) {
                    try {
                        dateTime = new SimpleDateFormat(DATE_TIME_FORMAT).parse(dateString + timeString);
                    } catch (ParseException e) {
                        LOGGER.warn("Could not parse string: " + dateString + " using specified formats.");
                    }
                }
            }
        }
    }

    public String getPropertyCode() {
        return propertyCode;
    }

    public String getDateString() {
        return dateString;
    }

    public String getTimeString() {
        return timeString;
    }

    public Date getDate() {
        return date;
    }

    public Date getDateTime() {
        return dateTime;
    }

    /**
     * For this class a valid ExtractFile will end in .tar.Z, have a non-empty property code, and parseable date/time segments.
     *
     * @return
     */
    public boolean isValid() {
        return !StringUtils.isEmpty(propertyCode) && date != null && dateTime != null;
    }

    public boolean isHistoryFile() {
        return isHistoryFile;
    }

    /**
     * Return the name of the folder we would expect this file to be in
     *
     * @return
     */
    public String getFolderName() {
        return folderName;
    }

    /**
     * Returns filename without the tar.Z
     *
     * @return
     */
    public String getBaseFilename() {
        return StringUtils.join(Arrays.asList(propertyCode, dateString, timeString), DELIMITER);
    }
}
