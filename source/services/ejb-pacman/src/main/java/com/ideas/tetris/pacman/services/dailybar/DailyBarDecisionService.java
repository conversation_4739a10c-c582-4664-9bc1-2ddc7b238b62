package com.ideas.tetris.pacman.services.dailybar;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.CPRecommendationService;
import com.ideas.tetris.pacman.services.configsparam.service.ConfigParameterNameService;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dailybar.constants.DailyBarConstants;
import com.ideas.tetris.pacman.services.dailybar.entity.DailyBarDecisions;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.decisiontranslator.DecisionUtils;
import com.ideas.tetris.pacman.services.pricing.ProductManagementService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.rest.annotation.DateFormat;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutput.DECISION_DAILYBAR_OUTPUT;
import static com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutputNonHiltonCRS.DECISION_DAILYBAR_OUTPUT_NON_HILTON_CRS;
import static com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceDailyBarOutput.PACE_DAILYBAR_OUTPUT;
import static com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceDailyBarOutputNonHiltonCRS.PACE_DAILYBAR_OUTPUT_NON_HILTON_CRS;
import static org.apache.commons.lang.StringUtils.lowerCase;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class DailyBarDecisionService {

    private static final Logger LOGGER = Logger.getLogger(DailyBarDecisionService.class.getName());
    private static final String TAX_FACTOR = "taxFactor";
    private static final String MISC_ADJUSTMENT = "miscAdjustment";
    private static final String DECISION_START_DATE = "decisionStartDate";
    private static final String DECISION_END_DATE = "decisionEndDate";
    private static final String DATES = "dates";
    private static final String PRODUCT_ID = "productId";
    private static final String LATEST_FULL_REFRESH_DEC_ID = "latestFullRefreshDecId";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService crudService;

    @Autowired
    DateService dateService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    ExternalSystemHelper externalSystemHelper;

    @Autowired
	private CPRecommendationService cpRecommendationService;

    @Autowired
	private DecisionService decisionService;

    @Autowired
    ConfigParameterNameService configParameterNameService;

    @Autowired
	private ProductManagementService productManagementService;

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    public void setDecisionService(DecisionService decisionService) {
        this.decisionService = decisionService;
    }

    public List<DailyBarDecisions> getDailyBarDecisions(@DateFormat Date lastUploadedDate, String externalSystem) {
        double taxFactor = 0d;
        double miscAdjustment = 0d;
        String roundingOffParameterName = configParameterNameService.getIntegrationParameterName(lowerCase(externalSystem), Constants.ROUNDING_OFF);
        boolean applyRoundOFF = Boolean.parseBoolean(pacmanConfigParamsService.getParameterValue(roundingOffParameterName));
        return retrieveShortStayDailyBarDecisions(taxFactor, lastUploadedDate, miscAdjustment, getPrecisionForRoundOff(applyRoundOFF));
    }

    public List<DailyBarDecisions> getDailyBarDecisions(String taxAdjustment, @DateFormat Date lastUploadedDate, String precision) {
        double taxFactor = DecisionUtils.calculateTaxFactorForDecisions(taxAdjustment);
        return retrieveShortStayDailyBarDecisions(taxFactor, lastUploadedDate, 0d, getIntFromString(precision));
    }

    public List<DailyBarDecisions> getAllDailyBarDecisions(String taxAdjustment, Date lastUploadedDate, Date lastUploadedDateES, String precision, String miscAdjustment, String shortStayRateCode, String externalSystem) {
        double taxFactor = DecisionUtils.calculateTaxFactorForDecisions(taxAdjustment);
        return retrieveAllDailyBarDecisions(taxFactor, lastUploadedDate, lastUploadedDateES, Double.parseDouble(miscAdjustment), getIntFromString(precision), shortStayRateCode, externalSystem);
    }

    public Map<String, List<DailyBarDecisions>> getDailyBarDecisions(String taxAdjustment, Date lastUploadedDateSS, Date lastUploadedDateES, String precision, String miscAdjustment, String ssRateCode, String externalSystem) {
        double taxFactor = DecisionUtils.calculateTaxFactorForDecisions(taxAdjustment);
        return retrieveAllDailyBarDecisionsPerRateCode(taxFactor, lastUploadedDateSS, lastUploadedDateES, Double.parseDouble(miscAdjustment), getIntFromString(precision), ssRateCode);
    }

    public List<DailyBarDecisions> getDailyBarDecisions(boolean shouldApplyTax, @DateFormat Date lastUploadedDate, String externalSystem) {
        return retrieveShortStayDailyBarDecisions(deriveTaxFactor(shouldApplyTax, externalSystem), lastUploadedDate,
                deriveMiscAdjustment(externalSystem), derivePrecisionForRoundOff(externalSystem), false);
    }

    public List<DailyBarDecisions> getDailyBarDecisionsForAgileRates(
            String agileRateCode,
            boolean shouldApplyTax,
            Date lastUploadedDate,
            String externalSystem,
            Boolean retrieveOne) {
        return retrieveShortStayDailyBarDecisionsForAgileRates(agileRateCode, deriveTaxFactor(shouldApplyTax, externalSystem),
                lastUploadedDate, deriveMiscAdjustment(externalSystem), derivePrecisionForRoundOff(externalSystem), retrieveOne);
    }

    private int derivePrecisionForRoundOff(String externalSystem) {
        String roundingOffParameterName = configParameterNameService.getIntegrationParameterName(lowerCase(externalSystem), Constants.ROUNDING_OFF);
        boolean applyRoundOFF = Boolean.parseBoolean(pacmanConfigParamsService.getParameterValue(roundingOffParameterName));

        return getPrecisionForRoundOff(applyRoundOFF);
    }

    private double deriveMiscAdjustment(String externalSystem) {
        double miscAdjustment = 0d;
        String miscAdjustmentValueParameterName = configParameterNameService.getIntegrationParameterName(lowerCase(externalSystem), Constants.MISC_ADJUSTMENT_VALUE);
        if (isExternalSystemEligibleForMiscAdjustment(externalSystem)) {
            miscAdjustment = Double.parseDouble(pacmanConfigParamsService.getParameterValue(miscAdjustmentValueParameterName));
        }
        return miscAdjustment;
    }

    private double deriveTaxFactor(boolean shouldApplyTax, String externalSystem) {
        LOGGER.info("Apply Tax on Daily Bar Decisions is : " + shouldApplyTax);
        if (shouldApplyTax) {
            return DecisionUtils.calculateTaxFactorForDecisions(getTaxParameterValue(externalSystem));
        }

        return BigDecimal.ZERO.doubleValue();
    }

    private String getTaxParameterValue(final String externalSystem) {
        final String taxParameterName =
                configParameterNameService.getIntegrationParameterName(lowerCase(externalSystem), Constants.TAX_ADJUSTMENT_VALUE);

        return pacmanConfigParamsService.getParameterValue(taxParameterName);
    }

    public List<DailyBarDecisions> retrieveShortStayDailyBarDecisionsForAgileRates(
            String agileRateCode, double taxFactor, Date lastUploadedDate, double miscAdjustmentValue,
            int precision, boolean retrieveOne) {

        if (agileRateCode == null) {
            return new ArrayList<>();
        }

        List<Object[]> decisionsFromDB;
        final Product product = crudService.findByNamedQuerySingleResult
                (Product.GET_ALL_BY_NAME,
                        QueryParameter.with("name", agileRateCode).parameters());

        if (product == null) {
            return new ArrayList<>();
        }

        if (null == lastUploadedDate) {
            LOGGER.info("Fetching FULL AgileRates Decisions.");
            Map<String, Object> map = QueryParameter.with(TAX_FACTOR, taxFactor)
                    .and(DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                    .and(DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate())
                    .and(MISC_ADJUSTMENT, miscAdjustmentValue)
                    .and(PRODUCT_ID, product.getId()).parameters();

            String fullUploadQuery = DailyBarConstants.getDecisionDailyBarOutputFullForAgileRateUpToDecimal(retrieveOne, precision);
            fullUploadQuery = replaceNonHiltonCRSTables(fullUploadQuery, true, product);
            decisionsFromDB = crudService.findByNativeQuery(fullUploadQuery, map);
        } else {
            LOGGER.info("Fetching DIFFERENTIAL AgileRates Decisions.");
            String differentialDecisionQuery = getDifferentialDecisionQueryForAgileRate(retrieveOne, precision);
            differentialDecisionQuery = replaceNonHiltonCRSTables(differentialDecisionQuery, false, product);
            final BigInteger latestFullRefreshDecId = decisionService.getFullRefreshDecisionToCompare();
            LOGGER.info("Full Refresh Decision_ID to compare : " + latestFullRefreshDecId);
            Map<String, Object> map = QueryParameter.with(TAX_FACTOR, taxFactor)
                    .and(DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                    .and(DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate())
                    .and("lastUploadedDate", lastUploadedDate)
                    .and(MISC_ADJUSTMENT, miscAdjustmentValue)
                    .and(PRODUCT_ID, product.getId())
                    .and(LATEST_FULL_REFRESH_DEC_ID, latestFullRefreshDecId).parameters();

            decisionsFromDB = crudService.findByNativeQuery(differentialDecisionQuery, map);
        }
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)) {
            return createDailyBarDecisionsAsPerContractUploadChildBuckets(decisionsFromDB, precision == 0, true);
        } else {
            return createDailyBarDecisionsAsPerContractUploadChildBuckets(decisionsFromDB, precision == 0, false);
        }
    }

    private String replaceNonHiltonCRSTables(String uploadQuery, boolean isFullUpload, Product product) {
        if (shouldReplaceNonHiltonCRSTables(product)) {
            final String replacedQuery = uploadQuery.replace(DECISION_DAILYBAR_OUTPUT, DECISION_DAILYBAR_OUTPUT_NON_HILTON_CRS);
            return isFullUpload ? replacedQuery : replacedQuery.replace(PACE_DAILYBAR_OUTPUT, PACE_DAILYBAR_OUTPUT_NON_HILTON_CRS);
        }
        return uploadQuery;
    }

    protected boolean shouldReplaceNonHiltonCRSTables(Product product) {
        return PacmanWorkContextHelper.isHiltonClientCode() && cpRecommendationService.isIHotelierEnabledAndProductSendByAdjustment(product);
    }


    public Map<String, List<DailyBarDecisions>> getDailyBarDecisions(boolean shouldApplyTax, @DateFormat Date lastUploadedDate
            , String externalSystem, String ssRateCode) {
        //in case of ratchet, we have same upload date for all decisions, thus this rest will use same upload date for Short stay and Extended Stay.
        return getDailyBarDecisions(shouldApplyTax, lastUploadedDate, lastUploadedDate, externalSystem, ssRateCode);
    }


    public Map<String, List<DailyBarDecisions>> getDailyBarDecisions(boolean shouldApplyTax, @DateFormat Date lastUploadedDateSS
            , @DateFormat Date lastUploadedDateES, String externalSystem, String ssRateCode) {
        LOGGER.info("Apply Tax on Daily Bar Decisions is : " + shouldApplyTax);
        double taxFactor = 0d;
        double miscAdjustment = 0d;
        String roundingOffParameterName = configParameterNameService.getIntegrationParameterName(lowerCase(externalSystem), Constants.ROUNDING_OFF);
        String applyTaxValueParameterName = configParameterNameService.getIntegrationParameterName(lowerCase(externalSystem), Constants.TAX_ADJUSTMENT_VALUE);
        String miscAdjustmentValueParameterName = configParameterNameService.getIntegrationParameterName(lowerCase(externalSystem), Constants.MISC_ADJUSTMENT_VALUE);
        boolean applyRoundOFF = Boolean.parseBoolean(pacmanConfigParamsService.getParameterValue(roundingOffParameterName));
        if (shouldApplyTax) {
            String taxValueStr = pacmanConfigParamsService.getParameterValue(applyTaxValueParameterName);
            taxFactor = DecisionUtils.calculateTaxFactorForDecisions(taxValueStr);
        }
        if (isExternalSystemEligibleForMiscAdjustment(externalSystem)) {
            miscAdjustment = Double.parseDouble(pacmanConfigParamsService.getParameterValue(miscAdjustmentValueParameterName));
        }

        return retrieveAllDailyBarDecisionsPerRateCode(taxFactor, lastUploadedDateSS, lastUploadedDateES, miscAdjustment, getPrecisionForRoundOff(applyRoundOFF), ssRateCode);
    }


    public DailyBarDecisions create(String roomType,
                                    @DateFormat Date occupancyDate,
                                    BigDecimal adultRate,
                                    BigDecimal childRate,
                                    BigDecimal doubleRate,
                                    BigDecimal singleRate,
                                    String ratePlan) {

        DailyBarDecisions dailyBarDecisions = new DailyBarDecisions();
        dailyBarDecisions.setRoomType(roomType);
        dailyBarDecisions.setOccupancyDate(occupancyDate);
        dailyBarDecisions.setAdultRate(childRate);
        dailyBarDecisions.setChildRate(childRate);
        dailyBarDecisions.setDoubleRate(doubleRate);
        dailyBarDecisions.setSingleRate(singleRate);
        dailyBarDecisions.setRatePlan(ratePlan);
        crudService.save(dailyBarDecisions);
        return dailyBarDecisions;
    }


    public DailyBarDecisions create(String roomType,
                                    @DateFormat Date occupancyDate,
                                    BigDecimal adultRate,
                                    BigDecimal childRate,
                                    BigDecimal doubleRate,
                                    BigDecimal singleRate,
                                    String ratePlan,
                                    Integer ratePlanId,
                                    Integer rateTypeId) {
        DailyBarDecisions dailyBarDecisions = new DailyBarDecisions();
        dailyBarDecisions.setRoomType(roomType);
        dailyBarDecisions.setOccupancyDate(occupancyDate);
        dailyBarDecisions.setAdultRate(childRate);
        dailyBarDecisions.setChildRate(childRate);
        dailyBarDecisions.setDoubleRate(doubleRate);
        dailyBarDecisions.setSingleRate(singleRate);
        dailyBarDecisions.setRatePlan(ratePlan);
        dailyBarDecisions.setRatePlanId(ratePlanId);
        dailyBarDecisions.setRoomTypeId(rateTypeId);
        crudService.save(dailyBarDecisions);
        return dailyBarDecisions;
    }

    public Map<String, List<DailyBarDecisions>> getAllDecisionsForTheseDates(String taxAdjustment, String precision, List<Date> dates, String miscAdjustmentValue, String rateCode, boolean forAllRoomClasses, boolean byRoomClass, Date lastUploadedDate, String externalSystem) {
        LOGGER.info("Fetching FULL for Differential Dailybar Decisions.");
        Map<String, List<DailyBarDecisions>> decisionsMap = new HashMap<>();

        List<Object[]> decisionsFromDB = getAllDbDecisionForDates(dates, miscAdjustmentValue, precision, taxAdjustment, forAllRoomClasses, byRoomClass, lastUploadedDate);
        decisionsMap.put(rateCode, createDailyBarDecisionsAsPerContractUploadChildBuckets(decisionsFromDB, getIntFromString(precision) == 0, false));
        return decisionsMap;
    }

    public List<DailyBarDecisions> getAllDecisionForDates(final boolean applyTax,
                                                          final Date lastUploadedDate,
                                                          final String externalSystem,
                                                          final List<Date> dates) {

        final double miscAdjustment = deriveMiscAdjustment(externalSystem);
        final int precision = derivePrecisionForRoundOff(externalSystem);
        final String taxAdjustment = applyTax ? getTaxParameterValue(externalSystem) : StringUtils.EMPTY;

        final List<Object[]> decisions = getAllDbDecisionForDates(
                dates,
                String.valueOf(miscAdjustment), String.valueOf(precision), taxAdjustment,
                true, false,
                lastUploadedDate);

        return createDailyBarDecisionsAsPerContractUploadChildBuckets(decisions, precision == 0, false);
    }

    public List<DailyBarDecisions> getAllDecisionForDates(List<Date> dates, String miscAdjustmentValue, String precision, String taxAdjustment, boolean forAllRoomClasses, boolean byRoomClass, Date lastUploadedDate, String externalSystem) {
        List<Object[]> decisionsFromDB = getAllDbDecisionForDates(dates, miscAdjustmentValue, precision, taxAdjustment, forAllRoomClasses, byRoomClass, lastUploadedDate);
        return createDailyBarDecisionsAsPerContractUploadChildBuckets(decisionsFromDB, getIntFromString(precision) == 0, false);
    }

    private List<Object[]> getAllDbDecisionForDates(List<Date> dates, String miscAdjustmentValue, String precision, String taxAdjustment, boolean forAllRoomClasses, boolean byRoomClass, Date lastUploadedDate) {
        double taxFactor = DecisionUtils.calculateTaxFactorForDecisions(taxAdjustment);
        if (forAllRoomClasses) {
            return crudService.findByNativeQuery(DailyBarConstants.getAllDecisionDailyBarOutputFullForAllRoomClassesUpToDecimalForDates(getIntFromString(precision)),
                    QueryParameter.with(TAX_FACTOR, taxFactor)
                            .and(DATES, dates)
                            .and(MISC_ADJUSTMENT, miscAdjustmentValue)
                            .parameters());
        } else if (byRoomClass) {
            return crudService.findByNativeQuery(DailyBarConstants.getDecisionDailyBarOutputDifferentialByRoomClassUpToDecimalForDates(getIntFromString(precision)),
                    QueryParameter.with(TAX_FACTOR, taxFactor)
                            .and(DATES, dates)
                            .and(MISC_ADJUSTMENT, miscAdjustmentValue)
                            .and("lastUploadedDate", lastUploadedDate)
                            .parameters());
        }
        return crudService.findByNativeQuery(DailyBarConstants.getDecisionDailyBarOutputFullUpToDecimalForDates(getIntFromString(precision)),
                QueryParameter.with(TAX_FACTOR, taxFactor)
                        .and(DATES, dates)
                        .and(MISC_ADJUSTMENT, miscAdjustmentValue)
                        .parameters());
    }

    @SuppressWarnings("unchecked")
    private List<DailyBarDecisions> retrieveShortStayDailyBarDecisions(double taxFactor, Date lastUploadedDate, double miscAdjustmentValue, int precision) {
        List<Object[]> decisionsFromDB;
        if (null == lastUploadedDate) {
            LOGGER.info("Fetching FULL Dailybar Decisions.");
            decisionsFromDB = crudService.findByNativeQuery(DailyBarConstants.getDecisionDailyBarOutputFullUpToDecimal(precision, false), QueryParameter.with(TAX_FACTOR, taxFactor).and(DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                    .and(DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate()).and(MISC_ADJUSTMENT, miscAdjustmentValue).parameters());
        } else {
            LOGGER.info("Fetching DIFFERENTIAL Dailybar Decisions.");
            final BigInteger latestFullRefreshDecId = decisionService.getFullRefreshDecisionToCompare();
            LOGGER.info("Full Refresh Decision_ID to compare : " + latestFullRefreshDecId);
            decisionsFromDB = crudService.findByNativeQuery(DailyBarConstants.getDecisionDailyBarOutputDifferentialUpToDecimal(precision, false), QueryParameter.with(TAX_FACTOR, taxFactor).and(DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                    .and(DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate()).and("lastUploadedDate", lastUploadedDate).and(MISC_ADJUSTMENT, miscAdjustmentValue)
                    .and(LATEST_FULL_REFRESH_DEC_ID, latestFullRefreshDecId).parameters());

        }
        return createDailyBarDecisionsAsPerContractUploadChildBuckets(decisionsFromDB, precision == 0, false);
    }

    private List<DailyBarDecisions> retrieveShortStayDailyBarDecisions(double taxFactor, Date lastUploadedDate, double miscAdjustmentValue, int precision, boolean isIndependentProductFlow) {
        List<Object[]> decisionsFromDB;
        if (null == lastUploadedDate) {
            LOGGER.info("Fetching FULL Dailybar Decisions.");
            decisionsFromDB = crudService.findByNativeQuery(DailyBarConstants.getDecisionDailyBarOutputFullUpToDecimal(precision, isIndependentProductFlow),
                    QueryParameter.with(TAX_FACTOR, taxFactor).and(DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                            .and(DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate()).and(MISC_ADJUSTMENT, miscAdjustmentValue).parameters());
        } else {
            LOGGER.info("Fetching DIFFERENTIAL Dailybar Decisions.");
            String differentialDecisionQuery = getDifferentialDecisionQuery(precision, isIndependentProductFlow);
            final BigInteger latestFullRefreshDecId = decisionService.getFullRefreshDecisionToCompare();
            LOGGER.info("Full Refresh Decision_ID to compare : " + latestFullRefreshDecId);
            decisionsFromDB = crudService.findByNativeQuery(differentialDecisionQuery, QueryParameter.with(TAX_FACTOR, taxFactor).and(DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                    .and(DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate()).and("lastUploadedDate", lastUploadedDate).and(MISC_ADJUSTMENT, miscAdjustmentValue)
                    .and(LATEST_FULL_REFRESH_DEC_ID, latestFullRefreshDecId).parameters());

        }
        // assign product_name to the decision
        return createDailyBarDecisionsAsPerContractUploadChildBuckets(decisionsFromDB, precision == 0, isIndependentProductFlow);
    }

    private String getDifferentialDecisionQuery(Integer precision, boolean isIndependentProductFlow) {
        return DailyBarConstants.getDecisionDailyBarOutputDifferentialUpToDecimalOperaChildBuckets(precision, isIndependentProductFlow);
    }

    private String getDifferentialDecisionQueryForAgileRate(boolean retrieveOne, Integer roundingDepth) {
        return DailyBarConstants.getDecisionDailyBarOutputDifferentialUpToDecimalOperaChildBucketsAgileRates(retrieveOne, roundingDepth);
    }

    private List<DailyBarDecisions> retrieveExtendedStayDailyBarDecisions(double taxFactor, double miscAdjustmentValue, int precision, Date lastUploadedDate) {
        List<Object[]> esDailyBarDecisions;
        if (null == lastUploadedDate) {
            esDailyBarDecisions = crudService.findByNativeQuery(DailyBarConstants.getESDecisionDailyBarOutputFull(precision)
                    , QueryParameter.with(TAX_FACTOR, taxFactor)
                            .and(MISC_ADJUSTMENT, miscAdjustmentValue)
                            .and(DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                            .and(DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate())
                            .parameters());
        } else {
            esDailyBarDecisions = crudService.findByNativeQuery(DailyBarConstants.getESDecisionDailyBarOutputDifferential(precision)
                    , QueryParameter.with(TAX_FACTOR, taxFactor)
                            .and(MISC_ADJUSTMENT, miscAdjustmentValue)
                            .and(DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                            .and(DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate())
                            .and("lastUploadDate", lastUploadedDate)
                            .parameters());
        }

        return createDailyBarDecisionsAsPerContractUploadChildBuckets(esDailyBarDecisions, precision == 0, false);
    }

    @SuppressWarnings("unchecked")
    private List<DailyBarDecisions> retrieveAllDailyBarDecisions(double taxFactor, Date lastUploadedDateSS, Date lastUploadedDateES, double miscAdjustmentValue, int precision, String shortStayRateCode, String externalSystem) {
        List<DailyBarDecisions> ssDailyBarDecisions = retrieveShortStayDailyBarDecisions(taxFactor, lastUploadedDateSS, miscAdjustmentValue, precision, false);
        ssDailyBarDecisions.forEach(decision -> decision.setRatePlan(shortStayRateCode));
        if (pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED.value())) {
            List<DailyBarDecisions> esDailyBarDecisions = retrieveExtendedStayDailyBarDecisions(taxFactor, miscAdjustmentValue, precision, lastUploadedDateES);
            ssDailyBarDecisions.addAll(esDailyBarDecisions);
        }
        return ssDailyBarDecisions;
    }

    @SuppressWarnings("unchecked")
    private Map<String, List<DailyBarDecisions>> retrieveAllDailyBarDecisionsPerRateCode(double taxFactor, Date lastUploadedDateSS, Date lastUploadedDateES, double miscAdjustmentValue, int precision, String ssRateCode) {
        Map<String, List<DailyBarDecisions>> allDailyBarDecisionsPerRateCode;
        allDailyBarDecisionsPerRateCode = new HashMap<>();
        List<DailyBarDecisions> ssDailyBarDecisions = retrieveShortStayDailyBarDecisions(taxFactor, lastUploadedDateSS, miscAdjustmentValue, precision, false);
        if (CollectionUtils.isNotEmpty(ssDailyBarDecisions)) {
            allDailyBarDecisionsPerRateCode.put(ssRateCode, ssDailyBarDecisions);
        }
        //if ES Decisions are turned OFF, do not fetch them
        if (pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED.value())) {
            List<DailyBarDecisions> esDailyBarDecisions = retrieveExtendedStayDailyBarDecisions(taxFactor, miscAdjustmentValue, precision, lastUploadedDateES);
            Map<String, List<DailyBarDecisions>> esDailyBarPerRateCode = setESDailyBarDecisionsAsPerContract(esDailyBarDecisions);
            allDailyBarDecisionsPerRateCode.putAll(esDailyBarPerRateCode);
        }
        return allDailyBarDecisionsPerRateCode;
    }

    private List<DailyBarDecisions> createDailyBarDecisionsAsPerContractUploadChildBuckets(List<Object[]> decisionsFromDB, boolean applyRoundOFF, boolean isIndependentProductFlow) {
        List<DailyBarDecisions> dailyBarDecisionList = new ArrayList<>();
        Map<Integer, String> uploadEnabledIndependentProducts = getUploadEnabledIndependentProducts(isIndependentProductFlow);
        for (Object[] row : decisionsFromDB) {
            DailyBarDecisions dailyBarDecisions = new DailyBarDecisions();
            dailyBarDecisions.setOccupancyDate((Date) row[0]);
            dailyBarDecisions.setRoomType((String) row[1]);
            dailyBarDecisions.setRatePlan(getRatePlan(row, uploadEnabledIndependentProducts, isIndependentProductFlow));
            dailyBarDecisions.setSingleRate((getBigDecimal(row[3], applyRoundOFF)));
            dailyBarDecisions.setDoubleRate((getBigDecimal(row[4], applyRoundOFF)));
            dailyBarDecisions.setTripleRate((getBigDecimal(row[5], applyRoundOFF)));
            dailyBarDecisions.setQuadRate((getBigDecimal(row[6], applyRoundOFF)));
            dailyBarDecisions.setQuintRate((getBigDecimal(row[7], applyRoundOFF)));
            dailyBarDecisions.setAdultRate((getBigDecimal(row[8], applyRoundOFF)));
            dailyBarDecisions.setChildRate((getBigDecimal(row[9], applyRoundOFF)));
            dailyBarDecisions.setOneChildRate((getBigDecimal(row[10], applyRoundOFF)));
            dailyBarDecisions.setTwoChildRate((getBigDecimal(row[11], applyRoundOFF)));
            dailyBarDecisions.setThreeChildRate((getBigDecimal(row[12], applyRoundOFF)));
            dailyBarDecisions.setFourChildRate((getBigDecimal(row[13], applyRoundOFF)));
            dailyBarDecisions.setFiveChildRate((getBigDecimal(row[14], applyRoundOFF)));
            dailyBarDecisions.setChildAgeOneRate((getBigDecimal(row[15], applyRoundOFF)));
            dailyBarDecisions.setChildAgeTwoRate((getBigDecimal(row[16], applyRoundOFF)));
            dailyBarDecisions.setChildAgeThreeRate((getBigDecimal(row[17], applyRoundOFF)));
            dailyBarDecisions.setChildAgeFourRate((getBigDecimal(row[18], applyRoundOFF)));
            dailyBarDecisionList.add(dailyBarDecisions);
        }
        return dailyBarDecisionList;
    }

    private Map<Integer, String> getUploadEnabledIndependentProducts(boolean isIndpendentProductFlow) {
        Map<Integer, String> uploadEnabledIndependentProducts = new HashMap<>();
        if (isIndpendentProductFlow) {
            uploadEnabledIndependentProducts = productManagementService.getUploadEnabledIndependentProducts()
                    .stream().collect(Collectors.toMap(Product::getId, Product::getName));
        }
        return uploadEnabledIndependentProducts;
    }

    private String getRatePlan(Object[] row, Map<Integer, String> uploadEnabledProducts, boolean isIndependentProductFlow) {
        return isIndependentProductFlow ? uploadEnabledProducts.get(((BigInteger) row[19]).intValue()) :
                (String) row[2];
    }

    private BigDecimal getBigDecimal(Object o, boolean roundOff) {
        if (null == o || (o instanceof BigDecimal)) {
            return (BigDecimal) o;
        }
        return roundOff ? BigDecimal.valueOf((Double) o).setScale(0) : BigDecimal.valueOf((Double) o);
    }

    private Map<String, List<DailyBarDecisions>> setESDailyBarDecisionsAsPerContract(List<DailyBarDecisions> dailyBarDecisions) {
        Map<String, List<DailyBarDecisions>> dailyBarDecisionsPerRatePlan = new HashMap<>();
        List<DailyBarDecisions> dailyBarDecisionList;
        for (DailyBarDecisions dailyBarDecision : dailyBarDecisions) {
            String ratePlan = dailyBarDecision.getRatePlan();
            if (dailyBarDecisionsPerRatePlan.containsKey(ratePlan)) {
                dailyBarDecisionList = dailyBarDecisionsPerRatePlan.get(ratePlan);
            } else {
                dailyBarDecisionList = new ArrayList<>();
            }
            dailyBarDecisionList.add(dailyBarDecision);
            dailyBarDecisionsPerRatePlan.put(ratePlan, dailyBarDecisionList);
        }
        return dailyBarDecisionsPerRatePlan;
    }

    private int getIntFromString(String stringValue) {
        int returnInt = 0;
        if (StringUtils.isNotBlank(stringValue)) {
            returnInt = Integer.parseInt(stringValue);
        }
        return returnInt;
    }

    private int getPrecisionForRoundOff(boolean applyRoundOFF) {
        if (applyRoundOFF) {
            return 0;
        } else {
            return 2;
        }
    }

    private boolean isExternalSystemEligibleForMiscAdjustment(String externalSystem) {
        return externalSystem.equalsIgnoreCase(Constants.OPERA) || externalSystem.equalsIgnoreCase(Constants.REZVIEW) || externalSystem.equalsIgnoreCase(Constants.ORS)
                || externalSystem.equalsIgnoreCase(Constants.MYFIDELIO);
    }

    @VisibleForTesting
    public void setConfigParameterNameService(ConfigParameterNameService configParameterNameService) {
        this.configParameterNameService = configParameterNameService;
    }
}
