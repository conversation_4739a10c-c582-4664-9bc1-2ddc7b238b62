package com.ideas.tetris.pacman.services.learning;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.platform.services.Stage;
import org.apache.commons.lang3.StringUtils;

public enum LMSProperty {

    URBANCLUB("320011", setParametersForUrbanClubProperty()),
    COASTAL("320014", setParametersForCoastalProperty()),
    CITYSTAY("320015", setParametersForCityStayProperty()),
    DELMAR("320016", setParametersForDelmarProperty()),
    CANYON("320017", setParametersForCanyonProperty()),
    MAJESTICPINES("320018", setParametersForMajesticpinesProperty()),

    INITIAL("720011", setParametersForInitialProperty()),
    ROOMS("720012", setParametersForRoomsProperty()),
    PRICING("720013", setParametersForPricingProperty()),
    COMPLETE("720014", setParametersForCompleteProperty());

    public static final String OPERA = "opera";
    public static final String AMERICA_PHOENIX = "America/Phoenix";
    private final String endsWith;
    private final String startsWith;
    private final String defaultTemplateName;

    private LMSPropertyParameters parameters;

    private LMSProperty(String defaultTemplateName, LMSPropertyParameters parameters) {
        this.defaultTemplateName = defaultTemplateName;
        this.endsWith = String.valueOf(defaultTemplateName.charAt(defaultTemplateName.length() - 1));
        this.startsWith = String.valueOf(defaultTemplateName.charAt(0));
        this.parameters = parameters;
    }

    private static LMSPropertyParameters setParametersForUrbanClubProperty() {
        LMSPropertyParameters parameters = new LMSPropertyParameters();
        parameters.setBarDecision(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        parameters.setStage(Stage.TWO_WAY);
        parameters.setContinuousPricing(true);
        parameters.setGroupPricingEnabled(true);
        parameters.setGroupPricingMultiPropertyEnabled(true);
        parameters.setFunctionSpaceEnabled(false);
        parameters.setLDB(false);
        parameters.setPropertyTimeZone("America/Phoenix");
        parameters.setExternalSystem("opera");
        parameters.setWebrateAlertsEnabled(false);
        parameters.setManualBarUploadEnabled(true);
        parameters.setSTREnabled(false);
        parameters.setMarketPerformanceEnabled(true);
        parameters.setComponentRoomsEnabled(false);
        parameters.setDemand360Enabled(true);
        parameters.setAgileRatesEnabled(true);
        parameters.setUseCompactWebratePace(true);
        parameters.setPricingRedesignEnabled(true);
        parameters.setSupplementalEnabled(true);
        parameters.setAnalyticalAgileRateRCEnabled(true);
        parameters.setQualifiedRatePlanConfigurationEnabled(true);
        parameters.setNewPricingInvestigatorEnabled(true);
        parameters.setFixedAboveBARProductEnabled(true);
        parameters.setLinkedProductHierarchyEnabled(true);
        return parameters;
    }

    private static LMSPropertyParameters setParametersForCoastalProperty() {
        LMSPropertyParameters parameters = new LMSPropertyParameters();
        parameters.setBarDecision(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        parameters.setContinuousPricing(true);
        parameters.setGroupPricingEnabled(true);
        parameters.setFunctionSpaceEnabled(false);
        parameters.setLDB(true);
        parameters.setPropertyTimeZone("Pacific/Honolulu");
        parameters.setExternalSystem(OPERA);
        parameters.setWebrateAlertsEnabled(true);
        parameters.setManualBarUploadEnabled(true);
        parameters.setSTREnabled(true);
        parameters.setMarketPerformanceEnabled(false);
        parameters.setComponentRoomsEnabled(true);
        parameters.setDemand360Enabled(true);
        parameters.setAMSEnabled(false);
        parameters.setExtendedStayEnabled(false);
        parameters.setAdvancedPriceRankEnabled(true);
        parameters.setGroupPricingMultiPropertyEnabled(true);
        parameters.setRRAEnabled(true);
        parameters.setPerPersonPricingEnabled(true);
        parameters.setChildAgeBucketsEnabled(true);
        parameters.setUploadChildAgeBucketsEnabled(true);
        parameters.setUploadAdultsBeyond2Enabled(true);
        parameters.setUploadChildrenBeyondExtraEnabled(true);
        parameters.setPricingRedesignEnabled(true);
        parameters.setSupplementalEnabled(false);
        parameters.setAgileRatesEnabled(true);
        parameters.setNewPricingInvestigatorEnabled(true);
        parameters.setFixedAboveBARProductEnabled(true);
        parameters.setLinkedProductHierarchyEnabled(true);
        return parameters;
    }

    private static LMSPropertyParameters setParametersForCityStayProperty() {
        LMSPropertyParameters parameters = new LMSPropertyParameters();
        parameters.setBarDecision(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        parameters.setContinuousPricing(false);
        parameters.setGroupPricingEnabled(true);
        parameters.setFunctionSpaceEnabled(false);
        parameters.setLDB(false);
        parameters.setPropertyTimeZone(AMERICA_PHOENIX);
        parameters.setExternalSystem(OPERA);
        parameters.setWebrateAlertsEnabled(false);
        parameters.setManualBarUploadEnabled(true);
        parameters.setSTREnabled(false);
        parameters.setMarketPerformanceEnabled(false);
        parameters.setComponentRoomsEnabled(false);
        parameters.setDemand360Enabled(false);
        parameters.setAMSEnabled(true);
        parameters.setExtendedStayEnabled(true);
        parameters.setAdvancedPriceRankEnabled(false);
        parameters.setGroupPricingMultiPropertyEnabled(true);
        parameters.setRRAEnabled(false);
        parameters.setPricingRedesignEnabled(false);
        parameters.setSupplementalEnabled(true);
        parameters.setAgileRatesEnabled(false);
        parameters.setEnablePriceDropRestrictionsForDOWAndSeasons(false);
        return parameters;
    }

    private static LMSPropertyParameters setParametersForDelmarProperty() {
        LMSPropertyParameters parameters = new LMSPropertyParameters();
        parameters.setBarDecision(Constants.BAR_DECISION_VALUE_LOS);
        parameters.setContinuousPricing(false);
        parameters.setGroupPricingEnabled(false);
        parameters.setFunctionSpaceEnabled(true);
        parameters.setLDB(false);
        parameters.setPropertyTimeZone("America/Phoenix");
        parameters.setExternalSystem("opera");
        parameters.setWebrateAlertsEnabled(true);
        parameters.setManualBarUploadEnabled(true);
        parameters.setSTREnabled(false);
        parameters.setMarketPerformanceEnabled(true);
        parameters.setComponentRoomsEnabled(false);
        parameters.setDemand360Enabled(true);
        parameters.setAgileRatesEnabled(false);
        parameters.setUseCompactWebratePace(true);
        parameters.setGroupPricingEnabled(false);
        parameters.setPricingRedesignEnabled(false);
        parameters.setSupplementalEnabled(true);
        parameters.setFunctionSpacePackageEnabled(true);
        parameters.setEnablePriceDropRestrictionsForDOWAndSeasons(false);
        return parameters;
    }

    private static LMSPropertyParameters setParametersForCanyonProperty() {
        LMSPropertyParameters parameters = new LMSPropertyParameters();
        parameters.setBarDecision(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        parameters.setStage(Stage.TWO_WAY);
        parameters.setContinuousPricing(true);
        parameters.setGroupPricingEnabled(true);
        parameters.setLDB(false);
        parameters.setPropertyTimeZone("America/Phoenix");
        parameters.setDemand360Enabled(true);
        parameters.setExternalSystem("opera");
        parameters.setGroupPricingEnabled(true);
        parameters.setSupplementalEnabled(true);
        parameters.setAnalyticalAgileRateRCEnabled(true);
        parameters.setAgileRatesEnabled(true);
        parameters.setMarketPerformanceEnabled(true);
        parameters.setPricingRedesignEnabled(true);
        parameters.setGroupPricingMultiPropertyEnabled(true);
        parameters.setNewPricingInvestigatorEnabled(true);
        parameters.setIndependentProductsEnabled(true);
        parameters.setIncludeIndependentProductsInReportEnabled(true);
        parameters.setIndependentAndLinkedForAtAGlanceEnabled(true);
        parameters.setProductDimensionForCompetitiveNotificationsEnabled(true);
        parameters.setUseMktAccomActivityForPastDataEnabled(true);
        parameters.setUseOldProductFamilyIdEnabled(true);
        parameters.setFixedAboveBARProductEnabled(true);
        parameters.setLinkedProductHierarchyEnabled(true);
        return parameters;
    }

    private static LMSPropertyParameters setParametersForMajesticpinesProperty() {
        LMSPropertyParameters parameters = new LMSPropertyParameters();
        parameters.setBarDecision(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        parameters.setStage(Stage.TWO_WAY);
        parameters.setContinuousPricing(true);
        parameters.setGroupPricingEnabled(true);
        parameters.setLDB(false);
        parameters.setPropertyTimeZone("America/Phoenix");
        parameters.setDemand360Enabled(true);
        parameters.setExternalSystem("opera");
        parameters.setGroupPricingEnabled(true);
        parameters.setSupplementalEnabled(true);
        parameters.setAnalyticalAgileRateRCEnabled(true);
        parameters.setAgileRatesEnabled(true);
        parameters.setMarketPerformanceEnabled(true);
        parameters.setPricingRedesignEnabled(true);
        parameters.setGroupPricingMultiPropertyEnabled(true);
        parameters.setNewPricingInvestigatorEnabled(true);
        parameters.setIndependentProductsEnabled(true);
        parameters.setIncludeIndependentProductsInReportEnabled(true);
        parameters.setIndependentAndLinkedForAtAGlanceEnabled(true);
        parameters.setProductDimensionForCompetitiveNotificationsEnabled(true);
        parameters.setUseMktAccomActivityForPastDataEnabled(true);
        parameters.setUseOldProductFamilyIdEnabled(true);
        parameters.setFixedAboveBARProductEnabled(true);
        parameters.setLinkedProductHierarchyEnabled(true);
        parameters.setPerPersonPricingEnabled(true);
        parameters.setPricingInvestigatorFetchCompetitorPricesFromAllChannels(true);
        parameters.setChannelCostEnabled(true);
        parameters.setChannelCostV3(true);
        parameters.setPricingInvestigatorFetchCompetitorPricesFromAllChannels(true);
        parameters.setAdvancedPriceRankEnabled(true);
        parameters.setUseCpPaceDiffrentialTableEnabled(true);
        return parameters;
    }
    private static LMSPropertyParameters setParametersForInitialProperty() {
        LMSPropertyParameters parameters = new LMSPropertyParameters();
        parameters.setBarDecision(Constants.BAR_DECISION_VALUE_RATEOFDAY);
        parameters.setStage(Stage.ONE_WAY);
        parameters.setContinuousPricing(true);
        parameters.setPropertyTimeZone("America/Nassau");
        parameters.setExternalSystem("opera");
        parameters.setManualBarUploadEnabled(true);
        parameters.setDemand360Enabled(true);
        parameters.setAdvancedPriceRankEnabled(true);
        parameters.setGroupPricingEnabled(true);
        parameters.setAgileRatesEnabled(true);
        parameters.setUseCompactWebratePace(true);
        parameters.setPricingRedesignEnabled(true);
        parameters.setSupplementalEnabled(true);
        parameters.setNewPricingInvestigatorEnabled(true);
        parameters.setFixedAboveBARProductEnabled(true);
        parameters.setLinkedProductHierarchyEnabled(true);
        return parameters;
    }
    private static LMSPropertyParameters setParametersForRoomsProperty() {
        LMSPropertyParameters parameters = setParametersForInitialProperty();
        return parameters;
    }
    private static LMSPropertyParameters setParametersForPricingProperty() {
        LMSPropertyParameters parameters = setParametersForInitialProperty();
        return parameters;
    }
    private static LMSPropertyParameters setParametersForCompleteProperty() {
        LMSPropertyParameters parameters = setParametersForInitialProperty();
        return parameters;
    }


    public LMSPropertyParameters getParameters() {
        return parameters;
    }

    public String getEndsWith() {
        return endsWith;
    }
    public String getStartsWith() {
        return startsWith;
    }

    public String getTemplateName() {
        return System.getProperty("lmsProperty" + StringUtils.capitalize(name().toLowerCase()), defaultTemplateName);
    }

    public static LMSProperty getLMSProperty(Integer propertyId) {
        if (propertyId != null) {
            for (LMSProperty lmsProperty : values()) {
                if (StringUtils.startsWith(propertyId.toString(), lmsProperty.getStartsWith()) &&
                        StringUtils.endsWith(propertyId.toString(), lmsProperty.getEndsWith())) {
                    return lmsProperty;
                }
            }
        }
        return null;
    }

    public static String getPropertyCode(Integer propertyId) {
        LMSProperty lmsProperty = getLMSProperty(propertyId);
        if (lmsProperty != null) {
            return lmsProperty.name() + (propertyId % 1000);
        }
        return null;
    }

    public static String getPropertyName(Integer propertyId) {
        LMSProperty lmsProperty = getLMSProperty(propertyId);
        if (lmsProperty != null) {
            return lmsProperty.name();
        }
        return null;
    }

    public static String getTemplateName(Integer propertyId) {
        LMSProperty lmsProperty = getLMSProperty(propertyId);
        if (lmsProperty != null) {
            return lmsProperty.getTemplateName();
        }
        return null;
    }

    public static Stage getStage(Integer propertyId) {
        LMSProperty lmsProperty = getLMSProperty(propertyId);
        if (lmsProperty != null) {
            return lmsProperty.getParameters().getStage();
        }
        return Stage.ONE_WAY;
    }
}
