package com.ideas.tetris.pacman.services.rates;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetaDataBuffer;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.ratepopulation.dtos.RateUnqualifiedDetailsDto;
import com.ideas.tetris.pacman.services.unqualifiedrate.component.RateUnqualifiedComponent;
import com.ideas.tetris.pacman.services.unqualifiedrate.dto.RateHeader;
import com.ideas.tetris.pacman.services.unqualifiedrate.dto.Season;
import com.ideas.tetris.pacman.services.unqualifiedrate.dto.SeasonDetail;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.*;
import com.ideas.tetris.pacman.services.unqualifiedrate.enums.ActionKeyEnum;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.util.streamutils.StreamUtils.keepOnly;

@Component
@Transactional
public class UnqualifiedRateConverter extends RateConverter {

    @Autowired
    RateUnqualifiedComponent rateUnqualifiedComponent;

    @Override
    protected void deleteDetailsStartingFrom(List<Integer> ids, Date minStartDate) {
        tenantCrudService.executeUpdateByNamedQuery(RateUnqualifiedDetails.DELETE_BY_RATE_UNQUALIFIED_IDS_AND_START_DATE, QueryParameter.with("rateUnqualifiedIds", ids).and("date", minStartDate).parameters());
    }

    @Override
    protected AbstractRate getHeader(String name) {
        return findEligibleRate(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, name);
    }

    @Override
    protected List<AbstractDetail> getDetails(List<Integer> ids) {
        return tenantCrudService.findByNamedQuery(RateUnqualifiedDetails.GET_ALL_BY_RATE_UNQUALIFIED_IDS, QueryParameter.with("rateUnqualifiedIdList", ids).parameters());
    }

    @Override
    protected AbstractRate createNewHeader() {
        return new RateUnqualified();
    }

    @Override
    protected AbstractDetail createNewDetail() {
        return new RateUnqualifiedDetails();
    }

    @Override
    protected AbstractDetail createNewDetail(AbstractDetail detail) {
        return detail.cloneDetail();
    }

    @Override
    protected void convertSpecificFields(Map<String, Object> dto, AbstractRate rateHeader) {
        ((RateUnqualified) rateHeader).setSystemDefault(0);
        ((RateUnqualified) rateHeader).setDerivedRateCode((String) dto.get("derivedRateCode"));
    }

    @Override
    protected void setHeaderReferenceOnDetails(AbstractRate rate, AbstractDetail detail) {
        ((RateUnqualifiedDetails) detail).setRateUnqualifiedId(rate.getId());
    }


    private void saveUnqualifiedMaxLOS(List<AbstractRate> rateHeaders) {
        for (AbstractRate rateHeader : rateHeaders) {
            int maxLOS = rateUnqualifiedComponent.getMaxLOS();
            List<RateUnqualifiedAccomClass> rateUnqualifiedAccomClasses = tenantCrudService.findByNamedQuery(RateUnqualifiedAccomClass.GET_ACCOM_CLASS_ID_BY_RATE_UNQUALIFIED_ID, QueryParameter.with("id", rateHeader.getId()).parameters());
            if (rateUnqualifiedAccomClasses.isEmpty()) {
                rateUnqualifiedComponent.saveRateUnqualifiedRoomClassMapping(rateHeader.getId());
                rateUnqualifiedComponent.saveRateUnqualifiedDefaults(rateHeader.getId(), maxLOS);
            }
        }
    }

    @Override
    protected void softDeleteHeader(Integer fileMetaDataId, AbstractRate rateHeader, Date caughtUpDate) {
        rateHeader.setStatusId(INACTIVE);
        RateHeader rateHeaderObject = new RateHeader();
        populateHeader(rateHeader, rateHeaderObject);
        List<RateHeader> rateHeaders = rateUnqualifiedComponent.fetchDetails(Arrays.asList((RateUnqualified) rateHeader));
        if (CollectionUtils.isNotEmpty(rateHeaders) && CollectionUtils.isNotEmpty(rateHeaders.get(0).getSeasons())) {
            rateHeaderObject.setSeasons(setActionKeys(rateHeaders.get(0).getSeasons(), caughtUpDate));
        }
        rateUnqualifiedComponent.saveUnqualifiedRates(Arrays.asList(rateHeaderObject), fileMetaDataId);
    }

    @Override
    protected void saveHeaders(List<RateHeaderAndDetail> completeRates) {
        List<AbstractRate> headers = completeRates.stream().map(RateHeaderAndDetail::getHeader).collect(Collectors.toList());
        tenantCrudService.save(headers);
        saveUnqualifiedMaxLOS(headers);
    }

    @Override
    protected void truncateEndDateOfSeasonsWitinRangeOfGivenDate(Integer headerId, Date dateToTruncateDetailsFrom) {
        List<RateUnqualifiedDetails> seasonsToUpdate =
                tenantCrudService.findByNamedQuery(RateUnqualifiedDetails.BY_RATE_PLAN_ID_AND_ACTIVE_ON_DATE, QueryParameter.with("rateUnqualifiedId", headerId).and("date", dateToTruncateDetailsFrom).parameters());
        seasonsToUpdate
                .forEach(rateUnqualifiedDetail -> {
                    if (!dateToTruncateDetailsFrom.equals(rateUnqualifiedDetail.getStartDate())) {
                        rateUnqualifiedDetail.setEndDate(DateUtil.addDaysToDate(dateToTruncateDetailsFrom, -1));
                        rateUnqualifiedDetail.clearOutOfRangeDOWsWithRangeCheck();
                        tenantCrudService.save(rateUnqualifiedDetail);
                    }
                });
    }


    private List<Season> setActionKeys(List<Season> seasons, Date caughtUpDate) {
        DateParameter caughtUpDateParameter = caughtUpDate != null ? new DateParameter(caughtUpDate) : dateService.getCaughtUpDateParameter();
        for (Season season : seasons) {
            DateParameter detailsEndDate = season.getDetailsEndDate();
            DateParameter detailsStartDate = season.getDetailsStartDate();
            if (detailsEndDate.getTime().before(caughtUpDateParameter.getTime())) {
                setActionKeyOnDetails(season, ActionKeyEnum.UNCHANGED);
            } else if (detailsStartDate.getTime().before(caughtUpDateParameter.getTime())) {
                season.setActionKey(ActionKeyEnum.UPDATE);
                season.setDetailsEndDate(DateParameter.fromDate(DateUtil.addDaysToDate(caughtUpDateParameter.getTime(), -1)));
                setActionKeyOnDetails(season, ActionKeyEnum.UPDATE);
                setNegativeOne(season);
            } else {
                season.setActionKey(ActionKeyEnum.DELETE);
                setActionKeyOnDetails(season, ActionKeyEnum.DELETE);
            }
        }
        return seasons;
    }

    private void setActionKeyOnDetails(Season season, ActionKeyEnum key) {
        List<SeasonDetail> newDetails = new ArrayList<>();
        for (SeasonDetail detail : season.getSeasonDetails()) {
            if (!detail.hasNoRateValueForAllDows()) {
                detail.setActionKey(key);
                newDetails.add(detail);
            }
        }
        season.setSeasonDetails(newDetails);
    }

    @Override
    protected FileMetadata createFileMetadata(Map<String, Object> dto) {
        Date dateTimeByTimeZone = DateUtil.getDateTimeByTimeZone(DateUtil.getCurrentDate(), dateService.getPropertyTimeZone());
        String today = DateUtil.formatDate(dateTimeByTimeZone, "yyyyMMdd_HHmm");
        String fileName = String.format("%s_%s_%s_%s_Rates", "NGI", dto.get("clientCode"), dto.get("propertyCode"), today);
        FileMetaDataBuffer metaDataBuffer = new FileMetaDataBuffer(fileName, Constants.UNQUALIFIED_RATE_PLAN, (String) dto.get("correlationId"));
        metaDataBuffer.eventDate(dateTimeByTimeZone);
        FileMetadata metaData = metaDataBuffer.value();
        return tenantCrudService.save(metaData);
    }

    private void setNegativeOne(Season season) {
        for (SeasonDetail seasonDetail : season.getSeasonDetails()) {
            cleanUpLastWeek(season.getDetailsEndDate().getTime(), seasonDetail.toEntity(seasonDetail.getId(), season));
        }
    }

    private void populateHeader(AbstractRate abstractRate, RateHeader rateHeaderObject) {
        rateHeaderObject.setActionKey(ActionKeyEnum.DELETE);
        rateHeaderObject.setCreatedateDTTM(abstractRate.getCreateDate());
        rateHeaderObject.setCurrency(abstractRate.getCurrency());
        rateHeaderObject.setDerivedRateCode(((RateUnqualified) abstractRate).getDerivedRateCode());
        rateHeaderObject.setDescription(abstractRate.getDescription());
        rateHeaderObject.setFileMetadataId(abstractRate.getFileMetadataId());
        rateHeaderObject.setId(abstractRate.getId());
        rateHeaderObject.setIncludesPackage(abstractRate.getIncludesPackage());
        rateHeaderObject.setLastUpdateDTTM(abstractRate.getLastUpdatedDate());
        rateHeaderObject.setName(abstractRate.getName());
        rateHeaderObject.setPriceRelative(rateHeaderObject.getPriceRelative());
        rateHeaderObject.setPropertyId(abstractRate.getPropertyId());
        rateHeaderObject.setRanking(((RateUnqualified) abstractRate).getRanking());
        rateHeaderObject.setRateUnqualifiedEndDate(((RateUnqualified) abstractRate).getRateUnqualifiedEndDate());
        rateHeaderObject.setRateUnqualifiedStartDate(((RateUnqualified) abstractRate).getRateUnqualifiedStartDate());
        rateHeaderObject.setRemarks(abstractRate.getRemarks());
        rateHeaderObject.setStatusId(INACTIVE);
        rateHeaderObject.setSystemDefault(((RateUnqualified) abstractRate).getSystemDefault());
        rateHeaderObject.setYieldable(abstractRate.getYieldable());
    }

    @Override
    public void saveRateDetailsInBatch(List<AbstractDetail> detailsToSave) {
        final List<RateUnqualifiedDetailsDto> tableBatch = detailsToSave.stream()
                .flatMap(keepOnly(RateUnqualifiedDetails.class))
                .map(RateUnqualifiedDetailsDto::fromEntity)
                .collect(Collectors.toList());

        if (!tableBatch.isEmpty()) {
            tenantCrudService.execute(RateUnqualifiedDetailsDto.USP_RATE_UNQUALIFIED_DETAILS_INSERT, tableBatch);
        }
    }
}
