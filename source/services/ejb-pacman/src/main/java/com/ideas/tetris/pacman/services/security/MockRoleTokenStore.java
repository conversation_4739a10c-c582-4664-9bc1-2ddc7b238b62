package com.ideas.tetris.pacman.services.security;

import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public final class MockRoleTokenStore {

    private static Map<String, Role> MOCK_ROLE_CACHE = new ConcurrentHashMap<String, Role>();

    private MockRoleTokenStore() {
    }

    public static Role getMockRole() {
        if (isEnabled()) {
            return MOCK_ROLE_CACHE.get(getPrincipalToken());
        }

        return null;
    }

    public static void setMockRole(Role role) {
        if (isEnabled()) {
            MOCK_ROLE_CACHE.put(getPrincipalToken(), role);
        }
    }

    private static String getPrincipalToken() {
        return PacmanThreadLocalContextHolder.getPrincipal().getToken();
    }

    public static boolean isEnabled() {
        return SystemConfig.isMockRolesEnabled();
    }

}