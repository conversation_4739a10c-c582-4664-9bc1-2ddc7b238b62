package com.ideas.tetris.pacman.services.forecast;

import com.ideas.tetris.pacman.services.forecast.dto.GroupForecastDto;
import com.ideas.tetris.pacman.services.forecast.repository.GroupForecastRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

@Component
public class GroupForecastService {
    @Autowired
    private GroupForecastRepository repository;

    public List<GroupForecastDto> retrieveGroupForecasts(Integer propertyId, LocalDate startDate, LocalDate endDate) {
        return repository.retrieveGroupForecasts(propertyId, startDate, endDate);
    }
}
