package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.datafeed.dto.*;
import com.ideas.tetris.pacman.services.datafeed.dto.grouppricing.*;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.*;
import com.ideas.tetris.pacman.services.datafeed.dto.pricestrategy.PriceStrategyRatePlanConfiguration;
import com.ideas.tetris.pacman.services.datafeed.dto.pricestrategy.RateCodeConfiguration;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.*;
import com.ideas.tetris.pacman.services.datafeed.dto.propertyattribute.PropertySpecificAttribute;
import com.ideas.tetris.pacman.services.datafeed.dto.rateshopping.*;
import com.ideas.tetris.pacman.services.datafeed.entity.*;
import com.ideas.tetris.pacman.services.extendedstay.config.service.ProductConfigurationDetails;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationConferenceAndBanquet;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBProjection;
import com.ideas.tetris.pacman.services.pacealert.dto.PropertyOnBooksPaceAlertDTO;
import com.ideas.tetris.pacman.services.reports.mcatmapping.dto.MCATMappingDTO;
import com.ideas.tetris.pacman.services.reports.outputoverride.dto.AgileRatesProductPricingOverrideDTO;
import com.ideas.tetris.pacman.services.reports.outputoverride.dto.OutputOverrideOverbookingDTO;
import com.ideas.tetris.pacman.services.reports.outputoverride.dto.OutputOverridePricingDTO;
import com.ideas.tetris.pacman.services.str.dto.STRDailyDTO;
import com.ideas.tetris.pacman.services.str.dto.STRMonthlyDTO;
import com.ideas.tetris.pacman.services.webrate.dto.IgnoreChannelConfigDTO;
import com.ideas.tetris.pacman.services.webrate.dto.OccupancyBasedCMPCConfigExcelDto;

import java.util.List;

public enum DatafeedServiceEndpoint {

    MCAT_MAPPING(MCATMappingDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getmcat();
        }
    },
    MKT_SEG_CONFIG(MarketSegmentConfig.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getMktSegConfig();
        }
    },
    DECISION_CONFIGURATION(DecisionConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getDecisionConfigurations();
        }
    },
    BAR_OVERRIDE_DETAILS(OutputOverridePricingDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getBarOverrideDetails(datafeedRequest);
        }
    },
    INDEPENDENT_PRODUCT_OVERRIDE_DETAILS(IndependentOverride.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getIndependentProductOverrideDetails(datafeedRequest);
        }
    },

    INVENTORY_LIMIT(InventoryLimit.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getInventoryLimitDetails(datafeedRequest);
        }
    },

    OVERBOOKING_OVERRIDE_DETAILS(OutputOverrideOverbookingDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getOverbookingOverrideDetails(datafeedRequest);
        }
    },
    INPUT_OVERRIDE_DETAILS(InputOverride.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getInputOverrideDetails(datafeedRequest);
        }
    },
    ROLES_AND_PERMISSIONS(RolePermission.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRolesAndPermissions();
        }
    },
    USER_ACTIVITY_LOG(UserActivityLog.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.userActivityLog(datafeedRequest);
        }
    },
    USER_DETAILS(UserDetails.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getUsersData(datafeedRequest);//Optix getting use details for Optix
        }
    },
    SYSTEM_HEALTH_DETAILS(InformationManagerSystemHealth.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getSystemHealthDetails();
        }
    },
    COST_OF_WALK_DETAILS(CostOfWalkConfig.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getCostOfWalkDetails(datafeedRequest);
        }
    },
    RATE_SHOPPING_COMPETITOR_DETAILS(RateCompetitor.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRateShoppingCompetitorDetails();
        }
    },
    RATE_SHOPPING_CHANNEL_DETAILS(RateChannel.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRateShoppingChannelDetails();
        }
    },
    RATE_SHOPPING_ACCOM_CLASS_MAPPING(AccomClassMapping.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRateShoppingAccomClassMappings();
        }
    },
    RATE_SHOPPING_SCHEDULES(RateShoppingSchedule.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRateShoppingSchedules();
        }
    },
    RATE_SHOPPING_IGNORE_COMP_DATA(RateShoppingIgnoreCompetitor.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRateShoppingIgnoreCompData(datafeedRequest);
        }
    },
    RATE_SHOPPING_COMPETITIVE_CHANNEL_CONFIGURATION(CompetitiveChannel.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRateShoppingCompetitiveConfigurations(datafeedRequest);
        }
    },
    BENEFIT_MEASUREMENT(BenefitMeasurementDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getBenefitsMeasurementDtos();
        }
    },
    SCHEDULED_REPORTS(ScheduledReportsDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getScheduledReportsDtos();
        }
    },
    PROPERTY_BASIC_INFORMATION_LIMITED_LAST_BUILD_DATE(PropertyBasicInformationEnhancedLimitedBuildDate.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getEnhancedPropertyBasicInformationWithLastLDbUpdate();
        }
    },
    PROPERTY_BASIC_INFORMATION_ENHANCED_WITH_BOOKED_STATUS_AND_LDB_AND_WINDOW_SETTINGS(PropertyInformationEnhancedBookedStatusAndLimitedBuildDateAndWindowSettings.class) {
        @Override
        protected List getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPropertyInformationEnhancedWithBookedStatusAndLdbAndWindowSettings();
        }
    },
    CHANNEL_FORECAST(ChannelForecastDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getChannelForecastDtos(datafeedRequest);
        }
    },
    PROPERTY_SPECIFIC_CONFIGURATION(PropertySpecificConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPropertySpecificConfiguration();
        }
    },
    PROPERTY_SPECIFIC_CONFIGURATION_ENHANCED(PropertySpecificConfigurationEnhanced.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPropertySpecificConfigurationEnhanced(datafeedRequest);
        }
    },
    PRICE_DROP_RESTRICTIONS(PriceDropRestrictionsDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPriceDropRestrictionsDTOList();
        }
    },
    INFORMATION_MANAGER_NOTIFICATION_CONFIGURATION(InformationManagerNotificationConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getInformationManagerNotificationConfigurations();
        }
    },
    PROPERTY_SPECIFIC_ATTRIBUTE(PropertySpecificAttribute.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPropertySpecificAttributes();
        }
    },
    RATE_SHOPPING_COMPETITIVE_MARKET_POSITION_CONFIGURATION(RateCompetitorMarketPositionConfig.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRateCompetitorMarketPositionConfiguration(datafeedRequest);
        }
    },
    PRICING_STRATEGY_ARRIVAL_CONFIGURATION(ArrivalConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPricingStrategyArrivalConfig(datafeedRequest);
        }
    },
    ROOM_CLASS_PRICE_RANK(PriceRankDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRoomClassPriceRank();
        }
    },
    ROOM_CLASS_MIN_PRICE_DIFF(MinPriceDifferentDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRoomClassMinPriceDiff(datafeedRequest);
        }
    },
    PRICING_STRATEGY_LOS_CONFIGURATION(LOSConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPricingStrategyLOSConfig(datafeedRequest);
        }
    },
    PRICING_STRATEGY_RATE_PLAN_CONFIGURATION(PriceStrategyRatePlanConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPricingStrategyRatePlanConfiguration(datafeedRequest);
        }
    },
    GROUP_PRICING_BASE_ROOM_TYPE_RATE_CONFIGURATION(BaseRoomTypeRateConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupPricingBaseRoomTypeRateConfig(datafeedRequest);
        }
    },
    PRICING_STRATEGY_RATE_CODE_CONFIGURATION(RateCodeConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPricingStrategyRateCodeConfig();
        }
    },
    PRICING_STRATEGY_USER_OVERRIDE_CONFIGURATION(UserOverrideConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPricingStrategyUserOverride(datafeedRequest);
        }
    },
    GROUP_PRICING_CONFIGURATION(GroupPricingConfig.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupPricingConfigDetails();
        }
    },
    GROUP_PRICING_CONFERENCE_BANQUET_CONFIGURATION(GroupPricingConfigurationConferenceAndBanquet.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupPricingConferenceAndBanquetConfigurations();
        }
    },
    GROUP_PRICING_PREFERRED_ROOM_TYPE_CONFIGURATION(GroupPricingPreferredRoomTypeConfig.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupPricingPreferredRoomTypeConfigurations();
        }
    },
    GROUP_PRICING_ANCILLARY_CONFIGURATION(AncillaryConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupPricingAncillaryConfiguration(datafeedRequest);
        }
    },
    GROUP_PRICING_ROOM_TYPE_OFFSET_CONFIGURATION(GroupPricingRoomTypeOffsetConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupPricingRoomTypeOffsetConfiguration(datafeedRequest);
        }
    },
    GROUP_PRICING_MIN_PROFIT_CONFIGURATION(GroupPricingMinProfitConfigurationDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupPricingMinProfitConfiguration();
        }
    },

    CP_OFFSET_CONFIGURATION(CPOffsetConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getCPOffsetConfiguration(datafeedRequest);
        }
    },

    PROPERTY_BASIC_INFORMATION_ENHANCED_BOOKED_STATUS(PropertyBasicInformationEnhancedBookedStatus.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getEnhancedPropertyBasicInformationWithBookedStatus();
        }
    },

    OUT_OF_ORDER_OVERRIDE_DATA(OutOfOrderDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getOutOfOverrideDetails(datafeedRequest);
        }
    },

    FORECAST_ARRIVALS_DEPARTURES_DATAFEED(OperationsForecastArrivalsDeparturesDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getOperationsForecastArrivalsDepartures(datafeedRequest.getStartDate(), datafeedRequest.getEndDate());
        }
    },

    TAX_INCLUSIVE_CONFIGURATION(TaxInclusiveConfigurationDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getTaxInclusiveConfiguration();
        }
    },
    AGILE_PRODUCT_SEND_DECISION(ProductSendDecisionAsAdjustmentDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileProductSendDecisionDetails();
        }
    },

    ROOM_TYPE_ST2Y(RoomTypeHistoryST2YDto.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRoomType(datafeedRequest);
        }
    },

    MARKET_SEGMENT_HISTORY_ST2Y(MarketSegmentHistoryST2YDto.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getMarketSegmentSt2y(datafeedRequest);
        }
    },

    OVERBOOKING_CONFIGURATION_ENHANCED(OverbookingConfigurationEnhanced.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getOverbookingConfiguration(datafeedRequest);
        }
    },

    ROOM_TYPE_ST19(RoomTypeHistoryST19Dto.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRoomType(datafeedRequest);
        }
    },

    EXTENDED_STAY_PRODUCT_CONFIGURATION(ProductConfigurationDetails.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getExtendedStayProductConfiguration();
        }
    },

    EXTENDED_STAY_COMPETITOR_CONFIGURATION(ExtendedStayCompetitor.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getExtendedStayCompetitorConfiguration();
        }
    },

    DAILY_BAR_INPUT_CONFIG(DailyBarInputConfig.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getDailyBarDetails();
        }
    },

    CP_BASE_ROOM_TYPE_CONFIGURATION(CPBaseRoomTypeConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getCPBaseRoomTypeConfiguration();
        }
    },

    CP_BAR_ROUNDING_RULES_CONFIGURATION(CPBARRoundingRulesConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getCPBARRoundingRulesConfiguration();
        }
    },
    CP_CEILING_FLOOR_TRANSIENT_CONFIGURATION(CPCeilingFloorTransientConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getContinuousPricingFloorCeilingTransientConfiguration(datafeedRequest);
        }
    },
    CP_SUPPLEMENTS_CONFIGURATION(CPSupplementsConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getContinuousPricingSupplementsConfiguration(datafeedRequest);
        }
    },
    INVENTORY_SHARING(InventorySharing.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getInventorySharingData(datafeedRequest);
        }
    },
    AGILE_RATE_PRODUCTS(CPDecisionBAROutput.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileRateProductsPricingData(datafeedRequest);
        }
    },
    AGILE_RATES_OVERRIDE_DETAILS(AgileRatesProductPricingOverrideDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileRateProductsOverrideDetails(datafeedRequest);
        }
    },
    LDB_PROJECTIONS(LDBProjection.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getLDBProjectionData(datafeedRequest);
        }
    },
    PROFIT_FORECAST_GROUP(ForecastGroupProfitDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getProfitForecastGroupData(datafeedRequest);
        }
    },
    PROFIT_ROOM_CLASS(RoomClassProfitDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getProfitRoomClassData(datafeedRequest);
        }
    },

    INVENTORY_HISTORY(InventoryHistory.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getInventoryHistory(datafeedRequest);
        }
    },

    GROUP_EVALUATIONS(GroupEvaluationData.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupEvaluationDataFeed(datafeedRequest);
        }
    },
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_DEFINATION(PricingDataProductDefinitionDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileRateProductDefination(datafeedRequest);
        }
    },
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_RATE_CODE_ASSIGNMENT(PricingDataProductRateCodeAssignmentDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileRateProductRateCodeAssignment(datafeedRequest);
        }
    },
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_HIERARCHY(PricingDataProductHierarchyDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileRateProductHierarchy();
        }
    },
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_GROUPS(PricingDataProductGroupsDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileRateProductGroups();
        }
    },
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_OPTIMIZED(PricingDataProductOptimizationDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileRateProductOptimization();
        }
    },

    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_ROOM_TYPE_ASSIGNMENT(PricingDataProductRoomTypeAssignmentDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileRateProductRoomTypeAssignment(datafeedRequest);
        }
    },
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_DEFAULT_VALUE(PricingDataProductDefaultValueDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileRateProductDefaultValue(datafeedRequest);
        }
    },
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_SEASON_VALUE(PricingDataProductSeasonValueDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileRateProductSeasonalValue(datafeedRequest);
        }
    },
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_PACKAGE_ELEMENTS_ASSIGNMENT(PricingDataProductPackageElementAssignmentDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileRateProductPackageElementAssignment(datafeedRequest.getDatafeedName());
        }
    },
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_PACKAGE_ELEMENTS(PricingDataProductPackageElementsDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileRateProductPackageElements();
        }
    },
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_PACKAGE_ELEMENTS_DOW(PricingDataProductPackageElementsDowDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getAgileRateProductPackageElementsEnhanced();
        }
    },
    VIRTUAL_PROPERTY_MAPPING_HILTON(VirtualPropertyMappingDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getVirtualPropertyMappings();
        }
    },
    BUDGET_DATA(BudgetDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getBudgetDetails(datafeedRequest);
        }
    },
    USER_FORECAST_DATA(UserForecastDataDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getForecastData(datafeedRequest);
        }
    },
    PRODUCT_RATE_SHOP_DEFINITION(ProductRateShopDefinitionDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getProductRateShopDefinitions();
        }
    },
    PRODUCT_CLASSIFICATION(ProductClassificationDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getProductClassifications(datafeedRequest);
        }
    },
    PRODUCT_CHILD_PRICING_TYPE(ProductChildPricingTypeDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getProductChildPricingTypes(datafeedRequest);
        }
    },
    REVPLAN_MARKET_OCCUPANCY_FORECAST(RevplanMarketOccupancyForecast.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRevplanMarketOccupancyForecastDtos(datafeedRequest);
        }
    },
    REVPLAN_MARKET_SEGMENT(RevplanMarketSegment.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getMarketSegmentNonPaceData(datafeedRequest);
        }
    },
    REVPLAN_MARKET_SEGMENT_PACE(RevplanMarketSegmentPace.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getMarketSegmentPaceData(datafeedRequest);
        }
    },
    REVPLAN_MARKET_ACCOM_ACTIVITY(RevplanMarketAccomActivity.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getMarketAccomActivityData(datafeedRequest);
        }
    },
    OPTIX_RESERVATION_NIGHT(ReservationNightDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getReservationNight(datafeedRequest);
        }
    },
    OPTIX_RESERVATION_NIGHT_CHANGE(ReservationNightChangeDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getReservationChangeNight(datafeedRequest);
        }
    },
    OPTIX_BUDGET_DATA(BudgetDataDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getBudgetData(datafeedRequest);
        }
    },
    OPTIX_USER_FORECAST_DATA(UserForecastDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getUserForecastData(datafeedRequest);
        }
    },
    OPTIX_OCCUPANCY_FCST(OccupancyFCSTDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getOccupancyFCST(datafeedRequest);
        }
    },
    OPTIX_MARKET_ACCOM_ACTIVITY(MarketAccomActivityDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getMarketAccomActivity(datafeedRequest);
        }

    },
    OPTIX_FORECAST_GROUP(ForecastGroupDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getForecastGroupInfo();
        }
    },
    OPTIX_PACE_GROUP_BLOCK(PaceGroupBlockDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPaceGroupBlock(datafeedRequest);
        }
    },
    OPTIX_PACE_TOTAL_ACTIVITY(PaceTotalActivityDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPaceTotalActivity(datafeedRequest);
        }
    },
    OPTIX_PACE_GROUP_MASTER(PaceGroupMasterDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPaceGroupMaster(datafeedRequest);
        }
    },
    OPTIX_ROLE_PERMISSION(RolePermission.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRolesAndPermissions();
        }
    },

    OPTIX_PACE_WEB_RATE(PaceWebrateDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPaceWebrate(datafeedRequest);
        }
    },

    OPTIX_LRV_PACE(LRVPaceDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getLRVPace(datafeedRequest);
        }
    },

    OPTIX_WEB_RATE(WebrateDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getWebrate(datafeedRequest);
        }
    },

    OPTIX_GROUP_BLOCK(GroupBlockDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupBlock(datafeedRequest);
        }
    },
    OPTIX_GROUP_MASTER(GroupMasterDTO.class) {
        @Override
        protected List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupMaster(datafeedRequest);
        }
    },
    GROUP_FINAL_FORECAST_OVERRIDE(GroupFinalForecastOverrideDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupFinalForecastOverride(datafeedRequest);
        }
    },
    OPTIX_STR_DAILY(STRDailyDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getSTRDailyData(datafeedRequest);
        }
    },
    OPTIX_STR_MONTHLY(STRMonthlyDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getSTRMonthlyData();
        }
    },
    ROOM_CLASS_CONFIGURATION(RoomClassConfiguration.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRoomClassConfiguration(datafeedRequest);
        }
    },
    OPTIX_HOTEL_CAPACITY_DEMAND360(D360CompCapacityDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getHotelCapacityDemand360(datafeedRequest);
        }
    },
    OPTIX_BOOKING_SUMMARY_DEMAND360(D360BookingSummaryDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getBookingSummaryDemand360(datafeedRequest);
        }
    },
    OPTIX_COMPONENT_ROOM_MAPPING(ComponentRoomMappingDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getComponentRoomMapping();
        }
    },
    OPTIX_PROPERTY_BASIC_INFORMATION_ENHANCED(PropertyBasicInformationEnhanced.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getEnhancedPropertyBasicInformation();
        }
    },
    OPTIX_PROPERTY_ON_BOOKS_PACE_ALERT(PropertyOnBooksPaceAlertDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getPropertyOnBooksPaceAlert();
        }
    },
    OPTIX_PROPERTY_LEVEL_DATA(PropertyLevelDataDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getOptixSpecificPropertyLevelData(datafeedRequest);
        }
    },
    PRODUCT_FREE_NIGHT_DEFINITION(ProductFreeNightDefinitionDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getProductFreeNightDefinition(datafeedRequest);
        }
    },
    PRODUCT_GROUP_PRODUCT_DEFINITION(ProductGroupProductDefinition.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getProductGroupProductDefinition(datafeedRequest);
        }
    },
    GROUP_PRICING_SCROOM_TYPE_MAPPING(GroupPricingSCRoomTypeMapping.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupPricingSCRoomTypeMapping();
        }
    },
    GROUP_PRICING_SC_MARKET_SEGMENT_MAPPING(GroupPricingSCMarketSegmentMapping.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupPricingSCMarketSegmentMapping();
        }
    },
    RATE_SHOPPING_OCCUPANCY_BASED_CMPC(OccupancyBasedCMPCConfigExcelDto.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRateShoppingOccupancyBasedCMPCDtos();
        }
    },
    RATE_SHOPPING_IGNORE_CHANNEL_CONFIG(IgnoreChannelConfigDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getRateShoppingIgnoreChannelConfigDtos();
        }
    },
    MEETING_PACKAGE_PRICING(MeetingPackagePricing.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getMeetingPackagePricingCfgs(datafeedRequest);
        }
    },
    MEETING_PACKAGE_BASE_PRODUCT_PRICING_OVERRIDES(MeetingPackageBaseProductPricingOverridesDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getMeetingPackagePricingOverrideCfgs(datafeedRequest);
        }
    },
    OPTIX_PRICING_SENSITIVITY_COEFFICIENT(PricingSensitivityCoefficient.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getOptixPricingSensitivityCoefficientCfgs(datafeedRequest);
        }
    },
    OPTIX_PROFIT_ROOM_CLASS(RoomClassProfitDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getProfitRoomClassData(datafeedRequest);
        }
    },
    OPTIX_PROFIT_FORECAST_GROUP(ForecastGroupProfitDTO.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getProfitForecastGroupData(datafeedRequest);
        }
    },
    INFORMATION_MANAGER_ALERT(InformationManagerAlert.class) {
        @Override
        protected List<Object> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getInformationManagerAlerts(datafeedRequest);
        }
    };


    DatafeedServiceEndpoint(Class<?> entityClass) {
        this.entityClass = entityClass;
    }

    protected abstract List<Object> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest);

    private final Class<?> entityClass;

    public Class<?> getEntityClass() {
        return entityClass;
    }

    public static DatafeedServiceEndpoint valueOfSimpleClassName(String simpleClassName) {
        for (DatafeedServiceEndpoint datafeedServiceEndpoint : values()) {
            if (simpleClassName.equals(datafeedServiceEndpoint.getEntityClass().getSimpleName())) {
                return datafeedServiceEndpoint;
            }
        }
        return null;
    }
}
