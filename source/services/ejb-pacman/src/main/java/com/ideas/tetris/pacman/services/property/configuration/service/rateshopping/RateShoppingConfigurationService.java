package com.ideas.tetris.pacman.services.property.configuration.service.rateshopping;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.RateShoppingPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecordStatus;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import org.apache.commons.lang.StringUtils;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.List;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@RateShoppingConfigurationService.Qualifier
@Component
@Transactional
public class RateShoppingConfigurationService extends AbstractPropertyConfigurationService {

    public static final String WEB_RATE_RANKING_NAME = "None";
    private static final int SUBSCRIBING_PROPERTY_CODE_MAX_LENGTH = 150;

    @Override
    public PropertyConfigurationRecordType getPropertyConfigurationRecordType() {
        return PropertyConfigurationRecordType.RS;
    }

    @Override
    public List<PropertyConfigurationRecordFailure> doValidate(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId) {
        RateShoppingPropertyConfigurationDto rspcd = (RateShoppingPropertyConfigurationDto) propertyConfigurationDto;

        List<PropertyConfigurationRecordFailure> exceptions = new ArrayList<PropertyConfigurationRecordFailure>();

        // Validate Subscribing Property Code
        String subscribingPropertyCode = rspcd.getSubscribingPropertyCode();


        if (StringUtils.isEmpty(subscribingPropertyCode)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Subscribing Property Code is required"));
        } else if (subscribingPropertyCode.length() > SUBSCRIBING_PROPERTY_CODE_MAX_LENGTH) {
            rspcd.setSubscribingPropertyCode(StringUtils.left(subscribingPropertyCode, SUBSCRIBING_PROPERTY_CODE_MAX_LENGTH));
            exceptions.add(new PropertyConfigurationRecordFailure(ConfigurationFileRecordStatus.WARNING, "Subscribing Property Code cannot be longer than " + SUBSCRIBING_PROPERTY_CODE_MAX_LENGTH + " characters.  The value has been trimmed."));
        }

        return exceptions;
    }

    @Override
    public void doHandle(PropertyConfigurationDto pcd, Integer propertyId) {
        RateShoppingPropertyConfigurationDto rspcd = (RateShoppingPropertyConfigurationDto) pcd;

        propertyRolloutService.addParameterValue(pcd.getPropertyCode(), IPConfigParamName.BAR_WEB_RATE_ALIAS.value(), rspcd.getSubscribingPropertyCode(), true);
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
