package com.ideas.tetris.pacman.services.rollback;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datasourceswitching.DataSourceCacheBean;
import com.ideas.tetris.pacman.services.property.dataextraction.DatabaseExtractionService;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackDataBuilder;
import com.ideas.tetris.platform.common.businessservice.async.AsyncJobCallback;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.daoandentities.entity.DBLoc;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.Collection;
import java.util.concurrent.Future;

@Transactional(propagation = Propagation.NOT_SUPPORTED)
@Component
public class Demand360BackupService {
    private static final Logger LOGGER = Logger.getLogger(Demand360BackupService.class);
    private final String[] demand360Tables = {"D360_Booking_Summary_Pace", "D360_MKT_Hist_Capacity"};
    @Autowired
	private RollbackHelper rollbackHelper;
    @Autowired
	private DatabaseExtractionService dbExtractionService;
    @Autowired
	private DataSourceCacheBean dataSourceCacheBean;
    @TenantCrudServiceBean.Qualifier
	@Qualifier("tenantCrudServiceBean")
    @Autowired
	private CrudService crudService;

    @Async
    @AsyncJobCallback
    public Future<String> backup(JobStepContext jobStepContext, WorkContextType workContext, String clientCode, String propertyCode) {
        return AsyncCallbackDataBuilder.buildFuture(backupTables(clientCode, propertyCode));
    }

    public String backupDemand360Table(String clientCode, String propertyCode) {
        return backupTables(clientCode, propertyCode);

    }

    @Async
    @AsyncJobCallback    public Future<String> restore(JobStepContext jobStepContext, WorkContextType workContext, String clientCode, String propertyCode) {
        return AsyncCallbackDataBuilder.buildFuture(restoreTables(clientCode, propertyCode));
    }

    public String restoreDemand360Tables(String clientCode, String propertyCode) {
        return restoreTables(clientCode, propertyCode);
    }

    private String backupTables(String clientCode, String propertyCode) {
        File demand360BackupFolder = getDemand360BackupFolder(clientCode, propertyCode);
        try {
            createDirectoryIfItDoesNotExist(demand360BackupFolder);
            FileUtils.cleanDirectory(new File(demand360BackupFolder.getAbsolutePath()));
        } catch (IOException e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "ERROR: Failed to clean directory " + demand360BackupFolder.getAbsolutePath(), e);
        }
        return createBackupFiles(demand360BackupFolder, getDbLoc(PacmanWorkContextHelper.getPropertyId()));
    }

    private String restoreTables(String clientCode, String propertyCode) {
        File demand360BackupFolder = getDemand360BackupFolder(clientCode, propertyCode);
        Collection<File> files = FileUtils.listFiles(demand360BackupFolder, new String[]{"tsv"}, false);
        if (files.isEmpty()) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, MessageFormat.format("ERROR: There are no " +
                    "backup files present at {0} for {1} - {2}", demand360BackupFolder.getAbsolutePath(), clientCode, propertyCode));
        }
        files.forEach(this::restoreData);
        return "Successfully restored Demand360 Tables";
    }

    private void createDirectoryIfItDoesNotExist(File demand360BackupFolder) {
        if (!demand360BackupFolder.exists()) {
            demand360BackupFolder.mkdirs();
        }
    }

    private File getDemand360BackupFolder(String clientCode, String propertyCode) {
        return rollbackHelper.getDemand360BackupFolder(clientCode, propertyCode);
    }

    private String createBackupFiles(File demand360BackupFolder, DBLoc dbLoc) {
        try {
            if (dbExtractionSuccessful(demand360BackupFolder, dbLoc)) {
                moveToDataBackupFolder(demand360BackupFolder, dbLoc.getDbName());
            } else {
                throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "ERROR: Failed to create backup at: " + demand360BackupFolder.getAbsolutePath());
            }
        } catch (IOException e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "ERROR: Failed to create backup at: " + demand360BackupFolder.getAbsolutePath(), e);
        }
        return Boolean.TRUE.toString();
    }

    private boolean dbExtractionSuccessful(File demand360BackupFolder, DBLoc dbLoc) {
        String schemaZenCommand = dbExtractionService.getSchemaZenCommand(demand360BackupFolder.getAbsolutePath(), dbLoc);
        return dbExtractionService.executeSchemaZen(schemaZenCommand + String.join(",", demand360Tables));
    }

    private void moveToDataBackupFolder(File demand360BackupFolder, String dbName) throws IOException {
        File srcDir = new File(demand360BackupFolder + File.separator + dbName + File.separator + "data");
        if (srcDir.exists()) {
            FileUtils.copyDirectory(srcDir, demand360BackupFolder);
        }
        FileUtils.deleteQuietly(new File(demand360BackupFolder + File.separator + dbName));
    }

    private DBLoc getDbLoc(Integer propertyId) {
        DBLoc dbLoc = dataSourceCacheBean.getDBLoc(propertyId);
        if (null == dbLoc) {
            throw new TetrisException("No Database Location record exists for the property : " + propertyId);
        }
        return dbLoc;
    }

    private void restoreData(File file) {
        String tableName = FilenameUtils.removeExtension(file.getName());
        crudService.executeUpdateByNativeQuery("TRUNCATE TABLE " + tableName);
        String query = "BULK INSERT " + tableName +
                " FROM '" + file.getAbsolutePath() + "'" +
                " WITH (FIELDTERMINATOR = '\\t') ";
        int insertCount = crudService.executeUpdateByNativeQuery(query);
        LOGGER.info("Demand360Restore: Inserted " + insertCount + " records in " + tableName);
    }

}
