package com.ideas.tetris.pacman.services.datafeed.dto.rateshopping;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ideas.tetris.platform.common.rest.unmarshaller.DateSerializer;

import java.io.Serializable;
import java.util.Date;

public class RateCompetitorMarketPositionConfig implements Serializable {

    private String configCategory;
    private String accomClassCode;
    @JsonSerialize(using = DateSerializer.class)
    private Date startDate;
    @JsonSerialize(using = DateSerializer.class)
    private Date endDate;
    private String compMktPosConstraintSunday;
    private String compMktPosConstraintMonday;
    private String compMktPosConstraintTuesday;
    private String compMktPosConstraintWednesday;
    private String compMktPosConstraintThursday;
    private String compMktPosConstraintFriday;
    private String compMktPosConstraintSaturday;
    private String productName;


    public String getConfigCategory() {
        return configCategory;
    }

    public void setConfigCategory(String configCategory) {
        this.configCategory = configCategory;
    }

    public String getAccomClassCode() {
        return accomClassCode;
    }

    public void setAccomClassCode(String accomClassCode) {
        this.accomClassCode = accomClassCode;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getCompMktPosConstraintMonday() {
        return compMktPosConstraintMonday;
    }

    public void setCompMktPosConstraintMonday(String compMktPosConstraintMonday) {
        this.compMktPosConstraintMonday = compMktPosConstraintMonday;
    }

    public String getCompMktPosConstraintTuesday() {
        return compMktPosConstraintTuesday;
    }

    public void setCompMktPosConstraintTuesday(String compMktPosConstraintTuesday) {
        this.compMktPosConstraintTuesday = compMktPosConstraintTuesday;
    }

    public String getCompMktPosConstraintWednesday() {
        return compMktPosConstraintWednesday;
    }

    public void setCompMktPosConstraintWednesday(String compMktPosConstraintWednesday) {
        this.compMktPosConstraintWednesday = compMktPosConstraintWednesday;
    }

    public String getCompMktPosConstraintThursday() {
        return compMktPosConstraintThursday;
    }

    public void setCompMktPosConstraintThursday(String compMktPosConstraintThursday) {
        this.compMktPosConstraintThursday = compMktPosConstraintThursday;
    }

    public String getCompMktPosConstraintFriday() {
        return compMktPosConstraintFriday;
    }

    public void setCompMktPosConstraintFriday(String compMktPosConstraintFriday) {
        this.compMktPosConstraintFriday = compMktPosConstraintFriday;
    }

    public String getCompMktPosConstraintSaturday() {
        return compMktPosConstraintSaturday;
    }

    public void setCompMktPosConstraintSaturday(String compMktPosConstraintSaturday) {
        this.compMktPosConstraintSaturday = compMktPosConstraintSaturday;
    }

    public String getCompMktPosConstraintSunday() {
        return compMktPosConstraintSunday;
    }

    public void setCompMktPosConstraintSunday(String compMktPosConstraintSunday) {
        this.compMktPosConstraintSunday = compMktPosConstraintSunday;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
}
