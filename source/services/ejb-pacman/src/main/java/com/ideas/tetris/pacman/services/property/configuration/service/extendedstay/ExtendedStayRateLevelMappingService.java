package com.ideas.tetris.pacman.services.property.configuration.service.extendedstay;

import com.ideas.tetris.pacman.Service;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.ExtendedStayRateLevelMapping;
import com.ideas.tetris.pacman.services.crudservice.RatchetCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.apache.commons.collections.CollectionUtils;

import javax.inject.Inject;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Service
@Component
public class ExtendedStayRateLevelMappingService {
    @RatchetCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("ratchetCrudServiceBean")
	protected CrudService ratchetCrudService;

    public List<ExtendedStayRateLevelMapping> getESTrueRateLevelMappings() {
        final List<ExtendedStayRateLevelMapping> esTrueMappings = ratchetCrudService.findByNamedQuery(
                ExtendedStayRateLevelMapping.EXTENDED_STAY_TRUE_BY_PROP_CODES,
                QueryParameter.with("propertyCodes",
                        List.of(PacmanWorkContextHelper.getPropertyCode())).parameters());
        if (CollectionUtils.isEmpty(esTrueMappings)) {
            throw new TetrisException(ErrorCode.MISSING_DATA, "No extended stay rate levels presents");
        }
        return esTrueMappings;
    }

}