package com.ideas.tetris.pacman.services.budget.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
public class RevPlanJobNotificationDTO extends RevPlanSubmitTypeRequestDTO {

    private String status;

    private Set<String> warningMessage;

    private List<String> errorMessage;

    public RevPlanJobNotificationDTO(RevPlanSubmitTypeRequestDTO revPlanSubmitTypeRequestDTO, String status, List<String> errorMessage, Set<String> warningMessage) {
        super(revPlanSubmitTypeRequestDTO);
        this.status = status;
        this.warningMessage = warningMessage;
        this.errorMessage = errorMessage;
    }
}
