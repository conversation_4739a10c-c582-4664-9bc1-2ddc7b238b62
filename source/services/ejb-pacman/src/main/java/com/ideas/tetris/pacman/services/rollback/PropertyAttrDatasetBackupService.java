package com.ideas.tetris.pacman.services.rollback;

import com.ideas.sas.service.SASClientService;
import com.ideas.tetris.pacman.common.constants.Constants;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

import static com.ideas.tetris.pacman.common.constants.Constants.COPY_FILE;
import static com.ideas.tetris.pacman.common.constants.Constants.DELETE_FILE_QUIETLY;
import static com.ideas.tetris.pacman.common.constants.Constants.DELETE_PATH;
import static com.ideas.tetris.pacman.common.constants.Constants.DESC_PATH;
import static com.ideas.tetris.pacman.common.constants.Constants.SRC_PATH;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class PropertyAttrDatasetBackupService {

    private static final Logger LOGGER = Logger.getLogger(PropertyAttrDatasetBackupService.class);

    @Autowired
	protected RollbackHelper rollbackHelper;
    @Autowired
	private SASClientService sasClientService;

    public void backup(Integer propertyId) {
        String propertyAttrDatasetPath = rollbackHelper.getAnalyticalDatasetDirectory(propertyId) + File.separator + Constants.PROPERTY_ATTR_DATASET_FILENAME;

        String backupFolderPath = rollbackHelper.getPropertyAttrDatasetBackupFolder(propertyId);
        sasClientService.executeFileOps(DELETE_FILE_QUIETLY, new HashMap<>(Map.of(DELETE_PATH, backupFolderPath)));
        sasClientService.executeFileOps(COPY_FILE, new HashMap<>(Map.of(SRC_PATH, propertyAttrDatasetPath, DESC_PATH, backupFolderPath)));
        LOGGER.debug("Property Attr Dataset backup path :" + backupFolderPath);
    }
}
