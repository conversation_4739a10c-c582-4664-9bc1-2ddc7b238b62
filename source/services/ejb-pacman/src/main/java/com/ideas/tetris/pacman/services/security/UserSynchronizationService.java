package com.ideas.tetris.pacman.services.security;

import com.ideas.infra.tetris.security.EncryptionDecryption;
import com.ideas.infra.tetris.security.Password;
import com.ideas.infra.tetris.security.cognito.CognitoUserManagementService;
import com.ideas.infra.tetris.security.cognito.dto.CognitoUser;
import com.ideas.infra.tetris.security.cognito.dto.CognitoUserPoolConfig;
import com.ideas.infra.tetris.security.cognito.dto.Name;
import com.ideas.infra.tetris.security.cognito.dto.UpdateUserRequest;
import com.ideas.infra.tetris.security.domain.AuthGroupRoleMapping;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.infra.tetris.security.domain.PropertyRoleMapping;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.client.service.ClientConfigService;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientUserMapping;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.systemconfig.PacmanSystemConfigService;
import com.ideas.tetris.pacman.services.user.migration.cognito.CognitoUserSyncService;
import com.ideas.tetris.pacman.util.CollectionUtils;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.ENABLE_FDS_SYNC_FOR_USERS;
import static com.ideas.tetris.pacman.common.constants.Constants.INACTIVE_STATUS_ID;

@Component
@Transactional
public class UserSynchronizationService implements UserSynchronizationServiceLocal {
    private static final Logger LOGGER = Logger.getLogger(UserSynchronizationService.class);

    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService globalCrudService;
    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
    ClientConfigService clientConfigService;
    @Autowired
    PropertyService propertyService;
    @Autowired
    AuthorizationService authorizationService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    UserSynchronizationAsyncService userSynchronizationAsyncService;
    @Autowired
    JobServiceLocal jobService;
    @Autowired
    UserService userService;
    @Autowired
    CognitoUserSyncService cognitoUserSyncService;
    @Autowired
    PacmanSystemConfigService systemConfigService;
    @Autowired
	private CognitoUserManagementService cognitoUserManagementService;

    public GlobalUser findGlobalUserByUid(Integer userID) {
        return globalCrudService.find(GlobalUser.class, userID);
    }

    @Override
    public Integer createUser(LDAPUser ldapUser, boolean isInternal, String clientCode, String password) {
        return persistUser(null, ldapUser, isInternal, clientCode, password, true);
    }

    @Override
    public Integer persistUser(Integer userID, LDAPUser ldapUser, boolean isInternal, String clientCode, String password, boolean isTenantSyncRequired) {
        Integer persistedUserId = null;
        try {
            boolean isCreationMode = null == userID;

            // Will create if userID null, otherwise update
            persistedUserId = persistGlobalUser(userID, ldapUser, isInternal, clientCode, password);


            // Note: Do we even need Global.Client_User table anymore. Seems like relic of db per client days.
            // Not switching clients and new props handled in property rollout
            if (isCreationMode) {
                persistClientUserMapping(persistedUserId, isInternal);
            }

            if (isTenantSyncRequired) {
                if (useUniqueUserIDInsteadOfEmailEnabled() && ldapUser.getUniqueUserID() != null) {
                    syncUserToTenantDatabasesByUniqueUserID(ldapUser.getCn(), ldapUser.getActive(), isInternal, persistedUserId, isCreationMode, ldapUser.getUniqueUserID());
                } else if (ldapUser.getMail() != null && (!useUniqueUserIDInsteadOfEmailEnabled() || isInternal)) {
                    syncUserToTenantDatabasesByEmail(ldapUser.getMail(), ldapUser.getCn(), ldapUser.getActive(), isInternal, persistedUserId, isCreationMode);
                }
            }
        } catch (Exception ex) {
            String message = "Failed to store user in tetris: " + ex.getMessage();
            LOGGER.error(message, ex);
            throw new RuntimeException(message, ex);
        }

        return persistedUserId;
    }

    public void syncUserToTenantDatabasesByEmail(String email, String userName, boolean isActive, boolean isInternal, Integer persistedUserId, boolean isCreationMode) {
        // Update the User tables in each client property db
        if (SystemConfig.isUserTenantSyncJobEnabled()) {
            Map<String, Object> parameters = createParameterForTenantUserSynchronizationJob(userName, isActive, isInternal, persistedUserId, isCreationMode);
            parameters.put(JobParameterKey.EMAIL, email);
            jobService.startGuaranteedNewInstance(JobName.TenantUserSynchronization, parameters);
        } else {
            persistTenantUserByEmail(persistedUserId, email, userName, isActive, isInternal, isCreationMode);
        }
    }

    public void syncUserToTenantDatabasesByUniqueUserID(String userName, boolean isActive, boolean isInternal, Integer persistedUserId, boolean isCreationMode, String uniqueUserID) {
        if (SystemConfig.isUserTenantSyncJobEnabled()) {
            Map<String, Object> parameters = createParameterForTenantUserSynchronizationJob(userName, isActive, isInternal, persistedUserId, isCreationMode);
            parameters.put(JobParameterKey.UNIQUE_USER_ID, uniqueUserID);
            jobService.startGuaranteedNewInstance(JobName.TenantUserSynchronization, parameters);
        } else {
            persistTenantUserByUniqueUserID(persistedUserId, uniqueUserID, userName, isActive, isInternal, isCreationMode);
        }
    }

    private void persistTenantUserByEmail(Integer persistedUserId, String email, String userName, boolean isActive, boolean isInternal, boolean isCreationMode) {
        List<Property> properties = isInternal ? propertyService.getAllPropertyDetails() :
                propertyService.getPropertiesForClient();
        userSynchronizationAsyncService.persistTenantUserAsyncWithEmail(PacmanThreadLocalContextHolder.getWorkContext(), properties, persistedUserId, email, userName, isActive, isCreationMode);
    }

    private void persistTenantUserByUniqueUserID(Integer persistedUserId, String uniqueUserID, String userName, boolean isActive, boolean isInternal, boolean isCreationMode) {
        List<Property> properties = isInternal ? propertyService.getAllPropertyDetails() :
                propertyService.getPropertiesForClient();
        userSynchronizationAsyncService.persistTenantUserAsyncWithUniqueUserID(PacmanThreadLocalContextHolder.getWorkContext(), properties, persistedUserId, uniqueUserID, userName, isActive, isCreationMode);
    }


    private Map<String, Object> createParameterForTenantUserSynchronizationJob(String userName, boolean isActive, boolean isInternal, Integer persistedUserId, boolean isCreationMode) {
        Map<String, Object> parameters = new HashMap<>();
        if (!isInternal) {
            parameters.put(JobParameterKey.CLIENT_CODE, PacmanWorkContextHelper.getClientCode());
        }
        parameters.put(JobParameterKey.GLOBAL_USER_ID, persistedUserId);
        parameters.put(JobParameterKey.USERNAME, userName);
        parameters.put(JobParameterKey.IS_ACTIVE, isActive);
        parameters.put(JobParameterKey.IS_CREATION_MODE, isCreationMode);
        parameters.put(JobParameterKey.IS_INTERNAL, isInternal);
        parameters.put(JobParameterKey.UNIQUE_USER_ID, StringUtils.EMPTY);
        parameters.put(JobParameterKey.EMAIL, StringUtils.EMPTY);
        return parameters;
    }


    private Integer persistGlobalUser(Integer userID, LDAPUser ldapUser, boolean isInternal, String clientCode, String password) {
        GlobalUser user = userID != null ? findGlobalUserByUid(userID) : null;

        // creation mode
        if (user == null) {
            user = new GlobalUser();
            user.setId(userID);
            Password encryptedPassword = EncryptionDecryption.encrypt(password);
            user.setUserPassword(new UserPassword(encryptedPassword.getSaltedHash(), encryptedPassword.getSalt()));
            if (userID != null) {
                LOGGER.warn("No GlobalUser found for id: " + userID);
            }
        }

        user.setScreenName(ldapUser.getMail());
        if (!StringUtils.equals(user.getEmail(), ldapUser.getMail())) {
            user.setSunFmSaml2NameIdInfo(null);
            user.setSunFmSaml2NameIdInfokey(null);
        }
        if (ldapUser.getMail() == null && useUniqueUserIDInsteadOfEmailEnabled()) {
            user.setScreenName(ldapUser.getUniqueUserID());
        }
        user.setEmail(ldapUser.getMail());
        user.setFullName(ldapUser.getCn());
        user.setActive(ldapUser.getActive());

        user.setClientCode(clientCode);
        user.setInternal(isInternal);
        user.setFirstName(ldapUser.getGivenName());
        user.setLastName(ldapUser.getSn());
        user.setCorporate(ldapUser.getIsCorporate());
        user.setSalesforceAccess(ldapUser.getHasSalesforceAccess());
        user.setPasswordNeverExpire(ldapUser.getPasswordNeverExpire());
        user.setLearningAccess(ldapUser.getHasLearningAccess());
        user.setSalesforcePortalId(ldapUser.getSalesforcePortalId());
        user.setUniqueUserID(ldapUser.getUniqueUserID());
        user.setSalesforceOrganizationId(ldapUser.getSalesforceOrganizationId());
        user.setIntegrationUser(ldapUser.isIntegrationUser());
        Integer maxRetriesAllowed = pacmanConfigParamsService.getIntegerParameterValue(GUIConfigParamName.CORE_PROPERTY_MAX_FAILED_AUTHENTICATION_RETRIES_ALLOWED.value());
        if (maxRetriesAllowed == null) {
            maxRetriesAllowed = 5;
        }
        user.setRemainingFailedAttemptsAllowed(maxRetriesAllowed);
        user.setLanguage(ldapUser.getLanguage());
        if (null == ldapUser.getLanguage()) {
            user.setLanguage("en_US");
        }
        setGlobalUserAuthGroupRoles(user, ldapUser.getAuthGroupRoles());
        setGlobalUserIndividualPropertyRoles(user, ldapUser.getPropertyRoles());
        if (ldapUser.getCognitoUserId() != null) {
            user.setCognitoUserId(ldapUser.getCognitoUserId());
        }
        user = globalCrudService.save(user);

        return user.getId();
    }

    private CognitoUser persistUserInCognito(GlobalUser user) {
        try {
            UpdateUserRequest updateUserRequest = UpdateUserRequest
                    .builder()
                    .username(user.getEmail())
                    .email(user.getEmail())
                    .name(new Name(user.getFirstName(), user.getLastName()))
                    .active(user.isActive())
                    .clientCode(user.getClientCode())
                    .applicationName(systemConfigService.getG3ApplicationName())
                    .userType(user.getClientCode().equalsIgnoreCase(Constants.INTERNAL_CLIENT_NAME) ? "INTERNAL" : "CLIENT")
                    .discoverAccess(user.getLearningAccess())
                    .supportAccess(user.getSalesforceAccess())
                    .build();

            CognitoUserManagementService cognitoUserManagementService = new CognitoUserManagementService();
            CognitoUserPoolConfig externalUserPoolConfig = cognitoUserManagementService.getCognitoUserPoolConfigForExternalUserPool();
            List<CognitoUser> users = cognitoUserManagementService.searchUserByEmail(user.getEmail(), externalUserPoolConfig.getUserPoolId());
            //Only assign flag when we need to update cognito on the next login
            //We require a password to be filled in on the first insert into FDS, so we have a placeholder for now
            if (CollectionUtils.isEmpty(users)) {
                updateUserRequest.setPassword(userService.getRandomPassword()); //Consider checking toggle here for SSO rather than external
            }

            return user.getCognitoUserId() != null ?
                    cognitoUserManagementService.updateCognitoUserAppIntegrationWithUserId(user.getCognitoUserId(), updateUserRequest) :
                    cognitoUserManagementService.updateCognitoUserAppIntegration(updateUserRequest);
        } catch (Exception e) {
            LOGGER.error("There was an error updating the user in cognito. ", e);
        }
        return null;
    }

    private boolean shouldEnableFDSSyncForClient(String clientCode) {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(ENABLE_FDS_SYNC_FOR_USERS.getParameterName(), clientCode));
    }

    private boolean shouldEnableFDSSyncForInternalUsers(GlobalUser user) {
        return user.isInternal() && SystemConfig.isCognitoUserSyncEnabledForInternalUsers();
    }

    private boolean useUniqueUserIDInsteadOfEmailEnabled() {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.USE_UNIQUE_USERID_INSTEADOF_EMAILID.getParameterName()));
    }

    private void persistClientUserMapping(int userID, boolean isInternal) {
        ClientUserMapping mapping = null;
        if (!isInternal) {
            int clientID = PacmanWorkContextHelper.getClientId();
            mapping = new ClientUserMapping();
            mapping.setClientID(clientID);
            mapping.setUserID(userID);
            globalCrudService.save(mapping);
        } else {
            // Get all clients
            List<Client> clients = clientConfigService.getAllClientDetails();
            for (Client client : clients) {
                mapping = new ClientUserMapping();
                mapping.setClientID(client.getId());
                mapping.setUserID(userID);
                globalCrudService.save(mapping);
            }
        }
    }

    @Override
    public void removeUser(String uid, boolean isInternal) {
        Integer userID = null;
        try {
            userID = Integer.parseInt(uid);
            removeGlobalUser(userID);
            removeTenantUser(userID, isInternal);
        } catch (Exception ex) {
            String message = "Failed to update user status to inactive : " + uid + " " + ex.getMessage();
            LOGGER.error(message, ex);
            throw new RuntimeException(message, ex);
        }
    }

    private void removeGlobalUser(Integer userID) {
        GlobalUser user = findGlobalUserByUid(userID);
        user.setStatusId(INACTIVE_STATUS_ID);
        globalCrudService.save(user);
    }

    private void removeTenantUser(Integer userID, boolean isInternal) {
        List<Property> properties = isInternal ?
                propertyService.getAllPropertyDetails() : propertyService.getPropertiesForClient();
        // better be one or we're in a pickle
        Integer firstPropertyId = properties.get(0).getId();
        User user = multiPropertyCrudService.find(firstPropertyId, User.class, userID);
        user.setStatusId(INACTIVE_STATUS_ID);
        for (Property property : properties) {
            multiPropertyCrudService.save(property.getId(), user);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public void deleteUser(Integer uid) {
        try {
            List<ClientUserMapping> mappings = globalCrudService.findByNamedQuery(ClientUserMapping.BY_USERID,
                    QueryParameter.with(ClientUserMapping.PARAM_USER_ID, uid).parameters());
            if (null != mappings && !mappings.isEmpty()) {
                for (ClientUserMapping mapping : mappings) {
                    globalCrudService.delete(ClientUserMapping.class, mapping.getId());
                }
            }
            globalCrudService.delete(GlobalUser.class, uid);

            multiPropertyCrudService.delete(PacmanWorkContextHelper.getPropertyId(), User.class, uid);
        } catch (Exception e) {
            String message = "Failed to delete user: " + uid;
            LOGGER.error(message, e);
            throw new RuntimeException(message, e);
        }
    }

    @Override
    public void createTenantUser(Integer propertyId, Integer userId, String email, String uid, boolean active, boolean internal) {
        Property property = globalCrudService.find(Property.class, propertyId);
        userSynchronizationAsyncService.persistTenantUserSynchronousByEmailID(Arrays.asList(property), userId, email, uid, active, true);
    }

    @Override
    public void deleteInternalUser(Integer uid) {

        try {
            List<ClientUserMapping> mappings = globalCrudService.findByNamedQuery(ClientUserMapping.BY_USERID,
                    QueryParameter.with(ClientUserMapping.PARAM_USER_ID, uid).parameters());
            if (null != mappings && !mappings.isEmpty()) {
                for (ClientUserMapping mapping : mappings) {
                    globalCrudService.delete(ClientUserMapping.class, mapping.getId());
                }
            }
            globalCrudService.delete(GlobalUser.class, uid);

            List<Property> properties = propertyService.getAllPropertyDetails();
            // better be one or we're in a pickle
            Integer firstPropertyId = properties.get(0).getId();
            User user = multiPropertyCrudService.find(firstPropertyId, User.class, uid);
            for (Property property : properties) {
                multiPropertyCrudService.delete(property.getId(), User.class, user.getId());
            }

        } catch (Exception e) {
            String message = "Failed to delete user: " + uid;
            LOGGER.error(message, e);
            throw new RuntimeException(message, e);
        }

    }

    @Override
    public List<GlobalUser> getAllDatabaseUsers(List<String> clientList) {
        List<GlobalUser> globalUsers = new ArrayList<GlobalUser>();
        for (String clientCode : clientList) {
            globalUsers.addAll(globalCrudService.findByNamedQuery(GlobalUser.BY_CLIENT_CODE, QueryParameter.with("clientCode", clientCode).parameters()));
        }
        return globalUsers;
    }


    private void setGlobalUserAuthGroupRoles(GlobalUser globalUser, List<AuthGroupRoleMapping> authGroupRoles) {
        AuthGroupRoleMapping authGroupRoleMapping;
        if (authGroupRoles != null && !authGroupRoles.isEmpty()) {
            if (authGroupRoles.size() > 1) {
                LOGGER.error("Only one auth group per user is supported but user has more than one. Using first. User: " + globalUser.getEmail());
            }
            authGroupRoleMapping = authGroupRoles.get(0);
            UserAuthGroupRole authGroupRole = globalUser.getAuthGroupRole();
            if (authGroupRole == null) {
                //create
                authGroupRole = new UserAuthGroupRole();
            }
            //else update
            authGroupRole.setGlobalUser(globalUser);
            authGroupRole.setAuthGroupId(Integer.valueOf(authGroupRoleMapping.getAuthGroupId()));
            authGroupRole.setRoleId(authGroupRoleMapping.getRoleId());
            globalUser.setAuthGroupRole(authGroupRole);
        } else {
            globalUser.setAuthGroupRole(null);
        }
    }

    private void setGlobalUserIndividualPropertyRoles(GlobalUser globalUser, List<PropertyRoleMapping> propertyRoles) {
        ArrayList<UserIndividualPropertyRole> userIndividualPropertyRoles = new ArrayList<>(globalUser.getIndividualPropertyRoles());
        globalUser.getIndividualPropertyRoles().clear();
        if (propertyRoles != null) {
            for (PropertyRoleMapping propertyRole : propertyRoles) {
                int propertyId = propertyRole.getPropertyIdAsInt();
                Property propertySummary = globalCrudService.find(Property.class, propertyId);
                if (propertySummary != null) {
                    UserIndividualPropertyRole individualPropertyRole = getIndividualPropertyRole(userIndividualPropertyRoles, propertyId);
                    if (individualPropertyRole == null) {
                        individualPropertyRole = new UserIndividualPropertyRole();
                    }
                    individualPropertyRole.setGlobalUser(globalUser);
                    individualPropertyRole.setPropertyId(propertySummary.getId());
                    individualPropertyRole.setRoleId(propertyRole.getRoleId());
                    globalUser.getIndividualPropertyRoles().add(individualPropertyRole);
                } else {
                    LOGGER.warn("During user sync setting user's individual properties, No property found with id: " + propertyId);
                }
            }
        }
    }

    private UserIndividualPropertyRole getIndividualPropertyRole(ArrayList<UserIndividualPropertyRole> individualPropertyRoles, Integer propertyId) {
        ArrayList<UserIndividualPropertyRole> userIndividualPropertyRoles = new ArrayList<>(individualPropertyRoles);
        for (UserIndividualPropertyRole userIndividualPropertyRole : userIndividualPropertyRoles) {
            if (userIndividualPropertyRole.getPropertyId().intValue() == propertyId.intValue()) {
                return userIndividualPropertyRole;
            }
        }
        return null;
    }
}

class TenantDatabaseOutOfSyncException extends RuntimeException {
    private static final long serialVersionUID = 4981771951400617739L;

    public TenantDatabaseOutOfSyncException(Throwable cause) {
        super(cause);
    }
}
