package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.reports.outputoverride.OutputOverrideService;
import com.ideas.tetris.pacman.services.reports.outputoverride.dto.OutputOverrideOverbookingDTO;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;

import javax.inject.Inject;
import java.time.LocalDate;
import java.util.List;

import static com.ideas.tetris.pacman.common.constants.Constants.OVERBOOKING_LIMIT;
import static com.ideas.tetris.pacman.common.constants.Constants.OVERBOOKING_VALUE;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class DatafeedOverbookingOverrideService {

    public static final String LIMIT = StringUtils.capitalize(OVERBOOKING_LIMIT);
    public static final String VALUE = StringUtils.capitalize(OVERBOOKING_VALUE);
    private static final String COMMON_PROPERTY = "common.property";
    @Autowired
	private OutputOverrideService outputOverrideService;

    public List getOverbookingOverrideDetails(LocalDate startDate, LocalDate endDate, Integer isRollingDate, String rollingStartDate, String rollingEndDate, Language language) {
        List<OutputOverrideOverbookingDTO> outputOverrideOverbookingDTOs = outputOverrideService.
                getOverrideOverbookingData(startDate, endDate, isRollingDate, rollingStartDate, rollingEndDate, language);
        if (null == outputOverrideOverbookingDTOs) {
            return ListUtils.EMPTY_LIST;
        }
        addOverBookingLimit(outputOverrideOverbookingDTOs);
        return outputOverrideOverbookingDTOs;

    }

    private void addOverBookingLimit(List<OutputOverrideOverbookingDTO> overrideOverbookingData) {

        for (OutputOverrideOverbookingDTO overbookingDTO : overrideOverbookingData) {
            if (COMMON_PROPERTY.equalsIgnoreCase(overbookingDTO.getAccomClassName())) {
                overbookingDTO.setAccomClassName(ResourceUtil.getText(COMMON_PROPERTY, Language.ENGLISH));
            }

            overbookingDTO.setOverrideCategory(ResourceUtil.getText(overbookingDTO.getOverrideCategory(), Language.ENGLISH));

            if (overbookingDTO.getCostofWalkValueOvr().intValue() == 0) {
                overbookingDTO.setCostofWalkValueOvr(null);
            }

            if (LIMIT.equalsIgnoreCase(overbookingDTO.getOverbookingTypeName())) {
                overbookingDTO.setOverBookingLimit(overbookingDTO.getOverbookingOvr());
            }

            if (!overbookingDTO.getOverbookingTypeName().equalsIgnoreCase(VALUE)) {
                overbookingDTO.setOverbookingOvr(null);
            }
        }
    }

}
