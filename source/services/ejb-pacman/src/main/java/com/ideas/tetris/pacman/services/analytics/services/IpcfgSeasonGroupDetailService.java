package com.ideas.tetris.pacman.services.analytics.services;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.roa.processgroup.entity.SeasonGroupDetail;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;

@Service
public class IpcfgSeasonGroupDetailService {
    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    private CrudService tenantCrudService;


    public List<SeasonGroupDetail> getSeasonGroupDetails(LocalDate startDate, LocalDate endDate, Collection<Integer> seasonGroupIds) {
        return tenantCrudService.findByNamedQuery(SeasonGroupDetail.FIND_BY_SEASON_DATE_RANGE_AND_IDS, QueryParameter
                .with("startDate", startDate.format(DateTimeFormatter.ISO_DATE))
                .and("endDate", endDate.format(DateTimeFormatter.ISO_DATE))
                .and("seasonGroupIds", seasonGroupIds)
                .parameters());
    }


}
