package com.ideas.tetris.pacman.services.reports;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.entity.AsyncReport;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import javax.inject.Inject;
import java.util.Date;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class AsyncReportsService {

    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    public AsyncReport findAsyncReportById(Integer id) {
        return crudService.findByNamedQuerySingleResult(AsyncReport.GET_ASYNC_REPORTS_BY_ID, QueryParameter.with("id", id).parameters());
    }

    public AsyncReport createAsyncReport(int userId, String reportType, String reportFormat, String pageCode, String paramStr, String reportFileName) {
        AsyncReport asyncReport = new AsyncReport();
        asyncReport.setUserId(userId);
        asyncReport.setPageCode(pageCode);
        asyncReport.setReportType(reportType);
        asyncReport.setReportFormat(reportFormat);
        asyncReport.setParameters(paramStr);
        asyncReport.setFileName(reportFileName);
        asyncReport.setCreated_DTTM(new Date());
        return crudService.save(asyncReport);
    }
}
