package com.ideas.tetris.pacman.services.problem;

import com.ideas.tetris.pacman.services.problem.entity.ProblemView;
import com.ideas.tetris.pacman.services.problem.entity.SupportBulletin;
import com.ideas.tetris.platform.common.entity.AbstractCriteria;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

public class ProblemBulletinCriteria extends AbstractCriteria<SupportBulletin> {
    private String jobName;
    private String stepName;

    public ProblemBulletinCriteria() {
        super(SupportBulletin.class);
    }

    public ProblemBulletinCriteria(ProblemView problem) {
        this();
        jobName = problem.getJobName();
        stepName = problem.getStepName();
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getStepName() {
        return stepName;
    }

    public void setStepName(String stepName) {
        this.stepName = stepName;
    }

    @Override
    public DetachedCriteria getDetachedCriteria() {
        DetachedCriteria detachedCriteria = super.getDetachedCriteria();
        detachedCriteria.add(Restrictions.eq("active", true));
        detachedCriteria.add(Restrictions.or(Restrictions.isNull("jobName"),
                Restrictions.eq("jobName", jobName)));
        detachedCriteria.add(Restrictions.or(Restrictions.isNull("stepName"),
                Restrictions.eq("stepName", stepName)));

        return detachedCriteria;
    }

}
