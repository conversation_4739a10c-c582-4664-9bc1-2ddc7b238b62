package com.ideas.tetris.pacman.services.reports.inventoryhistory.dto;

public class InventoryHistoryParams {

    private boolean isRollingDate;

    public String getSelectedProducts() {
        return selectedProducts;
    }

    public void setSelectedProducts(String selectedProducts) {
        this.selectedProducts = selectedProducts;
    }

    private String selectedProducts;
    private String startDate;
    private String endDate;
    private String rollingStartDate;
    private String rollingEndDate;
    private InventoryDecisionType inventoryDecisionType;
    private String level;
    private InventoryFPLOSLevel inventoryFPLOSLevel;
    private InventoryOverbookingLevel inventoryOverbookingLevel;
    private String selectedRateLevels;
    private String selectedSRP;
    private String selectedRoomTypes;
    private boolean isSRPFPLOSAtTotalEnabled;
    private boolean isLV0Selected;
    private String includeSuccessfulControls;
    private String includeFailures;
    private boolean includePace;
    private boolean includeConsortia;

    public boolean isRollingDate() {
        return isRollingDate;
    }

    public void setRollingDate(boolean rollingDate) {
        isRollingDate = rollingDate;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getRollingStartDate() {
        return rollingStartDate;
    }

    public void setRollingStartDate(String rollingStartDate) {
        this.rollingStartDate = rollingStartDate;
    }

    public String getRollingEndDate() {
        return rollingEndDate;
    }

    public void setRollingEndDate(String rollingEndDate) {
        this.rollingEndDate = rollingEndDate;
    }

    public InventoryDecisionType getInventoryDecisionType() {
        return inventoryDecisionType;
    }

    public void setInventoryDecisionType(InventoryDecisionType inventoryDecisionType) {
        this.inventoryDecisionType = inventoryDecisionType;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public InventoryFPLOSLevel getInventoryFPLOSLevel() {
        return inventoryFPLOSLevel;
    }

    public void setInventoryFPLOSLevel(InventoryFPLOSLevel inventoryFPLOSLevel) {
        this.inventoryFPLOSLevel = inventoryFPLOSLevel;
    }

    public InventoryOverbookingLevel getInventoryOverbookingLevel() {
        return inventoryOverbookingLevel;
    }

    public void setInventoryOverbookingLevel(InventoryOverbookingLevel inventoryOverbookingLevel) {
        this.inventoryOverbookingLevel = inventoryOverbookingLevel;
    }

    public String getSelectedRateLevels() {
        return selectedRateLevels;
    }

    public void setSelectedRateLevels(String selectedRateLevels) {
        this.selectedRateLevels = selectedRateLevels;
    }

    public String getSelectedSRP() {
        return selectedSRP;
    }

    public void setSelectedSRP(String selectedSRP) {
        this.selectedSRP = selectedSRP;
    }

    public String getSelectedRoomTypes() {
        return selectedRoomTypes;
    }

    public void setSelectedRoomTypes(String selectedRoomTypes) {
        this.selectedRoomTypes = selectedRoomTypes;
    }

    public boolean isSRPFPLOSAtTotalEnabled() {
        return isSRPFPLOSAtTotalEnabled;
    }

    public void setSRPFPLOSAtTotalEnabled(boolean SRPFPLOSAtTotalEnabled) {
        isSRPFPLOSAtTotalEnabled = SRPFPLOSAtTotalEnabled;
    }

    public boolean isLV0Selected() {
        return isLV0Selected;
    }

    public void setLV0Selected(boolean LV0Selected) {
        isLV0Selected = LV0Selected;
    }

    public String isIncludeSuccessfulControls() {
        return includeSuccessfulControls;
    }

    public void setIncludeSuccessfulControls(String includeSuccessfulControls) {
        this.includeSuccessfulControls = includeSuccessfulControls;
    }

    public String isIncludeFailures() {
        return includeFailures;
    }

    public void setIncludeFailures(String includeFailures) {
        this.includeFailures = includeFailures;
    }

    public boolean isIncludePace() {
        return includePace;
    }

    public void setIncludePace(boolean includePace) {
        this.includePace = includePace;
    }

    public boolean isIncludeConsortia() {
        return includeConsortia;
    }

    public void setIncludeConsortia(boolean includeConsortia) {
        this.includeConsortia = includeConsortia;
    }
}
