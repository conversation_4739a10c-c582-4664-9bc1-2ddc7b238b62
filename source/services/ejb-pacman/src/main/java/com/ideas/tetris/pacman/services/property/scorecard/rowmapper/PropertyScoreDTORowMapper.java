package com.ideas.tetris.pacman.services.property.scorecard.rowmapper;

import com.ideas.tetris.pacman.services.property.scorecard.dto.PropertyScoreDTO;
import com.ideas.tetris.platform.common.crudservice.RowMapper;

import java.math.BigDecimal;
import java.util.Date;

public class PropertyScoreDTORowMapper implements RowMapper<PropertyScoreDTO> {
    @Override
    public PropertyScoreDTO mapRow(Object[] row) {
        PropertyScoreDTO propertyScoreDTO = new PropertyScoreDTO();
        propertyScoreDTO.setPropertyId((Integer) row[0]);
        propertyScoreDTO.setStartDate((Date) row[1]);
        propertyScoreDTO.setEndDate((Date) row[2]);
        propertyScoreDTO.setScore((BigDecimal) row[3]);
        propertyScoreDTO.setFutureScore((BigDecimal) row[4]);
        propertyScoreDTO.setFutureScoreStartDate((Date) row[5]);
        propertyScoreDTO.setFutureScoreEndDate((Date) row[6]);
        propertyScoreDTO.setGenerationDate((Date) row[7]);
        propertyScoreDTO.setEarliestDecisionDate((Date) row[8]);
        return propertyScoreDTO;
    }
}
