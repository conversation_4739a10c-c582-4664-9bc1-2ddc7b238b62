package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.marketsegment.service.MarketSegmentService;
import com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants;
import com.ideas.tetris.pacman.services.opera.metric.OperaMetrics;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackData;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackDataBuilder;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.job.JobCallback;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.Async;
import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.function.Function;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * Created by idnpak on 2/25/2015.
 */
@Component
@Transactional
public class OperaPreProcessStageDataService {
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
	protected OperaPreProcessTransactionDataService operaPreProcessTransactionDataService;
    @Autowired
    OperaTransformGroupDataService operaTransformGroupDataService;
    @Autowired
    AccommodationService accommodationService;
    @Autowired
    MarketSegmentService marketSegmentService;

    private static final Logger LOGGER = Logger.getLogger(OperaPreProcessStageDataService.class.getName());

    @Async
    public Future<Integer> preProcessStageTransactionData(JobStepContext jobStepContext, WorkContextType buildWorkContext, String correlationId) {
        return invokeAsync(jobStepContext, buildWorkContext, correlationId, c -> preProcessStageTransactionData(c, 0));
    }

    @Async
    public Future<Integer> updateAnalyticalMarketSegments(JobStepContext jobStepContext, WorkContextType buildWorkContext, String correlationId) {
        return invokeAsync(jobStepContext, buildWorkContext, correlationId, c -> updateAnalyticalMarketSegments(c, 0));
    }

    @Async
    public Future<Integer> updateGroupAnalyticalMarketSegmentsDefault(JobStepContext jobStepContext, WorkContextType buildWorkContext, String correlationId) {
        return invokeAsync(jobStepContext, buildWorkContext, correlationId, c -> updateGroupAnalyticalMarketSegmentsDefault(c, 0));
    }

    @Async
    public Future<Integer> preProcessStageGroupData(JobStepContext jobStepContext, WorkContextType buildWorkContext, String correlationId) {
        return invokeAsync(jobStepContext, buildWorkContext, correlationId, c -> preProcessStageGroupData(c, 0));
    }

    private Future<Integer> invokeAsync(JobStepContext jobStepContext, WorkContextType buildWorkContext, String correlationId, Function<String, Integer> function) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(buildWorkContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            numberOfRows = function.apply(correlationId);
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    public JobCallback getJobCallback() {
        return new JobCallback();
    }

    public enum PreProcessDataMetricType {
        PRE_PROCESS_STAGE_DATA, UPDATE_ANALYTICAL_MARKET_SEGMENTS;
    }

    public static final OperaMetrics<PreProcessDataMetricType> metricsPreProcess = new OperaMetrics<PreProcessDataMetricType>();

    private static final String SQL_QUERY_FOR_UNIQUE_ROOM_TYPES = new StringBuilder()
            .append("select distinct room_type from opera.Stage_Transaction ")
            .append("		union select distinct booked_room_type as room_type from opera.Stage_Transaction where booked_room_type<>''")
            .append("		union select distinct room_type as room_type from opera.Stage_Occupancy_Summary where room_type<>''")
            .append("		union select distinct room_type from opera.stage_group_block").toString();

    private static final String SQL_QUERY_FOR_UNIQUE_MARKET_SEGMENT_CODES = new StringBuilder()
            .append("SELECT DISTINCT Market_Code as Market_Code FROM opera.Stage_Transaction ")
            .append("		union select distinct market_segment as Market_Code from opera.stage_group_master")
            .append("		union select distinct Market_Code as Market_Code from opera.Stage_Occupancy_Summary where Market_Code<>''")
            .append("		EXCEPT select distinct market_code from opera.Yield_Category_Rule").toString();

    private static final String FIND_INACTIVE_ACCOMMODATION_TYPES_SQL = new StringBuilder()
            .append("SELECT rt.room_type AS Room_Type\n")
            .append("  FROM   (SELECT room_type\n")
            .append("  FROM   opera.stage_transaction\n")
            .append("  UNION\n")
            .append("  SELECT room_type\n")
            .append("  FROM   opera.stage_occupancy_summary\n")
            .append("  WHERE  room_type <> ''\n")
            .append("  UNION\n")
            .append("  SELECT room_type\n")
            .append("  FROM   opera.stage_group_block) rt\n")
            .append("  INNER JOIN accom_type at\n")
            .append("  ON rt.room_type = at.accom_type_code\n")
            .append("  AND at.display_status_id = 2")
            .toString();

    private static final String CREATE_UPDATE_PSEUDO_ACCOM_TYPES = new StringBuilder()
            .append("; with pseudoRoomTypesInAccomTypeTable as ")
            .append("( ")
            .append("select Accom_Type_ID, Accom_Type_Code from accom_type where Status_ID = 6 and Display_Status_ID = 4")
            .append("),")
            .append("nonPseudoAccomTypes as ")
            .append(" ( ")
            .append(" select Accom_Type_ID, Accom_Type_Code from accom_type where Status_ID <> 6")
            .append(" ),")
            .append("pseudoRTList as ")
            .append("( ")
            .append("select * from (")
            .append("    select value as [Value] ")
            .append("    from string_split (:pseudoList , ',') ")
            .append("    ) as prt where value <> ''")
            .append("    ),")
            .append("newNonPseudoRoomType as ") //Pseudo RT converted to actual RT. Update the status_ID and Display_Status_ID = 1 for these accom_types
            .append("( ")
            .append("select  ")
            .append("  acct.Accom_Type_Code as newNonPseudoRoomType  ")
            .append("    from pseudoRoomTypesInAccomTypeTable prt ")
            .append("    inner join Accom_Type acct  ")
            .append("    on prt.Accom_Type_ID = acct.Accom_Type_ID  ")
            .append("    where acct.Accom_Type_Code not in (select value from pseudoRTList) ")
            .append("), ")
            .append("newPseudoInitiallyActiveNonActive as ")//initially active RT now pseudoRT
            .append(" ( ")
            .append("   select value as newPseudoRT from pseudoRTList where value in (select accom_Type_Code from nonPseudoAccomTypes) ")
            .append(" ),")
            .append("newPseudoRT as ")//to be added to Accom_Type as pseudo RT
            .append("( ")
            .append("select value as pseudoRT from pseudoRTList where value not in (select Accom_Type_Code from pseudoRoomTypesInAccomTypeTable) ")
            .append("and value not in (select accom_type_code from nonPseudoAccomTypes)")
            .append(") ")
            .append("select * ")
            .append("into #psroomType ")
            .append("from  ")
            .append("( ")
            .append("select newNonPseudoRoomType, pseudoRT, newPseudoRT from ")
            .append("        newNonPseudoRoomType nrt ")
            .append("        full join newPseudoRT nprt ")
            .append("        on nrt.newNonPseudoRoomType = nprt.pseudoRT ")
            .append("        full join newPseudoInitiallyActiveNonActive aat ")
            .append("        on nrt.newNonPseudoRoomType = aat.newPseudoRT ")
            .append(")as a; ")
            .append("update Accom_Type set Status_ID = 1, display_status_id = 1 where Accom_Type_Code in (select newNonPseudoRoomType from #psroomType where newNonPseudoRoomType is not null); ")
            .append("update Accom_Type set Status_ID = 6, display_status_id = 4, accom_class_id = 1 where Accom_Type_Code in (select newPseudoRT from #psroomType where newPseudoRT is not null); ")
            .append("INSERT INTO [dbo].[Accom_Type] ")
            .append("     select ")
            .append("            :propertyID ")
            .append("            ,pseudoRT,pseudoRT,'Pseudo Room',0,1,0,6,GETDATE(),0,11043,GETDATE(),11043,'N',4,0,NULL  ")
            .append("     from  ")
            .append("            #psroomType  ")
            .append("     where pseudoRT is not null;")
            .append("drop table #psroomType;")
            .toString();

    private static final String CORRECT_SHARERS_THAT_ARE_NOT_TRUE_SHARERS = new StringBuilder(
            " update opera.stage_transaction set sharers = null where LTRIM(RTRIM(confirmation_number)) = LTRIM(RTRIM(sharers))")
            .toString();

    public int preProcessStageData(String correlationId, boolean runYieldCategoryByRuleAndCreateMarketSegments) {
        LOGGER.info("Started pre processing stage data for feed : " + correlationId);
        metricsPreProcess.start(PreProcessDataMetricType.PRE_PROCESS_STAGE_DATA);
        int numRows = 0;
        try {
            numRows = preProcessStageTransactionData(correlationId, numRows);

            if (runYieldCategoryByRuleAndCreateMarketSegments) {
                metricsPreProcess.start(PreProcessDataMetricType.UPDATE_ANALYTICAL_MARKET_SEGMENTS);

                numRows = updateAnalyticalMarketSegments(correlationId, numRows);

                numRows = updateGroupAnalyticalMarketSegmentsDefault(correlationId, numRows);

                metricsPreProcess.stop(PreProcessDataMetricType.UPDATE_ANALYTICAL_MARKET_SEGMENTS);

                numRows = preProcessStageGroupData(correlationId, numRows);

                metricsPreProcess.stop(PreProcessDataMetricType.PRE_PROCESS_STAGE_DATA);
            }
        } finally {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug(new StringBuilder().append("Finished Pre processing stage data ").append(numRows)
                        .append("  rows:\n").append(metricsPreProcess.toString()).toString());
            }
        }
        LOGGER.info("Completed pre processing stage data for feed : " + correlationId);
        return numRows;
    }

    private int preProcessStageGroupData(String correlationId, int numRows) {
        numRows += operaTransformGroupDataService.preProcessGroupData(correlationId);
        return numRows;
    }

    private int updateGroupAnalyticalMarketSegmentsDefault(String correlationId, int numRows) {
        // Update MS for Groups according to MS by Rule.
        // This needs to happen before creating list of all
        // participating
        // market segments.
        numRows += operaTransformGroupDataService.updateAnalyticalMarketSegments(correlationId);

        return numRows;
    }

    private int updateAnalyticalMarketSegments(String correlationId, int numRows) {
        // Updating analytical market segments needs to go first as
        // later
        // transformations
        // may reference them within the transaction data (such as
        // occupancy
        // summary transformations and creating missing market
        // segments).

        numRows += operaPreProcessTransactionDataService.updateAnalyticalMarketSegments(correlationId);
        return numRows;
    }

    private int preProcessStageTransactionData(String correlationId, int numRows) {
        // The below 2 conditions are valid to data being pulled from raw
        // transaction to complete the current feed trans
        // If arrival date changes in second feed, delete the reservation
        // record from first feed.
        numRows += operaPreProcessTransactionDataService.deleteTrans();
        // Adjust the departure dates for out of window transactions
        // If departure date changes in second feed, ensure that departure
        // date in first feed data is updated with latest departure date
        numRows += operaPreProcessTransactionDataService.updateDepartureDt();
        // Update MS,RT, RC, Arrival and Departure for post departure
        // transaction
        // Use the MS,RT and RC of the primary transaction - i.e transaction
        // with minimum transaction_dt
        numRows += operaPreProcessTransactionDataService.transformPostDepartureTransactions(correlationId);
        // Sharers marked with same confirmation number need to be treated
        // as individual reservations
        numRows += crudService.executeUpdateByNativeQuery(CORRECT_SHARERS_THAT_ARE_NOT_TRUE_SHARERS);
        return numRows;
    }

    public List<String> findInactiveAccomTypes() {
        return crudService.findByNativeQuery(FIND_INACTIVE_ACCOMMODATION_TYPES_SQL);
    }

    public void createOrUpdatePseudoAccomTypes() {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INCLUDE_PSEUDO_IN_REVENUE)) {
            String roomTypes = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.PSEUDO_ROOM_TYPE_CODES);
            crudService.executeUpdateByNativeQuery(CREATE_UPDATE_PSEUDO_ACCOM_TYPES, QueryParameter.with("pseudoList", roomTypes)
                    .and("propertyID", PacmanWorkContextHelper.getPropertyId()).parameters());
        }
    }

    public void createAccomTypeAndMSEntriesForNewTypes(String correlationId) {
        // Create missing accommodation types
        createOrUpdatePseudoAccomTypes();
        String criteria = buildRoomTypeCriteria();
        String findSql = SQL_QUERY_FOR_UNIQUE_ROOM_TYPES.replace("{criteria}", criteria);
        accommodationService.createMissingAccomTypes(findSql);
        // Create missing market segments
        Set<String> createMissingMktSegs = null;
        createMissingMktSegs = marketSegmentService.createMissing(SQL_QUERY_FOR_UNIQUE_MARKET_SEGMENT_CODES);
        createMktSegDetailsProposed(createMissingMktSegs);
    }

    protected String buildRoomTypeCriteria() {
    	StringBuilder buffer = new StringBuilder();
    	List<String> roomTypesToInclude = getParameterValueAsListOfStrings(IntegrationConfigParamName.ROOM_TYPES_TO_INCLUDE.value(Constants.OPERA));
    	if (!roomTypesToInclude.isEmpty()) {
    		buffer.append(" IN (");
    		boolean first = true;
    		for (String roomType : roomTypesToInclude) {
    			if (first) {
    				first = false;
    			} else {
    				buffer.append(", ");
    			}
    			buffer.append("'" + roomType + "'");
    		}
    		buffer.append(")");
    	} else {
    		buffer.append(" NOT IN (''");
    		List<String> roomTypesToExclude = getParameterValueAsListOfStrings(IntegrationConfigParamName.PSEUDO_ROOM_TYPE_CODES.value());
    		for (String roomType : roomTypesToExclude) {
    			buffer.append(", '" + roomType + "'");
    		}
    		buffer.append(")");
    	}
    	return buffer.toString();
    }

    private List<String> getParameterValueAsListOfStrings(String parameterKey) {
        List<String> list = new ArrayList<String>();
        String value = pacmanConfigParamsService.getParameterValue(parameterKey);
        if (!StringUtils.isBlank(value)) {
            String[] individualValues = value.split(",");
            for (String individualValue : individualValues) {
                list.add(individualValue.trim());
            }
        }
        return list;
    }

    private void createMktSegDetailsProposed(Collection<String> createMissingMktSegs) {
        LOGGER.info("Started Creating MS Details.");
        if (!CollectionUtils.isEmpty(createMissingMktSegs)) {
            int recordsInserted = crudService.executeUpdateByNativeQuery(OperaTransactionServiceConstants.CREATE_MKT_SEG_DETAILS_PROPOSED,
                    QueryParameter.with("mktCodeList", createMissingMktSegs).and("processStatus", ProcessStatus.UPDATED).parameters());
            LOGGER.debug("No of records added in MKT_SEG_DETAILS_PROPOSED are " + recordsInserted);
        } else {
            LOGGER.info("No new Market segment detected so no records added in MKT_SEG_DETAILS_PROPOSED");
        }
        LOGGER.info("Completed Creating MS Details.");
    }
}
