package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.businessgroup.service.BusinessGroupService;
import com.ideas.tetris.pacman.services.datafeed.dto.MarketSegmentConfig;
import com.ideas.tetris.pacman.services.independentproducts.service.IndependentProductsService;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegAttributes;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegBusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetails;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetailsProposed;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegForecastGroup;
import com.ideas.tetris.pacman.services.marketsegment.service.MarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.forecastgroup.service.ForecastGroupFinalService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.ideas.tetris.pacman.common.constants.Constants.BLOCK;
import static com.ideas.tetris.pacman.common.constants.Constants.BUSINESS_TYPE;
import static com.ideas.tetris.pacman.common.constants.Constants.FENCED;
import static com.ideas.tetris.pacman.common.constants.Constants.NEGATIVE_CODE;
import static com.ideas.tetris.pacman.common.constants.Constants.NO;
import static com.ideas.tetris.pacman.common.constants.Constants.NON_BLOCK;
import static com.ideas.tetris.pacman.common.constants.Constants.NON_PACKAGED;
import static com.ideas.tetris.pacman.common.constants.Constants.PACKAGED;
import static com.ideas.tetris.pacman.common.constants.Constants.POSITIVE_CODE;
import static com.ideas.tetris.pacman.common.constants.Constants.QUALIFIED;
import static com.ideas.tetris.pacman.common.constants.Constants.UNASSIGNED;
import static com.ideas.tetris.pacman.common.constants.Constants.UNFENCED;
import static com.ideas.tetris.pacman.common.constants.Constants.UNQUALIFIED;
import static com.ideas.tetris.pacman.common.constants.Constants.YES;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class MarketSegmentConfigService {


    public static final int BLOCK_PERCENTAGE = 100;
    @Autowired
	private MarketSegmentService marketSegmentService;
    @Autowired
	private ForecastGroupFinalService forecastGroupFinalService;
    @Autowired
	private BusinessGroupService businessGroupService;
    @Autowired
	private IndependentProductsService independentProductsService;
    @Autowired
	private PacmanConfigParamsService configParamsService;

    public static final String TRANSIENT = BUSINESS_TYPE.TRANSIENT.getCode();
    public static final String SYSTEM_DECISION = "System Decision";
    public static final String GROUP = BUSINESS_TYPE.GROUP.getCode();

    public List getMktSegConfigDetails() {
        List<MarketSegmentConfig> mktSegCofigs = new ArrayList<>();
        List<MktSeg> marketSegments = marketSegmentService.getMktSegByPropertyId();
        List<ForecastGroup> forecastGroups = forecastGroupFinalService.getForecastGroupByPropertyId();
        List<BusinessGroup> businessGroups = businessGroupService.getAllBusinessGroupsAssociatedWithMktSeg();
        populateWithMarketSegments(mktSegCofigs, marketSegments);
        List<MktSegForecastGroup> mktSegForecastGroups = generateAllMktSegForecastGroups(forecastGroups);
        populateWithMktSegForecast(mktSegCofigs, mktSegForecastGroups);
        List<MktSegBusinessGroup> mktSegBusinessGroups = generateAllMktSegBusinessGroup(businessGroups);
        populateWithBusinessGroupDetails(mktSegCofigs, mktSegBusinessGroups);
        populateWithBusinessGroupsWithoutMktSeg(mktSegCofigs, businessGroups);

        Collections.sort(mktSegCofigs);
        return mktSegCofigs;
    }

    private void populateWithBusinessGroupsWithoutMktSeg(List<MarketSegmentConfig> mktSegCofigs, List<BusinessGroup> businessGroups) {
        for (BusinessGroup businessGroup : businessGroups) {
            MarketSegmentConfig marketSegmentConfig = new MarketSegmentConfig();
            marketSegmentConfig.setBusinessViewName(businessGroup.getName());
            marketSegmentConfig.setBusinessViewDescription(businessGroup.getDescription());
            mktSegCofigs.add(marketSegmentConfig);
        }

    }

    private void populateWithBusinessGroupDetails(List<MarketSegmentConfig> mktSegCofigs, List<MktSegBusinessGroup> mktSegBusinessGroups) {
        List<MktSegBusinessGroup> processedMktSegBusinessGroup = new ArrayList<>();
        for (MarketSegmentConfig mktSegConfig : mktSegCofigs) {
            mktSegConfig.setBusinessViewName(UNASSIGNED);
            mktSegConfig.setBusinessViewDescription(UNASSIGNED);
            for (MktSegBusinessGroup mktSegBusinessGroup : mktSegBusinessGroups) {
                if (mktSegBusinessGroup.getMktSeg().getCode().equalsIgnoreCase(mktSegConfig.getMktSegCode())) {
                    mktSegConfig.setBusinessViewName(mktSegBusinessGroup.getBusinessGroup().getName());
                    mktSegConfig.setBusinessViewDescription(mktSegBusinessGroup.getBusinessGroup().getDescription());
                    processedMktSegBusinessGroup.add(mktSegBusinessGroup);
                }
            }
        }
        mktSegBusinessGroups.removeAll(processedMktSegBusinessGroup);
    }

    private List<MktSegBusinessGroup> generateAllMktSegBusinessGroup(List<BusinessGroup> businessGroups) {
        List<MktSegBusinessGroup> mktSegBusinessGroups = new ArrayList<>();
        List<BusinessGroup> processedBusinessGroups = new ArrayList<>();
        for (BusinessGroup businessGroup : businessGroups) {
            if (businessGroup.getMktSegBusinessGroups().size() > 0) {
                mktSegBusinessGroups.addAll(businessGroup.getMktSegBusinessGroups());
                processedBusinessGroups.add(businessGroup);
            }
        }
        businessGroups.removeAll(processedBusinessGroups);
        return mktSegBusinessGroups;
    }

    private void populateWithMktSegForecast(List<MarketSegmentConfig> mktSegCofigs, List<MktSegForecastGroup> mktSegForecastGroups) {
        for (MktSegForecastGroup mktSegForecastGroup : mktSegForecastGroups) {
            for (MarketSegmentConfig mktSegCofig : mktSegCofigs) {
                if (mktSegForecastGroup.getMktSeg().getCode().equalsIgnoreCase(mktSegCofig.getMktSegCode())) {
                    mktSegCofig.setForecastGroupCode(mktSegForecastGroup.getForecastGroup().getCode());
                    mktSegCofig.setForecastGroupName(mktSegForecastGroup.getForecastGroup().getName());
                    mktSegCofig.setForecastGroupDescription(mktSegForecastGroup.getForecastGroup().getDescription());
                }
            }
        }
    }

    private List<MktSegForecastGroup> generateAllMktSegForecastGroups(List<ForecastGroup> forecastGroups) {
        List<MktSegForecastGroup> mktSegForecastGroups = new ArrayList<>();
        for (ForecastGroup forecastGroup : forecastGroups) {
            mktSegForecastGroups.addAll(forecastGroup.getMktSegForecastGroup());
        }
        return mktSegForecastGroups;
    }

    private void populateWithMarketSegments(List<MarketSegmentConfig> mktSegCofigDetails, List<MktSeg> marketSegments) {
        boolean includeProductName = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.HIERARCHY_INDEPENDENT_PRODUCT_COLUMN_ENABLED);
        for (MktSeg mktSeg : marketSegments) {
            MarketSegmentConfig marketSegmentConfig = new MarketSegmentConfig();
            MktSegDetails mktSegDetails = mktSeg.getMktSegDetails();
            MktSegDetailsProposed mktSegDetailsProposed = mktSeg.getMktSegDetailsProposed();
            marketSegmentConfig.setMktSegCode(mktSeg.getCode());
            marketSegmentConfig.setMktSegName(mktSeg.getName());
            marketSegmentConfig.setMktSegDescription(mktSeg.getDescription());
            if (includeProductName) {
                final Optional<Product> optionalProduct = independentProductsService.getProductMappedToMarketSegment(mktSeg.getCode());
                marketSegmentConfig.setBaseProduct(optionalProduct.isEmpty() ? "" : optionalProduct.get().getName());
            }
            populateWithMktSegDetails(marketSegmentConfig, getDetails(mktSegDetails, mktSegDetailsProposed));
            mktSegCofigDetails.add(marketSegmentConfig);
        }
    }

    private MktSegAttributes getDetails(MktSegDetails mktSegDetails, MktSegDetailsProposed mktSegDetailsProposed) {
        if (null != mktSegDetails) {
            return mktSegDetails;
        } else {
            return mktSegDetailsProposed;
        }

    }

    private void populateWithMktSegDetails(MarketSegmentConfig marketSegmentConfig, MktSegAttributes source) {
        if (null == source) {
            return;
        }
        String businessTypeName = source.getBusinessType().getName();

        marketSegmentConfig.setBusinessType(source.getBusinessType().getName());
        marketSegmentConfig.setBooking(getBooking(businessTypeName, source.getBookingBlockPc(), source.getQualified()));
        marketSegmentConfig.setLinked(getLink(businessTypeName, source.getLink(), source.getQualified()));
        marketSegmentConfig.setSelling(getSelling(businessTypeName, source.getQualified(), source.getFenced(), source.getPackageValue()));
        marketSegmentConfig.setContract(getContract(businessTypeName, source.getQualified()));
        marketSegmentConfig.setForecastType(source.getForecastActivityType().getName());
        marketSegmentConfig.setControl(source.getYieldType().getName());
        marketSegmentConfig.setPricedByBar(evaluateResponse(source.getPriceByBar(), NEGATIVE_CODE, POSITIVE_CODE, NO, YES));

    }

    private String getContract(String businessTypeName, Integer qualified) {
        if (businessTypeName.equalsIgnoreCase(GROUP)) {
            return SYSTEM_DECISION;
        }
        return evaluateResponse(qualified, NEGATIVE_CODE, POSITIVE_CODE, UNQUALIFIED, QUALIFIED);
    }

    private String getSelling(String businessTypeName, Integer qualified, Integer fenced, Integer packageValue) {
        if ((businessTypeName.equalsIgnoreCase(TRANSIENT) && qualified == POSITIVE_CODE) || businessTypeName.equalsIgnoreCase(GROUP)) {
            return SYSTEM_DECISION;
        }
        return evaluateResponse(fenced, NEGATIVE_CODE, POSITIVE_CODE, UNFENCED, FENCED) + " " +
                evaluateResponse(packageValue, NEGATIVE_CODE, POSITIVE_CODE, NON_PACKAGED, PACKAGED);
    }

    private String getLink(String businessType, Integer link, Integer qualified) {
        if ((TRANSIENT.equalsIgnoreCase(businessType) && qualified == NEGATIVE_CODE) || GROUP.equalsIgnoreCase(businessType)) {
            return SYSTEM_DECISION;
        }
        return evaluateResponse(link, NEGATIVE_CODE, POSITIVE_CODE, NO, YES);
    }

    private String evaluateResponse(Integer receivedCode, Integer negativeCode, Integer positiveCode, String negativeResponse, String positiveResponse) {
        if (receivedCode != null) {
            if (receivedCode.equals(negativeCode)) {
                return negativeResponse;
            }
            if (receivedCode.equals(positiveCode)) {
                return positiveResponse;
            }
        }
        return null;
    }

    public String getBooking(String businessType, Integer bookingBlock, Integer qualified) {
        if ((TRANSIENT.equalsIgnoreCase(businessType) && qualified == NEGATIVE_CODE) || GROUP.equalsIgnoreCase(businessType)) {
            return SYSTEM_DECISION;
        }
        return evaluateResponse(bookingBlock, NEGATIVE_CODE, BLOCK_PERCENTAGE, NON_BLOCK, BLOCK);
    }
}
