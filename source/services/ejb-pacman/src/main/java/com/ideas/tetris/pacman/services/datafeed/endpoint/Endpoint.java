package com.ideas.tetris.pacman.services.datafeed.endpoint;

/* This enum stores all list of rest point configured
 * fileName : this field value should be same as configured at NGI side filename in DatafeedEndpoint enum */

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.datafeed.endpoint.DatafeedEndPointFunctions.*;


public enum Endpoint {
    ROOM_TYPE_ST2Y("RoomType", EndpointFrequencyType.DAILY, EndpointBucket.CORE, DatafeedEndPointFunctions.ST19_FOR_DATAFEED_MSRT_DISABLED),
    ROOM_TYPE_ST19("RoomType", EndpointFrequencyType.DAILY, EndpointBucket.CORE, DatafeedEndPointFunctions.ST19_FOR_DATAFEED_MSRT_ENABLED),
    ROOM_TYPE_ACTIVITY("RoomTypeActivity", EndpointFrequencyType.ON_DEMAND, EndpointBucket.CORE),
    ROOM_TYPE_FORECAST("RoomTypeForecast", EndpointFrequencyType.ON_DEMAND, EndpointBucket.CORE),
    ROOM_TYPE_OVERBOOKING("RoomTypeOverbooking", EndpointFrequencyType.ON_DEMAND, EndpointBucket.CORE),
    MARKET_SEGMENT_ST2Y("MarketSegment", EndpointFrequencyType.DAILY, EndpointBucket.CORE, DatafeedEndPointFunctions.ST19_FOR_DATAFEED_MSRT_DISABLED),
    MARKET_SEGMENT_ST19("MarketSegment", EndpointFrequencyType.DAILY, EndpointBucket.CORE, DatafeedEndPointFunctions.ST19_FOR_DATAFEED_MSRT_ENABLED),
    MARKET_SEGMENT_FORECAST("MarketSegmentForecast", EndpointFrequencyType.ON_DEMAND, EndpointBucket.CORE),
    MARKET_SEGMENT_ACTIVITY("MarketSegmentActivity", EndpointFrequencyType.ON_DEMAND, EndpointBucket.CORE),
    ORIGINAL_MARKET_SEGMENT_FORECAST("OriginalMarketSegmentForecast", EndpointFrequencyType.ON_DEMAND, EndpointBucket.MARS),
    TOTAL_ACTIVITY_SUMMARY("TotalActivitySummary", EndpointFrequencyType.ON_DEMAND, EndpointBucket.MARS),
    REVPLAN_SPECIAL_EVENT("SpecialEvent", EndpointFrequencyType.ON_DEMAND, EndpointBucket.MARS),
    REVPLAN_RESERVATION_NIGHT("ReservationNight", EndpointFrequencyType.ON_DEMAND, EndpointBucket.MARS),
    REVPLAN_ROOM_TYPE("RoomType", EndpointFrequencyType.ON_DEMAND, EndpointBucket.MARS),
    REVPLAN_MARKET_SEGMENT("RevplanMarketSegment", EndpointFrequencyType.NEVER, EndpointBucket.MARS),
    REVPLAN_MARKET_SEGMENT_PACE("RevplanMarketSegmentPace", EndpointFrequencyType.NEVER, EndpointBucket.MARS),
    REVPLAN_MARKET_ACCOM_ACTIVITY("RevplanMarketAccomActivity", EndpointFrequencyType.NEVER, EndpointBucket.MARS),
    REVPLAN_MARKET_OCCUPANCY_FORECAST("RevPlanMarketOccupancyForecast", EndpointFrequencyType.ON_DEMAND, EndpointBucket.MARS),
    ROOM_CLASS("RoomClass", EndpointFrequencyType.DAILY, EndpointBucket.CORE),
    MARKET_SEGMENT_CONFIG("MarketSegmentConfig", EndpointFrequencyType.DAILY, EndpointBucket.CORE, DatafeedEndPointFunctions.PRODUCT_NAME_COLUMN_DISABLED),
    MARKET_SEGMENT_CONFIG_ENHANCED("MarketSegmentConfig", EndpointFrequencyType.DAILY, EndpointBucket.CORE, DatafeedEndPointFunctions.PRODUCT_NAME_COLUMN_ENABLED),
    ROOM_CLASS_CONFIGURATION("RoomClassConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.CORE, DISCONTINUED_ROOM_TYPE_IN_ROOM_CLASS_CONFIG_DATAFEED_DISABLED),
    ROOM_CLASS_CONFIGURATION_WITH_DISCONTINUED_ROOM_TYPE_ENHANCED(ROOM_CLASS_CONFIGURATION, DISCONTINUED_ROOM_TYPE_IN_ROOM_CLASS_CONFIG_DATAFEED_ENABLED),
    FORECAST_GROUP("ForecastGroup_Wash_RemainingDemand", EndpointFrequencyType.DAILY, EndpointBucket.CORE),
    MCAT_MAPPING("MCATMapping", EndpointFrequencyType.DAILY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.HILTON_SPECIFIC),
    MARKET_SEGMENT_MAPPING("MarketSegmentMapping", EndpointFrequencyType.DAILY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.MARKET_SEGMENT_MAPPING),
    INVENTORY_HISTORY("InventoryHistory", EndpointFrequencyType.DAILY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.INVENTORY_HISTORY_ENABLED),
    INVENTORY_HISTORY_ENHANCED("InventoryHistory", EndpointFrequencyType.DAILY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.ENHANCED_INVENTORY_HISTORY_ENABLED),
    DECISION_CONFIGURATION("DecisionConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.HILTON_SPECIFIC_UNIQUE_USER_ID_NOT_APPLICABLE),
    DECISION_CONFIGURATION_ENHANCED("DecisionConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.HILTON_SPECIFIC_UNIQUE_USER_ID_APPLICABLE),
    BAR_OVERRIDE("BAR_Override", EndpointFrequencyType.WEEKLY, EndpointBucket.SYS_OVERRIDE, DatafeedEndPointFunctions.BAR_OVERRIDE),
    INDEPENDENT_OVERRIDE("Independent_Override", EndpointFrequencyType.WEEKLY, EndpointBucket.SYS_OVERRIDE),
    INVENTORY_LIMIT("InventoryLimit",EndpointFrequencyType.DAILY,EndpointBucket.SYS_CONFIG,DatafeedEndPointFunctions.INVENTORY_LIMIT_ENABLED),
    BAR_OVERRIDE_CLOSE_LV0(BAR_OVERRIDE, DatafeedEndPointFunctions.HILTON_NON_CONTINUOUS_PRICING),
    CP_BAR_OVERRIDE(BAR_OVERRIDE, DatafeedEndPointFunctions.NON_HILTON_CONTINUOUS_PRICING),
    CP_BAR_OVERRIDE_CLOSE_LV0("BAR_Override_CP", EndpointFrequencyType.WEEKLY, EndpointBucket.SYS_OVERRIDE, DatafeedEndPointFunctions.HILTON_CONTINUOUS_PRICING),
    PROFIT_FORECAST_GROUP("Profit_ForecastGroup", EndpointFrequencyType.DAILY, EndpointBucket.CORE, DatafeedEndPointFunctions.PROFIT_OPTIMIZATION_DATAFEED_ENABLED),
    PROFIT_ROOM_CLASS("Profit_RoomClass", EndpointFrequencyType.DAILY, EndpointBucket.CORE, DatafeedEndPointFunctions.PROFIT_OPTIMIZATION_DATAFEED_ENABLED),
    OVERBOOKING_OVERRIDE("Overbooking_Override", EndpointFrequencyType.WEEKLY, EndpointBucket.SYS_OVERRIDE),
    INPUT_OVERRIDE("Input_Override", EndpointFrequencyType.WEEKLY, EndpointBucket.SYS_OVERRIDE),
    ROLE_PERMISSION("Role", EndpointFrequencyType.CLIENT_MONTHLY, EndpointBucket.USER_ACTIVITY, DatafeedEndPointFunctions.ROLE_PERMISSION_ENABLED),
    ROLE_PERMISSION_ENHANCED("Role", EndpointFrequencyType.CLIENT_MONTHLY, EndpointBucket.USER_ACTIVITY, DatafeedEndPointFunctions.ROLE_PERMISSION_WITH_RANK_COLUMN_ENABLED),
    AUTHORIZATION_GROUP("AuthorizationGroup", EndpointFrequencyType.CLIENT_MONTHLY, EndpointBucket.SYS_CONFIG),
    USER_ACTIVITY_LOG("UserActivity", EndpointFrequencyType.CLIENT_WEEKLY, EndpointBucket.USER_ACTIVITY, UNIQUE_USER_ID_NOT_ENABLED),
    USER_REPORT("UserReport", EndpointFrequencyType.CLIENT_DAILY, EndpointBucket.USER_ACTIVITY, USER_REPORT_WITH_UNIQUE_USER_ID_NOT_ENABLED),
    USER_ACTIVITY_LOG_WITH_UNIQUE_USER_ID("UserActivity", EndpointFrequencyType.CLIENT_WEEKLY, EndpointBucket.USER_ACTIVITY, UNIQUE_USER_ID_ENABLED),
    USER_REPORT_WITH_UNIQUE_USER_ID("UserReport", EndpointFrequencyType.CLIENT_DAILY, EndpointBucket.USER_ACTIVITY, USER_REPORT_WITH_UNIQUE_USER_ID_ENABLED),
    USER_REPORT_WITH_PROPERTY_CODE_ENHANCED(USER_REPORT, DatafeedEndPointFunctions.USER_REPORT_WITH_PROPERTY_CODE),
    USER_REPORT_WITH_UNIQUE_USER_ID_AND_PROPERTY_CODE_ENHANCED(USER_REPORT_WITH_UNIQUE_USER_ID, DatafeedEndPointFunctions.USER_REPORT_WITH_USER_ID_AND_PROPERTY_CODE),
    VIRTUAL_PROPERTY_MAPPING_HILTON("VirtualPropertyMapping", EndpointFrequencyType.CLIENT_DAILY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.VIRTUAL_PROPERTY_MAPPING_FOR_HILTON),
    INVENTORY_SHARING("InventorySharing", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG),
    INDIVIDUAL_GROUP_WASH_BY_GROUP("GroupWashByGroup", EndpointFrequencyType.DAILY, EndpointBucket.SYS_OVERRIDE),
    SPECIAL_EVENT("SpecialEvent", EndpointFrequencyType.WEEKLY, EndpointBucket.SYS_CONFIG),
    INFORMATION_MANAGER_ALERT("InformationManagerAlerts", EndpointFrequencyType.DAILY, EndpointBucket.INFO_MANAGER),
    INFORMATION_MANAGER_NOTIFICATION_ENHANCED("InformationManagerNotifications", EndpointFrequencyType.DAILY, EndpointBucket.INFO_MANAGER),
    INFORMATION_MANAGER_EXCEPTION("InformationManagerExceptions", EndpointFrequencyType.DAILY, EndpointBucket.INFO_MANAGER),
    INFORMATION_MANAGER_NOTIFICATION_CONFIGURATION_ENHANCED("NotificationConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.INFO_MANAGER),
    COST_OF_WALK("CostOfWalk", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG),
    INFORMATION_MANAGER_SYSTEM_HEALTH("InformationManagerSystemHealth", EndpointFrequencyType.MONTHLY, EndpointBucket.INFO_MANAGER, DatafeedEndPointFunctions.SYSTEM_HEALTH),
    RATE_SHOPPING_COMPETITOR_CONFIGURATION("RateShoppingCompetitorConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_NAME_COLUMN_DISABLED),
    RATE_SHOPPING_COMPETITOR_CONFIGURATION_ENHANCED("RateShoppingCompetitorConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_NAME_COLUMN_ENABLED),
    RATE_SHOPPING_CHANNEL_CONFIGURATION("RateShoppingChannelConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG),
    RATE_SHOPPING_ACCOM_CLASS_MAPPING_CONFIGURATION("RateShoppingRoomClassMappingConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG),
    BENEFIT_MEASUREMENT("BenefitMeasurement", EndpointFrequencyType.MONTHLY, EndpointBucket.CORE, DatafeedEndPointFunctions.BENEFIT_MEASUREMENT_ENABLED),
    BENEFIT_MEASUREMENT_ENHANCED("BenefitMeasurement", EndpointFrequencyType.MONTHLY, EndpointBucket.CORE, DatafeedEndPointFunctions.BENEFIT_MEASUREMENT_WITH_PROFIT_COLUMNS_ENABLED),
    SCHEDULED_REPORTS("ScheduledReports", EndpointFrequencyType.WEEKLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.SCHEDULED_REPORTS_ENABLED),
    CHANNEL_FORECAST("ChannelForecast", EndpointFrequencyType.DAILY, EndpointBucket.CORE, DatafeedEndPointFunctions.CHANNEL_FORECAST_ENABLED),
    OVERBOOKING_CONFIGURATION("OverbookingConfig", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.SPECIAL_USE_ROOM_TYPES_DATAFEED_DISABLED),
    OVERBOOKING_CONFIGURATION_ENHANCED("OverbookingConfig", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.SPECIAL_USE_ROOM_TYPES_DATAFEED_ENABLED),
    RATE_SHOPPING_SCHEDULE("RateShoppingScheduleConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG),
    RATE_SHOPPING_COMPETITIVE_CHANNEL_CONFIGURATION("RateShoppingCompetitiveChannelReferenceConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_NAME_COLUMN_DISABLED),
    RATE_SHOPPING_COMPETITIVE_CHANNEL_CONFIGURATION_ENHANCED("RateShoppingCompetitiveChannelReferenceConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_NAME_COLUMN_ENABLED),
    RATE_SHOPPING_IGNORE_COMPETITOR_DATA_CONFIG("RateShoppingIgnoreCompetitorDataConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_NAME_COLUMN_DISABLED_IN_IGNORE_COMPETITOR),
    RATE_SHOPPING_IGNORE_COMPETITOR_DATA_CONFIG_ENHANCED("RateShoppingIgnoreCompetitorDataConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_NAME_COLUMN_ENABLED_IN_IGNORE_COMPETITOR),
    RATE_SHOPPING_IGNORE_COMPETITOR_DATA_CONFIG_WITH_CHANNEL_ENHANCED(RATE_SHOPPING_IGNORE_COMPETITOR_DATA_CONFIG, DatafeedEndPointFunctions.CHANNEL_COLUMN_ENABLED),
    RATE_SHOPPING_IGNORE_COMPETITOR_DATA_CONFIG_WITH_PRODUCT_NAME_COLUMN_AND_CHANNEL_ENHANCED(RATE_SHOPPING_IGNORE_COMPETITOR_DATA_CONFIG_ENHANCED, DatafeedEndPointFunctions.PRODUCT_NAME_AND_CHANNEL_COLUMN_ENABLED),
    OPTIX_RATE_SHOPPING_COMPETITOR_CONFIGURATION("RateShoppingCompetitorConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.OPTIX_WEB_RATE_ENABLED),
    PROPERTY_SPECIFIC_CONFIG("PropertySpecific", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PROPERTY_SPECIFIC_CONFIG),
    PROPERTY_SPECIFIC_CONFIG_ENHANCED("PropertySpecific", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PROPERTY_SPECIFIC_CONFIG_ENHANCED),
    PROPERTY_SPECIFIC_CONFIG_UPDATED("PropertySpecific", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PROPERTY_SPECIFIC_CONFIG_UPDATED),
    PROPERTY_SPECIFIC_CONFIG_CLOSE_LV0(PROPERTY_SPECIFIC_CONFIG, DatafeedEndPointFunctions.HILTON_SPECIFIC_CONFIG),
    PROPERTY_SPECIFIC_CONFIG_CLOSE_LV0_ENHANCED(PROPERTY_SPECIFIC_CONFIG_ENHANCED, DatafeedEndPointFunctions.HILTON_SPECIFIC_CONFIG_ENHANCED),
    PROPERTY_SPECIFIC_CONFIG_CLOSE_LV0_UPDATED(PROPERTY_SPECIFIC_CONFIG_ENHANCED, DatafeedEndPointFunctions.HILTON_SPECIFIC_CONFIG_UPDATED),
    PRICE_DROP_RESTRICTIONS("PriceDropRestrictions", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRICE_DROP_RESTRICTIONS),
    PROPERTY_SPECIFIC_ATTRIBUTE("PropertyAttribute", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PROPERTY_SPECIFIC_ATTRIBUTE),
    GROUP_PRICING_ANCILLARY_CONFIGURATION("GroupPricingAncillaryConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.GROUP_PRICING_CONFIGURATION_ENABLED),
    RATE_SHOPPING_COMPETITIVE_MARKET_POSITION_CONFIGURATION("RateShoppingCompetitiveMarketPositionConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_NAME_COLUMN_DISABLED),
    RATE_SHOPPING_COMPETITIVE_MARKET_POSITION_CONFIGURATION_ENHANCED("RateShoppingCompetitiveMarketPositionConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_NAME_COLUMN_ENABLED),
    PRICE_STRATEGY_ARRIVAL_CONFIGURATION("PriceStrategyAvailableforArrivalConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_DISABLED),
    PRICE_STRATEGY_LOS_CONFIGURATION("PriceStrategyConfigurationMinMaxLOSConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_DISABLED),
    PRICE_STRATEGY_RATE_CODE_CONFIGURATION("PriceStrategyRateCodeConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_DISABLED),
    PRICE_STRATEGY_USER_OVERRIDE_CONFIGURATION("PriceStrategyUserOverrideConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_DISABLED),
    PRICE_STRATEGY_CLOSE_RATE_PLAN_CONFIGURATION("PriceStrategyCloseRatePlanConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_DISABLED),
    PRICE_STRATEGY_RATE_PLAN_CONFIGURATION("PriceStrategyRatePlanConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_DISABLED),
    GROUP_PRICING_BASE_ROOM_TYPE_RATE_CONFIGURATION("GroupPricingBaseRoomTypeRateConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.GROUP_PRICING_CONFIGURATION_ENABLED),
    GROUP_PRICING_CONFIGURATION("GroupPricingGeneralConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.GROUP_PRICING_CONFIGURATION_ENABLED),
    GROUP_PRICING_CONFERENCE_BANQUET_CONFIGURATION("GroupPricingConferenceandBanquetConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.GROUP_PRICING_CONFIGURATION_ENABLED),
    GROUP_PRICING_PREFERRED_ROOM_TYPE_CONFIGURATION("GroupPricingPreferredRoomTypeConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.GROUP_PRICING_CONFIGURATION_ENABLED),
    GROUP_PRICING_ROOM_TYPE_OFFSET_CONFIGURATION("GroupPricingRoomTypeOffsetConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.GROUP_PRICING_CONFIGURATION_ENABLED),
    GROUP_PRICING_MIN_PROFIT_CONFIGURATION("GroupPricingMinProfitConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.GROUP_PRICING_MIN_PROFIT_CONFIGURATION_ENABLED),
    PROPERTY_BASIC_INFORMATION_ENHANCED_BOOKED_STATUS("Informational", EndpointFrequencyType.DAILY, EndpointBucket.CORE,DatafeedEndPointFunctions.ENHANCED_INFORMATION_LAST_LIMITED_DATA_BUILD_DISABLED),
    PROPERTY_BASIC_INFORMATION_ENHANCED_LAST_LIMITED_DATA_BUILD("Informational", EndpointFrequencyType.DAILY, EndpointBucket.CORE, DatafeedEndPointFunctions.ENHANCED_INFORMATION_LAST_LIMITED_DATA_BUILD),
    PROPERTY_BASIC_INFORMATION_ENHANCED_WITH_BOOKED_STATUS_AND_WINDOW_SETTINGS(PROPERTY_BASIC_INFORMATION_ENHANCED_BOOKED_STATUS, DatafeedEndPointFunctions.ENHANCED_INFORMATION_LAST_LIMITED_DATA_BUILD_DISABLED_AND_WINDOWS_SETTINGS_ENABLED),
    PROPERTY_BASIC_INFORMATION_ENHANCED_WITH_LAST_LIMITED_DATA_BUILD_AND_WINDOW_SETTINGS(PROPERTY_BASIC_INFORMATION_ENHANCED_LAST_LIMITED_DATA_BUILD, DatafeedEndPointFunctions.ENHANCED_INFORMATION_LAST_LIMITED_DATA_BUILD_ENABLED_AND_WINDOWS_SETTINGS_ENABLED),
    EXTENDED_STAY_PRODUCT_CONFIGURATION("ExtendedStayProductConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED),
    EXTENDED_STAY_COMPETITOR_CONFIGURATION("ExtendedStayCompetitorConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED),
    DAILY_BAR_INPUT_CONFIG("DailyBARInputConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG),
    CP_BASE_ROOM_TYPE_CONFIGURATION("CPBaseRoomTypeConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING),
    CP_BAR_ROUNDING_RULES_CONFIGURATION("CPBARRoundingRulesConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_INDEPENDENT_PRODUCT_COLUMN_DISABLED),
    CP_BAR_ROUNDING_RULES_CONFIGURATION_ENHANCED("CPBARRoundingRulesConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_INDEPENDENT_PRODUCT_COLUMN_ENABLED),
    CP_CEILING_FLOOR_TRANSIENT_CONFIGURATION_ENHANCED("CPCeilingFloorTransientConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_INDEPENDENT_PRODUCT_COLUMN_DISABLED),
    CP_CEILING_FLOOR_TRANSIENT_CONFIG_ENHANCED_INDEPENDENT_PRODUCT("CPCeilingFloorTransientConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_INDEPENDENT_PRODUCT_COLUMN_ENABLED),
    CP_OFFSET_CONFIGURATION_ENHANCED("CPOffsetsConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_AND_INDEPENDENT_PRODUCT_DISABLED_NON_HILTON),
    CP_OFFSET_CONFIGURATION_ENHANCED_WITH_PRODUCT("CPOffsetsConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_AND_INDEPENDENT_PRODUCT_ENABLED_NON_HILTON),
    CP_OFFSET_CONFIGURATION_ENHANCED_HILTON(CP_OFFSET_CONFIGURATION_ENHANCED, DatafeedEndPointFunctions.IS_CP_AND_CHILD_AGE_BASED_BUCKET_DISABLED_FOR_HILTON),
    CP_OFFSET_CONFIGURATION_ENHANCED_WITH_PRODUCT_HILTON(CP_OFFSET_CONFIGURATION_ENHANCED_WITH_PRODUCT, DatafeedEndPointFunctions.IS_CP_AND_CHILD_AGE_BASED_BUCKET_ENABLED_FOR_HILTON),
    CP_SUPPLEMENTS_CONFIGURATION("CPSupplementsConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_SUPPLEMENTS_AND_INDEPENDENT_PRODUCT_DISABLED),
    CP_SUPPLEMENTS_CONFIGURATION_ENHANCED("CPSupplementsConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_SUPPLEMENTS_AND_INDEPENDENT_PRODUCT_ENABLED),
    CP_SUPPLEMENTS_CONFIGURATION_ENHANCED_WITH_OFFSET_METHOD("CPSupplementsConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_SUPPLEMENTS_AND_INDEPENDENT_PRODUCT_DISABLED_AND_PERCENTAGE_COLUMN_CPSUPPLEMENT_ENABLED),
    CP_SUPPLEMENTS_CONFIGURATION_ENHANCED_WITH_OFFSET_METHOD_AND_PRODUCT_NAME("CPSupplementsConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.CONTINUOUS_PRICING_SUPPLEMENTS_AND_INDEPENDENT_PRODUCT_ENABLED_AND_PERCENTAGE_COLUMN_CPSUPPLEMENT_ENABLED),
    PROPERTY_LEVEL_DATA("HotelLevel", EndpointFrequencyType.DAILY, EndpointBucket.CORE),
    OPTIX_PROPERTY_LEVEL_DATA("HotelLevel", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    PRICING_DATA_AGILE_RATES("Pricing", EndpointFrequencyType.DAILY, EndpointBucket.CORE, DatafeedEndPointFunctions.AGILE_RATES_APPLICABLE),
    PRICING_DATA_AGILE_RATES_OVERRIDES("PricingOverrides", EndpointFrequencyType.WEEKLY, EndpointBucket.SYS_OVERRIDE, DatafeedEndPointFunctions.AGILE_RATES_APPLICABLE),
    LDB_PROJECTIONS("LDBProjections", EndpointFrequencyType.WEEKLY, EndpointBucket.CORE, DatafeedEndPointFunctions.LDB_PROJECTION_ENABLED),
    GROUP_EVALUATIONS("SavedGroupPricingEvaluations", EndpointFrequencyType.WEEKLY, EndpointBucket.CORE, DatafeedEndPointFunctions.ONLY_GROUP_EVALUATION_ENABLED),
    GROUP_EVALUATIONS_ENHANCED("SavedGroupPricingEvaluations", EndpointFrequencyType.WEEKLY, EndpointBucket.CORE, DatafeedEndPointFunctions.ENHANCED_GROUP_EVALUATION_ENABLED),
    PRODUCT_GROUP_PRODUCT_DEFINITION("ProductGroupProductDefinition", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_GROUP_PRODUCT_DEFINITION_ENABLED),
    GROUP_PRICING_SCROOM_TYPE_MAPPING("GroupPricingSCRoomTypeMapping", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.GROUP_PRICING_SCROOM_TYPE_MAPPING_ENABLED),
    GROUP_PRICING_SC_MARKET_SEGMENT_MAPPING("GroupPricingSCMarketSegmentMapping", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.GROUP_PRICING_SC_MARKET_SEGMENT_MAPPING_ENABLED),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_DEFINATION("ProductDefinition", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE_DISABLED),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_DEFINATION_ENHANCED("ProductDefinition", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE_ENABLED),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_DEFINITION_HILTON("ProductDefinition", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE_DISABLED_HILTON),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_DEFINITION_ENHANCED_HILTON("ProductDefinition", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE_ENABLED_HILTON),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_RATE_CODE_ASSIGNMENT("ProductRateCodeAssignment", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_ROOM_TYPE_ASSIGNMENT("ProductRoomTypeAssignment", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_PACKAGE_ELEMENTS_ASSIGNMENT("ProductPackageElementAssignment", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_PACKAGE_ELEMENTS("ProductPackageElements", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_PACKAGE_ELEMENTS_ENHANCED_DISABLED),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_PACKAGE_ELEMENTS_DOW("ProductPackageElements", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_PACKAGE_ELEMENTS_ENHANCED_ENABLED),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_DEFAULT_VALUE("ProductDefaultValue", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_SEASON_VALUE("ProductSeasonValue", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE),
    AGILE_PRODUCT_SEND_DECISION("ProductSendDecisionAsAdjustment", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_PRODUCT_SEND_DECISION_APPLICABLE),
    OPTIX_CP_OFFSET_CONFIGURATION_ENHANCED("CPOffsetConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.CONTINUOUS_PRICING_ENABLED),
    OPTIX_CP_SUPPLEMENTS_CONFIGURATION("CPSupplementsConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.CONTINUOUS_PRICING_SUPPLEMENTS_AND_INDEPENDENT_PRODUCT_DISABLED),
    OPTIX_CP_CEILING_FLOOR_TRANSIENT_CONFIGURATION_ENHANCED("CPCeilingFloorTransientConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.CONTINUOUS_PRICING_ENABLED),
    OPTIX_CP_BASE_ROOM_TYPE_CONFIGURATION("CPBaseRoomTypeConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.CONTINUOUS_PRICING),
    OPTIX_CP_BAR_ROUNDING_RULES_CONFIGURATION("CPBARRoundingRulesConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.CONTINUOUS_PRICING),
    OPTIX_PRICING_DATA_AGILE_RATES("Pricing", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.AGILE_RATES_APPLICABLE),
    OPTIX_STR_DAILY("STRDaily", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.STR_ENABLED),
    OPTIX_STR_MONTHLY("STRMonthly", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.STR_ENABLED),
    OPTIX_HOTEL_CAPACITY_DEMAND360("D360CompCapacity", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.DEMAND360_ENABLED),
    OPTIX_BOOKING_SUMMARY_DEMAND360("D360BookingSummary", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.DEMAND360_ENABLED),
    OPTIX_RESERVATION_NIGHT("ReservationNight", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_RESERVATION_NIGHT_CHANGE("ReservationNightChange", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.IS_FIRST_UPLOAD),
    OPTIX_BUDGET_DATA("BudgetData", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_USER_FORECAST_DATA("MyForecast", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_OCCUPANCY_FCST("OccupancyFCST", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_ROOM_TYPE("RoomType", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_PROPERTY_SPECIFIC_ATTRIBUTE("PropertyAttribute", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_PROPERTY_SPECIFIC_CONFIG("PropertySpecific", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.OPTIX_PROPERTY_SPECIFIC_CONFIG),
    OPTIX_PROPERTY_SPECIFIC_CONFIG_ENHANCED("PropertySpecific", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.OPTIX_PROPERTY_SPECIFIC_CONFIG_ENHANCED),
    OPTIX_SPECIAL_EVENT("SpecialEvent", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_MARKET_SEGMENT_MAPPING("MarketSegmentMapping", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_MARKET_SEGMENT_CONFIG("MarketSegmentConfig", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_ROOM_CLASS_CONFIGURATION("RoomClassConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_MARKET_ACCOM_ACTIVITY("MarketAccomActivity", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_USER_REPORT("UserReport", EndpointFrequencyType.CLIENT_DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_FORECAST_GROUP("ForecastGroup", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_GROUP_BLOCK("GroupBlocks", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_PACE_GROUP_BLOCK("PaceGroupBlock", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.IS_FIRST_UPLOAD),
    OPTIX_PACE_WEBRATE("PaceWebrate", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.PACE_WEB_RATE_ENABLED),
    OPTIX_WEBRATE("Webrate", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.OPTIX_WEB_RATE_ENABLED),
    OPTIX_GROUP_MASTER("GroupMaster", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_AUTHORIZATION_GROUP("AuthorizationGroup", EndpointFrequencyType.CLIENT_DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_USER_ACTIVITY_LOG("UserActivity", EndpointFrequencyType.CLIENT_DAILY, EndpointBucket.OPTIX_CORE),
    GROUP_FINAL_FORECAST_OVERRIDE("GroupFinalForecastOverride", EndpointFrequencyType.DAILY, EndpointBucket.SYS_OVERRIDE, DatafeedEndPointFunctions.GROUP_FINAL_FORECAST_OVERRIDE_ENABLED),
    OPTIX_PROPERTY_BASIC_INFORMATION_ENHANCED("Informational", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_ROLE_PERMISSION("Role", EndpointFrequencyType.CLIENT_DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_RATE_SHOPPING_IGNORE_COMPETITOR_DATA_CONFIG("RateShoppingIgnoreCompetitorDataConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.OPTIX_WEB_RATE_ENABLED),
    OPTIX_RATE_SHOPPING_CHANNEL_CONFIGURATION("RateShoppingChannelConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.OPTIX_WEB_RATE_ENABLED),
    OPTIX_RATE_SHOPPING_COMPETITIVE_CHANNEL_CONFIGURATION("RateShoppingCompetitiveChannelReferenceConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.OPTIX_WEB_RATE_ENABLED),
    OPTIX_RATE_SHOPPING_COMPETITIVE_MARKET_POSITION_CONFIGURATION("RateShoppingCompetitiveMarketPositionConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.OPTIX_WEB_RATE_ENABLED),
    OPTIX_RATE_SHOPPING_ACCOM_CLASS_MAPPING_CONFIGURATION("RateShoppingRoomClassMappingConfiguration", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.OPTIX_WEB_RATE_ENABLED),
    OPTIX_PRODUCT_RATE_SHOP_DEFINITION("ProductRateShopDefinition", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.OPTIX_WEB_RATE_ENABLED),
    OPTIX_BAR_OVERRIDE("BAR_Override", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.BAR_OVERRIDE),
    OPTIX_BAR_OVERRIDE_CLOSE_LV0(OPTIX_BAR_OVERRIDE, DatafeedEndPointFunctions.HILTON_NON_CONTINUOUS_PRICING),
    OPTIX_CP_BAR_OVERRIDE(OPTIX_BAR_OVERRIDE, DatafeedEndPointFunctions.NON_HILTON_CONTINUOUS_PRICING),
    OPTIX_PRICING_DATA_AGILE_RATES_OVERRIDES("PricingOverrides", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.AGILE_RATES_APPLICABLE),
    OPTIX_PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_SEASON_VALUE("ProductSeasonValue", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE),
    OPTIX_INPUT_OVERRIDE("Input_Override", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_OVERBOOKING_OVERRIDE("Overbooking_Override", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_OUT_OF_ORDER_OVERRIDE("OutofOrderOverride", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.OUT_OF_ORDER_OVERRIDE_ENABLED),
    OPTIX_PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_RATE_CODE_ASSIGNMENT("ProductRateCodeAssignment", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE),
    OPTIX_PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_ROOM_TYPE_ASSIGNMENT("ProductRoomTypeAssignment", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE),
    OPTIX_PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_DEFINATION_ENHANCED("ProductDefinition", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE_ENABLED),
    OPTIX_PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_PACKAGE_ELEMENTS("ProductPackageElements", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE),
    OPTIX_PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_PACKAGE_ELEMENTS_ASSIGNMENT("ProductPackageElementAssignment", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE),
    OPTIX_PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_DEFAULT_VALUE("ProductDefaultValue", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE),
    OPTIX_FORECAST_GROUP_REMAINING_DEMAND("ForecastGroup_Wash_RemainingDemand", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_GROUP_FINAL_FORECAST_OVERRIDE("GroupFinalForecastOverride", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.GROUP_FINAL_FORECAST_OVERRIDE_ENABLED),
    OPTIX_INDIVIDUAL_GROUP_WASH_BY_GROUP("GroupWashByGroup", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_COMPONENT_ROOM_MAPPING("ComponentRoom", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OUT_OF_ORDER_OVERRIDE("OutofOrderOverride", EndpointFrequencyType.WEEKLY, EndpointBucket.SYS_OVERRIDE, DatafeedEndPointFunctions.OUT_OF_ORDER_OVERRIDE_ENABLED),
    OPTIX_PACE_GROUP_MASTER("PaceGroupMaster", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.IS_FIRST_UPLOAD),
    OPTIX_PROPERTY_ON_BOOKS_PACE_ALERT("PropertyOnbooksPaceAlert", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    TAX_INCLUSIVE_CONFIGURATION("TaxInclusiveConfiguration", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.TAX_INCLUSIVE_CONFIGURATION_ENABLED),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_HIERARCHY("ProductHierarchy", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_PRODUCT_OPTIMIZATION_ENHANCED_DISABLED),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_HIERARCHY_ENHANCED("ProductHierarchy", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_PRODUCT_OPTIMIZATION_ENHANCED_ENABLED),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_GROUPS("ProductGroups", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_PRODUCT_OPTIMIZATION_ENABLED),
    PRICING_DATA_AGILE_CONFIGURATION_PRODUCT_OPTIMIZED("OptimizationLevel", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.AGILE_PRODUCT_OPTIMIZATION_ENABLED),
    USER_FORECAST_DATA("MyForecast", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG),
    BUDGET_DATA("Budget", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG),
    OPTIX_PACE_TOTAL_ACTIVITY("PaceTotalActivity", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.IS_FIRST_UPLOAD),
    OPTIX_LRV_PACE("LRVPace", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.IS_FIRST_UPLOAD),
    FORECAST_ARRIVALS_DEPARTURES_DATAFEED("ForecastArrivalsDepartures", EndpointFrequencyType.DAILY, EndpointBucket.CORE),
    ROOM_CLASS_PRICE_RANK("PriceRank", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG),
    ROOM_CLASS_MIN_PRICE_DIFFERENTIALS("MinimumPriceDifferentials", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG),
    PRODUCT_FREE_NIGHT_DEFINITION("ProductFreeNightDefinition", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_FREE_NIGHT_DEFINITION_ENABLED),
    PRODUCT_RATE_SHOP_DEFINITION("ProductRateShopDefinition", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_RATE_SHOP_DEFINITION_ENABLED),
    PRODUCT_RATE_SHOP_DEFINITION_ENHANCED("ProductRateShopDefinition", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_RATE_SHOP_DEFINITION_ENHANCED_ENABLED),
    PRODUCT_CLASSIFICATION("ProductClassification", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_CLASSIFICATION_ENABLED),
    PRODUCT_CHILD_PRICING_TYPE("ProductChildPricingType", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.PRODUCT_CHILD_PRICING_TYPE_ENABLED),
    RATE_SHOPPING_OCCUPANCY_BASED_CMPC("RateShoppingOccupancyBasedCMPC", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.RATE_SHOPPING_OCCUPANCY_BASED_CMPC_DATAFEED_ENABLED),
    RATE_SHOPPING_IGNORE_CHANNEL_CONFIG("RateShoppingIgnoreChannelConfig", EndpointFrequencyType.MONTHLY, EndpointBucket.SYS_CONFIG, DatafeedEndPointFunctions.RATE_SHOPPING_IGNORE_CHANNEL_CONFIG_DATAFEED_ENABLED),
    MEETING_PACKAGE_PRICING("MeetingPackagePricing", EndpointFrequencyType.DAILY, EndpointBucket.CORE, MEETING_PACKAGE_PRICING_ENABLED),
    MEETING_PACKAGE_BASE_PRODUCT_PRICING_OVERRIDES("MeetingPackageBaseProductPricingOverrides", EndpointFrequencyType.WEEKLY, EndpointBucket.SYS_OVERRIDE, MEETING_PACKAGE_PRICING_ENABLED),
    OPTIX_PRICING_SENSITIVITY_COEFFICIENT("PricingSensitivityCoefficient", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE),
    OPTIX_PROFIT_ROOM_CLASS("Profit_RoomClass", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.OPTIX_ROOM_CLASS_PROFIT_AND_FORECAST_GROUP_PROFIT__DATAFEED_ENABLED),
    OPTIX_PROFIT_FORECAST_GROUP("Profit_ForecastGroup", EndpointFrequencyType.DAILY, EndpointBucket.OPTIX_CORE, DatafeedEndPointFunctions.OPTIX_ROOM_CLASS_PROFIT_AND_FORECAST_GROUP_PROFIT__DATAFEED_ENABLED);

    private final String fileName;
    private final EndpointFrequencyType frequencyType;
    private final EndpointBucket bucket;
    private final Function<DatafeedEndPointCriteria, Boolean> condition;

    Endpoint(String fileName, EndpointFrequencyType frequencyType, EndpointBucket bucket, Function<DatafeedEndPointCriteria, Boolean> condition) {
        this.fileName = fileName;
        this.frequencyType = frequencyType;
        this.bucket = bucket;
        this.condition = condition;
    }

    Endpoint(String fileName, EndpointFrequencyType frequencyType, EndpointBucket bucket) {
        this.fileName = fileName;
        this.frequencyType = frequencyType;
        this.bucket = bucket;
        this.condition = DatafeedEndPointFunctions.GENERAL_AVAILABILITY;
    }

    Endpoint(Endpoint parent, Function<DatafeedEndPointCriteria, Boolean> condition) {
        this(parent.getFileName(), parent.getFrequencyType(), parent.getBucket(), condition);
    }

    public String getFileName() {
        return fileName;
    }

    public EndpointFrequencyType getFrequencyType() {
        return frequencyType;
    }

    public EndpointBucket getBucket() {
        return bucket;
    }

    public Function<DatafeedEndPointCriteria, Boolean> getCondition() {
        return condition;
    }

    public static List<Endpoint> getEndPointsForBucket(EndpointBucket endpointBucket) {
        return Arrays.asList(Endpoint.values()).stream().filter(endpoint -> endpointBucket == endpoint.getBucket()).collect(Collectors.toList());
    }
}
