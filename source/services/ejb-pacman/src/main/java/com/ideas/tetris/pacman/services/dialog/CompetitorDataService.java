package com.ideas.tetris.pacman.services.dialog;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.webrate.dto.CompetitorRateInfo;
import com.ideas.tetris.pacman.services.webrate.service.CompetitorRateInfoService;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

@Component
@Transactional
public class CompetitorDataService {
    @Autowired
    CompetitorRateInfoService competitorRateInfoService;
    @Autowired
    AccommodationService accommodationService;

    public CompetitorRateInfo getHighestCompetitorRate(LocalDate occupancyDate) {
        List<CompetitorRateInfo> rates = competitorRateInfoService.getCompetitorRates(LocalDateUtils.toDate(occupancyDate), getMasterClass());
        Optional<CompetitorRateInfo> highestRate = rates.stream().filter(rate -> rate.getRate().compareTo(BigDecimal.ZERO) > 0).max(Comparator.comparing(CompetitorRateInfo::getRate));
        return highestRate.isPresent() ? highestRate.get() : null;
    }

    public CompetitorRateInfo getLowestCompetitorRate(LocalDate occupancyDate) {
        List<CompetitorRateInfo> rates = competitorRateInfoService.getCompetitorRates(LocalDateUtils.toDate(occupancyDate), getMasterClass());
        Optional<CompetitorRateInfo> lowestRate = rates.stream().filter(rate -> rate.getRate().compareTo(BigDecimal.ZERO) > 0).min(Comparator.comparing(CompetitorRateInfo::getRate));
        return lowestRate.isPresent() ? lowestRate.get() : null;
    }

    public BigDecimal getAverageCompetitorRate(LocalDate occupancyDate) {
        List<CompetitorRateInfo> rates = competitorRateInfoService.getCompetitorRates(LocalDateUtils.toDate(occupancyDate), getMasterClass());
        OptionalDouble value = rates.stream().map(CompetitorRateInfo::getRate).map(Objects::requireNonNull).filter(rate -> rate.compareTo(BigDecimal.ZERO) > 0).mapToDouble(BigDecimal::doubleValue).average();
        return value.isPresent() ? BigDecimal.valueOf(value.getAsDouble()) : null;
    }

    public CompetitorRateInfo getRateForCompetitor(LocalDate occupancyDate, String competitorName) {
        List<CompetitorRateInfo> rates = competitorRateInfoService.getCompetitorRates(LocalDateUtils.toDate(occupancyDate), getMasterClass());
        Optional<CompetitorRateInfo> matchingRate = rates.stream().filter(rate -> rate.getCompetitorName() != null).filter(rate -> rate.getRate().compareTo(BigDecimal.ZERO) > 0).filter(rate -> rate.getCompetitorName().toLowerCase().trim().
                contains(competitorName.toLowerCase().trim())).findFirst();
        return matchingRate.isPresent() ? matchingRate.get() : null;
    }

    private AccomClass getMasterClass() {
        return accommodationService.findMasterClass(PacmanWorkContextHelper.getPropertyId());
    }
}
