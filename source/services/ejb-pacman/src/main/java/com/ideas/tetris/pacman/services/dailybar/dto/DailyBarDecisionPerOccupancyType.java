package com.ideas.tetris.pacman.services.dailybar.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideas.tetris.platform.common.rest.unmarshaller.JaxbDateSerializer;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by idnpak on 11/27/2014.
 */
@XmlType(propOrder = {"occupancyDate", "ratePlan", "occupancyType", "rate", "roomType"})
public class DailyBarDecisionPerOccupancyType {

    private Date occupancyDate;
    private String ratePlan;
    private String OccupancyType;
    private BigDecimal rate;
    private String roomType;

    @XmlAttribute(name = "ArrivalDate")
    @XmlJavaTypeAdapter(JaxbDateSerializer.class)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    @XmlAttribute(name = "DailyBARRatePlan")
    public String getRatePlan() {
        return ratePlan;
    }

    public void setRatePlan(String ratePlan) {
        this.ratePlan = ratePlan;
    }

    @XmlAttribute(name = "OccType")
    public String getOccupancyType() {
        return OccupancyType;
    }

    public void setOccupancyType(String occupancyType) {
        OccupancyType = occupancyType;
    }

    @XmlAttribute(name = "Rate")
    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    @XmlAttribute(name = "RoomType", required = false)
    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }
}
