package com.ideas.tetris.pacman.services.property.configuration.service.propertyattribute;

import com.ideas.tetris.pacman.common.constants.Constants;

public enum PropertyConfigurationClientAttributeMap {
    //OLD Hilton Custom Attributes
    GLOBAL_AREA("Global Area", "Global Area", Constants.CHAR, 0, 16, null),
    BRAND_CODE("Brand Code", "Brand Code", Constants.CHAR, 0, 16, null),
    MANAGEMENT_TYPE("Management Type", "Management Type", Constants.CHAR, 0, 16, null),
    MANAGEMENT_COMPANY("MgtCompany", "MgtCompany", Constants.CHAR, 0, 16, null),
    LOCATION_TYPE("Location Type", "Location Type", Constants.CHAR, 0, 16, null),
    COUNTRY("Country", "Country", Constants.CHAR, 0, 16, null),
    NUMBER_OF_ROOMS("Number of Rooms", "Number of Rooms", "Numeric", 1, 0, null),
    RMC<PERSON>("RMCC", "RMCC", Constants.CHAR, 0, 16, null),
    MARKET("Market", "Market", Constants.CHAR, 0, 16, null),
    //NEW Hilton Custom Attributes
    OPENING_DATE("Opening Date", "Opening Date", Constants.CHAR, 0, 16, null),
    OPEN_STATUS("Open Status", "Open Status", Constants.CHAR, 0, 16, null),
    TIME_ZONE("Time Zone", "Time Zone", Constants.CHAR, 0, 16, null),
    TIME_ZONE_NAME("Time Zone Name", "Time Zone Name", Constants.CHAR, 0, 16, null),
    ZIP_POSTAL_CODE("Zip/ Postal Cd", "Zip/ Postal Cd", Constants.CHAR, 0, 16, null),
    PROPERTY_OWNER("Property Owner", "Property Owner", Constants.CHAR, 0, 16, null),
    GEOGRAPHIC_REGION("Geographic Rgn", "Geographic Region", Constants.CHAR, 0, 16, null),
    PROPERTY_NAME("Property Name", "Property Name", Constants.CHAR, 0, 16, null),
    RDRM("RDRM", "RDRM", Constants.CHAR, 0, 16, null),
    RMCC_DIRECTOR("RMCC Director", "RMCC Director", Constants.CHAR, 0, 16, null),
    RMCC_SR_MANAGER("RMCC Sr Manager", "RMCC Sr Manager", Constants.CHAR, 0, 16, null),
    RMCC_MANAGER("RMCC Manager", "RMCC Manager", Constants.CHAR, 0, 16, null),
    RMCC_ASSISTANT_DIRECTOR("RMCC Assist Dir", "RMCC Assistant Director", Constants.CHAR, 0, 16, null),
    REGIONAL_VP_RM("Regional VP RM", "Regional VP RM", Constants.CHAR, 0, 16, null),
    TIER("Tier", "Tier", Constants.CHAR, 0, 16, null);

    private String attributeName;
    private String attributeDescription;
    private String displayType;
    private int userEntryLength;
    private int displayLength;
    private String defaultValue;

    private PropertyConfigurationClientAttributeMap(String attributeName, String attributeDescription,
                                                    String displayType, int userEntryLength, int displayLength, String defaultValue) {
        this.attributeName = attributeName;
        this.attributeDescription = attributeDescription;
        this.displayType = displayType;
        this.userEntryLength = userEntryLength;
        this.displayLength = displayLength;
        this.defaultValue = defaultValue;
    }

    public String getAttributeName() {
        return attributeName;
    }

    public String getAttributeDescription() {
        return attributeDescription;
    }

    public String getDisplayType() {
        return displayType;
    }

    public int getUserEntryLength() {
        return userEntryLength;
    }

    public int getDisplayLength() {
        return displayLength;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public PropertyConfigurationClientAttributeMap valueForAttributeName(String attributeName) {
        for (PropertyConfigurationClientAttributeMap clientAttributeMap : values()) {
            if (clientAttributeMap.getAttributeName().equals(attributeName)) {
                return clientAttributeMap;
            }
        }

        return null;
    }

}
