package com.ideas.tetris.pacman.services.walkme;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.client.service.AddClientDto;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.remoteAgent.OpmsInstallationType;
import com.ideas.tetris.pacman.services.remoteAgent.OrsInstallationType;
import com.ideas.tetris.pacman.services.walkme.dto.WalkMePropertyDTO;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.externalsystem.ExternalSubSystem;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import org.apache.commons.lang.StringUtils;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class WalkMeService {

    @Autowired
	private ClientService clientService;

    @Autowired
    WalkMeCookDataService walkMeCookDataService;

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;

    @Autowired
	private
    JobServiceLocal jobServiceLocal;

    @Autowired
	private ClientPropertyCacheService clientPropertyCacheService;

    @Autowired
	private WalkMeCookDataForMarketSegmentConfigurationService marketSegmentConfigurationService;

    @Autowired
	private WalkMeCookDataForRatePlanConfigurationService ratePlanConfigurationService;

    @Autowired
	private WalkMeCookDataForRateShoppingConfigurationService rateShoppingConfiguationService;

    @Autowired
	private WalkMeCookDataForCCFGService ccfgService;

    public static final String CLIENT_NAME = "Walk Me";
    public static final String CLIENT_CODE = "WalkMe";
    private static final String PROPERTY_TIMEZONE = "America/Chicago";
    private static final String YIELD_CURRENCY = "USD";
    public final List<WalkMePropertyDTO> walkMePropertyDTOList = new ArrayList<>();

    @PostConstruct
    protected void initialiseWalkMePropertiesList() {
        WalkMePropertyDTO propertyForRcGscMs = new WalkMePropertyDTO();
        propertyForRcGscMs.setPropertyCode(WalkMePropertiesRequirementsEnum.PROPERTY_FOR_RC_GSC_MS.getProperty());
        propertyForRcGscMs.setRequirements(WalkMePropertiesRequirementsEnum.PROPERTY_FOR_RC_GSC_MS);
        propertyForRcGscMs.setStatus(isPropertyBuilt(propertyForRcGscMs.getPropertyCode()) ? WalkMePropertyStatusEnum.BUILT :
                WalkMePropertyStatusEnum.READY_TO_BUILD);
        walkMePropertyDTOList.add(propertyForRcGscMs);
        WalkMePropertyDTO propertyForPcGp = new WalkMePropertyDTO();
        propertyForPcGp.setPropertyCode(WalkMePropertiesRequirementsEnum.PROPERTY_FOR_PC_GPC.getProperty());
        propertyForPcGp.setRequirements(WalkMePropertiesRequirementsEnum.PROPERTY_FOR_PC_GPC);
        propertyForPcGp.setStatus(isPropertyBuilt(propertyForPcGp.getPropertyCode()) ? WalkMePropertyStatusEnum.BUILT :
                WalkMePropertyStatusEnum.READY_TO_BUILD);
        walkMePropertyDTOList.add(propertyForPcGp);
        WalkMePropertyDTO propertyForRpGpCCFG = new WalkMePropertyDTO();
        propertyForRpGpCCFG.setPropertyCode(WalkMePropertiesRequirementsEnum.PROPERTY_FOR_RPC_CCFG.getProperty());
        propertyForRpGpCCFG.setRequirements(WalkMePropertiesRequirementsEnum.PROPERTY_FOR_RPC_CCFG);
        propertyForRpGpCCFG.setStatus(isPropertyBuilt(propertyForRpGpCCFG.getPropertyCode()) ? WalkMePropertyStatusEnum.BUILT :
                WalkMePropertyStatusEnum.READY_TO_BUILD);
        walkMePropertyDTOList.add(propertyForRpGpCCFG);
        WalkMePropertyDTO propertyForRpcRs = new WalkMePropertyDTO();
        propertyForRpcRs.setPropertyCode(WalkMePropertiesRequirementsEnum.PROPERTY_FOR_RPC_RS_GPC.getProperty());
        propertyForRpcRs.setRequirements(WalkMePropertiesRequirementsEnum.PROPERTY_FOR_RPC_RS_GPC);
        propertyForRpcRs.setStatus(isPropertyBuilt(propertyForRpcRs.getPropertyCode()) ? WalkMePropertyStatusEnum.BUILT :
                WalkMePropertyStatusEnum.READY_TO_BUILD);
        walkMePropertyDTOList.add(propertyForRpcRs);
    }

    public List<WalkMePropertyDTO> getAllWalkMeProperties() {
        return walkMePropertyDTOList;
    }

    public void addClient() {
        AddClientDto walkMeClient = new AddClientDto();
        walkMeClient.setName(CLIENT_NAME);
        walkMeClient.setCode(CLIENT_CODE);
        walkMeClient.setExternalSystem(ReservationSystem.OPERA.getConfigParameterValue());
        walkMeClient.setExternalSubSystem(ExternalSubSystem.None.getConfigParameterValue());
        walkMeClient.setOpmsInstallationType(OpmsInstallationType.DEDICATED.getConfigParamValue());
        walkMeClient.setOrsInstallationType(OrsInstallationType.NONE.getConfigParamValue());
        clientService.addClient(walkMeClient);
    }

    public boolean isWalkMeClientPresent() {
        Client walkMeClient = clientService.getClientByCode(CLIENT_CODE);
        return null != walkMeClient;
    }

    protected boolean isPropertyBuilt(String propertyCode) {
        Property property = getProperty(propertyCode);
        return null != property;
    }

    private Property getProperty(String propertyCode) {
        return globalCrudService.findByNamedQuerySingleResult(Property.BY_CLIENT_CODE_PROPERTY_CODE, QueryParameter.with(Constants.CLIENT_CODE, CLIENT_CODE)
                .and(Constants.PROPERTY_CODE, propertyCode).parameters());
    }

    public void refreshPropertyCache() {
        clientPropertyCacheService.reloadProperty(PacmanWorkContextHelper.getPropertyId());
    }

    public void buildDataForPropertyForRcGscMs() {
        walkMeCookDataService.addRequirementForUnMappedGroupStatusCode();
        walkMeCookDataService.addRequirementsForRoomClassConfiguration();
        marketSegmentConfigurationService.createDataForMarketSegmentConfiguration();
    }

    public void buildDataForPropertyForPcGpc() {
        walkMeCookDataService.setContinuousPricingToTrue();
        walkMeCookDataService.addRequirementsForGroupPricing();
        walkMeCookDataService.enableSupplementsForPricingConfiguration();
    }

    public void buildDataForPropertyForRpcCCFG() {
        walkMeCookDataService.setBarDecisionToBarByLOS();
        walkMeCookDataService.addRequirementsForMappedRoomClassRoomType();
        ratePlanConfigurationService.addRequirementsForRatePlanConfigurationBARByLOS();
        walkMeCookDataService.addRequirementForUnMappedGroupStatusCode();
        marketSegmentConfigurationService.enableAnalyticalMarketSegment();
        marketSegmentConfigurationService.addMarketSegments();
        marketSegmentConfigurationService.addGroupMarketSegment();
        marketSegmentConfigurationService.addHistoryGroupBlocks();
        ccfgService.addMarketSegmentAttribution();
        ccfgService.addMktSegDetailsProposed();
        ccfgService.addMarketSegmentMaster();
        ccfgService.addRequirementsForPaceDataAndHistoricalData();
        ccfgService.setAMSToTrue();
    }

    public void buildDataForPropertyForRpcRsGpc() {
        walkMeCookDataService.addRequirementsForMappedRoomClassRoomType();
        rateShoppingConfiguationService.addRequirementsForRateShoppingConfiguration();
        ratePlanConfigurationService.addRequirementsForRatePlanConfigurationBARByDay();
        walkMeCookDataService.addRequirementsForGroupPricingRcRp();
    }

    public void updateWalkMePropertyStatus(String propertyCode, WalkMePropertyStatusEnum status) {
        WalkMePropertyDTO walkMePropertyDTO = getWalkMePropertyDTO(propertyCode);
        walkMePropertyDTO.setStatus(status);
    }

    public WalkMePropertyDTO getWalkMePropertyDTO(String propertyCode) {
        synchronized (this) {
            return walkMePropertyDTOList.stream().filter(dto -> StringUtils.equalsIgnoreCase(propertyCode, dto.getPropertyCode()))
                    .findFirst().get();
        }
    }

    public void buildRebuildProperty(WalkMePropertyDTO walkMePropertyDTO) {
        triggerWalkMeJob(walkMePropertyDTO, Constants.FALSE, Constants.FALSE);
    }

    public void deleteProperty(WalkMePropertyDTO walkMePropertyDTO) {
        triggerWalkMeJob(walkMePropertyDTO, Constants.FALSE, Constants.TRUE);
    }

    private void triggerWalkMeJob(WalkMePropertyDTO walkMePropertyDTO, String skipDelete, String skipAddAndCookData) {
        WalkMePropertyDTO walkMeProperty = getWalkMePropertyDTO(walkMePropertyDTO.getPropertyCode());
        walkMeProperty.setStatus(Boolean.valueOf(skipAddAndCookData) ? WalkMePropertyStatusEnum.DELETE_IN_PROGRESS :
                WalkMePropertyStatusEnum.BUILD_IN_PROGRESS);
        HashMap<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.CLIENT_CODE, CLIENT_CODE);
        Property property = getProperty(walkMePropertyDTO.getPropertyCode());
        if (null != property) {
            parameters.put(JobParameterKey.PROPERTY_ID, property.getId());
        }
        parameters.put(JobParameterKey.PROPERTY_CODE, walkMePropertyDTO.getPropertyCode());
        parameters.put(JobParameterKey.PROPERTY_NAME, walkMePropertyDTO.getPropertyCode());
        parameters.put(JobParameterKey.PROPERTY_TIMEZONE, PROPERTY_TIMEZONE);
        parameters.put(JobParameterKey.YIELD_CURRENCY, YIELD_CURRENCY);
        parameters.put(JobParameterKey.SKIP_STEP_GROUP, skipDelete);
        parameters.put(JobParameterKey.SKIP_STEPS, skipAddAndCookData);
        parameters.put(JobParameterKey.SFDC_CASE_NUMBER, "walkme");
        parameters.put(JobParameterKey.USER_ID, PacmanWorkContextHelper.getUserId());
        parameters.put(JobParameterKey.DATE, LocalDateTime.now());
        jobServiceLocal.startJob(JobName.WalkMe, parameters);
    }
}
