package com.ideas.tetris.pacman.services.dialog;

import com.ideas.tetris.pacman.services.dashboard.DashboardMetric2;
import com.ideas.tetris.pacman.services.dashboard.DashboardService;
import com.ideas.tetris.pacman.services.dashboard.MetricRequest;
import com.ideas.tetris.pacman.services.dashboard.type.ActualEstimatedType;
import com.ideas.tetris.pacman.services.dashboard.type.GroupByType;
import com.ideas.tetris.pacman.services.dashboard.type.MetricType2;
import com.ideas.tetris.pacman.services.dashboard.type.YearType;
import com.ideas.tetris.platform.common.time.LocalDateUtils;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.function.ToIntFunction;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class CapacityDataService {

    @Autowired
	private DashboardService dashboardService;

    public BigDecimal getCapacity(LocalDate startDate, LocalDate endDate) {
        DashboardMetric2 dashboardMetric2 = dashboardService.getMetrics2(Arrays.asList(getCapacityMetric()), LocalDateUtils.toJodaLocalDate(startDate), LocalDateUtils.toJodaLocalDate(endDate));
        return new BigDecimal(extractMetricResponse(dashboardMetric2));
    }

    public BigDecimal getOOO(LocalDate startDate, LocalDate endDate) {
        DashboardMetric2 dashboardMetric2 = dashboardService.getMetrics2(Arrays.asList(getOOOMetric()), LocalDateUtils.toJodaLocalDate(startDate), LocalDateUtils.toJodaLocalDate(endDate));
        return new BigDecimal(extractMetricResponse(dashboardMetric2));
    }

    private int extractMetricResponse(DashboardMetric2 dashboardMetric2) {
        List<BigDecimal> dateRangeOutOfOrder = (List<BigDecimal>) dashboardMetric2.getMetricResponses().get(0).getMetricValuesByGroupByType().get(GroupByType.PROPERTY.name());
        ToIntFunction<BigDecimal> bigDecimalToIntFunction = (BigDecimal t) -> t.intValue() == Integer.MIN_VALUE ? 0 : t.intValue();
        return dateRangeOutOfOrder.stream().mapToInt(bigDecimalToIntFunction).sum();
    }

    public MetricRequest getCapacityMetric() {
        return getMetricRequestFor(MetricType2.TOTAL_CAPACITY, ActualEstimatedType.ACTUAL, YearType.CURRENT_YEAR, false);
    }

    public MetricRequest getOOOMetric() {
        return getMetricRequestFor(MetricType2.OUT_OF_ORDER, ActualEstimatedType.ACTUAL, YearType.CURRENT_YEAR, false);
    }

    public MetricRequest getMetricRequestFor(MetricType2 metricType2, ActualEstimatedType actualEstimatedType, YearType yearType, Boolean dowAdjustedForLastYear) {
        MetricRequest metricRequest = new MetricRequest();
        metricRequest.setMetricType(metricType2);
        metricRequest.setActualEstimatedType(actualEstimatedType);
        metricRequest.setGroupByType(GroupByType.PROPERTY);
        metricRequest.setYearType(yearType);
        metricRequest.setDowAdjustForLastYear(dowAdjustedForLastYear);
        return metricRequest;
    }
}
