package com.ideas.tetris.pacman.services.property.configuration;

import java.util.HashMap;
import java.util.Map;

public class RoomClassCapacityMap {

    protected static final ThreadLocal<Map<String, Integer>> roomClassCapacityMap = new ThreadLocal<Map<String, Integer>>();

    public static Integer getRoomClassCapacity(String propertyCode, String roomClassName) {
        if (propertyCode == null || roomClassName == null) {
            return null;
        }
        return getMap().get(buildKey(propertyCode, roomClassName));
    }

    public static void incrementRoomClassCapacity(String propertyCode, String roomClassName, int increment) {
        if (propertyCode == null || roomClassName == null) {
            return;
        }
        String key = buildKey(propertyCode, roomClassName);
        Map<String, Integer> map = getMap();
        Integer capacity = map.get(key);
        if (capacity == null) {
            capacity = 0;
        }
        capacity = capacity + increment;
        map.put(key, capacity);
    }

    private static Map<String, Integer> getMap() {
        if (roomClassCapacityMap.get() == null) {
            roomClassCapacityMap.set(new HashMap<String, Integer>());
        }
        return roomClassCapacityMap.get();
    }

    private static String buildKey(String propertyCode, String roomClassName) {
        return propertyCode + "_" + roomClassName;
    }
}
