package com.ideas.tetris.pacman.services.forecast.repository;

import com.ideas.tetris.pacman.Repository;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.demandoverride.entity.ArrivalDemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.entity.OccupancyDemandOverride;
import com.ideas.tetris.pacman.services.forecast.ExpectedForecastConstant;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailDTO;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailForPastSevenDaysDTO;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailGraphForPastSevenDaysDTO;
import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailOnBooksDTO;
import com.ideas.tetris.pacman.services.forecast.mapper.ExpectedForecastDetailForLastSevenDaysRowMapper;
import com.ideas.tetris.pacman.services.forecast.mapper.ExpectedForecastDetailGraphForLastSevenDaysRowMapper;
import com.ideas.tetris.pacman.services.forecast.mapper.ExpectedForecastDetailRowMapper;
import com.ideas.tetris.pacman.services.forecast.mapper.ExpectedForecastDetailsOnBooksRowMapper;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import javax.inject.Inject;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.services.forecast.ExpectedForecastConstant.DOW_END_DATE;
import static com.ideas.tetris.pacman.services.forecast.ExpectedForecastConstant.END_DATE;
import static com.ideas.tetris.pacman.services.forecast.ExpectedForecastConstant.OCCUPANCY_DATE;
import static com.ideas.tetris.pacman.services.forecast.ExpectedForecastConstant.PACE;
import static com.ideas.tetris.pacman.services.forecast.ExpectedForecastConstant.PROPERTY_ID;
import static com.ideas.tetris.pacman.services.forecast.ExpectedForecastConstant.START_DATE;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Repository
@Component
public class ExpectedForecastRepository {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    public List<ExpectedForecastDetailDTO> fetchExpectedForecastDetails() {
        return tenantCrudService.findByNativeQuery(
                getQuery(), QueryParameter.with(ExpectedForecastConstant.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters(), new ExpectedForecastDetailRowMapper());

    }

    public List<ExpectedForecastDetailOnBooksDTO> fetchExpectedForecastOnBooksDetails(Date occupancyStartDate, Date occupancyEndDate, Date businessEndDate) {
        String queryStr = "exec usp_g3_investigator_expected_forecast_onbooks :occupancyStartDate, :occupancyEndDate, :businessEndDate, :propertyId";
        return tenantCrudService.findByNativeQuery(
                queryStr, QueryParameter.with(ExpectedForecastConstant.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(ExpectedForecastConstant.OCCUPANCY_START_DATE, occupancyStartDate).and(ExpectedForecastConstant.OCCUPANCY_END_DATE, occupancyEndDate)
                        .and(ExpectedForecastConstant.BUSINESS_END_DATE, businessEndDate)
                        .parameters(), new ExpectedForecastDetailsOnBooksRowMapper());

    }

    public List<Object> getAvgDOWOfOnBooks(LocalDate occupancyDOWDate, String dayOfWeek, LocalDate businessDate) {
        String queryStr = "exec usp_g3_investigator_expected_forecast_onbooks_dow :occupancyDate, :dayOfWeek, :businessEndDate";
        return tenantCrudService.findByNativeQuery(
                queryStr, QueryParameter.with(ExpectedForecastConstant.OCCUPANCY_DATE, occupancyDOWDate)
                        .and(ExpectedForecastConstant.DAY_OF_WEEK, dayOfWeek)
                        .and(ExpectedForecastConstant.BUSINESS_END_DATE, businessDate).parameters());
    }

    private String getQuery() {
        return "SELECT OCCUPANCY_DT, " +
                "ACTUAL_ONBOOKS, " +
                "CONVERT(INT, ROUND(EXPECTED_ONBOOKS,0)) AS EXPECTED_ONBOOKS , " +
                "CONVERT(INT, ROUND(EXPECTED_ONBOOKS_LOWER_BOUND,0)) AS EXPECTED_ONBOOKS_LOWER_BOUND, " +
                "CONVERT(INT, ROUND(EXPECTED_ONBOOKS_UPPER_BOUND,0)) AS EXPECTED_ONBOOKS_UPPER_BOUND " +
                "FROM PROPERTY_ONBOOKS_PACE_ALERT " +
                "WHERE OCCUPANCY_DT IS NOT null AND PROPERTY_ID=:propertyId " +
                "ORDER BY OCCUPANCY_DT ASC";
    }

    public List<ExpectedForecastDetailForPastSevenDaysDTO> fetchExpectedForecastDetailForPastSevenDays(Date occupancyStartDate, Date occupancyEndDate, Date businessStartDate) {
        String queryStr = "exec usp_g3_inv_past_seven_days_data :occupancyStartDate, :occupancyEndDate, :businessStartDate, :propertyId";
        return tenantCrudService.findByNativeQuery(
                queryStr, QueryParameter.with(ExpectedForecastConstant.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and("occupancyStartDate", occupancyStartDate)
                        .and("occupancyEndDate", occupancyEndDate)
                        .and("businessStartDate", businessStartDate)
                        .parameters(), new ExpectedForecastDetailForLastSevenDaysRowMapper());
    }

    public List<OccupancyDemandOverride> getOccupancyDemandOverrides(Date startDate, Date endDate) {

        return tenantCrudService.findByNamedQuery(OccupancyDemandOverride.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID,
                QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .parameters());
    }

    public List<ArrivalDemandOverride> getArrivalDemandOverrides(Date startDate, Date endDate) {

        return tenantCrudService.findByNamedQuery(ArrivalDemandOverride.BY_ARRIVAL_DATE_RANGE_AND_PROPERTY_ID,
                QueryParameter.with(START_DATE, startDate).
                        and(END_DATE, endDate).
                        and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        parameters());
    }

    public List<ExpectedForecastDetailGraphForPastSevenDaysDTO> fetchExpectedForecastDetailGraphForPastSevenDays(Date occupancyDate, int pacePoint, Date dowDate) {

        QueryParameter queryParameters = QueryParameter.with(
                        ExpectedForecastConstant.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                .and(OCCUPANCY_DATE, occupancyDate)
                .and(DOW_END_DATE, dowDate)
                .and(PACE, pacePoint);

        String queryStr = "exec usp_g3_inv_past_seven_days_data_for_graph :occupancyDate, :propertyId, :dowEndDate, :pace";
        return tenantCrudService.findByNativeQuery(queryStr, queryParameters.parameters(), new ExpectedForecastDetailGraphForLastSevenDaysRowMapper());
    }
}
