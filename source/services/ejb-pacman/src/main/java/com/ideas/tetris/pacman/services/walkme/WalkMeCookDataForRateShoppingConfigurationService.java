package com.ideas.tetris.pacman.services.walkme;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateAccomType;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateChannel;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.platform.common.crudservice.CrudService;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class WalkMeCookDataForRateShoppingConfigurationService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    public void addRequirementsForRateShoppingConfiguration() {
        addRequirementsForCompetitorRoomType();
        addRequirementsForChannelSettings();
        addRequirementsForCompetitorSettings();
    }

    public void addRequirementsForCompetitorRoomType() {
        List<WebrateAccomType> webrateAccomTypeList = new ArrayList<>();
        webrateAccomTypeList.add(addWebrateAccomType(WalkMeConstants.WEBRATE_ACCOM_NAME_STANDARD));
        webrateAccomTypeList.add(addWebrateAccomType(WalkMeConstants.WEBRATE_ACCOM_NAME_STANDARD2));
        webrateAccomTypeList.add(addWebrateAccomType(WalkMeConstants.WEBRATE_ACCOM_NAME_DELUX));
        tenantCrudService.save(webrateAccomTypeList);
    }

    public void addRequirementsForChannelSettings() {
        List<WebrateChannel> webrateChannelList = new ArrayList<>();
        webrateChannelList.add(addWebrateChannel(WalkMeConstants.WEBRATE_CHANNEL1, WalkMeConstants.WEBRATE_CHANNEL_ID1));
        webrateChannelList.add(addWebrateChannel(WalkMeConstants.WEBRATE_CHANNEL2, WalkMeConstants.WEBRATE_CHANNEL_ID2));
        tenantCrudService.save(webrateChannelList);
    }

    public void addRequirementsForCompetitorSettings() {
        List<WebrateCompetitors> webrateCompetitorsList = new ArrayList<>();
        webrateCompetitorsList.add(addWebrateCompetitors(WalkMeConstants.WEBRATE_HOTEL_ID1, WalkMeConstants.WEBRATE_COMPETITOR1));
        webrateCompetitorsList.add(addWebrateCompetitors(WalkMeConstants.WEBRATE_HOTEL_ID2, WalkMeConstants.WEBRATE_COMPETITOR2));
        webrateCompetitorsList.add(addWebrateCompetitors(WalkMeConstants.WEBRATE_HOTEL_ID3, WalkMeConstants.WEBRATE_COMPETITOR3));
        webrateCompetitorsList.add(addWebrateCompetitors(WalkMeConstants.WEBRATE_HOTEL_ID4, WalkMeConstants.WEBRATE_COMPETITOR4));
        tenantCrudService.save(webrateCompetitorsList);
    }

    protected WebrateCompetitors addWebrateCompetitors(String webrateHotelID, String webrateCompetitorsName) {
        WebrateCompetitors webrateCompetitors = new WebrateCompetitors();
        webrateCompetitors.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        webrateCompetitors.setStatusId(1);
        webrateCompetitors.setWebrateHotelID(webrateHotelID);
        webrateCompetitors.setWebrateCompetitorsName(webrateCompetitorsName);
        webrateCompetitors.setWebrateCompetitorsAlias(webrateCompetitorsName);
        return webrateCompetitors;
    }

    protected WebrateChannel addWebrateChannel(String webrateChannelName, String fileChannelId) {
        WebrateChannel webrateChannel = new WebrateChannel();
        webrateChannel.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        webrateChannel.setStatusId(1);
        webrateChannel.setWebrateChannelName(webrateChannelName);
        webrateChannel.setWebrateChannelAlias(webrateChannelName);
        webrateChannel.setFileChannelID(fileChannelId);
        return webrateChannel;
    }

    protected WebrateAccomType addWebrateAccomType(String webrateAccomName) {
        WebrateAccomType webrateAccomType = new WebrateAccomType();
        webrateAccomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        webrateAccomType.setWebrateAccomName(webrateAccomName);
        webrateAccomType.setWebrateAccomAlias(webrateAccomName);
        return webrateAccomType;
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

}
