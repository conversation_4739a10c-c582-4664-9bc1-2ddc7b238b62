package com.ideas.tetris.pacman.services.analytics.services;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.roa.processgroup.entity.DowGroupDetail;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@Service
public class IpcfgDowGroupDetailService {
    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    private CrudService tenantCrudService;

    public List<DowGroupDetail> getDowGroupDetails(Collection<Integer> dowGroupIds) {
        return tenantCrudService.findByNamedQuery(DowGroupDetail.FIND_BY_DOW_IDS, QueryParameter
                .with("dowGroupIds", dowGroupIds)
                .parameters());
    }


}
