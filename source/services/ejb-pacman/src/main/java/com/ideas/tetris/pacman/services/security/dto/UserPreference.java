package com.ideas.tetris.pacman.services.security.dto;

public enum UserPreference {
    VIEWING_PREFERENCE("viewingPreference"),
    DEFAULT_PROPERTY("defaultProperty"),
    DEFAULT_PROPERTY_GROUP("defaultPropertyGroup"),
    DEFAULT_PROPERTY_OR_GROUP("defaultPropertyOrGroup"),
    DEFAULT_INVENTORY_GROUP("defaultInventoryGroup"),
    DEFAULT_LANDING_PAGE("defaultLandingPage"),
    DEFAULT_DATE_FORMAT("defaultDateFormat"),
    LANGUAGE("language"),
    FISCAL_CALENDAR_ENABLED("enableFiscalCalendar");

    private String preferenceId;

    UserPreference(String preferenceId) {
        this.preferenceId = preferenceId;
    }

    public String getPreferenceId() {
        return preferenceId;
    }
}
