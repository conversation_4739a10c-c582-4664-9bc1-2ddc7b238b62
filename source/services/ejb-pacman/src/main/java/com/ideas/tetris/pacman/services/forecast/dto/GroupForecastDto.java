package com.ideas.tetris.pacman.services.forecast.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GroupForecastDto {
    private Integer processGroupId;
    private Integer roomCategoryId;
    private Integer lumpId;
    private LocalDate occupancyDt;
    private Double occDemand;
    private Double occDemandStd;
}
