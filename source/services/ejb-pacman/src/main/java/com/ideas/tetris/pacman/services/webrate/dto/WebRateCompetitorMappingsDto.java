package com.ideas.tetris.pacman.services.webrate.dto;

import com.ideas.tetris.pacman.services.webrate.entity.*;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class WebRateCompetitorMappingsDto {
    private final boolean isPricingScreenOptimizationEnabled;
    private final Map<Integer, List<WebrateAccomClassMapping>> webrateAccomClassMappings;
    private final Map<String, WebrateCompetitorsAccomClass> webrateCompetitorsAccomMapping;
    private final Map<Integer, WebrateCompetitors> webrateCompetitorsIdMap;
    private final Map<Integer, WebrateAccomType> webrateAccomTypeIdMap;
    private final Map<Integer, List<WebrateOverrideCompetitorDetails>> webrateOvrdCompDtls;
    private final LocalDate sysDate;

    public WebRateCompetitorMappingsDto(boolean isPricingScreenOptimizationEnabled, Map<Integer, List<WebrateAccomClassMapping>> webrateAccomClassMappings, Map<String, WebrateCompetitorsAccomClass> webrateCompetitorsAccomMapping, Map<Integer, WebrateCompetitors> webrateCompetitorsIdMap, Map<Integer, WebrateAccomType> webrateAccomTypeIdMap, Map<Integer, List<WebrateOverrideCompetitorDetails>> webrateOvrdCompDtls, LocalDate sysDate) {
        this.isPricingScreenOptimizationEnabled = isPricingScreenOptimizationEnabled;
        this.webrateAccomClassMappings = webrateAccomClassMappings;
        this.webrateCompetitorsAccomMapping = webrateCompetitorsAccomMapping;
        this.webrateCompetitorsIdMap = webrateCompetitorsIdMap;
        this.webrateAccomTypeIdMap = webrateAccomTypeIdMap;
        this.webrateOvrdCompDtls = webrateOvrdCompDtls;
        this.sysDate = sysDate;
    }

    public WebRateCompetitorMappingsDto() {
        this(false, Collections.emptyMap(), Collections.emptyMap(), Collections.emptyMap(), Collections.emptyMap(), Collections.emptyMap(), null);
    }

    public boolean isPricingScreenOptimizationEnabled() {
        return isPricingScreenOptimizationEnabled;
    }

    public Map<Integer, List<WebrateAccomClassMapping>> getWebrateAccomClassMappings() {
        return webrateAccomClassMappings;
    }

    public Map<String, WebrateCompetitorsAccomClass> getWebrateCompetitorsAccomMapping() {
        return webrateCompetitorsAccomMapping;
    }

    public Map<Integer, WebrateCompetitors> getWebrateCompetitorsIdMap() {
        return webrateCompetitorsIdMap;
    }

    public Map<Integer, WebrateAccomType> getWebrateAccomTypeIdMap() {
        return webrateAccomTypeIdMap;
    }

    public Map<Integer, List<WebrateOverrideCompetitorDetails>> getWebrateOvrdCompDtls() {
        return webrateOvrdCompDtls;
    }

    public LocalDate getSysDate() {
        return sysDate;
    }
}
