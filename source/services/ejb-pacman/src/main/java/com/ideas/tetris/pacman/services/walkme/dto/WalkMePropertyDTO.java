package com.ideas.tetris.pacman.services.walkme.dto;

import com.ideas.tetris.pacman.services.walkme.WalkMePropertiesRequirementsEnum;
import com.ideas.tetris.pacman.services.walkme.WalkMePropertyStatusEnum;

import java.util.Arrays;

public class WalkMePropertyDTO {
    private String propertyId;
    private String propertyCode;
    private WalkMePropertyStatusEnum status;
    private WalkMePropertiesRequirementsEnum requirements;

    public WalkMePropertyDTO() {
        //default constructor
    }

    public String getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(String propertyId) {
        this.propertyId = propertyId;
    }

    public String getPropertyCode() {
        return propertyCode;
    }

    public void setPropertyCode(String propertyCode) {
        this.propertyCode = propertyCode;
    }

    public WalkMePropertyStatusEnum getStatus() {
        return status;
    }

    public void setStatus(WalkMePropertyStatusEnum status) {
        this.status = status;
    }

    public String getRequirements() {
        String requirementsString = Arrays.toString(requirements.getRequirements());
        return requirementsString.replace("[", "").replace("]", "");
    }

    public void setRequirements(WalkMePropertiesRequirementsEnum requirements) {
        this.requirements = requirements;
    }
}
