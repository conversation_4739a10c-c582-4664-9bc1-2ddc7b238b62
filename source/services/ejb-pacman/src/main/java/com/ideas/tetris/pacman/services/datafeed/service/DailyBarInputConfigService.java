package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.DailyBarInputConfig;
import com.ideas.tetris.pacman.services.marketsegment.entity.OffsetType;
import com.ideas.tetris.pacman.services.opera.entity.DailyBarConfig;
import com.ideas.tetris.pacman.services.opera.entity.DailyBarRateChart;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Component
@Transactional
public class DailyBarInputConfigService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    public List<DailyBarInputConfig> getDailyBarDetails() {
        List<DailyBarInputConfig> dailyBarInputConfigs = new ArrayList<>();
        Map<Integer, String> accomTypes = getAccomTypes();
        Map<Integer, String> productCodes = getProductCodes();
        Map<Integer, String> offsetTypes = getOffsetTypes();

        crudService.findAll(DailyBarConfig.class).forEach(dailyBarConfig -> {
            dailyBarInputConfigs.add(setDailyBarInputFeilds(accomTypes, productCodes, offsetTypes, dailyBarConfig));
        });
        return dailyBarInputConfigs;
    }

    private DailyBarInputConfig setDailyBarInputFeilds(Map<Integer, String> accomTypes, Map<Integer, String> productCodes, Map<Integer, String> offsetTypes, DailyBarConfig dailyBarConfig) {
        DailyBarInputConfig dailyBarInputConfig = new DailyBarInputConfig();

        dailyBarInputConfig.setRoomTypeCode(accomTypes.get(dailyBarConfig.getAccomTypeId()));
        dailyBarInputConfig.setProduct(productCodes.get(dailyBarConfig.getProductId()));
        dailyBarInputConfig.setRatePlan(dailyBarConfig.getRatePlan());
        dailyBarInputConfig.setSingleOffsetType(offsetTypes.get(dailyBarConfig.getSingleRateChart().getOffSetTypeId()));
        dailyBarInputConfig.setDoubleOffsetType(offsetTypes.get(dailyBarConfig.getDoubleRateChart().getOffSetTypeId()));
        dailyBarInputConfig.setExtraAdultOffsetType(offsetTypes.get(dailyBarConfig.getExtraAdultRateChart().getOffSetTypeId()));
        dailyBarInputConfig.setExtraChildOffsetType(offsetTypes.get(dailyBarConfig.getExtraChildRateChart().getOffSetTypeId()));
        dailyBarInputConfig.setStartDate(dailyBarConfig.getStartDate() == null ? null : dailyBarConfig.getStartDate().toDate());
        dailyBarInputConfig.setEndDate(dailyBarConfig.getEndDate() == null ? null : dailyBarConfig.getEndDate().toDate());

        setSingleOffsetsOfWeek(dailyBarConfig.getSingleRateChart(), dailyBarInputConfig);
        setDoubleOffsetsOfWeek(dailyBarConfig.getDoubleRateChart(), dailyBarInputConfig);
        setExtraAdultOffsetsOfWeek(dailyBarConfig.getExtraAdultRateChart(), dailyBarInputConfig);
        setExtraChildOffsetsOfWeek(dailyBarConfig.getExtraChildRateChart(), dailyBarInputConfig);

        return dailyBarInputConfig;
    }

    public Map<Integer, String> getAccomTypes() {
        List<AccomType> accomTypeList = crudService.findByNamedQuery(AccomType.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        return accomTypeList.stream().collect(Collectors.toMap(AccomType::getId, AccomType::getAccomTypeCode));
    }

    public Map<Integer, String> getProductCodes() {
        return crudService.findAll(Product.class).stream().collect(Collectors.toMap(Product::getId, Product::getCode));
    }

    public Map<Integer, String> getOffsetTypes() {
        Map<Integer, String> offsetTypes = new HashMap<>();
        for (OffsetType offsetType : crudService.findAll(OffsetType.class)) {
            offsetTypes.put(offsetType.getId(), OFFSET_TYPE_SYMBOL.getLabelOf(offsetType.getName()));
        }
        return offsetTypes;
    }

    private void setSingleOffsetsOfWeek(DailyBarRateChart dailyBarRateChart, DailyBarInputConfig dailyBarInputConfig) {
        dailyBarInputConfig.setSingleMonday(round(dailyBarRateChart.getMondayRate()));
        dailyBarInputConfig.setSingleTuesday(round(dailyBarRateChart.getTuesdayRate()));
        dailyBarInputConfig.setSingleWednesday(round(dailyBarRateChart.getWednesdayRate()));
        dailyBarInputConfig.setSingleThursday(round(dailyBarRateChart.getThursdayRate()));
        dailyBarInputConfig.setSingleFriday(round(dailyBarRateChart.getFridayRate()));
        dailyBarInputConfig.setSingleSaturday(round(dailyBarRateChart.getSaturdayRate()));
        dailyBarInputConfig.setSingleSunday(round(dailyBarRateChart.getSundayRate()));
    }

    private void setDoubleOffsetsOfWeek(DailyBarRateChart dailyBarRateChart, DailyBarInputConfig dailyBarInputConfig) {
        dailyBarInputConfig.setDoubleMonday(round(dailyBarRateChart.getMondayRate()));
        dailyBarInputConfig.setDoubleTuesday(round(dailyBarRateChart.getTuesdayRate()));
        dailyBarInputConfig.setDoubleWednesday(round(dailyBarRateChart.getWednesdayRate()));
        dailyBarInputConfig.setDoubleThursday(round(dailyBarRateChart.getThursdayRate()));
        dailyBarInputConfig.setDoubleFriday(round(dailyBarRateChart.getFridayRate()));
        dailyBarInputConfig.setDoubleSaturday(round(dailyBarRateChart.getSaturdayRate()));
        dailyBarInputConfig.setDoubleSunday(round(dailyBarRateChart.getSundayRate()));
    }

    private void setExtraAdultOffsetsOfWeek(DailyBarRateChart dailyBarRateChart, DailyBarInputConfig dailyBarInputConfig) {
        dailyBarInputConfig.setExtraAdultMonday(round(dailyBarRateChart.getMondayRate()));
        dailyBarInputConfig.setExtraAdultTuesday(round(dailyBarRateChart.getTuesdayRate()));
        dailyBarInputConfig.setExtraAdultWednesday(round(dailyBarRateChart.getWednesdayRate()));
        dailyBarInputConfig.setExtraAdultThursday(round(dailyBarRateChart.getThursdayRate()));
        dailyBarInputConfig.setExtraAdultFriday(round(dailyBarRateChart.getFridayRate()));
        dailyBarInputConfig.setExtraAdultSaturday(round(dailyBarRateChart.getSaturdayRate()));
        dailyBarInputConfig.setExtraAdultSunday(round(dailyBarRateChart.getSundayRate()));
    }

    private void setExtraChildOffsetsOfWeek(DailyBarRateChart dailyBarRateChart, DailyBarInputConfig dailyBarInputConfig) {
        dailyBarInputConfig.setExtraChildMonday(round(dailyBarRateChart.getMondayRate()));
        dailyBarInputConfig.setExtraChildTuesday(round(dailyBarRateChart.getTuesdayRate()));
        dailyBarInputConfig.setExtraChildWednesday(round(dailyBarRateChart.getWednesdayRate()));
        dailyBarInputConfig.setExtraChildThursday(round(dailyBarRateChart.getThursdayRate()));
        dailyBarInputConfig.setExtraChildFriday(round(dailyBarRateChart.getFridayRate()));
        dailyBarInputConfig.setExtraChildSaturday(round(dailyBarRateChart.getSaturdayRate()));
        dailyBarInputConfig.setExtraChildSunday(round(dailyBarRateChart.getSundayRate()));
    }

    public BigDecimal round(BigDecimal value) {
        return value == null ? value : BigDecimalUtil.round(value, 2);
    }

    enum OFFSET_TYPE_SYMBOL {

        PERCENTAGE("Percentage", "P"),
        FIXED("Fixed", "F"),
        SET("Set", "S");

        private String value;
        private String label;

        OFFSET_TYPE_SYMBOL(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public static String getLabelOf(String value) {
            for (OFFSET_TYPE_SYMBOL type : values()) {
                if (type.getValue().equalsIgnoreCase(value)) {
                    return type.getLabel();
                }
            }
            return "";
        }

        public String getValue() {
            return value;
        }

        public String getLabel() {
            return label;
        }
    }

}
