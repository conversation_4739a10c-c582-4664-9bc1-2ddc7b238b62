package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesProductTypeEnum;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesPackage;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesPackageChargeType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ChildPricingType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductGroup;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductHierarchy;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductPackage;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateCode;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateOffset;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.ProductSendDecisionAsAdjustmentDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.PricingDataProductDefaultValueDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.PricingDataProductDefinitionDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.PricingDataProductGroupsDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.PricingDataProductHierarchyDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.PricingDataProductOptimizationDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.PricingDataProductPackageElementAssignmentDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.PricingDataProductPackageElementsDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.PricingDataProductPackageElementsDowDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.PricingDataProductRateCodeAssignmentDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.PricingDataProductRoomTypeAssignmentDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.PricingDataProductSeasonValueDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.ProductChildPricingTypeDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.ProductFreeNightDefinitionDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.ProductGroupProductDefinition;
import com.ideas.tetris.pacman.services.datafeed.entity.DataFeedEndpoint;
import com.ideas.tetris.pacman.services.datafeed.entity.DataFeedEndpointProductCodeMap;
import com.ideas.tetris.pacman.services.independentproducts.service.IndependentProductsService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.product.ProductCode;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.END_DATE;
import static com.ideas.tetris.pacman.common.constants.Constants.EXTENDED_WINDOW_PRODUCTS;
import static com.ideas.tetris.pacman.common.constants.Constants.IS_EXTENDED_WINDOW_ENABLED;
import static com.ideas.tetris.pacman.common.constants.Constants.IS_OPTIX_DATAFEED;
import static com.ideas.tetris.pacman.common.constants.Constants.ONE;
import static com.ideas.tetris.pacman.common.constants.Constants.PRODUCTS;
import static com.ideas.tetris.pacman.common.constants.Constants.PROPERTY_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.START_DATE;
import static com.ideas.tetris.pacman.common.constants.Constants.STATUS;
import static com.ideas.tetris.pacman.common.constants.Constants.ZERO;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class AgileRatesDataFeedService {

    private static final String OFFSET_ZERO = "Same adjustments for all Room Classes";
    private static final String OFFSET_ONE = "Different adjustments by Room Class";
    public static final Predicate<ProductRateCode> isProductRateCodeAgileRatesOrIndependentOrFixedAboveBarOrGroup = productRateCode -> Product.AGILE_RATES_PRODUCT_CODE.equals(productRateCode.getProduct().getCode()) || Product.FIXED_ABOVE_BAR_CODE.equals(productRateCode.getProduct().getCode()) || Product.INDEPENDENT_PRODUCT_CODE.equals(productRateCode.getProduct().getCode()) || Product.GROUP_PRODUCT_CODE.equals(productRateCode.getProduct().getCode());
    public static final Predicate<Product> isProductCodeAgileRatesOrIndependentOrFixedAboveBarOrGroup = product -> Product.INDEPENDENT_PRODUCT_CODE.equals(product.getCode()) || Product.AGILE_RATES_PRODUCT_CODE.equals(product.getCode()) || Product.FIXED_ABOVE_BAR_CODE.equals(product.getCode()) || Product.GROUP_PRODUCT_CODE.equals(product.getCode());
    public static final Predicate<Product> isProductCodeAgileRatesOrFixedAboveBarOrGroup = product -> Product.AGILE_RATES_PRODUCT_CODE.equals(product.getCode()) || Product.FIXED_ABOVE_BAR_CODE.equals(product.getCode()) || Product.GROUP_PRODUCT_CODE.equals(product.getCode());
    public static final Predicate<Product> isProductCodeAgileRatesOrIndependentOrFixedAboveBarOrGroupOrSystemDefault = product -> Product.INDEPENDENT_PRODUCT_CODE.equals(product.getCode()) || Product.AGILE_RATES_PRODUCT_CODE.equals(product.getCode()) || Product.FIXED_ABOVE_BAR_CODE.equals(product.getCode()) || Product.GROUP_PRODUCT_CODE.equals(product.getCode()) || product.isSystemDefault();
    public static final Predicate<Product> isProductCodeAgileRates = product -> Product.AGILE_RATES_PRODUCT_CODE.equals(product.getCode());
    public static final Predicate<ProductPackage> isProductPackageAgileRates = productPackage -> Product.AGILE_RATES_PRODUCT_CODE.equals(productPackage.getProduct().getCode());
    public static final Predicate<ProductAccomType> isProductAccomTyeAgileRatesOrIndependentOrFixedAboveBarOrGroup = productAccomType -> Product.AGILE_RATES_PRODUCT_CODE.equals(productAccomType.getProduct().getCode()) || Product.FIXED_ABOVE_BAR_CODE.equals(productAccomType.getProduct().getCode()) || Product.INDEPENDENT_PRODUCT_CODE.equals(productAccomType.getProduct().getCode()) || Product.GROUP_PRODUCT_CODE.equals(productAccomType.getProduct().getCode());
    public static final Predicate<ProductRateOffset> isProductRateOffsetAgileRatesOrFixedAboveBarOrGroup = productCode -> Product.AGILE_RATES_PRODUCT_CODE.equals(productCode.getProduct().getCode()) || Product.FIXED_ABOVE_BAR_CODE.equals(productCode.getProduct().getCode()) || Product.GROUP_PRODUCT_CODE.equals(productCode.getProduct().getCode());
    public static final Predicate<ProductRateOffset> isDefaultProduct = product -> product.getStartDate() == null && product.getEndDate() == null;
    public static final Predicate<Product> isNonSystemDefaultProduct = product -> !product.isSystemDefault();
    public static final Predicate<ProductRateCode> isNonSystemDefaultProductRateCode = productRateCode -> !productRateCode.getProduct().isSystemDefault();
    public static final Predicate<ProductAccomType> isNonSystemDefaultProductAccomType = productAccomType -> !productAccomType.getProduct().isSystemDefault();
    public static final Predicate<ProductRateOffset> isNonSystemDefaultProductRateOffset = productRateOffset -> !productRateOffset.getProduct().isSystemDefault();
    public static final Predicate<ProductPackage> isNonSystemDefaultProductPackage = productPackage -> !productPackage.getProduct().isSystemDefault();

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
	private DatafeedService datafeedService;

    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
	private IndependentProductsService independentProductsService;

    public List<PricingDataProductDefinitionDTO> findPricingDataProductDefinitionDTO(DatafeedRequest datafeedRequest) {
        List<PricingDataProductDefinitionDTO> result = new ArrayList<>();
        Map<Integer, String> productIDAndNames = new HashMap<>();
        List<Product> allProducts = tenantCrudService.findAll(Product.class);
        allProducts.forEach(product -> productIDAndNames.put(product.getId(), product.getName()));
        allProducts.stream()
                .filter(filterProductBasedOnApplicableProductCode(datafeedRequest.getDatafeedName()))
                .filter(datafeedRequest.isOptixDatafeed() ? product -> true : isNonSystemDefaultProduct)
                .forEach(agileProduct -> {
                    PricingDataProductDefinitionDTO dto = new PricingDataProductDefinitionDTO(agileProduct, productIDAndNames.get(agileProduct.getDependentProductId()));
                    AgileRatesProductTypeEnum productType = AgileRatesProductTypeEnum.fromValue(agileProduct.getType());
                    dto.setProductType(null != productType && !agileProduct.isSystemDefault() ? ResourceUtil.getText(productType.getCaptionKey(), Locale.US) : agileProduct.getType());
                    result.add(dto);
                });
        return result;
    }

    private Predicate<Product> filterNonSystemDefaultProducts() {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)) {
            return isNonSystemDefaultProduct;
        }
        return product -> true;
    }

    private Predicate<Product> filterProductBasedOnApplicableProductCode(String datafeedName) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)) {
            List<String> productCodes = getApplicableNonSystemDefaultProductCodes(datafeedName);
            return p -> productCodes.contains(p.getProductCode().getProductCodeName());
        }
        return isProductCodeAgileRatesOrIndependentOrFixedAboveBarOrGroupOrSystemDefault;

    }

    public List<ProductChildPricingTypeDTO> findProductChildPricingTypeDTO(String datafeedName) {
        List<ProductChildPricingTypeDTO> productChildPricingTypeDTOList = new ArrayList<>();
        List<Product> products = tenantCrudService.findByNamedQuery(Product.GET_BY_STATUS, QueryParameter.with(STATUS, TenantStatusEnum.ACTIVE).parameters());
        products.stream()
                .filter(filterAgileRatesProductCode(datafeedName))
                .filter(filterNonSystemDefaultProducts())
                .forEach(product -> {
                    ProductChildPricingTypeDTO dto = new ProductChildPricingTypeDTO(product.getName(), ChildPricingType.valueOfId(product.getChildPricingType()).getCaption());
                    productChildPricingTypeDTOList.add(dto);
                });
        return productChildPricingTypeDTOList;
    }

    private Predicate<Product> filterAgileRatesProductCode(String datafeedName) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)) {
            List<String> productCodes = getApplicableNonSystemDefaultProductCodes(datafeedName);
            return p -> productCodes.contains(p.getProductCode().getProductCodeName());
        }
        return isProductCodeAgileRates;

    }

    public List<ProductSendDecisionAsAdjustmentDTO> getAgileProductSendDecisionDetails() {
        return findAllProducts().stream().map(ProductSendDecisionAsAdjustmentDTO::new).collect(Collectors.toList());
    }

    private List<Product> findAllProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_ALL);
    }

    public List<PricingDataProductRateCodeAssignmentDTO> findProductRateCodeAssignment(DatafeedRequest datafeedRequest) {
        List<PricingDataProductRateCodeAssignmentDTO> result = new ArrayList<>();
        List<ProductRateCode> productRateCodes = tenantCrudService.findAll(ProductRateCode.class);
        productRateCodes.stream()
                .filter(filterProductRateCodeBasedOnApplicableProductCode(datafeedRequest.getDatafeedName()))
                .filter(filterNonSystemDefaultProductRateCodes())
                .forEach(prc -> {
                    PricingDataProductRateCodeAssignmentDTO dto = new PricingDataProductRateCodeAssignmentDTO(prc);
                    result.add(dto);
                });
        return result;
    }

    private Predicate<ProductRateCode> filterNonSystemDefaultProductRateCodes() {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)) {
            return isNonSystemDefaultProductRateCode;
        }
        return product -> true;
    }

    private Predicate<ProductRateCode> filterProductRateCodeBasedOnApplicableProductCode(String datafeedName) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)) {
            List<String> productCodes = getApplicableNonSystemDefaultProductCodes(datafeedName);
            return p -> productCodes.contains(p.getProduct().getProductCode().getProductCodeName());
        }
        return isProductRateCodeAgileRatesOrIndependentOrFixedAboveBarOrGroup;
    }

    public List<PricingDataProductHierarchyDTO> findProductHierarchy() {
        List<ProductHierarchy> productHierarchies = tenantCrudService.findAll(ProductHierarchy.class);
        return productHierarchies.stream().map(PricingDataProductHierarchyDTO::new).collect(Collectors.toList());
    }

    public List<PricingDataProductGroupsDTO> findProductGroups() {
        List<ProductGroup> productGroups = tenantCrudService.findAll(ProductGroup.class);
        return prepareList(productGroups);
    }

    public List<PricingDataProductOptimizationDTO> findAdjustmentsForOptimizedProducts() {
        List<PricingDataProductOptimizationDTO> resultDtos = new ArrayList<>();
        List<Product> products = tenantCrudService.findAll(Product.class).stream().filter(Product::isOptimized).collect(Collectors.toList());
        if (!products.isEmpty()) {
            if (products.get(0).isRoomClassOffset()) {
                resultDtos.add(new PricingDataProductOptimizationDTO(OFFSET_ONE));
            } else {
                resultDtos.add(new PricingDataProductOptimizationDTO(OFFSET_ZERO));
            }
        }
        return resultDtos;
    }

    public List<PricingDataProductRoomTypeAssignmentDTO> findProductRoomTypeAssignment(DatafeedRequest datafeedRequest) {
        List<PricingDataProductRoomTypeAssignmentDTO> result = new ArrayList<>();
        List<ProductAccomType> productAccomTypes = tenantCrudService.findAll(ProductAccomType.class);
        productAccomTypes.stream()
                .filter(filterProductAccomTypeBasedOnApplicableProductCode(datafeedRequest.getDatafeedName()))
                .filter(filterNonSystemDefaultProductRoomType())
                .forEach(pat -> {
                    PricingDataProductRoomTypeAssignmentDTO dto = new PricingDataProductRoomTypeAssignmentDTO(pat);
                    result.add(dto);
                });
        return result;
    }

    private Predicate<ProductAccomType> filterNonSystemDefaultProductRoomType() {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)) {
            return isNonSystemDefaultProductAccomType;
        }
        return product -> true;
    }

    private Predicate<ProductAccomType> filterProductAccomTypeBasedOnApplicableProductCode(String datafeedName) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)) {
            List<String> productCodes = getApplicableNonSystemDefaultProductCodes(datafeedName);
            return p -> productCodes.contains(p.getProduct().getProductCode().getProductCodeName());
        }
        return isProductAccomTyeAgileRatesOrIndependentOrFixedAboveBarOrGroup;
    }

    public List<PricingDataProductDefaultValueDTO> findProductDefaultValue(String datafeedName) {
        List<PricingDataProductDefaultValueDTO> result = new ArrayList<>();
        List<ProductRateOffset> productRateOffset = tenantCrudService.findAll(ProductRateOffset.class);
        productRateOffset.stream()
                .filter(filterProductRateOffsetBasedOnApplicableProductCode(datafeedName).and(isDefaultProduct))
                .filter(filterNonSystemDefaultProductRateOffset())
                .forEach(pro -> {
                    PricingDataProductDefaultValueDTO dto = new PricingDataProductDefaultValueDTO(pro);
                    result.add(dto);
                });
        return result;
    }

    public List<PricingDataProductSeasonValueDTO> findProductSeasonalValue(final Date startDate, final String datafeedName) {
        List<PricingDataProductSeasonValueDTO> result = new ArrayList<>();
        List<ProductRateOffset> productRateOffset = tenantCrudService.findByNamedQuery(ProductRateOffset.BY_SEASONAL_PRODUCT_BY_STARTDATE_OFFSET,
                QueryParameter.with(START_DATE, LocalDate.fromDateFields(startDate)).parameters());
        productRateOffset.stream()
                .filter(filterProductRateOffsetBasedOnApplicableProductCode(datafeedName))
                .filter(filterNonSystemDefaultProductRateOffset())
                .forEach(pro -> {
                    PricingDataProductSeasonValueDTO dto = new PricingDataProductSeasonValueDTO(pro);
                    result.add(dto);
                });
        return result;
    }

    private Predicate<ProductRateOffset> filterProductRateOffsetBasedOnApplicableProductCode(String datafeedName) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)) {
            List<String> productCodes = getApplicableNonSystemDefaultProductCodes(datafeedName);
            return p -> productCodes.contains(p.getProduct().getProductCode().getProductCodeName());
        }
        return isProductRateOffsetAgileRatesOrFixedAboveBarOrGroup;
    }

    private Predicate<ProductRateOffset> filterNonSystemDefaultProductRateOffset() {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)) {
            return isNonSystemDefaultProductRateOffset;
        }
        return product -> true;
    }

    public List<PricingDataProductPackageElementAssignmentDTO> findProductPackageElementAssignment(String datafeedName) {
        List<PricingDataProductPackageElementAssignmentDTO> result = new ArrayList<>();
        List<ProductPackage> productPackages = tenantCrudService.findAll(ProductPackage.class);
        productPackages.stream()
                .filter(filterProductPackageBasedOnApplicableProductCode(datafeedName))
                .filter(filterNonSystemDefaultProductPackage())
                .forEach(prp -> {
                    PricingDataProductPackageElementAssignmentDTO dto = new PricingDataProductPackageElementAssignmentDTO(prp);
                    result.add(dto);
                });
        return result;
    }

    private Predicate<ProductPackage> filterProductPackageBasedOnApplicableProductCode(String datafeedName) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)) {
            List<String> productCodes = getApplicableNonSystemDefaultProductCodes(datafeedName);
            return p -> productCodes.contains(p.getProduct().getProductCode().getProductCodeName());
        }
        return isProductPackageAgileRates;
    }

    private Predicate<ProductPackage> filterNonSystemDefaultProductPackage() {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)) {
            return isNonSystemDefaultProductPackage;
        }
        return product -> true;
    }

    public List<PricingDataProductPackageElementsDTO> findProductPackageElements() {
        List<PricingDataProductPackageElementsDTO> result = new ArrayList<>();
        List<AgileRatesPackage> productPackages = tenantCrudService.findAll(AgileRatesPackage.class);
        productPackages.forEach(arp -> {
            PricingDataProductPackageElementsDTO dto = new PricingDataProductPackageElementsDTO(arp);
            result.add(dto);
        });
        return result;
    }

    public List<PricingDataProductPackageElementsDowDTO> findProductPackageElementsEnhanced() {
        List<PricingDataProductPackageElementsDowDTO> result = new ArrayList<>();
        List<AgileRatesPackageChargeType> productPackages = tenantCrudService.findAll(AgileRatesPackageChargeType.class);
        productPackages.forEach(arp -> {
            PricingDataProductPackageElementsDowDTO dto = new PricingDataProductPackageElementsDowDTO(arp);
            result.add(dto);
        });
        return result;
    }

    public List<Object> getAgileRateProductsPricingData(DatafeedRequest datafeedRequest) {
        Map<String, Object> parameters = getParameters(datafeedRequest);
        List products = (List) parameters.get(PRODUCTS);
        if (products.isEmpty()) {
            return Collections.emptyList();
        }
        final List<Object> agileRateDecisions = tenantCrudService.findByNamedQuery(
                CPDecisionBAROutput.GET_DECISIONS_WITH_PRODUCT_AND_DATE_RANGE,
                parameters, datafeedRequest.getStartPosition(), datafeedRequest.getSize());
        return agileRateDecisions.stream()
                .map(AgileRateData::new)
                .collect(Collectors.toList());
    }

    public List<CPDecisionBAROutput> getAgileRateProductsPricingDataAccomType(Product product, LocalDate occupancyDate, Integer accomTypeId) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("product", product);
        parameters.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        parameters.put("occupancyDate", occupancyDate);
        parameters.put("accomTypeID", tenantCrudService.findByNamedQuery(AccomType.ALL_BY_IDS, QueryParameter.with("accomTypeIds", accomTypeId).parameters()).get(0));
        return tenantCrudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_FOR_AGILE_RATE_PRODUCTS_WITH_DATE_RANGE_FOR_ACCOM_TYPE, parameters);
    }

    private List<PricingDataProductGroupsDTO> prepareList(List<ProductGroup> productGroups) {
        Map<String, String> productGroupsMap = productGroups.stream().collect(Collectors.toMap(g -> g.getAgileRatesProductGroup().getName(), g -> g.getProduct().getName(), (a, b) -> a + "," + b));
        return productGroupsMap.entrySet().stream().map(e -> new PricingDataProductGroupsDTO(e.getKey(), e.getValue())).collect(Collectors.toList());
    }

    private List<Product> getAgileRateProducts(String datafeedName) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)) {
            List<String> productCodes = getApplicableNonSystemDefaultProductCodes(datafeedName);
            List<Product> products = tenantCrudService.findByNamedQuery(Product.GET_BY_STATUS, QueryParameter.with(STATUS, TenantStatusEnum.ACTIVE).parameters());
            return products.stream().filter(p -> productCodes.contains(p.getProductCode().getProductCodeName())).collect(Collectors.toList());
        }
        return tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_AGILE_RATES_INDEPENDENT_GROUP_PRODUCTS_BY_STATUS, QueryParameter.with(STATUS, TenantStatusEnum.ACTIVE).parameters());
    }

    private Map<String, Object> getParameters(DatafeedRequest datafeedRequest) {
        Map<String, Object> parameters = new HashMap<>();
        List<Product> PermittedProducts = getAgileRateProducts(datafeedRequest.getDatafeedName());
        Set<Integer> productIdsOfExtendedWindow = PermittedProducts.stream()
                .filter(p -> p.getId() == 1 || p.getProductCode().getProductCodeName().equalsIgnoreCase(Product.GROUP_PRODUCT_CODE))
                .map(Product::getId).collect(Collectors.toSet());
        parameters.put(PRODUCTS, PermittedProducts);
        parameters.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        parameters.put(START_DATE, new LocalDate(datafeedRequest.getStartDate()));
        parameters.put(END_DATE, new LocalDate(datafeedRequest.getEndDate()));
        parameters.put(IS_EXTENDED_WINDOW_ENABLED, datafeedRequest.isUseExtendedWindowDecision() ? ONE : ZERO);
        parameters.put(IS_OPTIX_DATAFEED, datafeedRequest.isOptixDatafeed() ? ONE : ZERO);
        parameters.put(EXTENDED_WINDOW_PRODUCTS, productIdsOfExtendedWindow);
        return parameters;
    }

    public List<ProductFreeNightDefinitionDTO> getProductFreeNightDefinition(String datafeedName) {
        List<ProductFreeNightDefinitionDTO> productFreeNightDefinitionDTOList = new ArrayList<>();
        List<Product> products = tenantCrudService.findAll(Product.class);
        products.stream()
                .filter(filterAgileRatesProductCode(datafeedName))
                .forEach(product -> {
                    ProductFreeNightDefinitionDTO dto = new ProductFreeNightDefinitionDTO(product.getName(), product.isFreeNightEnabled(), product.isFreeUpgradeEnabled());
                    productFreeNightDefinitionDTOList.add(dto);
                });
        return productFreeNightDefinitionDTOList;
    }

    public List<ProductGroupProductDefinition> getProductGroupProductDefinition() {
        List<ProductGroupProductDefinition> result = new ArrayList<>();
        List<Product> products = tenantCrudService.findByNamedQuery(Product.GET_ALL);
        List<Product> groupProducts = products.stream().filter(product -> Product.GROUP_PRODUCT_CODE.equals(product.getProductCode().getProductCodeName())).collect(Collectors.toList());
        groupProducts.forEach(groupProduct -> result.add(new ProductGroupProductDefinition(groupProduct.getName(), groupProduct.isUseInSmallGroupEval(), groupProduct.getMinRooms(), groupProduct.getMaxRooms())));
        return result;
    }

    public List<String> getApplicableNonSystemDefaultProductCodes(String datafeedName) {
        DataFeedEndpoint dataFeedEndpoint = datafeedService.getDatafeedEndpointFor(PacmanWorkContextHelper.getClientCode(), datafeedName);
        List<DataFeedEndpointProductCodeMap> productCodeMap = dataFeedEndpoint.getDataFeedEndpointProductCodeMaps();
        List<String> datafeedEnabledProductCodes = independentProductsService.findAllDatafeedEnabledProductCodes().stream().map(ProductCode::getProductCodeName).collect(Collectors.toList());
        return productCodeMap.stream().map(DataFeedEndpointProductCodeMap::getProductCode).filter(datafeedEnabledProductCodes::contains)
                .collect(Collectors.toList());
    }

    static class AgileRateData {
        private static final int ROUNDING_SCALE_FOR_PRICE = 2;
        private String arrivalDate;
        private String productName;
        private String accomClassName;
        private String accomTypeName;
        private BigDecimal price;

        AgileRateData(Object row) {
            Object[] columns = (Object[]) row;
            this.arrivalDate = getFormattedDate(columns[0].toString());
            this.productName = columns[1].toString();
            this.accomClassName = columns[2].toString();
            this.accomTypeName = columns[3].toString();
            this.price = columns[4] instanceof BigDecimal ? (BigDecimal) columns[4] : BigDecimal.ZERO;
        }

        private String getFormattedDate(String date) {
            DateTimeFormatter currentFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            DateTimeFormatter newFormat = DateTimeFormatter.ofPattern("dd-MMM-yyyy");
            return java.time.LocalDate.parse(date, currentFormat).format(newFormat);
        }

        public String getArrivalDate() {
            return arrivalDate;
        }

        public void setArrivalDate(String arrivalDate) {
            this.arrivalDate = arrivalDate;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public String getAccomClassName() {
            return accomClassName;
        }

        public void setAccomClassName(String accomClassName) {
            this.accomClassName = accomClassName;
        }

        public String getAccomTypeName() {
            return accomTypeName;
        }

        public void setAccomTypeName(String accomTypeName) {
            this.accomTypeName = accomTypeName;
        }

        public String getPrice() {
            return String.valueOf(price.setScale(ROUNDING_SCALE_FOR_PRICE, RoundingMode.HALF_UP));
        }

        public void setPrice(BigDecimal price) {
            this.price = price;
        }
    }
}
