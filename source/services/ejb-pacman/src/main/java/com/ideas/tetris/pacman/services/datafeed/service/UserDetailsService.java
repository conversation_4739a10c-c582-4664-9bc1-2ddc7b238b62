package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.services.datafeed.dto.UserDetails;
import com.ideas.tetris.pacman.services.reports.userreport.UserReportService;
import com.ideas.tetris.pacman.services.reports.userreport.dto.FilterDto;
import com.ideas.tetris.pacman.services.reports.userreport.dto.UserRolePermissionDetailsDTO;
import com.ideas.tetris.pacman.services.security.RoleService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class UserDetailsService {
    private static final Logger LOGGER = Logger.getLogger(UserDetailsService.class.getName());
    private static final String DOUBLE_HYPHEN_FORMAT = " -- ";
    @Autowired
    RoleService roleService;
    @Autowired
	private UserReportService userReportService;
    @Autowired
	private PacmanConfigParamsService configParamsService;

    public List<UserDetails> getUsersWithAllDetails(String clientCode) {
        final String displayCodeOrName = configParamsService.getValue("pacman." + clientCode,
                GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value());
        return getUserDetails(clientCode, displayCodeOrName);
    }

    public List<UserDetails> getUsersWithAllDetailsForOptix(String clientCode) {
        return getUserDetails(clientCode, "Code");
    }

    private List<UserDetails> getUserDetails(String clientCode, String displayCodeOrName) {
        LOGGER.info("DataFeed :: Request received for getUsersWithAllDetails , ClientCode " + clientCode);
        final Map<String, String> rolesMap = populateRolesMap();
        final FilterDto userFilterDto = prepareFilterDto();
        LOGGER.info("DataFeed :: rolesMapping found  " + rolesMap.size());
        final List<UserRolePermissionDetailsDTO> userRolePermissionDetailsList = userReportService.getUserRolePermissionDetailsDTOS(userFilterDto, clientCode, rolesMap, Locale.ENGLISH, displayCodeOrName);

        if (CollectionUtils.isEmpty(userRolePermissionDetailsList)) {
            return Collections.<UserDetails>emptyList();
        }
        final List<UserDetails> userDataList = new ArrayList<>();
        userRolePermissionDetailsList.stream().forEach(userRolePermissionDetailsObj -> {
            UserDetails userData = new UserDetails();
            userData.setAuthGroupName(userRolePermissionDetailsObj.getAuthGroupName());
            userData.setAuthGroupRole(DOUBLE_HYPHEN_FORMAT.equals(userRolePermissionDetailsObj.getAuthGroupRole()) ? null : userRolePermissionDetailsObj.getAuthGroupRole());
            userData.setEmail(userRolePermissionDetailsObj.getEmail());
            userData.setUniqueUserId(userRolePermissionDetailsObj.getUniqueUserID());
            userData.setFirstName(userRolePermissionDetailsObj.getFirstName());
            userData.setLastName(userRolePermissionDetailsObj.getLastName());
            userData.setProperty(userRolePermissionDetailsObj.getProperty());
            userData.setPropertyRole(DOUBLE_HYPHEN_FORMAT.equals(userRolePermissionDetailsObj.getPropertyRole()) ? null : userRolePermissionDetailsObj.getPropertyRole());
            userData.setStatus(userRolePermissionDetailsObj.getStatus());
            userData.setCreatedDate(DOUBLE_HYPHEN_FORMAT.equals(userRolePermissionDetailsObj.getCreatedDate()) ? null : parseDate(userRolePermissionDetailsObj.getCreatedDate()));
            userData.setLastLoginDate(DOUBLE_HYPHEN_FORMAT.equals(userRolePermissionDetailsObj.getLastLogin()) ? null : parseDate(userRolePermissionDetailsObj.getLastLogin()));
            userData.setPropertyCode(userRolePermissionDetailsObj.getPropertyCode());
            userDataList.add(userData);
        });
        LOGGER.info("DataFeed :: collected number of records = " + userDataList.size());
        return userDataList;
    }

    private FilterDto prepareFilterDto() {
        final FilterDto userFilterDto = new FilterDto();
        userFilterDto.setAuthorizationGroupAllSelected(0);
        userFilterDto.setAuthorizationGroupSelectedItems(Collections.<Integer>emptySet());
        userFilterDto.setPropertiesAllSelected(0);
        userFilterDto.setPropertiesSelectedItems(Collections.<Integer>emptySet());
        userFilterDto.setRolesAllSelected(0);
        userFilterDto.setRolesSelectedItems(Collections.<String>emptySet());
        userFilterDto.setStatus(-1);//We want records with Status = Active or Inactive
        userFilterDto.setUsersAllSelected(1);
        userFilterDto.setUsersSelectedItems(Collections.<Integer>emptySet());
        userFilterDto.setIsUserExternal(0);
        userFilterDto.setUserDefindedDateFormat(DateUtil.DATE_TIME_FORMAT_WITH_DAY_AND_TIMEZONE);
        return userFilterDto;
    }

    private Map<String, String> populateRolesMap() {
        Set<Role> roles = roleService.getAllRoles(false);//we  want externalUser roles only
        return roles.stream().collect(Collectors.toMap(Role::getUniqueIdentifier, Role::getRoleName));
    }

    private Date parseDate(final String dateString) {
        try {
            return DateUtil.parseDate(dateString, DateUtil.DATE_TIME_FORMAT_WITH_DAY_AND_TIMEZONE);
        } catch (ParseException e) {
            LOGGER.error("Exception occurred in parsing dateString:=" + dateString, e);
        }
        return null;
    }

    public void setUserReportService(UserReportService userReportService) {
        this.userReportService = userReportService;
    }

    public void setRoleService(RoleService roleService) {
        this.roleService = roleService;
    }
}
