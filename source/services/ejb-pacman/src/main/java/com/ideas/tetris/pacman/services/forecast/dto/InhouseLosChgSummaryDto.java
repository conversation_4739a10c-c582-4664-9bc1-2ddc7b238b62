package com.ideas.tetris.pacman.services.forecast.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InhouseLosChgSummaryDto {
    private Integer mktSegId;
    private Integer accomTypeId;
    private LocalDate arrivalDt;
    private LocalDate captureDt;
    private Integer losIni;
    private Integer losUpd;
    private Integer mktUpd;
    private Integer accomTypeUpd;
    private Integer arrivals;
    private Double revIni;
    private Double revUpd;
}
