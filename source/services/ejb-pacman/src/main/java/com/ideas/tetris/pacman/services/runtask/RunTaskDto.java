package com.ideas.tetris.pacman.services.runtask;

import com.ideas.tetris.pacman.services.sasruntask.entity.RunTaskMacro;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;

import java.time.LocalDate;
import java.util.LinkedHashSet;
import java.util.Set;

public class RunTaskDto {

    private RunTaskMacro runTaskMacro;

    private Set<Property> properties;

    private boolean runAll;

    private LocalDate startDate;

    private LocalDate endDate;

    private String parameters;

    public RunTaskMacro getRunTaskMacro() {
        return runTaskMacro;
    }

    public void setRunTaskMacro(RunTaskMacro runTaskMacro) {
        this.runTaskMacro = runTaskMacro;
    }

    public Set<Property> getProperties() {
        if (properties == null) {
            return new LinkedHashSet<Property>();
        }
        return properties;
    }

    public void setProperties(Set<Property> propertyIds) {
        if (propertyIds == null) {
            this.properties = new LinkedHashSet<Property>();
        }
        this.properties = propertyIds;
    }

    public boolean getRunAll() {
        return runAll;
    }

    public void setRunAll(boolean runAll) {
        this.runAll = runAll;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getParameters() {
        return parameters;
    }

    public void setParameters(String parameters) {
        this.parameters = parameters;
    }
}
