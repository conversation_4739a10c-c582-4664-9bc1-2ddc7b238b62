package com.ideas.tetris.pacman.services.reports.dto;

import com.ideas.tetris.pacman.services.dateservice.dto.DateTimeParameter;

import java.io.Serializable;
import java.util.List;

@SuppressWarnings({"squid:S1948", "squid:S3776"})
public class ScheduleReportDTO implements Serializable {
    private int id;
    private String pagecode;
    private ReportParamsDTO reportParams;
    private String reportJasperURI;
    private String actualReportName;

    private transient Object reportParamsObject = new Object();

    private String name;
    private String description;
    private Boolean startScheduleForNow;
    private String scheduleStartDateTime;
    private DateTimeParameter scheduleCalStartDateTime;
    private int recurrenceInterval;
    private String recurrenceIntervalUnit;
    private String recipientMail;


    //these are parameters need for displaying scheduled report
    private String createdBy;
    private String createdOn;
    private String updatedBy;

    public Integer getCreatedByUserId() {
        return createdByUserId;
    }

    public void setCreatedByUserId(Integer createdByUserId) {
        this.createdByUserId = createdByUserId;
    }

    private Integer createdByUserId;
    private String updatedOn;
    private String lastRunDate;
    private String nextRunDate;

    private String propertyTimezone;

    private long jasperScheduleID;
    // we might need this in future
    private List<String> outputFormat;

    private String mailContent;

    private String ftpServerName;
    private String ftpFolderPath;
    private String ftpUserName;
    private String ftpPassword;
    private String ftpType;
    private int ftpPort;
    private Boolean emailDelivery;
    private String dayOfWeek;
    private String executionSchedule;

    public String getFtpServerName() {
        return ftpServerName;
    }

    public void setFtpServerName(String ftpServerName) {
        this.ftpServerName = ftpServerName;
    }

    public String getFtpFolderPath() {
        return ftpFolderPath;
    }

    public void setFtpFolderPath(String ftpFolderPath) {
        this.ftpFolderPath = ftpFolderPath;
    }

    public String getFtpUserName() {
        return ftpUserName;
    }

    public void setFtpUserName(String ftpUserName) {
        this.ftpUserName = ftpUserName;
    }

    public String getFtpPassword() {
        return ftpPassword;
    }

    public void setFtpPassword(String ftpPassword) {
        this.ftpPassword = ftpPassword;
    }

    public String getFtpType() {
        return ftpType;
    }

    public void setFtpType(String ftpType) {
        this.ftpType = ftpType;
    }

    public int getFtpPort() {
        return ftpPort;
    }

    public void setFtpPort(int ftpPort) {
        this.ftpPort = ftpPort;
    }

    public Boolean getEmailDelivery() {
        return emailDelivery;
    }

    public void setEmailDelivery(Boolean emailDelivery) {
        this.emailDelivery = emailDelivery;
    }

    public String getMailContent() {
        return mailContent;
    }

    public void setMailContent(String mailContent) {
        this.mailContent = mailContent;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }


    /**
     * This method returns the location where report is deployed on the server.
     * For example - "Public/Report/DataExtractReport"
     *
     * @return
     */
    public String getReportJasperURI() {
        return reportJasperURI;
    }

    public void setReportJasperURI(String reportJasperURI) {
        this.reportJasperURI = reportJasperURI;
    }

    /**
     * Returns the Report Parameter - Left Panel parameters
     *
     * @return
     */
    public ReportParamsDTO getReportParams() {
        return reportParams;
    }

    public void setReportParams(ReportParamsDTO reportParams) {
        this.reportParams = reportParams;
    }

    /**
     * Returns the schedule description
     *
     * @return
     */
    public String getDescription() {
        return description;
    }

    public void setDescription(String scheduleDesc) {
        this.description = scheduleDesc;
    }

    /**
     * Returns the actual report name - Name of the Report which is scheduled ..
     *
     * @return
     */
    public String getActualReportName() {
        return actualReportName;
    }

    public void setActualReportName(String actualReportName) {
        this.actualReportName = actualReportName;
    }

    public String getName() {
        return name;
    }

    public void setName(String scheduleName) {
        this.name = scheduleName;
    }

    /**
     * Date and time since when the scheduling is expected to start.
     */
    public String getScheduleStartDateTime() {
        return scheduleStartDateTime;
    }

    public void setScheduleStartDateTime(String scheduleStartDateTime) {
        this.scheduleStartDateTime = scheduleStartDateTime;
    }

    /**
     * Email ids for users selected while scheduling the report.
     */
    public String getRecipientMail() {
        return recipientMail;
    }

    public void setRecipientMail(String recipientMail) {
        this.recipientMail = recipientMail;
    }

    /**
     * Whether to run the report & mail now
     * OR run & mail later
     *
     * @param startScheduleForNow
     */

    public Boolean getStartScheduleForNow() {
        return startScheduleForNow;
    }

    public void setStartScheduleForNow(Boolean startScheduleForNow) {
        this.startScheduleForNow = startScheduleForNow;
    }

    /**
     * Schedule time - Calendar time..
     *
     * @param scheduleCalStartDateTime
     */
    public DateTimeParameter getScheduleCalStartDateTime() {
        return scheduleCalStartDateTime;
    }

    public void setScheduleCalStartDateTime(DateTimeParameter scheduleCalStartDateTime) {
        this.scheduleCalStartDateTime = scheduleCalStartDateTime;
    }

    /**
     * Repeat after every field
     *
     * @return
     */
    public int getRecurrenceInterval() {
        return recurrenceInterval;
    }

    public void setRecurrenceInterval(int recurrenceInterval) {
        this.recurrenceInterval = recurrenceInterval;
    }

    /**
     * Repeat after DAYS Or HOURS Or WEEK
     *
     * @return
     */
    public String getRecurrenceIntervalUnit() {
        return recurrenceIntervalUnit;
    }

    public void setRecurrenceIntervalUnit(String recurrenceIntervalUnit) {
        this.recurrenceIntervalUnit = recurrenceIntervalUnit;
    }

    /**
     * Output format of report
     *
     * @return
     */
    public List<String> getOutputFormat() {
        return outputFormat;
    }

    public void setOutputFormat(List<String> outputFormat) {
        this.outputFormat = outputFormat;
    }

    public Object getReportParamsObject() {
        return reportParamsObject;
    }

    public void setReportParamsObject(Object paramObject) {
        reportParamsObject = paramObject;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(String createdon) {
        this.createdOn = createdon;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(String updatedOn) {
        this.updatedOn = updatedOn;
    }

    public String getLastRunDate() {
        return lastRunDate;
    }

    public void setLastRunDate(String lastRunDate) {
        this.lastRunDate = lastRunDate;
    }

    public String getNextRunDate() {
        return nextRunDate;
    }

    public void setNextRunDate(String nextRunDate) {
        this.nextRunDate = nextRunDate;
    }

    /**
     * @return the pagecode
     */
    public String getPagecode() {
        return pagecode;
    }

    /**
     * @param pagecode the pagecode to set
     */
    public void setPagecode(String pagecode) {
        this.pagecode = pagecode;
    }

    /**
     * @return the jasperScheduleID
     */
    public long getJasperScheduleID() {
        return jasperScheduleID;
    }

    /**
     * @param jasperScheduleID the jasperScheduleID to set
     */
    public void setJasperScheduleID(long jasperScheduleID) {
        this.jasperScheduleID = jasperScheduleID;
    }

    public String getPropertyTimezone() {
        return propertyTimezone;
    }

    public void setPropertyTimezone(String propertyTimezone) {
        this.propertyTimezone = propertyTimezone;
    }

    public String getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(String dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public String getExecutionSchedule() {
        return executionSchedule;
    }

    public void setExecutionSchedule(String executionSchedule) {
        this.executionSchedule = executionSchedule;
    }
}
