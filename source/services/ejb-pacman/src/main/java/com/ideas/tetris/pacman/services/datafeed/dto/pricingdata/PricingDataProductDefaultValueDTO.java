package com.ideas.tetris.pacman.services.datafeed.dto.pricingdata;

import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateOffset;

import java.io.Serializable;
import java.math.BigDecimal;

public class PricingDataProductDefaultValueDTO implements Serializable {

    private String productName;

    private String roomClassCode;

    private String daysToArrival;

    private String adjustmentType;

    private BigDecimal sundayFloorValue;

    private BigDecimal sundayCeilingValue;

    private BigDecimal mondayFloorValue;

    private BigDecimal mondayCeilingValue;

    private BigDecimal tuesdayFloorValue;

    private BigDecimal tuesdayCeilingValue;

    private BigDecimal wednesdayFloorValue;

    private BigDecimal wednesdayCeilingValue;

    private BigDecimal thursdayFloorValue;

    private BigDecimal thursdayCeilingValue;

    private BigDecimal fridayFloorValue;

    private BigDecimal fridayCeilingValue;

    private BigDecimal saturdayFloorValue;

    private BigDecimal saturdayCeilingValue;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getRoomClassCode() {
        return roomClassCode;
    }

    public void setRoomClassCode(String roomClassCode) {
        this.roomClassCode = roomClassCode;
    }

    public String getDaysToArrival() {
        return daysToArrival;
    }

    public void setDaysToArrival(String daysToArrival) {
        this.daysToArrival = daysToArrival;
    }

    public String getAdjustmentType() {
        return adjustmentType;
    }

    public void setAdjustmentType(String adjustmentType) {
        this.adjustmentType = adjustmentType;
    }

    public BigDecimal getSundayFloorValue() {
        return sundayFloorValue;
    }

    public void setSundayFloorValue(BigDecimal sundayFloorValue) {
        this.sundayFloorValue = sundayFloorValue;
    }

    public BigDecimal getSundayCeilingValue() {
        return sundayCeilingValue;
    }

    public void setSundayCeilingValue(BigDecimal sundayCeilingValue) {
        this.sundayCeilingValue = sundayCeilingValue;
    }

    public BigDecimal getMondayFloorValue() {
        return mondayFloorValue;
    }

    public void setMondayFloorValue(BigDecimal mondayFloorValue) {
        this.mondayFloorValue = mondayFloorValue;
    }

    public BigDecimal getMondayCeilingValue() {
        return mondayCeilingValue;
    }

    public void setMondayCeilingValue(BigDecimal mondayCeilingValue) {
        this.mondayCeilingValue = mondayCeilingValue;
    }

    public BigDecimal getTuesdayFloorValue() {
        return tuesdayFloorValue;
    }

    public void setTuesdayFloorValue(BigDecimal tuesdayFloorValue) {
        this.tuesdayFloorValue = tuesdayFloorValue;
    }

    public BigDecimal getTuesdayCeilingValue() {
        return tuesdayCeilingValue;
    }

    public void setTuesdayCeilingValue(BigDecimal tuesdayCeilingValue) {
        this.tuesdayCeilingValue = tuesdayCeilingValue;
    }

    public BigDecimal getWednesdayFloorValue() {
        return wednesdayFloorValue;
    }

    public void setWednesdayFloorValue(BigDecimal wednesdayFloorValue) {
        this.wednesdayFloorValue = wednesdayFloorValue;
    }

    public BigDecimal getWednesdayCeilingValue() {
        return wednesdayCeilingValue;
    }

    public void setWednesdayCeilingValue(BigDecimal wednesdayCeilingValue) {
        this.wednesdayCeilingValue = wednesdayCeilingValue;
    }

    public BigDecimal getThursdayFloorValue() {
        return thursdayFloorValue;
    }

    public void setThursdayFloorValue(BigDecimal thursdayFloorValue) {
        this.thursdayFloorValue = thursdayFloorValue;
    }

    public BigDecimal getThursdayCeilingValue() {
        return thursdayCeilingValue;
    }

    public void setThursdayCeilingValue(BigDecimal thursdayCeilingValue) {
        this.thursdayCeilingValue = thursdayCeilingValue;
    }

    public BigDecimal getFridayFloorValue() {
        return fridayFloorValue;
    }

    public void setFridayFloorValue(BigDecimal fridayFloorValue) {
        this.fridayFloorValue = fridayFloorValue;
    }

    public BigDecimal getFridayCeilingValue() {
        return fridayCeilingValue;
    }

    public void setFridayCeilingValue(BigDecimal fridayCeilingValue) {
        this.fridayCeilingValue = fridayCeilingValue;
    }

    public BigDecimal getSaturdayFloorValue() {
        return saturdayFloorValue;
    }

    public void setSaturdayFloorValue(BigDecimal saturdayFloorValue) {
        this.saturdayFloorValue = saturdayFloorValue;
    }

    public BigDecimal getSaturdayCeilingValue() {
        return saturdayCeilingValue;
    }

    public void setSaturdayCeilingValue(BigDecimal saturdayCeilingValue) {
        this.saturdayCeilingValue = saturdayCeilingValue;
    }

    public PricingDataProductDefaultValueDTO() {
    }

    public PricingDataProductDefaultValueDTO(ProductRateOffset pro) {

        this.productName = pro.getProduct().getName();
        this.roomClassCode = pro.getAccomClass() != null ? pro.getAccomClass().getCode() : "";

        if (pro.getAgileRatesDTARange() != null) {
            String minDTA = pro.getAgileRatesDTARange().getMinDaysToArrival().toString();
            String maxDTA = pro.getAgileRatesDTARange().getMaxDaysToArrival() == null ? "Up" : pro.getAgileRatesDTARange().getMaxDaysToArrival().toString();
            this.daysToArrival = minDTA + " - " + maxDTA;
        }
        this.adjustmentType = AgileRatesOffsetMethod.PERCENTAGE.equals(pro.getOffsetMethod()) ? "Percentage" : pro.getOffsetMethod().getCaption();
        this.sundayCeilingValue = pro.getSundayOffsetValueCeiling();
        this.sundayFloorValue = pro.getSundayOffsetValueFloor();
        this.mondayFloorValue = pro.getMondayOffsetValueFloor();
        this.mondayCeilingValue = pro.getMondayOffsetValueCeiling();
        this.tuesdayFloorValue = pro.getTuesdayOffsetValueFloor();
        this.tuesdayCeilingValue = pro.getTuesdayOffsetValueCeiling();
        this.wednesdayFloorValue = pro.getWednesdayOffsetValueFloor();
        this.wednesdayCeilingValue = pro.getWednesdayOffsetValueCeiling();
        this.thursdayFloorValue = pro.getThursdayOffsetValueFloor();
        this.thursdayCeilingValue = pro.getThursdayOffsetValueCeiling();
        this.fridayFloorValue = pro.getFridayOffsetValueFloor();
        this.fridayCeilingValue = pro.getFridayOffsetValueCeiling();
        this.saturdayFloorValue = pro.getSaturdayOffsetValueFloor();
        this.saturdayCeilingValue = pro.getSaturdayOffsetValueCeiling();
    }
}
