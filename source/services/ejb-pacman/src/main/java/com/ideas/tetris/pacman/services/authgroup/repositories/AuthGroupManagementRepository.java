package com.ideas.tetris.pacman.services.authgroup.repositories;

import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.services.daoandentities.entity.AuthorizationGroup;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.List;

import static com.ideas.tetris.pacman.services.security.UserAuthGroupRole.DELETE_BY_AUTH_GROUP_ID;
import static com.ideas.tetris.platform.common.crudservice.QueryParameter.with;
import static java.util.Collections.singletonList;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class AuthGroupManagementRepository {

    private static final org.apache.log4j.Logger LOGGER = Logger.getLogger(AuthGroupManagementRepository.class.getName());

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;

    public AuthorizationGroup getAuthorizationGroupById(int authGroupId) {
        return globalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_ID, with("id", authGroupId).parameters());
    }

    public AuthorizationGroup merge(AuthorizationGroup groupWithChangedValues) {
        return globalCrudService.getEntityManager().merge(groupWithChangedValues);
    }

    public AuthorizationGroup save(AuthorizationGroup group) {
        return globalCrudService.save(group);
    }

    public void delete(int authGroupId) {
        globalCrudService.executeUpdateByNamedQuery(DELETE_BY_AUTH_GROUP_ID, with("authGroupId", authGroupId).parameters());
        globalCrudService.delete(AuthorizationGroup.class, authGroupId);
    }

    public List<Integer> syncUsersAndProperties(int authGroupId, List<Integer> properties) {
        properties = isEmpty(properties) ? singletonList(-1) : properties;
        String listOfAuthorizedProperties = StringUtils.join(properties, ',');
        LOGGER.info("GETTING AFFECTED USERS FOR AUTH GROUP : " + authGroupId);
        List<Integer> affectedUsersList = globalCrudService.findByNativeQuery("exec dbo.get_affected_users_for_authorized_group " + authGroupId + "," + "'" + listOfAuthorizedProperties + "'", null);
        LOGGER.info("DONE GETTING AFFECTED USERS FOR AUTH GROUP : " + authGroupId);
        return affectedUsersList;
    }
}
