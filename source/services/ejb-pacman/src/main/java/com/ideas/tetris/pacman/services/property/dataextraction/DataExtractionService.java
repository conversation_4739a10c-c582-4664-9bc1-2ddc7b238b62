package com.ideas.tetris.pacman.services.property.dataextraction;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.sas.core.SASDatasetExtractionRequest;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.archive.ArchiveService;
import com.ideas.tetris.pacman.services.datasourceswitching.DataSourceCacheBean;
import com.ideas.tetris.pacman.services.email.EmailService;
import com.ideas.tetris.pacman.services.property.ExtractDetailsServiceLocal;
import com.ideas.tetris.pacman.services.property.dto.CrsExtractFile;
import com.ideas.tetris.pacman.services.property.dto.ExtractType;
import com.ideas.tetris.pacman.services.property.dto.Extractable;
import com.ideas.tetris.pacman.services.property.dto.PacmanExtractForContextFileFilter;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackDataBuilder;
import com.ideas.tetris.platform.common.businessservice.async.AsyncJobCallback;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.util.zip.ZipDirectory;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.DBLoc;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.SASFileLoc;
import com.ideas.tetris.platform.services.property.dataextraction.DataExtractionRequest;
import com.ideas.tetris.platform.services.sas.log.SASNodeLocator;
import org.apache.commons.io.FileUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.Future;
import java.util.regex.Matcher;

import static com.ideas.tetris.platform.common.job.JobParameterKey.*;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DATE_TIME_FORMAT;

/*
 * Currently copying all targeted files to common folder to facilitate zipping and provide folder structure
 * in zip file. Potentially cost of copying files over ease of compression could push to alternate solution
 * where we zip from native locations. Note - they are not always in a clean structure natively.
 */
@Transactional(propagation = Propagation.NOT_SUPPORTED, timeout = 240 * 60)
@Justification("DataExtraction can take time to place data on FTP, we need to ensure that there are no transaction timeouts.")
@Component
public class DataExtractionService extends ExtractionService {
    private static final Logger LOGGER = Logger.getLogger(DataExtractionService.class);

    static final String RESPONSE_EXTRACT_DB = "You should receive an email with the results when complete";
    static final String ERROR_INTERNAL_ONLY = "Internal eyes only";
    // Date in source filenames
    private static final String ZIPPED_EXTRACT_FILE = "DataExtracts_PropCode-{0}_PropId-{1}_Time-{2}.zip";

    @Autowired
	private DatabaseExtractionService dbExtractionService;
    @Autowired
	private EmailService emailService;
    @Autowired
	private ExtractDetailsServiceLocal extractDetailsService;
    @Autowired
	private DataSourceCacheBean dataSourceCache;
    @Autowired
	private SASNodeLocator sasNodeLocator;
    @Autowired
	private JobServiceLocal jobService;
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
	private ArchiveService archiveService;


    // Exposed for testing
    public String getT2snapExtractPath() {
        return SystemConfig.getExtractArchiveFolder();
    }

    public void initiateDataExtraction(String emailAddress, Integer propertyId, int extractTypes, LocalDate startDate, LocalDate endDate) {

        triggerJob(emailAddress, propertyId, extractTypes, startDate, endDate);
    }

    private void triggerJob(String emailAddress, Integer propertyId, int extractTypes, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> parameters = buildJobParameters(emailAddress, propertyId, extractTypes, startDate, endDate);
        jobService.startJob(JobName.DataExtractionUtilityJob, parameters);
    }

    private Map<String, Object> buildJobParameters(String emailAddress, Integer propertyId, int extractTypes, LocalDate startDate, LocalDate endDate) {
        return MapBuilder.with(EMAIL, emailAddress)
                .and(PROPERTY_ID, propertyId)
                .and(EXTRACT_TYPES, extractTypes)
                .and(DATE_START, startDate)
                .and(DATE_END, endDate)
                .and(DATE, DateUtil.formatDate(DateUtil.getCurrentDate(), DATE_TIME_FORMAT))
                .and(USER_ID, PacmanWorkContextHelper.getUserId())
                .get();
    }

    @Async
    @AsyncJobCallback
    public Future<List<ZipDirectory>> startDataExtraction(WorkContextType workContext, JobStepContext jobStepContext, DataExtractionRequest request) {
        LOGGER.info(getLogPreface() + " executing data extraction request : " + request.toPipeDelimited());

        final List<ZipDirectory> zipDirectories = getZipDirectories(request.getExtractTypes(), workContext, JavaLocalDateUtils.toJodaLocalDate(request.getStartDate()), JavaLocalDateUtils.toJodaLocalDate(request.getEndDate()));
        return AsyncCallbackDataBuilder.buildFuture(zipDirectories);
    }

    @Async
    @AsyncJobCallback    public Future<Boolean> ftpAndEmailResults(JobStepContext jobStepContext, DataExtractionRequest request, long exactMomemt, WorkContextType workContext, List<ZipDirectory> zipDirectories) {
        int requestedFileTypeCount = zipDirectories.size();
        checkForEmptyDatasets(zipDirectories);
        List<String> missingFileWarnings = checkForMissingFiles(zipDirectories);
        if (missingFileWarnings.size() == requestedFileTypeCount) {
            // finally will be executed even with return (there is a test confirming)
            fireOffEmail(request.getEmail(), workContext, missingFileWarnings, null, false);
            // No files at all, exist...gracefully
            return AsyncCallbackDataBuilder.buildFuture(false);
        }
        LOGGER.info(getLogPreface() + "done defining zip folders. Folder count: " + (requestedFileTypeCount - missingFileWarnings.size()));

        LOGGER.info(getLogPreface() + "zip start");
        String ftpFile = getSasDataSetZipFile(workContext.getPropertyCode(), workContext.getPropertyId().toString(), exactMomemt);
        File zippedFile = createZipFile(ftpFile, zipDirectories,
                0 != (request.getExtractTypes() & ExtractType.SAS_ANALYTIC_DATA_SET_EXCLUDE_PACE_DATA.getValue()));
        LOGGER.info("zipped file location: " + zippedFile.getPath());

        boolean wasProcessSuccessful1 = null != zippedFile && cleanUpTempDirectories(zipDirectories, zippedFile);
        String ftpFileLink = wasProcessSuccessful1 ? ftpZipFile(request, ftpFile, zippedFile) : null;
        boolean wasProcessSuccessful = (null != ftpFileLink);
        fireOffEmail(request.getEmail(), workContext, missingFileWarnings, ftpFileLink, wasProcessSuccessful);
        return AsyncCallbackDataBuilder.buildFuture(wasProcessSuccessful);
    }

    private static void checkForEmptyDatasets(List<ZipDirectory> zipDirectories) {
        for (ZipDirectory zipDirectory: zipDirectories) {
            if (ExtractType.SAS_DATASETS_COMBINED.getFolderName().equalsIgnoreCase(zipDirectory.getParentDirectory())) {
                if (null == zipDirectory.getDirectory()) {
                    zipDirectory.setIsEmpty(true);
                } else {
                    File[] files = zipDirectory.getDirectory().listFiles();
                    if (null != files && 0 != files.length) {
                        zipDirectory.setIsEmpty(false);
                    }
                }
                break;
            }
        }
    }

    private boolean cleanUpTempDirectories(List<ZipDirectory> zipDirectories, File zippedFile) {
        boolean wasSuccessful = true;
        try {
            for (ZipDirectory zipDirectory : zipDirectories) {
                if (zipDirectory.getIsTemporary()) {
                    FileUtils.deleteDirectory(zipDirectory.getDirectory());
                }
            }
        } catch (IOException e) {
            LOGGER.error(getLogPreface() + "unable delete local copy of extarct files", e);
            wasSuccessful = false;
            // Clean up the local zip file. Unfortunately we have to create locally before ftp'ing
            if (!zippedFile.delete()) {
                LOGGER.error("Failed to delete local extract zip file after failed temp folder cleanup: " +
                        zippedFile.getAbsolutePath());
            }
        }
        return wasSuccessful;
    }

    public String ftpZipFile(DataExtractionRequest request, String ftpFile, File zippedFile) {
        String ftpFileLink = null;
        try {
            createFtpDirectoryIfNotExists(request.getEmail());
            ftpFileLink = ftpZipFile(ftpFile, request.getEmail());
        } catch (IOException e) {
            LOGGER.error(getLogPreface() + "unable to create destination ftp folder", e);
        } finally {
            // Clean up the local zip file. Unfortunately we have to create locally before ftp'ing
            if (!zippedFile.delete()) {
                LOGGER.error("Failed to delete local extract zip file after failed to create ftp folder: " +
                        zippedFile.getAbsolutePath());
            }
        }
        return ftpFileLink;
    }

    private List<ZipDirectory> getZipDirectories(int extractTypes, WorkContextType workContext,
                                                 LocalDate startDate, LocalDate endDate) {
        List<ZipDirectory> zipDirectories = new ArrayList<>();
        getCombinedSASDatasetZipDirectory(extractTypes, zipDirectories);
        getCrsCustomerDataZipDirectory(extractTypes, workContext, startDate, endDate, zipDirectories);
        getWebRateExtractsZipDirectory(extractTypes, workContext, startDate, endDate, zipDirectories);
        getT2SnapZipDirectory(extractTypes, workContext, startDate, endDate, zipDirectories);
        if (skipScheduleReportTableToggleEnabled()) {
            getLDBTenantDBZipDirectory(extractTypes, workContext, zipDirectories);
        } else {
            getTenantDBZipDirectory(extractTypes, workContext, zipDirectories);
        }
        getLDBZipDirectory(extractTypes, workContext, zipDirectories);
        getGlobalConfigParametersDirectory(extractTypes, workContext.getPropertyId(), zipDirectories);

        for (ZipDirectory zipDirectory : zipDirectories) {
            checkForFiles(zipDirectory);
        }
        return zipDirectories;
    }

    private void getCombinedSASDatasetZipDirectory(int extractTypes, List<ZipDirectory> zipDirectories) {
        if (0 != (extractTypes & ExtractType.SAS_ANALYTIC_DATA_SET.getValue()) || 0 != (extractTypes & ExtractType.SAS_RATCHET_DATA_SET.getValue())) {
            zipDirectories.add(new ZipDirectory(
                    new File(getTempZipDirectory() + "/" +
                            ExtractType.SAS_DATASETS_COMBINED.getFolderName() + getNowAsMilliseconds()),
                    ExtractType.SAS_DATASETS_COMBINED.getFolderName(),
                    true)
            );
        }
    }

    private void getCrsCustomerDataZipDirectory(int extractTypes, WorkContextType workContext, LocalDate startDate, LocalDate endDate, List<ZipDirectory> zipDirectories) {
        if (0 != (extractTypes & ExtractType.CRS_CUSTOMER_DATA.getValue())) {
            File crsFolder = copyExtractFilesLocally(workContext, ExtractType.CRS_CUSTOMER_DATA, startDate, endDate);
            LOGGER.info(getLogPreface() + "crs extracts copy complete");
            zipDirectories.add(new ZipDirectory(crsFolder, ExtractType.CRS_CUSTOMER_DATA.getFolderName(), true));
        }
    }

    private void getWebRateExtractsZipDirectory(int extractTypes, WorkContextType workContext, LocalDate startDate, LocalDate endDate, List<ZipDirectory> zipDirectories) {
        if (0 != (extractTypes & ExtractType.RSS_WEB_RATE.getValue())) {
            File rssFolder = copyExtractFilesLocally(workContext, ExtractType.RSS_WEB_RATE, startDate, endDate);
            LOGGER.info(getLogPreface() + "rss extracts copy complete");
            zipDirectories.add(new ZipDirectory(rssFolder, ExtractType.RSS_WEB_RATE.getFolderName(), true));
        }
    }

    private void getT2SnapZipDirectory(int extractTypes, WorkContextType workContext, LocalDate startDate, LocalDate endDate, List<ZipDirectory> zipDirectories) {
        if (0 != (extractTypes & ExtractType.T2SNAP_POST_ETL.getValue())) {
            File t2snapFolder = copyT2snapFilesLocally(workContext, startDate, endDate);
            LOGGER.info(getLogPreface() + "t2snap extracts copy complete");
            zipDirectories.add(new ZipDirectory(t2snapFolder, ExtractType.T2SNAP_POST_ETL.getFolderName(), true));
        }
    }

    private void getTenantDBZipDirectory(int extractTypes, WorkContextType workContext, List<ZipDirectory> zipDirectories) {
        if (0 != (extractTypes & ExtractType.DB_TENANT.getValue())) {
            long millis = Calendar.getInstance().getTimeInMillis();
            File dbFolder = getTempExtractFolder(ExtractType.DB_TENANT, millis);
            String dbBackupPath = getOsBasedTempExtractFolderPath(ExtractType.DB_TENANT, millis);
            // If want to remove some complexity/coupling, could go directly to globalCrudService
            // Property find Property class by propertyId. DBLoc is property getDbLoc
            DBLoc dbLoc = dataSourceCache.getDBLoc(workContext.getPropertyId());
            try {
                dbExtractionService.backupDatabase(dbBackupPath, dbLoc.getDbName());
            } catch (Exception e) {
                LOGGER.error(getLogPreface() + "unable to backup database: " + dbLoc.getDbName(), e);
            }
            LOGGER.info(getLogPreface() + "database backup complete");
            zipDirectories.add(new ZipDirectory(dbFolder, ExtractType.DB_TENANT.getFolderName(), true));
        }
    }

    private void getLDBZipDirectory(int extractTypes, WorkContextType workContext, List<ZipDirectory> zipDirectories) {
        if (0 != (extractTypes & ExtractType.LIMITED_DB_TENANT.getValue())) {
            boolean excludeOperaTables = 0 != (extractTypes & ExtractType.LIMITED_DB_TENANT_EXCLUDE_OPERA.getValue());
            boolean excludePaceTables = 0 != (extractTypes & ExtractType.LIMITED_DB_TENANT_EXCLUDE_PACE.getValue());
            boolean excludeRevenueStreamTables = 0 != (extractTypes & ExtractType.LIMITED_DB_TENANT_EXCLUDE_REVENUE_STREAM.getValue());

            File dbFolder = getTempExtractFolder(ExtractType.LIMITED_DB_TENANT, Calendar.getInstance().getTimeInMillis());
            // If want to remove some complexity/coupling, could go directly to globalCrudService
            // Property find Property class by propertyId. DBLoc is property getDbLoc
            DBLoc dbLoc = dataSourceCache.getDBLoc(workContext.getPropertyId());
            LOGGER.info(getLogPreface() + "limited database backup start");
            try {
                dbExtractionService.extractLimitedDatabaseData(dbFolder.getAbsolutePath(), dbLoc, excludeOperaTables, excludePaceTables, excludeRevenueStreamTables);
            } catch (Exception e) {
                LOGGER.error(getLogPreface() + "unable to extract database data : " + dbLoc.getDbName(), e);
            }
            LOGGER.info(getLogPreface() + "limited database backup complete");
            zipDirectories.add(new ZipDirectory(dbFolder, ExtractType.LIMITED_DB_TENANT.getFolderName(), true));
        }
    }

    private void getLDBTenantDBZipDirectory(int extractTypes, WorkContextType workContext, List<ZipDirectory> zipDirectories) {
        if (0 != (extractTypes & ExtractType.DB_TENANT.getValue())) {
            long millis = Calendar.getInstance().getTimeInMillis();
            File dbFolder = getTempExtractFolder(ExtractType.DB_TENANT, millis);
            String dbBackupPath = getOsBasedTempExtractFolderPath(ExtractType.DB_TENANT, millis);
            DBLoc dbLoc = dataSourceCache.getDBLoc(workContext.getPropertyId());
            try {
                dbExtractionService.extractLimitedDatabaseData(dbBackupPath, dbLoc, false, false, false);
            } catch (Exception e) {
                LOGGER.error(getLogPreface() + "unable to backup Limited Tenant database: " + dbLoc.getDbName(), e);
            }
            LOGGER.info(getLogPreface() + "Limited Tenant database backup complete");
            zipDirectories.add(new ZipDirectory(dbFolder, ExtractType.DB_TENANT.getFolderName(), true));
        }

    }

    protected void getGlobalConfigParametersDirectory(int extractTypes, Integer propertyID, List<ZipDirectory> zipDirectories) {
        if (0 != (extractTypes & ExtractType.GLOBAL_CONFIG_PARAMS.getValue())) {
            File dbFolder = getTempExtractFolder(ExtractType.GLOBAL_CONFIG_PARAMS, Calendar.getInstance().getTimeInMillis());
            // If want to remove some complexity/coupling, could go directly to globalCrudService
            // Property find Property class by propertyId. DBLoc is property getDbLoc
            DBLoc dbLoc = dataSourceCache.getDBLoc(propertyID);
            LOGGER.info(getLogPreface() + "global config sql backup start");
            try {
                dbExtractionService.extractGlobalConfigParameters(dbFolder.getAbsolutePath(), dbLoc);
            } catch (Exception e) {
                LOGGER.error(getLogPreface() + "unable to create global config sql: " + dbLoc.getDbName(), e);
            }
            LOGGER.info(getLogPreface() + "global config sql creation backup complete");
            zipDirectories.add(new ZipDirectory(dbFolder, ExtractType.GLOBAL_CONFIG_PARAMS.getFolderName(), true));
        }
    }

    private List<String> checkForMissingFiles(List<ZipDirectory> zipDirectories) {
        List<String> missingFileWarnings = new ArrayList<>();
        Iterator<ZipDirectory> iterator = zipDirectories.iterator();
        while (iterator.hasNext()) {
            ZipDirectory zipDirectory = iterator.next();
            if (zipDirectory.getIsEmpty()) {
                try {
                    FileUtils.deleteDirectory(zipDirectory.getDirectory());
                } catch (IOException e) {
                    LOGGER.error(getLogPreface() + "unable to delete temp directory: " + zipDirectory.getDirectory().getAbsolutePath(), e);
                }
                String fileDescription = getExtractTypeDescription(zipDirectory.getParentDirectory());
                iterator.remove();
                missingFileWarnings.add("There were no " + fileDescription);
                LOGGER.info(getLogPreface() + "no " + fileDescription + " found");
            }
        }

        return missingFileWarnings;
    }

    private String getExtractTypeDescription(String folderName) {
        String description = "";
        for (ExtractType extractType : ExtractType.values()) {
            if (folderName.startsWith(extractType.getFolderName())) {
                description = extractType.getDescription();
                break;
            }
        }
        return description;
    }

    private void checkForFiles(ZipDirectory zipDirectory) {
        if (null == zipDirectory.getDirectory()) {
            zipDirectory.setIsEmpty(true);
        } else {
            File[] files = zipDirectory.getDirectory().listFiles();
            if (null == files || 0 == files.length) {
                zipDirectory.setIsEmpty(true);
            }
        }
    }

    @VisibleForTesting
    protected File copyExtractFilesLocally(WorkContextType workContext, ExtractType extractType,
                                           LocalDate startDate, LocalDate endDate) {
        File folder = new File(getTempZipDirectory() + "/" + extractType.getFolderName() + getNowAsMilliseconds());
        if (!folder.mkdir()) {
            LOGGER.error("Failed to create temp folder for zipping files locally: " + folder.getAbsolutePath());
        }

        try {
            Extractable extractable = extractType == ExtractType.CRS_CUSTOMER_DATA ?
                    extractDetailsService.getExtractDetailsWithFilePaths(workContext.getPropertyId()) :
                    extractDetailsService.getWebRateExtractDetailsWithFilePaths(workContext.getPropertyId());
            extractable.copyIncomingExtractsToDirectory(startDate.toDate(), endDate.toDate(), folder);
            if (extractType == ExtractType.CRS_CUSTOMER_DATA && isCloudEnabled(workContext)) {
                downloadAndCopyExtract(startDate, endDate, folder, extractable, pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_S3_REFRESHABLE_CONNECTION.getParameterName(),
                        workContext.getClientCode(), workContext.getPropertyCode()));
            } else {
                extractable.copyArchivedExtractsToDirectory(startDate.toDate(), endDate.toDate(), folder);
            }
        } catch (IOException e) {
            logExtractCopyException(extractType.getDescription(), e);
            folder = null;
        }
        return folder;
    }

    private void downloadAndCopyExtract(LocalDate startDate, LocalDate endDate, File folder, Extractable extractable, boolean useS3RefreshableConnection) throws IOException {
        List<File> downloadedExtracts = downloadExtractsFromCloud(extractable, startDate.toDate(), endDate.toDate(), useS3RefreshableConnection);
        extractable.copyArchivedExtractsToDirectory(startDate.toDate(), endDate.toDate(), folder);
        downloadedExtracts.forEach(FileUtils::deleteQuietly);
    }

    private List<File> downloadExtractsFromCloud(Extractable extractable, Date startDate, Date endDate, boolean useS3RefreshableConnection) {
        List<File> downloadedExtracts = new ArrayList<>();
        for (File extract : extractable.getArchivedExtracts()) {
            CrsExtractFile crsExtractFile = new CrsExtractFile(extract.getPath());
            Date extractDate = crsExtractFile.getDate();
            if (extractDate.before(startDate) || extractDate.after(endDate)) {
                continue;
            }
            if (!extract.exists()) {
                downloadedExtracts.add(archiveService.downloadExtract(extract.getAbsolutePath(), useS3RefreshableConnection));
            }
        }
        return downloadedExtracts;
    }

    public boolean isCloudEnabled(WorkContextType workContext) {
        return pacmanConfigParamsService.getBooleanParameterValue(
                PreProductionConfigParamName.USE_S3_FOR_ARCHIVE_EXTRACTS.getParameterName(),
                workContext.getClientCode(),
                workContext.getPropertyCode());
    }

    /*
     * Always of structure root\yyyy_mm\client\propCode\file(s)
     */
    private File copyT2snapFilesLocally(WorkContextType workContext, LocalDate startDate, LocalDate endDate) {
        File destinationFolder = new File(getTempZipDirectory() + "/" +
                ExtractType.T2SNAP_POST_ETL.getFolderName() + getNowAsMilliseconds());
        if (destinationFolder.mkdir()) {
            LOGGER.error("Failed to create folder to store t2snap files: " + destinationFolder.getAbsolutePath());
        }

        try {
            List<File> allT2snapFiles = getFilesRecursively(new File(getT2snapExtractPath()));
            FileFilter t2snapFileFilter = new PacmanExtractForContextFileFilter(startDate, endDate, workContext);

            for (File t2snapFile : allT2snapFiles) {
                if (t2snapFileFilter.accept(t2snapFile)) {
                    FileUtils.copyFile(t2snapFile, new File(destinationFolder.getCanonicalPath() +
                            "/" + t2snapFile.getName()));
                }
            }
        } catch (IOException e) {
            logExtractCopyException(ExtractType.T2SNAP_POST_ETL.getDescription(), e);
            destinationFolder = null;
        }
        return destinationFolder;
    }

    private void logExtractCopyException(String extractType, Exception e) {
        LOGGER.warn(getLogPreface() + "unable to copy " + extractType + " to common temp folder for compression", e);
    }

    /*
     * The link is no longer a hyperlink to the file as the ftp server is locked down and we would have to
     * embed the username:password@ in the url to get it to resolve - and we do not want to expose that
     * Note: The SAS squid proxy seems to kibosh passing in the username to force challenge for password
     *
     * Link now just a string to the ftp folder
     */
    private void fireOffEmail(String emailAddress, WorkContextType workContext, List<String> warnings,
                              String ftpFileLink, boolean wasProcessSuccessful) {
        String rawBody = wasProcessSuccessful ? SystemConfig.getEmailSasDataSetsBodySuccess() :
                SystemConfig.getEmailSasDataSetsBodyFailure();
        ArrayList<Object> values = new ArrayList<>();
        values.add(workContext.getPropertyCode());
        values.add(workContext.getPropertyId().toString());
        if (wasProcessSuccessful) {
            values.add(SystemConfig.getFtpServer());
            values.add(SystemConfig.getFtpServerUsernameCareUser());
            int firstPathFileSeparator = ftpFileLink.indexOf('/');
            int lastPathFileSeparator = ftpFileLink.lastIndexOf('/');
            values.add(ftpFileLink.substring(firstPathFileSeparator, lastPathFileSeparator));
            values.add(ftpFileLink.substring(lastPathFileSeparator + 1));
        }
        StringBuilder body = new StringBuilder(MessageFormat.format(rawBody, values.toArray()));
        if (!warnings.isEmpty()) {
            body.append("<br><br>");
            for (String warning : warnings) {
                body.append(warning).append("<br>");
            }
        }

        emailService.sendHtml(SystemConfig.getEmailCreateUserFrom(), emailAddress,
                wasProcessSuccessful ? SystemConfig.getEmailSasDataSetsSubjectSuccess() :
                        SystemConfig.getEmailSasDataSetsSubjectFailure(),
                body.toString());
    }

    @VisibleForTesting
	public
    String getLastFolder(String fullFolder) {
        Matcher matcher = REGEX_FILE_SEP.matcher(fullFolder);
        int separatorIndex = -1;
        while (matcher.find()) {
            separatorIndex = matcher.start();
        }
        return fullFolder.substring(separatorIndex + 1);
    }

    /*
     * Do not have a dedicated place to create the zip file before ftp'ing up to server
     * Doubling up on the analytics folder for now
     */
    private String getSasDataSetZipFile(String propertyCode, String propertyId, long milliseconds) {
        return getTempZipDirectory() + "/" + MessageFormat.format(ZIPPED_EXTRACT_FILE, propertyCode,
                propertyId, getNowFormattedForFile(milliseconds));
    }

    @VisibleForTesting
	public
    List<File> getFilesRecursively(File folder) {
        List<File> files = new ArrayList<>();
        for (File file : folder.listFiles()) {
            if (file.isFile()) {
                files.add(file);
            } else {
                // recurse big daddy
                files.addAll(getFilesRecursively(file));
            }
        }
        return files;
    }


    public String extractDatabase(String databaseName, String emailAddress) {
        String quickResponse = RESPONSE_EXTRACT_DB;

        // Lock down to internals
        if (!PacmanThreadLocalContextHolder.getPrincipal().isInternalUser()) {
            quickResponse = ERROR_INTERNAL_ONLY;
        } else {
            Property property = new Property();
            property.setId(PacmanWorkContextHelper.getPropertyId());
            property.setCode(PacmanWorkContextHelper.getPropertyCode());
            dbExtractionService.extractDatabase(databaseName, emailAddress,
                    PacmanWorkContextHelper.getClientCode(), property);
        }
        return quickResponse;
    }

    protected boolean skipScheduleReportTableToggleEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SKIP_SCHEDULED_REPORTS_TABLE);
    }

    public void startSASDatasetsExtraction(Long jobInstanceId, Integer extractTypes, Map<String, String> paths, String outputZipFilePath) {
        final SASFileLoc sasFileLoc = sasNodeLocator.getSasLocForProperty(PacmanWorkContextHelper.getPropertyId());
        final boolean skipFullDatasets = 0 != (extractTypes & ExtractType.SAS_ANALYTIC_DATA_SET_EXCLUDE_PACE_DATA.getValue());
        List<String> skipFullDatasetsFilters = new ArrayList<>();
        if (skipFullDatasets) {
            skipFullDatasetsFilters.add("SAS_ANALYTIC_DATA_SET_EXCLUDE_PACE_DATA");
        }
        SASDatasetExtractionRequest request = new SASDatasetExtractionRequest(getRequestId(), paths, outputZipFilePath + "/sasDatasets.zip", skipFullDatasetsFilters);
        request.execute(sasFileLoc.getSasServerName(), jobInstanceId, false);
    }

    public Map<String, String> getDatasetPaths(Integer extractTypes) {
        final SASFileLoc sasFileLoc = sasNodeLocator.getSasLocForProperty(PacmanWorkContextHelper.getPropertyId());
        Map<String, String> paths = new HashMap<>();
        if (0 != (extractTypes & ExtractType.SAS_ANALYTIC_DATA_SET.getValue())) {
            paths.put(ExtractType.SAS_ANALYTIC_DATA_SET.getFolderName(), sasFileLoc.getAnalyticsDataSetPath());
        }
        return paths;
    }

    private String getRequestId() {
        return "DataExtraction-" + PacmanWorkContextHelper.getClientCode() + "-" + PacmanWorkContextHelper.getPropertyCode() + UUID.randomUUID();
    }
}
