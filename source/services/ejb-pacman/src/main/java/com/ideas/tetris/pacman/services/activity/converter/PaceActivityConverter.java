package com.ideas.tetris.pacman.services.activity.converter;

import com.ideas.tetris.pacman.services.bestavailablerate.entity.ActivityEntity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceSummaryActivityEntity;
import com.ideas.tetris.platform.common.ngi.NGIConvertUtils;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * The ActivityConverter can be extended to provide a basic copy of properties
 * from a Map DTO to an ActivityEntity. It utilizes Apache ConvertUtilsBean to
 * help some of the basic properties to be copied.
 */
@Component
@Transactional
public abstract class PaceActivityConverter<E extends ActivityEntity, P extends PaceSummaryActivityEntity> extends ActivityConverter<E> {
    private static final Logger LOGGER = Logger.getLogger(PaceActivityConverter.class);

    public P convertToPaceEntity(E entity) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Converting Entity: " + entity + " to Pace");
        }

        // Look for an existing PaceActivityEntity for the DTO, if it doesn't exist, create a new one
        P paceEntity = findExistingOrCreateNewPaceActivity(entity);

        // Get a reference to the Pace Entity's ID, so it won't be lost in the property copy
        Integer id = paceEntity.getId();

        // Using copy all of the variables that can be copied by naming convention
        NGIConvertUtils.copyProperties(paceEntity, entity);

        // Reset the ID
        paceEntity.setId(id);

        // Get the BusinessDayEnd from the SnapShotDate
        // Use it to set the month and year (year is tricky because it's the ID of the year, not the actual year... don't blame me!)
        paceEntity.setBusinessDayEndDate(paceEntity.getBusinessDayEndFromSnapShotDate());

        return paceEntity;
    }

    public List<P> convertToPaceEntities(List<E> entities) {
        if (entities == null) {
            return Collections.emptyList();
        }

        List<P> paceEntities = new ArrayList<>();
        for (E entity : entities) {
            paceEntities.add(convertToPaceEntity(entity));
        }

        return paceEntities;
    }

    public abstract P findExistingOrCreateNewPaceActivity(E entity);

}
