package com.ideas.tetris.pacman.services.reports.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;

@Data
public class SavedReportsDTO {
    private Integer id;
    private String name;
    private String description;
    private String actualReportName;
    private String reportParameters;
    private String pageCode;
    private Integer createdByUserId;
    private LocalDateTime createdDate;
    private Integer lastUpdatedByUser;
    private LocalDateTime lastUpdatedDate;
    private transient Object reportParamsObject = new HashMap<>();
}
