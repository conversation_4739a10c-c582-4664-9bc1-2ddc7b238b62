package com.ideas.tetris.pacman.services.activity.converter;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.activity.converter.RoomTypeMarketSegmentActivityConverter.Qualifier;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ActivityEntity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceMktSegActivity;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.log4j.Logger;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Qualifier
@Component
@Transactional
public class RoomTypeMarketSegmentActivityConverter extends ActivityConverter<MktSegAccomActivity> {

    private static final Logger LOGGER = Logger.getLogger(RoomTypeMarketSegmentActivityConverter.class);
    public static final String PROPERTY_ID = "propertyId";

    @Override
    public Map<String, Object> convertFromEntity(ActivityEntity mktSegAccomActivity) {
        Map<String, Object> dto = super.convertFromEntity(mktSegAccomActivity);
        if (dto == null) {
            return null;
        }

        Integer accomTypeId;
        if (mktSegAccomActivity instanceof MktSegAccomActivity) {
            accomTypeId = ((MktSegAccomActivity) mktSegAccomActivity).getAccomTypeId();
            dto.put(ROOM_TYPE_CODE, findRoomTypeCodeForRoomTypeId(accomTypeId));
        }

        Integer mktSegId = null;
        if (mktSegAccomActivity instanceof MktSegAccomActivity) {
            mktSegId = ((MktSegAccomActivity) mktSegAccomActivity).getMktSegId();
        } else if (mktSegAccomActivity instanceof PaceMktSegActivity) {
            mktSegId = ((PaceMktSegActivity) mktSegAccomActivity).getMktSegId();
        }
        dto.put(MARKET_SEGMENT_CODE, findMarketSegmentCodeForMarketSegmentId(mktSegId));

        // Shouldn't return IDs since we are returning codes
        dto.remove(ACCOM_TYPE_ID);
        dto.remove(MKT_SEG_ID);
        return dto;
    }

    @Override
    public MktSegAccomActivity convertFromMap(Map<String, Object> dto, String correlationId, boolean isPast) {
        MktSegAccomActivity mktSegAccomActivity = super.convertFromMap(dto, correlationId, isPast);
        if (mktSegAccomActivity == null) {
            return null;
        }

        mktSegAccomActivity.setAccomTypeId(findOrCreateRoomTypeForCode(mktSegAccomActivity.getPropertyId(), getString(dto, ROOM_TYPE_CODE)).getId());
        mktSegAccomActivity.setMktSegId(marketSegmentRepository.findOrCreateMarketSegmentForCode(mktSegAccomActivity.getPropertyId(), getString(dto, MARKET_SEGMENT_CODE)));
        return mktSegAccomActivity;
    }

    /**
     * Looks for an existing MktSegAccomActivity record based on the ID if it's present, if it isn't, then
     * it will attempt to use the Occupancy Date / Property ID combination, and if that doesn't find an
     * existing record, it will simply return a new MktSegAccomActivity object.
     */
    @Override
    public MktSegAccomActivity findExistingOrCreateNewActivity(Integer propertyId, Map<String, Object> dto, String correlationId, boolean isPast) {
        // Look up the MktSegAccomActivity record
        MktSegAccomActivity mktSegAccomActivity;

        Integer id = getInteger(dto, ID);
        if (id != null) {
            mktSegAccomActivity = tenantCrudService.find(MktSegAccomActivity.class, id);
        } else {
            mktSegAccomActivity = tenantCrudService.findByNamedQuerySingleResult(
                    MktSegAccomActivity.BY_OCCUPANCY_DATE_AND_ACCOMTYPE_CODE_AND_MKT_SEG_CODE_AND_PROPERTY_ID,
                    QueryParameter.with("occupancyDate", getDate(dto, OCCUPANCY_DATE))
                            .and(PROPERTY_ID, propertyId)
                            .and("accomTypeCode", getString(dto, ROOM_TYPE_CODE))
                            .and("mktSegCode", getString(dto, MARKET_SEGMENT_CODE))
                            .parameters());
        }

        // If it doesn't exist, return create a new one
        if (mktSegAccomActivity == null) {
            mktSegAccomActivity = new MktSegAccomActivity();

        }

        FileMetadata metadata = findExistingFileMetadata(correlationId);
        mktSegAccomActivity.setSnapShotDate(metadata.getSnapshotDtTm());
        mktSegAccomActivity.setFileMetadataId(metadata.getId());

        return mktSegAccomActivity;
    }

    @Override
    public List<MktSegAccomActivity> findExistingOrCreateNewActivity(Integer propertyId, List<Map<String, Object>> dtos, String correlationId) {
        List<MktSegAccomActivity> mktSegAccomActivityEntities = new ArrayList<>();
        FileMetadata metadata = findExistingFileMetadata(correlationId);
        Set<Date> dtoOccupancyDateSet = new HashSet<>();

        identifyUniqueOccupancyDates(dtos, dtoOccupancyDateSet);

        List<MktSegAccomActivity> mktSegAccomActivityList = tenantCrudService.findByNamedQuery(
                MktSegAccomActivity.BY_OCCUPANCY_DATERANGE_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId)
                        .and("occupancyDates", dtoOccupancyDateSet)
                        .parameters());

        Map<String, MktSegAccomActivity> mktSegAccomActivityMap = new HashMap<>();
        for (MktSegAccomActivity mktSegAccomActivity : mktSegAccomActivityList) {
            mktSegAccomActivityMap.put(getKey(propertyId, mktSegAccomActivity.getMktSegId(), mktSegAccomActivity.getAccomTypeId(), mktSegAccomActivity.getOccupancyDate()), mktSegAccomActivity);
        }

        LOGGER.info("Adding entries in mktSegActivity existing: " + mktSegAccomActivityMap.size() + " and request contains : " + dtos.size());

        String marketSegmentCode;
        String roomTypeCode;
        Integer marketSegmentId;
        Integer roomTypeId;
        Map<String, Integer> accomTypeCodeIdMap = getAccomTypeIdsFromDB(propertyId, dtos);

        for (Map<String, Object> dto : dtos) {
            marketSegmentCode = getString(dto, MARKET_SEGMENT_CODE);
            roomTypeCode = getString(dto, ROOM_TYPE_CODE);

            marketSegmentId = marketSegmentRepository.findOrCreateMarketSegmentForCode(propertyId, marketSegmentCode);
            roomTypeId = accomTypeCodeIdMap.get(roomTypeCode);

            String occupancyDateString = (String) dto.get(OCCUPANCY_DATE);

            Date occupancyDate;

            try {
                occupancyDate = DateUtil.parseDate(occupancyDateString, OCCUPANCY_DATE_FORMAT_YYYY_MM_DD);
            } catch (ParseException pe) {
                throw new TetrisException("Failed to parse: " + occupancyDateString);
            }

            MktSegAccomActivity entity = mktSegAccomActivityMap.get(getKey(propertyId, marketSegmentId, roomTypeId, occupancyDate));
            if (entity == null) {
                entity = new MktSegAccomActivity();
            }

            Integer id = entity.getId();

            entity.setOccupancyDate(occupancyDate);

            entity.setArrivals(getBigDecimal(dto, ARRIVALS));
            BigDecimal cancellations = getBigDecimal(dto, CANCELLATIONS);
            if (cancellations == null) {
                cancellations = BigDecimal.ZERO;
            }
            entity.setCancellations(cancellations);
            entity.setDepartures(getBigDecimal(dto, DEPARTURES));
            entity.setFoodRevenue(getBigDecimal(dto, FOOD_REVENUE));
            BigDecimal noShows = getBigDecimal(dto, NO_SHOWS);
            if (noShows == null) {
                noShows = BigDecimal.ZERO;
            }
            entity.setNoShows(noShows);
            entity.setRoomRevenue(getBigDecimal(dto, ROOM_REVENUE));
            entity.setRoomsSold(getBigDecimal(dto, ROOMS_SOLD));
            entity.setTotalRevenue(getBigDecimal(dto, TOTAL_REVENUE));

            // Reset the ID if there was one and if the copy properties removed it
            if (entity.getId() == null && id != null) {
                entity.setId(id);
            }

            entity.setPropertyId(propertyId);
            entity.setAccomTypeId(roomTypeId);
            entity.setMktSegId(marketSegmentId);
            entity.setSnapShotDate(metadata.getSnapshotDtTm());
            entity.setFileMetadataId(metadata.getId());

            mktSegAccomActivityEntities.add(entity);
        }

        LOGGER.info("Finished finding and setting entries in mktSegActivity");

        return mktSegAccomActivityEntities;
    }

    protected Map<String, Integer> getAccomTypeIdsFromDB(Integer propertyId, List<Map<String, Object>> dtos) {
        Map<String, Integer> accomTypeIds = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        Set<String> accomTypeCodes = new HashSet<>();

        for (Map<String, Object> dto : dtos) {
            accomTypeCodes.add(getString(dto, ROOM_TYPE_CODE));
        }

        List<AccomType> accomTypesFromDB = findOrCreateRoomTypesForCodes(propertyId, accomTypeCodes).stream().distinct().collect(Collectors.toList());
        for (AccomType accomType : accomTypesFromDB) {
            accomTypeIds.put(accomType.getAccomTypeCode(), accomType.getId());
        }

        return accomTypeIds;
    }

    private void identifyUniqueOccupancyDates(List<Map<String, Object>> dtos, Set<Date> dtoOccupancyDateSet) {

        LOGGER.info("Starting additions to unique sets");

        Set<String> dtoOccupancyDateSetString = new HashSet<>();

        for (Map<String, Object> dto : dtos) {
            if (dto != null) {
                dtoOccupancyDateSetString.add((String) dto.get(OCCUPANCY_DATE));
            }
        }

        for (String dateString : dtoOccupancyDateSetString) {
            try {
                dtoOccupancyDateSet.add(DateUtil.parseDate(dateString, OCCUPANCY_DATE_FORMAT_YYYY_MM_DD));
            } catch (ParseException pe) {
                throw new TetrisException("Failed to parse: " + dateString);
            }
        }

        LOGGER.info("Completed additions to unique sets");
    }

    public String getKey(Integer propertyId, Integer mktSegId, Integer accomTypeId, Date occupancyDate) {
        return String.valueOf(propertyId) + "::" + mktSegId + "::" +
                accomTypeId + "::" + DateUtil.formatDate(occupancyDate, OCCUPANCY_DATE_FORMAT_YYYY_MM_DD);
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
