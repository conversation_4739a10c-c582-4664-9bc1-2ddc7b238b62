package com.ideas.tetris.pacman.services.catchup;

import com.ideas.tetris.platform.common.job.CatchupMode;
import org.joda.time.LocalDate;

public class CatchupParameters {
    private LocalDate startDate;
    private LocalDate endDate;
    private LocalDate activityFirstDate;
    private LocalDate activityLastDate;
    private LocalDate rateShoppingFirstDate;
    private LocalDate rateShoppingLastDate;
    private CatchupMode catchupMode;

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public CatchupMode getCatchupMode() {
        return catchupMode;
    }

    public void setCatchupMode(CatchupMode catchupMode) {
        this.catchupMode = catchupMode;
    }

    public LocalDate getActivityFirstDate() {
        return activityFirstDate;
    }

    public void setActivityFirstDate(LocalDate activityFirstDate) {
        this.activityFirstDate = activityFirstDate;
    }

    public LocalDate getActivityLastDate() {
        return activityLastDate;
    }

    public void setActivityLastDate(LocalDate activityLastDate) {
        this.activityLastDate = activityLastDate;
    }

    public LocalDate getRateShoppingFirstDate() {
        return rateShoppingFirstDate;
    }

    public void setRateShoppingFirstDate(LocalDate rateShoppingFirstDate) {
        this.rateShoppingFirstDate = rateShoppingFirstDate;
    }

    public LocalDate getRateShoppingLastDate() {
        return rateShoppingLastDate;
    }

    public void setRateShoppingLastDate(LocalDate rateShoppingLastDate) {
        this.rateShoppingLastDate = rateShoppingLastDate;
    }

}
