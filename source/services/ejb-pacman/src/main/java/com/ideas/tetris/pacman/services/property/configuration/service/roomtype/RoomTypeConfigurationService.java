package com.ideas.tetris.pacman.services.property.configuration.service.roomtype;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.property.configuration.RoomClassCapacityMap;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.RoomTypePropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecordStatus;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.List;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@RoomTypeConfigurationService.Qualifier
@Component
@Transactional
public class RoomTypeConfigurationService extends AbstractPropertyConfigurationService {

    private static final Logger LOGGER = Logger.getLogger(RoomTypeConfigurationService.class.getName());

    private static final int ROOM_TYPE_CODE_MAX_LENGTH = 50;
    private static final int ROOM_TYPE_DESCRIPTION_MAX_LENGTH = 250;

    @Override
    public PropertyConfigurationRecordType getPropertyConfigurationRecordType() {
        return PropertyConfigurationRecordType.RT;
    }

    @Override
    public List<PropertyConfigurationRecordFailure> doValidate(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId) {
        RoomTypePropertyConfigurationDto roomTypePropertyConfigurationDto = (RoomTypePropertyConfigurationDto) propertyConfigurationDto;

        List<PropertyConfigurationRecordFailure> exceptions = new ArrayList<PropertyConfigurationRecordFailure>();

        // Validate Room Class Name
        String roomClassName = roomTypePropertyConfigurationDto.getRoomClassName();
        if (StringUtils.isEmpty(roomClassName)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Room Class Name is required"));
        } else if (findAccomClass(propertyId, roomClassName) == null) {
            exceptions.add(new PropertyConfigurationRecordFailure("Room Class Name: " + roomClassName + " is not valid"));
        }

        // Validate Room Type Code
        String roomTypeCode = roomTypePropertyConfigurationDto.getRoomTypeCode();

        if (StringUtils.isEmpty(roomTypeCode)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Room Type Code is required"));
        } else if (roomTypeCode.length() > ROOM_TYPE_CODE_MAX_LENGTH) {
            roomTypePropertyConfigurationDto.setRoomTypeCode(StringUtils.left(roomTypeCode, ROOM_TYPE_CODE_MAX_LENGTH));
            exceptions.add(new PropertyConfigurationRecordFailure(ConfigurationFileRecordStatus.WARNING, "Room Type Code cannot be longer than " + ROOM_TYPE_CODE_MAX_LENGTH + " characters.  The value has been trimmed."));
        }

        // Validate Room Type Description
        String roomTypeDescription = roomTypePropertyConfigurationDto.getRoomTypeDescription();

        if (StringUtils.isNotEmpty(roomTypeDescription) && roomTypeDescription.length() > ROOM_TYPE_DESCRIPTION_MAX_LENGTH) {
            roomTypePropertyConfigurationDto.setRoomTypeDescription(StringUtils.left(roomTypeDescription, ROOM_TYPE_DESCRIPTION_MAX_LENGTH));
            exceptions.add(new PropertyConfigurationRecordFailure(ConfigurationFileRecordStatus.WARNING, "Room Type Description cannot be longer than " + ROOM_TYPE_DESCRIPTION_MAX_LENGTH + " characters.  The value has been trimmed."));
        }

        // Validate ROH
        if (roomTypePropertyConfigurationDto.isRohType() == null) {
            exceptions.add(new PropertyConfigurationRecordFailure("ROH Type must be 'Y' or 'N'"));
        }
        String capacityString = roomTypePropertyConfigurationDto.getCapacity();
        if (capacityString != null && capacityString.length() > 0) {
            try {
                Integer capacity = Integer.valueOf(capacityString);
                if (capacity < 0) {
                    exceptions.add(new PropertyConfigurationRecordFailure("Invalid room type capacity: " + capacity));
                }
            } catch (NumberFormatException e) {
                exceptions.add(new PropertyConfigurationRecordFailure("Invalid room type capacity: " + capacityString));
            }

        }
        return exceptions;
    }

    @Override
    public void doHandle(PropertyConfigurationDto pcd, Integer propertyId) {
        RoomTypePropertyConfigurationDto roomTypePropertyConfigurationDto = (RoomTypePropertyConfigurationDto) pcd;

        AccomClass accomClass = findAccomClass(propertyId, roomTypePropertyConfigurationDto.getRoomClassName());

        AccomType accomType = new AccomType();
        accomType.setPropertyId(propertyId);
        accomType.setAccomTypeCode(roomTypePropertyConfigurationDto.getRoomTypeCode());
        accomType.setStatusId(Constants.ACTIVE_STATUS_ID);
        accomType.setSystemDefault(0);
        accomType.setAccomTypeCapacity(0);
        accomType.setName(roomTypePropertyConfigurationDto.getRoomTypeCode());
        accomType.setDescription(roomTypePropertyConfigurationDto.getRoomTypeDescription());
        accomType.setRohType(roomTypePropertyConfigurationDto.isRohType() ? 1 : 0);
        accomType.setAccomClass(accomClass);

        LOGGER.info("Creating AccomType: " + accomType.getAccomTypeCode() + " for Property: " + pcd.getPropertyCode() + " and Class: " + accomType.getAccomClass().getName());
        crudService.save(accomType);

        int capacity = roomTypePropertyConfigurationDto.getCapacityAsInt();
        RoomClassCapacityMap.incrementRoomClassCapacity(
                pcd.getPropertyCode(), roomTypePropertyConfigurationDto.getRoomClassName(), capacity);
    }

    public AccomType findAccomTypeForPropertyWithROH(Integer propertyId) {
        return (AccomType) crudService.findByNamedQuerySingleResult(AccomType.GET_ROH_TYPE, QueryParameter.with("propertyId", propertyId).parameters());
    }

    public AccomType findAccomType(Integer propertyId, String name) {
        return (AccomType) crudService.findByNamedQuerySingleResult(AccomType.BY_PROPERTY_ID_TYPE, QueryParameter.with("propertyId", propertyId).and("accomTypeCode", name).parameters());
    }

    public AccomClass findAccomClass(Integer propertyId, String name) {
        return (AccomClass) crudService.findByNamedQuerySingleResult(AccomClass.BY_NAME, QueryParameter.with("propertyId", propertyId).and("name", name).parameters());
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
