package com.ideas.tetris.pacman.services.rates;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedAdjustment;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedDetails;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractDetail;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedDetails;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Component
@Transactional
public class RateRestService {

    @TenantCrudServiceBean.Qualifier
	@Qualifier("tenantCrudServiceBean")
    @Autowired
	protected CrudService tenantCrudService;

    @SuppressWarnings("unchecked")
    public List<RateUnqualified> getUnqualifiedRates(String names) {
        List<RateUnqualified> rateHeaders;

        if (names != null) {
            List<String> nameList = Arrays.asList(names.split(","));
            rateHeaders = tenantCrudService.findByNamedQuery(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES, QueryParameter.with("names", nameList).parameters());

            for (RateUnqualified header : rateHeaders) {
                List<AbstractDetail> details = getUnqualifiedDetails(header);
                header.setDetails(details);
            }
        } else {
            rateHeaders = new ArrayList<RateUnqualified>();
        }

        return rateHeaders;
    }

    private List<AbstractDetail> getUnqualifiedDetails(RateUnqualified header) {
        return tenantCrudService.findByNamedQuery(RateUnqualifiedDetails.BY_RATE_UNQUALIFIED_ID, QueryParameter.with("rateUnqualifiedId", header.getId()).parameters());
    }

    private List<AbstractDetail> getQualifiedDetails(RateQualified header) {
        return tenantCrudService.findByNamedQuery(RateQualifiedDetails.BY_RATE_QUALIFIED_ID, QueryParameter.with("rateQualifiedId", header.getId()).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<RateQualified> getQualifiedRates(String names) {
        List<String> nameList = Arrays.asList(names.split(","));

        List<RateQualified> rateHeaders = tenantCrudService.findByNamedQuery(RateQualified.GET_RATE_QUALIFIED_BY_NAMES, QueryParameter.with("names", nameList).parameters());

        for (RateQualified header : rateHeaders) {
            List<AbstractDetail> details = getQualifiedDetails(header);
            header.setDetails(details);
        }

        return rateHeaders;
    }

    public List<RateQualifiedAdjustment> getRateAdjustments(String rateCodeName) {
        return tenantCrudService.findByNamedQuery(RateQualifiedAdjustment.BY_RATE_PLAN_NAME, QueryParameter.with("rateCodeName", rateCodeName).parameters());
    }

    public void deleteAllQualifiedRates() {
        tenantCrudService.deleteAll(RateQualifiedAdjustment.class);
        tenantCrudService.deleteAll(RateQualifiedDetails.class);
        tenantCrudService.deleteAll(RateQualified.class);
    }

    public void deleteAllUnqualifiedRates() {
        tenantCrudService.deleteAll(RateUnqualifiedDetails.class);
        tenantCrudService.deleteAll(RateUnqualified.class);
    }
}
