package com.ideas.tetris.pacman.services.budget.model;

import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

import static com.ideas.tetris.pacman.util.Executor.computeIfTrue;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.formatDate;
import static java.lang.String.join;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.StringUtils.EMPTY;

public class BudgetUserForecastEntityModel {

    private String weekNumber;

    private Integer segmentId;

    private String segmentCode;
    private String segmentName;

    private Integer rooms = 0;

    private BigDecimal revenue = BigDecimal.ZERO;

    private LocalDate occupancyDate;

    private Level level;

    private List<BudgetUserForecastEntityModel> children = new ArrayList<>();

    public String getWeekNumber() {
        return weekNumber;
    }

    public void setWeekNumber(String weekNumber) {
        this.weekNumber = weekNumber;
    }

    public Integer getSegmentId() {
        return segmentId;
    }

    public void setSegmentId(Integer segmentId) {
        this.segmentId = segmentId;
    }

    public String getSegmentCode() {
        return segmentCode;
    }

    public void setSegmentCode(String segmentCode) {
        this.segmentCode = segmentCode;
    }

    public String getSegmentName() {
        return segmentName;
    }

    public void setSegmentName(String segmentName) {
        this.segmentName = segmentName;
    }

    public Integer getRooms() {
        return rooms;
    }

    public void setRooms(Integer rooms) {
        this.rooms = rooms;
    }

    public BigDecimal getRevenue() {
        return revenue;
    }

    public void setRevenue(BigDecimal revenue) {
        this.revenue = revenue;
    }

    public LocalDate getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(LocalDate occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public Level getLevel() {
        return level;
    }

    public void setLevel(Level level) {
        this.level = level;
    }

    public String getDow() {
        return occupancyDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.getDefault());
    }

    public List<BudgetUserForecastEntityModel> getChildren() {
        return children;
    }

    public void setChildren(List<BudgetUserForecastEntityModel> children) {
        this.children = children;
    }

    public boolean isGroupLevel() {
        return isDailyGroup() || isMonthlyGroup() || isWeeklyGroup();
    }

    public boolean isTotalLevel() {
        return isDailyTotal() || isWeeklyTotal() || isMonthlyTotal();
    }

    public enum Level {

        MONTHLY_TOTAL("budget.monthly.total"),
        WEEKLY_TOTAL("budget.weekly.total"),
        DAILY_TOTAL("budget.total"),
        DAILY_GROUP(""),
        WEEKLY_GROUP(""),
        MONTHLY_GROUP("");


        private String name;

        Level(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }

    }

    @Override
    public int hashCode() {
        return Objects.hash(getWeekNumber(), getSegmentId(), getSegmentCode(), getSegmentName(), getRooms(), getRevenue(),
                getOccupancyDate(), getLevel(), getChildren());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        BudgetUserForecastEntityModel that = (BudgetUserForecastEntityModel) o;

        if (segmentId != null ? !segmentId.equals(that.segmentId) : that.segmentId != null) return false;
        if (segmentCode != null ? !segmentCode.equals(that.segmentCode) : that.segmentCode != null) return false;
        if (segmentName != null ? !segmentName.equals(that.segmentName) : that.segmentName != null) return false;
        if (weekNumber != null ? !weekNumber.equals(that.weekNumber) : that.weekNumber != null) return false;
        if (rooms != null ? !rooms.equals(that.rooms) : that.rooms != null)
            return false;
        if (revenue != null ? !revenue.equals(that.revenue) : that.revenue != null)
            return false;
        if (occupancyDate != null ? !occupancyDate.equals(that.occupancyDate) : that.occupancyDate != null)
            return false;
        if (level != that.level) return false;
        return children != null ? children.equals(that.children) : that.children == null;
    }

    @Override
    public String toString() {
        return "BudgetUserForecastEntityModel{" +
                "segmentId=" + segmentId + "\n" +
                ", segmentCode='" + segmentCode + '\'' + "\n" +
                ", segmentName='" + segmentName + '\'' + "\n" +
                ", weekNumber='" + weekNumber + '\'' + "\n" +
                ", rooms=" + rooms + "\n" +
                ", revenue=" + revenue + "\n" +
                ", occupancyDate=" + occupancyDate + "\n" +
                ", level=" + level + "\n" +
                ", children=" + "\t" + children +
                '}';
    }

    public boolean isDailyTotal() {
        return getLevel().equals(Level.DAILY_TOTAL);
    }

    public boolean isWeeklyTotal() {
        return getLevel().equals(Level.WEEKLY_TOTAL);
    }

    public boolean isMonthlyTotal() {
        return getLevel().equals(Level.MONTHLY_TOTAL);
    }

    public boolean isDailyGroup() {
        return getLevel().equals(Level.DAILY_GROUP);
    }

    public boolean isWeeklyGroup() {
        return getLevel().equals(Level.WEEKLY_GROUP);
    }

    public boolean isMonthlyGroup() {
        return getLevel().equals(Level.MONTHLY_GROUP);
    }

    public boolean isDistributionApplicable() {
        return isWeeklyGroup() || isMonthlyGroup();
    }

    public boolean isAggreagationApplicable() {
        return isDailyGroup();
    }

    public String getId() {
        return join("_", getLevel().name(), getWeekNumberString(), getOccupancyDateString(), getSegmentIdString());
    }

    private String getSegmentIdString() {
        return computeIfTrue(nonNull(segmentId), () -> Integer.toString(segmentId), "");
    }

    private String getOccupancyDateString() {
        return computeIfTrue(nonNull(getOccupancyDate()), () -> formatDate(JavaLocalDateUtils.toDate(getOccupancyDate()), DateUtil.DEFAULT_DATE_FORMAT), "");
    }

    private String getWeekNumberString() {
        return this.weekNumber == null ? EMPTY : weekNumber;
    }

    public String getValidationMessage() {
        return "Invalid data for OccupancyDate:" + this.getOccupancyDate() + ", SegmentCode:" + this.getSegmentCode() + ", SegmentName:" + this.getSegmentName() + ", RoomSold:" + this.getRooms() + ", RoomRevenue:" + this.getRevenue();
    }

}
