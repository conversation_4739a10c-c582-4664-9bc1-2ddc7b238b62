package com.ideas.tetris.pacman.services.dialog;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.CPManagementService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.demandoverride.dto.LastRoomValueAccomClass;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValue;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingAccomLevelView;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingPropertyLevel;
import com.ideas.tetris.pacman.services.overbooking.service.OverbookingOverrideService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.LocalDateUtils;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class DecisionDataService {
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
	private OverbookingOverrideService overbookingOverrideService;

    @Autowired
	private CPManagementService cpManagementService;

    public BigDecimal getLastRoomValue(LocalDate occupancyDate) {
        LastRoomValue lrvDecision = tenantCrudService.findByNamedQuerySingleResult(LastRoomValue.BY_OCCUPANCY_DATE_FOR_MASTER_CLASS, QueryParameter.with("occupancyDate", LocalDateUtils.toDate(occupancyDate)).parameters());
        return lrvDecision == null ? null : lrvDecision.getValue();
    }

    public BigDecimal getLastRoomValueForRoomClass(LocalDate occupancyDate, String roomClassName) {
        LastRoomValueAccomClass lastRoomValueAccomClass = tenantCrudService.findByNamedQuerySingleResult(LastRoomValue.BY_OCCUPANCY_DATE_FOR_ROOM_CLASS, QueryParameter.with("occupancyDate", LocalDateUtils.toDate(occupancyDate)).and("accomClassName", roomClassName).parameters());
        return lastRoomValueAccomClass == null ? null : lastRoomValueAccomClass.getLrv();
    }

    public Map<String, BigDecimal> getLastRoomValueForAllRoomClasses(LocalDate occupancyDate) {
        List<LastRoomValueAccomClass> lastRoomValueAccomClasses = tenantCrudService.findByNamedQuery(LastRoomValue.BY_OCCUPANCY_DATE_FOR_ALL_ROOM_CLASS, QueryParameter.with("occupancyDate", LocalDateUtils.toDate(occupancyDate)).parameters());
        return lastRoomValueAccomClasses.stream().collect(Collectors.toMap(LastRoomValueAccomClass::getAccomClassName, LastRoomValueAccomClass::getLrv));
    }

    public BigDecimal getBarDecision(LocalDate occupancyDate) {
        Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        CPDecisionBAROutput barDecision = tenantCrudService.findByNamedQuerySingleResult(CPDecisionBAROutput.GET_MASTER_CLASS_DECISION_FOR_DATE, CPDecisionBAROutput.params(product, LocalDateUtils.toJodaLocalDate(occupancyDate)));
        return barDecision == null ? null : barDecision.getFinalBAR();
    }

    public BigDecimal getBarDecisionByRoomType(LocalDate occupancyDate, String roomTypeName) {
        CPDecisionBAROutput barDecisionRoomType = cpManagementService.getBarDecisionForAccomTypeNameAndDate(LocalDateUtils.toJodaLocalDate(occupancyDate), roomTypeName);
        return barDecisionRoomType == null ? null : (barDecisionRoomType.getFinalBAR());
    }

    public Map<String, BigDecimal> getBarDecisionsAllRoomTypes(LocalDate occupancyDate) {
        List<CPDecisionBAROutput> barDecisionsForAllRoomTypes = cpManagementService.getBarDecisionsForOccupancyDate(LocalDateUtils.toJodaLocalDate(occupancyDate));
        return barDecisionsForAllRoomTypes.stream().collect(Collectors.toMap(o -> o.getAccomType().getName(), t -> t.getFinalBAR() != null ? t.getFinalBAR().setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO));
    }

    public BigDecimal getOverbookingDecision(LocalDate occupancyDate) {
        List<OverbookingPropertyLevel> entities = overbookingOverrideService.executeQueryPropertyLevel(LocalDateUtils.toDate(occupancyDate), LocalDateUtils.toDate(occupancyDate));
        return entities == null || entities.isEmpty() || entities.get(0).getOverbookingDecision() == null ? null : new BigDecimal(entities.get(0).getOverbookingDecision());
    }

    public Integer getOverbookingDecision(LocalDate occupancyDate, String roomType) {
        Integer accomTypeId = getAccomTypeId(roomType);
        if (accomTypeId == null) {
            return null;
        }
        List<OverbookingAccomLevelView> entities = overbookingOverrideService.executeQueryAccomTypeLevel(accomTypeId, LocalDateUtils.toDate(occupancyDate), LocalDateUtils.toDate(occupancyDate));
        return entities == null || entities.isEmpty() ? null : entities.get(0).getOverbookingDecision();
    }

    public Map<String, Integer> getOverbookingDecisionsAllRoomTypes(LocalDate occupancyDate) {
        Optional<List<OverbookingAccomLevelView>> overbookingAccomLevelViews = Optional.ofNullable(overbookingOverrideService.executeQueryAccomTypeLevel(LocalDateUtils.toDate(occupancyDate)));
        return overbookingAccomLevelViews.isPresent() ? overbookingAccomLevelViews.get().stream().collect(Collectors.toMap(entity -> getAccomTypeCode(entity.getAccomTypeId()), OverbookingAccomLevelView::getOverbookingDecision)) : new HashMap();
    }

    private Integer getAccomTypeId(String roomTypeName) {
        AccomType accomType = tenantCrudService.findByNamedQuerySingleResult(AccomType.BY_NAME, QueryParameter.with("accomTypeName", roomTypeName).parameters());
        return accomType == null ? null : accomType.getId();
    }

    private String getAccomTypeCode(Integer accomTypeId) {
        AccomType accomType = tenantCrudService.find(AccomType.class, accomTypeId);
        return accomType == null ? null : accomType.getName();
    }
}
