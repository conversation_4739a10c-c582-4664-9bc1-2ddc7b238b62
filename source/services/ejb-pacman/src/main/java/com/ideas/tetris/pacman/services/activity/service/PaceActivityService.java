package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.activity.converter.PaceActivityConverter;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ActivityEntity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceSummaryActivityEntity;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.TableBatchAware;
import com.ideas.tetris.platform.common.rest.SimpleObjectRestWrapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Component;

/**
 * The ActivityService provides the base CRUD functionality for Activity data.
 * It will always use a converter to convert from a REST-specific Map DTO to
 * Entities.
 */
@Transactional
@Component
public abstract class PaceActivityService<E extends ActivityEntity, P extends PaceSummaryActivityEntity> extends ActivityService<E> {
    private static final Logger LOGGER = Logger.getLogger(PaceActivityService.class);

    /**
     * Creates or updates ActivityEntities for the List of DTOs provided.
     */
    @Override
    public List<Map<String, Object>> save(List<Map<String, Object>> dtos, String correlationId, boolean isPast) {
        List<E> entities = saveAndConvertDtos(dtos, correlationId, isPast);

        savePaceForEntities(getConverter().convertToPaceEntities(entities));

        return getConverter().convertFromEntities(entities);
    }

    public void saveAsBatch(List<Map<String, Object>> dtos, String correlationId, boolean saveWithPace) {
        List<E> entities = getConverter().convertFromMapAll(dtos, correlationId);
        List<TableBatchAware> batchAwares = convertToTableBatch(entities, saveWithPace);
        tenantCrudService.execute(batchAwares.get(0).getInsertStoredProcedureName(), batchAwares);
    }

    public void saveWithoutPace(List<Map<String, Object>> dtos, String correlationId, boolean isPast) {
        saveAndConvertDtos(dtos, correlationId, isPast);
    }

    private List<E> saveAndConvertDtos(List<Map<String, Object>> dtos, String correlationId, boolean isPast) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Saving " + dtos + " as Entity: " + getEntityClass());
        }
        List<E> entities = saveEntities(dtos, correlationId, isPast);
        LOGGER.info("Starting convert to pace entities: " + entities.size());

        return entities;
    }

    private void savePaceForEntities(List<P> paceEntities) {
        LOGGER.info("Completed convert to pace entities: " + paceEntities.size());

        LOGGER.info("Starting save of pace entities: " + paceEntities.size());

        if (CollectionUtils.isNotEmpty(paceEntities)) {
            tenantCrudService.save(paceEntities);
        }

        LOGGER.info("Completed save of pace entities: " + paceEntities.size());
    }

    /**
     * Creates or updates an ActivityEntity for the given ID.
     */

    public Map<String, Object> save(Integer id, Map<String, Object> dto, String correlationId, boolean isPast) {
        E entity = saveEntity(id, dto, correlationId, isPast);

        savePaceForEntity(entity);

        return getConverter().convertFromEntity(entity);
    }

    private void savePaceForEntity(E entity) {
        tenantCrudService.save(getConverter().convertToPaceEntity(entity));
    }

    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getPace(Date startDate, Date endDate) {
        return getConverter().convertFromEntities(tenantCrudService.findByNamedQuery(getPaceDateRangeQuery(), QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters()));
    }


    public SimpleObjectRestWrapper<Integer> deletePace(Date startDate, Date endDate) {
        return new SimpleObjectRestWrapper<>(tenantCrudService.executeUpdateByNamedQuery(deletePaceDateRangeQuery(), QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("startDate", startDate).and("endDate", endDate).parameters()));
    }

    protected abstract PaceActivityConverter<E, P> getConverter();

    protected abstract String getPaceDateRangeQuery();

    protected abstract String deletePaceDateRangeQuery();

    protected abstract List<TableBatchAware> convertToTableBatch(List<E> entities, boolean isCDP);

}
