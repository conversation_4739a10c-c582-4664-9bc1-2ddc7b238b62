package com.ideas.tetris.pacman.services.rates;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.USE_OCCUPANCY_BASED_RATE_VALUE_FOR_GROUPS;
import static com.ideas.tetris.pacman.common.constants.Constants.DOUBLE;
import static com.ideas.tetris.pacman.common.constants.Constants.USE_BASE_OCCUPANCY;
import static java.math.BigDecimal.ZERO;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class RateAmountCalculator {
    public static final String SINGLE_OCCUPANT_AMOUNT = "singleOccupantAmount";
    public static final String DOUBLE_OCCUPANT_AMOUNT = "doubleOccupantAmount";

    @Autowired
	private PacmanConfigParamsService configParamsService;

    public String getAmountType(DayOfWeek dayOfWeek) {
        return getAmountType(dayOfWeek.getCalendarDayOfWeek());
    }

    private String getAmountType(int dayOfWeek) {
        String configuration;
        if (dayOfWeek <= 4) {
            configuration = configParamsService.getParameterValue(FeatureTogglesConfigParamName.RATES_MONDAY_TO_THURSDAY);
        } else {
            configuration = configParamsService.getParameterValue(FeatureTogglesConfigParamName.RATES_FRIDAY_TO_SUNDAY);
        }

        if (USE_BASE_OCCUPANCY.equals(configuration) || null == configuration) {
            configuration = configParamsService.getParameterValue(FeatureTogglesConfigParamName.RATES_BASE_OCCUPANCY_NUMBER);
        }

        String amountType;
        if (DOUBLE.equalsIgnoreCase(configuration)) {
            amountType = DOUBLE_OCCUPANT_AMOUNT;
        } else {
            amountType = SINGLE_OCCUPANT_AMOUNT;
        }

        return amountType;
    }

    public Map<DayOfWeek, String> getAmountTypesForAllDOWs() {
        Map<DayOfWeek, String> dayOfWeekDOWRateMap = new HashMap<>();
        String monToThu = configParamsService.getParameterValue(FeatureTogglesConfigParamName.RATES_MONDAY_TO_THURSDAY);
        String friToSun = configParamsService.getParameterValue(FeatureTogglesConfigParamName.RATES_FRIDAY_TO_SUNDAY);
        String baseOccupancyNumber = configParamsService.getParameterValue(FeatureTogglesConfigParamName.RATES_BASE_OCCUPANCY_NUMBER);
        for (DayOfWeek dayOfWeek : DayOfWeek.values()) {
            String configuration = getAmountType(dayOfWeek.getCalendarDayOfWeek() <= 4 ? monToThu : friToSun, baseOccupancyNumber);
            if (DOUBLE.equalsIgnoreCase(configuration)) {
                dayOfWeekDOWRateMap.put(dayOfWeek, DOUBLE_OCCUPANT_AMOUNT);
            } else {
                dayOfWeekDOWRateMap.put(dayOfWeek, SINGLE_OCCUPANT_AMOUNT);
            }
        }
        return dayOfWeekDOWRateMap;
    }


    private String getAmountType(String weekDayBasedConfiguration, String baseOccupancyNumber) {
        if (USE_BASE_OCCUPANCY.equals(weekDayBasedConfiguration) || null == weekDayBasedConfiguration) {
            return baseOccupancyNumber;
        }
        return weekDayBasedConfiguration;
    }

    public BigDecimal getRateValue(LocalDate occupancyDate, RateAmountCalculatorContext context) {
        BigDecimal rateValue = null;
        if (configParamsService.getBooleanParameterValue(USE_OCCUPANCY_BASED_RATE_VALUE_FOR_GROUPS)) {
            rateValue = context.calculateRatesBasedOnOccupancy();
        }

        if (rateValue == null) {
            String amountType = getAmountType(occupancyDate.getDayOfWeek());

            if (SINGLE_OCCUPANT_AMOUNT.equals(amountType)) {
                rateValue = context.getRate();
            } else if (DOUBLE_OCCUPANT_AMOUNT.equals(amountType)) {
                rateValue = context.getDoubleRate();
            }
        }

        // fallback to find first non-null rate if rate not set
        return ObjectUtils.firstNonNull(rateValue, context.getRate(), context.getDoubleRate(), context.getTripleRate(), context.getQuadRate(), ZERO);
    }
}
