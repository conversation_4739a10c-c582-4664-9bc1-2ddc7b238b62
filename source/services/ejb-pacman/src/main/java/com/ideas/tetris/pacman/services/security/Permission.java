package com.ideas.tetris.pacman.services.security;

import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.infra.tetris.security.domain.UrlParams;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Utility class which will parse the permission string and put it in a usable object.  Permissions can
 * then be added and adjusted in the object.  The toString() method will return the string format.
 */
public class Permission {

    private static final String ACCESS_KEY = "access";
    private static final String FUNCTIONS_KEY = "functions";
    private static final String PAGE_CODE_KEY = "pageCode";

    private static final String PAGE_CODE_EQUALS = "pageCode=";
    private static final String AMP_FUNCTIONS_EQUALS_BRACE = "&functions={";
    private static final String AMP_ACCESS_EQUALS = "&access=";

    private static final Logger LOGGER = Logger.getLogger(Permission.class);

    private String pageCode;
    private Access pageAccess;

    private Map<String, Access> functionMap = new LinkedHashMap<>();

    public Permission(String permissionIn) {
        @SuppressWarnings("squid:CommentedOutCodeLine")
        /* The permission has the following format
           pageCode=group-pricing-configuration&access=readWrite&functions={function1:readWrite,function2:readOnly}
        */

        /* We can break apart the pieces by parsing as a URL - we will get 3 keys - pageCode, access, and functions
         The value for functions is in a JSON-like format but is missing quotes on values
        */
                Map<String, String> paramsMap = new UrlParams(permissionIn).toMap();

        pageCode = paramsMap.get(PAGE_CODE_KEY);
        pageAccess = Access.getAccessFromCode(paramsMap.get(ACCESS_KEY));

        String functions = paramsMap.get(FUNCTIONS_KEY);

        // If we have function permissions for the page..
        if (functions != null) {
            // Strip the braces from the start and end
            functions = StringUtils.strip(functions, "{}");
            // Get an array of functions and corresponding permissions the format of each string in the array
            // will be something like "functionName:access" where access is "readOnly" or "readWrite"
            String[] functionArray = StringUtils.split(functions, ",");

            for (String aFunctionArray : functionArray) {
                String[] functionPermissionArray = StringUtils.split(aFunctionArray, ":");

                Access access = functionPermissionArray.length == 2 ? Access.getAccessFromCode(functionPermissionArray[1]) : null;

                if (access != null) {
                    functionMap.put(functionPermissionArray[0], access);
                } else {
                    LOGGER.warn("Unable to parse function permission: " + aFunctionArray + " for pageCode: " + pageCode);
                }
            }
        }
    }

    public Permission(String pageCodeIn, Access accessIn) {
        this.pageCode = pageCodeIn;
        this.pageAccess = accessIn;
    }

    Access getFunctionAccess(String functionIn) {

        Access functionAccess = functionMap.get(functionIn);

        // Function access defaults to readOnly
        return functionAccess != null ? functionAccess : Access.READONLY;
    }

    void setFunctionAccess(String functionIn, Access accessIn) {
        functionMap.put(functionIn, accessIn);

        adjustCodePagePermission();
    }

    // Adjust the code page access based on the function access

    // If at least one function has readWrite, the page code has readWrite
    private void adjustCodePagePermission() {

        for (Access access : functionMap.values()) {
            if (access == Access.READWRITE) {
                this.setPageAccess(Access.READWRITE);
                break;
            }
        }
    }

    @Override
    public String toString() {

        @SuppressWarnings("squid:CommentedOutCodeLine")
        /* Here is the format we are building
         pageCode=group-pricing-configuration&access=readWrite&functions={function1:readWrite,function2:readWrite}
        */
                StringBuilder sb = new StringBuilder();

        sb.append(PAGE_CODE_EQUALS);
        sb.append(this.getPageCode());
        sb.append(AMP_ACCESS_EQUALS);
        sb.append(this.getPageAccess().getAccessCode());

        if (!this.functionMap.isEmpty()) {
            sb.append(AMP_FUNCTIONS_EQUALS_BRACE);

            boolean firstEntry = true;

            for (Map.Entry<String, Access> functionEntry : this.functionMap.entrySet()) {
                if (!firstEntry) {
                    sb.append(",");
                } else {
                    firstEntry = false;
                }
                sb.append(functionEntry.getKey());
                sb.append(":");
                sb.append(functionEntry.getValue().getAccessCode());
            }
            sb.append("}");
        }
        return sb.toString();
    }

    public String getPageCode() {
        return pageCode;
    }

    public Access getPageAccess() {
        return pageAccess;
    }

    void setPageAccess(Access pageAccess) {
        this.pageAccess = pageAccess;
    }

    public Map<String, Access> getFunctionMap() {
        return functionMap;
    }

    public static Access getRolePageAccess(Role role, String pageCodeToFindIn) {
        String pageCodeIdentifier = PAGE_CODE_EQUALS + pageCodeToFindIn;

        // Look through all permissions for the role to find the pageCode and look for the function permission
        for (String permission : role.getPermissions()) {

            // If this is the page code to find...
            if (StringUtils.startsWith(permission, pageCodeIdentifier)) {

                // See if it has the function permission is read/write
                if (StringUtils.contains(permission, Access.READWRITE.getAccessCode())) {
                    return Access.READWRITE;
                } else if (StringUtils.contains(permission, Access.READONLY.getAccessCode())) {
                    return Access.READONLY;
                }
            }
        }

        return null;
    }

    public static boolean roleHasFunctionWithAccess(Role role, String pageCodeToFindIn, String functionPermissionToFindIn, Access accessToFindIn) {

        String pageCodeIdentifier = PAGE_CODE_EQUALS + pageCodeToFindIn;
        String functionAndAccessToFind = functionPermissionToFindIn + ":" + accessToFindIn.getAccessCode();

        // Look through all permissions for the role to find the pageCode and look for the function permission
        for (String permission : role.getPermissions()) {
            // If this is the page code to find...
            if (StringUtils.startsWith(permission, pageCodeIdentifier)) {
                // See if it has the function permission to find with the access to find
                if (StringUtils.contains(permission, functionAndAccessToFind)) {
                    return true;
                }
                break;
            }
        }
        return false;
    }

    static Access getRoleFunctionAccess(Role role, String pageCodeToFindIn, String functionPermissionToFindIn) {
        String pageCodeIdentifier = PAGE_CODE_EQUALS + pageCodeToFindIn;
        String functionPermissionReadWrite = functionPermissionToFindIn + ":" + Access.READWRITE.getAccessCode();
        String functionPermissionReadOnly = functionPermissionToFindIn + ":" + Access.READONLY.getAccessCode();

        // Look through all permissions for the role to find the pageCode and look for the function permission
        for (String permission : role.getPermissions()) {

            // If this is the page code to find...
            if (StringUtils.startsWith(permission, pageCodeIdentifier)) {

                // See if it has the function permission is read/write
                if (StringUtils.contains(permission, functionPermissionReadWrite)) {
                    return Access.READWRITE;
                } else if (StringUtils.contains(permission, functionPermissionReadOnly)) {
                    return Access.READONLY;
                }
            }
        }

        return null;
    }

    static boolean roleHasFunction(Role role, String pageCodeToFindIn, String functionPermissionToFindIn) {

        String pageCodeIdentifier = PAGE_CODE_EQUALS + pageCodeToFindIn;

        // Look through all permissions for the role to find the pageCode and look for the function permission
        for (String permission : role.getPermissions()) {
            // If this is the page code to find and also contains the function permission to find...
            if (StringUtils.startsWith(permission, pageCodeIdentifier) && StringUtils.contains(permission, functionPermissionToFindIn)) {
                return true;
            }
        }
        return false;
    }

    public static void setPageAccessForRole(Role role, String pageCode, Access accessIn) {
        final List<Permission> pList = mapPermissionStringsToPermissionList(role.getPermissions());
        final Optional<Permission> permissionOptional = getPagePermissionFromList(pList, pageCode);
        Permission p = permissionOptional.orElse(new Permission(pageCode, accessIn));
        p.setPageAccess(accessIn);
        if (!permissionOptional.isPresent()) {
            pList.add(p);
        }
        role.setPermissions(mapPermissionListToPermissionStrings(pList));
    }

    private static Optional<Permission> getPagePermissionFromList(List<Permission> permissions, String pageCode) {
        return permissions.stream()
                .filter(permission -> permission.getPageCode().equals(pageCode))
                .findFirst();
    }

    public static void setFunctionAccessForRole(Role role, String pageCode, String functionToSet, Access accessIn) {
        final List<Permission> pList = mapPermissionStringsToPermissionList(role.getPermissions());
        final Optional<Permission> permissionOptional = getFunctionPermissionFromList(pList, pageCode, functionToSet);
        Permission p = permissionOptional.orElse(new Permission(pageCode, accessIn));
        p.setFunctionAccess(functionToSet, accessIn);
        if (!permissionOptional.isPresent()) {
            pList.add(p);
        }
        role.setPermissions(mapPermissionListToPermissionStrings(pList));
    }

    private static Optional<Permission> getFunctionPermissionFromList(List<Permission> permissions, String pageCode, String functionToSet) {
        return permissions.stream()
                .filter(permission -> matchesFunctionToSet(permission, pageCode, functionToSet))
                .findFirst();
    }

    private static boolean matchesFunctionToSet(Permission permission, String pageCode, String functionToSet) {
        return permission.getPageCode().equals(pageCode) && permission.getFunctionAccess(functionToSet) != null;
    }

    private static List<Permission> mapPermissionStringsToPermissionList(List<String> permissionStrings) {
        return permissionStrings.stream()
                .map(Permission::new)
                .collect(Collectors.toList());
    }

    private static List<String> mapPermissionListToPermissionStrings(List<Permission> rolePermissions) {
        return rolePermissions.stream()
                .map(Objects::toString)
                .collect(Collectors.toList());
    }
}