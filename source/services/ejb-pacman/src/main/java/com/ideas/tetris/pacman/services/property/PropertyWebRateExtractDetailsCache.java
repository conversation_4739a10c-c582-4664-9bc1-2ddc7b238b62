package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.services.property.dto.WebRateExtractDetails;
import com.ideas.tetris.platform.common.cache.AbstractCache;

import java.util.Optional;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


@Component
@Transactional
public class PropertyWebRateExtractDetailsCache extends AbstractCache<Integer, WebRateExtractDetails> {

    @Override
    protected boolean isAsync() {
        return false;
    }

    @Override
    protected Optional<Integer> getRedisLifespan() {
        return Optional.of(0);
    }
}
