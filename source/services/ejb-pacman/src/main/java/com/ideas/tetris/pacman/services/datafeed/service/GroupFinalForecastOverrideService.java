package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.GroupFinalForecastOverrideDTO;
import com.ideas.tetris.pacman.services.groupfinalforecast.dto.GroupFinalForecastOverrideDto;
import com.ideas.tetris.pacman.services.groupfinalforecast.entity.GffFgOverride;
import com.ideas.tetris.pacman.services.groupfinalforecast.entity.GroupFinalForecastOverride;
import com.ideas.tetris.pacman.services.groupfinalforecast.repository.GroupFinalForecastOverrideRepository;
import com.ideas.tetris.pacman.services.security.User;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.time.LocalDateUtils;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class GroupFinalForecastOverrideService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
    private GroupFinalForecastOverrideRepository repository;

    public List<GroupFinalForecastOverrideDTO> getGroupFinalForecastOverride(Date startDate, Date endDate) {

        List<GroupFinalForecastOverrideDTO> groupFinalForecastOverrideDTOList = new ArrayList<>();

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("propertyId", PacmanWorkContextHelper.getPropertyId());
        parameters.put("startDate", startDate);
        parameters.put("endDate", endDate);


        List<GffFgOverride> gffFgOverrideList = tenantCrudService.findByNamedQuery(GffFgOverride.FIND_ACTIVE_OVERRIDES_FOR_OCCUPANCY_DATE_RANGE_WITHOUT_EXPIRATION_DATE, parameters);

        for (GffFgOverride gffFgOverride : gffFgOverrideList) {
            GroupFinalForecastOverrideDTO groupFinalForecastOverrideDTO = new GroupFinalForecastOverrideDTO();
            groupFinalForecastOverrideDTO.setOccupancyDate(gffFgOverride.getOccupancyDate());
            groupFinalForecastOverrideDTO.setForecastGroupCode(gffFgOverride.getForecastGroup().getCode());
            groupFinalForecastOverrideDTO.setGroupFinalForecastOverride(gffFgOverride.getFgOccupancyOverride());
            groupFinalForecastOverrideDTO.setOverrideExpirationDate(gffFgOverride.getExpirationDate());
            groupFinalForecastOverrideDTO.setOverrideLastModifiedOn(LocalDateUtils.toDate(gffFgOverride.getLastUpdatedDate()));
            groupFinalForecastOverrideDTO.setOverrideLastModifiedBy(getUserName(gffFgOverride.getLastUpdatedByUserId()));
            groupFinalForecastOverrideDTOList.add(groupFinalForecastOverrideDTO);
        }

        return groupFinalForecastOverrideDTOList;
    }

    private String getUserName(Integer userId) {
        return Optional.ofNullable(userId)
                .map(id -> tenantCrudService.find(User.class, id))
                .map(User::getName)
                .orElse("-");
    }

    public List<GroupFinalForecastOverrideDto> getGroupFinalForecastOverrides() {
        List<GroupFinalForecastOverride> gffOverrides = repository.findActiveOverrides();
        return gffOverrides.stream().map(gffo -> new GroupFinalForecastOverrideDto(gffo.getForecastGroup().getId(),
                        gffo.getAccomClass().getId(), gffo.getOccupancyDate(), gffo.getAcOccupancyOverride()))
                .collect(Collectors.toList());
    }

}
