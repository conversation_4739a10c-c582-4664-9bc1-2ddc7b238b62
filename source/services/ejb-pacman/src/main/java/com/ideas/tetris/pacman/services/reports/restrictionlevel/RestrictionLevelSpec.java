package com.ideas.tetris.pacman.services.reports.restrictionlevel;

import java.time.LocalDate;
import java.util.List;

public class RestrictionLevelSpec {

    private LocalDate startDate;
    private LocalDate endDate;
    private LocalDate changesSince;
    private List<Integer> accomTypeIds;
    private int isRollingDate;
    private String rollingStartDate;
    private String rollingEndDate;
    private String rollingBusinessDate;
    private String reportStyleKey;
    private String reportTypeKey;
    private boolean isSrpFplosAtTotalLevelEnabled;

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public LocalDate getChangesSince() {
        return changesSince;
    }

    public void setChangesSince(LocalDate changesSince) {
        this.changesSince = changesSince;
    }

    public List<Integer> getAccomTypeIds() {
        return accomTypeIds;
    }

    public void setAccomTypeIds(List<Integer> accomTypeIds) {
        this.accomTypeIds = accomTypeIds;
    }

    public int getIsRollingDate() {
        return isRollingDate;
    }

    public void setIsRollingDate(int isRollingDate) {
        this.isRollingDate = isRollingDate;
    }

    public String getRollingStartDate() {
        return rollingStartDate;
    }

    public void setRollingStartDate(String rollingStartDate) {
        this.rollingStartDate = rollingStartDate;
    }

    public String getRollingEndDate() {
        return rollingEndDate;
    }

    public void setRollingEndDate(String rollingEndDate) {
        this.rollingEndDate = rollingEndDate;
    }

    public String getRollingBusinessDate() {
        return rollingBusinessDate;
    }

    public void setRollingBusinessDate(String rollingBusinessDate) {
        this.rollingBusinessDate = rollingBusinessDate;
    }

    public String getReportStyleKey() {
        return reportStyleKey;
    }

    public void setReportStyleKey(String reportStyleKey) {
        this.reportStyleKey = reportStyleKey;
    }

    public String getReportTypeKey() {
        return reportTypeKey;
    }

    public void setReportTypeKey(String reportTypeKey) {
        this.reportTypeKey = reportTypeKey;
    }

    public boolean isFull() {
        return "common.full".equals(reportStyleKey);
    }

    public boolean isMinMaxLos() {
        return "common.min.los".equals(reportTypeKey) || "minmaxlos".equals(reportTypeKey);
    }

    public void setSrpFplosAtTotalLevelEnabled(boolean isSrpFplosAtTotalLevelEnabled) {
        this.isSrpFplosAtTotalLevelEnabled = isSrpFplosAtTotalLevelEnabled;
    }

    public boolean isSrpFplosAtTotalLevelEnabled() {
        return isSrpFplosAtTotalLevelEnabled;
    }
}
