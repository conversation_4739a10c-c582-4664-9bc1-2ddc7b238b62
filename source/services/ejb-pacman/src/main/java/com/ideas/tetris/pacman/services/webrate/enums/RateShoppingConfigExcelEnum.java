package com.ideas.tetris.pacman.services.webrate.enums;

public enum RateShoppingConfigExcelEnum {
    ROOM_CLASS_MAPPING("roomClassMappingCompetetiveRoomTypeToRoomClassMapping"),
    OCCUPANCY_BASED_CONSTRAINT_CONFIG("occupancyBasedConstraint"),
    CHANNEL_SETTINGS("channelSettings"),
    STANDARD_CMPC_CONFIG("standard.constraint"),
    COMPETITOR_SETTING("competitorSettings"),
    RATE_SHOPPING_SCHEDULES("RATE_SHOPPING_SCHEDULE"),
    RATE_ADJUSTMENT("webrate.competitor.rate.adjustment.title"),
    IGNORE_COMPETITOR_DATA("webrate.competitor.rate.excel.title");

    private String setting;

    RateShoppingConfigExcelEnum(String setting) {
        this.setting = setting;
    }

    public String getSetting() {
        return setting;
    }
}
