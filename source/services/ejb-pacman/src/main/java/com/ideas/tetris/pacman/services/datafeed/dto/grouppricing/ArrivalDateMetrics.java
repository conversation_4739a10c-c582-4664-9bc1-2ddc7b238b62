package com.ideas.tetris.pacman.services.datafeed.dto.grouppricing;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.toDate;
import static java.lang.Integer.parseInt;

public class ArrivalDateMetrics {
    private int uniqueGroupId;
    private Date arrivalDate;
    private BigDecimal metrics;

    public int getUniqueGroupId() {
        return uniqueGroupId;
    }

    private void setUniqueGroupId(int uniqueGroupId) {
        this.uniqueGroupId = uniqueGroupId;
    }

    public Date getArrivalDate() {
        return arrivalDate;
    }

    private void setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public BigDecimal getMetrics() {
        return Optional.ofNullable(metrics).orElse(BigDecimal.ZERO);
    }

    private void setMetrics(BigDecimal metrics) {
        this.metrics = metrics;
    }

    public static ArrivalDateMetrics mapRow(Object[] row) {
        final ArrivalDateMetrics arrivalDateMetrics = new ArrivalDateMetrics();
        arrivalDateMetrics.setUniqueGroupId(parseInt(GroupEvaluationData.getStringAt(row, 0)));
        arrivalDateMetrics.setArrivalDate(toDate(GroupEvaluationData.getStringAt(row, 1)));
        arrivalDateMetrics.setMetrics(GroupEvaluationData.getBigDecimalAt(row, 2));
        return arrivalDateMetrics;
    }
}
