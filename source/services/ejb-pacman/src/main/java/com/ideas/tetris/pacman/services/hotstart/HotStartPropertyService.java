package com.ideas.tetris.pacman.services.hotstart;


import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.hotstart.entity.HotstartParamPropertyAttributes;
import com.ideas.tetris.pacman.services.hotstart.entity.HotstartParamRuntimeAttributes;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttribute;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum;
import com.ideas.tetris.pacman.services.roa.attribute.service.ROAPropertyAttributeService;
import com.ideas.tetris.pacman.services.roa.forecastparams.entity.RuntimeParam;
import com.ideas.tetris.pacman.services.roa.forecastparams.service.ROARuntimeParamService;
import com.ideas.tetris.pacman.services.roa.service.ROAService;
import com.ideas.tetris.pacman.services.sas.entity.ForecastTask;
import com.ideas.tetris.pacman.services.sas.entity.ProcessGroup;
import com.ideas.tetris.pacman.services.syncflags.service.SyncDisplayNameService;
import com.ideas.tetris.pacman.util.CustomizedDisplayName;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.function.Function;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.IS_HOT_START_PROPERTY;
import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.PROPERTY_MAX_HOTSTART_DAYS_THRESHOLD;
import static com.ideas.tetris.pacman.common.configparams.IPConfigParamName.CORE_PROPERTY_MIN_CONSECUTIVE_DAYS_THRESHOLD;

@Component
@Transactional
public class HotStartPropertyService {

    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
    @Qualifier("globalCrudServiceBean")
    CrudService globalCrudService;
    @Autowired
    ROAPropertyAttributeService roaPropertyAttributeService;
    @Autowired
    ROARuntimeParamService roaRuntimeParamService;
    @Autowired
    ROAService roaService;
    @Autowired
    PacmanConfigParamsService configParamsService;
    @Autowired
    DateService dateService;
    @Autowired
    private SyncEventAggregatorService syncEventAggregatorService;
    @Autowired
    private SyncDisplayNameService syncDisplayNameService;

    @Autowired
    private AlertService alertService;

    private static final Logger LOGGER = Logger.getLogger(HotStartPropertyService.class);

    public String adjustSASParameters() {
        long days = getDaysSinceFirstArrivaldate();
        if (days == -1) {
            return "Hotel Arrival  Date is not available";
        }
        LOGGER.info("Hotel Arrival  Date is " + days + " days ago");
        Double parameterValue = configParamsService.getParameterValue(PROPERTY_MAX_HOTSTART_DAYS_THRESHOLD);
        if (days >= parameterValue) {
            removeSASParametersOverrides();
            configParamsService.addParameterValue(IS_HOT_START_PROPERTY.getParameterName(), "false");
            configParamsService.addParameterValue(CORE_PROPERTY_MIN_CONSECUTIVE_DAYS_THRESHOLD.getParameterName(), "14");
            alertService.createAlert(AlertType.SwitchFromHotStartToStandardBuild.getName(), "SwitchFromHotStartToStandardBuild.desc","");
            LOGGER.info("Alert trigger:Switch from hot start to standard build");
            return "Hotel Arrival  Date is more than 365 days. Disabling Hot Start Property";
        }
        adjustSASParametersByDays(days);
        return "Step Completed Successfully";
    }



    public long getDaysSinceFirstArrivaldate() {
        Date firstActualArrivalDate = getFirstActualArrivalDate();
        if (firstActualArrivalDate == null) {
            LOGGER.warn("Hotel Arrival  Date is not available");
            return -1;
        }
        Date caughtUpDate = dateService.getCaughtUpDate();
        if (caughtUpDate == null) {
            LOGGER.warn("Caught Up Date is not available");
            return -1;
        }
        return (caughtUpDate.getTime() - firstActualArrivalDate.getTime()) / (1000 * 60 * 60 * 24);
    }

    private void adjustSASParametersByDays(long days) {
        List<HotstartParamPropertyAttributes> allPropertyAttributes = globalCrudService.findAll(HotstartParamPropertyAttributes.class);
        List<HotstartParamRuntimeAttributes> allRuntimeAttributes = globalCrudService.findAll(HotstartParamRuntimeAttributes.class);

        adjustPropertyAttributesByDays(days, allPropertyAttributes);
        adjustRuntimeAttributesByDays(days, allRuntimeAttributes);
    }

    private void adjustPropertyAttributesByDays(long days, List<HotstartParamPropertyAttributes> allPropertyAttributes) {
        allPropertyAttributes.forEach(hotstartParamPropertyAttributes -> {
            String paramName = hotstartParamPropertyAttributes.getParamName();
            String value = getPropertyAttributeValueByDays(days, hotstartParamPropertyAttributes);
            if(isDefaultOverrideNeeded(value)){
                roaPropertyAttributeService.removePropertyAttributeOverrideForSpecifiedParameters(Arrays.asList(paramName));
            }
            if (!isDefaultOverrideNeeded(value)) {
                PropertyAttributeEnum propertyAttributeEnum = PropertyAttributeEnum.getByPropertyName(paramName);
                if (propertyAttributeEnum != null) {
                    PropertyAttribute propertyAttribute = roaPropertyAttributeService.getPropertyAttribute(propertyAttributeEnum);
                    if (propertyAttribute != null) {
                        roaPropertyAttributeService.save(propertyAttribute.getId(), value);
                        LOGGER.info("HotStart Property: Property Attribute " + paramName + " set to " + value + ".");
                    } else {
                        LOGGER.warn("No property attribute found for property name: " + paramName);
                    }
                } else {
                    LOGGER.warn("No property attribute enum found for property name: " + paramName);
                }
            }
        });
        LOGGER.info("Property Attributes adjusted successfully");
    }

    private static boolean isDefaultOverrideNeeded(String value) {
        return "DEFAULT".equalsIgnoreCase(value);
    }

    private void adjustRuntimeAttributesByDays(long days, List<HotstartParamRuntimeAttributes> allRuntimeAttributes) {
        allRuntimeAttributes.forEach(hotstartParamRuntimeAttributes -> {
            String taskName = hotstartParamRuntimeAttributes.getTaskName();
            String paramName = hotstartParamRuntimeAttributes.getParamName();
            String value = getRuntimeAttributeValueByDays(days, hotstartParamRuntimeAttributes);
            if(isDefaultOverrideNeeded(value)){
                roaRuntimeParamService.removeRuntimeOverridesForSpecifiedParameters(Arrays.asList(paramName));
            }
            if (!isDefaultOverrideNeeded(value)) {
                List<ProcessGroup> processGroups = roaService.findProcessGroups();
                processGroups.forEach(processGroup -> {
                    List<ForecastTask> compatibleForecastTasks = roaService.getCompatibleForecastTasks(processGroup.getProcessGroupId());
                    processForecastTasks(taskName, paramName, value, processGroup, compatibleForecastTasks);
                });
            }
        });
        LOGGER.info("Runtime Attributes adjusted successfully");

    }

    private void processForecastTasks(String taskName, String paramName, String value, ProcessGroup processGroup, List<ForecastTask> compatibleForecastTasks) {
        if(compatibleForecastTasks!=null) {
            compatibleForecastTasks.stream()
                    .filter(forecastTask -> forecastTask.getTaskName().equals(taskName))
                    .forEach(forecastTaskId -> {
                        List<RuntimeParam> forecastParameters = roaRuntimeParamService.getForecastParameters(forecastTaskId.getId());
                        processForecastParameters(paramName, value, processGroup, forecastTaskId, forecastParameters);
                    });
        }
        else {
            LOGGER.warn("No compatible forecast tasks found for process group: " + processGroup);
        }
    }

    private void processForecastParameters(String paramName, String value, ProcessGroup processGroup, ForecastTask forecastTaskId, List<RuntimeParam> forecastParameters) {
       if(forecastParameters !=null) {
           forecastParameters.stream()
                   .filter(runtimeParam -> runtimeParam.getParamName().equals(paramName))
                   .forEach(runtimeParamId -> {
                       roaRuntimeParamService.save(processGroup.getProcessGroupId(), forecastTaskId.getId(), runtimeParamId.getRuntimeParamId(), value);
                       LOGGER.info("HotStart Property: Runtime Attribute " + paramName + " set to " + value + ".");
                   });
       }else {
              LOGGER.warn("No forecast parameters found for forecast task: " + forecastTaskId);
       }
    }
    private String getRuntimeAttributeValueByDays(long days, HotstartParamRuntimeAttributes hotstartParamRuntimeAttributes) {
        return getAttributeValueByDays(days, hotstartParamRuntimeAttributes,
                HotstartParamRuntimeAttributes::getValue60Days,
                HotstartParamRuntimeAttributes::getValue90Days,
                HotstartParamRuntimeAttributes::getValue120Days,
                HotstartParamRuntimeAttributes::getValue150Days,
                HotstartParamRuntimeAttributes::getValue180Days);
    }

    private String getPropertyAttributeValueByDays(long days, HotstartParamPropertyAttributes propertyAttribute) {
        return getAttributeValueByDays(days, propertyAttribute,
                HotstartParamPropertyAttributes::getValue60Days,
                HotstartParamPropertyAttributes::getValue90Days,
                HotstartParamPropertyAttributes::getValue120Days,
                HotstartParamPropertyAttributes::getValue150Days,
                HotstartParamPropertyAttributes::getValue180Days);
    }
    private <T> String getAttributeValueByDays(long days, T attribute, Function<T, String> getValue60Days, Function<T, String> getValue90Days, Function<T, String> getValue120Days, Function<T, String> getValue150Days, Function<T, String> getValue180Days) {
        if (days < 90) {
            return getValue60Days.apply(attribute);
        } else if (days < 120) {
            return getValue90Days.apply(attribute);
        } else if (days < 150) {
            return getValue120Days.apply(attribute);
        } else if (days < 180) {
            return getValue150Days.apply(attribute);
        } else {
            return getValue180Days.apply(attribute);
        }
    }
    public Date getFirstActualArrivalDate() {
       return (Date) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), TotalActivity.GET_FIRST_ACTUAL_ARRIVAL_DATE);
    }

    public void removeSASParametersOverrides() {
        List<HotstartParamPropertyAttributes> allPropertyAttributes = globalCrudService.findAll(HotstartParamPropertyAttributes.class);
        List<HotstartParamRuntimeAttributes> allRuntimeAttributes = globalCrudService.findAll(HotstartParamRuntimeAttributes.class);
        removeRuntimeOverrides(allRuntimeAttributes);
        removePropertyOverrides(allPropertyAttributes);
        registerSyncEventWhenSASParamsAreOverridden();
    }

    private void registerSyncEventWhenSASParamsAreOverridden() {
        if (syncEventAggregatorService.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)) {
            syncDisplayNameService.addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                    CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
        }
    }

    private void removePropertyOverrides(List<HotstartParamPropertyAttributes> allPropertyAttributes) {
        List<String> propertyNames = new ArrayList<>();
        allPropertyAttributes.forEach(hotstartParamPropertyAttributes -> {
            String paramName = hotstartParamPropertyAttributes.getParamName();
            propertyNames.add(paramName);
        });
        roaPropertyAttributeService.removePropertyAttributeOverrideForSpecifiedParameters(propertyNames);
    }

    private void removeRuntimeOverrides(List<HotstartParamRuntimeAttributes> allRuntimeAttributes) {
        List<String> propertyNames = new ArrayList<>();
        allRuntimeAttributes.forEach(hotstartParamRuntimeAttributes -> {
            String paramName = hotstartParamRuntimeAttributes.getParamName();
            propertyNames.add(paramName);
        });
        roaRuntimeParamService.removeRuntimeOverridesForSpecifiedParameters(propertyNames);
    }
}
