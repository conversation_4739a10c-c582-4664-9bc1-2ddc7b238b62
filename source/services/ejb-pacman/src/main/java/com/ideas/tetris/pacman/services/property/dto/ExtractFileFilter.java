package com.ideas.tetris.pacman.services.property.dto;

import java.io.File;
import java.io.FileFilter;

public class ExtractFileFilter implements FileFilter {
    @Override
    public boolean accept(File candidate) {
        // e.g. DALMC.20110225.0000.tar.Z
        boolean shouldAccept = false;
        if (!candidate.isDirectory()) {
            String[] segments = candidate.getName().split("[_.]");
            shouldAccept = isValidCrsExtract(segments) || isValidHistoralCrsExtract(segments);
        }
        return shouldAccept;
    }

    private static boolean isValidCrsExtract(String[] segments) {
        return segments.length == AbstractExtractDetails.FILE_NAME_SEGMENTS_STANDARD &&
                ExtractDetails.FILE_PART_TAR.equals(segments[AbstractExtractDetails.FILE_NAME_SEGMENTS_STANDARD - 2]) &&
                lastSegmentZipOrZ(segments[AbstractExtractDetails.FILE_NAME_SEGMENTS_STANDARD - 1]);
    }

    public static boolean lastSegmentZipOrZ(String lastSegment) {
        return ExtractDetails.FILE_PART_BALL_ZIP.equalsIgnoreCase(lastSegment)
                || ExtractDetails.FILE_PART_BALL.equals(lastSegment);
    }

    private static boolean isValidHistoralCrsExtract(String[] segments) {
        return segments.length == AbstractExtractDetails.FILE_NAME_SEGMENTS_HISTORY &&
                ExtractDetails.FILE_PART_HISTORY.equals(segments[ExtractDetails.SEGMENT_HISTORY]) &&
                isValidHistoralCrsExtractSuffix(segments);
    }

    private static boolean isValidHistoralCrsExtractSuffix(String[] segments) {
        return ExtractDetails.FILE_PART_TAR.equals(segments[AbstractExtractDetails.FILE_NAME_SEGMENTS_HISTORY - 2]) &&
                ExtractDetails.FILE_PART_BALL.equals(segments[AbstractExtractDetails.FILE_NAME_SEGMENTS_HISTORY - 1]);
    }
}
