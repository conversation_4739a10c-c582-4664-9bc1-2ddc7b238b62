package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.OperationsForecastArrivalsDeparturesDTO;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import javax.inject.Inject;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Component
@Transactional
public class OperationsForecastArrivalsDeparturesDataService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;


    public Boolean loadOperationsForecastArrivalsDeparturesDataIntoPacman(List<OperationsForecastArrivalsDeparturesDTO> data) {
        StringBuilder queryToInsertData = new StringBuilder();
        int decisionId = 10;

        data.forEach(operationsDTO -> {
            queryToInsertData.append("INSERT INTO [dbo].[Arr_Dep_FCST] ([Decision_ID] ,[Property_ID],[Occupancy_DT],[Arrival_OTB],[Departures_OTB],[Staythru_OTB]");
            queryToInsertData.append(",[Arr_Fcst],[Dep_Fcst],[Staythru_Fcst],[Month_ID],[Year_ID],[CreateDate_DTTM]) ");
            queryToInsertData.append("values (" + decisionId + ",011033,'" + DateUtil.formatDate(operationsDTO.getOccupancyDate(), DateUtil.DEFAULT_DATE_FORMAT) + "'," + operationsDTO.getOnBooksArrivals() + "," + operationsDTO.getOnBooksDepartures() + "," + operationsDTO.getOnBooksStayThrus() + ",");
            queryToInsertData.append(operationsDTO.getForecastArrivals() + "," + operationsDTO.getForecastDepartures() + "," + operationsDTO.getForecastStayThrus() + ",");
            queryToInsertData.append("03,2016,'2016-02-21T18:10:00');");
            queryToInsertData.append("insert into PP_Occupancy_FCST values (011033, " + decisionId + ", '" + DateUtil.formatDate(operationsDTO.getOccupancyDate(), DateUtil.DEFAULT_DATE_FORMAT) + "'," + operationsDTO.getForecastAdults() + ",");
            queryToInsertData.append(operationsDTO.getForecastChildren() + ", '2017-02-21T18:10:00');");
        });

        if (!queryToInsertData.toString().isEmpty()) {
            tenantCrudService.executeUpdateByNativeQuery(queryToInsertData.toString());
            return true;
        }
        return false;
    }


    public Boolean deleteData() {

        StringBuilder dataToDelete = new StringBuilder("DELETE FROM [dbo].[Arr_Dep_FCST] WHERE occupancy_DT between '2016-03-23' and '2016-03-24';");
        dataToDelete.append("delete from PP_Occupancy_FCST where occupancy_dt between '2016-03-23' and '2016-03-24';");
        tenantCrudService.executeUpdateByNativeQuery(dataToDelete.toString());
        return true;
    }

}
