package com.ideas.tetris.pacman.services.reports;

public enum ReportType {
    BOOKING_PACE("bookingPace"),
    DATA_EXTRACTION("dataExtraction"),
    FORECAST_VALIDATION("forecastValidation"),
    INPUT_OVERRIDE("inputOverride"),
    OUTPUT_OVERRIDE("outputOverride"),
    PICKUP_AND_CHANGE("pickupAndChange"),
    PRICING_OVERRIDE_HISTORY("pricingOverrideHistory"),
    PRICING_PACE("pricingPace"),
    PRICING("pricing"),
    USER_ACTIVITY_LOG("userActivityLog"),
    BOOKING_SITUATION_REPORT("bookingSituation"),
    PERFORMANCE_COMPARISON_REPORT("performanceComparison"),
    COMPARITIVE_BOOKING_PACE("compbookingPace"),
    SRP_PRODUCTION_REPORT("srpReport"),
    MCAT_REPORT("mcat"),
    INVERNTORY_HISTORY_REPORT("inventoryhistoryreport");

    private String name;

    private ReportType(String label) {
        this.name = label;
    }

    public static ReportType fromString(String name) {

        for (ReportType type : ReportType.values()) {
            if (type.name == name) {
                return type;
            }
        }
        return null;
    }
}
