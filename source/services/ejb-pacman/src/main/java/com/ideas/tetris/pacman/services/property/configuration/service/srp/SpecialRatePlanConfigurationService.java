package com.ideas.tetris.pacman.services.property.configuration.service.srp;

import com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.RatchetClient;
import com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.RatchetExplodedMcatAttributes;
import com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.RatchetProperty;
import com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.RatchetSrpAttributes;
import com.ideas.tetris.pacman.services.crudservice.RatchetCrudServiceBean;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.SpecialRatePlanPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@SpecialRatePlanConfigurationService.Qualifier
@Component
@Transactional
public class SpecialRatePlanConfigurationService extends AbstractPropertyConfigurationService {

    private static final String RATCHET_CLIENT = "ratchetClient";

    private static final Logger LOGGER = Logger.getLogger(SpecialRatePlanConfigurationService.class);

    static final Date ATTRIBUTED_START_DATE = DateUtil.getDateWithoutTime(1, Calendar.JANUARY, 1980);
    static final Date ATTRIBUTED_END_DATE = DateUtil.getDateWithoutTime(31, Calendar.DECEMBER, 2173);
    static final Integer STATUS = 1;

    @RatchetCrudServiceBean.Qualifier
    @Autowired
    @org.springframework.beans.factory.annotation.Qualifier("ratchetCrudServiceBean")
	protected CrudService ratchetCrudService;

    @Override
    public PropertyConfigurationRecordType getPropertyConfigurationRecordType() {
        return PropertyConfigurationRecordType.SRP;
    }

    @Override
    public List<PropertyConfigurationRecordFailure> doValidate(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId) {
        List<PropertyConfigurationRecordFailure> failures = new ArrayList<PropertyConfigurationRecordFailure>();
        SpecialRatePlanPropertyConfigurationDto dto = (SpecialRatePlanPropertyConfigurationDto) propertyConfigurationDto;

		if (StringUtils.isEmpty(dto.getRatePlanId())) {
			failures.add(new PropertyConfigurationRecordFailure("Rate Plan ID is required"));
		}

		if (StringUtils.isEmpty(dto.getMarketCategory())) {
			failures.add(new PropertyConfigurationRecordFailure("Market Segment is required"));
		} else {
			Long count = ratchetCrudService.findByNamedQuerySingleResult(RatchetExplodedMcatAttributes.COUNT_MCAT_BY_RATCHET_CLIENT, QueryParameter.with(RATCHET_CLIENT, findRatchetClient()).and("marketCategory", dto.getMarketCategory()).parameters());
			if (count < 1) {
				failures.add(new PropertyConfigurationRecordFailure("Invalid Market Segment"));
			}
		}

		validateYN(failures, "Block", dto.getBlock());
		validateYN(failures, "Qualified", dto.getQualified());
		validateYN(failures, "Linked", dto.getLinked());
		validateYN(failures, "Package", dto.getPackage());
		validateYN(failures, "Fenced", dto.getFenced());
		validateYN(failures, "Yieldable", dto.getYieldable());

		if (StringUtils.isEmpty(dto.getControlType()) || !("F".equalsIgnoreCase(dto.getControlType()) || "N".equalsIgnoreCase(dto.getControlType()) || "S".equalsIgnoreCase(dto.getControlType()))) {
			failures.add(new PropertyConfigurationRecordFailure("Control Type must be F or N or S"));
		} else {
			if ("N".equalsIgnoreCase(dto.getYieldable()) && !"N".equalsIgnoreCase(dto.getControlType())) {
				failures.add(new PropertyConfigurationRecordFailure("Control Type must be N when Yieldable is N"));
			}
			if ("Y".equalsIgnoreCase(dto.getYieldable()) && !("F".equalsIgnoreCase(dto.getControlType()) || "S".equalsIgnoreCase(dto.getControlType()))) {
				failures.add(new PropertyConfigurationRecordFailure("Control Type must be F or S when Yieldable is Y"));
			}
		}

        return failures;
    }

	public void validateYN(List<PropertyConfigurationRecordFailure> failures,
			String name, String value) {
		if (StringUtils.isEmpty(value)
				|| (!"Y".equalsIgnoreCase(value) && !"N".equalsIgnoreCase(value))) {
			failures.add(new PropertyConfigurationRecordFailure(name
					+ " must be Y or N"));
		}
	}

	public RatchetClient findRatchetClient() {
		WorkContextType workContext = PacmanThreadLocalContextHolder.getWorkContext();
		RatchetClient ratchetClient = ratchetCrudService.findByNamedQuerySingleResult(RatchetClient.BY_CLIENT_CODE, QueryParameter.with("clientCode", workContext.getClientCode()).parameters());
		if (ratchetClient == null) {
			throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Unable to find ratchet client for client code " + workContext.getClientCode());
		}
		return ratchetClient;
	}

    @Override
    public void doHandle(PropertyConfigurationDto pcd, Integer propertyId) {
        SpecialRatePlanPropertyConfigurationDto dto = (SpecialRatePlanPropertyConfigurationDto) pcd;

		// we need to find the ratchet client and property
		RatchetClient ratchetClient = findRatchetClient();
		RatchetProperty ratchetProperty = ratchetCrudService.findByNamedQuerySingleResult(RatchetProperty.BY_RATCHET_PROPERTY_CODE_AND_CLIENT_ID, QueryParameter.with("propertyCode", dto.getPropertyCode()).and("clientId", ratchetClient.getId()).parameters());
		if (ratchetProperty == null) {
			throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Unable to find ratchet property for client id " + ratchetClient.getId() + " and property code " + dto.getPropertyCode());
		}

		RatchetSrpAttributes srpAttributes = ratchetCrudService.findByNamedQuerySingleResult(RatchetSrpAttributes.BY_RATCHET_PROPERTY_AND_SRP_ID, QueryParameter.with("ratchetProperty", ratchetProperty).and("srpId", dto.getRatePlanId()).parameters());
		if (srpAttributes == null) {
			srpAttributes = new RatchetSrpAttributes();
			srpAttributes.setRatchetProperty(ratchetProperty);
			srpAttributes.setSrpId(dto.getRatePlanId());
			srpAttributes.setAttributedStartDate(LocalDate.fromDateFields(ATTRIBUTED_START_DATE));
			srpAttributes.setAttributedEndDate(LocalDate.fromDateFields(ATTRIBUTED_END_DATE));
			srpAttributes.setStatus(STATUS);
		}

		// now try to find the sub market category if applicable
		String subMarketCategory = null;
		// first check if this is for a market catagory that is not mcat+ eligible
		RatchetExplodedMcatAttributes explodedAttributes = ratchetCrudService.findByNamedQuerySingleResult(RatchetExplodedMcatAttributes.BY_RATCHET_CLIENT_AND_MCAT_NOT_MCAT_PLUS_ELIGIBLE, QueryParameter.with(RATCHET_CLIENT, ratchetClient).and("marketCategory", dto.getMarketCategory()).parameters());
		if (explodedAttributes == null) {
			// now look by mcat and attributes
			explodedAttributes = ratchetCrudService.findByNamedQuerySingleResult(RatchetExplodedMcatAttributes.BY_RATCHET_CLIENT_AND_MCAT_AND_ATTRIBUTES, QueryParameter.with(RATCHET_CLIENT, ratchetClient).and("marketCategory", dto.getMarketCategory()).and("attributes", dto.getAttributes()).parameters());
			if (explodedAttributes != null) {
				subMarketCategory = explodedAttributes.getSubMarketCategory();
			}
		} else {
			subMarketCategory = explodedAttributes.getSubMarketCategory();
		}
		if (subMarketCategory == null) {
			throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Unable to find sub market category (including default) for client " + ratchetClient.getCode() + "and market category " + dto.getMarketCategory() + " and attributes " + dto.getAttributes());
		}

		srpAttributes.setMarketCategory(dto.getMarketCategory());
		srpAttributes.setSubMarketCategory(subMarketCategory);
		srpAttributes.setQualified(dto.getQualified());
		srpAttributes.setBlock(dto.getBlock());
		srpAttributes.setLinked(dto.getLinked());
		srpAttributes.setPackageCode(dto.getPackage());
		srpAttributes.setFenced(dto.getFenced());
		srpAttributes.setYieldable(dto.getYieldable());
		srpAttributes.setControlType(dto.getControlType());
		srpAttributes.setAttributes(dto.getAttributes());

        if (!srpAttributes.isPersisted()) {
            LOGGER.info("Creating RatchetSrpAttributes for Property: " + pcd.getPropertyCode() + " and rate plan: " + dto.getRatePlanId());
            ratchetCrudService.save(srpAttributes);
        } else {
            LOGGER.info("Updating RatchetSrpAttributes for Property: " + pcd.getPropertyCode() + " and rate plan: " + dto.getRatePlanId());
            ratchetCrudService.save(srpAttributes);
        }
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
