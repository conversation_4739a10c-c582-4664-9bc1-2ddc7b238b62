package com.ideas.tetris.pacman.services.reports.bookingbylos;

import java.time.LocalDate;

/**
 * Created by idnsmu on 3/26/2015.
 */
public class BookingByLOS {
    private LocalDate startDate;
    private LocalDate endDate;
    private LocalDate asOfBookingDate;
    private int bookingStay;
    private int isRollingDate;
    private String rollingStartDate;
    private String rollingEndDate;
    private String rollingAsOfBookingDate;

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public LocalDate getAsOfBookingDate() {
        return asOfBookingDate;
    }

    public void setAsOfBookingDate(LocalDate asOfBookingDate) {
        this.asOfBookingDate = asOfBookingDate;
    }

    public int getBookingStay() {
        return bookingStay;
    }

    public void setBookingStay(int bookingStay) {
        this.bookingStay = bookingStay;
    }

    public String getRollingStartDate() {
        return rollingStartDate;
    }

    public void setRollingStartDate(String rollingStartDate) {
        this.rollingStartDate = rollingStartDate;
    }

    public String getRollingEndDate() {
        return rollingEndDate;
    }

    public void setRollingEndDate(String rollingEndDate) {
        this.rollingEndDate = rollingEndDate;
    }

    public String getRollingAsOfBookingDate() {
        return rollingAsOfBookingDate;
    }

    public void setRollingAsOfBookingDate(String rollingAsOfBookingDate) {
        this.rollingAsOfBookingDate = rollingAsOfBookingDate;
    }

    public int getIsRollingDate() {
        return isRollingDate;
    }

    public void setIsRollingDate(int isRollingDate) {
        this.isRollingDate = isRollingDate;
    }
}
