package com.ideas.tetris.pacman.services.webrate.dto;

import lombok.*;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
@EqualsAndHashCode
public class OccupancyBasedCMPCConfigExcelDto {
    private String productName;
    private String accomClass;
    private String category;
    private Date startDate;
    private Date endDate;
    private String mobSunday;
    private String mobMonday;
    private String mobTuesday;
    private String mobWednesday;
    private String mobThursday;
    private String mobFriday;
    private String mobSaturday;
    private String mmpSunday;
    private String mmpMonday;
    private String mmpTuesday;
    private String mmpWednesday;
    private String mmpThursday;
    private String mmpFriday;
    private String mmpSaturday;
}