package com.ideas.tetris.pacman.services.property.dto;

import com.ideas.tetris.pacman.services.sas.entity.MarkDateDataType;

import java.util.Date;

public class PropertyExcludedDates {
    private Integer entityId;
    private Date startDate;
    private Date endDate;
    private String notes;
    private ConfigOperation operation = ConfigOperation.VIEW;
    private MarkDateDataType configDataType;

    private Integer ConfigDataID;
    private String configDataTypeName;

    public Integer getEntityId() {
        return entityId;
    }

    public void setEntityId(Integer entityId) {
        this.entityId = entityId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public ConfigOperation getOperation() {
        return operation;
    }

    public void setOperation(ConfigOperation operation) {
        this.operation = operation;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public MarkDateDataType getConfigDataType() {
        return configDataType;
    }

    public void setConfigDataType(MarkDateDataType configDataType) {
        this.configDataType = configDataType;
    }

    public Integer getConfigDataID() {
        return ConfigDataID;
    }

    public void setConfigDataID(Integer configDataID) {
        ConfigDataID = configDataID;
    }

    public String getConfigDataTypeName() {
        return configDataTypeName;
    }

    public void setConfigDataTypeName(String configDataTypeName) {
        this.configDataTypeName = configDataTypeName;
    }
}
