package com.ideas.tetris.pacman.services.reports.performancecomparison;


import com.ideas.tetris.pacman.services.accommodation.dto.AccomClassSummary;
import com.ideas.tetris.pacman.services.marketsegment.dto.ForecastGroupSummary;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessType;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;

import java.time.LocalDate;

/**
 * Created by idnmal on 3/24/14.
 */
public class PerformanceComparisonReportFilterParams {
    private LocalDate analysisStartDate;
    private LocalDate analysisEndDate;
    private LocalDate comparisonStartDate;
    private LocalDate comparisonEndDate;
    private Integer paceDays;
    private ForecastGroupSummary forecastGroup;
    private MktSeg marketSegment;
    private BusinessType businessType;
    private BusinessGroup businessView;
    private Integer los;
    private Boolean rollingDateSelected;
    private String rollingAnalysisStartDate;
    private String rollingAnalysisEndDate;
    private String rollingComparisonStartDate;
    private AccomClassSummary roomClass;
    private WebrateCompetitors webrateCompetitor;
    private Object emptyObject;
    private boolean competitorFieldEnabled;
    private String barDecision;
    private boolean includeDiscontinuedMS;

    public boolean getCompetitorFieldEnabled() {
        return competitorFieldEnabled;
    }

    public void setCompetitorFieldEnabled(boolean competitorFieldEnabled) {
        this.competitorFieldEnabled = competitorFieldEnabled;
    }

    private PerformanceComparisonReportViewTypeEnum reportViewTypeEnum;

    public PerformanceComparisonReportViewTypeEnum getReportViewTypeEnum() {
        return reportViewTypeEnum;
    }

    public void setReportViewTypeEnum(PerformanceComparisonReportViewTypeEnum reportViewTypeEnum) {
        this.reportViewTypeEnum = reportViewTypeEnum;
    }

    public LocalDate getAnalysisStartDate() {
        return analysisStartDate;
    }

    public void setAnalysisStartDate(LocalDate analysisStartDate) {

        this.analysisStartDate = analysisStartDate;
    }

    public LocalDate getAnalysisEndDate() {
        return analysisEndDate;
    }

    public void setAnalysisEndDate(LocalDate analysisEndDate) {
        this.analysisEndDate = analysisEndDate;
    }

    public LocalDate getComparisonStartDate() {
        return comparisonStartDate;
    }

    public void setComparisonStartDate(LocalDate comparisonStartDate) {
        this.comparisonStartDate = comparisonStartDate;
    }

    public LocalDate getComparisonEndDate() {
        return comparisonEndDate;
    }

    public void setComparisonEndDate(LocalDate comparisonEndDate) {
        this.comparisonEndDate = comparisonEndDate;
    }

    public Integer getPaceDays() {
        return paceDays;
    }

    public void setPaceDays(Integer paceDays) {
        this.paceDays = paceDays;
    }


    public Boolean getRollingDateSelected() {
        return rollingDateSelected;
    }

    public void setRollingDateSelected(Boolean rollingDateSelected) {
        this.rollingDateSelected = rollingDateSelected;
    }


    public String getRollingAnalysisStartDate() {
        return rollingAnalysisStartDate;
    }

    public void setRollingAnalysisStartDate(String rollingAnalysisStartDate) {
        this.rollingAnalysisStartDate = rollingAnalysisStartDate;
    }

    public String getRollingAnalysisEndDate() {
        return rollingAnalysisEndDate;
    }

    public void setRollingAnalysisEndDate(String rollingAnalysisEndDate) {
        this.rollingAnalysisEndDate = rollingAnalysisEndDate;
    }


    public String getRollingComparisonStartDate() {
        return rollingComparisonStartDate;
    }

    public void setRollingComparisonStartDate(String rollingComparisonStartDate) {
        this.rollingComparisonStartDate = rollingComparisonStartDate;
    }


    public AccomClassSummary getRoomClass() {
        return roomClass;
    }

    public void setRoomClass(AccomClassSummary roomClass) {
        this.roomClass = roomClass;
    }

    public WebrateCompetitors getWebrateCompetitor() {
        return webrateCompetitor;
    }

    public void setWebrateCompetitor(WebrateCompetitors webrateCompetitor) {
        this.webrateCompetitor = webrateCompetitor;
    }

    public ForecastGroupSummary getForecastGroup() {
        return forecastGroup;
    }

    public void setForecastGroup(ForecastGroupSummary forecastGroup) {
        this.forecastGroup = forecastGroup;
    }

    public MktSeg getMarketSegment() {
        return marketSegment;
    }

    public void setMarketSegment(MktSeg marketSegment) {
        this.marketSegment = marketSegment;
    }

    public BusinessType getBusinessType() {
        return businessType;
    }

    public void setBusinessType(BusinessType businessType) {
        this.businessType = businessType;
    }

    public BusinessGroup getBusinessView() {
        return businessView;
    }

    public void setBusinessView(BusinessGroup businessView) {
        this.businessView = businessView;
    }

    public Integer getLos() {
        return los;
    }

    public void setLos(Integer los) {
        this.los = los;
    }

    public Object getEmptyObject() {
        return emptyObject;
    }

    public void setEmptyObject(Object emptyObject) {
        this.emptyObject = emptyObject;
    }

    public String getBarDecision() {
        return barDecision;
    }

    public void setBarDecision(String barDecision) {
        this.barDecision = barDecision;
    }

    public boolean isIncludeDiscontinuedMS() {
        return includeDiscontinuedMS;
    }

    public void setIncludeDiscontinuedMS(boolean includeDiscontinuedMS) {
        this.includeDiscontinuedMS = includeDiscontinuedMS;
    }
}
