package com.ideas.tetris.pacman.services.security;

import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.util.bean.SpringBeanLocator;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class AsyncUserService {
    private UserService userService;

    @Async
    public void clearUnathourizedDefaultPropertyFromLdap(Integer userId, List<Integer> authorizedPropertyIds,
                                                         WorkContextType workContextType, TetrisPrincipal tetrisPrincipal) {
        getUserService().clearUnathourizedDefaultPropertyFromLdap(userId, authorizedPropertyIds, workContextType, tetrisPrincipal);
    }

    private UserService getUserService() {
        if (userService == null) {
            userService = SpringBeanLocator.getBeanByType(UserService.class);
        }
        return userService;
    }
}
