package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ConsolidatedPropertyView;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.property.dto.ExtractDetails;
import com.ideas.tetris.pacman.services.property.dto.PropertySearchCriteria;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.Stage;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class FilterReadyForCatchup {
    private static final int CATCHUP_READY_MISSING_FILES_DAYS = 7;

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;
    @Autowired
	protected AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
	protected AuthorizationService authorizationService;
    @Autowired
	protected ExtractDetailsServiceLocal extractDetailsService;

    @SuppressWarnings({"squid:S3776", "squid:MethodCyclomaticComplexity", "checkstyle:com.puppycrawl.tools.checkstyle.checks.metrics.NPathComplexityCheck"})
    public List<ConsolidatedPropertyView> filter(final List<ConsolidatedPropertyView> properties, final PropertySearchCriteria searchCriteria) {
        List<ConsolidatedPropertyView> filteredProperties = properties;
        if (searchCriteria.isReadyForCatchup()) {
            filteredProperties = new ArrayList<ConsolidatedPropertyView>();
            boolean isReadyForCatchup = true;
            for (ConsolidatedPropertyView property : properties) {
                isReadyForCatchup = true;
                ExtractDetails extractDetails = null;
                extractDetails = extractDetailsService.getExtractDetails(property.getPropertyId());
                if (isReadyForCatchup && extractDetails == null) {
                    isReadyForCatchup = false;
                }
                if (isReadyForCatchup && !Stage.DATA_CAPTURE.getCode().equals(property.getStage().getCode())) {
                    isReadyForCatchup = false;
                }
                if (isReadyForCatchup && (getHistoricalExtractDate(extractDetails) == null || property.getSrpAttributionExtractDate() == null)) {
                    isReadyForCatchup = false;
                }
                Date firstIncomingDate = getFirstIncomingExtractDate(extractDetails);
                if (isReadyForCatchup && !(firstIncomingDate != null && firstIncomingDate.equals(getHistoricalExtractDate(extractDetails)))) {
                    isReadyForCatchup = false;
                }
                if (isReadyForCatchup) {
                    isReadyForCatchup = extractDetails.isSrpAttributeExtractAvailable();
                }
                if (isReadyForCatchup && exceedsAllowedMissingCrsFilesDates(extractDetails)) {
                    isReadyForCatchup = false;
                }

                if (isReadyForCatchup) {
                    filteredProperties.add(property);
                }
            }
        }
        return filteredProperties;
    }

    private Date getHistoricalExtractDate(ExtractDetails extractDetails) {
        if (extractDetails == null || extractDetails.getHistoricalExtractDate() == null) {
            return null;
        }
        return extractDetails.getHistoricalExtractDate().getTime();
    }

    private Date getFirstIncomingExtractDate(ExtractDetails extractDetails) {
        if (extractDetails == null || extractDetails.getFirstIncomingExtractDate() == null) {
            return null;
        }
        return extractDetails.getFirstIncomingExtractDate().getTime();
    }

    @SuppressWarnings({"squid:CallToDeprecatedMethod"})
    private boolean exceedsAllowedMissingCrsFilesDates(ExtractDetails extractDetails) {
        boolean exceedsAllowedMissingCrsFilesDates = false;
        List<Date> incomingExtractDates = null;
        incomingExtractDates = new ArrayList<Date>();
        for (DateParameter idp : extractDetails.getIncomingExtractDates()) {
            incomingExtractDates.add(idp.getTime());
        }

        Date previousDate = incomingExtractDates.get(0);
        for (Date currentDate : incomingExtractDates) {
            long missingFilesForDays = Math.abs(DateUtil.daysBetween(previousDate, currentDate));
            if (missingFilesForDays > CATCHUP_READY_MISSING_FILES_DAYS) {
                exceedsAllowedMissingCrsFilesDates = true;
                break;
            }
            previousDate = currentDate;
        }
        return exceedsAllowedMissingCrsFilesDates;
    }
}
