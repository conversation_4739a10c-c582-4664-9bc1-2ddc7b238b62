package com.ideas.tetris.pacman.services.upgrade;

import java.io.Serializable;

public class MovePropertyRequestDTO implements Serializable {
    private static final long serialVersionUID = -599648494425153269L;

    private Integer propertyId;
    private String databaseServerName;
    private String sasServerName;

    public MovePropertyRequestDTO() {
    }

    public MovePropertyRequestDTO(Integer propertyId, String databaseServerName, String sasServerName) {
        this.propertyId = propertyId;
        this.databaseServerName = databaseServerName;
        this.sasServerName = sasServerName;
    }

    public Integer getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Integer propertyId) {
        this.propertyId = propertyId;
    }

    public String getDatabaseServerName() {
        return databaseServerName;
    }

    public void setDatabaseServerName(String databaseServerName) {
        this.databaseServerName = databaseServerName;
    }

    public String getSasServerName() {
        return sasServerName;
    }

    public void setSasServerName(String sasServerName) {
        this.sasServerName = sasServerName;
    }
}
