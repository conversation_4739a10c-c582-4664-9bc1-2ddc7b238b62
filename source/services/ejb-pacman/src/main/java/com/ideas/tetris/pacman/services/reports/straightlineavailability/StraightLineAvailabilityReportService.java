package com.ideas.tetris.pacman.services.reports.straightlineavailability;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.security.StraightLineAvailabilityUtility;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.StraightLineAvailabilityBaseDataDTO;
import com.ideas.tetris.pacman.services.straightlineavailability.StraightLineAvailabilityService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class StraightLineAvailabilityReportService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;
    @Autowired
	private DateService dateService;
    @Autowired
	private PacmanConfigParamsService configParamsService;
    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;

    final static int MINLOS = 2;
    final static int MAXLOS = 7;

    private final String straightLineBaseDataQuery = "select * from\n" +
            "(\n" +
            "\tselect rc.Accom_Class_ID,aa.Occupancy_DT,-1 as  Accom_Type_ID,\n" +
            "\tSUM(aa.Accom_Capacity) + SUM(doa.Overbooking_Decision) - sum(aa.Rooms_Sold) - sum(Rooms_Not_Avail_Maint) - sum(Rooms_Not_Avail_Other) as rooms_Remaining\n" +
            "\t ,rc.accom_class_code from Accom_Activity aa\n" +
            "\tinner join  Accom_Type rt \n" +
            "\ton aa.Accom_Type_ID = rt.Accom_Type_ID and aa.Property_ID = rt.Property_ID\n" +
            "\tinner join accom_class rc \n" +
            "\ton rc.Accom_Class_ID = rt.Accom_Class_ID and rc.Property_ID = rt.Property_ID\n" +
            "\tinner join Decision_Ovrbk_Accom doa \n" +
            "\ton doa.Accom_Type_ID = rt.Accom_Type_ID and doa.Occupancy_DT = aa.Occupancy_DT and aa.Property_ID = doa.Property_ID\n" +
            "\twhere rt.Status_ID = 1 and rc.Status_ID = 1 and rt.Property_ID = :Property_ID and aa.Occupancy_DT between :startDate and :endDate \n" +
            "\tgroup by aa.Property_ID,rc.Accom_Class_ID,rc.accom_class_code,aa.Occupancy_DT\n" +
            ") RCLevel\n" +
            "UNION ALL\n" +
            "(\n" +
            "\tselect rc.Accom_Class_ID,  aa.Occupancy_DT, rt.Accom_Type_ID,\n" +
            "\taa.Accom_Capacity + doa.Overbooking_Decision - aa.Rooms_Sold - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other as rooms_Remaining\n" +
            "\t ,rc.accom_class_code from Accom_Activity aa\n" +
            "\tinner join  Accom_Type rt \n" +
            "\ton aa.Accom_Type_ID = rt.Accom_Type_ID and aa.Property_ID = rt.Property_ID\n" +
            "\tinner join accom_class rc \n" +
            "\ton rc.Accom_Class_ID = rt.Accom_Class_ID and rc.Property_ID = rt.Property_ID\n" +
            "\tinner join Decision_Ovrbk_Accom doa \n" +
            "\ton doa.Accom_Type_ID = rt.Accom_Type_ID and doa.Occupancy_DT = aa.Occupancy_DT and aa.Property_ID = doa.Property_ID\n" +
            "\twhere rt.Status_ID = 1 and rc.Status_ID = 1 and rt.Property_ID = :Property_ID and aa.Occupancy_DT between :startDate and :endDate \n" +
            ") \n" +
            "order by Accom_Class_ID,Occupancy_DT , Accom_Type_ID ";

    private final String straightLineBaseDataCRQuery = "select rc.Accom_Class_ID,  aa.Occupancy_DT, rt.Accom_Type_ID,\n" +
            "\taa.Remaining_Capacity + doa.Overbooking_Decision as rooms_Remaining\n" +
            "\t ,rc.accom_class_code from CR_Accom_Activity aa\n" +
            "\tinner join  Accom_Type rt \n" +
            "\ton aa.Accom_Type_ID = rt.Accom_Type_ID and aa.Property_ID = rt.Property_ID\n" +
            "\tinner join accom_class rc \n" +
            "\ton rc.Accom_Class_ID = rt.Accom_Class_ID and rc.Property_ID = rt.Property_ID\n" +
            "\tinner join Decision_Ovrbk_Accom doa \n" +
            "\ton doa.Accom_Type_ID = rt.Accom_Type_ID and doa.Occupancy_DT = aa.Occupancy_DT and aa.Property_ID = doa.Property_ID\n" +
            "\twhere rt.Status_ID = 1 and rc.Status_ID = 1 and rt.Property_ID = :Property_ID and aa.Occupancy_DT between :startDate and :endDate";

    private Map<String, Object> getParameterMap(String startDate, String endDate, Integer propertyId) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("Property_ID", propertyId);
        parameters.put("startDate", startDate);
        parameters.put("endDate", endDate);
        return parameters;
    }

    private List<StraightLineAvailabilityBaseDataDTO> getBaseData(String startDate, String endDate, Integer propertyId, String query) {
        Map<String, Object> parameters = getParameterMap(startDate, endDate, propertyId);
        return crudService.findByNativeQuery(query, parameters,
                row -> new StraightLineAvailabilityBaseDataDTO((Integer) row[0],
                        DateUtil.convertJavaUtilDateToLocalDate((Date) row[1], true), (Integer) row[2], (BigDecimal) row[3], row[4].toString()));

    }

    public List<StraightLineAvailabilityBaseDataDTO> getSlaIssueDates(String startDate, String endDate, Integer numberOfRooms,
                                                                      String rollingStartDate, String rollingEndDate, boolean rollingDate) {
        startDate = rollingDate ? getSpecificDateFromRollingDate(rollingStartDate) : startDate;
        endDate = rollingDate ? getSpecificDateFromRollingDate(rollingEndDate) : endDate;
        List<StraightLineAvailabilityBaseDataDTO> baseData = getData(startDate, endDate);
        return null != baseData ? new StraightLineAvailabilityService(MAXLOS, MINLOS, numberOfRooms).getSLABlockers(baseData, true) : new ArrayList<>();
    }

    public List<StraightLineAvailabilityBaseDataDTO> getData(String startDate, String endDate) {
        final Integer propertyId = PacmanWorkContextHelper.getPropertyId();

        final List<StraightLineAvailabilityBaseDataDTO> baseData = getBaseData(startDate, endDate, propertyId, straightLineBaseDataQuery);
        final List<StraightLineAvailabilityBaseDataDTO> baseDataCR = getCRData(startDate, endDate, propertyId);
        StraightLineAvailabilityUtility.overrideCRDataIntoBaseData(baseData, baseDataCR);

        return baseData;
    }

    private List<StraightLineAvailabilityBaseDataDTO> getCRData(String startDate, String endDate, Integer propertyId) {
        return isComponentRoomEnabled(propertyId) ? getBaseData(startDate, endDate, propertyId, straightLineBaseDataCRQuery) : null;
    }

    private boolean isComponentRoomEnabled(Integer propertyId) {
        Property property = globalCrudService.find(Property.class, propertyId);
        final String propertyCode = property.getCode();
        final String clientCode = property.getClient().getCode();
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value(), clientCode, propertyCode);
    }

    private String getSpecificDateFromRollingDate(String rollingDate) {
        LocalDate caughtUpDate = dateService.getCaughtUpJavaLocalDate();
        java.sql.Date date = crudService.findByNativeQuerySingleResult("select absolute_date from ufn_get_absolute_dates_from_rolling_dates (:rolling_date ,:caughtup_date)",
                QueryParameter.with("rolling_date", rollingDate).and("caughtup_date", caughtUpDate).parameters());
        return DateUtil.convertJavaUtilDateToLocalDate(date, true).toString();
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    public void setConfigParamsService(PacmanConfigParamsService configParamsService) {
        this.configParamsService = configParamsService;
    }
}
