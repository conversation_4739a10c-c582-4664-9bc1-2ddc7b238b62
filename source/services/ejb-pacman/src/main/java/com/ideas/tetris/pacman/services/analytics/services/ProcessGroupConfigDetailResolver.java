package com.ideas.tetris.pacman.services.analytics.services;

import com.ideas.tetris.pacman.services.roa.processgroup.entity.DowGroupDetail;
import com.ideas.tetris.pacman.services.roa.processgroup.entity.SeasonGroupDetail;
import com.ideas.tetris.pacman.services.roa.referenceprice.entity.HorizonGroupDetail;
import com.ideas.tetris.pacman.util.CollectionUtils;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class ProcessGroupConfigDetailResolver {
     private Function<LocalDate, Integer> getSeasonNumber;
     private Function<LocalDate, Integer> getDowNumber;
     private Function<Double, Integer> getHorizonNumber;

    public ProcessGroupConfigDetailResolver(List<SeasonGroupDetail> seasonGroupDetail,
                                            List<DowGroupDetail> dowGroupDetail,
                                            List<HorizonGroupDetail> horizonGroupDetail) {
        NavigableMap<LocalDate, Integer> seasonGrpNumByStartDate = seasonGroupDetail.stream()
                .collect(CollectionUtils.toNavigableMap(s -> LocalDate.parse(s.getStart_Date()), SeasonGroupDetail::getSeasonGroupNumber));
        Map<Integer, Integer> dowGrpNumByDow = dowGroupDetail.stream().collect(Collectors.toMap(DowGroupDetail::getDow, DowGroupDetail::getDowGroupNumber));
        NavigableMap<Double, Integer> horizonGrpNumByDta = horizonGroupDetail.stream()
                .collect(CollectionUtils.toNavigableMap(HorizonGroupDetail::getFrom_dta, HorizonGroupDetail::getHorizonGroupNumber));
        this.getSeasonNumber = d -> seasonGrpNumByStartDate.floorEntry(d).getValue();
        this.getDowNumber = d -> dowGrpNumByDow.get(d.getDayOfWeek().getValue());
        this.getHorizonNumber = dta -> horizonGrpNumByDta.floorEntry(dta).getValue();
    }
}
