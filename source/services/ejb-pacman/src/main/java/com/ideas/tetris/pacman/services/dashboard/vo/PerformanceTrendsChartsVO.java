/**
 *
 */
package com.ideas.tetris.pacman.services.dashboard.vo;

import java.io.Serializable;
import java.util.Map;

public class PerformanceTrendsChartsVO implements Serializable {

    private String startDateStr;
    private String endDateStr;
    private String lastUpdatedDateStr;
    private String closeOfBusinessDateStr;

    private Map<String, Integer> onTheBooksMap;
    private Map<String, Integer> demandToComeMap;

    private Map<String, Integer> capacityMap;
    private Map<String, Integer> roomSoldLYMap;

    private Map<String, Double> adrOnBooksMap;
    private Map<String, Double> lastYearADRMap;

    private Map<String, Double> revPARMap;
    private Map<String, Double> lastYearRevPARMap;

    private Map<String, Integer> outOfOrderMap;

    private Map<String, Integer> availableCapacityDataMap;

    private int yAxisCapacity = 0;

    public String getStartDateStr() {
        return startDateStr;
    }

    public void setStartDateStr(String startDateStr) {
        this.startDateStr = startDateStr;
    }

    public String getEndDateStr() {
        return endDateStr;
    }

    public void setEndDateStr(String endDateStr) {
        this.endDateStr = endDateStr;
    }

    public String getLastUpdatedDateStr() {
        return lastUpdatedDateStr;
    }

    public void setLastUpdatedDateStr(String lastUpdatedDateStr) {
        this.lastUpdatedDateStr = lastUpdatedDateStr;
    }

    public String getCloseOfBusinessDateStr() {
        return closeOfBusinessDateStr;
    }

    public void setCloseOfBusinessDateStr(String closeOfBusinessDateStr) {
        this.closeOfBusinessDateStr = closeOfBusinessDateStr;
    }

    public Map<String, Integer> getOnTheBooksMap() {
        return onTheBooksMap;
    }

    public void setOnTheBooksMap(Map<String, Integer> onTheBooksMap) {
        this.onTheBooksMap = onTheBooksMap;
    }

    public Map<String, Integer> getCapacityMap() {
        return capacityMap;
    }

    public void setCapacityMap(Map<String, Integer> capacityMap) {
        this.capacityMap = capacityMap;
    }

    public Map<String, Integer> getOutOfOrderMap() {
        return outOfOrderMap;
    }

    public void setOutOfOrderMap(Map<String, Integer> outOfOrderMap) {
        this.outOfOrderMap = outOfOrderMap;
    }

    public Map<String, Integer> getRoomSoldLYMap() {
        return roomSoldLYMap;
    }

    public void setRoomSoldLYMap(Map<String, Integer> roomSoldLYMap) {
        this.roomSoldLYMap = roomSoldLYMap;
    }

    public Map<String, Integer> getDemandToComeMap() {
        return demandToComeMap;
    }

    public void setDemandToComeMap(Map<String, Integer> demandToComeMap) {
        this.demandToComeMap = demandToComeMap;
    }

    public Map<String, Double> getRevPARMap() {
        return revPARMap;
    }

    public void setRevPARMap(Map<String, Double> revPARMap) {
        this.revPARMap = revPARMap;
    }

    public Map<String, Double> getLastYearADRMap() {
        return lastYearADRMap;
    }

    public void setLastYearADRMap(Map<String, Double> lastYearADRMap) {
        this.lastYearADRMap = lastYearADRMap;
    }

    public Map<String, Double> getLastYearRevPARMap() {
        return lastYearRevPARMap;
    }

    public void setLastYearRevPARMap(Map<String, Double> lastYearRevPARMap) {
        this.lastYearRevPARMap = lastYearRevPARMap;
    }

    public Map<String, Double> getAdrOnBooksMap() {
        return adrOnBooksMap;
    }

    public void setAdrOnBooksMap(Map<String, Double> adrOnBooksMap) {
        this.adrOnBooksMap = adrOnBooksMap;
    }

    public int getyAxisCapacity() {
        return yAxisCapacity;
    }

    public void setyAxisCapacity(int yAxisCapacity) {
        this.yAxisCapacity = yAxisCapacity;
    }

    public Map<String, Integer> getAvailableCapacityDataMap() {
        return availableCapacityDataMap;
    }

    public void setAvailableCapacityDataMap(
            Map<String, Integer> availableCapacityDataMap) {
        this.availableCapacityDataMap = availableCapacityDataMap;
    }

}
