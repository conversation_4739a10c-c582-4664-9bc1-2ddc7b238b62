package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.datafeed.dto.rateshopping.CompetitiveChannel;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateDefaultChannel;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateOverrideChannel;
import com.ideas.tetris.pacman.services.webrate.service.DefaultReferenceChannelService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.DEFAULT;
import static com.ideas.tetris.pacman.common.constants.Constants.OVERRIDE;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class CompetitiveChannelService {

    @Autowired
    DefaultReferenceChannelService defaultReferenceChannelService;

    @Autowired
    AgileRatesConfigurationService agileRatesConfigurationService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    public List getAllCompetitiveChannelDetails(Integer propertyId, Date date) {
        boolean includeProductName = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.HIERARCHY_INDEPENDENT_PRODUCT_COLUMN_ENABLED);
        Map<Integer, String> productsMap = Collections.emptyMap();
        List<Product> productsList = agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts();
        productsMap = productsList.stream().collect(Collectors.toMap(Product::getId, Product::getName));

        List<CompetitiveChannel> competitiveChannels = new ArrayList<>();
        List<WebrateDefaultChannel> webrateDefaultChannels = new ArrayList<>();
        if (includeProductName) {
            webrateDefaultChannels = defaultReferenceChannelService.getAllDefaultChannelsByPropertyId(propertyId);
        } else {
            WebrateDefaultChannel webrateDefaultChannel = defaultReferenceChannelService.getDefaultChannelByPropertyId(propertyId);
            if (null != webrateDefaultChannel) {
                webrateDefaultChannels.add(webrateDefaultChannel);
            }
        }
        populateDefaultChannelDetails(competitiveChannels, webrateDefaultChannels, productsMap);

        List<WebrateOverrideChannel> webrateOverrideChannels;
        if (includeProductName) {
            webrateOverrideChannels = defaultReferenceChannelService.getAllOverrideChannelsByPropertyId(propertyId);
        } else {
            webrateOverrideChannels = defaultReferenceChannelService.getBarOverrideChannelByPropertyId(propertyId);
        }
        webrateOverrideChannels = webrateOverrideChannels
                .stream()
                .filter(e -> e.getChannelOverrideEndDT().after(date) || e.getChannelOverrideEndDT().equals(date))
                .collect(Collectors.toList());
        populateOverrideChannelDetails(competitiveChannels, webrateOverrideChannels, productsMap);

        return competitiveChannels;
    }

    private void populateOverrideChannelDetails(List<CompetitiveChannel> competitiveChannels, List<WebrateOverrideChannel> webrateOverrideChannels, Map<Integer, String> productsMap) {
        for (WebrateOverrideChannel webrateOverrideChannel : webrateOverrideChannels) {
            CompetitiveChannel competitiveChannel = new CompetitiveChannel();
            competitiveChannel.setCategory(OVERRIDE);
            competitiveChannel.setRateChannelSun(webrateOverrideChannel.getWebrateChannelSun().getWebrateChannelName());
            competitiveChannel.setRateChannelMon(webrateOverrideChannel.getWebrateChannelMon().getWebrateChannelName());
            competitiveChannel.setRateChannelTues(webrateOverrideChannel.getWebrateChannelTues().getWebrateChannelName());
            competitiveChannel.setRateChannelWed(webrateOverrideChannel.getWebrateChannelWed().getWebrateChannelName());
            competitiveChannel.setRateChannelThurs(webrateOverrideChannel.getWebrateChannelThurs().getWebrateChannelName());
            competitiveChannel.setRateChannelFri(webrateOverrideChannel.getWebrateChannelFri().getWebrateChannelName());
            competitiveChannel.setRateChannelSat(webrateOverrideChannel.getWebrateChannelSat().getWebrateChannelName());
            competitiveChannel.setOverrideChannelName(webrateOverrideChannel.getWebrateOverrideName());
            competitiveChannel.setOverrideStartDate(webrateOverrideChannel.getChannelOverrideStartDT());
            competitiveChannel.setOverrideEndDate(webrateOverrideChannel.getChannelOverrideEndDT());
            competitiveChannel.setProductName(productsMap.get(webrateOverrideChannel.getProductID()));
            competitiveChannels.add(competitiveChannel);
        }
    }

    private void populateDefaultChannelDetails(List<CompetitiveChannel> competitiveChannels, List<WebrateDefaultChannel> webrateDefaultChannels, Map<Integer, String> productsMap) {
        for (WebrateDefaultChannel webrateDefaultChannel : webrateDefaultChannels) {
            CompetitiveChannel competitiveChannel = new CompetitiveChannel();
            competitiveChannel.setCategory(DEFAULT);
            competitiveChannel.setRateChannelSun(webrateDefaultChannel.getWebrateChannelSun().getWebrateChannelName());
            competitiveChannel.setRateChannelMon(webrateDefaultChannel.getWebrateChannelMon().getWebrateChannelName());
            competitiveChannel.setRateChannelTues(webrateDefaultChannel.getWebrateChannelTues().getWebrateChannelName());
            competitiveChannel.setRateChannelWed(webrateDefaultChannel.getWebrateChannelWed().getWebrateChannelName());
            competitiveChannel.setRateChannelThurs(webrateDefaultChannel.getWebrateChannelThurs().getWebrateChannelName());
            competitiveChannel.setRateChannelFri(webrateDefaultChannel.getWebrateChannelFri().getWebrateChannelName());
            competitiveChannel.setRateChannelSat(webrateDefaultChannel.getWebrateChannelSat().getWebrateChannelName());
            competitiveChannel.setProductName(productsMap.get(webrateDefaultChannel.getProductID()));
            competitiveChannels.add(competitiveChannel);
        }
    }
}
