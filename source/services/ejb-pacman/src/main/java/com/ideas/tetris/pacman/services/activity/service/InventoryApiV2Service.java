package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.api.client.inventory.InventoryV2Api;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.platform.common.rest.mapper.PlatformNGIRestClient;

import javax.inject.Inject;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class InventoryApiV2Service {

    @Autowired
    PlatformNGIRestClient platformNgiRestClient;

    public void deleteNucleusInventories() {
        var clientCode = PacmanWorkContextHelper.getClientCode();
        var propertyCode = PacmanWorkContextHelper.getPropertyCode();
        var apiV2Instance = getApiV2Instance(clientCode, propertyCode);
        apiV2Instance.deleteInventoriesForAProperty(clientCode, propertyCode);
    }

    public InventoryV2Api getApiV2Instance(String clientCode, String propertyCode) {
        return new InventoryV2Api(platformNgiRestClient.getClientV2API(platformNgiRestClient.getPMSInboundNGIBaseUrl(clientCode, propertyCode)));
    }
}
