package com.ideas.tetris.pacman.services.reports.dataextraction;

import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionReportDto;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionRoomClass;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionType;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.ReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.DataExtractionReportCriteria;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil.getText;


public class DataExtractionRoomClassConverter {
    public static ReportSheet getRoomClassReportSheet(Map<DataExtractionType, List<DataExtractionReportDto>> records, ScheduledReport<DataExtractionReportCriteria> scheduledReport) {

        Language language = scheduledReport.getLanguage();
        DecimalFormat decimalFormat = ScheduledReportUtils.getLocaleDecimalFormat(language.getLocale());
        ReportSheet roomClassSheet = new ReportSheet(getText("room.class", language));
        roomClassSheet.setReportTitle(getText("dataExtractionReport.title.at.room.class.level", scheduledReport.getLanguage()));
        List<DataExtractionReportDto> dataExtractionReportDtoList = records.get(DataExtractionType.ROOM_CLASS);
        Object[] headerArray = getRoomClassHeaderList(scheduledReport.getLanguage(), scheduledReport.getReportCriteria()).toArray();
        DataExtractionReportCriteria reportCriteria = scheduledReport.getReportCriteria();

        for (int i = 0; i < headerArray.length; i++) {
            roomClassSheet.addColumn(String.class);
        }
        roomClassSheet.addHeaderRow(headerArray);

        dataExtractionReportDtoList.forEach(dto -> {
            List<Object> dataList = new ArrayList<Object>();
            DataExtractionRoomClass data = (DataExtractionRoomClass) dto;

            if (reportCriteria.isShowLastYearData()) {
                dataList.add(DataExtractionReportUtil.getStringValue(data.getPropertyName())); //  Property Name
                if (data.getDayOfWeek() != null && !data.getDayOfWeek().isEmpty()) {
                    dataList.add(getText(DataExtractionReportUtil.getStringValue(data.getDayOfWeek()).toLowerCase(), language)); // Day of Week
                } else {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getDayOfWeek())); // Day of Week
                }
                dataList.add(ScheduledReportUtils.getDateString(data.getOccupancyDate())); //  Occupancy Date
                dataList.add(ScheduledReportUtils.getDateString(data.getComparisonDateLastYear())); //  Comparison Date Last Year
                dataList.add(DataExtractionReportUtil.getStringValue(data.getAccomClassName())); //  Room Class

                if (reportCriteria.isRoomClassCapacity()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCapacityThisYear())); //  Capacity This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCapacityLastYear())); //  Capacity Last Year Actual
                }

                if (reportCriteria.isRoomClassRoomsSold() && reportCriteria.isRoomClassRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); //  Occupancy On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }

                if (reportCriteria.isRoomClassRoomsSold() && !reportCriteria.isRoomClassRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); //  Occupancy On Books Last Year Actual
                }

                if (!reportCriteria.isRoomClassRoomsSold() && reportCriteria.isRoomClassRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); //  Occupancy On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }

                if (reportCriteria.isRoomClassArrivals()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsThisYear())); //  Arrivals This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsLastYear())); //  Arrivals Last Year Actual
                }

                if (reportCriteria.isRoomClassDepartures()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesThisYear())); //  Departures This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesLastYear())); //  Departures Last Year Actual
                }

                if (reportCriteria.isRoomClassOutOfOrder()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsNotAvailableMaintenanceThisYear())); //  Rooms N/A - Out of Order This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsNotAvailableMaintenanceLastYear())); //  Rooms N/A - Out of Order Last Year Actual
                }
                if (reportCriteria.isRoomClassRoomsNotAvailableOutOfOrder()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsNotAvailableOtherThisYear())); //  Rooms N/A - Other This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsNotAvailableOtherLastYear())); //  Rooms N/A - Other Last Year Actual
                }

                if (reportCriteria.isRoomClassCancellations()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledThisYear())); //  Cancelled This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledLastYear())); //  Cancelled Last Year Actual
                }

                if (reportCriteria.isRoomClassNoShow()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowThisYear())); //  No Show This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowLastYear())); //  No Show Last Year Actual
                }

                if (reportCriteria.isRoomClassRevenue() && reportCriteria.isRoomClassRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); //  Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueLastYear(), decimalFormat)); //  Forecasted Room Revenue Last Year Actual
                }

                if (reportCriteria.isRoomClassRevenue() && !reportCriteria.isRoomClassRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); //  Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueLastYear(), decimalFormat)); //  Forecasted Room Revenue Last Year Actual

                }

                if (!reportCriteria.isRoomClassRevenue() && reportCriteria.isRoomClassRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); //  Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                }

                if (reportCriteria.isRoomClassOccupancyForecast()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOccupancyNumberThisYear(), decimalFormat)); //  Occupancy Forecast This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOccupancyNumberLastYear(), decimalFormat)); //  Occupancy Forecast Last Year Actual
                }

                if (reportCriteria.isRoomClassSystemUnconstrainedDemand()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getDemandRoomSoldThisYear(), decimalFormat)); //  System Unconstrained Demand This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getDemandRoomSoldLastYear(), decimalFormat)); //  System Unconstrained Demand Last Year Actual
                }

                if (reportCriteria.isRoomClassUserUnconstrainedDemand()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserDemandRoomSoldThisYear(), decimalFormat)); //  User Demand This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserDemandRoomSoldLastYear(), decimalFormat)); //  User Demand Last Year Actual
                }

                if (reportCriteria.isRoomClassRevPAR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevPARThisYear(), decimalFormat)); //  RevPAR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevPARLastYear(), decimalFormat)); //  RevPAR On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRevparThisYear(), decimalFormat)); //  RevPAR Forecast This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRevparLastYear(), decimalFormat)); //  RevPAR Forecast Last Year Actual
                }

                if (reportCriteria.isRoomClassADR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRThisYear(), decimalFormat)); //  ADR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRLastYear(), decimalFormat)); //  ADR On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrThisYear(), decimalFormat)); //  ADR Forecast This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrLastYear(), decimalFormat)); //  ADR Forecast Last Year Actual
                }

                if (reportCriteria.isRoomClassLRV()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getLrvThisYear(), decimalFormat)); //  Last Room Value This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getLrvLastYear(), decimalFormat)); //  Last Room Value Last Year Actual
                }

                if (reportCriteria.isRoomClassBAR()) {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getLos()) + "\n(" + ScheduledReportUtils.getI18NFormatedValue(data.getPrice(), decimalFormat) + ")"); //  Bar by Day
                }

            } else {
                dataList.add(DataExtractionReportUtil.getStringValue(data.getPropertyName())); //  Property Name
                if (data.getDayOfWeek() != null && !data.getDayOfWeek().isEmpty()) {
                    dataList.add(getText(DataExtractionReportUtil.getStringValue(data.getDayOfWeek()).toLowerCase(), language)); // Day of Week
                } else {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getDayOfWeek())); // Day of Week
                }
                dataList.add(ScheduledReportUtils.getDateString(data.getOccupancyDate())); //  Occupancy Date
                dataList.add(DataExtractionReportUtil.getStringValue(data.getAccomClassName())); //  Room Class

                if (reportCriteria.isRoomClassCapacity()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCapacityThisYear())); //  Capacity This Year
                }

                if (reportCriteria.isRoomClassRoomsSold() && reportCriteria.isRoomClassRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }

                if (reportCriteria.isRoomClassRoomsSold() && !reportCriteria.isRoomClassRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                }

                if (!reportCriteria.isRoomClassRoomsSold() && reportCriteria.isRoomClassRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }

                if (reportCriteria.isRoomClassArrivals()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsThisYear())); //  Arrivals This Year
                }

                if (reportCriteria.isRoomClassDepartures()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesThisYear())); //  Departures This Year
                }

                if (reportCriteria.isRoomClassOutOfOrder()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsNotAvailableMaintenanceThisYear())); //  Rooms N/A - Out of Order This Year
                }
                if (reportCriteria.isRoomClassRoomsNotAvailableOutOfOrder()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsNotAvailableOtherThisYear())); //  Rooms N/A - Other This Year
                }

                if (reportCriteria.isRoomClassCancellations()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledThisYear())); //  Cancelled This Year
                }

                if (reportCriteria.isRoomClassNoShow()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowThisYear())); //  No Show This Year
                }

                if (reportCriteria.isRoomClassRevenue() && reportCriteria.isRoomClassRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                }

                if (reportCriteria.isRoomClassRevenue() && !reportCriteria.isRoomClassRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                }

                if (!reportCriteria.isRoomClassRevenue() && reportCriteria.isRoomClassRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                }

                if (reportCriteria.isRoomClassOccupancyForecast()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOccupancyNumberThisYear(), decimalFormat)); //  Occupancy Forecast This Year
                }

                if (reportCriteria.isRoomClassSystemUnconstrainedDemand()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getDemandRoomSoldThisYear(), decimalFormat)); //  System Unconstrained Demand This Year
                }

                if (reportCriteria.isRoomClassUserUnconstrainedDemand()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserDemandRoomSoldThisYear(), decimalFormat)); //  User Demand This Year
                }

                if (reportCriteria.isRoomClassRevPAR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevPARThisYear(), decimalFormat)); //  RevPAR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRevparThisYear(), decimalFormat)); //  RevPAR Forecast This Year
                }

                if (reportCriteria.isRoomClassADR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRThisYear(), decimalFormat)); //  ADR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrThisYear(), decimalFormat)); //  ADR Forecast This Year
                }

                if (reportCriteria.isRoomClassLRV()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getLrvThisYear(), decimalFormat)); //  Last Room Value This Year
                }

                if (reportCriteria.isRoomClassBAR()) {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getLos()) + "\n(" + ScheduledReportUtils.getI18NFormatedValue(data.getPrice(), decimalFormat) + ")"); //  Bar by Day
                }

            }

            roomClassSheet.addRow(dataList.toArray());
        });
        return roomClassSheet;

    }

    private static List<Object> getRoomClassHeaderList(Language language, DataExtractionReportCriteria reportCriteria) {
        List<Object> headers = new ArrayList<>();
        if (reportCriteria.isShowLastYearData()) {
            headers.add(getText("common.propertyName", language));
            headers.add(getText("report.dow", language));
            headers.add(getText("occupancyDate", language));
            headers.add(getText("common.comparisonDateLastYear", language));
            headers.add(getText("room.class", language));

            if (reportCriteria.isRoomClassCapacity()) {
                headers.add(getText("common.capacity", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.capacity", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomClassRoomsSold() && reportCriteria.isRoomClassRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isRoomClassRoomsSold() && !reportCriteria.isRoomClassRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
            }

            if (!reportCriteria.isRoomClassRoomsSold() && reportCriteria.isRoomClassRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isRoomClassArrivals()) {
                headers.add(getText("arrivals", language) + " " + getText("common.thisYear", language));
                headers.add(getText("arrivals", language) + " " + getText("common.lastYearActual", language));
            }
            if (reportCriteria.isRoomClassDepartures()) {
                headers.add(getText("common.departures", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.departures", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomClassOutOfOrder()) {
                headers.add(getText("report.column.roomsNAOutOfOrder", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsNAOutOfOrder", language) + " " + getText("common.lastYearActual", language));
            }
            if (reportCriteria.isRoomClassRoomsNotAvailableOutOfOrder()) {
                headers.add(getText("report.column.roomsNAOther", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsNAOther", language) + " " + getText("common.lastYearActual", language));
            }
            if (reportCriteria.isRoomClassCancellations()) {
                headers.add(getText("cancelled", language) + " " + getText("common.thisYear", language));
                headers.add(getText("cancelled", language) + " " + getText("common.lastYearActual", language));
            }
            if (reportCriteria.isRoomClassNoShow()) {
                headers.add(getText("noshow", language) + " " + getText("common.thisYear", language));
                headers.add(getText("noshow", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomClassRevenue() && reportCriteria.isRoomClassRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomClassRevenue() && !reportCriteria.isRoomClassRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.lastYearActual", language));

            }

            if (!reportCriteria.isRoomClassRevenue() && reportCriteria.isRoomClassRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isRoomClassOccupancyForecast()) {
                headers.add(getText("common.occupancyForecast", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.occupancyForecast", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomClassSystemUnconstrainedDemand()) {
                headers.add(getText("systemUnconstrainedDemand", language) + " " + getText("common.thisYear", language));
                headers.add(getText("systemUnconstrainedDemand", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomClassUserUnconstrainedDemand()) {
                headers.add(getText("report.column.userDemand", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.userDemand", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomClassRevPAR()) {
                headers.add(getText("revpar.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("revpar.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.forecastedRevPar", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRevPar", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomClassADR()) {
                headers.add(getText("report.column.bookedAdr", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedAdr", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.forecastedAdr", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedAdr", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomClassLRV()) {
                headers.add(getText("last.room.value", language) + " " + getText("common.thisYear", language));
                headers.add(getText("last.room.value", language) + " " + getText("common.lastYearActual", language));
            }
            if (reportCriteria.isRoomClassBAR()) {
                headers.add(getText("bar.by.day", language));
            }

        } else {
            headers.add(getText("common.propertyName", language));
            headers.add(getText("report.dow", language));
            headers.add(getText("occupancyDate", language));
            headers.add(getText("room.class", language));

            if (reportCriteria.isRoomClassCapacity()) {
                headers.add(getText("common.capacity", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomClassRoomsSold() && reportCriteria.isRoomClassRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isRoomClassRoomsSold() && !reportCriteria.isRoomClassRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
            }

            if (!reportCriteria.isRoomClassRoomsSold() && reportCriteria.isRoomClassRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isRoomClassArrivals()) {
                headers.add(getText("arrivals", language) + " " + getText("common.thisYear", language));
            }
            if (reportCriteria.isRoomClassDepartures()) {
                headers.add(getText("common.departures", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomClassOutOfOrder()) {
                headers.add(getText("report.column.roomsNAOutOfOrder", language) + " " + getText("common.thisYear", language));
            }
            if (reportCriteria.isRoomClassRoomsNotAvailableOutOfOrder()) {
                headers.add(getText("report.column.roomsNAOther", language) + " " + getText("common.thisYear", language));
            }
            if (reportCriteria.isRoomClassCancellations()) {
                headers.add(getText("cancelled", language) + " " + getText("common.thisYear", language));
            }
            if (reportCriteria.isRoomClassNoShow()) {
                headers.add(getText("noshow", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomClassRevenue() && reportCriteria.isRoomClassRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomClassRevenue() && !reportCriteria.isRoomClassRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
            }

            if (!reportCriteria.isRoomClassRevenue() && reportCriteria.isRoomClassRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isRoomClassOccupancyForecast()) {
                headers.add(getText("common.occupancyForecast", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomClassSystemUnconstrainedDemand()) {
                headers.add(getText("systemUnconstrainedDemand", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomClassUserUnconstrainedDemand()) {
                headers.add(getText("report.column.userDemand", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomClassRevPAR()) {
                headers.add(getText("revpar.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRevPar", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomClassADR()) {
                headers.add(getText("report.column.bookedAdr", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedAdr", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomClassLRV()) {
                headers.add(getText("last.room.value", language) + " " + getText("common.thisYear", language));
            }
            if (reportCriteria.isRoomClassBAR()) {
                headers.add(getText("bar.by.day", language));
            }

        }

        return headers;
    }
}
