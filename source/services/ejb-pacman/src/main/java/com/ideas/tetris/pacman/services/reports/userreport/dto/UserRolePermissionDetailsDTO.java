package com.ideas.tetris.pacman.services.reports.userreport.dto;

public class UserRolePermissionDetailsDTO {

    private Integer id;
    private String lastName;
    private String firstName;
    private String status;
    private String email;
    private String createdDate;
    private String lastLogin;
    private String authGroupName;
    private String authGroupRole;
    private String property;
    private String propertyRole;
    private boolean isInternal;
    private String clientCode;
    private String excelCreatedDate;
    private String uniqueUserID;
    private String propertyCode;

    public String getExcelLastLogin() {
        return excelLastLogin;
    }

    public void setExcelLastLogin(String excelLastLogin) {
        this.excelLastLogin = excelLastLogin;
    }

    public String getExcelCreatedDate() {
        return excelCreatedDate;
    }

    public void setExcelCreatedDate(String excelCreatedDate) {
        this.excelCreatedDate = excelCreatedDate;
    }

    private String excelLastLogin;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }

    public String getLastLogin() {
        return lastLogin;
    }

    public void setLastLogin(String lastLogin) {
        this.lastLogin = lastLogin;
    }

    public String getAuthGroupName() {
        return authGroupName;
    }

    public void setAuthGroupName(String authGroupName) {
        this.authGroupName = authGroupName;
    }

    public String getAuthGroupRole() {
        return authGroupRole;
    }

    public void setAuthGroupRole(String authGroupRole) {
        this.authGroupRole = authGroupRole;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public String getPropertyRole() {
        return propertyRole;
    }

    public void setPropertyRole(String propertyRole) {
        this.propertyRole = propertyRole;
    }

    public boolean isInternal() {
        return isInternal;
    }

    public void setInternal(boolean isInternal) {
        this.isInternal = isInternal;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getUniqueUserID() {
        return uniqueUserID;
    }

    public void setUniqueUserID(String uniqueUserID) {
        this.uniqueUserID = uniqueUserID;
    }

    public String getPropertyCode() {
        return propertyCode;
    }

    public void setPropertyCode(String propertyCode) {
        this.propertyCode = propertyCode;
    }
}
