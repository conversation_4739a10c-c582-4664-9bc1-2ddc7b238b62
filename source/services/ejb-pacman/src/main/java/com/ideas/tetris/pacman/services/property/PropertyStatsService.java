package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.property.dto.PropertyStats;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.commons.collections.CollectionUtils;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;
import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Transactional(propagation = Propagation.NOT_SUPPORTED)
@Component
public class PropertyStatsService {

    private static final String PROPERTY_DETAILS_QUERY = "select \n" +
            "  cpv.property_id,\n" +
            "  cpv.client_code,\n" +
            "  cpv.property_code,\n" +
            "  db.server_name,\n" +
            "  sas.sas_server_name,\n" +
            "  cpv.stage,\n" +
            "  cpv.property_time_zone,\n" +
            "  cpv.external_system\n" +
            "from vw_consolidated_property_view cpv\n" +
            "  join property p on cpv.property_id = p.property_id\n" +
            "  join dbloc db on p.dbloc_id = db.dbloc_id\n" +
            "  join sas_file_loc sas on p.property_id = sas.property_id\n";

    private static final String PROPERTY_STATS_QUERY = "select p.property_id, accom.accom_types, accom.total_accom_capacity, mkt.market_segments, fcg.forecast_groups\n" +
            "from property p\n" +
            "       left join (select property_id, count(*) accom_types, sum(accom_type_capacity) total_accom_capacity from accom_type where property_id > 1 group by property_id) accom\n" +
            "         on p.property_id = accom.property_id\n" +
            "       left join (select property_id, count(*) market_segments from mkt_seg where property_id > 1 group by property_id) mkt \n" +
            "         on p.property_id = mkt.property_id\n" +
            "       left join (select property_id, count(*) forecast_groups from forecast_group where property_id > 1 group by property_id) fcg\n" +
            "      on p.property_id = fcg.property_id\n" +
            "where p.property_id > 1\n";

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;
    @Autowired
	private AbstractMultiPropertyCrudService multiPropertyCrudService;

    @ForTesting
	public
    void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    @ForTesting
	public
    void setMultiPropertyCrudService(AbstractMultiPropertyCrudService multiPropertyCrudService) {
        this.multiPropertyCrudService = multiPropertyCrudService;
    }


    public List<PropertyStats> getPropertyStats() {
        return getPropertyStats(Collections.emptyList());
    }

    public List<PropertyStats> getPropertyStats(List<Integer> propertyIds) {
        String propertyDetailsQuery = PROPERTY_DETAILS_QUERY;
        Map<String, Object> parameters = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(propertyIds)) {
            propertyDetailsQuery = PROPERTY_DETAILS_QUERY + "where cpv.property_id in (:propertyIds)";
            parameters = QueryParameter.with("propertyIds", propertyIds).parameters();
        }

        Map<Integer, PropertyStats.PropertyDetails> propertyDetailsMap = globalCrudService.findByNativeQuery(propertyDetailsQuery, parameters,
                        PropertyStats.PropertyDetails::new)
                .stream()
                .collect(Collectors.toMap(PropertyStats.PropertyDetails::getPropertyId, Function.identity()));

        // this shouldn't happen, but no need to enter the multi-property stuff if there is no work to do
        if (propertyDetailsMap.isEmpty()) {
            return Collections.emptyList();
        }

        //TODO: add database details
        //Map<Integer, PropertyDbDetails>

        List<Integer> propertyIdsToQuery = new ArrayList<>(propertyDetailsMap.keySet());
        return multiPropertyCrudService.findByNativeQuerySingleResult(propertyIdsToQuery, PROPERTY_STATS_QUERY, Collections.emptyMap(), row -> {
            Integer propertyId = (Integer) row[0];
            return new PropertyStats(propertyDetailsMap.get(propertyId), row);
        });
    }

}
