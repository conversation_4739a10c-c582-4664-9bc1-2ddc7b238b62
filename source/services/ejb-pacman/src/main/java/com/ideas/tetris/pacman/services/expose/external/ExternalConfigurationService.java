package com.ideas.tetris.pacman.services.expose.external;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterValue;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang.StringUtils;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class ExternalConfigurationService {

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
	private PropertyService propertyService;


    public Integer getPropertyIdByWebRateAlias(String webRateAlias) {
        List<ConfigParameterValue> configParamValues = configParamsService.getParameterValueByParameterNameAndValue(
                IPConfigParamName.BAR_WEB_RATE_ALIAS.value(), webRateAlias);
        if (configParamValues.isEmpty()) {
            return Integer.valueOf(-1);
        }
        validateConstantContextIsFullyQualified(configParamValues, IPConfigParamName.BAR_WEB_RATE_ALIAS.value(), webRateAlias);
        ConfigParameterValue workingConfigValue = configParamValues.get(0);
        WorkContextType workContextType = PacmanWorkContextHelper.deriveWorkContextFromRegulatorOrConfigParamContext(workingConfigValue
                .getContext());
        Property targetProperty = propertyService.getPropertyByCode(workContextType.getClientCode(), workContextType.getPropertyCode());
        return targetProperty.getId();
    }


    public List<Property> getProperties(String integrationTypeStr,
                                        String sendingSystemPropertyId) {
        // Determine the IntegrationType to know which ConfigParameter should be used to find alias
        IntegrationType integrationType = IntegrationType.valueOf(integrationTypeStr);

        // Look up the ConfigParameterValue for the IntegrationType and external property id
        List<ConfigParameterValue> configParamValues = configParamsService.getParameterValueByParameterNameAndValue(
                integrationType.getConstant(), sendingSystemPropertyId);
        if (configParamValues.isEmpty()) {
            return null;
        }

        // Validate that the parameter value is set at the property level
        validateConstantContextIsFullyQualified(configParamValues, integrationType.getConstant(), sendingSystemPropertyId);

        // Create a list of Propertys to return
        List<Property> properties = new ArrayList<>();
        for (ConfigParameterValue workingConfigValue : configParamValues) {
            // Build a WorkContextType from the ConfigParameterValue
            WorkContextType workContextType = PacmanWorkContextHelper.deriveWorkContextFromRegulatorOrConfigParamContext(workingConfigValue
                    .getContext());
            // Look up the Property based on the Client Code / Property Code in the WorkContext
            Property property = propertyService.getPropertyByCode(workContextType.getClientCode(), workContextType.getPropertyCode());
            if (property != null) {
                properties.add(property);
            }
        }

        return properties;
    }

    public IntegrationTypeAliasDto findIntegrationTypeAliasDto(BusinessModule businessModule) {
        List<IntegrationType> integrationTypes = IntegrationType.valuesForBusinessModule(businessModule);
        for (IntegrationType integrationType : integrationTypes) {

            String value = configParamsService.getParameterValue(integrationType.getConstant());
            if (StringUtils.isNotEmpty(value)) {
                return new IntegrationTypeAliasDto(integrationType, value);
            }
        }

        return null;
    }

    /**
     * This does validations of the constant returned to try to provide a more detailed message on what the consuming
     * rest consumers should expect
     *
     * @param configParamValues
     * @param constant
     * @param externalPropertyId
     */
    private void validateConstantContextIsFullyQualified(List<ConfigParameterValue> configParamValues, String constant,
                                                         String externalPropertyId) {

        if (configParamValues.size() > 1) {
            String errorString = "Values found for " + constant + " where we expected only one context for value " + constant + ": "
                    + externalPropertyId + " : \n";
            for (ConfigParameterValue value : configParamValues) {
                errorString += value.getContext() + ",\n";
            }
            throw new TetrisException(ErrorCode.TOO_MANY_VALUES_FOUND, errorString);
        }
        if (configParamValues.get(0).getContext().split("\\.").length != 3) {
            String errorString = "The value found for " + constant + " is not specified at the correct context for " + externalPropertyId
                    + " : \nSet at " + configParamValues.get(0).getContext();
            throw new TetrisException(ErrorCode.WEB_RATE_ALIAS_NOT_SET, errorString);
        }
    }

}
