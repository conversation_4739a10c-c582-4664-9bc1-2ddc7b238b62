package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.daoandentities.entity.ConnectivityLocation;
import com.ideas.tetris.platform.services.daoandentities.entity.PropertyConnectivityDetails;
import com.ideas.tetris.platform.services.daoandentities.entity.TransportMechanismType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Transactional
public class EditPropertyService {

    @GlobalCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("globalCrudServiceBean")
    private CrudService globalCrudService;

    public List<ConnectivityLocation> getConnectivityLocation() {
        return globalCrudService.findByNamedQuery(ConnectivityLocation.ALL_CONNECTIVITY_LOCATIONS);
    }

    public List<TransportMechanismType> getTransportMechanismType() {
        return globalCrudService.findByNamedQuery(TransportMechanismType.ALL);
    }

    public PropertyConnectivityDetails getPropertyConnectivityDetails(int propertyId) {
        return globalCrudService.findByNamedQuerySingleResult(PropertyConnectivityDetails.PROPERTY_CONNECTIVITY_DETAILS_BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
    }

    public PropertyConnectivityDetails updatePropertyConnectivityDetails(PropertyConnectivityDetails propertyConnectivityDetails) {
        return globalCrudService.save(propertyConnectivityDetails);
    }

    public int deletePropertyConnectivityDetails(Integer propertyId) {
        return globalCrudService.executeUpdateByNamedQuery(PropertyConnectivityDetails.PROPERTY_CONNECTIVITY_DELETE_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());
    }
}
