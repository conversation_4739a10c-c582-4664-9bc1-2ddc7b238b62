package com.ideas.tetris.pacman.services.reports.pricingpace.dto;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Date;


public class PricingPaceBarByLos {

    /*
     * Arrival_DT date, businessdate date, daystoArrival int, dow varchar (10),
     * LOS1 nvarchar(50), Price_LOS1 numeric(19,5),override1 nvarchar(50), LOS2
     * nvarchar(50), Price_LOS2 numeric(19,5),override2 nvarchar(50), LOS3
     * nvarchar(50), Price_LOS3 numeric(19,5),override3 nvarchar(50), LOS4
     * nvarchar(50), Price_LOS4 numeric(19,5),override4 nvarchar(50), LOS5
     * nvarchar(50), Price_LOS5 numeric(19,5),override5 nvarchar(50), LOS6
     * nvarchar(50), Price_LOS6 numeric(19,5),override6 nvarchar(50), LOS7
     * nvarchar(50), Price_LOS7 numeric(19,5),override7 nvarchar(50), LOS8
     * nvarchar(50), Price_LOS8 numeric(19,5),override8 nvarchar(50), Accom_name
     * nvarchar(150), Rooms_Sold numeric(18,0), Property_Rooms_Sold
     * numeric(18,0), Property_Forecast numeric(8,2), Accom_Forecast
     * numeric(8,2), PropertyOccFcst numeric(8,2), OccFcst numeric(8,2),
     * webrate1 numeric(8,2),comp1_name nvarchar(150), webrate2
     * numeric(8,2),comp2_name nvarchar(150), webrate3 numeric(8,2),comp3_name
     * nvarchar(150), webrate4 numeric(8,2),comp4_name nvarchar(150), webrate5
     * numeric(8,2),comp5_name nvarchar(150), webrate6 numeric(8,2),comp6_name
     * nvarchar(150), webrate7 numeric(8,2),comp7_name nvarchar(150), webrate8
     * numeric(8,2),comp8_name nvarchar(150), webrate9 numeric(8,2),comp9_name
     * nvarchar(150), webrate10 numeric(8,2),comp10_name nvarchar(150),
     * webrate11 numeric(8,2),comp11_name nvarchar(150), webrate12
     * numeric(8,2),comp12_name nvarchar(150), webrate13
     * numeric(8,2),comp13_name nvarchar(150), webrate14
     * numeric(8,2),comp14_name nvarchar(150), webrate15
     * numeric(8,2),comp15_name nvarchar(150)
     */

    private Date arrivalDate;
    private ZonedDateTime caughtUpdate;
    private Date businessDate;
    private Integer daysToArrival;
    private String dow;
    private Integer LOS;
    private String LOS1;
    private BigDecimal LOS1Price;
    private String override1;
    private String LOS2;
    private BigDecimal LOS2Price;
    private String override2;
    private String LOS3;
    private BigDecimal LOS3Price;
    private String override3;
    private String LOS4;
    private BigDecimal LOS4Price;
    private String override4;
    private String LOS5;
    private BigDecimal LOS5Price;
    private String override5;
    private String LOS6;
    private BigDecimal LOS6Price;
    private String override6;
    private String LOS7;
    private BigDecimal LOS7Price;
    private String override7;
    private String LOS8;
    private BigDecimal LOS8Price;
    private String override8;
    private String accomName;
    private BigDecimal roomSold;
    private BigDecimal propertyRoomSold;
    private BigDecimal propertyForecast;
    private BigDecimal accomForecast;
    private BigDecimal propertyForecastPercent;
    private BigDecimal accomForecastPercent;
    private BigDecimal webrate1;
    private String compName1;
    private BigDecimal webrate2;
    private String compName2;
    private BigDecimal webrate3;
    private String compName3;
    private BigDecimal webrate4;
    private String compName4;
    private BigDecimal webrate5;
    private String compName5;
    private BigDecimal webrate6;
    private String compName6;
    private BigDecimal webrate7;
    private String compName7;
    private BigDecimal webrate8;
    private String compName8;
    private BigDecimal webrate9;
    private String compName9;
    private BigDecimal webrate10;
    private String compName10;
    private BigDecimal webrate11;
    private String compName11;
    private BigDecimal webrate12;
    private String compName12;
    private BigDecimal webrate13;
    private String compName13;
    private BigDecimal webrate14;
    private String compName14;
    private BigDecimal webrate15;
    private String compName15;

    public Date getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public ZonedDateTime getCaughtUpdate() {
        return caughtUpdate;
    }

    public void setCaughtUpdate(ZonedDateTime caughtUpdate) {
        this.caughtUpdate = caughtUpdate;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public Integer getDaysToArrival() {
        return daysToArrival;
    }

    public void setDaysToArrival(Integer daysToArrival) {
        this.daysToArrival = daysToArrival;
    }

    public String getDow() {
        return dow;
    }

    public void setDow(String dow) {
        this.dow = dow;
    }

    public Integer getLOS() {
        return LOS;
    }

    public void setLOS(Integer lOS) {
        LOS = lOS;
    }

    public String getLOS1() {
        return LOS1;
    }

    public void setLOS1(String lOS1) {
        LOS1 = lOS1;
    }

    public BigDecimal getLOS1Price() {
        return LOS1Price;
    }

    public void setLOS1Price(BigDecimal lOS1Price) {
        LOS1Price = lOS1Price;
    }

    public String getOverride1() {
        return override1;
    }

    public void setOverride1(String override1) {
        this.override1 = override1;
    }

    public String getLOS2() {
        return LOS2;
    }

    public void setLOS2(String lOS2) {
        LOS2 = lOS2;
    }

    public BigDecimal getLOS2Price() {
        return LOS2Price;
    }

    public void setLOS2Price(BigDecimal lOS2Price) {
        LOS2Price = lOS2Price;
    }

    public String getOverride2() {
        return override2;
    }

    public void setOverride2(String override2) {
        this.override2 = override2;
    }

    public String getLOS3() {
        return LOS3;
    }

    public void setLOS3(String lOS3) {
        LOS3 = lOS3;
    }

    public BigDecimal getLOS3Price() {
        return LOS3Price;
    }

    public void setLOS3Price(BigDecimal lOS3Price) {
        LOS3Price = lOS3Price;
    }

    public String getOverride3() {
        return override3;
    }

    public void setOverride3(String override3) {
        this.override3 = override3;
    }

    public String getLOS4() {
        return LOS4;
    }

    public void setLOS4(String lOS4) {
        LOS4 = lOS4;
    }

    public BigDecimal getLOS4Price() {
        return LOS4Price;
    }

    public void setLOS4Price(BigDecimal lOS4Price) {
        LOS4Price = lOS4Price;
    }

    public String getOverride4() {
        return override4;
    }

    public void setOverride4(String override4) {
        this.override4 = override4;
    }

    public String getLOS5() {
        return LOS5;
    }

    public void setLOS5(String lOS5) {
        LOS5 = lOS5;
    }

    public BigDecimal getLOS5Price() {
        return LOS5Price;
    }

    public void setLOS5Price(BigDecimal lOS5Price) {
        LOS5Price = lOS5Price;
    }

    public String getOverride5() {
        return override5;
    }

    public void setOverride5(String override5) {
        this.override5 = override5;
    }

    public String getLOS6() {
        return LOS6;
    }

    public void setLOS6(String lOS6) {
        LOS6 = lOS6;
    }

    public BigDecimal getLOS6Price() {
        return LOS6Price;
    }

    public void setLOS6Price(BigDecimal lOS6Price) {
        LOS6Price = lOS6Price;
    }

    public String getOverride6() {
        return override6;
    }

    public void setOverride6(String override6) {
        this.override6 = override6;
    }

    public String getLOS7() {
        return LOS7;
    }

    public void setLOS7(String lOS7) {
        LOS7 = lOS7;
    }

    public BigDecimal getLOS7Price() {
        return LOS7Price;
    }

    public void setLOS7Price(BigDecimal lOS7Price) {
        LOS7Price = lOS7Price;
    }

    public String getOverride7() {
        return override7;
    }

    public void setOverride7(String override7) {
        this.override7 = override7;
    }

    public String getLOS8() {
        return LOS8;
    }

    public void setLOS8(String lOS8) {
        LOS8 = lOS8;
    }

    public BigDecimal getLOS8Price() {
        return LOS8Price;
    }

    public void setLOS8Price(BigDecimal lOS8Price) {
        LOS8Price = lOS8Price;
    }

    public String getOverride8() {
        return override8;
    }

    public void setOverride8(String override8) {
        this.override8 = override8;
    }

    public String getAccomName() {
        return accomName;
    }

    public void setAccomName(String accomName) {
        this.accomName = accomName;
    }

    public BigDecimal getRoomSold() {
        return roomSold;
    }

    public void setRoomSold(BigDecimal roomSold) {
        this.roomSold = roomSold;
    }

    public BigDecimal getPropertyRoomSold() {
        return propertyRoomSold;
    }

    public void setPropertyRoomSold(BigDecimal propertyRoomSold) {
        this.propertyRoomSold = propertyRoomSold;
    }

    public BigDecimal getPropertyForecast() {
        return propertyForecast;
    }

    public void setPropertyForecast(BigDecimal propertyForecast) {
        this.propertyForecast = propertyForecast;
    }

    public BigDecimal getAccomForecast() {
        return accomForecast;
    }

    public void setAccomForecast(BigDecimal accomForecast) {
        this.accomForecast = accomForecast;
    }

    public BigDecimal getPropertyForecastPercent() {
        return propertyForecastPercent;
    }

    public void setPropertyForecastPercent(BigDecimal propertyForecastPercent) {
        this.propertyForecastPercent = propertyForecastPercent;
    }

    public BigDecimal getAccomForecastPercent() {
        return accomForecastPercent;
    }

    public void setAccomForecastPercent(BigDecimal accomForecastPercent) {
        this.accomForecastPercent = accomForecastPercent;
    }

    public BigDecimal getWebrate1() {
        return webrate1;
    }

    public void setWebrate1(BigDecimal webrate1) {
        this.webrate1 = webrate1;
    }

    public String getCompName1() {
        return compName1;
    }

    public void setCompName1(String compName1) {
        this.compName1 = compName1;
    }

    public BigDecimal getWebrate2() {
        return webrate2;
    }

    public void setWebrate2(BigDecimal webrate2) {
        this.webrate2 = webrate2;
    }

    public String getCompName2() {
        return compName2;
    }

    public void setCompName2(String compName2) {
        this.compName2 = compName2;
    }

    public BigDecimal getWebrate3() {
        return webrate3;
    }

    public void setWebrate3(BigDecimal webrate3) {
        this.webrate3 = webrate3;
    }

    public String getCompName3() {
        return compName3;
    }

    public void setCompName3(String compName3) {
        this.compName3 = compName3;
    }

    public BigDecimal getWebrate4() {
        return webrate4;
    }

    public void setWebrate4(BigDecimal webrate4) {
        this.webrate4 = webrate4;
    }

    public String getCompName4() {
        return compName4;
    }

    public void setCompName4(String compName4) {
        this.compName4 = compName4;
    }

    public BigDecimal getWebrate5() {
        return webrate5;
    }

    public void setWebrate5(BigDecimal webrate5) {
        this.webrate5 = webrate5;
    }

    public String getCompName5() {
        return compName5;
    }

    public void setCompName5(String compName5) {
        this.compName5 = compName5;
    }

    public BigDecimal getWebrate6() {
        return webrate6;
    }

    public void setWebrate6(BigDecimal webrate6) {
        this.webrate6 = webrate6;
    }

    public String getCompName6() {
        return compName6;
    }

    public void setCompName6(String compName6) {
        this.compName6 = compName6;
    }

    public BigDecimal getWebrate7() {
        return webrate7;
    }

    public void setWebrate7(BigDecimal webrate7) {
        this.webrate7 = webrate7;
    }

    public String getCompName7() {
        return compName7;
    }

    public void setCompName7(String compName7) {
        this.compName7 = compName7;
    }

    public BigDecimal getWebrate8() {
        return webrate8;
    }

    public void setWebrate8(BigDecimal webrate8) {
        this.webrate8 = webrate8;
    }

    public String getCompName8() {
        return compName8;
    }

    public void setCompName8(String compName8) {
        this.compName8 = compName8;
    }

    public BigDecimal getWebrate9() {
        return webrate9;
    }

    public void setWebrate9(BigDecimal webrate9) {
        this.webrate9 = webrate9;
    }

    public String getCompName9() {
        return compName9;
    }

    public void setCompName9(String compName9) {
        this.compName9 = compName9;
    }

    public BigDecimal getWebrate10() {
        return webrate10;
    }

    public void setWebrate10(BigDecimal webrate10) {
        this.webrate10 = webrate10;
    }

    public String getCompName10() {
        return compName10;
    }

    public void setCompName10(String compName10) {
        this.compName10 = compName10;
    }

    public BigDecimal getWebrate11() {
        return webrate11;
    }

    public void setWebrate11(BigDecimal webrate11) {
        this.webrate11 = webrate11;
    }

    public String getCompName11() {
        return compName11;
    }

    public void setCompName11(String compName11) {
        this.compName11 = compName11;
    }

    public BigDecimal getWebrate12() {
        return webrate12;
    }

    public void setWebrate12(BigDecimal webrate12) {
        this.webrate12 = webrate12;
    }

    public String getCompName12() {
        return compName12;
    }

    public void setCompName12(String compName12) {
        this.compName12 = compName12;
    }

    public BigDecimal getWebrate13() {
        return webrate13;
    }

    public void setWebrate13(BigDecimal webrate13) {
        this.webrate13 = webrate13;
    }

    public String getCompName13() {
        return compName13;
    }

    public void setCompName13(String compName13) {
        this.compName13 = compName13;
    }

    public BigDecimal getWebrate14() {
        return webrate14;
    }

    public void setWebrate14(BigDecimal webrate14) {
        this.webrate14 = webrate14;
    }

    public String getCompName14() {
        return compName14;
    }

    public void setCompName14(String compName14) {
        this.compName14 = compName14;
    }

    public BigDecimal getWebrate15() {
        return webrate15;
    }

    public void setWebrate15(BigDecimal webrate15) {
        this.webrate15 = webrate15;
    }

    public String getCompName15() {
        return compName15;
    }

    public void setCompName15(String compName15) {
        this.compName15 = compName15;
    }

}
