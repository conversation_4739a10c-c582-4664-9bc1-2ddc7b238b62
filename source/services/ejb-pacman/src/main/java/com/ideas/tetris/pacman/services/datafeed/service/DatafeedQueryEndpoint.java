package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.datafeed.dto.MarketSegmentHistoryST19Dto;
import com.ideas.tetris.pacman.services.datafeed.dto.MarketSegmentHistoryST2YDto;
import com.ideas.tetris.pacman.services.datafeed.entity.*;

import java.util.Optional;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static java.util.Optional.empty;

public enum DatafeedQueryEndpoint {

    ROOM_TYPE(RoomType.class, RoomType.FIND_BY_DATES_BETWEEN, false, false, PROPERTY_ID, START_DATE, END_DATE),
    ROOM_TYPE_REV_PLAN(RoomTypeRevPlan.class, RoomType.REV_PLAN_FIND_BY_DATES_BETWEEN, false, false, PROPERTY_ID, START_DATE, END_DATE),
    ROOM_TYPE_HISTORY(RoomTypeHistory.class, RoomTypeHistory.FIND_BY_DATES_BETWEEN, false, true, PROPERTY_ID, START_DATE, END_DATE, CAUGHT_UP_DATE),
    MARKET_SEGMENT(MarketSegment.class, MarketSegment.FIND_BY_DATES_BETWEEN, false, false, PROPERTY_ID, START_DATE, END_DATE),
    MARKET_SEGMENT_HISTORY_ST2Y(MarketSegmentHistoryST2YDto.class, MarketSegmentHistorySTLY.FIND_BY_DATES_BETWEEN_ST2Y, false, true, PROPERTY_ID, START_DATE, END_DATE, CAUGHT_UP_DATE, IS_EXTENDED_WINDOW_ENABLED),
    MARKET_SEGMENT_HISTORY_ST19(MarketSegmentHistoryST19Dto.class, MarketSegmentHistorySTLY.FIND_BY_DATES_BETWEEN_ST19, false, true, PROPERTY_ID, START_DATE, END_DATE, CAUGHT_UP_DATE, IS_EXTENDED_WINDOW_ENABLED),
    MARKET_SEGMENT_HISTORY_ST19_OPT(MarketSegmentHistoryST19Dto.class, MarketSegmentHistorySTLY.FIND_BY_DATES_BETWEEN_ST19_OPT, false, true, PROPERTY_ID, START_DATE, END_DATE, CAUGHT_UP_DATE, IS_EXTENDED_WINDOW_ENABLED),

    ORIGINAL_MARKET_SEGMENT_FORECAST(OriginalMarketSegmentForecast.class, OriginalMarketSegmentForecast.FIND_BY_DATES_BETWEEN, false, false, CLIENT_CODE, PROPERTY_CODE, PROPERTY_ID, START_DATE, END_DATE),
    TOTAL_ACTIVITY_SUMMARY(TotalActivitySummary.class, TotalActivitySummary.FIND_BY_DATES_BETWEEN, false, false, CLIENT_CODE, PROPERTY_CODE, PROPERTY_ID, START_DATE, END_DATE),
    ROOM_CLASS(RoomClass.class, RoomClass.FIND_BY_DATES_BETWEEN, false, true, PROPERTY_ID, START_DATE, END_DATE),
    FORECAST_GROUP_HISTORY(ForecastGroupHistory.class, ForecastGroupHistory.FIND_BY_DATES_BETWEEN, false, true, PROPERTY_ID, START_DATE, END_DATE, IS_EXTENDED_WINDOW_ENABLED, IS_OPTIX_DATAFEED),
    AUTHORIZATION_GROUP(PropertyAuthorizationGroup.class, PropertyAuthorizationGroup.FIND_BY_CLIENT_ID, true, false, CLIENT_CODE),
    SPECIAL_EVENT(SpecialEvent.class, SpecialEvent.FIND_FEATURE_SPECIAL_EVENTS, false, false, PROPERTY_ID, START_DATE, END_DATE, DATAFEED_SPECIAL_EVENT_INSTANCE_NAME_ENABLED),
    INDIVIDUAL_GROUP_WASH_BY_GROUP(IndividualGroupWashByGroup.class, IndividualGroupWashByGroup.FIND_BY_DATE, false, false, PROPERTY_ID, START_DATE),
    INFORMATION_MANAGER_EXCEPTION(InformationManagerExceptions.class, InformationManagerExceptions.FIND_ALL_EXCEPTIONS, false, false, PROPERTY_ID, LAST_SUCCESS_DATE),
    INFORMATION_MANAGER_NOTIFICATION(InformationManagerNotification.class, InformationManagerNotification.FIND_BY_LAST_MODIFIED_DATE, false, false, PROPERTY_ID, LAST_SUCCESS_DATE, AGILE_RATES_ENABLED),
    OVERBOOKING_CONFIGURATION(OverbookingConfiguration.class, OverbookingConfiguration.FIND_BY_START_DATE, false, false, PROPERTY_ID, START_DATE),
    PRICE_STRATEGY_CLOSE_RATE_PLAN(PriceStrategyCloseRatePlan.class, PriceStrategyCloseRatePlan.FIND_CLOSE_RATE_PLANS, false, false, PROPERTY_ID, START_DATE),
    PROPERTY_LEVEL_DATA(PropertyLevelData.class, PropertyLevelData.FIND_BY_DATES_BETWEEN, false, true, PROPERTY_ID, START_DATE, END_DATE, DATAFEED_SPECIAL_EVENT_INSTANCE_NAME_ENABLED);
    private final Class<?> entityClass;
    private final String namedQuery;
    private final String[] parameters;
    private final Boolean clientLevel;
    private final Boolean historyDataOffsetApplicable;

    DatafeedQueryEndpoint(Class<?> entityClass, String namedQuery, Boolean clientLevel, Boolean historyDataOffsetApplicable, String... parameters) {
        this.entityClass = entityClass;
        this.namedQuery = namedQuery;
        this.clientLevel = clientLevel;
        this.historyDataOffsetApplicable = historyDataOffsetApplicable;
        this.parameters = parameters;
    }

    public Class<?> getEntityClass() {
        return entityClass;
    }

    public String getNamedQuery() {
        return namedQuery;
    }

    public Boolean isClientLevel() {
        return clientLevel;
    }

    public Boolean getHistoryDataOffsetApplicable() {
        return historyDataOffsetApplicable;
    }

    public boolean hasParameter(String param) {
        for (String parameter : parameters) {
            if (param.equals(parameter)) {
                return true;
            }
        }
        return false;
    }

    public static Optional<DatafeedQueryEndpoint> valueOfSimpleClassName(String simpleClassName, boolean toggleForST19DataFeed, boolean toggleForST2yDataFeed) {
        for (DatafeedQueryEndpoint datafeedQueryEndpoint : values()) {
            if (simpleClassName.equals(datafeedQueryEndpoint.getEntityClass().getSimpleName())) {
                if (datafeedQueryEndpoint.getEntityClass().getSimpleName().equals("MarketSegmentHistoryST19Dto") && toggleForST19DataFeed) {
                    return Optional.of(DatafeedQueryEndpoint.MARKET_SEGMENT_HISTORY_ST19_OPT);
                }
                if (datafeedQueryEndpoint.getEntityClass().getSimpleName().equals("MarketSegmentHistoryST2YDto") && toggleForST2yDataFeed) {
                    return empty();
                }
                return Optional.of(datafeedQueryEndpoint);
            }
        }
        return empty();
    }

}
