package com.ideas.tetris.pacman.services.reports.restrictionlevel;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Transactional
public class FPLosFullReport extends RestrictionLevelReport {
    @Override
    protected String getQuery() {
        return "select * from dbo.ufn_restriction_level_report_by_fplos_full(:start_date, :end_date, :caught_date, :accom_type_id, :isRollingDate, :rollingStartDate, :rollingEndDate, :isDecisionAtHotelLevel) order by occupancy_date";
    }
}
