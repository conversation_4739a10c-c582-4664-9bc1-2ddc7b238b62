package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.CPCeilingFloorTransientConfiguration;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.security.User;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertJavaUtilDateToLocalDate;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertJodaToJavaLocalDate;

@Component
@Transactional
public class CPCeilingFloorTransientConfigurationService {

    @Autowired
    PricingConfigurationService pricingConfigurationService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    PropertyService propertyService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    public List getContinuousPricingFloorCeilingTransientConfiguration(DatafeedRequest datafeedRequest) {
        List<CPCeilingFloorTransientConfiguration> cpCeilingFloorTransientConfigurations = new ArrayList<>();
        List<PricingBaseAccomType> allPricingBaseAccomTypes = pricingConfigurationService.getAllTransientPricingBaseAccomTypesForAllAccomTypes();
        cpCeilingFloorTransientConfigurations.addAll(populateCPCeilingFloorTransientConfiguration(
                pricingConfigurationService.getPricingBaseAccomTypesDefaults(allPricingBaseAccomTypes), Constants.DEFAULT));
        cpCeilingFloorTransientConfigurations.addAll(populateCPCeilingFloorTransientConfiguration(
                getTransientPricingbaseAccomTypeSeasons(allPricingBaseAccomTypes, datafeedRequest.getStartDate()), Constants.SEASONAL));
        return cpCeilingFloorTransientConfigurations;
    }

    private List<PricingBaseAccomType> getTransientPricingbaseAccomTypeSeasons(List<PricingBaseAccomType> allPricingBaseAccomTypes, Date startDate) {
        LocalDate startLocalDate = convertJavaUtilDateToLocalDate(startDate);
        List<PricingBaseAccomType> pricingBaseAccomTypes = pricingConfigurationService.getPricingBaseAccomTypesSeasons(allPricingBaseAccomTypes);
        return pricingBaseAccomTypes.stream().filter(pricingBaseAccomType ->
                !convertJodaToJavaLocalDate(pricingBaseAccomType.getEndDate()).isBefore(startLocalDate)).collect(Collectors.toList());
    }

    private BigDecimal setScaleTwoFor(BigDecimal value) {
        return (value == null) ? null : value.setScale(2);
    }

    private List<CPCeilingFloorTransientConfiguration> populateCPCeilingFloorTransientConfiguration(List<PricingBaseAccomType> pricingBaseAccomTypeList, final String category) {
        List<CPCeilingFloorTransientConfiguration> cpCeilingFloorTransientConfigurations = new ArrayList<>();
        for (PricingBaseAccomType pricingBaseAccomType : pricingBaseAccomTypeList) {
            cpCeilingFloorTransientConfigurations.add(getConfiguration(category, pricingBaseAccomType));
        }
        return cpCeilingFloorTransientConfigurations;
    }

    private CPCeilingFloorTransientConfiguration getConfiguration(String category, PricingBaseAccomType pricingBaseAccomType) {
        return getCpCeilingFloorTransientConfigurationPricesWithTax(category, pricingBaseAccomType);
    }

    private boolean useUniqueUserIDInsteadOfEmailEnabled() {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.USE_UNIQUE_USERID_INSTEADOF_EMAILID.getParameterName()));
    }

    private CPCeilingFloorTransientConfiguration getCpCeilingFloorTransientConfigurationPricesWithTax(String category, PricingBaseAccomType pricingBaseAccomType) {
        CPCeilingFloorTransientConfiguration cpCeilingFloorTransientConfiguration =
                new CPCeilingFloorTransientConfiguration(pricingBaseAccomType.getAccomType().getAccomTypeCode(),
                        pricingBaseAccomType.getStartDate() != null ? pricingBaseAccomType.getStartDate().toDate() : null,
                        pricingBaseAccomType.getEndDate() != null ? pricingBaseAccomType.getEndDate().toDate() : null,
                        category,
                        setScaleTwoFor(pricingBaseAccomType.getSundayCeilingRateWithTax()),
                        setScaleTwoFor(pricingBaseAccomType.getSundayFloorRateWithTax()),
                        setScaleTwoFor(pricingBaseAccomType.getMondayCeilingRateWithTax()),
                        setScaleTwoFor(pricingBaseAccomType.getMondayFloorRateWithTax()),
                        setScaleTwoFor(pricingBaseAccomType.getTuesdayCeilingRateWithTax()),
                        setScaleTwoFor(pricingBaseAccomType.getTuesdayFloorRateWithTax()),
                        setScaleTwoFor(pricingBaseAccomType.getWednesdayCeilingRateWithTax()),
                        setScaleTwoFor(pricingBaseAccomType.getWednesdayFloorRateWithTax()),
                        setScaleTwoFor(pricingBaseAccomType.getThursdayCeilingRateWithTax()),
                        setScaleTwoFor(pricingBaseAccomType.getThursdayFloorRateWithTax()),
                        setScaleTwoFor(pricingBaseAccomType.getFridayCeilingRateWithTax()),
                        setScaleTwoFor(pricingBaseAccomType.getFridayFloorRateWithTax()),
                        setScaleTwoFor(pricingBaseAccomType.getSaturdayCeilingRateWithTax()),
                        setScaleTwoFor(pricingBaseAccomType.getSaturdayFloorRateWithTax()),
                        pricingBaseAccomType.getSeasonName(),
                        getLastUpdatedDateFor(pricingBaseAccomType),
                        getLastUpdatedByUser(pricingBaseAccomType.getLastUpdatedByUserId()));

        if (pricingBaseAccomType.getProductID() != null) {
            cpCeilingFloorTransientConfiguration.setProductName(fetchProduct(pricingBaseAccomType.getProductID()));
        }
        return cpCeilingFloorTransientConfiguration;
    }

    private String fetchProduct(Integer productID) {
        Product p = tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID, (QueryParameter.with("productId", productID).parameters()));
        return p != null ? p.getName() : "";
    }

    private String getLastUpdatedByUser(Integer lastUpdatedByUserId) {
        return useUniqueUserIDInsteadOfEmailEnabled() ? getUserName(lastUpdatedByUserId) : getUserEmail(lastUpdatedByUserId);
    }

    public String getLastUpdatedDateFor(PricingBaseAccomType pricingBaseAccomType) {
        return Optional.ofNullable(pricingBaseAccomType.getLastUpdatedDate()).map(this::getLastUpdatedDate).orElse(StringUtils.EMPTY);
    }

    private String getLastUpdatedDate(LocalDateTime localDateTime) {
        ZonedDateTime lastUpdatedInPropertyTimeZone = JavaLocalDateUtils.convertToZone(localDateTime, ZoneId.of(propertyService.getPropertyTimeZone().getID()));
        return DateTimeFormatter.ofPattern("dd-MMM-yyyy HH:mm:ss z").format(lastUpdatedInPropertyTimeZone);
    }

    public String getUserEmail(Integer userId) {
        return getUserByID(userId)
                .map(User::getEmail)
                .orElse("");
    }

    public String getUserName(Integer userId) {
        return getUserByID(userId)
                .map(User::getName)
                .orElse("");
    }

    private Optional<User> getUserByID(Integer userId) {
        return Optional.ofNullable(userId)
                .map(id -> tenantCrudService.find(User.class, id));
    }
}
