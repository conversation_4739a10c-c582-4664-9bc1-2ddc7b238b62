package com.ideas.tetris.pacman.services.reports.pricingpace.dto;

import org.joda.time.DateTime;

import java.math.BigDecimal;
import java.util.Date;

public class PricingPaceBarByDay {

    /*
     *
     * Caught_up_DTTM datetime,
     * businessdate date,
     * dow varchar (10),
     * override nvarchar(50),
     * LOS int ,
     * Accom_name nvarchar(150),
     * Rate_Code_Name nvarchar(50),
     * price numeric(19,5),
     * Rooms_Sold numeric(18,0),
     * Property_Rooms_Sold numeric(18,0),
     * Property_Forecast numeric(8,2),
     * Accom_Forecast numeric(8,2),
     * PropertyOccFcst numeric(8,2),
     * OccFcst numeric(8,2)
     */

    private Integer daysToArrival;
    private DateTime caughtUpdate;
    private Date businessDate;
    private String dow;
    private String override;
    private Integer los;
    private String accomName;
    private String rateCodeName;
    private BigDecimal price;
    private BigDecimal roomSold;
    private BigDecimal propertyRoomSold;
    private BigDecimal propertyForecast;
    private BigDecimal accomForecast;
    private BigDecimal propertyForecastPercent;
    private BigDecimal accomForecastPercent;

    public Integer getDaysToArrival() {
        return daysToArrival;
    }

    public void setDaysToArrival(Integer daysToArrival) {
        this.daysToArrival = daysToArrival;
    }

    public DateTime getCaughtUpdate() {
        return caughtUpdate;
    }

    public void setCaughtUpdate(DateTime caughtUpdate) {
        this.caughtUpdate = caughtUpdate;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public String getDow() {
        return dow;
    }

    public void setDow(String dow) {
        this.dow = dow;
    }

    public String getOverride() {
        return override;
    }

    public void setOverride(String override) {
        this.override = override;
    }

    public Integer getLos() {
        return los;
    }

    public void setLos(Integer los) {
        this.los = los;
    }

    public String getAccomName() {
        return accomName;
    }

    public void setAccomName(String accomName) {
        this.accomName = accomName;
    }

    public String getRateCodeName() {
        return rateCodeName;
    }

    public void setRateCodeName(String rateCodeName) {
        this.rateCodeName = rateCodeName;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getRoomSold() {
        return roomSold;
    }

    public void setRoomSold(BigDecimal roomSold) {
        this.roomSold = roomSold;
    }

    public BigDecimal getPropertyRoomSold() {
        return propertyRoomSold;
    }

    public void setPropertyRoomSold(BigDecimal propertyRoomSold) {
        this.propertyRoomSold = propertyRoomSold;
    }

    public BigDecimal getPropertyForecast() {
        return propertyForecast;
    }

    public void setPropertyForecast(BigDecimal propertyForecast) {
        this.propertyForecast = propertyForecast;
    }

    public BigDecimal getAccomForecast() {
        return accomForecast;
    }

    public void setAccomForecast(BigDecimal accomForecast) {
        this.accomForecast = accomForecast;
    }

    public BigDecimal getPropertyForecastPercent() {
        return propertyForecastPercent;
    }

    public void setPropertyForecastPercent(BigDecimal propertyForecastPercent) {
        this.propertyForecastPercent = propertyForecastPercent;
    }

    public BigDecimal getAccomForecastPercent() {
        return accomForecastPercent;
    }

    public void setAccomForecastPercent(BigDecimal accomForecastPercent) {
        this.accomForecastPercent = accomForecastPercent;
    }

}
