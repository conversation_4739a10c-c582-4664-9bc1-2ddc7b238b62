package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.opera.constants.OperaSummaryServiceConstants;
import com.ideas.tetris.pacman.services.opera.metric.OperaMetrics;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.JavaLocalDateInterval;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * Created by idnpak on 2/24/2015.
 */
@Component
public class OperaTransformSummaryDataService {

    public static final OperaMetrics<TransformSummaryMetricType> metricsTransformSummary = new OperaMetrics<>();
    public static final OperaMetrics<TransformSummaryServiceMetricType> metricTransformSummary = new OperaMetrics<>();
    private static final Logger LOGGER = Logger.getLogger(OperaTransformSummaryDataService.class.getName());

    private static final String CTAT_DATA_LOAD_ID = "ctatDataLoadId";
    private static final String PTAT_DATA_LOAD_ID = "ptatDataLoadId";
    private static final String CTATPTAT_DATA_LOAD_ID = "ctatptatDataLoadId";
    private static final String BUSINESS_START_DATE = "businessStartDate";
    private static final String BUSINESS_END_DATE = "businessEndDate";
    private static final String ACCOM_CODES = "accomCodes";
    private static final String CSAT_DATA_LOAD_ID = "csatDataLoadId";
    private static final String PSAT_DATA_LOAD_ID = "psatDataLoadId";
    private static final String CSATPSAT_DATA_LOAD_ID = "csatpsatDataLoadId";
    private static final String CSAT = "CSAT";
    private static final String BUSINESS_DATE = "businessDate";
    private static final String PSAT = "PSAT";
    private static final String PTAT = "PTAT";
    private static final String CTAT = "CTAT";

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    OperaUtilityService operaUtilityService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    public int transformSummaryData(String correlationId) {
        LOGGER.info("Started transforming summary data for feed : " + correlationId);
        int numRows = 0;
        try {
            metricsTransformSummary.start(TransformSummaryMetricType.TRANSFORM_STAGE_DATA_SUMMARY);
            numRows += transformStageData(correlationId);
            metricsTransformSummary.stop(TransformSummaryMetricType.TRANSFORM_STAGE_DATA_SUMMARY);
        } finally {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Finished transforming all stage Summary data " +
                        numRows + "  rows:\n" + metricsTransformSummary.toString());
            }
        }
        LOGGER.info("Completed transforming summary data for feed : " + correlationId);
        return numRows;
    }

    public int zeroFillAdjustments(String correlationId) {
        LOGGER.info("Started zero fill summary data for feed : " + correlationId);
        int numRows = 0;
        try {
            metricsTransformSummary.start(TransformSummaryMetricType.TRANSFORM_STAGE_DATA_SUMMARY);
            numRows += zeroFillAndOtherAdjustments(correlationId);
            numRows += crudService.executeUpdateByNativeQuery(OperaOccupancySummaryData.UPDATE_FOREIGN_KEYS_SQL);
            metricsTransformSummary.stop(TransformSummaryMetricType.TRANSFORM_STAGE_DATA_SUMMARY);
        } finally {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Finished zero fill all stage Summary data " +
                        numRows + "  rows:\n" + metricsTransformSummary.toString());
            }
        }
        LOGGER.info("Completed zero fill summary data for feed : " + correlationId);
        return numRows;
    }

    public int transformStageData(String correlationId) {
        int numRows = 0;
        Map<String, Integer> dataLoadMetadataIDMap = operaUtilityService.getDataLoadMetadataIDMap(correlationId);
        try {

            metricTransformSummary.start(TransformSummaryServiceMetricType.UPDATE_ACCOM_CAPACITIES);
            updateAccomCapacities(dataLoadMetadataIDMap);
            metricTransformSummary.stop(TransformSummaryServiceMetricType.UPDATE_ACCOM_CAPACITIES);

            int roomCountAdjustments = crudService
                    .executeUpdateByNativeQuery(OperaSummaryServiceConstants.CHANGE_NEGATIVE_ROOM_NUMBER_VALUES_TO_ZERO);

            if (roomCountAdjustments > 0) {
                LOGGER.info("Room counts for " + roomCountAdjustments +
                        " Occupancy_Summary row(s) adjusted for correlation ID " + correlationId +
                        " because values were negative.");
            }

        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, correlationId
                    + ": Could not load occupancy summary data ", e);
        } finally {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Finished transforming all summary stage data " +
                        numRows + "  rows:\n" + metricTransformSummary.toString());
            }
        }
        return numRows;
    }

    public int zeroFillAndOtherAdjustments(String correlationId) {
        int numRows = 0;
        Map<String, Integer> dataLoadMetadataIDMap = operaUtilityService.getDataLoadMetadataIDMap(correlationId);
        JavaLocalDateInterval businessDateRange = operaUtilityService.getBusinessDateRange();

        metricTransformSummary.start(TransformSummaryServiceMetricType.ZERO_FILL_RT_OCCUPANCY);
        numRows += fillZeroMissingRoomtypeOccupancydate(dataLoadMetadataIDMap, businessDateRange);
        metricTransformSummary.stop(TransformSummaryServiceMetricType.ZERO_FILL_RT_OCCUPANCY);

        if (!pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.OPERA_TRANSFORM_TRANSACTIONS_MULTI_STEP)) {
            metricTransformSummary.start(TransformSummaryServiceMetricType.ZERO_FILL_RT_MS_OCCUPANCY);
            numRows += fillZeroMissingRoomtypeMarketcodeOccupancydate(dataLoadMetadataIDMap, businessDateRange);
            metricTransformSummary.stop(TransformSummaryServiceMetricType.ZERO_FILL_RT_MS_OCCUPANCY);
        } else {
            LOGGER.info(MessageFormat.format("Skipped {0} as {1} is enabled", TransformSummaryServiceMetricType.ZERO_FILL_RT_MS_OCCUPANCY, FeatureTogglesConfigParamName.OPERA_TRANSFORM_TRANSACTIONS_MULTI_STEP.getParameterName()));
        }

        // Temporary fix for departure counts adjustment
        // For transaction Dt = min(occupancy_dt) in current extract.
        // Retain value from dbo.mkt_accom_activity to avoid zero departure
        // count for this date in current extract

        metricTransformSummary.start(TransformSummaryServiceMetricType.ADJUST_DEPARTURE_COUNT);
        numRows += adjustDepartureCountsFromPreviousProcessing(dataLoadMetadataIDMap,
                businessDateRange.getStartDate());
        metricTransformSummary.stop(TransformSummaryServiceMetricType.ADJUST_DEPARTURE_COUNT);
        return numRows;
    }

    public int updateAccomCapacities(Map<String, Integer> dataLoadIdMap) {
        return crudService.executeUpdateByNativeQuery(OperaSummaryServiceConstants.UPDATE_ACCOM_TYPE_CAPACITIES,
                QueryParameter.with("ctatDataloadId", dataLoadIdMap.get(CTAT)).parameters());
    }

    public int fillZeroMissingRoomtypeMarketcodeOccupancydate(Map<String, Integer> dataLoadIdMap,
                                                              JavaLocalDateInterval businessDateRange) {
        Date businessDate = operaUtilityService.getBusinessDateFromRawFormatted().toDate();
        final String query = OperaSummaryServiceConstants.SQL_ZEROFILL_MS_AT_SUMMARY_OPTIMIZED;
        int numRowsUpdated = crudService.executeUpdateByNativeQuery(
                query,
                QueryParameter.with(CSAT_DATA_LOAD_ID, dataLoadIdMap.get(CSAT)).and(BUSINESS_DATE, businessDate)
                        .and(PSAT_DATA_LOAD_ID, dataLoadIdMap.get(PSAT))
                        .and(CSATPSAT_DATA_LOAD_ID, Arrays.asList(dataLoadIdMap.get(CSAT), dataLoadIdMap.get(PSAT)))
                        .and(BUSINESS_START_DATE, businessDateRange.getStartDate())
                        .and(BUSINESS_END_DATE, businessDateRange.getEndDate()).parameters());
        LOGGER.info("INSERT_ZERO_VALUES_MISSING_ACCOMTYPE_MKTCODE_OCCUPANCYDATE : " + numRowsUpdated);
        return numRowsUpdated;
    }

    int zeroFillMissingRoomTypeMktSummaryForRoomTypeCodes(List<String> accomTypeCodes, Map<String, Integer> dataLoadIdMap,
                                                          JavaLocalDateInterval businessDateRange) {
        Date businessDate = operaUtilityService.getBusinessDateFromRawFormatted().toDate();
        int numRowsUpdated = crudService.executeUpdateByNativeQuery(
                OperaSummaryServiceConstants.INSERT_ZERO_VALUES_MISSING_ACCOMTYPE_MKTCODE_OCCUPANCYDATE_BY_ACCOM_TYPE_CODES,
                QueryParameter.with(CSAT_DATA_LOAD_ID, dataLoadIdMap.get(CSAT)).and(BUSINESS_DATE, businessDate)
                        .and(PSAT_DATA_LOAD_ID, dataLoadIdMap.get(PSAT))
                        .and(CSATPSAT_DATA_LOAD_ID, Arrays.asList(dataLoadIdMap.get(CSAT), dataLoadIdMap.get(PSAT)))
                        .and(BUSINESS_START_DATE, businessDateRange.getStartDate())
                        .and(BUSINESS_END_DATE, businessDateRange.getEndDate())
                        .and(ACCOM_CODES, accomTypeCodes)
                        .parameters());
        LOGGER.info("INSERT_ZERO_VALUES_MISSING_ACCOMTYPE_MKTCODE_OCCUPANCYDATE_BY_ACCOM_TYPE_CODES : " + numRowsUpdated);

        return numRowsUpdated;
    }

    public int fillZeroMissingRoomtypeOccupancydate(Map<String, Integer> dataLoadIdMap,
                                                    JavaLocalDateInterval businessDateRange) {
        Date businessDate = operaUtilityService.getBusinessDateFromRawFormatted().toDate();
        return crudService.executeUpdateByNativeQuery(
                OperaSummaryServiceConstants.INSERT_ZERO_VALUES_MISSING_ACCOMTYPE_OCCUPANCYDATE,
                QueryParameter.with(CTAT_DATA_LOAD_ID, dataLoadIdMap.get(CTAT)).and(BUSINESS_DATE, businessDate)
                        .and(PTAT_DATA_LOAD_ID, dataLoadIdMap.get(PTAT))
                        .and(CTATPTAT_DATA_LOAD_ID, Arrays.asList(dataLoadIdMap.get(CTAT), dataLoadIdMap.get(PTAT)))
                        .and(BUSINESS_START_DATE, businessDateRange.getStartDate())
                        .and(BUSINESS_END_DATE, businessDateRange.getEndDate()).parameters());
    }

    int zeroFillMissingRoomTypeSummaryForRoomTypeCodes(List<String> accomTypeCodes, Map<String, Integer> dataLoadIdMap,
                                                       JavaLocalDateInterval businessDateRange) {
        Date businessDate = operaUtilityService.getBusinessDateFromRawFormatted().toDate();
        int numRowsUpdated = crudService.executeUpdateByNativeQuery(
                OperaSummaryServiceConstants.INSERT_ZERO_VALUES_MISSING_ACCOMTYPE_OCCUPANCYDATE_BY_ACCOM_TYPE_CODES,
                QueryParameter.with(CTAT_DATA_LOAD_ID, dataLoadIdMap.get(CTAT)).and(BUSINESS_DATE, businessDate)
                        .and(PTAT_DATA_LOAD_ID, dataLoadIdMap.get(PTAT))
                        .and(CTATPTAT_DATA_LOAD_ID, Arrays.asList(dataLoadIdMap.get(CTAT), dataLoadIdMap.get(PTAT)))
                        .and(BUSINESS_START_DATE, businessDateRange.getStartDate())
                        .and(BUSINESS_END_DATE, businessDateRange.getEndDate())
                        .and(ACCOM_CODES, accomTypeCodes)
                        .parameters());
        LOGGER.info("INSERT_ZERO_VALUES_MISSING_ACCOMTYPE_OCCUPANCYDATE_BY_ACCOM_TYPE_CODES : " + numRowsUpdated);

        return numRowsUpdated;
    }

    private int adjustDepartureCountsFromPreviousProcessing(Map<String, Integer> dataLoadIdMap,
                                                            LocalDate businessStartDate) {
        LOGGER.info("STARTED ADJUSTING DEPARTURE COUNTS FOR LAST DAY IN PAST");
        int numRowsUpdated = 0;
        numRowsUpdated = crudService.executeUpdateByNativeQuery(
                OperaSummaryServiceConstants.UPDATE_DEPARTURES_FOR_EARLIEST_PAST_DAY_WITH_MSBR,
                QueryParameter.with(BUSINESS_START_DATE, businessStartDate)
                        .and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        LOGGER.info("ADJUSTED DEPARTURE COUNTS FOR : " + numRowsUpdated + " records");
        return numRowsUpdated;
    }

    public String specificZeroFilling(String correlationId, List<String> accomTypeCodes, JavaLocalDateInterval accomDateRange, JavaLocalDateInterval mktAccomDateRange) {
        Map<String, Integer> dataLoadMetadataIDMap = operaUtilityService.getDataLoadMetadataIDMap(correlationId);

        int accomActivityZerofillCount = zeroFillMissingRoomTypeSummaryForRoomTypeCodes(accomTypeCodes, dataLoadMetadataIDMap, accomDateRange);
        int mktAccomActivityZerofillCount = zeroFillMissingRoomTypeMktSummaryForRoomTypeCodes(accomTypeCodes, dataLoadMetadataIDMap, mktAccomDateRange);

        return "Stage_Occupancy_Summary zeroFilled for Accom: " + accomActivityZerofillCount + " | " + "Stage_Occupancy_Summary zeroFilled for MktAccom: " + mktAccomActivityZerofillCount;
    }


    public enum TransformSummaryMetricType {
        TRANSFORM_STAGE_DATA_SUMMARY
    }


    public enum TransformSummaryServiceMetricType {
        ZERO_FILL_RT_OCCUPANCY, ZERO_FILL_RT_MS_OCCUPANCY, UPDATE_ACCOM_CAPACITIES, ADJUST_DEPARTURE_COUNT
    }

}
