package com.ideas.tetris.pacman.services.reports.individualgroupwash.dto;

import com.ideas.tetris.pacman.services.groupwash.WashOverrideByGroup;
import com.ideas.tetris.platform.common.time.LocalDateUtils;

import java.math.BigDecimal;
import java.util.Date;

public class IndividualGroupWashOverrideHistoryDto {
    private IndividualGroupBlockTableDto individualGroupBlockTableDto;
    private IndividualGroupMasterDto individualGroupMasterDto;
    private WashOverrideByGroup washOverrideByGroup;
    private String notesAsText;
    private String pickupTypeAsText;
    private String overrideStatusAsText;
    private Date overrideDate;
    private BigDecimal userWashValue;
    private BigDecimal userWashPercent;

    public IndividualGroupBlockTableDto getIndividualGroupBlockTableDto() {
        return individualGroupBlockTableDto;
    }

    public void setIndividualGroupBlockTableDto(IndividualGroupBlockTableDto individualGroupBlockTableDto) {
        this.individualGroupBlockTableDto = individualGroupBlockTableDto;
    }

    public IndividualGroupMasterDto getIndividualGroupMasterDto() {
        return individualGroupMasterDto;
    }

    public void setIndividualGroupMasterDto(IndividualGroupMasterDto individualGroupMasterDto) {
        this.individualGroupMasterDto = individualGroupMasterDto;
    }

    public WashOverrideByGroup getWashOverrideByGroup() {
        return washOverrideByGroup;
    }

    public void setWashOverrideByGroup(WashOverrideByGroup washOverrideByGroup) {
        this.washOverrideByGroup = washOverrideByGroup;
        overrideDate = LocalDateUtils.toDate(washOverrideByGroup.getCreateDate());
    }

    public void setNotesAsText(String notesAsText) {
        this.notesAsText = notesAsText;
    }

    public String getNotesAsText() {
        return notesAsText;
    }

    public void setPickupTypeAsText(String pickupTypeAsText) {
        this.pickupTypeAsText = pickupTypeAsText;
    }

    public String getPickupTypeAsText() {
        return pickupTypeAsText;
    }

    public String getOverrideStatusAsText() {
        return overrideStatusAsText;
    }

    public void setOverrideStatusAsText(String overrideStatusAsText) {
        this.overrideStatusAsText = overrideStatusAsText;
    }

    public BigDecimal getUserWashValue() {
        return userWashValue;
    }

    public void setUserWashValue(BigDecimal userWashValue) {
        this.userWashValue = userWashValue;
    }

    public BigDecimal getUserWashPercent() {
        return userWashPercent;
    }

    public void setUserWashPercent(BigDecimal userWashPercent) {
        this.userWashPercent = userWashPercent;
    }

    public Date getOverrideDate() {
        return overrideDate;
    }

}
