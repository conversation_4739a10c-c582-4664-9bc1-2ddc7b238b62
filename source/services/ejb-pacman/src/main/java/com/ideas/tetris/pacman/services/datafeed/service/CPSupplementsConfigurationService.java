package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.AccomTypeSupplementService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomTypeSupplement;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomTypeSupplementSeason;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.CPSupplementsConfiguration;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.DateTimeComparator;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class CPSupplementsConfigurationService {

    @Autowired
	private AccomTypeSupplementService accomTypeSupplementService;

    @Autowired
	private PacmanConfigParamsService configParamsService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    public List getContinuousPricingSupplementsConfiguration(DatafeedRequest datafeedRequest) {
        List<CPSupplementsConfiguration> cpSupplementsConfigurations = new ArrayList<>();
        cpSupplementsConfigurations.addAll(populateCPSupplementValuesConfiguration(Constants.DEFAULT, null));
        cpSupplementsConfigurations.addAll(populateCPSeasonalSupplementValuesConfiguration(datafeedRequest.getStartDate()));
        return cpSupplementsConfigurations;
    }

    private List<CPSupplementsConfiguration> populateCPSeasonalSupplementValuesConfiguration(Date startDate) {
        List<AccomTypeSupplementSeason> accomTypeSupplementSeasons = getCPSeasonalSupplementConfiguration(startDate);
        List<CPSupplementsConfiguration> cpSeasonalSupplementsConfigurations = new ArrayList<>();
        for (AccomTypeSupplementSeason accomTypeSupplementSeason : accomTypeSupplementSeasons) {
            cpSeasonalSupplementsConfigurations.addAll(populateCPSupplementValuesConfiguration(Constants.SEASONAL, accomTypeSupplementSeason));
        }
        return cpSeasonalSupplementsConfigurations;
    }

    private List<AccomTypeSupplementSeason> getCPSeasonalSupplementConfiguration(Date startDate) {
        List<AccomTypeSupplementSeason> supplementSeasonList = new ArrayList<>();
        List<Product> independentSystemDefaultProducts = crudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_INDEPENDENT_PRODUCTS);
        independentSystemDefaultProducts.stream().forEach(independentSystemDefaultProduct -> {
            supplementSeasonList.addAll(accomTypeSupplementService.findCPConfigSupplementSeasons(independentSystemDefaultProduct).stream().filter(accomTypeSupplementSeason ->
                    DateTimeComparator.getDateOnlyInstance().compare(accomTypeSupplementSeason.getEndDate().toDate(), startDate) >= 0).collect(Collectors.toList()));
        });

        return supplementSeasonList;
    }

    private List<CPSupplementsConfiguration> populateCPSupplementValuesConfiguration(String category, AccomTypeSupplementSeason season) {
        Map<AccomClass, Map<AccomType, List<OccupancyType>>> accomTypeSupplements = accomTypeSupplementService.getEmptySupplementStructure(null);
        List<CPSupplementsConfiguration> cpDefaultSupplementsConfigurations = new ArrayList<>();
        boolean isPercentageForSupplementConfigEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_COlUMN_FOR_CPSUPPLEMENT_DATAFEED);
        for (AccomClass accomClass : accomTypeSupplements.keySet()) {
            Map<AccomType, List<OccupancyType>> accomTypeMap = accomTypeSupplements.get(accomClass);
            for (AccomType accomType : accomTypeMap.keySet()) {
                List<OccupancyType> occupancyTypes = accomTypeMap.get(accomType);
                for (OccupancyType occupancyType : occupancyTypes) {
                    List<AccomTypeSupplement> supplementList = getSupplementConfig(accomType, occupancyType, season);

                    for (AccomTypeSupplement accomTypeSupplement : supplementList) {
                        String occupancyTypeText = ResourceUtil.getText(occupancyType.getKey(), Language.ENGLISH);

                        CPSupplementsConfiguration cpSupplementsConfiguration = new CPSupplementsConfiguration(accomType.getAccomTypeCode(), occupancyTypeText.toUpperCase(), accomTypeSupplement.getName(), category,
                                getSupplementValueWithScale(accomTypeSupplement.getSundaySupplementValue()),
                                getSupplementValueWithScale(accomTypeSupplement.getMondaySupplementValue()),
                                getSupplementValueWithScale(accomTypeSupplement.getTuesdaySupplementValue()),
                                getSupplementValueWithScale(accomTypeSupplement.getWednesdaySupplementValue()),
                                getSupplementValueWithScale(accomTypeSupplement.getThursdaySupplementValue()),
                                getSupplementValueWithScale(accomTypeSupplement.getFridaySupplementValue()),
                                getSupplementValueWithScale(accomTypeSupplement.getSaturdaySupplementValue()),
                                accomTypeSupplement.getStartDate() == null ? null : accomTypeSupplement.getStartDate().toDate(),
                                accomTypeSupplement.getEndDate() == null ? null : accomTypeSupplement.getEndDate().toDate(),
                                getSupplementMethod(accomTypeSupplement), isPercentageForSupplementConfigEnabled);
                        if (accomTypeSupplement.getProductID() != null) {
                            cpSupplementsConfiguration.setProductName(fetchProduct(accomTypeSupplement.getProductID()));
                        }
                        cpDefaultSupplementsConfigurations.add(cpSupplementsConfiguration);
                    }
                }
            }
        }
        return cpDefaultSupplementsConfigurations;
    }

    private String getSupplementMethod(AccomTypeSupplement accomTypeSupplement) {
        return accomTypeSupplement.getOffsetMethod() == null ? null :
                Objects.equals(accomTypeSupplement.getOffsetMethod(), OffsetMethod.FIXED_OFFSET) ? "FIXED" : accomTypeSupplement.getOffsetMethod().name();
    }

    private String fetchProduct(Integer productID) {
        Product p = crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, (QueryParameter.with("productId", productID).parameters()));
        return p != null ? p.getName() : "";
    }

    private List<AccomTypeSupplement> getSupplementConfig(AccomType accomType, OccupancyType occupancyType, AccomTypeSupplementSeason season) {
        List<AccomTypeSupplement> supplements = new ArrayList<>();
        if (season != null) {
            for (AccomTypeSupplement seasonSupplement : season.getSupplements()) {
                if (seasonSupplement.getOccupancyType() == occupancyType && seasonSupplement.getAccomType().getId() == accomType.getId()) {
                    supplements.add(seasonSupplement);
                }
            }
        } else {
            supplements = accomTypeSupplementService.findSupplementByAccomTypeAndOccupancyType(accomType, occupancyType);
        }
        return supplements;
    }

    private BigDecimal getSupplementValueWithScale(BigDecimal supplementValue) {
        return supplementValue == null ? null : supplementValue.setScale(2);
    }
}
