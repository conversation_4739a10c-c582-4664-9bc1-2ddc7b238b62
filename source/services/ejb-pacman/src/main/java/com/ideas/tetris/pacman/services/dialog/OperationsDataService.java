package com.ideas.tetris.pacman.services.dialog;

import com.ideas.g3.dialog.dto.IntentType;
import com.ideas.tetris.pacman.services.property.dto.ActivitySummary;
import com.ideas.tetris.pacman.services.reports.operations.OperationsReportService;
import com.ideas.tetris.pacman.services.reports.operations.dto.OperationsReportDTO;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class OperationsDataService {
    @Autowired
    OperationsReportService operationsReportService;

    public BigDecimal getStatisticValue(IntentType intentType, Boolean isForecast, LocalDate startDate, LocalDate endDate) {
        ActivitySummary results = new ActivitySummary();
        List<OperationsReportDTO> operationsDtos = operationsReportService.generateReport(startDate, endDate);
        operationsDtos.forEach(dto -> results.addOperationsDto(dto));
        if (isForecast) {
            if (intentType.equals(IntentType.ARRIVALS)) {
                return new BigDecimal(results.getForecastArrivals());
            } else if (intentType.equals(IntentType.DEPARTURES)) {
                return new BigDecimal(results.getForecastDepartures());
            } else if (intentType.equals(IntentType.OCCUPANCY)) {
                return new BigDecimal(results.getForecastOccupancy());
            }
        } else { // on books
            if (intentType.equals(IntentType.ARRIVALS)) {
                return new BigDecimal(results.getOnBooksArrivals());
            } else if (intentType.equals(IntentType.DEPARTURES)) {
                return new BigDecimal(results.getOnBooksDepartures());
            } else if (intentType.equals(IntentType.OCCUPANCY)) {
                return new BigDecimal(results.getOnBooksOccupancy());
            }
        }
        return BigDecimal.ZERO; // TODO:  handle error
    }
}
