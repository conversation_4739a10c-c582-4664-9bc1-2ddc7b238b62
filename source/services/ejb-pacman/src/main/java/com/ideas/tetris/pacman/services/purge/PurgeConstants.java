package com.ideas.tetris.pacman.services.purge;

import static java.lang.String.format;

public class PurgeConstants {

    private PurgeConstants() {
        super();
    }

    public static final String OPERA = "opera";
    public static final String DATA_LOAD_METADATA_ID = "DATA_LOAD_METADATA_ID";
    public static final String INFO_MGR_INSTANCE_ID = "INFO_MGR_INSTANCE_ID";
    public static final String OCCUPANCY_DT = "OCCUPANCY_DT";
    public static final String END_DT = "END_DT";
    public static final String ARRIVAL_DT = "ARRIVAL_DT";
    public static final String OCCUPANCY_DATE = "OCCUPANCY_DATE";
    public static final String DECISION_ID = "Decision_Id";
    public static final String FORECAST_AS_OF_DATE = "Forecast_As_Of_Date";
    public static final String CAPTURE_DT = "CAPTURE_DT";

    public static final String DBO = "dbo";

    public static final String PLACEHOLDER_PROPERTY_ID_CONDITIONAL = "%propertyIdConditional";  // e.g. "(PROPERTY_ID = 123) AND"
    public static final String PLACEHOLDER_FIELD_TO_COMPARE = "%fieldToCompare";
    public static final String PLACEHOLDER_DAYS_AT_A_TIME = "%daysToDeleteAtATime";
    public static final String PLACEHOLDER_TABLE_NAME = "%tableName";
    public static final String PLACEHOLDER_CAUGHT_UP_DATE = "%caughtUpDate";
    public static final String PLACEHOLDER_FILE_METADATA_IDS = "%fileMetaDataIds";
    public static final String PLACEHOLDER_INSTANCE_IDS = "%instanceIds";
    public static final String PLACEHOLDER_DATE_ADD_FUNCTION = "%dateAddFunction";
    public static final int MAX_CHARS_FOR_WHERE_CLAUSE = 7880;
    public static final String EMPTY = "''";

    public static final String PLACEHOLDER_MIN_DATE = "%minDate";

    public static final String MAX_DAYS_AT_A_TIME = " AND %s < '%s'";

    public static final String KEEP_ONE_YEAR_IN_PAST = year(-1);
    public static final String KEEP_TWO_YEARS_IN_PAST = year(-2);
    public static final String KEEP_THREE_YEARS_IN_PAST = year(-3);
    public static final String KEEP_FIVE_YEARS_IN_PAST = year(-5);
    public static final String KEEP_SEVEN_DAYS_IN_PAST = day(-7);
    public static final String KEEP_TEN_DAYS_IN_PAST = day(-10);
    public static final String KEEP_FIFTEEN_DAYS_IN_PAST = day(-15);
    public static final String KEEP_SIXTY_DAYS_IN_PAST = day(-60);
    public static final String KEEP_THIRTY_DAYS_IN_PAST = day(-30);
    public static final String KEEP_TEN_DAYS_DECISION_ID_IN_PAST = decisionIdDay(-10);
    public static final String KEEP_SIX_MONTHS_IN_PAST = month(-6);

    private static String year(int i) {
        return format("WHERE %s%s < DATEADD(YY,%s,'%s')%s",
                PLACEHOLDER_PROPERTY_ID_CONDITIONAL, PLACEHOLDER_FIELD_TO_COMPARE, Integer.toString(i), PLACEHOLDER_CAUGHT_UP_DATE, PLACEHOLDER_MIN_DATE);
    }

    private static String month(int i) {
        return format("WHERE %s%s < DATEADD(mm,%s,'%s')%s",
                PLACEHOLDER_PROPERTY_ID_CONDITIONAL, PLACEHOLDER_FIELD_TO_COMPARE, Integer.toString(i), PLACEHOLDER_CAUGHT_UP_DATE, PLACEHOLDER_MIN_DATE);
    }

    public static String day(int i) {
        return format("WHERE %s%s < DATEADD(dd,%s,'%s')%s",
                PLACEHOLDER_PROPERTY_ID_CONDITIONAL, PLACEHOLDER_FIELD_TO_COMPARE, Integer.toString(i), PLACEHOLDER_CAUGHT_UP_DATE, PLACEHOLDER_MIN_DATE);
    }

    public static String decisionIdDay(int i) {
        return format("WHERE %s%s not in (select distinct Decision_Id from Decision where Decision_Type_ID in (1, 2) and Process_Status_ID = 13 and business_dt >= dateadd(dd, %s, '%s'))",
                PLACEHOLDER_PROPERTY_ID_CONDITIONAL, PLACEHOLDER_FIELD_TO_COMPARE, Integer.toString(i), PLACEHOLDER_CAUGHT_UP_DATE);
    }

    public static final String WHERE_FIELD_TO_COMPARE_METADATAID =
            format("WHERE %s in (%s)",
                    PLACEHOLDER_FIELD_TO_COMPARE, PLACEHOLDER_FILE_METADATA_IDS);

    public static final String WHERE_FIELD_TO_COMPARE_INSTANCEID =
            format("WHERE %s in (%s)",
                    PLACEHOLDER_FIELD_TO_COMPARE, PLACEHOLDER_INSTANCE_IDS);

    public static final String WHERE_FIELD_TO_COMPARE_COMMENTS_INSTANCEID =
            format("WHERE Info_Mgr_History_ID in (select Info_Mgr_History_ID from Info_Mgr_History where %s in (%s))",
                    PLACEHOLDER_FIELD_TO_COMPARE, PLACEHOLDER_INSTANCE_IDS);

    public static final String WHERE_TABLE_NAME_ID_METADATAID_AND_INCOMING_FILE_TYPE_CODE_YC =
            format("WHERE %s_ID in (%s) AND INCOMING_FILE_TYPE_CODE='YC'",
                    PLACEHOLDER_TABLE_NAME, PLACEHOLDER_FILE_METADATA_IDS);

    public static final String MIN_DATE_SELECT = String.format("SELECT DATEADD(DAY, %s, MIN(%s)) FROM %s",
            PLACEHOLDER_DAYS_AT_A_TIME, PLACEHOLDER_FIELD_TO_COMPARE, PLACEHOLDER_TABLE_NAME);
}
