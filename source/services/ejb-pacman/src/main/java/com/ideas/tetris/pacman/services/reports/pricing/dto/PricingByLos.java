package com.ideas.tetris.pacman.services.reports.pricing.dto;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Date;

public class PricingByLos {

    private Date arrivalDate;
    private String dow;
    private BigDecimal roomSold;
    private BigDecimal outOfOrder;
    private BigDecimal occupancyForecast;
    private BigDecimal occupancyForecastPercent;
    private BigDecimal propertyOccupancyForecast;
    private BigDecimal propertyOccupancyForecastPercent;
    private String accomClassName;
    private String notes;

    private BigDecimal compRate1;
    private String compName1;
    private BigDecimal compRate2;
    private String compName2;
    private BigDecimal compRate3;
    private String compName3;
    private BigDecimal compRate4;
    private String compName4;
    private BigDecimal compRate5;
    private String compName5;
    private BigDecimal compRate6;
    private String compName6;
    private BigDecimal compRate7;
    private String compName7;
    private BigDecimal compRate8;
    private String compName8;
    private BigDecimal compRate9;
    private String compName9;
    private BigDecimal compRate10;
    private String compName10;
    private BigDecimal compRate11;
    private String compName11;
    private BigDecimal compRate12;
    private String compName12;
    private BigDecimal compRate13;
    private String compName13;
    private BigDecimal compRate14;
    private String compName14;
    private BigDecimal compRate15;
    private String compName15;
    private BigDecimal lrv;
    private BigDecimal totalRooms;
    private ZonedDateTime createDateTime;
    private String userName;
    private String override1;
    private Integer LOS1;
    private String rateCodeName1;
    private BigDecimal LOSPrice1;
    private String override2;
    private Integer LOS2;
    private String rateCodeName2;
    private BigDecimal LOSPrice2;
    private String override3;
    private Integer LOS3;
    private String rateCodeName3;
    private BigDecimal LOSPrice3;
    private String override4;
    private Integer LOS4;
    private String rateCodeName4;
    private BigDecimal LOSPrice4;
    private String override5;
    private Integer LOS5;
    private String rateCodeName5;
    private BigDecimal LOSPrice5;
    private String override6;
    private Integer LOS6;
    private String rateCodeName6;
    private BigDecimal LOSPrice6;
    private String override7;
    private Integer LOS7;
    private String rateCodeName7;
    private BigDecimal LOSPrice7;
    private String override8;
    private Integer LOS8;
    private String rateCodeName8;
    private BigDecimal LOSPrice8;

    private String Floor_Ovr_Rate_Code_Name1;
    private String Floor_Ovr_Rate_Code_Name2;
    private String Floor_Ovr_Rate_Code_Name3;
    private String Floor_Ovr_Rate_Code_Name4;
    private String Floor_Ovr_Rate_Code_Name5;
    private String Floor_Ovr_Rate_Code_Name6;
    private String Floor_Ovr_Rate_Code_Name7;
    private String Floor_Ovr_Rate_Code_Name8;


    private Integer Decision_Reason_Type_ID1;
    private Integer Decision_Reason_Type_ID2;
    private Integer Decision_Reason_Type_ID3;
    private Integer Decision_Reason_Type_ID4;
    private Integer Decision_Reason_Type_ID5;
    private Integer Decision_Reason_Type_ID6;
    private Integer Decision_Reason_Type_ID7;
    private Integer Decision_Reason_Type_ID8;

    private String LV0_Closed_RoomTypes_LOS1;
    private String LV0_Closed_RoomTypes_LOS2;
    private String LV0_Closed_RoomTypes_LOS3;
    private String LV0_Closed_RoomTypes_LOS4;
    private String LV0_Closed_RoomTypes_LOS5;
    private String LV0_Closed_RoomTypes_LOS6;
    private String LV0_Closed_RoomTypes_LOS7;
    private String LV0_Closed_RoomTypes_LOS8;

    private String barDetailsLOS1;
    private String barDetailsLOS2;
    private String barDetailsLOS3;
    private String barDetailsLOS4;
    private String barDetailsLOS5;
    private String barDetailsLOS6;
    private String barDetailsLOS7;
    private String barDetailsLOS8;

    public Date getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public String getDow() {
        return dow;
    }

    public void setDow(String dow) {
        this.dow = dow;
    }

    public BigDecimal getRoomSold() {
        return roomSold;
    }

    public void setRoomSold(BigDecimal roomSold) {
        this.roomSold = roomSold;
    }

    public BigDecimal getOutOfOrder() {
        return outOfOrder;
    }

    public void setOutOfOrder(BigDecimal outOfOrder) {
        this.outOfOrder = outOfOrder;
    }

    public BigDecimal getOccupancyForecast() {
        return occupancyForecast;
    }

    public void setOccupancyForecast(BigDecimal occupancyForecast) {
        this.occupancyForecast = occupancyForecast;
    }

    public BigDecimal getOccupancyForecastPercent() {
        return occupancyForecastPercent;
    }

    public void setOccupancyForecastPercent(BigDecimal occupancyForecastPercent) {
        this.occupancyForecastPercent = occupancyForecastPercent;
    }

    public BigDecimal getPropertyOccupancyForecast() {
        return propertyOccupancyForecast;
    }

    public void setPropertyOccupancyForecast(
            BigDecimal propertyOccupancyForecast) {
        this.propertyOccupancyForecast = propertyOccupancyForecast;
    }

    public BigDecimal getPropertyOccupancyForecastPercent() {
        return propertyOccupancyForecastPercent;
    }

    public void setPropertyOccupancyForecastPercent(
            BigDecimal propertyOccupancyForecastPercent) {
        this.propertyOccupancyForecastPercent = propertyOccupancyForecastPercent;
    }

    public String getAccomClassName() {
        return accomClassName;
    }

    public void setAccomClassName(String accomClassName) {
        this.accomClassName = accomClassName;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCompRate1() {
        return compRate1;
    }

    public void setCompRate1(BigDecimal compRate1) {
        this.compRate1 = compRate1;
    }

    public String getCompName1() {
        return compName1;
    }

    public void setCompName1(String compName1) {
        this.compName1 = compName1;
    }

    public BigDecimal getCompRate2() {
        return compRate2;
    }

    public void setCompRate2(BigDecimal compRate2) {
        this.compRate2 = compRate2;
    }

    public String getCompName2() {
        return compName2;
    }

    public void setCompName2(String compName2) {
        this.compName2 = compName2;
    }

    public BigDecimal getCompRate3() {
        return compRate3;
    }

    public void setCompRate3(BigDecimal compRate3) {
        this.compRate3 = compRate3;
    }

    public String getCompName3() {
        return compName3;
    }

    public void setCompName3(String compName3) {
        this.compName3 = compName3;
    }

    public BigDecimal getCompRate4() {
        return compRate4;
    }

    public void setCompRate4(BigDecimal compRate4) {
        this.compRate4 = compRate4;
    }

    public String getCompName4() {
        return compName4;
    }

    public void setCompName4(String compName4) {
        this.compName4 = compName4;
    }

    public BigDecimal getCompRate5() {
        return compRate5;
    }

    public void setCompRate5(BigDecimal compRate5) {
        this.compRate5 = compRate5;
    }

    public String getCompName5() {
        return compName5;
    }

    public void setCompName5(String compName5) {
        this.compName5 = compName5;
    }

    public BigDecimal getCompRate6() {
        return compRate6;
    }

    public void setCompRate6(BigDecimal compRate6) {
        this.compRate6 = compRate6;
    }

    public String getCompName6() {
        return compName6;
    }

    public void setCompName6(String compName6) {
        this.compName6 = compName6;
    }

    public BigDecimal getCompRate7() {
        return compRate7;
    }

    public void setCompRate7(BigDecimal compRate7) {
        this.compRate7 = compRate7;
    }

    public String getCompName7() {
        return compName7;
    }

    public void setCompName7(String compName7) {
        this.compName7 = compName7;
    }

    public BigDecimal getCompRate8() {
        return compRate8;
    }

    public void setCompRate8(BigDecimal compRate8) {
        this.compRate8 = compRate8;
    }

    public String getCompName8() {
        return compName8;
    }

    public void setCompName8(String compName8) {
        this.compName8 = compName8;
    }

    public BigDecimal getCompRate9() {
        return compRate9;
    }

    public void setCompRate9(BigDecimal compRate9) {
        this.compRate9 = compRate9;
    }

    public String getCompName9() {
        return compName9;
    }

    public void setCompName9(String compName9) {
        this.compName9 = compName9;
    }

    public BigDecimal getCompRate10() {
        return compRate10;
    }

    public void setCompRate10(BigDecimal compRate10) {
        this.compRate10 = compRate10;
    }

    public String getCompName10() {
        return compName10;
    }

    public void setCompName10(String compName10) {
        this.compName10 = compName10;
    }

    public BigDecimal getCompRate11() {
        return compRate11;
    }

    public void setCompRate11(BigDecimal compRate11) {
        this.compRate11 = compRate11;
    }

    public String getCompName11() {
        return compName11;
    }

    public void setCompName11(String compName11) {
        this.compName11 = compName11;
    }

    public BigDecimal getCompRate12() {
        return compRate12;
    }

    public void setCompRate12(BigDecimal compRate12) {
        this.compRate12 = compRate12;
    }

    public String getCompName12() {
        return compName12;
    }

    public void setCompName12(String compName12) {
        this.compName12 = compName12;
    }

    public BigDecimal getCompRate13() {
        return compRate13;
    }

    public void setCompRate13(BigDecimal compRate13) {
        this.compRate13 = compRate13;
    }

    public String getCompName13() {
        return compName13;
    }

    public void setCompName13(String compName13) {
        this.compName13 = compName13;
    }

    public BigDecimal getCompRate14() {
        return compRate14;
    }

    public void setCompRate14(BigDecimal compRate14) {
        this.compRate14 = compRate14;
    }

    public String getCompName14() {
        return compName14;
    }

    public void setCompName14(String compName14) {
        this.compName14 = compName14;
    }

    public BigDecimal getCompRate15() {
        return compRate15;
    }

    public void setCompRate15(BigDecimal compRate15) {
        this.compRate15 = compRate15;
    }

    public String getCompName15() {
        return compName15;
    }

    public void setCompName15(String compName15) {
        this.compName15 = compName15;
    }

    public BigDecimal getLRV() {
        return lrv;
    }

    public void setLRV(BigDecimal lrv) {
        this.lrv = lrv;
    }

    public BigDecimal getTotalRooms() {
        return totalRooms;
    }

    public void setTotalRooms(BigDecimal totalRooms) {
        this.totalRooms = totalRooms;
    }

    public ZonedDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(ZonedDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getOverride1() {
        return override1;
    }

    public void setOverride1(String override1) {
        this.override1 = override1;
    }

    public Integer getLOS1() {
        return LOS1;
    }

    public void setLOS1(Integer lOS1) {
        LOS1 = lOS1;
    }

    public String getRateCodeName1() {
        return rateCodeName1;
    }

    public void setRateCodeName1(String rateCodeName1) {
        this.rateCodeName1 = rateCodeName1;
    }

    public BigDecimal getLOSPrice1() {
        return LOSPrice1;
    }

    public void setLOSPrice1(BigDecimal lOSPrice1) {
        LOSPrice1 = lOSPrice1;
    }

    public String getOverride2() {
        return override2;
    }

    public void setOverride2(String override2) {
        this.override2 = override2;
    }

    public Integer getLOS2() {
        return LOS2;
    }

    public void setLOS2(Integer lOS2) {
        LOS2 = lOS2;
    }

    public String getRateCodeName2() {
        return rateCodeName2;
    }

    public void setRateCodeName2(String rateCodeName2) {
        this.rateCodeName2 = rateCodeName2;
    }

    public BigDecimal getLOSPrice2() {
        return LOSPrice2;
    }

    public void setLOSPrice2(BigDecimal lOSPrice2) {
        LOSPrice2 = lOSPrice2;
    }

    public String getOverride3() {
        return override3;
    }

    public void setOverride3(String override3) {
        this.override3 = override3;
    }

    public Integer getLOS3() {
        return LOS3;
    }

    public void setLOS3(Integer lOS3) {
        LOS3 = lOS3;
    }

    public String getRateCodeName3() {
        return rateCodeName3;
    }

    public void setRateCodeName3(String rateCodeName3) {
        this.rateCodeName3 = rateCodeName3;
    }

    public BigDecimal getLOSPrice3() {
        return LOSPrice3;
    }

    public void setLOSPrice3(BigDecimal lOSPrice3) {
        LOSPrice3 = lOSPrice3;
    }

    public String getOverride4() {
        return override4;
    }

    public void setOverride4(String override4) {
        this.override4 = override4;
    }

    public Integer getLOS4() {
        return LOS4;
    }

    public void setLOS4(Integer lOS4) {
        LOS4 = lOS4;
    }

    public String getRateCodeName4() {
        return rateCodeName4;
    }

    public void setRateCodeName4(String rateCodeName4) {
        this.rateCodeName4 = rateCodeName4;
    }

    public BigDecimal getLOSPrice4() {
        return LOSPrice4;
    }

    public void setLOSPrice4(BigDecimal lOSPrice4) {
        LOSPrice4 = lOSPrice4;
    }

    public String getOverride5() {
        return override5;
    }

    public void setOverride5(String override5) {
        this.override5 = override5;
    }

    public Integer getLOS5() {
        return LOS5;
    }

    public void setLOS5(Integer lOS5) {
        LOS5 = lOS5;
    }

    public String getRateCodeName5() {
        return rateCodeName5;
    }

    public void setRateCodeName5(String rateCodeName5) {
        this.rateCodeName5 = rateCodeName5;
    }

    public BigDecimal getLOSPrice5() {
        return LOSPrice5;
    }

    public void setLOSPrice5(BigDecimal lOSPrice5) {
        LOSPrice5 = lOSPrice5;
    }

    public String getOverride6() {
        return override6;
    }

    public void setOverride6(String override6) {
        this.override6 = override6;
    }

    public Integer getLOS6() {
        return LOS6;
    }

    public void setLOS6(Integer lOS6) {
        LOS6 = lOS6;
    }

    public String getRateCodeName6() {
        return rateCodeName6;
    }

    public void setRateCodeName6(String rateCodeName6) {
        this.rateCodeName6 = rateCodeName6;
    }

    public BigDecimal getLOSPrice6() {
        return LOSPrice6;
    }

    public void setLOSPrice6(BigDecimal lOSPrice6) {
        LOSPrice6 = lOSPrice6;
    }

    public String getOverride7() {
        return override7;
    }

    public void setOverride7(String override7) {
        this.override7 = override7;
    }

    public Integer getLOS7() {
        return LOS7;
    }

    public void setLOS7(Integer lOS7) {
        LOS7 = lOS7;
    }

    public String getRateCodeName7() {
        return rateCodeName7;
    }

    public void setRateCodeName7(String rateCodeName7) {
        this.rateCodeName7 = rateCodeName7;
    }

    public BigDecimal getLOSPrice7() {
        return LOSPrice7;
    }

    public void setLOSPrice7(BigDecimal lOSPrice7) {
        LOSPrice7 = lOSPrice7;
    }

    public String getOverride8() {
        return override8;
    }

    public void setOverride8(String override8) {
        this.override8 = override8;
    }

    public Integer getLOS8() {
        return LOS8;
    }

    public void setLOS8(Integer lOS8) {
        LOS8 = lOS8;
    }

    public String getRateCodeName8() {
        return rateCodeName8;
    }

    public void setRateCodeName8(String rateCodeName8) {
        this.rateCodeName8 = rateCodeName8;
    }

    public BigDecimal getLOSPrice8() {
        return LOSPrice8;
    }

    public void setLOSPrice8(BigDecimal lOSPrice8) {
        LOSPrice8 = lOSPrice8;
    }

    public String getFloor_Ovr_Rate_Code_Name1() {
        return Floor_Ovr_Rate_Code_Name1;
    }

    public void setFloor_Ovr_Rate_Code_Name1(String floor_Ovr_Rate_Code_Name1) {
        Floor_Ovr_Rate_Code_Name1 = floor_Ovr_Rate_Code_Name1;
    }

    public String getFloor_Ovr_Rate_Code_Name2() {
        return Floor_Ovr_Rate_Code_Name2;
    }

    public void setFloor_Ovr_Rate_Code_Name2(String floor_Ovr_Rate_Code_Name2) {
        Floor_Ovr_Rate_Code_Name2 = floor_Ovr_Rate_Code_Name2;
    }

    public String getFloor_Ovr_Rate_Code_Name3() {
        return Floor_Ovr_Rate_Code_Name3;
    }

    public void setFloor_Ovr_Rate_Code_Name3(String floor_Ovr_Rate_Code_Name3) {
        Floor_Ovr_Rate_Code_Name3 = floor_Ovr_Rate_Code_Name3;
    }

    public String getFloor_Ovr_Rate_Code_Name4() {
        return Floor_Ovr_Rate_Code_Name4;
    }

    public void setFloor_Ovr_Rate_Code_Name4(String floor_Ovr_Rate_Code_Name4) {
        Floor_Ovr_Rate_Code_Name4 = floor_Ovr_Rate_Code_Name4;
    }

    public String getFloor_Ovr_Rate_Code_Name5() {
        return Floor_Ovr_Rate_Code_Name5;
    }

    public void setFloor_Ovr_Rate_Code_Name5(String floor_Ovr_Rate_Code_Name5) {
        Floor_Ovr_Rate_Code_Name5 = floor_Ovr_Rate_Code_Name5;
    }

    public String getFloor_Ovr_Rate_Code_Name6() {
        return Floor_Ovr_Rate_Code_Name6;
    }

    public void setFloor_Ovr_Rate_Code_Name6(String floor_Ovr_Rate_Code_Name6) {
        Floor_Ovr_Rate_Code_Name6 = floor_Ovr_Rate_Code_Name6;
    }

    public String getFloor_Ovr_Rate_Code_Name7() {
        return Floor_Ovr_Rate_Code_Name7;
    }

    public void setFloor_Ovr_Rate_Code_Name7(String floor_Ovr_Rate_Code_Name7) {
        Floor_Ovr_Rate_Code_Name7 = floor_Ovr_Rate_Code_Name7;
    }

    public String getFloor_Ovr_Rate_Code_Name8() {
        return Floor_Ovr_Rate_Code_Name8;
    }

    public void setFloor_Ovr_Rate_Code_Name8(String floor_Ovr_Rate_Code_Name8) {
        Floor_Ovr_Rate_Code_Name8 = floor_Ovr_Rate_Code_Name8;
    }

    public Integer getDecision_Reason_Type_ID1() {
        return Decision_Reason_Type_ID1;
    }

    public void setDecision_Reason_Type_ID1(Integer decision_Reason_Type_ID1) {
        Decision_Reason_Type_ID1 = decision_Reason_Type_ID1;
    }

    public Integer getDecision_Reason_Type_ID2() {
        return Decision_Reason_Type_ID2;
    }

    public void setDecision_Reason_Type_ID2(Integer decision_Reason_Type_ID2) {
        Decision_Reason_Type_ID2 = decision_Reason_Type_ID2;
    }

    public Integer getDecision_Reason_Type_ID3() {
        return Decision_Reason_Type_ID3;
    }

    public void setDecision_Reason_Type_ID3(Integer decision_Reason_Type_ID3) {
        Decision_Reason_Type_ID3 = decision_Reason_Type_ID3;
    }

    public Integer getDecision_Reason_Type_ID4() {
        return Decision_Reason_Type_ID4;
    }

    public void setDecision_Reason_Type_ID4(Integer decision_Reason_Type_ID4) {
        Decision_Reason_Type_ID4 = decision_Reason_Type_ID4;
    }

    public Integer getDecision_Reason_Type_ID5() {
        return Decision_Reason_Type_ID5;
    }

    public void setDecision_Reason_Type_ID5(Integer decision_Reason_Type_ID5) {
        Decision_Reason_Type_ID5 = decision_Reason_Type_ID5;
    }

    public Integer getDecision_Reason_Type_ID6() {
        return Decision_Reason_Type_ID6;
    }

    public void setDecision_Reason_Type_ID6(Integer decision_Reason_Type_ID6) {
        Decision_Reason_Type_ID6 = decision_Reason_Type_ID6;
    }

    public Integer getDecision_Reason_Type_ID7() {
        return Decision_Reason_Type_ID7;
    }

    public void setDecision_Reason_Type_ID7(Integer decision_Reason_Type_ID7) {
        Decision_Reason_Type_ID7 = decision_Reason_Type_ID7;
    }

    public Integer getDecision_Reason_Type_ID8() {
        return Decision_Reason_Type_ID8;
    }

    public void setDecision_Reason_Type_ID8(Integer decision_Reason_Type_ID8) {
        Decision_Reason_Type_ID8 = decision_Reason_Type_ID8;
    }

    public String getLV0_Closed_RoomTypes_LOS1() {
        return LV0_Closed_RoomTypes_LOS1;
    }

    public void setLV0_Closed_RoomTypes_LOS1(String LV0_Closed_RoomTypes_LOS1) {
        this.LV0_Closed_RoomTypes_LOS1 = LV0_Closed_RoomTypes_LOS1;
    }

    public String getLV0_Closed_RoomTypes_LOS2() {
        return LV0_Closed_RoomTypes_LOS2;
    }

    public void setLV0_Closed_RoomTypes_LOS2(String LV0_Closed_RoomTypes_LOS2) {
        this.LV0_Closed_RoomTypes_LOS2 = LV0_Closed_RoomTypes_LOS2;
    }

    public String getLV0_Closed_RoomTypes_LOS3() {
        return LV0_Closed_RoomTypes_LOS3;
    }

    public void setLV0_Closed_RoomTypes_LOS3(String LV0_Closed_RoomTypes_LOS3) {
        this.LV0_Closed_RoomTypes_LOS3 = LV0_Closed_RoomTypes_LOS3;
    }

    public String getLV0_Closed_RoomTypes_LOS4() {
        return LV0_Closed_RoomTypes_LOS4;
    }

    public void setLV0_Closed_RoomTypes_LOS4(String LV0_Closed_RoomTypes_LOS4) {
        this.LV0_Closed_RoomTypes_LOS4 = LV0_Closed_RoomTypes_LOS4;
    }

    public String getLV0_Closed_RoomTypes_LOS5() {
        return LV0_Closed_RoomTypes_LOS5;
    }

    public void setLV0_Closed_RoomTypes_LOS5(String LV0_Closed_RoomTypes_LOS5) {
        this.LV0_Closed_RoomTypes_LOS5 = LV0_Closed_RoomTypes_LOS5;
    }

    public String getLV0_Closed_RoomTypes_LOS6() {
        return LV0_Closed_RoomTypes_LOS6;
    }

    public void setLV0_Closed_RoomTypes_LOS6(String LV0_Closed_RoomTypes_LOS6) {
        this.LV0_Closed_RoomTypes_LOS6 = LV0_Closed_RoomTypes_LOS6;
    }

    public String getLV0_Closed_RoomTypes_LOS7() {
        return LV0_Closed_RoomTypes_LOS7;
    }

    public void setLV0_Closed_RoomTypes_LOS7(String LV0_Closed_RoomTypes_LOS7) {
        this.LV0_Closed_RoomTypes_LOS7 = LV0_Closed_RoomTypes_LOS7;
    }

    public String getLV0_Closed_RoomTypes_LOS8() {
        return LV0_Closed_RoomTypes_LOS8;
    }

    public void setLV0_Closed_RoomTypes_LOS8(String LV0_Closed_RoomTypes_LOS8) {
        this.LV0_Closed_RoomTypes_LOS8 = LV0_Closed_RoomTypes_LOS8;
    }

    public String getBarDetailsLOS1() {
        return barDetailsLOS1;
    }

    public void setBarDetailsLOS1(String barDetailsLOS1) {
        this.barDetailsLOS1 = barDetailsLOS1;
    }

    public String getBarDetailsLOS2() {
        return barDetailsLOS2;
    }

    public void setBarDetailsLOS2(String barDetailsLOS2) {
        this.barDetailsLOS2 = barDetailsLOS2;
    }

    public String getBarDetailsLOS3() {
        return barDetailsLOS3;
    }

    public void setBarDetailsLOS3(String barDetailsLOS3) {
        this.barDetailsLOS3 = barDetailsLOS3;
    }

    public String getBarDetailsLOS4() {
        return barDetailsLOS4;
    }

    public void setBarDetailsLOS4(String barDetailsLOS4) {
        this.barDetailsLOS4 = barDetailsLOS4;
    }

    public String getBarDetailsLOS5() {
        return barDetailsLOS5;
    }

    public void setBarDetailsLOS5(String barDetailsLOS5) {
        this.barDetailsLOS5 = barDetailsLOS5;
    }

    public String getBarDetailsLOS6() {
        return barDetailsLOS6;
    }

    public void setBarDetailsLOS6(String barDetailsLOS6) {
        this.barDetailsLOS6 = barDetailsLOS6;
    }

    public String getBarDetailsLOS7() {
        return barDetailsLOS7;
    }

    public void setBarDetailsLOS7(String barDetailsLOS7) {
        this.barDetailsLOS7 = barDetailsLOS7;
    }

    public String getBarDetailsLOS8() {
        return barDetailsLOS8;
    }

    public void setBarDetailsLOS8(String barDetailsLOS8) {
        this.barDetailsLOS8 = barDetailsLOS8;
    }

    @Override
    public String toString() {
        return "PricingByLos{" +
                "arrivalDate=" + arrivalDate +
                ", dow='" + dow + '\'' +
                ", roomSold=" + roomSold +
                ", outOfOrder=" + outOfOrder +
                ", occupancyForecast=" + occupancyForecast +
                ", occupancyForecastPercent=" + occupancyForecastPercent +
                ", propertyOccupancyForecast=" + propertyOccupancyForecast +
                ", propertyOccupancyForecastPercent=" + propertyOccupancyForecastPercent +
                ", accomClassName='" + accomClassName + '\'' +
                ", notes='" + notes + '\'' +
                ", compRate1=" + compRate1 +
                ", compName1='" + compName1 + '\'' +
                ", compRate2=" + compRate2 +
                ", compName2='" + compName2 + '\'' +
                ", compRate3=" + compRate3 +
                ", compName3='" + compName3 + '\'' +
                ", compRate4=" + compRate4 +
                ", compName4='" + compName4 + '\'' +
                ", compRate5=" + compRate5 +
                ", compName5='" + compName5 + '\'' +
                ", compRate6=" + compRate6 +
                ", compName6='" + compName6 + '\'' +
                ", compRate7=" + compRate7 +
                ", compName7='" + compName7 + '\'' +
                ", compRate8=" + compRate8 +
                ", compName8='" + compName8 + '\'' +
                ", compRate9=" + compRate9 +
                ", compName9='" + compName9 + '\'' +
                ", compRate10=" + compRate10 +
                ", compName10='" + compName10 + '\'' +
                ", compRate11=" + compRate11 +
                ", compName11='" + compName11 + '\'' +
                ", compRate12=" + compRate12 +
                ", compName12='" + compName12 + '\'' +
                ", compRate13=" + compRate13 +
                ", compName13='" + compName13 + '\'' +
                ", compRate14=" + compRate14 +
                ", compName14='" + compName14 + '\'' +
                ", compRate15=" + compRate15 +
                ", compName15='" + compName15 + '\'' +
                ", lrv=" + lrv +
                ", totalRooms=" + totalRooms +
                ", createDateTime=" + createDateTime +
                ", userName='" + userName + '\'' +
                ", override1='" + override1 + '\'' +
                ", LOS1=" + LOS1 +
                ", rateCodeName1='" + rateCodeName1 + '\'' +
                ", LOSPrice1=" + LOSPrice1 +
                ", override2='" + override2 + '\'' +
                ", LOS2=" + LOS2 +
                ", rateCodeName2='" + rateCodeName2 + '\'' +
                ", LOSPrice2=" + LOSPrice2 +
                ", override3='" + override3 + '\'' +
                ", LOS3=" + LOS3 +
                ", rateCodeName3='" + rateCodeName3 + '\'' +
                ", LOSPrice3=" + LOSPrice3 +
                ", override4='" + override4 + '\'' +
                ", LOS4=" + LOS4 +
                ", rateCodeName4='" + rateCodeName4 + '\'' +
                ", LOSPrice4=" + LOSPrice4 +
                ", override5='" + override5 + '\'' +
                ", LOS5=" + LOS5 +
                ", rateCodeName5='" + rateCodeName5 + '\'' +
                ", LOSPrice5=" + LOSPrice5 +
                ", override6='" + override6 + '\'' +
                ", LOS6=" + LOS6 +
                ", rateCodeName6='" + rateCodeName6 + '\'' +
                ", LOSPrice6=" + LOSPrice6 +
                ", override7='" + override7 + '\'' +
                ", LOS7=" + LOS7 +
                ", rateCodeName7='" + rateCodeName7 + '\'' +
                ", LOSPrice7=" + LOSPrice7 +
                ", override8='" + override8 + '\'' +
                ", LOS8=" + LOS8 +
                ", rateCodeName8='" + rateCodeName8 + '\'' +
                ", LOSPrice8=" + LOSPrice8 +
                ", Floor_Ovr_Rate_Code_Name1='" + Floor_Ovr_Rate_Code_Name1 + '\'' +
                ", Floor_Ovr_Rate_Code_Name2='" + Floor_Ovr_Rate_Code_Name2 + '\'' +
                ", Floor_Ovr_Rate_Code_Name3='" + Floor_Ovr_Rate_Code_Name3 + '\'' +
                ", Floor_Ovr_Rate_Code_Name4='" + Floor_Ovr_Rate_Code_Name4 + '\'' +
                ", Floor_Ovr_Rate_Code_Name5='" + Floor_Ovr_Rate_Code_Name5 + '\'' +
                ", Floor_Ovr_Rate_Code_Name6='" + Floor_Ovr_Rate_Code_Name6 + '\'' +
                ", Floor_Ovr_Rate_Code_Name7='" + Floor_Ovr_Rate_Code_Name7 + '\'' +
                ", Floor_Ovr_Rate_Code_Name8='" + Floor_Ovr_Rate_Code_Name8 + '\'' +
                ", Decision_Reason_Type_ID1=" + Decision_Reason_Type_ID1 +
                ", Decision_Reason_Type_ID2=" + Decision_Reason_Type_ID2 +
                ", Decision_Reason_Type_ID3=" + Decision_Reason_Type_ID3 +
                ", Decision_Reason_Type_ID4=" + Decision_Reason_Type_ID4 +
                ", Decision_Reason_Type_ID5=" + Decision_Reason_Type_ID5 +
                ", Decision_Reason_Type_ID6=" + Decision_Reason_Type_ID6 +
                ", Decision_Reason_Type_ID7=" + Decision_Reason_Type_ID7 +
                ", Decision_Reason_Type_ID8=" + Decision_Reason_Type_ID8 +
                '}';
    }
}
