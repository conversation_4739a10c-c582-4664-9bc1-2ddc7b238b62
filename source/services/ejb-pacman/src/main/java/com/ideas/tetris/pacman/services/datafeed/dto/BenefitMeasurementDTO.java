package com.ideas.tetris.pacman.services.datafeed.dto;

import java.math.BigDecimal;

import static java.math.BigDecimal.ZERO;

public class BenefitMeasurementDTO {
    private String monthDate;

    private BigDecimal heuristicOccupancy = ZERO;
    private BigDecimal heuristicRevenue = ZERO;
    private BigDecimal heuristicAdr = ZERO;
    private BigDecimal heuristicRevpar = ZERO;

    private BigDecimal actualOccupancy = ZERO;
    private BigDecimal actualRevenue = ZERO;
    private BigDecimal actualAdr = ZERO;
    private BigDecimal actualRevpar = ZERO;

    private BigDecimal benefitsRevenue = ZERO;
    private BigDecimal benefitsOccupancy = ZERO;
    private BigDecimal benefitsADR = ZERO;
    private BigDecimal benefitsRevpar = ZERO;

    private BigDecimal estimatedRevenue = ZERO;
    private BigDecimal estimatedOccupancy = ZERO;
    private BigDecimal estimatedAdr = ZERO;
    private BigDecimal estimatedRevPar = ZERO;

    private BigDecimal ancillaryRevenueActual = ZERO;
    private BigDecimal ancillaryRevenueWithoutRms = ZERO;
    private BigDecimal ancillaryRevenueEstimatedBenefits = ZERO;
    private BigDecimal ancillaryRevenueGain = ZERO;
    private BigDecimal ancillaryProfitActual = ZERO;
    private BigDecimal ancillaryProfitWithoutRms = ZERO;
    private BigDecimal ancillaryProfitEstimatedBenefits = ZERO;
    private BigDecimal ancillaryProfitGain = ZERO;

    private BigDecimal profitEstimatedBenefit = ZERO;
    private BigDecimal profitSimulated = ZERO;
    private BigDecimal profitActual = ZERO;
    private BigDecimal profitGain = ZERO;

    private BigDecimal proPOREstimatedBenefit = ZERO;
    private BigDecimal proPORSimulated = ZERO;
    private BigDecimal proPORActual = ZERO;
    private BigDecimal proPORGain = ZERO;

    private BigDecimal proPAREstimatedBenefit = ZERO;
    private BigDecimal proPARSimulated = ZERO;
    private BigDecimal proPARActual = ZERO;
    private BigDecimal proPARGain = ZERO;

    public BigDecimal getAncillaryRevenueActual() {
        return ancillaryRevenueActual;
    }

    public void setAncillaryRevenueActual(BigDecimal ancillaryRevenueActual) {
        this.ancillaryRevenueActual = ancillaryRevenueActual;
    }

    public BigDecimal getAncillaryRevenueWithoutRms() {
        return ancillaryRevenueWithoutRms;
    }

    public void setAncillaryRevenueWithoutRms(BigDecimal ancillaryRevenueWithoutRms) {
        this.ancillaryRevenueWithoutRms = ancillaryRevenueWithoutRms;
    }

    public BigDecimal getAncillaryRevenueEstimatedBenefits() {
        return ancillaryRevenueEstimatedBenefits;
    }

    public void setAncillaryRevenueEstimatedBenefits(BigDecimal ancillaryRevenueEstimatedBenefits) {
        this.ancillaryRevenueEstimatedBenefits = ancillaryRevenueEstimatedBenefits;
    }

    public BigDecimal getAncillaryRevenueGain() {
        return ancillaryRevenueGain;
    }

    public void setAncillaryRevenueGain(BigDecimal ancillaryRevenueGain) {
        this.ancillaryRevenueGain = ancillaryRevenueGain;
    }

    public BigDecimal getAncillaryProfitActual() {
        return ancillaryProfitActual;
    }

    public void setAncillaryProfitActual(BigDecimal ancillaryProfitActual) {
        this.ancillaryProfitActual = ancillaryProfitActual;
    }

    public BigDecimal getAncillaryProfitWithoutRms() {
        return ancillaryProfitWithoutRms;
    }

    public void setAncillaryProfitWithoutRms(BigDecimal ancillaryProfitWithoutRms) {
        this.ancillaryProfitWithoutRms = ancillaryProfitWithoutRms;
    }

    public BigDecimal getAncillaryProfitEstimatedBenefits() {
        return ancillaryProfitEstimatedBenefits;
    }

    public void setAncillaryProfitEstimatedBenefits(BigDecimal ancillaryProfitEstimatedBenefits) {
        this.ancillaryProfitEstimatedBenefits = ancillaryProfitEstimatedBenefits;
    }

    public BigDecimal getAncillaryProfitGain() {
        return ancillaryProfitGain;
    }

    public void setAncillaryProfitGain(BigDecimal ancillaryProfitGain) {
        this.ancillaryProfitGain = ancillaryProfitGain;
    }

    public BigDecimal getBenefitsADR() {
        return benefitsADR;
    }

    public void setBenefitsADR(BigDecimal benefitsADR) {
        this.benefitsADR = benefitsADR;
    }

    public BigDecimal getBenefitsRevpar() {
        return benefitsRevpar;
    }

    public void setBenefitsRevpar(BigDecimal benefitsRevpar) {
        this.benefitsRevpar = benefitsRevpar;
    }

    public BigDecimal getEstimatedOccupancy() {
        return estimatedOccupancy;
    }

    public void setEstimatedOccupancy(BigDecimal estimatedOccupancy) {
        this.estimatedOccupancy = estimatedOccupancy;
    }

    public BigDecimal getEstimatedAdr() {
        return estimatedAdr;
    }

    public void setEstimatedAdr(BigDecimal estimatedAdr) {
        this.estimatedAdr = estimatedAdr;
    }

    public BigDecimal getEstimatedRevPar() {
        return estimatedRevPar;
    }

    public void setEstimatedRevPar(BigDecimal estimatedRevPar) {
        this.estimatedRevPar = estimatedRevPar;
    }

    public BigDecimal getBenefitsRevenue() {
        return benefitsRevenue;
    }

    public void setBenefitsRevenue(BigDecimal benefitsRevenue) {
        this.benefitsRevenue = benefitsRevenue;
    }

    public BigDecimal getBenefitsOccupancy() {
        return benefitsOccupancy;
    }

    public void setBenefitsOccupancy(BigDecimal benefitsOccupancy) {
        this.benefitsOccupancy = benefitsOccupancy;
    }

    public BigDecimal getHeuristicOccupancy() {
        return heuristicOccupancy;
    }

    public void setHeuristicOccupancy(BigDecimal heuristicOccupancy) {
        this.heuristicOccupancy = heuristicOccupancy;
    }

    public BigDecimal getHeuristicRevenue() {
        return heuristicRevenue;
    }

    public void setHeuristicRevenue(BigDecimal heuristicRevenue) {
        this.heuristicRevenue = heuristicRevenue;
    }

    public BigDecimal getHeuristicAdr() {
        return heuristicAdr;
    }

    public void setHeuristicAdr(BigDecimal heuristicAdr) {
        this.heuristicAdr = heuristicAdr;
    }

    public BigDecimal getHeuristicRevpar() {
        return heuristicRevpar;
    }

    public void setHeuristicRevpar(BigDecimal heuristicRevpar) {
        this.heuristicRevpar = heuristicRevpar;
    }

    public BigDecimal getActualOccupancy() {
        return actualOccupancy;
    }

    public void setActualOccupancy(BigDecimal actualOccupancy) {
        this.actualOccupancy = actualOccupancy;
    }

    public BigDecimal getActualRevenue() {
        return actualRevenue;
    }

    public void setActualRevenue(BigDecimal actualRevenue) {
        this.actualRevenue = actualRevenue;
    }

    public BigDecimal getActualAdr() {
        return actualAdr;
    }

    public void setActualAdr(BigDecimal actualAdr) {
        this.actualAdr = actualAdr;
    }

    public BigDecimal getActualRevpar() {
        return actualRevpar;
    }

    public void setActualRevpar(BigDecimal actualRevpar) {
        this.actualRevpar = actualRevpar;
    }

    public BigDecimal getEstimatedRevenue() {
        return estimatedRevenue;
    }

    public void setEstimatedRevenue(BigDecimal estimatedRevenue) {
        this.estimatedRevenue = estimatedRevenue;
    }

    public String getMonthDate() {
        return monthDate;
    }

    public void setMonthDate(String monthDate) {
        this.monthDate = monthDate;
    }

    public BigDecimal getProfitEstimatedBenefit() {
        return profitEstimatedBenefit;
    }

    public void setProfitEstimatedBenefit(BigDecimal profitEstimatedBenefit) {
        this.profitEstimatedBenefit = profitEstimatedBenefit;
    }

    public BigDecimal getProfitSimulated() {
        return profitSimulated;
    }

    public void setProfitSimulated(BigDecimal profitSimulated) {
        this.profitSimulated = profitSimulated;
    }

    public BigDecimal getProfitActual() {
        return profitActual;
    }

    public void setProfitActual(BigDecimal profitActual) {
        this.profitActual = profitActual;
    }

    public BigDecimal getProfitGain() {
        return profitGain;
    }

    public void setProfitGain(BigDecimal profitGain) {
        this.profitGain = profitGain;
    }

    public BigDecimal getProPOREstimatedBenefit() {
        return proPOREstimatedBenefit;
    }

    public void setProPOREstimatedBenefit(BigDecimal proPOREstimatedBenefit) {
        this.proPOREstimatedBenefit = proPOREstimatedBenefit;
    }

    public BigDecimal getProPORSimulated() {
        return proPORSimulated;
    }

    public void setProPORSimulated(BigDecimal proPORSimulated) {
        this.proPORSimulated = proPORSimulated;
    }

    public BigDecimal getProPORActual() {
        return proPORActual;
    }

    public void setProPORActual(BigDecimal proPORActual) {
        this.proPORActual = proPORActual;
    }

    public BigDecimal getProPORGain() {
        return proPORGain;
    }

    public void setProPORGain(BigDecimal proPORGain) {
        this.proPORGain = proPORGain;
    }

    public BigDecimal getProPAREstimatedBenefit() {
        return proPAREstimatedBenefit;
    }

    public void setProPAREstimatedBenefit(BigDecimal proPAREstimatedBenefit) {
        this.proPAREstimatedBenefit = proPAREstimatedBenefit;
    }

    public BigDecimal getProPARSimulated() {
        return proPARSimulated;
    }

    public void setProPARSimulated(BigDecimal proPARSimulated) {
        this.proPARSimulated = proPARSimulated;
    }

    public BigDecimal getProPARActual() {
        return proPARActual;
    }

    public void setProPARActual(BigDecimal proPARActual) {
        this.proPARActual = proPARActual;
    }

    public BigDecimal getProPARGain() {
        return proPARGain;
    }

    public void setProPARGain(BigDecimal proPARGain) {
        this.proPARGain = proPARGain;
    }
}
