package com.ideas.tetris.pacman.services.activity.converter;

import com.ideas.tetris.pacman.services.activity.converter.RoomTypeHotelMarketSegmentActivityConverter.Qualifier;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ActivityEntity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.HotelMktSegAccomActivity;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.log4j.Logger;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Qualifier
@Component
@Transactional
public class RoomTypeHotelMarketSegmentActivityConverter extends ActivityConverter<HotelMktSegAccomActivity> {

    private static final Logger LOGGER = Logger.getLogger(RoomTypeHotelMarketSegmentActivityConverter.class);
    public static final String PROPERTY_ID = "propertyId";

    @Override
    public Map<String, Object> convertFromEntity(ActivityEntity hotelMktSegAccomActivity) {
        Map<String, Object> dto = super.convertFromEntity(hotelMktSegAccomActivity);
        if (dto == null) {
            return null;
        }
        String accomTypeCode = ((HotelMktSegAccomActivity) hotelMktSegAccomActivity).getAccomTypeCode();
        dto.put(ROOM_TYPE_CODE, accomTypeCode);
        String mktSegCode = ((HotelMktSegAccomActivity) hotelMktSegAccomActivity).getMktSegCode();
        dto.put(MARKET_SEGMENT_CODE, mktSegCode);
        return dto;
    }

    @Override
    public HotelMktSegAccomActivity convertFromMap(Map<String, Object> dto, String correlationId, boolean isPast) {
        HotelMktSegAccomActivity hotelMktSegAccomActivity = super.convertFromMap(dto, correlationId, isPast);
        if (hotelMktSegAccomActivity == null) {
            return null;
        }

        hotelMktSegAccomActivity.setAccomTypeCode(getString(dto, ROOM_TYPE_CODE));
        hotelMktSegAccomActivity.setMktSegCode(getString(dto, MARKET_SEGMENT_CODE));
        return hotelMktSegAccomActivity;
    }

    /**
     * Looks for an existing HotelMktSegAccomActivity record based on the ID if it's present, if it isn't, then
     * it will attempt to use the Occupancy Date / Property ID combination, and if that doesn't find an
     * existing record, it will simply return a new HotelMktSegAccomActivity object.
     */
    @Override
    public HotelMktSegAccomActivity findExistingOrCreateNewActivity(Integer propertyId, Map<String, Object> dto, String correlationId, boolean isPast) {
        // Look up the HotelMktSegAccomActivity record
        HotelMktSegAccomActivity hotelMktSegAccomActivity;

        Integer id = getInteger(dto, ID);
        if (id != null) {
            hotelMktSegAccomActivity = tenantCrudService.find(HotelMktSegAccomActivity.class, id);
        } else {
            hotelMktSegAccomActivity = tenantCrudService.findByNamedQuerySingleResult(
                    HotelMktSegAccomActivity.BY_OCCUPANCY_DATE_AND_ACCOMTYPE_CODE_AND_MKT_SEG_CODE_AND_PROPERTY_ID,
                    QueryParameter.with("occupancyDate", getDate(dto, OCCUPANCY_DATE))
                            .and(PROPERTY_ID, propertyId)
                            .and("accomTypeCode", getString(dto, ROOM_TYPE_CODE))
                            .and("mktSegCode", getString(dto, MARKET_SEGMENT_CODE))
                            .parameters());
        }

        // If it doesn't exist, return create a new one
        if (hotelMktSegAccomActivity == null) {
            hotelMktSegAccomActivity = new HotelMktSegAccomActivity();

        }

        FileMetadata metadata = findExistingFileMetadata(correlationId);
        hotelMktSegAccomActivity.setSnapShotDate(metadata.getSnapshotDtTm());
        hotelMktSegAccomActivity.setFileMetadataId(metadata.getId());

        return hotelMktSegAccomActivity;
    }

    @Override
    public List<HotelMktSegAccomActivity> findExistingOrCreateNewActivity(Integer propertyId, List<Map<String, Object>> dtos, String correlationId) {
        List<HotelMktSegAccomActivity> hotelMktSegAccomActivityEntities = new ArrayList<>();
        FileMetadata metadata = findExistingFileMetadata(correlationId);
        Set<Date> dtoOccupancyDateSet = new HashSet<>();

        identifyUniqueOccupancyDates(dtos, dtoOccupancyDateSet);

        List<HotelMktSegAccomActivity> hotelMktSegAccomActivityList = tenantCrudService.findByNamedQuery(
                HotelMktSegAccomActivity.BY_OCCUPANCY_DATERANGE_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId)
                        .and("occupancyDates", dtoOccupancyDateSet)
                        .parameters());

        Map<String, HotelMktSegAccomActivity> hotelMktSegAccomActivityMap = new HashMap<>();
        for (HotelMktSegAccomActivity hotelMktSegAccomActivity : hotelMktSegAccomActivityList) {
            hotelMktSegAccomActivityMap.put(getKey(propertyId, hotelMktSegAccomActivity.getMktSegCode(), hotelMktSegAccomActivity.getAccomTypeCode(), hotelMktSegAccomActivity.getOccupancyDate()), hotelMktSegAccomActivity);
        }

        LOGGER.info("Adding entries in hotelMktSegActivity existing: " + hotelMktSegAccomActivityMap.size() + " and request contains : " + dtos.size());

        String marketSegmentCode;
        String roomTypeCode;

        for (Map<String, Object> dto : dtos) {
            marketSegmentCode = getString(dto, MARKET_SEGMENT_CODE);
            roomTypeCode = getString(dto, ROOM_TYPE_CODE);

            String occupancyDateString = (String) dto.get(OCCUPANCY_DATE);

            Date occupancyDate;

            try {
                occupancyDate = DateUtil.parseDate(occupancyDateString, OCCUPANCY_DATE_FORMAT_YYYY_MM_DD);
            } catch (ParseException pe) {
                throw new TetrisException("Failed to parse: " + occupancyDateString);
            }

            HotelMktSegAccomActivity entity = hotelMktSegAccomActivityMap.get(getKey(propertyId, marketSegmentCode, roomTypeCode, occupancyDate));
            if (entity == null) {
                entity = new HotelMktSegAccomActivity();
            }

            Integer id = entity.getId();

            entity.setOccupancyDate(occupancyDate);

            entity.setArrivals(getBigDecimal(dto, ARRIVALS));
            BigDecimal cancellations = getBigDecimal(dto, CANCELLATIONS);
            if (cancellations == null) {
                cancellations = BigDecimal.ZERO;
            }
            entity.setCancellations(cancellations);
            entity.setDepartures(getBigDecimal(dto, DEPARTURES));
            entity.setFoodRevenue(getBigDecimal(dto, FOOD_REVENUE));
            BigDecimal noShows = getBigDecimal(dto, NO_SHOWS);
            if (noShows == null) {
                noShows = BigDecimal.ZERO;
            }
            entity.setNoShows(noShows);
            entity.setRoomRevenue(getBigDecimal(dto, ROOM_REVENUE));
            entity.setRoomsSold(getBigDecimal(dto, ROOMS_SOLD));
            entity.setTotalRevenue(getBigDecimal(dto, TOTAL_REVENUE));

            // Reset the ID if there was one and if the copy properties removed it
            if (entity.getId() == null && id != null) {
                entity.setId(id);
            }

            entity.setPropertyId(propertyId);
            entity.setAccomTypeCode(roomTypeCode);
            entity.setMktSegCode(marketSegmentCode);
            entity.setSnapShotDate(metadata.getSnapshotDtTm());
            entity.setFileMetadataId(metadata.getId());

            hotelMktSegAccomActivityEntities.add(entity);
        }

        LOGGER.info("Finished finding and setting entries in hotelMktSegActivity");

        return hotelMktSegAccomActivityEntities;
    }

    private void identifyUniqueOccupancyDates(List<Map<String, Object>> dtos, Set<Date> dtoOccupancyDateSet) {

        LOGGER.info("Starting additions to unique sets");

        Set<String> dtoOccupancyDateSetString = new HashSet<>();

        for (Map<String, Object> dto : dtos) {
            if (dto != null) {
                dtoOccupancyDateSetString.add((String) dto.get(OCCUPANCY_DATE));
            }
        }

        for (String dateString : dtoOccupancyDateSetString) {
            try {
                dtoOccupancyDateSet.add(DateUtil.parseDate(dateString, OCCUPANCY_DATE_FORMAT_YYYY_MM_DD));
            } catch (ParseException pe) {
                throw new TetrisException("Failed to parse: " + dateString);
            }
        }

        LOGGER.info("Completed additions to unique sets");
    }

    public String getKey(Integer propertyId, String mktSegCode, String accomTypeCode, Date occupancyDate) {
        return String.valueOf(propertyId) + "::" + mktSegCode + "::" +
                accomTypeCode + "::" + DateUtil.formatDate(occupancyDate, OCCUPANCY_DATE_FORMAT_YYYY_MM_DD);
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
