package com.ideas.tetris.pacman.services.license.feature;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity;
import com.ideas.tetris.pacman.services.informationmanager.service.InformationManagerCleanupService;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.license.entities.LicensePackage;
import com.ideas.tetris.platform.common.license.migration.actions.LicenseFeatureUpgradable;
import com.ideas.tetris.platform.common.license.util.LicenseFeatureConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class InformationManagerExceptionFeature extends LicenseFeatureUpgradable {
    @Autowired
    protected InformationManagerCleanupService informationManagerCleanUpService;

    @Autowired
    private AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Override
    public String getFeatureCode() {
        return LicenseFeatureConstants.INFO_MGR_EXCEPTIONS;
    }

    @Override
    protected void cleanUp(int propertyId, LicensePackage currentLicensePackage, LicensePackage newLicensePackage, Map<String, Object> featuresInputMapToDowngrade) {
        deleteNotificationsAndConfigs(propertyId);
    }

    public void deleteNotificationsAndConfigs(int propertyId) {
        List<InformationMgrAlertConfigEntity> notificationConfigs = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, InformationMgrAlertConfigEntity.FIND_BY_PROPERTY_ID_ALERT_CATEGORY,
                QueryParameter.with(Constants.PROPERTY_ID, propertyId).and("category", Constants.SYSTEM_EXCEPTION_CATEGORY).parameters());
        notificationConfigs.forEach(config -> informationManagerCleanUpService.deleteExceptionConfiguration(propertyId, config.getId()));
    }
}
