package com.ideas.tetris.pacman.services.datafeed.dto.rateshopping;

public class RateCompetitor {

    private String competitorPropertyName;
    private String competitorDisplayName;
    private String roomClassCode;
    private String isUseInDemandModelEnabled;
    private String isUseInCompetetiveModelEnabled;
    private String isDisabled;
    private String selfHotelIndicator;
    private String productName;

    public String getCompetitorPropertyName() {
        return competitorPropertyName;
    }

    public String getCompetitorDisplayName() {
        return competitorDisplayName;
    }

    public String getRoomClassCode() {
        return roomClassCode;
    }

    public String getIsUseInDemandModelEnabled() {
        return isUseInDemandModelEnabled;
    }

    public String getIsUseInCompetetiveModelEnabled() {
        return isUseInCompetetiveModelEnabled;
    }

    public String getIsDisabled() {
        return isDisabled;
    }

    public void setCompetitorPropertyName(String competitorPropertyName) {
        this.competitorPropertyName = competitorPropertyName;
    }

    public void setCompetitorDisplayName(String competitorDisplayName) {
        this.competitorDisplayName = competitorDisplayName;
    }

    public void setRoomClassCode(String roomClassCode) {
        this.roomClassCode = roomClassCode;
    }

    public void setIsUseInDemandModelEnabled(String isUseInDemandModelEnabled) {
        this.isUseInDemandModelEnabled = isUseInDemandModelEnabled;
    }

    public void setIsUseInCompetetiveModelEnabled(String isUseInCompetetiveModelEnabled) {
        this.isUseInCompetetiveModelEnabled = isUseInCompetetiveModelEnabled;
    }

    public void setIsDisabled(String isDisabled) {
        this.isDisabled = isDisabled;
    }

    public String getSelfHotelIndicator() {
        return selfHotelIndicator;
    }

    public void setSelfHotelIndicator(String selfHotelIndicator) {
        this.selfHotelIndicator = selfHotelIndicator;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
}
