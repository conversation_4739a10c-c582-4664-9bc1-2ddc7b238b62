package com.ideas.tetris.pacman.services.dashboard.vo;


import java.io.Serializable;

public class BasicPricingSummaryVO implements Serializable {
    protected String occupancyOnBooks;
    protected String occupancyOnBooksPercentage;
    protected String revenueOnBooks;
    protected String ADROnBooks;
    protected String revPAR;

    private String roomSoldMTDAndForcastForRestMonth;
    private String roomSoldMTDAndForcastRestOfMonthPercentage;
    private String estimatedRoomRevenue;
    private String estimatedADR;
    private String estimatedRevPAR;

    protected String budgetedRooms;
    protected String budgetedRoomsPercentage;
    protected String budgetedRevenue;
    protected String budgetedADR;
    protected String budgetedRevPAR;

    protected Double roomCapacity;

    public String getEstimatedRevPAR() {
        return estimatedRevPAR;
    }

    public void setEstimatedRevPAR(String estimatedRevPAR) {
        this.estimatedRevPAR = estimatedRevPAR;
    }

    public String getRoomSoldMTDAndForcastForRestMonth() {
        return roomSoldMTDAndForcastForRestMonth;
    }

    public void setRoomSoldMTDAndForcastForRestMonth(
            String roomSoldMTDAndForcastForRestMonth) {
        this.roomSoldMTDAndForcastForRestMonth = roomSoldMTDAndForcastForRestMonth;
    }

    public String getRoomSoldMTDAndForcastRestOfMonthPercentage() {
        return roomSoldMTDAndForcastRestOfMonthPercentage;
    }

    public void setRoomSoldMTDAndForcastRestOfMonthPercentage(
            String roomSoldMTDAndForcastRestOfMonthPercentage) {
        this.roomSoldMTDAndForcastRestOfMonthPercentage = roomSoldMTDAndForcastRestOfMonthPercentage;
    }

    public String getEstimatedRoomRevenue() {
        return estimatedRoomRevenue;
    }

    public void setEstimatedRoomRevenue(String estimatedRoomRevenue) {
        this.estimatedRoomRevenue = estimatedRoomRevenue;
    }

    public String getEstimatedADR() {
        return estimatedADR;
    }

    public void setEstimatedADR(String estimatedADR) {
        this.estimatedADR = estimatedADR;
    }


    public String getBudgetedRooms() {
        return budgetedRooms;
    }

    public void setBudgetedRooms(String budgetedRooms) {
        this.budgetedRooms = budgetedRooms;
    }

    public String getBudgetedRoomsPercentage() {
        return budgetedRoomsPercentage;
    }

    public void setBudgetedRoomsPercentage(String budgetedRoomsPercentage) {
        this.budgetedRoomsPercentage = budgetedRoomsPercentage;
    }

    public String getBudgetedRevenue() {
        return budgetedRevenue;
    }

    public void setBudgetedRevenue(String budgetedRevenue) {
        this.budgetedRevenue = budgetedRevenue;
    }

    public String getBudgetedADR() {
        return budgetedADR;
    }

    public void setBudgetedADR(String budgetedADR) {
        this.budgetedADR = budgetedADR;
    }

    public String getBudgetedRevPAR() {
        return budgetedRevPAR;
    }

    public void setBudgetedRevPAR(String budgetedRevPAR) {
        this.budgetedRevPAR = budgetedRevPAR;
    }

    public String getRevPAR() {
        return revPAR;
    }

    public void setRevPAR(String revPAR) {
        this.revPAR = revPAR;
    }


    public String getOccupancyOnBooks() {
        return occupancyOnBooks;
    }

    public void setOccupancyOnBooks(String occupancyOnBooks) {
        this.occupancyOnBooks = occupancyOnBooks;
    }

    public String getOccupancyOnBooksPercentage() {
        return occupancyOnBooksPercentage;
    }

    public void setOccupancyOnBooksPercentage(String occupancyOnBooksPercentage) {
        this.occupancyOnBooksPercentage = occupancyOnBooksPercentage;
    }

    public String getRevenueOnBooks() {
        return revenueOnBooks;
    }

    public void setRevenueOnBooks(String revenueOnBooks) {
        this.revenueOnBooks = revenueOnBooks;
    }

    public String getADROnBooks() {
        return ADROnBooks;
    }

    public void setADROnBooks(String aDROnBooks) {
        ADROnBooks = aDROnBooks;
    }

    public Double getRoomCapacity() {
        return roomCapacity;
    }

    public void setRoomCapacity(Double roomCapacity) {
        this.roomCapacity = roomCapacity;
    }

}
