package com.ideas.tetris.pacman.services.security;


import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


@SuppressWarnings("serial")
public class JSessionServlet extends HttpServlet {

    @Override
    public void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        doGet(request, response);
    }

    @Override
    public void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        //return back the jsessionid so the front-end node/grails can use it to make calls to the backend node.
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json");
        String jsessionId = "{\"jsessionid\":\"" + request.getSession().getId() + "\"}";
        response.getWriter().println(jsessionId);
    }

}
