package com.ideas.tetris.pacman.services.reports.userreport.dto;

import java.util.ArrayList;
import java.util.List;

public class UsersAuthGroupPropertyDetails {
    Integer userID;
    String authGrpName;
    String authGrpRole;
    List<PropertyRoleAndLastAccessDTO> authGrpDetails = new ArrayList<PropertyRoleAndLastAccessDTO>();
    List<PropertyRoleAndLastAccessDTO> individualPropertyDetails = new ArrayList<PropertyRoleAndLastAccessDTO>();


    public void setUserID(Integer userID) {
        this.userID = userID;
    }

    public String getAuthGrpName() {
        return authGrpName;
    }

    public void setAuthGrpName(String authGrpName) {
        this.authGrpName = authGrpName;
    }

    public String getAuthGrpRole() {
        return authGrpRole;
    }

    public void setAuthGrpRole(String authGrpRole) {
        this.authGrpRole = authGrpRole;
    }

    public List<PropertyRoleAndLastAccessDTO> getIndividualPropertyDetails() {
        return individualPropertyDetails;
    }

    public void setIndividualPropertyDetails(List<PropertyRoleAndLastAccessDTO> individualPropertyDetails) {
        this.individualPropertyDetails = individualPropertyDetails;
    }

    public List<PropertyRoleAndLastAccessDTO> getAuthGrpDetails() {
        return authGrpDetails;
    }

    public void setAuthGrpDetails(List<PropertyRoleAndLastAccessDTO> authGrpDetails) {
        this.authGrpDetails = authGrpDetails;
    }

    public Integer getUserID() {
        return userID;
    }

}
