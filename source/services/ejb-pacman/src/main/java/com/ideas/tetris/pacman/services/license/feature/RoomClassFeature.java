package com.ideas.tetris.pacman.services.license.feature;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.roomconfiguration.RoomClassMigrationService;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.license.entities.LicensePackage;
import com.ideas.tetris.platform.common.license.migration.actions.LicenseFeatureUpgradable;
import com.ideas.tetris.platform.common.license.util.LicenseFeatureConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

@Component
public class RoomClassFeature extends LicenseFeatureUpgradable {
    @Autowired
    private RoomClassMigrationService roomClassMigrationService;

    @Override
    public String getFeatureCode() {
        return LicenseFeatureConstants.ROOM_CLASS;
    }

    @Override
    protected boolean isCleanUpRequired(LicensePackage currentLicensePackage, LicensePackage newLicensePackage, Map<String, Object> featuresInputMapToDowngrade) {
        if (featuresInputMapToDowngrade != null) {
            String value = (String) featuresInputMapToDowngrade.get(JobParameterKey.DELETE_ALL_ROOM_CLASS);
            if (Boolean.parseBoolean(value)) {
                return true;
            }
        }
        return super.isCleanUpRequired(currentLicensePackage, newLicensePackage, featuresInputMapToDowngrade);
    }

    @Override
    protected void cleanUp(int propertyId, LicensePackage currentLicensePackage, LicensePackage newLicensePackage, Map<String, Object> featuresInputMapToDowngrade) {
//      Delete all RCs when deleteAllRCs passed as true
        Optional.ofNullable(featuresInputMapToDowngrade).ifPresent(featuresToRetain -> {
            String value = (String) featuresToRetain.get(JobParameterKey.DELETE_ALL_ROOM_CLASS);
            Optional<Boolean> deleteAllRCs = Optional.ofNullable(value != null ? Boolean.parseBoolean(value) : null);
            deleteAllRCs.ifPresentOrElse(delete -> {
                if (Boolean.TRUE.equals(delete)) {
                    roomClassMigrationService.deleteAllRoomClasses();
                } else {
                    retainRoomClass(featuresToRetain);
                }
            }, () -> retainRoomClass(featuresToRetain));
        });
    }

    private void retainRoomClass(Map<String, Object> featuresToRetain) {
        Optional<AccomClass> roomClassToRetain = Optional.ofNullable((AccomClass) featuresToRetain.get(JobParameterKey.ROOMCLASS_TO_RETAIN));
        roomClassToRetain.ifPresent(roomClass -> roomClassMigrationService.downgradeToSingleRoomClass(roomClass));
    }

}
