package com.ideas.tetris.pacman.services.datafeed.dto.pricestrategy;


import java.util.Date;

public class RateCodeConfiguration {
    private String ratePlanName;
    private String ratePlanDescription;
    private Date startDate;
    private Date endDate;
    private Integer rank;

    public RateCodeConfiguration(String ratePlanName, String ratePlanDescription, Date startDate, Date endDate, Integer rank) {
        this.ratePlanName = ratePlanName;
        this.ratePlanDescription = ratePlanDescription;
        this.startDate = startDate;
        this.endDate = endDate;
        this.rank = rank;
    }

    public RateCodeConfiguration() {
    }

    public String getRatePlanName() {
        return ratePlanName;
    }

    public void setRatePlanName(String ratePlanName) {
        this.ratePlanName = ratePlanName;
    }

    public String getRatePlanDescription() {
        return ratePlanDescription;
    }

    public void setRatePlanDescription(String ratePlanDescription) {
        this.ratePlanDescription = ratePlanDescription;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }
}
