package com.ideas.tetris.pacman.services.datafeed.endpoint;

/* This enum stores list of frequency types associated with each rest call
 * ON_DEMAND : this is something defined for manual creation of data */


import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum EndpointFrequencyType {
    DAILY("Daily"),
    WEEKLY("Weekly"),
    MONTHLY("Monthly"),
    ON_DEMAND("On-demand"),
    CLIENT_DAILY("Daily"),
    CLIENT_WEEKLY("Weekly"),
    CLIENT_MONTHLY("Monthly"),
    NEVER("Never");

    EndpointFrequencyType(String typeValue) {
        this.typeValue = typeValue;
    }

    private final String typeValue;

    public String getTypeValue() {
        return typeValue;
    }

    public static String getValueByName(String name) {
        for (EndpointFrequencyType type : values()) {
            if (type.name().equalsIgnoreCase(name)) {
                return type.getTypeValue();
            }
        }
        return name;
    }

    public static Set<EndpointFrequencyType> getPropertyLevelFrequencyTypes() {
        return Stream.of(EndpointFrequencyType.DAILY, EndpointFrequencyType.WEEKLY, EndpointFrequencyType.MONTHLY).collect(Collectors.toSet());
    }

    public static Set<EndpointFrequencyType> getClientLevelFrequencyTypes() {
        return Stream.of(EndpointFrequencyType.CLIENT_DAILY, EndpointFrequencyType.CLIENT_WEEKLY, EndpointFrequencyType.CLIENT_MONTHLY).collect(Collectors.toSet());
    }
}
