package com.ideas.tetris.pacman.services.webrate.vo;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;

import java.io.Serializable;
import java.time.LocalDateTime;


public class WebrateCompetitorsVO implements Serializable {
    Integer webRateCompetitorsId;
    String webrateHotelId;
    String webrateCompetitorsName;
    String webrateCompetitorsAlias;
    String webrateCompetitorsDescription;
    Boolean shouldDelete;
    Integer lastUpdatedByUserId;
    LocalDateTime lastUpdatedDate;

    public WebrateCompetitorsVO() {
    }

    public WebrateCompetitorsVO(WebrateCompetitors webrateCompetitors) {
        this.setWebRateCompetitorsId(webrateCompetitors.getId());
        this.setWebrateHotelId(webrateCompetitors.getWebrateHotelID());
        this.setWebrateCompetitorsName(webrateCompetitors.getWebrateCompetitorsName());
        this.setWebrateCompetitorsAlias(webrateCompetitors.getWebrateCompetitorsAlias());
        this.setWebrateCompetitorsDescription(webrateCompetitors.getWebrateCompetitorsDescription());
        this.setShouldDelete(webrateCompetitors.getShouldDelete());
        this.setLastUpdatedByUserId(Integer.valueOf(PacmanWorkContextHelper.getUserId()));
        this.setLastUpdatedDate(LocalDateTime.now());
    }

    public Integer getWebRateCompetitorsId() {
        return webRateCompetitorsId;
    }

    public void setWebRateCompetitorsId(Integer webRateCompetitorsId) {
        this.webRateCompetitorsId = webRateCompetitorsId;
    }

    public String getWebrateHotelId() {
        return webrateHotelId;
    }

    public void setWebrateHotelId(String webrateHotelId) {
        this.webrateHotelId = webrateHotelId;
    }

    public String getWebrateCompetitorsName() {
        return webrateCompetitorsName;
    }

    public void setWebrateCompetitorsName(String webrateCompetitorsName) {
        this.webrateCompetitorsName = webrateCompetitorsName;
    }

    public String getWebrateCompetitorsAlias() {
        return webrateCompetitorsAlias;
    }

    public void setWebrateCompetitorsAlias(String webrateCompetitorsAlias) {
        this.webrateCompetitorsAlias = webrateCompetitorsAlias;
    }

    public String getWebrateCompetitorsDescription() {
        return webrateCompetitorsDescription;
    }

    public void setWebrateCompetitorsDescription(String webrateCompetitorsDescription) {
        this.webrateCompetitorsDescription = webrateCompetitorsDescription;
    }

    public Boolean getShouldDelete() {
        return shouldDelete;
    }

    public void setShouldDelete(Boolean shouldDelete) {
        this.shouldDelete = shouldDelete;
    }

    public Integer getLastUpdatedByUserId() {
        return lastUpdatedByUserId;
    }

    public void setLastUpdatedByUserId(Integer lastUpdatedByUserId) {
        this.lastUpdatedByUserId = lastUpdatedByUserId;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }
}
