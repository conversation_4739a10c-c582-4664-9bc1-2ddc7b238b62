package com.ideas.tetris.pacman.services.forecast.mapper;

import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailOnBooksDTO;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

import static com.ideas.tetris.pacman.services.forecast.ExpectedForecastConstant.HIPHEN;

public class ExpectedForecastDetailsOnBooksRowMapper implements RowMapper<ExpectedForecastDetailOnBooksDTO> {

    @Override
    public ExpectedForecastDetailOnBooksDTO mapRow(Object[] row) {

        ExpectedForecastDetailOnBooksDTO expectedForecastDetailOnBooksDTO = new ExpectedForecastDetailOnBooksDTO();
        expectedForecastDetailOnBooksDTO.setOccupancyDate((Date) row[0]);
        expectedForecastDetailOnBooksDTO.setTransientOnBooks(getValueFromRow(row[1]));
        expectedForecastDetailOnBooksDTO.setExpectedOnBooks(getValueFromRow(row[2]));
        expectedForecastDetailOnBooksDTO.setStly(row[3] == null ? "0" : row[3] + StringUtils.EMPTY);
        expectedForecastDetailOnBooksDTO.setSt2y(row[4] == null ? "0" : row[4] + StringUtils.EMPTY);
        expectedForecastDetailOnBooksDTO.setTransientOnBooksOthers(getValueFromRow(row[5]));
        expectedForecastDetailOnBooksDTO.setGroupOnBooks(getValueFromRow(row[6]));
        expectedForecastDetailOnBooksDTO.setTotalOnBooks(getValueFromRow(row[7]));
        expectedForecastDetailOnBooksDTO.setTotalForecast(getValueFromRow(row[8]));
        expectedForecastDetailOnBooksDTO.setMyForecast(getValueFromRow(row[9]));
        return expectedForecastDetailOnBooksDTO;
    }

    private String getValueFromRow(Object row) {
        return row == null ? HIPHEN : row + StringUtils.EMPTY;
    }
}
