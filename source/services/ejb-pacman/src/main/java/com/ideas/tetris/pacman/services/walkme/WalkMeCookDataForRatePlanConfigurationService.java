package com.ideas.tetris.pacman.services.walkme;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.unqualifiedrate.dto.RateHeader;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedAccomClass;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedDefaults;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedDetails;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component
@Transactional
public class WalkMeCookDataForRatePlanConfigurationService {

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    @Autowired
    WalkMeCookDataService walkMeCookDataService;

    public void addRequirementsForRatePlanConfigurationBARByLOS() {
        addRequirementsForRateHeaders();
        addRequirementsForRateDetails();
        addRequirementsForPriceStrategyConfigurationForBARByLOS();
    }

    public void addRequirementsForRatePlanConfigurationBARByDay() {
        addRequirementsForRateHeaders();
        addRequirementsForRateDetails();
        addRequirementsForPriceStrategyConfigurationForBARByDay();
    }

    public void addRequirementsForRateHeaders() {
        List<RateUnqualified> rateUnqualifiedList = new ArrayList<>();
        rateUnqualifiedList.add(addRateUnqualified(WalkMeConstants.RATE_UNQUALIFIED0));
        rateUnqualifiedList.add(addRateUnqualified(WalkMeConstants.RATE_UNQUALIFIED1));
        rateUnqualifiedList.add(addRateUnqualified(WalkMeConstants.RATE_UNQUALIFIED2));
        rateUnqualifiedList.add(addRateUnqualified(WalkMeConstants.RATE_UNQUALIFIED3));
        rateUnqualifiedList.add(addRateUnqualified(WalkMeConstants.RATE_UNQUALIFIED4));
        rateUnqualifiedList.add(addRateUnqualified(WalkMeConstants.RATE_UNQUALIFIED5));
        rateUnqualifiedList.add(addRateUnqualified(WalkMeConstants.RATE_UNQUALIFIED6));
        rateUnqualifiedList.add(addRateUnqualified(WalkMeConstants.RATE_UNQUALIFIED7));
        rateUnqualifiedList.add(addRateUnqualified(WalkMeConstants.RATE_UNQUALIFIED8));
        tenantCrudService.save(rateUnqualifiedList);
    }

    public void addRequirementsForRateDetails() {
        List<RateUnqualifiedDetails> rateUnqualifiedDetailsList = new ArrayList<>();
        List<AccomType> accomTypeList = tenantCrudService.findByNamedQuery(AccomType.BY_PROPERTY_ID, QueryParameter.with(Constants.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        List<RateUnqualified> rateUnqualifiedList = tenantCrudService.findByNamedQuery(RateUnqualified.BY_PROPERTY_ID, QueryParameter.with(Constants.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        for (RateUnqualified rateUnqualified : rateUnqualifiedList) {
            for (AccomType accomType : accomTypeList) {
                RateUnqualifiedDetails rateUnqualifiedDetails = new RateUnqualifiedDetails();
                rateUnqualifiedDetails.setRateUnqualifiedId(rateUnqualified.getId());
                rateUnqualifiedDetails.setAccomTypeId(accomType.getId());
                rateUnqualifiedDetails.setStartDate(new LocalDate(2017, 11, 18).toDate());
                rateUnqualifiedDetails.setEndDate((new LocalDate(2099, 10, 14)).toDate());
                BigDecimal value = BigDecimal.valueOf(120.00);
                for (DayOfWeek day : DayOfWeek.values()) {
                    rateUnqualifiedDetails.applyValueByDayOfWeek(day, value);
                }
                rateUnqualifiedDetailsList.add(rateUnqualifiedDetails);
            }
        }
        tenantCrudService.save(rateUnqualifiedDetailsList);
    }

    public void addRequirementsForPriceStrategyConfigurationForBARByDay() {
        addRateUnqualifiedAccomClassAndDefaultsForPriceStrategyConfiguration(1f);
        enableAllowAvailableForArrival();
        enableSingleBarDecision();
    }

    public void addRequirementsForPriceStrategyConfigurationForBARByLOS() {
        addRateUnqualifiedAccomClassAndDefaultsForPriceStrategyConfiguration(8f);
        enableAllowMinMaxLOSOverride();
        enableAllowAvailableForArrival();
        enableSingleBarDecision();
    }

    private void addRateUnqualifiedAccomClassAndDefaultsForPriceStrategyConfiguration(Float maxLOS) {
        List<RateUnqualifiedAccomClass> rateUnqualifiedAccomClassList = new ArrayList<>();
        List<RateUnqualifiedDefaults> rateUnqualifiedDefaultsList = new ArrayList<>();
        List<RateUnqualified> rateUnqualifiedList = tenantCrudService.findByNamedQuery(RateUnqualified.BY_PROPERTY_ID, QueryParameter.with(Constants.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        List<AccomClass> accomClassList = tenantCrudService.findByNamedQuery(AccomClass.BY_PROPERTY_ID, QueryParameter.with(Constants.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        for (RateUnqualified rateUnqualified : rateUnqualifiedList) {
            for (AccomClass accomClass : accomClassList) {
                RateUnqualifiedAccomClass rateUnqualifiedAccomClass = createRateUnqualifiedAccomClass(rateUnqualified, accomClass);
                RateUnqualifiedDefaults rateUnqualifiedDefaults = createRateUnqualifiedDefaults(rateUnqualifiedAccomClass, maxLOS);
                rateUnqualifiedAccomClassList.add(rateUnqualifiedAccomClass);
                rateUnqualifiedDefaultsList.add(rateUnqualifiedDefaults);
            }
        }
        tenantCrudService.save(rateUnqualifiedAccomClassList);
        tenantCrudService.save(rateUnqualifiedDefaultsList);
    }

    private RateUnqualifiedDefaults createRateUnqualifiedDefaults(RateUnqualifiedAccomClass rateUnqualifiedAccomClass, Float maxLOS) {
        RateUnqualifiedDefaults rateUnqualifiedDefaults = new RateUnqualifiedDefaults();
        rateUnqualifiedDefaults.setRateUnqualifiedAccomClass(rateUnqualifiedAccomClass);
        rateUnqualifiedDefaults.setUserOverrideOnly(0);
        rateUnqualifiedDefaults.setMondayAvailable(1);
        rateUnqualifiedDefaults.setTuesdayAvailable(1);
        rateUnqualifiedDefaults.setWednesdayAvailable(1);
        rateUnqualifiedDefaults.setThursdayAvailable(1);
        rateUnqualifiedDefaults.setFridayAvailable(1);
        rateUnqualifiedDefaults.setSaturdayAvailable(1);
        rateUnqualifiedDefaults.setSundayAvailable(1);
        rateUnqualifiedDefaults.setSundayMinLos(1f);
        rateUnqualifiedDefaults.setMondayMinLos(1f);
        rateUnqualifiedDefaults.setTuesdayMinLos(1f);
        rateUnqualifiedDefaults.setWednesdayMinLos(1f);
        rateUnqualifiedDefaults.setThursdayMinLos(1f);
        rateUnqualifiedDefaults.setFridayMinLos(1f);
        rateUnqualifiedDefaults.setSaturdayMinLos(1f);
        rateUnqualifiedDefaults.setSundayMaxLos(maxLOS);
        rateUnqualifiedDefaults.setMondayMaxLos(maxLOS);
        rateUnqualifiedDefaults.setTuesdayMaxLos(maxLOS);
        rateUnqualifiedDefaults.setWednesdayMaxLos(maxLOS);
        rateUnqualifiedDefaults.setThursdayMaxLos(maxLOS);
        rateUnqualifiedDefaults.setFridayMaxLos(maxLOS);
        rateUnqualifiedDefaults.setSaturdayMaxLos(maxLOS);
        return rateUnqualifiedDefaults;
    }

    private RateUnqualifiedAccomClass createRateUnqualifiedAccomClass(RateUnqualified rateUnqualified, AccomClass accomClass) {
        RateUnqualifiedAccomClass rateUnqualifiedAccomClass = new RateUnqualifiedAccomClass();
        rateUnqualifiedAccomClass.setRateUnqualifiedId(rateUnqualified.getId());
        rateUnqualifiedAccomClass.setAccomClassId(accomClass.getId());
        return rateUnqualifiedAccomClass;
    }

    protected RateUnqualified addRateUnqualified(String name) {
        RateUnqualified rateUnqualified = new RateUnqualified();
        rateUnqualified.setDefaultValues();
        RateHeader rateHeader = setRateHeaderWithDefaultValues(name);
        rateUnqualified.setUserUpdatedValues(rateHeader, 2);
        return rateUnqualified;
    }

    protected RateHeader setRateHeaderWithDefaultValues(String name) {
        RateHeader rateHeader = new RateHeader();
        rateHeader.setName(name);
        rateHeader.setDescription(name);
        rateHeader.setRateUnqualifiedStartDate(new DateParameter(18, 11, 2018));
        rateHeader.setRateUnqualifiedEndDate(new DateParameter(14, 10, 2099));
        return rateHeader;
    }

    protected void enableAllowMinMaxLOSOverride() {
        configParamsService.updateParameterValue(walkMeCookDataService.getContextPath(), IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value(), Constants.TRUE);
    }

    protected void enableAllowAvailableForArrival() {
        configParamsService.updateParameterValue(walkMeCookDataService.getContextPath(), IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value(), Constants.TRUE);
    }

    protected void enableSingleBarDecision() {
        configParamsService.updateParameterValue(walkMeCookDataService.getContextPath(), IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value(), Constants.FALSE);
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }
}
