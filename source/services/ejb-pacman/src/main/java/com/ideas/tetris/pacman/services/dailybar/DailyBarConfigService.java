package com.ideas.tetris.pacman.services.dailybar;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.contextholder.WorkContextRestEasyController;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.marketsegment.entity.OffsetType;
import com.ideas.tetris.pacman.services.opera.entity.DailyBarConfig;
import com.ideas.tetris.pacman.services.opera.entity.DailyBarRateChart;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.collections.BidiMap;
import org.apache.commons.collections.bidimap.DualHashBidiMap;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

@Component
@Transactional
public class DailyBarConfigService {
    public static final String CODE = "code";
    public static final String BAR = "BAR";
    private static Logger log = Logger.getLogger(DailyBarConfigService.class.getName());

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Autowired
	private PropertyService propertyService;

    @Autowired
    WorkContextRestEasyController workContextRestEasyController;

    @Autowired
    AccommodationService accommodationService;


    public void persistAll(List<DailyBarConfig> dailyBarConfigs) {
        log.info("Daily BAR configuration replaced for property ID: " + workContextRestEasyController.getCurrentWorkContext().getPropertyId());

        crudService.deleteAll(DailyBarConfig.class);
        crudService.deleteAll(DailyBarRateChart.class);
        crudService.save(dailyBarConfigs);
    }

    public List<DailyBarConfig> findAll() {
        return crudService.findAll(DailyBarConfig.class);
    }

    public BidiMap fetchAccomCodeIdMap() {
        return cacheAccomTypeNameId();
    }

    public BidiMap fetchProductCodeId() {
        return cacheProductCodeAndId(crudService.findByNamedQuery(Product.GET_PRODUCTS_NOT_IN_CODES, QueryParameter.with("codes", Arrays.asList(Product.AGILE_RATES_PRODUCT_CODE, Product.FIXED_ABOVE_BAR_CODE)).parameters()));
    }

    public BidiMap fetchBarProduct() {
        BidiMap productMap = new DualHashBidiMap();
        Product barProduct = crudService.findByNamedQuerySingleResult(Product.GET_BY_CODE, QueryParameter.with(CODE, BAR).parameters());
        productMap.put(barProduct.getCode(), barProduct.getId());
        return productMap;
    }

    public BidiMap fetchOffsetNameId() {
        return cacheOffsetNameId(crudService.findAll(OffsetType.class));
    }

    private BidiMap cacheOffsetNameId(List<OffsetType> offsetTypes) {
        BidiMap cacheOffsetNameId = new DualHashBidiMap();
        for (OffsetType offsetType : offsetTypes) {
            cacheOffsetNameId.put(offsetType.getName(), offsetType.getId());
        }
        return cacheOffsetNameId;
    }

    private BidiMap cacheProductCodeAndId(List<Product> products) {
        BidiMap cacheProductCodeId = new DualHashBidiMap();
        for (Product product : products) {
            cacheProductCodeId.put(product.getCode(), product.getId());
        }
        return cacheProductCodeId;
    }

    private BidiMap cacheAccomTypeNameId() {
        BidiMap cacheAccomData = new DualHashBidiMap();
        List<AccomType> accomTypeList = getAccomTypesByPropertyId();
        for (AccomType accomType : accomTypeList) {
            cacheAccomData.put(accomType.getAccomTypeCode(), accomType.getId());
        }
        return cacheAccomData;
    }

    @SuppressWarnings("unchecked")
    private List<AccomType> getAccomTypesByPropertyId() {
        return crudService.findByNamedQuery(AccomType.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public String getPropertyName() {
        Property property = propertyService.getPropertyById(PacmanWorkContextHelper.getPropertyId());
        return property.getName();
    }

    public BidiMap getAssignedAccomTypesWithCapacity() {
        List<AccomType> accomTypes = accommodationService.getAllAssignedAccomTypesWithCapacity();
        BidiMap cacheAccomTypes = new DualHashBidiMap();
        for (AccomType accomType : accomTypes) {
            cacheAccomTypes.put(accomType.getAccomTypeCode(), accomType.getId());
        }

        return cacheAccomTypes;
    }

    public Long getBarProductCountWithinDateRange(LocalDate startDate, LocalDate endDate) {
        Product barProduct = crudService.findByNamedQuerySingleResult(Product.GET_BY_CODE, QueryParameter.with(CODE, BAR).parameters());
        return crudService.findByNamedQuerySingleResult(DailyBarConfig.COUNT_BY_PRODUCT_ID_WITHIN_DATE_RANGE,
                QueryParameter.with("productId", barProduct.getId())
                        .and("startDate", startDate)
                        .and("endDate", endDate).parameters());
    }
}
