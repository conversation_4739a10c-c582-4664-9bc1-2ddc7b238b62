package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.property.dto.ExtractDetails;
import com.ideas.tetris.pacman.services.property.dto.MonthFolderFileFilter;
import com.ideas.tetris.pacman.services.property.dto.PacmanExtractDetails;
import com.ideas.tetris.pacman.services.property.dto.WebRateExtractDetails;
import com.ideas.tetris.pacman.util.file.PacmanFileUtilService;
import com.ideas.tetris.pacman.util.file.PacmanFileUtilService.PacmanQualifier;
import com.ideas.tetris.platform.common.businessservice.async.AsyncJobCallback;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.io.FileUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

@Component
@Transactional
public class ExtractDetailsService implements ExtractDetailsServiceLocal {

    private static final Logger LOGGER = Logger.getLogger(ExtractDetailsService.class.getName());
    public static final String VALID = "valid";

    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService tenantCrudService;
    @Autowired
	private ExtractDetailsCacheService cacheService;
    @Autowired
	private ExtractMapperServiceLocal mapperService;
    @PacmanQualifier
	@Autowired
	@Qualifier("pacmanFileUtilService")
    PacmanFileUtilService pacmanFileUtilService;

    public void setCacheService(ExtractDetailsCacheService cacheService) {
        this.cacheService = cacheService;
    }

    public void setMapperService(ExtractMapperServiceLocal mapperService) {
        this.mapperService = mapperService;
    }

    @Override
    public boolean areBdeExtractsAvailableForPastDays(Integer propertyId, int dayCount) {
        ExtractDetails extractDetails = rebuildExtractDetails(propertyId);
        LocalDate lastRequiredDate = LocalDate.now().minusDays(1);
        LocalDate firstRequiredDate = lastRequiredDate.minusDays(dayCount);
        for (LocalDate date = firstRequiredDate; !date.isAfter(lastRequiredDate); date = date.plusDays(1)) {
            List<String> extracts = extractDetails.getArchivedFilesForDate(date);
            if (extracts == null || extracts.isEmpty()) {
                return false;
            }
        }
        return true;
    }

    @Override
    public ExtractDetails getExtractDetails(Integer propertyId) {
        ExtractDetails results = cacheService.getExtractDetails(propertyId);
        if (results == null) {

            results = rebuildExtractDetails(propertyId);
        }
        return results;
    }

    @Override
    public ExtractDetails getExtractDetails(Property property) {
        ExtractDetails results = cacheService.getExtractDetails(property.getId());
        if (results == null) {
            results = rebuildExtractDetails(property);
        }
        return results;
    }

    @Override
    public void putExtractDetails(Integer propertyId, ExtractDetails extractDetails) {
        if (propertyId == null || extractDetails == null) {
            return;
        }
        cacheService.putExtractDetails(propertyId, extractDetails);
    }

    public WebRateExtractDetails getWebRateExtractDetailsWithFilePaths(Integer propertyId) {
        Map<Integer, WebRateExtractDetails> webRateExtractsOnDisk = mapperService.mapWebRateExtractsOnDisk(propertyId);
        if (webRateExtractsOnDisk != null) {
            return webRateExtractsOnDisk.get(propertyId);
        }
        return new WebRateExtractDetails();
    }

    @Override
    public WebRateExtractDetails getWebRateExtractDetails(Integer propertyId) {
        WebRateExtractDetails results = cacheService.getWebRateExtractDetails(propertyId);
        if (results == null) {
            results = rebuildWebRateExtractDetails(propertyId);
        }
        return results;
    }

    @Override
    public WebRateExtractDetails getWebRateExtractDetails(Property property) {
        WebRateExtractDetails results = cacheService.getWebRateExtractDetails(property.getId());
        if (results == null) {
            results = rebuildWebRateExtractDetails(property);
        }
        return results;
    }

    @Override
    public ExtractDetails rebuildExtractDetails(Integer propertyId) {
        return getExtractDetails(propertyId, mapperService.mapExtractsOnDisk(propertyId));
    }

    @Override
    public ExtractDetails rebuildExtractDetails(Property property) {
        return getExtractDetails(property.getId(), mapperService.mapExtractsOnDisk(property));
    }

    private ExtractDetails getExtractDetails(Integer propertyId, Map<Integer, ExtractDetails> map) {
        if (map == null || map.get(propertyId) == null) {
            return null;
        }
        ExtractDetails details = map.get(propertyId);
        cacheService.putExtractDetails(propertyId, details);
        return details;
    }

    // used for newly added properties that won't show up in the ConsolidatedPropertyView yet
    @Override

    public ExtractDetails rebuildExtractDetails(Integer propertyId, String clientCode, String propertyCode) {
        Map<Integer, ExtractDetails> map = mapperService.mapExtractsOnDisk(propertyId, clientCode, propertyCode);
        if (map == null || map.get(propertyId) == null) {
            return null;
        }
        ExtractDetails details = map.get(propertyId);
        cacheService.putExtractDetails(propertyId, details);
        return details;
    }

    @Override
    public WebRateExtractDetails rebuildWebRateExtractDetails(Integer propertyId) {
        return rebuildWebRateExtractDetails(propertyId, mapperService.mapWebRateExtractsOnDisk(propertyId));
    }

    @Override
    public WebRateExtractDetails rebuildWebRateExtractDetails(Property property) {
        return rebuildWebRateExtractDetails(property.getId(), mapperService.mapWebRateExtractsOnDisk(property));
    }

    private WebRateExtractDetails rebuildWebRateExtractDetails(Integer propertyId, Map<Integer, WebRateExtractDetails> map) {
        if (map == null || map.get(propertyId) == null) {
            return null;
        }
        WebRateExtractDetails details = map.get(propertyId);
        cacheService.putWebRateExtractDetails(propertyId, details);
        return details;
    }

    // used for newly added properties that won't show up in the ConsolidatedPropertyView yet
    @Override
    public WebRateExtractDetails rebuildWebRateExtractDetails(Integer propertyId, String clientCode, String
            propertyCode) {
        Map<Integer, WebRateExtractDetails> map = mapperService.mapWebRateExtractsOnDisk(propertyId, clientCode, propertyCode);
        if (map == null || map.get(propertyId) == null) {
            return null;
        }
        WebRateExtractDetails details = map.get(propertyId);
        cacheService.putWebRateExtractDetails(propertyId, details);
        return details;
    }

    @Override
    public PacmanExtractDetails getPacmanExtractDetails(Integer propertyId) {
        Map<Integer, PacmanExtractDetails> map = mapperService.mapPacmanExtractsOnDisk(propertyId);
        if (map == null || map.get(propertyId) == null) {
            return null;
        }
        return map.get(propertyId);
    }

    // Leaving for old installation status invocation. Eventually deprecate as properties with Gbs of extracts will
    // timeout. This needs to be asynchronous. Use rollbackPropertyExtracts
    @Override
    public ExtractDetails moveArchivedExtractsToIncoming(Integer propertyId) {
        long startTime = System.currentTimeMillis();
        ExtractDetails extractDetails = getExtractDetailsWithFilePaths(propertyId);
        pacmanFileUtilService.moveFilesMaintainingDateClientPropertyPath(extractDetails.getArchivedExtracts(),
                new File(SystemConfig.getCrsIncomingFolder()));
        extractDetails = rebuildExtractDetails(propertyId);
        LOGGER.info("Move extracts from archived to incoming & rebuild details (ms): " +
                (System.currentTimeMillis() - startTime));
        return extractDetails;
    }

    public ExtractDetails getExtractDetailsWithFilePaths(Integer propertyId) {
        Map<Integer, ExtractDetails> extractsOnDisk = mapperService.mapExtractsOnDisk(propertyId);
        if (extractsOnDisk != null) {
            return extractsOnDisk.get(propertyId);
        }
        return new ExtractDetails();
    }

    @Override
    public ExtractDetails moveIncomingExtractsToArchive(Integer propertyId) {
        ExtractDetails details = getExtractDetailsWithFilePaths(propertyId);
        pacmanFileUtilService.moveFilesMaintainingDateClientPropertyPath(details.getIncomingExtracts(),
                new File(SystemConfig.getCrsArchiveFolder()));
        return rebuildExtractDetails(propertyId);
    }

    // Leaving for old installation status invocation. Eventually deprecate as properties with Gbs of extracts will
    // timeout. This needs to be asynchronous. Use rollbackPropertyExtracts
    @Override
    public WebRateExtractDetails moveArchivedWebRateExtractsToIncoming(Integer propertyId) {
        long startTime = System.currentTimeMillis();
        WebRateExtractDetails webRateExtractDetails = getWebRateExtractDetailsWithFilePaths(propertyId);
        pacmanFileUtilService.moveFilesMaintainingDateClientPropertyPath(webRateExtractDetails.getArchivedExtracts(),
                new File(SystemConfig.getRssIncomingFolder()));
        webRateExtractDetails = rebuildWebRateExtractDetails(propertyId);
        LOGGER.info("Move web rate extracts from archived to incoming & rebuild details (ms): " +
                (System.currentTimeMillis() - startTime));
        return webRateExtractDetails;
    }

    @Override
    public WebRateExtractDetails moveIncomingWebRateExtractsToArchive(Integer propertyId) {
        WebRateExtractDetails details = getWebRateExtractDetailsWithFilePaths(propertyId);
        pacmanFileUtilService.moveFilesMaintainingDateClientPropertyPath(details.getIncomingExtracts(),
                new File(SystemConfig.getRssArchiveFolder()));
        return rebuildWebRateExtractDetails(propertyId);
    }

    @Override
    public WebRateExtractDetails moveWebRateIncomingExtractToArchive(Integer propertyId, File
            incomingWebRateExtract) {
        WebRateExtractDetails webRateExtractDetails = SystemConfig.useExtractCacheFirst() ? getWebRateExtractDetails(propertyId) : getWebRateExtractDetailsWithFilePaths(propertyId);
        webRateExtractDetails.moveIncomingExtractToArchive(incomingWebRateExtract);
        return webRateExtractDetails;
    }

    @Async
    @AsyncJobCallback
    @Override
    public Future<String> rollbackPropertyExtracts(JobStepContext jobStepContext, ExtractDetails extractDetails,
                                                   WebRateExtractDetails webRateExtractDetails) {
        long startTime = System.currentTimeMillis();
        // Rollback job for non-ratchet integration calls this
        if (null != extractDetails) {
            pacmanFileUtilService.moveFilesMaintainingDateClientPropertyPath(extractDetails.getArchivedExtracts(),
                    new File(SystemConfig.getCrsIncomingFolder()));
            LOGGER.info("Move extracts from archived to incoming & rebuild details (ms): " +
                    (System.currentTimeMillis() - startTime));
            startTime = System.currentTimeMillis();
        } else {
            LOGGER.info("No extracts to move. Really expedites matters, doesn't it.");
        }
        if (null != webRateExtractDetails) {
            pacmanFileUtilService.moveFilesMaintainingDateClientPropertyPath(webRateExtractDetails.getArchivedExtracts(),
                    new File(SystemConfig.getRssIncomingFolder()));
            LOGGER.info("Move web rate extracts from archived to incoming & rebuild details (ms): " +
                    (System.currentTimeMillis() - startTime));
        }
        return new AsyncResult<String>("Success");
    }

    @Override
    public void deleteExtracts(String clientCode, String propertyCode) {
        File dataFolder = new File(SystemConfig.getG3DataFolder());
        if (dataFolder.exists() && dataFolder.isDirectory()) {
            File[] list = dataFolder.listFiles();
            for (File f : list) {
                if (f.isDirectory()) {
                    // see if it has an incoming folder
                    deleteFromFolder(clientCode, propertyCode, f, "incoming");
                    // see if it has an archive folder
                    deleteFromFolder(clientCode, propertyCode, f, "archive");
                }
            }
        }
    }

    private void deleteFromFolder(String clientCode, String propertyCode, File f, String incoming) {
        File incomingFolder = new File(f, incoming);
        if (incomingFolder.exists() && incomingFolder.isDirectory()) {
            deleteExtractsFrom(incomingFolder, clientCode, propertyCode);
        }
    }

    private void deleteExtractsFrom(File folder, String clientCode, String propertyCode) {
        File[] monthlyDirectories = folder.listFiles(new MonthFolderFileFilter());
        for (File monthlyDirectory : monthlyDirectories) {
            File propertyDirectory = new File(monthlyDirectory.getAbsolutePath() + "/" + clientCode + "/" + propertyCode);
            if (propertyDirectory.exists() && propertyDirectory.isDirectory()) {
                try {
                    FileUtils.deleteDirectory(propertyDirectory);
                } catch (IOException ioe) {
                    throw new TetrisException(ErrorCode.FILE_SYSTEM, "Unable to delete extracts from: " + propertyDirectory, ioe);
                }
            }
        }
    }
}
