package com.ideas.tetris.pacman.services.security.login.vo;

import com.fasterxml.jackson.annotation.JsonGetter;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class AuthorizedUserVO implements Serializable {
    private String authorization;
    private Map<Integer, PropertyVO> associatedPropertiesObjectMap;
    private Map<Integer, Set<ModulePermissionVO>> propertiesModulesPermissionsMap;
    private long auditId;
    private String userId;
    private String userName;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public AuthorizedUserVO() {
    }

    public AuthorizedUserVO(String authorization) {
        this.authorization = authorization;
    }

    public Map<Integer, Set<ModulePermissionVO>> getPropertiesModulesPermissionsMap() {
        return propertiesModulesPermissionsMap;
    }

    @JsonGetter("propertiesModulesPermissionsMap")
    public Map<String, Map<String, String>> getPropertiesModulesPermissionsMapping() {
        Map<String, Map<String, String>> map = new HashMap<>();
        for (Integer propertyId : propertiesModulesPermissionsMap.keySet()) {
            Set<ModulePermissionVO> modulePermissionVOS = propertiesModulesPermissionsMap.get(propertyId);
            if (CollectionUtils.isNotEmpty(modulePermissionVOS)) {
                Map<String, String> modulePermissionMap = modulePermissionVOS.stream().collect(Collectors.toMap(ModulePermissionVO::getModuleName, ModulePermissionVO::getModulePermission));
                map.put(propertyId.toString(), modulePermissionMap);
            }
        }
        return map;
    }

    public void setPropertiesModulesPermissionsMap(Map<Integer, Set<ModulePermissionVO>> propertiesModulesPermissionsMap) {
        this.propertiesModulesPermissionsMap = propertiesModulesPermissionsMap;
    }

    public void setAuthorization(String authorization) {
        this.authorization = authorization;
    }

    public String getAuthorization() {
        return authorization;
    }

    public Map<Integer, PropertyVO> getAssociatedPropertiesObjectMap() {
        return associatedPropertiesObjectMap;
    }

    public void setAssociatedPropertiesObjectMap(
            Map<Integer, PropertyVO> associatedPropertiesObjectMap) {
        this.associatedPropertiesObjectMap = associatedPropertiesObjectMap;
    }

    public long getAuditId() {
        return auditId;
    }

    public void setAuditId(long auditId) {
        this.auditId = auditId;
    }

}
