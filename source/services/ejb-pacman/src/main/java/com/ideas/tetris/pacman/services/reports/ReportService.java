package com.ideas.tetris.pacman.services.reports;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.infra.tetris.security.EncryptionDecryption;
import com.ideas.infra.tetris.security.LDAPException;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.IndividualTransactions;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateTimeParameter;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.reports.dto.ScheduleReportDTO;
import com.ideas.tetris.pacman.services.reports.entity.ScheduleReport;
import com.ideas.tetris.pacman.services.reports.enums.G3Report;
import com.ideas.tetris.pacman.services.reports.userreport.ScheduledReportFrequency;
import com.ideas.tetris.pacman.services.scheduledreport.PostBDEScheduleEnabledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ReportType;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.User;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.sftp.SFTPDetails;
import com.ideas.tetris.pacman.services.sftp.SSHJSftpService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.reports.ReportSource;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.log4j.Logger;
import org.hibernate.exception.ConstraintViolationException;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;
import javax.inject.Inject;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.rmi.RemoteException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.TextStyle;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.USE_SSHJ_SFTP_LIBRARY_FOR_SCHEDULED_REPORT;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DEFAULT_DATE_FORMAT_FOR_SCHEDULED_REPORTS;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class ReportService {
    private static final String REPORT_PARAM_VALUE_SEPERATOR = "=";
    public static final String SCHEDULE_PARAM_SEPARATOR = "##";
    static final String DATA_EXTRACTION_REPORT = "DATA_EXTRACTION";
    static final String PICKUP_AND_CHANGE_REPORT = "PICKUP_AND_CHANGE";
    private static final String DEFAULT_DATE = "1970010100000";
    private static final String PARAM_END_DATE = "param_EndDate";
    private static final String PARAM_START_DATE = "param_StartDate";
    public static final String END_DATE = "EndDate";
    public static final String START_DATE = "StartDate";
    private static final String OPERATIONS_REPORT = "OPERATIONS_REPORT";
    private static final String RESTRICTION_REPORT = "RESTRICTION_REPORT";
    private static final String INDIVIDUAL_GROUP_WASH = "INDIVIDUAL_GROUP_WASH";
    private static final String MEETING_PACKAGE_PRICING_REPORT = "MEETING_PACKAGE_PRICING_REPORT";
    private static final String SPECIAL_EVENTS = "SPECIAL_EVENTS";
    private static final String MCAT_REPORT = "MCAT_REPORT";
    private static final String INPUT_OVERRIDE = "INPUT_OVERRIDE";
    private static final String FORECAST_VALIDATION = "FORECAST_VALIDATION";
    private static final String SRP_PRODUCTION_REPORT = "SRP_PRODUCTION_REPORT";
    private static final String PRICING = "PRICING";
    private static final String PRICING_OVERRIDE_HISTORY = "PRICING_OVERRIDE_HISTORY";
    private static final String PERFORMANCE_COMPARISON_REPORT = "PERFORMANCE_COMPARISON_REPORT";
    private static final String OUTPUT_OVERRIDE = "OUTPUT_OVERRIDE";
    private static final String BOOKING_SITUATION_REPORT = "BOOKING_SITUATION_REPORT";
    private static final String PROPERTY_ID = "propertyId";
    private static final String ERROR_MESSAGE_SCHEDULE_REPORT_CONSTRAINT_VIOLATION_OCCURED = "Schedule Report - Constraint  Violation Occured ..";
    private static final String ERROR_MESSAGE_SCHEDULE_REPORT_REMOTING_EXCEPTION_OCCURED = "Schedule Report - Remoting Exception Occured ..";
    private static final String ERROR_MESSAGE_FAILED_TO_CONNECT_TO_FTP_SERVER = "Failed to connect to FTP Server";
    private static final String ERROR_MESSAGE_FAILED_TO_CONNECT_TO_SFTP_SERVER = "Failed to connect to SFTP Server";
    private static final String ERROR_MESSAGE_SCHEDULE_REPORT_ERROR_OCCURED = "Schedule Report - Error Occured ..";
    private static final String MESSAGE_NUMERIC_USER_ID_EXPECTED = "Here, an Integer userId was expected. A different string was passed.";
    private static final String ERROR_MESSAGE_SCHEDULE_REPORT_EXCEPTION_OCCURED = "Schedule Report - Exception Occured ..";
    private static final String FTP_PROTOCOL = "FTP";
    private static final String CHANNEL_TYPE = "sftp";
    private static final String SFTP_TEST_FILE_NAME = "TestFileForIDeaS-SFTP.txt";
    private static final String FTP_TEST_FILE_NAME = "TestFileForIDeaS-FTP.txt";
    private static final String TOTAL_SCHEDULES_FOUND = "Total schedules found = ";
    private static final String USER_ID_IN_CONTEXT = " This could be a normal situation. userId in context was -> ";
    private static final String BDE = "BDE";
    private static final String NONE = "- -";

    @Autowired
	protected UserService userService;

    @Autowired
	private JasperRESTExecutionService jasperRESTExecutionService;

    private static final Logger LOGGER = Logger.getLogger(ReportService.class.getName());
    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService globalCrudService;

    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;

    @Autowired
	private DateService dateServiceLocal;

    @Autowired
	private SSHJSftpService sshjSftpService;

    public void setDateServiceLocal(DateService dateServiceLocal) {
        this.dateServiceLocal = dateServiceLocal;
    }

    public DateService getDateServiceLocal() {
        return dateServiceLocal;
    }

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    @Autowired
    PropertyService propertyService;

    public List<String> getAllSRPNames() {
        return crudService.findByNamedQuery(IndividualTransactions.GET_RATE_CODES);

    }

    public List<ScheduleReportDTO> getAllSchedulesForVerifyingDuplicates(String locale) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        LOGGER.info("Fetching All schedules for property.");
        List<ScheduleReport> schedules = loadSchedulesByPropertyId(propertyId);
        LOGGER.info(TOTAL_SCHEDULES_FOUND + schedules.size());
        return convertToDTO(schedules, locale);
    }

    @SuppressWarnings("unchecked")



    public List<ScheduleReport> loadSchedulesByUserNames() {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        return crudService.findByNamedQuery(ScheduleReport.BY_UserName, QueryParameter.with("createdBy", workContext.getUserId()).parameters());
    }


    public List<ScheduleReportDTO> getSchedules( String locale) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        List<ScheduleReport> schedules = null;

        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);

        if (isCurrentUserCorporateFromGlobal(workContext.getUserId())) {
            LOGGER.info("Get schedules for Corporate user");
            schedules = loadSchedulesByPropertyId(propertyId);
        }
        else {
            LOGGER.info("Get schedules for normal user");
            boolean isAllScheduledReportsEnabledForPropertyUser = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_ALL_SCHEDULED_REPORTS_FOR_PROPERTY_USERS);
            boolean isRoleRankingEnabled = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_ROLE_RANKING);
            if(isRoleRankingEnabled && isAllScheduledReportsEnabledForPropertyUser) {
                schedules = crudService.findByNamedQuery(ScheduleReport.ALL, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
            } else if(!isRoleRankingEnabled && isAllScheduledReportsEnabledForPropertyUser) {
                schedules = crudService.findByNamedQuery(ScheduleReport.BY_UserName, QueryParameter.with("createdBy", workContext.getUserId()).parameters());
            } else if(isRoleRankingEnabled && !isAllScheduledReportsEnabledForPropertyUser) {
                schedules = crudService.findByNamedQuery(ScheduleReport.BY_UserName, QueryParameter.with("createdBy", workContext.getUserId()).parameters());
            } else {
                schedules = crudService.findByNamedQuery(ScheduleReport.BY_UserName, QueryParameter.with("createdBy", workContext.getUserId()).parameters());
            }
        }
        LOGGER.info(TOTAL_SCHEDULES_FOUND + schedules.size());
        Set<String> pages = getAccessiblePages(propertyId);
        List<ScheduleReport> accessibleSchedules = new ArrayList<>();

        if ((pages != null && pages.contains("-666")) || pages == null) {
            return convertToDTO(schedules, locale);
        } else {
            for (int i = 0; i < schedules.size(); i++) {
                ScheduleReport scheduleItem = schedules.get(i);
                if (pages.contains(scheduleItem.getPageCode())) {
                    accessibleSchedules.add(scheduleItem);
                }
            }
        }

        LOGGER.info("Final schedules found = " + accessibleSchedules.size());
        return convertToDTO(accessibleSchedules, locale);
    }

    public List<ScheduleReport> getAllSchedulesByReportType(ReportType reportType) {
        Set<String> actualReportNames = new HashSet<>();
        actualReportNames.add(PostBDEScheduleEnabledReport.forReportType(reportType.name()).getActualReportName());
        return crudService.findByNamedQuery(ScheduleReport.BY_ACTUAL_REPORT_NAMES, QueryParameter.with("actualReportNames", actualReportNames).parameters());
    }

    public List<ScheduleReport> getAllQualifiedSchedulesByReportType(ReportType reportType, Date businessDate,
                                                                     ReportSource reportSource) {
        List<ScheduleReport> allReports = crudService.findByNamedQuery(ScheduleReport.BY_REPORT_TYPE_BUSINESS_DATE,
                QueryParameter.with("reportType", PostBDEScheduleEnabledReport.forReportType(reportType.name())
                        .getActualReportName()).and("businessDate", businessDate).parameters());
        List<ScheduleReport> qualifiedReports = allReports.stream()
                .filter(scheduleReport -> isExecutionScheduleMatching(scheduleReport, reportSource))
                .filter(scheduleReport -> isReportToBeDelivered(scheduleReport, businessDate))
                .collect(Collectors.toList());
        LOGGER.info("Qualified report ids for report type '" + reportType + "' = " + qualifiedReports.stream()
                .map(ScheduleReport::getId).collect(Collectors.toList()));
        return qualifiedReports;
    }

    @VisibleForTesting
	public
    boolean isExecutionScheduleMatching(ScheduleReport scheduleReport, ReportSource reportSource) {
        String executionSchedule = scheduleReport.getExecutionSchedule();
        if (executionSchedule == null) {
            executionSchedule = Constants.BDE;
        }

        if (ReportSource.AFTER_BDE == reportSource) {
            return executionSchedule.toUpperCase().contains(Constants.BDE);
        } else if (ReportSource.AFTER_CDP == reportSource) {
            return executionSchedule.toUpperCase().contains(Constants.CDP);
        }

        return false;
    }

    private boolean isReportToBeDelivered(ScheduleReport scheduleReport, Date businessDate) {
        final LocalDate scheduleStartDate = DateUtil.convertJavaUtilDateToLocalDate(scheduleReport.getScheduleStartTime());
        final LocalDate currentBusinessDate = DateUtil.convertJavaUtilDateToLocalDate(businessDate);

        if (scheduleStartDate.isAfter(currentBusinessDate)) {
            return false;
        }

        ScheduledReportFrequency scheduledReportFrequency =
                ScheduledReportFrequency.forUnitName(scheduleReport.getRecurrenceUnit());

        if (ScheduledReportFrequency.DAYS == scheduledReportFrequency) {
            return isDailyFrequencyMatching(scheduleStartDate, currentBusinessDate, scheduleReport);
        } else {
            return isWeeklyFrequencyMatching(scheduleStartDate, currentBusinessDate, scheduleReport);
        }
    }

    private boolean isDailyFrequencyMatching(LocalDate scheduleStartDate, LocalDate now, ScheduleReport scheduleReport) {
        int frequency = scheduleReport.getGenerationFrequency();
        return ScheduledReportFrequency.DAYS.isScheduledReportApplicable(scheduleStartDate, now, frequency);
    }

    private boolean isWeeklyFrequencyMatching(LocalDate scheduleStartDate, LocalDate businessDate, ScheduleReport scheduleReport) {
        int frequency = scheduleReport.getGenerationFrequency();
        Set<DayOfWeek> validDaysOfWeek = getValidDaysOfWeekForScheduledReport(scheduleStartDate, scheduleReport.getDayOfWeek());
        if (!validDaysOfWeek.contains(getDayOfWeek(businessDate))) {
            return false;
        }

        if (frequency == 1) {
            return true;
        }

        LocalDate startOfWeekDateForScheduledStartDate = getStartOfWeek(scheduleStartDate);
        LocalDate startOfWeekDateForBDEDate = getStartOfWeek(businessDate);
        return ScheduledReportFrequency.WEEKS.isScheduledReportApplicable(startOfWeekDateForScheduledStartDate,
                startOfWeekDateForBDEDate, frequency);
    }

    public Set<DayOfWeek> getValidDaysOfWeekForScheduledReport(LocalDate scheduleStartDate, String dowValues) {
        Set<DayOfWeek> validDaysOfWeek;
        if (dowValues == null) {
            validDaysOfWeek = new HashSet<>();
            validDaysOfWeek.add(getDayOfWeek(scheduleStartDate));
        } else {
            validDaysOfWeek = Arrays.stream(dowValues.split(","))
                    .map(DayOfWeek::forShortCaption).collect(Collectors.toSet());
        }
        return validDaysOfWeek;
    }

    private DayOfWeek getDayOfWeek(LocalDate localDate) {
        return DayOfWeek.forShortCaption(localDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
    }

    private LocalDate getStartOfWeek(LocalDate localDate) {
        return localDate.with(ChronoField.DAY_OF_WEEK, java.time.DayOfWeek.MONDAY.getValue());
    }

    public boolean runScheduledReport(ScheduleReport scheduleReport, Date businessDate, ReportSource reportSource) {
        return jasperRESTExecutionService.callJasper(scheduleReport, businessDate, reportSource);
    }

    public List<ScheduleReport> loadSchedulesByPropertyId(Integer propertyId) {
        return crudService.findByNamedQuery(ScheduleReport.ALL, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    private boolean isCurrentUserCorporateFromGlobal(String userId) {
        // Do not use PacmanThreadLocalContextHolder getPrincipal isCorporate as that can be cached
        boolean isCorporate = false;
        // If user internal, automatically corporate (backwards compatibility)
        GlobalUser globalUser = globalCrudService.find(GlobalUser.class, Integer.parseInt(userId));

        if (globalUser != null) {
            isCorporate = globalUser.isCorporate() || globalUser.isInternal();
        }

        return isCorporate;
    }

    private Set<String> getAccessiblePages(Integer propertyId) {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        Set<String> pages = null;
        try {
            if (userService != null) {
                pages = userService.getAuthorizedPagesForProperty(workContext.getUserId(), PacmanWorkContextHelper.getPropertyId().toString());
            }
        } catch (LDAPException e) {
            LOGGER.error("Unable to get authorized pages for Property: " + propertyId, e);
        }
        return pages;

    }




    public ScheduleReportDTO getSchedule( int id) {
        TimeZone propertyTimeZoneDetails = null;
        propertyTimeZoneDetails = dateServiceLocal.getPropertyTimeZone();
        ScheduleReport schedule = loadScheduleById(id);
        return getScheduleReportDTO(schedule, propertyTimeZoneDetails, null);
    }

    public ScheduleReport loadScheduleById(int id) {
        return crudService.findByNamedQuerySingleResult(ScheduleReport.BY_SCHEDULE_ID, QueryParameter.with("id", id).parameters());
    }




    public void deleteSchedule( int id) {
        ScheduleReport schedule = loadScheduleById(id);
        deleteSchedule(schedule);
    }

    public void deleteSchedule(ScheduleReport schedule) {
        if (schedule == null) {
            return;
        }
        deleteScheduleFromDB(schedule);
    }

    protected List<ScheduleReportDTO> convertToDTO(List<ScheduleReport> schedules, String locale) {
        List<ScheduleReportDTO> scheduleDTO = new ArrayList<>();
        TimeZone propertyTimeZoneDetails = dateServiceLocal.getPropertyTimeZone();

        for (ScheduleReport scheduleReport : schedules) {
            scheduleDTO.add(getScheduleReportDTO(scheduleReport, propertyTimeZoneDetails, locale));
        }
        return scheduleDTO;
    }

    private ScheduleReportDTO getScheduleReportDTO(ScheduleReport scheduleReport, TimeZone propertyTimeZone, String locale) {
        SimpleDateFormat df = new SimpleDateFormat("MM/dd/yyyy HH:mm ");
        if (locale != null) {
            df = new SimpleDateFormat(dateServiceLocal.getUserPreferredDateFormat() + " HH:mm ", new Locale(locale.split("_")[0]));
        }

        ScheduleReportDTO dto = new ScheduleReportDTO();
        dto.setId(scheduleReport.getId());
        dto.setActualReportName(scheduleReport.getActualReportName());
        dto.setRecipientMail(scheduleReport.getEmailRecipients());
        dto.setRecurrenceInterval(scheduleReport.getGenerationFrequency());
        dto.setRecurrenceIntervalUnit(scheduleReport.getRecurrenceUnit());
        dto.setReportJasperURI(scheduleReport.getJasperReportURI());
        dto.setReportParamsObject(scheduleReport.getRportParameters());
        dto.setPagecode(scheduleReport.getPageCode());
        dto.setDescription(scheduleReport.getDescription());
        dto.setName(scheduleReport.getName());
        dto.setCreatedBy(getUserName(scheduleReport.getCreatedByUserId()));
        dto.setCreatedByUserId(scheduleReport.getCreatedByUserId());
        String updatedBy = getUserName(scheduleReport.getLastUpdatedByUserId());
        dto.setUpdatedBy(updatedBy != null ? updatedBy : "-");
        dto.setCreatedOn(scheduleReport.getCreateDate() != null ? df.format(LocalDateUtils.toDate(scheduleReport.getCreateDate())) : "-");
        dto.setScheduleStartDateTime(scheduleReport.getScheduleStartTime() != null ? df.format(convertDateToPropertyTimezone(scheduleReport.getScheduleStartTime(), propertyTimeZone)) : "-");
        dto.setExecutionSchedule(getDeliverMode(scheduleReport));
        dto.setUpdatedOn(scheduleReport.getLastUpdatedDate() != null ? df.format(convertDateToPropertyTimezone(LocalDateUtils.toDate(scheduleReport.getLastUpdatedDate()), propertyTimeZone)) + " " + getTimeZoneString(LocalDateUtils.toDate(scheduleReport.getLastUpdatedDate()), propertyTimeZone) : "-");
        dto.setLastRunDate(scheduleReport.getLastRunDate() != null ? df.format(scheduleReport.getLastRunDate()) : "-");
        dto.setNextRunDate(scheduleReport.getNextRunDate() != null ? df.format(scheduleReport.getNextRunDate()) : "-");
        dto.setJasperScheduleID(scheduleReport.getJasperScheduleID());
        dto.setPropertyTimezone(getTimeZoneString(scheduleReport.getScheduleStartTime(), propertyTimeZone));
        dto.setFtpServerName(scheduleReport.getFtpServer());
        dto.setFtpFolderPath(scheduleReport.getFtpFolderPath());
        dto.setFtpUserName(scheduleReport.getFtpUserName());
        if (null != scheduleReport.getFtpPassword() && !"".equals(scheduleReport.getFtpPassword())) {
            try {
                String decryptedPassword = EncryptionDecryption.decode(scheduleReport.getFtpPassword());
                dto.setFtpPassword(decryptedPassword);
            } catch (IOException ioe) {
                LOGGER.debug("Error While decoding FTP/SFTP password", ioe);
                dto.setFtpPassword(scheduleReport.getFtpPassword());
            }
        } else {
            dto.setFtpPassword(scheduleReport.getFtpPassword());
        }
        dto.setFtpType(scheduleReport.getFtpType());
        dto.setFtpPort(scheduleReport.getFtpPort() == null ? 0 : scheduleReport.getFtpPort());

        if (scheduleReport.getEmailDelivery() == null) {
            dto.setEmailDelivery(true);
        } else {
            dto.setEmailDelivery(scheduleReport.getEmailDelivery());
        }
        dto.setDayOfWeek(scheduleReport.getDayOfWeek());
        return dto;
    }

    protected String getDeliverMode(ScheduleReport scheduleReport) {
        if (scheduleReport.getExecutionSchedule() != null) {
            return scheduleReport.getExecutionSchedule();
        }
        return NONE;
    }

    private String getTimeZoneString(Date date, TimeZone timeZone) {
        boolean isPropertyInDayLightSavingForThisDate;
        if (null == timeZone) {
            return "";
        } else {
            isPropertyInDayLightSavingForThisDate = timeZone.inDaylightTime(date);
            return timeZone.getDisplayName(isPropertyInDayLightSavingForThisDate, 0);
        }
    }


    public ScheduleReport populateScheduleDb(ScheduleReportDTO schedule, long jasperReportID) {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        int userId = Integer.parseInt(workContext.getUserId());
        ScheduleReport scheduleData = new ScheduleReport();

        if (schedule.getId() == 0) {
            scheduleData.setCreatedByUserId(userId);
            scheduleData.setCreateDate(new Date());
            scheduleData.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        } else {
            scheduleData = loadScheduleById(schedule.getId());
            scheduleData.setLastUpdatedByUserId(userId);
            scheduleData.setLastUpdatedDate(new Date());
        }

        assignBasicScheduleInfo(schedule, jasperReportID, scheduleData);

        return saveSchedule(scheduleData);

    }

    protected String getUserName(Integer userId) {
        String userName = "-";
        if (userId != null) {
            try {
                User user = crudService.find(User.class, userId);
                if (user != null) {
                    userName = user.getName();
                }
            } catch (NumberFormatException e) {
                LOGGER.warn(
                        MESSAGE_NUMERIC_USER_ID_EXPECTED +
                                USER_ID_IN_CONTEXT + userId);
            }
        }
        return userName;
    }

    protected String getUserEmail(String userId) {
        String userEmail = null;
        try {
            User user = crudService.find(User.class, Integer.parseInt(userId));
            if (user != null) {
                userEmail = user.getEmail();
            }
        } catch (NumberFormatException e) {
            LOGGER.warn(
                    MESSAGE_NUMERIC_USER_ID_EXPECTED +
                            USER_ID_IN_CONTEXT + userId);
        }
        return userEmail;
    }


    protected String getGlobalUserEmail(String userId) {
        String userEmail = null;
        try {
            GlobalUser globalUser = globalCrudService.find(GlobalUser.class, Integer.parseInt(userId));
            if (globalUser != null) {
                userEmail = globalUser.getEmail();
            }
        } catch (NumberFormatException e) {
            LOGGER.warn(
                    MESSAGE_NUMERIC_USER_ID_EXPECTED +
                            USER_ID_IN_CONTEXT + userId);
        }
        return userEmail;
    }

    /**
     * Saves schedule, determines whether to call create or update based on the existence of an ID.
     *
     * @param schedule
     */
    public ScheduleReport saveSchedule(ScheduleReport schedule) {
        return crudService.save(schedule);
    }

    /**
     * deletes schedule from pacman db
     *
     * @param schedule
     */
    public void deleteScheduleFromDB(ScheduleReport schedule) {
        // 0 ID means that the schedule needs to be created
        if (schedule != null) {
            crudService.delete(schedule);
        }
    }


    private String getReportParameters(ScheduleReportDTO schedule) {
        StringBuilder params = new StringBuilder();

        Map<String, String> hm = (Map<String, String>) schedule.getReportParamsObject();
        Set set = hm.entrySet();
        Iterator i = set.iterator();

        while (i.hasNext()) {
            Map.Entry me = (Map.Entry) i.next();
            params.append(me.getKey().toString() + REPORT_PARAM_VALUE_SEPERATOR);
            if (reportParamContainesOnlyDateObject(me)) {
                params.append(me.getValue() != null ? getFormattedDate(me.getValue().toString()) : DEFAULT_DATE);
            } else {
                params.append(me.getValue());
            }
            params.append(SCHEDULE_PARAM_SEPARATOR);

        }
        return params.toString();
    }

    public String convertParamMapToString(Map<String, String> reportParams, String seperator) {
        Set<String> keys = reportParams.keySet();
        ArrayList<String> list = new ArrayList<>(keys);
        Collections.sort(list);

        ArrayList<String> keyValueList = new ArrayList<>();
        for (String key : list) {
            StringBuilder params = new StringBuilder();
            params.append(key);
            params.append(REPORT_PARAM_VALUE_SEPERATOR);
            if (isDateParam(key)) {
                params.append(reportParams.get(key).replace("-", ""));
            } else {
                params.append(reportParams.get(key));
            }
            keyValueList.add(params.toString());
        }
        return String.join(seperator, keyValueList);
    }

    public boolean isDateParam(String key) {
        return key.equalsIgnoreCase(PARAM_START_DATE) || key.equalsIgnoreCase(PARAM_END_DATE)
                || "param_Business_StartDate".equalsIgnoreCase(key) || "param_Business_EndDate".equalsIgnoreCase(key)
                || key.equalsIgnoreCase(START_DATE) || key.equalsIgnoreCase(END_DATE);
    }


    private boolean reportParamContainesOnlyDateObject(Map.Entry me) {
        return me.getKey().toString().contains("Date") && !me.getKey().toString().contains("Rolling") && !me.getKey().toString().contains("userDateFormat") && !me.getKey().toString().contains("param_calculatePaceFromEndDate");
    }

    private Map<String, String> getUIParameters(String params) {
        Map<String, String> hm = new HashMap<>();
        String[] parameter = params.split("##");

        for (String p : parameter) {
            //key  = value
            String[] keyValue = p.split(REPORT_PARAM_VALUE_SEPERATOR);
            hm.put(keyValue[0], keyValue[1]);
        }

        return hm;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)

    public void updateScheduleRecipientMailForInactiveUser( String clientCode) {
        //get all inactive user from tenant db
        List<Integer> inactiveUsers = getInactiveUsers(clientCode);
        //loop those user id and remove email id from the schedules
        for (Integer globalUserId : inactiveUsers) {
            removeMailIdFromSchedule(globalUserId.toString());
        }
    }

    protected List<Integer> getInactiveUsers(String clientCode) {
        //get all inactive users using globalCrudService
        String query = " select user_id from Users where Status_ID = 2 and Client_Code =  '" + clientCode + "'";
        return globalCrudService.findByNativeQuery(query);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void removeMailIdFromSchedule(String uid) {
        String deactiveEmail = getGlobalUserEmail(uid);
        List<ScheduleReport> schedules = getSchduleIdsFromPacmanDB(deactiveEmail);
        updateTenantDB(deactiveEmail, schedules);
        LOGGER.info("Successful update of email recipient for user id " + uid);
    }

    private void updateTenantDB(String deactiveEmail, List<ScheduleReport> schedules) {
        String[] toAddressUpdated;
        for (ScheduleReport schedule : schedules) {
            toAddressUpdated = removeEmailFromMailRecipiant(deactiveEmail, schedule.getEmailRecipients());
            schedule.setEmailRecipients(convertArrayTOString(toAddressUpdated));
            updateEmailRecipientFromPacmanDB(schedule, schedule.getEmailRecipients());
        }
    }

    private String convertArrayTOString(String[] toAddressUpdated) {

        StringBuilder sb = new StringBuilder();

        for (String email : toAddressUpdated) {
            sb.append(email).append(",");
        }

        sb.deleteCharAt(sb.lastIndexOf(","));

        return sb.toString();

    }

    protected void updateEmailRecipientFromPacmanDB(ScheduleReport scheduleReport, String newRecepients) {
        if (newRecepients.isEmpty()) {
            multiPropertyCrudService.delete(scheduleReport.getPropertyId(), ScheduleReport.class, scheduleReport.getId());
        } else {
            multiPropertyCrudService.executeNativeDeleteOnSingleProperty(scheduleReport.getPropertyId(), ScheduleReport.UPDATE_EMAIL_RECIPIENTS, QueryParameter.with("newEmailId", newRecepients).and("scheduleId", scheduleReport.getId()).parameters());
        }
    }

    protected String[] removeEmailFromMailRecipiant(String deactiveEmail, String emailRecipients) {

        String emails = emailRecipients.replace(deactiveEmail, "");
        emails = emails.replace(",,", ",");

        if (emails.length() > 0 && !emails.isEmpty()) {

            if (emails.endsWith(",")) {
                emails = emails.substring(0, emails.length() - 1);
            }

            if (emails.startsWith(",")) {
                emails = emails.substring(1);
            }
        }

        return emails.split(",");
    }

    private List<ScheduleReport> getSchduleIdsFromPacmanDB(String deactiveEmail) {
        List<Integer> propertyIDs = propertyService.getPropertyIdsForClient(PacmanWorkContextHelper.getClientId());

        if (propertyIDs.isEmpty()) {
            return new ArrayList<>();
        }

        String query = " select * from ScheduledReports where EmailRecipients like '%" + deactiveEmail + "%'";

        try {
            return multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(propertyIDs, query, null, ScheduleReport.class);

        } catch (Exception e) {
            LOGGER.warn("No result found for Email Recipient - " + deactiveEmail, e);
            return new ArrayList<>();
        }
    }

    public void scheduleReport(ScheduleReportDTO schedule) {
        try {
            LOGGER.info("Trying to Save the report");
            tryScheduleReport(schedule);
            LOGGER.info("Report Saved Successfully");
        } catch (ConstraintViolationException c) {
            LOGGER.error(ERROR_MESSAGE_SCHEDULE_REPORT_CONSTRAINT_VIOLATION_OCCURED, c);
            throw new TetrisException(ErrorCode.DUPLICATE_DATA, ERROR_MESSAGE_SCHEDULE_REPORT_CONSTRAINT_VIOLATION_OCCURED, c);
        } catch (RemoteException re) {
            LOGGER.error(ERROR_MESSAGE_SCHEDULE_REPORT_REMOTING_EXCEPTION_OCCURED, re);
            if (re.getMessage().indexOf("The start date is in the past") > -1) {
                throw new TetrisException(ErrorCode.SERVICE_ERROR, "Schedule Start time should be in future.", re);
            } else if (re.getMessage().indexOf(ERROR_MESSAGE_FAILED_TO_CONNECT_TO_FTP_SERVER) > -1) {
                throw new TetrisException(ErrorCode.SERVICE_ERROR, ERROR_MESSAGE_FAILED_TO_CONNECT_TO_FTP_SERVER, re);
            }
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, ERROR_MESSAGE_SCHEDULE_REPORT_REMOTING_EXCEPTION_OCCURED, re);
        } catch (Exception e) {
            LOGGER.error(ERROR_MESSAGE_SCHEDULE_REPORT_EXCEPTION_OCCURED, e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, ERROR_MESSAGE_SCHEDULE_REPORT_ERROR_OCCURED, e);
        }
    }

    private void tryScheduleReport(ScheduleReportDTO schedule) throws RemoteException {
        long newJobId = -1;
        convertScheduleTimeToServerTime(schedule);
        if (Boolean.TRUE.equals(schedule.getEmailDelivery())) {
            LOGGER.info("Validating Email delivery.");
            scheduleReportEmail(schedule);
        } else {
            LOGGER.info("Validating FTP/SFTP delivery.");
            scheduleReportNonEmail(schedule);
        }
        LOGGER.info("Saving to Pacman");
        populateScheduleDb(schedule, newJobId);
        LOGGER.info("Saved to Pacman successfully");
    }

    private void scheduleReportEmail(ScheduleReportDTO schedule) {
        verifyDuplicateSchedule(schedule);
    }

    private void scheduleReportNonEmail(ScheduleReportDTO schedule) throws RemoteException {
        boolean validConnection;
        String errorMessage;
        FileTransferDetails ftpInfo = new FileTransferDetails();
        ftpInfo.setFolderPath(schedule.getFtpFolderPath());
        ftpInfo.setServerName(schedule.getFtpServerName());
        ftpInfo.setUserName(schedule.getFtpUserName());
        ftpInfo.setPassword(schedule.getFtpPassword());
        ftpInfo.setPort(schedule.getFtpPort());
        if (FTP_PROTOCOL.equals(schedule.getFtpType())) {
            LOGGER.info("Validating FTP Connection");
            validConnection = testFTPConnection(ftpInfo);
            errorMessage = ERROR_MESSAGE_FAILED_TO_CONNECT_TO_FTP_SERVER;
        } else {
            LOGGER.info("Validating SFTP Connection");
            validConnection = testSFTPConnection(ftpInfo);
            errorMessage = ERROR_MESSAGE_FAILED_TO_CONNECT_TO_SFTP_SERVER;
        }
        if (!validConnection) {
            LOGGER.info("Failed to validate the connection.");
            throw new RemoteException(errorMessage);
        }
        verifyDuplicateSchedule(schedule);
    }

    private boolean testFTPConnection(FileTransferDetails outputFTPInfo) {
        FTPClient ftpClient = new FTPClient();
        try {
            ftpClient.connect(outputFTPInfo.getServerName(), outputFTPInfo.getPort());
            int replyCode = ftpClient.getReplyCode();
            if (!FTPReply.isPositiveCompletion(replyCode)) {
                return false;
            }
            boolean success = ftpClient.login(outputFTPInfo.getUserName(), outputFTPInfo.getPassword());
            if (!success) {
                return false;
            } else {
                String folderPath = outputFTPInfo.getFolderPath() != null ? outputFTPInfo.getFolderPath() : "";
                boolean hasWriteAccess = false;
                if (ftpClient.changeWorkingDirectory(folderPath)) {
                    ftpClient.enterLocalPassiveMode();
                    try (OutputStream fos = ftpClient.storeFileStream(FTP_TEST_FILE_NAME)) {
                        fos.write(1234);
                    }
                    hasWriteAccess = true;
                    ftpClient.deleteFile(FTP_TEST_FILE_NAME);
                }
                ftpClient.logout();
                ftpClient.disconnect();
                return hasWriteAccess;
            }
        } catch (IOException ex) {
            LOGGER.error("Unable to establish connection with the FTP server", ex);
            return false;
        }
    }

    @VisibleForTesting
	public
    boolean testSFTPConnection(FileTransferDetails outputFTPInfo) {
        if (pacmanConfigParamsService.getBooleanParameterValue(USE_SSHJ_SFTP_LIBRARY_FOR_SCHEDULED_REPORT)) {
            return sshjSftpService.testSFTPConnectionWithSSHJ(SFTP_TEST_FILE_NAME, new SFTPDetails(outputFTPInfo.getServerName(), outputFTPInfo.getPort(), outputFTPInfo.getUserName(), EncryptionDecryption.encode(outputFTPInfo.getPassword()), outputFTPInfo.getFolderPath()));
        }
        return testSFTPConnectionWithJsch(outputFTPInfo);
    }

    @VisibleForTesting
	public
    boolean testSFTPConnectionWithJsch(FileTransferDetails outputFTPInfo) {
        ChannelSftp sftpChannel = null;
        Session session = null;
        try {
            String directoryPath = outputFTPInfo.getFolderPath();
            if (directoryPath == null || directoryPath.equals(StringUtils.EMPTY)) {
                throw new IllegalArgumentException("Remote Directory Path not provided.Please provide correct directory path");
            }
            session = getSFPSession(outputFTPInfo);
            sftpChannel = getSFTPChannel(session);
            changeDirectory(directoryPath, sftpChannel);
            putFile(sftpChannel, SFTP_TEST_FILE_NAME);
            deleteFile(sftpChannel, SFTP_TEST_FILE_NAME);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Unable to establish connection with the SFTP server.", ex);
            return false;
        } finally {
            if (sftpChannel != null) {
                sftpChannel.exit();
            }
            if (session != null) {
                session.disconnect();
            }
            LOGGER.debug("Connection disconnected successfully.");
        }
    }

    private ChannelSftp getSFTPChannel(Session session) throws JSchException {
        Channel channel = session.openChannel(CHANNEL_TYPE);
        channel.connect();
        LOGGER.debug("SFTP channel has been opened successfully.");
        return (ChannelSftp) channel;
    }

    private Session getSFPSession(FileTransferDetails outputFTPInfo) throws JSchException {
        JSch jsch = new JSch();
        String userName = outputFTPInfo.getUserName();
        String host = outputFTPInfo.getServerName();
        Integer port = outputFTPInfo.getPort();

        Session session = jsch.getSession(userName, host, port);
        String password = String.valueOf(outputFTPInfo.getPassword());
        if (password != null) {
            session.setPassword(password);
        }
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();
        LOGGER.debug("Connection established with the SFTP server.");
        return session;
    }

    private void deleteFile(ChannelSftp sftpChannel, String fileName) throws SftpException {
        sftpChannel.rm(fileName);
    }

    private void changeDirectory(String directoryPath, ChannelSftp sftpChannel) throws SftpException {
        if (sftpChannel == null) {
            throw new IllegalArgumentException("Please connect to FTP server first before changing directory!");
        }
        if (directoryPath == null || directoryPath.equals(StringUtils.EMPTY)) {
            return;
        }
        sftpChannel.cd(directoryPath);
    }

    private void putFile(ChannelSftp sftpChannel, String fileName) throws SftpException {
        ByteArrayInputStream inputData = new ByteArrayInputStream(RandomStringUtils.randomAlphanumeric(5).getBytes());
        sftpChannel.put(inputData, sftpChannel.pwd() + "/" + fileName);
        LOGGER.debug("Test file successfully create in the remote directory");
    }


    private void convertScheduleTimeToServerTime(ScheduleReportDTO schedule) {
        Date scheduleStartDate = DateUtil.getDateTimeByTimeZone(schedule.getScheduleCalStartDateTime().getTime(), TimeZone.getTimeZone(Calendar.getInstance().getTimeZone().getID()), dateServiceLocal.getPropertyTimeZone());
        schedule.setScheduleCalStartDateTime(new DateTimeParameter(scheduleStartDate));
        schedule.setScheduleStartDateTime(scheduleStartDate.toString());
    }

    private Date convertDateToPropertyTimezone(Date date, TimeZone propertyTimeZone) {
        return DateUtil.getDateTimeByTimeZone(date, propertyTimeZone);
    }

    @VisibleForTesting
	public
    void verifyDuplicateSchedule(ScheduleReportDTO schedule) {
        LOGGER.info("Verifying Duplicate Schedules");
        String scheduleName = schedule.getName();
        int currentScheduleReportId = schedule.getId();
        List<ScheduleReportDTO> schedules = getAllSchedulesForVerifyingDuplicates(null);
        for (ScheduleReportDTO sch : schedules) {
            if (sch.getName().trim().equalsIgnoreCase(scheduleName.trim()) && sch.getId() != currentScheduleReportId) {
                LOGGER.info("Duplicate report name " + sch.getName() + " for schedule report id = " + sch.getId());
                throw new TetrisException(ErrorCode.DUPLICATE_DATA, ERROR_MESSAGE_SCHEDULE_REPORT_CONSTRAINT_VIOLATION_OCCURED);
            }
        }
    }


    private Calendar getScheduleTime(ScheduleReportDTO schedule) {
        if (Boolean.TRUE.equals(schedule.getStartScheduleForNow())) {
            return Calendar.getInstance();
        } else {
            Calendar cal1 = Calendar.getInstance();
            cal1.setTime(schedule.getScheduleCalStartDateTime().getTime());
            return cal1;
        }

    }

    //helper
    private Date getFormattedDate(String date) {
        try {
            return new SimpleDateFormat("yyyyMMdd").parse(date.substring(0, 8));
        } catch (ParseException e) {
            LOGGER.error("Unable to parse date: " + date, e);
        }
        return null;
    }

    public String getJNDINameString(int propertyId) {
        String len = propertyId + "";
        StringBuilder jndi = new StringBuilder("");
        for (int i = len.length(); i < 6; i++) {
            jndi.append("0");
        }
        jndi.append(propertyId);
        return jndi.toString();
    }

    /**
     * @return the crudService
     */
    public CrudService getCrudService() {
        return crudService;
    }

    /**
     * @param crudService the crudService to set
     */
    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    public void setPropertyService(PropertyService propertyService) {
        this.propertyService = propertyService;
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public void setMultiPropertyCrudService(AbstractMultiPropertyCrudService multiPropertyCrudService) {
        this.multiPropertyCrudService = multiPropertyCrudService;
    }


    /*
     * Rest API for access the schedule report
     * add and delete the dummy schedules.
     * delete the unused schedules
     *
     * */


    @Transactional(propagation = Propagation.NOT_SUPPORTED)


    public void scheduleReportOnMultipleProperties( int clientId,  int schNumber,  int startNumber,  String emails) {

        if (null == emails) {
            LOGGER.error("Email list is null or empty");
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "Email list is null or empty ..");
        }

        if (0 == schNumber) {
            schNumber = 10;
        }

        LOGGER.info("Client id >>>>>>>>>>" + clientId);

        PacmanWorkContextHelper.setClientId(clientId);
        List<Integer> propertyIDs = propertyService.getPropertyIdsForClient(clientId);

        LOGGER.info("Property id size >>>>>>>>>>" + propertyIDs.size());

        for (Integer propertyID : propertyIDs) {
            LOGGER.info("property id >>>>>>>>>>" + propertyID);
            try {
                scheduleReportOnProperty(schNumber, emails, propertyID);
            } catch (ConstraintViolationException c) {
                LOGGER.error(ERROR_MESSAGE_SCHEDULE_REPORT_CONSTRAINT_VIOLATION_OCCURED, c);
                throw new TetrisException(ErrorCode.DUPLICATE_DATA, ERROR_MESSAGE_SCHEDULE_REPORT_CONSTRAINT_VIOLATION_OCCURED, c);
            } catch (Exception e) {
                LOGGER.error(ERROR_MESSAGE_SCHEDULE_REPORT_EXCEPTION_OCCURED, e);
                throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, ERROR_MESSAGE_SCHEDULE_REPORT_ERROR_OCCURED, e);
            }
        }
    }

    private void scheduleReportOnProperty(int schNumber, String emails, Integer propertyID) {
        ScheduleReportDTO schedule = new ScheduleReportDTO();
        String format = String.format("%%0%dd", 6);

        for (int postFix = 0; postFix < schNumber; postFix++) {
            Map parameters = new HashMap();
            parameters.put("param_Property_ID", propertyID);
            parameters.put("JNDI_NAME", String.format(format, propertyID));
            parameters.put(PARAM_START_DATE, "20120925");
            parameters.put(PARAM_END_DATE, "20120903");
            parameters.put("param_IsDemandOvrideByOccDt", true);
            parameters.put("param_IsDemandOvrideByArrDtAndLOS", true);
            parameters.put("param_IsWashOverride", true);
            parameters.put("param_isGffOverrideFetch", true);
            parameters.put("param_IsNotesChecked", true);
            parameters.put("param_isRollingDate", "1");
            parameters.put("param_Rolling_Start_Date", "today");
            parameters.put("param_Rolling_End_Date", "today");
            parameters.put("IS_IGNORE_PAGINATION", true);
            parameters.put("userLocale", "en_US");
            parameters.put("param_User_ID", "11403");
            parameters.put("param_SheetForCriteria", false);
            parameters.put("isRestrictHighestBarEnabled", "false");
            parameters.put("isContinuousPricingEnabled", false);

            //Output formats e.g xls,pdf,html,docx,doc
            ArrayList<String> outPutFormats = new ArrayList<>();
            outPutFormats.add("XLSX");

            //Report schedule parameters (right pane parametes)
            int num = postFix + schNumber;
            schedule.setName("sch_InputOverrideReport" + num);
            schedule.setDescription("sch_InputOverrideReport_" + num);
            schedule.setActualReportName(INPUT_OVERRIDE);
            schedule.setReportJasperURI("InputOverrideReport");
            schedule.setOutputFormat(outPutFormats);
            schedule.setReportParamsObject(parameters);
            schedule.setPagecode("input-override-report");
            schedule.setEmailDelivery(true);
            schedule.setFtpType("FTP");
            schedule.setExecutionSchedule(BDE);
            Calendar cal = Calendar.getInstance();
            cal.set(2022, 07, 24, 12, 45);

            schedule.setScheduleCalStartDateTime(new DateTimeParameter(cal.getTime()));

            setRecipientMail(emails, postFix, schedule);

            schedule.setRecurrenceInterval(1);
            schedule.setRecurrenceIntervalUnit("Days");
            schedule.setMailContent("This is Test for adding multiple schedule rest call, you will get different msg during actual business flow");
            populateScheduleforTenantDB(schedule, 0, propertyID);
        }
    }

    private void setRecipientMail(String emails, int postFix, ScheduleReportDTO schedule) {
        String[] recipientEmail = null;
        if (!emails.isEmpty()) {
            recipientEmail = emails.split("~");
        } else {
            recipientEmail = new String[3];
            recipientEmail[0] = "<EMAIL>";
            recipientEmail[1] = "<EMAIL>,<EMAIL>";
            recipientEmail[2] = "<EMAIL>,<EMAIL>,<EMAIL>";
        }
        if (postFix == 0) {
            schedule.setRecipientMail(recipientEmail[0]);
        } else if (postFix == 1) {
            schedule.setRecipientMail(recipientEmail[1]);
        } else {
            schedule.setRecipientMail(recipientEmail[2]);
        }
    }

    private ScheduleReport populateScheduleforTenantDB(ScheduleReportDTO schedule, long jasperReportID, int propertyID) {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        int userId = Integer.parseInt(workContext.getUserId());
        ScheduleReport scheduleData = new ScheduleReport();

        if (schedule.getId() == 0) {
            scheduleData.setCreatedByUserId(userId);
            scheduleData.setCreateDate(new Date());
            scheduleData.setPropertyId(propertyID);
        } else {
            scheduleData = (ScheduleReport) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyID, ScheduleReport.BY_SCHEDULE_ID, QueryParameter.with("id", schedule.getId()).parameters());
            scheduleData.setLastUpdatedByUserId(userId);
            scheduleData.setLastUpdatedDate(new Date());
        }

        assignBasicScheduleInfo(schedule, jasperReportID, scheduleData);
        saveScheduleforTenatDB(scheduleData, propertyID);
        return scheduleData;

    }

    private void assignBasicScheduleInfo(ScheduleReportDTO schedule, long jasperReportID, ScheduleReport scheduleData) {
        scheduleData.setActualReportName(schedule.getActualReportName());
        scheduleData.setName(schedule.getName());
        scheduleData.setDescription(schedule.getDescription());
        scheduleData.setGenerationFrequency(schedule.getRecurrenceInterval());
        scheduleData.setRecurrenceUnit(schedule.getRecurrenceIntervalUnit());
        scheduleData.setEmailRecipients(schedule.getRecipientMail());
        scheduleData.setJasperReportURI(schedule.getReportJasperURI());
        scheduleData.setJasperScheduleID(jasperReportID);
        scheduleData.setRportParameters(getReportParameters(schedule));
        scheduleData.setScheduleStartTime(getScheduleTime(schedule).getTime());
        scheduleData.setPageCode(schedule.getPagecode());
        scheduleData.setFtpServer(schedule.getFtpServerName());
        scheduleData.setFtpFolderPath(schedule.getFtpFolderPath());
        scheduleData.setFtpUserName(schedule.getFtpUserName());
        if (null != schedule.getFtpPassword() && !"".equals(schedule.getFtpPassword())) {
            String encryptedPassword = EncryptionDecryption.encode(schedule.getFtpPassword());
            scheduleData.setFtpPassword(encryptedPassword);
        } else {
            scheduleData.setFtpPassword(schedule.getFtpPassword());
        }
        scheduleData.setFtpType(schedule.getFtpType());
        scheduleData.setFtpPort(schedule.getFtpPort());

        scheduleData.setEmailDelivery(schedule.getEmailDelivery());
        scheduleData.setDayOfWeek(schedule.getDayOfWeek());
        scheduleData.setExecutionSchedule(schedule.getExecutionSchedule());
        scheduleData.setOutputFormat("XLSX");
    }

    /**
     * Saves schedule, determines whether to call create or update based on the existence of an ID.
     *
     * @param schedule
     */
    public void saveScheduleforTenatDB(ScheduleReport schedule, Integer propertyID) {
        multiPropertyCrudService.save(propertyID, schedule);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)


    public void deleteJasperScheduleForTenant( int clientId) {
        List<ScheduleReport> schedules = null;
        PacmanWorkContextHelper.setClientId(clientId);
        List<Integer> propertyIDs = propertyService.getPropertyIdsForClient(PacmanWorkContextHelper.getClientId());

        for (Integer propertyID : propertyIDs) {
            schedules = loadSchedulesByPropertyIdMultiPropertyCrud(propertyID);
            for (ScheduleReport schedule : schedules) {
                deleteJasperSchedule(schedule, propertyID);
            }
        }
    }

    private void deleteJasperSchedule(ScheduleReport schedule, Integer propertyID) {
        try {
            if ((schedule.getName()).contains("sch_InputOverrideReport")) {
                deleteScheduleFromTenantDB(schedule, propertyID);
            }
        } catch (ConstraintViolationException c) {
            LOGGER.error(ERROR_MESSAGE_SCHEDULE_REPORT_CONSTRAINT_VIOLATION_OCCURED, c);
            throw new TetrisException(ErrorCode.DUPLICATE_DATA, ERROR_MESSAGE_SCHEDULE_REPORT_CONSTRAINT_VIOLATION_OCCURED, c);
        } catch (Exception e) {
            LOGGER.error(ERROR_MESSAGE_SCHEDULE_REPORT_EXCEPTION_OCCURED, e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, ERROR_MESSAGE_SCHEDULE_REPORT_ERROR_OCCURED, e);
        }
    }

    private List loadSchedulesByPropertyIdMultiPropertyCrud(Integer propertyID) {
        return multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyID, ScheduleReport.ALL, QueryParameter.with(PROPERTY_ID, propertyID).parameters());
    }


    public void deleteScheduleFromTenantDB(ScheduleReport schedule, Integer propertyId) {
        // 0 ID means that the schedule needs to be created
        if (schedule != null) {
            multiPropertyCrudService.delete(propertyId, ScheduleReport.class, schedule.getId());
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)


    public void deleteJasperScheduleByClientORServerBased( int clientId,  String entity) {
        LOGGER.info("entity>>>>>>>>>> " + entity);
        deleteNonJasperScheduleByClientORServerBased(clientId);
    }

    private void deleteNonJasperScheduleByClientORServerBased(int clientId) {

        PacmanWorkContextHelper.setClientId(clientId);
        List<Integer> propertyIDs = propertyService.getPropertyIdsForClient(PacmanWorkContextHelper.getClientId());
        propertyService.getPropertyIdsForClient();

        for (Integer propertyID : propertyIDs) {
            List<ScheduleReport> schedules = loadSchedulesByPropertyIdMultiPropertyCrud(propertyID);
            for (ScheduleReport schedule : schedules) {
                try {
                    deleteScheduleFromTenantDB(schedule, propertyID);
                } catch (ConstraintViolationException c) {
                    LOGGER.error(ERROR_MESSAGE_SCHEDULE_REPORT_CONSTRAINT_VIOLATION_OCCURED, c);
                    throw new TetrisException(ErrorCode.DUPLICATE_DATA, ERROR_MESSAGE_SCHEDULE_REPORT_CONSTRAINT_VIOLATION_OCCURED, c);
                } catch (Exception e) {
                    LOGGER.error(ERROR_MESSAGE_SCHEDULE_REPORT_EXCEPTION_OCCURED, e);
                    throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, ERROR_MESSAGE_SCHEDULE_REPORT_ERROR_OCCURED, e);
                }
            }
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateScheduleReportMailId(String oldMailId, String newMailId) {
        updateScheduleReportMailIDInPacmanDB(oldMailId, newMailId);

        LOGGER.info("Successful updated mail recipient.. ");
    }

    /**
     * @param oldMailId
     * @param newMailId
     */
    private void updateScheduleReportMailIDInPacmanDB(String oldMailId, String newMailId) {
        List<Integer> propertyIDs = propertyService.getPropertyIdsForClient(PacmanWorkContextHelper.getClientId());
        for (Integer propertyID : propertyIDs) {
            multiPropertyCrudService.executeNativeDeleteOnSingleProperty(propertyID, ScheduleReport.UPDATE_EMAIL, QueryParameter.with("oldEmailId", oldMailId)
                    .and("newEmailId", newMailId).parameters());
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateScheduleReportMailId(Integer propertyId, String oldMailId, String newMailId) {
        updateScheduleReportMailIDInPacmanDB(propertyId, oldMailId, newMailId);

        LOGGER.info("Successful updated mail recipient.. ");
    }

    /**
     * @param propertyId
     * @param oldMailId
     * @param newMailId
     */
    private void updateScheduleReportMailIDInPacmanDB(Integer propertyId, String oldMailId, String newMailId) {
        multiPropertyCrudService.executeNativeDeleteOnSingleProperty(propertyId, ScheduleReport.UPDATE_EMAIL, QueryParameter.with("oldEmailId", oldMailId)
                .and("newEmailId", newMailId).parameters());
    }

    public List<Long> findAllJasperScheduleIdsForProperty(Integer propertyId) {
        return multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId,
                ScheduleReport.ALL_JASPER_SCHEDULE_IDS_BY_PROPERTY, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public void validateToSchedule(String reportType, String jasperReportUri) {
        boolean limitToScheduleFeatureEnabled = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_SCHEDULE_REPORTS_LIMIT.value());
        if (limitToScheduleFeatureEnabled) {
            validateNumberOfSchedule(reportType, jasperReportUri);
        }
    }

    private void validateNumberOfSchedule(String reportType, String jasperReportUri) {
        Integer numberOfScheduleConfigured = pacmanConfigParamsService.getIntegerParameterValue(getConfigParamForReport(reportType));
        if (numberOfScheduleConfigured != null && numberOfScheduleConfigured != 0) {
            boolean allowToSchedule;
            if (RESTRICTION_REPORT.equalsIgnoreCase(reportType)) {
                Map<String, Integer> restrictionReportsCountByUri = getAlreadyScheduledRestrictionReportsCount(reportType);

                Integer allRestrictionReportsCount = 0;
                if (MapUtils.isNotEmpty(restrictionReportsCountByUri)) {
                    allRestrictionReportsCount = restrictionReportsCountByUri.values().stream().reduce(0, Integer::sum);
                }
                allowToSchedule = allRestrictionReportsCount < numberOfScheduleConfigured;
                if (allowToSchedule) {
                    validateNumberOfScheduleForRestrictionReport(jasperReportUri, numberOfScheduleConfigured, restrictionReportsCountByUri);
                }
            } else {
                Long existingSchedule = getAlreadyScheduledFor(reportType);
                allowToSchedule = existingSchedule < numberOfScheduleConfigured;
            }
            if (!allowToSchedule) {
                LOGGER.error("Schedule Report - Max Schedule is already reached so can't schedule new report..");
                throw new TetrisException(ErrorCode.MAX_SCHEDULE_REACHED, "Schedule Report - Max Schedule reached");
            }
        }
    }

    private void validateNumberOfScheduleForRestrictionReport(String jasperReportUri, Integer numberOfScheduleConfigured, Map<String, Integer> restrictionReportsCountByUri) {
        Integer reservedForFPLos = numberOfScheduleConfigured / 2;
        Integer reservedForMinMaxLos = numberOfScheduleConfigured - reservedForFPLos;

        validateNumberOfScheduleForRestrictionReport(jasperReportUri, restrictionReportsCountByUri,
                reservedForMinMaxLos, G3Report.RESTRICTION_MINMAXLOS_REPORT,
                "Schedule Report - Max Schedule is already reached for Min/Max LOS Restriction Report, so can't schedule new report.",
                "Schedule Report - Max Schedule reached for Min/Max LOS Restriction Report.");

        validateNumberOfScheduleForRestrictionReport(jasperReportUri, restrictionReportsCountByUri,
                reservedForFPLos, G3Report.RESTRICTION_FPLOS_REPORT,
                "Schedule Report - Max Schedule is already reached for FP LOS Restriction Report, so can't schedule new report.",
                "Schedule Report - Max Schedule reached for FP LOS Restriction Report.");
    }

    private void validateNumberOfScheduleForRestrictionReport(String jasperReportUri,
                                                              Map<String, Integer> restrictionReportsCountByUri,
                                                              Integer reservedSchedulesCount,
                                                              G3Report g3Report, String logMsg, String exceptionMsg) {
        Integer actualReportsCount = MapUtils.isNotEmpty(restrictionReportsCountByUri) &&
                restrictionReportsCountByUri.containsKey(g3Report.getReportUnitName()) ?
                restrictionReportsCountByUri.get(g3Report.getReportUnitName()) : 0;

        if (g3Report.getReportUnitName().equalsIgnoreCase(jasperReportUri) &&
                actualReportsCount >= reservedSchedulesCount) {
            LOGGER.error(logMsg);
            throw new TetrisException(ErrorCode.MAX_SCHEDULE_REACHED, exceptionMsg);
        }
    }

    private Map<String, Integer> getAlreadyScheduledRestrictionReportsCount(String reportType) {
        Map<String, Integer> restrictionReportsCountByUri = new HashMap<>();
        List<Object[]> result = crudService.findByNamedQuery(ScheduleReport.NUMBER_OF_SCHEDULES_FOR_RESTRICTION_REPORT,
                QueryParameter.with("actualReportName", reportType).parameters());
        if (CollectionUtils.isNotEmpty(result)) {
            result.forEach(row -> restrictionReportsCountByUri.put((String) row[0], ((Long) row[1]).intValue()));
        }
        return restrictionReportsCountByUri;
    }

    private String getConfigParamForReport(String reportType) {
        switch (reportType) {
            case BOOKING_SITUATION_REPORT:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_BOOKING_SITUATION_REPORT.value();
            case DATA_EXTRACTION_REPORT:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_DATA_EXTRACTION_REPORT.value();
            case OUTPUT_OVERRIDE:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_OUTPUT_OVERRIDE_REPORT.value();
            case PERFORMANCE_COMPARISON_REPORT:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_PERFORMANCE_COMPARISON_REPORT.value();
            case PRICING_OVERRIDE_HISTORY:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_PRICING_OVERRIDE_HISTORY_REPORT.value();
            case PRICING:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_PRICING_REPORT.value();
            case SRP_PRODUCTION_REPORT:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_RATE_PLAN_PRODUCTION_REPORT.value();
            case FORECAST_VALIDATION:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_FORECAST_VALIDATION_REPORT.value();
            case INPUT_OVERRIDE:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_INPUT_OVERRIDE_REPORT.value();
            case MCAT_REPORT:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_MARKET_SEGMENT_MAPPING_REPORT.value();
            case PICKUP_AND_CHANGE_REPORT:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_PICKUP_AND_CHANGE_REPORT.value();
            case SPECIAL_EVENTS:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_SPECIAL_EVENT_REPORT.value();
            case OPERATIONS_REPORT:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_OPERATIONS_REPORT.value();
            case RESTRICTION_REPORT:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_RESTRICTION_REPORT.value();
            case INDIVIDUAL_GROUP_WASH:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_INDIVIDUAL_GROUP_WASH_REPORT.value();
            case MEETING_PACKAGE_PRICING_REPORT:
                return FeatureTogglesConfigParamName.MAX_ALLOWED_SCHEDULES_FOR_MEETING_PACKAGE_PRICING_REPORT.value();
            default:
                return null;
        }
    }

    private Long getAlreadyScheduledFor(String reportType) {
        Long reportScheduleCount;
        if (vaadinScheduledReport(reportType)) {
            ReportType vaadinScheduledReportType = ReportType.valueOf(reportType);
            reportScheduleCount = globalCrudService.findByNamedQuerySingleResult(ScheduledReport.NUMBER_OF_SCHEDULES_FOR_REPORT, QueryParameter.with("reportType", vaadinScheduledReportType).parameters());
        } else {
            reportScheduleCount = crudService.findByNamedQuerySingleResult(ScheduleReport.NUMBER_OF_SCHEDULES_FOR_REPORT, QueryParameter.with("actualReportName", reportType).parameters());
        }
        return reportScheduleCount;
    }

    private boolean vaadinScheduledReport(String reportType) {
        return ReportType.SPECIAL_EVENTS.name().equals(reportType);
    }




    public void deleteOldScheduledReports() {
        List<ScheduleReport> deleteTenantSchedulesList = new ArrayList<>();

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        List<ScheduleReport> schedules = loadSchedulesByPropertyId(propertyId);
        populateDeleteList(deleteTenantSchedulesList, schedules);

        if (!deleteTenantSchedulesList.isEmpty()) {
            crudService.delete(deleteTenantSchedulesList);
        }
    }

    private void populateDeleteList(List<ScheduleReport> deleteTenantSchedulesList, List<ScheduleReport> schedules) {
        Date caughtUpDate = dateServiceLocal.getCaughtUpDate();
        for (ScheduleReport scheduleReport : schedules) {
            String reportParamsStr = scheduleReport.getRportParameters();
            String reportType = scheduleReport.getActualReportName();
            if (DATA_EXTRACTION_REPORT.equalsIgnoreCase(reportType)) {
                Map<String, String> parameterMap = getUIParameters(reportParamsStr);
                Date endDate = null;
                if ("0".equals(parameterMap.get("param_isRollingDate"))) {
                    endDate = populateEndDate(reportType, parameterMap);
                    if (endDate != null && caughtUpDate.after(DateUtil.addDaysToDate(endDate, 2))) {
                        deleteTenantSchedulesList.add(scheduleReport);
                    }
                }
            }

        }
    }

    private Date populateEndDate(String reportType, Map<String, String> parameterMap) {
        Date endDate = null;
        if (DATA_EXTRACTION_REPORT.equalsIgnoreCase(reportType)) {
            endDate = getEndDate(END_DATE, parameterMap);
        }
        return endDate;
    }

    private Date getEndDate(String endDateStr, Map<String, String> parameterMap) {
        if (DEFAULT_DATE.equals(parameterMap.get(endDateStr))) {
            return null;
        }
        String dateStr = parameterMap.get(endDateStr);
        try {
            return DateUtil.parseDate(dateStr, DEFAULT_DATE_FORMAT_FOR_SCHEDULED_REPORTS);
        } catch (ParseException e) {
            LOGGER.error("Failed to parse the end date", e);
        }
        return null;
    }

    public Map<String, String> getReportParameters(String reportParamsObject) {
        String[] parameterArray = reportParamsObject.split(SCHEDULE_PARAM_SEPARATOR);
        Map<String, String> parameterMap = new HashMap<>();
        for (String reportParameter : parameterArray) {
            String[] parameterNameAndValue = reportParameter.split(REPORT_PARAM_VALUE_SEPERATOR);
            if (parameterNameAndValue.length == 1) {
                parameterMap.put(parameterNameAndValue[0], "");
            } else {
                parameterMap.put(parameterNameAndValue[0], parameterNameAndValue[1]);
            }
        }
        return parameterMap;
    }

    public long getTotalSchedules(final int propertyId, ReportSource reportSource) {
        LOGGER.info("Getting total schedules");
        List<ScheduleReport> reports =
                crudService.findByNamedQuery(ScheduleReport.ALL, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        if (ReportSource.AFTER_CDP == reportSource) {
            return reports.stream()
                    .filter(report -> report.getExecutionSchedule() != null)
                    .filter(report -> report.getExecutionSchedule().contains(Constants.CDP)).count();
        } else {
            return reports.stream()
                    .filter(report -> report.getExecutionSchedule() == null
                            || report.getExecutionSchedule().contains(Constants.BDE))
                    .count();
        }
    }

    public void updateScheduleAudit(int scheduleId) {
        WorkContextType workContext = PacmanThreadLocalContextHolder.getWorkContext();
        Integer userId = Integer.valueOf(workContext.getUserId());
        crudService.executeUpdateByNativeQuery(
                "UPDATE ScheduledReports_AUD " +
                        "SET Last_Updated_DTTM = :lastUpdatedDTTM, Last_Updated_By_User_ID = :lastUpdatedByUserId " +
                        "WHERE ID = :scheduleId AND REVTYPE = 2",
                QueryParameter.with("lastUpdatedDTTM", DateUtil.getCurrentDate())
                        .and("lastUpdatedByUserId", userId).and("scheduleId", scheduleId)
                        .parameters());
    }

    public void updateScheduleReportEmail(ScheduleReport scheduledReport, String newRecipients) {
        updateEmailRecipientFromPacmanDB(scheduledReport, newRecipients);
    }
}
