package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.OutOfOrderDTO;
import com.ideas.tetris.pacman.services.outoforderoverride.entity.OutOfOrderOverride;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class OutOfOrderOverrideDataService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;


    public Boolean loadOOOOverrideDataIntoPacman(List<OutOfOrderDTO> data) {
        boolean isDataSaved = false;
        List<OutOfOrderOverride> outOfOrderOverrideList = new ArrayList<>();

        data.forEach(outOfOrderDTO -> {

            AccomType accomType = new AccomType();
            accomType.setId(Integer.valueOf(outOfOrderDTO.getRoomTypeCode()));
            OutOfOrderOverride outOfOrderOverride = new OutOfOrderOverride();
            outOfOrderOverride.setOccupancyDate(outOfOrderDTO.getOccupancyDate());
            outOfOrderOverride.setRoomType(accomType);
            outOfOrderOverride.setValue(outOfOrderDTO.getOutOfOrder());
            outOfOrderOverrideList.add(outOfOrderOverride);

        });

        if (!outOfOrderOverrideList.isEmpty()) {
            tenantCrudService.save(outOfOrderOverrideList);
            isDataSaved = true;
        }

        return isDataSaved;
    }


    public Boolean deleteData() {

        int accomTypeID1 = 7;
        int accomTypeID2 = 21;
        AccomType accomType1 = tenantCrudService.findByNamedQuerySingleResult(AccomType.ALL_BY_ACCOM_TYPE_ID, QueryParameter.with("id", accomTypeID1).parameters());
        AccomType accomType2 = tenantCrudService.findByNamedQuerySingleResult(AccomType.ALL_BY_ACCOM_TYPE_ID, QueryParameter.with("id", accomTypeID2).parameters());

        tenantCrudService.executeUpdateByNamedQuery(OutOfOrderOverride.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomType", accomType1).parameters());
        tenantCrudService.executeUpdateByNamedQuery(OutOfOrderOverride.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomType", accomType2).parameters());
        return true;
    }
}
