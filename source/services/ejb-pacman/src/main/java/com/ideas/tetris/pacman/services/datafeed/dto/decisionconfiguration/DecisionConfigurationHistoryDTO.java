package com.ideas.tetris.pacman.services.datafeed.dto.decisionconfiguration;

import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DecisionConfigurationHistoryDTO {

    public static final String DATE_FORMAT = "dd-MMM-yyyy";
    public static final String TIME_FORMAT = "HH:mm";

    private String name;
    private String action;
    private String note;
    private String dateTime;
    private String decisions;

}
