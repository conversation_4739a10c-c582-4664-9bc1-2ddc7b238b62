package com.ideas.tetris.pacman.services.benefits.service;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.Service;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.benefits.dto.BenefitsDto;
import com.ideas.tetris.pacman.services.benefits.repository.BenefitsMeasurementRepository;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.demandoverride.DemandDetailsUtil;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.runtask.RunTaskOutputService;
import com.ideas.tetris.pacman.util.Runner;
import com.ideas.tetris.pacman.util.csv.CsvFileReader;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.systemconfig.SASSettings;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Benefits;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.ENABLE_PHYSICAL_CAPACITY_CONSIDERATION;
import static com.ideas.tetris.pacman.common.configparams.GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME;
import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.getPropertyGroupId;
import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.getPropertyId;
import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.getUserId;
import static com.ideas.tetris.pacman.services.benefits.dto.BenefitsDto.Category.GROUP;
import static com.ideas.tetris.pacman.services.benefits.dto.BenefitsDto.Category.PROPERTY;
import static com.ideas.tetris.pacman.util.Executor.putIfTrue;
import static com.ideas.tetris.pacman.util.Runner.runIfFalse;
import static com.ideas.tetris.pacman.util.Runner.runIfNotEmpty;
import static com.ideas.tetris.platform.common.job.JobParameterKey.BENEFITS_FIRST_TIMERS;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DATE_FORMAT_MONTH_YEAR;
import static java.lang.Integer.parseInt;
import static java.math.BigDecimal.ZERO;
import static java.math.RoundingMode.HALF_UP;
import static java.util.Objects.isNull;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang.StringUtils.isNotEmpty;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Service
@Component
public class BenefitsMeasurementService {

    private static final Logger LOGGER = Logger.getLogger(BenefitsMeasurementService.class);

    public static final String FORWARD_SLASH = "/";

    @Autowired
    BenefitsMeasurementRepository repository;

    @Autowired
    JobServiceLocal jobService;

    @Autowired
    DateService dateService;

    @Autowired
	private RunTaskOutputService outputService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    @Autowired
    ClientPropertyCacheService clientPropertyCacheService;

    @Autowired
    PropertyGroupService propertyGroupService;

    @Autowired
    PropertyService propertyService;

    public List<BenefitsDto> getBenefits(String startMonthYearString, String endMonthYearString) {
        var startMonth = LocalDate.parse(startMonthYearString, DateTimeFormat.forPattern(DATE_FORMAT_MONTH_YEAR));
        var endMonth = LocalDate.parse(endMonthYearString, DateTimeFormat.forPattern(DATE_FORMAT_MONTH_YEAR));
        List<BenefitsDto> foundBenefits = getBenefits(startMonth.getMonthOfYear(), endMonth.getMonthOfYear(), startMonth.getYear(), endMonth.getYear());
        fillEmptyBenefits(foundBenefits, startMonth, endMonth);
        return foundBenefits;
    }

    private void fillEmptyBenefits(List<BenefitsDto> foundBenefits, LocalDate startMonth, LocalDate endMonth) {
        var requestedMonths = new ArrayList<MonthYear>();
        while (startMonth.isBefore(endMonth)) {
            requestedMonths.add(new MonthYear(startMonth.getYear(), startMonth.getMonthOfYear()));
            startMonth = startMonth.plusMonths(1);
        }

        foundBenefits.forEach(benefitsDto -> {
            var foundMonths = benefitsDto.getChildren().stream()
                    .map(child -> new MonthYear(child.getYear(), child.getMonth())).collect(Collectors.toList());
            requestedMonths.stream()
                    .filter(element -> !foundMonths.contains(element))
                    .forEach(month -> {
                        var emptyChild = new BenefitsDto();
                        emptyChild.setPropertyId(benefitsDto.getPropertyId());
                        emptyChild.setMonth(month.getMonth());
                        emptyChild.setYear(month.getYear());
                        emptyChild.setCategory(BenefitsDto.Category.MONTH.getId());
                        emptyChild.setDataAvailable(false);
                        benefitsDto.addChild(emptyChild, true);
                    });
        });
    }

    public List<BenefitsDto> getBenefits(Integer startMonthIndex, Integer endMonthIndex, Integer startYear, Integer endYear) {
        List<Benefits> monthlyData = repository.getBenefits(startMonthIndex, endMonthIndex, startYear, endYear);
        ArrayList<BenefitsDto> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(monthlyData)) {
            return result;
        }
        BenefitsDto benefitsDto = new BenefitsDto();
        result.add(benefitsDto);
        boolean displayPropertyCode = shouldDisplayPropertyCode();
        if (getPropertyGroupId() == null) {
            Property property = propertyService.getPropertyById(getPropertyId());
            benefitsDto.setPropertyCode(displayPropertyCode ? property.getCode() : property.getName());
            benefitsDto.setPropertyId(PacmanThreadLocalContextHolder.getWorkContext().getPropertyId());
            benefitsDto.setCategory(PROPERTY.getId());
        } else {
            benefitsDto.setCategory(GROUP.getId());
            Map<Integer, Property> properties = propertyGroupService.getPropertiesOfPropertyGroup(getPropertyGroupId()).stream()
                    .collect(Collectors.toMap(Property::getId, property -> property));
            benefitsDto.setPropertyCode(repository.getPropertyGroupName(getPropertyGroupId()));
            benefitsDto.setPropertyId(getPropertyGroupId());
            Map<Integer, List<Benefits>> benefitsForPropertiesUnderPropertyGroup = repository
                    .getBenefitsForPropertiesUnderPropertyGroup(getPropertyGroupId(), startMonthIndex, endMonthIndex, startYear, endYear);
            benefitsForPropertiesUnderPropertyGroup.entrySet().stream()
                    .forEach(entry -> {
                        Property property = properties.get(entry.getKey());
                        BenefitsDto propertyDto = new BenefitsDto();
                        propertyDto.setCategory(PROPERTY.getId());
                        propertyDto.setPropertyCode(displayPropertyCode ? property.getCode() : property.getName());
                        propertyDto.setPropertyId(entry.getKey());
                        entry.getValue().stream().map(BenefitsDto::new).forEach(child -> propertyDto.addChild(child, shouldAddToSummary(child)));
                        result.add(propertyDto);
                    });
            Map<String, List<BenefitsDto>> propertyToMonths = findNegativeBenefitMonthsForProperties(result);
            getUpdatedMonthlySummary(monthlyData, result, propertyToMonths);
        }
        monthlyData.stream().map(BenefitsDto::new).forEach(child -> benefitsDto.addChild(child, shouldAddToSummary(child)));
        return result;
    }

    private void getUpdatedMonthlySummary(List<Benefits> monthlyData, ArrayList<BenefitsDto> result, Map<String, List<BenefitsDto>> propertyToMonths) {

        for (BenefitsDto dto : result) {
            if (dto.getCategory() == 1) {
                continue;
            }

            List<BenefitsDto> months = propertyToMonths.get(dto.getPropertyCode());
            if (!CollectionUtils.isEmpty(months)) {
                updateMonthlySummary(months, monthlyData);
            }
        }
    }

    private void updateMonthlySummary(List<BenefitsDto> months, List<Benefits> monthlyData) {
        for (BenefitsDto benefitsDtoForMonth : months) {
            for (Benefits benefitsForMonth : monthlyData) {
                if (benefitsDtoForMonth.getMonth().equals(benefitsForMonth.getMonth()) &&
                        benefitsDtoForMonth.getYear().equals(benefitsForMonth.getYear())) {
                    benefitsForMonth.setCapacity(benefitsForMonth.getCapacity() - benefitsDtoForMonth.getCapacity());
                    benefitsForMonth.setHeuristicOccupancy(benefitsForMonth.getHeuristicOccupancy() - benefitsDtoForMonth.getHeuristicOccupancy().intValue());
                    benefitsForMonth.setHeuristicRevenue(benefitsForMonth.getHeuristicRevenue().subtract(benefitsDtoForMonth.getHeuristicRevenue()));
                    benefitsForMonth.setHeuristicAdr(getDivisionResult(benefitsForMonth.getHeuristicRevenue(), BigDecimal.valueOf(benefitsForMonth.getHeuristicOccupancy())));
                    benefitsForMonth.setHeuristicRevpar(getDivisionResult(benefitsForMonth.getHeuristicRevenue(), BigDecimal.valueOf(benefitsForMonth.getCapacity())));

                    benefitsForMonth.setActualOccupancy(benefitsForMonth.getActualOccupancy() - benefitsDtoForMonth.getActualOccupancy().intValue());
                    benefitsForMonth.setActualRevenue(benefitsForMonth.getActualRevenue().subtract(benefitsDtoForMonth.getActualRevenue()));
                    benefitsForMonth.setActualAdr(getDivisionResult(benefitsForMonth.getActualRevenue(), BigDecimal.valueOf(benefitsForMonth.getActualOccupancy())));
                    benefitsForMonth.setActualRevpar(getDivisionResult(benefitsForMonth.getActualRevenue(), BigDecimal.valueOf(benefitsForMonth.getCapacity())));
                    Runner.runIfElse(benefitsForMonth.getHeuristicOccupancy() == 0,
                            () -> benefitsForMonth.setBenefitOccupancy(BigDecimal.ZERO),
                            () -> benefitsForMonth.setBenefitOccupancy(new BigDecimal(String.valueOf((double) (benefitsForMonth.getActualOccupancy() * 100) / benefitsForMonth.getHeuristicOccupancy() - 100))));
                    benefitsForMonth.setBenefitRevenue(calculateBenefitsPercentage(benefitsForMonth.getActualRevenue(), benefitsForMonth.getHeuristicRevenue()));
                    benefitsForMonth.setBenefitADR(calculateBenefitsPercentage(benefitsForMonth.getActualAdr(), benefitsForMonth.getHeuristicAdr()));

                    benefitsForMonth.setBenefitRevpar(calculateBenefitsPercentage(benefitsForMonth.getActualRevpar(), benefitsForMonth.getHeuristicRevpar()));
                    benefitsForMonth.setAncillaryRevenue(benefitsForMonth.getAncillaryRevenue().subtract(benefitsDtoForMonth.getAncillaryRevenue()));
                    benefitsForMonth.setAncillaryRevenueWithoutRms(benefitsForMonth.getAncillaryRevenueWithoutRms().subtract(benefitsDtoForMonth.getAncillaryRevenueWithoutRms()));
                    benefitsForMonth.setAncillaryRevenueGain(benefitsForMonth.getAncillaryRevenueGain().subtract(benefitsDtoForMonth.getAncillaryRevenueGain()));
                    benefitsForMonth.setAncillaryProfit(benefitsForMonth.getAncillaryProfit().subtract(benefitsDtoForMonth.getAncillaryProfit()));
                    benefitsForMonth.setAncillaryProfitWithoutRms(benefitsForMonth.getAncillaryProfitWithoutRms().subtract(benefitsDtoForMonth.getAncillaryProfitWithoutRms()));
                    benefitsForMonth.setAncillaryProfitGain(benefitsForMonth.getAncillaryProfitGain().subtract(benefitsDtoForMonth.getAncillaryProfitGain()));
                    benefitsForMonth.setAncillaryRevenueGainInPercent(getDivisionResult(benefitsForMonth.getAncillaryRevenueGain().multiply(new BigDecimal(100)), benefitsForMonth.getAncillaryRevenueWithoutRms()));

                    benefitsForMonth.setAncillaryProfitGainInPercentage(getDivisionResult(benefitsForMonth.getAncillaryProfitGain().multiply(new BigDecimal(100)), benefitsForMonth.getAncillaryProfitWithoutRms()));

                    benefitsForMonth.setActualProfit(benefitsForMonth.getActualProfit().subtract(benefitsDtoForMonth.getActualProfit()));
                    benefitsForMonth.setHeuristicProfit(benefitsForMonth.getHeuristicProfit().subtract(benefitsDtoForMonth.getHeuristicProfit()));

                    benefitsForMonth.setActualProPOR(getDivisionResult(benefitsForMonth.getActualProfit(), BigDecimal.valueOf(benefitsForMonth.getActualOccupancy())));
                    benefitsForMonth.setHeuristicProPOR(getDivisionResult(benefitsForMonth.getHeuristicProfit(), BigDecimal.valueOf(benefitsForMonth.getHeuristicOccupancy())));

                    benefitsForMonth.setActualProPAR(getDivisionResult(benefitsForMonth.getActualProfit(), BigDecimal.valueOf(benefitsForMonth.getCapacity())));
                    benefitsForMonth.setHeuristicProPAR(getDivisionResult(benefitsForMonth.getHeuristicProfit(), BigDecimal.valueOf(benefitsForMonth.getCapacity())));

                    benefitsForMonth.setBenefitProfitInPercent(calculateBenefitsPercentage(benefitsForMonth.getActualProfit(), benefitsForMonth.getHeuristicProfit()));
                    benefitsForMonth.setBenefitProPORInPercent(calculateBenefitsPercentage(benefitsForMonth.getActualProPOR(), benefitsForMonth.getHeuristicProPOR()));
                    benefitsForMonth.setBenefitProPARInPercent(calculateBenefitsPercentage(benefitsForMonth.getActualProPAR(), benefitsForMonth.getHeuristicProPAR()));
                }
            }
        }
    }

    protected static BigDecimal calculateBenefitsPercentage(BigDecimal actual, BigDecimal heuristic) {
        return getDivisionResult(actual.subtract(heuristic).multiply(BigDecimal.valueOf(100)), heuristic);
    }

    private static BigDecimal getDivisionResult(BigDecimal dividend, BigDecimal divisor) {
        return isNull(divisor) || divisor.compareTo(ZERO) == 0 ? ZERO : dividend.divide(divisor, 2, HALF_UP);
    }

    private boolean shouldAddToSummary(BenefitsDto child) {
        if (shouldBenefitProfitMetricsEnabled()) {
            return isPositiveRevenueWithProfitGain(child.getBenefitsRevenue(), child.getAncillaryProfitGain(), child.getBenefitProfitInPercent());
        } else {
            return child.getBenefitsRevenue().compareTo(BigDecimal.ZERO) >= 0 &&
                    (child.getActualRevpar().subtract(child.getHeuristicRevpar())).compareTo(BigDecimal.ZERO) >= 0;
        }
    }

    public boolean isPositiveRevenueWithProfitGain(BigDecimal benefitRevenue, BigDecimal ancillaryProfitGain, BigDecimal benefitProfitGain) {
        return benefitRevenue.add(ancillaryProfitGain).compareTo(BigDecimal.ZERO) >= 0
                && (!isBenefitInProfitEnabled() || benefitProfitGain.compareTo(BigDecimal.valueOf(0.1)) >= 0);
    }

    private Map<String, List<BenefitsDto>> findNegativeBenefitMonthsForProperties(List<BenefitsDto> benefitsDto) {
        Map<String, List<BenefitsDto>> propertyToMonths = new HashMap<>();
        for (BenefitsDto dto : benefitsDto) {
            if (dto.getCategory() == 1) {
                continue;
            }

            List<BenefitsDto> negativeBenefitMonths = findNegativeBenefitMonths(dto);
            propertyToMonths.put(dto.getPropertyCode(), negativeBenefitMonths);
        }
        return propertyToMonths;
    }

    private List<BenefitsDto> findNegativeBenefitMonths(BenefitsDto dto) {
        List<BenefitsDto> negativeBenefitMonths = new ArrayList<>();
        dto.getChildren().forEach(monthDto -> {
            if (!monthDto.isRevenueNonNegative()) {
                negativeBenefitMonths.add(monthDto);
            }
        });
        return negativeBenefitMonths;
    }

    protected boolean shouldDisplayPropertyCode() {
        String displayCodeOrName = configParamsService.getValue("pacman." + PacmanWorkContextHelper.getClientCode(), CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value());
        return "Code".equalsIgnoreCase(displayCodeOrName);
    }

    public boolean shouldBenefitProfitMetricsEnabled() {
        return shouldBenefitProfitMetricsEnabled(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED.value());
    }

    public boolean isBenefitInProfitEnabled() {
        return shouldBenefitProfitMetricsEnabled(PreProductionConfigParamName.ENABLE_PROFIT_IN_BENEFITS.value());
    }

    public boolean shouldBenefitProfitMetricsEnabled(String configParamName) {

        Map<Integer, String> contextsForProperties = new HashMap<>();
        if (getPropertyGroupId() == null) {
            Property property = clientPropertyCacheService.getProperty(PacmanThreadLocalContextHolder.getWorkContext().getPropertyId());
            contextsForProperties.put(property.getId(), property.getClient().getCode() + "," + property.getCode());
        } else {
            contextsForProperties = repository.getContextsForPropertyGroup(getPropertyGroupId());
        }
        return isProfitMetricsEnabledForAnyProperty(contextsForProperties, configParamName);
    }

    public boolean isProfitMetricsEnabledForAnyProperty(Map<Integer, String> propertiesContexts, String configParamName) {
        return propertiesContexts.values().stream().anyMatch(propertyValue -> {
            String[] contexts = propertyValue.split(",");
            return configParamsService.getBooleanParameterValue(configParamName, contexts[0], contexts[1]);
        });
    }


    public Date getSystemTodayDate() {
        if ((getPropertyGroupId() == null)) {
            return dateService.getCaughtUpDate(PacmanWorkContextHelper.getPropertyId());
        } else {
            List<Property> properties = propertyGroupService.getPropertiesOfPropertyGroup(getPropertyGroupId());
            return CollectionUtils.isNotEmpty(properties) ? dateService.getCaughtUpDate(properties.get(0).getId()) : DateUtil.getCurrentDateWithoutTime();
        }
    }

    public boolean propertyIsTwoWay() {
        final Stage propertyStage = propertyService.getPropertyStage(PacmanWorkContextHelper.getPropertyId());
        return Stage.TWO_WAY.equals(propertyStage);
    }

    public void saveBenefits(List<Benefits> benefits) {
        repository.saveBenefits(benefits);
    }

    public void cleanBenefitsData(String monthYear, String propertyIds) {
        var month = LocalDate.parse(monthYear, DateTimeFormat.forPattern(DATE_FORMAT_MONTH_YEAR));
        repository.cleanBenefitsData(month.getMonthOfYear(), month.getYear(), propertyIds);
    }

    public void startBenefitMeasurementJob(Integer maxProperties) {
        var today = LocalDate.fromDateFields(dateService.getCurrentDate());
        final int dayOfMonth = today.getDayOfMonth();
        final String[] dayRangeForRunningBMJob = SystemConfig.dayRangeForRunningBMJob();
        if (dayOfMonth >= Integer.parseInt(dayRangeForRunningBMJob[0]) && dayOfMonth <= Integer.parseInt(dayRangeForRunningBMJob[1])) {
            LocalDate targetMonth = today.minusMonths(1);
            var monthYear = targetMonth.toString(DateTimeFormat.forPattern(DATE_FORMAT_MONTH_YEAR));
            String propertyIds = StringUtils.join(repository.getPropertyIdsToProcess(targetMonth.getMonthOfYear(), targetMonth.getYear(), maxProperties), ",");
            LOGGER.info("Processing BenefitMeasurementJob for | Month : " + monthYear + " | Properties : " + propertyIds);
            startBenefitMeasurementJob(monthYear, propertyIds);
        } else {
            LOGGER.info("Skipping BenefitMeasurementJob as dayOfMonth is out of range : " + dayOfMonth);
        }
    }

    public void startBenefitMeasurementJob(String monthYear, String propertyIds) {
        var outputPath = "BenefitMeasurementJob" + FORWARD_SLASH + DateUtil.formatDate(new Date(), DateUtil.DATE_TIME_FORMAT_FOR_DIR_NAME);
        if (isNotEmpty(propertyIds) && !jobService.isJobActive(JobName.BenefitMeasurementJob)) {
            List<Integer> properties = Arrays.stream(propertyIds.split(",")).map(Integer::parseInt).distinct().collect(Collectors.toList());
            List<Integer> firstTimers = repository.getFirstTimers(properties);
            var month = LocalDate.parse(monthYear, DateTimeFormat.forPattern(DATE_FORMAT_MONTH_YEAR));
            LocalDate startDate = month.dayOfMonth().withMinimumValue();
            LocalDate endDate = month.dayOfMonth().withMaximumValue();
            HashMap<String, Object> parameters = new HashMap<>();
            parameters.put(JobParameterKey.TASK_NAME, "bm");
            parameters.put(JobParameterKey.DATE_START, DateUtil.formatDate(startDate.toDate(), DateUtil.DEFAULT_DATE_FORMAT));
            parameters.put(JobParameterKey.DATE_END, DateUtil.formatDate(endDate.toDate(), DateUtil.DEFAULT_DATE_FORMAT));
            parameters.put(JobParameterKey.USER_ID, Objects.nonNull(getUserId()) ? getUserId() : "11403");
            parameters.put(JobParameterKey.OUTPUTPATH, getRunTaskOutputBasePathForBM(outputPath));
            parameters.put(JobParameterKey.PROPERTY_LIST, StringUtils.join(properties, "|"));
            putIfTrue(isNotEmpty(firstTimers), parameters, BENEFITS_FIRST_TIMERS, StringUtils.join(firstTimers, "|"));
            jobService.startGuaranteedNewInstance(JobName.BenefitMeasurementJob, parameters);
        }
    }

    public List<Integer> filterPropertiesForMonth(LocalDate month, String propertyIds) {
        if (StringUtils.isBlank(propertyIds)) {
            return Collections.emptyList();
        }
        List<Integer> properties = Arrays.stream(propertyIds.split("[|]")).map(Integer::parseInt).collect(Collectors.toList());
        if (isEmpty(properties)) {
            return properties;
        }
        return repository.filterPropertiesForMonth(month.getMonthOfYear(), month.getYear(), properties);
    }

    public List<String> startBenefitMeasurementBackDateJob(String propertyIds) {
        List<String> months = new ArrayList<>();
        LocalDate startDate = LocalDate.now().minusMonths(2);
        int i = 0;
        while (i < 3) {
            String monthYear = startDate.minusMonths(i).toString(DATE_FORMAT_MONTH_YEAR);
            months.add(monthYear);
            cleanBenefitsData(monthYear, propertyIds);
            i++;
        }
        List<Integer> properties = Arrays.stream(propertyIds.split(",")).map(Integer::parseInt).distinct().collect(Collectors.toList());
        properties = repository.filterPropertiesForMonth(startDate.getMonthOfYear(), startDate.getYear(), properties);
        if (!properties.isEmpty()) {
            startBenefitMeasurementBackDateJob(startDate, startDate, StringUtils.join(properties, "|"), 0);
            return months;
        } else {
            return List.of("No valid property found");
        }
    }


    public void startBenefitMeasurementBackDateJob(LocalDate startDate, LocalDate endDate, String propertyIds, Integer pastMonths) {
        var outputPath = "BenefitMeasurementBackDateJob" + FORWARD_SLASH +
                DateUtil.formatDate(new Date(), DateUtil.DATE_TIME_FORMAT_FOR_DIR_NAME) +
                DateUtil.formatDate(startDate.toDate(), DateUtil.DATE_FORMAT_SAS);
        HashMap<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.TASK_NAME, "bm");
        parameters.put(JobParameterKey.DATE_START, DateUtil.formatDate(startDate.toDate(), DateUtil.DEFAULT_DATE_FORMAT));
        parameters.put(JobParameterKey.DATE_END, DateUtil.formatDate(endDate.toDate(), DateUtil.DEFAULT_DATE_FORMAT));
        parameters.put(JobParameterKey.USER_ID, Objects.nonNull(getUserId()) ? getUserId() : "11403");
        parameters.put(JobParameterKey.OUTPUTPATH, getRunTaskOutputBasePathForBM(outputPath));
        parameters.put(JobParameterKey.PROPERTY_LIST, propertyIds);
        parameters.put(JobParameterKey.BENEFITS_PAST_MONTHS, pastMonths);
        parameters.put(BENEFITS_FIRST_TIMERS, propertyIds);
        jobService.startGuaranteedNewInstance(JobName.BenefitMeasurementRunTaskBackDateJob, parameters);
    }

    public String populateBenefits(String date, String outputPath, List<String> properties, Map<Integer, Integer> capacities, List<Integer> benefitInProfitEnabledProperties) {
        var year = Integer.parseInt(date.substring(0, 4));
        var month = Integer.parseInt(date.substring(5, 7));
        List<Benefits> benefitsList = new ArrayList<>();
        BenefitMeasurementXMLParser parser = new BenefitMeasurementXMLParser();
        Set<Integer> errorProperties = properties.stream().map(Integer::parseInt).collect(Collectors.toSet());
        for (Integer propertyId : errorProperties) {
            if (!capacities.containsKey(propertyId)) {
                LOGGER.info("Benefit Measurement | Skipping property as error csv file or no capacities found. Please check csv file, date range or property stage. | Property ID : " + propertyId);
                continue;
            }
            var dir = new File(outputPath + File.separator + propertyId + File.separator + "bm");
             File file = getFileFromDirectory(dir, "xml");
            if(null != file){
                    LOGGER.info("Benefit measurements being saved for : " + file.getName());
                    Map<Integer, Boolean> fgBTMap = new HashMap<>();
                    Map<Integer, Integer> fgForecastTypeMap = repository.getFGForecastTypeMap(propertyId);
                    fgForecastTypeMap.forEach((key, value) -> fgBTMap.put(key, DemandDetailsUtil.isBlockForecastGroup(value)));
                    parser.setProfitInBenefitEnabled(benefitInProfitEnabledProperties.contains(propertyId));
                    Benefits benefits = parser.parseXML(file, month, year, propertyId, fgBTMap, capacities.get(propertyId));
                    printLog(propertyId, benefits);
                    runIfFalse(isNull(benefits), () -> benefitsList.add(benefits));
                    errorProperties=errorProperties.stream().filter(pid->!pid.equals(propertyId)).collect(Collectors.toSet());
                    cleanSasDataFilesFromBMDirectory(dir);
                }
        }
        repository.saveBenefits(benefitsList);

        return zeroInsertData(year, month, errorProperties);
    }

    private void printLog(Integer propertyId, Benefits benefits) {
        Property property = repository.getProperty(propertyId);
        if(isNull(benefits)) {
            printLog("Benefit Measurement | Error Generating Benefit Data during parsing", property);
        } else {
            printLog("Benefit Measurement | Generating Benefit Data ", property);
        }
    }


    @VisibleForTesting
    protected void cleanSasDataFilesFromBMDirectory(File dir) {
        Arrays.stream(dir.listFiles((f, p) -> !(p.endsWith(".xml") || p.endsWith(".csv")))).forEach(File::delete);
    }


    private File getFileFromDirectory(File directory, String fileExtension) {
        if (directory.exists()) {
            return Arrays.stream(Objects.requireNonNull(directory.listFiles((d, f) -> StringUtils.equals(fileExtension, FilenameUtils.getExtension(f)) && f.startsWith("bm")))).findFirst().orElse(null);
        }
        return null;
    }

    private String getRunTaskOutputBasePathForBM(String bmFolderName) {
        return SASSettings.getRunTaskOutputPath() + FORWARD_SLASH + bmFolderName;
    }

    public Set<String> findValidPropertiesFromCsvFile(String date, String outputPath, Set<String> properties) {
        var dir = new File(outputPath + File.separator + "bm");
        File[] files = dir.listFiles((dir1, file) -> StringUtils.equals("csv", FilenameUtils.getExtension(file)) && file.startsWith("bm_log"));
        Set<Integer> errorProperties = new HashSet<>();
        if (null != files) {
            for (File file : files) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.info("Benefit measurements csv being read for : " + file.getName());
                }
                var propertyID = StringUtils.substringsBetween(file.getName(), "bm_log_", "_")[0];
                if (isCsvFileContainsError(file.getAbsolutePath())) {
                    errorProperties.add(parseInt(propertyID));
                    properties.remove(propertyID);
                }
            }
        }

        var year = Integer.parseInt(date.substring(0, 4));
        var month = Integer.parseInt(date.substring(5, 7));
        zeroInsertData(year, month, errorProperties);
        return properties;
    }

    private String zeroInsertData(int year, int month, Set<Integer> errorProperties) {
        var errorPropertiesStr = StringUtils.join(errorProperties, ",");
        runIfNotEmpty(errorProperties, () -> {
            repository.zeroInsert(month, year, errorProperties);
            List<Property> properties = repository.getProperties(errorProperties);
            properties.forEach(p -> printLog("Benefit Measurement | Error Generating Benefit Data | Populating Zero ", p));
        });
        return errorPropertiesStr;
    }

    public void printLog(String msg, Property property) {
        LOGGER.info(msg.concat("| Client Code: " + property.getClient().getCode() + " Property ID: " + property.getId()));
    }

    public boolean isCsvFileContainsError(String file) {
        if (!new File(file).exists()) {
            return true;
        }
        try {
            List<Boolean> csvWithErrorList = new CsvFileReader().read(file, ",", columns ->
                    !columns[3].equals("0") && !columns[4].contains(SystemConfig.ignoredMacroErrorForBMJob()));
            // remove csv header output
            csvWithErrorList.remove(0);
            return csvWithErrorList.stream().anyMatch(Predicate.isEqual(true));
        } catch(Exception e) {
            LOGGER.error("Unable to read BM CSV file: '" + file);
            return true;
        }
    }

    public Map<Integer, Integer> getCapacities(List<String> properties, Date startDate, Date endDate) {
        List<Integer> propertyIds = properties.stream().map(Integer::parseInt).collect(Collectors.toList());
        Map<Integer, String> capacities = repository.getCapacities(propertyIds, startDate, endDate);
        Map<Integer, String> contextsForProperties = repository.getContextsForProperties(propertyIds);
        Map<Integer, Integer> result = new HashMap<>();
        var log = new StringBuilder();
        propertyIds.stream()
                .filter(contextsForProperties::containsKey)
                .filter(capacities::containsKey)
                .forEach(propertyId -> {
                    String[] caps = capacities.get(propertyId).split(",");
                    String[] contexts = contextsForProperties.get(propertyId).split(",");
                    Integer physicalCapacity = Integer.parseInt(caps[0]);
                    Integer availableCapacity = Integer.parseInt(caps[1]);
                    var isPhysicalCapacityEnabled = configParamsService.getBooleanParameterValue(
                            ENABLE_PHYSICAL_CAPACITY_CONSIDERATION.getParameterName(), contexts[0], contexts[1]
                    );
                    result.put(propertyId, isPhysicalCapacityEnabled ? physicalCapacity : availableCapacity);
                    log.append(propertyId).append("=").append(isPhysicalCapacityEnabled ? physicalCapacity : availableCapacity).append(" ");
                });
        LOGGER.info("Benefit Measurement | Capacities | " + log);
        return result;
    }

    public List<Integer> getBenefitEnabledProperties(List<String> properties) {
        List<Integer> propertyIds = properties.stream().map(Integer::parseInt).collect(Collectors.toList());
        Map<Integer, String> contextsForProperties = repository.getContextsForProperties(propertyIds);
        propertyIds.removeIf(propertyId -> !(contextsForProperties.containsKey(propertyId) &&
                isProfitInBenefitsEnabled(contextsForProperties.get(propertyId))));
        LOGGER.info("Benefit Measurement | Profit Benefit Enabled | " + propertyIds);
        return propertyIds;
    }

    private boolean isProfitInBenefitsEnabled(String contextsForProperties) {
        String[] contexts = contextsForProperties.split(",");
        return configParamsService.getBooleanParameterValue(
                PreProductionConfigParamName.ENABLE_PROFIT_IN_BENEFITS.getParameterName(), contexts[0], contexts[1]);
    }



    public boolean isAllPropertiesNonCp() {
        List<Property> properties = propertyGroupService.getPropertiesOfPropertyGroup(getPropertyGroupId());
        properties = propertyService.findPropertyByIds(properties.stream().map(Property::getId).collect(Collectors.toList()));
        return properties
                .stream()
                .noneMatch(property -> propertyService.isPropertyCP(property));
    }

    public int deleteBenefits(Integer propertyId) {
        Integer count = repository.deleteBenefitsForPropertyId(propertyId);
        LOGGER.info("Total number of rows deleted: " + count);
        return count;
    }

    private static class MonthYear {
        private int year;
        private int month;

        public MonthYear(int year, int month) {
            this.year = year;
            this.month = month;
        }

        public int getYear() {
            return year;
        }

        public void setYear(int year) {
            this.year = year;
        }

        public int getMonth() {
            return month;
        }

        public void setMonth(int month) {
            this.month = month;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            var monthYear = (MonthYear) o;
            return year == monthYear.year && month == monthYear.month;
        }

        @Override
        public int hashCode() {
            return Objects.hash(year, month);
        }
    }
}
