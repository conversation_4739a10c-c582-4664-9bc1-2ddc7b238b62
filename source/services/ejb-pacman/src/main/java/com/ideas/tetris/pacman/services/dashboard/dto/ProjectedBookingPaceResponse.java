package com.ideas.tetris.pacman.services.dashboard.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class ProjectedBookingPaceResponse {
    private String clientCode;
    private String propertyCode;
    private String forecastGroup;
    private LocalDate asOfDate;
    private List<OccupancyData> occupancyData;
}
