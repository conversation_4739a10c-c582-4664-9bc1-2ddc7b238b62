package com.ideas.tetris.pacman.services.dashboard.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ideas.tetris.platform.common.rest.unmarshaller.DateSerializer;

import java.util.Date;

public class BasicPricingSpecialEventVO {

    private Integer specialEventId;
    private String startDate;
    private String endDate;
    private String eventDescription;
    private String queryStartDate;
    private String queryEndDate;
    private String impactForecast = "N"; //TODO: Need to check how we can get this information
    private transient Date eventStartDate;
    private transient Date eventEndDate;

    public Integer getSpecialEventId() {
        return specialEventId;
    }

    public void setSpecialEventId(Integer specialEventId) {
        this.specialEventId = specialEventId;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getEventDescription() {
        return eventDescription;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public String getQueryStartDate() {
        return queryStartDate;
    }

    public void setQueryStartDate(String queryStartDate) {
        this.queryStartDate = queryStartDate;
    }

    public String getQueryEndDate() {
        return queryEndDate;
    }

    public void setQueryEndDate(String queryEndDate) {
        this.queryEndDate = queryEndDate;
    }

    public String getImpactForecast() {
        return impactForecast;
    }

    public void setImpactForecast(String impactForecast) {
        this.impactForecast = impactForecast;
    }

    @JsonSerialize(using = DateSerializer.class)
    public Date getEventStartDate() {
        return eventStartDate;
    }

    public void setEventStartDate(Date eventStartDate) {
        this.eventStartDate = eventStartDate;
    }

    @JsonSerialize(using = DateSerializer.class)
    public Date getEventEndDate() {
        return eventEndDate;
    }

    public void setEventEndDate(Date eventEndDate) {
        this.eventEndDate = eventEndDate;
    }

    @Override
    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("\nDescription =  " + eventDescription);
        sb.append("\nStartDate =  " + startDate);
        sb.append("\nEndDate =  " + endDate);
        sb.append("\nimpactsForecast = " + impactForecast);
        sb.append("\neventStartDate = " + eventStartDate);
        sb.append("\neventEndDate = " + eventEndDate);
        return sb.toString();
    }

    @Override
    public Object clone() {
        BasicPricingSpecialEventVO output = new BasicPricingSpecialEventVO();
        output.setStartDate(this.getStartDate());
        output.setEndDate(this.getEndDate());
        output.setEventDescription(this.getEventDescription());
        output.setImpactForecast(this.getImpactForecast());
        output.setEventStartDate(this.getEventStartDate());
        output.setEventEndDate(this.getEventEndDate());
        return output;
    }
}
