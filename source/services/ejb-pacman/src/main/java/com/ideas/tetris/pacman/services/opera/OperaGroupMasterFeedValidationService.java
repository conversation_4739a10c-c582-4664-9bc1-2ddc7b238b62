package com.ideas.tetris.pacman.services.opera;

import com.google.common.collect.ImmutableMap;
import com.ideas.g3.integration.opera.dto.OperaDataLoadTypeCode;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.List;
import java.util.Map;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Created by idnrbk on 4/22/2015.
 */
@OperaGroupMasterFeedValidationService.Qualifier
@Component
@Transactional
public class OperaGroupMasterFeedValidationService implements OperaFeedValidationService {

    private static final Logger LOGGER = Logger.getLogger(OperaGroupMasterFeedValidationService.class.getName());

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }

    @TenantCrudServiceBean.Qualifier
	@Autowired
    @org.springframework.beans.factory.annotation.Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    private static final String[] OPERA_DATALOAD_TYPE_CODE = new String[]{OperaDataLoadTypeCode.PGM.name(), OperaDataLoadTypeCode.CGM.name()};

    private static final String QUERY_DUPLICATE_GROUP_MASTER_RECORDS = "Select Group_ID,Block_Code,Market_Segment,Status,Arrival_Dt,Departure_Dt" +
            " from opera.history_group_master where Data_Load_Metadata_ID in (:dlmIds) group by Group_ID,Block_Code,Market_Segment,Status,Arrival_dt,departure_dt having count(Group_id) > 1";

    private static final String QUERY_COUNT_GROUP_MASTER_WITH_MISSING_GROUP_ID = "Select count(*) from " +
            "opera.history_group_master where Data_Load_Metadata_ID in (:dlmIds) and Group_id = '' ";

    private static final String QUERY_GROUP_ID_WITH_MISSING_GROUP_CODE = "Select Group_id from " +
            "opera.history_group_master where Data_Load_Metadata_ID in (:dlmIds) and Group_id <> '' and Block_Code = '' ";

    private static final String QUERY_GROUP_ID_WITH_MISSING_MARKET_SEGMENT = "Select Group_id from " +
            "opera.history_group_master where Data_Load_Metadata_ID in (:dlmIds) and Group_id <> '' and Market_Segment = '' ";

    private static final String QUERY_GROUP_ID_WITH_MISSING_STATUS = "Select Group_id from " +
            "opera.history_group_master where Data_Load_Metadata_ID in (:dlmIds) and Group_id <> '' and Status = '' ";

    private static final String QUERY_GROUP_ID_WITH_ARRIVAL_DATE = "Select Group_id from " +
            "opera.history_group_master where Data_Load_Metadata_ID in (:dlmIds) and Group_id <> '' and Arrival_dt = '' ";

    private static final String QUERY_GROUP_ID_WITH_DEPARTURE_DATE = "Select Group_id from " +
            "opera.history_group_master where Data_Load_Metadata_ID in (:dlmIds) and Group_id <> '' and Departure_Dt = '' ";

    private static final String PRIMARY_ATTRIBUTE = "Group ID";
    private static final String ENTITY = "Group Master";

    public static final String MSG_GROUP_MASTER_VALIDATION_FAILED = "Feed validation failed for " + ENTITY + ".\n";
    public static final String MSG_MISSING_GROUP_ID = "Group Master Record(s) have missing " + PRIMARY_ATTRIBUTE;
    public static final String MSG_INVALID_GROUP_CODE = " have invalid Block Code";
    public static final String MSG_INVALID_MARKET_SEGMENT = " have invalid Market Segment";
    public static final String MSG_INVALID_STATUS = " have invalid Status";
    public static final String MSG_DUPLICATE_RECORDS = "Duplicate Records (Group ID, Group Code, Market Segment, Status, Arrival Date, Departure Date)";
    public static final String MSG_DUPLICATE = " are present in " + ENTITY;
    public static final String MSG_INVALID_ARRIVAL_DATE = " have invalid arrival date";
    public static final String MSG_INVALID_DEPARTURE_DATE = " have invalid departure date";

    public static Map<String, String> queryMap = ImmutableMap.<String, String>builder()
            .put(MSG_INVALID_GROUP_CODE, QUERY_GROUP_ID_WITH_MISSING_GROUP_CODE)
            .put(MSG_INVALID_MARKET_SEGMENT, QUERY_GROUP_ID_WITH_MISSING_MARKET_SEGMENT)
            .put(MSG_INVALID_STATUS, QUERY_GROUP_ID_WITH_MISSING_STATUS)
            .put(MSG_INVALID_ARRIVAL_DATE, QUERY_GROUP_ID_WITH_ARRIVAL_DATE)
            .put(MSG_INVALID_DEPARTURE_DATE, QUERY_GROUP_ID_WITH_DEPARTURE_DATE)
            .build();

    @Override
    public String[] getDataLoadTypes() {
        return OPERA_DATALOAD_TYPE_CODE;
    }

    @Override
    public String validateFeed(String correlationId, List<Integer> operaDataLoadTypeCodes) {
        boolean flagMissingGroupID = isGroupMasterMissingGroupID(correlationId, operaDataLoadTypeCodes);
        boolean flagMissingMandatoryAttribute = isGroupMasterMissingMandatoryAttribute(correlationId, operaDataLoadTypeCodes);
        boolean flagDuplicateGroupMaster = isDuplicateGroupMasterPresent(correlationId, operaDataLoadTypeCodes);

        if (flagMissingGroupID || flagMissingMandatoryAttribute || flagDuplicateGroupMaster) {
            return MSG_GROUP_MASTER_VALIDATION_FAILED;
        }
        return StringUtils.EMPTY;
    }

    private boolean isGroupMasterMissingGroupID(String correlationId, List<Integer> operaDataLoadTypeCodes) {
        List<Integer> queryResults = crudService.findByNativeQuery(QUERY_COUNT_GROUP_MASTER_WITH_MISSING_GROUP_ID,
                QueryParameter.with("dlmIds", operaDataLoadTypeCodes).parameters());
        Integer count = queryResults.get(0);
        if (count > 0) {
            LOGGER.error("OperaFeedValidationStep Failed: " + count + " " + MSG_MISSING_GROUP_ID);
            return true;
        }
        return false;
    }

    private boolean isGroupMasterMissingMandatoryAttribute(String correlationId, List<Integer> operaDataLoadTypeCodes) {
        boolean result = false;
        for (String msg : queryMap.keySet()) {
            List<String> queryResults = crudService.findByNativeQuery(queryMap.get(msg),
                    QueryParameter.with("dlmIds", operaDataLoadTypeCodes).parameters());
            result = logMissingDataError(queryResults, msg, PRIMARY_ATTRIBUTE) || result;
        }
        return result;
    }

    private boolean isDuplicateGroupMasterPresent(String correlationId, List<Integer> operaDataLoadTypeCodes) {
        List<String> queryResults = crudService.findByNativeQuery(QUERY_DUPLICATE_GROUP_MASTER_RECORDS,
                QueryParameter.with("dlmIds", operaDataLoadTypeCodes).parameters(), new RowMapper<String>() {
                    @Override
                    public String mapRow(Object[] row) {
                        return (new StringBuffer("{").append(row[0]).append(";").append(row[1]).append(";").append(row[2]).append(";").append(row[3]).append(";").append(row[4]).append(";").append(row[5]).append("}")).toString();
                    }
                });
        return logMissingDataError(queryResults, MSG_DUPLICATE, MSG_DUPLICATE_RECORDS);
    }

    private boolean logMissingDataError(List<String> queryResults, String missingAttribute, String initialMessage) {
        if (!CollectionUtils.isEmpty(queryResults)) {
            LOGGER.error((new StringBuffer("OperaFeedValidationStep Failed: ").append(initialMessage).append(" : ").append(queryResults.toString()).append(missingAttribute)).toString());
            return true;
        }
        return false;
    }


}
