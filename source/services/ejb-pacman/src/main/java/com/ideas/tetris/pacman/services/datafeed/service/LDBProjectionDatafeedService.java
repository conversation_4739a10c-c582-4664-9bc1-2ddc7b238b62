package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBProjection;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.END_DATE;
import static com.ideas.tetris.pacman.common.constants.Constants.START_DATE;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Component
@Transactional
public class LDBProjectionDatafeedService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
	private DateService dateService;


    public List<Object> fetchLDBProjectionData(DatafeedRequest datafeedRequest) {
        LocalDate forecastWindowEndDateBDE = formatDate(dateService.getForecastWindowEndDateBDE());
        List<Object> projections = tenantCrudService.findByNamedQuery(
                LDBProjection.GET_ALL_FOR_DATE_RANGE, QueryParameter.with(START_DATE,
                        formatDate(datafeedRequest.getStartDate())).and(END_DATE, forecastWindowEndDateBDE).parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize());

        return projections.stream()
                .map(LDBProjectionDatafeedService.LDBProjectionData::new)
                .collect(Collectors.toList());
    }

    private LocalDate formatDate(Date startDate) {
        return LocalDate.fromDateFields(startDate);
    }

    class LDBProjectionData {
        private static final int ROUNDING_SCALE_FOR_PRICE = 2;
        private String occupancyDate;
        private String marketSegmentCode;
        private int projectedRooms;
        private BigDecimal projectedRevenue;

        LDBProjectionData(Object row) {
            Object[] columns = (Object[]) row;
            this.occupancyDate = getFormattedDate(columns[0].toString());
            this.marketSegmentCode = columns[1].toString();
            this.projectedRooms = Integer.valueOf(columns[2].toString());
            this.projectedRevenue = columns[3] instanceof BigDecimal ? (BigDecimal) columns[3] : BigDecimal.ZERO;
        }

        private String getFormattedDate(String date) {
            DateTimeFormatter currentFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            DateTimeFormatter newFormat = DateTimeFormatter.ofPattern("dd-MMM-yyyy");
            return java.time.LocalDate.parse(date, currentFormat).format(newFormat);
        }

        public String getOccupancyDate() {
            return occupancyDate;
        }

        public void setOccupancyDate(String occupancyDate) {
            this.occupancyDate = occupancyDate;
        }

        public String getMarketSegmentCode() {
            return marketSegmentCode;
        }

        public void setMarketSegmentCode(String marketSegmentCode) {
            this.marketSegmentCode = marketSegmentCode;
        }

        public int getProjectedRooms() {
            return projectedRooms;
        }

        public void setProjectedRooms(int projectedRooms) {
            this.projectedRooms = projectedRooms;
        }

        public String getProjectedRevenue() {
            return String.valueOf(projectedRevenue.setScale(ROUNDING_SCALE_FOR_PRICE, RoundingMode.HALF_UP));
        }

        public void setProjectedRevenue(BigDecimal projectedRevenue) {
            this.projectedRevenue = projectedRevenue;
        }

        @Override
        public String toString() {
            return occupancyDate + " " + marketSegmentCode + " " + projectedRooms + " " + getProjectedRevenue();
        }
    }
}
