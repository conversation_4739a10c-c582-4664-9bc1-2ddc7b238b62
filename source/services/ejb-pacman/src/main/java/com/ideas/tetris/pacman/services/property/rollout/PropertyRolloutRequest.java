package com.ideas.tetris.pacman.services.property.rollout;

import com.ideas.tetris.platform.services.daoandentities.entity.VirtualPropertyMapping;

import java.util.Map;

public class PropertyRolloutRequest {
    private AddPropertyParams addPropertyParams;
    private Map<String, VirtualPropertyMapping> virtualPropertyMappings;

    public AddPropertyParams getAddPropertyParams() {
        return addPropertyParams;
    }

    public void setAddPropertyParams(AddPropertyParams addPropertyParams) {
        this.addPropertyParams = addPropertyParams;
    }

    public Map<String, VirtualPropertyMapping> getVirtualPropertyMappings() {
        return virtualPropertyMappings;
    }

    public void setVirtualPropertyMappings(Map<String, VirtualPropertyMapping> virtualPropertyMappings) {
        this.virtualPropertyMappings = virtualPropertyMappings;
    }
}
