package com.ideas.tetris.pacman.services.security;

import com.ideas.infra.tetris.security.domain.LDAPUser;

import java.util.List;

import org.springframework.aop.SpringProxy;
public interface UserSynchronizationServiceLocal extends SpringProxy {
    Integer createUser(LDAPUser user, boolean isInternal, String clientCode, String password);

    Integer persistUser(Integer userID, LDAPUser user, boolean isInternal, String clientCode, String password, boolean isTenantSyncRequired);

    void removeUser(String uid, boolean isInternal);

    void deleteUser(Integer uid);

    void createTenantUser(Integer propertyId, Integer userId, String email, String uid, boolean active, boolean internal);

    void deleteInternalUser(Integer uid);

    List<GlobalUser> getAllDatabaseUsers(List<String> clientCodes);

}
