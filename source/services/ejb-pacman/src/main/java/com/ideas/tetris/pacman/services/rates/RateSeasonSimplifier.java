package com.ideas.tetris.pacman.services.rates;

import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractDetail;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.Deque;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ideas.tetris.platform.common.utils.map.MapUtil.VALUE_NEGATIVE_ONE;

public class RateSeasonSimplifier {

    private Map<Integer, Deque<AbstractDetail>> mapOfSplitMergeResultDeques;
    private final Map<Integer, List<AbstractDetail>> detailByAccomTypes;

    public RateSeasonSimplifier(Map<Integer, List<AbstractDetail>> detailByAccomTypes) {
        this.detailByAccomTypes = detailByAccomTypes;
    }

    public void setMapOfSplitMergeResultDeques(Map<Integer, Deque<AbstractDetail>> mapOfSplitMergeResultDeques) {
        this.mapOfSplitMergeResultDeques = mapOfSplitMergeResultDeques;
    }

    public List<AbstractDetail> simplifyRateDetailsForView() {
        //1. Create stack for all accom types
        mapOfSplitMergeResultDeques = performSplitMerge();
        detailByAccomTypes.clear();
        List<AbstractDetail> commonResult = findCommonSeasons();
        commonResult.sort(Comparator.comparing(AbstractDetail::getStartDate).thenComparing(AbstractDetail::getAccomTypeId));
        return commonResult;
    }


    private RateDetailKey buildKey(AbstractDetail toBeAdded) {
        return new RateDetailKey(toBeAdded.getStartDate(), toBeAdded.getEndDate(), toBeAdded.getAccomTypeId());
    }

    /**
     * previousIte:   |_______|
     * New:           |_______|
     */
    private boolean doesToBeAddedOverlapExactly(AbstractDetail fromPreviousIteration, AbstractDetail toBeAdded) {
        return hasSameStartEndDate(fromPreviousIteration, toBeAdded);
    }

    private boolean hasSameStartEndDate(AbstractDetail fromPreviousIteration, AbstractDetail toBeAdded) {
        return fromPreviousIteration.getStartDate().equals(toBeAdded.getStartDate()) &&
                fromPreviousIteration.getEndDate().equals(toBeAdded.getEndDate());
    }

    /**
     * previousIte: |_____________|
     * New:         |______|
     */
    private boolean doesOverlapFromStart(AbstractDetail fromPreviousIteration, AbstractDetail toBeAdded) {
        return fromPreviousIteration.getStartDate().equals(toBeAdded.getStartDate());
    }

    /**
     * previousIte: |__________|
     * New:           |______|
     * <p>
     * previousIte: |__________|
     * New:             |__________|
     * <p>
     * previousIte:     |__________|
     * New:         |__________|
     */
    private boolean doesOverlapWithin(AbstractDetail fromPreviousIteration, AbstractDetail toBeAdded) {
        return isEitherOverlappingCompletely(fromPreviousIteration, toBeAdded) ||
                isToBeAddedWithinFromEnd(fromPreviousIteration, toBeAdded) ||
                isToBeAddedWithinFromStart(fromPreviousIteration, toBeAdded);
    }

    /**
     * previousIte:     |__________|
     * New:         |__________|
     * <p>
     * previousIte:            |__________|
     * New:         |__________|
     */
    private boolean isToBeAddedWithinFromEnd(AbstractDetail fromPreviousIteration, AbstractDetail toBeAdded) {
        return fromPreviousIteration.getStartDate().compareTo(toBeAdded.getStartDate()) >= 0 &&
                fromPreviousIteration.getEndDate().compareTo(toBeAdded.getEndDate()) >= 0;
    }

    /**
     * previousIte: |__________|
     * New:             |__________|
     * <p>
     * previousIte: |________|
     * New:             |________|
     */
    protected boolean isToBeAddedWithinFromStart(AbstractDetail fromPreviousIteration, AbstractDetail toBeAdded) {
        return fromPreviousIteration.getStartDate().before(toBeAdded.getStartDate()) &&
                fromPreviousIteration.getEndDate().compareTo(toBeAdded.getStartDate()) >= 0 &&
                fromPreviousIteration.getEndDate().compareTo(toBeAdded.getEndDate()) != 0;
    }

    /**
     * previousIte: |______________|
     * New:             |______|
     * previousIte:     |______|
     * New:     |______________|
     */
    protected boolean isEitherOverlappingCompletely(AbstractDetail fromPreviousIteration, AbstractDetail toBeAdded) {
        return (toBeAdded.getStartDate().compareTo(fromPreviousIteration.getStartDate()) <= 0 && toBeAdded.getEndDate().compareTo(fromPreviousIteration.getEndDate()) >= 0) ||
                (fromPreviousIteration.getStartDate().compareTo(toBeAdded.getStartDate()) <= 0 && fromPreviousIteration.getEndDate().compareTo(toBeAdded.getEndDate()) >= 0);
    }


    /**
     * previousIte: |______________|
     * New:                 |______|
     */
    private boolean doesOverlapFromEnd(AbstractDetail fromPreviousIteration, AbstractDetail toBeAdded) {
        return fromPreviousIteration.getEndDate().equals(toBeAdded.getEndDate());
    }

    private Map<Integer, Deque<AbstractDetail>> performSplitMerge() {
        Map<Integer, Deque<AbstractDetail>> splitMergeResult = new LinkedHashMap<>();
        for (Map.Entry<Integer, List<AbstractDetail>> entry : detailByAccomTypes.entrySet()) {
            var deque = performSplitMergeForAccomType(entry.getValue());
            splitMergeResult.put(entry.getKey(), new ArrayDeque<>(new SeasonMergeService().mergeSeasonsUptoSevenDays(new ArrayList<>(deque))));
        }
        return splitMergeResult;
    }

    private Deque<AbstractDetail> performSplitMergeForAccomType(List<AbstractDetail> detailsToBeSplitAndMerged) {
        Map<RateDetailKey, AbstractDetail> splitMergeResultForAccomType = new LinkedHashMap<>();
        detailsToBeSplitAndMerged.stream().forEach(detailToBeAdded -> performSplitMergeIteration(splitMergeResultForAccomType, detailToBeAdded));

        List<AbstractDetail> listOfSplitMergedDetailsForAccomType = new ArrayList<>(splitMergeResultForAccomType.values());
        listOfSplitMergedDetailsForAccomType.sort(Comparator.comparing(AbstractDetail::getStartDate));
        Deque<AbstractDetail> detailsForAccomType = new ArrayDeque<>();
        detailsForAccomType.addAll(listOfSplitMergedDetailsForAccomType);
        return detailsForAccomType;
    }

    private void performSplitMergeIteration(Map<RateDetailKey, AbstractDetail> resultFromPreviousIteration, AbstractDetail toBeAdded) {
        Map<RateDetailKey, AbstractDetail> currentIterationSplitMergeResult = new LinkedHashMap<>();
        List<AbstractDetail> eligibleForSplitMerge = findEligibleDetailsForSplitMerge(resultFromPreviousIteration, buildKey(toBeAdded));
        if (eligibleForSplitMerge.isEmpty()) {
            resultFromPreviousIteration.put(buildKey(toBeAdded), toBeAdded);
            return;
        }
        eligibleForSplitMerge.sort(Comparator.comparing(AbstractDetail::getStartDate));
        eligibleForSplitMerge.stream().forEach(fromPreviousIteration -> {
            if (doesToBeAddedOverlapExactly(fromPreviousIteration, toBeAdded)) {
                mergeRatesForExactlyOverlapping(fromPreviousIteration, toBeAdded, currentIterationSplitMergeResult);
            } else if (doesOverlapFromStart(fromPreviousIteration, toBeAdded)) {
                splitAndMergeForOverlappingFromStart(fromPreviousIteration, toBeAdded, currentIterationSplitMergeResult);
            } else if (doesOverlapWithin(fromPreviousIteration, toBeAdded)) {
                splitAndMergeForOverlappingWithin(fromPreviousIteration, toBeAdded, currentIterationSplitMergeResult);
            } else if (doesOverlapFromEnd(fromPreviousIteration, toBeAdded)) {
                splitAndMergeForOverlappingFromEnd(fromPreviousIteration, toBeAdded, currentIterationSplitMergeResult);
            }
        });
        resultFromPreviousIteration.putAll(currentIterationSplitMergeResult);
        return;
    }

    private List<AbstractDetail> findEligibleDetailsForSplitMerge(Map<RateDetailKey, AbstractDetail> resultFromPreviousIteration, RateDetailKey keyForToBeAddedDetail) {
        List<AbstractDetail> eligibleDetails = new ArrayList<>();
        List<RateDetailKey> toBeRemoved = new ArrayList<>();
        Iterator<RateDetailKey> iterator = resultFromPreviousIteration.keySet().iterator();
        while (iterator.hasNext()) {
            RateDetailKey currentKey = iterator.next();
            if (currentKey.isEligibleForSplitMerge(keyForToBeAddedDetail)) {
                eligibleDetails.add(resultFromPreviousIteration.get(currentKey));
                toBeRemoved.add(currentKey);
            }
        }
        resultFromPreviousIteration.keySet().removeAll(toBeRemoved);
        return eligibleDetails;
    }

    private void mergeRatesForExactlyOverlapping(AbstractDetail fromPreviousIteration, AbstractDetail toBeAdded, Map<RateDetailKey, AbstractDetail> splitMergeSeasonsFromCurrentIteration) {
        if (toBeAdded.getSunday().intValue() == VALUE_NEGATIVE_ONE.intValue()) {
            toBeAdded.setSunday(fromPreviousIteration.getSunday());
        }
        if (toBeAdded.getMonday().intValue() == VALUE_NEGATIVE_ONE.intValue()) {
            toBeAdded.setMonday(fromPreviousIteration.getMonday());
        }
        if (toBeAdded.getTuesday().intValue() == VALUE_NEGATIVE_ONE.intValue()) {
            toBeAdded.setTuesday(fromPreviousIteration.getTuesday());
        }
        if (toBeAdded.getWednesday().intValue() == VALUE_NEGATIVE_ONE.intValue()) {
            toBeAdded.setWednesday(fromPreviousIteration.getWednesday());
        }
        if (toBeAdded.getThursday().intValue() == VALUE_NEGATIVE_ONE.intValue()) {
            toBeAdded.setThursday(fromPreviousIteration.getThursday());
        }
        if (toBeAdded.getFriday().intValue() == VALUE_NEGATIVE_ONE.intValue()) {
            toBeAdded.setFriday(fromPreviousIteration.getFriday());
        }
        if (toBeAdded.getSaturday().intValue() == VALUE_NEGATIVE_ONE.intValue()) {
            toBeAdded.setSaturday(fromPreviousIteration.getSaturday());
        }
        splitMergeSeasonsFromCurrentIteration.put(buildKey(toBeAdded), toBeAdded);
        return;
    }

    /**
     * previousIte: |_____________|
     * New:         |______|
     */
    private void splitAndMergeForOverlappingFromStart(AbstractDetail fromPreviousIteration, AbstractDetail toBeAdded, Map<RateDetailKey, AbstractDetail> splitMergeResultCurrentIteration) {
        //handle first season
        AbstractDetail seasonForMergingFirstSeasonRateValues = fromPreviousIteration.cloneDetail();
        seasonForMergingFirstSeasonRateValues.setStartDate(toBeAdded.getStartDate());
        seasonForMergingFirstSeasonRateValues.setEndDate(toBeAdded.getEndDate());
        seasonForMergingFirstSeasonRateValues.clearOutOfRangeDOWsWithRangeCheck();
        mergeRatesForExactlyOverlapping(seasonForMergingFirstSeasonRateValues, toBeAdded, splitMergeResultCurrentIteration);
        if (shouldCreateLastSeason(fromPreviousIteration, toBeAdded)) {
            //handle end season
            AbstractDetail endSeason = fromPreviousIteration.cloneDetail();
            endSeason.setStartDate(DateUtil.addDaysToDate(toBeAdded.getEndDate(), 1));
            endSeason.clearOutOfRangeDOWsWithRangeCheck();
            splitMergeResultCurrentIteration.put(buildKey(endSeason), endSeason);
            return;
        }
        return;
    }


    private void splitAndMergeForOverlappingWithin(AbstractDetail fromPreviousIteration, AbstractDetail toBeAdded, Map<RateDetailKey, AbstractDetail> splitMergeSeasonsFromCurrentIteration) {
        if (fromPreviousIteration.getStartDate().before(toBeAdded.getStartDate())) {
            //handle first season
            AbstractDetail startSeason = fromPreviousIteration.cloneDetail();
            startSeason.setEndDate(DateUtil.addDaysToDate(toBeAdded.getStartDate(), -1));
            startSeason.clearOutOfRangeDOWsWithRangeCheck();
            splitMergeSeasonsFromCurrentIteration.put(buildKey(startSeason), startSeason);
        }
        //handle middle season
        AbstractDetail seasonForMergingMiddleSeasonRateValues = fromPreviousIteration.cloneDetail();
        seasonForMergingMiddleSeasonRateValues.setStartDate(toBeAdded.getStartDate());
        seasonForMergingMiddleSeasonRateValues.setEndDate(toBeAdded.getEndDate());
        seasonForMergingMiddleSeasonRateValues.clearOutOfRangeDOWsWithRangeCheck();
        mergeRatesForExactlyOverlapping(seasonForMergingMiddleSeasonRateValues, toBeAdded, splitMergeSeasonsFromCurrentIteration);
        if (shouldCreateLastSeason(fromPreviousIteration, toBeAdded)) {
            //handle 2nd season
            AbstractDetail endSeasons = fromPreviousIteration.cloneDetail();
            endSeasons.setStartDate(DateUtil.addDaysToDate(toBeAdded.getEndDate(), 1));
            endSeasons.clearOutOfRangeDOWsWithRangeCheck();
            splitMergeSeasonsFromCurrentIteration.put(buildKey(endSeasons), endSeasons);
        }
        return;
    }

    /**
     * previousIte:       |_______|
     * New:           |___________|
     */
    private void splitAndMergeForOverlappingFromEnd(AbstractDetail fromPreviousIteration, AbstractDetail toBeAdded, Map<RateDetailKey, AbstractDetail> splitMergeResultCurrentIteration) {
        //handle start season
        AbstractDetail firstSeason = fromPreviousIteration.cloneDetail();
        firstSeason.setEndDate(DateUtil.addDaysToDate(toBeAdded.getStartDate(), -1));
        firstSeason.clearOutOfRangeDOWsWithRangeCheck();
        splitMergeResultCurrentIteration.put(buildKey(firstSeason), firstSeason);
        //handle last season
        AbstractDetail seasonForMergingLastSeasonRateValues = fromPreviousIteration.cloneDetail();
        seasonForMergingLastSeasonRateValues.setStartDate(toBeAdded.getStartDate());
        seasonForMergingLastSeasonRateValues.setEndDate(toBeAdded.getEndDate());
        seasonForMergingLastSeasonRateValues.clearOutOfRangeDOWsWithRangeCheck();
        mergeRatesForExactlyOverlapping(seasonForMergingLastSeasonRateValues, toBeAdded, splitMergeResultCurrentIteration);
        return;
    }

    private boolean shouldCreateLastSeason(AbstractDetail fromPreviousIteration, AbstractDetail toBeAdded) {
        return fromPreviousIteration.getEndDate().after(toBeAdded.getEndDate());
    }

    //can we rename it in a better way
    private List<AbstractDetail> findCommonSeasons() {
        List<AbstractDetail> result = new ArrayList<>();
        //2. Pop 1 from all
        Map<RateDetailKey, AbstractDetail> poppedFromAllAccomTypes = popOneFromAllAccomTypes();
        while (!poppedFromAllAccomTypes.isEmpty()) {
            // 3.1. Check if all same
            if (isAllHasSameStartEndDate(new ArrayList<>(poppedFromAllAccomTypes.values()))) {
                //-> Add all to result
                result.addAll(poppedFromAllAccomTypes.values());
            } else {
                //Find LED LowestEndDate
                Date lowestEndDate = findLowestEndDate(poppedFromAllAccomTypes);
                //Find all with SD <= LED from popped
                Map<RateDetailKey, AbstractDetail> eligibleForCommon = findEligibleForCommon(lowestEndDate, poppedFromAllAccomTypes);
                //3.2. Check if something is common
                if (isThereACommonSeasonToExtract(eligibleForCommon)) {
                    //-> Move back which is not common
                    poppedFromAllAccomTypes.keySet().removeAll(eligibleForCommon.keySet());
                    pushBackToOriginalDeques(poppedFromAllAccomTypes);

                    //-> Find HSD Highest Start Date
                    Date highestStartDate = findHighestStartDate(new ArrayList<>(eligibleForCommon.values()));
                    CommonExtractionResult extractionResult = performCommonSeasonsExtraction(highestStartDate, lowestEndDate, eligibleForCommon);

                    //-> Move all common to result
                    result.addAll(extractionResult.getCommonSeasons().values());

                    // -> Move back remaining after common ED back to their respective stacks
                    pushBackToOriginalDeques(extractionResult.getAfterCommonRangeSeasons());

                    //Check if before common SD can be moved to result
                    handleSeasonsBeforeCommonStartDate(result, extractionResult);

                } else { // 3.3. Check if nothing common
                    //Get all with ED <= LED into result which can directly be added in result
                    Map<RateDetailKey, AbstractDetail> toBeAddedToResult = getDetailsToBeAddedInFinalResult(lowestEndDate, poppedFromAllAccomTypes);
                    //Move above in result
                    result.addAll(toBeAddedToResult.values());
                    //Move back all remaining after LED back to their respective stacks
                    poppedFromAllAccomTypes.keySet().removeAll(toBeAddedToResult.keySet());
                    pushBackToOriginalDeques(poppedFromAllAccomTypes);
                }
            }
            poppedFromAllAccomTypes = popOneFromAllAccomTypes();
        }
        return result;
    }

    private void handleSeasonsBeforeCommonStartDate(List<AbstractDetail> result, CommonExtractionResult extractionResult) {
        if (isThereACommonSeasonToExtract(extractionResult.getBeforeCommonRangeSeasons())) {
            pushBackToOriginalDeques(extractionResult.getBeforeCommonRangeSeasons());
        } else {
            result.addAll(extractionResult.getBeforeCommonRangeSeasons().values());
        }
    }

    protected boolean isThereACommonSeasonToExtract(Map<RateDetailKey, AbstractDetail> beforeCommonRangeSeasons) {
        //Find LED LowestEndDate
        Date lowestEndDate = findLowestEndDate(beforeCommonRangeSeasons);
        //Find all with SD <= LED from given details
        Map<RateDetailKey, AbstractDetail> eligibleForCommon = findEligibleForCommon(lowestEndDate, beforeCommonRangeSeasons);

        return isThereACommonSeasonToExtract(eligibleForCommon.values().size());
    }

    protected CommonExtractionResult performCommonSeasonsExtraction(final Date commonStartDate, final Date commonEndDate, Map<RateDetailKey, AbstractDetail> eligibleForCommon) {
        CommonExtractionResult extractionResult = new CommonExtractionResult();
        eligibleForCommon.values().stream()
                .forEach(detail -> {
                    AbstractDetail commonSeason = createCommonSeason(commonStartDate, commonEndDate, detail);
                    extractionResult.addToCommonSeasons(commonSeason);
                    if (detail.getStartDate().before(commonStartDate)) {
                        //create before seasons
                        AbstractDetail beforeCommonRangeSeason = createSeasonBeforeCommonStartDate(commonStartDate, detail);
                        extractionResult.addToBeforeCommonRangeSeasons(beforeCommonRangeSeason);
                    }
                    if (detail.getEndDate().after(commonEndDate)) {
                        //create after seasons
                        AbstractDetail afterCommonRangeSeason = createSeasonAfterCommonEndDate(commonEndDate, detail);
                        extractionResult.addToAfterCommonRangeSeasons(afterCommonRangeSeason);
                    }
                });
        eligibleForCommon.clear();
        return extractionResult;
    }

    private AbstractDetail createSeasonAfterCommonEndDate(Date commonEndDate, AbstractDetail detail) {
        AbstractDetail afterCommonRangeSeason = detail.cloneDetail();
        afterCommonRangeSeason.setStartDate(DateUtil.addDaysToDate(commonEndDate, 1));
        afterCommonRangeSeason.clearOutOfRangeDOWsWithRangeCheck();
        return afterCommonRangeSeason;
    }

    private AbstractDetail createSeasonBeforeCommonStartDate(Date commonStartDate, AbstractDetail detail) {
        AbstractDetail beforeCommonRangeSeason = detail.cloneDetail();
        beforeCommonRangeSeason.setEndDate(DateUtil.addDaysToDate(commonStartDate, -1));
        beforeCommonRangeSeason.clearOutOfRangeDOWsWithRangeCheck();
        return beforeCommonRangeSeason;
    }

    private AbstractDetail createCommonSeason(Date commonStartDate, Date commonEndDate, AbstractDetail detail) {
        AbstractDetail commonSeason = detail.cloneDetail();
        commonSeason.setStartDate(commonStartDate);
        commonSeason.setEndDate(commonEndDate);
        commonSeason.clearOutOfRangeDOWsWithRangeCheck();
        return commonSeason;
    }

    private void pushBackToOriginalDeques(Map<RateDetailKey, AbstractDetail> poppedOneFromAllAccomTypes) {
        poppedOneFromAllAccomTypes.entrySet().stream()
                .forEach(entry -> mapOfSplitMergeResultDeques.get(entry.getKey().getAccomTypeId()).push(entry.getValue()));
        poppedOneFromAllAccomTypes.clear();
    }

    private boolean isThereACommonSeasonToExtract(Integer allEligibleCount) {
        return allEligibleCount > 1;
    }

    private Map<RateDetailKey, AbstractDetail> getDetailsToBeAddedInFinalResult(Date lowestEndDate, Map<RateDetailKey, AbstractDetail> poppedOneFromAllAccomTypes) {
        return poppedOneFromAllAccomTypes.entrySet().stream()
                .filter(entry -> entry.getValue().getEndDate().compareTo(lowestEndDate) <= 0)
                .collect((Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    }

    protected Map<RateDetailKey, AbstractDetail> findEligibleForCommon(Date lowestEndDate, Map<RateDetailKey, AbstractDetail> poppedOneFromAllAccomTypes) {
        return poppedOneFromAllAccomTypes.entrySet().stream()
                .filter(entry -> entry.getValue().getStartDate().compareTo(lowestEndDate) <= 0)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    protected Date findLowestEndDate(Map<RateDetailKey, AbstractDetail> poppedOneFromAllAccomTypes) {
        Optional<AbstractDetail> detailWithLowestStartDate = poppedOneFromAllAccomTypes.values().stream().min(Comparator.comparing(AbstractDetail::getEndDate));
        return detailWithLowestStartDate.isPresent() ? detailWithLowestStartDate.get().getEndDate() : null;
    }


    protected Date findHighestStartDate(List<AbstractDetail> poppedOneFromAllAccomTypes) {
        Optional<AbstractDetail> detailWithLowestStartDate = poppedOneFromAllAccomTypes.stream().max(Comparator.comparing(AbstractDetail::getStartDate));
        return detailWithLowestStartDate.isPresent() ? detailWithLowestStartDate.get().getStartDate() : null;
    }

    protected boolean isAllHasSameStartEndDate(List<AbstractDetail> poppedOneFromAllAccomTypes) {
        if (poppedOneFromAllAccomTypes.isEmpty() || poppedOneFromAllAccomTypes.size() == 1) {
            return true;
        }
        Date startDate = poppedOneFromAllAccomTypes.get(0).getStartDate();
        Date endDate = poppedOneFromAllAccomTypes.get(0).getEndDate();
        return poppedOneFromAllAccomTypes.stream().allMatch(abstractDetail -> hasSameStartEndDate(abstractDetail, startDate, endDate));
    }

    public boolean hasSameStartEndDate(AbstractDetail checkWith, Date startDate, Date endDate) {
        return startDate.equals(checkWith.getStartDate()) && endDate.equals(checkWith.getEndDate());
    }

    protected Map<RateDetailKey, AbstractDetail> popOneFromAllAccomTypes() {
        /*
        2. Pop 1 from all
            Declare a list of Deques for each accom type at class level, should be HashMap
            Write method to pop from all, which return list
            Sort based on StartDate
         */
        List<AbstractDetail> poppedDetails = mapOfSplitMergeResultDeques.entrySet().stream()
                .filter(integerDequeEntry -> !integerDequeEntry.getValue().isEmpty())
                .map(integerDequeEntry -> integerDequeEntry.getValue().pop())
                .collect(Collectors.toList());
        poppedDetails.sort(Comparator.comparing(AbstractDetail::getStartDate));
        return poppedDetails.stream()
                .collect(Collectors.toMap(this::buildKey, detail -> detail));
    }

    private class CommonExtractionResult {
        //Is map needed here?
        private Map<RateDetailKey, AbstractDetail> commonSeasons = new LinkedHashMap<>();
        private Map<RateDetailKey, AbstractDetail> beforeCommonRangeSeasons = new LinkedHashMap<>();
        private Map<RateDetailKey, AbstractDetail> afterCommonRangeSeasons = new LinkedHashMap<>();

        public void addToCommonSeasons(AbstractDetail commonSeason) {
            commonSeasons.put(buildKey(commonSeason), commonSeason);
        }

        public void addToBeforeCommonRangeSeasons(AbstractDetail beforeCommonRangeSeason) {
            beforeCommonRangeSeasons.put(buildKey(beforeCommonRangeSeason), beforeCommonRangeSeason);
        }

        public void addToAfterCommonRangeSeasons(AbstractDetail afterCommonRangeSeason) {
            afterCommonRangeSeasons.put(buildKey(afterCommonRangeSeason), afterCommonRangeSeason);
        }

        public Map<RateDetailKey, AbstractDetail> getCommonSeasons() {
            return commonSeasons;
        }

        public Map<RateDetailKey, AbstractDetail> getBeforeCommonRangeSeasons() {
            return beforeCommonRangeSeasons;
        }

        public Map<RateDetailKey, AbstractDetail> getAfterCommonRangeSeasons() {
            return afterCommonRangeSeasons;
        }
    }
}
