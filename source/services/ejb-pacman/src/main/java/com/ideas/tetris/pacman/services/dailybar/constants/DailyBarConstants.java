package com.ideas.tetris.pacman.services.dailybar.constants;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;

/**
 * Created by idnpak on 10/6/2015.
 */
public class DailyBarConstants {

    private static final BigDecimal DUMMY_VALUE_FOR_COMPARE = BigDecimal.valueOf(-99999.00).setScale(2, RoundingMode.CEILING);


    public static final StringBuilder GET_ES_RATE_UNQUALIFIED_DETAILS_FULL = new StringBuilder()
            .append(" SELECT product_id, product_code, Accom_Type.Accom_Type_Code AS accom_type_code ")
            .append("     ,Accom_Type.Accom_Type_ID AS accom_type_id ")
            .append("     ,baseESOverd.Occupancy_Date, baseESOverd.Rate_Value, Rate_Code_name ")
            .append(" INTO #ES_RATE_UNQUALIFIED ")
            .append(" FROM ( ")
            .append("     SELECT espd.Product_id AS product_id, Code AS product_code ")
            .append("         ,Accom_Type.Accom_Type_Code AS accom_type_code ")
            .append("         ,Accom_Type.Accom_Type_ID AS accom_type_id, espd.ES_Rate_Unqualified_ID ")
            .append("         ,espd.occupancy_date, espd.rate_value, Rate_Code_name ")
            .append("     FROM ( ")
            .append("         SELECT ES_Product_Definition.Product_id, Code ")
            .append("             ,ES_Product_Definition.Accom_Type_ID AS Accom_Type_ID ")
            .append("             ,ES_Rate_Unqualified.ES_Rate_Unqualified_ID ")
            .append("             ,ES_Rate_Code AS Rate_Code_name ")
            .append("             ,Occupancy_Date ")
            .append("             ,Rate_Value ")
            .append("         FROM ES_Product_Definition ")
            .append("         JOIN ES_Rate_Unqualified ON ES_Product_Definition.ES_Rate_Code = ES_Rate_Unqualified.ES_Rate_Code_Name ")
            .append("         JOIN Product ON ES_Product_Definition.Product_ID = Product.Product_ID ")
            .append("         INNER JOIN (select case when ud.ES_Rate_Unqualified_ID is not null then ud.ES_Rate_Unqualified_ID else uo.ES_Rate_Unqualified_ID end as ES_Rate_Unqualified_ID, ")
            .append("         case when ud.Occupancy_Date is not null then ud.Occupancy_Date else uo.Occupancy_Date end as Occupancy_Date, ")
            .append("         case when ud.Accom_Type_ID is not null then ud.Accom_Type_ID else uo.Accom_Type_ID end as Accom_Type_ID, ")
            .append("         case when uo.Rate_Value is not null then uo.Rate_Value else ud.Rate_Value end as Rate_Value ")
            .append("         from ES_Rate_Unqualified_Details ud ")
            .append("         full outer join ES_Rate_Unqualified_Override uo on ud.ES_Rate_Unqualified_ID = uo.ES_Rate_Unqualified_ID ")
            .append("         and ud.Occupancy_Date = uo.Occupancy_Date and ud.Accom_Type_ID = uo.Accom_Type_ID ")
            .append("         where ud.Occupancy_Date between :decisionStartDate AND :decisionEndDate OR uo.Occupancy_Date between :decisionStartDate AND :decisionEndDate ) as allData ")
            .append("         ON allData.ES_Rate_Unqualified_ID = ES_Rate_Unqualified.ES_Rate_Unqualified_ID ")
            .append("     ) AS espd ")
            .append("     JOIN Accom_Type ON espd.Accom_Type_ID = accom_type.Accom_Type_ID ")
            .append(" ) AS baseESOverd ")
            .append(" CROSS JOIN dbo.accom_type; ");

    public static final StringBuilder REMOVE_DATA_FOR_ACCOM_TYPES_WITH_NO_CAPACITY = new StringBuilder()
            .append(" DELETE FROM #ES_RATE_UNQUALIFIED ")
            .append(" WHERE accom_type_id IN ( ")
            .append("     SELECT [Accom_Type_ID] ")
            .append("     FROM [Accom_Type] ")
            .append("     WHERE [Accom_Type_Capacity] <= 0 OR status_id =2); ");

    public static final StringBuilder CALCULATE_DAILYBAR_RATES_WITHOUT_DOUBLE = new StringBuilder()
            .append(" SELECT occupancy_date, accom_type_id, product_id ")
            .append("     ,Single_Rate = CASE WHEN SingleOffsetType = 'FIXED' THEN (rate_value + SingleOffsetValue) ")
            .append("         ELSE (Rate_Value + (SingleOffsetValue * 0.01 * Rate_Value)) END ")
            .append("     ,ExtraAdultOffsetType, ExtraAdultOffsetValue ")
            .append("     ,ExtraChildOffsetType, ExtraChildOffsetValue ")
            .append("     ,DoubleOffsetType, DoubleOffsetValue ")
            .append(" INTO #DailyBarDecisionOutput_NoDbl ")
            .append(" FROM #DailybarRateOffsetMapForAllAccom ");

    public static final StringBuilder CALCULATE_ES_DAILYBAR_RATES = new StringBuilder()
            .append(" SELECT :decisions_id AS Decision_Id, occupancy_date, accom_type_id, ( ")
            .append("         SELECT Rate_Unqualified_id ")
            .append("         FROM Rate_Unqualified ")
            .append("         WHERE Rate_Code_Name = 'NONE' ")
            .append("     ) AS rate_unqualified_id ")
            .append("     ,product_id, Single_Rate ")
            .append("     ,Double_Rate = CASE WHEN DoubleOffsetType= 'FIXED' THEN (Single_Rate + DoubleOffsetValue) ")
            .append("         ELSE (Single_Rate + (DoubleOffsetValue * 0.01 * Single_Rate)) END ")
            .append("     ,Adult_Rate = CASE WHEN ExtraAdultOffsetType= 'FIXED' THEN ExtraAdultOffsetValue ")
            .append("         ELSE (ExtraAdultOffsetValue * 0.01 * Single_Rate) END ")
            .append("     ,Child_Rate = CASE WHEN ExtraChildOffsetType= 'FIXED' THEN ExtraChildOffsetValue ")
            .append("         ELSE (ExtraChildOffsetValue * 0.01 * Single_Rate) END ")
            .append(" INTO #ESDailyBarDecisionOutput_Temp ")
            .append(" FROM #DailyBarDecisionOutput_NoDbl ");

    public static final StringBuilder INSERT_ES_DECISON_DAILYBAR_OUTPUT = new StringBuilder()
            .append(" MERGE Decision_Dailybar_Output AS target ")
            .append(" USING #ESDailyBarDecisionOutput_Temp AS source ")
            .append(" ON target.Occupancy_Date = source.occupancy_date ")
            .append("     AND target.Accom_Type_ID = source.Accom_Type_ID ")
            .append("     AND target.product_id = source.product_id ")
            .append(" WHEN MATCHED THEN ")
            .append("     UPDATE SET Decision_Id = :decisions_id, product_id = source.product_id ")
            .append("         ,Single_Rate = source.single_rate, Double_Rate = source.Double_Rate ")
            .append("         ,Adult_Rate = source.Adult_Rate, Child_Rate =source.Child_Rate ")
            .append("         ,rate_unqualified_id = source.rate_unqualified_id ")
            .append("         ,CreateDate_DTTM = CURRENT_TIMESTAMP ")
            .append(" WHEN NOT MATCHED THEN ")
            .append("     INSERT ( Decision_Id, Occupancy_Date, Accom_Type_ID, rate_unqualified_id, Single_Rate ")
            .append("         ,Double_Rate, Adult_Rate, Child_Rate, CreateDate_DTTM, Product_id ) ")
            .append("     VALUES ( :decisions_id, source.occupancy_date, source.Accom_Type_ID ")
            .append("         ,source.rate_unqualified_id, source.single_rate, source.Double_Rate ")
            .append("         ,source.Adult_Rate, source.Child_Rate, CURRENT_TIMESTAMP, product_id );");

    public static final String INSERT_PACE_DECISON_DAILYBAR_OUTPUT = "" +
            " INSERT INTO PACE_Dailybar_Output ( Decision_ID, Occupancy_Date, Accom_Type_ID, Rate_Unqualified_ID " +
            "     ,Single_Rate, Double_Rate, Adult_Rate, Child_Rate, Product_Id ) " +
            " SELECT Decision_ID, Occupancy_Date, Accom_Type_ID, Rate_Unqualified_ID, Single_Rate, Double_Rate " +
            "     ,Adult_Rate, Child_Rate, Product_Id " +
            " FROM Decision_Dailybar_Output " +
            " WHERE decision_id = :decisionId ";

    // No space before "Occupancy_Date" so we can prepend a table alias when needed.
    private static final String OCCUPANCY_ACCOM_TYPE_RATE_CODE = "Occupancy_Date, Accom_Type_Code, Rate_Code_Name ";

    private static final String PRODUCT_ID_NULL_OR_DAILY = " (Product_ID IS null OR Product_ID IN ( SELECT Product_ID FROM Product WHERE TYPE = 'DAILY')) ";
    private static final String PRODUCT_ID_IN_INDEPENDENT_PRODUCT = " (Product_ID IN ( SELECT Product_ID FROM Product WHERE CODE='INDEPENDENT' and status_id=1 and is_upload=1)) ";


    private static final String OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END = " Occupancy_Date >= :decisionStartDate AND Occupancy_Date <= :decisionEndDate ";

    public static final StringBuilder GET_ES_RATE_UNQUALIFIED_OVERRIDE_DIFFERENTIAL = new StringBuilder()
            .append(" SELECT product_id, product_code, Accom_Type.Accom_Type_Code AS accom_type_code ")
            .append("     ,Accom_Type.Accom_Type_ID as accom_type_id ")
            .append("     ,Occupancy_Date,Rate_Value ")
            .append("     ,Rate_Code_name ")
            .append(" INTO #ES_RATE_UNQUALIFIED ")
            .append(" FROM ( ")
            .append(" SELECT espd.Product_id AS product_id, Code AS product_code ")
            .append("     ,Accom_Type.Accom_Type_Code AS accom_type_code ")
            .append("     ,Accom_Type.Accom_Type_ID AS accom_type_id ")
            .append("     ,espd.ES_Rate_Unqualified_ID, occupancy_date, rate_value, Rate_Code_name ")
            .append(" FROM ( ")
            .append("     SELECT ES_Product_Definition.Product_id, Code ")
            .append("             ,ES_Product_Definition.Accom_Type_ID AS Accom_Type_ID ")
            .append("             ,ES_Rate_Unqualified.ES_Rate_Unqualified_ID ")
            .append("             ,ES_Rate_Code AS Rate_Code_name, Occupancy_Date, Rate_Value ")
            .append("     FROM ES_Product_Definition ")
            .append("     JOIN ES_Rate_Unqualified ON ES_Product_Definition.ES_Rate_Code = ES_Rate_Unqualified.ES_Rate_Code_Name ")
            .append("     JOIN Product ON ES_Product_Definition.Product_ID = Product.Product_ID ")
            .append("     JOIN ES_Rate_Unqualified_Override AS esro ON esro.Accom_Type_ID = ES_Product_Definition.Accom_Type_ID ")
            .append("         AND ES_Rate_Unqualified.ES_Rate_Unqualified_ID = esro.ES_Rate_Unqualified_ID ")
            .append("     WHERE ").append(OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END)
            .append("     ) AS espd  ")
            .append("     JOIN Accom_Type ON espd.Accom_Type_ID = accom_type.Accom_Type_ID ")
            .append(" ) AS baseESOverd ")
            .append(" CROSS JOIN dbo.accom_type; ");

    private static final String FROM = " FROM ( ";
    private static final String RATES_WITH_TAX_SELECT_COLUMNS = "" +
            " ,(Single_Rate      + (Single_Rate      * :taxFactor)) AS Single_Rate " +
            " ,(Double_Rate      + (Double_Rate      * :taxFactor)) AS Double_Rate " +
            " ,(Triple_Rate      + (Triple_Rate      * :taxFactor)) AS Triple_Rate " +
            " ,(Quad_Rate        + (Quad_Rate        * :taxFactor)) AS Quad_Rate " +
            " ,(Quint_Rate       + (Quint_Rate       * :taxFactor)) AS Quint_Rate " +
            " ,(Adult_Rate       + (Adult_Rate       * :taxFactor)) AS Adult_Rate " +
            " ,(Child_Rate       + (Child_Rate       * :taxFactor)) AS Child_Rate " +
            " ,(One_Child_Rate   + (One_Child_Rate   * :taxFactor)) AS One_Child_Rate " +
            " ,(Two_Child_Rate   + (Two_Child_Rate   * :taxFactor)) AS Two_Child_Rate " +
            " ,(Three_Child_Rate + (Three_Child_Rate * :taxFactor)) AS Three_Child_Rate " +
            " ,(Four_Child_Rate  + (Four_Child_Rate  * :taxFactor)) AS Four_Child_Rate " +
            " ,(Five_Child_Rate  + (Five_Child_Rate  * :taxFactor)) AS Five_Child_Rate " +
            " ,(Child_Age_1_Rate + (Child_Age_1_Rate * :taxFactor)) AS Child_Age_1_Rate " +
            " ,(Child_Age_2_Rate + (Child_Age_2_Rate * :taxFactor)) AS Child_Age_2_Rate " +
            " ,(Child_Age_3_Rate + (Child_Age_3_Rate * :taxFactor)) AS Child_Age_3_Rate " +
            " ,(Child_Age_4_Rate + (Child_Age_4_Rate * :taxFactor)) AS Child_Age_4_Rate ";

    private static final String RATES_WITH_TAX = RATES_WITH_TAX_SELECT_COLUMNS + FROM;

    private static final String DECISION_DAILYBAR_ACCOM_TYPE_RATE_UNQUALIFIED_JOIN = "  FROM Decision_Dailybar_Output dbo " +
            "  INNER JOIN Accom_Type at ON dbo.Accom_Type_Id = at.Accom_Type_ID " +
            "  INNER JOIN Rate_Unqualified rq ON dbo.Rate_Unqualified_ID = rq.Rate_Unqualified_ID ";
    private static final String RATES_ADJUSTMENT_SELECT_COLUMNS = "" +
            "  ,(Single_Rate + :miscAdjustment) AS Single_Rate " +
            "  ,(Double_Rate + :miscAdjustment) AS Double_Rate " +
            "  ,(Triple_Rate + :miscAdjustment) AS Triple_Rate " +
            "  ,(Quad_Rate   + :miscAdjustment) AS Quad_Rate " +
            "  ,(Quint_Rate  + :miscAdjustment) AS Quint_Rate " +
            "  ,Adult_Rate, Child_Rate " +
            "  ,One_Child_Rate, Two_Child_Rate, Three_Child_Rate, Four_Child_Rate, Five_Child_Rate " +
            "  ,Child_Age_1_Rate, Child_Age_2_Rate, Child_Age_3_Rate, Child_Age_4_Rate ";
    private static final String RATES_WITH_ADJUSTMENT = RATES_ADJUSTMENT_SELECT_COLUMNS +
            DECISION_DAILYBAR_ACCOM_TYPE_RATE_UNQUALIFIED_JOIN;

    private static final String RATES_WITH_ADJUSTMENT_AND_PRODUCT_ID = RATES_ADJUSTMENT_SELECT_COLUMNS + ", product_id" +
            DECISION_DAILYBAR_ACCOM_TYPE_RATE_UNQUALIFIED_JOIN;

    private static final String WHERE_RATE_NOT_EQUAL_OR_DATE_NULL = "" +
            " WHERE pt.Single_Rate  <> npt.Single_Rate " +
            "     OR pt.Double_Rate <> npt.Double_Rate " +
            "     OR pt.Triple_Rate <> npt.Triple_Rate " +
            "     OR pt.Quad_Rate   <> npt.Quad_Rate " +
            "     OR pt.Quint_Rate  <> npt.Quint_Rate " +
            "     OR pt.Adult_Rate  <> npt.Adult_Rate " +
            "     OR pt.Child_Rate  <> npt.Child_Rate " +
            "     OR isNull(pt.One_Child_Rate,   -1) <> isNull(npt.One_Child_Rate,   -1) " +
            "     OR isNull(pt.Two_Child_Rate,   -1) <> isNull(npt.Two_Child_Rate,   -1) " +
            "     OR isNull(pt.Three_Child_Rate, -1) <> isNull(npt.Three_Child_Rate, -1) " +
            "     OR isNull(pt.Four_Child_Rate,  -1) <> isNull(npt.Four_Child_Rate,  -1) " +
            "     OR isNull(pt.Five_Child_Rate,  -1) <> isNull(npt.Five_Child_Rate,  -1) " +
            "     OR isNull(pt.Child_Age_1_Rate, -1) <> isNull(npt.Child_Age_1_Rate, -1) " +
            "     OR isNull(pt.Child_Age_2_Rate, -1) <> isNull(npt.Child_Age_2_Rate, -1) " +
            "     OR isNull(pt.Child_Age_3_Rate, -1) <> isNull(npt.Child_Age_3_Rate, -1) " +
            "     OR isNull(pt.Child_Age_4_Rate, -1) <> isNull(npt.Child_Age_4_Rate, -1) " +
            "     OR pt.Occupancy_Date IS null ";

    private static final String SELECT_RATES_FROM_DECISION_DAILYBAR_OUTPUT = "" +
            " SELECT DBR.Occupancy_Date, Accom_Type_ID " +
            "     ,(Single_Rate + :miscAdjustment) AS Single_Rate " +
            "     ,(Double_Rate + :miscAdjustment) AS Double_Rate " +
            "     ,(Triple_Rate + :miscAdjustment) AS Triple_Rate " +
            "     ,(Quad_Rate   + :miscAdjustment) AS Quad_Rate " +
            "     ,(Quint_Rate  + :miscAdjustment) AS Quint_Rate " +
            "     ,Adult_Rate, Child_Rate, Rate_Unqualified_ID, Product_ID " +
            "     ,One_Child_Rate, Two_Child_Rate, Three_Child_Rate, Four_Child_Rate, Five_Child_Rate " +
            "     ,Child_Age_1_Rate, Child_Age_2_Rate, Child_Age_3_Rate, Child_Age_4_Rate " +
            " FROM dbo.Decision_Dailybar_Output DBR ";

    private static final StringBuilder DAILY_BAR_RATE_CHART_OFFSET_VALUE_ALL_OR_WEEKDAY = new StringBuilder()
            .append(" SELECT CASE ")
            .append("     WHEN Offset_Value_All IS NOT null THEN Offset_Value_All")
            .append("     ELSE CASE (DATEPART(WEEKDAY, occupancy_date)) ")
            .append("         WHEN 1 THEN (Sunday_Rate ) ")
            .append("         WHEN 2 THEN (Monday_Rate ) ")
            .append("         WHEN 3 THEN (Tuesday_Rate  ) ")
            .append("         WHEN 4 THEN (Wednesday_Rate ) ")
            .append("         WHEN 5 THEN (Thursday_Rate) ")
            .append("         WHEN 6 THEN (Friday_Rate) ")
            .append("         WHEN 7 THEN (Saturday_Rate) ")
            .append("     END ")
            .append(" END ")
            .append(" FROM Daily_Bar_Rate_Chart ");

    private static final StringBuilder SELECT_OFFSET_TYPE_WHERE_DAILY_BAR_RATE_CHART_ID_EQUALS = new StringBuilder()
            .append(" SELECT Offset_Type ")
            .append(" FROM Daily_Bar_Rate_Chart ")
            .append(" WHERE Daily_Bar_Rate_Chart_ID = ");

    public static final StringBuilder GET_ES_DAILYBAR_RATE_OFFSET_MAP_FOR_ALL_ACCOMS = new StringBuilder()
            .append(" SELECT occupancy_date, Rate_Code_Name, Rate_Plan, Rate_value, accom_type_id, product_id ")
            .append("     ,product_code, SingleOffsetType, SingleOffsetValue, DoubleOffsetType, DoubleOffsetValue ")
            .append("     ,ExtraAdultOffsetType, ExtraAdultOffsetValue, ExtraChildOffsetType, ExtraChildOffsetValue ")
            .append(" INTO #DailybarRateOffsetMapForAllAccom ")
            .append(" FROM ( ")
            .append("     SELECT Daily_Bar_Config_id, br.occupancy_date, br.Rate_Code_Name, dbc.Rate_Plan ")
            .append("         ,br.Rate_value, br.accom_type_id, product_code, br.product_id, ( ")
            .append(DAILY_BAR_RATE_CHART_OFFSET_VALUE_ALL_OR_WEEKDAY)
            .append("         WHERE Daily_Bar_Rate_Chart_ID = dbc.Single_Rate_Chart_ID ")
            .append("     ) AS SingleOffsetValue, ( ")
            .append(SELECT_OFFSET_TYPE_WHERE_DAILY_BAR_RATE_CHART_ID_EQUALS).append("dbc.Single_Rate_Chart_ID ")
            .append("     ) AS SingleOffsetType, ( ")
            .append(DAILY_BAR_RATE_CHART_OFFSET_VALUE_ALL_OR_WEEKDAY)
            .append("         WHERE Daily_Bar_Rate_Chart_ID = dbc.Double_Rate_Chart_ID ")
            .append("     ) AS DoubleOffsetValue, ( ")
            .append(SELECT_OFFSET_TYPE_WHERE_DAILY_BAR_RATE_CHART_ID_EQUALS).append("dbc.Double_Rate_Chart_ID ")
            .append("     ) AS DoubleOffsetType, ( ")
            .append(DAILY_BAR_RATE_CHART_OFFSET_VALUE_ALL_OR_WEEKDAY)
            .append("         WHERE Daily_Bar_Rate_Chart_ID = dbc.Extra_Adult_Rate_Chart_ID ")
            .append("     ) AS ExtraAdultOffsetValue, ( ")
            .append(SELECT_OFFSET_TYPE_WHERE_DAILY_BAR_RATE_CHART_ID_EQUALS).append("dbc.Extra_Adult_Rate_Chart_ID ")
            .append("     ) AS ExtraAdultOffsetType, ( ")
            .append(DAILY_BAR_RATE_CHART_OFFSET_VALUE_ALL_OR_WEEKDAY)
            .append("         WHERE Daily_Bar_Rate_Chart_ID = dbc.Extra_Child_Rate_Chart_ID ")
            .append("     ) AS ExtraChildOffsetValue, ( ")
            .append(SELECT_OFFSET_TYPE_WHERE_DAILY_BAR_RATE_CHART_ID_EQUALS).append("dbc.Extra_Child_Rate_Chart_ID ")
            .append("     ) AS ExtraChildOffsetType ")
            .append("     FROM Daily_Bar_Config AS dbc ")
            .append("     INNER JOIN #ES_RATE_UNQUALIFIED AS br ON  Accom_Type_Code = dbc.Room_Type ")
            .append("         AND br.product_id = dbc.Product_id ")
            .append("         AND br.Occupancy_Date between dbc.[Start_Date] and dbc.End_Date ")
            .append("         AND (Rate_Code_Name = dbc.Rate_Plan OR dbc.Rate_Plan IS null) ")
            .append(" ) AS dailybar; ");

    private DailyBarConstants() {
    }

    private static String roundedRates(int roundingDepth) {
        return " ,CAST((CASE WHEN Single_Rate<0      THEN 0 ELSE Single_Rate      END) AS DECIMAL(19," + roundingDepth + ")) AS Single_Rate " +
                " ,CAST((CASE WHEN Double_Rate<0      THEN 0 ELSE Double_Rate      END) AS DECIMAL(19," + roundingDepth + ")) AS Double_Rate " +
                " ,CAST((CASE WHEN Triple_Rate<0      THEN 0 ELSE Triple_Rate      END) AS DECIMAL(19," + roundingDepth + ")) AS Triple_Rate " +
                " ,CAST((CASE WHEN Quad_Rate<0        THEN 0 ELSE Quad_Rate        END) AS DECIMAL(19," + roundingDepth + ")) AS Quad_Rate " +
                " ,CAST((CASE WHEN Quint_Rate<0       THEN 0 ELSE Quint_Rate       END) AS DECIMAL(19," + roundingDepth + ")) AS Quint_Rate " +
                " ,CAST((CASE WHEN Adult_Rate<0       THEN 0 ELSE Adult_Rate       END) AS DECIMAL(19," + roundingDepth + ")) AS Adult_Rate " +
                " ,CAST((CASE WHEN Child_Rate<0       THEN 0 ELSE Child_Rate       END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Rate " +
                " ,CAST((CASE WHEN One_Child_Rate<0   THEN 0 ELSE One_Child_Rate   END) AS DECIMAL(19," + roundingDepth + ")) AS One_Child_Rate " +
                " ,CAST((CASE WHEN Two_Child_Rate<0   THEN 0 ELSE Two_Child_Rate   END) AS DECIMAL(19," + roundingDepth + ")) AS Two_Child_Rate " +
                " ,CAST((CASE WHEN Three_Child_Rate<0 THEN 0 ELSE Three_Child_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Three_Child_Rate " +
                " ,CAST((CASE WHEN Four_Child_Rate<0  THEN 0 ELSE Four_Child_Rate  END) AS DECIMAL(19," + roundingDepth + ")) AS Four_Child_Rate " +
                " ,CAST((CASE WHEN Five_Child_Rate<0  THEN 0 ELSE Five_Child_Rate  END) AS DECIMAL(19," + roundingDepth + ")) AS Five_Child_Rate " +
                " ,CAST((CASE WHEN Child_Age_1_Rate<0 THEN 0 ELSE Child_Age_1_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Age_1_Rate " +
                " ,CAST((CASE WHEN Child_Age_2_Rate<0 THEN 0 ELSE Child_Age_2_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Age_2_Rate " +
                " ,CAST((CASE WHEN Child_Age_3_Rate<0 THEN 0 ELSE Child_Age_3_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Age_3_Rate " +
                " ,CAST((CASE WHEN Child_Age_4_Rate<0 THEN 0 ELSE Child_Age_4_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Age_4_Rate ";
    }

    private static String hiltonCPFileColumns(int roundingDepth) {
        return " Occupancy_Date " +
                " ,Accom_Type_Code " +
                " ,Single_Rate " +
                " ,Double_Rate " +
                " ,Adult_Rate " +
                " ,Child_Rate " +
                " ,CAST((CASE WHEN Child_Age_1_Rate<0 THEN 0 ELSE Child_Age_1_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Age_1_Rate " +
                " ,CAST((CASE WHEN Child_Age_2_Rate<0 THEN 0 ELSE Child_Age_2_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Age_2_Rate " +
                " ,CAST((CASE WHEN Child_Age_3_Rate<0 THEN 0 ELSE Child_Age_3_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Age_3_Rate " +
                " ,dfd.Product_ID " +
                " ,Triple_Rate " +
                " ,Quad_Rate ";
    }

    public static String getHiltonCPFileDecisionsFull(int roundingDepth) {
        return "" +
                " SELECT " + hiltonCPFileColumns(roundingDepth) +
                " FROM Decision_Dailybar_Output dfd " +
                " INNER JOIN Accom_Type at ON dfd.Accom_Type_Id = at.Accom_Type_ID " +
                " INNER JOIN Rate_Unqualified rq ON dfd.Rate_Unqualified_ID = rq.Rate_Unqualified_ID " +
                " INNER JOIN Product pd on dfd.product_id=pd.product_id" +
                " WHERE " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                " AND (pd.system_default=1 or pd.is_upload=1 and pd.status_id=1)" +
                " AND at.Status_ID = 1 " +
                "  ORDER BY Occupancy_Date, Accom_Type_Code, dfd.Product_ID";
    }

    public static String getHiltonCPFileDecisionsDifferential(int roundingDepth, BigInteger fullRefreshDecisionToCompare) {
        return "" +
                " SELECT " + hiltonCPFileColumns(roundingDepth) +
                " FROM ( " +
                "     SELECT npt.* " +
                "     FROM ( " +
                "         SELECT DBR.Occupancy_Date, DBR.Accom_Type_ID, Single_Rate, Double_Rate, Adult_Rate, Child_Rate, Child_Age_1_Rate, Child_Age_2_Rate, Child_Age_3_Rate, " +
                "                 Rate_Unqualified_ID, dbr.Product_ID, Triple_Rate, Quad_Rate " +
                "         FROM dbo.Decision_Dailybar_Output DBR join accom_type at on DBR.Accom_Type_ID=at.Accom_Type_ID and at.Status_ID=1 " +
                " inner join product p on dbr.product_id=p.product_id " +
                "         WHERE " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                "             and (p.system_default=1 or p.is_upload=1 and p.status_id=1) " +
                "     ) AS npt " +
                "     LEFT JOIN ( " +
                "         SELECT PGB.Decision_id, PGB.Occupancy_Date, PGB.Accom_Type_ID, Single_Rate, Double_Rate, Adult_Rate, Child_Rate, Child_Age_1_Rate, Child_Age_2_Rate, Child_Age_3_Rate, Product_ID, Triple_Rate, Quad_Rate  " +
                "         FROM ( " +
                "             SELECT Occupancy_Date, Accom_Type_ID, MAX(D.Decision_id) AS Decision_id, dbr.Product_ID  as Product_IDS" +
                "             FROM dbo.PACE_Dailybar_Output DBR" +
                " inner join product p on dbr.product_id=p.product_id  " +
                "             INNER JOIN Decision D ON DBR.Decision_ID = D.Decision_ID " +
                "                 AND " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                "                 AND D.End_DTTM  <= :lastUploadedDate " +
                "                and (p.system_default=1 or p.is_upload=1 and p.status_id=1) " +
                "                 AND D.Decision_ID >= " + fullRefreshDecisionToCompare +
                "             GROUP BY Occupancy_Date, Accom_Type_ID, dbr.Product_ID " +
                "         ) AS PGB " +
                "         INNER JOIN PACE_Dailybar_Output PDB ON PGB.Occupancy_Date = PDB.Occupancy_Date " +
                "             AND PGB.Accom_Type_ID = PDB.Accom_Type_ID " +
                "             AND PGB.Decision_id = PDB.Decision_id " +
                "             AND PGB.Product_IDS = PDB.Product_ID " +
                "     ) AS pt " +
                "     ON pt.Occupancy_Date = npt.Occupancy_Date " +
                "         AND pt.Accom_Type_ID = npt.Accom_Type_ID " +
                "         AND ((pt.product_id IS null AND npt.product_id IS null ) OR (pt.product_id = npt.product_id)) " +
                "     WHERE pt.Occupancy_Date IS null " +
                "         OR pt.Single_Rate <> npt.Single_Rate " +
                "         OR pt.Double_Rate <> npt.Double_Rate " +
                "         OR pt.Adult_Rate <> npt.Adult_Rate " +
                "         OR pt.Child_Rate <> npt.Child_Rate " +
                "         OR isNull(pt.Child_Age_1_Rate," + DUMMY_VALUE_FOR_COMPARE + ") <> isNull(npt.Child_Age_1_Rate," + DUMMY_VALUE_FOR_COMPARE + ") " +
                "         OR isNull(pt.Child_Age_2_Rate," + DUMMY_VALUE_FOR_COMPARE + ") <> isNull(npt.Child_Age_2_Rate," + DUMMY_VALUE_FOR_COMPARE + ") " +
                "         OR isNull(pt.Child_Age_3_Rate," + DUMMY_VALUE_FOR_COMPARE + ") <> isNull(npt.Child_Age_3_Rate," + DUMMY_VALUE_FOR_COMPARE + ") " +
                "         OR pt.Triple_Rate <> npt.Triple_Rate " +
                "         OR pt.Quad_Rate <> npt.Quad_Rate " +
                " ) AS dfd " +
                " INNER JOIN Accom_Type AS at ON dfd.Accom_Type_Id = at.Accom_Type_ID " +
                " INNER JOIN Rate_Unqualified AS rq ON dfd.Rate_Unqualified_ID = rq.Rate_Unqualified_ID " +
                " ORDER BY Occupancy_Date, Accom_Type_Code, Product_ID; ";
    }

    public static String getDecisionDailyBarOutputFullUpToDecimal(int roundingDepth, boolean isIndependentProductFlow) {
        return "" +
                " SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + roundedRates(roundingDepth) + ", product_id" +
                " FROM ( " +
                "     SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + RATES_WITH_TAX_SELECT_COLUMNS + ", product_id " + FROM +
                "         SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + RATES_ADJUSTMENT_SELECT_COLUMNS + ", product_id " +
                DECISION_DAILYBAR_ACCOM_TYPE_RATE_UNQUALIFIED_JOIN +
                "         WHERE " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                "             AND " + (isIndependentProductFlow ? PRODUCT_ID_IN_INDEPENDENT_PRODUCT : PRODUCT_ID_NULL_OR_DAILY) +
                "     ) dailybarwithmisc " +
                " ) dailybarwithmiscandtax ORDER BY " + OCCUPANCY_ACCOM_TYPE_RATE_CODE;
    }

    public static String getDecisionDailyBarOutputFullUpToDecimalForDates(int roundingDepth) {
        return "" +
                " SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + roundedRates(roundingDepth) +
                " FROM ( " +
                "     SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + RATES_WITH_TAX +
                "         SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + RATES_WITH_ADJUSTMENT +
                "         WHERE Occupancy_Date in (:dates) " +
                "             AND " + PRODUCT_ID_NULL_OR_DAILY +
                "     ) dailybarwithmisc " +
                " ) dailybarwithmiscandtax ORDER BY " + OCCUPANCY_ACCOM_TYPE_RATE_CODE;
    }

    public static String getAllDecisionDailyBarOutputFullForAllRoomClassesUpToDecimalForDates(int roundingDepth) {
        return "" +
                " SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + roundedRates(roundingDepth) +
                " FROM ( " +
                "     SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + RATES_WITH_TAX +
                "         SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + RATES_WITH_ADJUSTMENT +
                "         WHERE dbo.Occupancy_Date IN (:dates) " +
                "             AND " + PRODUCT_ID_NULL_OR_DAILY +
                "     ) dailybarwithmisc " +
                " ) dailybarwithmiscandtax ORDER BY " + OCCUPANCY_ACCOM_TYPE_RATE_CODE;
    }

    public static String getDecisionDailyBarOutputDifferentialByRoomClassUpToDecimalForDates(int roundingDepth) {
        return "" +
                " SELECT dailybarwithmiscandtax." + OCCUPANCY_ACCOM_TYPE_RATE_CODE + roundedRates(roundingDepth) +
                " FROM ( " +
                "     SELECT dailybarwithmisc." + OCCUPANCY_ACCOM_TYPE_RATE_CODE + RATES_WITH_TAX +
                "         SELECT dbo." + OCCUPANCY_ACCOM_TYPE_RATE_CODE + RATES_WITH_ADJUSTMENT +
                "         INNER JOIN ( " +
                "             SELECT src.Occupancy_Date, act.Accom_Type_id from accom_type act " +
                "             INNER JOIN ( " +
                "                 SELECT Occupancy_Date, Accom_Class_ID " +
                "                 FROM ( " +
                "                     SELECT Occupancy_Date, Accom_Class_ID " +
                "                     FROM ( " +
                "                         SELECT npt.* " +
                "                         FROM ( " +
                "                         " + SELECT_RATES_FROM_DECISION_DAILYBAR_OUTPUT +
                "                             WHERE Occupancy_Date in (:dates) " +
                "                                 AND " + PRODUCT_ID_NULL_OR_DAILY +
                "                         ) AS npt " +
                "                         LEFT JOIN ( " +
                "                             SELECT PGB.Decision_id, PGB.Occupancy_Date, PGB.Accom_Type_ID " +
                "                                 ,Single_Rate, Double_Rate, Triple_rate, Quad_Rate, Quint_Rate " +
                "                                 ,Adult_Rate, Child_Rate " +
                "                                 ,One_Child_Rate, Two_Child_Rate, Three_Child_Rate, Four_Child_Rate, Five_Child_Rate " +
                "                                 ,Child_Age_1_Rate, Child_Age_2_Rate, Child_Age_3_Rate, Child_Age_4_Rate " +
                "                                 ,Product_ID " +
                "                             FROM ( " +
                "                                 SELECT Occupancy_Date, Accom_Type_ID, MAX(D.Decision_id) AS Decision_id " +
                "                                 FROM dbo.PACE_Dailybar_Output DBR " +
                "                                 INNER JOIN Decision D ON DBR.Decision_ID = D.Decision_ID " +
                "                                     AND Occupancy_Date in (:dates) " +
                "                                     AND D.End_DTTM  <= :lastUploadedDate " +
                "                                     AND " + PRODUCT_ID_NULL_OR_DAILY +
                "                                 GROUP BY Occupancy_Date, Accom_Type_ID " +
                "                             ) AS PGB " +
                "                             INNER JOIN PACE_Dailybar_Output PDB ON PGB.Occupancy_Date = PDB.Occupancy_Date " +
                "                                 AND PGB.Accom_Type_ID = PDB.Accom_Type_ID " +
                "                                 AND PGB.Decision_id = PDB.Decision_id " +
                "                         ) AS pt " +
                "                         ON pt.Occupancy_Date = npt.Occupancy_Date " +
                "                             AND pt.Accom_Type_ID = npt.Accom_Type_ID " +
                "                             AND ((pt.product_id IS null AND npt.product_id IS null ) OR (pt.product_id = npt.product_id)) " + WHERE_RATE_NOT_EQUAL_OR_DATE_NULL +
                "                     ) AS dfd " +
                "                     INNER JOIN Accom_Type AS at ON dfd.Accom_Type_Id = at.Accom_Type_ID " +
                "                     INNER JOIN Rate_Unqualified AS rq ON dfd.Rate_Unqualified_ID = rq.Rate_Unqualified_ID " +
                "                 ) AS filteredAccomClass " +
                "                 GROUP BY Accom_Class_Id, filteredAccomClass.Occupancy_Date " +
                "             ) AS src " +
                "             ON act.Accom_Class_ID = src.Accom_Class_ID " +
                "         ) AS map " +
                "         ON dbo.Occupancy_Date = map.Occupancy_Date " +
                "             AND dbo.Accom_Type_ID = map.Accom_Type_ID " +
                "         WHERE " + PRODUCT_ID_NULL_OR_DAILY +
                "     ) dailybarwithmisc " +
                " ) dailybarwithmiscandtax " +
                " ORDER BY " + OCCUPANCY_ACCOM_TYPE_RATE_CODE;
    }

    public static String getDecisionDailyBarOutputDifferentialUpToDecimal(int roundingDepth, boolean isIndependentProductFlow) {
        return "" +
                " SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + roundedRates(roundingDepth) + " ,product_id" +
                " FROM ( " +
                "     SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + RATES_WITH_TAX_SELECT_COLUMNS + ", product_id" + FROM +
                "         SELECT npt.* " +
                "         FROM ( " +
                "         " + SELECT_RATES_FROM_DECISION_DAILYBAR_OUTPUT +
                "             WHERE " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                "                 AND " + (isIndependentProductFlow ? PRODUCT_ID_IN_INDEPENDENT_PRODUCT : PRODUCT_ID_NULL_OR_DAILY) +
                "         ) AS npt " +
                "         LEFT JOIN ( " +
                "             SELECT PGB.Decision_id, PGB.Occupancy_Date, PGB.Accom_Type_ID " +
                "                 ,Single_Rate, Double_Rate, Triple_rate, Quad_Rate, Quint_Rate " +
                "                 ,Adult_Rate, Child_Rate " +
                "                 ,One_Child_Rate, Two_Child_Rate, Three_Child_Rate, Four_Child_Rate, Five_Child_Rate " +
                "                 ,Child_Age_1_Rate, Child_Age_2_Rate, Child_Age_3_Rate, Child_Age_4_Rate " +
                "                 ,Product_ID " +
                "             FROM ( " +
                "                 SELECT Occupancy_Date, Accom_Type_ID, MAX(D.Decision_id) AS Decision_id " +
                "                 FROM dbo.PACE_Dailybar_Output DBR " +
                "                 INNER JOIN Decision D ON DBR.Decision_ID = D.Decision_ID " +
                "                     AND " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                "                     AND D.End_DTTM  <= :lastUploadedDate " +
                "                     AND D.Decision_ID >= :latestFullRefreshDecId " +
                "                     AND " + (isIndependentProductFlow ? PRODUCT_ID_IN_INDEPENDENT_PRODUCT : PRODUCT_ID_NULL_OR_DAILY) +
                "                 GROUP BY Occupancy_Date, Accom_Type_ID " +
                "             ) AS PGB " +
                "             INNER JOIN PACE_Dailybar_Output PDB ON PGB.Occupancy_Date = PDB.Occupancy_Date " +
                "                 AND PGB.Accom_Type_ID = PDB.Accom_Type_ID " +
                "                 AND PGB.Decision_id = PDB.Decision_id " +
                "         ) AS pt " +
                "         ON pt.Occupancy_Date = npt.Occupancy_Date " +
                "             AND pt.Accom_Type_ID = npt.Accom_Type_ID  " +
                "             AND ((pt.product_id IS null AND npt.product_id IS null ) OR (pt.product_id = npt.product_id)) " + WHERE_RATE_NOT_EQUAL_OR_DATE_NULL +
                "     ) AS dfd " +
                "     INNER JOIN Accom_Type AS at ON dfd.Accom_Type_Id = at.Accom_Type_ID " +
                "     INNER JOIN Rate_Unqualified AS rq ON dfd.Rate_Unqualified_ID = rq.Rate_Unqualified_ID " +
                " ) AS dailybarwithtax " +
                " ORDER BY " + OCCUPANCY_ACCOM_TYPE_RATE_CODE;
    }

    public static String getESDecisionDailyBarOutputFull(int roundingDepth) {
        return "" +
                " SELECT Occupancy_Date, Accom_Type_Code, ES_Rate_Code AS Rate_Code_Name " + roundedRates(roundingDepth) +
                " FROM ( " +
                "     SELECT Occupancy_Date, Accom_Type_Code, Product_ID " +
                "         ,((Single_Rate + :miscAdjustment) + ((Single_Rate + :miscAdjustment) * :taxFactor)) AS Single_Rate " +
                "         ,((Double_Rate + :miscAdjustment) + ((Double_Rate + :miscAdjustment) * :taxFactor)) AS Double_Rate " +
                "         ,((Triple_Rate + :miscAdjustment) + ((Triple_Rate + :miscAdjustment) * :taxFactor)) AS Triple_Rate " +
                "         ,((Quad_Rate   + :miscAdjustment) + ((Quad_Rate   + :miscAdjustment) * :taxFactor)) AS Quad_Rate " +
                "         ,((Quint_Rate  + :miscAdjustment) + ((Quint_Rate  + :miscAdjustment) * :taxFactor)) AS Quint_Rate " +
                "         ,(Adult_Rate       + (Adult_Rate       * :taxFactor)) AS Adult_Rate " +
                "         ,(Child_Rate       + (Child_Rate       * :taxFactor)) AS Child_Rate " +
                "         ,(One_Child_Rate   + (One_Child_Rate   * :taxFactor)) AS One_Child_Rate " +
                "         ,(Two_Child_Rate   + (Two_Child_Rate   * :taxFactor)) AS Two_Child_Rate " +
                "         ,(Three_Child_Rate + (Three_Child_Rate * :taxFactor)) AS Three_Child_Rate " +
                "         ,(Four_Child_Rate  + (Four_Child_Rate  * :taxFactor)) AS Four_Child_Rate " +
                "         ,(Five_Child_Rate  + (Five_Child_Rate  * :taxFactor)) AS Five_Child_Rate " +
                "         ,(Child_Age_1_Rate + (Child_Age_1_Rate * :taxFactor)) AS Child_Age_1_Rate " +
                "         ,(Child_Age_2_Rate + (Child_Age_2_Rate * :taxFactor)) AS Child_Age_2_Rate " +
                "         ,(Child_Age_3_Rate + (Child_Age_3_Rate * :taxFactor)) AS Child_Age_3_Rate " +
                "         ,(Child_Age_4_Rate + (Child_Age_4_Rate * :taxFactor)) AS Child_Age_4_Rate " +
                "     FROM Decision_Dailybar_Output dbo " +
                "     INNER JOIN Accom_Type at ON dbo.Accom_Type_Id = at.Accom_Type_ID " +
                "     WHERE Product_ID in (" +
                "         SELECT Product_ID " +
                "         FROM Product " +
                "         WHERE Type = 'Extended Stay' " +
                "     ) " +
                "         AND " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                " ) AS dailybarwithmiscandtax " +
                " JOIN ( " +
                "     SELECT pd.Product_ID,pd.ES_Rate_Code " +
                "     FROM ES_Product_Definition as pd " +
                "     JOIN Product p ON pd.Product_ID = p.Product_ID " +
                "     WHERE p.Type = 'Extended Stay' " +
                " ) AS espd " +
                " ON dailybarwithmiscandtax.Product_id = espd.Product_ID " +
                " ORDER BY " + OCCUPANCY_ACCOM_TYPE_RATE_CODE;
    }

    public static String getESDecisionDailyBarOutputDifferential(int roundingDepth) {
        return "" +
                " SELECT Occupancy_Date, Accom_Type_Code, ES_Rate_Code AS Rate_Code_Name " + roundedRates(roundingDepth) +
                " FROM ( " +
                "     SELECT Occupancy_Date, Accom_Type_Code, Product_ID " +
                "         ,((Single_Rate + :miscAdjustment) + ((Single_Rate + :miscAdjustment) * :taxFactor)) AS Single_Rate " +
                "         ,((Double_Rate + :miscAdjustment) + ((Double_Rate + :miscAdjustment) * :taxFactor)) AS Double_Rate " +
                "         ,((Triple_Rate + :miscAdjustment) + ((Triple_Rate + :miscAdjustment) * :taxFactor)) AS Triple_Rate " +
                "         ,((Quad_Rate   + :miscAdjustment) + ((Quad_Rate   + :miscAdjustment) * :taxFactor)) AS Quad_Rate " +
                "         ,((Quint_Rate  + :miscAdjustment) + ((Quint_Rate  + :miscAdjustment) * :taxFactor)) AS Quint_Rate " +
                "         ,(Adult_Rate       + (Adult_Rate       * :taxFactor)) AS Adult_Rate " +
                "         ,(Child_Rate       + (Child_Rate       * :taxFactor)) AS Child_Rate " +
                "         ,(One_Child_Rate   + (One_Child_Rate   * :taxFactor)) AS One_Child_Rate " +
                "         ,(Two_Child_Rate   + (Two_Child_Rate   * :taxFactor)) AS Two_Child_Rate " +
                "         ,(Three_Child_Rate + (Three_Child_Rate * :taxFactor)) AS Three_Child_Rate " +
                "         ,(Four_Child_Rate  + (Four_Child_Rate  * :taxFactor)) AS Four_Child_Rate " +
                "         ,(Five_Child_Rate  + (Five_Child_Rate  * :taxFactor)) AS Five_Child_Rate " +
                "         ,(Child_Age_1_Rate + (Child_Age_1_Rate * :taxFactor)) AS Child_Age_1_Rate " +
                "         ,(Child_Age_2_Rate + (Child_Age_2_Rate * :taxFactor)) AS Child_Age_2_Rate " +
                "         ,(Child_Age_3_Rate + (Child_Age_3_Rate * :taxFactor)) AS Child_Age_3_Rate " +
                "         ,(Child_Age_4_Rate + (Child_Age_4_Rate * :taxFactor)) AS Child_Age_4_Rate " +
                "     FROM Decision_Dailybar_Output dbo " +
                "     INNER JOIN Accom_Type at ON dbo.Accom_Type_Id = at.Accom_Type_ID " +
                "     WHERE Product_ID IN ( " +
                "         SELECT Product_ID " +
                "         FROM Product where Type = 'Extended Stay' ) " +
                "     AND Decision_ID IN ( " +
                "         SELECT Decision_id " +
                "         FROM Decision " +
                "         WHERE Decision_Type_ID = ( " +
                "             SELECT Decision_Type_ID " +
                "             FROM Decision_Type " +
                "             WHERE Decision_Type_Name = 'ES DailyBar' ) " +
                "         AND Start_DTTM > :lastUploadDate ) " +
                "     AND " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                " ) AS dailybarwithmiscandtax " +
                " JOIN ( " +
                "     SELECT pd.Product_ID, pd.ES_Rate_Code " +
                "     FROM ES_Product_Definition AS pd " +
                "     JOIN Product p ON pd.Product_ID = p.Product_ID " +
                "     WHERE p.Type = 'Extended Stay' " +
                " ) AS espd " +
                " ON dailybarwithmiscandtax.Product_id = espd.Product_ID " +
                " ORDER BY " + OCCUPANCY_ACCOM_TYPE_RATE_CODE;
    }

    public static String getDecisionDailyBarOutputDifferentialUpToDecimalOperaChildBuckets(int roundingDepth, boolean isIndependentProductFlow) {
        return "" +
                " SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + roundedRates(roundingDepth) + " ,product_id" +
                " FROM ( " +
                "     SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + RATES_WITH_TAX_SELECT_COLUMNS + ",product_id" + FROM +
                "         SELECT npt.* " +
                "         FROM ( " +
                "         " + SELECT_RATES_FROM_DECISION_DAILYBAR_OUTPUT +
                "             WHERE  " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                "                 AND " + (isIndependentProductFlow ? PRODUCT_ID_IN_INDEPENDENT_PRODUCT : PRODUCT_ID_NULL_OR_DAILY) +
                "         ) AS npt " +
                "         LEFT JOIN ( " +
                "             SELECT PGB.Decision_id, PGB.Occupancy_Date, PGB.Accom_Type_ID " +
                "                 ,Single_Rate, Double_Rate, Triple_rate, Quad_Rate, Quint_Rate " +
                "                 ,Adult_Rate, Child_Rate " +
                "                 ,Child_Age_1_Rate, Child_Age_2_Rate, Child_Age_3_Rate, Child_Age_4_Rate " +
                "                 ,One_Child_Rate, Two_Child_Rate, Three_Child_Rate, Four_Child_Rate, Five_Child_Rate " +
                "                 ,Product_ID " +
                "             FROM ( " +
                "                 SELECT Occupancy_Date, Accom_Type_ID, MAX(D.Decision_id) AS Decision_id " +
                "                 FROM dbo.PACE_Dailybar_Output DBR " +
                "                 INNER JOIN Decision D ON DBR.Decision_ID = D.Decision_ID " +
                "                     AND " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                "                     AND D.End_DTTM <= :lastUploadedDate " +
                "                     AND D.Decision_ID >= :latestFullRefreshDecId " +
                "                     AND " + (isIndependentProductFlow ? PRODUCT_ID_IN_INDEPENDENT_PRODUCT : PRODUCT_ID_NULL_OR_DAILY) +
                "                 GROUP BY Occupancy_Date,Accom_Type_ID " +
                "             ) AS PGB " +
                "             INNER JOIN PACE_Dailybar_Output PDB ON PGB.Occupancy_Date = PDB.Occupancy_Date " +
                "                 AND PGB.Accom_Type_ID = PDB.Accom_Type_ID " +
                "                 AND PGB.Decision_id = PDB.Decision_id " +
                "         ) AS pt " +
                "         ON pt.Occupancy_Date = npt.Occupancy_Date " +
                "             AND pt.Accom_Type_ID = npt.Accom_Type_ID " +
                "             AND ((pt.product_id IS null AND npt.product_id IS null) OR (pt.product_id = npt.product_id)) " + WHERE_RATE_NOT_EQUAL_OR_DATE_NULL +
                "     ) AS dfd " +
                "     INNER JOIN Accom_Type AS at ON dfd.Accom_Type_Id = at.Accom_Type_ID " +
                "     INNER JOIN Rate_Unqualified AS rq ON dfd.Rate_Unqualified_ID = rq.Rate_Unqualified_ID " +
                " ) as dailybarwithtax ORDER BY " + OCCUPANCY_ACCOM_TYPE_RATE_CODE;
    }

    public static String getDecisionDailyBarOutputFullForAgileRateUpToDecimal(boolean retrieveOne, int roundingDepth) {
        return "" +
                " SELECT " + (retrieveOne ? "TOP(1) " : "") + OCCUPANCY_ACCOM_TYPE_RATE_CODE + roundedRates(roundingDepth) + ", product_id" +
                " FROM ( " +
                "     SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + RATES_WITH_TAX_SELECT_COLUMNS + ", product_id" + FROM +
                "         SELECT Occupancy_Date, at.Accom_Type_Code, rq.Rate_Code_Name " + RATES_WITH_ADJUSTMENT_AND_PRODUCT_ID +
                "         WHERE " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                "             AND dbo.product_id = :productId " +
                "     ) dailybarwithmisc " +
                " ) dailybarwithmiscandtax ORDER BY " + OCCUPANCY_ACCOM_TYPE_RATE_CODE;
    }

    public static String getDecisionDailyBarOutputDifferentialUpToDecimalAgileRates(boolean retrieveOne, int roundingDepth) {
        return "" +
                " SELECT " + (retrieveOne ? "TOP(1) " : "") + OCCUPANCY_ACCOM_TYPE_RATE_CODE +
                "     ,CAST((CASE WHEN Single_Rate<0      THEN 0 ELSE Single_Rate      END) AS DECIMAL(19," + roundingDepth + ")) AS Single_Rate " +
                "     ,CAST((CASE WHEN Double_Rate<0      THEN 0 ELSE Double_Rate      END) AS DECIMAL(19," + roundingDepth + ")) AS Double_Rate " +
                "     ,CAST((CASE WHEN Triple_Rate<0      THEN 0 ELSE Triple_Rate      END) AS DECIMAL(19," + roundingDepth + ")) AS Triple_Rate " +
                "     ,CAST((CASE WHEN Quad_Rate<0        THEN 0 ELSE Quad_Rate        END) AS DECIMAL(19," + roundingDepth + ")) AS Quad_Rate " +
                "     ,CAST((CASE WHEN Quint_Rate<0       THEN 0 ELSE Quint_Rate       END) AS DECIMAL(19," + roundingDepth + ")) AS Quint_Rate " +
                "     ,CAST((CASE WHEN Adult_Rate<0       THEN 0 ELSE Adult_Rate       END) AS DECIMAL(19," + roundingDepth + ")) AS Adult_Rate " +
                "     ,CAST((CASE WHEN Child_Rate<0       THEN 0 ELSE Child_Rate       END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Rate " +
                "     ,CAST((CASE WHEN One_Child_Rate<0   THEN 0 ELSE One_Child_Rate   END) AS DECIMAL(19," + roundingDepth + ")) AS One_Child_Rate " +
                "     ,CAST((CASE WHEN Two_Child_Rate<0   THEN 0 ELSE Two_Child_Rate   END) AS DECIMAL(19," + roundingDepth + ")) AS Two_Child_Rate " +
                "     ,CAST((CASE WHEN Three_Child_Rate<0 THEN 0 ELSE Three_Child_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Three_Child_Rate " +
                "     ,CAST((CASE WHEN Four_Child_Rate<0  THEN 0 ELSE Four_Child_Rate  END) AS DECIMAL(19," + roundingDepth + ")) AS Four_Child_Rate " +
                "     ,CAST((CASE WHEN Five_Child_Rate<0  THEN 0 ELSE Five_Child_Rate  END) AS DECIMAL(19," + roundingDepth + ")) AS Five_Child_Rate " +
                " FROM ( " +
                "     SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE +
                "         ,(Single_Rate      + (Single_Rate      * :taxFactor)) AS Single_Rate " +
                "         ,(Double_Rate      + (Double_Rate      * :taxFactor)) AS Double_Rate " +
                "         ,(Triple_Rate      + (Triple_Rate      * :taxFactor)) AS Triple_Rate " +
                "         ,(Quad_Rate        + (Quad_Rate        * :taxFactor)) AS Quad_Rate " +
                "         ,(Quint_Rate       + (Quint_Rate       * :taxFactor)) AS Quint_Rate " +
                "         ,(Adult_Rate       + (Adult_Rate       * :taxFactor)) AS Adult_Rate " +
                "         ,(Child_Rate       + (Child_Rate       * :taxFactor)) AS Child_Rate " +
                "         ,(One_Child_Rate   + (One_Child_Rate   * :taxFactor)) AS One_Child_Rate " +
                "         ,(Two_Child_Rate   + (Two_Child_Rate   * :taxFactor)) AS Two_Child_Rate " +
                "         ,(Three_Child_Rate + (Three_Child_Rate * :taxFactor)) AS Three_Child_Rate " +
                "         ,(Four_Child_Rate  + (Four_Child_Rate  * :taxFactor)) AS Four_Child_Rate " +
                "         ,(Five_Child_Rate  + (Five_Child_Rate  * :taxFactor)) AS Five_Child_Rate " +
                "     FROM ( " +
                "         SELECT npt.* " +
                "         FROM ( " +
                "             SELECT DBR.Occupancy_Date, Accom_Type_ID " +
                "                 ,(Single_Rate + :miscAdjustment) AS Single_Rate " +
                "                 ,(Double_Rate + :miscAdjustment) AS Double_Rate " +
                "                 ,(Triple_Rate + :miscAdjustment) AS Triple_Rate " +
                "                 ,(Quad_Rate   + :miscAdjustment) AS Quad_Rate " +
                "                 ,(Quint_Rate  + :miscAdjustment) AS Quint_Rate " +
                "                 ,Adult_Rate, Child_Rate, Rate_Unqualified_ID, Product_ID " +
                "                 ,One_Child_Rate, Two_Child_Rate, Three_Child_Rate, Four_Child_Rate, Five_Child_Rate " +
                "             FROM dbo.Decision_Dailybar_Output DBR " +
                "             WHERE " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                "                 AND DBR.product_id = :productId " +
                "         ) AS npt " +
                "         LEFT JOIN ( " +
                "             SELECT PGB.Decision_id, PGB.Occupancy_Date, PGB.Accom_Type_ID, Single_Rate,Double_Rate " +
                "                 ,Triple_rate, Quad_Rate, Quint_Rate, Adult_Rate, Child_Rate " +
                "                 ,One_Child_Rate, Two_Child_Rate, Three_Child_Rate, Four_Child_Rate, Five_Child_Rate " +
                "                 ,Product_ID " +
                "             FROM ( " +
                "                 SELECT Occupancy_Date, Accom_Type_ID, MAX(D.Decision_id) AS Decision_id " +
                "                 FROM dbo.PACE_Dailybar_Output DBR " +
                "                 INNER JOIN Decision D ON DBR.Decision_ID = D.Decision_ID " +
                "                     AND " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                "                     AND D.End_DTTM  <= :lastUploadedDate " +
                "                     AND DBR.product_id = :productId " +
                "                     AND D.Decision_ID >= :latestFullRefreshDecId " +
                "                 GROUP BY Occupancy_Date,Accom_Type_ID " +
                "             ) AS PGB " +
                "             INNER JOIN PACE_Dailybar_Output PDB ON PGB.Occupancy_Date = PDB.Occupancy_Date " +
                "                 AND PGB.Accom_Type_ID = PDB.Accom_Type_ID " +
                "                 AND PGB.Decision_id = PDB.Decision_id " +
                "         ) AS pt " +
                "         ON pt.Occupancy_Date = npt.Occupancy_Date " +
                "             AND pt.Accom_Type_ID = npt.Accom_Type_ID " +
                "             AND pt.product_id = npt.Product_ID " +
                "         WHERE pt.Single_Rate  <> npt.Single_Rate " +
                "             OR pt.Double_Rate <> npt.Double_Rate " +
                "             OR pt.Triple_Rate <> npt.Triple_Rate " +
                "             OR pt.Quad_Rate   <> npt.Quad_Rate " +
                "             OR pt.Quint_Rate  <> npt.Quint_Rate " +
                "             OR pt.Adult_Rate  <> npt.Adult_Rate " +
                "             OR pt.Child_Rate  <> npt.Child_Rate " +
                "             OR isNull(pt.One_Child_Rate,   -1) <> isNull(npt.One_Child_Rate,   -1) " +
                "             OR isNull(pt.Two_Child_Rate,   -1) <> isNull(npt.Two_Child_Rate,   -1) " +
                "             OR isNull(pt.Three_Child_Rate, -1) <> isNull(npt.Three_Child_Rate, -1) " +
                "             OR isNull(pt.Four_Child_Rate,  -1) <> isNull(npt.Four_Child_Rate,  -1) " +
                "             OR isNull(pt.Five_Child_Rate,  -1) <> isNull(npt.Five_Child_Rate,  -1) " +
                "             OR pt.Occupancy_Date IS null " +
                "     ) AS dfd " +
                "     INNER JOIN Accom_Type AS at ON dfd.Accom_Type_Id = at.Accom_Type_ID " +
                "     INNER JOIN Rate_Unqualified AS rq ON dfd.Rate_Unqualified_ID = rq.Rate_Unqualified_ID " +
                " ) as dailybarwithtax " +
                " ORDER BY " + OCCUPANCY_ACCOM_TYPE_RATE_CODE;
    }

    public static String getDecisionDailyBarOutputDifferentialUpToDecimalOperaChildBucketsAgileRates(boolean retrieveOne, int roundingDepth) {
        return "" +
                " SELECT " + (retrieveOne ? "TOP(1) " : "") + OCCUPANCY_ACCOM_TYPE_RATE_CODE + roundedRates(roundingDepth) + ", product_id" +
                " FROM ( " +
                "     SELECT " + OCCUPANCY_ACCOM_TYPE_RATE_CODE + RATES_WITH_TAX_SELECT_COLUMNS + ", product_id" + FROM +
                "         SELECT npt.* " +
                "         FROM ( " +
                "         " + SELECT_RATES_FROM_DECISION_DAILYBAR_OUTPUT +
                "             WHERE " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                "                 AND DBR.product_id = :productId " +
                "         ) AS npt " +
                "         LEFT JOIN ( " +
                "             SELECT PGB.Decision_id, PGB.Occupancy_Date, PGB.Accom_Type_ID " +
                "                 ,Single_Rate, Double_Rate, Triple_Rate, Quad_Rate, Quint_Rate " +
                "                 ,Adult_Rate, Child_Rate " +
                "                 ,Child_Age_1_Rate, Child_Age_2_Rate, Child_Age_3_Rate, Child_Age_4_Rate" +
                "                 ,One_Child_Rate, Two_Child_Rate, Three_Child_Rate, Four_Child_Rate, Five_Child_Rate " +
                "                 ,Product_ID " +
                "             FROM ( " +
                "                 SELECT Occupancy_Date, Accom_Type_ID, MAX(D.Decision_id) AS Decision_id " +
                "                 FROM dbo.PACE_Dailybar_Output DBR " +
                "                 INNER JOIN Decision D ON DBR.Decision_ID = D.Decision_ID " +
                "                     AND " + OCCUPANCY_DATE_BETWEEN_DECISION_START_AND_END +
                "                     AND D.End_DTTM <= :lastUploadedDate " +
                "                     AND DBR.product_id = :productId " +
                "                     AND D.Decision_ID >= :latestFullRefreshDecId " +
                "                 GROUP BY Occupancy_Date, Accom_Type_ID " +
                "             ) AS PGB " +
                "             INNER JOIN PACE_Dailybar_Output PDB ON PGB.Occupancy_Date = PDB.Occupancy_Date " +
                "                 AND PGB.Accom_Type_ID = PDB.Accom_Type_ID " +
                "                 AND PGB.Decision_id = PDB.Decision_id " +
                "         ) AS pt " +
                "         ON pt.Occupancy_Date = npt.Occupancy_Date " +
                "             AND pt.Accom_Type_ID = npt.Accom_Type_ID " +
                "             AND pt.product_id = npt.Product_ID " + WHERE_RATE_NOT_EQUAL_OR_DATE_NULL +
                "     ) AS dfd " +
                "     INNER JOIN Accom_Type AS at ON dfd.Accom_Type_Id = at.Accom_Type_ID " +
                "     INNER JOIN Rate_Unqualified AS rq ON dfd.Rate_Unqualified_ID = rq.Rate_Unqualified_ID " +
                " ) as dailybarwithtax " +
                " ORDER BY " + OCCUPANCY_ACCOM_TYPE_RATE_CODE;
    }
}
