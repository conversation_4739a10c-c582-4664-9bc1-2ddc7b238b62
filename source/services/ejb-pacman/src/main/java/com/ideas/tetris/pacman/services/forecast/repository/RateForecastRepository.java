package com.ideas.tetris.pacman.services.forecast.repository;


import com.ideas.tetris.pacman.services.forecast.dto.RateForecastDto;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Component
public class RateForecastRepository {
    @Autowired
    private SasDbToolService sasDbToolService;

    public List<RateForecastDto> retrieveRateForecasts(Integer propertyId, LocalDate startDate, LocalDate endDate) {
        String startDt = LocalDateUtils.getSasDate(startDate);
        String endDt = LocalDateUtils.getSasDate(endDate);
        var query = "select process_grp_id as processGrpId, lump_id as lumpId, room_category_id as roomCategoryId, arrival_dt as arrivalDt format best12. , los, rate from tenant.rate_fcst where arrivalDt between '"+startDt+"'d and '"+endDt+"'d order by processGrpId, lumpId, roomCategoryId, arrivalDt;";
        var sasDbQueryResult = sasDbToolService.executeQueryTenant(propertyId,query);
        if (Objects.isNull(sasDbQueryResult)) {
            return List.of();
        }
        var data = sasDbQueryResult.getData();
        if (Objects.isNull(data)) {
            return List.of();
        }
        return data.stream()
                .map(list -> new RateForecastDto(
                        ((Double)list.get(0)).intValue(),
                        ((Double)list.get(1)).intValue(),
                        ((Double)list.get(2)).intValue(),
                        LocalDateUtils.toJavaLocalDateFromSasDate(((Double) list.get(3)).longValue()),
                        ((Double)list.get(4)).intValue(),
                        (Double) list.get(5)))
                .collect(Collectors.toList());
    }
}

