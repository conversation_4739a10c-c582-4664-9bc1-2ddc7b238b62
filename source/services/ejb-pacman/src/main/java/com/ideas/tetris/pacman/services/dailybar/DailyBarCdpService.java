package com.ideas.tetris.pacman.services.dailybar;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Slf4j
@Component
@Transactional
public class DailyBarCdpService extends BaseDailyBarService {

    @Autowired
	private SyncEventAggregatorService syncEventAggregatorService;


    @Override
    public void createDecisions() {
        createDailyBarDecisionsIfConfigured();
    }

    @Override
	public
    Date getOptimizationWindowEndDate() {
        if (pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ON_DEMAND_IDP_ENABLED)
                && syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue() && (!PacmanWorkContextHelper.isHiltonClientCode() || pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.OPERATION_TYPE_AGNOSTIC_UPLOAD_ENABLED))) {
            log.info("OnDemand Optimization enabled, sync flag is on, running for an upload window end date BDE");
            return dateService.getDecisionUploadWindowEndDate();
        }
        return dateService.getOptimizationWindowEndDateCDP();
    }
}
