package com.ideas.tetris.pacman.services.datafeed.endpoint;


import com.ideas.tetris.pacman.services.datafeed.service.DatafeedQueryEndpoint;
import com.ideas.tetris.pacman.services.datafeed.service.DatafeedServiceEndpoint;

import java.util.function.Predicate;

public final class DatafeedEndPointPredicates {

    public static Predicate<Endpoint> isClientBucketEndPoint(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return endpoint -> datafeedEndPointCriteria.getClientBuckets().contains(endpoint.getBucket());
    }

    public static Predicate<Endpoint> isFrequencyEndPoint(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return endpoint -> datafeedEndPointCriteria.getFrequencies().contains(endpoint.getFrequencyType());
    }

    public static Predicate<DatafeedQueryEndpoint> isQueryEndPoint(String datafeedType) {
        return endpoint -> endpoint.getEntityClass().getSimpleName().equals(datafeedType);
    }

    public static Predicate<DatafeedServiceEndpoint> isServiceEndPoint(String datafeedType) {
        return endpoint -> endpoint.getEntityClass().getSimpleName().equals(datafeedType);
    }

}
