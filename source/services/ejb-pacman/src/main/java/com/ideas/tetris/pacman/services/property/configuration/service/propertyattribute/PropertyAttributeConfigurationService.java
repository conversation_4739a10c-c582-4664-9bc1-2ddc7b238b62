package com.ideas.tetris.pacman.services.property.configuration.service.propertyattribute;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.AttributeDisplayType;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientAttribute;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientAttributeValue;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientPropertyAttributePairing;
import com.ideas.tetris.pacman.services.fds.ups.UPSService;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.PropertyAttributePropertyNewAttributeConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecordStatus;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.rules.RulesService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import javax.persistence.Query;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@PropertyAttributeConfigurationService.Qualifier
@Component
@Transactional
public class PropertyAttributeConfigurationService extends AbstractPropertyConfigurationService {
    private static final Logger LOGGER = Logger.getLogger(PropertyAttributeConfigurationService.class);

    private static final int FIELD_LENGTH = 150;

    @Autowired
	protected RulesService rulesService;
    @Autowired
	protected PropertyGroupService propertyGroupService;
    @Autowired
	protected AuthorizationService authorizationService;
    @Autowired
	protected PacmanConfigParamsService configParamsService;
    @Autowired
	protected UPSService upsService;


    public void setRulesService(RulesService rulesService) {
        this.rulesService = rulesService;
    }

    @Override
    public PropertyConfigurationRecordType getPropertyConfigurationRecordType() {
        return PropertyConfigurationRecordType.PA;
    }

    @Override
    public List<PropertyConfigurationRecordFailure> doValidate(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId) {
        List<PropertyConfigurationRecordFailure> exceptions = new ArrayList<PropertyConfigurationRecordFailure>();

        PropertyAttributePropertyNewAttributeConfigurationDto papcd = (PropertyAttributePropertyNewAttributeConfigurationDto) propertyConfigurationDto;

        //OLD Hilton Custom Attributes
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.GLOBAL_AREA, papcd.getGlobalArea());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.BRAND_CODE, papcd.getBrandCode());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.MANAGEMENT_TYPE, papcd.getManagementType());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.MANAGEMENT_COMPANY, papcd.getManagementCompany());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.LOCATION_TYPE, papcd.getLocationType());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.COUNTRY, papcd.getCountry());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.NUMBER_OF_ROOMS, papcd.getNumberOfRooms() != null ? String.valueOf(papcd.getNumberOfRooms()) : null);
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.RMCC, papcd.getRmcc());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.MARKET, papcd.getMarket());

        //NEW Hilton Custom Attributes
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.OPENING_DATE, papcd.getOpeningDate());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.OPEN_STATUS, papcd.getOpenStatus());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.TIME_ZONE, papcd.getTimeZone());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.TIME_ZONE_NAME, papcd.getTimeZoneName());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.ZIP_POSTAL_CODE, papcd.getZipPostalCode());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.PROPERTY_OWNER, papcd.getPropertyOwner());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.GEOGRAPHIC_REGION, papcd.getGeographicRegion());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.PROPERTY_NAME, papcd.getPropertyName());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.RDRM, papcd.getRdrm());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.RMCC_DIRECTOR, papcd.getRmccDirector());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.RMCC_SR_MANAGER, papcd.getRmccSrManager());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.RMCC_MANAGER, papcd.getRmccManager());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.RMCC_ASSISTANT_DIRECTOR, papcd.getRmccAssistantDirector());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.REGIONAL_VP_RM, papcd.getRegionalVpRm());
        validateAttributeValue(exceptions, PropertyConfigurationClientAttributeMap.TIER, papcd.getTier());

        return exceptions;
    }

    @Override
    public void doHandle(PropertyConfigurationDto pcd, Integer propertyId) {
        // Delete All Property Attribute Pairs
        LOGGER.debug("Deleting PropertyAttributes for Property: " + pcd.getPropertyCode());
        deleteAllClientPropertyAttributePairing(propertyId);
        propertyGroupService.removePropertyFromPropertyGroups(propertyId);
        authorizationService.deletePropertyFromAuthorizationGroups(propertyId);

        // Add or update all the Property's configuration values
        LOGGER.info("Creating PropertyAttributes for Property: " + pcd.getPropertyCode());

        PropertyAttributePropertyNewAttributeConfigurationDto papcd = (PropertyAttributePropertyNewAttributeConfigurationDto) pcd;

        //OLD Hilton Custom Attributes
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.GLOBAL_AREA, StringUtils.left(papcd.getGlobalArea(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.BRAND_CODE, StringUtils.left(papcd.getBrandCode(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.MANAGEMENT_TYPE, StringUtils.left(papcd.getManagementType(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.MANAGEMENT_COMPANY, StringUtils.left(papcd.getManagementCompany(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.LOCATION_TYPE, StringUtils.left(papcd.getLocationType(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.COUNTRY, StringUtils.left(papcd.getCountry(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.NUMBER_OF_ROOMS, String.valueOf(papcd.getNumberOfRooms()));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.RMCC, StringUtils.left(papcd.getRmcc(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.MARKET, StringUtils.left(papcd.getMarket(), FIELD_LENGTH));

        //NEW Hilton Custom Attributes
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.OPENING_DATE, StringUtils.left(papcd.getOpeningDate(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.OPEN_STATUS, StringUtils.left(papcd.getOpenStatus(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.TIME_ZONE, StringUtils.left(papcd.getTimeZone(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.TIME_ZONE_NAME, StringUtils.left(papcd.getTimeZoneName(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.ZIP_POSTAL_CODE, StringUtils.left(papcd.getZipPostalCode(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.PROPERTY_OWNER, StringUtils.left(papcd.getPropertyOwner(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.GEOGRAPHIC_REGION, StringUtils.left(papcd.getGeographicRegion(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.PROPERTY_NAME, StringUtils.left(papcd.getPropertyName(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.RDRM, StringUtils.left(papcd.getRdrm(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.RMCC_DIRECTOR, StringUtils.left(papcd.getRmccDirector(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.RMCC_SR_MANAGER, StringUtils.left(papcd.getRmccSrManager(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.RMCC_MANAGER, StringUtils.left(papcd.getRmccManager(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.RMCC_ASSISTANT_DIRECTOR, StringUtils.left(papcd.getRmccAssistantDirector(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.REGIONAL_VP_RM, StringUtils.left(papcd.getRegionalVpRm(), FIELD_LENGTH));
        addOrUpdateConfigurationValue(propertyId, PropertyConfigurationClientAttributeMap.TIER, StringUtils.left(papcd.getTier(), FIELD_LENGTH));

        // Update user authorization groups based on property attributes
        rulesService.recomputeRuleBasedGroupsForNewProperty(propertyId);
        if (configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_CLIENT_ATTRIBUTES_ENABLED)) {
            //Update attribute assignments for the new property
            upsService.updateCustomAttributeAssignmentsForProperties(Arrays.asList(propertyId));
        }
    }

    public void validateAttributeValue(List<PropertyConfigurationRecordFailure> exceptions, PropertyConfigurationClientAttributeMap clientAttributeMap, String value) {
        String attributeName = clientAttributeMap.getAttributeName();
        String displayType = clientAttributeMap.getDisplayType();

        if (StringUtils.isNotEmpty(value) && value.length() > FIELD_LENGTH) {
            exceptions.add(new PropertyConfigurationRecordFailure(ConfigurationFileRecordStatus.WARNING, attributeName + " cannot be longer than " + FIELD_LENGTH + " characters.  The value has been trimmed."));
        } else {

            if (StringUtils.equalsIgnoreCase(displayType, "Numeric")) {
                try {
                    if (value == null || Integer.parseInt(value) < 0) {
                        exceptions.add(new PropertyConfigurationRecordFailure(attributeName + " must be a positive number"));
                    }
                } catch (NumberFormatException nfe) {
                    exceptions.add(new PropertyConfigurationRecordFailure(attributeName + " must be a positive number"));
                }
            } else if (StringUtils.equalsIgnoreCase(displayType, "Boolean") && (!StringUtils.equalsIgnoreCase(value, "Y") && !StringUtils.equalsIgnoreCase(value, "N"))) {
                exceptions.add(new PropertyConfigurationRecordFailure(attributeName + " must be Y or N"));
            }
        }
    }

    public void addOrUpdateConfigurationValue(Integer propertyId, PropertyConfigurationClientAttributeMap clientAttributeMap, String value) {
        if (StringUtils.isEmpty(value)) {
            LOGGER.warn("Unable to set null value to attribute: " + clientAttributeMap.getAttributeName() + " for Property: " + propertyId);
            return;
        }

        ClientAttribute clientAttribute = findOrCreateClientAttribute(clientAttributeMap);

        ClientAttributeValue clientAttributeValue = findClientAttributeValueByAttributeAndValue(clientAttribute, value);
        if (clientAttributeValue == null) {
            clientAttributeValue = new ClientAttributeValue();
            clientAttributeValue.setClientAttribute(clientAttribute);
            clientAttributeValue.setClientAttributeValue(value);
            clientAttributeValue.setStatus(Status.ACTIVE);

            if (StringUtils.equals(clientAttributeMap.getDefaultValue(), value)) {
                clientAttributeValue.setClientAttributeDefault(1);
            } else {
                clientAttributeValue.setClientAttributeDefault(0);
            }

            clientAttributeValue = globalCrudService.save(clientAttributeValue);

            if (configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_CLIENT_ATTRIBUTES_ENABLED)) {
                //Update Custom Attribute values in UPS
                upsService.updateCustomAttributeValue(clientAttribute);
            }
        }

        ClientPropertyAttributePairing clientPropertyAttributePairing = findClientAttributeValueByAttributeAndValue(propertyId, clientAttributeValue);
        if (clientPropertyAttributePairing == null) {
            clientPropertyAttributePairing = new ClientPropertyAttributePairing();
            clientPropertyAttributePairing.setClientAttributeValue(clientAttributeValue);
            clientPropertyAttributePairing.setPropertyID(propertyId);
            clientPropertyAttributePairing.setStatus(Status.ACTIVE);
            globalCrudService.save(clientPropertyAttributePairing);
        }
    }

    public ClientAttribute findOrCreateClientAttribute(PropertyConfigurationClientAttributeMap clientAttributeMap) {
        ClientAttribute clientAttribute = findClientAttributeByName(clientAttributeMap.getAttributeName());
        if (clientAttribute == null) {
            clientAttribute = new ClientAttribute();
            clientAttribute.setClientId(PlatformThreadLocalContextHolder.getWorkContext().getClientId());
            clientAttribute.setClientAttributeName(clientAttributeMap.getAttributeName());
            clientAttribute.setClientAttributeDescription(clientAttributeMap.getAttributeDescription());
            clientAttribute.setAttributeDisplayLength(Integer.valueOf(clientAttributeMap.getDisplayLength()));
            clientAttribute.setUserLengthEntry(clientAttributeMap.getUserEntryLength());
            clientAttribute.setAttributeDisplayType(findAttributeDisplayType(clientAttributeMap.getDisplayType()));
            clientAttribute = globalCrudService.save(clientAttribute);
            if (configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_CLIENT_ATTRIBUTES_ENABLED)) {
                //Create new attribute in UPS
                upsService.saveCustomAttributeValue(clientAttribute);
            }
        }

        return clientAttribute;
    }

    public ClientPropertyAttributePairing findClientAttributeValueByAttributeAndValue(Integer propertyId, ClientAttributeValue clientAttributeValue) {
        return (ClientPropertyAttributePairing) globalCrudService.findByNamedQuerySingleResult(ClientPropertyAttributePairing.BYPROPERTYID_ANDATTRIBUTE_VALUE,
                QueryParameter.with("propertyID", propertyId).and("clientAttributeValue", clientAttributeValue).parameters());
    }

    public ClientAttribute findClientAttributeByName(String name) {
        return (ClientAttribute) globalCrudService.findByNamedQuerySingleResult(ClientAttribute.BY_NAME,
                QueryParameter.with("name", name).and("clientId",
                        PacmanThreadLocalContextHolder.getWorkContext().getClientId()).parameters());
    }

    public AttributeDisplayType findAttributeDisplayType(String name) {
        return (AttributeDisplayType) globalCrudService.findByNamedQuerySingleResult(AttributeDisplayType.BY_NAME, QueryParameter.with("name", name).parameters());
    }

    public ClientAttributeValue findClientAttributeValueByAttributeAndValue(ClientAttribute clientAttribute, String value) {
        return (ClientAttributeValue) globalCrudService.findByNamedQuerySingleResult(ClientAttributeValue.BY_ATTRIBUTE_AND_VALUE,
                QueryParameter.with("clientAttribute", clientAttribute).and("value", value).parameters());
    }

    public void deleteAllClientPropertyAttributePairing(Integer propertyId) {
        Query query = globalCrudService.getEntityManager().createNamedQuery(ClientPropertyAttributePairing.DELETE_BY_PROPERTY_ID);
        query.setParameter("propertyId", propertyId);
        query.executeUpdate();
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
