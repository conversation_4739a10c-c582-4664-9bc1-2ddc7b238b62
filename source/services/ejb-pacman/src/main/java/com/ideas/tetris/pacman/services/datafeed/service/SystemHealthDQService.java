package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.datafeed.dto.InformationManagerSystemHealth;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.dto.DQIHealthDetails;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.dto.PropertyHealth;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.dto.PropertyHealthDetails;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.dto.SystemHealthCondition;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.service.SystemHealthService;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.collections.CollectionUtils;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class SystemHealthDQService {

    public static final String SYSTEM_HEALTH_DQI = "systemHealth.DQI.";
    public static final String DQI_COLOR_RED = "RED";
    public static final String DQI_COLOR_YELLOW = "YELLOW";
    public static final String DQI_COLOR_GREEN = "GREEN";

    private static final BigDecimal DQI_COLOR_RED_VALUE = BigDecimal.ONE;
    private static final BigDecimal DQI_COLOR_YELLOW_VALUE = BigDecimal.valueOf(0.25);
    private static final BigDecimal DQI_COLOR_GREEN_VALUE = BigDecimal.ZERO;

    @Autowired
	private SystemHealthService systemHealthService;

    @Autowired
	private AlertService alertService;

    public List getSystemHealthDetails() {
        List<PropertyHealth> propertyHealths = systemHealthService.getPropertyHealth();
        List<PropertyHealthDetails> dqiHealthDetails = new ArrayList<>();
        List<InformationManagerSystemHealth> systemHealthDetails = new ArrayList<>();
        if (CollectionUtils.isEmpty(propertyHealths)) {
            return dqiHealthDetails;
        }
        PropertyHealth propHealth = propertyHealths.get(0);

        ensureDQIStatusPresent(propHealth);

        Map<SystemHealthCondition, List<PropertyHealthDetails>> propertyHealthDetailsMap = systemHealthService.getPropertyHealthDetails(propHealth);
        dqiHealthDetails = propertyHealthDetailsMap.get(SystemHealthCondition.DQI_STATUS);

        if (null == dqiHealthDetails) {
            return Collections.emptyList();
        }

        populateSystemHealthDetails(systemHealthDetails, dqiHealthDetails, propHealth);
        updateDQINames(systemHealthDetails);
        return systemHealthDetails;
    }

    private void populateSystemHealthDetails(List<InformationManagerSystemHealth> systemHealthDetails, List<PropertyHealthDetails> dqiHealthDetails, PropertyHealth propHealth) {
        dqiHealthDetails.stream().forEach(e -> {
            DQIHealthDetails dqiHealth = (DQIHealthDetails) e;
            InformationManagerSystemHealth informationManagerSystemHealth = getInformationManagerSystemHealth(propHealth, dqiHealth);
            systemHealthDetails.add(informationManagerSystemHealth);
        });
    }

    private InformationManagerSystemHealth getInformationManagerSystemHealth(PropertyHealth propHealth, DQIHealthDetails dqiHealth) {
        InformationManagerSystemHealth informationManagerSystemHealth = new InformationManagerSystemHealth();
        informationManagerSystemHealth.setDqiName(dqiHealth.getDqiName());
        informationManagerSystemHealth.setDaysAnalysed(dqiHealth.getDaysAnalysed());
        informationManagerSystemHealth.setDaysFailed(dqiHealth.getDaysFailed());
        informationManagerSystemHealth.setIndicator(dqiHealth.getIndicator());
        informationManagerSystemHealth.setWeight(dqiHealth.getWeight());
        informationManagerSystemHealth.setTriggeredDataTime(getDateTimeInServerTimeZone(propHealth));
        informationManagerSystemHealth.setScore(getScore(dqiHealth.getIndicator()));
        return informationManagerSystemHealth;
    }

    private Date getDateTimeInServerTimeZone(PropertyHealth propHealth) {
        TimeZone propertyTimeZone = alertService.getPropertyTimeZone(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
        return DateUtil.getDateTimeByTimeZone(propHealth.getLastGoodExecDateTimeParameter().getTime(),
                Calendar.getInstance().getTimeZone(), propertyTimeZone);
    }

    private BigDecimal getScore(String indicator) {
        BigDecimal retValue = DQI_COLOR_GREEN_VALUE;

        if (indicator.equalsIgnoreCase(DQI_COLOR_YELLOW)) {
            retValue = DQI_COLOR_YELLOW_VALUE;
        } else if (indicator.equalsIgnoreCase(DQI_COLOR_RED)) {
            retValue = DQI_COLOR_RED_VALUE;
        }
        return retValue;
    }

    private void updateDQINames(List<InformationManagerSystemHealth> informationManagerSystemHealths) {
        for (InformationManagerSystemHealth systemHealth : informationManagerSystemHealths) {
            systemHealth.setDqiName(ResourceUtil.getText(SYSTEM_HEALTH_DQI + systemHealth.getDqiName(), Language.ENGLISH));
        }
    }

    private void ensureDQIStatusPresent(PropertyHealth propHealth) {
        if (CollectionUtils.isEmpty(propHealth.getHealthCheckCondition())) {
            propHealth.getHealthCheckCondition().add(SystemHealthCondition.DQI_STATUS);
        }
    }
}
