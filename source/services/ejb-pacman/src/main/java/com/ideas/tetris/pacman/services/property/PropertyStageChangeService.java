package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.PropertyCriteria;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.service.DecisionConfigurationService;
import com.ideas.tetris.pacman.services.limiteddatabuild.LDBService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationLTBDEService;
import com.ideas.tetris.pacman.services.property.dto.PropertyStageChange;
import com.ideas.tetris.pacman.services.virtualproperty.service.VirtualPropertyMappingService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import com.ideas.tetris.platform.services.jmsclient.TetrisEventManager;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Transactional
public class PropertyStageChangeService {
    private static final Logger LOGGER = Logger.getLogger(PropertyStageChangeService.class);

    @Autowired
    PacmanConfigParamsService configParamsService;
    @Autowired
    PropertyService propertyService;
    @Autowired
    TetrisEventManager tetrisEventManager;
    @Autowired
    @Qualifier("ldbService")
    LDBService ldbService;
    @Autowired
    DecisionConfigurationService decisionConfigurationService;
    @Autowired
    ClientPropertyCacheService clientPropertyCacheService;

    @Autowired
    VirtualPropertyMappingService virtualPropertyMappingService;
    @Inject
    PricingConfigurationLTBDEService pricingConfigurationLTBDEService;

    @GlobalCrudServiceBean.Qualifier
	@Qualifier("globalCrudServiceBean")
    @Autowired
	private CrudService globalCrudService;

    private String getCurrentUser() {
        return PacmanThreadLocalContextHolder.getPrincipal() != null ?
                PacmanThreadLocalContextHolder.getPrincipal().getDisplayName() : "NA";
    }


    public String getCurrentStageCode(int propertyId) {
        Stage currentStage = getCurrentStage(propertyId);
        return currentStage != null ? currentStage.getCode() : "unknown";
    }

    public void changeStage(int propertyId, String stageCode) {
        Stage stage = Stage.valueForCode(stageCode);
        changeStage(propertyId, stage, null, "Stage changed to " + stageCode);
    }

    public Property changeStage(int propertyId, Stage stage, Boolean srpFplosAtTotalLevel, String notes) {
        Property property = propertyService.getPropertyById(propertyId, false);
        switch (stage) {
            case PAUSED:
            case DORMANT:
            case DATA_CAPTURE:
            case CATCHUP:
            case POPULATION:
                property = changePropertyStage(property, stage, notes, null);
                break;
            case ONE_WAY:
                property = changePropertyStage(property, stage, notes, srpFplosAtTotalLevel);
                break;
            case TWO_WAY:
                property = changePropertyStage(property, stage, notes, srpFplosAtTotalLevel);
                configParamsService.addParameterValue(getPropertyContextForParameter(property),
                        IntegrationConfigParamName.OVERRIDE_VARIABLE_DECISION_WINDOW.value(), Boolean.TRUE.toString());
                break;
            default:
                LOGGER.warn("Unahndled stage change: " + stage.getCode());
        }

        // Reload the property in the cache
        if (!SystemConfig.isStageChangeSingleCacheRefreshEnabled()) {
            clientPropertyCacheService.reloadProperty(property.getId());
        }
        return property;
    }

    public void setInitialStage(Property property, Stage stage, String notes) {
        tetrisEventManager.raiseEvent(tetrisEventManager.buildPropertyStageChangedEvent(property.getId(), getCurrentUser(), null, stage, notes));
    }

    private Property changePropertyStage(Property property, Stage newStage, String notes, Boolean srpFplosAtTotalLevel) {
        String decisions = decisionConfigurationService.getDecisions(property);
        String scheduledTwoWayDate = decisionConfigurationService.getScheduledTwoWayDateValueString();
        Stage previousStage = property.getStage();

        if (newStage.equals(previousStage)) {
            LOGGER.info("Kiboshing change stage call as new stage is previous stage: " + newStage);
            return property;
        }

        // US5102 AT5: In One-Way "the value of the srpFplosAtTotalLevel global parameter is not changed at the Property or Client level"
        if (newStage.equals(Stage.ONE_WAY) && null != srpFplosAtTotalLevel) {
            srpFplosAtTotalLevel = getSrpFplopsAtTotalLevel(property);
        }

        configureDailyProcessingMonitoring(property, newStage);

        // for now, we are still maintaining stage in both places: the property table and global parameters
        property.setStage(newStage);
        property = propertyService.updateProperty(property);
        if (isVirtualPropertyChangedToTwoWay(property)) {
            markPhysicalPropertyStageDormant(property);
            virtualPropertyMappingService.disableParallelProcessing(property.getId());
        }

        if(isLongTermBDEPropertyMovedToTwoWayFirstTime(property)){
            pricingConfigurationLTBDEService.enableLTBDEForPricing(true);
        }

        // raise a stage change event
        String user = getCurrentUser();
        if (null != srpFplosAtTotalLevel) {
            tetrisEventManager.raiseEvent(tetrisEventManager.buildPropertyStageChangedEvent(property.getId(), user,
                    previousStage, newStage, notes, decisions, scheduledTwoWayDate, true, srpFplosAtTotalLevel));
        } else {
            tetrisEventManager.raiseEvent(tetrisEventManager.buildPropertyStageChangedEvent(property.getId(), user,
                    previousStage, newStage, notes));
        }
        ldbService.stageChanged(property.getId(), newStage);
        return property;
    }

    protected boolean isLongTermBDEPropertyMovedToTwoWayFirstTime(Property property) {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.LONG_TERM_BDE_PROCESSING_ENABLED) &&
            Stage.TWO_WAY.equals(property.getStage()) && decisionConfigurationService.isPropertyMovingToTwoWayFirstTime();
    }

    private boolean isVirtualPropertyChangedToTwoWay(Property property) {
        return Stage.TWO_WAY.equals(property.getStage()) && property.isVirtualProperty();
    }

    private void markPhysicalPropertyStageDormant(Property property) {
        List<String> physicalPropertyCodes = virtualPropertyMappingService.getPhysicalPropertyCodesForVirtualProperty(property.getId());
        List<Property> properties = getPropertiesFor(physicalPropertyCodes);
        markStageDormant(properties);
        ldbService.stageChanged(properties, Stage.DORMANT);
    }

    private void markStageDormant(List<Property> properties) {
        List<Property> updatedProperties = properties.stream().peek(p -> p.setStage(Stage.DORMANT)).collect(Collectors.toList());
        globalCrudService.save(updatedProperties);
    }

    private List<Property> getPropertiesFor(List<String> physicalPropertyCodes) {
        PropertyCriteria propertyCriteria = new PropertyCriteria();
        propertyCriteria.setPropertyCodes(physicalPropertyCodes);
        return propertyService.getProperties(propertyCriteria);
    }

    private void configureDailyProcessingMonitoring(Property property, Stage newStage) {
        Stage previousStage = property.getStage();

        // Disable Daily Processing Monitoring if we are switching to Dormant Mode
        if (newStage == Stage.DORMANT) {
            configParamsService.addParameterValue(getPropertyContextForParameter(property),
                    GUIConfigParamName.DAILY_PROCESSING_MONITORING_ENABLED.value(), Boolean.FALSE.toString());
        }

        // Enable Daily Processing Monitoring if we are switching from Dormant Mode or
        // if we are switching to Paused Mode
        if (previousStage == Stage.DORMANT || newStage == Stage.PAUSED) {
            configParamsService.addParameterValue(getPropertyContextForParameter(property),
                    GUIConfigParamName.DAILY_PROCESSING_MONITORING_ENABLED.value(), Boolean.TRUE.toString());
        }
    }

    public Stage getCurrentStage(int propertyId) {
        Property property = propertyService.getPropertyById(propertyId);
        return property != null ? property.getStage() : null;
    }

    private Boolean getSrpFplopsAtTotalLevel(Property property) {
        return Boolean.valueOf(configParamsService.getValue(getPropertyContextForParameter(property),
                IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL.value()));
    }

    public String getPropertyContextForParameter(Property property) {
        return getPropertyContextForParameter(property.getClient().getCode(), property.getCode());
    }

    private String getPropertyContextForParameter(String clientCode, String propertyCode) {
        return new StringBuilder(Constants.CONFIG_PARAMS_NODE_PREFIX).append(".").append(clientCode)
                .append(".").append(propertyCode).toString();
    }

    public List<PropertyStageChange> getPropertyStageChangesWithStageChangeFlag() {
        return globalCrudService.findByNamedQuery(PropertyStageChange.FIND_PROPERTY_STAGE_CHANGES_WITH_DECISION_STAGE_CHANGE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<PropertyStageChange> getPropertyStageChanges() {
        return globalCrudService.findByNamedQuery(PropertyStageChange.FIND_PROPERTY_STAGE_CHANGES,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }
}
