package com.ideas.tetris.pacman.services.forecast.dto;

import java.util.Date;

public class ExpectedForecastDetailDTO {
    private Date occupancyDate;
    private Integer actualOnbooks;
    private Integer expectedOnbooks;
    private Integer expectedOnbooksLowerBound;
    private Integer expectedOnbooksUpperBound;

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public Integer getActualOnbooks() {
        return actualOnbooks;
    }

    public void setActualOnbooks(Integer actualOnbooks) {
        this.actualOnbooks = actualOnbooks;
    }

    public Integer getExpectedOnbooks() {
        return expectedOnbooks;
    }

    public void setExpectedOnbooks(Integer expectedOnbooks) {
        this.expectedOnbooks = expectedOnbooks;
    }

    public Integer getExpectedOnbooksLowerBound() {
        return expectedOnbooksLowerBound;
    }

    public void setExpectedOnbooksLowerBound(Integer expectedOnbooksLowerBound) {
        this.expectedOnbooksLowerBound = expectedOnbooksLowerBound;
    }

    public Integer getExpectedOnbooksUpperBound() {
        return expectedOnbooksUpperBound;
    }

    public void setExpectedOnbooksUpperBound(Integer expectedOnbooksUpperBound) {
        this.expectedOnbooksUpperBound = expectedOnbooksUpperBound;
    }
}
