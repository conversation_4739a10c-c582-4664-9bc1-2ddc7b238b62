package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.daoandentities.entity.PropertyAudit;

import java.time.LocalDateTime;

public class PropertyAuditDto {
    public static final String DUMMY_USER_DISPLAY_NAME = "System";
    public static final Integer DUMMY_USER_ID = 1;
    private LocalDateTime modifiedDate;
    private String stage;
    private Boolean forceFullDecisions;
    private String modifiedBy;
    private String revType;
    private String propertyName;
    private String propertyCode;
    private Integer propertyId;
    private String action;

    public PropertyAuditDto() {

    }

    public PropertyAuditDto(PropertyAudit entity, CrudService crudService) {
        modifiedDate = DateUtil.convertJavaUtilDateToLocalDateTime(entity.getModifiedDate());
        stage = entity.getStage();
        setForceFullDecisions(entity.isForceFullDecisions());
        GlobalUser user = getUser(entity.getModifiedByUserId(), crudService);
        if (user == null) {
            modifiedBy = "unknown";
        } else if (user.getId().equals(DUMMY_USER_ID)) {
            modifiedBy = DUMMY_USER_DISPLAY_NAME;
        } else {
            modifiedBy = user.getFullName();
        }
        if (entity.getRevType() == PropertyAudit.ADDED_ACTION) {
            setRevType("Added");
        } else if (entity.getRevType() == PropertyAudit.UPDATED_ACTION) {
            setRevType("Updated");
        } else if (entity.getRevType() == PropertyAudit.DELETED_ACTION) {
            setRevType("Deleted");
        }
        propertyName = entity.getPropertyName();
        propertyCode = entity.getPropertyCode();
        propertyId = entity.getPropertyId();
    }

    private GlobalUser getUser(Long modifiedByUserId, CrudService crudService) {
        if (modifiedByUserId == null) {
            return null;
        }
        return crudService.find(GlobalUser.class, modifiedByUserId.intValue());
    }


    public String getStage() {
        return stage;
    }

    public void setContext(String stage) {
        this.stage = stage;
    }

    public LocalDateTime getModifiedDate() {
        return modifiedDate;
    }

    public void setModifiedDate(LocalDateTime modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    public String getModifiedBy() {
        return modifiedBy;
    }

    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy;
    }

    public String getRevType() {
        return revType;
    }

    public void setRevType(String revType) {
        this.revType = revType;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public Boolean getForceFullDecisions() {
        return forceFullDecisions;
    }

    public void setForceFullDecisions(Boolean forceFullDecisions) {
        this.forceFullDecisions = forceFullDecisions;
    }

    public Integer getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Integer propertyId) {
        this.propertyId = propertyId;
    }

    public String getPropertyCode() {
        return propertyCode;
    }

    public void setPropertyCode(String propertyCode) {
        this.propertyCode = propertyCode;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

}

