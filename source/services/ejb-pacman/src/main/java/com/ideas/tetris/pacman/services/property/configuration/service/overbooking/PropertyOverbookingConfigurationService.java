package com.ideas.tetris.pacman.services.property.configuration.service.overbooking;

import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingProperty;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.PropertyOverbookingLimitPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.log4j.Logger;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.List;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@PropertyOverbookingConfigurationService.Qualifier
@Component
@Transactional
public class PropertyOverbookingConfigurationService extends AbstractPropertyConfigurationService {

    private static final Logger LOGGER = Logger.getLogger(PropertyOverbookingConfigurationService.class.getName());

    @Override
    public PropertyConfigurationRecordType getPropertyConfigurationRecordType() {
        return PropertyConfigurationRecordType.POBK;
    }

    @Override
    public List<PropertyConfigurationRecordFailure> doValidate(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId) {
        PropertyOverbookingLimitPropertyConfigurationDto polpcd = (PropertyOverbookingLimitPropertyConfigurationDto) propertyConfigurationDto;

        List<PropertyConfigurationRecordFailure> exceptions = new ArrayList<PropertyConfigurationRecordFailure>();

        // Validate Sunday
        validateDay(exceptions, polpcd.getSunday(), "Sunday");

        // Validate Monday
        validateDay(exceptions, polpcd.getMonday(), "Monday");

        // Validate Tuesday
        validateDay(exceptions, polpcd.getTuesday(), "Tuesday");

        // Validate Wednesday
        validateDay(exceptions, polpcd.getWednesday(), "Wednesday");

        // Validate Thursday
        validateDay(exceptions, polpcd.getThursday(), "Thursday");

        // Validate Friday
        validateDay(exceptions, polpcd.getFriday(), "Friday");

        // Validate Saturday
        validateDay(exceptions, polpcd.getSaturday(), "Saturday");

        return exceptions;
    }

    @Override
    public void doHandle(PropertyConfigurationDto pcd, Integer propertyId) {
        PropertyOverbookingLimitPropertyConfigurationDto polpcd = (PropertyOverbookingLimitPropertyConfigurationDto) pcd;

        OverbookingProperty overbookingProperty = findOverbookingProperty(propertyId);
        if (overbookingProperty == null) {
            overbookingProperty = new OverbookingProperty();
            overbookingProperty.setPropertyId(propertyId);
            overbookingProperty.setCreatedByUserId(findUserId());
        }

        overbookingProperty.setSundayCeiling(polpcd.getSunday());
        overbookingProperty.setMondayCeiling(polpcd.getMonday());
        overbookingProperty.setTuesdayCeiling(polpcd.getTuesday());
        overbookingProperty.setWednesdayCeiling(polpcd.getWednesday());
        overbookingProperty.setThursdayCeiling(polpcd.getThursday());
        overbookingProperty.setFridayCeiling(polpcd.getFriday());
        overbookingProperty.setSaturdayCeiling(polpcd.getSaturday());

        if (overbookingProperty.getCreateDate() == null) {
            LOGGER.info("Creating OverbookingProperty for Property: " + pcd.getPropertyCode());
            crudService.save(overbookingProperty);
        } else {
            LOGGER.info("Updating OverbookingProperty for Property: " + pcd.getPropertyCode());
            crudService.save(overbookingProperty);
        }
    }

    public void validateDay(List<PropertyConfigurationRecordFailure> exceptions, Integer value, String day) {
        if (value == null || value.intValue() < -1) {
            exceptions.add(new PropertyConfigurationRecordFailure(day + " must be -1 or greater"));
        }
    }

    public OverbookingProperty findOverbookingProperty(Integer propertyId) {
        return (OverbookingProperty) crudService.findByNamedQuerySingleResult(OverbookingProperty.BY_PROPERTYID, QueryParameter.with("propertyId", propertyId).parameters());
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
