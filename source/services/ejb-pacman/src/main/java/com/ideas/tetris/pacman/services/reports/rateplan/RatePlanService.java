package com.ideas.tetris.pacman.services.reports.rateplan;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.rateplan.dto.AllSrpDTO;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
public class RatePlanService {

    private static final Logger LOGGER = Logger.getLogger(RatePlanService.class.getName());

    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    public List<AllSrpDTO> getTotalSRPDetails(LocalDate startDate, LocalDate endDate) {

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("start_date", new java.sql.Date(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                .and("end_date", new java.sql.Date(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()));

        try {

            List<Object[]> resultList = crudService.findByNativeQuery("select * from dbo.ufn_get_all_srp_by_ms_and_fg_for_date_range(:property_id, :start_date, :end_date) order by srp, Forecast_Group_Name, Mkt_Seg_Name", queryParameters.parameters());

            List<AllSrpDTO> srpDetails = new ArrayList<>();
            AllSrpDTO data;
            for (Object[] object : resultList) {
                data = new AllSrpDTO();
                data.setRatePlanName((String) object[0]);
                data.setRoomSold((Integer) object[2]);
                data.setRoomRevenue((BigDecimal) object[3]);
                data.setMarketSegmentName((String) object[6]);
                data.setForecastGroupName((String) object[8]);
                srpDetails.add(data);
            }

            return srpDetails;

        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<>();
        }

    }

}
