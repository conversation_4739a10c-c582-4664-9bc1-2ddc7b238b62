package com.ideas.tetris.pacman.services.reports.bookingbylos;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;

import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertLocalDateToJavaUtilDate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * Created by idnsmu on 3/26/2015.
 */
@Component
@Transactional
public class BookingByLOSReportService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Autowired
    DateService dateService;

    public List<BookingByLOSUiDTO> generateReport(BookingByLOS bookingByLOS) {

        QueryParameter parameters = QueryParameter.with("startDate", new java.sql.Date(convertLocalDateToJavaUtilDate(bookingByLOS.getStartDate()).getTime()))
                .and("endDate", new java.sql.Date(convertLocalDateToJavaUtilDate(bookingByLOS.getEndDate()).getTime()))
                .and("asOfBookingDate", new java.sql.Date(convertLocalDateToJavaUtilDate(bookingByLOS.getAsOfBookingDate()).getTime()))
                .and("caughtUpDate", new java.sql.Date(dateService.getCaughtUpDate().getTime()))
                .and("bookingStay", bookingByLOS.getBookingStay())
                .and("isRollingDate", bookingByLOS.getIsRollingDate())
                .and("rollingStartDate", bookingByLOS.getRollingStartDate())
                .and("rollingEndDate", bookingByLOS.getRollingEndDate())
                .and("rollingAsOfBookingDate", bookingByLOS.getRollingAsOfBookingDate());
        return crudService.findByNativeQuery("exec dbo.usp_get_booking_by_los_report :startDate, :endDate, :asOfBookingDate, :caughtUpDate, :bookingStay, :isRollingDate, :rollingStartDate, :rollingEndDate, :rollingAsOfBookingDate",
                parameters.parameters(), new RowMapper<>() {
                    @Override
                    public BookingByLOSUiDTO mapRow(Object[] row) {
                        BookingByLOSUiDTO bookingByLOSReport = new BookingByLOSUiDTO();
                        bookingByLOSReport.setArrivalDate((java.sql.Date) row[0]);
                        bookingByLOSReport.setDayOfWeek((String) row[1]);
                        bookingByLOSReport.setDl((Integer) row[2]);
                        setLosWithValue(bookingByLOSReport, row);
                        bookingByLOSReport.setTransSold((Integer) row[13]);
                        bookingByLOSReport.setGroupSold((Integer) row[14]);
                        bookingByLOSReport.setTotalSold((Integer) row[15]);
                        bookingByLOSReport.setStayBookings((BigDecimal) row[16]);
                        return bookingByLOSReport;
                    }
                });

    }

    private void setLosWithValue(BookingByLOSUiDTO bookingByLOSReport, Object[] row) {
        LinkedHashMap<String, Integer> losWithValue = new LinkedHashMap<>();
        losWithValue.put("LOS1", (Integer) row[3]);
        losWithValue.put("LOS2", (Integer) row[4]);
        losWithValue.put("LOS3", (Integer) row[5]);
        losWithValue.put("LOS4", (Integer) row[6]);
        losWithValue.put("LOS5", (Integer) row[7]);
        losWithValue.put("LOS6", (Integer) row[8]);
        losWithValue.put("LOS7To11", (Integer) row[9]);
        losWithValue.put("LOS12To16", (Integer) row[10]);
        losWithValue.put("LOS17To29", (Integer) row[11]);
        losWithValue.put("LOS30Plus", (Integer) row[12]);
        bookingByLOSReport.setLosWithValue(losWithValue);
    }

}
