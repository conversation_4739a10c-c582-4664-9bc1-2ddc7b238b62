package com.ideas.tetris.pacman.services.property.dto;

import java.math.BigDecimal;

public class PropertyStats {
    private static final long serialVersionUID = -7117551689378588124L;

    private PropertyDetails propertyDetails;
    private Integer accomTypes;
    private Integer totalAccomCapacity;
    private Integer forecastGroups;
    private Integer marketSegments;

    public PropertyStats(PropertyDetails propertyDetails, Object[] row) {
        this.propertyDetails = propertyDetails;
        this.accomTypes = bigDecimalToInteger(row[1]);
        this.totalAccomCapacity = bigDecimalToInteger(row[2]);
        this.forecastGroups = bigDecimalToInteger(row[3]);
        this.marketSegments = bigDecimalToInteger(row[4]);
    }

    public String getClientCode() {
        return propertyDetails.clientCode;
    }

    public String getPropertyCode() {
        return propertyDetails.propertyCode;
    }

    public Integer getPropertyId() {
        return propertyDetails.propertyId;
    }

    public String getDatabaseServer() {
        return propertyDetails.databaseServer;
    }

    public String getSasServer() {
        return propertyDetails.sasServer;
    }

    public String getTimezone() {
        return propertyDetails.timezone;
    }

    public String getPropertyStage() {
        return propertyDetails.propertyStage;
    }

    public String getExternalSystem() {
        return propertyDetails.externalSystem;
    }

    public Integer getAccomTypes() {
        return accomTypes;
    }

    public Integer getTotalAccomCapacity() {
        return totalAccomCapacity;
    }

    public Integer getForecastGroups() {
        return forecastGroups;
    }

    public Integer getMarketSegments() {
        return marketSegments;
    }

    private Integer bigDecimalToInteger(Object value) {
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof BigDecimal) {
            return ((BigDecimal) value).intValue();
        } else if (value == null) {
            return null;
        } else {
            throw new IllegalArgumentException("Invalid type: " + value.getClass().getSimpleName());
        }
    }

    public static class PropertyDetails {
        String clientCode;
        String propertyCode;
        Integer propertyId;
        String sasServer;
        String databaseServer;
        String propertyStage;
        String timezone;
        String externalSystem;

        public PropertyDetails(Object[] row) {
            propertyId = (Integer) row[0];
            clientCode = (String) row[1];
            propertyCode = (String) row[2];
            sasServer = (String) row[3];
            databaseServer = (String) row[4];
            propertyStage = (String) row[5];
            timezone = (String) row[6];
            externalSystem = (String) row[7];
        }

        public Integer getPropertyId() {
            return propertyId;
        }
    }
}
