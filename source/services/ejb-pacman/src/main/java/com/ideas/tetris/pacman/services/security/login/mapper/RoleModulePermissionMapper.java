package com.ideas.tetris.pacman.services.security.login.mapper;

import com.ideas.tetris.pacman.services.datafeed.dto.RolePermission;
import com.ideas.tetris.pacman.services.datafeed.service.RolePermissionService;
import com.ideas.tetris.pacman.services.security.login.util.RoleModulePermissionMapperUtil;
import com.ideas.tetris.pacman.services.security.login.vo.ModulePermissionVO;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Component
@Transactional
public class RoleModulePermissionMapper {

    private static final Logger LOGGER = Logger.getLogger(RoleModulePermissionMapper.class.getName());

    @Autowired
    RolePermissionService rolePermissionService;

    public Map<String, Set<ModulePermissionVO>> getRoleModulePermissionMap() {
        return getRoleModulePermissionMapBy(true, false, getModuleUrlMapping());
    }

    public Map<String, Set<ModulePermissionVO>> getRoleModulePermissionMapBy(boolean isForMobile, boolean includeInternal, Map<String, String> moduleUrlMap) {
        Map<String, String> accessLabelMap = RoleModulePermissionMapperUtil.getAccessLabelMap();
        List<RolePermission> rolePermissions = getRolePermissions(moduleUrlMap, includeInternal);

        Map<String, Set<ModulePermissionVO>> roleModulePermissionMap = new HashMap<>();

        for (RolePermission rolePermission : rolePermissions) {
            if (roleModulePermissionMap.containsKey(rolePermission.getRoleName())) {
                roleModulePermissionMap.get(rolePermission.getRoleName()).add(new ModulePermissionVO(moduleUrlMap.get(rolePermission.getModuleName()),
                        String.valueOf(accessLabelMap.get(rolePermission.getPermission())),
                        rolePermission.getModuleImmediateParent()));
            } else {
                Set<ModulePermissionVO> modulePermissionSet = new HashSet<>();
                modulePermissionSet.add(new ModulePermissionVO(moduleUrlMap.get(rolePermission.getModuleName()),
                        String.valueOf(accessLabelMap.get(rolePermission.getPermission())), rolePermission.getModuleImmediateParent()));
                roleModulePermissionMap.put(rolePermission.getRoleName(), modulePermissionSet);
            }
        }

        if(isForMobile){
            handleGMDashBoardModulePermission(roleModulePermissionMap);
        }
        return roleModulePermissionMap;
    }

    public static Map<String, String> getModuleUrlMap(boolean isForMobile) {
        Map<String, String> moduleUrlMap;
        if(isForMobile){
            moduleUrlMap = getModuleUrlMapping();
        } else {
            moduleUrlMap = getModuleUrlMappingForGroupPricing();
        }
        return moduleUrlMap;
    }

    public void handleGMDashBoardModulePermission(Map<String, Set<ModulePermissionVO>> roleModulePermissionMap) {
        Predicate<ModulePermissionVO> atAGlance = modulePermissionVO -> "AtAGlance".equals(modulePermissionVO.getModuleName());
        roleModulePermissionMap.forEach((roleName, modulePermissionVOSet) ->
                modulePermissionVOSet.stream().filter(atAGlance).findFirst().ifPresent(modulePermissionVO -> {
                    modulePermissionVOSet.add(new ModulePermissionVO("GMDashboard", modulePermissionVO.getModulePermission()));
                }));
    }

    public List<RolePermission> getRolePermissions(Map<String, String> moduleUrlMap, boolean includeInternal) {
        List<RolePermission> rolePermissions = rolePermissionService.getAccessibleRolePermissions(includeInternal);
        return rolePermissions.stream().filter(rolePermission -> moduleUrlMap.containsKey(rolePermission.getModuleName())).collect(Collectors.toList());
    }

    public static Map<String, String> getModuleUrlMapping() {
        Map<String, String> moduleUrlMap = new HashMap<>();
        moduleUrlMap.put("At a Glance", "AtAGlance");
        moduleUrlMap.put("Operations Report", "OperationsReport");
        return moduleUrlMap;
    }

    public static Map<String, String> getModuleUrlMappingForGroupPricing() {
        Map<String, String> moduleUrlMap = new HashMap<>();
        moduleUrlMap.put("Group Pricing Evaluation", "GroupPricingEvaluation");
        return moduleUrlMap;
    }
}
