package com.ideas.tetris.pacman.services.budget.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
public class RevPlanSubmitTypeRequestDTO implements Serializable {
    public static final String IS_REQUIRED_FIELD_ERR_MESSAGE = " is required field";


    private String revplanDataPullId;

    @NotNull(message = IS_REQUIRED_FIELD_ERR_MESSAGE)
    @JsonFormat(pattern = "dd-MM-yyyy")
    private LocalDate startDate;

    @NotNull(message = IS_REQUIRED_FIELD_ERR_MESSAGE)
    @JsonFormat(pattern = "dd-MM-yyyy")
    private LocalDate endDate;

    @NotBlank(message = IS_REQUIRED_FIELD_ERR_MESSAGE)
    private String submissionType;

    @NotBlank(message = IS_REQUIRED_FIELD_ERR_MESSAGE)
    private String configType;

    @NotBlank(message = IS_REQUIRED_FIELD_ERR_MESSAGE)
    private String revPlanCallBackUrl;

    private String revPlanCurrencyCode;

    private Boolean useRevplanExchangeRates;

    @Override
    public String toString() {
        return "RevPlanSubmitTypeRequestDTO{" +
                "revplanDataPullId='" + revplanDataPullId + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", submissionType='" + submissionType + '\'' +
                ", configType='" + configType + '\'' +
                ", revPlanCallBackUrl='" + revPlanCallBackUrl + '\'' +
                ", revPlanCurrencyCode='" + revPlanCurrencyCode + '\'' +
                ", useRevplanExchangeRates='" + useRevplanExchangeRates + '\'' +
                '}';
    }

    public RevPlanSubmitTypeRequestDTO(RevPlanSubmitTypeRequestDTO revPlanSubmitTypeRequestDTO) {
        setStartDate(revPlanSubmitTypeRequestDTO.getStartDate());
        setEndDate(revPlanSubmitTypeRequestDTO.getEndDate());
        setConfigType(revPlanSubmitTypeRequestDTO.getConfigType());
        setSubmissionType(revPlanSubmitTypeRequestDTO.getSubmissionType());
        setRevPlanCallBackUrl(revPlanSubmitTypeRequestDTO.getRevPlanCallBackUrl());
        setRevplanDataPullId(revPlanSubmitTypeRequestDTO.getRevplanDataPullId());
        setRevPlanCurrencyCode(revPlanSubmitTypeRequestDTO.getRevPlanCurrencyCode());
        setUseRevplanExchangeRates(revPlanSubmitTypeRequestDTO.getUseRevplanExchangeRates());
    }

}
