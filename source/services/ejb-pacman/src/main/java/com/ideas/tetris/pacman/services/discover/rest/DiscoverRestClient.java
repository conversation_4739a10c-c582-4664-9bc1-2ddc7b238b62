package com.ideas.tetris.pacman.services.discover.rest;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.services.discover.entity.DiscoverUser;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.Response;
import java.util.Collections;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Transactional
public class DiscoverRestClient {

    private static final Logger LOGGER = Logger.getLogger(DiscoverRestClient.class);

    private final Client client = ClientBuilder.newBuilder().build();

    @Autowired
    private DiscoverRestClientHelper discoverRestClientHelper;

    public DiscoverUser get(DiscoverRestEndPoints restEndpoint, Map<String, String> parameters) {
        LOGGER.debug("Getting discover user using discover V2 API");
        String urlToCall = discoverRestClientHelper.createGetOrPostUrl(restEndpoint, parameters, false);
        JSONObject result = makeRestCallAndLoadResults(urlToCall, buildHeader());
        if (result == null) {
            return null;
        }
        return discoverRestClientHelper.getDiscoverUserFromResponse(result);
    }

    public void put(DiscoverRestEndPoints discoverRestEndPoints, String discoverUserId, Entity<?> entity) {
        LOGGER.debug("Updating discover user using discover V2 API");
        discoverRestClientHelper.validateParams(discoverRestEndPoints, entity);
        WebTarget target = client.target(discoverRestClientHelper.createPutUrl(discoverRestEndPoints, discoverUserId, false));
        Response response = null;
        try {
            response = target.request().headers(buildHeader()).put(entity);
            discoverRestClientHelper.checkResponse(response);
        } catch (Exception ce) {
            LOGGER.error("Error occurred while communicating with Discover at this time");
            throw new TetrisException(ErrorCode.DISCOVER_REST_CLIENT_FAILURE, "discover.error.while.communicating.with.server", ce);
        } finally {
            discoverRestClientHelper.close(response);
        }
    }

    public DiscoverUser post(DiscoverRestEndPoints discoverRestEndpoint, Entity<?> entity) {
        LOGGER.debug("Creating discover user using discover V2 API");
        WebTarget target = client.target(
                discoverRestClientHelper.createGetOrPostUrl(discoverRestEndpoint, Collections.emptyMap(), false));
        Response response = null;
        DiscoverUser discoverUser = null;
        try {
            response = target.request().headers(buildHeader()).post(entity);
            discoverRestClientHelper.checkResponse(response);
            JSONObject result = new JSONObject(response.readEntity(String.class));
            discoverUser = discoverRestClientHelper.getDiscoverUserFromResponse(result);
        } catch (Exception ce) {
            LOGGER.error("Error occurred while creating user in Discover.", ce);
            throw new TetrisException(ErrorCode.DISCOVER_REST_CLIENT_FAILURE, "discover.error.occurred.while.creating.new.user", ce);
        } finally {
            discoverRestClientHelper.close(response);
        }
        return discoverUser;
    }

    private JSONObject makeRestCallAndLoadResults(String urlToCall, MultivaluedHashMap headers) {
        String responseString = discoverRestClientHelper.makeRestCallAndReturnResponse(urlToCall, headers);
        return handleResponse(responseString);
    }

    @VisibleForTesting
	public
    MultivaluedHashMap buildHeader() {
        MultivaluedHashMap headers = new MultivaluedHashMap();
        headers.add("Content-Type", "application/json");
        headers.add("Accept", "application/json");
        return headers;
    }

    private JSONObject handleResponse(String responseString) {
        JSONObject result = null;
        try {
            JSONArray jsonArray = new JSONArray(responseString);
            if (jsonArray.length() > 0) {
                LOGGER.debug("discover user found");
                result = (JSONObject) jsonArray.get(0);
            } else {
                LOGGER.debug("discover user not found");
            }
        } catch (JSONException jsonException) {
            LOGGER.error(jsonException);
            throw new TetrisException(ErrorCode.DISCOVER_REST_CLIENT_FAILURE, jsonException.getMessage());
        }
        return result;
    }
}
