package com.ideas.tetris.pacman.services.datafeed.service;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.ForecastGroupProfitDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.RoomClassProfitDTO;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.CAUGHT_UP_DATE;
import static com.ideas.tetris.pacman.common.constants.Constants.END_DATE;
import static com.ideas.tetris.pacman.common.constants.Constants.PROPERTY_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.START_DATE;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class ProfitDataFeedService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
	private DateService dateService;

    public List<ForecastGroupProfitDTO> fetchProfitForecastGroupData(DatafeedRequest datafeedRequest) {
        List<Object> result = tenantCrudService.findByNamedQuery(MktSegAccomActivity.GET_PROFIT_DATA_AT_FORECAST_GROUP_LEVEL, getParameters(datafeedRequest), datafeedRequest.getStartPosition(), datafeedRequest.getSize());
        return Optional.ofNullable(result)
                .orElse(Collections.emptyList()).stream()
                .map(ForecastGroupProfitDTO::new)
                .collect(Collectors.toList());
    }

    public List<RoomClassProfitDTO> fetchProfitRoomClassData(DatafeedRequest datafeedRequest) {
        List<Object> result = tenantCrudService.findByNamedQuery(MktSegAccomActivity.GET_PROFIT_DATA_AT_ROOM_CLASS_LEVEL, getParameters(datafeedRequest), datafeedRequest.getStartPosition(), datafeedRequest.getSize());
        return Optional.ofNullable(result)
                .orElse(Collections.emptyList()).stream()
                .map(RoomClassProfitDTO::new)
                .collect(Collectors.toList());
    }

    private Map<String, Object> getParameters(DatafeedRequest datafeedRequest) {
        LocalDate systemDate = LocalDateUtils.fromDate(dateService.getCaughtUpDate());
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        parameters.put(START_DATE, new LocalDate(datafeedRequest.getStartDate()));
        parameters.put(END_DATE, new LocalDate(datafeedRequest.getEndDate()));
        parameters.put(CAUGHT_UP_DATE, systemDate.minusDays(1));
        return parameters;
    }

    @VisibleForTesting
    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }
}
