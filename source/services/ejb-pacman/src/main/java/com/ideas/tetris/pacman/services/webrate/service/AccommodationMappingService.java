package com.ideas.tetris.pacman.services.webrate.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.fds.G3SNSService;
import com.ideas.tetris.pacman.common.fds.dto.EventType;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.configautomation.dto.RoomClassMappingDTO;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.eventaggregator.SystemComponent;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetails;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetailsProposed;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegForecastGroup;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.rateshoppingadjustment.entity.RateShoppingAdjustment;
import com.ideas.tetris.pacman.services.rateshoppingadjustment.enumerations.OperationTypes;
import com.ideas.tetris.pacman.services.rateshoppingadjustment.enumerations.ValueTypes;
import com.ideas.tetris.pacman.services.rdl.entity.WebrateTypeProduct;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.services.webrate.dto.WebRateCompetitorAverageRateDTO;
import com.ideas.tetris.pacman.services.webrate.dto.WebrateAccomClassMappingDTO;
import com.ideas.tetris.pacman.services.webrate.dto.WebrateAccomTypeAverageRateDTO;
import com.ideas.tetris.pacman.services.webrate.entity.*;
import com.ideas.tetris.pacman.services.webrate.enums.CompetitiveMarketPositionConstraintEnum;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.common.constants.NotificationKeyConstants.*;
import static com.ideas.tetris.pacman.services.webrate.entity.WebrateAccomType.PARAM_ACCOM_NAMES;
import static com.ideas.tetris.pacman.services.webrate.entity.WebrateAccomType.PARAM_PROPERTY_ID;
import static com.ideas.tetris.pacman.services.webrate.entity.WebrateRankingAccomClass.STANDARD_CMPC_DEFAULT_CONFIG_WITH_PROPERTY_NAME;

@Component
@Transactional
public class AccommodationMappingService {

    private static final BigDecimal ACCEPTABLE_RATE_WINDOW_PERCENTAGE = BigDecimal.valueOf(30);
    private static final String BEST_FLEXIBLE_TYPE_CODE = "BEST_FLEXIBLE";
    private static final String SEMI_FLEXIBLE_TYPE_CODE = "SEMI_FLEXIBLE";
    private static final String ANY_NON_QUALIFIED_TYPE_CODE = "ANY_NON_QUALIFIED";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Autowired
	private AccommodationService accommodation2Service;

    @Autowired
	private SyncEventAggregatorService syncEventAggregatorService;

    @Autowired
	private WebrateShoppingCleanUpService webrateShoppingCleanUpService;

    @Autowired
	private WebrateShoppingDataService webrateShoppingDataService;

    @Autowired
	private DefaultReferenceChannelService defaultReferenceChannelService;

    @Autowired
    AgileRatesConfigurationService agileRatesConfigurationService;

    @Autowired
	private TaxService taxService;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
    CompetitorDataFilterService competitorDataFilterService;

    private static final String WEBRATE_STATUS_AVAILABLE = "A";
    private static final int SUGGESTION_DATA_WINDOW_IN_YEARS = 2;
    private static final int DATA_WINDOW_LAST_YEAR = 1;
    private final List<WebrateAccomType> webrateAccomTypesToBeDeleted = new ArrayList<>();
    @Autowired
	private DateService dateService;

    @Autowired
	private PropertyService propertyService;
    @Autowired
	private AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
	private AlertService alertService;

    @Autowired
	private G3SNSService g3SNSService;

    private static Logger logger = Logger.getLogger(AccommodationMappingService.class.getName());

    public List<WebrateAccomType> getAccomodationMappingByProperty() {
        Integer propertyId = ((WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY)).getPropertyId();
        return getAccomodationMappingByProperty(propertyId);
    }

    @SuppressWarnings("unchecked")
    public List<WebrateAccomType> getAccomodationMappingByProperty(Integer propertyId) {
        return crudService.findByNamedQuery(WebrateAccomType.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());
    }

    public List<WebrateAccomClassMappingDTO> getAccomClassMappings(Integer propertyId) {
        List<WebrateAccomType> webrateAccomTypes = getAccomodationMappingByProperty();
        List<String> competitors = getCompetitors(propertyId);
        List<WebrateAccomClassMappingDTO> accomClassMappingDTOS = new ArrayList<>();
        webrateAccomTypes.forEach(webrateAccomType -> {
            WebrateAccomClassMappingDTO webrateAccomClassMappingDTO = new WebrateAccomClassMappingDTO();
            if (webrateAccomType.getWebrateAccomClassMappings() != null
                    && !webrateAccomType.getWebrateAccomClassMappings().isEmpty()) {
                webrateAccomType.getWebrateAccomClassMappings().forEach(webrateAccomClassMapping -> {
                    webrateAccomClassMappingDTO.setAccomClass(webrateAccomClassMapping.getAccomClass());
                });
                webrateAccomClassMappingDTO.setAccomClasses(webrateAccomType.getWebrateAccomClassMappings().
                        stream().map(WebrateAccomClassMapping::getAccomClass).collect(Collectors.toSet()));
            }
            webrateAccomClassMappingDTO.setWebrateAccomTypeId(webrateAccomType.getId());
            webrateAccomClassMappingDTO.setWebrateAccomName(webrateAccomType.getWebrateAccomName());
            webrateAccomClassMappingDTO.setWebrateAccomTypeAlias(webrateAccomType.getWebrateAccomAlias());
            webrateAccomClassMappingDTO.setCreateDate(LocalDateUtils.toDate(webrateAccomType.getCreateDate()));
            webrateAccomClassMappingDTO.setCompetitors(competitors);
            accomClassMappingDTOS.add(webrateAccomClassMappingDTO);
        });
        return accomClassMappingDTOS;
    }

    public List<WebrateAccomClassMappingDTO> getAccomMappingDetails(Integer propertyId) {
        List<WebrateAccomType> webrateAccomTypes = getAccomodationMappingByProperty();
        List<String> competitors = getCompetitors(propertyId);
        List<WebrateAccomClassMappingDTO> accomClassMappings = new ArrayList<>();
        for (WebrateAccomType webrateAccomType : webrateAccomTypes) {
            if (null != webrateAccomType.getWebrateAccomClassMappings() && !webrateAccomType.getWebrateAccomClassMappings().isEmpty()) {
                for (WebrateAccomClassMapping webrateAccomClassMapping : webrateAccomType.getWebrateAccomClassMappings()) {
                    WebrateAccomClassMappingDTO webrateAccomClassMappingDTO = getWebrateAccomClassMappingDTO(competitors, webrateAccomType, webrateAccomClassMapping);
                    accomClassMappings.add(webrateAccomClassMappingDTO);
                }
            } else {
                WebrateAccomClassMappingDTO webrateAccomClassMappingDTO = getWebrateAccomClassMappingDTO(competitors, webrateAccomType, null);
                accomClassMappings.add(webrateAccomClassMappingDTO);
            }

        }
        return accomClassMappings;
    }

    private List<String> getCompetitors(Integer propertyId) {
        List<WebrateCompetitors> allCompetitorsByProperty = competitorDataFilterService.getAllCompetitorsByProperty(propertyId);
        if (configParamsService.getBooleanParameterValue(GUIConfigParamName.DISPLAY_NAME_DISPLAY_NAME_ENABLED)) {
            return allCompetitorsByProperty.stream()
                    .map(WebrateCompetitors::getWebrateCompetitorsAlias)
                    .collect(Collectors.toList());
        } else {
            return allCompetitorsByProperty.stream()
                    .map(WebrateCompetitors::getWebrateCompetitorsName)
                    .collect(Collectors.toList());
        }
    }

    private WebrateAccomClassMappingDTO getWebrateAccomClassMappingDTO(List<String> competitors, WebrateAccomType webrateAccomType, WebrateAccomClassMapping webrateAccomClassMapping) {
        WebrateAccomClassMappingDTO webrateAccomClassMappingDTO = new WebrateAccomClassMappingDTO();
        webrateAccomClassMappingDTO.setWebrateAccomTypeId(webrateAccomType.getId());
        webrateAccomClassMappingDTO.setWebrateAccomTypeAlias(webrateAccomType.getWebrateAccomName());
        webrateAccomClassMappingDTO.setWebrateAccomName(webrateAccomType.getWebrateAccomAlias());
        if (null != webrateAccomClassMapping) {
            webrateAccomClassMappingDTO.setAccomClass(webrateAccomClassMapping.getAccomClass());
        }
        webrateAccomClassMappingDTO.setCreateDate(LocalDateUtils.toDate(webrateAccomType.getCreateDate()));
        webrateAccomClassMappingDTO.setCompetitors(competitors);
        return webrateAccomClassMappingDTO;
    }

    public List<WebrateAccomType> getWebrateAccomTypesToBeDeleted() {
        return webrateAccomTypesToBeDeleted;
    }

    public void updateOrDeleteAccomodationMapping(List<WebrateAccomClassMappingDTO> webrateAccomClassMappingDTOs) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Map<WebrateAccomType, AccomClass> changedMapping = new HashMap<>();
        Map<WebrateAccomType, AccomClass> currentMapping = new HashMap<>();
        List<WebrateAccomType> webrateAccomTypes = getAccomodationMappingByProperty(propertyId);
        List<String> rdlExcludedRoomTypes = getRdlExcludedCustomRoomTypes();
        webrateAccomTypes = webrateAccomTypes.stream().filter(webrateAccomType -> !rdlExcludedRoomTypes.contains(
                webrateAccomType.getWebrateAccomName().toLowerCase())).collect(Collectors.toList());
        Map<Integer, WebrateAccomClassMappingDTO> webrateAccomClassMappingDTOMap =
                webrateAccomClassMappingDTOs.stream().collect(Collectors.toMap(WebrateAccomClassMappingDTO::getWebrateAccomTypeId, Function.identity()));
        boolean isMultiAccomClassMappingEnabled = isAccomTypeToMultipleAccomClassesMappingEnabled();
        webrateAccomTypes.forEach(webrateAccomType -> {
            if (isMultiAccomClassMappingEnabled) {
                populateModifiedMappingsForMultiAccomClasses(webrateAccomType, webrateAccomClassMappingDTOMap.get(webrateAccomType.getId()), changedMapping, currentMapping);
            } else {
                populateModifiedMappings(webrateAccomType, webrateAccomClassMappingDTOMap.get(webrateAccomType.getId()), changedMapping, currentMapping);
            }
        });
        if(isMultiAccomClassMappingEnabled) {
            saveAndRemoveAccomClassMappings(webrateAccomClassMappingDTOMap, webrateAccomTypes.stream().filter(webrateAccomType -> !webrateAccomType.getShouldDelete()).collect(Collectors.toList()));
            webrateAccomTypes = webrateAccomTypes.stream().filter(WebrateAccomType::getShouldDelete).collect(Collectors.toList());
        }
        List<AccomClass> cleanUpCPCNotificationsForRoomClass = getRoomClassesToCleanUpCPCNotifications(changedMapping, currentMapping);
        saveAccomMapping(webrateAccomTypes, null, propertyId,
                PacmanWorkContextHelper.getUserId(), cleanUpCPCNotificationsForRoomClass);
    }

    public List<String> getRdlExcludedCustomRoomTypes() {
        List<String> rdlExcludedCustomRoomTypes = new ArrayList<>();
        String rdlExcludedCustomRoomsTypesConfigValue = configParamsService.getParameterValue(FeatureTogglesConfigParamName.RDL_EXCLUDED_CUSTOM_ROOM_TYPES);
        if (org.apache.commons.lang.StringUtils.isNotBlank(rdlExcludedCustomRoomsTypesConfigValue)) {
            rdlExcludedCustomRoomTypes = Arrays.stream(rdlExcludedCustomRoomsTypesConfigValue.split(","))
                    .filter(org.apache.commons.lang.StringUtils::isNotBlank)
                    .map(org.apache.commons.lang.StringUtils::trim)
                    .map(String::toLowerCase)
                    .collect(Collectors.toList());
        }
        return rdlExcludedCustomRoomTypes;
    }

    public boolean isAccomTypeToMultipleAccomClassesMappingEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_MAP_ACCOM_TYPE_TO_MULTIPLE_ACCOM_CLASSES_ENABLED);
    }

    private void saveAndRemoveAccomClassMappings(Map<Integer, WebrateAccomClassMappingDTO> webrateAccomClassMappingDTOMap, List<WebrateAccomType> webrateAccomTypes) {
        webrateAccomTypes.forEach(item -> {
            Set<AccomClass> storedAccomClassSet = new HashSet<>();
            if (item.getWebrateAccomClassMappings() != null) {
                storedAccomClassSet = item.getWebrateAccomClassMappings().
                        stream().map(WebrateAccomClassMapping::getAccomClass).collect(Collectors.toSet());
            }
            saveAccomClassMapping(webrateAccomClassMappingDTOMap.get(item.getId()), storedAccomClassSet, item);
            removeAccomClassMapping(webrateAccomClassMappingDTOMap.get(item.getId()), storedAccomClassSet, item);
        });
    }

    private void saveAccomClassMapping(WebrateAccomClassMappingDTO webrateAccomClassMappingDTO,
                                       Set<AccomClass> storedAccomClassSet, WebrateAccomType webrateAccomType) {
        Set<AccomClass> toSave = new HashSet<>(storedAccomClassSet);
        if (webrateAccomClassMappingDTO.getAccomClasses() != null) {
            toSave.addAll(webrateAccomClassMappingDTO.getAccomClasses());
        }
        toSave.removeAll(storedAccomClassSet);

        for(AccomClass accomClassToSave : toSave) {
            WebrateAccomClassMapping webrateAccomClassMappingToSave = new WebrateAccomClassMapping();
            webrateAccomClassMappingToSave.setWebrateAccomType(webrateAccomType);
            webrateAccomClassMappingToSave.setAccomClass(accomClassToSave);
            crudService.save(webrateAccomClassMappingToSave);
        }
    }

    private void removeAccomClassMapping(WebrateAccomClassMappingDTO webrateAccomClassMappingDTO,
                                         Set<AccomClass> storedAccomClassSet, WebrateAccomType webrateAccomType) {
        Set<AccomClass> toDelete = new HashSet<>(storedAccomClassSet);
        if (webrateAccomClassMappingDTO.getAccomClasses() != null) {
            toDelete.removeAll(webrateAccomClassMappingDTO.getAccomClasses());
        }
        for(AccomClass accomClassToDelete : toDelete) {
            if(accomClassToDelete != null) {
                WebrateAccomClassMapping wacmToDelete = crudService.findByNamedQuerySingleResult(WebrateAccomClassMapping.BY_ACCOM_CLASS_ACCOM_TYPE,
                        QueryParameter.with("accomClassId", accomClassToDelete.getId()).and("accomTypeId", webrateAccomType.getId()).parameters());
                if(wacmToDelete != null) {
                    crudService.delete(wacmToDelete); //aud entry deletion
                }
                crudService.executeUpdateByNamedQuery(WebrateAccomClassMapping.DELETE_BY_ACCOM_CLASS_ACCOM_TYPE,
                        QueryParameter.with("accomClassId", accomClassToDelete.getId()).and("accomTypeId", webrateAccomType.getId()).parameters()); //actual delete
            }
        }
    }

    public void deleteWebrateAccomTypes(List<String> webrateAccomTypes) {
        Set<String> trimmedWebrateAccomTypes = webrateAccomTypes.stream()
                .filter(Objects::nonNull)
                .map(String::trim)
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(trimmedWebrateAccomTypes)) {
            Integer propertyId = PacmanWorkContextHelper.getPropertyId();
            List<WebrateAccomType> accomTypesToBeDeleted = crudService.findByNamedQuery(WebrateAccomType.BY_PROPERTY_ID_AND_ACCOM_NAMES,
                    QueryParameter.with(PARAM_PROPERTY_ID, propertyId)
                            .and(PARAM_ACCOM_NAMES, trimmedWebrateAccomTypes).parameters());
            Set<WebrateAccomClassMapping> webrateAccomClassMappingsToBeDeleted = new HashSet<>();
            accomTypesToBeDeleted.forEach(webrateAccomType -> webrateAccomClassMappingsToBeDeleted.addAll(webrateAccomType.getWebrateAccomClassMappings()));
            if (CollectionUtils.isNotEmpty(webrateAccomClassMappingsToBeDeleted)) {
                crudService.delete(webrateAccomClassMappingsToBeDeleted);
            }
            webrateShoppingCleanUpService.cleanUpWebrateAccomTypes(accomTypesToBeDeleted);
        }
    }

    private void populateModifiedMappings(WebrateAccomType webrateAccomType,
                                          WebrateAccomClassMappingDTO webrateAccomClassMappingDTO,
                                          Map<WebrateAccomType, AccomClass> changedMapping,
                                          Map<WebrateAccomType, AccomClass> currentMapping) {
        webrateAccomType.setShouldDelete(webrateAccomClassMappingDTO.isShouldDelete());
        webrateAccomType.setWebrateAccomName(webrateAccomClassMappingDTO.getWebrateAccomName());
        webrateAccomType.setWebrateAccomAlias(webrateAccomClassMappingDTO.getWebrateAccomTypeAlias());
        if (webrateAccomClassMappingDTO.getAccomClass() != null) {
            if (webrateAccomType.getWebrateAccomClassMappings() != null
                    && !webrateAccomType.getWebrateAccomClassMappings().isEmpty()) {
                WebrateAccomClassMapping mapping = webrateAccomType.getWebrateAccomClassMappings().iterator().next();
                if (!mapping.getAccomClass().getId().equals(webrateAccomClassMappingDTO.getAccomClass().getId())) {
                    Set<WebrateAccomClassMapping> webrateAccomClassMappings = updateWebrateAccomClassMappings(webrateAccomType,
                            webrateAccomClassMappingDTO.getAccomClass(), mapping);
                    webrateAccomType.setWebrateAccomClassMappings(webrateAccomClassMappings);
                    changedMapping.put(webrateAccomType, webrateAccomClassMappingDTO.getAccomClass());
                }
            } else {
                Set<WebrateAccomClassMapping> webrateAccomClassMappings = updateWebrateAccomClassMappings(webrateAccomType, webrateAccomClassMappingDTO.getAccomClass(), new WebrateAccomClassMapping());
                webrateAccomType.setWebrateAccomClassMappings(webrateAccomClassMappings);
            }
            currentMapping.put(webrateAccomType, webrateAccomClassMappingDTO.getAccomClass());
        }
    }

    private void populateModifiedMappingsForMultiAccomClasses(WebrateAccomType webrateAccomType,
                                          WebrateAccomClassMappingDTO webrateAccomClassMappingDTO,
                                          Map<WebrateAccomType, AccomClass> changedMapping,
                                          Map<WebrateAccomType, AccomClass> currentMapping) {
        webrateAccomType.setShouldDelete(webrateAccomClassMappingDTO.isShouldDelete());
        webrateAccomType.setWebrateAccomName(webrateAccomClassMappingDTO.getWebrateAccomName());
        webrateAccomType.setWebrateAccomAlias(webrateAccomClassMappingDTO.getWebrateAccomTypeAlias());
        if (webrateAccomClassMappingDTO.getAccomClasses() != null) {
            if (webrateAccomType.getWebrateAccomClassMappings() != null
                    && !webrateAccomType.getWebrateAccomClassMappings().isEmpty()) {
                for (WebrateAccomClassMapping mapping : webrateAccomType.getWebrateAccomClassMappings()) {
                    if (webrateAccomClassMappingDTO.getAccomClasses().stream().noneMatch(v -> v.getId().equals(mapping.getAccomClass().getId()))) {
                        changedMapping.put(webrateAccomType, webrateAccomClassMappingDTO.getAccomClass());
                    }
                }
            }
            webrateAccomClassMappingDTO.getAccomClasses().forEach(accomClass -> currentMapping.put(webrateAccomType, accomClass));
        }
    }

    private void populateModifiedMappings(Map<WebrateAccomType, AccomClass> changedMapping, Map<WebrateAccomType, AccomClass> currentMapping, WebrateAccomClassMappingDTO webrateAccomClassMappingDTO) {
        if (webrateAccomClassMappingDTO.getAccomClass() != null) {
            final WebrateAccomClassMapping mapping = getWebrateAccomClassMapping(webrateAccomClassMappingDTO.getWebrateAccomTypeId());
            WebrateAccomType webrateAccomType = getWebrateAccomType(webrateAccomClassMappingDTO.getWebrateAccomTypeId());
            if (mapping != null) {
                if (!mapping.getAccomClass().getId().equals(webrateAccomClassMappingDTO.getAccomClass().getId())) {
                    changedMapping.put(webrateAccomType, webrateAccomClassMappingDTO.getAccomClass());
                    Set<WebrateAccomClassMapping> webrateAccomClassMappings = updateWebrateAccomClassMappings(webrateAccomType, webrateAccomClassMappingDTO.getAccomClass(), mapping);
                    webrateAccomType.setWebrateAccomClassMappings(webrateAccomClassMappings);
                } else {
                    Set<WebrateAccomClassMapping> webrateAccomClassMappings = getWebrateAccomClassMappings(webrateAccomClassMappingDTO, new WebrateAccomClassMapping());
                    webrateAccomType.setWebrateAccomClassMappings(webrateAccomClassMappings);
                }
            }
            currentMapping.put(webrateAccomType, webrateAccomClassMappingDTO.getAccomClass());
        }
    }

    private void getDeletedMappings(List<WebrateAccomType> webrateAccomTypes, WebrateAccomClassMappingDTO item) {
        if (item.isShouldDelete()) {
            Objects.requireNonNull(webrateAccomTypes.stream().filter(webrateAccomType -> item.getWebrateAccomTypeId() == (webrateAccomType.getId())).findFirst().orElse(null)).setShouldDelete(true);
        }
    }

    protected List<AccomClass> getRoomClassesToCleanUpCPCNotifications(Map<WebrateAccomType, AccomClass> changedMapping, Map<WebrateAccomType, AccomClass> currentMapping) {
        List<AccomClass> accomClasses = new ArrayList<>();
        if (configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CPC_NOTIFICATION_BY_ROOM_CLASS)) {
            changedMapping.values().forEach(accomClass -> {
                if (currentMapping.values().stream().noneMatch(v -> v.getId().equals(accomClass.getId()))) {
                    accomClasses.add(accomClass);
                }
            });
        }
        return accomClasses;
    }

    private Set<WebrateAccomClassMapping> updateWebrateAccomClassMappings(WebrateAccomType webrateAccomType,
                                                                          AccomClass accomClass,
                                                                          WebrateAccomClassMapping accomClassMapping) {
        accomClassMapping.setWebrateAccomType(webrateAccomType);
        accomClassMapping.setAccomClass(accomClass);
        return new HashSet<WebrateAccomClassMapping>() {{
            add(accomClassMapping);
        }};
    }

    private Set<WebrateAccomClassMapping> getWebrateAccomClassMappings(WebrateAccomClassMappingDTO webrateAccomClassMappingDTO, WebrateAccomClassMapping accomClassMapping) {
        accomClassMapping.setWebrateAccomType(getWebrateAccomType(webrateAccomClassMappingDTO.getWebrateAccomTypeId()));
        accomClassMapping.getWebrateAccomType().setWebrateAccomAlias(webrateAccomClassMappingDTO.getWebrateAccomName());
        accomClassMapping.setAccomClass(getAccomClass(webrateAccomClassMappingDTO.getAccomClass().getCode()));
        return Set.of(accomClassMapping);
    }

    private AccomClass getAccomClass(String accomClassCode) {
        return crudService.findByNamedQuerySingleResult(
                AccomClass.BY_CODE,
                QueryParameter.with("code", accomClassCode).and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    private WebrateAccomType getWebrateAccomType(int accomTypeId) {
        return crudService.findByNamedQuerySingleResult(
                WebrateAccomType.BY_ID,
                QueryParameter.with("id", accomTypeId).parameters());
    }

    private WebrateAccomClassMapping getWebrateAccomClassMapping(int accomTypeId) {
        List<Object> mapping = crudService.findByNamedQuery(WebrateAccomClassMapping.BY_ACCOMTYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());
        if (mapping.isEmpty()) {
            return null;
        }
        return (WebrateAccomClassMapping) mapping.get(0);
    }

    public List<AccomClass> getActiveNonDefaultAccomClasses() {
        List<AccomClass> accomClasses = accommodation2Service.getActiveNonDefaultAccomClasses();
        accomClasses = accomClasses.stream().filter(ac -> null != ac.getAccomTypes() && !ac.hasZeroCapacity()).collect(Collectors.toList());
        accomClasses.add(accommodation2Service.findUnassignedAccomClass(PacmanWorkContextHelper.getPropertyId()));
        return accomClasses;
    }

    public boolean isRateShoppingAutoConfigurationAllowed() {
        return !isRDLEnabled() && webrateDefaultChannelIsNotConfigured() && straightBarMarketSegmentsArePresent()
                && allAccomTypesAreMapped() && propertyIsNotLimitedDataBuild();
    }

    private boolean isRDLEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);
    }

    public boolean propertyIsNotLimitedDataBuild() {
        return !configParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED);
    }

    public boolean isWebrateAccomTypeCleanupJobRunning() {
        return webrateShoppingDataService.isWebrateCleanupJobRunningOrWebrateSourceUpdateJobRunning(JobName.WebrateAccomTypeCleanupJob);
    }

    @SuppressWarnings("unchecked")
    public List<WebrateRanking> getAllWebrateRanking() {
        return crudService.findByNamedQuery(WebrateRanking.ALL);
    }

    @SuppressWarnings("unchecked")
    public List<WebrateRankingAccomClass> getWebrateRankingAccomClassForProperty(Integer propertyId) {
        //getAllAccomClassDetails() uses workContext
        List<WebrateRankingAccomClass> webrateRankingAcList;
        List<WebrateRankingAccomClassOverride> webrateRankingAcOvrList;

        WorkContextType wc = (WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY);
        wc.setPropertyId(propertyId);

        List<Integer> accomIdList = new ArrayList<>();

        List<AccomClass> accomList = accommodation2Service.getAccomClassesByViewOrder();
        for (AccomClass accomClassObj : accomList) {
            accomIdList.add(accomClassObj.getId());
        }

        if (!accomList.isEmpty()) {
            webrateRankingAcList = crudService.findByNamedQuery(WebrateRankingAccomClass.BY_ACCOMCLASS_ID, QueryParameter.with("accomIdList", accomIdList).parameters());

            if (SystemConfig.isMarketPostionConstraintEnabled()) {
                logger.info("isIgnoreCompetitorDowEnabled - True");

                if (CollectionUtils.isNotEmpty(webrateRankingAcList)) {
                    for (WebrateRankingAccomClass webrateRankingAccomClass : webrateRankingAcList) {
                        webrateRankingAcOvrList = crudService.findByNamedQuery(WebrateRankingAccomClassOverride.BY_ACCOMCLASS_ID_PRODUCT_ID,
                                QueryParameter.with("accomId", webrateRankingAccomClass.getAccomClass().getId())
                                        .and("productId", webrateRankingAccomClass.getProductID()).parameters());
                        List<WebrateRankingAccomClassOverride> webrateRankingAcOvrListForProduct = webrateRankingAcOvrList.stream().filter(wraco -> wraco.getAccomClass().equals(webrateRankingAccomClass.getAccomClass()) && wraco.getProductID().equals(webrateRankingAccomClass.getProductID())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(webrateRankingAcOvrListForProduct)) {
                            Set<WebrateRankingAccomClassOverride> webrateRankingAcOvrSet = new HashSet<>();
                            for (WebrateRankingAccomClassOverride webrateRankingAccomClassOvr : webrateRankingAcOvrListForProduct) {
                                webrateRankingAcOvrSet.add(webrateRankingAccomClassOvr);
                            }
                            webrateRankingAccomClass.setWebrateRankingACOverrides(webrateRankingAcOvrSet);
                        }
                    }
                }
            } else {
                if (CollectionUtils.isNotEmpty(webrateRankingAcList)) {
                    for (WebrateRankingAccomClass webrateRankingAccomClass : webrateRankingAcList) {
                        webrateRankingAccomClass.setWebrateRankingMonday(webrateRankingAccomClass.getWebrateRanking());
                        webrateRankingAccomClass.setWebrateRankingTuesday(webrateRankingAccomClass.getWebrateRanking());
                        webrateRankingAccomClass.setWebrateRankingWednesday(webrateRankingAccomClass.getWebrateRanking());
                        webrateRankingAccomClass.setWebrateRankingThursday(webrateRankingAccomClass.getWebrateRanking());
                        webrateRankingAccomClass.setWebrateRankingFriday(webrateRankingAccomClass.getWebrateRanking());
                        webrateRankingAccomClass.setWebrateRankingSaturday(webrateRankingAccomClass.getWebrateRanking());
                        webrateRankingAccomClass.setWebrateRankingSunday(webrateRankingAccomClass.getWebrateRanking());
                    }
                }
            }

            return webrateRankingAcList;
        }

        return null;
    }

    public boolean saveAccomMapping(List<WebrateAccomType> accomTypeList,
                                    List<WebrateRankingAccomClass> webrateRankingAccomClassList, Integer propertyId, String userId) {
        return saveAccomMapping(accomTypeList, webrateRankingAccomClassList, propertyId, userId, null);
    }

    public boolean saveAccomMapping(List<WebrateAccomType> accomTypeList,
                                    List<WebrateRankingAccomClass> webrateRankingAccomClassList, Integer propertyId, String userId,
                                    List<AccomClass> cleanUpCPCNotificationsForRoomClass) {

        if (accomTypeList != null) {

            accomTypeList.stream()
                    .filter(at -> !at.getShouldDelete())
                    .peek(this::setDefaultWebrateAccomAlias)
                    .forEach(crudService::save);

            if (CollectionUtils.isNotEmpty(cleanUpCPCNotificationsForRoomClass)) {
                cleanUpCPCNotificationsForRoomClass.forEach(webrateShoppingCleanUpService::cleanUpCPCByRoomClassNotificationsConfig);
            }

            List<WebrateAccomType> toBeDeleted = accomTypeList.stream()
                    .filter(WebrateAccomType::getShouldDelete)
                    .collect(Collectors.toList());

            Set<WebrateAccomClassMapping> webrateAccomClassMappingsToBeDeleted = new HashSet<>();
            toBeDeleted.stream().forEach(webrateAccomType -> webrateAccomClassMappingsToBeDeleted.addAll(webrateAccomType.getWebrateAccomClassMappings()));

            webrateShoppingCleanUpService.cleanUpWebrateAccomTypes(toBeDeleted);

            updateCompetitorAccomClassReferenceForAllProducts(propertyId, webrateAccomClassMappingsToBeDeleted);
            checkAndResolveNewCompetitiveRoomTypeAlert(accomTypeList, propertyId);
        }
        createAndUpdateWebRateRankingAccommodationClass(webrateRankingAccomClassList, propertyId, userId);
        return true;
    }

    public void createAndUpdateWebRateRankingAccommodationClass(List<WebrateRankingAccomClass> webrateRankingAccomClassList,
                                                                Integer propertyId, String userId) {
        if (null == webrateRankingAccomClassList) {
            webrateRankingAccomClassList = createDefaultWebrateRankingAccomClassesForSystemDefaultAndIndependentProducts(propertyId, userId);
        }
        if (webrateRankingAccomClassList != null) {
            boolean isDirty = false;
            boolean syncRequiredForDefaultConfig = false;
            for (WebrateRankingAccomClass webrateRankingAccomClass : webrateRankingAccomClassList) {
                boolean isRCmappingDeleted = false;

                // Check to see if a flag is dirty
                if (!isDirty && isDirty(webrateRankingAccomClass)) {
                    isDirty = true;
                }

                // Validate default configuration settings for sync
                if (SystemConfig.isMarketPostionConstraintEnabled()) {
                    if (!isDirty && isSyncRequiredForDefaultConfigChanged(webrateRankingAccomClass)) {
                        syncRequiredForDefaultConfig = true;
                    }

                    if (syncRequiredForDefaultConfig && !syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
                        syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
                    }

                }

                if (webrateRankingAccomClass.getIsDeleted() != null && webrateRankingAccomClass.getIsDeleted().intValue() == 1) {
                    if (webrateRankingAccomClass.getId() != null) { //US3722
                        if (SystemConfig.isMarketPostionConstraintEnabled()) {
                            isRCmappingDeleted = true;
                            List<WebrateRankingAccomClassOverride> webrateRankingAccomClsOvrSet = crudService.findByNamedQuery(WebrateRankingAccomClassOverride.BY_ACCOMCLASS_ID_PRODUCT_ID,
                                    QueryParameter.with("accomId", webrateRankingAccomClass.getAccomClass().getId())
                                            .and("productId", webrateRankingAccomClass.getProductID()).parameters());
                            if (CollectionUtils.isNotEmpty(webrateRankingAccomClsOvrSet)) {
                                for (WebrateRankingAccomClassOverride webrateRankingACOvr : webrateRankingAccomClsOvrSet) {
                                    crudService.delete(WebrateRankingAccomClassOverride.class, webrateRankingACOvr.getId());
                                }
                            }
                        }
                        crudService.delete(WebrateRankingAccomClass.class, webrateRankingAccomClass.getId());
                        crudService.getEntityManager().flush();
                    }
                } else {
                    if (!SystemConfig.isMarketPostionConstraintEnabled()) {
                        webrateRankingAccomClass.setWebrateRankingMonday(webrateRankingAccomClass.getWebrateRanking());
                        webrateRankingAccomClass.setWebrateRankingTuesday(webrateRankingAccomClass.getWebrateRanking());
                        webrateRankingAccomClass.setWebrateRankingWednesday(webrateRankingAccomClass.getWebrateRanking());
                        webrateRankingAccomClass.setWebrateRankingThursday(webrateRankingAccomClass.getWebrateRanking());
                        webrateRankingAccomClass.setWebrateRankingFriday(webrateRankingAccomClass.getWebrateRanking());
                        webrateRankingAccomClass.setWebrateRankingSaturday(webrateRankingAccomClass.getWebrateRanking());
                        webrateRankingAccomClass.setWebrateRankingSunday(webrateRankingAccomClass.getWebrateRanking());
                    }
                    crudService.save(webrateRankingAccomClass);
                }

                if (SystemConfig.isMarketPostionConstraintEnabled() && !isRCmappingDeleted) {
                    boolean syncRequired = false;
                    Set<WebrateRankingAccomClassOverride> webrateRankingAccomClassOvrSet;
                    webrateRankingAccomClassOvrSet = webrateRankingAccomClass.getWebrateRankingACOverrides();
                    if (CollectionUtils.isNotEmpty(webrateRankingAccomClassOvrSet)) {
                        for (WebrateRankingAccomClassOverride webrateRankingACOvr : webrateRankingAccomClassOvrSet) {
                            if (!syncRequired && isSyncRequired(webrateRankingACOvr)) {
                                syncRequired = true;
                            }
                            crudService.save(webrateRankingACOvr);
                        }

                        if (syncRequired && !syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
                            syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
                        }
                    }
                }
            }
        }
    }

    public void checkAndResolveNewCompetitiveRoomTypeAlert(List<WebrateAccomType> webrateAccomTypes, Integer propertyId) {
        boolean resolveAlert = webrateAccomTypes.stream().allMatch(at -> at.getShouldDelete() || CollectionUtils.isNotEmpty(at.getWebrateAccomClassMappings()));
        if (resolveAlert) {
            alertService.resolveAllAlerts(AlertType.NewCompetitiveRoomTypeFound, propertyId);
        }
    }

    public boolean isPropertyCompetitiveMarketPositionConstraintsActive() {
        long webrateRankingAccomClassList = crudService.findByNamedQuerySingleResult(WebrateRankingAccomClass.COUNT_OF_NON_NONE_CONSTRAINTS);
        long webrateRankingAccomClassOverrideList = crudService.findByNamedQuerySingleResult(WebrateRankingAccomClassOverride.COUNT_ALL);

        return webrateRankingAccomClassList != 0 || webrateRankingAccomClassOverrideList != 0;
    }

    public List<String> getUnmappedWebrateAccomTypes() {
        return crudService.findByNativeQuery(WebrateAccomType.GET_UNMAPPED_ACCOM_TYPES);
    }

    private void setDefaultWebrateAccomAlias(WebrateAccomType webrateAccomType) {
        if (StringUtils.isEmpty(webrateAccomType.getWebrateAccomAlias())) {
            webrateAccomType.setWebrateAccomAlias("Display Name");
        }
    }

    public void createAndSaveDefaultWebrateRankingAccomClassesForProduct(Integer propertyId, String userId, Product product) {
        List<WebrateRankingAccomClass> defaultWebrateRankingAccomClasses = createDefaultWebrateRankingAccomClassesForProduct(propertyId, userId, product);
        crudService.save(defaultWebrateRankingAccomClasses);
    }

    public List<WebrateRankingAccomClass> createDefaultWebrateRankingAccomClassesForSystemDefaultAndIndependentProducts(Integer propertyId, String userId) {
        List<Product> allProducts = agileRatesConfigurationService.findSystemDefaultProductAndIndependentProducts();
        return createDefaultWebrateRankingAccomClasses(propertyId, userId, allProducts);
    }

    public List<WebrateRankingAccomClass> createDefaultWebrateRankingAccomClassesForProduct(Integer propertyId, String userId, Product product) {
        return createDefaultWebrateRankingAccomClasses(propertyId, userId, Arrays.asList(product));
    }

    public List<WebrateRankingAccomClass> createDefaultWebrateRankingAccomClasses(Integer propertyId, String userId, List<Product> allProducts) {
        List<WebrateRankingAccomClass> webrateRankingAccomClasses = crudService.findAll(WebrateRankingAccomClass.class);

        List<AccomClass> accomClasses = crudService.findByNamedQuery(
                WebrateAccomClassMapping.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());

        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);

        List<ProductAccomType> productAccomTypes = new ArrayList<>();
        if (!isIndependentProductsEnabled) {
            //If independent products is disabled; filter list to BAR product only
            allProducts = allProducts.stream().filter(product -> product.isSystemDefault()).collect(Collectors.toList());
        } else {
            productAccomTypes = crudService.findAll(ProductAccomType.class);
        }

        List<WebrateRankingAccomClass> finalWebrateRankingAccomClassesList = new ArrayList<>();
        for (Product product : allProducts) {
            List<AccomClass> accomClassesForProduct = findRoomClassesByProduct(product, accomClasses, productAccomTypes);

            List<WebrateRankingAccomClass> productWebrateRankingAccomClassesList = webrateRankingAccomClasses.stream()
                    .filter(wrac -> wrac.getProductID().equals(product.getId()))
                    .collect(Collectors.toList());
            finalWebrateRankingAccomClassesList.addAll(productWebrateRankingAccomClassesList);

            //Get all Accom Classes defined for the selected Product
            List<AccomClass> accomClassesInWebrateRankingAccomClass = productWebrateRankingAccomClassesList.stream()
                    .map(wrac -> wrac.getAccomClass())
                    .collect(Collectors.toList());

            //Set all Webrate Ranking Accom Classes to Deleted for Accom Classes not in the Accom Class List
            List<WebrateRankingAccomClass> webrateRankingAccomClassesToDelete = productWebrateRankingAccomClassesList.stream()
                    .filter(wrac -> !accomClassesForProduct.contains(wrac.getAccomClass()))
                    .peek(wrac -> wrac.setIsDeleted(1))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(webrateRankingAccomClassesToDelete)) {
                for (WebrateRankingAccomClass webrateRankingAccomClass : webrateRankingAccomClassesToDelete) {
                    List<WebrateRankingAccomClassOverride> webrateRankingAccomClsOvrSet = crudService.findByNamedQuery(WebrateRankingAccomClassOverride.BY_ACCOMCLASS_ID_PRODUCT_ID,
                            QueryParameter.with("accomId", webrateRankingAccomClass.getAccomClass().getId())
                                    .and("productId", webrateRankingAccomClass.getProductID()).parameters());
                    if (CollectionUtils.isNotEmpty(webrateRankingAccomClsOvrSet)) {
                        crudService.delete(webrateRankingAccomClsOvrSet);
                    }
                }
                finalWebrateRankingAccomClassesList.removeAll(webrateRankingAccomClassesToDelete);
                crudService.delete(webrateRankingAccomClassesToDelete);
            }

            //Create default Webrate Ranking Accom Class for any Room Classes that don't exist in the Webrate Ranking Accom Class list
            List<WebrateRankingAccomClass> newWebrateRankingAccomClasses = accomClassesForProduct.stream()
                    .filter(ac -> !accomClassesInWebrateRankingAccomClass.contains(ac))
                    .map(ac -> createNewWebrateRankingAccomClass(ac, product, userId))
                    .collect(Collectors.toList());

            finalWebrateRankingAccomClassesList.addAll(newWebrateRankingAccomClasses);
        }

        return finalWebrateRankingAccomClassesList;
    }

    public List<AccomClass> findRoomClassesByProduct(Product product, List<AccomClass> accomClasses, List<ProductAccomType> productAccomTypes) {
        if (product.isSystemDefault()) {
            return accomClasses;
        }
        List<ProductAccomType> productAccomTypesForProduct = productAccomTypes.stream()
                .filter(productAccomType -> productAccomType.getProduct().equals(product))
                .collect(Collectors.toList());
        Predicate<AccomClass> validAccomClassPredicate = accomClass -> (accomClass.getSystemDefault() == 0);
        return accomClasses.stream()
                .filter(accomClass -> accomClass.getAccomTypes().stream()
                        .anyMatch(productAccomTypesForProduct.stream()
                                .map(ProductAccomType::getAccomType)
                                .collect(Collectors.toSet())::contains))
                .filter(validAccomClassPredicate)
                .collect(Collectors.toList());
    }

    private WebrateRankingAccomClass createNewWebrateRankingAccomClass(AccomClass ac, Product product, String userId) {
        WebrateRankingAccomClass webrateRankingAccomClass = new WebrateRankingAccomClass();
        webrateRankingAccomClass.setAccomClass(ac);
        webrateRankingAccomClass.setProductID(product.getId());
        WebrateRanking defaultWebrateRanking = crudService.findByNamedQuerySingleResult(WebrateRanking.FIND_BY_NAME, QueryParameter.with("webrateRankingName", "None").parameters());
        webrateRankingAccomClass.setWebrateRanking(defaultWebrateRanking);
        webrateRankingAccomClass.setWebrateRankingSunday(defaultWebrateRanking);
        webrateRankingAccomClass.setWebrateRankingMonday(defaultWebrateRanking);
        webrateRankingAccomClass.setWebrateRankingTuesday(defaultWebrateRanking);
        webrateRankingAccomClass.setWebrateRankingWednesday(defaultWebrateRanking);
        webrateRankingAccomClass.setWebrateRankingThursday(defaultWebrateRanking);
        webrateRankingAccomClass.setWebrateRankingFriday(defaultWebrateRanking);
        webrateRankingAccomClass.setWebrateRankingSaturday(defaultWebrateRanking);
        final Date now = new Date();
        webrateRankingAccomClass.setCreateDate(now);
        int userIdInt;
        try {
            userIdInt = userId != null ? Integer.valueOf(userId) : 1;
        } catch (NumberFormatException e) {
            userIdInt = SYSTEM_USER_ID;
        }
        webrateRankingAccomClass.setCreatedByUserId(userIdInt);
        webrateRankingAccomClass.setLastUpdatedDate(now);
        webrateRankingAccomClass.setLastUpdatedByUserId(userIdInt);
        return webrateRankingAccomClass;
    }

    public WebrateRankingAccomClassOverride saveWebrateRankingAccomClassOverride(WebrateRankingAccomClassOverride webrateRankingAccomClassOverride) {
        boolean syncRequired = isSyncRequired(webrateRankingAccomClassOverride);
        webrateRankingAccomClassOverride = crudService.save(webrateRankingAccomClassOverride);
        if (syncRequired && !syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
        }
        return webrateRankingAccomClassOverride;
    }

    public void removeSeasonOverride(List<WebrateRankingAccomClassOverride> webrateRankingAccomClassOVRList) {
        if (CollectionUtils.isNotEmpty(webrateRankingAccomClassOVRList)) {
            for (WebrateRankingAccomClassOverride webrateRankingACOvr : webrateRankingAccomClassOVRList) {
                if (webrateRankingACOvr.getIsDeleted() != null && webrateRankingACOvr.getIsDeleted().intValue() == 1) {
                    crudService.delete(WebrateRankingAccomClassOverride.class, webrateRankingACOvr.getId());
                    crudService.getEntityManager().flush();

                    if (!syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
                        syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
                    }
                }
            }
        }
    }

    public boolean isSyncRequired(WebrateRankingAccomClassOverride webrateRankingAccomClassOverride) {
        // If the override channel is new, a sync is required
        if (webrateRankingAccomClassOverride.getId() == null) {
            return true;
        }

        WebrateRankingAccomClassOverride existingWebrateRankingAccomClassOverride = crudService.find(WebrateRankingAccomClassOverride.class, webrateRankingAccomClassOverride.getId());
        if (null != existingWebrateRankingAccomClassOverride) {
            // If any of the day overrides or start/end dates changed, a sync is required
            EqualsBuilder equalsBuilder = new EqualsBuilder();
            equalsBuilder.append(webrateRankingAccomClassOverride.getWebrateRankingStartDT(), existingWebrateRankingAccomClassOverride.getWebrateRankingStartDT());
            equalsBuilder.append(webrateRankingAccomClassOverride.getWebrateRankingEndDT(), existingWebrateRankingAccomClassOverride.getWebrateRankingEndDT());
            equalsBuilder.append(webrateRankingAccomClassOverride.getWebrateRankingOvrSunday(), existingWebrateRankingAccomClassOverride.getWebrateRankingOvrSunday());
            equalsBuilder.append(webrateRankingAccomClassOverride.getWebrateRankingOvrMonday(), existingWebrateRankingAccomClassOverride.getWebrateRankingOvrMonday());
            equalsBuilder.append(webrateRankingAccomClassOverride.getWebrateRankingOvrTuesday(), existingWebrateRankingAccomClassOverride.getWebrateRankingOvrTuesday());
            equalsBuilder.append(webrateRankingAccomClassOverride.getWebrateRankingOvrWednesday(), existingWebrateRankingAccomClassOverride.getWebrateRankingOvrWednesday());
            equalsBuilder.append(webrateRankingAccomClassOverride.getWebrateRankingOvrThursday(), existingWebrateRankingAccomClassOverride.getWebrateRankingOvrThursday());
            equalsBuilder.append(webrateRankingAccomClassOverride.getWebrateRankingOvrFriday(), existingWebrateRankingAccomClassOverride.getWebrateRankingOvrFriday());
            equalsBuilder.append(webrateRankingAccomClassOverride.getWebrateRankingOvrSaturday(), existingWebrateRankingAccomClassOverride.getWebrateRankingOvrSaturday());
            return !equalsBuilder.isEquals();
        }
        return false;
    }

    public boolean isSyncRequiredForDefaultConfigChanged(WebrateRankingAccomClass webrateRankingAccomClass) {
        // If newly configured default settings, a sync is required
        if (webrateRankingAccomClass.getId() == null) {
            return true;
        }

        WebrateRankingAccomClass existingWebrateRankingAccomClass = crudService.find(WebrateRankingAccomClass.class, webrateRankingAccomClass.getId());
        if (null != existingWebrateRankingAccomClass) {
            // If any of the day overrides or start/end dates changed, a sync is required
            EqualsBuilder equalsBuilder = new EqualsBuilder();
            equalsBuilder.append(webrateRankingAccomClass.getWebrateRankingSunday(), existingWebrateRankingAccomClass.getWebrateRankingSunday());
            equalsBuilder.append(webrateRankingAccomClass.getWebrateRankingMonday(), existingWebrateRankingAccomClass.getWebrateRankingMonday());
            equalsBuilder.append(webrateRankingAccomClass.getWebrateRankingTuesday(), existingWebrateRankingAccomClass.getWebrateRankingTuesday());
            equalsBuilder.append(webrateRankingAccomClass.getWebrateRankingWednesday(), existingWebrateRankingAccomClass.getWebrateRankingWednesday());
            equalsBuilder.append(webrateRankingAccomClass.getWebrateRankingThursday(), existingWebrateRankingAccomClass.getWebrateRankingThursday());
            equalsBuilder.append(webrateRankingAccomClass.getWebrateRankingFriday(), existingWebrateRankingAccomClass.getWebrateRankingFriday());
            equalsBuilder.append(webrateRankingAccomClass.getWebrateRankingSaturday(), existingWebrateRankingAccomClass.getWebrateRankingSaturday());
            return !equalsBuilder.isEquals();
        } else {
            return false;
        }

    }


    public boolean isDirty(WebrateRankingAccomClass webrateRankingAccomClass) {
        // New SebrateRankingAccomClass rqeuired a sync
        if (webrateRankingAccomClass.getId() == null) {
            clearAndRegisterSyncEvent();
            return true;
        }

        // If the WebrateRankingAccomClass is deleted, a sync is required
        if (webrateRankingAccomClass.getIsDeleted() != null && webrateRankingAccomClass.getIsDeleted() == 1) {
            clearAndRegisterSyncEvent();
            return true;
        }

        // Get the one that was last saved in the database
        WebrateRankingAccomClass existingWebrateRankingAccomClass = crudService.find(WebrateRankingAccomClass.class, webrateRankingAccomClass.getId());

        // Check to see if the AccomClass is different, if so a sync is required
        EqualsBuilder webrateRankingAccomClassEqualsBuilder = new EqualsBuilder();
        webrateRankingAccomClassEqualsBuilder.append(existingWebrateRankingAccomClass.getAccomClass(), webrateRankingAccomClass.getAccomClass());

        if (!webrateRankingAccomClassEqualsBuilder.isEquals()) {
            clearAndRegisterSyncEvent();
            return true;
        }

        if (!webrateRankingAccomClassEqualsBuilder.isEquals()) {
            clearAndRegisterSyncEvent();

            return true;
        }

        if (!SystemConfig.isMarketPostionConstraintEnabled()) {
            // Check to see if the WebrateRanking is different, if so dirty the no-sync flag
            webrateRankingAccomClassEqualsBuilder.append(existingWebrateRankingAccomClass.getWebrateRanking(), webrateRankingAccomClass.getWebrateRanking());
            if (!webrateRankingAccomClassEqualsBuilder.isEquals() && !syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
                syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
                return true;
            }
        }

        return false;
    }

    private void clearAndRegisterSyncEvent() {
        // If there was a webrate config change that a sync isn't required for, it needs to be removed
        syncEventAggregatorService.clearSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);

        // Register Sync Event
        syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED);
    }

    public void updateCompetitorAccomClassReferenceForProduct(Integer propertyId, Product product) {
        updateCompetitorAccomClassReference(propertyId, Arrays.asList(product));
    }

    public void updateCompetitorAccomClassReferenceForAllProducts(Integer propertyId) {
        List<Product> allProducts = agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts();
        updateCompetitorAccomClassReference(propertyId, allProducts);
    }

    public void updateCompetitorAccomClassReferenceForAllProducts(Integer propertyId, Set<WebrateAccomClassMapping> webrateAccomClassMappingsToBeDeleted) {
        List<Product> allProducts = agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts();
        updateCompetitorAccomClassReference(propertyId, allProducts, webrateAccomClassMappingsToBeDeleted);
    }

    /*
     * To Update the Competitors with the new Accom Class Mapping (non-Javadoc)
     *
     * @seecom.ideas.tetris.pacman.services.webrate.service.AccommodationMappingServiceLocal
     * #saveAccomMapping(java.util.List, java.util.List, java.lang.Integer)
     *
     */
    @SuppressWarnings("unchecked")
    protected void updateCompetitorAccomClassReference(Integer propertyId, List<Product> allProducts) {
        List<AccomClass> accomClasses = crudService.findByNamedQuery(
                WebrateAccomClassMapping.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());
        List<WebrateCompetitorsAccomClass> allWebrateCompetitorsAccomClass = crudService.findAll(WebrateCompetitorsAccomClass.class);
        List<WebrateCompetitors> competitorsList = crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());

        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        boolean isRDLEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);

        List<ProductAccomType> productAccomTypes = new ArrayList<>();
        if (!isIndependentProductsEnabled && !isRDLEnabled) {
            //If independent products is disabled; filter list to BAR product only
            allProducts = allProducts.stream().filter(product -> product.isSystemDefault()).collect(Collectors.toList());
        } else {
            productAccomTypes = crudService.findAll(ProductAccomType.class);
        }

        List<WebrateCompetitorsAccomClass> webrateCompetitorsAccomClassToDelete = new ArrayList<>();
        for (Product product : allProducts) {
            List<AccomClass> accomClassesForProduct = findRoomClassesByProduct(product, accomClasses, productAccomTypes);
            List<WebrateCompetitorsAccomClass> webrateCompetitorsAccomClassesForProduct = allWebrateCompetitorsAccomClass.stream().filter(wcac -> wcac.getProductID().equals(product.getId())).collect(Collectors.toList());

            webrateCompetitorsAccomClassesForProduct.stream().forEach(webrateCompetitorsAccomClass -> {
                if (!accomClassesForProduct.contains(webrateCompetitorsAccomClass.getAccomClass())) {
                    webrateCompetitorsAccomClassToDelete.add(webrateCompetitorsAccomClass);
                }
            });

            createWebrateCompAccomMapping(product, accomClassesForProduct, webrateCompetitorsAccomClassesForProduct, competitorsList);
        }

        if (CollectionUtils.isNotEmpty(webrateCompetitorsAccomClassToDelete)) {
            List<Integer> webrateCompetitorsAccomClassIDsToDelete = webrateCompetitorsAccomClassToDelete.stream().map(WebrateCompetitorsAccomClass::getId).collect(Collectors.toList());
            //Delete the WebrateOverrideCompetitorDetails entries associated with the WebrateCompetitorsAccomClass entries to be deleted
            List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetailsList = crudService
                    .findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS,
                            QueryParameter.with("compAccomClassIds", webrateCompetitorsAccomClassIDsToDelete).parameters());

            //Delete the WebrateOverrideCompetitor entries associated with the WebrateOverrideCompetitorDetails entries to be deleted
            List<WebrateOverrideCompetitor> webrateOverrideCompetitorsToDelete = webrateOverrideCompetitorDetailsList.stream().map(WebrateOverrideCompetitorDetails::getWebrateOverrideCompetitor).collect(Collectors.toList());

            webrateCompetitorsAccomClassToDelete.forEach(webrateCompetitorsAccomClass -> {
                WebrateCompetitors webrateCompetitor = webrateCompetitorsAccomClass.getWebrateCompetitor();
                webrateCompetitor.getWebrateCompetitorsAccomClasses().remove(webrateCompetitorsAccomClass);
            });

            webrateShoppingCleanUpService.cleanupWebrateCompetitorChannelMappings(webrateOverrideCompetitorDetailsList);
            crudService.delete(webrateOverrideCompetitorDetailsList);
            crudService.delete(webrateOverrideCompetitorsToDelete);
            //Delete WebrateCompetitorsAccomClass entries to be deleted
            crudService.delete(webrateCompetitorsAccomClassToDelete);
        }
    }

    protected void updateCompetitorAccomClassReference(Integer propertyId, List<Product> allProducts, Set<WebrateAccomClassMapping> webrateAccomClassMappingsToBeDeleted) {
        List<WebrateAccomClassMapping> allWebrateAccomClassMapping = new ArrayList<>();
        allWebrateAccomClassMapping.addAll(crudService.findAll(WebrateAccomClassMapping.class));
        allWebrateAccomClassMapping.removeAll(webrateAccomClassMappingsToBeDeleted);
        Set<AccomClass> accomClasses = allWebrateAccomClassMapping.stream()
                .map(WebrateAccomClassMapping::getAccomClass)
                .filter(accomClass -> accomClass.getAccomTypes() != null).collect(Collectors.toSet());

        List<WebrateCompetitorsAccomClass> allWebrateCompetitorsAccomClass = crudService.findAll(WebrateCompetitorsAccomClass.class);
        List<WebrateCompetitors> competitorsList = crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());

        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        boolean isRDLEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);

        List<ProductAccomType> productAccomTypes = new ArrayList<>();
        if (!(isIndependentProductsEnabled || isRDLEnabled)) {
            //If independent products is disabled; filter list to BAR product only
            allProducts = allProducts.stream().filter(product -> product.isSystemDefault()).collect(Collectors.toList());
        } else {
            productAccomTypes = crudService.findAll(ProductAccomType.class);
        }

        List<WebrateCompetitorsAccomClass> webrateCompetitorsAccomClassToDelete = new ArrayList<>();
        for (Product product : allProducts) {
            List<AccomClass> accomClassesForProduct = findRoomClassesByProduct(product, new ArrayList<>(accomClasses), productAccomTypes);
            List<WebrateCompetitorsAccomClass> webrateCompetitorsAccomClassesForProduct = allWebrateCompetitorsAccomClass.stream().filter(wcac -> wcac.getProductID().equals(product.getId())).collect(Collectors.toList());

            webrateCompetitorsAccomClassesForProduct.stream().forEach(webrateCompetitorsAccomClass -> {
                if (!accomClassesForProduct.contains(webrateCompetitorsAccomClass.getAccomClass())) {
                    webrateCompetitorsAccomClassToDelete.add(webrateCompetitorsAccomClass);
                }
            });

            createWebrateCompAccomMapping(product, accomClassesForProduct, webrateCompetitorsAccomClassesForProduct, competitorsList);
        }

        if (CollectionUtils.isNotEmpty(webrateCompetitorsAccomClassToDelete)) {
            List<Integer> webrateCompetitorsAccomClassIDsToDelete = webrateCompetitorsAccomClassToDelete.stream().map(WebrateCompetitorsAccomClass::getId).collect(Collectors.toList());
            //Delete the WebrateOverrideCompetitorDetails entries associated with the WebrateCompetitorsAccomClass entries to be deleted
            List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetailsList = crudService
                    .findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS,
                            QueryParameter.with("compAccomClassIds", webrateCompetitorsAccomClassIDsToDelete).parameters());

            //Delete the WebrateOverrideCompetitor entries associated with the WebrateOverrideCompetitorDetails entries to be deleted
            Set<WebrateOverrideCompetitor> webrateOverrideCompetitorsToDelete = webrateOverrideCompetitorDetailsList.stream().map(WebrateOverrideCompetitorDetails::getWebrateOverrideCompetitor).collect(Collectors.toSet());

            webrateCompetitorsAccomClassToDelete.forEach(webrateCompetitorsAccomClass -> {
                WebrateCompetitors webrateCompetitor = webrateCompetitorsAccomClass.getWebrateCompetitor();
                webrateCompetitor.getWebrateCompetitorsAccomClasses().remove(webrateCompetitorsAccomClass);
            });

            webrateShoppingCleanUpService.cleanupWebrateCompetitorChannelMappings(webrateOverrideCompetitorDetailsList);
            crudService.delete(webrateOverrideCompetitorDetailsList);
            crudService.delete(webrateOverrideCompetitorsToDelete);
            //Delete WebrateCompetitorsAccomClass entries to be deleted
            crudService.delete(webrateCompetitorsAccomClassToDelete);
        }
    }

    public void deleteWebrateOverrideCompetitors(List<AccomClass> accomClassList) {
        List<WebrateCompetitorsAccomClass> webrateCompetitorsAccomClassToDelete = crudService.findByNamedQuery(WebrateCompetitorsAccomClass.BY_ACCOM_CLASS,
                QueryParameter.with("accomClassList", accomClassList).parameters());

        List<Integer> webrateCompetitorsAccomClassIDsToDelete = webrateCompetitorsAccomClassToDelete.stream().map(WebrateCompetitorsAccomClass::getId).collect(Collectors.toList());
        List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetailsList = new ArrayList<>();
        List<WebrateOverrideCompetitor> webrateOverrideCompetitorsToDelete = new ArrayList<>();

        //Delete the WebrateOverrideCompetitorDetails entries associated with the WebrateCompetitorsAccomClass entries to be deleted
        if (CollectionUtils.isNotEmpty(webrateCompetitorsAccomClassIDsToDelete)) {
            webrateOverrideCompetitorDetailsList = crudService
                    .findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS,
                            QueryParameter.with("compAccomClassIds", webrateCompetitorsAccomClassIDsToDelete).parameters());

            //Delete the WebrateOverrideCompetitor entries associated with the WebrateOverrideCompetitorDetails entries to be deleted
            webrateOverrideCompetitorsToDelete = webrateOverrideCompetitorDetailsList.stream().map(WebrateOverrideCompetitorDetails::getWebrateOverrideCompetitor).collect(Collectors.toList());
        }

        webrateCompetitorsAccomClassToDelete.forEach(webrateCompetitorsAccomClass -> {
            WebrateCompetitors webrateCompetitor = webrateCompetitorsAccomClass.getWebrateCompetitor();
            webrateCompetitor.getWebrateCompetitorsAccomClasses().remove(webrateCompetitorsAccomClass);
        });

        webrateShoppingCleanUpService.cleanupWebrateCompetitorChannelMappingsBasedOnAccomClass(accomClassList);
        crudService.delete(webrateOverrideCompetitorDetailsList);
        crudService.delete(webrateOverrideCompetitorsToDelete);
        //Delete WebrateCompetitorsAccomClass entries to be deleted
        crudService.delete(webrateCompetitorsAccomClassToDelete);
    }

    @SuppressWarnings("unchecked")
    public void deleteInactiveWebrateAccomClassMapping(Integer propertyId) {

        List<AccomClass> inactiveAccomClassList = crudService.findByNamedQuery(AccomClass.ALL_INACTIVE_NON_DEFAULT,
                QueryParameter.with("propertyId", propertyId).parameters());
        List<Integer> inactiveAccomClassIds = inactiveAccomClassList.stream().map(AccomClass::getId).collect(Collectors.toList());

        List<Integer> webrateCompetitorAccomClassIds = new ArrayList<>();
        if (!inactiveAccomClassIds.isEmpty()) {
            webrateCompetitorAccomClassIds = crudService.findByNamedQuery(
                    WebrateCompetitorsAccomClass.BY_ACCOM_IDS, QueryParameter.with("accomIdList", inactiveAccomClassIds).parameters());
        }
        List<Integer> webrateOverrideCompetitorDetailsIds = new ArrayList<>();
        if (!webrateCompetitorAccomClassIds.isEmpty()) {
            webrateOverrideCompetitorDetailsIds = crudService
                    .findByNamedQuery(WebrateOverrideCompetitorDetails.IDS_BY_ACCOMMAPPING_IDS,
                            QueryParameter.with("compAccomClassIds", webrateCompetitorAccomClassIds).parameters());
        }

        webrateShoppingCleanUpService.cleanupWebrateCompetitorChannelMappingsBasedOnAccomClass(inactiveAccomClassList);
        deleteWebrateCompOverrideDetails(webrateOverrideCompetitorDetailsIds);
        deleteWebrateCompAccomMapping(webrateCompetitorAccomClassIds);
        if (!inactiveAccomClassIds.isEmpty()) {
            deleteWebrateAccomClassMapping(inactiveAccomClassIds);
            deleteWebrateRankingAccomClass(inactiveAccomClassIds);
            deleteWebrateRankingAccomClassOverrides(inactiveAccomClassIds);
        }
    }

    private void deleteWebrateCompAccomMapping(List<Integer> compAccomClassToDelete) {
        for (Integer mappingId : compAccomClassToDelete) {
            WebrateCompetitorsAccomClass compAccomClass = crudService.find(WebrateCompetitorsAccomClass.class, mappingId);
            if (compAccomClass != null) {
                WebrateCompetitors webrateCompetitors = compAccomClass.getWebrateCompetitor();
                webrateCompetitors.getWebrateCompetitorsAccomClasses().remove(compAccomClass);
                crudService.delete(WebrateCompetitorsAccomClass.class, compAccomClass.getId());
            }
        }
    }

    private void deleteWebrateCompOverrideDetails(List<Integer> overrideCompDetailsToDelete) {
        for (Integer overrideDetailsId : overrideCompDetailsToDelete) {
            WebrateOverrideCompetitorDetails overrideCompDetails = crudService.find(WebrateOverrideCompetitorDetails.class, overrideDetailsId);

            if (overrideCompDetails != null) {
                WebrateOverrideCompetitor webrateOverrideCompetitor = overrideCompDetails.getWebrateOverrideCompetitor();
                webrateOverrideCompetitor.getWebrateOverrideCompetitorDetails().remove(overrideCompDetails);
                crudService.delete(WebrateOverrideCompetitorDetails.class, overrideCompDetails.getId());
                crudService.delete(WebrateOverrideCompetitor.class, webrateOverrideCompetitor.getId());
            }
        }

    }

    private void deleteWebrateAccomClassMapping(List<Integer> accomClassIds) {
        crudService.executeUpdateByNamedQuery(WebrateAccomClassMapping.DELETE_BY_ACCOM_CLASS_IDS, QueryParameter.with("accomClassIds", accomClassIds).parameters());
    }

    private void deleteWebrateRankingAccomClass(List<Integer> accomClassIds) {
        crudService.executeUpdateByNamedQuery(WebrateRankingAccomClass.DELETE_BY_ACCOM_CLASS_IDS, QueryParameter.with("accomClassIds", accomClassIds).parameters());
    }

    public void deleteWebrateRankingAccomClass(Product product) {
        crudService.executeUpdateByNamedQuery(WebrateRankingAccomClass.DELETE_BY_PRODUCT,
                QueryParameter.with("productId", product.getId()).parameters());
    }

    public void deleteWebrateRankingAccomClassOverrides(Product product) {
        crudService.executeUpdateByNamedQuery(WebrateRankingAccomClassOverride.DELETE_BY_PRODUCT, QueryParameter.with("productId", product.getId()).parameters());
    }

    public void deleteWebrateCompetitorsAccomClass(Product product) {
        crudService.executeUpdateByNamedQuery(WebrateCompetitorsAccomClass.DELETE_BY_PRODUCT, QueryParameter.with("productId", product.getId()).parameters());
    }

    public void deleteWebrateOverrideCompetitor(Product product) {
        crudService.executeUpdateByNamedQuery(WebrateOverrideCompetitorDetails.DELETE_BY_PRODUCT, QueryParameter.with("productId", product.getId()).parameters());
        crudService.executeUpdateByNamedQuery(WebrateOverrideCompetitor.DELETE_BY_PRODUCT, QueryParameter.with("productId", product.getId()).parameters());
    }

    public void deleteWebrateDefaultChannel(Product product) {
        crudService.executeUpdateByNamedQuery(WebrateDefaultChannel.DELETE_BY_PRODUCT, QueryParameter.with("productId", product.getId()).parameters());
    }

    public void deleteWebRateDefaultChannelByPropertyId(Integer propertyId) {
        crudService.executeUpdateByNamedQuery(WebrateDefaultChannel.DELETE_BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
    }

    public void deleteWebrateOverrideChannel(Product product) {
        crudService.executeUpdateByNamedQuery(WebrateOverrideChannel.DELETE_BY_PRODUCT, QueryParameter.with("productId", product.getId()).parameters());
    }

    public void copyWebrateOverrideChannelToIndependentProducts(List<Integer> productIDs) {
        List<WebrateOverrideChannel> barWebrateOverrides = crudService.findByNamedQuery(WebrateOverrideChannel.BY_PROPERTY_ID_FOR_BAR_ONLY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        List<WebrateOverrideChannel> toBeSavedChannels = new ArrayList<>();
        barWebrateOverrides.forEach(
                ovr -> toBeSavedChannels.addAll(getWebrateOverrideChannel(productIDs, ovr))
        );
        crudService.save(toBeSavedChannels);
    }

    private List<WebrateOverrideChannel> getWebrateOverrideChannel(List<Integer> productIDs, WebrateOverrideChannel ovr) {
        return productIDs.stream().map(pr -> getChannelOverride(ovr, pr)).collect(Collectors.toList());
    }

    private static WebrateOverrideChannel getChannelOverride(WebrateOverrideChannel ovr, Integer productID) {
        WebrateOverrideChannel webrateChannel = new WebrateOverrideChannel();
        webrateChannel.setProductID(productID);
        webrateChannel.setPropertyId(ovr.getPropertyId());
        webrateChannel.setWebrateOverrideName(ovr.getWebrateOverrideName());
        webrateChannel.setChannelOverrideStartDT(ovr.getChannelOverrideStartDT());
        webrateChannel.setChannelOverrideEndDT(ovr.getChannelOverrideEndDT());
        webrateChannel.setWebrateChannelMon(ovr.getWebrateChannelMon());
        webrateChannel.setWebrateChannelTues(ovr.getWebrateChannelTues());
        webrateChannel.setWebrateChannelWed(ovr.getWebrateChannelWed());
        webrateChannel.setWebrateChannelThurs(ovr.getWebrateChannelThurs());
        webrateChannel.setWebrateChannelFri(ovr.getWebrateChannelFri());
        webrateChannel.setWebrateChannelSat(ovr.getWebrateChannelSat());
        webrateChannel.setWebrateChannelSun(ovr.getWebrateChannelSun());

        return webrateChannel;
    }

    public void enableDemandBasedOnBARForIndependentProducts(List<Integer> productList) {
        List<WebrateCompetitorsAccomClass> webrateCompetitorsAccomClasses = crudService.findAll(WebrateCompetitorsAccomClass.class);

        List<WebrateCompetitorsAccomClass> competitorsAccomClassesOfBAR = webrateCompetitorsAccomClasses.stream()
                .filter(wcac -> wcac.getProductID() == 1).collect(Collectors.toList());

        webrateCompetitorsAccomClasses.stream().filter(wcac -> wcac.getProductID() != 1
                && productList.stream().anyMatch(pr -> Objects.equals(pr, wcac.getProductID()))).forEach(
                wec -> {
                    Integer isDemandEnabled = getDemandEnabledForBAR(wec.getWebrateCompetitor().getId(), wec.getAccomClass().getId(), competitorsAccomClassesOfBAR);
                    if (isDemandEnabled != null) {
                        wec.setDemandEnabled(isDemandEnabled);
                    }
                }
        );
    }

    public Integer getDemandEnabledForBAR(Integer webrateCompetitorId, Integer accomClassId, List<WebrateCompetitorsAccomClass> demandMappingForBAR) {
        return demandMappingForBAR.stream()
                .filter(wcac -> wcac.getWebrateCompetitor().getId().equals(webrateCompetitorId) && wcac.getAccomClass().getId().equals(accomClassId))
                .map(WebrateCompetitorsAccomClass::getDemandEnabled)
                .findFirst()
                .orElse(null);

    }

    private static Map<WebrateCompetitors, Integer> getWebrateCompetitorsDemandMap(List<WebrateCompetitorsAccomClass> webrateCompetitorsAccomClasses) {
        return webrateCompetitorsAccomClasses.stream().filter(wcac -> wcac.getProductID() == 1)
                .collect(Collectors.toMap(WebrateCompetitorsAccomClass::getWebrateCompetitor, WebrateCompetitorsAccomClass::getDemandEnabled));
    }

    private void deleteWebrateRankingAccomClassOverrides(List<Integer> accomClassIds) {
        crudService.executeUpdateByNamedQuery(WebrateRankingAccomClassOverride.DELETE_BY_ACCOMCLASS_IDS, QueryParameter.with("accomClassIds", accomClassIds).parameters());
    }

    private void createWebrateCompAccomMapping(Product product, List<AccomClass> accomClassesForProduct, List<WebrateCompetitorsAccomClass> webrateCompetitorsAccomClassesForProduct, List<WebrateCompetitors> webrateCompetitorsList) {
        String webRateHotelId = configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());

        List<WebrateCompetitors> webrateCompetitorsToSave = new ArrayList<>();
        for (WebrateCompetitors webrateCompetitor : webrateCompetitorsList) {
            List<AccomClass> accomClassesToAdd = new ArrayList<>();
            List<WebrateCompetitorsAccomClass> existingWebrateCompetitorsAccomClassForProduct = webrateCompetitorsAccomClassesForProduct.stream().filter(webrateCompetitorsAccomClass -> webrateCompetitorsAccomClass.getWebrateCompetitor().equals(webrateCompetitor)).collect(Collectors.toList());
            List<AccomClass> existingAccomClassesForProduct = existingWebrateCompetitorsAccomClassForProduct.stream().map(WebrateCompetitorsAccomClass::getAccomClass).collect(Collectors.toList());
            accomClassesForProduct.stream().forEach(accomClass -> {
                if (!existingAccomClassesForProduct.contains(accomClass)) {
                    accomClassesToAdd.add(accomClass);
                }
            });

            if (CollectionUtils.isNotEmpty(accomClassesToAdd)) {
                webrateCompetitorsToSave.add(webrateCompetitor);
                updateCompetitorWithNewAccomClasses(webRateHotelId, accomClassesToAdd, webrateCompetitor, product);
                updateCompetitorStatus(webrateCompetitor);
            }
        }

        if (CollectionUtils.isNotEmpty(webrateCompetitorsToSave)) {
            crudService.save(webrateCompetitorsToSave);
        }
    }

    public void createWebrateCompAccomMappings(List<WebrateCompetitorsAccomClass> allWebrateCompetitorsAccomClass, WebrateCompetitors webrateCompetitor, List<AccomClass> accomClasses) {
        String webRateHotelId = configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        List<Product> allProducts = agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts();

        List<ProductAccomType> productAccomTypes = new ArrayList<>();
        if (!isIndependentProductsEnabled) {
            //If independent products is disabled; filter list to BAR product only
            allProducts = allProducts.stream().filter(product -> product.isSystemDefault()).collect(Collectors.toList());
        } else {
            productAccomTypes = crudService.findAll(ProductAccomType.class);
        }

        for (Product product : allProducts) {
            List<AccomClass> accomClassesForProduct = findRoomClassesByProduct(product, accomClasses, productAccomTypes);
            List<WebrateCompetitorsAccomClass> webrateCompetitorsAccomClassesForProduct = allWebrateCompetitorsAccomClass.stream().filter(wcac -> wcac.getProductID().equals(product.getId())).collect(Collectors.toList());

            List<AccomClass> accomClassesToAdd = new ArrayList<>();
            List<WebrateCompetitorsAccomClass> existingWebrateCompetitorsAccomClassForProduct = webrateCompetitorsAccomClassesForProduct.stream().filter(webrateCompetitorsAccomClass -> webrateCompetitorsAccomClass.getWebrateCompetitor().equals(webrateCompetitor)).collect(Collectors.toList());
            List<AccomClass> existingAccomClassesForProduct = existingWebrateCompetitorsAccomClassForProduct.stream().map(WebrateCompetitorsAccomClass::getAccomClass).collect(Collectors.toList());
            accomClassesForProduct.stream().forEach(accomClass -> {
                if (!existingAccomClassesForProduct.contains(accomClass)) {
                    accomClassesToAdd.add(accomClass);
                }
            });

            if (CollectionUtils.isNotEmpty(accomClassesToAdd)) {
                updateCompetitorWithNewAccomClasses(webRateHotelId, accomClassesToAdd, webrateCompetitor, product);
            }
        }
        crudService.save(webrateCompetitor);
    }

    public void createWebrateCompAccomMappingsForRDL(List<WebrateCompetitorsAccomClass> allWebrateCompetitorsAccomClass, WebrateCompetitors webrateCompetitor, List<AccomClass> accomClasses) {

        List<Product> allProducts = agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts();
        List<ProductAccomType> productAccomTypes = new ArrayList<>();
        String webRateHotelId = configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());

        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        boolean isRDLEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);

        if (!(isIndependentProductsEnabled || isRDLEnabled)) {
            allProducts = allProducts.stream().filter(Product::isSystemDefault).collect(Collectors.toList());
        } else {
            productAccomTypes = crudService.findAll(ProductAccomType.class);
        }

        for (Product product : allProducts) {
            List<WebrateCompetitorsAccomClass> webrateCompetitorsAccomClassesForProduct = allWebrateCompetitorsAccomClass.stream().filter(wcac -> wcac.getProductID().equals(product.getId())).collect(Collectors.toList());

            List<WebrateCompetitorsAccomClass> existingWebrateCompetitorsAccomClassForProduct = webrateCompetitorsAccomClassesForProduct.stream().filter(webrateCompetitorsAccomClass -> webrateCompetitorsAccomClass.getWebrateCompetitor().equals(webrateCompetitor)).collect(Collectors.toList());
            List<AccomClass> existingAccomClassesForProduct = existingWebrateCompetitorsAccomClassForProduct.stream().map(WebrateCompetitorsAccomClass::getAccomClass).collect(Collectors.toList());
            List<AccomClass> accomClassesToAdd = new ArrayList<>();
            findRoomClassesByProduct(product, accomClasses, productAccomTypes).forEach(accomClass -> {
                if (!existingAccomClassesForProduct.contains(accomClass)) {
                    accomClassesToAdd.add(accomClass);
                }
            });

            if (CollectionUtils.isNotEmpty(accomClassesToAdd)) {
                updateCompetitorWithNewAccomClasses(webRateHotelId, accomClassesToAdd, webrateCompetitor, product);
            }
        }
        crudService.save(webrateCompetitor);
    }

    public void updateCompetitorWithNewAccomClasses(final String webRateHotelId, final List<AccomClass> accomClassesToAdd, WebrateCompetitors webrateCompetitor, Product product) {
        Property property = propertyService.getPropertyById(PacmanWorkContextHelper.getPropertyId());
        for (AccomClass accomClass : accomClassesToAdd) {
            if (accomClass.getSystemDefault() != 1) {
                WebrateCompetitorsAccomClass newCompetitorMapping = new WebrateCompetitorsAccomClass();
                newCompetitorMapping.setProductID(product.getId());
                newCompetitorMapping.setAccomClass(accomClass);
                newCompetitorMapping.setWebrateCompetitor(webrateCompetitor);
                Set<WebrateCompetitorsAccomClass> accomClasses = webrateCompetitor.getWebrateCompetitorsAccomClasses() == null ? new HashSet<>() : webrateCompetitor.getWebrateCompetitorsAccomClasses();
                if ((StringUtils.isNotEmpty(webRateHotelId) && webRateHotelId.equalsIgnoreCase(webrateCompetitor.getWebrateHotelID())) || isRDLSelfProperty(webrateCompetitor, property)) {
                    newCompetitorMapping.setDemandEnabled(ACTIVE_STATUS_ID);
                    newCompetitorMapping.setRankingEnabled(ACTIVE_STATUS_ID);
                    webrateCompetitor.setStatusId(ACTIVE_STATUS_ID);
                } else {
                    newCompetitorMapping.setDemandEnabled(ZERO);
                    newCompetitorMapping.setRankingEnabled(ZERO);
                }
                accomClasses.add(newCompetitorMapping);
                webrateCompetitor.setWebrateCompetitorsAccomClasses(accomClasses);
            }
        }
    }

    private boolean isRDLSelfProperty(WebrateCompetitors webrateCompetitor, Property property) {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED) &&
                StringUtils.isNotEmpty(property.getUpsId()) && property.getUpsId().equals(webrateCompetitor.getUpsId());
    }

    public void updateCompetitorStatus(WebrateCompetitors webrateCompetitor) {
        Boolean isAnyChildEnabled = false;
        if (webrateCompetitor.getWebrateCompetitorsAccomClasses() != null) {
            isAnyChildEnabled = webrateCompetitor.getWebrateCompetitorsAccomClasses().stream().anyMatch(accomClass -> accomClass.getDemandEnabled() == 1);
        }
        if(!Objects.equals(webrateCompetitor.getStatusId(), NEW_STATUS_ID)){
            webrateCompetitor.setStatusId(isAnyChildEnabled ? ACTIVE_STATUS_ID : INACTIVE_STATUS_ID);
        }
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setAccommodationService(AccommodationService accommodation2Service) {
        this.accommodation2Service = accommodation2Service;
    }

    public void setSyncEventAggregatorService(SyncEventAggregatorService syncEventAggregatorService) {
        this.syncEventAggregatorService = syncEventAggregatorService;
    }

    public void setWebrateShoppingCleanUpService(WebrateShoppingCleanUpService webrateShoppingCleanUpService) {
        this.webrateShoppingCleanUpService = webrateShoppingCleanUpService;
    }

    public List<WebrateAccomTypeAverageRateDTO> getAverageRatesForWebrateAccomTypes() {
        LocalDate caughtUpDate = dateService.getCaughtUpJavaLocalDate();
        List<Object[]> results = crudService.findByNamedQuery(Webrate.GET_AVG_RATE_FOR_ACCOM_TYPE_AND_DATE_RANGE, QueryParameter
                .with("webrateStatus", WEBRATE_STATUS_AVAILABLE)
                .and("lengthOfStay", 1)
                .and("startDate", caughtUpDate.minusYears(SUGGESTION_DATA_WINDOW_IN_YEARS).toString())
                .and("endDate", caughtUpDate.toString())
                .parameters());
        return Optional.ofNullable(results)
                .orElse(Collections.emptyList()).stream()
                .map(WebrateAccomTypeAverageRateDTO::new)
                .collect(Collectors.toList());
    }

    public List<WebrateAccomTypeAverageRateDTO> getADRByAccomClasses() {
        LocalDate caughtUpDate = dateService.getCaughtUpJavaLocalDate();
        List<Object[]> results = crudService.findByNamedQuery(MktSegAccomActivity.GET_ADR_BY_ACCOM_CLASS_FOR_DATE_RANGE_AND_MKT_SEGS, QueryParameter
                .with("startDate", caughtUpDate.minusYears(SUGGESTION_DATA_WINDOW_IN_YEARS))
                .and("endDate", caughtUpDate)
                .and("mktSegsIds", getStraightBarMarketSegments())
                .parameters());
        return Optional.ofNullable(results)
                .orElse(Collections.emptyList()).stream()
                .map(WebrateAccomTypeAverageRateDTO::new)
                .collect(Collectors.toList());
    }

    public List<Integer> getStraightBarMarketSegments() {
        List<Integer> straightBarMktSegIds = crudService.findByNamedQuery(MktSegDetails.GET_STRAIGHT_BAR_MS_IDS);
        if (straightBarMktSegIds.isEmpty()) {
            return crudService.findByNamedQuery(MktSegDetailsProposed.GET_STRAIGHT_BAR_MS_IDS);
        }
        return straightBarMktSegIds;
    }

    public boolean straightBarMarketSegmentsArePresent() {
        return CollectionUtils.isNotEmpty(getStraightBarMarketSegments());
    }

    public void mapWebrateAccomTypesToAccomClasses(List<WebrateAccomType> webrateAccomTypes, List<AccomClass> accomClasses) {
        List<WebrateAccomTypeAverageRateDTO> webrateAccomTypesAverageRates = getAverageRatesForWebrateAccomTypes();
        List<WebrateAccomTypeAverageRateDTO> accomClassesADRList = getADRByAccomClasses();

        Optional<BigDecimal> minimumAverageRate = getMinimumAverageRate(accomClassesADRList);
        Optional<BigDecimal> maximumAverageRate = getMaximumAverageRate(accomClassesADRList);

        if (minimumAverageRate.isPresent() && maximumAverageRate.isPresent()) {
            webrateAccomTypes.forEach(webrateAccomType -> {
                Optional<BigDecimal> averageRate = webrateAccomTypesAverageRates.stream()
                        .filter(dto -> dto.getWebrateAccomTypeId().equals(webrateAccomType.getId()))
                        .map(WebrateAccomTypeAverageRateDTO::getAverageRate)
                        .findFirst();
                if (averageRate.isPresent() && averageRateIsWithinRange(averageRate.get(), minimumAverageRate.get(), maximumAverageRate.get())) {
                    Optional<Integer> mappedClassId = accomClassesADRList.stream()
                            .min(Comparator.comparing(dto -> averageRate.get().subtract(dto.getAverageRate()).abs()))
                            .map(WebrateAccomTypeAverageRateDTO::getWebrateAccomTypeId);
                    if (mappedClassId.isPresent()) {
                        WebrateAccomClassMapping webrateAccomClassMapping = new WebrateAccomClassMapping();
                        webrateAccomClassMapping.setWebrateAccomType(webrateAccomType);
                        webrateAccomClassMapping.setAccomClass(accomClasses.stream()
                                .filter(accomClass -> accomClass.getId().equals(mappedClassId.get()))
                                .findFirst().orElse(null));
                        webrateAccomType.setWebrateAccomClassMappings(Stream.of(webrateAccomClassMapping).collect(Collectors.toCollection(HashSet::new)));
                    }
                }
            });
        }
    }

    protected Optional<BigDecimal> getMinimumAverageRate(List<WebrateAccomTypeAverageRateDTO> accomClassesADRList) {
        Optional<BigDecimal> minimumADR = accomClassesADRList.stream()
                .min(Comparator.comparing(WebrateAccomTypeAverageRateDTO::getAverageRate))
                .map(WebrateAccomTypeAverageRateDTO::getAverageRate);
        return minimumADR.map(rate -> rate.subtract(rate
                .multiply(ACCEPTABLE_RATE_WINDOW_PERCENTAGE)
                .divide(BigDecimalUtil.ONE_HUNDRED, RoundingMode.HALF_UP)));
    }

    protected Optional<BigDecimal> getMaximumAverageRate(List<WebrateAccomTypeAverageRateDTO> accomClassesADRList) {
        Optional<BigDecimal> minimumADR = accomClassesADRList.stream()
                .max(Comparator.comparing(WebrateAccomTypeAverageRateDTO::getAverageRate))
                .map(WebrateAccomTypeAverageRateDTO::getAverageRate);
        return minimumADR.map(rate -> rate.add(rate
                .multiply(ACCEPTABLE_RATE_WINDOW_PERCENTAGE)
                .divide(BigDecimalUtil.ONE_HUNDRED, RoundingMode.HALF_UP)));
    }

    protected boolean averageRateIsWithinRange(BigDecimal averageRate, BigDecimal minRate, BigDecimal maxRate) {
        return (BigDecimalUtil.isGreaterThan(averageRate, minRate) && BigDecimalUtil.isLessThan(averageRate, maxRate))
                || BigDecimalUtil.equals(averageRate, minRate)
                || BigDecimalUtil.equals(averageRate, maxRate);
    }

    public boolean webrateDefaultChannelIsNotConfigured() {
        WebrateDefaultChannel webrateDefaultChannel = crudService.findByNamedQuerySingleResult(WebrateDefaultChannel.BY_PROPERTY_ID_FOR_BAR_ONLY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        return webrateDefaultChannel == null;
    }

    public void createDefaultChannelForProduct(Product product) {
        createDefaultChannelForProducts(Arrays.asList(product));
    }

    public void createDefaultChannelForAllProducts() {
        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        List<Product> allProducts = agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts();
        if (!isIndependentProductsEnabled) {
            //If independent products is disabled; filter list to BAR product only
            allProducts = allProducts.stream().filter(product -> product.isSystemDefault()).collect(Collectors.toList());
        }
        createDefaultChannelForProducts(allProducts);
    }

    @SuppressWarnings({"squid:S3776"})
    protected void createDefaultChannelForProducts(List<Product> allProducts) {
        List<WebrateDefaultChannel> allWebrateDefaultChannel = crudService.findByNamedQuery(WebrateDefaultChannel.ALL_CHANNELS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        WebrateDefaultChannel barWebrateDefaultChannel = allWebrateDefaultChannel.stream().filter(webrateDefaultChannel -> webrateDefaultChannel.getProductID().equals(1)).findFirst().orElse(null);
        List<WebrateChannel> webrateChannelsList = getWebRateChannelsList();
        WebrateChannel defaultChannel = CollectionUtils.isNotEmpty(webrateChannelsList) ? webrateChannelsList.get(0) : null;

        List<WebrateDefaultChannel> defaultChannelsToAdd = new ArrayList<>();
        if (null != defaultChannel) {
            for (Product product : allProducts) {
                WebrateDefaultChannel defaultWebrateChannel = allWebrateDefaultChannel.stream().filter(webrateDefaultChannel -> webrateDefaultChannel.getProductID().equals(product.getId())).findFirst().orElse(null);
                if (null == defaultWebrateChannel) {
                    WebrateDefaultChannel webrateDefaultChannel = getWebrateDefaultChannel(barWebrateDefaultChannel, defaultChannel, product);
                    defaultChannelsToAdd.add(webrateDefaultChannel);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(defaultChannelsToAdd)) {
            defaultReferenceChannelService.saveDefaultChannels(defaultChannelsToAdd);
        }
    }

    public WebrateDefaultChannel getWebrateDefaultChannel(WebrateDefaultChannel barWebrateDefaultChannel, WebrateChannel defaultChannel, Product product) {
        WebrateDefaultChannel webrateDefaultChannel = new WebrateDefaultChannel();
        webrateDefaultChannel.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        webrateDefaultChannel.setProductID(product.getId());
        webrateDefaultChannel.setWebrateChannelMon(barWebrateDefaultChannel != null ? barWebrateDefaultChannel.getWebrateChannelMon() : defaultChannel);
        webrateDefaultChannel.setWebrateChannelTues(barWebrateDefaultChannel != null ? barWebrateDefaultChannel.getWebrateChannelTues() : defaultChannel);
        webrateDefaultChannel.setWebrateChannelWed(barWebrateDefaultChannel != null ? barWebrateDefaultChannel.getWebrateChannelWed() : defaultChannel);
        webrateDefaultChannel.setWebrateChannelThurs(barWebrateDefaultChannel != null ? barWebrateDefaultChannel.getWebrateChannelThurs() : defaultChannel);
        webrateDefaultChannel.setWebrateChannelFri(barWebrateDefaultChannel != null ? barWebrateDefaultChannel.getWebrateChannelFri() : defaultChannel);
        webrateDefaultChannel.setWebrateChannelSat(barWebrateDefaultChannel != null ? barWebrateDefaultChannel.getWebrateChannelSat() : defaultChannel);
        webrateDefaultChannel.setWebrateChannelSun(barWebrateDefaultChannel != null ? barWebrateDefaultChannel.getWebrateChannelSun() : defaultChannel);
        return webrateDefaultChannel;
    }

    public List<WebrateChannel> getWebRateChannelsList() {
        return crudService.findByNamedQuery(WebrateChannel.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void autoConfigureRateShopping() {
        autoConfigureWebrateAccomClassMapping();
        autoConfigureDefaultChannel();
        autoConfigureCompetitorSettings();
    }

    public void autoConfigureRoomClassMapping() {
        deleteExistingWebrateAccomClassMappings();
        createNewWebrateAccomClassMappings();
        deleteExistingNewCompetitiveRoomTypeAlert();
    }

    private void deleteExistingNewCompetitiveRoomTypeAlert() {
        alertService.resolveAllAlerts(AlertType.NewCompetitiveRoomTypeFound, PacmanWorkContextHelper.getPropertyId());
    }

    private void createNewWebrateAccomClassMappings() {
        List<WebrateAccomType> webrateAccomTypes = getAccomodationMappingByProperty();
        Map<String, AccomClass> accomClassByNameMap = getAccomClasses().stream()
                .collect(Collectors.toMap(accomClass -> accomClass.getName().toLowerCase(),
                        Function.identity(), (existingAccomClass, newAccomClass) -> newAccomClass));
        AccomClass unassigned = accomClassByNameMap.get("unassigned");
        List<WebrateAccomClassMapping> webrateAccomClassMappings = new ArrayList<>();
        webrateAccomTypes.forEach(webrateAccomType -> {
            AccomClass accomClass = accomClassByNameMap.get(webrateAccomType.getWebrateAccomName().toLowerCase());
            WebrateAccomClassMapping webrateAccomClassMapping = new WebrateAccomClassMapping();
            webrateAccomClassMapping.setWebrateAccomType(webrateAccomType);
            webrateAccomClassMapping.setAccomClass(null != accomClass ? accomClass : unassigned);
            webrateAccomClassMappings.add(webrateAccomClassMapping);
        });
        crudService.saveWithFlushAndClear(webrateAccomClassMappings, webrateAccomClassMappings.size());
        createCompetitorAccomClassMapping();
    }

    private void createCompetitorAccomClassMapping() {
        List<WebrateCompetitors> webrateCompetitors = crudService.findAll(WebrateCompetitors.class);
        for (WebrateCompetitors webrateCompetitor : webrateCompetitors) {
            List<WebrateAccomClassMapping> allWebrateAccomClassMappings = crudService.findAll(WebrateAccomClassMapping.class);
            List<WebrateCompetitorsAccomClass> allWebrateCompetitorsAccomClass = crudService.findAll(WebrateCompetitorsAccomClass.class);
            Set<AccomClass> accomClasses = allWebrateAccomClassMappings.stream().map(WebrateAccomClassMapping::getAccomClass).filter(Objects::nonNull).collect(Collectors.toSet());
            createWebrateCompAccomMappingsForRDL(allWebrateCompetitorsAccomClass, webrateCompetitor, new ArrayList<>(accomClasses));
        }
    }

    private void deleteExistingWebrateAccomClassMappings() {
        crudService.deleteAll(WebrateAccomClassMapping.class);
    }

    public void autoConfigureWebrateAccomClassMapping() {
        List<WebrateAccomType> webrateAccomTypes = getAccomodationMappingByProperty();
        mapWebrateAccomTypesToAccomClasses(webrateAccomTypes, getAccomClasses());
        saveAccomMapping(webrateAccomTypes, null, PacmanWorkContextHelper.getPropertyId(), null);
    }

    protected List<AccomClass> getAccomClasses() {
        List<AccomClass> accomClasses = accommodation2Service.getActiveNonDefaultAccomClasses();
        accomClasses = Optional.ofNullable(accomClasses).orElseGet(Collections::emptyList)
                .stream().filter(ac -> null != ac.getAccomTypes() && !ac.hasZeroCapacity()).collect(Collectors.toList());
        accomClasses.add(accommodation2Service.findUnassignedAccomClass(PacmanWorkContextHelper.getPropertyId()));
        return accomClasses;
    }

    public void autoConfigureDefaultChannel() {
        webrateShoppingDataService.updateAllNewStatusToActive(WebrateChannel.UPDATE_ALL_NEW_TO_ACTIVE_BY_PROPERTY);
        List<WebrateChannel> webrateChannels = webrateShoppingDataService.getAllChannelsByProperty();
        if (!webrateChannels.isEmpty()) {
            boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
            List<Product> allProducts = agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts();
            if (!isIndependentProductsEnabled) {
                //If independent products is disabled; filter list to BAR product only
                allProducts = allProducts.stream().filter(product -> product.isSystemDefault()).collect(Collectors.toList());
            }

            List<WebrateDefaultChannel> webrateDefaultChannels = new ArrayList<>();
            for (Product product : allProducts) {
                WebrateDefaultChannel webrateDefaultChannel = new WebrateDefaultChannel();
                webrateDefaultChannel.setPropertyId(PacmanWorkContextHelper.getPropertyId());
                webrateDefaultChannel.setProductID(product.getId());
                webrateDefaultChannel.setWebrateChannelMon(webrateChannels.get(0));
                webrateDefaultChannel.setWebrateChannelTues(webrateChannels.get(0));
                webrateDefaultChannel.setWebrateChannelWed(webrateChannels.get(0));
                webrateDefaultChannel.setWebrateChannelThurs(webrateChannels.get(0));
                webrateDefaultChannel.setWebrateChannelFri(webrateChannels.get(0));
                webrateDefaultChannel.setWebrateChannelSat(webrateChannels.get(0));
                webrateDefaultChannel.setWebrateChannelSun(webrateChannels.get(0));
                webrateDefaultChannels.add(webrateDefaultChannel);
            }
            defaultReferenceChannelService.saveDefaultChannels(webrateDefaultChannels);
        }
    }

    public void autoConfigureCompetitorSettings() {
        setCompetitorPricesToMedianPriceOrder();
        setRateShoppingAdjustmentForDefaultTax();
        updateNewCompetitorStatusToActive();
    }

    private void setCompetitorPricesToMedianPriceOrder() {
        configParamsService.updateParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value(), BAR_OVRD_DISPLAY_COMPETITOR_VALUE_MEDIAN);
        configParamsService.updateParameterValue(IPConfigParamName.BAR_BAR_OVRD_ABSOLUTE_COMPETITOR.value(), "");
    }

    protected void setRateShoppingAdjustmentForDefaultTax() {
        if (isContinuousPricingEnabled()) {
            Tax defaultTax = taxService.findTax();
            if (defaultTax != null && BigDecimalUtil.isGreaterThan(defaultTax.getRoomTaxRate(), BigDecimal.ZERO)) {
                RateShoppingAdjustment taxAdjustment = new RateShoppingAdjustment();
                taxAdjustment.setPropertyId(PacmanWorkContextHelper.getPropertyId());
                taxAdjustment.setOtherOffsetValue(null);
                taxAdjustment.setOtherTaxValueType(ValueTypes.PERCENTAGE);
                taxAdjustment.setOtherOperationType(OperationTypes.DEDUCT);

                taxAdjustment.setTaxOffsetValue(defaultTax.getRoomTaxRate().negate());
                taxAdjustment.setTaxValueType(ValueTypes.PERCENTAGE);
                taxAdjustment.setTaxOperationType(OperationTypes.DEDUCT);
                crudService.save(taxAdjustment);
            }
        }
    }

    public void updateNewCompetitorStatusToActive() {
        List<WebrateCompetitors> webrateCompetitors = crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        webrateCompetitors.forEach(webrateCompetitor -> {
            webrateCompetitor.setStatusId(ACTIVE_STATUS_ID);
            webrateCompetitor.getWebrateCompetitorsAccomClasses().forEach(accomClass -> accomClass.setDemandEnabled(ACTIVE_STATUS_ID));
        });
        crudService.save(webrateCompetitors);
        alertService.resolveAllAlerts(AlertType.NewWebRateCompetitorFound, PacmanWorkContextHelper.getPropertyId());
    }

    public boolean rateShoppingConfigurationPrerequisiteDataIsNotPresent() {
        return webrateDataIsNotPresent() || channelDataIsNotPresent() || competitorDataIsNotPresent();
    }

    private boolean competitorDataIsNotPresent() {
        return crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()).isEmpty();
    }

    private boolean channelDataIsNotPresent() {
        return webrateShoppingDataService.getAllChannelsByProperty().isEmpty();
    }

    private boolean webrateDataIsNotPresent() {
        return getAccomodationMappingByProperty().isEmpty();
    }

    private boolean isContinuousPricingEnabled() {
        return configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
    }

    public boolean allAccomTypesAreMapped() {
        List<AccomType> unmappedAccomTypes = crudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        return unmappedAccomTypes == null || unmappedAccomTypes.isEmpty();
    }

    public List<AccomClass> getMappedAccomClasses(Integer propertyId) {
        return multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId,
                WebrateAccomClassMapping.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());
    }

    public AccomClass getAccomClassById(Integer propertyId, Integer accomClassId) {
        return (AccomClass) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId,
                AccomClass.GET_BY_PROPERTY_ID_AND_ACCOM_CLASS_ID,
                QueryParameter.with("propertyId", propertyId).and("accomClassId", accomClassId).parameters());
    }

    public int getCountByAccomClassIdAndProductIdForActiveRanking(int accomClassId, int productId) {
        Long count = crudService.findByNamedQuerySingleResult(WebrateCompetitorsAccomClass.COUNT_ACCOM_CLASS_ID_AND_PRODUCT_ACTIVE_RANKING,
                QueryParameter.with("accomClassId", accomClassId)
                        .and("productId", productId).parameters());
        return null != count ? count.intValue() : 0;
    }

    public int getMaxWebrateRankingForDefaultConfig(List<WebrateRankingAccomClass> webrateRankingAccomClassList) {
        if (CollectionUtils.isNotEmpty(webrateRankingAccomClassList)) {
            List<Integer> ranking = new ArrayList<>();
            webrateRankingAccomClassList.forEach(webrateRankingAccomClass -> {
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClass.getWebrateRankingSunday().getWebrateRankingName()));
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClass.getWebrateRankingMonday().getWebrateRankingName()));
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClass.getWebrateRankingTuesday().getWebrateRankingName()));
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClass.getWebrateRankingWednesday().getWebrateRankingName()));
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClass.getWebrateRankingThursday().getWebrateRankingName()));
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClass.getWebrateRankingFriday().getWebrateRankingName()));
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClass.getWebrateRankingSaturday().getWebrateRankingName()));
            });
            return Collections.max(ranking);
        }
        return 0;
    }

    public int getMaxWebrateRankingForSeasonConfig(List<WebrateRankingAccomClassOverride> webrateRankingAccomClassOverrideList) {
        if (CollectionUtils.isNotEmpty(webrateRankingAccomClassOverrideList)) {
            List<Integer> ranking = new ArrayList<>();
            webrateRankingAccomClassOverrideList.forEach(webrateRankingAccomClassOverride -> {
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClassOverride.getWebrateRankingOvrSunday().getWebrateRankingName()));
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClassOverride.getWebrateRankingOvrMonday().getWebrateRankingName()));
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClassOverride.getWebrateRankingOvrTuesday().getWebrateRankingName()));
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClassOverride.getWebrateRankingOvrWednesday().getWebrateRankingName()));
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClassOverride.getWebrateRankingOvrThursday().getWebrateRankingName()));
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClassOverride.getWebrateRankingOvrFriday().getWebrateRankingName()));
                ranking.add(CompetitiveMarketPositionConstraintEnum.forWebrateRanking(webrateRankingAccomClassOverride.getWebrateRankingOvrSaturday().getWebrateRankingName()));
            });
            return Collections.max(ranking);
        }
        return 0;
    }

    public List<WebrateRankingAccomClassOverride> getWebrateRankingAccomClassOverrideList(int accomClassId, int productId) {
        List<WebrateRankingAccomClassOverride> webrateRankingAccomClsOvrSet = crudService.findByNamedQuery(WebrateRankingAccomClassOverride.BY_ACCOMCLASS_ID_PRODUCT_ID,
                QueryParameter.with("accomId", accomClassId)
                        .and("productId", productId).parameters());

        return webrateRankingAccomClsOvrSet;
    }

    public int getEffectiveCompetitorsCount(int accomClassId, int productId, Date systemDate, Date startDate, Date endDate, List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetailsList) {
        int rankingCount = getCountByAccomClassIdAndProductIdForActiveRanking(accomClassId, productId);
        AtomicInteger counter = new AtomicInteger();
        List<WebrateOverrideCompetitor> webrateOverrideCompetitorListByProductId = getWebrateOverrideCompetitor(webrateOverrideCompetitorDetailsList, accomClassId);
        if (!webrateOverrideCompetitorListByProductId.isEmpty()) {
            webrateOverrideCompetitorListByProductId.forEach(webrateOverrideCompetitor -> {
                boolean systemDateWithinIgnoreConfigDates = DateUtil.isDateBetween(startDate, endDate, systemDate);
                Date ovrStartDate = webrateOverrideCompetitor.getCompetitorOverrideStartDT();
                Date ovrEndDate = webrateOverrideCompetitor.getCompetitorOverrideEndDT();
                if (systemDateWithinIgnoreConfigDates
                        && ((startDate.equals(ovrEndDate) || startDate.before(ovrEndDate))
                        && (endDate.equals(ovrStartDate) || endDate.after(ovrStartDate)))) {
                    counter.getAndIncrement();
                }
            });
        }
        return rankingCount - counter.get();
    }

    public int getPresentAndFutureEffectiveCompetitorsCount(int accomClassId, int productId, Date systemDate, List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetailsList) {
        int rankingCount = getCountByAccomClassIdAndProductIdForActiveRanking(accomClassId, productId);
        AtomicInteger counter = new AtomicInteger();
        List<WebrateOverrideCompetitor> webrateOverrideCompetitorListByProductId = getWebrateOverrideCompetitor(webrateOverrideCompetitorDetailsList, accomClassId);
        if (!webrateOverrideCompetitorListByProductId.isEmpty()) {
            webrateOverrideCompetitorListByProductId.forEach(webrateOverrideCompetitor -> {
                Date ovrStartDate = webrateOverrideCompetitor.getCompetitorOverrideStartDT();
                Date ovrEndDate = webrateOverrideCompetitor.getCompetitorOverrideEndDT();
                boolean systemDateWithinIgnoreConfigDates = DateUtil.isDateBetween(ovrStartDate, ovrEndDate, systemDate);
                if (systemDateWithinIgnoreConfigDates || ovrStartDate.after(systemDate)) {
                    counter.getAndIncrement();
                }
            });
        }
        return rankingCount - counter.get();
    }

    public int getEffectiveCompetitorsCountForSeasonConfig(WebrateRankingAccomClassOverride webrateRankingAccomClassOverride, List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetailsList) {
        int rankingCount = getCountByAccomClassIdAndProductIdForActiveRanking(webrateRankingAccomClassOverride.getAccomClass().getId(), webrateRankingAccomClassOverride.getProductID());
        Date startDate = webrateRankingAccomClassOverride.getStartDate().getTime();
        Date endDate = webrateRankingAccomClassOverride.getEndDate().getTime();
        AtomicInteger counter = new AtomicInteger();
        List<WebrateOverrideCompetitor> webrateOverrideCompetitorListByProductId = getWebrateOverrideCompetitor(webrateOverrideCompetitorDetailsList, webrateRankingAccomClassOverride.getAccomClass().getId());
        if (CollectionUtils.isNotEmpty(webrateOverrideCompetitorListByProductId)) {
            webrateOverrideCompetitorListByProductId.forEach(webrateOverrideCompetitor -> {
                boolean isStartDateInRange = DateUtil.isDateBetween(webrateOverrideCompetitor.getStartDate().getTime(), webrateOverrideCompetitor.getEndDate().getTime(), startDate);
                boolean isEndDateInRange = DateUtil.isDateBetween(webrateOverrideCompetitor.getStartDate().getTime(), webrateOverrideCompetitor.getEndDate().getTime(), endDate);
                boolean isIgnoreCompetitorStartDateInRange = DateUtil.isDateBetween(startDate, endDate, webrateOverrideCompetitor.getStartDate().getTime());
                boolean isIgnoreCompetitorEndDateInRange = DateUtil.isDateBetween(startDate, endDate, webrateOverrideCompetitor.getEndDate().getTime());
                if (isStartDateInRange || isEndDateInRange || isIgnoreCompetitorStartDateInRange || isIgnoreCompetitorEndDateInRange) {
                    counter.getAndIncrement();
                }
            });
        }
        return rankingCount - counter.get();
    }

    public int getEffectiveCompetitorsCountForDefaultConfig(int accomClassId, int productId, Date systemDate, List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetailsList) {
        int rankingCount = getCountByAccomClassIdAndProductIdForActiveRanking(accomClassId, productId);
        List<WebrateOverrideCompetitor> webrateOverrideCompetitorList = getWebrateOverrideCompetitor(webrateOverrideCompetitorDetailsList, accomClassId);
        AtomicInteger counter = new AtomicInteger();
        if (CollectionUtils.isNotEmpty(webrateOverrideCompetitorList)) {
            webrateOverrideCompetitorList.forEach(WebrateOverrideCompetitor -> {
                if (DateUtil.isDateBetween(WebrateOverrideCompetitor.getStartDate().getTime(), WebrateOverrideCompetitor.getEndDate().getTime(), systemDate)) {
                    counter.getAndIncrement();
                }
            });
        }
        return rankingCount - counter.get();
    }

    public List<WebrateOverrideCompetitor> getWebrateOverrideCompetitor(List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetailsList, int accomClassId) {
        if (CollectionUtils.isNotEmpty(webrateOverrideCompetitorDetailsList)) {
            List<WebrateOverrideCompetitor> webrateOverrideCompetitorList = new ArrayList<>();
            webrateOverrideCompetitorDetailsList.forEach(webrateOverrideCompetitorDetails -> {
                if (webrateOverrideCompetitorDetails.getWebrateCompetitorsAccomClass().getAccomClass().getId() == accomClassId && webrateOverrideCompetitorDetails.getWebrateCompetitorsAccomClass().getRankingEnabled() == 1) {
                    webrateOverrideCompetitorList.add(webrateOverrideCompetitorDetails.getWebrateOverrideCompetitor());
                }
            });
            return webrateOverrideCompetitorList;
        }
        return Collections.emptyList();
    }

    public boolean addDefaultWebRateChannel(String channelName) {
        List<WebrateChannel> webRateChannelsList = getWebRateChannelsList();
        List<Product> products = agileRatesConfigurationService.getAgileAndSystemDefaultProductsAndIndependentProducts();
        final WebrateChannel webrateChannel = webRateChannelsList.stream().filter(x -> x.getWebrateChannelName().equalsIgnoreCase(channelName))
                .findFirst().orElse(null);
        if (Optional.ofNullable(webrateChannel).isPresent()) {
            List<WebrateDefaultChannel> webRateDefaultChannels = products.stream()
                    .map(product -> getWebrateDefaultChannel(null, webrateChannel, product))
                    .collect(Collectors.toList());
            List<WebrateDefaultChannel> defaultChannel = defaultReferenceChannelService.getAllDefaultChannelsByPropertyId(PacmanWorkContextHelper.getPropertyId());
            if (Optional.ofNullable(defaultChannel).isPresent()) {
                deleteWebRateDefaultChannelByPropertyId(PacmanWorkContextHelper.getPropertyId());
            }
            defaultReferenceChannelService.saveDefaultChannels(webRateDefaultChannels);
            defaultReferenceChannelService.updateAllNewStatusToActive();
            return true;
        }
        return false;
    }

    public void removeAllWebRateAccomClassMapping() {
        crudService.deleteAll(WebrateAccomClassMapping.class);
    }

    public void activateCompetitors() {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        List<WebrateCompetitors> webRateCompetitors = crudService.findByNamedQuery(WebrateCompetitors.BY_PROPERTYID_AND_STATUS_IN,
                QueryParameter.with("propertyId", propertyId).parameters());
        if (webRateCompetitors.size() == 1) {
            webRateCompetitors.forEach(webRateCompetitor -> {
                webRateCompetitor.setStatusId(ACTIVE_STATUS_ID);
                webRateCompetitor.getWebrateCompetitorsAccomClasses().forEach(accomClass -> accomClass.setDemandEnabled(ACTIVE_STATUS_ID));
            });
        } else {
            List<WebrateType> webRateTypes = getWebRateTypes();
            List<WebrateTypeProduct> webRateTypeProducts = getWebRateTypeProducts(webRateTypes);
            Map<Product, List<WebrateTypeProduct>> webRateTypeProductsByProductIds = webRateTypeProducts.stream().collect(Collectors.groupingBy(WebrateTypeProduct::getProduct));
            String webRateHotelId = configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
            Property property = propertyService.getPropertyById(propertyId);
            webRateTypeProductsByProductIds.forEach((key, value) -> {
                Integer productId = key.getId();
                int webRateTypeId = value.get(0).getWebrateType().getId();
                Set<Integer> mappedLosValues = value.stream().map(WebrateTypeProduct::getLos).collect(Collectors.toSet());
                Map<Integer, BigDecimal> avgHistoricalAdrByAccomClass =
                        getAvgHistoricalAdrByAccomClass(computeMarketSegmentIds(productId, webRateTypeId, webRateTypes), DATA_WINDOW_LAST_YEAR)
                                .stream().collect(Collectors.toMap(WebrateAccomTypeAverageRateDTO::getWebrateAccomTypeId,
                                        WebrateAccomTypeAverageRateDTO::getAverageRate));
                Set<Integer> competitorIds = webRateCompetitors.stream().map(WebrateCompetitors::getId).collect(Collectors.toSet());
                List<WebRateCompetitorAverageRateDTO> webRateCompetitorAverageRateDTOS =
                        getHistoricalAverageCompetitorsRates(competitorIds, productId, mappedLosValues, avgHistoricalAdrByAccomClass);
                Map<Integer, Map<Integer, List<WebRateCompetitorAverageRateDTO>>> webRatesByCompetitorsAndAccomClasses =
                        webRateCompetitorAverageRateDTOS.stream()
                                .collect(Collectors.groupingBy(WebRateCompetitorAverageRateDTO::getCompetitorId,
                                        Collectors.groupingBy(WebRateCompetitorAverageRateDTO::getAccomClassId)));
                Map<Integer, Double> meanByAccomClassId = computeMean(webRateCompetitorAverageRateDTOS);
                Map<Integer, List<Double>> percentageDiffsByAccomClasses = computePercentageDifferenceByAccomClasses(webRateCompetitorAverageRateDTOS);
                Map<Integer, Double> standardDeviation2ByRoomClasses = computeStandardDeviation2(percentageDiffsByAccomClasses, meanByAccomClassId);

                webRateCompetitors.forEach(webRateCompetitor -> {
                    webRateCompetitor.getWebrateCompetitorsAccomClasses().stream()
                            .filter(webRateCompetitorsAccomClass -> webRateCompetitorsAccomClass.getProductID().equals(productId))
                            .forEach(accomClass -> {
                                boolean isSelfHotel = false;
                                if ((webRateHotelId != null && webRateHotelId.equalsIgnoreCase(webRateCompetitor.getWebrateHotelID()))
                                        || isRDLSelfProperty(webRateCompetitor, property)) {
                                    isSelfHotel = true;
                                }
                                Integer accomClassId = accomClass.getAccomClass().getId();
                                List<WebRateCompetitorAverageRateDTO> webRateDTO = null;
                                if (MapUtils.isNotEmpty(webRatesByCompetitorsAndAccomClasses)
                                        && (null != webRatesByCompetitorsAndAccomClasses.get(webRateCompetitor.getId()))) {
                                    webRateDTO = webRatesByCompetitorsAndAccomClasses.get(webRateCompetitor.getId()).get(accomClassId);
                                }
                                Double percentageDiff = null != webRateDTO ? webRateDTO.get(0).getPercentageDiff() : null;
                                Double thresholdPercentageDiff = standardDeviation2ByRoomClasses.get(accomClassId);
                                if (isSelfHotel || (null != thresholdPercentageDiff && null != percentageDiff && percentageDiff <= thresholdPercentageDiff)) {
                                    accomClass.setDemandEnabled(ACTIVE_STATUS_ID);
                                } else {
                                    accomClass.setDemandEnabled(0);
                                }
                            });
                    updateCompetitorStatus(webRateCompetitor);
                });
            });
        }
        crudService.save(webRateCompetitors);
        alertService.resolveAllAlerts(AlertType.NewWebRateCompetitorFound, PacmanWorkContextHelper.getPropertyId());
    }

    private List<WebrateType> getWebRateTypes() {
        return crudService.findByNamedQuery(WebrateType.ALL);
    }

    private List<WebrateTypeProduct> getWebRateTypeProducts(List<WebrateType> webRateTypes) {
        Set<Integer> webRateTypeIds = webRateTypes.stream()
                .filter(p -> p.getWebrateTypeCode().equalsIgnoreCase(BEST_FLEXIBLE_TYPE_CODE) ||
                        p.getWebrateTypeCode().equalsIgnoreCase(SEMI_FLEXIBLE_TYPE_CODE) ||
                        p.getWebrateTypeCode().equalsIgnoreCase(ANY_NON_QUALIFIED_TYPE_CODE))
                .map(WebrateType::getId)
                .collect(Collectors.toSet());
        return CollectionUtils.isEmpty(webRateTypeIds) ? Collections.emptyList() :
                crudService.findByNamedQuery(WebrateTypeProduct.GET_BY_WEBRATE_TYPE_IN,
                        QueryParameter.with("webrateTypeIds", webRateTypeIds).parameters());
    }

    private Product getPrimaryProduct() {
        return crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
    }

    private List<Integer> computeMarketSegmentIds(Integer productId, int webRateTypeId, List<WebrateType> webRateTypes) {
        Product product = getPrimaryProduct();
        WebrateType bestFlexibleWebRateType = webRateTypes.stream()
                .filter(w -> w.getWebrateTypeCode().equalsIgnoreCase(BEST_FLEXIBLE_TYPE_CODE))
                .filter(w -> w.getId().equals(webRateTypeId))
                .filter(w -> productId.equals(product.getId()))
                .findFirst().orElse(null);
        List<Integer> marketSegmentIds;
        if (null != bestFlexibleWebRateType) {
            marketSegmentIds = getStraightBarMarketSegments();
        } else {
            marketSegmentIds = crudService.findByNamedQuery(MktSegForecastGroup.GET_MS_BY_FORECAST_GROUP_AND_PRODUCT,
                    QueryParameter.with("productId", productId).parameters());
        }
        return marketSegmentIds;
    }

    public List<WebrateAccomTypeAverageRateDTO> getAvgHistoricalAdrByAccomClass(List<Integer> marketSegmentIds, int suggestionDataWindowInYears) {
        LocalDate caughtUpDate = dateService.getCaughtUpJavaLocalDate();
        List<Object[]> results = CollectionUtils.isEmpty(marketSegmentIds) ? Collections.emptyList() :
                crudService.findByNamedQuery(MktSegAccomActivity.GET_ADR_BY_ACCOM_CLASS_FOR_DATE_RANGE_AND_MKT_SEGS,
                        QueryParameter.with("startDate", caughtUpDate.minusYears(suggestionDataWindowInYears))
                                .and("endDate", caughtUpDate)
                                .and("mktSegsIds", marketSegmentIds)
                                .parameters());
        return Optional.ofNullable(results)
                .orElse(Collections.emptyList()).stream()
                .map(WebrateAccomTypeAverageRateDTO::new)
                .collect(Collectors.toList());
    }

    public List<WebRateCompetitorAverageRateDTO> getHistoricalAverageCompetitorsRates(Set<Integer> webRateCompetitorIds, int productId, Set<Integer> mappedLosValues,
                                                                                      Map<Integer, BigDecimal> avgHistoricalAdrByWebRateAccomTypes) {
        LocalDate caughtUpDate = dateService.getCaughtUpJavaLocalDate();
        List<Object[]> results = crudService.findByNamedQuery(Webrate.GET_AVG_RATE_FOR_COMPETITOR_AND_DATE_RANGE, QueryParameter
                .with("webrateStatus", WEBRATE_STATUS_AVAILABLE)
                .and("lengthOfStay", mappedLosValues)
                .and("webRateCompetitorIds", webRateCompetitorIds)
                .and("productId", productId)
                .and("startDate", caughtUpDate.minusYears(DATA_WINDOW_LAST_YEAR).toString())
                .and("endDate", caughtUpDate.toString())
                .parameters());
        return Optional.ofNullable(results)
                .orElse(Collections.emptyList()).stream()
                .map(row -> new WebRateCompetitorAverageRateDTO(row, avgHistoricalAdrByWebRateAccomTypes))
                .collect(Collectors.toList());
    }

    private Map<Integer, Double> computeMean(List<WebRateCompetitorAverageRateDTO> webRateCompetitorAverageRateDTOS) {
        return webRateCompetitorAverageRateDTOS.stream()
                .collect(Collectors.groupingBy(WebRateCompetitorAverageRateDTO::getAccomClassId,
                        Collectors.averagingDouble(WebRateCompetitorAverageRateDTO::getPercentageDiff)));
    }

    private Map<Integer, List<Double>> computePercentageDifferenceByAccomClasses(List<WebRateCompetitorAverageRateDTO> webRateCompetitorAverageRateDTOS) {
        return webRateCompetitorAverageRateDTOS.stream()
                .collect(Collectors.groupingBy(WebRateCompetitorAverageRateDTO::getAccomClassId,
                        Collectors.mapping(WebRateCompetitorAverageRateDTO::getPercentageDiff, Collectors.toList())));
    }

    private Map<Integer, Double> computeStandardDeviation2(Map<Integer, List<Double>> percentageDiffsByAccomClasses,
                                                           Map<Integer, Double> meanByAccomClassId) {
        Map<Integer, Double> sampleStandardDeviation2ByRoomClasses = new HashMap<>();
        percentageDiffsByAccomClasses.forEach((roomClassId, percentageDiffs) -> {
            AtomicReference<Double> summation = new AtomicReference<>(0.0);
            percentageDiffs.forEach(percentageDiff -> {
                double diff = percentageDiff - meanByAccomClassId.get(roomClassId);
                summation.set(summation.get() + diff * diff);
            });
            double sampleVariance = summation.get() / (percentageDiffs.size() - 1);
            sampleStandardDeviation2ByRoomClasses.put(roomClassId, (double) Math.round(Math.sqrt(sampleVariance) * 2));
        });
        return sampleStandardDeviation2ByRoomClasses;
    }

    public void rdlCRTMappingRebuild() {
        try {
            if (isTestProperty()) {
                logger.info("Skip publishing CUSTOM_ROOM_TYPE_MAPPING_REBUILD event to RDL for test property");
                return;
            }
            Property property = propertyService.getPropertyById(PacmanWorkContextHelper.getPropertyId());
            g3SNSService.publishToSNS(EventType.CUSTOM_ROOM_TYPE_MAPPING_REBUILD, prepareEventSource(property));
            logger.info("Sent CUSTOM_ROOM_TYPE_MAPPING_REBUILD event to RDL " +  property.getUpsId());
        } catch (JsonProcessingException e) {
            logger.error("Error while publishing rebuild sns event to RDL");
        }
    }

    private boolean isTestProperty() {
        return StringUtils.isNotBlank(configParamsService.getConfigParameterValueAtContext(
                Constants.getPropertyConfigContext(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())
                , FeatureTogglesConfigParamName.RDL_CLONED_FROM_PROPERTY_UPS_ID.value()));
    }

    private Map<String, Object> prepareEventSource(Property property) {
        Map<String, Object> eventSource = new HashMap<>();
        eventSource.put(UNIFIED_PROPERTY_ID_KEY, property.getUpsId());
        eventSource.put(TIER_KEY, configParamsService.getParameterValue(FeatureTogglesConfigParamName.RDL_DEFAULT_TIER));
        eventSource.put(CUSTOM_TYPE_MAPPING_OWNER_KEY, configParamsService.getParameterValue(FeatureTogglesConfigParamName.RDL_DEFAULT_VENDOR));
        eventSource.put(SCHEDULE_OWNER_KEY, configParamsService.getParameterValue(FeatureTogglesConfigParamName.RDL_DEFAULT_VENDOR));
        eventSource.put(CLIENT_CODE_KEY, property.getClient().getCode());
        eventSource.put(EXCLUDED_CUSTOM_TYPES_KEY, configParamsService.getParameterValue(FeatureTogglesConfigParamName.RDL_EXCLUDED_CUSTOM_ROOM_TYPES));
        return eventSource;
    }

    private List<WebrateAccomClassMapping> getAllWebRateAccomClassMapping() {
        return crudService.findAll(WebrateAccomClassMapping.class);
    }

    public List<RoomClassMappingDTO> getAllWebRateAccomTypeAccomClassMapping() {
        List<WebrateAccomType> webRateAccomTypes = getWebRateAccomTypes();
        return webRateAccomTypes.stream().map(this::mapToCompetitiveRoomTypeAccomClassMapping)
                .collect(Collectors.toList());
    }

    private RoomClassMappingDTO mapToCompetitiveRoomTypeAccomClassMapping(WebrateAccomType webrateAccomType) {
        RoomClassMappingDTO roomClassMappingDTO = new RoomClassMappingDTO();
        roomClassMappingDTO.setCompetitiveRT(webrateAccomType.getWebrateAccomName());
        roomClassMappingDTO.setAccomClassCode(webrateAccomType.getWebrateAccomClassMappings().stream()
                .filter(mapping -> null != mapping.getAccomClass())
                .map(mapping -> mapping.getAccomClass().getCode()).collect(Collectors.toList()));
        return roomClassMappingDTO;
    }

    public List<WebrateAccomType> getWebRateAccomTypes() {
        return crudService.findAll(WebrateAccomType.class);
    }

    public void saveCompetitiveRoomTypeToAccomClass(List<RoomClassMappingDTO> roomClassMappingRequests, List<AccomClass> activeAccomClasses,
                                                    List<WebrateAccomType> existingWebRateAccomTypes) {
        List<WebrateAccomClassMapping> existingWebRateAccomClassMappings = getAllWebRateAccomClassMapping();
        Map<String, Map<String, WebrateAccomClassMapping>> webRateAccomClassMappingByName = existingWebRateAccomClassMappings.stream()
                .collect(Collectors.groupingBy(mapping -> mapping.getWebrateAccomType().getWebrateAccomName().toUpperCase(),
                        Collectors.toMap(c -> c.getAccomClass().getCode().toUpperCase(), Function.identity())));
        Map<String, AccomClass> roomClassMappingByCode = activeAccomClasses.stream()
                .collect(Collectors.toMap(mapping -> mapping.getCode().toUpperCase(), Function.identity()));
        Map<String, WebrateAccomType> webRateAccomTypeMappingByCode = existingWebRateAccomTypes.stream()
                .collect(Collectors.toMap(mapping -> mapping.getWebrateAccomName().toUpperCase(), Function.identity()));
        List<WebrateAccomClassMapping> webRateAccomClassMappingsToBeSaved = new ArrayList<>();
        roomClassMappingRequests.forEach(roomClassMappingRequest -> {
            String competitiveRTFromRequest = roomClassMappingRequest.getCompetitiveRT().toUpperCase();
            roomClassMappingRequest.getAccomClassCode().forEach(accomCode -> {
                String accomClassToBeMappedFromRequest = accomCode.toUpperCase();
                if (!(webRateAccomClassMappingByName.containsKey(competitiveRTFromRequest)) ||
                        (webRateAccomClassMappingByName.containsKey(competitiveRTFromRequest) &&
                                !webRateAccomClassMappingByName.get(competitiveRTFromRequest).containsKey(accomClassToBeMappedFromRequest))) {
                    WebrateAccomClassMapping webRateAccomClassMappingToBeInserted = new WebrateAccomClassMapping();
                    webRateAccomClassMappingToBeInserted.setAccomClass(roomClassMappingByCode.get(accomClassToBeMappedFromRequest));
                    webRateAccomClassMappingToBeInserted.setWebrateAccomType(webRateAccomTypeMappingByCode.get(competitiveRTFromRequest));
                    webRateAccomClassMappingsToBeSaved.add(webRateAccomClassMappingToBeInserted);
                }
            });
        });
        if (CollectionUtils.isNotEmpty(webRateAccomClassMappingsToBeSaved)) {
            crudService.saveWithFlushAndClear(webRateAccomClassMappingsToBeSaved, webRateAccomClassMappingsToBeSaved.size());
            createAndUpdateWebRateRankingAccommodationClass(null, PacmanWorkContextHelper.getPropertyId(), PacmanWorkContextHelper.getUserId());
            createCompetitorAccomClassMapping();
        }
    }

    public boolean isMapAccomTypeToMultipleAccomClassesEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_MAP_ACCOM_TYPE_TO_MULTIPLE_ACCOM_CLASSES_ENABLED);
    }

    public void deleteCompetitiveRoomTypeToAccomClassMapping(List<RoomClassMappingDTO> roomClassMappingRequests) {
        List<WebrateAccomClassMapping> existingWebRateAccomClassMappings = getAllWebRateAccomClassMapping();
        Map<String, Map<String, WebrateAccomClassMapping>> webRateAccomClassMappingByName = existingWebRateAccomClassMappings.stream()
                .collect(Collectors.groupingBy(mapping -> mapping.getWebrateAccomType().getWebrateAccomName().toUpperCase(),
                        Collectors.toMap(c -> c.getAccomClass().getCode().toUpperCase(), Function.identity())));
        List<WebrateAccomClassMapping> webRateAccomClassMappingToBeDeleted = new ArrayList<>();
        roomClassMappingRequests.forEach(roomClassMappingRequest -> {
            String competitiveRTFromRequest = roomClassMappingRequest.getCompetitiveRT().toUpperCase();
            roomClassMappingRequest.getAccomClassCode().forEach(accomCode -> {
                String accomClassToBeMappedFromRequest = accomCode.toUpperCase();
                if (webRateAccomClassMappingByName.containsKey(competitiveRTFromRequest) &&
                        webRateAccomClassMappingByName.get(competitiveRTFromRequest).containsKey(accomClassToBeMappedFromRequest)) {
                    webRateAccomClassMappingToBeDeleted.add(webRateAccomClassMappingByName.get(competitiveRTFromRequest).get(accomClassToBeMappedFromRequest));
                }
            });
        });
        if (CollectionUtils.isNotEmpty(webRateAccomClassMappingToBeDeleted)) {
            List<AccomClass> activeAccomClasses = accommodation2Service.getAllActiveAccomClasses();
            existingWebRateAccomClassMappings.removeAll(webRateAccomClassMappingToBeDeleted);
            Set<Integer> accomClassesToBePreserved = new HashSet<>();
            existingWebRateAccomClassMappings.forEach(mapping -> accomClassesToBePreserved.add(mapping.getAccomClass().getId()));
            List<AccomClass> accomClassesToBeDeleted = activeAccomClasses.stream()
                    .filter(classes -> !accomClassesToBePreserved.contains(classes.getId())).collect(Collectors.toList());
            removeWebRateAccomClassMapping(webRateAccomClassMappingToBeDeleted, accomClassesToBeDeleted);
        }
    }

    public void removeWebRateAccomClassMapping(List<WebrateAccomClassMapping> webRateAccomClassMappingToBeDeleted, List<AccomClass> accomClassesToBeDeleted) {
        Set<Integer> competitorAccomTypeIds = webRateAccomClassMappingToBeDeleted.stream().map(WebrateAccomClassMapping::getId).collect(Collectors.toSet());
        List<Integer> accomClassIdsToBeDeleted = accomClassesToBeDeleted.stream().map(AccomClass::getId).collect(Collectors.toList());
        if (!accomClassesToBeDeleted.isEmpty()) {
            deleteWebrateOverrideCompetitors(accomClassesToBeDeleted);
            deleteWebrateRankingAccomClass(accomClassIdsToBeDeleted);
            deleteWebrateRankingAccomClassOverrides(accomClassIdsToBeDeleted);
        }
        webRateAccomClassMappingToBeDeleted.forEach(mappingToBeDeleted -> crudService
                .executeUpdateByNamedQuery(WebrateAccomClassMapping.DELETE_BY_COMPETITOR_ACCOM_TYPE_IDS,
                        QueryParameter.with("competitorAccomTypeIds", competitorAccomTypeIds).parameters()));
    }

    public List<WebrateRankingAccomClass> getStandardDefaultCMPCConfig(){
        return crudService.findByNamedQuery(STANDARD_CMPC_DEFAULT_CONFIG_WITH_PROPERTY_NAME);
    }

    public List<WebrateRankingAccomClassOverride> getStandardSeasonCMPCConfig(){
        return crudService.findAll(WebrateRankingAccomClassOverride.class);
    }

    public void deleteWebrateCompetitorChannelMappings(Product product) {
        webrateShoppingCleanUpService.cleanupWebrateCompetitorChannelMappingsBasedOnProduct(product);
    }
}
