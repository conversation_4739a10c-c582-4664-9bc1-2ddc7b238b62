package com.ideas.tetris.pacman.services.rates;

import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.SemiYieldableRateDetails;
import com.ideas.tetris.pacman.services.ratepopulation.dtos.SemiYieldableRatesBatchDto;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractDetail;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractRate;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.util.streamutils.StreamUtils.keepOnly;

@Component
@Transactional
public class SemiYieldableRateConverter extends RateConverter {

    private static final Logger LOGGER = Logger.getLogger(RateConverter.class);

    @Override
    protected AbstractDetail createNewDetail() {
        return new SemiYieldableRateDetails();
    }

    @Override
    protected AbstractDetail createNewDetail(AbstractDetail detail) {
        return detail.cloneDetail();
    }

    @Override
    protected void setHeaderReferenceOnDetails(AbstractRate rate, AbstractDetail detail) {
        ((SemiYieldableRateDetails) detail).setRateCodeName(rate.getName());
    }

    @Override
    protected void saveRateDetailsInBatch(List<AbstractDetail> detailsToSave) {
        final List<SemiYieldableRatesBatchDto> tableBatch = detailsToSave.stream()
                .flatMap(keepOnly(SemiYieldableRateDetails.class))
                .map(SemiYieldableRatesBatchDto::fromEntity)
                .collect(Collectors.toList());

        Set<String> rateCodeNames = tableBatch.stream().map(SemiYieldableRatesBatchDto::getRateCodeName).collect(Collectors.toSet());
        deleteExistingRateDetails(rateCodeNames);
        if (!tableBatch.isEmpty()) {
            tenantCrudService.execute(SemiYieldableRatesBatchDto.USP_SEMI_YIELDABLE_RATE_DETAILS_INSERT, tableBatch);
        }
    }

    @Override
    protected void convertSpecificFields(Map<String, Object> dto, AbstractRate rateHeader) {
        throw new UnsupportedOperationException("convertSpecificFields");
    }

    @Override
    protected FileMetadata createFileMetadata(Map<String, Object> dto) {
        throw new UnsupportedOperationException("createFileMetadata");
    }

    @Override
    protected void softDeleteHeader(Integer fileMetaDataId, AbstractRate rateHeader, Date caughtUpDate) {
        throw new UnsupportedOperationException("softDeleteHeader");
    }

    @Override
    protected void saveHeaders(List<RateHeaderAndDetail> records) {
        // Headers used for Reference Only. No need to save.
    }

    @Override
    protected void truncateEndDateOfSeasonsWitinRangeOfGivenDate(Integer headerId, Date dateToTruncateDetailsFrom) {
        throw new UnsupportedOperationException("Not Supported Operation : truncateEndDateOfSeasonsWitinRangeOfGivenDate");
    }

    @Override
    protected void deleteDetailsStartingFrom(List<Integer> rateHeaderIds, Date endDate) {
        throw new UnsupportedOperationException("Not Supported Operation : deleteDetailsStartingFrom");
    }

    @Override
    protected AbstractRate getHeader(String name) {
        return null;
    }

    @Override
    protected List<AbstractDetail> getDetails(List<Integer> ids) {
        throw new UnsupportedOperationException("getDetails");
    }

    @Override
    protected AbstractRate createNewHeader() {
        throw new UnsupportedOperationException("createNewHeader");
    }

    protected void deleteExistingRateDetails(Set<String> rateCodes) {
        if (!rateCodes.isEmpty()) {
            LOGGER.info("Deleting Existing semi Yieldable Details of " + rateCodes.size() + " ratecodes.");
            tenantCrudService.executeUpdateByNamedQuery(SemiYieldableRateDetails.DELETE_DETAILS_BY_RATE_CODE_NAMES,
                    QueryParameter.with("rateCodeNames", rateCodes).parameters());
        }
    }

}
