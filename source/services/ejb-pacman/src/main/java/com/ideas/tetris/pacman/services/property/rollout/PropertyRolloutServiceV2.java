package com.ideas.tetris.pacman.services.property.rollout;

import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.services.daoandentities.entity.VirtualPropertyMapping;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.ideas.tetris.pacman.services.property.PropertyBuildType.LDB;

@Component
@Transactional
public class PropertyRolloutServiceV2 {
    private static final Logger LOGGER = Logger.getLogger(PropertyRolloutServiceV2.class.getName());

    @Autowired
	private JobServiceLocal jobService;


    public Long addProperty(AddPropertyParams addPropertyParams, Map<String, VirtualPropertyMapping> vpMappings) {
        String clientCode = addPropertyParams.getClientCode();
        String propertyCode = addPropertyParams.getPropertyCode();

        // make sure AddProperty job not already active for this property
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.CLIENT_CODE, clientCode);
        parameters.put(JobParameterKey.PROPERTY_CODE, propertyCode);
        if (jobService.isJobActive(JobName.AddProperty, parameters)) {
            throw new TetrisException(ErrorCode.JOB_ALREADY_ACTIVE,
                    "Active job already exists for client: " + clientCode + " and property: " + propertyCode);
        }

        // add the rest of the parameters we need and start the AddProperty job
        // required parameters
        parameters.put(JobParameterKey.PROPERTY_NAME, addPropertyParams.getPropertyName());
        parameters.put(JobParameterKey.PROPERTY_TIMEZONE, addPropertyParams.getPropertyTimezone());
        parameters.put(JobParameterKey.YIELD_CURRENCY, addPropertyParams.getYieldCurrency());
        parameters.put(JobParameterKey.ESTIMATED_CAPACITY, addPropertyParams.getEstimatedCapacity());
        parameters.put(JobParameterKey.SUBSCRIPTION_TYPE, addPropertyParams.getSubscriptionType());
        parameters.put(JobParameterKey.CONFIGURATION_METHOD, addPropertyParams.getConfigurationMethod());
        parameters.put(JobParameterKey.PROPERTY_BUILD_TYPE, addPropertyParams.getBuildType());

        // optional parameters
        addOptionalParameter(parameters, JobParameterKey.WEB_RATE_ALIAS, addPropertyParams.getWebrateAlias());
        addOptionalParameter(parameters, JobParameterKey.CRS_TIMEZONE, addPropertyParams.getCrsTimezone());
        addOptionalParameter(parameters, JobParameterKey.EXTERNAL_SYSTEM, addPropertyParams.getExternalSystem());
        addOptionalParameter(parameters, JobParameterKey.EXTERNAL_SUB_SYSTEM, addPropertyParams.getExternalSubSystem());
        addOptionalParameter(parameters, JobParameterKey.ONBOARD_OPTION, addPropertyParams.getOnboardOptions());
        addOptionalParameter(parameters, JobParameterKey.NGI_ENVIRONMENT, addPropertyParams.getNgiEnvironment());
        addOptionalParameter(parameters, JobParameterKey.REMOTE_AGENT_ID, addPropertyParams.getRemoteAgentId());
        addOptionalParameter(parameters, JobParameterKey.BASE_CURRENCY, addPropertyParams.getBaseCurrency());
        addOptionalParameter(parameters, JobParameterKey.ENABLEYC, addPropertyParams.isEnableYC());
        addOptionalParameter(parameters, JobParameterKey.SFDC_ACCOUNT_NO, addPropertyParams.getSFDCAccountNo());
        addOptionalParameter(parameters, JobParameterKey.DEFAULT_ROOM_REVENUE_TAX_IN_RATE_OUTPUT, addPropertyParams.getDefaultRoomRevenueTaxInRateOutput());
        addVPParameters(vpMappings, clientCode, parameters);
        parameters.put(JobParameterKey.LIMITED_DATA_BUILD, addPropertyParams.isLimitedDataBuild() || LDB.equals(addPropertyParams.getBuildType()));
        parameters.put(JobParameterKey.MIGRATED_FROM_G2, addPropertyParams.isPropertyMigratedFromG2());
        Long jobId = jobService.startGuaranteedNewInstance(JobName.AddProperty, parameters);
        LOGGER.info("Started AddProperty with job id " + jobId + " for property " + addPropertyParams.getPropertyCode());
        return jobId;
    }

    private void addVPParameters(Map<String, VirtualPropertyMapping> vpMappings, String clientCode, Map<String, Object> parameters) {
        if (vpMappings != null && !vpMappings.isEmpty() && StringUtils.equalsIgnoreCase("Hilton", clientCode)) {
            parameters.put(JobParameterKey.IS_VIRTUAL_PROPERTY, true);
            addOptionalParameter(parameters, JobParameterKey.INCOMING_SERIALIZABLE, new ArrayList<>(vpMappings.values()));
        }
    }

    private void addOptionalParameter(Map<String, Object> parameters, String parameter, Object value) {
        if (value != null) {
            parameters.put(parameter, value);
        }
    }


    public Long deleteProperty(Integer propertyId, String sfdcCaseNumber) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.PROPERTY_ID, propertyId);

        // make sure delete property job not already active for this property
        if (jobService.isJobActive(JobName.DeleteProperty, parameters)) {
            throw new TetrisException(ErrorCode.JOB_ALREADY_ACTIVE,
                    "Active DeleteProperty job already exists for property: " + propertyId);
        }

        parameters.put(JobParameterKey.SFDC_CASE_NUMBER, sfdcCaseNumber);

        Long jobId = jobService.startGuaranteedNewInstance(JobName.DeleteProperty, parameters);
        LOGGER.info("Started DeleteProperty with job id " + jobId + " for property " + propertyId);
        return jobId;
    }

}
