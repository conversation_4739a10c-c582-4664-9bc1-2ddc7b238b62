package com.ideas.tetris.pacman.services.security.login.mapper;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.UserIndividualPropertyRole;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.apache.log4j.Logger;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class PropertyRoleMapper {

    private static final Logger LOGGER = Logger.getLogger(PropertyRoleMapper.class.getName());
    @Autowired
    UserService userService;

    public Map<Integer, String> getPropertyRoleMapping(GlobalUser globalUser, Integer clientId) {
        Map<Integer, String> finalPropertyRoleMap = getIndividualPropertyRoleMapping(globalUser);
        Map<Integer, String> authGroupPropertyRoleMap = getAuthGroupPropertyRoleMapping(globalUser, clientId);
        for (Map.Entry<Integer, String> entry : authGroupPropertyRoleMap.entrySet()) {
            final Integer propertyId = entry.getKey();
            if (!finalPropertyRoleMap.containsKey(propertyId)) {
                finalPropertyRoleMap.put(propertyId, authGroupPropertyRoleMap.get(propertyId));
            }
        }
        return finalPropertyRoleMap;
    }

    public Map<Integer, String> getIndividualPropertyRoleMapping(GlobalUser globalUser) {
        return globalUser.getIndividualPropertyRoles().stream().collect(Collectors.toMap(UserIndividualPropertyRole::getPropertyId,
                UserIndividualPropertyRole::getRoleId));
    }

    public Map<Integer, String> getAuthGroupPropertyRoleMapping(GlobalUser globalUser, Integer clientId) {
        Map<Integer, String> authGroupPropertyRoleMap = new HashMap<>();
        if (globalUser.getAuthGroupRole() != null) {
            final String roleId = globalUser.getAuthGroupRole().getRoleId();

            for (Integer propertyId : getPropertyIdsByAuthGroupId(globalUser.getAuthGroupRole().getAuthGroupId(), clientId)) {
                authGroupPropertyRoleMap.put(propertyId, roleId);
            }
        }
        return authGroupPropertyRoleMap;
    }

    public List<Integer> getPropertyIdsByAuthGroupId(Integer authGroupId, Integer clientId) {
        try {
            return userService.extractPropertyIdsByAuthGroupId(authGroupId, clientId);
        } catch (TetrisException e) {
            String errorMessage = String.format("Exception has occurred in PropertyRoleMapper::getPropertyIdsByAuthGroupId() while getting propertyIds by authGroupId=%d for User ID=%s with Client Code=%s", authGroupId, PacmanWorkContextHelper.getUserId(), PacmanWorkContextHelper.getClientCode());
            LOGGER.error(errorMessage, e);
            return Collections.emptyList();
        }
    }
}
