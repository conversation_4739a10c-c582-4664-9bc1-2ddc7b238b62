package com.ideas.tetris.pacman.services.catchup;

import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.JobViewCriteria;
import com.ideas.tetris.pacman.services.property.ExtractDetailsServiceLocal;
import com.ideas.tetris.pacman.services.property.dto.WebRateExtractDetails;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.List;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.GUIConfigParamName.POPULATION_ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class CatchupHelper {
    private static final Logger logger = Logger.getLogger(CatchupHelper.class);
    @Autowired
	private JobMonitorService jobMonitorService;
    @Autowired
	protected ExtractDetailsServiceLocal extractDetailsService;
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;

    public boolean isProperStage(Property property) {
        if (Stage.TWO_WAY.equals(property.getStage())) {
            logIneligibility("it's TWO_WAY stage", property.getCode());
            return false;
        } else {
            return true;
        }
    }

    public boolean isNotInConflictWithAMS(Property property) {
        final boolean amsEnabled = pacmanConfigParamsService.getBooleanParameterValue(
                ANALYTICAL_MARKET_SEGMENT_ENABLED.getParameterName(),
                property.getClient().getCode(), property.getCode());
        final boolean amsMappingComplete = pacmanConfigParamsService.getBooleanParameterValue(
                POPULATION_ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE.getParameterName(),
                property.getClient().getCode(), property.getCode());
        if (amsEnabled ^ amsMappingComplete) {
            logIneligibility("AMS Mapping was not complete.", property.getCode());
            return false;
        }
        return true;
    }

    public boolean areJobsNotRunningForProperty(Property property, List<String> jobNames) {
        JobViewCriteria criteria = new JobViewCriteria();
        criteria.setPropertyId(property.getId());
        criteria.setJobNames(jobNames);
        criteria.setActiveStatuses();
        if (jobMonitorService.getJobs(criteria).isEmpty()) {
            return true;
        } else {
            logIneligibility("job already running", property.getCode());
            return false;
        }
    }

    private void logIneligibility(String reason, String propertyCode) {
        logger.info("Property not eligible for catchup because " + reason + ":" + propertyCode);
    }

    public boolean hasActivityOrWebRates(Property property, boolean hasActivityData) {
        WebRateExtractDetails webRateExtractDetails = extractDetailsService.getWebRateExtractDetails(property.getId());

        if (webRateExtractDetails.getNumberOfIncomingExtracts() == 0 && !hasActivityData) {
            logIneligibility("no days to catchup", property.getCode());
            return false;
        } else {
            return true;
        }
    }
}
