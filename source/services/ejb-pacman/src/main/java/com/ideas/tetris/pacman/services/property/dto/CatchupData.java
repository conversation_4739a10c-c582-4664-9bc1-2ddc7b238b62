package com.ideas.tetris.pacman.services.property.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.property.configuration.dto.CatchupStatus;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.io.Serializable;
import java.util.Date;

public class CatchupData implements Serializable {
    private static final long serialVersionUID = 3977439379998168011L;
    private Integer detailId;
    private Date processStartDate;
    private Date processEndDate;
    private String user;
    private CatchupStatus status;

    private Date extractStartDate;
    private DateParameter extractStartDateParam;
    private boolean eligibleForCatchup = false;
    private String reasonForCatchupNotEligible;
    private boolean eligibilityOverridable = false;

    private Date extractEndDate;
    private DateParameter extractEndDateParam;
    private boolean includeWebRatesInCatchup = false;

    public CatchupData() {
    }

    public CatchupData(CatchupData catchupDataBean) {
        this.setDetailId(catchupDataBean.getDetailId());
        this.setProcessStartDate(catchupDataBean.getProcessStartDate());
        this.setProcessEndDate(catchupDataBean.getProcessEndDate());
        this.setUser(catchupDataBean.getUser());
        this.setStatus(catchupDataBean.getStatus());
        this.setExtractStartDate(catchupDataBean.getExtractStartDate());
        this.setExtractStartDateParam(catchupDataBean.getExtractStartDateParam());
        this.setEligibleForCatchup(catchupDataBean.isEligibleForCatchup());
        this.setReasonForCatchupNotEligible(catchupDataBean.getReasonForCatchupNotEligible());
        this.setEligibilityOverridable(catchupDataBean.isEligibilityOverridable());
        this.setExtractEndDate(catchupDataBean.getExtractEndDate());
        this.setExtractEndDateParam(catchupDataBean.getExtractEndDateParam());
        this.setIncludeWebRatesInCatchup(catchupDataBean.isIncludeWebRatesInCatchup());
    }

    public CatchupStatus getStatus() {
        return status;
    }

    public void setStatus(CatchupStatus status) {
        this.status = status;
    }

    public DateParameter getExtractStartDateParam() {
        return this.extractStartDateParam;
    }

    public void setExtractStartDateParam(DateParameter dateParam) {
        this.extractStartDateParam = dateParam;
        if (dateParam != null) {
            this.extractStartDate = dateParam.getTime();
        } else {
            this.extractStartDate = null;
        }
    }

    public DateParameter getExtractEndDateParam() {
        return this.extractEndDateParam;
    }

    public void setExtractEndDateParam(DateParameter dateParam) {
        this.extractEndDateParam = dateParam;
        if (dateParam != null) {
            this.extractEndDate = dateParam.getTime();
        } else {
            this.extractEndDate = null;
        }
    }

    public Date getProcessStartDate() {
        return processStartDate;
    }

    public void setProcessStartDate(Date processStartDate) {
        this.processStartDate = processStartDate;
    }

    public Date getProcessEndDate() {
        return processEndDate;
    }

    public void setProcessEndDate(Date processEndDate) {
        this.processEndDate = processEndDate;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    @JsonIgnore
    public Date getExtractStartDate() {
        return extractStartDate;
    }

    public void setExtractStartDate(Date extractStartDate) {
        this.extractStartDate = extractStartDate;
        if (extractStartDate != null) {
            this.extractStartDateParam = new DateParameter(extractStartDate);
        }
    }

    @JsonIgnore
    public Date getExtractEndDate() {
        return extractEndDate;
    }

    public void setExtractEndDate(Date extractEndDate) {
        this.extractEndDate = extractEndDate;
        if (extractEndDate != null) {
            this.extractEndDateParam = new DateParameter(extractEndDate);
        }
    }

    public boolean isIncludeWebRatesInCatchup() {
        return includeWebRatesInCatchup;
    }

    public void setIncludeWebRatesInCatchup(boolean includeWebRatesInCatchup) {
        this.includeWebRatesInCatchup = includeWebRatesInCatchup;
    }

    public boolean isEligibleForCatchup() {
        return eligibleForCatchup;
    }

    public void setEligibleForCatchup(boolean eligibleForCatchup) {
        this.eligibleForCatchup = eligibleForCatchup;
    }

    public String getReasonForCatchupNotEligible() {
        return reasonForCatchupNotEligible;
    }

    public void setReasonForCatchupNotEligible(String reasonForCatchupNotEligible) {
        this.reasonForCatchupNotEligible = reasonForCatchupNotEligible;
    }

    public boolean isEligibleForCatchupForEndDate(Date endDate) {
        return (extractStartDate != null && extractEndDate != null &&
                endDate != null && !endDate.after(extractEndDate));
    }

    public boolean isEligibilityOverridable() {
        return eligibilityOverridable;
    }

    public void setEligibilityOverridable(boolean eligibilityOverridable) {
        this.eligibilityOverridable = eligibilityOverridable;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj == this) {
            return true;
        }
        if (obj.getClass() != getClass()) {
            return false;
        }
        CatchupData catchupData = (CatchupData) obj;
        return new EqualsBuilder().append(detailId, catchupData.detailId).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(21, 41).append(detailId).toHashCode();
    }
}
