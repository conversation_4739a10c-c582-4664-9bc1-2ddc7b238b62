package com.ideas.tetris.pacman.services.dashboard.dto;

import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.MultiPropertyAggregate;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Sum;

import java.math.BigDecimal;
import java.math.RoundingMode;

@MultiPropertyAggregate
public class RevenueOccupancyDto {
    private static final int CALCULATED_SCALE = 2;

    @Sum(scale = 1)
    private BigDecimal occupancyNumber;

    @Sum(scale = 0)
    private BigDecimal revenue;

    private String businessType;

    public BigDecimal getOccupancyNumber() {
        return occupancyNumber;
    }

    public void setOccupancyNumber(BigDecimal occupancyNumber) {
        this.occupancyNumber = occupancyNumber;
    }

    public BigDecimal getRevenue() {
        return revenue;
    }

    public void setRevenue(BigDecimal revenue) {
        this.revenue = revenue;
    }

    public BigDecimal getADR() {
        if (revenue == null || occupancyNumber == null || occupancyNumber.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(revenue.doubleValue() / occupancyNumber.doubleValue()).setScale(CALCULATED_SCALE, RoundingMode.HALF_UP);
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
}
