package com.ideas.tetris.pacman.services.dashboard.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Key;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.MultiPropertyAggregate;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Sum;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import java.math.BigDecimal;
import java.util.Date;

@MultiPropertyAggregate
public class RevenueByMarketSegmentDto {
    @Key
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    private Date occupancyDate;
    @Key
    private String marketSegCode;
    @Sum
    private BigDecimal revenue;

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public String getMarketSegCode() {
        return marketSegCode;
    }

    public void setMarketSegCode(String marketSegCode) {
        this.marketSegCode = marketSegCode;
    }

    public BigDecimal getRevenue() {
        return revenue;
    }

    public void setRevenue(BigDecimal revenue) {
        this.revenue = revenue;
    }
}
