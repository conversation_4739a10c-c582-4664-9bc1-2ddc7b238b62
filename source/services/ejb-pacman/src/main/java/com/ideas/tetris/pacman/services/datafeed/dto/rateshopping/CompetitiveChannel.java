package com.ideas.tetris.pacman.services.datafeed.dto.rateshopping;

import java.util.Date;

public class CompetitiveChannel {

    private String category;
    private String rateChannelSun;
    private String rateChannelMon;
    private String rateChannelTues;
    private String rateChannelWed;
    private String rateChannelThurs;
    private String rateChannelFri;
    private String rateChannelSat;
    private String overrideChannelName;
    private Date overrideStartDate;
    private Date overrideEndDate;
    private String productName;

    public String getCategory() {
        return category;
    }

    public String getRateChannelMon() {
        return rateChannelMon;
    }

    public String getRateChannelTues() {
        return rateChannelTues;
    }

    public String getRateChannelWed() {
        return rateChannelWed;
    }

    public String getRateChannelThurs() {
        return rateChannelThurs;
    }

    public String getRateChannelFri() {
        return rateChannelFri;
    }

    public String getRateChannelSat() {
        return rateChannelSat;
    }

    public String getRateChannelSun() {
        return rateChannelSun;
    }

    public String getOverrideChannelName() {
        return overrideChannelName;
    }

    public Date getOverrideStartDate() {
        return overrideStartDate;
    }

    public Date getOverrideEndDate() {
        return overrideEndDate;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public void setRateChannelMon(String rateChannelMon) {
        this.rateChannelMon = rateChannelMon;
    }

    public void setRateChannelTues(String rateChannelTues) {
        this.rateChannelTues = rateChannelTues;
    }

    public void setRateChannelWed(String rateChannelWed) {
        this.rateChannelWed = rateChannelWed;
    }

    public void setRateChannelThurs(String rateChannelThurs) {
        this.rateChannelThurs = rateChannelThurs;
    }

    public void setRateChannelFri(String rateChannelFri) {
        this.rateChannelFri = rateChannelFri;
    }

    public void setRateChannelSat(String rateChannelSat) {
        this.rateChannelSat = rateChannelSat;
    }

    public void setRateChannelSun(String rateChannelSun) {
        this.rateChannelSun = rateChannelSun;
    }

    public void setOverrideChannelName(String overrideChannelName) {
        this.overrideChannelName = overrideChannelName;
    }

    public void setOverrideStartDate(Date overrideStartDate) {
        this.overrideStartDate = overrideStartDate;
    }

    public void setOverrideEndDate(Date overrideEndDate) {
        this.overrideEndDate = overrideEndDate;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
}
