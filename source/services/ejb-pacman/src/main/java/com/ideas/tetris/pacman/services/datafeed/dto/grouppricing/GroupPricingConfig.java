package com.ideas.tetris.pacman.services.datafeed.dto.grouppricing;

import java.math.BigDecimal;

public class GroupPricingConfig {

    private String roomClass;
    private BigDecimal roomServicingCost;

    public GroupPricingConfig(String roomClass, BigDecimal roomServicingCost) {
        this.roomClass = roomClass;
        this.roomServicingCost = roomServicingCost;
    }

    public String getRoomClass() {
        return roomClass;
    }

    public BigDecimal getRoomServicingCost() {
        return roomServicingCost;
    }

    public void setRoomClass(String roomClass) {
        this.roomClass = roomClass;
    }

    public void setRoomServicingCost(BigDecimal roomServicingCost) {
        this.roomServicingCost = roomServicingCost;
    }
}
