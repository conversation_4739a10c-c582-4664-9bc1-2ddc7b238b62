package com.ideas.tetris.pacman.services.purge;

import com.ideas.sas.core.SASClientResponse;
import com.ideas.sas.service.SASClientService;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.sas.log.SASNodeLocator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import static com.ideas.tetris.pacman.common.constants.Constants.LIST_FILE;
import static com.ideas.tetris.pacman.common.constants.Constants.SRC_PATH;
import static java.lang.String.format;

@Component
@Transactional
public class SASDataSetLocator {

    public static final String SAS_DATASET_FILE_EXTENSION = ".sas7bdat";
    @Autowired
	private SASNodeLocator sasNodeLocator;
    @Autowired
	private SASClientService sasClientService;

    public File getDataSetFolder(Integer propertyId) {
        String dataSetPath = sasNodeLocator.determineSasAnalyticsDataSetLocationForJBossNodes(propertyId);
        File dataSetFolder = new File(dataSetPath);
        if (!dataSetFolder.exists() || !dataSetFolder.isDirectory()) {
            throw new TetrisException(ErrorCode.GFT_FILE_ERROR, "SAS dataset folder " + dataSetPath + " does not exist or is not a directory");
        }
        return dataSetFolder;
    }

    public String getSASDataSetFolder(Integer propertyId) {
        String dataSetPath = sasNodeLocator.determineSasAnalyticsDataSetLocationForSASNodes(propertyId);
        return dataSetPath;
    }


    public File getPartitionDataSetsFolder(Integer propertyId) {
        String dataSetPath = sasNodeLocator.determineSasAnalyticsDataSetLocationForJBossNodes(propertyId);
        File partitionsDataSetFolder = new File(dataSetPath, "partitions");
        if (!partitionsDataSetFolder.exists() || !partitionsDataSetFolder.isDirectory()) {
            throw new TetrisException(ErrorCode.GFT_FILE_ERROR, format("SAS dataset folder %s/partitions does not exist or is not a directory", dataSetPath));
        }
        return partitionsDataSetFolder;
    }

    public String getSASPartitionDataSetsFolder(Integer propertyId) {
        String partitionsDataSetFolder = sasNodeLocator.determineSasAnalyticsDataSetLocationForSASNodes(propertyId) + File.separator + "partitions" + File.separator;
        return partitionsDataSetFolder;
    }

    public String getSASOptCsvFolder(Integer propertyId) {
        return sasNodeLocator.determineSasAnalyticsDataSetLocationForSASNodes(propertyId) + File.separator + "csv" + File.separator;
    }

    public String getSASOptTempFolder(Integer propertyId) {
        return sasNodeLocator.determineSasAnalyticsDataSetLocationForSASNodes(propertyId) + File.separator + "purge_temp" + File.separator;
    }

    public String getListOfFileNamesFromSASNode(String sasDatasetPath) {
        SASClientResponse sasClientResponse = sasClientService.executeFileOps(LIST_FILE, new HashMap<>(Map.of(SRC_PATH, sasDatasetPath)));
        return sasClientResponse.getResponse();
    }

    public boolean dataSetFileExists(String dataSet, File dataSetFolder) {
        String fileName = dataSet.toLowerCase() + SAS_DATASET_FILE_EXTENSION;
        File file = new File(dataSetFolder, fileName);
        return file.exists();
    }

}
