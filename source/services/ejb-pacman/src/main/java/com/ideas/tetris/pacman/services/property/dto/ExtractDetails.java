package com.ideas.tetris.pacman.services.property.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.joda.time.LocalDate;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;

@SuppressWarnings("serial")
public class ExtractDetails extends AbstractExtractDetails implements Extractable, Serializable {
    public static final String FILE_PART_TAR = "tar";
    public static final String FILE_PART_BALL = "Z";
    public static final String FILE_PART_BALL_ZIP = "zip";
    public static final String FILE_PART_HISTORY = "history";
    public static final int SEGMENT_HISTORY = 3;

    private DateParameter historicalExtractDate;
    private DateParameter firstArchivedExtractDate;
    private DateParameter lastArchivedExtractDate;
    private List<DateParameter> incomingExtractDates = new ArrayList<DateParameter>();
    private List<DateParameter> missingDates = new ArrayList<DateParameter>();
    private int totalExtracts = 0;
    private boolean srpAttributeExtractAvailable = false;


    public List<String> getArchivedFilesForDate(LocalDate date) {
        return archivedExtracts.get(date.toDate());
    }

    @JsonIgnore
    public void setSrpAttributeExtractDateAvailability(Date date) {
        List<String> paths = incomingExtracts.get(date);
        if (CollectionUtils.isNotEmpty(paths)) {
            srpAttributeExtractAvailable = true;
        }
    }

    public void setArchivedExtracts(Map<Date, List<String>> archivedExtracts) {   // do not call
        this.archivedExtracts = archivedExtracts;
    }

    @Override
    public void copyIncomingExtractsToDirectory(Date startDate, Date endDate, File directory) throws IOException {
        for (File extract : getIncomingExtracts()) {
            CrsExtractFile crsExtractFile = new CrsExtractFile(extract.getPath());
            Date extractDate = crsExtractFile.getDate();
            if (extractDate.before(startDate) || extractDate.after(endDate)) {
                continue;
            }
            FileUtils.copyFileToDirectory(extract, directory);
        }
    }

    @Override
    public void copyArchivedExtractsToDirectory(Date startDate, Date endDate, File directory) throws IOException {
        for (File extract : getArchivedExtracts()) {
            CrsExtractFile crsExtractFile = new CrsExtractFile(extract.getPath());
            Date extractDate = crsExtractFile.getDate();
            if (extractDate.before(startDate) || extractDate.after(endDate)) {
                continue;
            }
            FileUtils.copyFileToDirectory(extract, directory);
        }
    }

    @Override
    public void postConstruct() {
        historicalExtractDate = null;
        firstIncomingExtractDate = null;
        firstArchivedExtractDate = null;
        lastIncomingExtractDate = null;
        lastArchivedExtractDate = null;
        totalExtracts = 0;

        if (!incomingExtracts.isEmpty()) {
            List<Date> sortedDates = new ArrayList<Date>(incomingExtracts.keySet());
            Collections.sort(sortedDates);
            buildIncomingExtractDates(sortedDates);
            firstIncomingExtractDate = new DateParameter(sortedDates.get(0));
            lastIncomingExtractDate = new DateParameter(sortedDates.get(sortedDates.size() - 1));
        }
        if (!archivedExtracts.isEmpty()) {
            List<Date> sortedDates = new ArrayList<Date>(archivedExtracts.keySet());
            Collections.sort(sortedDates);
            firstArchivedExtractDate = new DateParameter(sortedDates.get(0));
            lastArchivedExtractDate = new DateParameter(sortedDates.get(sortedDates.size() - 1));
        }
        List<String> allPaths = buildAllPaths();
        totalExtracts = allPaths.size();
        Date previousDate = null;
        for (String path : allPaths) {
            Date currentDate = deriveDate(path);
            if (historicalExtractDate == null && path.endsWith(".history.tar.Z")) {
                historicalExtractDate = new DateParameter(currentDate);
            }
            if (currentDate != null && currentDate.equals(previousDate)) {
                Date realDate = DateUtil.addDaysToDate(currentDate, -1);
                if (missingDates.contains(new DateParameter(realDate))) {
                    missingDates.remove(new DateParameter(realDate));
                }
            } else {
                missingDates.addAll(getMissingDates(previousDate, currentDate));
                previousDate = currentDate;
            }
        }
    }

    @Override
    protected void clearHelperVariables() {
        // Clear the helper lists dates
        // they will be rebuilt by the postConstruct call
        incomingExtractDates.clear();

        numberOfIncomingExtracts = 0;
        numberOfArchivedExtracts = 0;
    }

    @JsonIgnore
    protected List<DateParameter> getMissingDates(Date previousDate, Date currentDate) {
        List<DateParameter> missing = new ArrayList<DateParameter>();
        if (previousDate == null || currentDate == null || currentDate.before(previousDate)) {
            return missing;
        }
        Date dateToCompare = DateUtil.addDaysToDate(currentDate, -1);
        while (dateToCompare.after(previousDate)) {
            missing.add(new DateParameter(dateToCompare));
            dateToCompare = DateUtil.addDaysToDate(dateToCompare, -1);
        }
        return missing;
    }

    private void buildIncomingExtractDates(List<Date> sortedDates) {
        incomingExtractDates.clear();
        for (Date date : sortedDates) {
            incomingExtractDates.add(new DateParameter(date));
        }
    }

    private List<String> buildAllPaths() {
        List<String> allPaths = new ArrayList<String>();
        for (List<String> paths : incomingExtracts.values()) {
            allPaths.addAll(paths);
            numberOfIncomingExtracts += paths.size();
        }
        for (List<String> paths : archivedExtracts.values()) {
            allPaths.addAll(paths);
            numberOfArchivedExtracts += paths.size();
        }
        Collections.sort(allPaths, new PathComparator());
        return allPaths;
    }

    public DateParameter getFirstArchivedExtractDate() {
        return firstArchivedExtractDate;
    }

    public void setFirstArchivedExtractDate(DateParameter firstArchivedExtractDate) {  // do not call
        this.firstArchivedExtractDate = firstArchivedExtractDate;
    }

    public DateParameter getLastArchivedExtractDate() {
        return lastArchivedExtractDate;
    }

    public void setLastArchivedExtractDate(DateParameter lastArchivedExtractDate) {
        this.lastArchivedExtractDate = lastArchivedExtractDate;
    }

    public DateParameter getHistoricalExtractDate() {
        return historicalExtractDate;
    }

    public void setHistoricalExtractDate(DateParameter historicalExtractDate) {  // do not call
        this.historicalExtractDate = historicalExtractDate;
    }

    public int getTotalExtracts() {
        return totalExtracts;
    }

    public void setTotalExtracts(int totalExtracts) {
        this.totalExtracts = totalExtracts;
    }

    public List<DateParameter> getIncomingExtractDates() {
        return incomingExtractDates;
    }

    public void setIncomingExtractDates(List<DateParameter> incomingExtractDates) {
        this.incomingExtractDates = incomingExtractDates;
    }

    public List<DateParameter> getMissingDates() {
        return missingDates;
    }

    public void setMissingDates(List<DateParameter> missingDates) {
        this.missingDates = missingDates;
    }

    private List<String> getRecentIncomingExtracts(int numberOfExtracts) {
        List<String> extracts = new ArrayList<String>();
        if (numberOfIncomingExtracts > 0) {
            int endIndex = numberOfIncomingExtracts;
            int startIndex = 0;
            if (numberOfIncomingExtracts >= numberOfExtracts) {
                startIndex = endIndex - numberOfExtracts;
            }
            List<String> incomingPaths = getIncomingPaths();
            if (CollectionUtils.isNotEmpty(incomingPaths)) {
                extracts.addAll(incomingPaths.subList(startIndex, endIndex));
            }
        }
        return extracts;
    }

    private List<String> getRecentArchivedExtracts(int numberOfExtracts) {
        List<String> extracts = new ArrayList<String>();
        if (numberOfArchivedExtracts > 0) {
            int endIndex = numberOfArchivedExtracts;
            int startIndex = 0;
            if (numberOfArchivedExtracts >= numberOfExtracts) {
                startIndex = endIndex - numberOfExtracts;
            }
            List<String> archivedPaths = getArchivedPaths();
            if (CollectionUtils.isNotEmpty(archivedPaths)) {
                extracts.addAll(archivedPaths.subList(startIndex, endIndex));
            }
        }
        return extracts;
    }

    public boolean isSrpAttributeExtractAvailable() {
        return srpAttributeExtractAvailable;
    }

    public boolean checkIfNotExists(File extract) {
        Date date = deriveDate(extract.getName());
        return archivedExtracts.get(date) == null
                || !archivedExtracts.get(date).contains(extract.getAbsolutePath());
    }

    class PathComparator implements Comparator<String> {
        @Override
        public int compare(String o1, String o2) {
            File file1 = new File(o1);
            File file2 = new File(o2);
            return file1.getName().compareTo(file2.getName());
        }
    }
}
