package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.tetris.pacman.services.activity.converter.ActivityConverter;
import com.ideas.tetris.pacman.services.activity.converter.RoomTypeHotelMarketSegmentActivityConverter;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.HotelMktSegAccomActivity;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * Provides REST access to the HotelMktSegAccomActivity information. It extends the
 * ActivityService to provide the standard set of endpoints used by the activity
 * data.
 */
@Component
@Transactional
public class RoomTypeHotelMarketSegmentActivityService extends ActivityService<HotelMktSegAccomActivity> {
    private static final Logger LOGGER = Logger.getLogger(RoomTypeHotelMarketSegmentActivityService.class);

    @Autowired
	private RoomTypeHotelMarketSegmentActivityConverter roomTypeHotelMarketSegmentActivityConverter;

    @Override
    protected Comparator<Map<String, Object>> getComparator() {
        return null;
    }

    @Override
    public List<Map<String, Object>> save(List<Map<String, Object>> dtos, String correlationId, boolean isPast) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Saving " + dtos + " as Entity: " + getEntityClass());
        }

        // Convert the DTOs to entities and save them
        saveEntities(dtos, correlationId, isPast);

        // Convert the entities into DTOs
        return dtos;
    }

    @Override
    protected List<HotelMktSegAccomActivity> saveEntities(List<Map<String, Object>> dtos, String correlationId, boolean isPast) {
        LOGGER.info("Starting save of entities: " + dtos.size());
        List<HotelMktSegAccomActivity> entities = getConverter().convertFromMapAll(dtos, correlationId);

        LOGGER.info("About to save entities to database: " + dtos.size());

        tenantCrudService.save(entities);

        LOGGER.info("Completed save of entities: " + dtos.size());
        return entities;
    }

    @Override
    protected Class<HotelMktSegAccomActivity> getEntityClass() {
        return HotelMktSegAccomActivity.class;
    }

    @Override
    protected ActivityConverter<HotelMktSegAccomActivity> getConverter() {
        return roomTypeHotelMarketSegmentActivityConverter;
    }

    @Override
    protected String getDateRangeQuery() {
        return HotelMktSegAccomActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID;
    }

    @Override
    protected String deleteDateRangeQuery() {
        return HotelMktSegAccomActivity.DELETE_BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID;
    }
}
