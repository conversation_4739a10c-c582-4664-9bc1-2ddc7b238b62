package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.bestavailablerate.PrettyPricingService;
import com.ideas.tetris.pacman.services.bestavailablerate.PricingDigit;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PrettyPricingRuleRow;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.CPBARRoundingRulesConfiguration;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.platform.common.crudservice.CrudService;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class CPBarRoundingRulesConfigurationService {

    @Autowired
    PrettyPricingService prettyPricingService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    public static final String ALL_NUMBERS = "0,1,2,3,4,5,6,7,8,9";

    public List<CPBARRoundingRulesConfiguration> getCPBARRoundingRulesConfiguration() {

        List<CPBARRoundingRulesConfiguration> cpbarRoundingRulesConfigurationList = new ArrayList<>();
        List<Product> products = crudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_INDEPENDENT_PRODUCTS);
        products.forEach(product -> {
            Map<PricingDigit, PrettyPricingRuleRow> pricingDigitPrettyPricingRuleRowEntry = getPricingDigitPrettyPricingRuleRowMap(product.getId());
            if (!pricingDigitPrettyPricingRuleRowEntry.isEmpty()) {
                CPBARRoundingRulesConfiguration cpbarRoundingRulesConfiguration = new CPBARRoundingRulesConfiguration();
                setRulesForAllDigits(cpbarRoundingRulesConfiguration, pricingDigitPrettyPricingRuleRowEntry, ALL_NUMBERS);
                cpbarRoundingRulesConfiguration.setProductName(product.getName());
                cpbarRoundingRulesConfigurationList.add(cpbarRoundingRulesConfiguration);
            }
        });
        return cpbarRoundingRulesConfigurationList;
    }

    public Map<PricingDigit, PrettyPricingRuleRow> getPricingDigitPrettyPricingRuleRowMap() {
        //Hard coded to System Default Product
        return prettyPricingService.getPricingRule(1).getRules();
    }

    public Map<PricingDigit, PrettyPricingRuleRow> getPricingDigitPrettyPricingRuleRowMap(Integer productId) {
        return prettyPricingService.getPricingRule(productId).getRules();
    }

    public void setRulesForAllDigits(CPBARRoundingRulesConfiguration cpbarRoundingRulesConfiguration, Map<PricingDigit, PrettyPricingRuleRow> pricingDigitPrettyPricingRuleRowEntry, String defaultRule) {
        cpbarRoundingRulesConfiguration.setTenThousands(getValue(pricingDigitPrettyPricingRuleRowEntry, PricingDigit.TEN_THOUSANDS, defaultRule));
        cpbarRoundingRulesConfiguration.setThousands(getValue(pricingDigitPrettyPricingRuleRowEntry, PricingDigit.THOUSANDS, defaultRule));
        cpbarRoundingRulesConfiguration.setHundreds(getValue(pricingDigitPrettyPricingRuleRowEntry, PricingDigit.HUNDREDS, defaultRule));
        cpbarRoundingRulesConfiguration.setTens(getValue(pricingDigitPrettyPricingRuleRowEntry, PricingDigit.TENS, defaultRule));
        cpbarRoundingRulesConfiguration.setOnes(getValue(pricingDigitPrettyPricingRuleRowEntry, PricingDigit.ONES, defaultRule));
        cpbarRoundingRulesConfiguration.setTenths(getValue(pricingDigitPrettyPricingRuleRowEntry, PricingDigit.TENTHS, defaultRule));
        cpbarRoundingRulesConfiguration.setHundredths(getValue(pricingDigitPrettyPricingRuleRowEntry, PricingDigit.HUNDREDTHS, defaultRule));
    }

    private String getValue(Map<PricingDigit, PrettyPricingRuleRow> pricingDigitPrettyPricingRuleRowEntry, PricingDigit pricingDigit, String defaultRule) {
        return pricingDigitPrettyPricingRuleRowEntry.containsKey(pricingDigit) ? pricingDigitPrettyPricingRuleRowEntry.get(pricingDigit).getRuleNumbers() : defaultRule;
    }

    public CPBARRoundingRulesConfiguration getBARRoundingRules() {
        Map<PricingDigit, PrettyPricingRuleRow> pricingDigitPrettyPricingRuleRowEntry = getPricingDigitPrettyPricingRuleRowMap();
        CPBARRoundingRulesConfiguration cpbarRoundingRulesConfiguration = new CPBARRoundingRulesConfiguration();
        setRulesForAllDigits(cpbarRoundingRulesConfiguration, pricingDigitPrettyPricingRuleRowEntry, "All");
        return cpbarRoundingRulesConfiguration;
    }

    @ForTesting
    public void setPrettyPricingService(PrettyPricingService prettyPricingService) {
        this.prettyPricingService = prettyPricingService;
    }
}
