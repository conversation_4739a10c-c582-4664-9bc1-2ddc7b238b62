package com.ideas.tetris.pacman.services.datafeed.dto.pricingdata;


public class ProductRateShopDefinitionDTO {

    private String productName;

    private Integer minShoppedLOS;

    private Integer maxShoppedLOS;

    private String rateShoppingRateType;

    public ProductRateShopDefinitionDTO(String productName, Integer minShoppedLOS, Integer maxShoppedLOS, String rateShoppingRateType) {
        this.productName = productName;
        this.minShoppedLOS = minShoppedLOS;
        this.maxShoppedLOS = maxShoppedLOS;
        this.rateShoppingRateType = rateShoppingRateType;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getMinShoppedLOS() {
        return minShoppedLOS;
    }

    public void setMinShoppedLOS(Integer minShoppedLOS) {
        this.minShoppedLOS = minShoppedLOS;
    }

    public Integer getMaxShoppedLOS() {
        return maxShoppedLOS;
    }

    public void setMaxShoppedLOS(Integer maxShoppedLOS) {
        this.maxShoppedLOS = maxShoppedLOS;
    }

    public String getRateShoppingRateType() {
        return rateShoppingRateType;
    }

    public void setRateShoppingRateType(String rateShoppingRateType) {
        this.rateShoppingRateType = rateShoppingRateType;
    }
}
