package com.ideas.tetris.pacman.services.analytics.services;

import com.ideas.tetris.pacman.services.analytics.dto.BookingCurve;
import com.ideas.tetris.pacman.services.analytics.dto.BookingCurveEvent;
import com.ideas.tetris.pacman.services.analytics.dto.BookingCurveOverride;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class BookingCurveService {
    @Autowired
    private SasDbToolService sasDbToolService;
    @TenantCrudServiceBean.Qualifier
    @Qualifier("tenantCrudServiceBean")
    @Autowired
    private CrudService tenantCrudService;

    private String toCommaSeparatedString(Collection<Integer> data) {
        return data.stream().map(String::valueOf).collect(Collectors.joining(","));
    }

    public static final String FETCH_BOOKING_CURVE_BY_SEASON_AND_LOS =
            "SELECT bc.process_grp_id   AS processGroupId,\n" +
                    "       bc.lump_id          AS forecastGroupId,\n" +
                    "       bc.room_category_id AS roomClassId,\n" +
                    "       bc.dow_grp_num      AS dowGroupNumber,\n" +
                    "       bc.season_grp_num   AS seasonGroupNumber,\n" +
                    "       bc.horizon_grp_num  AS horizonGroupNumber,\n" +
                    "       bc.value            AS value\n" +
                    "FROM   tenant.booking_curve bc\n" +
                    "WHERE  bc.season_grp_num IN (:seasonGroupNumber)\n" +
                    "   AND bc.lump_id IN (:forecastGroupIds)\n" +
                    "   AND bc.room_category_id IN (:accomClassIds)\n" +
                    "   AND bc.los = :los;";

    public static final String FETCH_BOOKING_EVENT_CURVE_BY_EVENT_IDS =
            "SELECT bc.process_grp_id   AS processGroupId,\n" +
                    "       bc.lump_id          AS forecastGroupId,\n" +
                    "       bc.room_category_id AS roomClassId,\n" +
                    "       bc.event_id      AS eventId,\n" +
                    "       bc.horizon_grp_num      AS horizonGroupNumber,\n" +
                    "       bc.value            AS value\n" +
                    "FROM   tenant.booking_curve_event bc\n" +
                    "WHERE  bc.event_id IN (:eventId)\n" +
                    "   AND bc.lump_id IN (:forecastGroupIds)\n" +
                    "   AND bc.room_category_id IN (:accomClassIds)\n" +
                    "   AND bc.los = :los;";

        public static String FETCH_BOOKING_CURVE_OVERRIDE =
                    "SELECT obcp.forecast_group_id    AS forecastGroupId,\n" +
                "       obcp.accom_class_id       AS accomClassId,\n" +
                "       obcp.start_date           AS startDate,\n" +
                "       obcp.end_date             AS endDate,\n" +
                "       obcp.los                  AS los,\n" +
                "       obcp.reading_group_number AS readingGroupNumber,\n" +
                "       obcp.value                AS value\n"+
                " FROM  ovrd_booking_curve_pattern obcp\n" +
                " INNER JOIN forecast_group fg ON fg.forecast_group_id = obcp.forecast_group_id\n" +
                " INNER JOIN accom_class ac ON ac.accom_class_id = obcp.accom_class_id\n" +
                " WHERE obcp.los = :los\n" +
                "   AND fg.status_id = 1\n" +
                "   AND ac.status_id = 1\n" +
                "   AND obcp.status_id = 1\n" +
                "   AND obcp.start_date <= :endDate\n" +
                "   AND obcp.end_date >= :startDate";

    public List<BookingCurve> getBookingCurve(List<Integer> seasonGroupNumbers, Collection<Integer> distinctForecastGroupIds, Collection<Integer> distinctAccomClassIds, int los) {
        String query = FETCH_BOOKING_CURVE_BY_SEASON_AND_LOS
                .replace(":seasonGroupNumber", toCommaSeparatedString(seasonGroupNumbers))
                .replace(":forecastGroupIds", toCommaSeparatedString(distinctForecastGroupIds))
                .replace(":accomClassIds", toCommaSeparatedString(distinctAccomClassIds))
                .replace(":los", String.valueOf(los));
        return sasDbToolService.executeQuery(query, BookingCurve.class);
    }

    public List<BookingCurveEvent> getBookingCurveEvent(List<Integer> eventIds, Collection<Integer> distinctForecastGroupIds, Collection<Integer> distinctAccomClassIds, int los) {
        if (eventIds.isEmpty()) {
            return List.of();
        }
        String query = FETCH_BOOKING_EVENT_CURVE_BY_EVENT_IDS
                .replace(":eventId", toCommaSeparatedString(eventIds))
                .replace(":forecastGroupIds", toCommaSeparatedString(distinctForecastGroupIds))
                .replace(":accomClassIds", toCommaSeparatedString(distinctAccomClassIds))
                .replace(":los", String.valueOf(los));
        return sasDbToolService.executeQuery(query, BookingCurveEvent.class);
    }

    public List<BookingCurveOverride> getBookingCurveOverride(LocalDate startDate, LocalDate endDate, int los) {
        List<Object[]> result = tenantCrudService.findByNativeQuery(FETCH_BOOKING_CURVE_OVERRIDE, QueryParameter.with("startDate", startDate.format(DateTimeFormatter.ISO_DATE))
                .and("endDate", endDate.format(DateTimeFormatter.ISO_DATE)).and("los", los).parameters());
        Function<Object[], BookingCurveOverride> toBookingCurveOverride = o -> BookingCurveOverride.builder().forecastGroupId((Integer) o[0])
                .accomClassId((Integer) o[1]).startDate(((Date) o[2]).toLocalDate()).endDate(((Date) o[3]).toLocalDate()).los((Short) o[4])
                .readingGroupNumber((Integer) o[5]).value((Double) o[6]).build();
        return result.stream().map(toBookingCurveOverride).collect(Collectors.toList());
    }

}
