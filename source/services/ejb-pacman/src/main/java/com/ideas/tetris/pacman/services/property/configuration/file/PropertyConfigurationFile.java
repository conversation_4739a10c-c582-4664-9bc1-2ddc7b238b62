package com.ideas.tetris.pacman.services.property.configuration.file;

import com.google.common.io.Files;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.MetaDataPropertyConfigurationDto;
import org.apache.commons.lang.StringUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class PropertyConfigurationFile {
    public static final String HEADER = "_HEADER_";
    public static final String FOOTER = "_FOOTER_";

    private File configurationFile;
    private String error = null;
    private long totalRecords = 0;

    private MetaDataPropertyConfigurationDto metaDataRecord;
    private Set<String> propertyCodes = new HashSet<>();
    private Map<String, List<String>> recordMap = new HashMap<>();

    public PropertyConfigurationFile(File configurationFile) {
        this(configurationFile, false);
    }

    @SuppressWarnings("squid:S1166")
    public PropertyConfigurationFile(File configurationFile, boolean loadAllRecords) {
        if (configurationFile == null || !configurationFile.exists()) {
            error = "Configuration file " + configurationFile + " does not exist!";
        } else {
            this.configurationFile = configurationFile;
            try {
                loadFile(loadAllRecords);
            } catch (IOException e) {
                error = e.getMessage();
            }
        }
    }

    public File getConfigurationFile() {
        return configurationFile;
    }

    public boolean isValid() {
        return error == null;
    }

    public String getError() {
        return error;
    }

    public MetaDataPropertyConfigurationDto getMetaDataRecord() {
        return metaDataRecord;
    }

    public Set<String> getPropertyCodes() {
        return propertyCodes;
    }

    public int getTotalProperties() {
        return propertyCodes.size();
    }

    public long getTotalRecords() {
        return totalRecords;
    }

    public List<String> getRecordsForProperty(String propertyCode) {
        return recordMap.get(propertyCode) != null ? recordMap.get(propertyCode) : new ArrayList<>();
    }

    private void loadFile(boolean loadAll) throws IOException {
        BufferedReader reader = null;
        String line;
        totalRecords = 0;
        try {
            reader = Files.newReader(configurationFile, Charset.defaultCharset());
            line = reader.readLine();
            if (line != null && HEADER.equals(line)) {
                line = reader.readLine();
            }

            parseMetaDataRecord(line);

            while ((line = reader.readLine()) != null) {
                if (StringUtils.isNotEmpty(line)) {
                    parseRecord(line, loadAll);
                }
            }

            // after loading everything, get rid of any records that are in the map for property codes
            // that are not in the property code list built from the PC records
            Set<String> keys = new HashSet<>();
            keys.addAll(recordMap.keySet());
            for (String propertyCode : keys) {
                if (!propertyCodes.contains(propertyCode)) {
                    recordMap.remove(propertyCode);
                }
            }
        } finally {
            if (reader != null) {
                reader.close();
            }
        }
    }

    private void parseRecord(String record, boolean loadAll) throws IOException {
        String[] fields = StringUtils.splitPreserveAllTokens(record, PropertyConfigurationDto.DELIMITER);
        if (fields.length < 2) { // at a minimum, we need to be able to determine the record type and property code
            throw new IOException("Unable to parse record type or property code for record: " + record);
        }
        PropertyConfigurationRecordType recordType = PropertyConfigurationRecordType.valueOfRecordType(fields[0]);
        String propertyCode = fields[1];
        if (PropertyConfigurationRecordType.PC.equals(recordType)) {
            propertyCodes.add(propertyCode);
        }
        if (loadAll) {
            List<String> propertyRecords = recordMap.get(propertyCode);
            if (propertyRecords == null) {
                propertyRecords = new ArrayList<>();
                recordMap.put(propertyCode, propertyRecords);
            }
            propertyRecords.add(record);
        }
        totalRecords++;
    }

    private void parseMetaDataRecord(String record) throws IOException {
        if (!StringUtils.isBlank(record)) {
            String[] fields = StringUtils.splitPreserveAllTokens(record, PropertyConfigurationDto.DELIMITER);

            if (fields.length > 0 && PropertyConfigurationRecordType.CONFIG_META.getRecordType().equals(fields[0])) {
                metaDataRecord = new MetaDataPropertyConfigurationDto();
                metaDataRecord.setRecordFields(fields);
                totalRecords++;
            }
        }
        if (metaDataRecord == null) {
            throw new IOException("First line of the configuration file " + configurationFile
                    + " is not a valid Configuration MetaData record.");
        }
    }

}
