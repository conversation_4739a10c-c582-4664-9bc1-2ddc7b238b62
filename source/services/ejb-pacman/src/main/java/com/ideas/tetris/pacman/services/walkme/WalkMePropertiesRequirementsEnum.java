package com.ideas.tetris.pacman.services.walkme;

public enum WalkMePropertiesRequirementsEnum {
    PROPERTY_FOR_RC_GSC_MS("PropertyForRcGscMs", new String[]{"Rooms Configuration", "Group Status Code", "Market Segment Configuration"}),
    PROPERTY_FOR_RPC_CCFG("PropertyForRpCCFG", new String[]{"Rate Plan Configuration", "CCFG"}),
    PROPERTY_FOR_RPC_RS_GPC("PropertyForRpRsGpc", new String[]{"Rate Plan Configuration", "Rate Shopping", "Group Pricing Configuration"}),
    PROPERTY_FOR_PC_GPC("PropertyForPcGp", new String[]{"Pricing Configuration", "Group Pricing Configuration"});

    private String property;
    private String[] requirements;

    WalkMePropertiesRequirementsEnum(String property, String[] requirements) {
        this.property = property;
        this.requirements = requirements;
    }

    public String getProperty() {
        return property;
    }

    public String[] getRequirements() {
        return requirements;
    }
}
