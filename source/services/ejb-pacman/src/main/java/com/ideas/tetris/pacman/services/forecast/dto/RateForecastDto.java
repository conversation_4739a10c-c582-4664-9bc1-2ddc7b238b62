package com.ideas.tetris.pacman.services.forecast.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RateForecastDto {
    private Integer processGrpId ;
    private Integer lumpId;
    private Integer roomCategoryId;
    private LocalDate arrivalDt;
    private Integer los;
    private Double rate ;
}
