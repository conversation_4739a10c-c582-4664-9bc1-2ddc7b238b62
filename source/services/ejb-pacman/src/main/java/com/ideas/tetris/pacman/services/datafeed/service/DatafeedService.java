package com.ideas.tetris.pacman.services.datafeed.service;


import com.ideas.tetris.pacman.common.configparams.*;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.service.AccomClassPriceRankService;
import com.ideas.tetris.pacman.services.activity.service.MarketSegmentActivityService;
import com.ideas.tetris.pacman.services.activity.service.Pageable;
import com.ideas.tetris.pacman.services.bestavailablerate.PrettyPricingService;
import com.ideas.tetris.pacman.services.budget.BudgetDataService;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.*;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.D360BookingSummaryDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.D360CompCapacityDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.ReservationNightDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.WebrateDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.GroupPricingSCMarketSegmentMapping;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.GroupPricingSCRoomTypeMapping;
import com.ideas.tetris.pacman.services.datafeed.endpoint.DatafeedEndPointCriteria;
import com.ideas.tetris.pacman.services.datafeed.endpoint.EndpointBucket;
import com.ideas.tetris.pacman.services.datafeed.endpoint.EndpointFrequencyType;
import com.ideas.tetris.pacman.services.datafeed.entity.*;
import com.ideas.tetris.pacman.services.datafeed.service.pricingstrategy.ArrivalLOSService;
import com.ideas.tetris.pacman.services.datafeed.service.pricingstrategy.PricingStrategyService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.extendedstay.config.service.ESAProductDefinitionConfigurationService;
import com.ideas.tetris.pacman.services.forecast.OccupancyForecastService;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpaceConfigurationService;
import com.ideas.tetris.pacman.services.groupflooroverride.GroupFloorOverrideRepository;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.ConferenceAndBanquetService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationConferenceAndBanquetService;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionConfigService;
import com.ideas.tetris.pacman.services.inventorylimit.InventoryLimitDecisionService;
import com.ideas.tetris.pacman.services.limiteddatabuild.LDBService;
import com.ideas.tetris.pacman.services.pricedroprestriction.PriceDropRestrictionService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.property.PropertyConfigParamService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.reports.entity.ScheduleReport;
import com.ideas.tetris.pacman.services.reports.mcatmapping.MCATMappingReportService;
import com.ideas.tetris.pacman.services.reports.outputoverride.OutputOverrideService;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.virtualproperty.service.VirtualPropertyMappingService;
import com.ideas.tetris.pacman.services.webrate.service.RateShoppingConfigService;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterValue;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.Seconds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.*;
import static com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName.*;
import static com.ideas.tetris.pacman.common.constants.Constants.AGILE_RATES_ENABLED;
import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig.shouldUpdateDataDurationBasedOnOffsetFromDB;
import static com.ideas.tetris.pacman.services.datafeed.entity.DatafeedFtpConfig.BY_CLIENT_CODE;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DEFAULT_DATE_FORMAT;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.*;
import static java.lang.Boolean.parseBoolean;
import static java.util.Objects.isNull;

@Component
@Transactional
public class DatafeedService {

    public static final int FIRST_TIME_EXECUTION_YEAR = 1970;
    public static final String CONTEXT_DELIMITER = ".";
    public static final String IS_CLIENT_LEVEL_REQUEST = "isClientLevelRequest";

    @Autowired
    DateService dateService;

    @Autowired
    MCATMappingReportService mcatMappingReportService;

    @Autowired
    MarketSegmentConfigService marketSegmentConfigService;

    @Autowired
    MarketSegmentActivityService marketSegmentActivityService;

    @Autowired
    DecisionConfigurationService decisionConfigurationService;

    @Autowired
    OutputOverrideService outputOverrideService;

    @Autowired
    InputOverrideDataService inputOverrideDataService;

    @Autowired
    DatafeedOverbookingOverrideService datafeedOverbookingOverrideService;

    @Autowired
    RolePermissionService rolePermissionService;

    @Autowired
    UserActivityLogService userActivityLogService;

    @Autowired
    UserDetailsService userDetailsService;

    @Autowired
    SystemHealthDQService systemHealthDQService;

    @Autowired
    RateShoppingDetailsService rateShoppingDetailsService;

    @Autowired
    CostOfWalkConfigService costOfWalkConfigService;

    @Autowired
    RateShoppingAccomMappingService rateShoppingAccomMappingService;

    @Autowired
    RateShoppingIgnoreCompDataService rateShoppingIgnoreCompDataService;

    @Autowired
    CompetitiveChannelService competitiveChannelService;
    @Autowired
    @Qualifier("ldbService")
    LDBService ldbService;

    @Autowired
    BenefitMeasurementsService benefitMeasurementsService;

    @Autowired
    ChannelForecastService channelForecastService;

    @Autowired
    PropertySpecificConfigurationService propertySpecificConfigurationService;

    @Autowired
    ExceptionConfigService exceptionConfigService;

    @Autowired
    PropertyAttributeService propertyAttributeService;

    @Autowired
    PricingStrategyService pricingStrategyService;

    @Autowired
    ArrivalLOSService arrivalLOSService;

    @Autowired
    RateCompetitorMarketPositionConfigService rateCompetitorMarketPositionConfigService;

    @Autowired
    GroupPricingConfigService groupPricingConfigService;

    @Autowired
    GroupPricingBaseRoomTypeRateConfigService groupPricingBaseRoomTypeRateConfig;

    @Autowired
    GroupPricingConfigurationConferenceAndBanquetService groupPricingConfigurationConferenceAndBanquetService;

    @Autowired
    GroupPricingAncillaryConfigService groupPricingAncillaryConfigService;

    @Autowired
    GroupPricingRoomTypeOffsetConfigService groupPricingRoomTypeOffsetConfigService;

    @Autowired
    GroupPricingMinProfitConfigurationService groupPricingMinProfitConfigurationService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    PropertyService propertyService;

    @Autowired
    ESAProductDefinitionConfigurationService esaProductDefinitionConfigurationService;

    @Autowired
    PricingConfigurationService pricingConfigurationService;

    @Autowired
    PrettyPricingService prettyPricingService;

    @Autowired
    CPCeilingFloorTransientConfigurationService cpCeilingFloorTransientConfigurationService;

    @Autowired
    CPSupplementsConfigurationService cpSupplementsConfigurationService;

    @Autowired
    CPBarRoundingRulesConfigurationService cpBarRoundingRulesConfigurationService;

    @Autowired
    InventoryHistoryDataService inventoryHistoryDataService;

    @Autowired
    CPBaseRoomTypeConfigurationService cpBaseRoomTypeConfigurationService;

    @Autowired
    ExtendedStayCompetitorConfigurationService extendedStayCompetitorConfigurationService;

    @Autowired
    InformationManagerNotificationConfigurationService informationManagerNotificationConfigurationService;

    @Autowired
    PropertyBasicInformationService propertyBasicInformationService;

    @Autowired
    GroupFinalForecastOverrideService groupFinalForecastOverrideService;

    @Autowired
    OutOfOrderOverrideService outOfOrderOverrideService;

    @Autowired
    TaxInclusiveConfigurationService taxInclusiveConfigurationService;

    @Autowired
	private AgileRatesDataFeedService agileRatesDataFeedService;

    @Autowired
	private LDBProjectionDatafeedService ldbProjectionDatafeedService;

    @Autowired
	private ProfitDataFeedService profitDataFeedService;

    @Autowired
	private GroupEvaluationDataFeedService groupEvaluationDataFeedService;

    @Autowired
	private OptixDatafeedService optixDatafeedService;

    @Autowired
	private ReservationNightDatafeedService reservationNightDatafeedService;

    @Autowired
	private BudgetDataService budgetDataService;

    @Autowired
	private OperationsForecastArrivalsDeparturesDatafeedService operationsForecastADDatafeedService;

    @Autowired
	private AccomClassPriceRankService accomClassPriceRankService;

    @Autowired
	private VirtualPropertyMappingService virtualPropertyMappingService;

    @Autowired
	private PriceDropRestrictionService priceDropRestrictionService;

    @Autowired
	private ProductRateCodeDefinitionService productRateCodeDefinitionService;

    @Autowired
	private ProductClassificationService productClassificationService;

    @Autowired
	private FunctionSpaceConfigurationService functionSpaceConfigurationService;

    @Autowired
	private GroupFloorOverrideRepository overrideRepository;

    @Autowired
    private RateShoppingConfigService rateShoppingConfigService;

    private static final Logger LOGGER = Logger.getLogger(DatafeedService.class.getName());
    private static final String LITERAL_DATAFEED = "Datafeed ";
    private static final String LITERAL_PAGE = "Page:";
    private static final String LITERAL_SIZE = "Size:";


    private static final int NOT_ROLLING_DATE = 0;
    @Autowired
	private OccupancyForecastService occupancyForecastService;
    @TenantCrudServiceBean.Qualifier
	@Qualifier("tenantCrudServiceBean")
    @Autowired
    private CrudService tenantCrudService;

    @GlobalCrudServiceBean.Qualifier
    @Autowired
    private CrudService globalCrudService;


    @Autowired
    DailyBarInputConfigService dailyBarInputConfigService;

    @Autowired
    private InventorySharingService inventorySharingService;

    @Autowired
    private DatafeedEndPointCriteriaService datafeedEndPointCriteriaService;

    @Autowired
	private DatafeedEndpointService endpointService;
    @Autowired
	private PropertyConfigParamService propertyConfigParamService;
    @Autowired
	private OverbookingConfigurationService overbookingConfigService;

    @Autowired
    private ConferenceAndBanquetService conferenceAndBanquetService;

    @Autowired
    private InventoryLimitDecisionService inventoryLimitDecisionService;

    @Autowired
    private MeetingPackageBaseProductPricingOverridesService meetingPackageBaseProductPricingOverridesService;


    @SuppressWarnings("unchecked")
    public List<Object> get(String datafeedType, DatafeedRequest datafeedRequest) {
        String propertyCode = PacmanWorkContextHelper.getPropertyCode();
        String clientCode = PacmanWorkContextHelper.getClientCode();
        DateTime start = new DateTime();
        datafeedRequest.setDataFeedType(datafeedType);
        updateDataDuration(datafeedRequest, clientCode);
        LOGGER.info(LITERAL_DATAFEED + clientCode + ":" + propertyCode + ", Start:" + datafeedType + ", " + LITERAL_PAGE + datafeedRequest.getPage() + ", " + LITERAL_SIZE + datafeedRequest.getSize());
        boolean toggleForST19DataFeed = false;
        boolean toggleForST2yDataFeed = false;
        if (datafeedType != null && datafeedType.equals("MarketSegmentHistoryST19Dto")) {
            toggleForST19DataFeed = propertyConfigParamService.isOptimizedST19DataFeedFetch();
            LOGGER.info("Toggle for MarketSegmentHistoryST19Dto Optimized Datafeed :" + toggleForST19DataFeed);
        }
        if (datafeedType != null && datafeedType.equals("MarketSegmentHistoryST2YDto")) {
            toggleForST2yDataFeed = propertyConfigParamService.isOptimizedMarketSegmentST2yDataFeedFetch();
            LOGGER.info("Toggle for MarketSegmentHistoryST2yDto Optimized Datafeed :" + toggleForST2yDataFeed);
        }
        Optional<DatafeedQueryEndpoint> optionalEndpoint = DatafeedQueryEndpoint.valueOfSimpleClassName(datafeedType, toggleForST19DataFeed,toggleForST2yDataFeed);


        // Check to see if it's a known entity
        if (optionalEndpoint.isPresent()) {
            DatafeedQueryEndpoint datafeedQueryEndpoint = optionalEndpoint.get();
            CrudService crudService = datafeedQueryEndpoint.isClientLevel() ? globalCrudService : tenantCrudService;
            List resultByNamedQuery = crudService.findByNamedQuery(datafeedQueryEndpoint.getNamedQuery(), buildParameters(datafeedQueryEndpoint, datafeedRequest), datafeedRequest.getStartPosition(), datafeedRequest.getSize());
            LOGGER.info(LITERAL_DATAFEED + clientCode + ":" + propertyCode + ", End-Q:" + datafeedType + ", " + LITERAL_PAGE + datafeedRequest.getPage() + ", " + LITERAL_SIZE + datafeedRequest.getSize() + ", Result Size:" + resultByNamedQuery.size() + ", Time:" + Seconds.secondsBetween(start, new DateTime()).getSeconds() + " sec");
            return resultByNamedQuery;
        }

        DatafeedServiceEndpoint datafeedServiceEndpoint = DatafeedServiceEndpoint.valueOfSimpleClassName(datafeedType);

        if (datafeedServiceEndpoint == null) {
            throw new IllegalArgumentException("Unknown type of Datafeed Data: " + datafeedType);
        }

        List<Object> dataByServiceEndpoint = datafeedServiceEndpoint.getData(this, datafeedRequest);
        LOGGER.info(LITERAL_DATAFEED + clientCode + ":" + propertyCode + ", End-S:" + datafeedType + ", " + LITERAL_PAGE + datafeedRequest.getPage() + ", " + LITERAL_SIZE + datafeedRequest.getSize() + ", Result Size:" + (dataByServiceEndpoint != null ? dataByServiceEndpoint.size() : "empty") + ", Time:" + Seconds.secondsBetween(start, new DateTime()).getSeconds() + " sec");
        return dataByServiceEndpoint;
    }

    public <T> Stream<T> getStreaming(String datafeedType, DatafeedRequest datafeedRequest, WorkContextType context) {
        PacmanWorkContextHelper.setWorkContext(context);
        String propertyCode = context.getPropertyCode();
        String clientCode = context.getClientCode();
        DateTime start = new DateTime();
        datafeedRequest.setDataFeedType(datafeedType);
        updateDataDuration(datafeedRequest, clientCode);
        LOGGER.info(LITERAL_DATAFEED + clientCode + ":" + propertyCode + ", Start:" + datafeedType + ", " + LITERAL_PAGE + datafeedRequest.getPage() + ", " + LITERAL_SIZE + datafeedRequest.getSize());
        boolean toggleForST19DataFeed = false;
        boolean toggleForST2yDataFeed = false;
        if (datafeedType != null && datafeedType.equals("MarketSegmentHistoryST19Dto")) {
            toggleForST19DataFeed = propertyConfigParamService.isOptimizedST19DataFeedFetch();
            LOGGER.info("Toggle for MarketSegmentHistoryST19Dto Optimized Datafeed :" + toggleForST19DataFeed);
        }
        if (datafeedType != null && datafeedType.equals("MarketSegmentHistoryST2YDto")) {
            toggleForST2yDataFeed = propertyConfigParamService.isOptimizedMarketSegmentST2yDataFeedFetch();
            LOGGER.info("Toggle for MarketSegmentHistoryST2yDto Optimized Datafeed :" + toggleForST2yDataFeed);
        }
        Optional<DatafeedQueryEndpoint> optionalEndpoint = DatafeedQueryEndpoint.valueOfSimpleClassName(datafeedType, toggleForST19DataFeed,toggleForST2yDataFeed);


        // Check to see if it's a known entity
        if (optionalEndpoint.isPresent()) {
            DatafeedQueryEndpoint datafeedQueryEndpoint = optionalEndpoint.get();
            CrudService crudService = datafeedQueryEndpoint.isClientLevel() ? globalCrudService : tenantCrudService;
            List resultByNamedQuery = crudService.findByNamedQuery(datafeedQueryEndpoint.getNamedQuery(), buildParameters(datafeedQueryEndpoint, datafeedRequest), datafeedRequest.getStartPosition(), datafeedRequest.getSize());
            LOGGER.info(LITERAL_DATAFEED + clientCode + ":" + propertyCode + ", End-Q:" + datafeedType + ", " + LITERAL_PAGE + datafeedRequest.getPage() + ", " + LITERAL_SIZE + datafeedRequest.getSize() + ", Result Size:" + resultByNamedQuery.size() + ", Time:" + Seconds.secondsBetween(start, new DateTime()).getSeconds() + " sec");
            return resultByNamedQuery.stream();
        }

        var datafeedServiceEndpoint = DatafeedServiceV2Endpoint.valueOfSimpleClassName(datafeedType);

        if (datafeedServiceEndpoint == null) {
            throw new IllegalArgumentException("Unknown type of Datafeed Data: " + datafeedType);
        }

        LOGGER.info(LITERAL_DATAFEED + clientCode + ":" + propertyCode + ", End-S:" + datafeedType + ", " + LITERAL_PAGE + datafeedRequest.getPage() + ", " + LITERAL_SIZE + datafeedRequest.getSize() + ", Time:" + Seconds.secondsBetween(start, new DateTime()).getSeconds() + " sec");
        return datafeedServiceEndpoint.getData(this, datafeedRequest);
    }

    private void updateDataDuration(DatafeedRequest datafeedRequest, String clientCode) {
        if (!StringUtils.isEmpty(datafeedRequest.getDatafeedName()) && shouldUpdateDataDurationBasedOnOffsetFromDB()) {
            Optional.ofNullable(getDatafeedEndpointFor(clientCode, datafeedRequest.getDatafeedName())).ifPresent(endpoint -> updateDuration(datafeedRequest, endpoint));
        }
    }

    private void updateDuration(DatafeedRequest datafeedRequest, DataFeedEndpoint endpoint) {
        final Date caughtUpDate = DateUtil.removeTimeFromDate(dateService.getCaughtUpDate());
        Optional.ofNullable(endpoint.getStartDateOffset()).filter(offset -> offset != 0).ifPresent(offset -> datafeedRequest.setStartDate(DateUtils.addDays(isMissingExtractDatafeedEndpoint(datafeedRequest) ? datafeedRequest.getSystemDate() : caughtUpDate, offset)));
        Optional.ofNullable(endpoint.getEndDateOffset()).filter(offset -> offset != 0).ifPresent(offset -> datafeedRequest.setEndDate(DateUtils.addDays(isMissingExtractDatafeedEndpoint(datafeedRequest) ? datafeedRequest.getSystemDate() : caughtUpDate, offset)));
    }

    public boolean isMissingExtractDatafeedEndpoint(DatafeedRequest datafeedRequest) {
        return isEnableDataFeedForMissingExtracts() && datafeedRequest.isMissingExtract() && null != datafeedRequest.getSystemDate();
    }

    public boolean isEnableDataFeedForMissingExtracts() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_DATA_FEED_FOR_MISSING_EXTRACTS);
    }

    public DataFeedEndpoint getDatafeedEndpointFor(String clientCode, String datafeedName) {
        return globalCrudService.findByNamedQuerySingleResult(DataFeedEndpoint.FETCH_DATAFEED_DETAILS_FOR_DATAFEED_NAME,
                QueryParameter.with("clientCode", clientCode).and("datafeedName", datafeedName).parameters());
    }

    public Map<String, Object> buildParameters(DatafeedQueryEndpoint datafeedQueryEndpoint, DatafeedRequest datafeedRequest) {
        Map<String, Object> parameters = new HashMap<>();

        if (datafeedQueryEndpoint.hasParameter(PROPERTY_ID)) {
            parameters.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        }

        if (datafeedQueryEndpoint.hasParameter(CLIENT_CODE)) {
            parameters.put(CLIENT_CODE, PacmanWorkContextHelper.getClientCode());
        }

        if (datafeedQueryEndpoint.hasParameter(PROPERTY_CODE)) {
            parameters.put(PROPERTY_CODE, PacmanWorkContextHelper.getPropertyCode());
        }

        if (datafeedQueryEndpoint.hasParameter(START_DATE)) {
            parameters.put(START_DATE, getStartDateForHistoryData(datafeedQueryEndpoint, datafeedRequest));
        }

        if (datafeedQueryEndpoint.hasParameter(END_DATE)) {
            parameters.put(END_DATE, datafeedRequest.getEndDate());
        }

        if (datafeedQueryEndpoint.hasParameter(LAST_SUCCESS_DATE)) {
            parameters.put(LAST_SUCCESS_DATE, datafeedRequest.getLastSuccessDate());
        }

        if (datafeedQueryEndpoint.hasParameter(CAUGHT_UP_DATE)) {
            parameters.put(CAUGHT_UP_DATE, DateUtil.formatDate(dateService.getCaughtUpDate(), DEFAULT_DATE_FORMAT));
        }

        if (datafeedQueryEndpoint.hasParameter(AGILE_RATES_ENABLED)) {
            boolean isAgileRatesEnabled = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED);
            parameters.put(AGILE_RATES_ENABLED, isAgileRatesEnabled ? ONE : ZERO);
        }

        if (datafeedQueryEndpoint.hasParameter(DATAFEED_SPECIAL_EVENT_INSTANCE_NAME_ENABLED)) {
            parameters.put(DATAFEED_SPECIAL_EVENT_INSTANCE_NAME_ENABLED, isDatafeedSpecialEventInstanceNameEnabled());
        }

        setExtraParameters(datafeedQueryEndpoint, datafeedRequest, parameters);

        return parameters;
    }

    private int isDatafeedSpecialEventInstanceNameEnabled() {
        boolean isHiltonInstanceNameSupportEnabled = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SPECIAL_EVENT_INSTANCE_NAME_SUPPORT_ENABLED);
        boolean isHiltonEventNameForDatafeedEnabled = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.DATAFEED_SE_INSTANCE_NAME_SUPPORT_ENABLED);
        return isHiltonInstanceNameSupportEnabled && isHiltonEventNameForDatafeedEnabled ? ONE : ZERO;
    }

    private static void setExtraParameters(DatafeedQueryEndpoint datafeedQueryEndpoint, DatafeedRequest datafeedRequest, Map<String, Object> parameters) {
        if (datafeedQueryEndpoint.hasParameter(IS_EXTENDED_WINDOW_ENABLED)) {
            parameters.put(IS_EXTENDED_WINDOW_ENABLED, datafeedRequest.isUseExtendedWindowDecision() ? ONE : ZERO);
        }

        if (datafeedQueryEndpoint.hasParameter(IS_OPTIX_DATAFEED)) {
            parameters.put(IS_OPTIX_DATAFEED, datafeedRequest.isOptixDatafeed() ? ONE : ZERO);
        }
    }

    // This method is specific to consider the property caught date as base date for any historical data for first time request.
    //Note : datafeedRequest.getStartDate() this date is adjusted date @ NGI side as per rest end point. e.g - for Room Type data feed its caught date - 14 days.

    public Date getStartDateForHistoryData(DatafeedQueryEndpoint datafeedQueryEndpoint, DatafeedRequest datafeedRequest) {
        Integer historyDataOffset = getHistoryDataOffsetValue(datafeedRequest);
        if (historyDataOffset > 0 && (datafeedQueryEndpoint == null || endpointService.isHistoryDataOffsetApplicable(datafeedQueryEndpoint)) && shouldIncludeHistoryData(datafeedRequest)) {
            return (dateService.getCaughtUpLocalDate().minusDays(historyDataOffset)).toDate();
        }
        return datafeedRequest.getStartDate();
    }

    public Date getStartDateForHistoryData(DatafeedRequest datafeedRequest) {
        return getStartDateForHistoryData(null, datafeedRequest);
    }

    private Integer getHistoryDataOffsetValue(DatafeedRequest datafeedRequest) {
        final IntegrationConfigParamName historyDataOffsetParam = datafeedRequest.isOptixDatafeed() ? OPTIX_DATAFEED_HISTORY_DATA_OFFSET
                : "REVPLAN_MARKET_ACCOM_ACTIVITY".equalsIgnoreCase(datafeedRequest.getDatafeedName()) ? DATAFEED_REVPLAN_HISTORY_DATA_OFFSET : DATAFEED_HISTORY_DATA_OFFSET;
        return pacmanConfigParamsService.getIntegerParameterValue(historyDataOffsetParam.value());
    }

    public boolean shouldIncludeHistoryData(DatafeedRequest datafeedRequest) {
        if (datafeedRequest == null) {
            return Boolean.FALSE;
        }
        return isFirstTimeRequest(datafeedRequest) || endpointService.shouldIncludeHistoryFor(datafeedRequest.getDataFeedType());
    }

    private boolean isFirstTimeRequest(DatafeedRequest datafeedRequest) {
        return datafeedRequest.isIncludeHistoryData() ||
                Optional.ofNullable(datafeedRequest.getLastSuccessDate())
                        .map(lastSuccessDate -> LocalDate.fromDateFields(lastSuccessDate).getYear() <= FIRST_TIME_EXECUTION_YEAR)
                        .orElse(Boolean.FALSE);
    }

    public List getmcat() {
        return mcatMappingReportService.generateMCATMapping();
    }

    public List getMktSegConfig() {
        return marketSegmentConfigService.getMktSegConfigDetails();
    }

    public List getDecisionConfigurations() {
        return decisionConfigurationService.getDecisionConfigurations();
    }

    public List getBarOverrideDetails(Pageable pageable) {
        return outputOverrideService.getOverridePricingData(DateUtil.convertJavaUtilDateToLocalDate(pageable.getStartDate()), DateUtil.convertJavaUtilDateToLocalDate(pageable.getEndDate()), NOT_ROLLING_DATE,
                StringUtils.EMPTY, StringUtils.EMPTY, Language.ENGLISH);
    }

    public List getOverbookingOverrideDetails(Pageable pageable) {
        return datafeedOverbookingOverrideService.getOverbookingOverrideDetails(DateUtil.convertJavaUtilDateToLocalDate(pageable.getStartDate()), DateUtil.convertJavaUtilDateToLocalDate(pageable.getEndDate()), NOT_ROLLING_DATE,
                StringUtils.EMPTY, StringUtils.EMPTY, Language.ENGLISH);
    }

    public List getInputOverrideDetails(Pageable pageable) {
        return inputOverrideDataService.getInputOverrideForDatafeed(pageable.getStartDate(), pageable.getEndDate());
    }

    public List getRolesAndPermissions() {
        return rolePermissionService.getAllRolesAndPermissions(false);
    }

    public List userActivityLog(DatafeedRequest datafeedRequest) {
        return userActivityLogService.getUserAvtivityLog(PacmanWorkContextHelper.getClientCode(), getStartDateForUserActivity(datafeedRequest), datafeedRequest.getEndDate());
    }

    private Date getStartDateForUserActivity(DatafeedRequest datafeedRequest) {
        if (isFirstTimeRequest(datafeedRequest) && datafeedRequest.isOptixDatafeed()) {
            final int historyDataOffset = Optional.ofNullable(pacmanConfigParamsService.getParameterValueByClientLevel(OPTIX_DATAFEED_HISTORY_DATA_OFFSET.value()))
                    .map(Integer::parseInt)
                    .orElse(0);
            return DateUtils.addDays(datafeedRequest.getStartDate(), -historyDataOffset);
        }
        return datafeedRequest.getStartDate();
    }

    public List getUsersData(DatafeedRequest datafeedRequest) {
        final String clientCode = PacmanWorkContextHelper.getClientCode();
        return datafeedRequest.isOptixDatafeed() ? userDetailsService.getUsersWithAllDetailsForOptix(clientCode) :
                userDetailsService.getUsersWithAllDetails(clientCode);
    }

    public List getSystemHealthDetails() {
        return systemHealthDQService.getSystemHealthDetails();
    }

    public List getCostOfWalkDetails(Pageable pageable) {
        return costOfWalkConfigService.getCostOfWalkDetails(PacmanWorkContextHelper.getPropertyId(), pageable.getStartDate());
    }

    public List getRateShoppingCompetitorDetails() {
        return rateShoppingDetailsService.getAllRateCompetitorDetails();
    }

    public List getRateShoppingChannelDetails() {
        return rateShoppingDetailsService.getAllRateChannelDetails();
    }

    public List getRateShoppingAccomClassMappings() {
        return rateShoppingAccomMappingService.getAllAccomMappingDetails();
    }

    public List getRateShoppingIgnoreCompData(Pageable pageable) {
        return rateShoppingIgnoreCompDataService.getRateShoppingIgnoreCompData(PacmanWorkContextHelper.getPropertyId(), pageable.getStartDate());
    }

    public List getRateShoppingSchedules() {
        return rateShoppingDetailsService.getRateShoppingScheduleDetails();
    }

    public List getRateShoppingCompetitiveConfigurations(Pageable pageable) {
        return competitiveChannelService.getAllCompetitiveChannelDetails(PacmanWorkContextHelper.getPropertyId(), pageable.getStartDate());
    }

    public List getBenefitsMeasurementDtos() {
        return benefitMeasurementsService.getBenefits();
    }

    public List getScheduledReportsDtos() {
        TimeZone propertyTimeZoneDetails = dateService.getPropertyTimeZone();
        SimpleDateFormat sdfForConvert = new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss z");
        sdfForConvert.setTimeZone(propertyTimeZoneDetails);
        List<ScheduledReportsDTO> scheduleList = tenantCrudService.findByNamedQuery(ScheduleReport.ALL_SCHEDULED_REPORTS_FOR_DATAFEED);
        for (ScheduledReportsDTO report : scheduleList) {
            report.setScheduledStartAsString(sdfForConvert.format(report.getScheduledStart()));
            report.setUpdatedOnAsString(sdfForConvert.format(report.getUpdatedOn()));
        }
        return scheduleList;
    }

    public List getChannelForecastDtos(DatafeedRequest datafeedRequest) {
        return channelForecastService.getChannelForecastDtos(datafeedRequest);
    }

    public List getPropertySpecificConfiguration() {
        return propertySpecificConfigurationService.getPropertySpecificConfiguration(PacmanWorkContextHelper.getPropertyId());
    }

    public List getPropertySpecificConfigurationEnhanced(DatafeedRequest datafeedRequest) {
        return propertySpecificConfigurationService.getPropertySpecificConfigurationEnhanced(PacmanWorkContextHelper.getPropertyId(), isPriceDropApplicable(datafeedRequest));
    }

    private boolean isPriceDropApplicable(DatafeedRequest datafeedRequest) {
        return datafeedRequest.isOptixDatafeed()
                || !pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_PRICE_DROP_RESTRICTIONS_DATAFEED_ENABLED);
    }

    public List getPriceDropRestrictionsDTOList() {
        return priceDropRestrictionService.getPriceDropRestrictionsDTOList();
    }

    public List getInformationManagerNotificationConfigurations() {
        return informationManagerNotificationConfigurationService.getInformationManagerNotificationConfigurations();
    }

    public List getRateCompetitorMarketPositionConfiguration(Pageable pageable) {
        return rateCompetitorMarketPositionConfigService.getRateCompetitorMarketPositionConfiguration(PacmanWorkContextHelper.getPropertyId(), pageable.getStartDate());
    }

    public List getPricingStrategyArrivalConfig(Pageable pageable) {
        return arrivalLOSService.getValidatedArrivalConfigurations(pageable.getStartDate());
    }

    public List getPricingStrategyLOSConfig(Pageable pageable) {
        return arrivalLOSService.getValidatedLosConfigurations(pageable.getStartDate());
    }

    public List getPricingStrategyRatePlanConfiguration(Pageable pageable) {
        return pricingStrategyService.getAllCurrentAndFutureRatePlanConfigurationsAfter(DateUtil.addDaysToDate(pageable.getStartDate(), -1));
    }

    public List getPropertySpecificAttributes() {
        return propertyAttributeService.getPropertySpecificAttributes(PacmanWorkContextHelper.getPropertyId());
    }

    public List getPricingStrategyRateCodeConfig() {
        return pricingStrategyService.getRateCodeConfigs();
    }

    public List getPricingStrategyUserOverride(Pageable pageable) {
        return pricingStrategyService.getUserOverrideConfigurations(pageable.getStartDate());
    }

    public List getRoomClassPriceRank() {
        return accomClassPriceRankService.getRoomClassPriceRank();
    }

    public List getRoomClassMinPriceDiff(DatafeedRequest datafeedRequest) {
        return pricingConfigurationService.getDefaultAndSeasonMinPriceDiff(datafeedRequest.getStartDate());
    }

    public List getGroupPricingBaseRoomTypeRateConfig(Pageable pageable) {
        return groupPricingBaseRoomTypeRateConfig.getGroupPricingBaseRoomTypeRateConfig(pageable.getStartDate());
    }

    public List getGroupPricingConfigDetails() {
        return groupPricingConfigService.getGroupPricingConfigDetails(PacmanWorkContextHelper.getPropertyId());
    }

    public List getGroupPricingAncillaryConfiguration(Pageable pageable) {
        return groupPricingAncillaryConfigService.getAncillaryConfiguration(pageable.getStartDate());
    }

    public List getGroupPricingConferenceAndBanquetConfigurations() {
        return conferenceAndBanquetService.getConferenceAndBanquets();
    }

    public List<Object> getGroupPricingPreferredRoomTypeConfigurations() {
        return groupPricingConfigService.getGroupPricingPreferredRoomTypeConfigurations();
    }

    public List getGroupPricingRoomTypeOffsetConfiguration(Pageable pageable) {
        return groupPricingRoomTypeOffsetConfigService.getGroupPricingRoomTypeOffsetConfiguration(pageable.getStartDate());
    }

    public List getGroupPricingMinProfitConfiguration() {
        return groupPricingMinProfitConfigurationService.getGroupPricingMinProfitConfiguration();
    }

    public List getCPOffsetConfiguration(Pageable pageable) {
        return groupPricingRoomTypeOffsetConfigService.getCPPricingRoomTypeOffsetConfiguration(pageable.getStartDate());
    }

    public Map<String, List<DataFeedEndpointRequestDTO>> getPermissibleEndpointsForProperty(Set<EndpointFrequencyType> frequencyTypes, String clientCode, boolean includeEndpoints) {
        return getPermissibleEndpointsForProperty(DATAFEED_BUCKET.value(), frequencyTypes, clientCode, true, includeEndpoints);
    }

    public Map<String, List<DataFeedEndpointRequestDTO>> getPermissibleEndpointsForProperty(String configParamName, Set<EndpointFrequencyType> frequencyTypes, String clientCode, boolean includeCore, boolean includeEndpoints) {
        final DatafeedEndPointCriteria datafeedEndPointCriteria = datafeedEndPointCriteriaService.getDatafeedEndPointCriteriaOptionalCore(configParamName, frequencyTypes, clientCode, includeCore, false);
        return endpointService.getEndPointsDTOMapByCriteria(datafeedEndPointCriteria, includeEndpoints);
    }

    public Map<String, List<String>> getStreamPermissibleEndpointsForProperty(String configParamName, Set<EndpointFrequencyType> frequencyTypes, String clientCode, boolean includeCore) {
        final DatafeedEndPointCriteria datafeedEndPointCriteria = datafeedEndPointCriteriaService.getDatafeedEndPointCriteriaOptionalCore(configParamName, frequencyTypes, clientCode, includeCore, false);
        return endpointService.getEndPointsMapByCriteria(datafeedEndPointCriteria);
    }

    public Map<String, List<DataFeedEndpointRequestDTO>> getPermissibleEndpointsForProperty(String configParamName, Set<EndpointFrequencyType> frequencyTypes, String clientCode, boolean includeCore, boolean isFirstUpload, boolean includeEndpoints) {
        final DatafeedEndPointCriteria datafeedEndPointCriteria = datafeedEndPointCriteriaService.getDatafeedEndPointCriteriaOptionalCore(configParamName, frequencyTypes, clientCode, includeCore, isFirstUpload);
        if (!datafeedEndPointCriteria.isFirstUpload()) {
            return endpointService.getEndPointsDTOMapByCriteriaWithPaceFiles(datafeedEndPointCriteria, includeEndpoints);
        } else {
            return endpointService.getEndPointsDTOMapByCriteria(datafeedEndPointCriteria, includeEndpoints);
        }
    }

    public Map<String, List<DataFeedEndpointRequestDTO>> getPermissibleEndpointsForClient(Set<EndpointFrequencyType> frequencyTypes, String clientCode, boolean includeEndpoints) {
        return getPermissibleEndpointsForClient(DATAFEED_BUCKET.value(), frequencyTypes, clientCode, includeEndpoints);
    }

    public Map<String, List<DataFeedEndpointRequestDTO>> getPermissibleEndpointsForClient(String configParamName, Set<EndpointFrequencyType> frequencyTypes, String clientCode, boolean includeEndpoints) {
        final DatafeedEndPointCriteria datafeedEndPointCriteria = datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(configParamName, frequencyTypes, clientCode, true);
        return endpointService.getEndPointsDTOMapByCriteria(datafeedEndPointCriteria, includeEndpoints);
    }

    public List getEnhancedPropertyBasicInformation() {
        return propertyBasicInformationService.getEnhancedPropertyBasicInformation();
    }

    public List getEnhancedPropertyBasicInformationWithBookedStatus() {
        return propertyBasicInformationService.getEnhancedPropertyBasicInformationWithBookedStatus();
    }

    public List getEnhancedPropertyBasicInformationWithLastLDbUpdate() {
        return propertyBasicInformationService.getEnhancedPropertyBasicInformationWithLastLDBUpdate();
    }

    public List getPropertyInformationEnhancedWithBookedStatusAndLdbAndWindowSettings() {
        return propertyBasicInformationService.getPropertyInformationEnhancedWithBookedStatusAndLdbAndWindowSettings();
    }

    public List getOutOfOverrideDetails(DatafeedRequest datafeedRequest) {
        return outOfOrderOverrideService.getOutOfOrderOverride(datafeedRequest);
    }

    public List getTaxInclusiveConfiguration() {
        return taxInclusiveConfigurationService.getTaxInclusiveConfiguration();
    }

    public List getAgileProductSendDecisionDetails() {
        return agileRatesDataFeedService.getAgileProductSendDecisionDetails();
    }

    public List getGroupFinalForecastOverride(DatafeedRequest datafeedRequest) {
        return groupFinalForecastOverrideService.getGroupFinalForecastOverride(datafeedRequest.getStartDate(), datafeedRequest.getEndDate());
    }

    public List getOperationsForecastArrivalsDepartures(Date startDate, Date endDate) {
        return operationsForecastADDatafeedService.getForecastArrivalsDepartures(startDate, endDate);
    }

    public List getProductRateShopDefinitions() {
        return productRateCodeDefinitionService.getProductRateShopDefinitions();
    }

    public List getProductClassifications(DatafeedRequest datafeedRequest) {
        return productClassificationService.getProductClassifications(datafeedRequest.getDatafeedName());
    }

    public List getProductChildPricingTypes(DatafeedRequest datafeedRequest) {
        return agileRatesDataFeedService.findProductChildPricingTypeDTO(datafeedRequest.getDatafeedName());
    }

    public List getRoomType(DatafeedRequest datafeedRequest) {
        String continuousPricingEnabled = pacmanConfigParamsService.getParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value());
        Boolean rateOfDayEnabled = pacmanConfigParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value()).equals(BAR_DECISION_VALUE_RATEOFDAY);
        Date startDate = datafeedRequest.getStartDate();
        Integer historyDataOffset = getHistoryDataOffsetValue(datafeedRequest);
        if (historyDataOffset > 0 && shouldIncludeHistoryData(datafeedRequest)) {
            startDate = (dateService.getCaughtUpLocalDate().minusDays(historyDataOffset)).toDate();
        }

        boolean isSt19Enabled = pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.ENABLE_ST19_FOR_DATAFEED_MSRT);

        QueryParameter queryParameter = QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                .and("startDate", DateUtil.formatDate(startDate, DEFAULT_DATE_FORMAT))
                .and("endDate", DateUtil.formatDate(datafeedRequest.getEndDate(), DEFAULT_DATE_FORMAT))
                .and("caughtUpDate", DateUtil.formatDate(dateService.getCaughtUpDate(), DEFAULT_DATE_FORMAT))
                .and("continuousPricingEnabled", continuousPricingEnabled)
                .and("rateOfDayEnabled", rateOfDayEnabled.toString());

        if (isSt19Enabled && Boolean.TRUE.equals(pacmanConfigParamsService
                .getParameterValue(PreProductionConfigParamName.ENABLE_ROOM_TYPE_ST19_BY_PROCEDURE))) {
            queryParameter.and("pageOffset", datafeedRequest.getStartPosition())
                    .and("pageSize", datafeedRequest.getSize());
            LOGGER.info("fetching ST 19 data by procedure call for property : " + PacmanWorkContextHelper.getPropertyId());
            return tenantCrudService.findByNativeQuery(RoomTypeHistorySTLY.GET_ROOM_TYPE_ST_19_DATA
                    , queryParameter.parameters(), getRowMapperForSt19());
        }
        if (isEnableDataFeedForMissingExtracts() && datafeedRequest.isMissingExtract() && null != datafeedRequest.getSystemDate()) {
            LOGGER.info("fetching Room Type ST2Y data for missing extracts by procedure call for property : " + PacmanWorkContextHelper.getPropertyId());
            boolean isDatafeedSendNullDecisionsForMissingExtractsEnable = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_DATA_FEED_SEND_NULL_DECISIONS_FOR_MISSING_EXTRACTS);
            queryParameter.and("caughtUpDate", DateUtil.formatDate(datafeedRequest.getSystemDate(), DEFAULT_DATE_FORMAT)).and("pageOffset", datafeedRequest.getStartPosition()).and("pageSize", datafeedRequest.getSize()).and("isSendNullDecisionsEnabled", isDatafeedSendNullDecisionsForMissingExtractsEnable);
            return tenantCrudService.findByNativeQuery(RoomTypeHistorySTLY.FIND_BY_DATES_ROOM_TYPE_ST2Y_MISSING_EXTRACT, queryParameter.parameters(), RoomTypeHistoryST2YDto.getRowMapperForRoomTypeHistoryST2Y());
        } else {
            LOGGER.info("fetching Room Type ST2Y data by native query call for property : " + PacmanWorkContextHelper.getPropertyId());
            return tenantCrudService.findByNamedQuery(isSt19Enabled ? RoomTypeHistorySTLY.FIND_BY_DATES_WITH_ST19
                    : RoomTypeHistorySTLY.FIND_BY_DATES_WITH_ST2Y, queryParameter.parameters(), datafeedRequest.getStartPosition(), datafeedRequest.getSize());
        }
    }

    private RowMapper<RoomTypeHistoryST19Dto> getRowMapperForSt19() {
        return row -> {
            RoomTypeHistoryST19Dto dto = new RoomTypeHistoryST19Dto();
            dto.setOccupancyDate((Date) row[0]);
            dto.setComparisonDateLastYear((Date) row[1]);
            dto.setRoomClassCode((String) row[2]);
            dto.setRoomTypeCode((String) row[3]);
            dto.setRoomTypeDescription((String) row[4]);
            dto.setInventoryDataCapacityThisYear(getIntegerValue(row[5]));
            dto.setInventoryDataCapacityComparisonDateLastYearActual(getIntegerValue(row[6]));
            dto.setInventoryDataOccupancyOnBooksThisYear(getIntegerValue(row[7]));
            dto.setInventoryDataOccupancyOnBooksComparisonDateLastYear(getIntegerValue(row[8]));
            dto.setInventoryDataArrivalsThisYear(getIntegerValue(row[9]));
            dto.setInventoryDataArrivalsComparisonDateLastYear(getIntegerValue(row[10]));
            dto.setInventoryDataDeparturesThisYear(getIntegerValue(row[11]));
            dto.setInventoryDataDeparturesComparisonDateLastYear(getIntegerValue(row[12]));
            dto.setInventoryDataRoomsNAOOOThisYear(getIntegerValue(row[13]));
            dto.setInventoryDataRoomsNAOOOComparisonDateLastYear(getIntegerValue(row[14]));
            dto.setInventoryDataRoomsNAOtherThisYear(getIntegerValue(row[15]));
            dto.setInventoryDataRoomsNAOtherComparisonDateLastYear(getIntegerValue(row[16]));
            dto.setInventoryDataCancelledThisYear(getIntegerValue(row[17]));
            dto.setInventoryDataCancelledComparisonDateLastYear(getIntegerValue(row[18]));
            dto.setInventoryDataNoShowThisYear(getIntegerValue(row[19]));
            dto.setInventoryDataNoShowComparisonDateLastYear(getIntegerValue(row[20]));
            dto.setInventoryDataBookedRoomRevenueThisYear((BigDecimal) row[21]);
            dto.setInventoryDataBookedRoomRevenueComparisonDateLastYear((BigDecimal) row[22]);
            dto.setForecastOccupancyThisYear((BigDecimal) row[23]);
            dto.setForecastOccupancyComparisonDateLastYear((BigDecimal) row[24]);
            dto.setForecastRoomRevenueThisYear((BigDecimal) row[25]);
            dto.setForecastRoomRevenueComparisonDateLastYear((BigDecimal) row[26]);
            dto.setDecisionsOverbookingThisYear((BigDecimal) row[27]);
            dto.setDecisionsOverbookingComparisonDateLastYear((BigDecimal) row[28]);
            dto.setDecisionsLRVThisYear((BigDecimal) row[29]);
            dto.setDecisionsLRVComparisonDateLastYear((BigDecimal) row[30]);
            dto.setOptimalBARLOS1((BigDecimal) row[31]);
            dto.setDecisionsBARLOS1((String) row[32]);
            dto.setDecisionsBARLOS2((String) row[33]);
            dto.setDecisionsBARLOS3((String) row[34]);
            dto.setDecisionsBARLOS4((String) row[35]);
            dto.setDecisionsBARLOS5((String) row[36]);
            dto.setDecisionsBARLOS6((String) row[37]);
            dto.setDecisionsBARLOS7((String) row[38]);
            dto.setInventoryDataOccupancyOnBooksSameTimeLastYear(getIntegerValue(row[39]));
            dto.setInventoryDataBookedRoomRevenueSameTimeLastYear((BigDecimal) row[40]);
            dto.setComparisonDateTwoYearsAgo((Date) row[41]);
            dto.setInventoryDataOccupancyOnBooksSameTimeTwoYearsAgo(getIntegerValue(row[42]));
            dto.setInventoryDataBookedRoomRevenueSameTimeTwoYearsAgo((BigDecimal) row[43]);
            dto.setInventoryDataOccupancyOnBooksComparisonDateTwoYearsAgo(getIntegerValue(row[44]));
            dto.setInventoryDataBookedRoomRevenueComparisonDateTwoYearsAgo((BigDecimal) row[45]);
            dto.setComparisonDateST19((Date) row[46]);
            dto.setInventoryDataOccupancyOnBooksST19(getIntegerValue(row[47]));
            dto.setInventoryDataBookedRoomRevenueST19((BigDecimal) row[48]);
            dto.setInventoryDataOccupancyOnBooksComparisonDateST19(getIntegerValue(row[49]));
            dto.setInventoryDataBookedRoomRevenueComparisonDateST19((BigDecimal) row[50]);
            return dto;
        };
    }

    private Integer getIntegerValue(Object rowValue) {
        return rowValue == null ? null : ((BigDecimal) rowValue).intValue();
    }

    public List getMarketSegmentSt2y(DatafeedRequest datafeedRequest) {
        Date startDate = datafeedRequest.getStartDate();
        Integer historyDataOffset = getHistoryDataOffsetValue(datafeedRequest);
        if (historyDataOffset > 0 && shouldIncludeHistoryData(datafeedRequest)) {
            startDate = (dateService.getCaughtUpLocalDate().minusDays(historyDataOffset)).toDate();
        }

        QueryParameter queryParameter = QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                .and("caughtUpDate", DateUtil.formatDate(dateService.getCaughtUpDate(), DEFAULT_DATE_FORMAT))
                .and("startDate", DateUtil.formatDate(startDate, DEFAULT_DATE_FORMAT))
                .and("endDate", DateUtil.formatDate(datafeedRequest.getEndDate(), DEFAULT_DATE_FORMAT))
                .and("isExtendedWindowEnabled", datafeedRequest.isUseExtendedWindowDecision())
                .and("pageOffset", datafeedRequest.getStartPosition())
                .and("pageSize", datafeedRequest.getSize());

        if (isEnableDataFeedForMissingExtracts() && datafeedRequest.isMissingExtract() && null != datafeedRequest.getSystemDate()) {
            LOGGER.info("fetching ST2Y data for missing extracts by procedure call for property : " + PacmanWorkContextHelper.getPropertyId());
            boolean isDatafeedSendNullDecisionsForMissingExtractsEnable = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_DATA_FEED_SEND_NULL_DECISIONS_FOR_MISSING_EXTRACTS);
            queryParameter.and("caughtUpDate", DateUtil.formatDate(datafeedRequest.getSystemDate(), DEFAULT_DATE_FORMAT)).and("isSendNullDecisionsEnabled", isDatafeedSendNullDecisionsForMissingExtractsEnable);
            return tenantCrudService.findByNativeQuery(MarketSegmentHistorySTLY.FIND_BY_DATES_BETWEEN_ST2Y_MISSING_EXTRACT, queryParameter.parameters(), MarketSegmentHistoryST2YDto.getRowMapperForMarketSegmentSt2y());
        } else {
            LOGGER.info("fetching ST2Y data by procedure call for property : " + PacmanWorkContextHelper.getPropertyId());
            return tenantCrudService.findByNativeQuery(MarketSegmentHistorySTLY.FIND_BY_DATES_BETWEEN_ST2Y_OPT, queryParameter.parameters(), MarketSegmentHistoryST2YDto.getRowMapperForMarketSegmentSt2y());
        }
    }

    public List getRoomClassConfiguration(DatafeedRequest datafeedRequest) {
        return tenantCrudService.findByNamedQuery(datafeedRequest.isOptixDatafeed() ? RoomClassConfiguration.ROOM_CLASS_CONFIGURATION_PSEUDO : RoomClassConfiguration.ROOM_CLASS_CONFIGURATION,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .parameters());
    }

    public Set<String> getDatafeedEnabledClientCodes(FeatureTogglesConfigParamName configParamName) {
        final Set<String> clientCodes = new HashSet<>();

        List<ConfigParameterValue> configParameterValues = pacmanConfigParamsService.getParameterValueByParameterNameAndPredefinedValueAndPredefinedValueType(configParamName.value(), "true", "boolean");

        configParameterValues.stream().forEach(configParameterValue -> {
            if (configParameterValue.getContext().contains(CONTEXT_DELIMITER)) {
                clientCodes.add(configParameterValue.getContext().split("\\.")[1]);//client code
            }
        });

        return clientCodes;
    }

    public Set<String> getClientLevelDatafeedEnabledClientCodes() {
        final Set<String> clientCodes = getDatafeedEnabledClientCodes(FeatureTogglesConfigParamName.IS_DATAFEED_FILE_ENABLED);
        final Set<String> eligibleClientCodes = new HashSet<>();

        for (String clientCode : clientCodes) {

            if (isEligibleForClientLevelDatafeed(clientCode)) {
                eligibleClientCodes.add(clientCode);
            }
        }

        return eligibleClientCodes;
    }

    public List getExtendedStayProductConfiguration() {
        return esaProductDefinitionConfigurationService.getExtendedStayProductDefinitions();
    }

    public List getExtendedStayCompetitorConfiguration() {
        return extendedStayCompetitorConfigurationService.getExtendedStayCompetitorConfiguration();
    }

    public List getCPBaseRoomTypeConfiguration() {
        return cpBaseRoomTypeConfigurationService.getCPBaseRoomTypeConfiguration();
    }

    public List getCPBARRoundingRulesConfiguration() {
        return cpBarRoundingRulesConfigurationService.getCPBARRoundingRulesConfiguration();
    }

    public boolean isEligibleForClientLevelDatafeed(final String clientCode) {
        Set<EndpointBucket> endPointBuckets = datafeedEndPointCriteriaService.getClientLevelBuckets(clientCode);
        return CollectionUtils.isEmpty(endPointBuckets) ? Boolean.FALSE : (endPointBuckets.contains(EndpointBucket.USER_ACTIVITY) || endPointBuckets.contains(EndpointBucket.SYS_CONFIG));
    }

    public List getDailyBarDetails() {
        return dailyBarInputConfigService.getDailyBarDetails();
    }

    public List<Object> getContinuousPricingFloorCeilingTransientConfiguration(DatafeedRequest datafeedRequest) {
        return cpCeilingFloorTransientConfigurationService.getContinuousPricingFloorCeilingTransientConfiguration(datafeedRequest);
    }

    public List<Object> getContinuousPricingSupplementsConfiguration(DatafeedRequest datafeedRequest) {
        return cpSupplementsConfigurationService.getContinuousPricingSupplementsConfiguration(datafeedRequest);
    }

    public List getInventorySharingData(DatafeedRequest datafeedRequest) {
        return inventorySharingService.getInventorySharingData(datafeedRequest);
    }

    public void setPacmanConfigParamsService(final PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    public List<Object> getAgileRateProductsPricingData(DatafeedRequest datafeedRequest) {
        if (shouldIncludeHistoryData(datafeedRequest) && getHistoryDataOffsetValue(datafeedRequest) > 0) {
            datafeedRequest.setStartDate(dateService.getCaughtUpLocalDate().minusDays(getHistoryDataOffsetValue(datafeedRequest)).toDate());
        }
        return agileRatesDataFeedService.getAgileRateProductsPricingData(datafeedRequest);
    }

    public List getAgileRateProductsOverrideDetails(DatafeedRequest datafeedRequest) {
        return outputOverrideService.fetchAgileRatesProductPricingOverrideData(datafeedRequest);
    }

    public List getLDBProjectionData(DatafeedRequest datafeedRequest) {
        return ldbProjectionDatafeedService.fetchLDBProjectionData(datafeedRequest);
    }

    public List getProfitForecastGroupData(DatafeedRequest datafeedRequest) {
        return profitDataFeedService.fetchProfitForecastGroupData(datafeedRequest);
    }

    public List getProfitRoomClassData(DatafeedRequest datafeedRequest) {
        return profitDataFeedService.fetchProfitRoomClassData(datafeedRequest);
    }

    public List getInventoryHistory(DatafeedRequest datafeedRequest) {
        return inventoryHistoryDataService.getInventoryHistoryData(datafeedRequest);
    }

    public List getGroupEvaluationDataFeed(DatafeedRequest datafeedRequest) {
        return groupEvaluationDataFeedService.getGroupEvaluationDataFeed(getStartDateForHistoricalRunForGroupEvaluation(datafeedRequest), datafeedRequest.getStartPosition(), datafeedRequest.getSize());
    }

    public List getAgileRateProductDefination(DatafeedRequest datafeedRequest) {
        return agileRatesDataFeedService.findPricingDataProductDefinitionDTO(datafeedRequest);
    }

    public List getAgileRateProductRateCodeAssignment(DatafeedRequest datafeedRequest) {
        return agileRatesDataFeedService.findProductRateCodeAssignment(datafeedRequest);
    }

    public List getAgileRateProductHierarchy() {
        return agileRatesDataFeedService.findProductHierarchy();
    }

    public List getAgileRateProductGroups() {
        return agileRatesDataFeedService.findProductGroups();
    }

    public List getAgileRateProductOptimization() {
        return agileRatesDataFeedService.findAdjustmentsForOptimizedProducts();
    }

    public List getAgileRateProductRoomTypeAssignment(DatafeedRequest datafeedRequest) {
        return agileRatesDataFeedService.findProductRoomTypeAssignment(datafeedRequest);
    }

    public List getAgileRateProductDefaultValue(DatafeedRequest datafeedRequest) {
        return agileRatesDataFeedService.findProductDefaultValue(datafeedRequest.getDatafeedName());
    }


    public List getAgileRateProductSeasonalValue(DatafeedRequest datafeedRequest) {
        return agileRatesDataFeedService.findProductSeasonalValue(datafeedRequest.getStartDate(), datafeedRequest.getDatafeedName());
    }

    public List getAgileRateProductPackageElementAssignment(String datafeedName) {
        return agileRatesDataFeedService.findProductPackageElementAssignment(datafeedName);
    }

    public List getAgileRateProductPackageElements() {
        return agileRatesDataFeedService.findProductPackageElements();
    }

    public List getAgileRateProductPackageElementsEnhanced() {
        return agileRatesDataFeedService.findProductPackageElementsEnhanced();
    }

    public List getVirtualPropertyMappings() {
        return virtualPropertyMappingService.getMappingsForVirtualPropertyForDatafeed(PacmanWorkContextHelper.getClientId());
    }

    public List getReservationNight(DatafeedRequest datafeedRequest) {
        return reservationNightDatafeedService.getReservationNight(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public Stream<ReservationNightDTO> getReservationNightStreaming(DatafeedRequest datafeedRequest) {
        return reservationNightDatafeedService.getReservationNightStreaming(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getReservationChangeNight(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getReservationChangeNight(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getBudgetData(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getBudgetData(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getUserForecastData(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getUserForecastData(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getBudgetDetails(DatafeedRequest datafeedRequest) {
        return budgetDataService.getBudgetDetails(datafeedRequest);
    }

    public List getForecastData(DatafeedRequest datafeedRequest) {
        return budgetDataService.getForecastDetails(datafeedRequest);
    }

    public List getOccupancyFCST(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getOccupancyFCST(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getForecastGroupInfo() {
        return optixDatafeedService.getForecastGroupInfo();
    }

    public List getMarketAccomActivity(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getMarketAccomActivity(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getGroupBlock(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getGroupBlock(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public Stream<GroupBlockDTO> getGroupBlockStreaming(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getGroupBlockStreaming(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getPaceGroupBlock(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getPaceGroupBlock(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getPaceTotalActivity(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getPaceTotalActivity(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getPaceWebrate(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getPaceWebrate(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getWebrate(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getWebrate(datafeedRequest);
    }

    public Stream<WebrateDTO> getWebrateStreaming(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getWebrateStreaming(datafeedRequest);
    }

    public List getSTRDailyData(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getSTRDailyData(datafeedRequest.getStartDate(), datafeedRequest.getEndDate());
    }

    public List getSTRMonthlyData() {
        return optixDatafeedService.getSTRMonthlyData();
    }

    public List getHotelCapacityDemand360(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getHotelCapacityDemand360(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public Stream<D360CompCapacityDTO> getHotelCapacityDemand360Streaming(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getHotelCapacityDemand360Streaming(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getBookingSummaryDemand360(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getBookingSummaryDemand360(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public Stream<D360BookingSummaryDTO> getBookingSummaryDemand360Streaming(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getBookingSummaryDemand360Streaming(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getGroupMaster(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getGroupMaster(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public Stream<GroupMasterDTO> getGroupMasterStreaming(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getGroupMasterStreaming(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getComponentRoomMapping() {
        return optixDatafeedService.getComponentRoomMapping();
    }

    public List getPaceGroupMaster(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getPaceGroupMaster(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getLRVPace(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getLRVPace(datafeedRequest, isFirstTimeRequest(datafeedRequest));
    }

    public List getProductFreeNightDefinition(DatafeedRequest datafeedRequest) {
        return agileRatesDataFeedService.getProductFreeNightDefinition(datafeedRequest.getDatafeedName());
    }

    public List getProductGroupProductDefinition(DatafeedRequest datafeedRequest) {
        return agileRatesDataFeedService.getProductGroupProductDefinition();
    }

    public List getMarketSegmentPaceData(DatafeedRequest datafeedRequest) {
        return marketSegmentActivityService.getMarketSegmentPaceData(datafeedRequest.getStartDate(), datafeedRequest.getEndDate(), datafeedRequest.getStartPosition(), datafeedRequest.getSize());
    }

    public List getMarketSegmentNonPaceData(DatafeedRequest datafeedRequest) {
        return marketSegmentActivityService.getMarketSegmentNonPaceData(datafeedRequest.getStartDate(), datafeedRequest.getEndDate(), datafeedRequest.getStartPosition(), datafeedRequest.getSize());
    }

    public List getMarketAccomActivityData(DatafeedRequest datafeedRequest) {
        return marketSegmentActivityService.getMarketAccomActivityData(getStartDateForHistoryData(datafeedRequest), datafeedRequest.getEndDate(), datafeedRequest.getStartPosition(), datafeedRequest.getSize());
    }

    public List getGroupPricingSCRoomTypeMapping() {
        final var guestRoomCategory = functionSpaceConfigurationService.getGuestRoomCategories();
        final var assignedAccomTypes = functionSpaceConfigurationService.getAssignedAccomTypes();
        List<GroupPricingSCRoomTypeMapping> result = new ArrayList<>();
        Optional.ofNullable(guestRoomCategory)
                .orElse(List.of())
                .forEach(scRoomType ->
                        result.add(new GroupPricingSCRoomTypeMapping(scRoomType.getRoomCategory(),
                                assignedAccomTypes.stream().filter(accomType -> accomType.getId().equals(scRoomType.getAccomTypeId()))
                                        .map(x -> x.getAccomTypeCode()).findFirst().orElse(EMPTY_STRING)
                        )));
        return result;
    }

    public List getGroupPricingSCMarketSegmentMapping() {
        final var fSmarketSegment = functionSpaceConfigurationService.getMarketSegments();
        final var marketSegSummary = overrideRepository.getAllMarketSegments();
        List<GroupPricingSCMarketSegmentMapping> result = new ArrayList<>();
        Optional.ofNullable(fSmarketSegment)
                .orElse(List.of())
                .forEach(scMktSeg ->
                        result.add(new GroupPricingSCMarketSegmentMapping(scMktSeg.getAbbreviation(),
                                marketSegSummary.stream().filter(mktSeg -> mktSeg.getId().equals(scMktSeg.getMarketSegmentId()))
                                        .map(x -> x.getCode()).findFirst().orElse(EMPTY_STRING)
                        )));
        return result;
    }

    private Date getStartDateForHistoricalRunForGroupEvaluation(DatafeedRequest datafeedRequest) {
        Date startDate = datafeedRequest.getStartDate();
        if (shouldIncludeHistoryData(datafeedRequest)) {
            startDate = new Date(0);
        }
        return startDate;
    }

    public Set<EndpointFrequencyType> getApplicableEndpointFrequencyTypes() {
        final Date caughtUpDate = dateService.getCaughtUpDate();
        return generateApplicableEndpointFrequencyTypesBasedOnDate(caughtUpDate);
    }

    public Set<EndpointFrequencyType> getApplicableEndpointFrequencyTypes(Date systemDate) {
        return generateApplicableEndpointFrequencyTypesBasedOnDate(systemDate);
    }

    private Set<EndpointFrequencyType> generateApplicableEndpointFrequencyTypesBasedOnDate(Date caughtUpDate) {
        Set<EndpointFrequencyType> frequencyTypes = new HashSet<>();
        frequencyTypes.add(EndpointFrequencyType.DAILY);
        if (generateWeeklyDatafeedForTheGivenDate(caughtUpDate)) {
            frequencyTypes.add(EndpointFrequencyType.WEEKLY);
        }
        if (generateMonthlyDatafeedForTheGivenDate(caughtUpDate)) {
            frequencyTypes.add(EndpointFrequencyType.MONTHLY);
        }
        return frequencyTypes;
    }

    public boolean generateWeeklyDatafeedForTheGivenDate(Date date) {
        String weekday = pacmanConfigParamsService.getParameterValue(DATAFEED_FREQUENCY_WEEKLY);
        if (null == weekday) {
            return false;
        }
        return weekday.contains(DateUtil.getDayOfWeekShortName(date));
    }

    public boolean generateMonthlyDatafeedForTheGivenDate(Date date) {
        Integer dayOfMonth = pacmanConfigParamsService.getParameterValue(DATAFEED_FREQUENCY_MONTHLY);
        if (null == dayOfMonth) {
            return false;
        }
        if (dayOfMonth < 1 || dayOfMonth > 28) {
            throw new TetrisException(DATAFEED_FREQUENCY_MONTHLY.value() + " value must be set between 1 to 28");
        }
        return DateUtil.getDateForDate(date) == dayOfMonth;
    }

    public Set<DatafeedFtpConfigDTO> getSftpDetailsDTOS(String clientCode) {
        HashSet<DatafeedFtpConfigDTO> sftpDetails = new HashSet<>();
        sftpDetails.add(getDatafeedFtpConfigFromConfigParameters());
        if (pacmanConfigParamsService.getBooleanParameterValue(ENABLE_DATAFEED_UPLOAD_TO_MULTIPLE_LOCATIONS)) {
            sftpDetails.addAll(getDatafeedFtpConfigFromDatafeedFTPConfigTable(clientCode));
        }
        return sftpDetails;
    }


    private DatafeedFtpConfigDTO getDatafeedFtpConfigFromConfigParameters() {
        DatafeedFtpConfigDTO datafeedFtpConfigDTO = new DatafeedFtpConfigDTO();
        datafeedFtpConfigDTO.setHost(pacmanConfigParamsService.getParameterValue(DATAFEED_FTP_HOST));
        datafeedFtpConfigDTO.setPort(pacmanConfigParamsService.getIntegerParameterValue(DATAFEED_FTP_PORT.getParameterName()));
        datafeedFtpConfigDTO.setUser(pacmanConfigParamsService.getParameterValue(DATAFEED_FTP_USERNAME));
        datafeedFtpConfigDTO.setPassword(pacmanConfigParamsService.getParameterValue(DATAFEED_FTP_PASSWORD));
        datafeedFtpConfigDTO.setSftpCertificateFileName(pacmanConfigParamsService.getParameterValue(DATAFEED_SFTP_CERTIFICATE_FILE_NAME));
        datafeedFtpConfigDTO.setSftpPrivateKeyPassPhrase(pacmanConfigParamsService.getParameterValue(DATAFEED_SFTP_PRIVATE_KEY_PASS_PHRASE));
        datafeedFtpConfigDTO.setSftpConnectionTimeOut(String.valueOf(pacmanConfigParamsService.getIntegerParameterValue(DATAFEED_SFTP_TIMEOUT_SECONDS.getParameterName())));
        datafeedFtpConfigDTO.setRemoteDirectory(pacmanConfigParamsService.getParameterValue(DATAFEED_FTP_REMOTE_DIRECTORY));
        datafeedFtpConfigDTO.setEncryptionEnabled(pacmanConfigParamsService.getBooleanParameterValue(DATAFEED_ENCRYPTION_ENABLED));
        datafeedFtpConfigDTO.setEncryptionKeyFileName(pacmanConfigParamsService.getParameterValue(DATAFEED_ENCRYPTION_KEY_FILE_NAME));
        return datafeedFtpConfigDTO;
    }

    private List<DatafeedFtpConfigDTO> getDatafeedFtpConfigFromDatafeedFTPConfigTable(String clientCode) {
        List<DatafeedFtpConfig> datafeedFtpConfigs = globalCrudService.findByNativeQuery(BY_CLIENT_CODE, QueryParameter.with("clientCode", clientCode).parameters(), DatafeedFtpConfig.class);
        if (isNull(datafeedFtpConfigs)) {
            return Collections.emptyList();
        }
        return datafeedFtpConfigs.stream().map(this::getDatafeedFtpConfigDTO).collect(Collectors.toList());
    }

    public DatafeedFtpConfigDTO getDatafeedFtpConfigDTO(DatafeedFtpConfig ftpConfig) {
        DatafeedFtpConfigDTO configDTO = new DatafeedFtpConfigDTO();
        configDTO.setHost(ftpConfig.getHost());
        configDTO.setPort(ftpConfig.getPort());
        configDTO.setUser(ftpConfig.getUser());
        configDTO.setPassword(ftpConfig.getPassword());
        configDTO.setRemoteDirectory(ftpConfig.getRemoteDirectory());
        configDTO.setSftpCertificateFileName(ftpConfig.getSftpCertificateFileName());
        configDTO.setSftpPrivateKeyPassPhrase(ftpConfig.getSftpPrivateKeyPassPhrase());
        configDTO.setSftpConnectionTimeOut(ftpConfig.getSftpConnectionTimeOut());
        return configDTO;
    }

    public Map<String, Object> buildParameterMap(Date caughtUpDate, String clientCode, Map<String, List<DataFeedEndpointRequestDTO>> restEndpoint, Date datafeedLastSuccessfulRun,
                                                 String propertyCode, String clientPropertyCode, boolean useExtendedWindowDecision) {
        return buildMap(caughtUpDate, clientCode, restEndpoint, datafeedLastSuccessfulRun, propertyCode, clientPropertyCode, useExtendedWindowDecision, false);
    }

    public Map<String, Object> buildParameterMap(Date caughtUpDate, String clientCode, Map<String, List<DataFeedEndpointRequestDTO>> restEndpoint, Date datafeedLastSuccessfulRun,
                                                 String propertyCode, String clientPropertyCode, boolean useExtendedWindowDecision, boolean isMissingExtract) {
        return buildMap(caughtUpDate, clientCode, restEndpoint, datafeedLastSuccessfulRun, propertyCode, clientPropertyCode, useExtendedWindowDecision, isMissingExtract);
    }

    private Map<String, Object> buildMap(Date caughtUpDate, String clientCode, Map<String, List<DataFeedEndpointRequestDTO>> restEndpoint, Date datafeedLastSuccessfulRun,
                                         String propertyCode, String clientPropertyCode, boolean useExtendedWindowDecision, boolean isMissingExtract) {
        return MapBuilder.with(START_DATE, DateUtil.formatDate(caughtUpDate, DEFAULT_DATE_FORMAT))
                .and(END_DATE, DateUtil.formatDate(isMissingExtract ? getEndDateForMissingExtract(caughtUpDate) : dateService.getForecastWindowEndDateBDE(), DEFAULT_DATE_FORMAT))
                .and(DATAFEED_LAST_SUCCESSFUL_RUN, DateUtil.formatDate(datafeedLastSuccessfulRun, DATE_TIME_FORMAT))
                .and(FTP_DETAILS, getSftpDetailsDTOS(clientCode))
                .and(REST_ENDPOINT, restEndpoint)
                .and(FILE_PREFIX, StringUtils.defaultIfBlank(clientPropertyCode, propertyCode))
                .and(CHARACTER_ENCODING, pacmanConfigParamsService.getParameterValue(DATAFEED_CHARACTER_ENCODING))
                .and(RENAME_FILE_IN_SEPARATE_CONNECTION_WHILE_COPYING, pacmanConfigParamsService.getParameterValue(DATAFEED_RENAME_FILE_IN_SEPARATE_CONNECTION))
                .and(SHOULD_SKIP_DATAFEED_RENAME_WITH_TEMPORARY_SUFFIX, pacmanConfigParamsService.getBooleanParameterValue(SKIP_DATAFEED_RENAME_WITH_TEMPORARY_SUFFIX))
                .and(USE_EXTENDED_WINDOW_DECISION, useExtendedWindowDecision || endpointService.isExtendedWindowApplicable())
                .get();
    }

    public Date getEndDateForMissingExtract(Date systemDate) {
        Calendar c = new GregorianCalendar();
        c.setTime(systemDate);
        c.add(Calendar.DATE, dateService.getForecastWindowOffsetBDE());
        return c.getTime();
    }

    public Map<String, Object> buildParameterMapByClientCode(Date currentDate, Map<String, List<DataFeedEndpointRequestDTO>> restEndpoint, String clientCode) {
        return MapBuilder.with(Constants.END_DATE, DateUtil.formatDate(currentDate, DEFAULT_DATE_FORMAT))
                .and(Constants.START_DATE, DateUtil.formatDate(currentDate, DEFAULT_DATE_FORMAT))
                .and(Constants.DATAFEED_LAST_SUCCESSFUL_RUN, DateUtil.formatDate(new Date(0), DateUtil.DATE_TIME_FORMAT))
                .and(Constants.FTP_DETAILS, getSftpDetailsDTOSByClientCode(clientCode))
                .and(Constants.REST_ENDPOINT, restEndpoint)
                .and(Constants.CHARACTER_ENCODING, pacmanConfigParamsService.getParameterValueByClientLevel(DATAFEED_CHARACTER_ENCODING.value(), clientCode))
                .and(IS_CLIENT_LEVEL_REQUEST, true)
                .and(SHOULD_SKIP_DATAFEED_RENAME_WITH_TEMPORARY_SUFFIX, Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(SKIP_DATAFEED_RENAME_WITH_TEMPORARY_SUFFIX.value(), clientCode)))
                .and(RENAME_FILE_IN_SEPARATE_CONNECTION_WHILE_COPYING, Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(DATAFEED_RENAME_FILE_IN_SEPARATE_CONNECTION.value(), clientCode)))
                .get();
    }

    private HashSet<DatafeedFtpConfigDTO> getSftpDetailsDTOSByClientCode(String clientCode) {
        HashSet<DatafeedFtpConfigDTO> sftpDetails = new HashSet<>();
        sftpDetails.add(getDatafeedFtpConfigFromConfigParameters(clientCode));
        if (parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(ENABLE_CLIENT_LEVEL_DATAFEED_UPLOAD_TO_MULTIPLE_LOCATIONS.value(), clientCode))) {
            sftpDetails.addAll(getDatafeedFtpConfigFromDatafeedFTPConfigTableByClientCode(clientCode));
        }
        return sftpDetails;
    }

    private List<DatafeedFtpConfigDTO> getDatafeedFtpConfigFromDatafeedFTPConfigTableByClientCode(String clientCode) {
        List<DatafeedFtpConfig> datafeedFtpConfigs = globalCrudService.findByNativeQuery(BY_CLIENT_CODE,
                QueryParameter.with("clientCode", clientCode).parameters(), DatafeedFtpConfig.class);
        if (isNull(datafeedFtpConfigs)) {
            return Collections.emptyList();
        }
        String encryptionKeyFileName = getEncryptionKeyFileName(clientCode);
        boolean encryptionEnabled = isEncryptionEnabled(clientCode);
        return datafeedFtpConfigs.stream().map(this::getDatafeedFtpConfigDTO)
                .peek(dto -> dto.setEncryptionKeyFileName(encryptionKeyFileName))
                .peek(dto -> dto.setEncryptionEnabled(encryptionEnabled))
                .collect(Collectors.toList());
    }

    private boolean isEncryptionEnabled(String clientCode) {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_ENCRYPTION_ENABLED.value(), clientCode));
    }

    private DatafeedFtpConfigDTO getDatafeedFtpConfigFromConfigParameters(String clientCode) {
        DatafeedFtpConfigDTO datafeedFtpConfigDTO = new DatafeedFtpConfigDTO();
        datafeedFtpConfigDTO.setHost(pacmanConfigParamsService.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_FTP_HOST.value(), clientCode));
        datafeedFtpConfigDTO.setPort(Integer.parseInt(pacmanConfigParamsService.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_FTP_PORT.value(), clientCode)));
        datafeedFtpConfigDTO.setUser(pacmanConfigParamsService.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_FTP_USERNAME.value(), clientCode));
        datafeedFtpConfigDTO.setPassword(pacmanConfigParamsService.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_FTP_PASSWORD.value(), clientCode));
        datafeedFtpConfigDTO.setSftpCertificateFileName(pacmanConfigParamsService.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_SFTP_CERTIFICATE_FILE_NAME.value(), clientCode));
        datafeedFtpConfigDTO.setSftpPrivateKeyPassPhrase(pacmanConfigParamsService.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_SFTP_PRIVATE_KEY_PASS_PHRASE.value(), clientCode));
        datafeedFtpConfigDTO.setSftpConnectionTimeOut(pacmanConfigParamsService.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_SFTP_TIMEOUT_SECONDS.value(), clientCode));
        datafeedFtpConfigDTO.setRemoteDirectory(pacmanConfigParamsService.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_FTP_REMOTE_DIRECTORY.value(), clientCode));
        datafeedFtpConfigDTO.setEncryptionKeyFileName(getEncryptionKeyFileName(clientCode));
        datafeedFtpConfigDTO.setEncryptionEnabled(isEncryptionEnabled(clientCode));
        return datafeedFtpConfigDTO;
    }

    private String getEncryptionKeyFileName(String clientCode) {
        return pacmanConfigParamsService.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_ENCRYPTION_KEY_FILE_NAME.value(), clientCode);
    }

    public Set<EndpointFrequencyType> getFrequencyForClientDatafeed(String clientCode) {
        Date currentDate = dateService.getCurrentDate();
        Set<EndpointFrequencyType> frequencyType = new HashSet<>();
        frequencyType.add(EndpointFrequencyType.CLIENT_DAILY);
        String weekday = pacmanConfigParamsService.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_FREQUENCY_WEEKLY.value(), clientCode);
        if (weekday != null && weekday.contains(DateUtil.getDayOfWeekShortName(currentDate))) {
            frequencyType.add(EndpointFrequencyType.CLIENT_WEEKLY);
        }
        String monthly = pacmanConfigParamsService.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_FREQUENCY_MONTHLY.value(), clientCode);
        if (monthly != null && (Integer.valueOf(monthly)).equals(DateUtil.getDateForDate(currentDate))) {
            frequencyType.add(EndpointFrequencyType.CLIENT_MONTHLY);
        }
        return frequencyType;
    }

    public List getRevplanMarketOccupancyForecastDtos(DatafeedRequest datafeedRequest) {
        return occupancyForecastService.getRevplanMarketOccupancyForecastDtos(datafeedRequest);
    }

    public List getIndependentProductOverrideDetails(Pageable pageable) {
        return outputOverrideService.getIndependentProductOverrideDetails(convertJavaUtilDateToLocalDate(pageable.getStartDate()),
                convertJavaUtilDateToLocalDate(pageable.getEndDate()), NOT_ROLLING_DATE,
                StringUtils.EMPTY, StringUtils.EMPTY, Language.ENGLISH, pageable.getStartPosition(), pageable.getSize());
    }

    public List getInventoryLimitDetails(Pageable pageable) {
        return inventoryLimitDecisionService.getInventoryLimitDetails(convertJavaUtilDateToLocalDate(pageable.getStartDate()),
                convertJavaUtilDateToLocalDate(pageable.getEndDate()),
                pageable.getStartPosition(), pageable.getSize());
    }
    public List<Object> getOverbookingConfiguration(DatafeedRequest datafeedRequest) {
        return overbookingConfigService.getOverbookingConfigurationDetails(datafeedRequest);
    }

    public List getRateShoppingOccupancyBasedCMPCDtos() {
        return rateShoppingConfigService.getOccupancyBasedConfigData(true);
    }

    public List getRateShoppingIgnoreChannelConfigDtos() {
        return rateShoppingConfigService.getIgnoreChannelConfigs(true);
    }

    public List getMeetingPackagePricingCfgs(DatafeedRequest datafeedRequest) {
        Map<String, Object> parameters = QueryParameter.with(START_DATE, datafeedRequest.getStartDate())
                .and(END_DATE, datafeedRequest.getEndDate())
                .parameters();

        List<Object> result = tenantCrudService.findByNamedQuery(MeetingPackagePricing.GET_MEETING_PRICING_PACKAGE,
               parameters, datafeedRequest.getStartPosition(), datafeedRequest.getSize());

        return Optional.ofNullable(result)
                .orElse(Collections.emptyList());
    }

    public List getMeetingPackagePricingOverrideCfgs(DatafeedRequest datafeedRequest) {
        return meetingPackageBaseProductPricingOverridesService.getMeetingPackagePricingOverrideDto(datafeedRequest);
    }

    public List getPropertyOnBooksPaceAlert() {
        return optixDatafeedService.getPropertyOnBooksPaceAlerts();
   }

    public List getOptixSpecificPropertyLevelData(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getPropertyLevelData(datafeedRequest, isDatafeedSpecialEventInstanceNameEnabled(), isFirstTimeRequest(datafeedRequest));
    }

    public List getOptixPricingSensitivityCoefficientCfgs(DatafeedRequest datafeedRequest) {
        return optixDatafeedService.getOptixPricingSensitivityCoefficientCfgs(datafeedRequest);
    }

    public List getInformationManagerAlerts(DatafeedRequest datafeedRequest) {
        Map<String, Object> parameters = QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                .and(LAST_SUCCESS_DATE, datafeedRequest.getLastSuccessDate())
                .parameters();
        List<InformationManagerAlert> result = tenantCrudService.findByNamedQuery(InformationManagerAlert.FIND_ALERTS, parameters);

        return CollectionUtils.isEmpty(result) ? Collections.emptyList() : getInformationMangerAlertDtos(result);
    }

    private List<InformationMangerAlertDto> getInformationMangerAlertDtos(List<InformationManagerAlert> result) {
        return result.stream().map(row -> {
            InformationMangerAlertDto dto = new InformationMangerAlertDto();
            dto.setId(row.getId());
            dto.setStatus(row.getStatus());
            dto.setDescription(row.getDescription());
            dto.setAlertDescription(row.getAlertDescription());
            dto.setScore(row.getScore());
            dto.setCreatedTime(row.getCreatedTime());
            dto.setLastModifiedTime(row.getLastModifiedTime());
            dto.setDetails(basicHtmlToText(row.getDetails()));
            dto.setAlertDetails(basicHtmlToText(row.getAlertDetails()));
            return dto;
        }).collect(Collectors.toList());
    }

    private String basicHtmlToText(String html) {
        if (html == null || html.trim().isEmpty()) {
            return "";
        }
        return html.replaceAll("(?i)<br\\s*/?>", " ")
                .replaceAll("(?i)</?(ul|ol)>", "")
                .replaceAll("(?i)<li>", "(").replaceAll("(?i)</li>", ")")
                .replaceAll("(?i)</?(b|i|u|strong|em|div|p|span)>", "")
                .replaceAll("&nbsp;", " ")
                .replaceAll("&amp;", "&")
                .replaceAll("&lt;", "<")
                .replaceAll("&gt;", ">")
                .replaceAll("&quot;", "")
                .replaceAll("(?i)<table[^>]*>", "").replaceAll("(?i)</table>", "")
                .replaceAll("(?i)<tr[^>]*>", "(").replaceAll("(?i)</tr>", ")")
                .replaceAll("(?i)<t[dh][^>]*>", "")
                .replaceAll("(?i)</t[dh]>", " :: ")
                .replaceAll("<[^>]+>", "")
                .replaceAll("[\\n\\t\\r]+", "")
                .replaceAll("(?m)^\\s*", "")
                .replaceAll("\\s*::\\s*\\)", ")")
                .replaceAll("\\)\\s*\\(", ") (")
                .trim();
    }
}