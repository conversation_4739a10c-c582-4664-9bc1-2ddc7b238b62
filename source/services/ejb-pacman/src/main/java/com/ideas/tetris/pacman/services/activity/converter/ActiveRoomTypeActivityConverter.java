package com.ideas.tetris.pacman.services.activity.converter;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.activity.converter.ActiveRoomTypeActivityConverter.Qualifier;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ActivityEntity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceAccomActivity;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Qualifier
@Component
@Transactional
public class ActiveRoomTypeActivityConverter extends PaceActivityConverter<AccomActivity, PaceAccomActivity> {

    @Override
    public Map<String, Object> convertFromEntity(ActivityEntity accomActivity) {
        Map<String, Object> dto = super.convertFromEntity(accomActivity);
        if (dto == null) {
            return null;
        }

        Integer accomTypeId = null;
        if (accomActivity instanceof AccomActivity) {
            accomTypeId = ((AccomActivity) accomActivity).getAccomTypeId();
        } else if (accomActivity instanceof PaceAccomActivity) {
            accomTypeId = ((PaceAccomActivity) accomActivity).getAccomTypeId();
        }

        if (accomTypeId != null) {
            String roomTypeCode = findRoomTypeCodeForRoomTypeId(accomTypeId);
            dto.put(ROOM_TYPE_NAME, findRoomTypeNameForCode(roomTypeCode));
            dto.put(ROOM_CLASS_NAME, findRoomClassFromAccomTypeCode(roomTypeCode));
        }

        // Shouldn't return ID since we are returning code
        dto.remove(ACCOM_TYPE_ID);
        return dto;
    }

    private String findRoomTypeNameForCode(String roomTypeCode) {
        if (roomTypeCode.isEmpty()) {
            AccomType accomType = tenantCrudService.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", roomTypeCode).parameters());
            return accomType == null ? roomTypeCode : accomType.getName();
        }
        AccomType accomType = roomTypeCache.get(PacmanWorkContextHelper.getPropertyId(), roomTypeCode);
        return accomType.getName();
    }


    private String findRoomClassFromAccomTypeCode(String roomTypeCode) {
        if (roomTypeCode.isEmpty()) {
            AccomType accomType = tenantCrudService.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", roomTypeCode).parameters());
            return accomType == null ? roomTypeCode : accomType.getAccomClass().getName();
        }
        AccomType accomType = roomTypeCache.get(PacmanWorkContextHelper.getPropertyId(), roomTypeCode);
        return accomType.getAccomClass().getName();
    }

    @Override
    public AccomActivity findExistingOrCreateNewActivity(Integer propertyId, Map<String, Object> dto, String correlationId, boolean isPast) {

        return null;
    }

    @Override
    public List<AccomActivity> findExistingOrCreateNewActivity(Integer propertyId, List<Map<String, Object>> dtos, String correlationId) {
        return Collections.emptyList();
    }

    @Override
    public PaceAccomActivity findExistingOrCreateNewPaceActivity(AccomActivity entity) {
        return null;
    }


    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
