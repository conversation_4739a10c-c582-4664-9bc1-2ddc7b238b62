package com.ideas.tetris.pacman.services.security.login;

import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.infra.tetris.security.servlet.TetrisLoginHelper;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.security.login.mapper.PropertyDetailsMapper;
import com.ideas.tetris.pacman.services.security.login.mapper.PropertyModulePermissionMapper;
import com.ideas.tetris.pacman.services.security.login.vo.AuthorizedUserVO;
import com.ideas.tetris.pacman.services.security.login.vo.ModulePermissionVO;
import com.ideas.tetris.pacman.services.security.login.vo.PropertyVO;
import com.ideas.tetris.pacman.services.userpreferences.UserPreferenceService;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Component
@Transactional
public class LoginRestService {

    @Autowired
    private UserService userService;

    @Autowired
    private PropertyDetailsMapper propertyDetailsMapper;

    @Autowired
    private PropertyModulePermissionMapper propertyModulePermissionMapper;

    @Autowired
    protected PacmanConfigParamsService configParamsService;

    @Autowired
    UserPreferenceService userPreferenceService;

    @Autowired
    AuthorizationService authorizationService;

    @Autowired
    PropertyService propertyService;

    @GlobalCrudServiceBean.Qualifier
    @Qualifier("globalCrudServiceBean")
    @Autowired
    private CrudService globalCrudService;

    private static final Logger LOGGER = Logger.getLogger(LoginRestService.class.getName());

    public AuthorizedUserVO doSSOLogin() {
        try {
            LOGGER.info(String.format("Inside LoginRestService::doSSOLogin() for User ID %s with Client Code %s", PacmanWorkContextHelper.getUserId(), PacmanWorkContextHelper.getClientCode()));
            return getPropertiesAndPermissionsForUser();
        } catch (TetrisException e) {
            String errorMsg = String.format("Encountered error in LoginRestService::doSSOLogin() - '%s' for User ID - %s and Client Code - %s. See below stack trace for more info:", e.getMessage(), PacmanWorkContextHelper.getUserId(), PacmanWorkContextHelper.getClientCode());
            LOGGER.error(errorMsg, e);
            throw e;
        }
    }

    public AuthorizedUserVO doLogin() {
        try {
            LOGGER.info(String.format("Inside LoginRestService::doLogin() for User ID %s with Client Code %s", PacmanWorkContextHelper.getUserId(), PacmanWorkContextHelper.getClientCode()));
            return getPropertiesAndPermissionsForUser();
        } catch (TetrisException e) {
            String errorMsg = String.format("Encountered error in LoginRestService::doLogin() - '%s' for User ID - %s and Client Code - %s. See below stack trace for more info:", e.getMessage(), PacmanWorkContextHelper.getUserId(), PacmanWorkContextHelper.getClientCode());
            LOGGER.error(errorMsg, e);
            throw e;
        }
    }

    public String doLogout(String userName, String auditId, String device, final HttpServletRequest request, final HttpServletResponse response) {
        try {
            LOGGER.info(String.format("Inside LoginRestService::doLogout() for User ID %s with Client Code %s", PacmanWorkContextHelper.getUserId(), PacmanWorkContextHelper.getClientCode()));
            logout(request, response);
            return "SUCCESS";
        } catch (TetrisException e) {
            String errorMsg = String.format("Encountered error in LoginRestService::doLogout() - '%s' for User ID - %s and Client Code - %s. See below stack trace for more info:", e.getMessage(), PacmanWorkContextHelper.getUserId(), PacmanWorkContextHelper.getClientCode());
            LOGGER.error(errorMsg, e);
            throw e;
        }
    }


    public AuthorizedUserVO propertyModulesPermission(String propertyId) {
        try {
            LOGGER.info(String.format("Inside LoginRestService::propertymodulespermission() for User ID %s with Client Code %s", PacmanWorkContextHelper.getUserId(), PacmanWorkContextHelper.getClientCode()));
            return getUserPermissionByProperty(propertyId);
        } catch (Exception e) {
            String errorMsg = String.format("Encountered error in LoginRestService::propertymodulespermission() - '%s' for User ID - %s and Client Code - %s. See below stack trace for more info:", e.getMessage(), PacmanWorkContextHelper.getUserId(), PacmanWorkContextHelper.getClientCode());
            LOGGER.error(errorMsg, e);
            throw e;
        }
    }

    protected void logout(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession(false);
        TetrisLoginHelper.performLogout(request, response);
        session.invalidate();
    }

    public AuthorizedUserVO getUserPermissionByProperty(String propertyId) {
        AuthorizedUserVO authorizedUserVO = new AuthorizedUserVO();
        GlobalUser globalUser = userService.getGlobalUser(Integer.valueOf(PacmanWorkContextHelper.getUserId()), true);
        boolean isMobileOptimizationEnabled = isMobileLoginOptimizationEnabled(globalUser.getClientCode());
        if (isMobileOptimizationEnabled) {
            final Property property = propertyService.getPropertyById(Integer.parseInt(propertyId));
            if (null != property) {
                final List<Property> authorizedProperty = getActiveAuthorizedProperties().stream().filter(x -> x.getId() == property.getId()).collect(Collectors.toList());
                authorizedUserVO.setAssociatedPropertiesObjectMap(
                        propertyDetailsMapper.getAssociatedPropertiesMapOptimized(
                                property, authorizedProperty));
                authorizedUserVO.setPropertiesModulesPermissionsMap(propertyModulePermissionMapper
                        .getPropertyModulePermissionMapOptimized(globalUser, property));
            }
        }
        return authorizedUserVO;
    }


    public AuthorizedUserVO getPropertiesAndPermissionsForUser() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        AuthorizedUserVO authorizedUserVO = new AuthorizedUserVO();
        GlobalUser globalUser = userService.getGlobalUser(Integer.valueOf(PacmanWorkContextHelper.getUserId()), true);
        LDAPUser ldapUser = userService.get(globalUser.getEmail(), globalUser.getClientCode());
        if (ldapUser != null) { //Not sure if this check is needed.
            LOGGER.info(String.format(" Default Property Code for User Id - %d is - %s", globalUser.getId(), ldapUser.getDefaultProperty()));
        }
        Property defaultProperty = null;
        boolean isMobileOptimizationEnabled = isMobileLoginOptimizationEnabled(globalUser.getClientCode());
        if (isMobileOptimizationEnabled) {
            List<Property> activeAuthorizedProperties = getActiveAuthorizedProperties();
            defaultProperty = getDefaultProperty(activeAuthorizedProperties);
            authorizedUserVO.setAssociatedPropertiesObjectMap(
                    propertyDetailsMapper.getAssociatedPropertiesMapOptimized(
                            defaultProperty, activeAuthorizedProperties));
        } else {
            authorizedUserVO.setAssociatedPropertiesObjectMap(propertyDetailsMapper.getAssociatedPropertiesMap());
        }
        stopWatch.stop();
        LOGGER.info("Total time required for fetching caught-up date and capacity:" + stopWatch.toString() + " ms");
        stopWatch.reset();
        stopWatch.start();
        if (isMobileOptimizationEnabled) {
            authorizedUserVO.setPropertiesModulesPermissionsMap(propertyModulePermissionMapper
                    .getPropertyModulePermissionMapOptimized(globalUser, defaultProperty));
        } else {
            authorizedUserVO.setPropertiesModulesPermissionsMap(propertyModulePermissionMapper.getPropertyModulePermissionMap(globalUser));
        }

        LOGGER.info("Time for fetching property module role permissions:" + stopWatch.toString() + " ms");
        stopWatch.stop();
        stopWatch.reset();
        authorizedUserVO.setUserId(PacmanWorkContextHelper.getUserId());
        authorizedUserVO.setUserName(globalUser.getFullName());
        return authorizedUserVO;
    }

    public AuthorizedUserVO getAuthorizedPropertiesWithPermissions(boolean shouldOptimize, Client client){
        //Get authorized properties first
        Property defaultProperty = null;
        AuthorizedUserVO authorizedUserVO = new AuthorizedUserVO();
        Map<Integer, PropertyVO> authorizedProperties = new HashMap<>();
        if (shouldOptimize) {
            List<Property> activeAuthorizedProperties = getActiveAuthorizedPropertiesByClient(client.getId());
            defaultProperty = findDefaultPropertyWithinAuthorizedProperties(activeAuthorizedProperties);
            if (isNull(defaultProperty)) {
                LOGGER.warn("Default property not found for Client: " + client.getName());
            }
            authorizedProperties = propertyDetailsMapper.getAssociatedPropertiesMapOptimized(
                            defaultProperty, activeAuthorizedProperties);
        } else {
            authorizedProperties = propertyDetailsMapper.getAssociatedPropertiesMap();
        }

        authorizedUserVO.setAssociatedPropertiesObjectMap(authorizedProperties);

        //Now lets get the permissions for these properties only for group pricing evaluation
        GlobalUser globalUser = userService.getGlobalUser(Integer.valueOf(PacmanWorkContextHelper.getUserId()), true);
        if (shouldOptimize) {
            Map<Integer, Set<ModulePermissionVO>> propertyModulePermissionMapOptimizedBy = propertyModulePermissionMapper
                    .getPropertyModulePermissionMapOptimizedBy(globalUser, defaultProperty, false,
                            globalUser.isInternal() ? "ideas" : PacmanWorkContextHelper.getClientCode(), client.getId(), getModuleUrlMappingForGroupPricing());
            if(propertyModulePermissionMapOptimizedBy.isEmpty() && nonNull(defaultProperty)){
                LOGGER.info("Property module permission not found for default property : " + defaultProperty.getCode() );
                propertyModulePermissionMapOptimizedBy.put(defaultProperty.getId(), Set.of(new ModulePermissionVO("GroupPricingEvaluation", "NO_ACCESS")));
            }
            authorizedUserVO.setPropertiesModulesPermissionsMap(propertyModulePermissionMapOptimizedBy);
        } else {
            authorizedUserVO.setPropertiesModulesPermissionsMap(propertyModulePermissionMapper.getPropertyModulePermissionMap(globalUser));
        }
        return authorizedUserVO;
    }

    protected Property findDefaultPropertyWithinAuthorizedProperties(List<Property> activeAuthorizedProperties) {
        Property defaultProperty;
        defaultProperty = getDefaultProperty(activeAuthorizedProperties);
        Optional<Property> defaultPropertyOptional = activeAuthorizedProperties.stream().filter(activeProperty -> activeProperty.getId().equals(defaultProperty.getId())).findFirst();
        if(defaultPropertyOptional.isPresent()){
            return defaultProperty;
        }
        return isNotEmpty(activeAuthorizedProperties) ? activeAuthorizedProperties.get(0) : null;
    }

    public Property getDefaultProperty(List<Property> activeAuthorizedProperties) {
        Property property = userPreferenceService.getUserPreferences().getDefaultProperty();
        if (property != null) {
            LOGGER.info("Found default property for user:" + property.getName());
            return property;
        } else {
            LOGGER.info("Default property is not set. If authorized active properties are present, first will be picked");
        }
        return activeAuthorizedProperties.isEmpty() ? null : activeAuthorizedProperties.get(0);
    }

    public List<Property> getActiveAuthorizedProperties() {
        return authorizationService.retrieveActiveAuthorizedProperties();
    }

    public List<Property> getActiveAuthorizedPropertiesByClient(Integer clientId) {
        return authorizationService.retrieveAuthorizedProperties(Integer.valueOf(PlatformThreadLocalContextHolder.getWorkContext().getUserId()), clientId);
    }

    public boolean isMobileLoginOptimizationEnabled(String clientCode) {
        String configValue = configParamsService.getParameterValueByClientLevel(PreProductionConfigParamName.ENABLE_MOBILE_LOGIN_OPTIMIZATION.getParameterName(), clientCode);
        return Boolean.parseBoolean(configValue);
    }

    public Map<Integer, Set<ModulePermissionVO>> getGroupPricingPermissionForProperty(Property property){

        GlobalUser globalUser = userService.getGlobalUser(Integer.valueOf(PacmanWorkContextHelper.getUserId()), true);
        return propertyModulePermissionMapper
                .getPropertyModulePermissionMapOptimizedBy(globalUser, property, false,
                        globalUser.isInternal() ? "ideas" : PacmanWorkContextHelper.getClientCode(),
                        globalUser.isInternal() && nonNull(property) ? property.getClient().getId() : PacmanWorkContextHelper.getClientId(),
                        getModuleUrlMappingForGroupPricing());
    }

    public Map<String, String> getModuleUrlMappingForGroupPricing() {
        Map<String, String> moduleUrlMap = new HashMap<>();
        moduleUrlMap.put("Evaluations", "Evaluations");
        moduleUrlMap.put("Group Pricing Evaluation", "GroupPricingEvaluation");
        return moduleUrlMap;
    }

    public Map<Integer, Set<ModulePermissionVO>> getMeetingPackagePricingPermissionForProperty(Property property){
        GlobalUser globalUser = userService.getGlobalUser(Integer.valueOf(PacmanWorkContextHelper.getUserId()), true);
        return propertyModulePermissionMapper
                .getPropertyModulePermissionMapOptimizedBy(globalUser, property, false,
                        globalUser.isInternal() ? "ideas" : PacmanWorkContextHelper.getClientCode(),
                        globalUser.isInternal() && nonNull(property) ? property.getClient().getId() : PacmanWorkContextHelper.getClientId(),
                        getModuleUrlMapForMeetingPackagePricing());
    }

    public Map<String, String> getModuleUrlMapForMeetingPackagePricing() {
        Map<String, String> moduleUrlMap = new HashMap<>();
        moduleUrlMap.put("Meeting Package Pricing", "MeetingPackagePricing");
        moduleUrlMap.put("Meeting Package Configuration", "MeetingPackageConfiguration");
        moduleUrlMap.put("Base Configuration", "BaseConfiguration");
        moduleUrlMap.put("Base Meeting Room", "BaseMeetingRoom");
        moduleUrlMap.put("Day Parts", "DayParts");
        moduleUrlMap.put("Package Elements", "PackageElements");
        moduleUrlMap.put("Rounding Rules", "RoundingRules");
        moduleUrlMap.put("Meeting Package Product", "MeetingPackageProduct");
        moduleUrlMap.put("Meeting Package Independent Product", "MeetingPackageIndependentProduct");
        moduleUrlMap.put("Definition", "Definition");
        moduleUrlMap.put("Ceiling/Floor", "Ceiling/Floor");
        moduleUrlMap.put("Offsets", "Offsets");
        moduleUrlMap.put("Meeting Package Linked Product", "MeetingPackageLinkedProduct");
        moduleUrlMap.put("Defaults", "Defaults");
        moduleUrlMap.put("Seasons", "Seasons");
        moduleUrlMap.put("Meeting Package Manual Upload BAR Override", "MeetingPackageManualUploadBAROverride");
        return moduleUrlMap;
    }


}
