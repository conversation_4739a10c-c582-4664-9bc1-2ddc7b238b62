package com.ideas.tetris.pacman.services.analytics.services;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.activity.service.MarketSegmentActivityService;
import com.ideas.tetris.pacman.services.analytics.dto.*;
import com.ideas.tetris.pacman.services.bookingpace.dto.ForecastGroupOccFcstDto;
import com.ideas.tetris.pacman.services.bookingpace.dto.ForecastGroupSoldDto;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.forecast.OccupancyForecastService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.roa.processgroup.entity.DowGroupDetail;
import com.ideas.tetris.pacman.services.roa.processgroup.entity.ProcessGroupConfigurationDetails;
import com.ideas.tetris.pacman.services.roa.processgroup.entity.SeasonGroupDetail;
import com.ideas.tetris.pacman.services.roa.referenceprice.entity.HorizonGroupDetail;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.Stage;
import lombok.RequiredArgsConstructor;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertLocalDateToJavaUtilDate;
import static java.util.stream.Collectors.*;

@Service
@RequiredArgsConstructor
public class BookingPaceInputBuilderService {
    public static final int DEFAULT_LOS = -1;
    public static final int PROJECTED_BOOKING_PACE_FORECAST_WINDOW = 120;
    private final BookingCurveService bookingCurveService;
    private final IpcfgSeasonGroupDetailService seasonGroupDetailService;
    private final IpcfgDowGroupDetailService dowGroupDetailService;
    private final IpcfgHorizonGroupDetailService horizonGroupDetailService;
    private final FileMetadataService fileMetadataService;
    private final IpcfgProcessGroupService processGroupService;
    private final MarketSegmentActivityService marketSegmentActivityService;
    private final OccupancyForecastService occupancyForecastService;
    private final SpecialEventService specialEventService;
    private final PropertyService propertyService;
    Logger logger = Logger.getLogger(BookingPaceInputBuilderService.class);


    public BookingPaceInput build() {
        try {
            LocalDate systemDate = fileMetadataService.getCaughtUpLocalDate();
            LocalDate lastOccupancyDate = systemDate.plusDays(PROJECTED_BOOKING_PACE_FORECAST_WINDOW);
            List<ProcessGroupConfigurationDetails> processGroupConfigurationDetails = processGroupService.getProcessGroupConfigurationDetails();
            Set<Integer> forecastGroupIds = getDistinct(processGroupConfigurationDetails, ProcessGroupConfigurationDetails::getForecastGroupId);
            Set<Integer> accomClassIds = getDistinct(processGroupConfigurationDetails, ProcessGroupConfigurationDetails::getAccomClassId);

            Set<Integer> distinctSeasonGroupIds = getDistinct(processGroupConfigurationDetails, ProcessGroupConfigurationDetails::getSeasonGroupId);
            if(processGroupConfigurationDetails.isEmpty() || forecastGroupIds.isEmpty() || accomClassIds.isEmpty() || distinctSeasonGroupIds.isEmpty()) {
                return BookingPaceInput.builder().build();
            }
            List<SeasonGroupDetail> seasonGroupDetails = seasonGroupDetailService.getSeasonGroupDetails(systemDate, lastOccupancyDate, distinctSeasonGroupIds);
            BiFunction<Integer, Integer, ProcessGroupConfigDetailResolver> pgcdResolverByFgByRc = getProcessGroupConfigDetailResolverByFgByRc(processGroupConfigurationDetails, seasonGroupDetails);

            Map<BookingCurveKey, Double> bookingCurve = getBookingCurve(seasonGroupDetails, forecastGroupIds, accomClassIds);
            Map<ForecastGroupAccomClassKey, NavigableMap<LocalDate, List<BookingCurveOverride>>> overridesMap = getBookingCurveOverriden(systemDate, lastOccupancyDate );
            List<PropertySpecialEventInstance> specialEventsBetweenDates = specialEventService.getSpecialEventsBetweenEffectiveDates(convertLocalDateToJavaUtilDate(systemDate), convertLocalDateToJavaUtilDate(lastOccupancyDate));
            Map<BookingCurveEventKey, Double> bookingCurveEventMap = getSpecialEventBookingCurveMap(forecastGroupIds, accomClassIds, specialEventsBetweenDates);
            Map<LocalDate, List<Integer>> specialEventIdsByDate = specialEventsBetweenDates.stream()
                    .flatMap(p -> p.getEffectiveDateRange().stream().map(d -> Map.entry(d, p.getPropertySpecialEvent().getId())))
                    .collect(groupingBy(Map.Entry::getKey, Collectors.mapping(Map.Entry::getValue, Collectors.toList())));
            return BookingPaceInput.builder()
                    .systemDate(systemDate)
                    .lastOccupancyDate(lastOccupancyDate)
                    .solds(getSolds(systemDate, lastOccupancyDate))
                    .forecast(getForecast(systemDate, lastOccupancyDate))
                    .forecastGroupIds(forecastGroupIds)
                    .accomClassIds(accomClassIds)
                    .bookingCurve(bookingCurve)
                    .bookingCurveOverrides(overridesMap)
                    .bookingCurveEvent(bookingCurveEventMap)
                    .specialEventIdsByDate(specialEventIdsByDate)
                    .pgcdResolveByFgByRc(pgcdResolverByFgByRc)
                    .build();
        } catch (Exception e) {
            if(Stage.ONE_WAY.equals(propertyService.getPropertyStage(PacmanWorkContextHelper.getPropertyId()))) {
                // If the property is ONE_WAY or higher, we can log the error and return an empty BookingPaceInput
                logger.warn(String.format("Error building BookingPaceInput for property %s, returning empty input. Cause: %s", PacmanWorkContextHelper.getPropertyId(), e.getMessage()), e.getCause());
                return BookingPaceInput.builder().build();
            }
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error building BookingPaceInput", e.getCause());
        }
    }

    private BiFunction<Integer, Integer, ProcessGroupConfigDetailResolver> getProcessGroupConfigDetailResolverByFgByRc(
            List<ProcessGroupConfigurationDetails> processGroupConfigurationDetails,
            List<SeasonGroupDetail> seasonGroupDetails) {
        var distinctReadingHorizonGroupIds = getDistinct(processGroupConfigurationDetails, ProcessGroupConfigurationDetails::getReadingHorizonGroupId);
        var horizonGroupDetails = horizonGroupDetailService.getHorizonGroupDetails(distinctReadingHorizonGroupIds);
        var distinctDowGroupIds = getDistinct(processGroupConfigurationDetails, ProcessGroupConfigurationDetails::getDowGroupId);
        var dowGroupDetails = dowGroupDetailService.getDowGroupDetails(distinctDowGroupIds);


        var sgdById = seasonGroupDetails.stream().collect(groupingBy(SeasonGroupDetail::getSeasonGroupId));
        var dgdById = dowGroupDetails.stream().collect(groupingBy(DowGroupDetail::getDowGroupId));
        var hgdById = horizonGroupDetails.stream().collect(groupingBy(HorizonGroupDetail::getHorizonGroupId));
        var pgcdByFgByRc = processGroupConfigurationDetails.stream()
                .collect(groupingBy(ProcessGroupConfigurationDetails::getAccomClassId,
                collectingAndThen(toList(), l -> l.stream()
                        .collect(toMap(ProcessGroupConfigurationDetails::getForecastGroupId,
                                p -> new ProcessGroupConfigDetailResolver(
                                        sgdById.getOrDefault(p.getSeasonGroupId(), List.of()),
                                        dgdById.getOrDefault(p.getDowGroupId(), List.of()),
                                        hgdById.getOrDefault(p.getReadingHorizonGroupId(), List.of())))))));
        return (rc, fg) -> pgcdByFgByRc.getOrDefault(rc, Map.of()).getOrDefault(fg, new ProcessGroupConfigDetailResolver(List.of(), List.of(), List.of()));
    }

    private Map<BookingCurveEventKey, Double> getSpecialEventBookingCurveMap(Collection<Integer> distinctForecastGroupIds, Collection<Integer> distinctAccomClassIds, List<PropertySpecialEventInstance> specialEventsBetweenDates) {
        List<Integer> distinctEventIds = specialEventsBetweenDates.stream().map(p -> p.getPropertySpecialEvent().getId()).distinct().collect(Collectors.toList());
        List<BookingCurveEvent> bookingCurveEvent = bookingCurveService.getBookingCurveEvent(distinctEventIds, distinctForecastGroupIds, distinctAccomClassIds, DEFAULT_LOS);
        return bookingCurveEvent.stream()
                .collect(Collectors.toMap(
                        b -> new BookingCurveEventKey(
                                b.getForecastGroupId().intValue(),
                                b.getRoomClassId().intValue(),
                                b.getEventId().intValue(),
                                b.getHorizonGroupNumber().intValue()
                        ),
                        BookingCurveEvent::getValue));
    }

    private Map<ForecastGroupAccomClassKey, NavigableMap<LocalDate, List<BookingCurveOverride>>> getBookingCurveOverriden(LocalDate systemDate, LocalDate lastOccupancyDate) {
        List<BookingCurveOverride> bookingCurveOverrides = bookingCurveService.getBookingCurveOverride(systemDate, lastOccupancyDate, DEFAULT_LOS);
        return bookingCurveOverrides.stream()
                .collect(groupingBy(
                        override -> new ForecastGroupAccomClassKey(override.getForecastGroupId(),
                                override.getAccomClassId()),
                        groupingBy(BookingCurveOverride::getStartDate, TreeMap::new, Collectors.toList())
                ));
    }

    private Map<ForecastAccomClassDateKey, Double> getForecast(LocalDate systemDate, LocalDate lastOccupancyDate) {
        List<ForecastGroupOccFcstDto> forecastByForecastGroupByAccomClass = occupancyForecastService.getForecastByForecastGroupByAccomClass(systemDate, lastOccupancyDate);
        return forecastByForecastGroupByAccomClass.stream()
                .collect(Collectors.toMap(BookingPaceInputBuilderService::getKey, ForecastGroupOccFcstDto::getOccupacyForecast));
    }
    private Map<ForecastAccomClassDateKey, Integer> getSolds(LocalDate systemDate, LocalDate lastOccupancyDate) {
        List<ForecastGroupSoldDto> soldByForecastGroupByAccomClass = marketSegmentActivityService.getSoldByForecastGroupByAccomClass(systemDate, lastOccupancyDate);
        return soldByForecastGroupByAccomClass.stream()
                .collect(Collectors.toMap(BookingPaceInputBuilderService::getKey, ForecastGroupSoldDto::getSolds));
    }

    private Map<BookingCurveKey, Double> getBookingCurve(List<SeasonGroupDetail> seasonGroupDetails, Set<Integer> distinctForecastGroupIds, Set<Integer> distinctAccomClassIds) {
        List<Integer> seasonGroupNumbers = getSeasonGroupNumbers(seasonGroupDetails);
        List<BookingCurve> bookingCurves = bookingCurveService.getBookingCurve(seasonGroupNumbers, distinctForecastGroupIds, distinctAccomClassIds, DEFAULT_LOS);
        return bookingCurves.stream()
                .collect(Collectors.toMap(BookingPaceInputBuilderService::getBookingCurveKey, BookingCurve::getValue));
    }

    private static BookingCurveKey getBookingCurveKey(BookingCurve bookingCurve) {
        return new BookingCurveKey(
                bookingCurve.getForecastGroupId().intValue(),
                bookingCurve.getRoomClassId().intValue(),
                bookingCurve.getSeasonGroupNumber().intValue(),
                bookingCurve.getDowGroupNumber().intValue(),
                bookingCurve.getHorizonGroupNumber().intValue()
        );
    }

    private static ForecastAccomClassDateKey getKey(ForecastGroupOccFcstDto dto) {
        return new ForecastAccomClassDateKey(dto.getForecastGroupId(),
                dto.getAccomClassId(),
                dto.getOccupancyDate()
        );
    }

    private static ForecastAccomClassDateKey getKey(ForecastGroupSoldDto forecastGroupSoldDto) {
        return new ForecastAccomClassDateKey(forecastGroupSoldDto.getForecastGroupId(),
                forecastGroupSoldDto.getAccomClassId(),
                forecastGroupSoldDto.getOccupancyDate()
        );
    }


    private static Set<Integer> getDistinct(List<ProcessGroupConfigurationDetails> processGroupConfigurationDetails, Function<ProcessGroupConfigurationDetails, Integer> field) {
        return processGroupConfigurationDetails.stream().map(field).collect(Collectors.toSet());
    }

    public static List<Integer> getSeasonGroupNumbers(List<SeasonGroupDetail> seasonGroupDetails) {
        return seasonGroupDetails
                .stream()
                .map(SeasonGroupDetail::getSeasonGroupNumber)
                .collect(Collectors.toList());
    }
}
