package com.ideas.tetris.pacman.services.reports.bookingpace.dto;

import java.math.BigDecimal;
import java.util.Date;

public class BookingPaceDataLevelDTO {

    /*
     * Rooms_Sold numeric no 9 18 0
     * SnapShot_DTTM date no 3 10 0
     * daystoArrival int no 4 10 0
     * dow varchar no 10
     * businessdate date no 3 10 0
     * Forecast_Group_Name nvarchar no 200
     * Occupancy_NBR numeric no 5 8 2
     *
     */

    private BigDecimal roomSold;
    private Date captureDate;
    private Integer paceDays;
    private String dow;
    private Date businessDate;
    private String levelName;
    private BigDecimal forecast;
    private BigDecimal effectiveCapacity;
    private BigDecimal authorizedCapacity;

    public BigDecimal getRoomSold() {
        return roomSold;
    }

    public void setRoomSold(BigDecimal roomSold) {
        this.roomSold = roomSold;
    }

    public Date getCaptureDate() {
        return captureDate;
    }

    public void setCaptureDate(Date captureDate) {
        this.captureDate = captureDate;
    }

    public Integer getPaceDays() {
        return paceDays;
    }

    public void setPaceDays(Integer paceDays) {
        this.paceDays = paceDays;
    }

    public String getDow() {
        return dow;
    }

    public void setDow(String dow) {
        this.dow = dow;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }

    public BigDecimal getForecast() {
        return forecast;
    }

    public void setForecast(BigDecimal forecast) {
        this.forecast = forecast;
    }

    public BigDecimal getEffectiveCapacity() {
        return effectiveCapacity;
    }

    public void setEffectiveCapacity(BigDecimal effectiveCapacity) {
        this.effectiveCapacity = effectiveCapacity;
    }

    public BigDecimal getAuthorizedCapacity() {
        return authorizedCapacity;
    }

    public void setAuthorizedCapacity(BigDecimal authorizedCapacity) {
        this.authorizedCapacity = authorizedCapacity;
    }
}
