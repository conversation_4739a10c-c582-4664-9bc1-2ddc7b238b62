package com.ideas.tetris.pacman.services.reports.outputoverride;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.outputoverride.dto.OutputOverrideInventoryLimitDTO;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.JasperReportDataConverter;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.OutputOverrideReportCriteria;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportData;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.scheduledreport.service.JasperReportService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by idnsaw on 7/8/2016.
 */
@Component
@Transactional
public class OutputOverrideInventoryLimitService extends JasperReportService<ScheduledReportData, OutputOverrideReportCriteria> {
    @Autowired
    private OutputOverrideService outputOverrideService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    CrudService tenantCrudService;

    @Override
    protected JasperReportDataConverter<ScheduledReportData, OutputOverrideReportCriteria> getJasperReportDataConverter() {
        return null;
    }

    @Override
    public String getReportTitle(ScheduledReport<OutputOverrideReportCriteria> scheduledReport) {
        return ResourceUtil.getText("overrideReport.inventorylimit.title", scheduledReport.getLanguage());
    }

    @Override
    protected ScheduledReportData retrieveData(ScheduledReport<OutputOverrideReportCriteria> scheduledReport) {
        OutputOverrideReportCriteria criteria = scheduledReport.getReportCriteria();
        populateReportCriteria(criteria);
        List<OutputOverrideInventoryLimitDTO> dataList = outputOverrideService.getOverrideInventoryLimitData(criteria.getStartDate(),
                criteria.getEndDate(), criteria.getIsRollingDate(), criteria.getRollingStartDate(), criteria.getRollingEndDate(), scheduledReport.getLanguage());
        ScheduledReportSheet sheet = new ScheduledReportSheet("overrideReport.inventorylimit.title", dataList, OutputOverrideInventoryLimitDTO.class);
        List<ScheduledReportSheet> sheetList = new ArrayList<>(1);
        sheetList.add(sheet);
        return new ScheduledReportData("overrideReport.inventorylimit.title", sheetList);
    }

    private void populateReportCriteria(OutputOverrideReportCriteria reportCriteria) {
        String propertyId = reportCriteria.getPropertyId().toString();
        String userId = reportCriteria.getUserId().toString();
        String baseCurrency = reportCriteria.getCurrency();

        LocalDate startDate = reportCriteria.getStartDate();
        LocalDate endDate = reportCriteria.getEndDate();
        Integer rolling = reportCriteria.getIsRollingDate();
        String rollingStartDate = reportCriteria.getRollingStartDate();
        String rollingEndDate = reportCriteria.getRollingEndDate();

        String sql = " select * from dbo.ufn_get_filter_selection " +
                "('" + propertyId + "'," + userId + ",'" + baseCurrency + "'," +
                "'" + rolling + "'," +
                "'" + startDate + "'," +
                "'" + endDate + "','','','','','',''," +
                "'" + rollingStartDate + "'," +
                "'" + rollingEndDate + "','','','','','','' )";

        List<Object[]> resultList = tenantCrudService.findByNativeQuery(sql);
        if (resultList == null) {
            return;
        }
        Object[] result = resultList.get(0);
        reportCriteria.setPropertyName((String) result[0]);
        reportCriteria.setCreatedBy((String) result[1]);
        LocalDate localStartDate = DateUtil.convertJavaUtilDateToLocalDate((Date) result[3], true);
        reportCriteria.setCreatedOn(JavaLocalDateUtils.toJodaDateTime(ZonedDateTime.now()));
        reportCriteria.setStartDate(localStartDate);
        LocalDate localEndDate = DateUtil.convertJavaUtilDateToLocalDate((Date) result[4], true);
        reportCriteria.setEndDate(localEndDate);
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }
}
