package com.ideas.tetris.pacman.services.reports.bookingsituation.util;

import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.BookingSituationReportCriteria;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;

import java.util.HashMap;
import java.util.Map;

import static com.ideas.tetris.pacman.services.reports.ReportService.SCHEDULE_PARAM_SEPARATOR;

/**
 * Created by idnsru on 9/28/2016.
 */
public class BookingSituationReportUtility {
    public static String getTitle(BookingSituationReportCriteria criteria, Language lang) {
        if (criteria.isHotelChecked()) {
            return ResourceUtil.getText("booking.situation.report.at.hotel.level", lang);
        } else if (criteria.isTransientChecked()) {
            return ResourceUtil.getText("booking.situation.report.at.transient.level", lang);
        } else if (criteria.isGroupChecked()) {
            return ResourceUtil.getText("booking.situation.report.at.group.level", lang);
        } else if (criteria.isFgChecked()) {
            return ResourceUtil.getText("booking.situation.report.at.forecast.group.level", lang);
        } else if (criteria.isBvChecked()) {
            return ResourceUtil.getText("booking.situation.report.at.business.view.level", lang);
        } else {
            return ResourceUtil.getText("booking-situation-report", lang);
        }
    }

    public static Map<String, String> parseReportParams(String reportParameters) {

        Map<String, String> reportParamsMap = new HashMap<>();

        for (String reportParam : reportParameters.split(SCHEDULE_PARAM_SEPARATOR)) {
            String[] reportParamNameValue = reportParam.split("=");
            if (reportParamNameValue.length > 1) {
                reportParamsMap.put(reportParamNameValue[0], reportParamNameValue[1]);
            }
        }
        return reportParamsMap;
    }
}
