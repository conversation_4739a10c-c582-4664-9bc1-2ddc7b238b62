package com.ideas.tetris.pacman.services.security;


import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.platform.common.cache.AbstractCache;
import com.ideas.tetris.platform.common.crudservice.CrudService;

import javax.inject.Inject;
import java.util.Optional;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class GlobalRoleCache extends AbstractCache<Integer, GlobalRole> {

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;

    @Override
    protected GlobalRole loadKey(Integer key) {
        return globalCrudService.find(GlobalRole.class, key);
    }

    @Override
    protected boolean isAsync() {
        return true;
    }

    @Override
    protected Optional<Integer> getRedisLifespan() {
        return Optional.of(300000);
    }
}
