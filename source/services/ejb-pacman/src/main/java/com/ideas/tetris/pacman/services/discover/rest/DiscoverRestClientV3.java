package com.ideas.tetris.pacman.services.discover.rest;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.services.discover.entity.DiscoverUser;
import com.ideas.tetris.pacman.services.discover.services.DiscoverAccessTokenService;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import javax.inject.Inject;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.Response;
import java.util.Collections;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
@RequiredArgsConstructor
public class DiscoverRestClientV3 {

    private static final Logger LOGGER = Logger.getLogger(DiscoverRestClientV3.class);
    private final Client client = ClientBuilder.newBuilder().build();

    @Autowired
    private DiscoverRestClientHelper discoverRestClientHelper;
    @Autowired
	private DiscoverAccessTokenService discoverAccessTokenService;

    public DiscoverUser get(DiscoverRestEndPoints restEndpoint, Map<String, String> parameters) {
        LOGGER.debug("Getting discover user using discover V3 API");
        String urlToCall = discoverRestClientHelper.createGetOrPostUrl(restEndpoint, parameters, true);
        JSONObject result = makeRestCallAndLoadResults(urlToCall, buildHeader());
        return result == null ? null : discoverRestClientHelper.getDiscoverUserFromResponse(result);
    }

    @SuppressWarnings("unchecked")
    public void put(DiscoverRestEndPoints discoverRestEndPoints, String discoverUserId, Entity<?> entity) {
        LOGGER.debug("Updating discover user using discover V3 API");
        discoverRestClientHelper.validateParams(discoverRestEndPoints, entity);
        WebTarget target = client.target(discoverRestClientHelper.createPutUrl(discoverRestEndPoints, discoverUserId, true));
        Response response = null;
        try {
            response = target.request().headers(buildHeader()).put(entity);
            discoverRestClientHelper.checkResponse(response);
        } catch (Exception ce) {
            LOGGER.error("Error occurred while communicating with Discover at this time");
            throw new TetrisException(ErrorCode.DISCOVER_REST_CLIENT_FAILURE, "discover.error.while.communicating.with.server", ce);
        } finally {
            discoverRestClientHelper.close(response);
        }
    }

    @SuppressWarnings("unchecked")
    public DiscoverUser post(DiscoverRestEndPoints discoverRestEndpoint, Entity<?> entity) {
        LOGGER.debug("Creating discover user using discover V3 API");
        WebTarget target = client.target(
                discoverRestClientHelper.createGetOrPostUrl(discoverRestEndpoint, Collections.emptyMap(), true));
        Response response = null;
        DiscoverUser discoverUser = null;
        try {
            response = target.request().headers(buildHeader()).post(entity);
            discoverRestClientHelper.checkResponse(response);
            JSONObject result = new JSONObject(response.readEntity(String.class));
            if (result.has("user")) {
                result = result.getJSONObject("user");
            }
            discoverUser = discoverRestClientHelper.getDiscoverUserFromResponse(result);
        } catch (Exception ce) {
            LOGGER.error("Error occurred while creating user in Discover.", ce);
            throw new TetrisException(ErrorCode.DISCOVER_REST_CLIENT_FAILURE, "discover.error.occurred.while.creating.new.user", ce);
        } finally {
            discoverRestClientHelper.close(response);
        }
        return discoverUser;
    }

    private MultivaluedHashMap buildHeader() {
        return discoverAccessTokenService.buildHeaders(true);
    }

    private JSONObject makeRestCallAndLoadResults(String urlToCall, MultivaluedHashMap headers) {
        String responseString = discoverRestClientHelper.makeRestCallAndReturnResponse(urlToCall, headers);
        return handleResponse(responseString);
    }

    private JSONObject handleResponse(String responseString) {
        JSONObject result = null;
        try {
            JSONObject jsonResponse = new JSONObject(responseString);
            JSONArray users = jsonResponse.getJSONArray("users");
            if (users.length() > 0) {
                LOGGER.debug("discover user found");
                result = (JSONObject) users.get(0);
            } else {
                LOGGER.debug("discover user not found");
            }
        } catch (JSONException jsonException) {
            LOGGER.error(jsonException);
            throw new TetrisException(ErrorCode.DISCOVER_REST_CLIENT_FAILURE, jsonException.getMessage());
        }
        return result;
    }

    public String generateAccessToken() {
        MultivaluedHashMap map = discoverAccessTokenService.buildHeaders(true);
        if (MapUtils.isEmpty(map) || !map.containsKey("Authorization")) {
            return null;
        }
        return String.valueOf(map.get("Authorization"));
    }
}
