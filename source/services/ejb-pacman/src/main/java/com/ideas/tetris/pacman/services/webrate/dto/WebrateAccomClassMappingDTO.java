package com.ideas.tetris.pacman.services.webrate.dto;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;

import java.util.Date;
import java.util.List;
import java.util.Set;

public class WebrateAccomClassMappingDTO {
    private int webrateAccomTypeId;
    private String webrateAccomTypeAlias;
    private String webrateAccomName;
    private Date createDate;
    private List<String> competitors;
    private boolean shouldDelete;
    private AccomClass accomClass;
    private Set<AccomClass> accomClasses;

    public WebrateAccomClassMappingDTO() {
        // Do nothing
    }

    public int getWebrateAccomTypeId() {
        return webrateAccomTypeId;
    }

    public void setWebrateAccomTypeId(int webrateAccomTypeId) {
        this.webrateAccomTypeId = webrateAccomTypeId;
    }

    public String getWebrateAccomTypeAlias() {
        return webrateAccomTypeAlias;
    }

    public void setWebrateAccomTypeAlias(String webrateAccomTypeAlias) {
        this.webrateAccomTypeAlias = webrateAccomTypeAlias;
    }

    public String getWebrateAccomName() {
        return webrateAccomName;
    }

    public void setWebrateAccomName(String webrateAccomName) {
        this.webrateAccomName = webrateAccomName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public List<String> getCompetitors() {
        return competitors;
    }

    public void setCompetitors(List<String> competitors) {
        this.competitors = competitors;
    }

    public boolean isShouldDelete() {
        return shouldDelete;
    }

    public void setShouldDelete(boolean shouldDelete) {
        this.shouldDelete = shouldDelete;
    }

    public AccomClass getAccomClass() {
        return accomClass;
    }

    public void setAccomClass(AccomClass accomClass) {
        this.accomClass = accomClass;
    }

    public Set<AccomClass> getAccomClasses() {
        return accomClasses;
    }

    public void setAccomClasses(Set<AccomClass> accomClasses) {
        this.accomClasses = accomClasses;
    }
}
