package com.ideas.tetris.pacman.services.dailybar.dto;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "V5iOutput")
public class DailyBarDecisionContainer {

    private DailyBarDecisions dailyBarDecisions;

    @XmlElement(name = "PMSDecisions")
    public DailyBarDecisions getDailyBarDecisions() {
        return dailyBarDecisions;
    }

    public void setDailyBarDecisions(DailyBarDecisions dailyBarDecisions) {
        this.dailyBarDecisions = dailyBarDecisions;
    }
}
