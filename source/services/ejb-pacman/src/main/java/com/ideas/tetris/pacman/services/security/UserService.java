package com.ideas.tetris.pacman.services.security;

import com.ideas.infra.tetris.security.*;
import com.ideas.infra.tetris.security.cognito.CognitoLoginHelper;
import com.ideas.infra.tetris.security.cognito.CognitoUserManagementService;
import com.ideas.infra.tetris.security.cognito.dto.Name;
import com.ideas.infra.tetris.security.cognito.dto.UpdateUserRequest;
import com.ideas.infra.tetris.security.cognito.exception.CognitoException;
import com.ideas.infra.tetris.security.domain.AuthGroupRoleMapping;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.infra.tetris.security.domain.PropertyRoleMapping;
import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.infra.tetris.security.sso.OpenAMException;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.common.workcontext.WorkContextHelper;
import com.ideas.tetris.pacman.services.client.service.ClientAgentConfigService;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.clientdatamanagement.LdapUserConverter;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.LearningClientUser;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.PropertyGroup;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.PropertyPropertyGroup;
import com.ideas.tetris.pacman.services.contextholder.PacmanThreadLocalService;
import com.ideas.tetris.pacman.services.contextholder.WorkContextRestEasyController;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.discover.services.DiscoverService;
import com.ideas.tetris.pacman.services.email.EmailService;
import com.ideas.tetris.pacman.services.fds.uas.UASService;
import com.ideas.tetris.pacman.services.fds.uis.UISService;
import com.ideas.tetris.pacman.services.fds.uis.model.UISUserV2;
import com.ideas.tetris.pacman.services.fds.uis.model.entitlement.EntitlementV2;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.reports.ReportService;
import com.ideas.tetris.pacman.services.security.dto.UserPreferencesUtil;
import com.ideas.tetris.pacman.util.string.EmailUtil;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.errorhandling.TetrisSecurityException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.AuthorizationGroup;
import com.ideas.tetris.platform.services.daoandentities.entity.AuthorizationGroupPropertyMapping;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import com.ideas.tetris.platform.services.globalproperty.service.GlobalPropertyService;
import com.ideas.tetris.platform.services.util.bean.BeanLocator;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang.time.StopWatch;
import org.apache.log4j.Logger;
import org.hibernate.Hibernate;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.json.simple.JSONObject;
import org.opends.sdk.DN;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseCookie;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.OptimisticLockException;
import javax.security.auth.login.LoginException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.DatatypeConverter;
import javax.xml.bind.annotation.adapters.HexBinaryAdapter;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.ideas.infra.tetris.security.cognito.config.SystemConfig.shouldUseCookiesForCognitoLoginExternalSSOUsers;
import static com.ideas.infra.tetris.security.cognito.util.Constants.*;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.*;
import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.services.security.dto.UserPreference.DEFAULT_DATE_FORMAT;
import static com.ideas.tetris.pacman.services.security.dto.UserPreference.*;
import static com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.*;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang.StringUtils.EMPTY;

@SuppressWarnings("squid:S2068")
@Component
@Transactional
public class UserService {
    public static final int PASSWORD_LENGTH_GENERATED = 9;
    public static final String DD_MMM_YYYY = "dd-mmm-yyyy";
    public static final String DEFAULT_STANDARD_FORMAT_OFF = "MM/dd/yyyy";
    public static final String INVALID_USER = "Invalid User";
    public static final String PACMAN = "pacman";
    public static final String PACMAN_DOT = PACMAN + ".";
    public static final String PASSWORD_STRING = "password";
    public static final String CLIENT_CODE = "clientCode";
    public static final String TIMES = " times";
    public static final String STATUS = "status";
    public static final String REST_BASE_URL = "/pacman-platformsecurity/rest/";
    private static final String EMAIL_BODY = "<!DOCTYPE html> "
            + "<html>"
            + "<body>"
            + "The properties available to you in IDeaS G3 RMS have changed, and your default property is no longer valid. "
            + "You must log in and select a new default property in your preferences from the Welcome menu. "
            + "Click <a href=\"http://g3.ideas.com/help/Default.htm#cshid=20\">here</a> for help with setting your preferences.</body></html>";
    private static final String LIST_ALL_USERS_FOR_CLIENT_PROPETY = "SELECT * from Users u LEFT OUTER JOIN User_Individual_Property_Role uip ON u.User_ID = uip.User_ID and uip.Property_ID = :propertyId "
            + "WHERE  u.Client_Code = :clientCode and u.Status_ID = 1 and (uip.Property_ID = :propertyId OR  u.User_Preferences like :likeClause )";
    private static final String EMAIL_SUBJECT = "Properties Change in IDeaS G3 RMS";
    private static final Logger LOGGER = Logger.getLogger(UserService.class);
    private static final String INTERNAL = "internal";
    private static final String EXTERNAL = "external";
    private static final String PASSWORD_COMPOSITION_CHARS = "~!@#$%^&*()_+|-=\\{}][:\";><`?,./" +
            "abcdefghijklmnopqrstuvwzyx" +
            "ABCDEFGHIJKLMNOPQRSTUVWXYZ" +
            "0123456789";
    private static final int PASSWORD_HISTORY_COUNT = 4;
    private static final String SALT = "YYLmfY6IehjZMQ";
    private static final String CERT_USER_EMAIL_TMPL = "G3RestCertUser@{0}.com";
    private static final String PROPERTY_STRING = "property";
    private static final String MESSAGE_DIGEST_FAILURE = "Message digest failure: ";
    private final Pattern hasUppercase = Pattern.compile("[A-Z]");
    private final Pattern hasLowercase = Pattern.compile("[a-z]");
    private final Pattern hasNumber = Pattern.compile("\\d");
    private final Pattern hasSpecialChar = Pattern.compile("[~,!,@,#,$,%,^,&,*,(,),_,+,|,\\-,=,\\\\,{,},\\[,\\],:,\",;,',<,>,?,,,.,\\/]");
    @GlobalCrudServiceBean.Qualifier
    @Qualifier("globalCrudServiceBean")
    @Autowired
    private CrudService globalCrudService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
    private CrudService tenantCrudService;
    @Autowired
    private UserSynchronizationServiceLocal syncService;
    @Autowired
    private AuthorizationService authorizationService;
    @Autowired
    private EmailService emailService;
    @Autowired
    private PacmanThreadLocalService pacmanThreadLocalService;
    @Autowired
    private PropertyGroupService propertyGroupService;
    @Autowired
    private WorkContextRestEasyController workContextRestEasyController;
    @Autowired
    private PropertyService propertyService;
    @Autowired
    private UserGlobalDBService userGlobalDBService;
    @Autowired
    private PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    private UserAuthorizedPropertyCache userAuthorizedPropertyCache;
    @Autowired
    private PasswordSecurityService passwordSecurityService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private LdapUserConverter ldapUserConverter;
    @Autowired
    private JobServiceLocal jobServiceLocal;
    @Autowired
    private ReportService reportService;
    @Autowired
    UISService uisService;
    @Autowired
	private ClientPropertyCacheService clientPropertyCacheService;
    @Autowired
	private DiscoverService discoverService;
    @Autowired
    UASService uasService;
    @Autowired
    GlobalPropertyService globalPropertyService;
    @Autowired
    ClientService clientService;

    public static String getAgentUserEmail(String clientCode) {
        return MessageFormat.format(ClientAgentConfigService.AGENT_EMAIL_TMPL, clientCode);
    }

    public static String getG3CertUserEmail(String clientCode) {
        return MessageFormat.format(CERT_USER_EMAIL_TMPL, clientCode);
    }

    // Several test classes - not moving all to Mockito just yet
    public void setUserAuthorizedPropertyCache(UserAuthorizedPropertyCache userAuthorizedPropertyCache) {
        this.userAuthorizedPropertyCache = userAuthorizedPropertyCache;
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public void setSyncService(UserSynchronizationServiceLocal syncService) {
        this.syncService = syncService;
    }

    public void setAuthorizationService(AuthorizationService authorizationService) {
        this.authorizationService = authorizationService;
    }

    public void setEmailService(EmailService emailService) {
        this.emailService = emailService;
    }

    public PropertyGroupService getPropertyGroupService() {
        return this.propertyGroupService;
    }

    public void setPropertyGroupService(PropertyGroupService propertyGroupService) {
        this.propertyGroupService = propertyGroupService;
    }

    public void setWorkContextService(WorkContextRestEasyController workContextRestEasyController) {
        this.workContextRestEasyController = workContextRestEasyController;
    }

    public void setPacmanThreadLocalService(PacmanThreadLocalService value) {
        pacmanThreadLocalService = value;
    }

    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    public BeanLocator getBeanLocator() {
        return new BeanLocator();
    }


    public String isSSOEnabled() {
        return pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.SSOENABLED.value());
    }

    public Map<String, String> getDiscoverVisibilityDeciders() {
        Map<String, String> hasLearnAccessData = new HashMap<>();
        hasLearnAccessData.put("isLearningAccessEnabled", String.valueOf(isLearningAccessEnabled(PacmanWorkContextHelper.getClientCode())));
        hasLearnAccessData.put("hasLearningAccess", String.valueOf(userGlobalDBService.getGlobalUserById(Integer.parseInt(PacmanWorkContextHelper.getUserId())).getLearningAccess()));
        return hasLearnAccessData;
    }

    public boolean isLearningAccessEnabled(String clientCode) {
        return "true".equalsIgnoreCase(pacmanConfigParamsService.getValue(Constants.getClientConfigContext(clientCode), FeatureTogglesConfigParamName.LEARNING_ACCESS_ENABLED.value()));
    }

    public boolean isLearningAccessGivenByDefault(String clientCode) {
        return isLearningAccessEnabled(clientCode) && isLearningAccessGivenByDefaultEnabled(clientCode);
    }

    private boolean isLearningAccessGivenByDefaultEnabled(String clientCode) {
        return "true".equalsIgnoreCase(pacmanConfigParamsService.getValue(Constants.getClientConfigContext(clientCode), FeatureTogglesConfigParamName.LEARNING_ACCESS_GIVEN_BY_DEFAULT.value()));
    }

    public String isSSOEnabled(String email) {
        String client = getClientCodeByEmail(email);
        if (null != client && !client.equalsIgnoreCase(Constants.CLIENT_INTERNAL) && !shouldUseCognitoForExternalSsoUsers(client)) {
            return pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.SSOENABLED.value(), client);
        }

        return Boolean.FALSE.toString();
    }

    public String getUniversalLoginUrl() {
        return SystemConfig.getUniversalLoginUrl();
    }


    public String getSPInitiatedSSOURL(String email) {
        String client = getClientCodeByEmail(email);
        if (null != client) {
            String idPEntityID = pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.SSO_IDPENTITY_ID.value(), client);
            if (null != idPEntityID && !idPEntityID.isEmpty()) {
                client = client + "_NXT";
                return "sso/saml2/jsp/spSSOInit.jsp?metaAlias=/" + client + "/sp&idpEntityID=" + idPEntityID;
            }
        }

        return null;
    }

    public String getSPInitiatedSSOURLWithReplayState(String email) {
        String spInitiatedSSOURL = getSPInitiatedSSOURL(email);
        if (spInitiatedSSOURL == null) {
            return null;
        }
        return spInitiatedSSOURL + "&RelayState=http://" + getHostName() + REST_BASE_URL + "entry/ssologin";
    }

    protected String getHostName() {
        try {
            return InetAddress.getLocalHost().getCanonicalHostName();
        } catch (UnknownHostException e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error getting local host", e);
        }
    }

    private String getClientCodeByEmail(String email) {
        return getClientFromDBByEmail(email);
    }

    private String getClientFromDBByEmail(String email) {
        GlobalUser globalUserByEmail = userGlobalDBService.getGlobalUserByEmail(email);
        return globalUserByEmail != null ? globalUserByEmail.getClientCode() : null;
    }

    private String getClientFromDBById(int uid) {
        GlobalUser globalUserByEmail = userGlobalDBService.getGlobalUserById(uid);
        return globalUserByEmail.getClientCode();
    }

    public Set<LDAPUser> getAllUsers(boolean isInternal) {
        long startTime = new Date().getTime();
        String clientCode = isInternal ? Constants.CLIENT_INTERNAL : workContextRestEasyController.getCurrentClientCode();
        Set<LDAPUser> users = getAllUsers(clientCode);
        LOGGER.debug("getAllUsers took ms: " + (new Date().getTime() - startTime));
        return users;
    }

    public Set<LDAPUser> getAllUsers(String clientCode) {
        Set<LDAPUser> users = listLdapUsersForClient(clientCode);
        filterOutCorporateUsersIfNecessary(users);
        removeAgentAndCertUsers(users);

        return users;
    }


    public Set<LDAPUser> getAllAuthorizedUsers(boolean isInternal) {
        return getAllAuthorizedUsers(isInternal, null, Optional.empty(), false);
    }

    public boolean isInternalUser(String email) {
        final GlobalUser user = userGlobalDBService.getGlobalUserByEmail(email);
        return user != null && user.isInternal();
    }

    public String isSSOWithCognitoConfigured(String email, HttpServletRequest request, HttpServletResponse response) {
        GlobalUser user = getGlobalUserFor(email);
        if (!shouldUseCognitoFor(user)) {
            return EMPTY;
        }
        return createCognitoCallbackUrl(request, response, user.getClientCode());
    }

    public String createCognitoCallbackUrl(HttpServletRequest request, HttpServletResponse response, String clientCode) {
        String callbackUrl = nonNull(clientCode) && INTERNAL_CLIENT_NAME.equals(clientCode) ? getCallbackUrl(request, response) : getSolutionsUrl();
        return createCognitoCallbackUrl(clientCode, request, response, callbackUrl);
    }

    private boolean shouldUseCognitoFor(GlobalUser user) {
        return nonNull(user) && (user.isInternal() || shouldUseCognitoForExternalSsoUsers(user.getClientCode()));
    }

    public GlobalUser getGlobalUserFor(String email) {
        return Optional.ofNullable(userGlobalDBService.getGlobalUserByEmail(email))
                .orElseGet(() -> userGlobalDBService.getGlobalUserByUniqueUserId(email));
    }

    private boolean useUniqueUserIDInsteadOfEmailEnabled() {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.USE_UNIQUE_USERID_INSTEADOF_EMAILID.getParameterName()));
    }

    private String createCognitoCallbackUrl(String clientCode, HttpServletRequest request, HttpServletResponse response, String callbackUrl) {
        String url = CognitoLoginHelper.getCognitoAuthUrlForExternalUsers(clientCode, callbackUrl) + "&state=";
        if (shouldUseCookiesForCognitoLoginExternalSSOUsers()) {
            response.addHeader(HttpHeaders.SET_COOKIE, createResponseCookie(CLIENT_CODE_COOKIE_NAME, clientCode).toString());
        } else {
            url = url + clientCode;
        }
        LOGGER.debug("Cognito login URL: " + url);
        return url + STATE_PARAMETER_VALUE_SEPARATOR + getEncodedRedirectPath(request);
    }

    private String getEncodedRedirectPath(HttpServletRequest request) {
        String redirectPath = request.getParameter("redirectPath");
        return StringUtils.isNotEmpty(redirectPath) ? EncryptionDecryption.encode(redirectPath) : EMPTY;
    }

    private String getCallbackUrl(HttpServletRequest request, HttpServletResponse response) {
        String callbackUrl = request.getHeader("Referer").split("\\?")[0].replace("login", EMPTY);
        String callbackUrlCookieValue = callbackUrl + "CognitoLogin/external";
        response.addHeader(HttpHeaders.SET_COOKIE, createResponseCookie(CALLBACK_URL_COOKIE_NAME, callbackUrlCookieValue).toString());
        return callbackUrl;
    }

    private ResponseCookie createResponseCookie(String cookieName, String cookieValue) {
        Cookie callbackUrlCookie = new Cookie(cookieName, cookieValue);
        return ResponseCookie.from(callbackUrlCookie.getName(), callbackUrlCookie.getValue())
                .secure(true)
                .httpOnly(true)
                .path("/")
                .sameSite("None")
                .build();
    }

    public Set<LDAPUser> getAllAuthorizedUsers(boolean isInternal, boolean activeUsersOnly) {
        StopWatch timer = new StopWatch();
        timer.start();
        Set<LDAPUser> allAuthorizedUsers = getAllAuthorizedUsers(isInternal, null, Optional.empty(), activeUsersOnly);
        LOGGER.info("getAllAuthorizedUsers(internal-" + isInternal + ", active-" + activeUsersOnly + "), time: " + timer.getTime() + "ms");
        return allAuthorizedUsers;
    }

    private Set<LDAPUser> getAllAuthorizedUsers(boolean isInternal, String nameOrEmailAddressSubstring) {
        return getAllAuthorizedUsers(isInternal, nameOrEmailAddressSubstring, Optional.empty(), false);
    }

    public Set<LDAPUser> getAllAuthorizedUsers(boolean isInternal, String nameOrEmailAddressSubstring, Optional<String[]> sortKeys, boolean activeUsersOnly) {
        StopWatch timer = new StopWatch();
        timer.start();

        boolean authorizedUserIsCorporate = isCurrentUserCorporate() && SystemConfig.hasFeatureCorporateUsers();
        Set<LDAPUser> users = userGlobalDBService.getAllAuthorizedUsers(isInternal, authorizedUserIsCorporate, nameOrEmailAddressSubstring, sortKeys, activeUsersOnly);
        removeAgentAndCertUsers(users);
        LOGGER.debug("getAllAuthorizedUsers(" + isInternal + ")" + ", time: " + timer.getTime() + "ms");
        return users;
    }

    private void removeAgentAndCertUsers(Set<LDAPUser> users) {
        if (!isLoggedInUserIsInternal()) {
            removeG3Agent(users);
            removeG3CertUser(users);
        }
    }

    public Set<LDAPUser> getAllUsersAssociatedWithPropertyAndRole(Set<Property> properties, String roleId, boolean isInternal, boolean activeUsersOnly) {
        boolean authorizedUserIsCorporate = isCurrentUserCorporate() && SystemConfig.hasFeatureCorporateUsers();
        Set<LDAPUser> users = userGlobalDBService.getAllUsersAssociatedWithPropertyAndRole(properties, roleId, isInternal, authorizedUserIsCorporate, activeUsersOnly);
        removeAgentAndCertUsers(users);
        removeAliasUser(users);
        return users;
    }

    public Set<LDAPUser> getAllAuthorizedUsers() {
        return getAllAuthorizedUsers(false);
    }

    public List<LDAPUser> getAllAuthorizedUsersInOrder() {
        List<LDAPUser> users = new ArrayList<LDAPUser>(getAllAuthorizedUsers());
        Collections.sort(users, (o1, o2) -> {
            if (o1.getUserId() > o2.getUserId()) {
                return +1;
            } else if (o1.getUserId() < o2.getUserId()) {
                return -1;
            }
            return 0;
        });
        return users;
    }

    public Set<GlobalUser> getAllAuthorizedGlobalUsers(boolean isInternal, boolean hydrateRoles) {
        return getAllAuthorizedGlobalUsers(isInternal, workContextRestEasyController.getCurrentUserId(), hydrateRoles);
    }

    public Set<GlobalUser> getAllAuthorizedGlobalUsers(boolean isInternal, final Integer authorizedUserId, boolean hydrateRoles) {
        GlobalUserCriteria globalUserCriteria = new GlobalUserCriteria();
        globalUserCriteria.setAuthorizedUser(getGlobalUser(authorizedUserId, true));
        globalUserCriteria.setInternal(isInternal);
        globalUserCriteria.setHydrateRoles(hydrateRoles);
        return new HashSet<>(getGlobalUsers(globalUserCriteria));
    }

    public GlobalUser getGlobalUser(Integer userId, boolean hydrateRoles) {
        GlobalUser globalUser = globalCrudService.find(GlobalUser.class, userId);
        if (globalUser != null && hydrateRoles) {
            // NOTE: Modest optimization: this could be a fetch join on the
            // query instead
            Hibernate.initialize(globalUser.getAuthGroupRoles());
            Hibernate.initialize(globalUser.getIndividualPropertyRoles());
        }
        return globalUser;
    }

    public Optional<GlobalUser> getGlobalUserByUISId(String uisId) {
        return Optional.ofNullable(globalCrudService.findByNamedQuerySingleResult(GlobalUser.BY_UIS_ID, QueryParameter.with("uis_id", uisId).parameters()));
    }

    public Optional<GlobalUser> getGlobalUserByEmail(String email) {
        return Optional.ofNullable(globalCrudService.findByNamedQuerySingleResult(GlobalUser.BY_EMAIL,
                QueryParameter.with("emailAddress", email).parameters()));
    }

    public Optional<LDAPUser> getLDAPUserByClientCodeAndUISId(String clientCode, String uisId) {
        GlobalUser globalUser = globalCrudService.findByNamedQuerySingleResult(GlobalUser.BY_CLIENT_CODE_AND_UIS_ID,
                QueryParameter.with("uis_id", uisId)
                        .and("clientCode", clientCode)
                        .parameters());
        if (globalUser == null) {
            return Optional.empty();
        }
        return Optional.of(ldapUserConverter.toLDAPUser(globalUser));
    }

    public List<GlobalUser> getGlobalUsers(GlobalUserCriteria globalUserCriteria) {
        return globalCrudService.findByCriteria(globalUserCriteria);
    }

    public int getGlobalUsersCount(GlobalUserCriteria globalUserCriteria) {
        return globalCrudService.findCountByCriteria(globalUserCriteria);
    }

    public Set<LDAPUser> search(String searchFor,
                                boolean isInternal) {
        Set<LDAPUser> users = getAllAuthorizedUsers(isInternal, searchFor);

        // Think about moving search to client side now that we need to filter
        // out corporate users
        // and also need to resolve properties
        filterOutCorporateUsersIfNecessary(users);
        filterOutUsersNotInPropertySubset(users);
        return users;
    }

    /*
     * Do not use PacmanThreadLocalContextHolder getPrincipal isCorporate as
     * that can be cached Not enormously deleterious but querying LDAP is more
     * accurate
     */
    public boolean isCurrentUserCorporate() {
        boolean isCorporate;
        try {
            // If user internal, automatically corporate (backwards
            // compatibility)
            int userId = workContextRestEasyController.getCurrentUserId();
            LDAPUser currentUser = getUserById(String.valueOf(userId));
            isCorporate = currentUser.getIsCorporate();

        } catch (LDAPException le) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "Failed to get current user", le);
        }
        return isCorporate;
    }
    public boolean isCurrentUserNonCorporate() {
        boolean isNonCorporate = false;
        if(!isCurrentUserCorporate()) {
            isNonCorporate = true;
        }
        return isNonCorporate;
    }
    public List<GlobalUser> getAllInternalUsersForScheduledReports() {
        List<GlobalUser> internalUsers = globalCrudService.findByNamedQuery(GlobalUser.BY_INTERNAL_USERS);
        return internalUsers;
    }

    private void filterOutCorporateUsersIfNecessary(Set<LDAPUser> users) {
        // If current user non corporate, filter out corporate users
        if (!isCurrentUserCorporate() && SystemConfig.hasFeatureCorporateUsers()) {
            CollectionUtils.filter(users, object -> !((LDAPUser) object).getIsCorporate());
        }
    }

    public void filterOutUsersNotInPropertySubset(Set<LDAPUser> users) {
        // Non corporate to non corporate must have superset of the properties
        if (!isCurrentUserCorporate() && SystemConfig.hasFeatureCorporateUsers()) {
            LOGGER.debug("Current user is non-corporate so we're filtering out users not within property subset");
            long startTime = new Date().getTime();
            Integer currentUserId = workContextRestEasyController.getCurrentUserId();
            Integer clientId = workContextRestEasyController.getCurrentClientId();
            List<Property> currentUserProperties = authorizationService.retrieveAuthorizedProperties(currentUserId, clientId);
            List<Property> targetUserProperties;
            // Need an iterator to safely remove
            Iterator<LDAPUser> iterator = users.iterator();
            LDAPUser user;
            while (iterator.hasNext()) {
                user = iterator.next();
                if (!currentUserId.toString().equals(user.getUid())) {
                    targetUserProperties = authorizationService.retrieveAuthorizedProperties(Integer.valueOf(user.getUid()), clientId);
                    removeUserIfNotSuperset(iterator, currentUserProperties, targetUserProperties);
                }
            }
            LOGGER.info("getAllAuthorizedUsers filtering for user " + currentUserId + " took ms: " + (new Date().getTime() - startTime));
        }
    }

    private void removeUserIfNotSuperset(Iterator<LDAPUser> iterator, List<Property> currentUserProperties,
                                         List<Property> targetUserProperties) {
        if (!currentUserProperties.containsAll(targetUserProperties)) {
            iterator.remove();
        }
    }

    /**
     * @deprecated In favor of getById( int value ), since our ID's ARE int's
     */
    @SneakyThrows
    @Deprecated
    public LDAPUser getById(String id) {
        GlobalUser globalUser = userGlobalDBService.getGlobalUserById(Integer.parseInt(id));
        LDAPUser user = ldapUserConverter.toLDAPUser(globalUser);

        if (null != user && globalUser != null) {
            setPasswordActiveDuration(user, globalUser);
            setPasswordChangeAlertInfo(user, globalUser);

            user.setLanguage(getLanguagePreferenceForUser(globalUser));
            //TODO   not sure why we are setting fiscal calendar in else?
            UserPreferencesUtil.setUserPreferencesFromDB(globalUser, user);
            user.setEnableFiscalCalendar(UserPreferencesUtil.getFiscalCalendarUserPreference(globalUser.getUserPreferences()));
            setUserDateFormatForUser(user);
        }
        return user;
    }

    private LDAPUser getUserById(String id) {
        return ldapUserConverter.toLDAPUser(userGlobalDBService.getGlobalUserById(Integer.parseInt(id)));
    }

    private LDAPUser getUserByEmailFromDB(String email) {
        GlobalUser globalUser = userGlobalDBService.getGlobalUserByEmail(email);
        if (globalUser == null) {
            return null;
        }
        return ldapUserConverter.toLDAPUser(globalUser);
    }

    public LDAPUser getUserInformationById(String id) {
        //US29047 - Security Audit. Check to make sure that the user requesting this information is the current logged in user.
        Integer currentUserId = workContextRestEasyController.getCurrentUserId();
        Integer userId = Integer.valueOf(id);
        if (!currentUserId.equals(userId)) {
            throw TetrisSecurityException.USER_NOT_AUTHORIZED;
        }

        return getById(userId);
    }

    public String getLanguagePreferenceForUser(GlobalUser globalUser) {
        return (globalUser != null && !StringUtils.isEmpty(globalUser.getLanguage())) ? globalUser.getLanguage() : UserPreferencesUtil.DEFAULT_LANGUAGE;
    }

    private void setPasswordChangeAlertInfo(LDAPUser user, GlobalUser globalUser) {
        if (globalUser == null) {
            return;
        }

        if (globalUser.isInternal() || globalUser.getPasswordNeverExpire()) {
            user.setNotifyPasswordExpiration(false);
            user.setDaysRemainingToExpirePassword(90);
        } else {
            boolean notifyPasswordExpiration = false;
            int daysDifference = 15;
            java.time.LocalDateTime lastPasswordChangeDttm = globalUser.getLastPasswordChangeDttm();
            if (null != lastPasswordChangeDttm) {
                LocalDate lastPasswordChanged = LocalDate.fromDateFields(LocalDateUtils.toDate(lastPasswordChangeDttm));
                LocalDate nextPasswordChangeExpected = lastPasswordChanged.plusDays(Integer.parseInt(getPasswordActiveDurationForClientContext(user.getClient())));
                Integer configuredPWChangeAlertDuration = Integer.parseInt(getPasswordExpirationNotificationDurationForClientContext(user.getClient()));
                Days dateDifference = Days.daysBetween(LocalDate.now(), nextPasswordChangeExpected);

                notifyPasswordExpiration = configuredPWChangeAlertDuration >= dateDifference.getDays();
                daysDifference = dateDifference.getDays();
            }

            user.setNotifyPasswordExpiration(notifyPasswordExpiration);
            user.setDaysRemainingToExpirePassword(daysDifference);
        }
    }

    private String getPasswordActiveDurationForClientContext(String clientCode) {
        String clientContext = PACMAN_DOT + clientCode;
        return pacmanConfigParamsService.getValue(clientContext, GUIConfigParamName.CORE_PROPERTY_PASSWORD_ACTIVE_DURATION.value());
    }

    private String getPasswordExpirationNotificationDurationForClientContext(String clientCode) {
        String clientContext = PACMAN_DOT + clientCode;
        return pacmanConfigParamsService.getValue(clientContext, GUIConfigParamName.CORE_PROPERTY_PASSWORD_EXPIRY_ALERT_DAYS.value());
    }

    private void setPasswordActiveDuration(LDAPUser user, GlobalUser globalUser) {
        String clientContext = PACMAN_DOT + user.getClient();
        user.setPasswordActiveDuration("-1");
        if (null != globalUser) {
            user.setPasswordNeverExpire(globalUser.getPasswordNeverExpire());
            if (!globalUser.getPasswordNeverExpire()) {
                user.setPasswordActiveDuration(pacmanConfigParamsService.getValue(clientContext, GUIConfigParamName.CORE_PROPERTY_PASSWORD_ACTIVE_DURATION.value()));
            }
        }
    }

    private void setUserDateFormatForUser(LDAPUser userByUid) {
        if (userByUid != null && !SystemConfig.isUserDefinedDateFormatEnabled()) {
            userByUid.setDefaultDateFormat(DEFAULT_STANDARD_FORMAT_OFF);
        } else if (userByUid != null && SystemConfig.isUserDefinedDateFormatEnabled()
                && (StringUtils.isEmpty(userByUid.getDefaultDateFormat()) || userByUid.getDefaultDateFormat().contains("/"))) {
            userByUid.setDefaultDateFormat(DD_MMM_YYYY);
        }
    }

    public LDAPUser getById(int id) {
        return getById(Integer.toString(id));
    }

    public LDAPUser getByEmailFromDB(String email) {
        return getUserByEmailFromDB(email);
    }


    public LDAPUser getByUniqueUserIDFromDB(String uniqueUserID) {
        GlobalUser globalUser = userGlobalDBService.getGlobalUserByUniqueUserID(uniqueUserID, PacmanWorkContextHelper.getClientCode());
        if (globalUser == null) {
            return null;
        }
        return ldapUserConverter.toLDAPUser(globalUser);
    }

    /**
     * This method exists as there is no unique constraint on emailId; So there are users
     * with same email ids cross clients.
     * TODO: Once LDAP is removed, remove all duplicate users from [global].Users table and replace this method with getByEmailFromDB()
     */
    public LDAPUser get(String email, String clientCode) {
        GlobalUser globalUser = userGlobalDBService.get(email, clientCode);
        if (globalUser == null) {
            return null;
        }
        return ldapUserConverter.toLDAPUser(globalUser);
    }

    /**
     * Since no password provided, then we'll create a random password and email
     * it to user
     */
    public LDAPUser create(LDAPUser user, boolean isInternal) {
        LOGGER.debug("create() - NO password");
        return createWithRandomPasswordAndEmailToUser(user, isInternal);
    }

    public LDAPUser createUserAndSkipPasswordEmail(LDAPUser user, boolean isInternal) {
        return createUserInDBImpl(user, isInternal, getRandomPassword());
    }

    /**
     * If: no password provided, then we'll create a random password and email
     * it to user... Else: we're just going to create user with provided
     * password, and that's it.
     */

    public LDAPUser create(LDAPUser user, boolean isInternal,
                           String password) {
        return createInternal(user, isInternal, password);
    }

    public LDAPUser createInternal(LDAPUser user, boolean isInternal, String password) {
        LOGGER.debug("create() - WITH password");
        return password == null || password.isEmpty() ? createWithRandomPasswordAndEmailToUser(user, isInternal)
                : createUserInDBImpl(user, isInternal, password);
    }

    /**
     * Allows batch creation of test users in context of Hibernate session - Blackstone client only!
     */
    public void createTestUsers(int numUsers) {
        String clientCode = workContextRestEasyController.getCurrentClientCode();

        if (numUsers > 0 && clientCode.equalsIgnoreCase(PlatformWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE)) {
            Set<LDAPUser> existingUsers = getAllAuthorizedUsers();
            // Get number of current blackstone test users
            int testUserIndex = existingUsers.stream().filter(user -> user.getClient().equals(clientCode) &&
                    user.getGivenName().contains("first")).map(LDAPUser::getGivenName).collect(Collectors.toList()).size();
            // Get blackstone property Ids
            List<Integer> bstnProperties = propertyService.getActiveProperties(clientCode).stream().map(Property::getId).
                    collect(Collectors.toCollection(LinkedList::new));

            for (int i = 0; i < numUsers; i++) {

                LDAPUser user = new LDAPUser();
                user.setGivenName("first");
                user.setSn("last" + testUserIndex);
                user.setCn("first last" + testUserIndex);
                user.setMail("first_last" + testUserIndex + "@" + clientCode + ".com");
                user.setActive(true);

                // set up user properties - blackstone
                List<PropertyRoleMapping> propertyRoles = new ArrayList<>();
                for (int j = 0; j < bstnProperties.size(); j++) {
                    // Add Revenue Analyst Role
                    String roleId = "3";
                    propertyRoles.add(new PropertyRoleMapping(roleId, bstnProperties.get(j).toString()));
                }

                user.setPropertyRoles(propertyRoles);
                List<AuthGroupRoleMapping> authGroupRoles = new ArrayList<>();
                user.setAuthGroupRoles(authGroupRoles);

                LOGGER.info("Creating test user: " + user.getSn() + " with individual property count: " + bstnProperties.size());
                createUserInDBImpl(user, false, PASSWORD_STRING + testUserIndex);
                testUserIndex++;
            }
        } else {
            throw new TetrisException("No users created - None specified or Client is something other than Blackstone");
        }
    }

    /**
     * Create user with random password
     */
    protected LDAPUser createWithRandomPasswordAndEmailToUser(LDAPUser user, boolean isInternal) {
        String password = getRandomPassword();
        LDAPUser createdUser = createUserInDBImpl(user, isInternal, password);

        if (SystemConfig.isUniversalLoginGloballyEnabled()) {
            uisService.sendWelcomeEmailForNonSSOUsers(user.getMail(), user.getGivenName());
            return createdUser;
        }

        // Notify the user of their password
        if (!useUniqueUserIDInsteadOfEmailEnabled() || (isInternal && createdUser.getMail() != null)) {
            try {
                String text = generateEmailText(user.getCn(), createdUser.getMail(), password, isInternal);
                emailService.sendHtml(SystemConfig.getEmailCreateUserFrom(), createdUser.getMail(), SystemConfig.getEmailCreateUserSubject(),
                        text);
            } catch (Exception exception) {
                throw new TetrisException(ErrorCode.EMAIL_FAILED, "Problem sending email to new user", exception);
            }
        }
        return createdUser;
    }

    public String getRandomPassword() {
        SecureRandom random = new SecureRandom();
        List<Character> passwordCharacters = new ArrayList<Character>();
        passwordCharacters.add((char) ('0' + random.nextInt(10)));
        passwordCharacters.add((char) ('a' + random.nextInt(26)));
        passwordCharacters.add((char) ('A' + random.nextInt(26)));
        passwordCharacters.add(PASSWORD_COMPOSITION_CHARS.substring(0, 31).charAt(random.nextInt(31)));
        for (int i = 0; i < PASSWORD_LENGTH_GENERATED - 4; i++) {
            passwordCharacters.add(PASSWORD_COMPOSITION_CHARS.charAt(random.nextInt(PASSWORD_COMPOSITION_CHARS.length())));
        }
        Collections.shuffle(passwordCharacters);
        StringBuilder userPassword = new StringBuilder();
        for (Character ch : passwordCharacters) {
            userPassword.append(ch);
        }
        return userPassword.toString();
    }

    /**
     * Common method for public methods
     *
     * @return New user
     */
    protected LDAPUser createUserInDBImpl(LDAPUser user, boolean isInternal, String password) {
        validateNewUserMetadata(user, isInternal);
        // Lookup to ensure unique
        validateUserAlreadyExists(user, isInternal);

        // Determine client
        String client = isInternal ? Constants.CLIENT_INTERNAL : workContextRestEasyController.getCurrentClientCode();
        user.setClient(client);
        user.setIsCorporate(isUserCorporate(user));
        user.setSalesforceOrganizationId(SystemConfig.getSalesforceOrganizationId());
        user.setSalesforcePortalId(SystemConfig.getSalesforcePortalId());
        Integer dbUserId = syncService.createUser(user, isInternal, client, password);
        DN userDN = new LDAPMapper().getDN(LdapUserConverter.baseDN, String.valueOf(dbUserId), client, LDAPUser.class);
        user.setDN(userDN.toString());
        //TODO why we need this removed code?
        user.setUserId(dbUserId);
        //ldap should get updated with ldap role id not db role id
        user.setPropertyRoles(convertToLDAPPropertyRoles(user.getPropertyRoles()));
        user.setAuthGroupRoles(convertToLDAPAuthRoles(user.getAuthGroupRoles()));
        if (user.isInternal() && SystemConfig.isCognitoUserSyncEnabledForInternalUsers()) {
            startUserManagementSyncJob(dbUserId.toString(), INTERNAL_CLIENT_NAME.toUpperCase());
        } else if (shouldRunFDSUserManagementSync(user.getClient())) {
            startUserManagementSyncJob(dbUserId.toString(), user.getClient());
        }
        return user;
    }

    private void validateUserAlreadyExists(LDAPUser user, boolean isInternal) {
        LDAPUser existingUser = findExistingUser(user);
        if (null != existingUser) {
            if (!useUniqueUserIDInsteadOfEmailEnabled() || isInternal) {
                throw new TetrisException(ErrorCode.USER_WITH_SAME_EMAIL_ID_ALREADY_EXISTS, "Email already exists: " + user.getMail());
            } else {
                throw new TetrisException(ErrorCode.USER_WITH_SAME_USER_ID_ALREADY_EXISTS, "UserID already exists: " + user.getUniqueUserID());
            }
        }
    }

    private void validateNewUserMetadata(LDAPUser user, boolean isInternal) {
        if (user.getMail() == null && !useUniqueUserIDInsteadOfEmailEnabled()) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "Email address is a required field");
        } else if (user.getCn() == null) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "Common name (cn) is a required field");
        } else if (user.getSn() == null) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "Surname (sn) is a required field ");
        } else if (useUniqueUserIDInsteadOfEmailEnabled() && user.getUniqueUserID() == null && !isInternal) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "UniqueUserID is a required field ");
        }
    }

    /*
     * Determine isCorporate - just logic - any isCorporate role is corporate
     * user Prefer to resolve on server rather than trust client
     */
    public boolean isUserCorporate(LDAPUser user) {
        boolean isCorporate = !SystemConfig.hasFeatureCorporateUsers();
        if (SystemConfig.hasFeatureCorporateUsers()) {
            isCorporate = isCorporateDueToAuthGroups(user);
            if (!isCorporate) {
                isCorporate = isCorporateDueToPropertyRoles(user);
            }
        }
        return isCorporate;
    }

    private boolean isCorporateDueToAuthGroups(LDAPUser user) {
        boolean isCorporate = false;
        if (null != user.getAuthGroupRoles()) {
            for (AuthGroupRoleMapping mapping : user.getAuthGroupRoles()) {
                if (isRoleCorporate(mapping.getRoleId())) {
                    isCorporate = true;
                    break;
                }
            }
        }
        return isCorporate;
    }

    private boolean isCorporateDueToPropertyRoles(LDAPUser user) {
        boolean isCorporate = false;
        if (null != user.getPropertyRoles()) {
            for (PropertyRoleMapping mapping : user.getPropertyRoles()) {
                if (isRoleCorporate(mapping.getRoleId())) {
                    isCorporate = true;
                    break;
                }
            }
        }
        return isCorporate;
    }

    private boolean isRoleCorporate(String roleId) {
        boolean isCorporate = false;
        Role role;
        if (roleId.equals(Role.ALL_PERMS_ID)) {
            isCorporate = true;
        } else {
            role = roleService.getRole(roleId);
            if (role != null) {
                isCorporate = role.isCorporate();
            } else {
                LOGGER.warn("No role found with id: " + roleId);
            }
        }
        return isCorporate;
    }

    private String generateEmailText(String commonName, String email, String password, boolean isInternal) {
        final String emailBodyForRegularUsers = MessageFormat.format(SystemConfig.getEmailCreateUserBody(), commonName, email, password, SystemConfig.getG3ClientLink());
        return isInternal && shouldUseCognitoForInternalUsers() && SystemConfig.shouldHidePasswordWithCognitoForInterNalUsers() ?
                emailBodyForRegularUsers.replace("<br>Your account password is: " + password, EMPTY)
                : emailBodyForRegularUsers;
    }

    public LDAPUser getExistingUserByEmail(String email) {
        return getUserByEmailFromDB(email);
    }

    public LDAPUser getExistingUserByUID(String uid) {
        return getUserById(uid);
    }

    public LDAPUser getExistingUserByUID(int uid) {
        return getUserById(String.valueOf(uid));
    }

    public String deleteById(Integer uid) {
        LOGGER.info("deleteById()- userId: " + uid);
        try {
            String clientForUserId = getClientFromDBById(uid);
            boolean isInternal = clientForUserId.equals(Constants.CLIENT_INTERNAL);
            syncService.removeUser(uid.toString(), isInternal);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "An error occurred while deleting the user: " + uid, e);
        }
        return uid.toString();
    }

    public LDAPUser updateCore(String uid, LDAPUser user) {
        LDAPUser existingUser = findExistingUser(user);

        if (null != existingUser && !existingUser.getUid().equals(uid)) {
            if (useUniqueUserIDInsteadOfEmailEnabled()) {
                throw new TetrisException(ErrorCode.SECURITY_ERROR, "UserId  already exists");
            } else {
                throw new TetrisException(ErrorCode.SECURITY_ERROR, "Email already exists");
            }
        }

        Integer userId = Integer.parseInt(uid);
        checkCurrentUserAllowedToModify(userId);
        user.setIsCorporate(isUserCorporate(user));

        try {
            //ldap should get updated with ldap role id not db role id
            user.setPropertyRoles(convertToLDAPPropertyRoles(user.getPropertyRoles()));
            user.setAuthGroupRoles(convertToLDAPAuthRoles(user.getAuthGroupRoles()));
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "Something bad happened while trying to MODIFY the user", e);
        }

        boolean isInternal = user.isInternal();

        user.setPasswordNeverExpire(user.getPasswordNeverExpire());
        user.setLanguage(user.getLanguage());
        syncService.persistUser(userId, user, isInternal, user.getClient(), null, isSyncRequired(user, existingUser));
        return user;
    }

    private LDAPUser findExistingUser(LDAPUser user) {
        LDAPUser existingUser;
        if (useUniqueUserIDInsteadOfEmailEnabled() && !user.isInternal()) {
            existingUser = getByUniqueUserIDFromDB(user.getUniqueUserID());
        } else {
            existingUser = getByEmailFromDB(user.getMail());
        }
        return existingUser;
    }

    private boolean isSyncRequired(LDAPUser user, LDAPUser existingUser) {
        return isTenantSyncRequired(existingUser, user) || (useUniqueUserIDInsteadOfEmailEnabled() && existingUser != null && !Objects.equals(existingUser.getUniqueUserID(), user.getUniqueUserID()));
    }

    public boolean isTenantSyncRequired(LDAPUser existingUser, LDAPUser updatedUser) {
        boolean isRequired = true;
        if (null != existingUser && null != updatedUser) {
            isRequired = !
                    (
                            Objects.equals(existingUser.getGivenName(), updatedUser.getGivenName()) &&
                                    Objects.equals(existingUser.getSn(), updatedUser.getSn()) &&
                                    Objects.equals(existingUser.getMail(), updatedUser.getMail()) &&
                                    Objects.equals(existingUser.getActive(), updatedUser.getActive())
                    );
        }
        return isRequired;
    }

    private List<PropertyRoleMapping> convertToLDAPPropertyRoles(List<PropertyRoleMapping> propertyRoles) {
        propertyRoles.forEach(this::convertToLDAPPropertyRoleMapping);
        return propertyRoles;
    }

    private void convertToLDAPPropertyRoleMapping(PropertyRoleMapping propertyRoleMapping) {
        if (!propertyRoleMapping.getRoleId().equals(Role.ALL_PERMS_ID)) {
            propertyRoleMapping.setRoleId(propertyRoleMapping.getRoleId());
        }
    }

    private List<AuthGroupRoleMapping> convertToLDAPAuthRoles(List<AuthGroupRoleMapping> propertyRoles) {
        propertyRoles.forEach(this::convertToLDAPAuthGroupRoleMapping);
        return propertyRoles;
    }

    private void convertToLDAPAuthGroupRoleMapping(AuthGroupRoleMapping propertyRoleMapping) {
        if (!propertyRoleMapping.getRoleId().equals(Role.ALL_PERMS_ID)) {
            propertyRoleMapping.setRoleId(propertyRoleMapping.getRoleId());
        }
    }

    public LDAPUser update(String uid, LDAPUser user) {
        // updating the report schedules from pacman_db and jasper.
        // This flag is added to disable the removeScheduleRecipientMail from
        // schedules.
        boolean debugEnabled = LOGGER.isDebugEnabled();
        LOGGER.debug("retrieveAuthorizedProperties( uid: " + uid + ")");

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        if (!useUniqueUserIDInsteadOfEmailEnabled() || (user.isInternal() && user.getMail() != null)) {
            removeEmailsFromScheduleForDeactivatedUser(uid, user);
        }
        stopWatch.stop();
        if (debugEnabled) {
            LOGGER.debug("removeEmailsFromScheduleForDeactivatedUser time:" + stopWatch.toString());
        }

        // If Email id is updated, then update schedule reports on Jasper and
        // Pacman db
        stopWatch.reset();
        stopWatch.start();
        List<Integer> authorizedPropertyIds = extractAuthorizedPropertiesFromRolesForUser(user);
        stopWatch.stop();
        if (debugEnabled) {
            LOGGER.debug("extractAuthorizedPropertiesFromRolesForUser time:" + stopWatch.toString());
        }

        stopWatch.reset();
        stopWatch.start();
        clearPreferencesForUnauthorizedProperties(user, authorizedPropertyIds);
        stopWatch.stop();
        if (debugEnabled) {
            LOGGER.debug("clearPreferencesForUnauthorizedProperties time:" + stopWatch.toString());
        }

        if (user.getDefaultPropertyGroup() == null) {
            user.setDefaultPropertyGroupId(-1);
        }

        stopWatch.reset();
        stopWatch.start();
        LDAPUser updatedUser = this.updateCore(uid, user);
        stopWatch.stop();
        if (debugEnabled) {
            LOGGER.debug("updateCore time:" + stopWatch.toString());
        }

        Integer userId = Integer.parseInt(uid);
        // updating property group db
        if (updatedUser.getDefaultPropertyGroup() != null) {
            Integer propertyGroupForDefault = null;
            if (null != updatedUser.getDefaultPropertyGroup() && !updatedUser.getDefaultPropertyGroup().isEmpty()) {
                propertyGroupForDefault = Integer.parseInt(updatedUser.getDefaultPropertyGroup());
            }
            stopWatch.reset();
            stopWatch.start();
            propertyGroupService.updateDefaultPropertyGroupToDbForUser(propertyGroupForDefault, userId);
            stopWatch.stop();
            if (debugEnabled) {
                LOGGER.debug("propertyGroupService.updateDefaultPropertyGroupToDbForUser time:" + stopWatch.toString());
            }
        }

        stopWatch.reset();
        stopWatch.start();
        userAuthorizedPropertyCache.put(PacmanWorkContextHelper.getClientId(), user.getUserId(), authorizedPropertyIds);
        stopWatch.stop();
        if (debugEnabled) {
            LOGGER.debug("userAuthorizedPropertyCache.put time:" + stopWatch.toString());
        }

        stopWatch.reset();
        stopWatch.start();
        updatePropertyGroups(userId);
        stopWatch.stop();
        if (debugEnabled) {
            LOGGER.debug("updatePropertyGroups time:" + stopWatch.toString());
        }

        stopWatch.reset();
        stopWatch.start();
        updateUserPreferencesToGlobal(updatedUser);
        stopWatch.stop();
        if (debugEnabled) {
            LOGGER.debug("updateUserPreferencesToGlobal time:" + stopWatch.toString());
        }
        if (!user.isIntegrationUser()) {
            if (user.isInternal() && SystemConfig.isCognitoUserSyncEnabledForInternalUsers()) {
                startUserManagementSyncJob(updatedUser.getUserId().toString(), INTERNAL_CLIENT_NAME.toUpperCase());
            } else if (shouldRunFDSUserManagementSync(user.getClient())) {
                startUserManagementSyncJob(updatedUser.getUserId().toString(), user.getClient());
            }
        }
        return updatedUser;
    }

    private void removeEmailsFromScheduleForDeactivatedUser(String uid, LDAPUser user) {
        if (!user.getActive()) {
            reportService.removeMailIdFromSchedule(uid);
        }
    }

    public void clearPreferencesForUnauthorizedProperties(LDAPUser user, List<Integer> authorizedPropertyIds) {
        LDAPUser ldapUser = getById(user.getUserId());
        String defaultProperty = ldapUser.getDefaultProperty();
        if (null != defaultProperty && StringUtils.isNotEmpty(defaultProperty)
                && !authorizedPropertyIds.contains(Integer.parseInt(defaultProperty))) {
            user.setDefaultProperty("");
            if (PROPERTY_STRING.equalsIgnoreCase(ldapUser.getDefaultPropertyOrGroup())) {
                user.setDefaultPropertyOrGroup("");
            }
        }
    }

    public List<Integer> extractAuthorizedPropertiesFromRolesForUser(LDAPUser user) {
        List<Integer> authorizedPropertyIds = new ArrayList<Integer>();

        user.getAuthGroupRoles().forEach(authGroupRoleMapping ->
                authorizedPropertyIds.addAll(extractPropertyIdsForAuthGroupFromRole(authGroupRoleMapping)));

        authorizedPropertyIds.addAll(user.getPropertyRoles()
                .stream()
                .map(PropertyRoleMapping::getPropertyIdAsInt)
                .collect(Collectors.toList()));

        return authorizedPropertyIds;
    }

    @SuppressWarnings("unchecked")
	public
    List<Integer> extractPropertyIdsForAuthGroupFromRole(AuthGroupRoleMapping authGroupRoleMapping) {
        Integer authGroupId = Integer.parseInt(authGroupRoleMapping.getAuthGroupId());
        return extractPropertyIdsByAuthGroupId(authGroupId, PacmanWorkContextHelper.getClientId());
    }

    public List<Integer> extractPropertyIdsByAuthGroupId(Integer authGroupId, Integer clientId) {
        if (AuthorizationGroup.ALL_PROP_ID.equals(authGroupId)) {
            return globalCrudService.findByNamedQuery(Property.GET_ALL_ACTIVE_IDS_BY_CLIENT,
                    QueryParameter.with("clientId", clientId).parameters());
        } else {
            return globalCrudService.findByNamedQuery(AuthorizationGroupPropertyMapping.GET_PROPERTY_BY_AUTH_GROUP,
                    QueryParameter.with("authGroupId", authGroupId).and("statusId", Constants.ACTIVE_STATUS_ID).parameters());
        }
    }

    public void updatePropertyGroups(int userId) {
        int clientId = workContextRestEasyController.getCurrentClientId();
        List<PropertyGroup> propertyGroupList = propertyGroupService.getAllPropertyGroupsForUser(userId, clientId);
        if (null != propertyGroupList && !propertyGroupList.isEmpty()) {
            List<Property> authorizedPropertyList = authorizationService.retrieveAuthorizedProperties(userId, clientId);
            propertyGroupService.updatePropertyGroupsForUser(propertyGroupList, authorizedPropertyList);
        }

        List<PropertyGroup> ruleBasedPropertyGroupList = propertyGroupService.getAllRuleBasedPropertyGroupsForUser(userId, clientId);
        if (null != ruleBasedPropertyGroupList && !ruleBasedPropertyGroupList.isEmpty()) {
            for (PropertyGroup propertyGroup : ruleBasedPropertyGroupList) {
                propertyGroupService.syncRuleBasedPropertyGroup(propertyGroup);
            }
        }
    }

    public LDAPUser updateUserPreferences(String uid, LDAPUser user) {
        //Throw an error if the user being updated is not the currently logged in user
        if (!workContextRestEasyController.getCurrentUserId().equals(Integer.valueOf(uid))) {
            throw TetrisSecurityException.USER_NOT_AUTHORIZED;
        }

        //Throw an error when a non-editable parameter is trying to be changed
        LDAPUser existingUser = getById(Integer.valueOf(uid));
        if ((!existingUser.getCn().equals(user.getCn())) ||
                (!existingUser.getGivenName().equals(user.getGivenName())) ||
                isUniqueUserIdUpdated(user, existingUser) ||
                (!existingUser.getSn().equals(user.getSn())) ||
                (!existingUser.getUid().equals(user.getUid())) ||
                (!existingUser.getActive().equals(user.getActive())) ||
                (!existingUser.getClient().equals(user.getClient())) ||
                (!existingUser.getAuthGroupRoles().toString().equals(user.getAuthGroupRoles().toString())) ||
                (null != existingUser.getSalesforceOrganizationId() && !existingUser.getSalesforceOrganizationId().equals(user.getSalesforceOrganizationId())) ||
                (null != existingUser.getSalesforcePortalId() && !existingUser.getSalesforcePortalId().equals(user.getSalesforcePortalId())) ||
                (!existingUser.getIsCorporate().equals(user.getIsCorporate())) ||
                (!existingUser.getHasSalesforceAccess().equals(user.getHasSalesforceAccess())) ||
                (existingUser.isNotifyPasswordExpiration() != user.isNotifyPasswordExpiration()) ||
                (!existingUser.getDaysRemainingToExpirePassword().equals(user.getDaysRemainingToExpirePassword())) ||
                (!existingUser.getPropertyRoles().toString().equals(user.getPropertyRoles().toString())) ||
                (existingUser.getPasswordNeverExpire() != user.getPasswordNeverExpire()) ||
                (existingUser.isInternal() != user.isInternal()) ||
                (!existingUser.getDN().equals(user.getDN()))) {
            throw TetrisSecurityException.USER_NOT_AUTHORIZED;
        }
        return updateUserConfiguredPreferences(uid, user);
    }

    private boolean isUniqueUserIdUpdated(LDAPUser user, LDAPUser existingUser) {
        if (useUniqueUserIDInsteadOfEmailEnabled()) {
            return isNull(existingUser.getUniqueUserID()) || !existingUser.getUniqueUserID().equals(user.getUniqueUserID());
        }
        return !existingUser.getMail().equals(user.getMail());
    }

    public LDAPUser updateUserConfiguredPreferences(String uid, LDAPUser user) {
        if (null == user.getDefaultPropertyGroup()) {
            user.setDefaultPropertyGroupId(-1);
        }
        LDAPUser updatedUser = this.updateCore(uid, user);
        GlobalUser globalUser = globalCrudService.find(GlobalUser.class, user.getUserId());
        if (globalUser != null) {
            globalUser.setLanguage(user.getLanguage());
            globalUser.setUserPreferences(getUserPreferences(user));
            globalCrudService.save(globalUser);
        }
        Integer userId = Integer.parseInt(uid);
        Integer propertyGroupForDefault = null;
        if (StringUtils.isNotEmpty(updatedUser.getDefaultPropertyGroup())) {
            propertyGroupForDefault = Integer.parseInt(updatedUser.getDefaultPropertyGroup());
        }
        propertyGroupService.updateDefaultPropertyGroupToDbForUser(propertyGroupForDefault, userId);
        return updatedUser;
    }

    public String getUserPreferences(LDAPUser user) {
        JSONObject userPreferences = new JSONObject();
        userPreferences.put(VIEWING_PREFERENCE.getPreferenceId(), user.getViewingPreference());
        userPreferences.put(DEFAULT_PROPERTY.getPreferenceId(), user.getDefaultProperty());
        userPreferences.put(DEFAULT_PROPERTY_GROUP.getPreferenceId(), user.getDefaultPropertyGroup());
        userPreferences.put(DEFAULT_PROPERTY_OR_GROUP.getPreferenceId(), user.getDefaultPropertyOrGroup());
        userPreferences.put(DEFAULT_LANDING_PAGE.getPreferenceId(), user.getDefaultLandingPage());
        userPreferences.put(DEFAULT_DATE_FORMAT.getPreferenceId(), user.getDefaultDateFormat());
        userPreferences.put(LANGUAGE.getPreferenceId(), user.getLanguage());
        userPreferences.put(FISCAL_CALENDAR_ENABLED.getPreferenceId(), user.getEnableFiscalCalendar());
        userPreferences.put(DEFAULT_INVENTORY_GROUP.getPreferenceId(), user.getDefaultInventoryGroup());

        return userPreferences.toJSONString();
    }

    /*
     * Non corporate to non corporate must have superset of the properties
     */
    private void checkCurrentUserAllowedToModify(Integer targetUserId) {
        Integer currentUserId = workContextRestEasyController.getCurrentUserId();
        if (!isCurrentUserCorporate() && !currentUserId.equals(targetUserId)) {
            Integer clientId = workContextRestEasyController.getCurrentClientId();
            List<Property> currentUserProperties = authorizationService.retrieveAuthorizedProperties(currentUserId, clientId);
            List<Property> targetUserProperties = authorizationService.retrieveAuthorizedProperties(targetUserId, clientId);
            if (!currentUserProperties.containsAll(targetUserProperties)) {
                throw new TetrisException(ErrorCode.SECURITY_ERROR, "User does not have access to all target properties.");
            }
        }
    }

    public Set<LDAPUser> listUsersForRole(String roleId) {
        StopWatch timer = new StopWatch();
        timer.start();
        Set<LDAPUser> users = userGlobalDBService.listUsersForRole(roleId);

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("listUsersForRole(" + roleId + ") " + ", time: "
                    + timer.getTime() + "ms");
        }
        return users;
    }

    public Set<LDAPUser> listUsersForAuthGroup(String authGroupId,
                                               boolean isInternal) {
        String clientCode = isInternal ? Constants.CLIENT_INTERNAL : WorkContextHelper.getCurrent().getClientCode();
        List<GlobalUser> userBelongsToAuthGroups = globalCrudService.findByNamedQuery(
                UserAuthGroupRole.FIND_USERS_FOR_AUTH_GROUP, QueryParameter.with("authGroupId", Integer.valueOf(authGroupId)).and(CLIENT_CODE, clientCode).parameters());
        return userBelongsToAuthGroups.stream().map(globalUser -> ldapUserConverter.toLDAPUser(globalUser)).collect(Collectors.toSet());
    }


    // Demonstrate exception handling / serialization
    public LDAPUser getBad() {
        throw new TetrisException(ErrorCode.INVALID_REQUEST, "Invalid LDAP request");
    }


    public String updatePasswordFromUI(String userId, String password,
                                       String newPassword) {
        //US29047 - Security Audit. Check to make sure that the user changing their password is the actual logged in user.
        Integer currentUserId = workContextRestEasyController.getCurrentUserId();
        Integer id = Integer.valueOf(userId);
        if (!currentUserId.equals(id)) {
            throw TetrisSecurityException.USER_NOT_AUTHORIZED;
        }

        return changePassword(userId, password, newPassword);
    }

    public String changePassword(String userId, String password, String newPassword) {
        //This performs the same password validation as the javaScript validation. This is needed if someone attempts to change their password via a REST call.
        if (!validatePassword(newPassword)) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "The Password does not meet minimum requirements: " + newPassword);
        }

        LOGGER.debug("attempting to change password for UID: " + userId);
        try {

            //TODO grab this from DB and not from LDAP
            String client = getClientFromDBById(Integer.valueOf(userId)).toLowerCase();

            boolean isDBAuthenciationEnabledForClient = isOpenAmDisabledForClient(client);
            boolean isDBAuthenticationEnabledGlobal = isOpenAmDisabled();
            if (isDBAuthenciationEnabledForClient || isDBAuthenticationEnabledGlobal) {
                LOGGER.debug("Using DB to authenticate for userId: " + userId);
                authenticateUserUsingDB(userId, password);
            } else {
                authenticateUser(userId, password, client);
            }

        } catch (OpenAMException | CognitoException | LoginException e) {
            LOGGER.debug("Authentication for " + userId + " failed! ", e);
            int failCount = incrementFailCount();
            if (exceedsMaximumLoginAttempts(failCount)) {
                logThemOUT();
            }
            return "Authentication FAILED!";
        } catch (Exception e) {
            LOGGER.error("we threw an exception: " + e);
            return "FAIL";
        }
        return updatePassword(userId, newPassword);
    }
    private void authenticateUserUsingDB(String userId, String password) throws LoginException {
        GlobalUser globalUser = getGlobalUser(Integer.valueOf(userId), false);
        UserPassword userPassword = globalUser.getUserPassword();
        Password encryptedPass = EncryptionDecryption.encrypt(String.valueOf(password), DatatypeConverter.parseHexBinary(userPassword.getSalt()));

        boolean authenticated = encryptedPass.getSaltedHash().equals(userPassword.getPassword());
        if (!authenticated) {
            throw new LoginException("The password entered does not match the one stored in the db");
        }
    }

    public CognitoUserManagementService getCognitoUserManagementService() {
        return new CognitoUserManagementService();
    }

    protected void authenticateUser(String userId, String password, String client) throws OpenAMException {
        //authenticate with client's encrypted password
        String salt = userGlobalDBService.getSalt(userId);
        Password encryptedPassword = EncryptionDecryption.encrypt(password, DatatypeConverter.parseHexBinary(salt));
        TetrisSecurityService.getOpenAMClient().authenticate(userId, encryptedPassword.getSaltedHash(), client + DB_REALM_SUFFIX);
    }

    public boolean validatePassword(String password) {
        Boolean validPassword = true;

        //Password must be at least 8 characters
        if (password.length() < 8) {
            validPassword = false;
        }

        //Password should contain a mix of capital and lower-case letters, numbers and symbols
        if (!hasUppercase.matcher(password).find() ||
                !hasLowercase.matcher(password).find() ||
                !hasNumber.matcher(password).find() ||
                !hasSpecialChar.matcher(password).find()) {
            validPassword = false;
        }

        return validPassword;
    }

    private void updatePasswordHistory(GlobalUser user, String passwordHash, String passwordHistory) {
        if (null == passwordHistory) {
            passwordHistory = passwordHash;
        }
        user.setPasswordHistory(passwordHistory);
        globalCrudService.save(user);
    }

    public boolean isPasswordReused(String passwordHistory, String passwordHash) {
        if (null == passwordHistory) {
            return false;
        }
        return passwordHistory.contains(passwordHash);
    }

    public String buildPasswordHistory(String passwordHistory, String hash) {
        if (null == passwordHistory) {
            return null;
        }
        String[] passwords = passwordHistory.split(",");
        if (passwords.length < PASSWORD_HISTORY_COUNT) {
            return passwordHistory + "," + hash;
        }
        String separator = "";
        StringBuilder newPasswordHistory = new StringBuilder();
        for (int i = 1; i < passwords.length; i++) {
            newPasswordHistory.append(separator);
            separator = ",";
            newPasswordHistory.append(passwords[i]);
        }
        newPasswordHistory.append(separator).append(hash);
        return newPasswordHistory.toString();
    }

    public String getSaltedHash(String password) {
        StringBuilder hash = new StringBuilder();
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] digest = md.digest((SALT + password).getBytes("UTF-8"));
            for (int i = 0; i < digest.length; i++) {
                hash.append(Integer.toHexString(0xFF & digest[i]));
            }
        } catch (NoSuchAlgorithmException e) {
            LOGGER.error("Attempted to hash password with unsupported algorithm: SHA-1", e);
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "Service threw error when trying to hash password", e);
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("Attempted to hash password with unsupported encoding", e);
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "Service threw error when trying to hash password", e);
        }
        return hash.toString();
    }

    private String getErrorMessage(Exception e) {
        LOGGER.error("We threw an exception trying to reset the user password" + e);
        String failureMessage;
        if (e.getMessage().contains("reuse")) {
            failureMessage = "FAILED - Password Reused ";
        } else if (e.getMessage().contains(INVALID_USER)) {
            failureMessage = "FAILED - " + INVALID_USER;
        } else {
            failureMessage = "Changing the password FAILED! " + e.getMessage();
        }
        return failureMessage;
    }

    public int incrementFailCount() {
        int attempts = PacmanThreadLocalContextHolder.incrementLoginAttempts();
        logLoginAttempets(attempts, false);
        return attempts;
    }

    public boolean exceedsMaximumLoginAttempts(int failCount) {
        // look at some system param
        String maxLoginAttempts = com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.getMaximumLoginAttempts();
        int max = Integer.parseInt(maxLoginAttempts);
        LOGGER.info("The maximum number of login attempts is " + maxLoginAttempts);
        logLoginAttempets(max, true);

        boolean hasExceeded = failCount > max;
        LOGGER.info("The user " + (hasExceeded ? "has" : "has not") + " exceeded the maximum number of attempts");
        return hasExceeded;
    }

    private void logLoginAttempets(int count, boolean wasFail) {
        String message = "The user has attempted to login " + count + TIMES;
        if (!wasFail) {
            LOGGER.debug(message);
        } else {
            LOGGER.info(message);
        }
    }

    public void logThemOUT() {
        // TODO - log the user out since they've exceeded the maximum number of
        // login attempts
    }

    /**
     * Technically, i think we just need to switch the LDAP group from client to
     * ideas Could perform some cleanup. Not sure the investment pays off. Look
     * to removing salesforce entry (only supports soft delete) since internal
     * are not synced
     */

    public String convertUserToInternal(String uid) {
        return convertUserClient(uid, Constants.CLIENT_INTERNAL, true) + " (All client roles removed. Please assign desired internal roles.)";
    }

    public String convertUserToExternal(String uid) {
        // Convert to the client of the current work context
        String currentClientCode = workContextRestEasyController.getCurrentClientCode();
        return convertUserClient(uid, currentClientCode, true) + " (Internal roles removed. Please assign desired '" + currentClientCode
                + "' roles.)";
    }

    /*
     * What to do with roles as roles are client contingent & 'all permissions'
     * role joining the dinosaurs
     *
     * If pass 'clientId' as the url param, get exception in
     * 'multiPropertyCrudService.find' in UserSyncService.persistUser via
     * UserSyncService.persistTenantUser via UserSyncService.getTenantUser Must
     * use 'propertyId' - check Scott's context mechanism
     */
    private String convertUserClient(String uid, String newClient, boolean resetPropertyRoles) {
        if (!PacmanThreadLocalContextHolder.getPrincipal().isInternalUser()) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "You are not worthy of converting user's client.");
        }
        Boolean isMakingInternal = newClient.equals(Constants.CLIENT_INTERNAL);
        LDAPUser user = getUserById(uid);
        if (newClient.equals(user.getClient())) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "User already " + (isMakingInternal ? INTERNAL : EXTERNAL));
        }
        resetUsersPermissions(user, isMakingInternal, resetPropertyRoles);
        try {
            syncService.persistUser(Integer.valueOf(uid), user, isMakingInternal, newClient, null, true);
            // Can we ignore salesforce - could become transactional nightmare
            // for edge case
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "Unable to convert user to " + (isMakingInternal ? INTERNAL : EXTERNAL), e);
        }
        return uid;
    }

    private void resetUsersPermissions(LDAPUser user, boolean isMakingInternal, boolean resetPropertyRoles) {
        // Remove roles as will not apply to new client - we could grant
        // internals "All Permissions" if makes sense
        user.setAuthGroupRoles(null);
        if (isMakingInternal) {
            // UI was preventing setting to All Perms. Let's start there.
            user.setAuthGroupRoles(Collections.singletonList(new AuthGroupRoleMapping(Role.ALL_PERMS_ID, AuthorizationGroup.ALL_PROP_ID.toString())));
        }
        //This is for skipping reset of property roles in case of move property
        if (resetPropertyRoles) {
            user.setPropertyRoles(null);
        }
    }

    public Set<String> getAuthorizedPagesForProperty(String uid, String propertyId) {
        String dn = getDN(uid);

        if (dn == null) {
            logUserNotFound(uid);
            return new HashSet<>();
        }

        Set<String> permissions = authorizationService.getPermsForUserAndProperty(dn, propertyId, false);
        return parsePageCodesFromPermissions(permissions);
    }

    public Set<String> getAuthorizedPagePermissionsForProperty(String uid, String propertyId) {
        String dn = getDN(uid);

        if (dn == null) {
            logUserNotFound(uid);
            return new HashSet<>();
        }

        return authorizationService.getPermsForUserAndProperty(dn, propertyId, false);
    }

    private String getDN(String uid) {
        DN baseDN = DN.valueOf(com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.getSecurityLdapBaseDN());
        String dn;
        try {
            dn = new LDAPMapper().getDN(baseDN, uid, getClientFromDBById(Integer.valueOf(uid)), LDAPUser.class).toString();
        } catch (LDAPException e) {
            logUserLookupException(uid, e);
            return null;
        }

        return dn;
    }

    private void logUserLookupException(String uid, LDAPException e) {
        LOGGER.error("An LDAP exception was thrown: could not find a user with uid: " + uid, e);
    }

    private void logUserNotFound(String uid) {
        LOGGER.error("No user found with UID: " + uid);
    }

    public Set<String> getAuthorizedPagesForPropertyGroup(String uid,
                                                          String propertyGroupId) {
        DN baseDN = DN.valueOf(com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.getSecurityLdapBaseDN());
        String dn;
        try {
            dn = new LDAPMapper().getDN(baseDN, uid, getClientFromDBById(Integer.valueOf(uid)), LDAPUser.class).toString();
        } catch (LDAPException e) {
            logUserLookupException(uid, e);
            return new HashSet<String>();
        }
        if (dn == null) {
            logUserNotFound(uid);
            return new HashSet<String>();
        }

        PropertyGroup propertyGroup = propertyGroupService.getPropertyGroupById(Integer.parseInt(propertyGroupId));
        if (null == propertyGroup) {
            LOGGER.error("Could not lookup property group: " + propertyGroupId);
            return new HashSet<String>();
        }

        Set<String> permissions = new HashSet<String>();
        for (PropertyPropertyGroup group : propertyGroup.getPropertyPropertyGroups()) {
            permissions.addAll(authorizationService.getPermsForUserAndProperty(dn, String.valueOf(group.getProperty().getId()), false));
        }
        return parsePageCodesFromPermissions(permissions);
    }

    /*
     * LDAP holds permissions in a format like:
     * pageCode=authorization-groups&access=readWrite
     * pageCode=booking-pace-report&access=readWrite We'll parse these out to
     * return: "authorization-groups" and "booking-pace-report" Leave the
     * special role ALL_PERMS (-666)
     */
    public Set<String> parsePageCodesFromPermissions(Set<String> perms) {
        Set<String> pageCodes = new HashSet<String>();
        for (String perm : perms) {
            if (perm.equals(Role.ALL_PERMS_ID)) {
                pageCodes.add(perm);
            } else {
                String pageCode = StringUtils.substringBetween(perm, LDAPConstants.PAGE_CODE_KEY_VALUE, "&");
                if (pageCode != null) {
                    pageCodes.add(pageCode);
                } else {
                    LOGGER.warn("Could not obtain valid page code for permission string: " + perm);
                }
            }
        }
        return pageCodes;
    }

    /*
     * Need to add all the users (client & global) to the Users table. Really
     * only needed for reference (e.g. reports). We are not concerning with
     * access at this point as access can continually change as auth groups &
     * roles changes and we never hard delete once added. Just easier to add
     * entry once and be done.
     *
     * If client has another property, clone all users. Otherwise, if first, add
     * just global users
     */
    @SuppressWarnings("unchecked")
    public void createUsersForNewProperty() {
        List<GlobalUser> clientUsers = listDatabaseUsersForClient(workContextRestEasyController.getCurrentClientId());
        LOGGER.info("Number of users to add to new property: " + clientUsers.size());
        // Property rollout db maintain may add some
        List<User> prepopulatedUsers = tenantCrudService.findByNamedQuery(User.ALL);
        LOGGER.info("Number of users already existing for new property via db maintain: " + prepopulatedUsers.size());
        filterOutExistingUsers(clientUsers, prepopulatedUsers);
        LOGGER.info("Number of new users we are actually adding to new property: " + clientUsers.size());
        User user;
        for (GlobalUser globalUser : clientUsers) {
            user = new User();
            user.setId(globalUser.getId());
            user.setScreenName(globalUser.getScreenName());
            user.setName(globalUser.getFullName());
            user.setEmail(globalUser.getEmail());
            user.setStatusId(globalUser.getStatusId());
            try {
                tenantCrudService.save(user);
            } catch (Exception e) {
                LOGGER.info("User " + user.getId() + " could not be inserted for new property", e);
            }
        }
    }

    @SuppressWarnings("unchecked")
    public List<GlobalUser> listDatabaseUsersForClient(Integer clientId) {
        return globalCrudService.findByNamedQuery(GlobalUser.ALL_FOR_CLIENT, QueryParameter.with("clientId", clientId)
                .parameters());
    }

    public Set<LDAPUser> listLdapUsersForClient(String clientCode) {
        List<GlobalUser> globalUsers = globalCrudService.findByNamedQuery(GlobalUser.BY_CLIENT_CODE, QueryParameter.with(CLIENT_CODE, clientCode).parameters());
        return globalUsers.stream().map(globalUser -> ldapUserConverter.toLDAPUser(globalUser)).collect(Collectors.toSet());
    }

    public Set<LDAPUser> listAllUsersForClientAndAssociatedProperty(String clientCode, int propertyId) {
        List<GlobalUser> globalUsers = getUsersForClientAndAssociatedProperty(clientCode, propertyId);
        return globalUsers.stream().map(globalUser -> ldapUserConverter.toLDAPUser(globalUser)).collect(Collectors.toSet());
    }

    public Set<LDAPUser> getGlobalUsersByEmailIds(List<String> emails) {
        if (CollectionUtils.isEmpty(emails)) {
            return new HashSet<>();
        }
        List<GlobalUser> globalUsers = globalCrudService.findByNamedQuery(GlobalUser.ALL_ACTIVE_BY_EMAIL_IDS, QueryParameter.with("ids", emails).parameters());
        return globalUsers.stream().map(globalUser -> ldapUserConverter.toLDAPUser(globalUser)).collect(Collectors.toSet());
    }

    private void filterOutExistingUsers(List<GlobalUser> users, List<User> existingUsers) {
        for (User existingUser : existingUsers) {
            for (GlobalUser user : users) {
                if (existingUser.getId().equals(user.getId())) {
                    users.remove(user);
                    LOGGER.info("Removing user " + existingUser.getId() + " from new property users as appears created by db maintain");
                    break;
                }
            }
        }
    }

    public void setDefaultPropertyGroupToLdap(Integer userId, boolean isDefaultPropertyGroup, Integer propertyGroupId) {
        LDAPUser user = new LDAPUser();
        try {
            user = getById(userId);
            if (!isDefaultPropertyGroup) {
                propertyGroupId = -1;
            }
            user.setDefaultPropertyGroupId(propertyGroupId);
            user = this.updateCore(userId.toString(), user);
            updateUserPreferencesToGlobal(user);
        } catch (LDAPException e) {
            LOGGER.error("LDAP exception in setDefaultPropertyGroupToLdap() : ", e);
        }
    }

    /**
     * Clears the default property from ldap if its not in authorized property
     * ids. Also clears the defaultPropertyOrGroup option if its sets to
     * 'property'.
     */
    public void clearUnathourizedDefaultPropertyFromLdap(Integer userId, List<Integer> authorizedPropertyIds,
                                                         WorkContextType workContextType, TetrisPrincipal tetrisPrincipal) {
        // as this method gets called asynchronously need to set work context
        // got it from calling method
        PacmanWorkContextHelper.setWorkContext(workContextType);
        PacmanThreadLocalContextHolder.setPrincipal(tetrisPrincipal);
        LDAPUser user = new LDAPUser();
        try {
            user = getById(userId);
            String defaultProperty = user.getDefaultProperty();
            if (null != defaultProperty) {
                Integer defaultPropertyId = Integer.parseInt(defaultProperty);
                if (!authorizedPropertyIds.contains(defaultPropertyId)) {
                    user.setDefaultProperty("");
                    if (PROPERTY_STRING.equalsIgnoreCase(user.getDefaultPropertyOrGroup())) {
                        user.setDefaultPropertyOrGroup("");
                    }
                    user = this.updateCore(userId.toString(), user);
                    updateUserPreferencesToGlobal(user);
                }
            }
        } catch (LDAPException e) {
            LOGGER.error("LDAP exception in clearUnathourizedDefaultPropertyFromLdap() : ", e);
            notifyUserByEmail(user);
        }
    }

    public LDAPUser removeDefaultPropertyIfUnauthorized(LDAPUser user) {
        user.setDefaultProperty(EMPTY);
        if (PROPERTY_STRING.equalsIgnoreCase(user.getDefaultPropertyOrGroup())) {
            user.setDefaultPropertyOrGroup("");
        }
        updateUserPreferencesToGlobal(user);
        return user;
    }

    public void notifyUserByEmail(LDAPUser user) {
        emailService.sendHtml(SystemConfig.getEmailCreateUserFrom(), user.getMail(), EMAIL_SUBJECT, EMAIL_BODY);
    }

    public String getServerTime(HttpServletRequest request) {
        @SuppressWarnings("unchecked")
        List<LearningClientUser> available = globalCrudService.findByNamedQuery(LearningClientUser.NEXT_AVAILABLE, 1);
        long time = 0;

        if (!available.isEmpty()) {
            time = System.currentTimeMillis();
        }

        // do this just to create a session
        request.getSession().setAttribute("time", time);
        return Long.toString(time);
    }

    public Object getAvailableLmsUser(HttpServletRequest request, String time,
                                      String securityHash) {
        Map<String, String> map = new HashMap<>();
        verifySecurityHash(time, securityHash);

        for (int retries = 3; retries > 0; retries--) {
            try {
                allocateUser(map);
                break;
            } catch (Exception e) {
                map.clear();
                map.put(STATUS, e.getLocalizedMessage());

                if (ExceptionUtils.indexOfThrowable(e, OptimisticLockException.class) >= 0) {
                    LOGGER.warn("Optimistic lock encountered. Retrying " + retries + TIMES);
                    sleep(250);
                } else {
                    LOGGER.error("Error getting available LMS user", e);
                    break;
                }
            }
        }

        return map;
    }

    public Object getAvailableLmsUser2(HttpServletRequest request) {
        Map<String, String> map = new HashMap<>();

        for (int retries = 3; retries > 0; retries--) {
            try {
                allocateUser(map);
                break;
            } catch (Exception e) {
                map.clear();
                map.put(STATUS, e.getLocalizedMessage());

                if (ExceptionUtils.indexOfThrowable(e, OptimisticLockException.class) >= 0) {
                    LOGGER.warn("Optimistic lock encountered. Retrying " + retries + TIMES);
                    sleep(250);
                } else {
                    LOGGER.error("Error getting available LMS user", e);
                    break;
                }
            }
        }

        return map;
    }

    public Object getAvailableLmsUser3(HttpServletRequest request) {
        Map<String, String> map = new HashMap<>();

        map.put(STATUS, "SUCCESS");
        map.put("username", "<EMAIL>");
        map.put(PASSWORD_STRING, "password123");

        return map;
    }

    private void allocateUser(Map<String, String> map) {
        LocalDateTime now = LocalDateTime.now();

        @SuppressWarnings("unchecked")
        List<LearningClientUser> available = globalCrudService.findByNamedQuery(LearningClientUser.NEXT_AVAILABLE, 1);

        if (available.isEmpty()) {
            map.put(STATUS, "IN_USE");
        } else {
            LearningClientUser lcu = available.get(0);
            String uid = lcu.getClientUser().getUserID().toString();
            lcu.setStatus(LearningClientUser.Status.IN_USE.toString());
            lcu.setLastAllocatedTimestamp(now);
            lcu.setLastUpdatedTimestamp(now);

            LDAPUser user = lookupUser(uid);

            map.put(STATUS, "SUCCESS");
            map.put("username", user.getMail());
            String lmsUserPassword = RandomStringUtils.randomAlphanumeric(PASSWORD_LENGTH_GENERATED);
            map.put(PASSWORD_STRING, lmsUserPassword);

            Password encryptedPassword = EncryptionDecryption.encrypt(lmsUserPassword);
            GlobalUser globalUserById = userGlobalDBService.getGlobalUserById(user.getUserId());
            globalUserById.setUserPassword(new UserPassword(encryptedPassword.getSaltedHash(), encryptedPassword.getSalt()));
            globalCrudService.save(globalUserById);
            globalCrudService.save(lcu);
            globalCrudService.flush();
        }
    }

    private LDAPUser lookupUser(String uid) {
        try {
            LDAPUser user = getUserById(uid);
            LOGGER.info("Allocating user: " + uid + "(" + user.getMail() + ") for use by LMS");
            return user;
        } catch (LDAPException e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "LDAP failed in user lookup.", e);
        }
    }

    private void verifySecurityHash(String time, String securityHash) {
        // TODO get from SystemConfig?
        String secretKey = "sesame";

        try {
            if (System.currentTimeMillis() - Long.parseLong(time) > 30000) {
                throw new TetrisException(ErrorCode.SECURITY_ERROR, "Expired security token.");
            }

            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] thedigest = digest.digest((time + secretKey).getBytes("utf-8"));
            String computed = new HexBinaryAdapter().marshal(thedigest);

            if (!securityHash.equalsIgnoreCase(computed)) {
                throw new TetrisException(ErrorCode.SECURITY_ERROR, "Invalid security token.");
            }
        } catch (NumberFormatException e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, MESSAGE_DIGEST_FAILURE + e.getMessage(), e);
        } catch (NoSuchAlgorithmException e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, MESSAGE_DIGEST_FAILURE + e.getMessage(), e);
        } catch (UnsupportedEncodingException e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, MESSAGE_DIGEST_FAILURE + e.getMessage(), e);
        }
    }

    @SuppressWarnings("unchecked")
    public List<LearningClientUser> getAllLmsUsers() {
        return globalCrudService.findByNamedQuery(LearningClientUser.ALL);
    }

    @SuppressWarnings("unchecked")
    public List<LearningClientUser> getStaleLmsUsers() {
        LocalDateTime cutoff = LocalDateTime.now().minusHours(SystemConfig.getLmsHoursUntilStale());
        return globalCrudService.findByNamedQuery(LearningClientUser.STALE_IN_USE, QueryParameter.with("date", cutoff)
                .parameters());
    }

    public void cleanEmptyAuthGroupsFromUserPermissions(List<Integer> userBelongsToEmptyAuthGroups, List<Integer> emptyAuthGroupIds) {
        for (Integer userId : userBelongsToEmptyAuthGroups) {
            LDAPUser user = getUserById(userId.toString());
            List<AuthGroupRoleMapping> authGroupRoles = user.getAuthGroupRoles();
            Iterator<AuthGroupRoleMapping> authGroupRoleMappings = authGroupRoles.iterator();
            while (authGroupRoleMappings.hasNext()) {
                AuthGroupRoleMapping authGroupRoleMapping = authGroupRoleMappings.next();
                Integer authGroupId = null != authGroupRoleMapping.getAuthGroupId() ? Integer.parseInt(authGroupRoleMapping
                        .getAuthGroupId()) : 0;
                if (emptyAuthGroupIds.contains(authGroupId)) {
                    authGroupRoleMappings.remove();
                }
            }
            user.setAuthGroupRoles(authGroupRoles);
        }
    }

    @SuppressWarnings("squid:S2142")
    private void sleep(int ms) {
        try {
            Thread.sleep(ms);
        } catch (InterruptedException e1) {
            // do nothing
        }
    }

    public String getUserPreferredDateFormat() {
        String userId = PacmanWorkContextHelper.getUserId();
        GlobalUser globalUser = userGlobalDBService.getGlobalUserById(Integer.parseInt(userId));
        LDAPUser existingLDAPUser = ldapUserConverter.toLDAPUser(globalUser);
        if (existingLDAPUser != null) {
            setUserDateFormatForUser(existingLDAPUser);
            return StringUtils.replaceChars(existingLDAPUser.getDefaultDateFormat(), 'm', 'M');
        }
        return null;
    }

    public boolean deleteInternalUserPhysically(String uid) {
        try {
            String clientForUserId = getClientFromDBById(Integer.valueOf(uid));
            boolean isInternal = clientForUserId.equals(Constants.CLIENT_INTERNAL);
            if (isInternal) {
                syncService.deleteInternalUser(Integer.valueOf(uid));
            }
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "An error occurred while deleting the user: " + uid, e);
        }
        return true;
    }

    public String resetPassword(String token, String newPassword) {

        //This performs the same password validation as the javaScript validation. This is needed if someone attempts to reset their password via a REST call.
        if (!validatePassword(newPassword)) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "The Password does not meet minimum requirements: " + newPassword);
        }

        Integer userID = passwordSecurityService.validateToken(token);
        if (userID > 0) {
            return updatePassword(String.valueOf(userID), newPassword);
        }
        return "FAILED - " + INVALID_USER;
    }

    private String updatePassword(String userId, String newPassword) {
        PacmanThreadLocalContextHolder.resetLoginAttempts();
        try {
            GlobalUser user = globalCrudService.findByNamedQuerySingleResult(GlobalUser.BY_USERID,
                    QueryParameter.with("id", Integer.valueOf(userId)).parameters());
            if (null == user) {
                throw new TetrisException(INVALID_USER);
            }
            String newPasswordHash = getSaltedHash(newPassword);
            boolean isPasswordReused = isPasswordReused(user.getPasswordHistory(), newPasswordHash);
            if (isPasswordReused) {
                throw new TetrisException("Password reused");
            }
            Password encryptedPassword = EncryptionDecryption.encrypt(newPassword);
            user.setUserPassword(new UserPassword(encryptedPassword.getSaltedHash(), encryptedPassword.getSalt()));
            String newPasswordHistory = buildPasswordHistory(user.getPasswordHistory(), newPasswordHash);
            user.setLastPasswordChangeDttm(LocalDateTime.now());
            updatePasswordInCognito(user, newPassword);
            updatePasswordHistory(user, newPasswordHash, newPasswordHistory);
        } catch (Exception e) {
            return getErrorMessage(e);
        }
        return userId;
    }

    private void updatePasswordInCognito(GlobalUser user, String newPassword) {
        if (user.isInternal() || !shouldUseCognitoForExternalUsers(user.getClientCode())) {
            return;
        }

        UpdateUserRequest updateUserRequest = UpdateUserRequest
                .builder()
                .username(user.getEmail())
                .email(user.getEmail())
                .password(newPassword)
                .name(new Name(user.getFirstName(), user.getLastName()))
                .locale(user.getLanguage())
                .clientCode(user.getClientCode())
                .applicationName(System.getProperty("fds.application.name", StringUtils.EMPTY))
                .active(user.isActive())
                .userType(user.getClientCode().equalsIgnoreCase(Constants.INTERNAL_CLIENT_NAME) ? "INTERNAL" : "CLIENT")
                .discoverAccess(user.getLearningAccess())
                .supportAccess(user.getSalesforceAccess())
                .build();
        getCognitoUserManagementService().updateCognitoUserAppIntegrationWithUserId(user.getCognitoUserId(), updateUserRequest);
    }

    private boolean shouldUseCognitoForExternalUsers(String clientCode) {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(ENABLE_COGNITO_LOGIN_FOR_EXTERNAL_USERS.getParameterName(), clientCode));
    }

    private boolean shouldUseCognitoForExternalSsoUsers(String clientCode) {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(ENABLE_COGNITO_LOGIN_FOR_EXTERNAL_SSO_USERS.getParameterName(), clientCode));
    }

    public boolean shouldRunFDSUserManagementSync(String clientCode) {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(ENABLE_FDS_SYNC_FOR_USERS.getParameterName(), clientCode));
    }

    public boolean shouldRunFDSUserManagementSyncForInternalUsers(String clientCode) {
        return clientCode.equalsIgnoreCase(INTERNAL_CLIENT_NAME) && SystemConfig.isCognitoUserSyncEnabledForInternalUsers();
    }

    public boolean shouldRunRoleAssignmentSyncForInternalUsers(String clientCode) {
        return clientCode.equalsIgnoreCase(INTERNAL_CLIENT_NAME) && SystemConfig.isRoleAssignmentSyncEnabledForInternalUsers();
    }

    private void removeG3Agent(Set<LDAPUser> users) {
        removeUser(users, getAgentUserEmail(PacmanWorkContextHelper.getClientCode()));
    }

    private void removeG3CertUser(Set<LDAPUser> users) {
        removeUser(users, getG3CertUserEmail(PacmanWorkContextHelper.getClientCode()));
    }

    protected void removeAliasUser(Set<LDAPUser> users) {
        String aliasEmail = pacmanConfigParamsService.getParameterValueByClientLevel(IntegrationConfigParamName.CENTRAL_RMS_USER_ALIAS_NAME.getParameterName(), PacmanWorkContextHelper.getClientCode());
        if (aliasEmail != null && !isLoggedInUserIsInternal()) {
            removeUser(users, aliasEmail);
        }
    }

    private void removeUser(Set<LDAPUser> users, String userEmailId) {
        LDAPUser ldapUser = getByEmailFromDB(userEmailId);
        if (null != ldapUser) {
            users.remove(ldapUser);
        }
    }

    public boolean isLoggedInUserIsInternal() {
        GlobalUser user = userGlobalDBService.getGlobalUserById(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        return user.isInternal();
    }

    public String moveUserUnderNewClient(String userId, String newClient, List<PropertyRoleMapping> propertyRoleMappings, String defaultPropertyId) {
        GlobalUser globalUser = userGlobalDBService.getGlobalUserById(Integer.parseInt(userId));
        final String language = globalUser.getLanguage();

        WorkContextType workContextType = PacmanWorkContextHelper.getWorkContext();
        WorkContextType tempWorkContextType = new WorkContextType();
        tempWorkContextType.setPropertyId(Integer.parseInt(defaultPropertyId));
        tempWorkContextType.setClientCode(newClient);
        tempWorkContextType.setUserId(workContextType.getUserId());
        PacmanWorkContextHelper.setWorkContext(tempWorkContextType);

        convertUserClient(userId, newClient, false);

        PacmanWorkContextHelper.setWorkContext(workContextType);

        LDAPUser user = getUserById(userId);
        user.setPropertyRoles(propertyRoleMappings);
        user.setAuthGroupRoles(null);
        user.setDefaultProperty(defaultPropertyId);
        user.setDefaultPropertyGroupId(-1);
        user.setDefaultPropertyOrGroup(PROPERTY_STRING);
        user.setLanguage(language);
        updateUserConfiguredPreferences(userId, user);
        return userId;
    }

    public List<GlobalUser> removeG3Agent(List<GlobalUser> globalUsers) {
        return globalUsers.stream()
                .filter(u -> !u.getEmail().equalsIgnoreCase(getAgentUserEmail(u.getClientCode())))
                .collect(Collectors.toList());
    }

    public void updateUserPreferencesToGlobal(LDAPUser updatedLdapUser) {
        String userPreferences = getUserPreferences(updatedLdapUser);
        globalCrudService.executeUpdateByNamedQuery(GlobalUser.UPDATE_USER_PREFERENCES,
                QueryParameter.with("userPreferences", userPreferences)
                        .and("id", updatedLdapUser.getUserId()).parameters());
    }

    public int updateUserSalesforceOrganizationToGlobal(String clientCode, String organizationId) {
        return globalCrudService.executeUpdateByNamedQuery(GlobalUser.UPDATE_USER_SALESFORCE_ORG,
                QueryParameter.with("salesforceOrganizationId", organizationId)
                        .and(CLIENT_CODE, clientCode).parameters());
    }

    public int updateUserSalesforcePortalToGlobal(String clientCode, String portalId) {
        return globalCrudService.executeUpdateByNamedQuery(GlobalUser.UPDATE_USER_SALESFORCE_PORTAL,
                QueryParameter.with("salesforcePortalId", portalId)
                        .and(CLIENT_CODE, clientCode).parameters());
    }

    public List<GlobalUser> getUsersForClientAndAssociatedProperty(String clientCode, int propertyId) {
        QueryParameter parameters = QueryParameter.with(CLIENT_CODE, clientCode).and("propertyId", propertyId).and("likeClause", "%\"defaultProperty\":\"" + propertyId + "\"%");
        return globalCrudService.findByNativeQuery(LIST_ALL_USERS_FOR_CLIENT_PROPETY, parameters.parameters(), GlobalUser.class);
    }

    public User getUserForProperty(Integer userId) {
        return tenantCrudService.find(User.class, userId);
    }

    public boolean isUserMemberOfAuthGroupAndRole(String userId, int authGroupId, String roleId) {
        Map<String, Object> queryParameterMap = QueryParameter.with("userId", Integer.valueOf(userId))
                .and("authGroupId", authGroupId)
                .and("roleId", roleId).parameters();
        final long count = globalCrudService.findByNamedQuerySingleResult(
                UserAuthGroupRole.COUNT_USERS_WITH_AUTH_GROUP_AND_ROLE, queryParameterMap);
        return count != 0;
    }

    public List<User> getTenantUsersById(Set<Integer> userIdsSet) {
        return tenantCrudService.findByNamedQuery(User.BY_IDS, QueryParameter.with("ids", userIdsSet).parameters());
    }

    public User getTenantUserById(Integer userId) {
        return tenantCrudService.findByNamedQuerySingleResult(User.BY_ID, QueryParameter.with("id", userId).parameters());
    }

        public boolean isNewUser(String email) {
        return getByEmailFromDB(email) == null;
    }

    public void startUserManagementSyncJob(String idsToSync, String clientCode) {
        jobServiceLocal.startGuaranteedNewInstance(JobName.UserManagementSyncJob,
                MapBuilder.with(JobParameterKey.USER_IDS, idsToSync)
                        .and(JobParameterKey.CLIENT_CODE, clientCode).get());
    }

    public void createOrUpdateUserFromFDS(String userId, String email, String clientCode) {
        UISUserV2 user = uisService.getUserV2(userId);
        Optional<LDAPUser> ldapOptional = getLDAPUserByClientCodeAndUISId(clientCode, userId);
        boolean isInternal = clientCode != null ? clientCode.equalsIgnoreCase(INTERNAL_CLIENT_NAME) : false;
        boolean isSSOEnabled = Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.SSOENABLED.value(), clientCode));
        boolean isLearningAccessEnabled = Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.LEARNING_ACCESS_ENABLED.value(), clientCode));

        if (ldapOptional.isPresent()) {
            LDAPUser ldapUser = ldapOptional.get();
            updateUISToLDAPParameters(user, ldapUser, email, clientCode, isInternal, isSSOEnabled, isLearningAccessEnabled);

            try {
                if (ldapUser.getUserId() != null && Integer.parseInt(ldapUser.getUid()) > 0) {
                    updateLDAPUserByFDSUser(ldapUser.getUid(), ldapUser);
                    boolean toInvokeDiscoverAPI = SystemConfig.isOldDiscoverAPIsEnabled() &&
                            ldapUser.getHasLearningAccess() &&
                            hasDiscoverAccessFromEntitlements(user.getEntitlements()) &&
                            !EmailUtil.isEmailMatchForDomain(email, "sas.com") &&
                            !EmailUtil.isEmailMatchForDomain(email, "ideas.com");
                    //As long as the update did not throw an exception, we can update discover access if required
                    if (toInvokeDiscoverAPI) {
                        discoverService.createOrUpdateDiscoverUser(ldapUser);
                    }
                }
            } catch (TetrisException e) {
                LOGGER.error("Error creating user from FDS with error message " + e.getBaseMessage());
            } catch (Exception e) {
                LOGGER.error("failure :: UserService => updateUserFromFDS(" + userId + ", " + email + ", " + clientCode + ")", e);
            }

        } else {
            LDAPUser byEmailFromDB = getByEmailFromDB(email);
            if (byEmailFromDB != null) {
                LOGGER.error("User with email " + email + " already exists in the system. Skipping the user creation from FDS for user " + userId + " email " + email + " clientCode " + clientCode);
            } else {
                LDAPUser ldapUser = new LDAPUser();
                if (user.getName() != null) {
                    updateUISToLDAPParameters(user, ldapUser, email, clientCode, isInternal, isSSOEnabled, isLearningAccessEnabled);
                    try {
                        //Not allowed to create a new user if they do not have a G3 role assigned to them
                        if (CollectionUtils.isNotEmpty(ldapUser.getPropertyRoles()) || CollectionUtils.isNotEmpty(ldapUser.getAuthGroupRoles())) {
                            createUserAndSkipPasswordEmail(ldapUser, isInternal);
                        } else {
                            LOGGER.info("User has no roles or auth groups associated with this environment.  Skipping user creation for user: " + user.getId());
                        }
                    } catch (TetrisException e) {
                        LOGGER.error("Error creating user from FDS with error message " + e.getBaseMessage());
                    } catch (Exception e) {
                        LOGGER.error("failure :: UserService => updateUserFromFDS(" + userId + ", " + email + ", " + clientCode + ")", e);
                    }
                } else {
                    LOGGER.info("User has no name.  Skipping user creation for user: " + user.getId());
                }
            }
        }
    }
    public String getFdsId(String email) {
        try {
            GlobalUser globalUser = globalCrudService.findByNamedQuerySingleResult(
                    GlobalUser.BY_EMAIL,
                    QueryParameter.with("emailAddress", email).parameters()
            );
            if (globalUser == null) {
                LOGGER.warn("No user found for email");
                return "Not Available";
            }
            String fdsId = globalUser.getCognitoUserId();
            if (fdsId == null) {
                LOGGER.warn("CognitoUserId is null for user");
                return "Not Available";
            }
            return fdsId;
        } catch (Exception e) {
            LOGGER.error("Error occurred while fetching user for email");
            return "Not Available";
        }
    }
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateLDAPUserByFDSUser(String id, LDAPUser user) {
        update(id, user);
    }

    public void updateUISToLDAPParameters(UISUserV2 user, LDAPUser ldapUser, String email, String clientCode, boolean isInternal, boolean isSSOEnabled, boolean isLearningAccessEnabled) {
        ldapUser.setMail(email);
        //If an existing user is updated without a name, we will not null out the name as its required by the G3 db
        if (user.getName() != null) {
            ldapUser.setGivenName(user.getName().getGivenName());
            ldapUser.setSn(user.getName().getFamilyName());
            ldapUser.setCn(user.getName().getFormatted());
        }
        ldapUser.setActive(user.isActive());
        ldapUser.setRoles(convertRolesFromEntitlements(user.getEntitlements(), clientCode));
        ldapUser.setPasswordNeverExpire(isInternal || isSSOEnabled);
        ldapUser.setCognitoUserId(user.getId());
        ldapUser.setHasLearningAccess(isLearningAccessEnabled && hasDiscoverAccessFromEntitlements(user.getEntitlements()));
        ldapUser.setHasSalesforceAccess(hasOtherAccessFromEntitlements(user.getEntitlements(), "SUPPORTACCESS"));
    }

    public boolean hasOtherAccessFromEntitlements(List<EntitlementV2> entitlements, String entityId) {
        if (CollectionUtils.isEmpty(entitlements)) {
            return false;
        } else {
            return entitlements
                    .stream()
                    .filter(item -> item.getAuthVersion() == 1)
                    .filter(Objects::nonNull)
                    .anyMatch(item -> item.getEntityId().equalsIgnoreCase(entityId) &&
                            item.getAllowedAction().equals("ALLOW"));
        }
    }

    public boolean hasDiscoverAccessFromEntitlements(List<EntitlementV2> entitlements) {
        if (CollectionUtils.isEmpty(entitlements)) {
            return false;
        } else {
            boolean legacyDiscoverEntitlement = entitlements
                    .stream()
                    .filter(item -> item.getAuthVersion() == 1)
                    .filter(Objects::nonNull)
                    .anyMatch(item -> item.getEntityId().equalsIgnoreCase("DISCOVERACCESS") &&
                            item.getAllowedAction().equals("ALLOW"));

            boolean currentDiscoverEntitlement = entitlements
                    .stream()
                    .filter(item -> item.getAuthVersion() == 1)
                    .filter(Objects::nonNull)
                    .anyMatch(item -> item.getEntityId().equalsIgnoreCase("DISCOVER") &&
                            item.getAllowedAction().toUpperCase().contains("G3"));

            boolean discoverCompatibilityEnabled = isDiscoverCompatibilityEnabled() && legacyDiscoverEntitlement;
            boolean newDiscoverEnabled = isNewDiscoverEnabled() && currentDiscoverEntitlement;
            return newDiscoverEnabled || discoverCompatibilityEnabled;
        }
    }

    public List<String> convertRolesFromEntitlements(List<EntitlementV2> entitlements, String clientCode) {
        List<String> roles = new ArrayList<>();
        //Only valid entitlements with the role and at least one auth group or property id are considered
        List<EntitlementV2> entitlementsWithRoleAssignments = entitlements
                .stream()
                .filter(item -> item.getAuthVersion() == 2)
                .filter(item -> StringUtils.isNotEmpty(item.getAuthGroupId()) || CollectionUtils.isNotEmpty(item.getPropertyIds()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(entitlementsWithRoleAssignments)) {
            return roles;
        }
        LOGGER.info("Entitlements with role assignments: " + entitlementsWithRoleAssignments.stream().map(EntitlementV2::toString).collect(Collectors.joining(",")));

        Set<Role> allRolesForClient = roleService.getAllRoles(clientCode);
        if (clientCode.equalsIgnoreCase(INTERNAL_CLIENT_NAME)) {
            //Internal User cases
            allRolesForClient.add(getInternalAllPermissionsRole());
        }
        Map<String, String> uasRoleIdToG3RoleId = allRolesForClient
                .stream()
                .filter(item -> item.getUasRoleDictionaryUuid() != null)
                .filter(item -> item.getRoleName() == null || !item.getRoleName().equalsIgnoreCase("ORG_ADMIN"))
                .collect(Collectors.toMap(Role::getUasRoleDictionaryUuid, Role::getUniqueIdentifier));
        List<EntitlementV2> applicableG3Roles = entitlementsWithRoleAssignments
                .stream()
                .filter(item -> uasRoleIdToG3RoleId.containsKey(item.getRoleId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(applicableG3Roles)) {
            return roles;
        }

        boolean isInternal = clientCode.equalsIgnoreCase(INTERNAL_CLIENT_NAME);
        List<AuthorizationGroup> authGroupsByClientId;
        if (isInternal) {
            //Providing an empty list as auth group is hard coded to -666 in G3 aka All Properties
            authGroupsByClientId = new ArrayList<>();
            authGroupsByClientId.add(getInternalAllPropertiesAuthGroup());
        } else {
            //Adding all properties auth group to list of auth groups as it is a valid auth group
            Client client = clientPropertyCacheService.getClient(clientCode);
            authGroupsByClientId = authorizationService.getAuthGroupsByClientId(client.getId());
            boolean containsAllProperties = authGroupsByClientId
                    .stream()
                    .anyMatch(item -> item.getName() != null && item.getDescription() != null &&
                            item.getName().equalsIgnoreCase("All Properties") && item.getDescription().equals("All available properties"));
            if (!containsAllProperties) {
                AuthorizationGroup allPropertiesAuthGroup = uasService.getAllPropertiesAuthGroupForClient(client.getUpsClientUuid());
                authGroupsByClientId.add(allPropertiesAuthGroup);
            }
        }
        LOGGER.info("Collecting authGroups: " + authGroupsByClientId.stream().map(AuthorizationGroup::toString).collect(Collectors.joining(",")));
        Map<String, Integer> uasIdToG3AuthGroupId = authGroupsByClientId
                .stream()
                .filter(item -> item.getUasAuthGroupUuid() != null)
                .collect(Collectors.toMap(AuthorizationGroup::getUasAuthGroupUuid, authorizationGroup -> authorizationGroup.getId()));

        for (EntitlementV2 entitlement : applicableG3Roles) {
            String roleUUID = entitlement.getRoleId() != null ? entitlement.getRoleId() : "";
            if (isInternal) {
                if (uasRoleIdToG3RoleId.containsKey(roleUUID) && entitlement.getAuthGroupId() != null) {
                    String g3RoleId = uasRoleIdToG3RoleId.get(entitlement.getRoleId());
                    roles.add(createRoleStringForAuthGroupNRole(g3RoleId, -666));
                } else {
                    LOGGER.info("Role not found for roleUUID: " + roleUUID);
                }
            } else {
                if (uasRoleIdToG3RoleId.containsKey(entitlement.getRoleId())) {
                    if (StringUtils.isNotEmpty(entitlement.getAuthGroupId()) && uasIdToG3AuthGroupId.containsKey(entitlement.getAuthGroupId())) {
                        String g3RoleId = uasRoleIdToG3RoleId.get(entitlement.getRoleId());
                        Integer authGroupId = uasIdToG3AuthGroupId.get(entitlement.getAuthGroupId());
                        roles.add(
                                createRoleStringForAuthGroupNRole(
                                        g3RoleId,
                                        authGroupId));
                    }
                    if (CollectionUtils.isNotEmpty(entitlement.getPropertyIds())) {
                        for (String uasPropertyId : entitlement.getPropertyIds()) {
                            if (StringUtils.isNotEmpty(uasPropertyId)) {
                                Integer currentProperty = globalPropertyService.getPropertyByUPSId(uasPropertyId);
                                String g3RoleId = uasRoleIdToG3RoleId.get(entitlement.getRoleId());
                                uasRoleIdToG3RoleId.get(entitlement.getRoleId());
                                if (currentProperty != null && g3RoleId != null) {
                                    roles.add(createRoleStringForPropertyNRole(g3RoleId, currentProperty));
                                } else {
                                    LOGGER.error("Property not found for propertyId: " + uasPropertyId);
                                }
                            } else {
                                LOGGER.error("Property not provided for entitlement: " + entitlement);
                            }
                        }
                    }
                } else {
                    LOGGER.error("Role not found for roleUUID: " + roleUUID + " for entitlement: " + entitlement);
                }
            }
        }
        LOGGER.info("Roles mapped as: " + String.join(",", roles));
        return roles;
    }

    public List<EntitlementV2> pullCurrentEnvironmentRoleEntitlementsFromExistingUser(String userId, String clientUUID) {
        UISUserV2 user = uisService.getUserV2(userId);
        List<EntitlementV2> roles = new ArrayList<>();
        //Only valid entitlements with the role and at least one auth group or property id are considered
        List<EntitlementV2> entitlementsWithRoleAssignments = user.getEntitlements()
                .stream()
                .filter(item -> item.getAuthVersion() == 2)
                .filter(item -> StringUtils.isNotEmpty(item.getAuthGroupId()) || CollectionUtils.isNotEmpty(item.getPropertyIds()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(entitlementsWithRoleAssignments)) {
            return roles;
        }

        Set<Role> allRolesForClient = new HashSet<>();
        if (clientUUID.equals(CLIENT_INTERNAL_UUID)) {
            allRolesForClient = roleService.getAllRoles(CLIENT_INTERNAL);
            allRolesForClient.add(getInternalAllPermissionsRole());
        } else {
            Client client = clientService.findClientByUpsClientUuid(clientUUID);
            if (client.getCode() == null) {
                throw new CognitoException("Client not found for clientUUID: " + clientUUID);
            }
            allRolesForClient = roleService.getAllRoles(client.getCode());
        }
        Set<String> uasRoleIds = allRolesForClient
                .stream()
                .map(Role::getUasRoleDictionaryUuid)
                .collect(Collectors.toSet());
        return entitlementsWithRoleAssignments
                .stream()
                .filter(item -> uasRoleIds.contains(item.getRoleId()))
                .collect(Collectors.toList());
    }

    public Role getInternalAllPermissionsRole() {
        Role allPermissionsRoleForInternal = new Role();
        allPermissionsRoleForInternal.setUasRoleDictionaryUuid(RoleService.FDS_ALL_PERMISSIONS_ROLE_UUID);
        allPermissionsRoleForInternal.setUniqueIdentifier("-666");
        return allPermissionsRoleForInternal;
    }

    public AuthorizationGroup getInternalAllPropertiesAuthGroup() {
        AuthorizationGroup internalAuthGroup = new AuthorizationGroup();
        internalAuthGroup.setUasAuthGroupUuid(AuthorizationGroup.FDS_INTERNAL_ALL_PROPERTIES_AUTH_GROUP_UUID);
        internalAuthGroup.setId(-666);
        return internalAuthGroup;
    }

    //Using G3 ID values to map the LDAP user to G3 roles to property list
    private String createRoleStringForPropertyNRole(String roleId, Integer propertyId) {
        return new StringBuilder().append("roleId=").append(roleId).append(" ON PROP propertyId=").append(propertyId).toString();
    }

    //Using G3 ID values to map LDAP user to G3 roles to auth groups
    private String createRoleStringForAuthGroupNRole(String roleId, Integer authGroupId) {
        return new StringBuilder().append("roleId=").append(roleId).append(" ON GROUP groupId=").append(authGroupId).toString();
    }

    public void processUserDeleteFromFDS(String userId, String code) {
        Optional<LDAPUser> userOptional = getLDAPUserByClientCodeAndUISId(code, userId);
        if (userOptional.isPresent()) {
            LDAPUser user = userOptional.get();
            user.setActive(false);
            update(user.getUserId().toString(), user);
        } else {
            LOGGER.info("User does not exist in the system. Skipping the user deletion");
        }
    }
}
