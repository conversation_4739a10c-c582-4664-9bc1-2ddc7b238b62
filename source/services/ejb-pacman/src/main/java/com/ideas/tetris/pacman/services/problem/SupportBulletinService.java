package com.ideas.tetris.pacman.services.problem;

import com.ideas.tetris.pacman.services.problem.dto.Bulletin;
import com.ideas.tetris.pacman.services.problem.entity.SupportBulletin;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.entity.AbstractCriteria;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class SupportBulletinService {
    @JobCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("jobCrudServiceBean")
	protected CrudService jobCrudService;

    public List<Bulletin> getSupportBulletins(AbstractCriteria<SupportBulletin> criteria) {
        List<SupportBulletin> entities = jobCrudService.findByCriteria(criteria);
        List<Bulletin> results = new ArrayList<Bulletin>();
        for (SupportBulletin entity : entities) {
            Bulletin bulletin = createDTOFromEntity(entity);
            results.add(bulletin);
        }
        return results;

    }

    public Bulletin createSupportBulletin(Bulletin bulletin) {
        SupportBulletin entity = new SupportBulletin();
        String text = bulletin.getText();
        if (text != null && text.length() > SupportBulletin.TEXT_LENGTH) {
            text = text.substring(0, SupportBulletin.TEXT_LENGTH);
        }
        String contactPerson = bulletin.getContactPerson();
        if (contactPerson != null && contactPerson.length() > SupportBulletin.CONTACT_PERSON_LENGTH) {
            contactPerson = contactPerson.substring(0, SupportBulletin.CONTACT_PERSON_LENGTH);
        }
        String estimatedResolutionTime = bulletin.getEstimatedResolutionTime();
        if (estimatedResolutionTime != null && estimatedResolutionTime.length() > SupportBulletin.ESTIMATED_RESOLUTION_TIME_LENGTH) {
            estimatedResolutionTime = estimatedResolutionTime.substring(0, SupportBulletin.ESTIMATED_RESOLUTION_TIME_LENGTH);
        }
        entity.setText(text);
        entity.setContactPerson(contactPerson);
        entity.setEstimatedResolutionTime(estimatedResolutionTime);
        entity.setActive(true);
        entity.setJobName(bulletin.getJobName());
        entity.setStepName(bulletin.getStepName());
        entity.setLastModifiedDate(new Date());
        entity.setLastModifiedBy(PacmanThreadLocalContextHolder.getPrincipal().getDisplayName());
        entity.setAction(bulletin.getAction());

        entity = jobCrudService.save(entity);
        return createDTOFromEntity(entity);
    }

    public Bulletin closeSupportBulletin(Bulletin bulletin) {
        if (bulletin == null || bulletin.getId() == null) {
            return null;
        }
        SupportBulletin entity = jobCrudService.find(SupportBulletin.class, bulletin.getId());
        entity.setActive(false);
        entity.setClosedBy(PacmanThreadLocalContextHolder.getPrincipal().getDisplayName());
        entity.setClosedDate(new Date());
        entity = jobCrudService.save(entity);
        return createDTOFromEntity(entity);
    }

    public Bulletin reopenSupportBulletin(Bulletin bulletin) {
        if (bulletin == null || bulletin.getId() == null) {
            return null;
        }
        SupportBulletin entity = jobCrudService.find(SupportBulletin.class, bulletin.getId());
        entity.setActive(true);
        entity.setClosedBy(null);
        entity.setClosedDate(null);
        entity.setLastModifiedDate(new Date());
        entity.setLastModifiedBy(PacmanThreadLocalContextHolder.getPrincipal().getDisplayName());
        entity = jobCrudService.save(entity);
        return createDTOFromEntity(entity);
    }

    public Bulletin updateSupportBulletin(Bulletin bulletin) {
        if (bulletin == null || bulletin.getId() == null) {
            return null;
        }
        SupportBulletin entity = jobCrudService.find(SupportBulletin.class, bulletin.getId());
        String text = bulletin.getText();
        if (text != null && text.length() > SupportBulletin.TEXT_LENGTH) {
            text = text.substring(0, SupportBulletin.TEXT_LENGTH);
        }
        String contactPerson = bulletin.getContactPerson();
        if (contactPerson != null && contactPerson.length() > SupportBulletin.CONTACT_PERSON_LENGTH) {
            contactPerson = contactPerson.substring(0, SupportBulletin.CONTACT_PERSON_LENGTH);
        }
        String estimatedResolutionTime = bulletin.getEstimatedResolutionTime();
        if (estimatedResolutionTime != null && estimatedResolutionTime.length() > SupportBulletin.ESTIMATED_RESOLUTION_TIME_LENGTH) {
            estimatedResolutionTime = estimatedResolutionTime.substring(0, SupportBulletin.ESTIMATED_RESOLUTION_TIME_LENGTH);
        }
        entity.setText(text);
        entity.setContactPerson(contactPerson);
        entity.setEstimatedResolutionTime(estimatedResolutionTime);
        entity.setJobName(bulletin.getJobName());
        entity.setStepName(bulletin.getStepName());
        entity.setLastModifiedDate(new Date());
        entity.setLastModifiedBy(PacmanThreadLocalContextHolder.getPrincipal().getDisplayName());
        entity.setAction(bulletin.getAction());

        entity = jobCrudService.save(entity);
        return createDTOFromEntity(entity);
    }

    private Bulletin createDTOFromEntity(SupportBulletin entity) {
        return new Bulletin(entity);
    }
}
