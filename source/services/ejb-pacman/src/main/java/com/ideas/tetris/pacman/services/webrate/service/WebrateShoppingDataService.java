package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.centralrms.models.util.Tuple2;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantProperty;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.configautomation.dto.AccomClassDTAMapping;
import com.ideas.tetris.pacman.services.configautomation.dto.ProductWebrateCompetitorDTAMappingRequest;
import com.ideas.tetris.pacman.services.configautomation.dto.WebrateCompetitorDTAMapping;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceStepStateEntity;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.rdl.RDLInformation;
import com.ideas.tetris.pacman.services.rdl.entity.WebrateTypeProduct;
import com.ideas.tetris.pacman.services.rms.webrate.Status;
import com.ideas.tetris.pacman.services.rms.webrate.dto.Channel;
import com.ideas.tetris.pacman.services.rms.webrate.dto.CompetitiveRoomType;
import com.ideas.tetris.pacman.services.rms.webrate.dto.Competitor;
import com.ideas.tetris.pacman.services.rms.webrate.dto.WebratesDto;
import com.ideas.tetris.pacman.services.webrate.dto.CompetitorSettingConfigExcelDto;
import com.ideas.tetris.pacman.services.webrate.dto.CompetitorWebRateConfigDto;
import com.ideas.tetris.pacman.services.webrate.entity.*;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterPredefinedValue;
import com.ideas.tetris.platform.common.configparams.entities.ParameterPredefinedValue;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import io.vavr.Tuple4;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED;
import static com.ideas.tetris.pacman.common.constants.Constants.CONTEXT_KEY;
import static com.ideas.tetris.pacman.common.constants.Constants.PROPERTY_ID;
import static java.util.stream.Collectors.*;

@Component
@Transactional
public class WebrateShoppingDataService {
    private static final Logger LOGGER = Logger.getLogger(WebrateShoppingDataService.class.getName());
    private static final String PROPERTY_OR_COMPETITOR_NOT_FOUND_MSG = "No such competitor with Id %s exist for property %s in system.";
    private static final String PROPERTY_NOT_FOUND_MSG = "No such property with id %s exist in system or not same as set in context";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
	private AlertService alertService;

    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;

    @Autowired
	private WebrateShoppingCleanUpService webrateShoppingCleanUpService;

    @Autowired
    PropertyService propertyService;

    @Autowired
    private DynamicCMPCService dynamicCMPCService;
    @Autowired
    private AgileRatesConfigurationService agileRatesConfigurationService;

    public List<WebrateChannel> getAllChannelsByProperty() {
        Integer propertyId = ((WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY)).getPropertyId();
        return getAllChannelsByProperty(propertyId);
    }

    @SuppressWarnings("unchecked")
    public List<WebrateChannel> getAllChannelsByProperty(Integer propertyId) {
        List<WebrateChannel> channelList = crudService.findByNamedQuery(
                WebrateChannel.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());
        WebrateDefaultChannel defaultChannel = crudService.findByNamedQuerySingleResult(
                WebrateDefaultChannel.BY_PROPERTY_ID_FOR_BAR_ONLY,
                QueryParameter.with("propertyId", propertyId).parameters());
        List<WebrateOverrideChannel> overrideChannelList = crudService
                .findByNamedQuery(WebrateOverrideChannel.BY_PROPERTY_ID_FOR_BAR_ONLY,
                        QueryParameter.with("propertyId", propertyId).parameters());
        if (defaultChannel != null) {
            checkForDefaultChannelPresence(channelList, defaultChannel);
            checkForOverrideChannelPresence(channelList, overrideChannelList, true);
        } else {
            checkForOverrideChannelPresence(channelList, overrideChannelList, false);
        }
        return channelList;
    }

    /* To check the presence of channel in Override Table
     * (non-Javadoc)
     */
    public void checkForOverrideChannelPresence(List<WebrateChannel> channelList,
                                                List<WebrateOverrideChannel> overrideChannelList, boolean defaultPresent) {
        if (defaultPresent) {
            for (WebrateChannel channel : channelList) {
                for (WebrateOverrideChannel overrideChannel : overrideChannelList) {
                    if (channel.getIsUsed().intValue() == 0) {
                        if (channel.getWebrateChannelName()
                                .equalsIgnoreCase(overrideChannel.getWebrateChannelMon().getWebrateChannelName())) {
                            channel.setIsUsed(1);
                        } else if (channel.getWebrateChannelName()
                                .equalsIgnoreCase(overrideChannel.getWebrateChannelTues().getWebrateChannelName())) {
                            channel.setIsUsed(1);
                        } else if (channel.getWebrateChannelName()
                                .equalsIgnoreCase(overrideChannel.getWebrateChannelWed().getWebrateChannelName())) {
                            channel.setIsUsed(1);
                        } else if (channel.getWebrateChannelName()
                                .equalsIgnoreCase(overrideChannel.getWebrateChannelThurs().getWebrateChannelName())) {
                            channel.setIsUsed(1);
                        } else if (channel.getWebrateChannelName()
                                .equalsIgnoreCase(overrideChannel.getWebrateChannelFri().getWebrateChannelName())) {
                            channel.setIsUsed(1);
                        } else if (channel.getWebrateChannelName()
                                .equalsIgnoreCase(overrideChannel.getWebrateChannelSat().getWebrateChannelName())) {
                            channel.setIsUsed(1);
                        } else if (channel.getWebrateChannelName()
                                .equalsIgnoreCase(overrideChannel.getWebrateChannelSun().getWebrateChannelName())) {
                            channel.setIsUsed(1);
                        }
                    }
                }
            }
        } else {
            for (WebrateChannel channel : channelList) {
                for (WebrateOverrideChannel overrideChannel : overrideChannelList) {
                    if (channel.getWebrateChannelName()
                            .equalsIgnoreCase(overrideChannel.getWebrateChannelMon().getWebrateChannelName())) {
                        channel.setIsUsed(1);
                    } else if (channel.getWebrateChannelName()
                            .equalsIgnoreCase(overrideChannel.getWebrateChannelTues().getWebrateChannelName())) {
                        channel.setIsUsed(1);
                    } else if (channel.getWebrateChannelName()
                            .equalsIgnoreCase(overrideChannel.getWebrateChannelWed().getWebrateChannelName())) {
                        channel.setIsUsed(1);
                    } else if (channel.getWebrateChannelName()
                            .equalsIgnoreCase(overrideChannel.getWebrateChannelThurs().getWebrateChannelName())) {
                        channel.setIsUsed(1);
                    } else if (channel.getWebrateChannelName()
                            .equalsIgnoreCase(overrideChannel.getWebrateChannelFri().getWebrateChannelName())) {
                        channel.setIsUsed(1);
                    } else if (channel.getWebrateChannelName()
                            .equalsIgnoreCase(overrideChannel.getWebrateChannelSat().getWebrateChannelName())) {
                        channel.setIsUsed(1);
                    } else if (channel.getWebrateChannelName()
                            .equalsIgnoreCase(overrideChannel.getWebrateChannelSun().getWebrateChannelName())) {
                        channel.setIsUsed(1);
                    } else {
                        channel.setIsUsed(0);
                    }
                }
            }
        }
    }

    /* To check the presence of channel in default Table
     * (non-Javadoc)
     */
    public void checkForDefaultChannelPresence(
            List<WebrateChannel> channelList, WebrateDefaultChannel defaultChannel) {
        for (WebrateChannel channel : channelList) {
            if (channel.getWebrateChannelName()
                    .equalsIgnoreCase(defaultChannel.getWebrateChannelMon().getWebrateChannelName())) {
                channel.setIsUsed(1);
            } else if (channel.getWebrateChannelName()
                    .equalsIgnoreCase(defaultChannel.getWebrateChannelTues().getWebrateChannelName())) {
                channel.setIsUsed(1);
            } else if (channel.getWebrateChannelName()
                    .equalsIgnoreCase(defaultChannel.getWebrateChannelWed().getWebrateChannelName())) {
                channel.setIsUsed(1);
            } else if (channel.getWebrateChannelName()
                    .equalsIgnoreCase(defaultChannel.getWebrateChannelThurs().getWebrateChannelName())) {
                channel.setIsUsed(1);
            } else if (channel.getWebrateChannelName()
                    .equalsIgnoreCase(defaultChannel.getWebrateChannelFri().getWebrateChannelName())) {
                channel.setIsUsed(1);
            } else if (channel.getWebrateChannelName()
                    .equalsIgnoreCase(defaultChannel.getWebrateChannelSat().getWebrateChannelName())) {
                channel.setIsUsed(1);
            } else if (channel.getWebrateChannelName()
                    .equalsIgnoreCase(defaultChannel.getWebrateChannelSun().getWebrateChannelName())) {
                channel.setIsUsed(1);
            } else {
                channel.setIsUsed(0);
            }
        }
    }

    public List<CompetitorSettingConfigExcelDto> getALlCompetitorSettingsForExportToExcel(){
        List<Product> allEligibleProducts = getAllEligibleProducts();
        List<WebrateCompetitors> allCompetitorsByProperty = getAllCompetitorsByProperty();
        return allCompetitorsByProperty.stream()
                .flatMap(competitor -> competitor.getWebrateCompetitorsAccomClasses().stream()
                        .filter(accomClass -> isProductEligible(allEligibleProducts, accomClass.getProductID()))
                        .map(accomClass -> getCompetitorSettingConfigExcelDto(allEligibleProducts, competitor, accomClass))
                ).sorted(Comparator.comparing(CompetitorSettingConfigExcelDto::getProduct))
                .collect(Collectors.toList());
     }
    private boolean isProductEligible(List<Product> allEligibleProducts, Integer productID) {
        return allEligibleProducts.stream()
                .anyMatch(product -> product.getId().equals(productID));
    }
    private CompetitorSettingConfigExcelDto getCompetitorSettingConfigExcelDto(List<Product> allProductsList, WebrateCompetitors competitor, WebrateCompetitorsAccomClass accomClass) {
        CompetitorSettingConfigExcelDto dto = new CompetitorSettingConfigExcelDto();
        dto.setCompetitorName(competitor.getWebrateCompetitorsName());
        dto.setProduct(getProductName(allProductsList, accomClass.getProductID()));
        dto.setDisplayName(competitor.getWebrateCompetitorsAlias());
        dto.setRoomClass(accomClass.getAccomClass().getName());
        dto.setUseWithinDaysOfArrival(accomClass.getDaysToArrival());
        dto.setUseRateShoppingData(accomClass.getDemandEnabled()==1);
        dto.setUseInCompMktPositionConstraints(accomClass.getRankingEnabled() == 1);
        return dto;
    }

    private String getProductName(List<Product> allProductsList, Integer productID) {
        if (productID == null) {
            return "";
        }
        return allProductsList.stream()
                .filter(product -> product.getId().equals(productID))
                .map(Product::getName)
                .findFirst()
                .orElse("");
    }
    protected List<Product> getAllEligibleProducts() {
        List<Product> allProductsList = agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts();
        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(INDEPENDENT_PRODUCTS_ENABLED);
        boolean isRDLEnabled= configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);

        List<Product> allProducts;
        if (isIndependentProductsEnabled && isRDLEnabled) {
            allProducts = allProductsList;
        } else if (isIndependentProductsEnabled) {
            allProducts = getProducts(allProductsList, product -> product.isSystemDefault() || product.isIndependentProduct());

        } else if (isRDLEnabled) {
            allProducts = getProducts(allProductsList, product -> product.isSystemDefault() || product.isAgileRatesProduct());
        } else {
            allProducts = getProducts(allProductsList, product -> product.isSystemDefault());
        }
        allProducts.sort(Comparator.comparing(Product::getDisplayOrder));

        return allProducts;
    }
    private static List<Product> getProducts(List<Product> allProductsList, Predicate<Product> productPredicate) {
        return allProductsList.stream()
                .filter(productPredicate)
                .collect(Collectors.toList());
    }
    public List<WebrateCompetitors> getAllCompetitorsByProperty() {
        Integer propertyId = ((WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY)).getPropertyId();
        return getAllCompetitorsByProperty(propertyId);
    }

    @SuppressWarnings("unchecked")
    public List<WebrateCompetitors> getAllCompetitorsByProperty(Integer propertyId) {
        List<WebrateCompetitors> competitorsList = crudService
                .findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID,
                        QueryParameter.with("propertyId", propertyId).parameters());
        return getAllCompetitors(propertyId, competitorsList);
    }

    public void setUPSIdForCompetitors(int propertyId, int webRateHotelId, RDLInformation rdlInformation) {
        setUPSIdForCompetitor(propertyId, String.valueOf(webRateHotelId), rdlInformation.getUnifiedPropertyId());
    }

    public void setUPSIdForCompetitor(int propertyId, String webRateHotelId, String unifiedPropertyId) {
        TenantProperty tenantProperty = crudService.findByNamedQuerySingleResult(TenantProperty.GET_BY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        if (null == tenantProperty) {
            throw new TetrisException(ErrorCode.PROPERTY_NOT_FOUND, String.format(PROPERTY_NOT_FOUND_MSG, propertyId), null);
        }
        WebrateCompetitors webrateCompetitors = crudService.findByNamedQuerySingleResult(
                WebrateCompetitors.IDS_PROPERTY_ID_AND_WRHOTEL_ID, QueryParameter.with(PROPERTY_ID, propertyId)
                        .and("webrateHotelID", webRateHotelId).parameters());
        if (null == webrateCompetitors) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, String.format(PROPERTY_OR_COMPETITOR_NOT_FOUND_MSG, webRateHotelId, propertyId), null);
        }
        webrateCompetitors.setUpsId(unifiedPropertyId);
        crudService.save(webrateCompetitors);
    }


    public List<WebrateCompetitors> getAllActiveCompetitorsByProperty() {
        Integer propertyId = ((WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY)).getPropertyId();
        return getAllActiveCompetitorsByProperty(propertyId);
    }

    @SuppressWarnings("unchecked")
    public List<WebrateCompetitors> getAllActiveCompetitorsByProperty(Integer propertyId) {
        List<WebrateCompetitors> competitorsList = crudService
                .findByNamedQuery(WebrateCompetitors.BY_PROPERTYID_AND_STATUS,
                        QueryParameter.with("propertyId", propertyId).and("statusId", 1).parameters());
        return getAllCompetitors(propertyId, competitorsList);
    }


    public List<WebrateCompetitors> getAllActiveAndNewCompetitorsByProperty() {
        Integer propertyId = ((WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY)).getPropertyId();
        return getAllActiveAndNewCompetitorsByProperty(propertyId);
    }

    @SuppressWarnings("unchecked")
    public List<WebrateCompetitors> getAllActiveAndNewCompetitorsByProperty(Integer propertyId) {
        List<WebrateCompetitors> competitorsList = crudService.
                findByNamedQuery(WebrateCompetitors.BY_PROPERTY_ID_AND_STATUS_NOT,
                        QueryParameter.with(WebrateCompetitors.PARAM_PROP_ID, propertyId).and(WebrateCompetitors.PARAM_STATUS_ID, 2).parameters());
        return getAllCompetitors(propertyId, competitorsList);
    }


    @SuppressWarnings("unchecked")
	public
    List<WebrateCompetitors> getAllCompetitors(Integer propertyId,
                                               List<WebrateCompetitors> competitorsList) {
        String specificCompetitorValue = getSpecificCompetitorValue();
        String webRateHotelId = configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value());
        List<WebrateCompetitors> competitorListInOverride = crudService
                .findByNamedQuery(WebrateOverrideCompetitorDetails.COMP_ID_BY_PROPERTY_ID,
                        QueryParameter.with("propertyId", propertyId).parameters());
        for (WebrateCompetitors webrateCompetitor : competitorsList) {
            if (webrateCompetitor.getWebrateCompetitorsAccomClasses().isEmpty()) {
                List<AccomClass> accomClassList = crudService.findByNamedQuery(
                        WebrateAccomClassMapping.BY_PROPERTY_ID,
                        QueryParameter.with("propertyId", propertyId).parameters());
                webrateCompetitor.setAccomClassList(accomClassList);
            }
        }
        Property property = propertyService.getPropertyById(propertyId);
        for (WebrateCompetitors webrateCompetitor : competitorsList) {
            for (WebrateCompetitors overrideCompetitor : competitorListInOverride) {
                if (overrideCompetitor.getWebrateCompetitorsName()
                        .equalsIgnoreCase(webrateCompetitor.getWebrateCompetitorsName())) {
                    webrateCompetitor.setIsUsed(1);
                } else {
                    if (webrateCompetitor.getIsUsed() == null || webrateCompetitor.getIsUsed().intValue() != 1) {
                        webrateCompetitor.setIsUsed(0);
                    }
                }
            }
            //To Check for the self Competitor
            try {
                if ((webRateHotelId != null && webRateHotelId.equalsIgnoreCase(webrateCompetitor.getWebrateHotelID())) || isRDLSelfProperty(webrateCompetitor, property)) {

                    webrateCompetitor.setIsSelfCompetitor(1);
                    Iterator<WebrateCompetitorsAccomClass> itr = webrateCompetitor.getWebrateCompetitorsAccomClasses().iterator();
                    while (itr.hasNext()) {
                        itr.next().setIsSelfCompetitor(1);

                    }

                }
            } catch (NumberFormatException e) {
                // This will occur if WebRate Hotel Id is not properly set either in parameter_value table or in
                // web rate competitors table
            }

            //to check if it used as specific competitor
            if (null != specificCompetitorValue && webrateCompetitor.isusedAsSpecificCompetitor(specificCompetitorValue)) {
                webrateCompetitor.setIsUsedAsSpecificCompetitor(1);
            } else {
                webrateCompetitor.setIsUsedAsSpecificCompetitor(0);
            }
        }
        return competitorsList;
    }

    private boolean isRDLSelfProperty(WebrateCompetitors webrateCompetitor, Property property) {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED) &&
                StringUtils.isNotEmpty(property.getUpsId()) && property.getUpsId().equals(webrateCompetitor.getUpsId());
    }

    public String getSpecificCompetitorValue() {
        String context = Constants.CONFIG_PARAMS_NODE_PREFIX + "." + PacmanWorkContextHelper.getClientCode() + "." + PacmanWorkContextHelper.getPropertyCode();
        String displayCompetitor = configParamsService.getValue(context, IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value());
        if (null != displayCompetitor && displayCompetitor.equals(Constants.BAR_OVRD_DISPLAY_COMPETITOR_VALUE_ABSOLUTE)) {
            return configParamsService.getValue(context, IPConfigParamName.BAR_BAR_OVRD_ABSOLUTE_COMPETITOR.value());
        }
        return null;
    }

    public boolean saveWebrateShoppingData(List<WebrateCompetitors> competitorsList,
                                           List<WebrateChannel> webrateChannelList) {
        if (!updateWebRateCompetitors(competitorsList)) {
            return false;
        }
        return updateWebrateChannels(webrateChannelList);
    }

    private boolean updateWebRateCompetitors(
            List<WebrateCompetitors> competitorsList) {
        boolean extendedStayCompetitorsEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED.value());
        if (competitorsList != null) {

            boolean invalidWebrateCompetitor = competitorsList.stream()
                    .anyMatch(webrateCompetitors -> webrateCompetitors.getId() == null);

            if (invalidWebrateCompetitor) {
                return false;
            }

            boolean syncRequired = competitorsList.stream()
                    .anyMatch(webrateCompetitors -> isSyncRequired(webrateCompetitors));

            competitorsList.stream()
                    .filter(webrateCompetitors -> !webrateCompetitors.getShouldDelete())
                    .peek(this::setDefaultWebrateCompetitorsAlias)
                    .forEach(crudService::save);

            List<WebrateCompetitors> toBeDeleted = competitorsList.stream()
                    .filter(WebrateCompetitors::getShouldDelete)
                    .collect(toList());

            webrateShoppingCleanUpService.cleanUpWebrateCompetitors(toBeDeleted);

            if (!extendedStayCompetitorsEnabled) {
                updateAllNewStatusToActive(WebrateCompetitors.UPDATE_ALL_NEW_TO_ACTIVE_BY_PROPERTY);
            }
            if (syncRequired) {
                webrateShoppingCleanUpService.clearAndRegisterSyncEvent();
            }
        }
        if (!extendedStayCompetitorsEnabled) {
            resolveAlert(AlertType.NewWebRateCompetitorFound);
            resolveAlert(AlertType.NewRDLCompetitorFound);
        }
        return true;
    }

    public List<Product> findAllProducts() {
        return crudService.findByNamedQuery(Product.GET_ALL);
    }

    private void setDefaultWebrateCompetitorsAlias(WebrateCompetitors webrateCompetitor) {
        if (StringUtils.isEmpty(webrateCompetitor.getWebrateCompetitorsAlias())) {
            webrateCompetitor.setWebrateCompetitorsAlias("Display Name");
        }
    }

    private void setDefaultWebrateChannelAlias(WebrateChannel webrateChannel) {
        if (webrateChannel.getWebrateChannelAlias() == null
                || webrateChannel.getWebrateChannelAlias().isEmpty()) {
            webrateChannel.setWebrateChannelAlias("Display Name");
        }
    }

    public boolean updateWebrateChannels(List<WebrateChannel> webrateChannelList) {
        if (webrateChannelList != null) {
            boolean invalidWebrateCompetitor = webrateChannelList.stream()
                    .anyMatch(wc -> wc.getId() == null);

            if (invalidWebrateCompetitor) {
                return false;
            }

            webrateChannelList.stream()
                    .filter(wc -> !wc.getShouldDelete())
                    .peek(this::setDefaultWebrateChannelAlias)
                    .forEach(crudService::save);

            List<WebrateChannel> toBeDeleted = webrateChannelList.stream()
                    .filter(WebrateChannel::getShouldDelete)
                    .collect(toList());

            webrateShoppingCleanUpService.cleanUpWebrateChannels(toBeDeleted);
        }
        updateAllNewStatusToActive(WebrateChannel.UPDATE_ALL_NEW_TO_ACTIVE_BY_PROPERTY);

        resolveAlert(AlertType.NewWebRateChannelFound);
        return true;
    }

    public void updateAllNewStatusToActive(String queryString) {
        crudService.getEntityManager().createNamedQuery(queryString).setParameter("propertyId", PacmanWorkContextHelper.getPropertyId()).executeUpdate();
    }

    private void resolveAlert(AlertType alertType) {
        try {
            alertService.resolveAllAlerts(alertType, PacmanWorkContextHelper.getPropertyId());
        } catch (Exception e) {
            LOGGER.error(" ERROR - UNABLE TO RESOLVE ALERT - " + e.getMessage(), e);
        }
    }

    public boolean isSyncRequired(WebrateCompetitors webrateCompetitor) {
        boolean isWebrateCompetitorNew = webrateCompetitor.getId() == null;
        boolean isWebrateCompetitorActive = toBoolean(webrateCompetitor.getStatusId());

        // If the WebrateCompetitor is deleted
        if (webrateCompetitor.getShouldDelete()) {
            return true;
        }

        // If the WebrateCompetitor is new
        if (isWebrateCompetitorNew) {

            // If it's not active, a sync isn't required
            if (!isWebrateCompetitorActive) {
                return false;
            }

            // Now that we know it's active, time to see if demand or competitive market is enabled
            // If it is, a sync is required
            return hasDemandEnabledOrCompetitiveMarketEnabled(webrateCompetitor);
            // No other cases exist for a new WebrateCompetitor, so no sync is required

        }

        // Since it's not new, look up the previously saved version.
        WebrateCompetitors existingWebrateCompetitor = crudService.find(WebrateCompetitors.class, webrateCompetitor.getId());

        // If the status changed and there are accomclasses mapped that influence demand and market,
        // turn on the sync flag.
        if (!webrateCompetitor.getStatusId().equals(existingWebrateCompetitor.getStatusId())) {
            return true;
        }

        // If the WebrateCompetitor is active, check to see if any of the influencing of demand or market enabled
        // flags are different.  If they are, turn on the sync
        if (isWebrateCompetitorActive) {

            // For each of the WebrateCompetitorsAccomClass, check the following criteria
            // 1.  If new and influences demand or competitive market, sync is required
            // 2.  If existing and demand or competitive market enabled are different, a sync is required
            for (WebrateCompetitorsAccomClass webrateCompetitorsAccomClass : webrateCompetitor.getWebrateCompetitorsAccomClasses()) {

                // Derive boolean values for use in demand model and competitive market
                Integer demandEnabledValue = webrateCompetitorsAccomClass.getDemandEnabled();
                Integer rankingEnabledValue = webrateCompetitorsAccomClass.getRankingEnabled();

                boolean demandEnabled = toBoolean(demandEnabledValue);
                boolean competitiveMarket = toBoolean(rankingEnabledValue);

                // If the WebrateCompetitorAccomClass is new
                if (webrateCompetitorsAccomClass.getId() == null) {

                    // IF demandEnabled or competitiveMarketEnabled, a sync is required
                    if (demandEnabled || competitiveMarket) {
                        return true;
                    }

                    // Since it's new and it's not demand or competitiveMarket enabled, no sync is required
                    continue;
                }

                // Look up existing WebrateCompetitorsAccomClass to see if any values are different
                WebrateCompetitorsAccomClass existingWebrateCompetitorsAccomClass = crudService.find(WebrateCompetitorsAccomClass.class, webrateCompetitorsAccomClass.getId());

                // Check demand model has changed, if so a sync is required
                if (null != existingWebrateCompetitorsAccomClass) {
                    if (null != demandEnabledValue && !demandEnabledValue.equals(existingWebrateCompetitorsAccomClass.getDemandEnabled())) {
                        return true;
                    }

                    // Check demand model has changed, if so a sync is required
                    if (null != rankingEnabledValue && !rankingEnabledValue.equals(existingWebrateCompetitorsAccomClass.getRankingEnabled())) {
                        return true;
                    }
                }
            }
        }

        // None of the sync required criteria were found, a sync is not required
        return false;
    }

    public boolean hasDemandEnabledOrCompetitiveMarketEnabled(WebrateCompetitors webrateCompetitor) {
        Set<WebrateCompetitorsAccomClass> webrateCompetitorsAccomClasses = webrateCompetitor.getWebrateCompetitorsAccomClasses();
        if (webrateCompetitorsAccomClasses != null) {

            for (WebrateCompetitorsAccomClass webrateCompetitorsAccomClass : webrateCompetitorsAccomClasses) {

                // If demandEnabled, return true
                boolean demandEnabled = toBoolean(webrateCompetitorsAccomClass.getDemandEnabled());
                if (demandEnabled) {
                    return true;
                }

                // If competitiveMarketEnabled, return true
                boolean competitiveMarketEnabled = toBoolean(webrateCompetitorsAccomClass.getRankingEnabled());
                if (competitiveMarketEnabled) {
                    return true;
                }
            }
        }

        return false;
    }

    public boolean toBoolean(Integer integerValue) {
        return integerValue != null && integerValue.intValue() == 1;
    }

    /**
     * This method will return the List of All active competitors for room Class.
     *
     * @return {@list @link WebrateCompetitors}
     */
    @SuppressWarnings("unchecked")
    public List<WebrateCompetitors> getAllCompetitorsByRoomClass(List<Integer> listRoomClassIds) {
        return crudService.findByNamedQuery(WebrateCompetitorsAccomClass.COMP_BY_ACCOM_ID_AND_PROPERTY,
                QueryParameter.with("accomId", listRoomClassIds)
                        .and("propertyId", PacmanThreadLocalContextHolder.getWorkContext().getPropertyId())
                        .parameters());
    }

    public List<WebrateCompetitors> getAllCompetitorsByRoomClassForDecisionPaceReport(List<Integer> listRoomClassIds) {
        return crudService.findByNamedQuery(WebrateCompetitorsAccomClass.COMP_BY_ACCOM_ID_AND_PROPERTY_FOR_DECISION_PACE_REPORT,
                QueryParameter.with("accomId", listRoomClassIds)
                        .and("propertyId", PacmanThreadLocalContextHolder.getWorkContext().getPropertyId())
                        .parameters());
    }

    public Set<ParameterPredefinedValue> listCompetitionDisplayChoices() {
        Set<ConfigParameterPredefinedValue> configParameterPredefinedValues = configParamsService.getParameter(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value()).getConfigParameterPredefinedValueType().getConfigParameterPredefinedValues();
        LinkedHashSet<ParameterPredefinedValue> parameterPredefinedValues = new LinkedHashSet<>();
        for (ConfigParameterPredefinedValue configParameterPredefinedValue : configParameterPredefinedValues) {
            parameterPredefinedValues.add(configParameterPredefinedValue.toParameterPredefinedValue());
        }
        return parameterPredefinedValues;
    }


    public CompetitionDisplayComposite getCompetitionDisplay() {
        CompetitionDisplayComposite competitionDisplay = new CompetitionDisplayComposite();

        // Get competition display
        Set<ParameterPredefinedValue> choices = listCompetitionDisplayChoices();
        String currentChoice = configParamsService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value());
        Iterator<ParameterPredefinedValue> iterator = choices.iterator();
        ParameterPredefinedValue predefinedValue;
        while (iterator.hasNext()) {
            predefinedValue = iterator.next();
            if (predefinedValue.getValue().equalsIgnoreCase(currentChoice)) {
                competitionDisplay.setCompetitionChoice(predefinedValue);
                break;
            }
        }

        // Get channel for absolute


        List<WebrateCompetitors> compList = getAllCompetitorsByProperty();

        currentChoice = configParamsService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_ABSOLUTE_COMPETITOR.value());
        // The parameter UI does not force user to a WebrateChannel object like "barOvrdDisplayCompetitor"
        // so the user can enter a random string that does not match one of our choices
        if (null != currentChoice) {
            WebrateCompetitors defaultCompetitor = new WebrateCompetitors();
            defaultCompetitor.setWebrateCompetitorsName(currentChoice);
            competitionDisplay.setAbsoluteCompetitors(defaultCompetitor);
        }

        for (WebrateCompetitors competitor : compList) {
            if (competitor.getWebrateCompetitorsAlias().equalsIgnoreCase(currentChoice)) {
                competitionDisplay.setAbsoluteCompetitors(competitor);
                break;
            }
        }

        // Determine if exists at property level or if we defaulted from app or client
        competitionDisplay.setDoesExistAtPropertyLevel(
                null != configParamsService.getValue(getCurrentConfigParamsContext(), IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value(), true));

        return competitionDisplay;
    }


    public CompetitionDisplayComposite updateCompetitionDisplay(CompetitionDisplayComposite display) {
        String currentConfigParamsContext = getCurrentConfigParamsContext();

        // Update "barOvrdDisplayCompetitor"
        String predefinedValue = configParamsService.getParameterPredefinedValue(display.getCompetitionChoice().getId()).getValue();
        configParamsService.updateParameterValue(currentConfigParamsContext, IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value(), predefinedValue);

        // Update "barOvrdAbsoluteCompetitor" - sets a value, not predefined value or webrate channel

        // As per discussion with PO, replacing WebrateChannel with WebrateCompetitors.
        if (null != display.getAbsoluteCompetitors()) {
            configParamsService.updateParameterValue(currentConfigParamsContext, IPConfigParamName.BAR_BAR_OVRD_ABSOLUTE_COMPETITOR.value(), display.getAbsoluteCompetitors().getWebrateCompetitorsAlias());
        }

        return display;
    }


    public CompetitionDisplayComposite deleteCompetitionDisplay() {
        // Delete "barOvrdDisplayCompetitor"
        configParamsService.deleteParameterValue(getCurrentConfigParamsContext(), IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value(), true);

        // Delete "barOvrdAbsoluteCompetitor"
        configParamsService.deleteParameterValue(getCurrentConfigParamsContext(), IPConfigParamName.BAR_BAR_OVRD_ABSOLUTE_COMPETITOR.value(), true);

        return getCompetitionDisplay();
    }

    private String getCurrentConfigParamsContext() {
        String clientCode = ((WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY)).getClientCode();
        String propertyCode = ((WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY)).getPropertyCode();

        return "pacman." + clientCode + "." + propertyCode;
    }

    public void setConfigParamsService(PacmanConfigParamsService configParamsService) {
        this.configParamsService = configParamsService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setAlertService(AlertService alertService) {
        this.alertService = alertService;
    }

    public Integer getForecastWindowOffsetBDE() {
        String value = configParamsService.getParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());
        if (StringUtils.isNotBlank(value) && StringUtils.isNumeric(value)) {
            return new Integer(value);
        }
        LOGGER.warn("The Config Param has not been configured correctly " + IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());
        return 0;
    }

    public void actionAlertSteps(AlertType alertType, Integer alertId) {
        String queryString = "";
        if (alertType.equals(AlertType.NewWebRateChannelFound)) {
            queryString = WebrateChannel.UPDATE_ALL_NEW_TO_ACTIVE_BY_PROPERTY;

        }
        boolean extendedStayEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED.value());
        if (extendedStayEnabled) {
            checkAndResolveNewCompetitorAlert(AlertType.NewWebRateCompetitorFound);
        } else {
            resolveNewWebRateCompetitorAlert(alertType, alertId, queryString);
        }
    }

    private void resolveNewWebRateCompetitorAlert(AlertType alertType, Integer alertId, String queryString) {
        if (alertType.equals(AlertType.NewWebRateCompetitorFound)) {
            queryString = WebrateCompetitors.UPDATE_ALL_NEW_TO_ACTIVE_BY_PROPERTY;
        }
        updateAllNewStatusToActive(queryString);
        alertService.resolveAlert(alertId, PacmanWorkContextHelper.getPropertyId());
    }

    public boolean shouldResolveNewCompetitorAlert() {
        List actionedStates = multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), InfoMgrInstanceStepStateEntity.ACTIONED_STATES_BY_ALERT,
                QueryParameter.with("alertName", AlertType.NewWebRateCompetitorFound.getName()).and("actioned", true).parameters());
        return actionedStates.size() == 2;
    }


    public void checkAndResolveNewCompetitorAlert(AlertType alertType) {
        String queryString = null;
        boolean extendedStayEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED.value());
        if (extendedStayEnabled && shouldResolveNewCompetitorAlert()) {

            if (alertType.equals(AlertType.NewWebRateCompetitorFound)) {
                queryString = WebrateCompetitors.UPDATE_ALL_NEW_TO_ACTIVE_BY_PROPERTY;
            }
            updateAllNewStatusToActive(queryString);
            alertService.resolveAllAlerts(alertType, PacmanWorkContextHelper.getPropertyId());

        }
    }

    public boolean competitiveMarketPositionConstraintsConfigured(Integer productId) {
        String query = "select \n" +
                "( select COUNT(*) as override_count from Webrate_Ranking_AC_OVR " +
                "WHERE Product_ID = :productId) \n" +
                "+ \n" +
                "( select COUNT(*) as override_count from Webrate_Ranking_Accom_Class\n" +
                "\twhere (Monday_Webrate_Ranking_ID <> 1 \n" +
                "\tOR Tuesday_Webrate_Ranking_ID <> 1\n" +
                "\tOR Wednesday_Webrate_Ranking_ID <> 1\n" +
                "\tOR Thursday_Webrate_Ranking_ID <> 1\n" +
                "\tOR Friday_Webrate_Ranking_ID <> 1\n" +
                "\tOR Saturday_Webrate_Ranking_ID <> 1\n" +
                "\tOR Sunday_Webrate_Ranking_ID <> 1)\n" +
                "\tAND Product_ID = :productId\n" +
                ")";
        int standardCMPCCount = crudService.findByNativeQuerySingleResult(query, QueryParameter.with("productId", productId).parameters());
        long dynamicCMPCCount = dynamicCMPCService.getCountOfDcmpcCfgs(List.of(productId));
        return standardCMPCCount + dynamicCMPCCount > 0;
    }

    public boolean competitiveMarketPositionConstraintsConfiguredForProductIDs(List<Integer> productIds) {
        String query = "select \n" +
                "( select COUNT(*) as override_count from Webrate_Ranking_AC_OVR " +
                "WHERE Product_ID in (:productIds)) \n" +
                "+ \n" +
                "( select COUNT(*) as override_count from Webrate_Ranking_Accom_Class\n" +
                "\twhere (Monday_Webrate_Ranking_ID <> 1 \n" +
                "\tOR Tuesday_Webrate_Ranking_ID <> 1\n" +
                "\tOR Wednesday_Webrate_Ranking_ID <> 1\n" +
                "\tOR Thursday_Webrate_Ranking_ID <> 1\n" +
                "\tOR Friday_Webrate_Ranking_ID <> 1\n" +
                "\tOR Saturday_Webrate_Ranking_ID <> 1\n" +
                "\tOR Sunday_Webrate_Ranking_ID <> 1)\n" +
                "\tAND Product_ID in (:productIds)\n" +
                ")";
        int standardCMPCCount = crudService.findByNativeQuerySingleResult(query, QueryParameter.with("productIds", productIds).parameters());
        long dynamicCMPCCount = dynamicCMPCService.getCountOfDcmpcCfgs(productIds);
        return standardCMPCCount + dynamicCMPCCount > 0;
    }

    public boolean isWebrateCleanupJobRunningOrWebrateSourceUpdateJobRunning(JobName jobName) {
        return webrateShoppingCleanUpService.isWebrateCleanupJobRunning(jobName) || webrateShoppingCleanUpService.isWebrateCleanupJobRunning(JobName.WebrateSourceUpdateJob);
    }

    public List<WebrateOverrideCompetitorDetails> getAllWebrateOverrideCompetitorDetails() {
        return crudService.findAll(WebrateOverrideCompetitorDetails.class);
    }

    public WebrateOverrideCompetitorDetails getWebrateOverrideCompetitorDetails(Integer id) {
        return crudService.find(WebrateOverrideCompetitorDetails.class, id);
    }

    public WebrateCompetitors saveCompetitors(String propertyName, int propertyId, String hotelId, String upsId) {
        WebrateCompetitors webrateCompetitors = new WebrateCompetitors();
        webrateCompetitors.setUpsId(upsId);
        webrateCompetitors.setWebrateCompetitorsAlias(propertyName);
        webrateCompetitors.setWebrateCompetitorsName(propertyName);
        webrateCompetitors.setWebrateCompetitorsDescription(propertyName);
        webrateCompetitors.setPropertyId(propertyId);
        webrateCompetitors.setWebrateHotelID(hotelId);
        webrateCompetitors.setStatusId(TenantStatusEnum.ACTIVE.getId());
        return crudService.save(webrateCompetitors);
    }

    public List<CompetitorWebRateConfigDto> getCompetitorWebrateShopConfig() {
        final List<Object[]> byNamedQuery = crudService.findByNamedQuery(WebrateCompetitors.GET_COMPETITOR_CONFIGURATIONS);
        final var result = byNamedQuery
                .stream().map(o -> new Tuple4<>((String) o[0], (String) o[1], (String) o[2], (String) o[3]))
                .collect(toList());
        final var upsIdByCompName = result.stream().map(t -> Tuple2.of(t._1(), t._2())).distinct()
                .collect(toMap(Tuple2::getT1, Tuple2::getT2));
        return result.stream()
                .collect(groupingBy(Tuple4::_1, groupingBy(Tuple4::_4, mapping(t -> t._3, toList()))))
                .entrySet().stream().flatMap(e -> e.getValue().entrySet().stream()
                        .map(e1 -> new CompetitorWebRateConfigDto(e.getKey(), upsIdByCompName.get(e.getKey())
                                , e1.getKey(), e1.getValue())))
                .collect(toList());
    }

    public void deleteWebrateChannels(List<String> webrateChannels, boolean deleteAll) {
        List<WebrateChannel> toBeDeleted = new ArrayList<>();
        if (deleteAll) {
            toBeDeleted = crudService.findAll(WebrateChannel.class);
        } else {
            Set<String> trimmedWebrateChannels = webrateChannels.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(trimmedWebrateChannels)) {
                toBeDeleted = crudService.findByNamedQuery(WebrateChannel.BY_FILE_CHANNEL_IDS,
                        QueryParameter.with(WebrateChannel.PARAM_FILE_CHANNEL_IDS, trimmedWebrateChannels)
                                .parameters());
            }
        }
        webrateShoppingCleanUpService.cleanUpWebrateChannels(toBeDeleted);
    }

    public void deleteWebrateCompetitors(List<String> webrateCompetitorUpsIds, boolean deleteAll) {
        List<WebrateCompetitors> toBeDeleted = new ArrayList<>();
        if (deleteAll) {
            toBeDeleted = crudService.findAll(WebrateCompetitors.class);
        } else {
            Set<String> trimmedWebrateCompetitorUpsIds = webrateCompetitorUpsIds.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(trimmedWebrateCompetitorUpsIds)) {
                toBeDeleted = crudService.findByNamedQuery(WebrateCompetitors.BY_UPS_IDS,
                        QueryParameter.with(WebrateCompetitors.PARAM_UPS_IDS, trimmedWebrateCompetitorUpsIds).parameters());
            }
        }
        webrateShoppingCleanUpService.cleanUpWebrateCompetitors(toBeDeleted);
    }

    public void updateDTAForCompetitorAccomClass(List<ProductWebrateCompetitorDTAMappingRequest> requestList) {
        List<WebrateCompetitors> allCompetitorsByProperty = getAllCompetitorsByProperty();

        List<WebrateCompetitorDTAMapping> existingMappingForProperty = new ArrayList<>();

        allCompetitorsByProperty.forEach(compName -> {
            String webrateCompetitorsName = compName.getWebrateCompetitorsName();
            Set<AccomClassDTAMapping> accomClassDTAMappings = compName.getWebrateCompetitorsAccomClasses().stream().map(accomClass -> {
                AccomClassDTAMapping accomClassDTAMapping = new AccomClassDTAMapping();
                accomClassDTAMapping.setAccomClassCode(accomClass.getAccomClass().getCode());
                return accomClassDTAMapping;
            }).collect(toSet());
            WebrateCompetitorDTAMapping request = new WebrateCompetitorDTAMapping();
            request.setAccomClassCodeDTAMappings(accomClassDTAMappings);
            request.setWebrateCompetitorName(webrateCompetitorsName);
            existingMappingForProperty.add(request);
        });

        doValidateRequest(requestList, existingMappingForProperty);

        for (ProductWebrateCompetitorDTAMappingRequest req : requestList) {
            for (WebrateCompetitorDTAMapping mapping : req.getCompetitorDTAMappings()) {
                Optional<WebrateCompetitors> optional = allCompetitorsByProperty.stream().filter(comp -> comp.getWebrateCompetitorsName().equalsIgnoreCase(mapping.getWebrateCompetitorName())).findFirst();
                if (optional.isPresent()) {
                    WebrateCompetitors webrateCompetitor = optional.get();
                    webrateCompetitor.getWebrateCompetitorsAccomClasses().forEach(compAccomClass -> {
                        for (AccomClassDTAMapping accomClassDTAMapping : mapping.getAccomClassCodeDTAMappings()) {
                            if (compAccomClass.getAccomClass().getCode().equals(accomClassDTAMapping.getAccomClassCode())
                                    && req.getProductName().equalsIgnoreCase(crudService.find(Product.class, compAccomClass.getProductID()).getName())) {
                                compAccomClass.setDaysToArrival(accomClassDTAMapping.getDaysToArrival());
                            }
                        }
                    });
                }
            }
        }
        crudService.save(allCompetitorsByProperty);
    }

    private void doValidateRequest(List<ProductWebrateCompetitorDTAMappingRequest> requestMapping, List<WebrateCompetitorDTAMapping> existingMappingForProperty) {

        Set<String> productNames = new HashSet<>();
        List<ProductWebrateCompetitorDTAMappingRequest> duplicateProducts = requestMapping.stream().filter(request -> !productNames.add(request.getProductName()))
                .collect(toList());
        if (!duplicateProducts.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            duplicateProducts.forEach(comp -> sb.append(comp.getProductName()).append(" "));
            throw new TetrisException("Duplicate Products names found in request " + sb);
        }

        Set<String> existingProductNames = crudService.findAll(WebrateTypeProduct.class).stream().map(p -> p.getProduct().getName()).collect(Collectors.toSet());
        Product product = crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        existingProductNames.add(product.getName());

        if (!existingProductNames.containsAll(requestMapping.stream().map(ProductWebrateCompetitorDTAMappingRequest::getProductName).collect(Collectors.toSet()))) {
            StringBuilder sb = new StringBuilder();
            existingProductNames.forEach(productName -> sb.append(productName).append(" "));
            throw new TetrisException("Invalid Product Names found in request. Valid Product Names are " + sb);
        }

        Set<WebrateCompetitorDTAMapping> requestList;
        for (ProductWebrateCompetitorDTAMappingRequest req : requestMapping) {
            requestList = req.getCompetitorDTAMappings();
            Set<String> elements = new HashSet<>();
            List<WebrateCompetitorDTAMapping> duplicateCompetitors = requestList.stream().filter(request -> !elements.add(request.getWebrateCompetitorName()))
                    .collect(toList());
            if (!duplicateCompetitors.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                duplicateCompetitors.forEach(comp -> sb.append(comp.getWebrateCompetitorName()).append(" "));
                throw new TetrisException("Duplicate competitor names found in request " + sb + " For Product " + req.getProductName());
            }

            List<AccomClassDTAMapping> diplicateAccomCodes;
            for (WebrateCompetitorDTAMapping request : requestList) {
                Set<String> accomClasses = new HashSet<>();
                diplicateAccomCodes = request.getAccomClassCodeDTAMappings().stream().filter(record -> !accomClasses.add(record.getAccomClassCode()))
                        .collect(toList());
                if (!diplicateAccomCodes.isEmpty()) {
                    StringBuilder sb = new StringBuilder();
                    diplicateAccomCodes.forEach(comp -> sb.append(comp.getAccomClassCode()).append(" "));
                    throw new TetrisException("Duplicate Accom codes " + sb + " for Competitor " + request.getWebrateCompetitorName() + " For Product " + req.getProductName());
                }

                WebrateCompetitorDTAMapping match = existingMappingForProperty.stream()
                        .filter(current -> current.getWebrateCompetitorName().equalsIgnoreCase(request.getWebrateCompetitorName()))
                        .findAny().orElse(null);

                if (match != null) {
                    StringBuilder sb = new StringBuilder();
                    Set<String> matchAccomCodes = match.getAccomClassCodeDTAMappings().stream().map(AccomClassDTAMapping::getAccomClassCode).collect(toSet());
                    Set<String> requestAccomCodes = request.getAccomClassCodeDTAMappings().stream().map(AccomClassDTAMapping::getAccomClassCode).collect(toSet());

                    if (!matchAccomCodes.containsAll(requestAccomCodes)) {
                        matchAccomCodes.forEach(accomCode -> sb.append(accomCode).append(" "));
                        throw new TetrisException("Invalid Accom Codes found in request for competitor " + request.getWebrateCompetitorName() + " For Product " + req.getProductName() +
                                ". Valid Accom codes are " + sb);
                    }
                } else {
                    throw new TetrisException("Invalid competitor name found in request " + request.getWebrateCompetitorName());
                }
            }
        }
    }

    public List<WebrateChannel> getAllChannelsByPropertyForIgnoreChannelSettings() {
        Integer propertyId = ((WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY)).getPropertyId();

        List<WebrateChannel> channelList = crudService.findByNamedQuery(
                WebrateChannel.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());
        WebrateDefaultChannel defaultChannel = crudService.findByNamedQuerySingleResult(
                WebrateDefaultChannel.BY_PROPERTY_ID_FOR_BAR_ONLY,
                QueryParameter.with("propertyId", propertyId).parameters());

        if (defaultChannel != null && defaultChannel.isAllDaysSameChannel(defaultChannel)) {
            channelList.remove(defaultChannel.getWebrateChannelMon());
        }

        return channelList;
    }

    public List<WebrateChannel> getAllWebrateChannels() {
        return crudService.findAll(WebrateChannel.class);
    }

    public List<WebrateCompChannelMapping> getWebrateCompChannelMappings() {
        return crudService.findAll(WebrateCompChannelMapping.class);
    }

    public Map<WebrateOverrideCompetitorDetails, List<WebrateChannel>> getWebrateOvrCompetitorDetailsChannelMap(Integer productId, Set<Integer> allCompetitorsExcludingSelfIds) {
        List<WebrateCompChannelMapping> result = allCompetitorsExcludingSelfIds.isEmpty() ? Collections.emptyList() :
                getWebrateCompChannelMappingsByProductIdAndCompetitorIds(productId, allCompetitorsExcludingSelfIds);

        Map<WebrateOverrideCompetitorDetails, List<WebrateChannel>> mappingResult = new HashMap<>();
        for (WebrateCompChannelMapping mapping : result) {
            WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails = mapping.getWebrateOverrideCompetitorDetails();
            WebrateChannel channel = mapping.getWebrateChannel();
            mappingResult.computeIfAbsent(webrateOverrideCompetitorDetails, k -> new ArrayList<>()).add(channel);
        }
        return mappingResult;
    }

    public List<WebrateCompChannelMapping> getWebrateCompChannelMappingsByProductIdAndCompetitorIds(Integer productId, Set<Integer> allCompetitorsExcludingSelfIds) {
        return crudService.findByNamedQuery(WebrateCompChannelMapping.BY_PRODUCT_ID_AND_COMPETITOR_IDS,
                QueryParameter.with("productId", productId).and("competitorIds", allCompetitorsExcludingSelfIds).parameters());
    }

    public void saveCompChannelMapping(List<WebrateCompChannelMapping> mapping) {
        crudService.save(mapping);
    }

    public List<WebrateCompChannelMapping> findCompChannelMappingsByWebrateOvrDetails(WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails) {
        return crudService.findByNamedQuery(WebrateCompChannelMapping.BY_WEBRATE_OVR_COMP_DETAILS,
                QueryParameter.with("webrateOverrideCompetitorDetails", webrateOverrideCompetitorDetails).parameters());
    }

    public void deleteCompChannelMappings(List<WebrateCompChannelMapping> mapping) {
        crudService.delete(mapping);
    }

    public WebratesDto getWebrateConfigurations() {
        WebratesDto dto = new WebratesDto();
        dto.setCompetitors(getCompetitors());
        dto.setCompetitiveRoomtypes(getCompetitiveRoomTypes());
        dto.setChannels(getChannels());
        return dto;
    }

    private List<Competitor> getCompetitors() {
        return Optional.ofNullable(crudService.findByNamedQuery(WebrateCompetitors.GET_ALL_COMPETITORS))
                .orElse(Collections.emptyList())
                .stream().map(row -> {
                    Object[] r = (Object[]) row;
                    return new Competitor((Integer) r[0], (String) r[1], (String) r[2],
                            (String) r[3], (String) r[4], Status.getStatusFromId((Integer) r[5]));
                }).collect(Collectors.toList());
    }

    private List<CompetitiveRoomType> getCompetitiveRoomTypes() {
        return Optional.ofNullable(crudService.findByNamedQuery(WebrateAccomType.GET_ALL_ACCOM_TYPES))
                .orElse(Collections.emptyList())
                .stream().map(row -> {
                    Object[] r = (Object[]) row;
                    return new CompetitiveRoomType((Integer) r[0], (String) r[1]);
                }).collect(Collectors.toList());
    }

    private List<Channel> getChannels() {
        return Optional.ofNullable(crudService.findByNamedQuery(WebrateChannel.GET_ALL_CHANNELS))
                .orElse(Collections.emptyList())
                .stream().map(row -> {
                    Object[] r = (Object[]) row;
                    return new Channel((Integer) r[0], (String) r[1], (String) r[2],
                            (String) r[3], Status.getStatusFromId((Integer) r[4]));
                }).collect(Collectors.toList());
    }
}
