package com.ideas.tetris.pacman.services.vendor;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.ideas.tetris.platform.common.ngi.VendorCredentials;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Id;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class VendorConfig implements Serializable, VendorCredentialsAccessor {
    private static final long serialVersionUID = 8380766347578106411L;
    @Id
    private String inboundVendorId;
    private String outboundVendorId;
    @NotNull
    private String name;
    private List<ChainConfigParams> chains = new ArrayList<>();
    private VendorCredentials inboundCredentials;
    private VendorCredentials outboundCredentials;
    private NGIType integrationType;
    private Boolean unqualifiedRatesDirectPopulationDisabled;
    private Boolean qualifiedRatesDirectPopulationDisabled;
    private Boolean useCustomSoapAction;
    private String customReplyToAddress;
    private Boolean cancelMultiUnitDecrements;
    private Integer installationReservationsThreshold;
    private Boolean oxiRoomStayReservationByDay;
    private Boolean htngRoomStayReservationByDay;
    private Boolean folsRoomStayReservationByDay;
    private Boolean htngUseBasicAuth;
    private Boolean sendHTNGCallbackRequest;
    private Boolean dedicatedValidationResponseChannel;
    private Boolean includeRoomTypeHotelMarketSegmentActivity;
    private Boolean preventPseudoDataInActivity;
    private Boolean isComponentRoomsActivityEnabled;
    private Boolean useCurrencyInMessage;
    private Boolean useLegacyRoomStayHandling;
    private Boolean generateMarketSegmentStatsEnabled;
    private Boolean generateRateCodeStatsEnabled;
    private Boolean resetVirtualSuiteCounts;
    private Boolean generateHotelActivityFromRoomTypeActivity;
    private Boolean msrtSummaryPersistenceEnabled;
    private Boolean buildMSActivityUsingPMSMS;
    private Boolean summaryPersistenceEnabled;
    private String defaultMarketSegmentCode;
    private String defaultGroupMarketSegmentCode;
    private Boolean alternateHTNGGroupsFlow;
    private Boolean processSoftPickups;
    private String htngAvailRQCustomAction;
    private String htngInvBlockRSCustomAction;
    private String htngInvCountRSCustomAction;
    private String htngRatePlanRQCustomAction;
    private String htngRatePlanRSCustomAction;
    private String htngResRSCustomAction;
    private String htngStatsRSCustomAction;
    private Boolean mergeGroupBlocksRatesIfMissing;
    private Boolean mergeGroupBlocksForHeaderOnlyCancel;
    private String adjustReservationNetRates;
    private Boolean allowBookingAndCancellationDateUpdates;
    private Boolean runGroupAutoWash;
    private Boolean folsUseCloudProcessing;
    private Boolean rraUseCloudProcessing;
    private Boolean adjustIdpSoldsUsingSkewingFactor;
    private Integer decisionsThreshold;
    private List<String> pseudoRoomTypes;
    private Boolean usePastInventoryForStatsAlways;
    private Boolean ignoreRateDetails;
    private Integer groupPastDays;
    private Boolean oxiUseCloudProcessing;
    private String cloudMigrationStatus;
    private Boolean htngUseCloudProcessing;
    private Boolean htngCallbackOnCloudDisabled;

    @Override
    public VendorCredentials getOutboundCredentials() {
        return outboundCredentials;
    }

    @Override
    public void setOutboundCredentials(VendorCredentials outboundCredentials) {
        this.outboundCredentials = outboundCredentials;
    }

    @Override
    public VendorCredentials getInboundCredentials() {
        return inboundCredentials;
    }

    @Override
    public void setInboundCredentials(VendorCredentials inboundCredentials) {
        this.inboundCredentials = inboundCredentials;
    }

    public void cleanUpEmptyData() {
        if (getOutboundCredentials() != null && StringUtils.isBlank(getOutboundCredentials().getUsername()) && StringUtils.isBlank(getOutboundCredentials().getPassword()) && StringUtils.isBlank(getOutboundCredentials().getAuthenticationToken())) {
            setOutboundCredentials(null);
        }
        if (getInboundCredentials() != null && StringUtils.isBlank(getInboundCredentials().getUsername()) && StringUtils.isBlank(getInboundCredentials().getPassword())) {
            setInboundCredentials(null);
        }
        cleanUpEmptyChainData();
    }

    private void cleanUpEmptyChainData() {
        for (ChainConfigParams chain : getChains()) {
            chain.cleanUpEmptyChainData();
        }
    }

    public Boolean getComponentRoomsActivityEnabled() {
        return isComponentRoomsActivityEnabled;
    }

    public void setComponentRoomsActivityEnabled(Boolean componentRoomsActivityEnabled) {
        this.isComponentRoomsActivityEnabled = componentRoomsActivityEnabled;
    }
}
