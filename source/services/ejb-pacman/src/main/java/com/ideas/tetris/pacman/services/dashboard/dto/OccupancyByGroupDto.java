package com.ideas.tetris.pacman.services.dashboard.dto;


import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Key;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.MultiPropertyAggregate;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Sum;

import java.math.BigDecimal;

@MultiPropertyAggregate
public class OccupancyByGroupDto {
    @Key
    private String groupName;


    @Sum
    private BigDecimal occupancyNumber;


    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }


    public BigDecimal getOccupancyNumber() {
        return occupancyNumber;
    }

    public void setOccupancyNumber(BigDecimal occupancyNumber) {
        this.occupancyNumber = occupancyNumber;
    }

}
