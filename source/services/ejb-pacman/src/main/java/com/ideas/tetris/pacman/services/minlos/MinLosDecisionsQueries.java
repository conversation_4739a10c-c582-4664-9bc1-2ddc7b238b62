package com.ideas.tetris.pacman.services.minlos;

public class MinLosDecisionsQueries {

    private MinLosDecisionsQueries() {
    }

    public static final String GET_DIFFERENTIAL_MINLOS_DECISIONS = " select Arrival_dt,Accom_Type_Code,Rate_Code_Name,minlos from( " +
            " select npt.* from(select Arrival_dt,Accom_Type_ID, " +
            " 		 Rate_qualified_ID, minlos " +
            " 	 from dbo.Decision_Minlos DBR  " +
            "  where   Arrival_dt >= :decisionStartDate and Arrival_dt <= :decisionEndDate " +
            " 	) as npt left join " +
            " (select PGB.Decision_id,PGB.Arrival_dt,PGB.Accom_Type_ID,PGB.Rate_qualified_ID,PDB.Minlos " +
            " 	 from (select Arrival_dt,Accom_Type_ID, Rate_qualified_ID, " +
            " 	 MAX(D.Decision_id)as Decision_id   " +
            " 	 from dbo.PACE_Minlos DBR inner join Decision D on DBR.Decision_ID=D.Decision_ID " +
            " 	 and  Arrival_dt >= :decisionStartDate and Arrival_dt <= :decisionEndDate  " +
            " 	 and D.End_DTTM <= :lastUploadedDate  " +
            " 	 group by Arrival_DT,Accom_Type_ID,Rate_qualified_ID) as PGB inner join PACE_Minlos PDB " +
            " 	 on PGB.Arrival_DT=PDB.Arrival_DT and   " +
            " 	 PGB.Accom_Type_ID=PDB.Accom_Type_ID and   " +
            " 	PGB.Rate_qualified_ID=PDB.Rate_qualified_ID and " +
            " 	 PGB.Decision_id=PDB.Decision_id) as pt on   " +
            " 	 pt.arrival_dt=npt.arrival_dt and   " +
            " 	 pt.Accom_Type_ID=npt.Accom_Type_ID   " +
            " 	and pt.Rate_qualified_ID=npt.Rate_qualified_ID " +
            " 	 where pt.minlos <> npt.minlos  " +
            " or pt.arrival_dt is null  " +
            " 	 ) as dfd  " +
            " 	 inner join Accom_Type at on dfd.Accom_Type_Id =at.Accom_Type_ID inner join Rate_qualified rq " +
            " 	 on dfd.Rate_qualified_ID= rq.Rate_qualified_ID where rq.Yieldable = 1 and rq.status_id=1 order by Rate_Code_Name,Accom_Type_Code,Arrival_DT";

    public static final String GET_DIFFERENTIAL_MINLOS_DECISIONS_OPTIMIZED_SQL = "SELECT dfd.Arrival_dt, at.Accom_Type_Code, rq.Rate_Code_Name, dfd.minlos " +
            "FROM Decision_minlos AS dfd " +
            "INNER JOIN Accom_Type AS AT ON dfd.accom_type_id = at.Accom_Type_ID " +
            "INNER JOIN Rate_Qualified AS rq ON dfd.rate_qualified_id = rq.Rate_Qualified_ID and rq.Yieldable = 1 and rq.status_id=1 " +
            "LEFT JOIN " +
            "(SELECT PGB.Decision_id, PGB.Arrival_dt, PGB.Accom_Type_ID, PGB.Rate_qualified_ID, PGB.Minlos " +
            "FROM " +
            "(SELECT DBR.Arrival_dt, DBR.Accom_Type_ID, DBR.Rate_qualified_ID, DBR.Decision_id, DBR.Minlos " +
            ", Row_number() OVER (PARTITION BY DBR.Arrival_dt,DBR.Accom_Type_ID,DBR.Rate_qualified_ID ORDER BY DBR.Decision_id DESC) AS MAXDecisionID " +
            "FROM dbo.PACE_Minlos DBR " +
            "INNER JOIN Decision D ON DBR.Decision_ID=D.Decision_ID AND DBR.Arrival_dt >= :decisionStartDate  AND DBR.Arrival_dt <= :decisionEndDate AND D.End_DTTM <= :lastUploadedDate " +
            ") PGB " +
            "WHERE PGB.MAXDecisionID = 1 " +
            ") AS pt ON pt.arrival_dt = dfd.arrival_dt AND pt.Accom_Type_ID = dfd.Accom_Type_ID AND pt.Rate_qualified_ID = dfd.Rate_qualified_ID " +
            "WHERE  dfd.Arrival_dt >= :decisionStartDate and dfd.Arrival_dt <= :decisionEndDate " +
            "AND (pt.minlos <> dfd.minlos   or pt.arrival_dt is null) " +
            "ORDER BY rq.Rate_Code_Name, at.Accom_Type_Code, dfd.Arrival_DT";
    public static final String GET_COUNT_DIFFERENTIAL_MINLOS_DECISIONS_OPTIMIZED_SQL = "select top 1 1 " +
            "FROM Decision_minlos AS dfd " +
            "INNER JOIN Accom_Type AS AT ON dfd.accom_type_id = at.Accom_Type_ID " +
            "INNER JOIN Rate_Qualified AS rq ON dfd.rate_qualified_id = rq.Rate_Qualified_ID and rq.Yieldable = 1 and rq.status_id=1 " +
            "LEFT JOIN " +
            "(SELECT PGB.Decision_id, PGB.Arrival_dt, PGB.Accom_Type_ID, PGB.Rate_qualified_ID, PGB.Minlos " +
            "FROM " +
            "(SELECT DBR.Arrival_dt, DBR.Accom_Type_ID, DBR.Rate_qualified_ID, DBR.Decision_id, DBR.Minlos " +
            ", Row_number() OVER (PARTITION BY DBR.Arrival_dt,DBR.Accom_Type_ID,DBR.Rate_qualified_ID ORDER BY DBR.Decision_id DESC) AS MAXDecisionID " +
            "FROM dbo.PACE_Minlos DBR " +
            "INNER JOIN Decision D ON DBR.Decision_ID=D.Decision_ID AND DBR.Arrival_dt >= :decisionStartDate  AND DBR.Arrival_dt <= :decisionEndDate AND D.End_DTTM <= :lastUploadedDate " +
            ") PGB " +
            "WHERE PGB.MAXDecisionID = 1 " +
            ") AS pt ON pt.arrival_dt = dfd.arrival_dt AND pt.Accom_Type_ID = dfd.Accom_Type_ID AND pt.Rate_qualified_ID = dfd.Rate_qualified_ID " +
            "WHERE  dfd.Arrival_dt >= :decisionStartDate and dfd.Arrival_dt <= :decisionEndDate " +
            "AND (pt.minlos <> dfd.minlos   or pt.arrival_dt is null)";
    public static final String GET_FULL_MINLOS_DECISIONS = "select Arrival_DT,at.Accom_Type_Code, rq.Rate_Code_Name, Minlos " +
            " from Decision_minlos as dm inner join Accom_Type as at on dm.accom_type_id = at.Accom_Type_ID " +
            " inner join Rate_Qualified as rq on dm.rate_qualified_id = rq.Rate_Qualified_ID where Arrival_DT >= :decisionStartDate and Arrival_DT <= :decisionEndDate" +
            " and rq.Yieldable = 1 and rq.status_id=1 " +
            " order by Rate_Code_Name,Accom_Type_Code,Arrival_DT";
    public static final String GET_COUNT_FULL_MINLOS_DECISIONS = "select top 1 1 " +
            " from Decision_minlos as dm inner join Accom_Type as at on dm.accom_type_id = at.Accom_Type_ID " +
            " inner join Rate_Qualified as rq on dm.rate_qualified_id = rq.Rate_Qualified_ID where Arrival_DT >= :decisionStartDate and Arrival_DT <= :decisionEndDate" +
            " and rq.Yieldable = 1 and rq.status_id=1";
    public static final String GET_DIFFERENTIAL_MINLOS_DECISIONS_FOR_RATE_CODE_ROOM_TYPE = "SELECT diff.Arrival_DT, " + " Accom_Type_Code, " + " Rate_Code_Name, " + " MINLOS " +
            "FROM " + "  (SELECT DISTINCT npt.Arrival_DT, " + " npt.Rate_Qualified_ID " +
            "   FROM " + " (SELECT Arrival_dt, " + " Accom_Type_ID, " + " Rate_qualified_ID, " + " minlos " +
            "   FROM dbo.Decision_Minlos DBR " + " WHERE Arrival_dt >= :decisionStartDate " + " AND Arrival_dt <= :decisionEndDate ) AS npt " +
            "   LEFT JOIN " + " (SELECT PGB.Decision_id," + "PGB.Arrival_dt," + "PGB.Accom_Type_ID," + "PGB.Rate_qualified_ID," + "PDB.Minlos" +
            "      FROM " + " (SELECT Arrival_dt," + "Accom_Type_ID," + "Rate_qualified_ID," + "MAX(D.Decision_id)AS Decision_id " +
            "         FROM dbo.PACE_Minlos DBR " + "INNER JOIN Decision D ON DBR.Decision_ID=D.Decision_ID " + "AND Arrival_dt >= :decisionStartDate " + " AND Arrival_dt <= :decisionEndDate " +
            "         AND D.End_DTTM <= :lastUploadedDate " + " GROUP BY Arrival_DT," + "Accom_Type_ID," + "Rate_qualified_ID ) AS PGB " +
            "      INNER JOIN PACE_Minlos PDB ON PGB.Arrival_DT = PDB.Arrival_DT " + "AND PGB.Accom_Type_ID = PDB.Accom_Type_ID " + "AND PGB.Rate_qualified_ID = PDB.Rate_qualified_ID " +
            " AND PGB.Decision_id = PDB.Decision_id) AS pt ON pt.arrival_dt = npt.arrival_dt " + "AND pt.Accom_Type_ID = npt.Accom_Type_ID " + "AND pt.Rate_qualified_ID = npt.Rate_qualified_ID " +
            "   WHERE pt.minlos <> npt.minlos " + " OR pt.arrival_dt IS NULL ) AS diff " +
            " INNER JOIN " + " (SELECT Arrival_DT," + "dm.Rate_Qualified_ID," + "at.Accom_Type_Code," + "rq.Rate_Code_Name," + "Minlos" +
            "   FROM Decision_minlos AS dm " + " INNER JOIN Accom_Type AS AT ON dm.accom_type_id = at.Accom_Type_ID " +
            "   INNER JOIN Rate_Qualified AS rq ON dm.rate_qualified_id = rq.Rate_Qualified_ID " + "WHERE Arrival_DT >= :decisionStartDate " +
            " AND Arrival_DT <= :decisionEndDate and rq.Yieldable = 1 and rq.status_id=1) AS ful ON diff.Arrival_DT = ful.Arrival_DT " + "AND diff.Rate_Qualified_ID = ful.Rate_Qualified_ID " +
            " ORDER BY ful.Arrival_DT," + "ful.Rate_Code_Name, " + "ful.Accom_Type_Code";

    public static final String GET_DIFFERENTIAL_MINLOS_DECISIONS_FOR_RATE_CODE_ROOM_TYPE_OPTIMIZED_SQL = "SELECT ful.Arrival_DT, at.Accom_Type_Code, rq.Rate_Code_Name, ful.MINLOS " +
            "FROM Decision_minlos AS ful " +
            "INNER JOIN Accom_Type AS at ON ful.accom_type_id = at.Accom_Type_ID " +
            "INNER JOIN Rate_Qualified AS rq ON ful.rate_qualified_id = rq.Rate_Qualified_ID and rq.Yieldable = 1 and rq.status_id=1 " +
            "INNER JOIN " +
            "(SELECT DISTINCT npt.Arrival_DT,  npt.Rate_Qualified_ID FROM dbo.Decision_Minlos as npt " +
            "LEFT JOIN " +
            "(SELECT PGB.Decision_id, PGB.Arrival_dt, PGB.Accom_Type_ID, PGB.Rate_qualified_ID, PGB.Minlos FROM " +
            "(SELECT DBR.Arrival_dt, DBR.Accom_Type_ID, DBR.Rate_qualified_ID, DBR.Decision_id, DBR.Minlos " +
            ", Row_number() OVER (PARTITION BY DBR.Arrival_dt,DBR.Accom_Type_ID,DBR.Rate_qualified_ID ORDER BY DBR.Decision_id DESC) AS MAXDecisionID " +
            "FROM dbo.PACE_Minlos DBR " +
            "INNER JOIN Decision D ON DBR.Decision_ID=D.Decision_ID AND DBR.Arrival_dt >= :decisionStartDate  AND DBR.Arrival_dt <= :decisionEndDate AND D.End_DTTM <= :lastUploadedDate " +
            ") PGB " +
            "WHERE PGB.MAXDecisionID = 1 " +
            ") AS pt " +
            "ON pt.arrival_dt = npt.arrival_dt AND pt.Accom_Type_ID = npt.Accom_Type_ID AND pt.Rate_qualified_ID = npt.Rate_qualified_ID " +
            "WHERE npt.Arrival_dt >= :decisionStartDate  AND npt.Arrival_dt <= :decisionEndDate " +
            "AND (pt.minlos <> npt.minlos  OR pt.arrival_dt IS NULL) " +
            ") AS diff ON diff.Arrival_DT = ful.Arrival_DT " +
            "AND diff.Rate_Qualified_ID = ful.Rate_Qualified_ID " +
            "WHERE ful.Arrival_DT >=  :decisionStartDate  AND ful.Arrival_DT <= :decisionEndDate " +
            "ORDER BY ful.Arrival_DT, rq.Rate_Code_Name, at.Accom_Type_Code";
    public static final String GET_COUNT_DIFFERENTIAL_MINLOS_DECISIONS_FOR_RATE_CODE_ROOM_TYPE_OPTIMIZED_SQL = "select top 1 1 " +
            "FROM Decision_minlos AS ful " +
            "INNER JOIN Accom_Type AS at ON ful.accom_type_id = at.Accom_Type_ID " +
            "INNER JOIN Rate_Qualified AS rq ON ful.rate_qualified_id = rq.Rate_Qualified_ID and rq.Yieldable = 1 and rq.status_id=1 " +
            "INNER JOIN " +
            "(SELECT DISTINCT npt.Arrival_DT,  npt.Rate_Qualified_ID FROM dbo.Decision_Minlos as npt " +
            "LEFT JOIN " +
            "(SELECT PGB.Decision_id, PGB.Arrival_dt, PGB.Accom_Type_ID, PGB.Rate_qualified_ID, PGB.Minlos FROM " +
            "(SELECT DBR.Arrival_dt, DBR.Accom_Type_ID, DBR.Rate_qualified_ID, DBR.Decision_id, DBR.Minlos " +
            ", Row_number() OVER (PARTITION BY DBR.Arrival_dt,DBR.Accom_Type_ID,DBR.Rate_qualified_ID ORDER BY DBR.Decision_id DESC) AS MAXDecisionID " +
            "FROM dbo.PACE_Minlos DBR " +
            "INNER JOIN Decision D ON DBR.Decision_ID=D.Decision_ID AND DBR.Arrival_dt >= :decisionStartDate  AND DBR.Arrival_dt <= :decisionEndDate AND D.End_DTTM <= :lastUploadedDate " +
            ") PGB " +
            "WHERE PGB.MAXDecisionID = 1 " +
            ") AS pt " +
            "ON pt.arrival_dt = npt.arrival_dt AND pt.Accom_Type_ID = npt.Accom_Type_ID AND pt.Rate_qualified_ID = npt.Rate_qualified_ID " +
            "WHERE npt.Arrival_dt >= :decisionStartDate  AND npt.Arrival_dt <= :decisionEndDate " +
            "AND (pt.minlos <> npt.minlos  OR pt.arrival_dt IS NULL) " +
            ") AS diff ON diff.Arrival_DT = ful.Arrival_DT " +
            "AND diff.Rate_Qualified_ID = ful.Rate_Qualified_ID " +
            "WHERE ful.Arrival_DT >=  :decisionStartDate  AND ful.Arrival_DT <= :decisionEndDate ";
    public static final String DECISION_END_DATE = "decisionEndDate";
    public static final String LAST_UPLOADED_DATE = "lastUploadedDate";
    public static final String DECISION_START_DATE = "decisionStartDate";
}