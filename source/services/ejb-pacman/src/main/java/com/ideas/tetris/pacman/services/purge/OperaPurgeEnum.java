package com.ideas.tetris.pacman.services.purge;

import static com.ideas.tetris.pacman.services.purge.PurgeConstants.DATA_LOAD_METADATA_ID;
import static com.ideas.tetris.pacman.services.purge.PurgeConstants.OPERA;
import static com.ideas.tetris.pacman.services.purge.PurgeConstants.WHERE_FIELD_TO_COMPARE_METADATAID;
import static com.ideas.tetris.pacman.services.purge.PurgeConstants.WHERE_TABLE_NAME_ID_METADATAID_AND_INCOMING_FILE_TYPE_CODE_YC;

public enum OperaPurgeEnum implements Purgable {

    FILTERED_TRANSACTION("FILTERED_TRANSACTION", OPERA, DATA_LOAD_METADATA_ID, WHERE_FIELD_TO_COMPARE_METADATAID),
    HISTORY_GROUP_BLOCK("HISTORY_GROUP_BLOCK", OPERA, DATA_LOAD_METADATA_ID, WHERE_FIELD_TO_COMPARE_METADATAID),
    HISTORY_GROUP_MASTER("HISTORY_GROUP_MASTER", OPERA, DATA_LOAD_METADATA_ID, WHERE_FIELD_TO_COMPARE_METADATAID),
    HISTORY_TRANSACTION("HISTORY_TRANSACTION", OPERA, DATA_LOAD_METADATA_ID, WHERE_FIELD_TO_COMPARE_METADATAID),
    HISTORY_OCCUPANCY_SUMMARY("HISTORY_OCCUPANCY_SUMMARY", OPERA, DATA_LOAD_METADATA_ID, WHERE_FIELD_TO_COMPARE_METADATAID),
    HISTORY_INCOMING_METADATA("HISTORY_INCOMING_METADATA", OPERA, DATA_LOAD_METADATA_ID, WHERE_FIELD_TO_COMPARE_METADATAID),
    HISTORY_YIELD_CURRENCY("HISTORY_YIELD_CURRENCY", OPERA, DATA_LOAD_METADATA_ID, WHERE_FIELD_TO_COMPARE_METADATAID),
    DATA_LOAD_METADATA("DATA_LOAD_METADATA", OPERA, DATA_LOAD_METADATA_ID, WHERE_TABLE_NAME_ID_METADATAID_AND_INCOMING_FILE_TYPE_CODE_YC);

    private final String schema;
    private final String tableToDelete;
    private final String tableToCompare;
    private final String fieldToCompare;
    private final String whereClauseTemplate;

    OperaPurgeEnum(String tableName, String schema, String fieldToCompare, String whereClauseTemplate) {
        this.schema = schema;
        this.tableToDelete = this.name();
        this.tableToCompare = tableName;
        this.fieldToCompare = fieldToCompare;
        this.whereClauseTemplate = whereClauseTemplate;
    }

    public String getWhereClauseTemplate() {
        return whereClauseTemplate;
    }

    @Override
    public String getSchema() {
        return schema;
    }

    @Override
    public String getTableToDelete() {
        return tableToDelete;
    }

    @Override
    public String getTableToCompare() {
        return tableToCompare;
    }

    @Override
    public String getFieldToCompare() {
        return fieldToCompare;
    }

    @Override
    public boolean isFailedSilently() {
        return false;
    }
}
