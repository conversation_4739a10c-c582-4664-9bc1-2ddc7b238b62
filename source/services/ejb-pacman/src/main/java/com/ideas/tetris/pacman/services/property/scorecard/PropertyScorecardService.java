package com.ideas.tetris.pacman.services.property.scorecard;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.property.scorecard.dto.PropertyScoreDTO;
import com.ideas.tetris.pacman.services.property.scorecard.rowmapper.PropertyScoreDTORowMapper;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;

import javax.inject.Inject;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PropertyScorecardService {
    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    public List<PropertyScoreDTO> getPropertyScores(List<Integer> propertyIds) {
        String nativeQuery = "select decision.Property_ID, property_scorecard.Start_Date, property_scorecard.End_Date, " +
                "property_scorecard.Score,property_scorecard.FutureScore,property_scorecard.Future_Start_Date,property_scorecard.Future_End_Date,property_scorecard.Entry_Date, " +
                "decision.[earliest decision date] from " +
                "( " +
                "       select * from " +
                "       ( " +
                "              select * from Monitor_Process_Scores mps where mps.Score_Name = 'Property Scorecard' " +
                "              and mps.Entry_Date = ( select MAX(Entry_Date) from Monitor_Process_Scores where Score_Name = 'Property Scorecard') " +
                "       )propertyScore full outer join " +
                "       (" +
                "           select mps.Score as FutureScore ,mps.Start_Date as Future_Start_Date,mps.End_Date as Future_End_Date " +
                "           from Monitor_Process_Scores mps where mps.Score_Name = 'Forward Looking Alert Score' " +
                "           and mps.Entry_Date = ( select MAX(Entry_Date) from Monitor_Process_Scores where Score_Name = 'Forward Looking Alert Score') " +
                "       )futureScore on 1 = 1  " +
                ") property_scorecard right join  " +
                "(" +
                "           select d.Property_ID, MIN(d.caught_up_dttm) 'earliest decision date' from Decision d where d.Decision_Type_ID = 1 and d.Process_Status_ID = 13 " +
                "           group by d.Property_ID " +
                ") decision on 1 = 1";

        List<PropertyScoreDTO> propertyScores = multiPropertyCrudService
                .findByNativeQueryUnionAcrossProperties(
                        propertyIds,
                        nativeQuery,
                        null,
                        new PropertyScoreDTORowMapper());
        return propertyScores;
    }

    @ForTesting
    // use only for vaadin UI test.

    public void clearMonitorProcessScores(int propertyId) {
        tenantCrudService.executeUpdateByNativeQuery("delete from monitor_process_scores");
    }

    @ForTesting
    // use only for vaadin UI test.

    public void insertOnlyHistoryPropertyScoreIntoMonitorProcessScores(int propertyId) {

        tenantCrudService.executeUpdateByNativeQuery("delete from monitor_process_scores");

        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',100,DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-365,GETDATE()),DATEADD(DAY,-271,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',100,DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-365,GETDATE()),DATEADD(DAY,-271,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',100,DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-365,GETDATE()),DATEADD(DAY,-271,GETDATE()))");

        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',85,DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-181,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',0.5,DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-181,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',0.9,DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-181,GETDATE()))");

        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',94,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',1.5,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',2.9,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");

        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',95,GETDATE(),DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-1,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',3.5,GETDATE(),DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-1,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',4.9,GETDATE(),DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-1,GETDATE()))");
    }

    @ForTesting
    // use only for vaadin UI test.

    public void insertHistoryAndFuturePropertyScoreIntoMonitorProcessScores(int propertyId) {

        tenantCrudService.executeUpdateByNativeQuery("delete from monitor_process_scores");

        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',100,DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-365,GETDATE()),DATEADD(DAY,-271,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',100,DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-365,GETDATE()),DATEADD(DAY,-271,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',100,DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-365,GETDATE()),DATEADD(DAY,-271,GETDATE()))");

        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',85,DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-181,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',0.5,DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-181,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',0.9,DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-181,GETDATE()))");

        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',94,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',1.5,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',2.9,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");

        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',45,GETDATE(),DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-1,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',3.5,GETDATE(),DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-1,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',4.9,GETDATE(),DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-1,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Forward Looking Alert Score',96,GETDATE(),DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-1,GETDATE()))");
    }

    @ForTesting
    // use only for vaadin UI test.

    public void insertMultipleHistoryAndFuturePropertyScoreIntoMonitorProcessScores(int propertyId) {


        tenantCrudService.executeUpdateByNativeQuery("delete from monitor_process_scores");


        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',100,DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-365,GETDATE()),DATEADD(DAY,-271,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',100,DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-365,GETDATE()),DATEADD(DAY,-271,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',100,DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-365,GETDATE()),DATEADD(DAY,-271,GETDATE()))");


        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',85,DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-181,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',0.5,DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-181,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',0.9,DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-181,GETDATE()))");


        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',94,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',1.5,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',2.9,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Forward Looking Alert Score',11,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");

        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',95,GETDATE(),DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-1,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',3.5,GETDATE(),DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-1,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',4.9,GETDATE(),DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-1,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Forward Looking Alert Score',96,GETDATE(),DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-1,GETDATE()))");


    }

    @ForTesting
    // use only for vaadin UI test.

    public void insertHistoryAndFuturePropertyScoreLessThanSysDateIntoMonitorProcessScores(int propertyId) {


        tenantCrudService.executeUpdateByNativeQuery("delete from monitor_process_scores");


        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',100,DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-365,GETDATE()),DATEADD(DAY,-271,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',100,DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-365,GETDATE()),DATEADD(DAY,-271,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',100,DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-365,GETDATE()),DATEADD(DAY,-271,GETDATE()))");


        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',85,DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-181,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',0.5,DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-181,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',0.9,DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-270,GETDATE()),DATEADD(DAY,-181,GETDATE()))");


        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Property Scorecard',94,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Special Event MAPE Score',1.5,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Regular Day MAPE Score',2.9,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");
        tenantCrudService.executeUpdateByNativeQuery("insert into monitor_process_scores values('Forward Looking Alert Score',11,DATEADD(DAY,-90,GETDATE()),DATEADD(DAY,-180,GETDATE()),DATEADD(DAY,-91,GETDATE()))");


    }


}
