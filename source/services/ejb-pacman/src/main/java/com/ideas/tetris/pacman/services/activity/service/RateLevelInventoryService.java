package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.tetris.pacman.services.ngi.dto.Inventory;
import com.ideas.tetris.pacman.services.ngi.dto.RateLevelInventory;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ideas.tetris.platform.common.rest.mapper.RestEndpoints.GET_FIRST_CORRELATED_INVENTORY;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@Component
@Transactional
public class RateLevelInventoryService {

    static final String CLIENT_CODE = "clientCode";
    static final String PROPERTY_CODE = "propertyCode";
    static final String STATISTICS_CORRELATION_ID = "statisticsCorrelationId";

    @Autowired
	private RestClient restClient;

    public List<RateLevelInventory> getRateLevelInventories(final String clientCode,
                                                            final String propertyCode,
                                                            final String statisticsCorrelationId) {
        final Inventory inventory = getInventory(clientCode, propertyCode, statisticsCorrelationId);

        return Optional
                .ofNullable(inventory)
                .map(Inventory::getRateLevelInventories)
                .orElseGet(ArrayList::new)
                .stream()
                .collect(Collectors.groupingBy(
                        rateLevelInventory -> {
                            String rateLevel = rateLevelInventory.getRateLevel();
                            if (StringUtils.isNotBlank(rateLevel) && rateLevel.contains("_")) {
                                // group by physical property code if rateLevel has underscore symbol
                                return rateLevel.substring(0, rateLevel.indexOf("_"));
                            } else {
                                return "";
                            }
                        }
                ))
                .values()
                .stream()
                .map(groupedRateLevelInventories -> groupedRateLevelInventories.stream()
                        .sorted(Comparator.comparingInt(RateLevelInventory::getMinLOS))
                        .reduce(new ArrayList<RateLevelInventory>(), (list, nextInventory) -> {
                            if (!list.isEmpty()) {
                                list.get(list.size() - 1).setMaxLOS(nextInventory.getMinLOS() - 1);
                            }
                            list.add(nextInventory);
                            return list;
                        }, (list1, list2) -> list1))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private Inventory getInventory(final String clientCode,
                                   final String propertyCode,
                                   final String statisticsCorrelationId) {
        final Map<String, String> parameters = new HashMap<>();
        parameters.put(CLIENT_CODE, clientCode);
        parameters.put(PROPERTY_CODE, propertyCode);
        parameters.put(STATISTICS_CORRELATION_ID, statisticsCorrelationId);
        return restClient.getEntityFromEndpoint(GET_FIRST_CORRELATED_INVENTORY, parameters, Inventory.class);
    }
}
