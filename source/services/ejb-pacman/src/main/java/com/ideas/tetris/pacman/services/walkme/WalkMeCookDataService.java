package com.ideas.tetris.pacman.services.walkme;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRankStatus;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.costofwalk.entity.CostofWalk;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.groupwash.ForecastGroupSummary;
import com.ideas.tetris.pacman.services.groupwash.MarketSegmentForecastGroupSummary;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.services.opera.entity.DataLoadMetadata;
import com.ideas.tetris.pacman.services.opera.entity.HistoryGroupMaster;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingAccom;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.joda.time.LocalDateTime;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ACCOM_TYPE_CODE_DBL;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ACCOM_TYPE_CODE_DLX;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ACCOM_TYPE_CODE_K;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ACCOM_TYPE_CODE_Q;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ACCOM_TYPE_CODE_STE;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ACCOM_TYPE_NAME_DELUXE;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ACCOM_TYPE_NAME_DOUBLE;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ACCOM_TYPE_NAME_KING;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ACCOM_TYPE_NAME_QUEEN;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ACCOM_TYPE_NAME_SUITE;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.CORRELATION_ID;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.DELUXE;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.DOUBLE_ROOM;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.FORECAST_GROUP;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.G;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.KING_SIZE;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.PALACE;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.QUEEN_SIZE;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.STANDARD;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.TRAVELLERS;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.TRV;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.WALK_ME_BARTER;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.WALK_ME_DISCOUNT;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.WALK_ME_FILE;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.WALK_ME_GROUP_MARKET_SEGMENT;
import static com.ideas.tetris.pacman.services.walkme.WalkMeConstants.ZERO;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class WalkMeCookDataService {

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    public void addRequirementForUnMappedGroupStatusCode() {
        DataLoadMetadata dataLoadMetadata = addDataLoadMetaData();
        List<HistoryGroupMaster> historyGroupMasterList = new ArrayList<>();
        historyGroupMasterList.add(addHistoryGroupMasterWithStatus(dataLoadMetadata, WalkMeConstants.STATUS_ACT, WalkMeConstants.ONE));
        historyGroupMasterList.add(addHistoryGroupMasterWithStatus(dataLoadMetadata, WalkMeConstants.STATUS_CXL, WalkMeConstants.TWO));
        historyGroupMasterList.add(addHistoryGroupMasterWithStatus(dataLoadMetadata, WalkMeConstants.STATUS_DEF, WalkMeConstants.THREE));
        historyGroupMasterList.add(addHistoryGroupMasterWithStatus(dataLoadMetadata, WalkMeConstants.STATUS_EXP, WalkMeConstants.FOUR));
        historyGroupMasterList.add(addHistoryGroupMasterWithStatus(dataLoadMetadata, WalkMeConstants.STATUS_LOS, WalkMeConstants.FIVE));
        historyGroupMasterList.add(addHistoryGroupMasterWithStatus(dataLoadMetadata, WalkMeConstants.STATUS_TNT, WalkMeConstants.SIX));
        historyGroupMasterList.add(addHistoryGroupMasterWithStatus(dataLoadMetadata, WalkMeConstants.STATUS_UNC, WalkMeConstants.SEVEN));
        tenantCrudService.save(historyGroupMasterList);
    }

    protected HistoryGroupMaster addHistoryGroupMasterWithStatus(DataLoadMetadata dataLoadMetadata, String status, String groupID) {
        HistoryGroupMaster historyGroupMaster = new HistoryGroupMaster();
        historyGroupMaster.setHotelCode(PALACE);
        historyGroupMaster.setGroupId(groupID);
        historyGroupMaster.setBlockCode(TRV);
        historyGroupMaster.setGroupName(TRAVELLERS);
        historyGroupMaster.setMasterGroupId(ZERO);
        historyGroupMaster.setStatus(status);
        historyGroupMaster.setMarketSegment(WALK_ME_GROUP_MARKET_SEGMENT);
        historyGroupMaster.setArrivalDate(LocalDate.now().toString());
        historyGroupMaster.setDepartureDate(LocalDate.now().toString());
        historyGroupMaster.setInsertDate(LocalDate.now().toString());
        historyGroupMaster.setGroupType(G);
        historyGroupMaster.setDataLoadMetadataId(dataLoadMetadata.getId());
        return historyGroupMaster;
    }

    protected DataLoadMetadata addDataLoadMetaData() {
        DataLoadMetadata dataLoadMetadata = new DataLoadMetadata();
        dataLoadMetadata.setCorrelationId(CORRELATION_ID);
        dataLoadMetadata.setIncomingFileTypeCode(WALK_ME_FILE);
        dataLoadMetadata.setCreateDate(new LocalDateTime());
        return tenantCrudService.save(dataLoadMetadata);
    }

    public void addRequirementsForRoomClassConfiguration() {
        setPropertyInDataPopulationMode();
        createUnmappedAccomTypes();
        createCostOfWalkDefault();
        createOverBookingAccom();
        enableGroupPriceRankingAndMinimumPriceDifferential();
        setContinuousPricingToTrue();
    }

    protected void setPropertyInDataPopulationMode() {
        Property property = globalCrudService.findByNamedQuerySingleResult(Property.BY_CLIENT_CODE_PROPERTY_CODE,
                QueryParameter.with(Constants.CLIENT_CODE, PacmanWorkContextHelper.getClientCode())
                        .and(Constants.PROPERTY_CODE, PacmanWorkContextHelper.getPropertyCode()).parameters());
        property.setStage(Stage.POPULATION);
        globalCrudService.save(property);
    }

    protected void createUnmappedAccomTypes() {
        List<AccomType> accomTypeList = new ArrayList<>();
        accomTypeList.add(createAccomType(ACCOM_TYPE_NAME_DELUXE, ACCOM_TYPE_CODE_DLX, DELUXE, 25, Constants.UNASSIGNED));
        accomTypeList.add(createAccomType(ACCOM_TYPE_NAME_SUITE, ACCOM_TYPE_CODE_STE, ACCOM_TYPE_NAME_SUITE, 20, Constants.UNASSIGNED));
        accomTypeList.add(createAccomType(ACCOM_TYPE_NAME_DOUBLE, ACCOM_TYPE_CODE_DBL, DOUBLE_ROOM, 30, Constants.UNASSIGNED));
        accomTypeList.add(createAccomType(ACCOM_TYPE_NAME_QUEEN, ACCOM_TYPE_CODE_Q, QUEEN_SIZE, 40, Constants.UNASSIGNED));
        accomTypeList.add(createAccomType(ACCOM_TYPE_NAME_KING, ACCOM_TYPE_CODE_K, KING_SIZE, 50, Constants.UNASSIGNED));
        tenantCrudService.save(accomTypeList);
    }

    protected AccomType createAccomType(String accomTypeName, String accomTypeCode, String description, int accomTypeCapacity, String accomClassCode) {
        AccomType accomType = new AccomType();
        accomType.setAccomClass(tenantCrudService.findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with("code", accomClassCode)
                .and(Constants.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters()));
        accomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        accomType.setName(accomTypeName);
        accomType.setAccomTypeCode(accomTypeCode);
        accomType.setDescription(description);
        accomType.setAccomTypeCapacity(accomTypeCapacity);
        accomType.setSystemDefault(0);
        Date date = getCurrentDate();
        accomType.setLastUpdatedDate(date);
        accomType.setStatusId(1);
        accomType.setRohType(0);
        accomType.setCreatedByUserId(Integer.valueOf(PacmanWorkContextHelper.getUserId()));
        accomType.setCreateDate(date);
        accomType.setIsComponentRoom("N");
        accomType.setDisplayStatusId(1);
        return accomType;
    }

    public void createCostOfWalkDefault() {
        List<CostofWalk> costofWalkList = new ArrayList<>();
        costofWalkList.add(cookCostOfWalkData(ACCOM_TYPE_CODE_DLX));
        costofWalkList.add(cookCostOfWalkData(ACCOM_TYPE_CODE_STE));
        costofWalkList.add(cookCostOfWalkData(ACCOM_TYPE_CODE_DBL));
        costofWalkList.add(cookCostOfWalkData(ACCOM_TYPE_CODE_Q));
        costofWalkList.add(cookCostOfWalkData(ACCOM_TYPE_CODE_K));
        tenantCrudService.save(costofWalkList);
    }

    private CostofWalk cookCostOfWalkData(String accomTypeCode) {
        CostofWalk costofWalk = new CostofWalk();
        costofWalk.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        costofWalk.setAccomType(getAccomType(accomTypeCode));
        costofWalk.setSundayValue(new BigDecimal(80));
        costofWalk.setMondayValue(new BigDecimal(80));
        costofWalk.setTuesdayValue(new BigDecimal(80));
        costofWalk.setWednesdayValue(new BigDecimal(80));
        costofWalk.setThursdayValue(new BigDecimal(80));
        costofWalk.setFridayValue(new BigDecimal(80));
        costofWalk.setSaturdayValue(new BigDecimal(80));
        costofWalk.setCreatedByUserId(Integer.valueOf(PacmanWorkContextHelper.getUserId()));
        Date date = getCurrentDate();
        costofWalk.setCreateDate(date);
        costofWalk.setLastUpdatedByUserId(Integer.valueOf(PacmanWorkContextHelper.getUserId()));
        costofWalk.setLastUpdatedDate(date);
        return costofWalk;
    }

    protected AccomType getAccomType(String accomTypeCode) {
        return tenantCrudService.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", accomTypeCode).parameters());
    }

    public void createOverBookingAccom() {
        List<OverbookingAccom> overbookingAccomList = new ArrayList<>();
        overbookingAccomList.add(cookOverBookingData(ACCOM_TYPE_CODE_DLX));
        overbookingAccomList.add(cookOverBookingData(ACCOM_TYPE_CODE_STE));
        overbookingAccomList.add(cookOverBookingData(ACCOM_TYPE_CODE_DBL));
        overbookingAccomList.add(cookOverBookingData(ACCOM_TYPE_CODE_Q));
        overbookingAccomList.add(cookOverBookingData(ACCOM_TYPE_CODE_K));
        tenantCrudService.save(overbookingAccomList);
    }

    public OverbookingAccom cookOverBookingData(String accomTypeCode) {
        OverbookingAccom overbookingAccom = new OverbookingAccom();
        overbookingAccom.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        overbookingAccom.setAccomTypeId(getAccomType(accomTypeCode).getId());
        overbookingAccom.setSundayOverbookingTypeId(1);
        overbookingAccom.setMondayOverbookingTypeId(1);
        overbookingAccom.setTuesdayOverbookingTypeId(1);
        overbookingAccom.setWednesdayOverbookingTypeId(1);
        overbookingAccom.setThursdayOverbookingTypeId(1);
        overbookingAccom.setFridayOverbookingTypeId(1);
        overbookingAccom.setSaturdayOverbookingTypeId(1);
        overbookingAccom.setSundayCeiling(-1);
        overbookingAccom.setMondayCeiling(-1);
        overbookingAccom.setTuesdayCeiling(-1);
        overbookingAccom.setWednesdayCeiling(-1);
        overbookingAccom.setThursdayCeiling(-1);
        overbookingAccom.setFridayCeiling(-1);
        overbookingAccom.setSaturdayCeiling(-1);
        overbookingAccom.setCreatedByUserId(Integer.valueOf(PacmanWorkContextHelper.getUserId()));
        Date date = getCurrentDate();
        overbookingAccom.setCreateDate(date);
        overbookingAccom.setLastUpdatedByUserId(Integer.valueOf(PacmanWorkContextHelper.getUserId()));
        overbookingAccom.setLastUpdatedDate(date);
        return overbookingAccom;
    }

    protected void enableGroupPriceRankingAndMinimumPriceDifferential() {
        enableGroupPricing();
        configParamsService.updateParameterValue(getContextPath(), IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value(), Constants.FALSE);
        configParamsService.updateParameterValue(FeatureTogglesConfigParamName.IS_ADVANCED_PRICE_RANKING_ENABLED.value(), Constants.FALSE);
    }

    public void enableGroupPricing() {
        configParamsService.updateParameterValue(getContextPath(), FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value(), Constants.TRUE);
    }

    public void enableSupplementsForPricingConfiguration() {
        configParamsService.updateParameterValue(getContextPath(), GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value(), Constants.TRUE);
    }

    public void setContinuousPricingToTrue() {
        configParamsService.updateParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value(), Constants.TRUE);
        configParamsService.updateParameterValue(IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_RATEOFDAY);
        configParamsService.updateParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value(), Constants.FALSE);
    }

    public void setBarDecisionToBarByLOS() {
        configParamsService.updateParameterValue(getContextPath(), IPConfigParamName.BAR_BAR_DECISION.value(), Constants.BAR_DECISION_VALUE_LOS);
    }

    public String getContextPath() {
        return Constants.CONFIG_PARAMS_NODE_PREFIX + "." + PacmanWorkContextHelper.getClientCode() + "." + PacmanWorkContextHelper.getPropertyCode();
    }

    public void addRequirementsForGroupPricing() {
        enableGroupPricing();
        addRequirementsForMappedRoomClassRoomType();
        addMarketSegmentsForGroupPricingConfiguration();
    }

    public void addRequirementsForGroupPricingRcRp() {
        enableGroupPricing();
        addMarketSegmentsForGroupPricingConfiguration();
    }

    public void addRequirementsForMappedRoomClassRoomType() {
        addAccomClasses();
        addMappedRoomTypes();
    }

    protected void addAccomClasses() {
        tenantCrudService.save(Arrays.asList(
                addRoomClass(STANDARD, STANDARD, true, 1),
                addRoomClass(WalkMeConstants.ACCOM_TYPE_NAME_SUITE, WalkMeConstants.ACCOM_TYPE_NAME_SUITE, false, 2)
        ));
    }

    public AccomClass addRoomClass(String roomClassCode, String roomClassName, boolean isMasterClass, int order) {
        AccomClass accomClass = new AccomClass();
        accomClass.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        accomClass.setName(roomClassName);
        accomClass.setCode(roomClassCode);
        accomClass.setDescription(roomClassName);
        accomClass.setSystemDefault(0);
        accomClass.setStatusId(1);
        accomClass.setMasterClass(isMasterClass ? 1 : 0);
        accomClass.setRankOrder(order);
        accomClass.setCreatedByUserId(Integer.valueOf(PacmanWorkContextHelper.getUserId()));
        accomClass.setCreateDate(getCurrentDate());
        accomClass.setIsPriceRankConfigured(AccomClassPriceRankStatus.INCOMPLETE);
        accomClass.setExcludedForGroupEvaluation(false);
        return accomClass;
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    protected void addMappedRoomTypes() {
        tenantCrudService.save(Arrays.asList(createAccomType(ACCOM_TYPE_NAME_DELUXE, ACCOM_TYPE_CODE_DLX, DELUXE, 25, STANDARD),
                createAccomType(ACCOM_TYPE_NAME_SUITE, ACCOM_TYPE_CODE_STE, ACCOM_TYPE_NAME_SUITE, 20, STANDARD),
                createAccomType(ACCOM_TYPE_NAME_DOUBLE, ACCOM_TYPE_CODE_DBL, DOUBLE_ROOM, 30, STANDARD),
                createAccomType(ACCOM_TYPE_NAME_QUEEN, ACCOM_TYPE_CODE_Q, QUEEN_SIZE, 40, WalkMeConstants.ACCOM_TYPE_NAME_SUITE),
                createAccomType(ACCOM_TYPE_NAME_KING, ACCOM_TYPE_CODE_K, KING_SIZE, 50, WalkMeConstants.ACCOM_TYPE_NAME_SUITE)));
    }

    private Date getCurrentDate() {
        LocalDate localDate = LocalDate.now();
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public void addMarketSegmentsForGroupPricingConfiguration() {
        MarketSegmentSummary marketSegment1 = createAndAddMarketSegmentSummary(WALK_ME_BARTER);
        MarketSegmentSummary marketSegment2 = createAndAddMarketSegmentSummary(WALK_ME_DISCOUNT);
        ForecastGroupSummary forecastGroupSummary = new ForecastGroupSummary();
        forecastGroupSummary.setName(FORECAST_GROUP);
        forecastGroupSummary.setCode(FORECAST_GROUP);
        forecastGroupSummary.setForecastTypeId(1);
        forecastGroupSummary.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        forecastGroupSummary.setStatusId(1);
        tenantCrudService.save(forecastGroupSummary);
        createAndAddMarketSegmentForecastGroup(marketSegment1, forecastGroupSummary);
        createAndAddMarketSegmentForecastGroup(marketSegment2, forecastGroupSummary);
    }

    private void createAndAddMarketSegmentForecastGroup(MarketSegmentSummary marketSegment1, ForecastGroupSummary forecastGroupSummary) {
        MarketSegmentForecastGroupSummary marketSegmentForecastGroupSummary = new MarketSegmentForecastGroupSummary();
        marketSegmentForecastGroupSummary.setMarketSegment(marketSegment1);
        marketSegmentForecastGroupSummary.setForecastGroup(forecastGroupSummary);
        marketSegmentForecastGroupSummary.setStatusId(1);
        tenantCrudService.save(marketSegmentForecastGroupSummary);
    }

    private MarketSegmentSummary createAndAddMarketSegmentSummary(String marketSegmentName) {
        MarketSegmentSummary marketSegmentSummary = new MarketSegmentSummary();
        marketSegmentSummary.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        marketSegmentSummary.setName(marketSegmentName);
        marketSegmentSummary.setCode(marketSegmentName);
        marketSegmentSummary.setStatusId(1);
        marketSegmentSummary.setEditable(1);
        return tenantCrudService.save(marketSegmentSummary);
    }
}
