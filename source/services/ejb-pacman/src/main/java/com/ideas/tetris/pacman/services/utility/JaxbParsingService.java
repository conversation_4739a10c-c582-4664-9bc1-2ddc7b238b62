package com.ideas.tetris.pacman.services.utility;


import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;

public class JaxbParsingService<T> {
    private Class<T> clazz;

    private JaxbParsingService() {
        //Needs a class type in order to work, so default constructor is made private
    }

    public JaxbParsingService(Class<T> clazz) {
        new JaxbParsingService<T>();
        this.clazz = clazz;
    }

    public T createJaxbObjectFromXmlString(String xmlString) throws JAXBException {
        JAXBContext jaxbContext = JAXBContext.newInstance(clazz);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
        StringReader reader = new StringReader(xmlString);
        return (T) unmarshaller.unmarshal(reader);
    }
}
