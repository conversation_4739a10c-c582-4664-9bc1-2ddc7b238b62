package com.ideas.tetris.pacman.services.reports.bookingpace;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.bookingpace.dto.BookingPaceDataLevelDTO;
import com.ideas.tetris.pacman.services.reports.bookingpace.dto.TotalGroupAndTransientDTO;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertLocalDateToJavaUtilDate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class BookingPaceService {

    private static final Logger LOGGER = Logger.getLogger(BookingPaceService.class.getName());

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    private CrudService getCrudServiceBean() {
        return crudService;
    }

    public void setCrudServiceBean(CrudService crudService) {
        this.crudService = crudService;
    }

    public List<TotalGroupAndTransientDTO> getTotalGroupAndTransientData(LocalDate arrivalDate, Integer paceDays, final boolean isPhysicalCapacityEnabled, boolean compRoomsFilter, boolean isInventoryLimitFeatureEnable) {

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("arrival_date", new java.sql.Date(convertLocalDateToJavaUtilDate(arrivalDate).getTime()))
                .and("pace_days", paceDays)
                .and("comp_rooms_filter", compRoomsFilter)
                .and("param_IsGroupProductInventoryLimitEnabled", isInventoryLimitFeatureEnable);
        try {
            return getCrudServiceBean().findByNativeQuery("select * from dbo.ufn_get_booking_pace_total_trans_group_report(:property_id, :arrival_date, :pace_days, :comp_rooms_filter, :param_IsGroupProductInventoryLimitEnabled) order by Occupancy_DT DESC",
                    queryParameters.parameters(), row -> getTotalGroupAndTransientDataRowMapper(isPhysicalCapacityEnabled, row));
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<>();
        }
    }

    public List<BookingPaceDataLevelDTO> getForecastGroupData(LocalDate arrivalDate, int paceDays, boolean compRoomsFilter) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("arrival_date", new java.sql.Date(convertLocalDateToJavaUtilDate(arrivalDate).getTime()))
                .and("pace_days", paceDays)
                .and("comp_rooms_filter", compRoomsFilter);
        try {
            return getCrudServiceBean().findByNativeQuery("select * from ufn_get_booking_pace_fg_report(:property_id, :arrival_date, :pace_days, :comp_rooms_filter) order by SnapShot_DTTM DESC",
                    queryParameters.parameters(), getBookingPaceDataLevelDTORowMapper());
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<>();
        }
    }

    public List<BookingPaceDataLevelDTO> getMarketSegmentData(LocalDate arrivalDate, int paceDays, boolean compRoomsFilter) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("arrival_date", new java.sql.Date(convertLocalDateToJavaUtilDate(arrivalDate).getTime()))
                .and("pace_days", paceDays)
                .and("comp_rooms_filter", compRoomsFilter);
        try {
            return getCrudServiceBean().findByNativeQuery("select * from ufn_get_booking_pace_ms_report(:property_id, :arrival_date, :pace_days, :comp_rooms_filter)",
                    queryParameters.parameters(), getBookingPaceDataLevelDTORowMapper());
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<>();
        }
    }

    private TotalGroupAndTransientDTO getTotalGroupAndTransientDataRowMapper(final boolean isPhysicalCapacityEnabled, final Object[] row) {
        final TotalGroupAndTransientDTO data = new TotalGroupAndTransientDTO();

        data.setOccupancyDate((Date) row[0]);
        data.setBusinessDate((Date) row[1]);
        data.setPaceDays((Integer) row[2]);
        data.setDow((String) row[3]);
        data.setGroupRoomSold((BigDecimal) row[4]);
        data.setTransientRoomSold((BigDecimal) row[5]);
        data.setTotalRoomSold((BigDecimal) row[6]);
        data.setTotalAccomCapacity((BigDecimal) row[7]);
        data.setAvailableCapacity((BigDecimal) row[8]);
        data.setTransientForecast((BigDecimal) row[9]);
        data.setGroupForecast((BigDecimal) row[10]);
        if (isPhysicalCapacityEnabled) {
            data.setTransientForecastPercentage((BigDecimal) row[16]);
            data.setGroupForecastPercentage((BigDecimal) row[17]);
            data.setHotelForecastPercentage((BigDecimal) row[15]);
        } else {
            data.setTransientForecastPercentage((BigDecimal) row[11]);
            data.setGroupForecastPercentage((BigDecimal) row[12]);
            data.setHotelForecastPercentage((BigDecimal) row[14]);
        }

        data.setHotelForecast((BigDecimal) row[13]);
        data.setInventoryLimit((Integer) row[18]);
        return data;
    }

    public List<BookingPaceDataLevelDTO> getRoomClassDTOs(LocalDate arrivalDate, Integer paceDays, String zeroCapacityRtValue) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        try {
            List<Object[]> reportData = getCrudServiceBean().findByNativeQuery("exec dbo.usp_booking_pace_rc_report " + propertyId + ",'" + new java.sql.Date(convertLocalDateToJavaUtilDate(arrivalDate).getTime()) + "'," + paceDays + ",'" + zeroCapacityRtValue + "'");
            List<BookingPaceDataLevelDTO> resultList = new ArrayList<>(reportData.size());
            reportData.stream().forEach(objects -> {
                BookingPaceDataLevelDTO dtoObject = new BookingPaceDataLevelDTO();
                dtoObject.setRoomSold((BigDecimal) objects[0]);
                dtoObject.setCaptureDate((Date) objects[1]);
                dtoObject.setPaceDays((Integer) objects[2]);
                dtoObject.setDow((String) objects[3]);
                dtoObject.setBusinessDate((Date) objects[4]);
                dtoObject.setLevelName((String) objects[5]);
                dtoObject.setForecast((BigDecimal) objects[6]);
                dtoObject.setAuthorizedCapacity((BigDecimal) objects[7]);
                dtoObject.setEffectiveCapacity((BigDecimal) objects[8]);
                resultList.add(dtoObject);
            });

            return resultList;
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<>();
        }
    }

    private RowMapper<BookingPaceDataLevelDTO> getBookingPaceDataLevelDTORowMapper() {
        return new RowMapper<>() {
            @Override
            public BookingPaceDataLevelDTO mapRow(Object[] row) {
                BookingPaceDataLevelDTO data = new BookingPaceDataLevelDTO();
                data.setCaptureDate((Date) row[1]);
                data.setBusinessDate((Date) row[4]);
                data.setRoomSold((BigDecimal) row[0]);
                data.setPaceDays((Integer) row[2]);
                data.setDow((String) row[3]);
                data.setLevelName((String) row[5]);
                data.setForecast((BigDecimal) row[6]);
                return data;
            }
        };
    }

    public List<BookingPaceDataLevelDTO> getRoomTypeDTOs(LocalDate arrivalDate, Integer paceDays, String zeroCapacityRtValue) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        try {
            List<Object[]> reportData = getCrudServiceBean().findByNativeQuery("exec dbo.usp_booking_pace_rt_report " + propertyId + ",'" + new java.sql.Date(convertLocalDateToJavaUtilDate(arrivalDate).getTime()) + "'," + paceDays + ",'" + zeroCapacityRtValue + "'");
            List<BookingPaceDataLevelDTO> resultList = new ArrayList<>(reportData.size());
            reportData.stream().forEach(objects -> {
                BookingPaceDataLevelDTO dtoObject = new BookingPaceDataLevelDTO();
                dtoObject.setRoomSold((BigDecimal) objects[0]);
                dtoObject.setCaptureDate((Date) objects[1]);
                dtoObject.setPaceDays((Integer) objects[2]);
                dtoObject.setDow((String) objects[3]);
                dtoObject.setBusinessDate((Date) objects[4]);
                dtoObject.setLevelName((String) objects[5]);
                dtoObject.setForecast((BigDecimal) objects[6]);
                dtoObject.setAuthorizedCapacity((BigDecimal) objects[7]);
                dtoObject.setEffectiveCapacity((BigDecimal) objects[8]);
                resultList.add(dtoObject);
            });

            return resultList;
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<>();
        }
    }

    public List<BookingPaceDataLevelDTO> geBusinessViewData(LocalDate arrivalDate, int paceDays, boolean compRoomsFilter) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("arrival_date", new java.sql.Date(convertLocalDateToJavaUtilDate(arrivalDate).getTime()))
                .and("pace_days", paceDays)
                .and("comp_rooms_filter", compRoomsFilter);
        try {
            return getCrudServiceBean().findByNativeQuery("select * from ufn_get_booking_pace_bv_report(:property_id, :arrival_date, :pace_days, :comp_rooms_filter) order by daystoArrival, Business_Group_Name",
                    queryParameters.parameters(), new RowMapper<>() {
                        @Override
                        public BookingPaceDataLevelDTO mapRow(Object[] row) {
                            BookingPaceDataLevelDTO data = new BookingPaceDataLevelDTO();
                            data.setPaceDays((Integer) row[0]);
                            data.setCaptureDate((Date) row[1]);
                            data.setDow((String) row[2]);
                            data.setLevelName((String) row[3]);
                            data.setRoomSold((BigDecimal) row[4]);
                            data.setForecast((BigDecimal) row[5]);
                            return data;
                        }
                    });
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<>();
        }
    }
}

