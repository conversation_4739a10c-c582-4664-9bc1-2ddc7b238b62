package com.ideas.tetris.pacman.services.license.feature;

import com.ideas.tetris.pacman.services.benefits.service.BenefitsMeasurementService;
import com.ideas.tetris.platform.common.license.entities.LicensePackage;
import com.ideas.tetris.platform.common.license.migration.actions.LicenseFeatureUpgradable;
import com.ideas.tetris.platform.common.license.util.LicenseFeatureConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class BenefitMeasurementFeature extends LicenseFeatureUpgradable {

    @Autowired
    private BenefitsMeasurementService benefitsMeasurementService;

    @Override
    public String getFeatureCode() {
        return LicenseFeatureConstants.BENEFIT_MEASUREMENT;
    }

    @Override
    protected void cleanUp(int propertyId, LicensePackage currentLicensePackage, LicensePackage newLicensePackage, Map<String, Object> featuresInputMapToDowngrade) {
        benefitsMeasurementService.deleteBenefits(propertyId);
    }
}
