package com.ideas.tetris.pacman.services.forecast.controller;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.forecast.InhouseExtensionForecastService;
import com.ideas.tetris.pacman.services.forecast.dto.InhouseExtensionForecastDto;
import com.ideas.tetris.pacman.services.forecast.dto.InhouseLosChgSummaryDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/data/analytics/mr")
public class InhouseExtensionForecastController {
    @Autowired
    private InhouseExtensionForecastService inhouseExtensionForecastService;

    @GetMapping("/inhouse_extension_fcst/{startDate}/{endDate}/v1")
    public List<InhouseExtensionForecastDto> getInhouseExtensionForecasts(@PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)LocalDate startDate, @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)LocalDate endDate){
        var propertyId = PacmanWorkContextHelper.getPropertyId();
        return inhouseExtensionForecastService.retrieveInhouseExtensionForecasts(propertyId,startDate,endDate);
    }

    @GetMapping("/inhouse_los_chg_summary/v1")
    public List<InhouseLosChgSummaryDto> getInhouseLosChgSummary(){
        var propertyId = PacmanWorkContextHelper.getPropertyId();
        return inhouseExtensionForecastService.retrieveInhouseLosChgSummaries(propertyId);
    }
}
