package com.ideas.tetris.pacman.services.reports.dataextraction;

import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionMarketSegment;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionReportDto;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionType;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.ReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.DataExtractionReportCriteria;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil.getText;


public class DataExtractionMarketSegmentConverter {
    private static List<Object> getMarketSegmentHeaderList(Language language, DataExtractionReportCriteria reportCriteria) {
        List<Object> headers = new ArrayList<>();
        if (reportCriteria.isShowLastYearData()) {
            headers.add(getText("common.propertyName", language));
            headers.add(getText("report.dow", language));
            headers.add(getText("occupancyDate", language));
            headers.add(getText("common.comparisonDateLastYear", language));
            headers.add(getText("common.ms", language));

            if (reportCriteria.isMarketSegmentRoomsSold() && reportCriteria.isMarketSegmentRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }
            if (reportCriteria.isMarketSegmentRoomsSold() && !reportCriteria.isMarketSegmentRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
            }
            if (!reportCriteria.isMarketSegmentRoomsSold() && reportCriteria.isMarketSegmentRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isMarketSegmentArrivals()) {
                headers.add(getText("arrivals", language) + " " + getText("common.thisYear", language));
                headers.add(getText("arrivals", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isMarketSegmentDepartures()) {
                headers.add(getText("common.departures", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.departures", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isMarketSegmentCancellations()) {
                headers.add(getText("cancelled", language) + " " + getText("common.thisYear", language));
                headers.add(getText("cancelled", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isMarketSegmentNoShow()) {
                headers.add(getText("noshow", language) + " " + getText("common.thisYear", language));
                headers.add(getText("noshow", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isMarketSegmentRevenue() && reportCriteria.isMarketSegmentRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
            }
            if (reportCriteria.isMarketSegmentRevenue() && !reportCriteria.isMarketSegmentRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
            }
            if (!reportCriteria.isMarketSegmentRevenue() && reportCriteria.isMarketSegmentRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isMarketSegmentOccupancyForecast()) {
                headers.add(getText("common.occupancyForecast", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.occupancyForecast", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isMarketSegmentADR()) {
                headers.add(getText("report.column.bookedAdr", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedAdr", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.forecastedAdr", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedAdr", language) + " " + getText("common.lastYearActual", language));
            }

        } else {

            headers.add(getText("common.propertyName", language));
            headers.add(getText("report.dow", language));
            headers.add(getText("occupancyDate", language));
            headers.add(getText("common.ms", language));

            if (reportCriteria.isMarketSegmentRoomsSold() && reportCriteria.isMarketSegmentRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }
            if (reportCriteria.isMarketSegmentRoomsSold() && !reportCriteria.isMarketSegmentRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
            }
            if (!reportCriteria.isMarketSegmentRoomsSold() && reportCriteria.isMarketSegmentRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isMarketSegmentArrivals()) {
                headers.add(getText("arrivals", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isMarketSegmentDepartures()) {
                headers.add(getText("common.departures", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isMarketSegmentCancellations()) {
                headers.add(getText("cancelled", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isMarketSegmentNoShow()) {
                headers.add(getText("noshow", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isMarketSegmentRevenue() && reportCriteria.isMarketSegmentRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
            }
            if (reportCriteria.isMarketSegmentRevenue() && !reportCriteria.isMarketSegmentRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
            }
            if (!reportCriteria.isMarketSegmentRevenue() && reportCriteria.isMarketSegmentRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isMarketSegmentOccupancyForecast()) {
                headers.add(getText("common.occupancyForecast", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isMarketSegmentADR()) {
                headers.add(getText("report.column.bookedAdr", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedAdr", language) + " " + getText("common.thisYear", language));
            }

        }

        return headers;
    }

    public static ReportSheet getMarketSegmentReportSheet(Map<DataExtractionType, List<DataExtractionReportDto>> records, ScheduledReport<DataExtractionReportCriteria> scheduledReport) {
        Language language = scheduledReport.getLanguage();
        DecimalFormat decimalFormat = ScheduledReportUtils.getLocaleDecimalFormat(language.getLocale());
        ReportSheet marketSegmentSheet = new ReportSheet(getText("common.ms", language));
        marketSegmentSheet.setReportTitle(getText("dataExtractionReport.title.at.market.segment.level", scheduledReport.getLanguage()));
        List<DataExtractionReportDto> dataExtractionReportDtoList = records.get(DataExtractionType.MARKET_SEGMENT);
        Object[] headerArray = getMarketSegmentHeaderList(scheduledReport.getLanguage(), scheduledReport.getReportCriteria()).toArray();
        DataExtractionReportCriteria reportCriteria = scheduledReport.getReportCriteria();
        for (int i = 0; i < headerArray.length; i++) {
            marketSegmentSheet.addColumn(String.class);
        }
        marketSegmentSheet.addHeaderRow(headerArray);
        dataExtractionReportDtoList.forEach(dto -> {
            List<Object> dataList = new ArrayList<Object>();
            DataExtractionMarketSegment data = (DataExtractionMarketSegment) dto;

            if (reportCriteria.isShowLastYearData()) {
                dataList.add(DataExtractionReportUtil.getStringValue(data.getPropertyName())); //  Property Name
                if (data.getDayOfWeek() != null && !data.getDayOfWeek().isEmpty()) {
                    dataList.add(getText(DataExtractionReportUtil.getStringValue(data.getDayOfWeek()).toLowerCase(), language)); // Day of Week
                } else {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getDayOfWeek())); // Day of Week
                }
                dataList.add(ScheduledReportUtils.getDateString(data.getOccupancyDate())); //  Occupancy Date
                dataList.add(ScheduledReportUtils.getDateString(data.getComparisonDateLastYear())); //  Comparison Date Last Year
                dataList.add(DataExtractionReportUtil.getStringValue(data.getMarketSegmentName())); //  Market Segment

                if (reportCriteria.isMarketSegmentRoomsSold() && reportCriteria.isMarketSegmentRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); //  Occupancy On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }
                if (reportCriteria.isMarketSegmentRoomsSold() && !reportCriteria.isMarketSegmentRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); //  Occupancy On Books Last Year Actual
                }
                if (!reportCriteria.isMarketSegmentRoomsSold() && reportCriteria.isMarketSegmentRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); //  Occupancy On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }

                if (reportCriteria.isMarketSegmentArrivals()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsThisYear())); //  Arrivals This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsLastYear())); //  Arrivals Last Year Actual
                }

                if (reportCriteria.isMarketSegmentDepartures()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesThisYear())); //  Departures This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesLastYear())); //  Departures Last Year Actual
                }

                if (reportCriteria.isMarketSegmentCancellations()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledThisYear())); //  Cancelled This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledLastYear())); //  Cancelled Last Year Actual
                }

                if (reportCriteria.isMarketSegmentNoShow()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowThisYear())); //  No Show This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowLastYear())); //  No Show Last Year Actual
                }

                if (reportCriteria.isMarketSegmentRevenue() && reportCriteria.isMarketSegmentRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); //  Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueLastYear(), decimalFormat)); //  Forecasted Room Revenue Last Year Actual
                }
                if (reportCriteria.isMarketSegmentRevenue() && !reportCriteria.isMarketSegmentRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); //  Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueLastYear(), decimalFormat)); //  Forecasted Room Revenue Last Year Actual
                }
                if (!reportCriteria.isMarketSegmentRevenue() && reportCriteria.isMarketSegmentRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); //  Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                }

                if (reportCriteria.isMarketSegmentOccupancyForecast()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOccupancyForecastThisYear(), decimalFormat)); //  Occupancy Forecast This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOccupancyForecastLastYear(), decimalFormat)); //  Occupancy Forecast Last Year Actual
                }

                if (reportCriteria.isMarketSegmentADR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRThisYear(), decimalFormat)); //  ADR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRLastYear(), decimalFormat)); //  ADR On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrThisYear(), decimalFormat)); //  ADR Forecast This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrLastYear(), decimalFormat)); //  ADR Forecast Last Year Actual
                }

            } else {

                dataList.add(DataExtractionReportUtil.getStringValue(data.getPropertyName())); //  Property Name
                if (data.getDayOfWeek() != null && !data.getDayOfWeek().isEmpty()) {
                    dataList.add(getText(DataExtractionReportUtil.getStringValue(data.getDayOfWeek()).toLowerCase(), language)); // Day of Week
                } else {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getDayOfWeek())); // Day of Week
                }
                dataList.add(ScheduledReportUtils.getDateString(data.getOccupancyDate())); //  Occupancy Date
                dataList.add(DataExtractionReportUtil.getStringValue(data.getMarketSegmentName())); //  Market Segment

                if (reportCriteria.isMarketSegmentRoomsSold() && reportCriteria.isMarketSegmentRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }
                if (reportCriteria.isMarketSegmentRoomsSold() && !reportCriteria.isMarketSegmentRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                }
                if (!reportCriteria.isMarketSegmentRoomsSold() && reportCriteria.isMarketSegmentRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }

                if (reportCriteria.isMarketSegmentArrivals()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsThisYear())); //  Arrivals This Year
                }

                if (reportCriteria.isMarketSegmentDepartures()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesThisYear())); //  Departures This Year
                }

                if (reportCriteria.isMarketSegmentCancellations()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledThisYear())); //  Cancelled This Year
                }

                if (reportCriteria.isMarketSegmentNoShow()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowThisYear())); //  No Show This Year
                }

                if (reportCriteria.isMarketSegmentRevenue() && reportCriteria.isMarketSegmentRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                }
                if (reportCriteria.isMarketSegmentRevenue() && !reportCriteria.isMarketSegmentRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                }
                if (!reportCriteria.isMarketSegmentRevenue() && reportCriteria.isMarketSegmentRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                }

                if (reportCriteria.isMarketSegmentOccupancyForecast()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOccupancyForecastThisYear(), decimalFormat)); //  Occupancy Forecast This Year
                }

                if (reportCriteria.isMarketSegmentADR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRThisYear(), decimalFormat)); //  ADR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrThisYear(), decimalFormat)); //  ADR Forecast This Year
                }


            }
            marketSegmentSheet.addRow(dataList.toArray());
        });
        return marketSegmentSheet;

    }
}
