package com.ideas.tetris.pacman.services.reports.dataextraction;

import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionBusinessView;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionForecastGroup;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionHotel;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionHotelCP;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionMarketSegment;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionReportDto;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionRoomClass;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionRoomType;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.JasperReportDataConverter;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.DataExtractionReportCriteria;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportData;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.service.JasperReportService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class DataExtractionReportService extends JasperReportService<ScheduledReportData, DataExtractionReportCriteria> {


    @Autowired
	private PacmanConfigParamsService configParamsService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Override
    protected JasperReportDataConverter<ScheduledReportData, DataExtractionReportCriteria> getJasperReportDataConverter() {
        return null;
    }

    @Override
    public String getReportTitle(ScheduledReport<DataExtractionReportCriteria> scheduledReport) {
        return null;
    }

    @Override
    protected ScheduledReportData retrieveData(ScheduledReport<DataExtractionReportCriteria> scheduledReport) {
        List<ScheduledReportSheet> sheetList = new LinkedList<ScheduledReportSheet>();
        // Get the criteria and build up the common parameters used by all queries
        DataExtractionReportCriteria criteria = scheduledReport.getReportCriteria();

        if (criteria.isHotel()) {
            // Add the hotel-specific parameters
            QueryParameter hotelQueryParameters = buildCommonParameters(criteria).and("comp1", criteria.getComp1())
                    .and("comp2", criteria.getComp2())
                    .and("comp3", criteria.getComp3())
                    .and("comp4", criteria.getComp4())
                    .and("comp5", criteria.getComp5())
                    .and("isSTLY", getIsSTLY2YOr19(criteria.isHotelRoomsSoldSTLY(), criteria.isHotelRevenueSTLY()))
                    .and("isST2Y", getIsSTLY2YOr19(criteria.isHotelRoomsSoldST2Y(), criteria.isHotelRevenueST2Y()))
                    .and("isST19", getIsSTLY2YOr19(criteria.isHotelRoomsSoldST19(), criteria.isHotelRevenueST19()))
                    .and("isY2019", criteria.isShowYear2019Data())
                    .and("isExcludeCompRoom", 0)
                    .and("includePseudoRT", 0)
                    .and("param_IsMarketPerformanceChecked", 0);

            // Hotel level has two versions of the stored procedure (CP & non-CP)
            List<DataExtractionReportDto> dataList = null;
            if (configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())) {
                hotelQueryParameters.and("param_IsGroupProductInventoryLimitEnabled", 0);
                dataList = execute(DataExtractionHotelCP.FIND_FOR_REPORT_CP, hotelQueryParameters);
                sheetList.add(new ScheduledReportSheet("dataExtractionReport.hotelLevel.title", dataList, DataExtractionHotelCP.class));
            } else {
                dataList = execute(DataExtractionHotel.FIND_FOR_REPORT, hotelQueryParameters);
                sheetList.add(new ScheduledReportSheet("dataExtractionReport.hotelLevel.title", dataList, DataExtractionHotel.class));
            }
        }

        // All non-Hotel extracts have the same parameters
        if (criteria.isRoomClass()) {
            QueryParameter roomClassParameters = buildCommonParameters(criteria)
                    .and("isSTLY", getIsSTLY2YOr19(criteria.isRoomClassRoomsSoldSTLY(), criteria.isRoomClassRevenueSTLY()))
                    .and("isST2Y", getIsSTLY2YOr19(criteria.isRoomClassRoomsSoldST2Y(), criteria.isRoomClassRevenueST2Y()))
                    .and("isST19", getIsSTLY2YOr19(criteria.isRoomClassRoomsSoldST19(), criteria.isRoomClassRevenueST19()))
                    .and("isY2019", criteria.isShowYear2019Data());
            roomClassParameters.and(":includePseudoRT", 0).and("isExcludeCompRoom", 0);
            List<DataExtractionReportDto> dataList = execute(DataExtractionRoomClass.FIND_FOR_REPORT, roomClassParameters);
            sheetList.add(new ScheduledReportSheet("dataExtractionReport.AtRoomClass.title", dataList, DataExtractionRoomClass.class));
        }

        if (criteria.isRoomType()) {
            QueryParameter roomTypeParameters = buildCommonParameters(criteria)
                    .and("isSTLY", getIsSTLY2YOr19(criteria.isRoomTypeRoomsSoldSTLY(), criteria.isRoomTypeRevenueSTLY()))
                    .and("isST2Y", getIsSTLY2YOr19(criteria.isRoomTypeRoomsSoldST2Y(), criteria.isRoomTypeRevenueST2Y()))
                    .and("isST19", getIsSTLY2YOr19(criteria.isRoomTypeRoomsSoldST19(), criteria.isRoomTypeRevenueST19()))
                    .and("isY2019", criteria.isShowYear2019Data());
            // Room Type level has two versions of the stored procedure (CP & non-CP)
            roomTypeParameters.and(":includePseudoRT", 0).and("isExcludeCompRoom", 0);
            List<DataExtractionReportDto> dataList = null;
            if (configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())) {
                dataList = execute(DataExtractionRoomType.FIND_FOR_REPORT_CP, roomTypeParameters);
            } else {
                dataList = execute(DataExtractionRoomType.FIND_FOR_REPORT, roomTypeParameters);
            }
            sheetList.add(new ScheduledReportSheet("dataExtractionReport.atRoomType.title", dataList, DataExtractionRoomType.class));
        }

        if (criteria.isForecastGroup()) {
            QueryParameter forecastGroupParameters = buildCommonParameters(criteria)
                    .and("isSTLY", getIsSTLY2YOr19(criteria.isForecastGroupRoomsSoldSTLY(), criteria.isForecastGroupRevenueSTLY()))
                    .and("isST2Y", getIsSTLY2YOr19(criteria.isForecastGroupRoomsSoldST2Y(), criteria.isForecastGroupRevenueST2Y()))
                    .and("isST19", getIsSTLY2YOr19(criteria.isForecastGroupRoomsSoldST19(), criteria.isForecastGroupRevenueST19()))
                    .and("isY2019", criteria.isShowYear2019Data())
                    .and("isExcludeCompRoom", 0)
                    .and("totalRateEnabled", 0);
            List<DataExtractionReportDto> dataList = execute(DataExtractionForecastGroup.FIND_FOR_REPORT, forecastGroupParameters);
            sheetList.add(new ScheduledReportSheet("dataExtractionReport.ForecastGroup.title", dataList, DataExtractionForecastGroup.class));
        }

        if (criteria.isMarketSegment()) {
            QueryParameter marketSegmentParameters = buildCommonParameters(criteria)
                    .and("isSTLY", getIsSTLY2YOr19(criteria.isMarketSegmentRoomsSoldSTLY(), criteria.isMarketSegmentRevenueSTLY()))
                    .and("isST2Y", getIsSTLY2YOr19(criteria.isMarketSegmentRoomsSoldST2Y(), criteria.isMarketSegmentRevenueST2Y()))
                    .and("isST19", getIsSTLY2YOr19(criteria.isMarketSegmentRoomsSoldST19(), criteria.isMarketSegmentRevenueST19()))
                    .and("isY2019", criteria.isShowYear2019Data())
                    .and("inventoryGroupId", -1)
                    .and("includeDiscontinuedMS", 0)
                    .and("isExcludeCompRoom", 0)
                    .and("isConfigForInventoryGroupAtStlyAndSt2yEnabled", 0)
                    .and("totalRateEnabled", 0);
            List<DataExtractionReportDto> dataList = execute(DataExtractionMarketSegment.FIND_FOR_REPORT, marketSegmentParameters);
            sheetList.add(new ScheduledReportSheet("dataExtractionReport.MarketSegment.title", dataList, DataExtractionMarketSegment.class));
        }

        if (criteria.isBusinessView()) {
            QueryParameter businessViewParameters = buildCommonParameters(criteria)
                    .and("isSTLY", getIsSTLY2YOr19(criteria.isBusinessViewRoomsSoldSTLY(), criteria.isBusinessViewRevenueSTLY()))
                    .and("isST2Y", getIsSTLY2YOr19(criteria.isBusinessViewRoomsSoldST2Y(), criteria.isBusinessViewRevenueST2Y()))
                    .and("isST19", getIsSTLY2YOr19(criteria.isBusinessViewRoomsSoldST19(), criteria.isBusinessViewRevenueST19()))
                    .and("isY2019", criteria.isShowYear2019Data())
                    .and("inventoryGroupId", -1)
                    .and("isExcludeCompRoom", 0)
                    .and("includeDiscontinuedMS", 0)
                    .and("isConfigForInventoryGroupAtStlyAndSt2yEnabled", 0)
                    .and("totalRateEnabled",0);
            List<DataExtractionReportDto> dataList = execute(DataExtractionBusinessView.FIND_FOR_REPORT, businessViewParameters);
            sheetList.add(new ScheduledReportSheet("dataExtractionReport.BusinessView.title", dataList, DataExtractionBusinessView.class));
        }
        populateReportCriteria(criteria);
        ScheduledReportData data = new ScheduledReportData("dataExtractionReport.title.at.hotel.level", sheetList);
        return data;
    }

    private Integer getIsSTLY2YOr19(Boolean isRoomsSold, Boolean isRevenue) {
        return (isRoomsSold || isRevenue) ? 1 : 0;
    }

    private List<DataExtractionReportDto> execute(String query, QueryParameter queryParameter) {
        return tenantCrudService.findByNamedQuery(query, queryParameter.parameters());
    }

    private QueryParameter buildCommonParameters(DataExtractionReportCriteria criteria) {
        return QueryParameter.with("propertyId", criteria.getPropertyId())
                .and("recordTypeId", RecordType.T2SNAP_RECORD_TYPE_ID)
                .and("processStatusId", ProcessStatus.SUCCESSFUL)
                .and("startDate", criteria.getStartDate().toDate())
                .and("endDate", criteria.getEndDate().toDate())
                .and("isRollingDate", criteria.getIsRollingDate())
                .and("rollingStartDate", criteria.getRollingStartDate())
                .and("rollingEndDate", criteria.getRollingEndDate())
                .and("usePhysicalCapacity", criteria.getUsePhysicalCapacity())
                .and("includeZeroCapacityRT", 0)
                .and("productId1", -1)
                .and("productId2", -1)
                .and("productId3", -1)
                .and("productId4", -1)
                .and("productId5", -1)
                .and("productId6", -1)
                .and("productId7", -1)
                .and("productId8", -1)
                .and("productId9", -1)
                .and("productId10", -1)
                .and("productId11", -1)
                .and("productId12", -1)
                .and("productId13", -1)
                .and("productId14", -1)
                .and("productId15", -1)
                .and("productId16", -1)
                .and("productId17", -1)
                .and("productId18", -1)
                .and("productId19", -1)
                .and("productId20", -1)
                .and("productId21", -1)
                .and("productId22", -1)
                .and("productId23", -1)
                .and("productId24", -1)
                .and("productId25", -1);
    }

    private void populateReportCriteria(DataExtractionReportCriteria reportCriteria) {

        String propertyId = reportCriteria.getPropertyId().toString();
        String userId = String.valueOf(reportCriteria.getUserId());
        String baseCurrency = reportCriteria.getCurrency();
        Integer rolling = new Integer(reportCriteria.getIsRollingDate());
        String rollingStartDate = reportCriteria.getRollingStartDate();
        String rollingEndDate = reportCriteria.getRollingEndDate();

        String sql = "select * from dbo.ufn_get_filter_selection " +
                "('" + propertyId + "'," + userId + ",'" + baseCurrency + "'," +
                "'" + rolling + "'," +
                "'" + " " + "'," +
                "'" + " " + "'," +
                "'" + " " + "'," +
                "'" + " " + "'," +
                "'" + " " + "'," +
                "'" + " " + "'," +
                "'" + " " + "'," +
                "'" + " " + "'," +
                "'" + rollingStartDate + "'," +
                "'" + rollingEndDate + "'," +
                "'" + " " + "'," +
                "'" + " " + "'," +
                "'" + " " + "'," +
                "'" + " " + "'," +
                "'" + " " + "'," +
                "'" + " " + "')";

        List<Object[]> resultList = tenantCrudService.findByNativeQuery(sql);
        if (resultList != null) {
            Object[] result = resultList.get(0);
            reportCriteria.setPropertyName((String) result[0]);
            reportCriteria.setCreatedBy((String) result[1]);
            reportCriteria.setCreatedOn(new DateTime());
            reportCriteria.setStartDate(new LocalDate(result[3]));
            reportCriteria.setEndDate(new LocalDate(result[4]));

        }
    }

    public PacmanConfigParamsService getConfigParamsService() {
        return configParamsService;
    }

    public void setConfigParamsService(PacmanConfigParamsService configParamsService) {
        this.configParamsService = configParamsService;
    }

    public CrudService getTenantCrudService() {
        return tenantCrudService;
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public List<DataExtractionHotel> getDataExtractionHotels(int propertyId, Date startDate, Date endDate) {
        return tenantCrudService.findByNamedQuery(DataExtractionHotel.FIND_FOR_REPORT, getQueryParams(propertyId, startDate, endDate).parameters());
    }

    public QueryParameter getQueryParams(int propertyId, Date startDate, Date endDate) {
        return QueryParameter.with("propertyId", propertyId)
                .and("recordTypeId", RecordType.T2SNAP_RECORD_TYPE_ID)
                .and("processStatusId", ProcessStatus.SUCCESSFUL)
                .and("startDate", startDate)
                .and("endDate", endDate)
                .and("isRollingDate", 0)
                .and("rollingStartDate", null)
                .and("rollingEndDate", null)
                .and("comp1", -1)
                .and("comp2", -1)
                .and("comp3", -1)
                .and("comp4", -1)
                .and("comp5", -1)
                .and("isSTLY", 0)
                .and("isST2Y", 0)
                .and("usePhysicalCapacity", 1)
                .and("isExcludeCompRoom", 0)
                .and("isST19", 0)
                .and("isY2019", 0)
                .and("includeZeroCapacityRT", 1)
                .and("includePseudoRT", 1)
                .and("param_IsMarketPerformanceChecked", 0);
    }
}
