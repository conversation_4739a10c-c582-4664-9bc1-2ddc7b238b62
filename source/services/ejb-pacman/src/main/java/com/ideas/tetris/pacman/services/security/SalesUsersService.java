package com.ideas.tetris.pacman.services.security;

import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;

import javax.inject.Inject;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class SalesUsersService {
    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;

    public int calculateFirstPossibleUserID() {
        Object firstInactiveUserID = getFirstInactiveUserID();
        if (firstInactiveUserID != null) {
            return ((BigInteger) firstInactiveUserID).intValue();
        }
        Object maxUserID = getMaxUserID();
        return maxUserID == null ? 1 : ((BigInteger) maxUserID).intValue() + 1;
    }

    protected Object getMaxUserID() {
        return globalCrudService.findByNativeQuerySingleResult("select max(Sales_Users_ID) from Sales_Users", null);
    }

    protected Object getFirstInactiveUserID() {
        return globalCrudService.findByNativeQuerySingleResult("select min(Sales_Users_ID) from Sales_Users where Active=0", null);
    }

    public void addSalesUser(int userId, String firstName, String lastName, String email) {
        if (isUserPresent(email)) {
            throw new TetrisException(ErrorCode.USER_WITH_SAME_EMAIL_ID_ALREADY_EXISTS, "Email already exists: " + email);
        }
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("userId", userId);
        parameters.put("firstName", firstName);
        parameters.put("lastName", lastName);
        parameters.put("email", email);
        globalCrudService.executeUpdateByNativeQuery("delete Sales_Users where Sales_Users_ID = " + userId);
        globalCrudService.executeUpdateByNativeQuery("insert into Sales_Users values (:userId, :firstName, :lastName, :email, 1)", parameters);
    }

    public void saveSalesUser(String firstName, String lastName, String email, boolean active) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("email", email);
        int shouldBeActive = active == true ? 1 : 0;
        if (!active || isUserPresent(email)) {
            globalCrudService.executeUpdateByNativeQuery("update Sales_Users set Active= " + shouldBeActive + " where Email_Address=:email", parameters);
        } else {
            int userId = calculateFirstPossibleUserID();
            addSalesUser(userId, firstName, lastName, email);
        }
    }

    protected boolean isUserPresent(String email) {
        Integer count = (Integer) globalCrudService.findByNativeQuerySingleResult("select count(*) from Sales_Users where Email_Address = '" + email + "'", null);
        return count > 0 ? true : false;
    }

    public Object getSalesUserIdFor(String email) {
        return globalCrudService.findByNativeQuerySingleResult("select Sales_Users_ID from Sales_Users where Email_Address = '" + email + "'", null);
    }
}
