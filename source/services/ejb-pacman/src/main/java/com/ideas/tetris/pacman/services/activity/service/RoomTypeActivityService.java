package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.RoomTypeCache;
import com.ideas.tetris.pacman.services.accommodation.RoomTypeVendorMappingCache;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.activity.converter.PaceActivityConverter;
import com.ideas.tetris.pacman.services.activity.converter.RoomTypeActivityConverter;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceAccomActivity;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.componentrooms.entity.CRAccomActivity;
import com.ideas.tetris.pacman.services.dashboard.dto.AccomActivityBatchDto;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.opera.DataLoadSummaryService;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.TableBatchAware;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Provides REST access to the AccomActivity information. It extends the
 * ActivityService to provide the standard set of endpoints used by the activity
 * data.
 */
@Component
@Transactional
public class RoomTypeActivityService extends PaceActivityService<AccomActivity, PaceAccomActivity> {

    public static final String PROPERTY_ID = "propertyId";
    private static final Logger LOGGER = Logger.getLogger(RoomTypeActivityService.class);
    private static final String ACCOM_CODES = "accomCodes";

    @Autowired
    private DataLoadSummaryService summaryService;

    @RoomTypeActivityConverter.Qualifier
    @Autowired
	@Qualifier("roomTypeActivityConverter")
	private RoomTypeActivityConverter roomTypeActivityConverter;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
	private DateService dateService;

    @Autowired
	private RoomTypeCache roomTypeCache;

    @Autowired
	private RoomTypeVendorMappingCache roomTypeVendorMappingCache;

    @Autowired
	private FileMetadataService fileMetadataService;

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> save(Integer id, Map<String, Object> dto, String correlationId, boolean isPast) {
        Map<String, Object> saved = super.save(id, dto, correlationId, isPast);
        updateOverBookingConfigurationNewRoomType();
        return saved;
    }

    @Override
    protected Comparator<Map<String, Object>> getComparator() {
        return null;
    }

    @Override
    public List<Map<String, Object>> save(List<Map<String, Object>> dtos, String correlationId, boolean isPast) {
        return saveAndRunCalculations(dtos, correlationId, isPast, true);
    }

    @Override
    public void saveWithoutPace(List<Map<String, Object>> dtos, String correlationId, boolean isPast) {
        saveAndRunCalculations(dtos, correlationId, isPast, false);
    }

    //FIXME: Remove the timeout override once this step has been optimized
    @Justification("Zero-filling steps are failing fairly consistently in produciton.  The solution may require a more significant effort.  Put the timeouts in place until we have a permanent solution.")
    @Transactional(propagation = Propagation.REQUIRED, timeout = 600)
    public String zeroFilling(FileMetadata fileMetadata) {
        DateTime startOccupancyDate = new DateTime(fileMetadata.getSnapshotDt()).plusDays(-fileMetadata.getPastWindowSize());
        DateTime endOccupancyDate = new DateTime(fileMetadata.getSnapshotDt()).plusDays(fileMetadata.getFutureWindowSize() - 1);
        int countOfMktSegAccomActivity = tenantCrudService.executeUpdateByNamedQuery(AccomActivity.INSERT_ZERO_FILL_ACCOM_ACTIVITY,
                QueryParameter.with("startDate", startOccupancyDate.toString(DateTimeFormat.forPattern("yyyy-MM-dd")))
                        .and("endDate", endOccupancyDate.toString(DateTimeFormat.forPattern("yyyy-MM-dd")))
                        .and("fileMetaDataId", fileMetadata.getId())
                        .parameters());
        return "AccomActivity:" + countOfMktSegAccomActivity;
    }

    public String selectiveZeroFilling(List<String> accomTypeCodes, DateTime startOccupancyDate, DateTime endOccupancyDate, FileMetadata fileMetadata) {
        int countOfMktSegAccomActivity = tenantCrudService.executeUpdateByNamedQuery(AccomActivity.INSERT_ZERO_FILL_ACCOM_ACTIVITY_BY_ACCOM_TYPES,
                QueryParameter.with(START_DATE, startOccupancyDate.toString(DateTimeFormat.forPattern(YYYY_MM_DD)))
                        .and(END_DATE, endOccupancyDate.toString(DateTimeFormat.forPattern(YYYY_MM_DD)))
                        .and(FILE_META_DATA_ID_CAPTIALIZED_D, fileMetadata.getId())
                        .and(ACCOM_CODES, accomTypeCodes)
                        .parameters());
        return "AccomActivity:" + countOfMktSegAccomActivity;
    }


    public String roomTypeCapacityCarryForward(String fileLocation) {
        FileMetadata fileMetadata = fileMetadataService.findByFileLocation(fileLocation);
        DateTime endOccupancyDate = new DateTime(fileMetadata.getSnapshotDt()).plusDays(fileMetadata.getFutureWindowSize() - 1);
        int countOfCarryForwardRecords = 0;
        if (hasRecordsToBeZeroFilled(endOccupancyDate.toDate(), fileMetadata.getId())) {
            countOfCarryForwardRecords = tenantCrudService.executeUpdateByNamedQuery(AccomActivity.ROOM_TYPE_CAPACITY_CARRY_FORWARD,
                    QueryParameter.with("targetOccupancyDate",
                                    endOccupancyDate.toString(DateTimeFormat.forPattern("yyyy-MM-dd")))
                            .and("fileMetadataId", fileMetadata.getId())
                            .parameters());
        }
        return "AccomActivity rows affected : " + countOfCarryForwardRecords;
    }

    public void updateAccomCapacitiesFromActivity(String correlationId) {
        FileMetadata fileMetadata = fileMetadataService.findByFileLocation(correlationId);
        Date caughtUpDate = dateService.getCaughtUpDate();
        int count = tenantCrudService.executeUpdateByNamedQuery(AccomType.UPDATE_ACCOM_CAPACITY_FROM_ACTIVITY,
                QueryParameter.with("snapshotDate", caughtUpDate).and("fileMetadataId", fileMetadata.getId()).parameters());

        roomTypeCache.remove(fileMetadata.getPropertyId());
        roomTypeVendorMappingCache.remove(fileMetadata.getPropertyId());

        LOGGER.info("Upated Accom capacities: " + count);
    }

    private boolean hasRecordsToBeZeroFilled(Date occupancyDate, Integer fileMetadataId) {
        return (Integer) tenantCrudService.findByNamedQuerySingleResult(
                AccomActivity.COUNT_MISSING_ACTIVITY_BY_OCC_DATE_AND_FILEMEATADATA,
                QueryParameter.with("occupancyDate", DateUtil.removeTimeFromDate(occupancyDate))
                        .and("fileMetadataId", fileMetadataId).parameters()) > 0;
    }

    private List<Map<String, Object>> saveAndRunCalculations(List<Map<String, Object>> dtos,
                                                             String correlationId, boolean isPast, boolean savePace) {
        LOGGER.info("Starting convert roomTypeActivity: " + dtos.size());
        List<AccomActivity> entities = saveEntities(dtos, correlationId, isPast);

        if (savePace) {
            saveActivityPace(entities);
        }

        updateOverBookingConfigurationNewRoomType();

        LOGGER.info("Completed roomTypeActivity: " + dtos.size());
        return dtos;
    }

    private void updateOverBookingConfigurationNewRoomType() {
        summaryService.updateOverBookingConfigurationNewRoomType();
    }

    private void saveActivityPace(List<AccomActivity> entities) {
        // Save the Pace for the entities
        List<PaceAccomActivity> paceEntities = roomTypeActivityConverter.convertToPaceEntities(entities);
        if (CollectionUtils.isNotEmpty(paceEntities)) {
            tenantCrudService.save(paceEntities);
        }
    }

    @Override
    protected List<AccomActivity> saveEntities(List<Map<String, Object>> dtos, String correlationId, boolean isPast) {
        LOGGER.info("Starting save of entities: " + dtos.size());
        List<AccomActivity> entities = convertDtosToEntities(dtos, correlationId);

        tenantCrudService.save(entities);

        LOGGER.info("Completed save of entities: " + dtos.size());
        return entities;
    }

    private List<AccomActivity> convertDtosToEntities(List<Map<String, Object>> dtos, String correlationId) {
        return getConverter().convertFromMapAll(dtos, correlationId);
    }

    @Override
    protected Class<AccomActivity> getEntityClass() {
        return AccomActivity.class;
    }

    @Override
    protected String getDateRangeQuery() {
        return AccomActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID;
    }

    @Override
    protected String deleteDateRangeQuery() {
        return AccomActivity.DELETE_BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID;
    }

    @Override
    protected String getPaceDateRangeQuery() {
        return PaceAccomActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID;
    }

    @Override
    protected String deletePaceDateRangeQuery() {
        return PaceAccomActivity.DELETE_BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID;
    }

    @Override
    protected List<TableBatchAware> convertToTableBatch(List<AccomActivity> entities, boolean isCDP) {
        return entities.stream().map(activity ->
                AccomActivityBatchDto.builder()
                        .accomActivityId(activity.getId())
                        .accomTypeId(activity.getAccomTypeId())
                        .propertyId(activity.getPropertyId())
                        .fileMetadataId(activity.getFileMetadataId())
                        .accomCapacity(activity.getAccomCapacity())
                        .roomsSold(activity.getRoomsSold())
                        .roomsNotAvailableMaintenance(activity.getRoomsNotAvailableMaintenance())
                        .roomsNotAvailableOther(activity.getRoomsNotAvailableOther())
                        .arrivals(activity.getArrivals())
                        .departures(activity.getDepartures())
                        .cancellations(activity.getCancellations())
                        .noShows(activity.getNoShows())
                        .roomRevenue(activity.getRoomRevenue())
                        .foodRevenue(activity.getFoodRevenue())
                        .totalRevenue(activity.getTotalRevenue())
                        .totalProfit(activity.getTotalProfit())
                        .createDate(activity.getCreateDate())
                        .occupancyDate(activity.getOccupancyDate())
                        .snapShotDate(activity.getSnapShotDate())
                        .lastUpdatedDate(activity.getLastUpdatedDate())
                        .isCPD(isCDP)
                        .isRoomTypeActivity(true)
                        .build()
        ).collect(Collectors.toList());
    }

    @Override
    protected PaceActivityConverter<AccomActivity, PaceAccomActivity> getConverter() {
        return roomTypeActivityConverter;
    }

    public AccomActivity findActivityForDateByRoomType(LocalDate date, String accomTypeCode) {
        return tenantCrudService.findByNamedQuerySingleResult(
                AccomActivity.BY_OCCUPANCY_DATE_AND_ACCOMTYPE_CODE_AND_PROPERTY_ID,
                QueryParameter.with(
                                PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and("occupancyDate", date.toDate())
                        .and("accomTypeCode", accomTypeCode)
                        .parameters());
    }

    private List<AccomActivity> findActivityForDateByRoomType(LocalDate date, List<Integer> accomTypeIds) {
        return tenantCrudService.findByNamedQuery(
                AccomActivity.BY_OCCUPANCY_DATE_AND_ACCOMTYPE_IDS_AND_PROPERTY_ID,
                QueryParameter.with(
                                PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and("occupancyDate", date.toDate())
                        .and("accomTypeIds", accomTypeIds)
                        .parameters());
    }

    public Map<Integer, AccomActivity> findActivityRemainingCapacityForDateByRoomType(LocalDate date, List<Integer> accomTypeIds, Map<Integer, Integer> overbookingsByAccomTypeIds) {
        Map<Integer, AccomActivity> accomActivitiesByAccomTypeIds = new HashMap<>();
        if (isComponentRoomsEnabled()) {
            List<Object[]> availableRooms = retrieveAvailableRooms(date, accomTypeIds);

            if (areRoomsAvailable(availableRooms)) {
                availableRooms.forEach(objects -> {
                    Integer accomTypeId = (Integer) objects[1];
                    Integer overbookingValue = overbookingsByAccomTypeIds.get(accomTypeId);
                    AccomActivity accomActivity = createAccomActivity(objects, overbookingValue);

                    accomActivitiesByAccomTypeIds.put(accomTypeId, accomActivity);
                });
            }
        } else {
            List<AccomActivity> accomActivitiesByRoomType = findActivityForDateByRoomType(date, accomTypeIds);
            accomActivitiesByRoomType.forEach(accomActivity -> {
                Integer overbookingValue = overbookingsByAccomTypeIds.get(accomActivity.getAccomTypeId());
                Integer remainingCapacity = evaluateRemainingCapacity(accomActivity, overbookingValue);
                accomActivity.setRemainingCapacity(new BigDecimal(remainingCapacity));

                accomActivitiesByAccomTypeIds.put(accomActivity.getAccomTypeId(), accomActivity);
            });

        }
        return accomActivitiesByAccomTypeIds;
    }

    private Integer evaluateRemainingCapacity(AccomActivity accomActivity, Integer overbookingValue) {
        return overbookingValue == null ?
                (accomActivity.getAccomCapacity().intValue()
                        - accomActivity.getRoomsNotAvailableTotal().intValue() - accomActivity.getRoomsSold().intValue()) :
                (accomActivity.getAccomCapacity().intValue()
                        + overbookingValue
                        - accomActivity.getRoomsNotAvailableTotal().intValue() - accomActivity.getRoomsSold().intValue());
    }

    private AccomActivity createAccomActivity(Object[] objects, Integer overbooking) {

        AccomActivity accomActivity = new AccomActivity();
        accomActivity.setRoomsSold((BigDecimal) objects[2]);
        accomActivity.setAccomCapacity((BigDecimal) objects[3]);
        accomActivity.setRoomsNotAvailableMaintenance((BigDecimal) objects[4]);
        accomActivity.setRoomsNotAvailableOther((BigDecimal) objects[5]);
        BigDecimal remainingCapacity = (BigDecimal) objects[6];
        int remainingCapacityOvbk = remainingCapacity.intValue() + overbooking;
        accomActivity.setRemainingCapacity(new BigDecimal(remainingCapacityOvbk));
        return accomActivity;
    }

    private boolean areRoomsAvailable(List<Object[]> roomsAvailableList) {
        return CollectionUtils.isNotEmpty(roomsAvailableList);
    }

    private List<Object[]> retrieveAvailableRooms(LocalDate date, List<Integer> accomTypeIds) {
        return tenantCrudService.findByNamedQuery(
                CRAccomActivity.BY_OCCUPANCY_DATE_ACCOM_TYPE_IDS_AND_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and("occupancyDate", date.toDate())
                        .and("accomTypeIds", accomTypeIds)
                        .parameters());
    }

    private boolean isComponentRoomsEnabled() {
        return configParamsService.getParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED);
    }

    public void updatePace(String propertyId, Date startDate, Date endDate, FileMetadata fileMetadata) {

        // Fencing the use of corrected query for an iteration. TODO: Remove PaceAccomActivity.MERGE_PACE afterwards
        String mergePaceQueryName = SystemConfig.useCorrectedMergeAccomPaceQuery() ? PaceAccomActivity.MERGE_PACE_CORRECTED_QUERY : PaceAccomActivity.MERGE_PACE;

        int numberOfUpdatedRecords = tenantCrudService.executeUpdateByNamedQuery(mergePaceQueryName,
                QueryParameter.with("startDate", startDate)
                        .and("endDate", endDate)
                        .and(PROPERTY_ID, propertyId)
                        .and("snapshotDt", fileMetadata.getSnapshotDt())
                        .and("snapshotDtTm", fileMetadata.getSnapshotDtTm())
                        .and("fileMetaDataId", fileMetadata.getId())
                        .parameters());


        LOGGER.info("Finished save of pace, new pace records: " + numberOfUpdatedRecords);
    }

    public Integer updateNonPace(Integer propertyId, Date startDate, Date endDate, FileMetadata fileMetadata) {

        int numberOfUpdatedRecords = tenantCrudService.executeUpdateByNamedQuery(AccomActivity.UPDATE_ACCOM_ACTIVITY_FROM_MKT_ACCOM_ACTIVITY,
                QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and("propertyId", propertyId)
                        .and(FILE_META_DATA_ID_CAPTIALIZED_D, fileMetadata.getId())
                        .parameters());

        LOGGER.info("Finished updating accom_activity. Records updated: " + numberOfUpdatedRecords);
        return numberOfUpdatedRecords;
    }

    public Integer insertNonPaceForLostData(final Integer propertyId, final Date startDate, final Date endDate, final FileMetadata fileMetadata) {

        int numberOfUpdatedRecords = tenantCrudService.executeUpdateByNamedQuery(AccomActivity.INSERT_ACCOM_ACTIVITY_FROM_MKT_ACCOM_ACTIVITY,
                QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and("propertyId", propertyId)
                        .and(FILE_META_DATA_ID_CAPTIALIZED_D, fileMetadata.getId())
                        .and("snapshotDt", fileMetadata.getSnapshotDt())
                        .parameters());

        LOGGER.info("Finished inserting accom_activity. Records updated: " + numberOfUpdatedRecords);
        return numberOfUpdatedRecords;
    }

    public Map<LocalDate, BigDecimal> findPhysicalCapacityForOccupancyDateRange(Date startDate, Date endDate, List<Integer> displayStatusIds) {
        List<Object[]> capacitySummary = tenantCrudService.findByNamedQuery(
                AccomActivity.PHYSICAL_CAPACITY_BY_OCCUPANCY_DATE_RANGE,
                QueryParameter.with("displayStatusIds", displayStatusIds).and("startDate", startDate).and("endDate", endDate).parameters()
        );

        return capacitySummary.stream().collect(Collectors.toMap(summary -> LocalDate.fromDateFields((Date) summary[0]), summary -> (BigDecimal) summary[1]));
    }
}
