package com.ideas.tetris.pacman.services.reports.mcatmapping.dto;

import com.ideas.tetris.pacman.services.scheduledreport.reportapi.annotations.ColumnHeader;

public class MCATMappingDTO {

    @ColumnHeader(titleKey = "rate.code", order = 1)
    String rateCode;
    @ColumnHeader(titleKey = "market.segment", order = 2)
    String marketSegment;
    @ColumnHeader(titleKey = "original.market.segment", order = 4)
    String originalMarketSegment;
    @ColumnHeader(titleKey = "forecast.group", order = 3)
    String forecastGroup;

    public String getOriginalMarketSegment() {
        return originalMarketSegment;
    }

    public void setOriginalMarketSegment(String originalMarketSegmentIn) {
        this.originalMarketSegment = originalMarketSegmentIn;
    }

    public String getRateCode() {
        return rateCode;
    }

    public void setRateCode(String rateCodeIn) {
        this.rateCode = rateCodeIn;
    }

    public String getMarketSegment() {
        return marketSegment;
    }

    public void setMarketSegment(String marketSegment) {
        this.marketSegment = marketSegment;
    }

    public String getForecastGroup() {
        return forecastGroup;
    }

    public void setForecastGroup(String forecastGroup) {
        this.forecastGroup = forecastGroup;
    }

}
