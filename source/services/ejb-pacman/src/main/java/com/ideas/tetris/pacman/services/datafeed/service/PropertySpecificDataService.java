package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.roa.attribute.service.ROAPropertyAttributeService;

import javax.inject.Inject;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class PropertySpecificDataService {

    @Autowired
    ROAPropertyAttributeService roaPropertyAttributeService;

    private static final String PRICE_DROP_MIN_REV_GAIN = "PRICE_DROP_MIN_REV_GAIN";
    private static final String PRICE_DROP_MAX_VALUE = "PRICE_DROP_MAX_VALUE";
    private static final String PRICE_DROP_MIN_DTA = "PRICE_DROP_MIN_DTA";


    public void addPropertySpecificData(String minDTA, String priceDropGain, String priceDropMaxValue) {

        HashMap<String, String> attributesWithValue = new HashMap<>();
        attributesWithValue.put(PRICE_DROP_MIN_DTA, minDTA);
        attributesWithValue.put(PRICE_DROP_MIN_REV_GAIN, priceDropGain);
        attributesWithValue.put(PRICE_DROP_MAX_VALUE, priceDropMaxValue);
        roaPropertyAttributeService.saveMultiplePropertyAttributes(attributesWithValue);
    }


    public Boolean removePropertySpecificData() {
        roaPropertyAttributeService.deletePropertyAttribute(getPriceDropAttributeNames());
        return true;
    }

    private List<String> getPriceDropAttributeNames() {
        return Arrays.asList(PRICE_DROP_MIN_DTA, PRICE_DROP_MIN_REV_GAIN, PRICE_DROP_MAX_VALUE);
    }
}
