package com.ideas.tetris.pacman.services.vendor;

public enum NGIType {
    ACTIVITY_DATA_CDP("ACTIVITY_DATA_CDP"),
    OXI_SC("OXI_SC"),
    OXI_PMS("OXI_PMS"),
    FOLS("FOLS"),
    FUNCTION_SPACE_CLIENT("FUNCTION_SPACE_CLIENT"),
    DEMAND360_DATA_LOAD("DEMAND360_DATA_LOAD"),
    STR_MONTHLY("STR_MONTHLY"),
    QUALIFIED_RATES("QUALIFIED_RATES"),
    UNQUALIFIED_RATES("UNQUALIFIED_RATES"),
    ACTIVITY_DATA("ACTIVITY_DATA"),
    STR_WEEKLY("STR_WEEKLY"),
    HTNG("HTNG"),
    RRA("RRA"),
    APALEO("APALEO"),
    OHIP("OHIP"),
    SALES_AND_CATERING("SALES_AND_CATERING"),
    SIHOT("SIHOT"),
    IDEASFIL<PERSON>("IDeaSFile");

    private String name;

    NGIType(String name) {
        this.name = name;
    }
}
