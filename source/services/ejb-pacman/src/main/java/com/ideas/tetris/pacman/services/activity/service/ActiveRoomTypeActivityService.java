package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.tetris.pacman.services.activity.converter.ActiveRoomTypeActivityConverter;
import com.ideas.tetris.pacman.services.activity.converter.PaceActivityConverter;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceAccomActivity;
import com.ideas.tetris.pacman.services.dashboard.dto.AccomActivityBatchDto;
import com.ideas.tetris.platform.common.crudservice.TableBatchAware;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Component
@Transactional
public class ActiveRoomTypeActivityService extends PaceActivityService<AccomActivity, PaceAccomActivity> {

    @ActiveRoomTypeActivityConverter.Qualifier
    @Autowired
	@Qualifier("activeRoomTypeActivityConverter")
	private ActiveRoomTypeActivityConverter activeRoomTypeActivityConverter;

    @Override
    protected Comparator<Map<String, Object>> getComparator() {
        return ((lhs, rhs) -> {

            int ret = ((Date) lhs.get(ActiveRoomTypeActivityConverter.OCCUPANCY_DATE)).compareTo((Date) rhs.get(ActiveRoomTypeActivityConverter.OCCUPANCY_DATE));
            if (ret != 0) {
                return ret;
            }

            ret = ((String) lhs.get(ActiveRoomTypeActivityConverter.ROOM_CLASS_NAME)).compareTo((String) rhs.get(ActiveRoomTypeActivityConverter.ROOM_CLASS_NAME));
            if (ret != 0) {
                return ret;
            }

            return ((String) lhs.get(ActiveRoomTypeActivityConverter.ROOM_TYPE_NAME)).compareTo((String) rhs.get(ActiveRoomTypeActivityConverter.ROOM_TYPE_NAME));
        });
    }


    @Override
    protected Class<AccomActivity> getEntityClass() {
        return AccomActivity.class;
    }

    @Override
    protected String getDateRangeQuery() {
        return AccomActivity.ACTIVE_BY_DATE_RANGE;
    }

    @Override
    protected String deleteDateRangeQuery() {
        return null;
    }


    @Override
    protected PaceActivityConverter<AccomActivity, PaceAccomActivity> getConverter() {
        return activeRoomTypeActivityConverter;
    }

    @Override
    protected String getPaceDateRangeQuery() {
        return null;
    }

    @Override
    protected String deletePaceDateRangeQuery() {
        return null;
    }

    @Override
    protected List<TableBatchAware> convertToTableBatch(List<AccomActivity> entities, boolean isCDP) {
        return entities.stream().map(activity ->
                AccomActivityBatchDto.builder()
                        .accomActivityId(activity.getId())
                        .accomTypeId(activity.getAccomTypeId())
                        .propertyId(activity.getPropertyId())
                        .fileMetadataId(activity.getFileMetadataId())
                        .accomCapacity(activity.getAccomCapacity())
                        .roomsSold(activity.getRoomsSold())
                        .roomsNotAvailableMaintenance(activity.getRoomsNotAvailableMaintenance())
                        .roomsNotAvailableOther(activity.getRoomsNotAvailableOther())
                        .arrivals(activity.getArrivals())
                        .departures(activity.getDepartures())
                        .cancellations(activity.getCancellations())
                        .noShows(activity.getNoShows())
                        .roomRevenue(activity.getRoomRevenue())
                        .foodRevenue(activity.getFoodRevenue())
                        .totalRevenue(activity.getTotalRevenue())
                        .totalProfit(activity.getTotalProfit())
                        .createDate(activity.getCreateDate())
                        .occupancyDate(activity.getOccupancyDate())
                        .snapShotDate(activity.getSnapShotDate())
                        .lastUpdatedDate(activity.getLastUpdatedDate())
                        .isCPD(isCDP)
                        .build()
        ).collect(Collectors.toList());
    }
}
