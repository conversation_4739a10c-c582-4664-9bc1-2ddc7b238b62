package com.ideas.tetris.pacman.services.analytics.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class ProjectedBookingPace {
    private int fg;
    private int ac;
    private LocalDate occupancyDate;
    private LocalDate captureDate;
    private double pickup;
    private double forecast;
    private double solds;
    private double pdta;
    private double pcurrentDta;
    private double p0;
    private int dta;
    private int currentDta;
    private boolean soldsGreaterThanPickup;

}
