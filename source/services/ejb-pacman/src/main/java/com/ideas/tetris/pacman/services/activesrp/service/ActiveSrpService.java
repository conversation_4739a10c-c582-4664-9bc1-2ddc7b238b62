package com.ideas.tetris.pacman.services.activesrp.service;

import com.ideas.tetris.pacman.services.activesrp.entity.ActiveSrp;
import org.joda.time.LocalDate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Transactional
public class ActiveSrpService {

    public void filterActiveSrpWithBigSeasons(LocalDate optimizationStartDate, LocalDate optimizationEndDate, Map<?, List<ActiveSrp>> activeSeasons) {
        List<?> headersWithBigActiveSeasons = activeSeasons.entrySet().stream()
                .filter(entry -> (entry.getValue().size() == 1) &&
                        !entry.getValue().get(0).getStartDate().isAfter(optimizationStartDate)
                        && !entry.getValue().get(0).getEndDate().isBefore(optimizationEndDate))
                .map(e -> e.getKey()).collect(Collectors.toList());

        activeSeasons.keySet().removeAll(headersWithBigActiveSeasons);
    }

    public Set<String> getActiveSrpsBeyondOptimizationWindow(LocalDate optimizationStartDate, LocalDate optimizationEndDate, Map<String, List<ActiveSrp>> activeSeasons) {
        Set<String> results = new HashSet<>();
        final Set<String> keySet = activeSeasons.keySet();
        for (String key : keySet
        ) {
            final List<ActiveSrp> activeSrps = activeSeasons.get(key);
            if (srpSesonBeyondOptimizationWindow(optimizationStartDate, optimizationEndDate, activeSrps)) {
                results.add(key);
            }
        }
        return results;
    }

    private boolean srpSesonBeyondOptimizationWindow(LocalDate optimizationStartDate, LocalDate optimizationEndDate, List<ActiveSrp> activeSrps) {
        boolean srpSesonBeyondOptimizationWindow = false;
        for (ActiveSrp activeSrp : activeSrps) {
            srpSesonBeyondOptimizationWindow = isSrpSesonBeyondOptimizationWindow(optimizationStartDate, optimizationEndDate, activeSrp);
            if (!srpSesonBeyondOptimizationWindow) {
                break;
            }
        }
        return srpSesonBeyondOptimizationWindow;
    }

    private boolean isSrpSesonBeyondOptimizationWindow(LocalDate optimizationStartDate, LocalDate optimizationEndDate, ActiveSrp activeSrp) {
        return activeSrp.getEndDate().isBefore(optimizationStartDate) || activeSrp.getStartDate().isAfter(optimizationEndDate);
    }
}
