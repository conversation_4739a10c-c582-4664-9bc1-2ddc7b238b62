package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.services.property.dto.ExtractDetails;
import com.ideas.tetris.pacman.services.property.dto.PacmanExtractDetails;
import com.ideas.tetris.pacman.services.property.dto.WebRateExtractDetails;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.Future;


import org.springframework.aop.SpringProxy;
public interface ExtractDetailsServiceLocal extends SpringProxy {

    boolean areBdeExtractsAvailableForPastDays(Integer propertyId, int dayCount);

    ExtractDetails getExtractDetails(Integer propertyId);

    ExtractDetails getExtractDetailsWithFilePaths(Integer propertyId);

    ExtractDetails getExtractDetails(Property property);

    void putExtractDetails(Integer propertyId, ExtractDetails extractDetails);

    WebRateExtractDetails getWebRateExtractDetailsWithFilePaths(Integer propertyId);

    WebRateExtractDetails getWebRateExtractDetails(Integer propertyId);

    WebRateExtractDetails getWebRateExtractDetails(Property property);

    WebRateExtractDetails moveWebRateIncomingExtractToArchive(Integer propertyId, File incomingWebRateExtract);

    ExtractDetails rebuildExtractDetails(Integer propertyId);

    ExtractDetails rebuildExtractDetails(Property property);

    // used for newly added properties that won't show up in the ConsolidatedPropertyView yet
    ExtractDetails rebuildExtractDetails(Integer propertyId, String clientCode, String propertyCode);

    WebRateExtractDetails rebuildWebRateExtractDetails(Integer propertyId);

    WebRateExtractDetails rebuildWebRateExtractDetails(Property property);

    // used for newly added properties that won't show up in the ConsolidatedPropertyView yet
    WebRateExtractDetails rebuildWebRateExtractDetails(Integer propertyId, String clientCode, String propertyCode);

    // Leaving for old installation status invocation. Eventually deprecate as properties with Gbs of extracts will
    // timeout. This needs to be asynchronous. Use rollbackPropertyExtracts
    ExtractDetails moveArchivedExtractsToIncoming(Integer propertyId) throws IOException;

    ExtractDetails moveIncomingExtractsToArchive(Integer propertyId) throws IOException;

    // Leaving for old installation status invocation. Eventually deprecate as properties with Gbs of extracts will
    // timeout. This needs to be asynchronous. Use rollbackPropertyExtracts
    WebRateExtractDetails moveArchivedWebRateExtractsToIncoming(Integer propertyId) throws IOException;

    WebRateExtractDetails moveIncomingWebRateExtractsToArchive(Integer propertyId) throws IOException;

    PacmanExtractDetails getPacmanExtractDetails(Integer propertyId);

    // This is the preferred rollback method going forward as will not timeout on properties with Gbs of extracts
    Future<String> rollbackPropertyExtracts(JobStepContext jobStepContext, ExtractDetails extractDetails,
                                            WebRateExtractDetails webRateExtractDetails);

    void deleteExtracts( String clientCode,
                         String propertyCode);
}
