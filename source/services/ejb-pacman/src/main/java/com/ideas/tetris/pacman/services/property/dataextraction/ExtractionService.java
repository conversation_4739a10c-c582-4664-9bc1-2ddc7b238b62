package com.ideas.tetris.pacman.services.property.dataextraction;

import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.property.dto.ExtractType;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.util.remotesystem.IRemoteFileHandler;
import com.ideas.tetris.platform.common.util.remotesystem.RemoteFileHandlerFactory;
import com.ideas.tetris.platform.common.util.zip.ZipDirectory;
import com.ideas.tetris.platform.common.util.zip.ZipDirectoryService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.getremotefile.request.v1.GetRemoteFileType;
import org.apache.commons.io.FileUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.log4j.Logger;
import org.jboss.soa.esb.util.RemoteFileSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.net.URISyntaxException;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

@Transactional
@Component
public abstract class ExtractionService {
    private static final Logger LOGGER = Logger.getLogger(ExtractionService.class);
    private static final String FTP_LARGE_FILE_ISSUE = "Connection reset";
    private static final String FTP_TMP_SUFFIX = ".rosettaPart";
    static final int FTP_PORT = 21;
    // Backslash or forward slash
    static final Pattern REGEX_FILE_SEP = Pattern.compile("[\\/\\\\]");
    // support multiple extracts in single day span
    private static final String FTP_DATE_FORMAT = "yyyy_MM_dd_HH_mm_ss";

    @Autowired
	protected ZipDirectoryService zipDirectoryService; //TODO fix after fixing ZipDirectories

    // Exposed for testing
    public String getTempZipDirectory() {
        return SystemConfig.getExtractFtpFolder();
    }

	public String getTempZipDirectoryForLinux() {
		return SystemConfig.getExtractFtpFolderLinux();
	}
	public ExtractionService() {
	}

	@PostConstruct
	public void init() {
		// Need a folder sql server and our app (tetris services) can both write to
		// System getProperty SYS_PROP_TEMP_DIR : java.io.tmpdir
		File file = new File(getTempZipDirectory());
		if (!file.exists()) {
			if (!file.mkdirs()) {
				LOGGER.fatal("Cannot create folder for ftp'ing extract files. My word.");
			}
		} else {
			File[] existingFtps = file.listFiles(new StagnantFileFilter());
			if (existingFtps.length > 0) {
				whackStagnantFtpExtracts(existingFtps);
			}
		}
	}

	/*
	 * In a perfect world, we do not get here. We have had instances where service blew
	 * hard and cruft remained
	 */
	private void whackStagnantFtpExtracts(File[] files) {
		LOGGER.warn("Deleting stagnant extract ftp files");
		for (File cruftyFile : files) {
			if (cruftyFile.isFile()) {
				LOGGER.warn("Whacking file " + cruftyFile.getName() + ": " + cruftyFile.delete());
			} else {
				LOGGER.warn("Whacking folder " + cruftyFile.getName());
				try {
					FileUtils.deleteDirectory(cruftyFile);
				} catch (IOException e) {
					LOGGER.warn("Whack foiled for " + cruftyFile.getName(), e);
				}
			}
		}
	}

	protected String getNowFormattedForFile() {
		return getNowFormattedForFile(getNowAsMilliseconds());
	}

    protected String getNowFormattedForFile(long milliseconds) {
        return new SimpleDateFormat(FTP_DATE_FORMAT).format(new Date(milliseconds));
    }

    protected long getNowAsMilliseconds() {
        return new Date().getTime();
    }

    protected File getTempExtractFolder(ExtractType extractType, long millis) {
        File dbFolder = new File(getTempZipDirectory()+ "/" +
				extractType.getFolderName() + millis);
        if (!dbFolder.mkdir()) {
            LOGGER.error("Failed to delete" + extractType.getDescription() + " folder for extraction: " + dbFolder.getAbsolutePath());
        }
        return dbFolder;
    }

    protected String getOsBasedTempExtractFolderPath(ExtractType extractType, long millis) {
        return getTempZipDirectoryForLinux()+ "/" +
				extractType.getFolderName() + millis;
    }

	protected File createZipFile(String zipFilename, ZipDirectory zipDirectory) {
		return createZipFile(zipFilename, Arrays.asList(zipDirectory), false);
	}

	protected File createZipFile(String zipFilename, List<ZipDirectory> zipDirectories, boolean skipAnalyticsFullFiles) {
		File zippedFile = null;
		try {
			if (!zipDirectories.isEmpty()) {
				zippedFile = zipDirectoryService.zipDirectories(zipDirectories, zipFilename, skipAnalyticsFullFiles);
			} else {
				LOGGER.warn(getLogPreface() + "there were no data set files to zip up");
			}
		} catch (TetrisException e) {
			LOGGER.error(getLogPreface() + "I/O issue zipping data sets", e);
		}
		return zippedFile;
	}

	protected String ftpZipFile(String filename, String emailAddress) {
		GetRemoteFileType remoteFile = getRemoteFile(emailAddress);
		// Do not prepend remoteFile getProtocol() :// which would make it a link
		String ftpFileLink = remoteFile.getHostName() + remoteFile.getRemoteFolder() +
				filename.substring(filename.lastIndexOf('/'));

		try {
			uploadFtpFile(filename, remoteFile);
		} catch (TetrisException e) {
			// So large file were blowing up with SocketConnection : Connection reset
			// Essentially, whole file there but not renamed from temp *.rosettaPart
			// Would be nice to compare file sizes but do not want to download file
			if (null != e.getCause() && e.getCause().getMessage().contains(FTP_LARGE_FILE_ISSUE)) {
				try {
					// Implies darn big file so little extra time to release server locks shouldn't hurt
					Thread.sleep(2000);
					renameFtpFileIfSimplyPremature(filename, remoteFile);
				} catch (Exception ex) {
					LOGGER.error(getLogPreface() + "unable to quick fix issue with file rename " + ftpFileLink, ex);
					ftpFileLink = null;
				}
			} else {
				LOGGER.error(getLogPreface() + "unable to ftp zipped data to " + ftpFileLink, e);
				ftpFileLink = null;
			}
		} catch (URISyntaxException e) {
			LOGGER.error(getLogPreface() + "ftp file syntax whack: " + ftpFileLink, e);
			ftpFileLink = null;
		}
		return ftpFileLink;
	}

	protected void uploadFtpFile(String localFile, GetRemoteFileType remoteFile) throws URISyntaxException {
		long startTime = System.currentTimeMillis();
		RemoteFileHandlerFactory factory = new RemoteFileHandlerFactory();
		IRemoteFileHandler remoteFileHandler = factory.createRemoteFileHandler(remoteFile);
		remoteFileHandler.pushFileToServer(localFile);
		remoteFileHandler.quit();
		LOGGER.info("Closed a remote file system connection");
		long stopTime = System.currentTimeMillis();
		LOGGER.info(getLogPreface() + "ftp upload in ms: " + (stopTime - startTime));
	}

	/*
	 * FTP server may have been locking the file as replicating to other nodes but jboss code
	 * still needs to rename from temp name to final name
	 * From Sir Ben Moppett: if the Replication to the other FTP node is locking the file just
	 * before its being renamed so I added *.rosettaPart files to the DFS exclusion list
	 */
	 void renameFtpFileIfSimplyPremature(String localFile, GetRemoteFileType remoteFile)
			throws URISyntaxException {
		RemoteFileHandlerFactory factory = new RemoteFileHandlerFactory();
		IRemoteFileHandler remoteFileHandler = factory.createRemoteFileHandler(remoteFile);
		// Not sure i can grab file metadata w/o downloading file. Not interested in that albatross
		// and large files are what predicates this issue
		String fileOnly = localFile.substring(localFile.lastIndexOf('/') + 1);
		boolean wasSuccessful = remoteFileHandler.renameRemote(fileOnly + FTP_TMP_SUFFIX, fileOnly);
		remoteFileHandler.quit();
		LOGGER.info("Closed a remote file system connection");
		LOGGER.info(getLogPreface() + "ftp file rename successful: " + wasSuccessful);
	}

	private GetRemoteFileType getRemoteFile(String emailAddress) {
		GetRemoteFileType remoteFile = new GetRemoteFileType();
		remoteFile.setProtocol(RemoteFileSystem.FTP_PROTOCOL);
		remoteFile.setPort(FTP_PORT);
		remoteFile.setHostName(SystemConfig.getFtpServer());
		remoteFile.setRemoteFolder(getFtpDestinationFolder(emailAddress));
		remoteFile.setUserName(SystemConfig.getFtpServerUsername());
		remoteFile.setPassword(SystemConfig.getFtpServerPassword());
		return remoteFile;
	}

	/*
	 * Looked to integrate in RemoteFileHandler but too tightly composited about JBoss RemoteFileSystem
	 */
	protected void createFtpDirectoryIfNotExists(String emailAddress) throws IOException {
		FTPClient ftpClient = new FTPClient();
		String destinationDir = getFtpDestinationFolder(emailAddress);
		try {
			ftpClient.connect(SystemConfig.getFtpServer(), FTP_PORT);
			boolean wasSuccess = ftpClient.login(SystemConfig.getFtpServerUsername(),
					SystemConfig.getFtpServerPassword());
			LOGGER.info(getLogPreface() + "could FTPClient log onto ftp server: " + wasSuccess);
			wasSuccess = ftpClient.changeWorkingDirectory(destinationDir);
			if (!wasSuccess) {
				wasSuccess = ftpClient.makeDirectory(destinationDir);
				LOGGER.info(getLogPreface() + "FTPClient created ftp directory '" + destinationDir + "': " + wasSuccess);
			}
		} finally {
			if (null != ftpClient) {
				ftpClient.logout();
				ftpClient.disconnect();
			}
		}
	}

	public String getFtpDestinationFolder(String emailAddress) {
		return "/" + SystemConfig.getFtpRequestsFolder() + "/" + emailAddress;
	}

	/*
	 * Help distinguished between concurrent requests
	 */
	protected String getLogPreface() {
		return MessageFormat.format("Data extraction ({0} | {1} - {2}): ",
				PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode(),
				PacmanWorkContextHelper.getUserId());
	}

	private static final class StagnantFileFilter implements FileFilter {
		private static final int DAYS_TIL_ROTTEN = 2;
		private Date stagnantDate;

        StagnantFileFilter() {
            stagnantDate = DateUtil.addDaysToDate(new Date(), -DAYS_TIL_ROTTEN);
        }

        @Override
        public boolean accept(File file) {
            return file.lastModified() < stagnantDate.getTime();
        }
    }
}
