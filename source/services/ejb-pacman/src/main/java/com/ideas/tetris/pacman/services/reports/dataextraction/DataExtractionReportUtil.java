package com.ideas.tetris.pacman.services.reports.dataextraction;


import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.DataExtractionReportCriteria;
import org.joda.time.LocalDate;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

import static com.ideas.tetris.pacman.services.reports.ReportService.SCHEDULE_PARAM_SEPARATOR;

public class DataExtractionReportUtil {
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");

    public static DataExtractionReportCriteria populateReportCriteria(Map<String, String> reportParamsMap) throws ParseException {
        DataExtractionReportCriteria criteria = new DataExtractionReportCriteria();
        criteria.setCurrency(Constants.BASECURRENCY);
        criteria.setHotel(Boolean.valueOf(reportParamsMap.get("IsHotel")));
        criteria.setHotelADR(Boolean.valueOf(reportParamsMap.get("IsADR_Hotel")));
        criteria.setHotelArrivals(Boolean.valueOf(reportParamsMap.get("IsArr_Hotel")));
        criteria.setHotelBAR(Boolean.valueOf(reportParamsMap.get("IsBAR_Hotel")));
        criteria.setHotelBARByDay(Boolean.valueOf(reportParamsMap.get("isBARByDay_Hotel")));
        criteria.setHotelCancellations(Boolean.valueOf(reportParamsMap.get("IsCncel_Hotel")));
        criteria.setHotelCapacity(Boolean.valueOf(reportParamsMap.get("IsCap_Hotel")));
        criteria.setHotelDepartures(Boolean.valueOf(reportParamsMap.get("IsDep_Hotel")));
        criteria.setHotelLRV(Boolean.valueOf(reportParamsMap.get("IsLRV_Hotel")));
        criteria.setHotelNoShow(Boolean.valueOf(reportParamsMap.get("IsNS_Hotel")));
        criteria.setHotelOccupancyForecast(Boolean.valueOf(reportParamsMap.get("IsOcFcst_Hotel")));
        criteria.setHotelOutOfOrder(Boolean.valueOf(reportParamsMap.get("IsOOO_Hotel")));
        criteria.setHotelOverbooking(Boolean.valueOf(reportParamsMap.get("IsOvbk_Hotel")));
        criteria.setHotelRevenue(Boolean.valueOf(reportParamsMap.get("IsRev_Hotel")));
        criteria.setHotelRevenueSTLY(Boolean.valueOf(reportParamsMap.get("IsRev_STLY_Hotel")));
        criteria.setHotelRevPAR(Boolean.valueOf(reportParamsMap.get("IsRevPar_Hotel")));
        criteria.setHotelRoomsNotAvailableOutOfOrder(Boolean.valueOf(reportParamsMap.get("IsRNAO_Hotel")));
        criteria.setHotelRoomsSold(Boolean.valueOf(reportParamsMap.get("IsRS_Hotel")));
        criteria.setHotelRoomsSoldSTLY(Boolean.valueOf(reportParamsMap.get("IsRS_STLY_Hotel")));
        criteria.setHotelSpecialEvent(Boolean.valueOf(reportParamsMap.get("IsSplEvt_Hotel")));
        criteria.setHotelSystemUnconstrainedDemand(Boolean.valueOf(reportParamsMap.get("IsSysUnConDmdTtl_Hotel")));
        criteria.setHotelUserUnconstrainedDemand(Boolean.valueOf(reportParamsMap.get("IsUserUnConDmdTotal_Hotel")));
        criteria.setHotelWash(Boolean.valueOf(reportParamsMap.get("param_IsWashAtHotelLevel")));
        criteria.setHotelInventoryLimit(Boolean.valueOf(reportParamsMap.get("IsInventoryLimit_Hotel")));

        criteria.setRoomClass(Boolean.valueOf(reportParamsMap.get("IsRC")));
        criteria.setRoomClassADR(Boolean.valueOf(reportParamsMap.get("IsADR_RC")));
        criteria.setRoomClassArrivals(Boolean.valueOf(reportParamsMap.get("IsArr_RC")));
        criteria.setRoomClassBAR(Boolean.valueOf(reportParamsMap.get("IsBAR_RC")));
        criteria.setRoomClassDepartures(Boolean.valueOf(reportParamsMap.get("IsDep_RC")));
        criteria.setRoomClassCancellations(Boolean.valueOf(reportParamsMap.get("IsCncel_RC")));
        criteria.setRoomClassCapacity(Boolean.valueOf(reportParamsMap.get("IsCap_RC")));
        criteria.setRoomClassLRV(Boolean.valueOf(reportParamsMap.get("IsLRV_RC")));
        criteria.setRoomClassNoShow(Boolean.valueOf(reportParamsMap.get("IsNS_RC")));
        criteria.setRoomClassOccupancyForecast(Boolean.valueOf(reportParamsMap.get("IsOcFcst_RC")));
        criteria.setRoomClassOutOfOrder(Boolean.valueOf(reportParamsMap.get("IsOOO_RC")));
        criteria.setRoomClassRevenue(Boolean.valueOf(reportParamsMap.get("IsRev_RC")));
        criteria.setRoomClassRevenueSTLY(Boolean.valueOf(reportParamsMap.get("IsRev_STLY_RC")));
        criteria.setRoomClassRevPAR(Boolean.valueOf(reportParamsMap.get("IsRevPar_RC")));
        criteria.setRoomClassRoomsNotAvailableOutOfOrder(Boolean.valueOf(reportParamsMap.get("IsRNAO_RC")));
        criteria.setRoomClassRoomsSold(Boolean.valueOf(reportParamsMap.get("IsRS_RC")));
        criteria.setRoomClassRoomsSoldSTLY(Boolean.valueOf(reportParamsMap.get("IsRS_STLY_RC")));
        criteria.setRoomClassSystemUnconstrainedDemand(Boolean.valueOf(reportParamsMap.get("IsSysUnConDmd_RC")));
        criteria.setRoomClassUserUnconstrainedDemand(Boolean.valueOf(reportParamsMap.get("IsUserUnConDmd_RC")));

        criteria.setRoomType(Boolean.valueOf(reportParamsMap.get("IsRT")));
        criteria.setRoomTypeADR(Boolean.valueOf(reportParamsMap.get("IsADR_RT")));
        criteria.setRoomTypeArrivals(Boolean.valueOf(reportParamsMap.get("IsArr_RT")));
        criteria.setRoomTypeBAR(Boolean.valueOf(reportParamsMap.get("IsBAR_RT")));
        criteria.setRoomTypeCapacity(Boolean.valueOf(reportParamsMap.get("IsCap_RT")));
        criteria.setRoomTypeCancellations(Boolean.valueOf(reportParamsMap.get("IsCncel_RT")));
        criteria.setRoomTypeDepartures(Boolean.valueOf(reportParamsMap.get("IsDep_RT")));
        criteria.setRoomTypeNoShow(Boolean.valueOf(reportParamsMap.get("IsNS_RT")));
        criteria.setRoomTypeOutOfOrder(Boolean.valueOf(reportParamsMap.get("IsOOO_RT")));
        criteria.setRoomTypeOverbooking(Boolean.valueOf(reportParamsMap.get("IsOvbk_RT")));
        criteria.setRoomTypeRevenue(Boolean.valueOf(reportParamsMap.get("IsRev_RT")));
        criteria.setRoomTypeRevenueSTLY(Boolean.valueOf(reportParamsMap.get("IsRev_STLY_RT")));
        criteria.setRoomTypeRevPAR(Boolean.valueOf(reportParamsMap.get("IsRevPar_RT")));
        criteria.setRoomTypeRoomsNotAvailableOutOfOrder(Boolean.valueOf(reportParamsMap.get("IsRNAO_RT")));
        criteria.setRoomTypeRoomsSold(Boolean.valueOf(reportParamsMap.get("IsRS_RT")));
        criteria.setRoomTypeRoomsSoldSTLY(Boolean.valueOf(reportParamsMap.get("IsRS_STLY_RT")));

        criteria.setMarketSegment(Boolean.valueOf(reportParamsMap.get("IsMS")));
        criteria.setMarketSegmentADR(Boolean.valueOf(reportParamsMap.get("IsADR_MS")));
        criteria.setMarketSegmentArrivals(Boolean.valueOf(reportParamsMap.get("IsArr_MS")));
        criteria.setMarketSegmentCancellations(Boolean.valueOf(reportParamsMap.get("IsCncel_MS")));
        criteria.setMarketSegmentDepartures(Boolean.valueOf(reportParamsMap.get("IsDep_MS")));
        criteria.setMarketSegmentNoShow(Boolean.valueOf(reportParamsMap.get("IsNS_MS")));
        criteria.setMarketSegmentOccupancyForecast(Boolean.valueOf(reportParamsMap.get("IsOcFcst_MS")));
        criteria.setMarketSegmentRevenue(Boolean.valueOf(reportParamsMap.get("IsRev_MS")));
        criteria.setMarketSegmentRoomsSold(Boolean.valueOf(reportParamsMap.get("IsRS_MS")));
        criteria.setMarketSegmentRevenueSTLY(Boolean.valueOf(reportParamsMap.get("IsRev_STLY_MS")));
        criteria.setMarketSegmentRoomsSoldSTLY(Boolean.valueOf(reportParamsMap.get("IsRS_STLY_MS")));

        criteria.setForecastGroup(Boolean.valueOf(reportParamsMap.get("IsFG")));
        criteria.setForecastGroupADR(Boolean.valueOf(reportParamsMap.get("IsADR_FG")));
        criteria.setForecastGroupArrivals(Boolean.valueOf(reportParamsMap.get("IsArr_FG")));
        criteria.setForecastGroupCancellations(Boolean.valueOf(reportParamsMap.get("IsCncel_FG")));
        criteria.setForecastGroupDepartures(Boolean.valueOf(reportParamsMap.get("IsDep_FG")));
        criteria.setForecastGroupNoShow(Boolean.valueOf(reportParamsMap.get("IsNS_FG")));
        criteria.setForecastGroupOccupancyForecast(Boolean.valueOf(reportParamsMap.get("IsOcFcst_FG")));
        criteria.setForecastGroupRevenue(Boolean.valueOf(reportParamsMap.get("IsRev_FG")));
        criteria.setForecastGroupRevenueSTLY(Boolean.valueOf(reportParamsMap.get("IsRev_STLY_FG")));
        criteria.setForecastGroupRoomsSold(Boolean.valueOf(reportParamsMap.get("IsRS_FG")));
        criteria.setForecastGroupRoomsSoldSTLY(Boolean.valueOf(reportParamsMap.get("IsRS_STLY_FG")));
        criteria.setForecastGroupSystemGroupWashPerFG(Boolean.valueOf(reportParamsMap.get("IsSysGrpWashPer_FG")));
        criteria.setForecastGroupSystemUnconstrainedDemand(Boolean.valueOf(reportParamsMap.get("IsSysUnDmd_FG")));
        criteria.setForecastGroupUserUnconstrainedDemand(Boolean.valueOf(reportParamsMap.get("IsUserUnDmd_FG")));

        criteria.setBusinessView(Boolean.valueOf(reportParamsMap.get("IsBV")));
        criteria.setBusinessViewADR(Boolean.valueOf(reportParamsMap.get("IsADR_BV")));
        criteria.setBusinessViewArrivals(Boolean.valueOf(reportParamsMap.get("IsArr_BV")));
        criteria.setBusinessViewCancellations(Boolean.valueOf(reportParamsMap.get("IsCncel_BV")));
        criteria.setBusinessViewDepartures(Boolean.valueOf(reportParamsMap.get("IsDep_BV")));
        criteria.setBusinessViewNoShow(Boolean.valueOf(reportParamsMap.get("IsNS_BV")));
        criteria.setBusinessViewOccupancyForecast(Boolean.valueOf(reportParamsMap.get("IsOcFcst_BV")));
        criteria.setBusinessViewRevenue(Boolean.valueOf(reportParamsMap.get("IsRev_BV")));
        criteria.setBusinessViewRoomsSold(Boolean.valueOf(reportParamsMap.get("IsRS_BV")));
        criteria.setBusinessViewRevenueSTLY(Boolean.valueOf(reportParamsMap.get("IsRev_STLY_BV")));
        criteria.setBusinessViewRoomsSoldSTLY(Boolean.valueOf(reportParamsMap.get("IsRS_STLY_BV")));

        criteria.setCompetitor(Boolean.valueOf(reportParamsMap.get("Competitor")));
        criteria.setComp1(Integer.parseInt(reportParamsMap.get("Comp1")));
        criteria.setComp2(Integer.parseInt(reportParamsMap.get("Comp2")));
        criteria.setComp3(Integer.parseInt(reportParamsMap.get("Comp3")));
        criteria.setComp4(Integer.parseInt(reportParamsMap.get("Comp4")));
        criteria.setComp5(Integer.parseInt(reportParamsMap.get("Comp5")));

        criteria.setLos(Boolean.valueOf(reportParamsMap.get("isLOS")));
        criteria.setMaxLOS(Integer.parseInt(reportParamsMap.get("maxLOS")));

        criteria.setEndDate(LocalDate.fromDateFields(DATE_FORMAT.parse(reportParamsMap.get("EndDate").substring(0, 8))));
        criteria.setJndiName(reportParamsMap.get("JNDI_NAME"));
        criteria.setIgnorePagination(Boolean.valueOf(reportParamsMap.get("IS_IGNORE_PAGINATION")));
        criteria.setIsRollingDate(Integer.parseInt(reportParamsMap.get("param_isRollingDate")));
        criteria.setLanguage(Language.getLanguageFromString(reportParamsMap.get("userLocale")));
        criteria.setPropertyId(Integer.parseInt(reportParamsMap.get("param_Property_ID")));
        criteria.setRollingEndDate(reportParamsMap.get("param_Rolling_End_Date"));
        criteria.setRollingStartDate(reportParamsMap.get("param_Rolling_Start_Date"));
        criteria.setSheetPerCriteria(Boolean.valueOf(reportParamsMap.get("param_SheetForCriteria")));
        criteria.setShowLastYearData(Boolean.valueOf(reportParamsMap.get("param_IsShowLastYearDataChecked")));
        criteria.setStartDate(LocalDate.fromDateFields(DATE_FORMAT.parse(reportParamsMap.get("StartDate").substring(0, 8))));
        criteria.setUserId(Integer.parseInt(reportParamsMap.get("param_User_ID")));
        criteria.setUsePhysicalCapacity(Boolean.valueOf(reportParamsMap.get("usePhysicalCapacity")) ? 1 : 0);

        return criteria;
    }

    public static String getStringValue(Object obj) {
        if (obj == null) {
            return "";
        }
        return obj.toString();
    }

    public static Map<String, String> parseReportParams(String reportParameters) {

        Map<String, String> reportParamsMap = new HashMap<>();

        for (String reportParam : reportParameters.split(SCHEDULE_PARAM_SEPARATOR)) {
            String[] reportParamNameValue = reportParam.split("=");
            if (reportParamNameValue.length > 1) {
                reportParamsMap.put(reportParamNameValue[0], reportParamNameValue[1]);
            }
        }
        return reportParamsMap;
    }
}
