package com.ideas.tetris.pacman.services.security;

public class LoginResultInfo {
    private Integer statusCode;
    private Integer remainingFailedAttemptsCount = 5;
    private Integer userId;
    private String userToken;

    public LoginResultInfo() {
    }

    public LoginResultInfo(Integer statusCode, Integer remainingFailedAttemptsCount, Integer userId, String userToken) {
        this.statusCode = statusCode;
        this.remainingFailedAttemptsCount = remainingFailedAttemptsCount;
        this.userId = userId;
        this.userToken = userToken;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    public Integer getRemainingFailedAttemptsCount() {
        return remainingFailedAttemptsCount;
    }

    public void setRemainingFailedAttemptsCount(Integer remainingFailedAttemptsCount) {
        this.remainingFailedAttemptsCount = remainingFailedAttemptsCount;
    }

    public String getUserToken() {
        return userToken;
    }

    public void setUserToken(String userToken) {
        this.userToken = userToken;
    }

}
