package com.ideas.tetris.pacman.services.reports.specialevents;

import com.ideas.tetris.pacman.common.constants.Constants;

public enum SpecialDisplayEventType {
    ALL(-999, "all"), INFORMATION_ONLY(Constants.SPECIAL_EVENT_INFORMATION_ONLY, "informational.use.only");

    private int id;
    private String eventType;

    SpecialDisplayEventType(int id, String eventType) {
        this.id = id;
        this.eventType = eventType;
    }

    public int getId() {
        return id;
    }

    public String getEventType() {
        return eventType;
    }
}
