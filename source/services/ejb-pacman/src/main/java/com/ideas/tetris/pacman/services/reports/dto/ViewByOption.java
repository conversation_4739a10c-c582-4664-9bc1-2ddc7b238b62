package com.ideas.tetris.pacman.services.reports.dto;

import com.ideas.tetris.platform.common.entity.IdAwareEntity;

public class ViewByOption extends IdAwareEntity<Integer> {

    private Integer id;
    private String name;

    public ViewByOption() {
    }

    public ViewByOption(int id, String name) {
        this.id = id;
        this.name = name;
    }

    @Override
    public Integer getId() {
        return id;
    }

    @Override
    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String caption) {
        this.name = caption;
    }

}
