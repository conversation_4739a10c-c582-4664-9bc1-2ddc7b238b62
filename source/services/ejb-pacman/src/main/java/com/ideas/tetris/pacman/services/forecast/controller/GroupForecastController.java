package com.ideas.tetris.pacman.services.forecast.controller;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.forecast.GroupForecastService;
import com.ideas.tetris.pacman.services.forecast.dto.GroupForecastDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/data/analytics/mr")
public class GroupForecastController {
    @Autowired
    private GroupForecastService service;

    @GetMapping("/group_fcst/{startDate}/{endDate}/v1")
    public List<GroupForecastDto> getGroupForecasts(@PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate, @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate ){
        return service.retrieveGroupForecasts(PacmanWorkContextHelper.getPropertyId(),startDate,endDate);
    }
}
