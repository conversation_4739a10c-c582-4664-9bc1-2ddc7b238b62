package com.ideas.tetris.pacman.services.reports.dataextraction;

import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionReportDto;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionRoomType;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionType;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.ReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.DataExtractionReportCriteria;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil.getText;


public class DataExtractionRoomTypeConverter {
    private static List<Object> getRoomTypeHeaderList(Language language, DataExtractionReportCriteria reportCriteria) {
        List<Object> headers = new ArrayList<>();
        if (reportCriteria.isShowLastYearData()) {
            headers.add(getText("common.propertyName", language));
            headers.add(getText("report.dow", language));
            headers.add(getText("occupancyDate", language));
            headers.add(getText("common.comparisonDateLastYear", language));
            headers.add(getText("room.type", language));
            headers.add(getText("room.class", language));
            ;
            if (reportCriteria.isRoomTypeCapacity()) {
                headers.add(getText("common.capacity", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.capacity", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomTypeRoomsSold() && reportCriteria.isRoomTypeRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (!reportCriteria.isRoomTypeRoomsSold() && reportCriteria.isRoomTypeRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isRoomTypeRoomsSold() && !reportCriteria.isRoomTypeRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomTypeArrivals()) {
                headers.add(getText("arrivals", language) + " " + getText("common.thisYear", language));
                headers.add(getText("arrivals", language) + " " + getText("common.lastYearActual", language));
            }
            if (reportCriteria.isRoomTypeDepartures()) {
                headers.add(getText("common.departures", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.departures", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomTypeOutOfOrder()) {
                headers.add(getText("report.column.roomsNAOutOfOrder", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsNAOutOfOrder", language) + " " + getText("common.lastYearActual", language));
            }
            if (reportCriteria.isRoomTypeRoomsNotAvailableOutOfOrder()) {
                headers.add(getText("report.column.roomsNAOther", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsNAOther", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomTypeCancellations()) {
                headers.add(getText("cancelled", language) + " " + getText("common.thisYear", language));
                headers.add(getText("cancelled", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomTypeNoShow()) {
                headers.add(getText("noshow", language) + " " + getText("common.thisYear", language));
                headers.add(getText("noshow", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomTypeRevenue() && reportCriteria.isRoomTypeRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
            }

            if (!reportCriteria.isRoomTypeRevenue() && reportCriteria.isRoomTypeRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isRoomTypeRevenue() && !reportCriteria.isRoomTypeRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomTypeOverbooking()) {
                headers.add(getText("common.overbooking", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.overbooking", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomTypeRevPAR()) {
                headers.add(getText("revpar.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("revpar.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.forecastedRevPar", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRevPar", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isRoomTypeADR()) {
                headers.add(getText("report.column.bookedAdr", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedAdr", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.forecastedAdr", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedAdr", language) + " " + getText("common.lastYearActual", language));
            }

        } else {
            headers.add(getText("common.propertyName", language));
            headers.add(getText("report.dow", language));
            headers.add(getText("occupancyDate", language));
            headers.add(getText("room.type", language));
            headers.add(getText("room.class", language));
            ;
            if (reportCriteria.isRoomTypeCapacity()) {
                headers.add(getText("common.capacity", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomTypeRoomsSold() && reportCriteria.isRoomTypeRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (!reportCriteria.isRoomTypeRoomsSold() && reportCriteria.isRoomTypeRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isRoomTypeRoomsSold() && !reportCriteria.isRoomTypeRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomTypeArrivals()) {
                headers.add(getText("arrivals", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomTypeDepartures()) {
                headers.add(getText("common.departures", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomTypeOutOfOrder()) {
                headers.add(getText("report.column.roomsNAOutOfOrder", language) + " " + getText("common.thisYear", language));
            }
            if (reportCriteria.isRoomTypeRoomsNotAvailableOutOfOrder()) {
                headers.add(getText("report.column.roomsNAOther", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomTypeCancellations()) {
                headers.add(getText("cancelled", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomTypeNoShow()) {
                headers.add(getText("noshow", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomTypeRevenue() && reportCriteria.isRoomTypeRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
            }

            if (!reportCriteria.isRoomTypeRevenue() && reportCriteria.isRoomTypeRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isRoomTypeRevenue() && !reportCriteria.isRoomTypeRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomTypeOverbooking()) {
                headers.add(getText("common.overbooking", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomTypeRevPAR()) {
                headers.add(getText("revpar.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRevPar", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isRoomTypeADR()) {
                headers.add(getText("report.column.bookedAdr", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedAdr", language) + " " + getText("common.thisYear", language));
            }

        }

        return headers;
    }

    public static ReportSheet getRoomTypeReportSheet(Map<DataExtractionType, List<DataExtractionReportDto>> records, ScheduledReport<DataExtractionReportCriteria> scheduledReport) {

        Language language = scheduledReport.getLanguage();
        DecimalFormat decimalFormat = ScheduledReportUtils.getLocaleDecimalFormat(language.getLocale());
        ReportSheet roomTypeSheet = new ReportSheet(getText("room.type", language));
        roomTypeSheet.setReportTitle(getText("dataExtractionReport.title.at.room.type.level", scheduledReport.getLanguage()));
        List<DataExtractionReportDto> dataExtractionReportDtoList = records.get(DataExtractionType.ROOM_TYPE);
        Object[] headerArray = getRoomTypeHeaderList(scheduledReport.getLanguage(), scheduledReport.getReportCriteria()).toArray();
        DataExtractionReportCriteria reportCriteria = scheduledReport.getReportCriteria();
        for (int i = 0; i < headerArray.length; i++) {
            roomTypeSheet.addColumn(String.class);
        }
        roomTypeSheet.addHeaderRow(headerArray);
        dataExtractionReportDtoList.forEach(dto -> {
            List<Object> dataList = new ArrayList<Object>();
            DataExtractionRoomType data = (DataExtractionRoomType) dto;

            if (reportCriteria.isShowLastYearData()) {
                dataList.add(DataExtractionReportUtil.getStringValue(data.getPropertyName())); //  Property Name
                if (data.getDayOfWeek() != null && !data.getDayOfWeek().isEmpty()) {
                    dataList.add(getText(DataExtractionReportUtil.getStringValue(data.getDayOfWeek()).toLowerCase(), language)); // Day of Week
                } else {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getDayOfWeek())); // Day of Week
                }
                dataList.add(ScheduledReportUtils.getDateString(data.getOccupancyDate())); //  Occupancy Date
                dataList.add(ScheduledReportUtils.getDateString(data.getComparisonDateLastYear())); //  Comparison Date Last Year
                dataList.add(DataExtractionReportUtil.getStringValue(data.getAccomTypeName())); //  Room Type
                dataList.add(DataExtractionReportUtil.getStringValue(data.getAccomClassName())); //  Room Class
                if (reportCriteria.isRoomTypeCapacity()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCapacityThisYear())); //  Capacity This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCapacityLastYear())); //  Capacity Last Year Actual
                }

                if (reportCriteria.isRoomTypeRoomsSold() && reportCriteria.isRoomTypeRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); //  Occupancy On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }

                if (!reportCriteria.isRoomTypeRoomsSold() && reportCriteria.isRoomTypeRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); //  Occupancy On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }

                if (reportCriteria.isRoomTypeRoomsSold() && !reportCriteria.isRoomTypeRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); //  Occupancy On Books Last Year Actual
                }

                if (reportCriteria.isRoomTypeArrivals()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsThisYear())); //  Arrivals This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsLastYear())); //  Arrivals Last Year Actual
                }

                if (reportCriteria.isRoomTypeDepartures()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesThisYear())); //  Departures This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesLastYear())); //  Departures Last Year Actual
                }

                if (reportCriteria.isRoomTypeOutOfOrder()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsNotAvailableMaintenanceThisYear())); //  Rooms N/A - Out of Order This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsNotAvailableMaintenanceLastYear())); //  Rooms N/A - Out of Order Last Year Actual
                }
                if (reportCriteria.isRoomTypeRoomsNotAvailableOutOfOrder()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsNotAvailableOtherThisYear())); //  Rooms N/A - Other This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsNotAvailableOtherLastYear())); //  Rooms N/A - Other Last Year Actual
                }

                if (reportCriteria.isRoomTypeCancellations()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledThisYear())); //  Cancelled This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledLastYear())); //  Cancelled Last Year Actual
                }

                if (reportCriteria.isRoomTypeNoShow()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowThisYear())); //  No Show This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowLastYear())); //  No Show Last Year Actual
                }

                if (reportCriteria.isRoomTypeRevenue() && reportCriteria.isRoomTypeRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); //  Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueLastYear(), decimalFormat)); //  Forecasted Room Revenue Last Year Actual
                }

                if (!reportCriteria.isRoomTypeRevenue() && reportCriteria.isRoomTypeRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); //  Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                }

                if (reportCriteria.isRoomTypeRevenue() && !reportCriteria.isRoomTypeRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); //  Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueLastYear(), decimalFormat)); //  Forecasted Room Revenue Last Year Actual
                }

                if (reportCriteria.isRoomTypeOverbooking()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOverbookingDecisionThisYear(), decimalFormat)); //  Overbooking This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOverbookingDecisionLastYear(), decimalFormat)); //  Overbooking Last Year Actual
                }

                if (reportCriteria.isRoomTypeRevPAR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevPARThisYear(), decimalFormat)); //  RevPAR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevPARLastYear(), decimalFormat)); //  RevPAR On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRevparThisYear(), decimalFormat)); //  RevPAR Forecast This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRevparLastYear(), decimalFormat)); //  RevPAR Forecast Last Year Actual
                }

                if (reportCriteria.isRoomTypeADR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRThisYear(), decimalFormat)); //  ADR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRLastYear(), decimalFormat)); //  ADR On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrThisYear(), decimalFormat)); //  ADR Forecast This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrLastYear(), decimalFormat)); //  ADR Forecast Last Year Actual
                }

            } else {

                dataList.add(DataExtractionReportUtil.getStringValue(data.getPropertyName())); //  Property Name
                if (data.getDayOfWeek() != null && !data.getDayOfWeek().isEmpty()) {
                    dataList.add(getText(DataExtractionReportUtil.getStringValue(data.getDayOfWeek()).toLowerCase(), language)); // Day of Week
                } else {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getDayOfWeek())); // Day of Week
                }
                dataList.add(ScheduledReportUtils.getDateString(data.getOccupancyDate())); //  Occupancy Date
                dataList.add(DataExtractionReportUtil.getStringValue(data.getAccomTypeName())); //  Room Type
                dataList.add(DataExtractionReportUtil.getStringValue(data.getAccomClassName())); //  Room Class
                if (reportCriteria.isRoomTypeCapacity()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCapacityThisYear())); //  Capacity This Year
                }

                if (reportCriteria.isRoomTypeRoomsSold() && reportCriteria.isRoomTypeRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }

                if (!reportCriteria.isRoomTypeRoomsSold() && reportCriteria.isRoomTypeRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); //  Occupancy On Books STLY
                }

                if (reportCriteria.isRoomTypeRoomsSold() && !reportCriteria.isRoomTypeRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); //  Occupancy On Books This Year
                }

                if (reportCriteria.isRoomTypeArrivals()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsThisYear())); //  Arrivals This Year
                }

                if (reportCriteria.isRoomTypeDepartures()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesThisYear())); //  Departures This Year
                }

                if (reportCriteria.isRoomTypeOutOfOrder()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsNotAvailableMaintenanceThisYear())); //  Rooms N/A - Out of Order This Year
                }
                if (reportCriteria.isRoomTypeRoomsNotAvailableOutOfOrder()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsNotAvailableOtherThisYear())); //  Rooms N/A - Other This Year
                }

                if (reportCriteria.isRoomTypeCancellations()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledThisYear())); //  Cancelled This Year
                }

                if (reportCriteria.isRoomTypeNoShow()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowThisYear())); //  No Show This Year
                }

                if (reportCriteria.isRoomTypeRevenue() && reportCriteria.isRoomTypeRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                }

                if (!reportCriteria.isRoomTypeRevenue() && reportCriteria.isRoomTypeRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); //  Booked Room Revenue STLY
                }

                if (reportCriteria.isRoomTypeRevenue() && !reportCriteria.isRoomTypeRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); //  Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); //  Forecasted Room Revenue This Year
                }

                if (reportCriteria.isRoomTypeOverbooking()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOverbookingDecisionThisYear(), decimalFormat)); //  Overbooking This Year
                }

                if (reportCriteria.isRoomTypeRevPAR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevPARThisYear(), decimalFormat)); //  RevPAR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRevparThisYear(), decimalFormat)); //  RevPAR Forecast This Year
                }

                if (reportCriteria.isRoomTypeADR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRThisYear(), decimalFormat)); //  ADR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrThisYear(), decimalFormat)); //  ADR Forecast This Year
                }

            }

            roomTypeSheet.addRow(dataList.toArray());
        });
        return roomTypeSheet;

    }
}
