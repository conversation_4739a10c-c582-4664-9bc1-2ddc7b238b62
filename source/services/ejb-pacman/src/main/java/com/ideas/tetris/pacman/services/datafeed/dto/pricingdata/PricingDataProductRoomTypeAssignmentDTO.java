package com.ideas.tetris.pacman.services.datafeed.dto.pricingdata;

import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;

import java.io.Serializable;

public class PricingDataProductRoomTypeAssignmentDTO implements Serializable {

    private String productName;

    private String roomTypeAssigned;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getRoomTypeAssigned() {
        return roomTypeAssigned;
    }

    public void setRoomTypeAssigned(String roomTypeAssigned) {
        this.roomTypeAssigned = roomTypeAssigned;
    }

    public PricingDataProductRoomTypeAssignmentDTO() {
    }

    public PricingDataProductRoomTypeAssignmentDTO(ProductAccomType prodAccomType) {
        this.productName = prodAccomType.getProduct().getName();
        this.roomTypeAssigned = prodAccomType.getAccomType().getName();
    }
}
