package com.ideas.tetris.pacman.services.forecast.dto;

import com.ideas.tetris.pacman.util.LocalDateRangeIterator;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import org.joda.time.LocalDate;

import java.util.Iterator;
import java.util.Objects;
import java.util.function.Consumer;

public class MaskingDataTypeAndLocalDateDTO {

    private String dataType;

    private LocalDate startDate;

    private LocalDate endDate;

    public MaskingDataTypeAndLocalDateDTO() {
    }

    public MaskingDataTypeAndLocalDateDTO(String dataType, LocalDate startDate, LocalDate endDate) {
        this.dataType = dataType;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }


    public boolean isDateFallsInRange(org.joda.time.LocalDate target) {
        return LocalDateUtils.isDateBetween(target, startDate, endDate);
    }

    public Iterator<org.joda.time.LocalDate> iterator() {
        return new LocalDateRangeIterator(startDate, endDate);
    }

    public void forEach(Consumer<org.joda.time.LocalDate> consumer) {
        iterator().forEachRemaining(consumer);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MaskingDataTypeAndLocalDateDTO that = (MaskingDataTypeAndLocalDateDTO) o;
        return Objects.equals(dataType, that.dataType) && Objects.equals(startDate, that.startDate) && Objects.equals(endDate, that.endDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dataType, startDate, endDate);
    }
}
