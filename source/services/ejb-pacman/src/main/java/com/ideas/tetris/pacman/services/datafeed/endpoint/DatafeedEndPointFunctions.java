package com.ideas.tetris.pacman.services.datafeed.endpoint;


import com.google.common.annotations.VisibleForTesting;

import java.util.function.Function;

public final class DatafeedEndPointFunctions {

    public static final Function<DatafeedEndPointCriteria, Boolean> HILTON_SPECIFIC = DatafeedEndPointFunctions::isHiltonSpecific;
    public static final Function<DatafeedEndPointCriteria, Boolean> UNIQUE_USER_ID_ENABLED = DatafeedEndPointFunctions::isUseUniqueIDEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> UNIQUE_USER_ID_NOT_ENABLED = DatafeedEndPointFunctions::isUseUniqueIDNotEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> USER_REPORT_WITH_UNIQUE_USER_ID_ENABLED = DatafeedEndPointFunctions::isUseUniqueIDEnabledInUserReport;
    public static final Function<DatafeedEndPointCriteria, Boolean> USER_REPORT_WITH_UNIQUE_USER_ID_NOT_ENABLED = DatafeedEndPointFunctions::isUseUniqueIDNotEnabledInUserReport;
    public static final Function<DatafeedEndPointCriteria, Boolean> USER_REPORT_WITH_PROPERTY_CODE = DatafeedEndPointFunctions::isPropertyCodeEnabledInUserReportDataFeed;
    public static final Function<DatafeedEndPointCriteria, Boolean> USER_REPORT_WITH_USER_ID_AND_PROPERTY_CODE = DatafeedEndPointFunctions::isUniqueIDAndPropertyCodeEnabledInUserReportDataFeed;
    public static final Function<DatafeedEndPointCriteria, Boolean> HILTON_SPECIFIC_UNIQUE_USER_ID_APPLICABLE = DatafeedEndPointFunctions::isHiltonSpecificUniqueUserIDApplicable;
    public static final Function<DatafeedEndPointCriteria, Boolean> HILTON_SPECIFIC_UNIQUE_USER_ID_NOT_APPLICABLE = DatafeedEndPointFunctions::isHiltonSpecificUniqueUserIDNotApplicable;
    public static final Function<DatafeedEndPointCriteria, Boolean> HILTON_SPECIFIC_CONFIG = DatafeedEndPointFunctions::isHiltonSpecificConfig;
    public static final Function<DatafeedEndPointCriteria, Boolean> HILTON_SPECIFIC_CONFIG_ENHANCED = DatafeedEndPointFunctions::isHiltonSpecificConfigEnhanced;
    public static final Function<DatafeedEndPointCriteria, Boolean> HILTON_SPECIFIC_CONFIG_UPDATED = DatafeedEndPointFunctions::isHiltonSpecificConfigUpdated;
    public static final Function<DatafeedEndPointCriteria, Boolean> GENERAL_AVAILABILITY = datafeedEndPointCriteria -> isCommonEndPoint();
    public static final Function<DatafeedEndPointCriteria, Boolean> CONTINUOUS_PRICING = DatafeedEndPointFunctions::isContinuousPricingEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> CONTINUOUS_PRICING_SUPPLEMENTS_AND_INDEPENDENT_PRODUCT_DISABLED = DatafeedEndPointFunctions::isCPSupplementsAllowedAndIndependentProductDisabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> CONTINUOUS_PRICING_SUPPLEMENTS_AND_INDEPENDENT_PRODUCT_ENABLED = DatafeedEndPointFunctions::isCPSupplementsAllowedAndIndependentProductEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> CONTINUOUS_PRICING_AND_INDEPENDENT_PRODUCT_DISABLED_NON_HILTON = DatafeedEndPointFunctions::isCPIndependentProductDisabledForNonHilton;
    public static final Function<DatafeedEndPointCriteria, Boolean> CONTINUOUS_PRICING_AND_INDEPENDENT_PRODUCT_ENABLED_NON_HILTON = DatafeedEndPointFunctions::isCPAndIndependentProductEnabledForNonHilton;
    public static final Function<DatafeedEndPointCriteria, Boolean> IS_CP_AND_CHILD_AGE_BASED_BUCKET_DISABLED_FOR_HILTON = DatafeedEndPointFunctions::isCPChildAgeBasedBucketDisabledForHilton;
    public static final Function<DatafeedEndPointCriteria,Boolean> ENHANCED_INFORMATION_LAST_LIMITED_DATA_BUILD_DISABLED = DatafeedEndPointFunctions::isAdditionalInformationalFieldsLastLDBUpdateDisabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> ENHANCED_INFORMATION_LAST_LIMITED_DATA_BUILD = DatafeedEndPointFunctions::isAdditionalInformationalFieldsLastLDBUpdate;
    public static final Function<DatafeedEndPointCriteria,Boolean> ENHANCED_INFORMATION_LAST_LIMITED_DATA_BUILD_DISABLED_AND_WINDOWS_SETTINGS_ENABLED = DatafeedEndPointFunctions::isLastLDBUpdateDisabledAndWindowSettingsEnabled;
    public static final Function<DatafeedEndPointCriteria,Boolean> ENHANCED_INFORMATION_LAST_LIMITED_DATA_BUILD_ENABLED_AND_WINDOWS_SETTINGS_ENABLED = DatafeedEndPointFunctions::isLastLDBUpdateEnabledAndWindowSettingsEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> IS_CP_AND_CHILD_AGE_BASED_BUCKET_ENABLED_FOR_HILTON = DatafeedEndPointFunctions::isCPChildAgeBasedBucketEnabledForHilton;
    public static final Function<DatafeedEndPointCriteria, Boolean> SYSTEM_HEALTH = DatafeedEndPointFunctions::isSystemHealthAllowed;
    public static final Function<DatafeedEndPointCriteria, Boolean> BAR_OVERRIDE = DatafeedEndPointFunctions::isBarOverrideAllowed;
    public static final Function<DatafeedEndPointCriteria, Boolean> MARKET_SEGMENT_MAPPING = DatafeedEndPointFunctions::isMCATMappingAllowed;
    public static final Function<DatafeedEndPointCriteria, Boolean> PROPERTY_SPECIFIC_CONFIG = DatafeedEndPointFunctions::isPropertySpecificConfigurationAllowed;
    public static final Function<DatafeedEndPointCriteria, Boolean> OPTIX_PROPERTY_SPECIFIC_CONFIG = DatafeedEndPointFunctions::isOptixPropertySpecificConfigurationAllowed;
    public static final Function<DatafeedEndPointCriteria, Boolean> PROPERTY_SPECIFIC_CONFIG_ENHANCED = DatafeedEndPointFunctions::isPropertySpecificConfigurationEnhanced;
    public static final Function<DatafeedEndPointCriteria, Boolean> PROPERTY_SPECIFIC_CONFIG_UPDATED = DatafeedEndPointFunctions::isPropertySpecificConfigurationUpdated;
    public static final Function<DatafeedEndPointCriteria, Boolean> OPTIX_PROPERTY_SPECIFIC_CONFIG_ENHANCED = DatafeedEndPointFunctions::isOptixPropertySpecificConfigurationEnhanced;
    public static final Function<DatafeedEndPointCriteria, Boolean> PRICE_DROP_RESTRICTIONS = DatafeedEndPointFunctions::isPriceDropRestrictionsEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> PROPERTY_SPECIFIC_ATTRIBUTE = datafeedEndPointCriteria -> isCommonEndPoint();
    public static final Function<DatafeedEndPointCriteria, Boolean> CONTINUOUS_PRICING_DISABLED = DatafeedEndPointFunctions::isContinuousPricingDisabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> AGILE_RATES_APPLICABLE = DatafeedEndPointFunctions::isAgileRatesApplicable;
    public static final Function<DatafeedEndPointCriteria, Boolean> LDB_PROJECTION_ENABLED = DatafeedEndPointFunctions::isLDBProjectionEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> ONLY_GROUP_EVALUATION_ENABLED = DatafeedEndPointFunctions::isGroupEvaluationEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> ENHANCED_GROUP_EVALUATION_ENABLED = DatafeedEndPointFunctions::isGroupEvaluationIncludesBookingId;
    public static final Function<DatafeedEndPointCriteria, Boolean> AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE = DatafeedEndPointFunctions::isAgileRatesProductConfigurationApplicable;
    public static final Function<DatafeedEndPointCriteria, Boolean> AGILE_RATES_PRODUCT_CONFIGURATION_PACKAGE_ELEMENTS_ENHANCED_DISABLED = DatafeedEndPointFunctions::isAgileRatesProductPackageElementsEnhancedDisabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> AGILE_RATES_PRODUCT_CONFIGURATION_PACKAGE_ELEMENTS_ENHANCED_ENABLED = DatafeedEndPointFunctions::isAgileRatesProductPackageElementsEnhancedEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE_DISABLED = DatafeedEndPointFunctions::isAgileRatesProductConfigurationApplicableDisabledNonHilton;
    public static final Function<DatafeedEndPointCriteria, Boolean> AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE_ENABLED = DatafeedEndPointFunctions::isAgileRatesProductConfigurationApplicableEnabledNonHilton;
    public static final Function<DatafeedEndPointCriteria, Boolean> AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE_DISABLED_HILTON = DatafeedEndPointFunctions::isAgileRatesProductConfigurationApplicableDisabledForHilton;
    public static final Function<DatafeedEndPointCriteria, Boolean> AGILE_RATES_PRODUCT_CONFIGURATION_APPLICABLE_ENABLED_HILTON = DatafeedEndPointFunctions::isAgileRatesProductConfigurationApplicableEnabledForHilton;
    public static final Function<DatafeedEndPointCriteria, Boolean> GROUP_PRICING_CONFIGURATION_ENABLED = DatafeedEndPointFunctions::isGroupPricingConfigurationEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> GROUP_PRICING_MIN_PROFIT_CONFIGURATION_ENABLED = DatafeedEndPointFunctions::isGroupPricingMinProfitConfigurationEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> GROUP_FINAL_FORECAST_OVERRIDE_ENABLED = DatafeedEndPointFunctions::isGroupFinalForecastEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> AGILE_PRODUCT_SEND_DECISION_APPLICABLE = DatafeedEndPointFunctions::isProductSendDecisionAdjustmentEnabledForHilton;
    public static final Function<DatafeedEndPointCriteria, Boolean> HILTON_CONTINUOUS_PRICING = DatafeedEndPointFunctions::isContinuousPricingEnabledForHilton;
    public static final Function<DatafeedEndPointCriteria, Boolean> HILTON_NON_CONTINUOUS_PRICING = DatafeedEndPointFunctions::isContinuousPricingNotEnabledForHilton;
    public static final Function<DatafeedEndPointCriteria, Boolean> NON_HILTON_CONTINUOUS_PRICING = DatafeedEndPointFunctions::isContinuousPricingEnabledForNonHilton;
    public static final Function<DatafeedEndPointCriteria, Boolean> PROFIT_OPTIMIZATION_DATAFEED_ENABLED = DatafeedEndPointFunctions::isProfitOptimizationDataFeedEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> OUT_OF_ORDER_OVERRIDE_ENABLED = DatafeedEndPointFunctions::isOutOfOrderDataFeedEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> TAX_INCLUSIVE_CONFIGURATION_ENABLED = DatafeedEndPointFunctions::isTaxInclusiveConfigurationEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> IS_FIRST_UPLOAD = DatafeedEndPointFunctions::isFirstUpload;
    public static final Function<DatafeedEndPointCriteria, Boolean> PACE_WEB_RATE_ENABLED = DatafeedEndPointFunctions::isPaceWebRateEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> AGILE_PRODUCT_OPTIMIZATION_ENABLED = DatafeedEndPointFunctions::isAgileProductOptimizationFilesEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> AGILE_PRODUCT_OPTIMIZATION_ENHANCED_ENABLED = DatafeedEndPointFunctions::isAgileProductOptimizationFilesEnhancedEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> AGILE_PRODUCT_OPTIMIZATION_ENHANCED_DISABLED = DatafeedEndPointFunctions::isAgileProductOptimizationFilesEnhancedDisabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> OPTIX_WEB_RATE_ENABLED = DatafeedEndPointFunctions::isWebRateEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> CONTINUOUS_PRICING_ENABLED = DatafeedEndPointFunctions::isContinuousPricingEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> CONTINUOUS_PRICING_INDEPENDENT_PRODUCT_COLUMN_DISABLED = DatafeedEndPointFunctions::isContinuousPricingEnabledAndIndependentProductColumnDisabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> CONTINUOUS_PRICING_INDEPENDENT_PRODUCT_COLUMN_ENABLED = DatafeedEndPointFunctions::isContinuousPricingEnabledAndIndependentProductColumnEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> INVENTORY_HISTORY_ENABLED = DatafeedEndPointFunctions::isInventoryHistoryEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> STR_ENABLED = DatafeedEndPointFunctions::isStrEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> ENHANCED_INVENTORY_HISTORY_ENABLED = DatafeedEndPointFunctions::isEnhancedInventoryHistoryEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> DEMAND360_ENABLED = DatafeedEndPointFunctions::isDemand360Enabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED = DatafeedEndPointFunctions::isExtendedStayUnqualifiedRateManagementEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> VIRTUAL_PROPERTY_MAPPING_FOR_HILTON = DatafeedEndPointFunctions::isVirtualPropertyMappingForHiltonEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> ST19_FOR_DATAFEED_MSRT_ENABLED = DatafeedEndPointFunctions::isST19ForDatafeedMSRTEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> ST19_FOR_DATAFEED_MSRT_DISABLED = DatafeedEndPointFunctions::isST19ForDatafeedMSRTDisabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> PRODUCT_NAME_COLUMN_DISABLED = DatafeedEndPointFunctions::isProductColumnDisabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> PRODUCT_NAME_COLUMN_ENABLED = DatafeedEndPointFunctions::isProductColumnEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> PRODUCT_NAME_COLUMN_DISABLED_IN_IGNORE_COMPETITOR = DatafeedEndPointFunctions::isProductColumnDisabledInIgnoreCompetitor;
    public static final Function<DatafeedEndPointCriteria, Boolean> PRODUCT_NAME_COLUMN_ENABLED_IN_IGNORE_COMPETITOR = DatafeedEndPointFunctions::isProductColumnEnabledInIgnoreCompetitor;
    public static final Function<DatafeedEndPointCriteria, Boolean> CHANNEL_COLUMN_ENABLED = DatafeedEndPointFunctions::isChannelColumnEnabledInIgnoreCompetitor;
    public static final Function<DatafeedEndPointCriteria, Boolean> PRODUCT_NAME_AND_CHANNEL_COLUMN_ENABLED = DatafeedEndPointFunctions::isProductNameAndChannelColumnEnabledInIgnoreCompetitor;
    public static final Function<DatafeedEndPointCriteria, Boolean> PRODUCT_RATE_SHOP_DEFINITION_ENABLED = DatafeedEndPointFunctions::isProductRateShopDefinitionEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> PRODUCT_RATE_SHOP_DEFINITION_ENHANCED_ENABLED = DatafeedEndPointFunctions::isProductRateShopDefinitionEnhancedEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> PRODUCT_CLASSIFICATION_ENABLED = DatafeedEndPointFunctions::isProductClassificationEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> PRODUCT_CHILD_PRICING_TYPE_ENABLED = DatafeedEndPointFunctions::isProductChildPricingTypeEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> BENEFIT_MEASUREMENT_ENABLED = DatafeedEndPointFunctions::isBenefitMeasurementEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> BENEFIT_MEASUREMENT_WITH_PROFIT_COLUMNS_ENABLED = DatafeedEndPointFunctions::isBenefitMeasurementWithProfitColumnsEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> SCHEDULED_REPORTS_ENABLED = DatafeedEndPointFunctions::isScheduledReportsEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> CHANNEL_FORECAST_ENABLED = DatafeedEndPointFunctions::isChannelForecastEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> PRODUCT_FREE_NIGHT_DEFINITION_ENABLED = DatafeedEndPointFunctions::isProductFreeNightDefinitionEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> PRODUCT_GROUP_PRODUCT_DEFINITION_ENABLED = DatafeedEndPointFunctions::isProductGroupProductDefinitionEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> GROUP_PRICING_SCROOM_TYPE_MAPPING_ENABLED = DatafeedEndPointFunctions::isGroupPricingSCRoomTypeMappingEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> GROUP_PRICING_SC_MARKET_SEGMENT_MAPPING_ENABLED = DatafeedEndPointFunctions::isGroupPricingSCMarketSegmentMappingEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> ROLE_PERMISSION_ENABLED = DatafeedEndPointFunctions::isRolePermissionEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> ROLE_PERMISSION_WITH_RANK_COLUMN_ENABLED = DatafeedEndPointFunctions::isRolePermissionWithRankColumnEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> CONTINUOUS_PRICING_SUPPLEMENTS_AND_INDEPENDENT_PRODUCT_DISABLED_AND_PERCENTAGE_COLUMN_CPSUPPLEMENT_ENABLED = DatafeedEndPointFunctions::isCPSupplementsAllowedAndIndependentProductDisabledAndPercentageColumnForCPSupplementEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> CONTINUOUS_PRICING_SUPPLEMENTS_AND_INDEPENDENT_PRODUCT_ENABLED_AND_PERCENTAGE_COLUMN_CPSUPPLEMENT_ENABLED = DatafeedEndPointFunctions::isCPSupplementsAllowedAndIndependentProductEnabledAndPercentageColumnForCPSupplementEnabled;
    public static Function<DatafeedEndPointCriteria, Boolean> INVENTORY_LIMIT_ENABLED = DatafeedEndPointFunctions::isInventoryLimitEnabled;
    static final Function<DatafeedEndPointCriteria, Boolean> RATE_SHOPPING_OCCUPANCY_BASED_CMPC_DATAFEED_ENABLED = DatafeedEndPointFunctions::isRateShoppingOccupancyBasedCMPCDatafeedEnabled;
    static final Function<DatafeedEndPointCriteria, Boolean> RATE_SHOPPING_IGNORE_CHANNEL_CONFIG_DATAFEED_ENABLED = DatafeedEndPointCriteria::isRateShoppingIgnoreChannelConfigEnabled;
    static final Function<DatafeedEndPointCriteria, Boolean> OPTIX_ROOM_CLASS_PROFIT_AND_FORECAST_GROUP_PROFIT__DATAFEED_ENABLED = DatafeedEndPointFunctions::isProfitPopulationEnabled;


    public static Function<DatafeedEndPointCriteria, Boolean> SPECIAL_USE_ROOM_TYPES_DATAFEED_ENABLED = DatafeedEndPointFunctions::isSpecialUseRoomTypesDatafeedEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> SPECIAL_USE_ROOM_TYPES_DATAFEED_DISABLED = DatafeedEndPointFunctions::isSpecialUseRoomTypesDatafeedDisabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> DISCONTINUED_ROOM_TYPE_IN_ROOM_CLASS_CONFIG_DATAFEED_ENABLED = DatafeedEndPointFunctions::isDiscontinuedRTInRoomClassConfigDataFeedEnabled;
    public static final Function<DatafeedEndPointCriteria, Boolean> DISCONTINUED_ROOM_TYPE_IN_ROOM_CLASS_CONFIG_DATAFEED_DISABLED = DatafeedEndPointFunctions::isDiscontinuedRTInRoomClassConfigDataFeedDisabled;
    static final Function<DatafeedEndPointCriteria, Boolean> MEETING_PACKAGE_PRICING_ENABLED = DatafeedEndPointFunctions::isMeetingPackagePricingDataFeedEnabled;

    static Boolean isSpecialUseRoomTypesDatafeedEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isSpecialUseRoomTypesDatafeedEnabled();

    }

    static Boolean isSpecialUseRoomTypesDatafeedDisabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return !datafeedEndPointCriteria.isSpecialUseRoomTypesDatafeedEnabled();
    }

    public static Boolean isInventoryLimitEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isEnabledInventoryLimit();
    }

    private static boolean isIndependentProductsEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isIndependentProductsEnabled();
    }


    // Added for non-instantiability
    private DatafeedEndPointFunctions() {
    }

    @VisibleForTesting
    static Boolean isAgileRatesProductConfigurationApplicable(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isAgileRatesApplicable(datafeedEndPointCriteria) && datafeedEndPointCriteria.isAgileRatesProductConfigurationEnabled();
    }

    static Boolean isAgileRatesProductPackageElementsEnhancedDisabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isAgileRatesProductConfigurationApplicable(datafeedEndPointCriteria) && !datafeedEndPointCriteria.isEnhancedProductPackageElementDFEnabled();
    }

    static Boolean isAgileRatesProductPackageElementsEnhancedEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isAgileRatesProductConfigurationApplicable(datafeedEndPointCriteria) && datafeedEndPointCriteria.isEnhancedProductPackageElementDFEnabled();
    }

    static Boolean isAgileRatesProductConfigurationApplicableEnabledNonHilton(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isAgileRatesProductConfigurationApplicableEnabled(datafeedEndPointCriteria) && !datafeedEndPointCriteria.isHiltonClientCode();
    }

    static Boolean isAgileRatesProductConfigurationApplicableDisabledNonHilton(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isAgileRatesProductConfigurationApplicableDisabled(datafeedEndPointCriteria) && !datafeedEndPointCriteria.isHiltonClientCode();
    }

    static Boolean isAgileRatesProductConfigurationApplicableEnabledForHilton(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isAgileRatesProductConfigurationApplicableEnabled(datafeedEndPointCriteria)
                && datafeedEndPointCriteria.isHiltonClientCode();
    }

    static Boolean isAgileRatesProductConfigurationApplicableDisabledForHilton(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isAgileRatesProductConfigurationApplicableDisabled(datafeedEndPointCriteria)
                && datafeedEndPointCriteria.isHiltonClientCode();
    }

    private static Boolean isAgileRatesProductConfigurationApplicableDisabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return !datafeedEndPointCriteria.isRateProtectEnabled() && isAgileRatesApplicable(datafeedEndPointCriteria)
                && datafeedEndPointCriteria.isAgileRatesProductConfigurationEnabled();
    }
     static Boolean isAdditionalInformationalFieldsLastLDBUpdateDisabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return !datafeedEndPointCriteria.isAdditionalInformationalFieldsLastLDBUpdateEnabled() && !datafeedEndPointCriteria.isWindowSettingsEnabledInInformationalDataFeed();
    }
    static Boolean isAdditionalInformationalFieldsLastLDBUpdate(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isAdditionalInformationalFieldsLastLDBUpdateEnabled() && !datafeedEndPointCriteria.isWindowSettingsEnabledInInformationalDataFeed();
    }

    static Boolean isLastLDBUpdateDisabledAndWindowSettingsEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return !datafeedEndPointCriteria.isAdditionalInformationalFieldsLastLDBUpdateEnabled() && datafeedEndPointCriteria.isWindowSettingsEnabledInInformationalDataFeed();
    }

    static Boolean isLastLDBUpdateEnabledAndWindowSettingsEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isAdditionalInformationalFieldsLastLDBUpdateEnabled() && datafeedEndPointCriteria.isWindowSettingsEnabledInInformationalDataFeed();
    }

    private static Boolean isAgileRatesProductConfigurationApplicableEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isRateProtectEnabled() && isAgileRatesApplicable(datafeedEndPointCriteria)
                && datafeedEndPointCriteria.isAgileRatesProductConfigurationEnabled();
    }

    private static Boolean isGroupEvaluationEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isGroupEvaluationEnabled();
    }

    private static Boolean isGroupEvaluationIncludesBookingId(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isGroupEvaluationIncludesBookingId();
    }

    private static Boolean isLDBProjectionEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isLDBProjectionEnabled();
    }

    static Boolean isContinuousPricingEnabledForHilton(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isContinuousPricingEnabled(datafeedEndPointCriteria) && isHiltonSpecific(datafeedEndPointCriteria);
    }

    static Boolean isContinuousPricingNotEnabledForHilton(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return !isContinuousPricingEnabled(datafeedEndPointCriteria) && isHiltonSpecific(datafeedEndPointCriteria);
    }

    static Boolean isProductSendDecisionAdjustmentEnabledForHilton(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isHiltonSpecific(datafeedEndPointCriteria) && datafeedEndPointCriteria.isAgileRatesEnabled() && datafeedEndPointCriteria.isSendDecisionAdjustmentEnabled();
    }

    static Boolean isProfitOptimizationDataFeedEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isProfitPopulationEnabled(datafeedEndPointCriteria) && isProfitMetricsDatafeedEnabled(datafeedEndPointCriteria);
    }

    public static Boolean isProfitPopulationEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isProfitPopulationEnabled();
    }

    private static Boolean isProfitMetricsDatafeedEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isProfitMetricsDatafeedEnabled();
    }

    private static Boolean isContinuousPricingEnabledForNonHilton(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isContinuousPricingEnabled(datafeedEndPointCriteria) && !isHiltonSpecific(datafeedEndPointCriteria);
    }

    private static Boolean isGroupPricingConfigurationEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isGroupPricingEnabled(datafeedEndPointCriteria) || isFunctionSpaceEnabled(datafeedEndPointCriteria);
    }

    private static Boolean isGroupPricingMinProfitConfigurationEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isGroupPricingMinProfitEnabled(datafeedEndPointCriteria);
    }

    private static Boolean isAgileRatesApplicable(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isContinuousPricingEnabled(datafeedEndPointCriteria) && datafeedEndPointCriteria.isAgileRatesEnabled();
    }

    static Boolean isCommonEndPoint() {
        return true;
    }

    static Boolean isHiltonSpecific(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isHiltonClientCode();
    }

    static Boolean isHiltonSpecificUniqueUserIDApplicable(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isHiltonClientCode() && isUseUniqueIDEnabled(datafeedEndPointCriteria);
    }

    private static boolean isUseUniqueIDEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isUseUniqueIDEnabled();
    }

    private static boolean isUseUniqueIDNotEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return !isUseUniqueIDEnabled(datafeedEndPointCriteria);
    }

    private static boolean isUseUniqueIDEnabledInUserReport(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isUseUniqueIDEnabled(datafeedEndPointCriteria) && !datafeedEndPointCriteria.isPropertyCodeEnabledInUserReportDataFeed();
    }

    private static boolean isUseUniqueIDNotEnabledInUserReport(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isUseUniqueIDNotEnabled(datafeedEndPointCriteria) && !datafeedEndPointCriteria.isPropertyCodeEnabledInUserReportDataFeed();
    }

    private static boolean isPropertyCodeEnabledInUserReportDataFeed(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isUseUniqueIDNotEnabled(datafeedEndPointCriteria) && datafeedEndPointCriteria.isPropertyCodeEnabledInUserReportDataFeed();
    }

    private static boolean isUniqueIDAndPropertyCodeEnabledInUserReportDataFeed(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isUseUniqueIDEnabled(datafeedEndPointCriteria) && datafeedEndPointCriteria.isPropertyCodeEnabledInUserReportDataFeed();
    }

    static Boolean isHiltonSpecificUniqueUserIDNotApplicable(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isHiltonClientCode() && !isUseUniqueIDEnabled(datafeedEndPointCriteria);
    }

    static Boolean isHiltonSpecificConfig(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isHiltonSpecific(datafeedEndPointCriteria) && !datafeedEndPointCriteria.isPropertySpecificNewColumnsEnabled();
    }

    static Boolean isHiltonSpecificConfigEnhanced(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isHiltonSpecific(datafeedEndPointCriteria) && datafeedEndPointCriteria.isPropertySpecificNewColumnsEnabled() && !datafeedEndPointCriteria.isPropertySpecificUpdatedColumnsEnabled();
    }

    static Boolean isHiltonSpecificConfigUpdated(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isHiltonSpecific(datafeedEndPointCriteria) && datafeedEndPointCriteria.isPropertySpecificNewColumnsEnabled() && datafeedEndPointCriteria.isPropertySpecificUpdatedColumnsEnabled();
    }

    static Boolean isGroupPricingEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isGroupPricingEnabled();
    }

    static Boolean isGroupPricingMinProfitEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isGroupPricingMinProfitEnabled();
    }

    static Boolean isFunctionSpaceEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isFunctionSpaceEnabled();
    }

    static Boolean isContinuousPricingEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isContinuousPricingEnabled();
    }

    static Boolean isContinuousPricingDisabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return !datafeedEndPointCriteria.isContinuousPricingEnabled();
    }

    static Boolean isContinuousPricingEnabledAndIndependentProductColumnEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isContinuousPricingEnabled() && datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled();
    }

    static Boolean isContinuousPricingEnabledAndIndependentProductColumnDisabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isContinuousPricingEnabled() && !datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled();
    }

    static Boolean isProductColumnEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled();
    }

    static Boolean isProductColumnDisabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return !datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled();
    }

    static Boolean isProductColumnEnabledInIgnoreCompetitor(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isProductColumnEnabled(datafeedEndPointCriteria) && !datafeedEndPointCriteria.isChannelColumnEnabledInIgnoreCompetitor();
    }

    static Boolean isProductColumnDisabledInIgnoreCompetitor(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isProductColumnDisabled(datafeedEndPointCriteria) && !datafeedEndPointCriteria.isChannelColumnEnabledInIgnoreCompetitor();
    }

    static Boolean isChannelColumnEnabledInIgnoreCompetitor(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isProductColumnDisabled(datafeedEndPointCriteria) && datafeedEndPointCriteria.isChannelColumnEnabledInIgnoreCompetitor();
    }

    static Boolean isProductNameAndChannelColumnEnabledInIgnoreCompetitor(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isProductColumnEnabled(datafeedEndPointCriteria) && datafeedEndPointCriteria.isChannelColumnEnabledInIgnoreCompetitor();
    }

    static Boolean isSystemHealthAllowed(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isSystemHealthEnabled();
    }

    static Boolean isCPSupplementsAllowedAndIndependentProductDisabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isSupplementsEnabled(datafeedEndPointCriteria) && isContinuousPricingEnabled(datafeedEndPointCriteria) &&
                !datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled() &&
                !isPercentageColumnForCPSupplementOfDatafeedEnabled(datafeedEndPointCriteria);
    }

    static Boolean isCPSupplementsAllowedAndIndependentProductDisabledAndPercentageColumnForCPSupplementEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isSupplementsEnabled(datafeedEndPointCriteria) && isContinuousPricingEnabled(datafeedEndPointCriteria) &&
                !datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled() && isPercentageColumnForCPSupplementOfDatafeedEnabled(datafeedEndPointCriteria);
    }

    static Boolean isCPSupplementsAllowedAndIndependentProductEnabledAndPercentageColumnForCPSupplementEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isSupplementsEnabled(datafeedEndPointCriteria) && isContinuousPricingEnabled(datafeedEndPointCriteria) &&
                datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled() && isPercentageColumnForCPSupplementOfDatafeedEnabled(datafeedEndPointCriteria);
    }

    static Boolean isCPIndependentProductDisabledForNonHilton(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isContinuousPricingEnabled(datafeedEndPointCriteria) && !datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled() && !datafeedEndPointCriteria.isHiltonClientCode();
    }

    static Boolean isCPSupplementsAllowedAndIndependentProductEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isSupplementsEnabled(datafeedEndPointCriteria) && isContinuousPricingEnabled(datafeedEndPointCriteria) &&
                datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled() &&
                !isPercentageColumnForCPSupplementOfDatafeedEnabled(datafeedEndPointCriteria);
    }

    static Boolean isCPAndIndependentProductEnabledForNonHilton(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isContinuousPricingEnabled(datafeedEndPointCriteria) && datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled() && !datafeedEndPointCriteria.isHiltonClientCode();
    }

    static Boolean isCPChildAgeBasedBucketDisabledForHilton(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isContinuousPricingEnabled(datafeedEndPointCriteria)
                && datafeedEndPointCriteria.isHiltonClientCode() && !datafeedEndPointCriteria.isAgeBasedPricingForHiltonEnabled();
    }

    static Boolean isCPChildAgeBasedBucketEnabledForHilton(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isContinuousPricingEnabled(datafeedEndPointCriteria)
                && datafeedEndPointCriteria.isHiltonClientCode() && datafeedEndPointCriteria.isAgeBasedPricingForHiltonEnabled();
    }

    static Boolean isSupplementsEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isSupplementsEnabled();
    }

    static Boolean isPercentageColumnForCPSupplementOfDatafeedEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isPercentageColumnForCPSupplementOfDatafeedEnabled();
    }

    static Boolean isClientLevel(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isClientLevelRequest();
    }

    static Boolean isBarOverrideAllowed(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return !isContinuousPricingEnabled(datafeedEndPointCriteria) && !isHiltonSpecific(datafeedEndPointCriteria) && isCommonEndPoint();
    }

    static Boolean isMCATMappingAllowed(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isCommonEndPoint() && !isHiltonSpecific(datafeedEndPointCriteria);
    }

    static Boolean isPropertySpecificConfigurationAllowed(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isCommonEndPoint() && !isHiltonSpecific(datafeedEndPointCriteria) && !datafeedEndPointCriteria.isPropertySpecificNewColumnsEnabled();
    }

    static Boolean isOptixPropertySpecificConfigurationAllowed(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return !datafeedEndPointCriteria.isPropertySpecificNewColumnsEnabled();
    }

    static Boolean isPropertySpecificConfigurationEnhanced(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isCommonEndPoint() && !isHiltonSpecific(datafeedEndPointCriteria) && datafeedEndPointCriteria.isPropertySpecificNewColumnsEnabled() && !datafeedEndPointCriteria.isPropertySpecificUpdatedColumnsEnabled();
    }

    static Boolean isPropertySpecificConfigurationUpdated(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isCommonEndPoint() && !isHiltonSpecific(datafeedEndPointCriteria) && datafeedEndPointCriteria.isPropertySpecificNewColumnsEnabled() && datafeedEndPointCriteria.isPropertySpecificUpdatedColumnsEnabled();
    }

    static Boolean isOptixPropertySpecificConfigurationEnhanced(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isPropertySpecificNewColumnsEnabled();
    }

    static Boolean isPriceDropRestrictionsEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isPropertySpecificUpdatedColumnsEnabled();
    }

    static Boolean isGroupFinalForecastEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isGroupFinalForecastEnabled();
    }

    static Boolean isOutOfOrderDataFeedEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isOutOfOrderOverridesEnabled();
    }

    static Boolean isTaxInclusiveConfigurationEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {

        return datafeedEndPointCriteria.isTaxInclusiveConfigurationEnabled();
    }

    static Boolean isEnhancedInventoryHistoryEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return (datafeedEndPointCriteria.isEnhancedInventoryHistoryEnabled() && isHiltonSpecific(datafeedEndPointCriteria));
    }

    static Boolean isInventoryHistoryEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return (!datafeedEndPointCriteria.isEnhancedInventoryHistoryEnabled() && isHiltonSpecific(datafeedEndPointCriteria));
    }

    static Boolean isStrEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isStrEnabled();
    }

    static Boolean isDemand360Enabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isDemand360Enabled();
    }

    static Boolean isFirstUpload(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isFirstUpload();
    }

    static Boolean isPaceWebRateEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isPaceWebRateEnabled() && datafeedEndPointCriteria.isFirstUpload();
    }

    static Boolean isWebRateEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isPaceWebRateEnabled();
    }

    static Boolean isAgileProductOptimizationFilesEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isAgileProductOptimizationFilesEnabled();
    }

    static Boolean isAgileProductOptimizationFilesEnhancedEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isAgileProductOptimizationFilesEnabled() && datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled();
    }

    static Boolean isAgileProductOptimizationFilesEnhancedDisabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isAgileProductOptimizationFilesEnabled() && !datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled();
    }

    static Boolean isExtendedStayUnqualifiedRateManagementEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isExtendedStayUnqualifiedRateManagementEnabled();
    }

    static Boolean isVirtualPropertyMappingForHiltonEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return isHiltonSpecific(datafeedEndPointCriteria) && datafeedEndPointCriteria.isVirtualPropertyMappingForHiltonEnabled();
    }

    static Boolean isST19ForDatafeedMSRTEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isSt19ForDatafeedMSRTEnabled();
    }

    static Boolean isST19ForDatafeedMSRTDisabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return !datafeedEndPointCriteria.isSt19ForDatafeedMSRTEnabled();
    }

    static Boolean isProductRateShopDefinitionEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isProductRateShopDefinitionEnabled() && !datafeedEndPointCriteria.isProductRateShopDefinitionEnhancedEnabled();
    }

    static Boolean isProductRateShopDefinitionEnhancedEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isProductRateShopDefinitionEnabled() && datafeedEndPointCriteria.isProductRateShopDefinitionEnhancedEnabled();
    }

    static Boolean isProductClassificationEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isProductClassificationEnabled();
    }

    static Boolean isProductChildPricingTypeEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isAgeBasedPricingForHiltonEnabled() && datafeedEndPointCriteria.isHiltonClientCode();
    }

    static Boolean isBenefitMeasurementEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isBenefitMeasurementEnabled() && !datafeedEndPointCriteria.isBenefitMeasurementWithProfitColumnsEnabled();
    }

    static Boolean isBenefitMeasurementWithProfitColumnsEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isBenefitMeasurementEnabled() && datafeedEndPointCriteria.isBenefitMeasurementWithProfitColumnsEnabled();
    }

    static Boolean isScheduledReportsEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isScheduledReportsEnabled();
    }

    static Boolean isChannelForecastEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isChannelForecastEnabled();
    }

    static Boolean isProductFreeNightDefinitionEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isHiltonConsortiaFreeNightEnabled() && datafeedEndPointCriteria.isProductFreeNightDefinitionEnabled();
    }

    static Boolean isProductGroupProductDefinitionEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isProductGroupProductDefinitionEnabled();
    }

    static Boolean isGroupPricingSCMarketSegmentMappingEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isGroupPricingSCMarketSegmentMappingEnabled();
    }

    static Boolean isGroupPricingSCRoomTypeMappingEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isGroupPricingSCRoomTypeMappingEnabled();
    }

    static Boolean isRolePermissionEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return !datafeedEndPointCriteria.isRolePermissionWithRankColumnEnabled();
    }

    static Boolean isRolePermissionWithRankColumnEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isRolePermissionWithRankColumnEnabled();
    }

    static Boolean isRateShoppingOccupancyBasedCMPCDatafeedEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isRateShoppingOccupancyBasedCMPCEnabled();
    }

    static Boolean isRateShoppingIgnoreChannelConfigDatafeedEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isRateShoppingIgnoreChannelConfigEnabled();
    }

    static Boolean isDiscontinuedRTInRoomClassConfigDataFeedEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isDiscontinuedRTEnabledInRoomClassConfigDataFeed();
    }

    static Boolean isDiscontinuedRTInRoomClassConfigDataFeedDisabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return !datafeedEndPointCriteria.isDiscontinuedRTEnabledInRoomClassConfigDataFeed();
    }

    static Boolean isMeetingPackagePricingDataFeedEnabled(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        return datafeedEndPointCriteria.isMeetingPackagePricingDataFeedEnabled();
    }
}
