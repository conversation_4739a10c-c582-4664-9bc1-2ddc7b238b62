package com.ideas.tetris.pacman.services.reports.mcatmapping;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.RatchetCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.mcatmapping.dto.MCATMappingDTO;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.JasperReportDataConverter;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.MCATMappingReportConverter;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.MCATMappingReportCriteria;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportData;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.scheduledreport.service.JasperReportService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.TimeZone;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment.GET_DISTINCT_COMBINATION_OF_MAPPED_MARKET_CODES_AND_MARKET_CODES;

import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment.GET_DISTINCT_COMBINATION_OF_MAPPED_MARKET_CODES_AND_MARKET_CODES;

@Component
@Transactional
public class MCATMappingReportService extends JasperReportService<ScheduledReportData, MCATMappingReportCriteria> {

    private static final Logger LOGGER = Logger.getLogger(MCATMappingReportService.class.getName());
    private static final String MCAT_MAPPING_REPORT = "mcat-mapping-report";

    @MCATMappingReportConverter.Qualifier
    @Autowired
	@Qualifier("MCATMappingReportConverter")
	private JasperReportDataConverter<List<MCATMappingDTO>, MCATMappingReportCriteria> mcatMappingReportConverter;

    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    @RatchetCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("ratchetCrudServiceBean")
    CrudService ratchetCrudService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    @SuppressWarnings({"unchecked", "Duplicates"})
    private List<MCATMappingDTO> generateMCATMappingList(CrudService globalOrRatchetCrudService,
                                                         ScheduledReport<MCATMappingReportCriteria> scheduledReport) {

        Integer propertyId;

        if (scheduledReport != null) {
            propertyId = Integer.valueOf(scheduledReport.getReportCriteria().getPropertyId().toString());
        } else {
            propertyId = PacmanWorkContextHelper.getPropertyId();
        }

        try {
            return getMcatMappingDTOS(globalOrRatchetCrudService, propertyId);
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<>();
        }
    }

    public List<MCATMappingDTO> getMcatMappingDTOS(CrudService globalOrRatchetCrudService, Integer propertyId) {
        QueryParameter queryParameters = QueryParameter.with("propertyId", propertyId);
        if (null == globalOrRatchetCrudService) {
            List<Object[]> resultList = crudService.findByNativeQuery("select * from dbo.ufn_get_srp_submcat_fg_original_mkt_mapping(:propertyId)",
                    queryParameters.parameters());
            return getMcatMappingDTOsFromResultList(resultList);
        } else {
            List<Object[]> resultList = crudService.findByNativeQuery("select * from dbo.ufn_get_srp_submcat_fg_mapping(:propertyId)",
                    queryParameters.parameters());
            ArrayList<String> rateCodes = new ArrayList<>();
            ArrayList<String> marketSegmentCodes = new ArrayList<>();
            ArrayList<String> forecastGroups = new ArrayList<>();

            for (Object[] object : resultList) {
                rateCodes.add((String) object[0]);
                marketSegmentCodes.add((String) object[1]);
                forecastGroups.add((String) object[2]);
            }
            return generateSubMCATAndMCATMappingList(globalOrRatchetCrudService, rateCodes, marketSegmentCodes, forecastGroups);
        }
    }

    @SuppressWarnings("unchecked")
    private List<MCATMappingDTO> generateSubMCATAndMCATMappingList(CrudService globalOrRatchetCrudService,
                                                                   List<String> rateCodes,
                                                                   List<String> marketSegmentCodes,
                                                                   List<String> forecastGroups) {

        String rateCodeParams = StringUtils.join(rateCodes, "##");
        String marketSegmentParams = StringUtils.join(marketSegmentCodes, "##");
        String forecastGroupParams = StringUtils.join(forecastGroups, "##");
        QueryParameter queryParameters = QueryParameter.with("rateCodes", rateCodeParams)
                .and("marketSegments", marketSegmentParams).and("forecastGroups", forecastGroupParams);

        String query = "select * from dbo.ufn_get_submcat_mcat_fg_mapping(:rateCodes, :marketSegments, :forecastGroups)";

        List<Object[]> resultList = globalOrRatchetCrudService.findByNativeQuery(query, queryParameters.parameters());

		return getMcatMappingDTOS(resultList);
	}

	List<MCATMappingDTO> getMcatMappingDTOS(List<Object[]> resultList) {
		if(isHiltonIppEnabled()){
			return getMcatMappingDTOsFromResultListForHiltonIpp(resultList);
		}
		List<MCATMappingDTO> mcatMappingDTOS = getMcatMappingDTOsFromResultList(resultList);
		return mcatMappingDTOS;
	}

	List<MCATMappingDTO> getMcatMappingDTOsFromResultListForHiltonIpp(List<Object[]> resultList) {
		List<Object[]> distinctSubMcatToOriMcatCombinations = crudService.findByNamedQuery(GET_DISTINCT_COMBINATION_OF_MAPPED_MARKET_CODES_AND_MARKET_CODES);
		Map<String, String> subMcatToOriMcatMap = distinctSubMcatToOriMcatCombinations.stream()
				.collect(Collectors.toMap(row -> row[0].toString(), row -> row[1].toString()));
		Set<MCATMappingDTO> mcatMappings = new LinkedHashSet<>();
		for (Object[] object : resultList) {
			MCATMappingDTO mcatMappingDTO = createMcatMappingDtoWithoutOriMcat(object);
			mcatMappingDTO.setOriginalMarketSegment(subMcatToOriMcatMap.get(object[1].toString()));
			mcatMappings.add(mcatMappingDTO);
		}
		return new ArrayList<>(mcatMappings);
	}

	List<MCATMappingDTO> getMcatMappingDTOsFromResultList(List<Object[]> resultList) {
		Set<MCATMappingDTO> mcatMappings = new LinkedHashSet<>();
		for (Object[] object : resultList) {
			MCATMappingDTO mcatMappingDTO = createMcatMappingDtoWithoutOriMcat(object);
			mcatMappingDTO.setOriginalMarketSegment(object[2].toString());
			mcatMappings.add(mcatMappingDTO);
		}
		return new ArrayList<>(mcatMappings);
	}

	MCATMappingDTO createMcatMappingDtoWithoutOriMcat(Object[] object) {
		MCATMappingDTO mcatMappingDTO = new MCATMappingDTO();
		mcatMappingDTO.setRateCode(object[0].toString());
		mcatMappingDTO.setMarketSegment(object[1].toString());
		mcatMappingDTO.setForecastGroup(object[3].toString());
		return mcatMappingDTO;
	}

	boolean isHiltonIppEnabled(){
		return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_HILTON_IPP_ENABLED);
	}

    public List<MCATMappingDTO> generateMCATMapping() {
        return generateMCATMapping(null);
    }

    public List<MCATMappingDTO> generateMCATMapping(ScheduledReport<MCATMappingReportCriteria> scheduledReport) {
        String externalSystem = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value()).toLowerCase();
        if (Constants.OPERA.equals(externalSystem)) {
            return generateMCATMappingList(null, scheduledReport);
        } else if (Constants.REZVIEW.toLowerCase().equals(externalSystem)) {
            return generateMCATMappingList(ratchetCrudService, scheduledReport);
        }
        return generateMCATMappingList(getGlobalCrudService(), scheduledReport);
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    @Override
    public String getReportTitle(ScheduledReport<MCATMappingReportCriteria> scheduledReport) {
        return ResourceUtil.getText(MCAT_MAPPING_REPORT, scheduledReport.getLanguage());
    }

    @Override
    protected ScheduledReportData retrieveData(ScheduledReport<MCATMappingReportCriteria> scheduledReport) {
        populateReportCriteria(scheduledReport);
        List<MCATMappingDTO> dataList = generateMCATMapping(scheduledReport);

        ScheduledReportSheet sheet = new ScheduledReportSheet(MCAT_MAPPING_REPORT, dataList, MCATMappingDTO.class);
        List<ScheduledReportSheet> sheetList = new ArrayList<ScheduledReportSheet>(1);
        sheetList.add(sheet);
        return new ScheduledReportData(MCAT_MAPPING_REPORT, sheetList);
    }

    private void populateReportCriteria(ScheduledReport<MCATMappingReportCriteria> scheduledReport) {
        Integer propertyId;
        if (scheduledReport != null) {
            propertyId = Integer.valueOf(scheduledReport.getReportCriteria().getPropertyId().toString());
            Property property = getGlobalCrudService().find(Property.class, propertyId);
            TimeZone timeZone = getAlertService().getPropertyTimeZone(property);
            scheduledReport.getReportCriteria().setCreatedOn(ScheduledReportUtils.convertDateTimeToTimeZone(new DateTime(), timeZone));
        }
    }

    @Override
    protected JasperReportDataConverter<ScheduledReportData, MCATMappingReportCriteria> getJasperReportDataConverter() {
        return null;
    }
}
