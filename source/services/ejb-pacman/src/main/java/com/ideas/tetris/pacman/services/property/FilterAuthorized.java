package com.ideas.tetris.pacman.services.property;

import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ConsolidatedPropertyView;
import com.ideas.tetris.pacman.services.property.dto.PropertySearchCriteria;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
@Transactional
public class FilterAuthorized {
    private static final Logger LOGGER = Logger.getLogger(FilterAuthorized.class.getName());

    @Autowired
	protected AuthorizationService authorizationService;

    public List<ConsolidatedPropertyView> filter(final List<ConsolidatedPropertyView> properties, final PropertySearchCriteria searchCriteria) {
        // internal users may step right over the velvet rope
        TetrisPrincipal principal = PacmanThreadLocalContextHolder.getPrincipal();
        if (principal != null && principal.isInternalUser()) {
            return properties;
        }

        List<ConsolidatedPropertyView> filteredProperties = new ArrayList<ConsolidatedPropertyView>();

        List<Property> authorizedProperties = authorizationService.retrieveAuthorizedProperties();
        if (authorizedProperties != null && !authorizedProperties.isEmpty()) {
            Set<Integer> authorizedPropertyIds = new HashSet<Integer>();
            for (Property property : authorizedProperties) {
                authorizedPropertyIds.add(property.getId());
            }

            for (ConsolidatedPropertyView propertyView : properties) {
                if (authorizedPropertyIds.contains(propertyView.getPropertyId())) {
                    filteredProperties.add(propertyView);
                }
            }
        }

        return filteredProperties;
    }
}
