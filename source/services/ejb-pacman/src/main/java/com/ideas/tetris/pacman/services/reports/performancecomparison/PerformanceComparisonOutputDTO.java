package com.ideas.tetris.pacman.services.reports.performancecomparison;

/**
 * Created by idnmal on 3/25/14.
 */

import com.ideas.tetris.pacman.services.scheduledreport.reportapi.annotations.ColumnHeader;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.expressions.RoomRateExpression;

import java.math.BigDecimal;

public class PerformanceComparisonOutputDTO {

    @ColumnHeader(titleKey = "daysToArrival", order = 1, pattern = "##0")
    Integer daystoArrival;

    @ColumnHeader(titleKey = "performanceComparisonReport.occupancy.analysis.period", order = 2, pattern = "##0")
    BigDecimal occupancyOnBooksForAnalysis;

    @ColumnHeader(titleKey = "performanceComparisonReport.occupancy.comparison.period", order = 3, pattern = "##0")
    BigDecimal occupancyOnBooksForComparison;

    @ColumnHeader(titleKey = "performanceComparisonReport.occupancy.forecast.period", order = 4, pattern = "##0.#")
    BigDecimal occupancyForecastForAnalysis;
    BigDecimal competitorRate;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitorName($data)", "analysis.period"}, order = 6,
            condition = "showCompetitorName")
    String competitorName;

    @ColumnHeader(titleKey = {"report.column.barByDayForRC", "method:getRoomClassName($data)", "analysis.period"}, order = 5,
            expressionClass = RoomRateExpression.class, condition = "showRateColumn($data)")
    String rateName;
    @ColumnHeader(titleKey = "common.occupancyForecast", order = 7, condition = "false")
    BigDecimal rate;
    String barOverride;
    String roomClassName;

    public Integer getDaystoArrival() {
        return daystoArrival;
    }

    public void setDaystoArrival(Integer daystoArrival) {
        this.daystoArrival = daystoArrival;
    }

    public BigDecimal getOccupancyOnBooksForAnalysis() {
        return occupancyOnBooksForAnalysis;
    }

    public void setOccupancyOnBooksForAnalysis(BigDecimal occupancyOnBooksForAnalysis) {
        this.occupancyOnBooksForAnalysis = occupancyOnBooksForAnalysis;
    }

    public BigDecimal getOccupancyOnBooksForComparison() {
        return occupancyOnBooksForComparison;
    }

    public void setOccupancyOnBooksForComparison(BigDecimal occupancyOnBooksForComparison) {
        this.occupancyOnBooksForComparison = occupancyOnBooksForComparison;
    }

    public BigDecimal getOccupancyForecastForAnalysis() {
        return occupancyForecastForAnalysis;
    }

    public void setOccupancyForecastForAnalysis(BigDecimal occupancyForecastForAnalysis) {
        this.occupancyForecastForAnalysis = occupancyForecastForAnalysis;
    }

    public BigDecimal getCompetitorRate() {
        return competitorRate;
    }

    public void setCompetitorRate(BigDecimal competitorRate) {
        this.competitorRate = competitorRate;
    }

    public String getCompetitorName() {
        return competitorName;
    }

    public void setCompetitorName(String competitorName) {
        this.competitorName = competitorName;
    }

    public String getRateName() {
        return rateName;
    }

    public void setRateName(String rateName) {
        this.rateName = rateName;
    }

    public String getRoomClassName() {
        return roomClassName;
    }

    public void setRoomClassName(String roomClassName) {
        this.roomClassName = roomClassName;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public String getBarOverride() {
        return barOverride;
    }

    public void setBarOverride(String barOverride) {
        this.barOverride = barOverride;
    }
}


