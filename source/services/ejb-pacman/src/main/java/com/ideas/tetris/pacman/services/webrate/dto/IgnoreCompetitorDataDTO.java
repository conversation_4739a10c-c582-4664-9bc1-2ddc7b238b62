package com.ideas.tetris.pacman.services.webrate.dto;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateChannel;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateOverrideCompetitor;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateOverrideCompetitorDetails;
import com.ideas.tetris.pacman.services.webrate.seasonenum.SeasonFilterType;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Builder
@AllArgsConstructor
@Getter
@Setter
public class IgnoreCompetitorDataDTO {
    List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetails;
    Set<WebrateCompetitors> webrateCompetitors;
    Set<AccomClass> accomClasses;
    LocalDate startDate;
    LocalDate endDate;
    Set<WebrateChannel> webrateChannels;
    Set<DayOfWeek> selectedDaysOfWeek;
    String notes;
    SeasonFilterType seasonFilterType;
    String groupKey;
    String originalNote;
    LocalDate originalStartDate;
    LocalDate originalEndDate;
    boolean dateHasChanges;
    boolean competitorHasChanges;
    boolean accomClassHasChanges;
    boolean dowHasChanges;
    boolean channelHasChanges;
    boolean notesHasChanges;

    public IgnoreCompetitorDataDTO() {
        webrateCompetitors = new HashSet<>();
        accomClasses = new HashSet<>();
        webrateChannels = new HashSet<>();
        selectedDaysOfWeek = new HashSet<>(Arrays.asList(DayOfWeek.values()));
        notes = "";
        groupKey = "";
    }

    public Set<WebrateCompetitors> getWebrateCompetitors() {
        return webrateCompetitors;
    }

    public void setWebrateCompetitors(Set<WebrateCompetitors> webrateCompetitors) {
        this.webrateCompetitors = webrateCompetitors;
    }

    public Set<AccomClass> getAccomClasses() {
        return accomClasses;
    }

    public void setAccomClasses(Set<AccomClass> accomClasses) {
        this.accomClasses = accomClasses;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Set<DayOfWeek> getSelectedDaysOfWeek() {
        return selectedDaysOfWeek;
    }

    public void setSelectedDaysOfWeek(Set<DayOfWeek> selectedDaysOfWeek) {
        this.selectedDaysOfWeek = selectedDaysOfWeek;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public void setDaysOfWeek(WebrateOverrideCompetitor webrateOverrideCompetitor) {
        selectedDaysOfWeek = new HashSet<>();
        addDowToSelectedDowSet(webrateOverrideCompetitor.getIgnoreCompetitorDataOnMonday(), DayOfWeek.MONDAY);
        addDowToSelectedDowSet(webrateOverrideCompetitor.getIgnoreCompetitorDataOnTuesday(), DayOfWeek.TUESDAY);
        addDowToSelectedDowSet(webrateOverrideCompetitor.getIgnoreCompetitorDataOnWednesday(), DayOfWeek.WEDNESDAY);
        addDowToSelectedDowSet(webrateOverrideCompetitor.getIgnoreCompetitorDataOnThursday(), DayOfWeek.THURSDAY);
        addDowToSelectedDowSet(webrateOverrideCompetitor.getIgnoreCompetitorDataOnFriday(), DayOfWeek.FRIDAY);
        addDowToSelectedDowSet(webrateOverrideCompetitor.getIgnoreCompetitorDataOnSaturday(), DayOfWeek.SATURDAY);
        addDowToSelectedDowSet(webrateOverrideCompetitor.getIgnoreCompetitorDataOnSunday(), DayOfWeek.SUNDAY);
    }

    private void addDowToSelectedDowSet(Integer isIgnoreCompetitorOnDow, DayOfWeek dayOfWeek) {
        if (isIgnoreCompetitorOnDow.equals(1)) {
            selectedDaysOfWeek.add(dayOfWeek);
        }
    }

    public List<IgnoreCompetitorDataDTO> getGroupedIgnoreCompetitorDTO(List<IgnoreCompetitorDataDTO> dtos) {
        Map<String, IgnoreCompetitorDataDTO> groupedData = new HashMap<>();
        Map<String, IgnoreCompetitorDataDTO> competitorGroupedData = new HashMap<>();

        dtos.forEach(dto -> groupedData.merge(dto.getGroupKey(), dto, this::mergeDtos));
        groupedData.values().forEach(dto -> competitorGroupedData.merge(getGroupBasedOnCompetitorNames(dto), dto, this::mergeDtos));

        return competitorGroupedData.values().stream().sorted(Comparator.comparing(IgnoreCompetitorDataDTO::getStartDate))
                .collect(Collectors.toList());
    }

    public IgnoreCompetitorDataDTO mergeDtos(IgnoreCompetitorDataDTO dto1, IgnoreCompetitorDataDTO dto2) {
        setGroupedWebrateOverrrideCompetitorDetails(dto1, dto2);
        setGroupedWebrateCompetitors(dto1, dto2);
        setGroupedAccomClasses(dto1, dto2);
        return dto1;
    }

    private void setGroupedWebrateOverrrideCompetitorDetails(IgnoreCompetitorDataDTO dto, IgnoreCompetitorDataDTO dto2) {
        Set<WebrateOverrideCompetitorDetails> mergedDetails = new HashSet<>(dto.getWebrateOverrideCompetitorDetails());
        mergedDetails.addAll(dto2.getWebrateOverrideCompetitorDetails());
        dto.setWebrateOverrideCompetitorDetails(new ArrayList<>(mergedDetails));
    }

    private void setGroupedWebrateCompetitors(IgnoreCompetitorDataDTO dto, IgnoreCompetitorDataDTO dto2) {
        Set<WebrateCompetitors> existingCompetitors = dto.getWebrateCompetitors();
        existingCompetitors.addAll(dto2.getWebrateCompetitors());
        dto.setWebrateCompetitors(existingCompetitors);
    }

    private void setGroupedAccomClasses(IgnoreCompetitorDataDTO dto, IgnoreCompetitorDataDTO dto2) {
        Set<AccomClass> mergedAccomClasses = dto.getAccomClasses();
        mergedAccomClasses.addAll(dto2.getAccomClasses());
        dto.setAccomClasses(mergedAccomClasses);
    }

    private String getGroupBasedOnCompetitorNames(IgnoreCompetitorDataDTO dto) {
        return dto.startDate + "|" + dto.endDate + "|" + dto.selectedDaysOfWeek + "|" +
                dto.getWebrateCompetitors().stream().map(WebrateCompetitors::getWebrateCompetitorsName).collect(Collectors.toSet()) + "|" +
                dto.webrateChannels + "|" + dto.notes;
    }

    public List<WebrateOverrideCompetitorDetails> getWebrateOverrideCompetitorDetails() {
        return webrateOverrideCompetitorDetails;
    }

    public void setWebrateOverrideCompetitorDetails(List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetails) {
        this.webrateOverrideCompetitorDetails = webrateOverrideCompetitorDetails;
    }

    public Set<WebrateChannel> getWebrateChannels() {
        return webrateChannels;
    }

    public void setWebrateChannels(Set<WebrateChannel> webrateChannels) {
        this.webrateChannels = webrateChannels;
    }

    public SeasonFilterType getSeasonFilterType() {
        return seasonFilterType;
    }

    public void setSeasonFilterType(SeasonFilterType seasonFilterType) {
        this.seasonFilterType = seasonFilterType;
    }

    public String getGroupKey() {
        return groupKey;
    }

    public void setDtoGroupKey(IgnoreCompetitorDataDTO dto) {
        this.groupKey = dto.startDate + "|" + dto.endDate + "|" + dto.selectedDaysOfWeek + "|" +
                dto.accomClasses.stream().map(AccomClass::getName).collect(Collectors.toSet()) + "|" +
                dto.webrateChannels.stream().map(WebrateChannel::getWebrateChannelName).collect(Collectors.toSet()) + "|" +
                dto.notes;
    }

    public boolean hasChanges(IgnoreCompetitorDataDTO dto) {
        return dto.isDateHasChanges() || dto.isCompetitorHasChanges() || dto.isAccomClassHasChanges() ||
                dto.isChannelHasChanges() || dto.isDowHasChanges() || dto.isNotesHasChanges();
    }
}
