package com.ideas.tetris.pacman.services.dailybar.constants;

import org.springframework.aop.SpringProxy;
public interface AgileRatesConstants extends SpringProxy {

    String RATES_WITH_TAX = "" +
            " ,(Single_Rate      + (Single_Rate      * :taxFactor)) AS Single_Rate " +
            " ,(Double_Rate      + (Double_Rate      * :taxFactor)) AS Double_Rate " +
            " ,(Triple_Rate      + (Triple_Rate      * :taxFactor)) AS Triple_Rate " +
            " ,(Quad_Rate        + (Quad_Rate        * :taxFactor)) AS Quad_Rate " +
            " ,(Quint_Rate       + (Quint_Rate       * :taxFactor)) AS Quint_Rate " +
            " ,(Adult_Rate       + (Adult_Rate       * :taxFactor)) AS Adult_Rate " +
            " ,(Child_Rate       + (Child_Rate       * :taxFactor)) AS Child_Rate " +
            " ,(One_Child_Rate   + (One_Child_Rate   * :taxFactor)) AS One_Child_Rate " +
            " ,(Two_Child_Rate   + (Two_Child_Rate   * :taxFactor)) AS Two_Child_Rate " +
            " ,(Three_Child_Rate + (Three_Child_Rate * :taxFactor)) AS Three_Child_Rate " +
            " ,(Four_Child_Rate  + (Four_Child_Rate  * :taxFactor)) AS Four_Child_Rate " +
            " ,(Five_Child_Rate  + (Five_Child_Rate  * :taxFactor)) AS Five_Child_Rate " +
            " ,(Child_Age_1_Rate + (Child_Age_1_Rate * :taxFactor)) AS Child_Age_1_Rate " +
            " ,(Child_Age_2_Rate + (Child_Age_2_Rate * :taxFactor)) AS Child_Age_2_Rate " +
            " ,(Child_Age_3_Rate + (Child_Age_3_Rate * :taxFactor)) AS Child_Age_3_Rate " +
            " ,(Child_Age_4_Rate + (Child_Age_4_Rate * :taxFactor)) AS Child_Age_4_Rate " +
            " FROM ( ";

    String RATES_WITH_ADJUSTMENT = "" +
            "  , p.Name as Rate_Code_Name " +
            "  ,(Single_Rate + :miscAdjustment) AS Single_Rate " +
            "  ,(Double_Rate + :miscAdjustment) AS Double_Rate " +
            "  ,(Triple_Rate + :miscAdjustment) AS Triple_Rate " +
            "  ,(Quad_Rate   + :miscAdjustment) AS Quad_Rate " +
            "  ,(Quint_Rate  + :miscAdjustment) AS Quint_Rate " +
            "  ,Adult_Rate, Child_Rate " +
            "  ,One_Child_Rate, Two_Child_Rate, Three_Child_Rate, Four_Child_Rate, Five_Child_Rate " +
            "  ,Child_Age_1_Rate, Child_Age_2_Rate, Child_Age_3_Rate, Child_Age_4_Rate" +
            "  FROM Decision_Dailybar_Output dbo " +
            "  INNER JOIN Accom_Type at ON dbo.Accom_Type_Id = at.Accom_Type_ID " +
            "  INNER JOIN Rate_Unqualified rq ON dbo.Rate_Unqualified_ID = rq.Rate_Unqualified_ID " +
            "  INNER JOIN Product p ON dbo.Product_ID = p.Product_ID AND p.Is_Upload = 1 and p.code = 'AGILE_RATES'";

    private static String roundedRates(int roundingDepth) {
        return " ,CAST((CASE WHEN Single_Rate<0      THEN 0 ELSE Single_Rate      END) AS DECIMAL(19," + roundingDepth + ")) AS Single_Rate " +
                " ,CAST((CASE WHEN Double_Rate<0      THEN 0 ELSE Double_Rate      END) AS DECIMAL(19," + roundingDepth + ")) AS Double_Rate " +
                " ,CAST((CASE WHEN Triple_Rate<0      THEN 0 ELSE Triple_Rate      END) AS DECIMAL(19," + roundingDepth + ")) AS Triple_Rate " +
                " ,CAST((CASE WHEN Quad_Rate<0        THEN 0 ELSE Quad_Rate        END) AS DECIMAL(19," + roundingDepth + ")) AS Quad_Rate " +
                " ,CAST((CASE WHEN Quint_Rate<0       THEN 0 ELSE Quint_Rate       END) AS DECIMAL(19," + roundingDepth + ")) AS Quint_Rate " +
                " ,CAST((CASE WHEN Adult_Rate<0       THEN 0 ELSE Adult_Rate       END) AS DECIMAL(19," + roundingDepth + ")) AS Adult_Rate " +
                " ,CAST((CASE WHEN Child_Rate<0       THEN 0 ELSE Child_Rate       END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Rate " +
                " ,CAST((CASE WHEN One_Child_Rate<0   THEN 0 ELSE One_Child_Rate   END) AS DECIMAL(19," + roundingDepth + ")) AS One_Child_Rate " +
                " ,CAST((CASE WHEN Two_Child_Rate<0   THEN 0 ELSE Two_Child_Rate   END) AS DECIMAL(19," + roundingDepth + ")) AS Two_Child_Rate " +
                " ,CAST((CASE WHEN Three_Child_Rate<0 THEN 0 ELSE Three_Child_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Three_Child_Rate " +
                " ,CAST((CASE WHEN Four_Child_Rate<0  THEN 0 ELSE Four_Child_Rate  END) AS DECIMAL(19," + roundingDepth + ")) AS Four_Child_Rate " +
                " ,CAST((CASE WHEN Five_Child_Rate<0  THEN 0 ELSE Five_Child_Rate  END) AS DECIMAL(19," + roundingDepth + ")) AS Five_Child_Rate " +
                " ,CAST((CASE WHEN Child_Age_1_Rate<0 THEN 0 ELSE Child_Age_1_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Age_1_Rate " +
                " ,CAST((CASE WHEN Child_Age_2_Rate<0 THEN 0 ELSE Child_Age_2_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Age_2_Rate " +
                " ,CAST((CASE WHEN Child_Age_3_Rate<0 THEN 0 ELSE Child_Age_3_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Age_3_Rate " +
                " ,CAST((CASE WHEN Child_Age_4_Rate<0 THEN 0 ELSE Child_Age_4_Rate END) AS DECIMAL(19," + roundingDepth + ")) AS Child_Age_4_Rate ";
    }

    static String getAllDecisionAgileRatesOutputFullForAllRoomClassesUpToDecimalForDates(int roundingDepth) {
        return "" +
                " SELECT Occupancy_Date, Accom_Type_Code, Rate_Code_Name " + roundedRates(roundingDepth) +
                " FROM ( " +
                "     SELECT Occupancy_Date, Accom_Type_Code, Rate_Code_Name " + RATES_WITH_TAX +
                "         SELECT Occupancy_Date, Accom_Type_Code " + RATES_WITH_ADJUSTMENT +
                "         WHERE dbo.Occupancy_Date IN (:dates) " +
                "     ) dailybarwithmisc " +
                " ) dailybarwithmiscandtax ORDER BY Occupancy_Date, Accom_Type_Code, Rate_Code_Name";
    }

    String FULL_DECISIONS_FOR_AGILE_RATES = new StringBuilder()
            .append("with AgileProduct as ( ")
            .append("    select Product_ID, Name as Rate_Code_Name  ")
            .append("    from  Product ")
            .append("    where Is_Upload = 1 and Status_ID = 1 and Code <> 'BAR' ")
            .append("), ")
            .append("Decisions as ( ")
            .append("    select ")
            .append("        decisions.Occupancy_Date, ")
            .append("        roomtype.Accom_Type_Code, ")
            .append("        agileProduct.Rate_Code_Name, ")
            .append("        decisions.Single_Rate, decisions.Double_Rate, decisions.Triple_Rate, decisions.Quad_Rate, decisions.Quint_Rate, ")
            .append("        decisions.Adult_Rate, decisions.Child_Rate, ")
            .append("        decisions.One_Child_Rate, decisions.Two_Child_Rate, decisions.Three_Child_Rate, decisions.Four_Child_Rate, decisions.Five_Child_Rate, ")
            .append("        decisions.Child_Age_1_Rate, decisions.Child_Age_2_Rate, decisions.Child_Age_3_Rate, decisions.Child_Age_4_Rate ")
            .append("    from ")
            .append("        Decision_Dailybar_Output decisions  ")
            .append("        join Accom_Type roomtype ")
            .append("        on decisions.Accom_Type_ID = roomtype.Accom_Type_ID ")
            .append("        join AgileProduct agileProduct ")
            .append("        on decisions.Product_ID = agileProduct.Product_ID ")
            .append("    where ")
            .append("       decisions.Occupancy_Date >= :decisionStartDate and decisions.Occupancy_Date <= :decisionEndDate ")
            .append("), ")
            .append("DecisionsWithMiscAdjustment as ( ")
            .append("    select ")
            .append("        Occupancy_Date, ")
            .append("        Accom_Type_Code, ")
            .append("        Rate_Code_Name, ")
            .append("        (Single_Rate + :miscAdjustment) as Single_Rate,  ")
            .append("        (Double_Rate + :miscAdjustment) as Double_Rate,  ")
            .append("        (Triple_Rate + :miscAdjustment) as Triple_Rate,  ")
            .append("        (Quad_Rate + :miscAdjustment) as Quad_Rate,  ")
            .append("        (Quint_Rate + :miscAdjustment) as Quint_Rate, ")
            .append("        Adult_Rate, Child_Rate, ")
            .append("        One_Child_Rate, Two_Child_Rate, Three_Child_Rate, Four_Child_Rate, Five_Child_Rate, ")
            .append("        Child_Age_1_Rate, Child_Age_2_Rate, Child_Age_3_Rate, Child_Age_4_Rate ")
            .append("    from ")
            .append("        Decisions decisions  ")
            .append("), ")
            .append("DecisionsWithTax as ( ")
            .append("    select ")
            .append("        Occupancy_Date, ")
            .append("        Accom_Type_Code, ")
            .append("        Rate_Code_Name, ")
            .append("        (Single_Rate + (Single_Rate * :taxFactor)) as Single_Rate,  ")
            .append("        (Double_Rate + (Double_Rate * :taxFactor)) as Double_Rate,  ")
            .append("        (Triple_Rate + (Triple_Rate * :taxFactor)) as Triple_Rate,  ")
            .append("        (Quad_Rate + (Quad_Rate * :taxFactor)) as Quad_Rate,  ")
            .append("        (Quint_Rate + (Quint_Rate * :taxFactor)) as Quint_Rate, ")
            .append("        (Adult_Rate + (Adult_Rate * :taxFactor)) as Adult_Rate,  ")
            .append("        (Child_Rate + (Child_Rate * :taxFactor)) as Child_Rate, ")
            .append("        (One_Child_Rate + (One_Child_Rate * :taxFactor)) as One_Child_Rate,  ")
            .append("        (Two_Child_Rate + (Two_Child_Rate * :taxFactor)) as Two_Child_Rate,  ")
            .append("        (Three_Child_Rate + (Three_Child_Rate * :taxFactor)) as Three_Child_Rate,  ")
            .append("        (Four_Child_Rate + (Four_Child_Rate * :taxFactor)) as Four_Child_Rate,  ")
            .append("        (Five_Child_Rate + (Five_Child_Rate * :taxFactor)) as Five_Child_Rate, ")
            .append("        (Child_Age_1_Rate + (Child_Age_1_Rate * :taxFactor)) as Child_Age_1_Rate,  ")
            .append("        (Child_Age_2_Rate + (Child_Age_2_Rate * :taxFactor)) as Child_Age_2_Rate,  ")
            .append("        (Child_Age_3_Rate + (Child_Age_3_Rate * :taxFactor)) as Child_Age_3_Rate, ")
            .append("        (Child_Age_4_Rate + (Child_Age_4_Rate * :taxFactor)) as Child_Age_4_Rate ")
            .append("    from ")
            .append("        DecisionsWithMiscAdjustment   ")
            .append("), ")
            .append("FinalDecisions as (")
            .append("    select ")
            .append("        Occupancy_Date, Accom_Type_Code, Rate_Code_Name, ")
            .append("        (case when Single_Rate < 0 then 0 else ROUND(Single_Rate,:roundingDepth) end) as Single_Rate, ")
            .append("        (case when Double_Rate < 0 then 0 else ROUND(Double_Rate,:roundingDepth) end) as Double_Rate, ")
            .append("        (case when Triple_Rate < 0 then 0 else ROUND(Triple_Rate,:roundingDepth) end) as Triple_Rate, ")
            .append("        (case when Quad_Rate < 0 then 0 else ROUND(Quad_Rate,:roundingDepth) end) as Quad_Rate, ")
            .append("        (case when Quint_Rate < 0 then 0 else ROUND(Quint_Rate,:roundingDepth) end) as Quint_Rate, ")
            .append("        (case when Adult_Rate < 0 then 0 else ROUND(Adult_Rate,:roundingDepth) end) as Adult_Rate, ")
            .append("        (case when Child_Rate < 0 then 0 else ROUND(Child_Rate,:roundingDepth) end) as Child_Rate, ")
            .append("        (case when One_Child_Rate < 0 then 0 else ROUND(One_Child_Rate,:roundingDepth) end) as One_Child_Rate, ")
            .append("        (case when Two_Child_Rate < 0 then 0 else ROUND(Two_Child_Rate,:roundingDepth) end) as Two_Child_Rate, ")
            .append("        (case when Three_Child_Rate < 0 then 0 else ROUND(Three_Child_Rate,:roundingDepth) end) as Three_Child_Rate, ")
            .append("        (case when Four_Child_Rate < 0 then 0 else ROUND(Four_Child_Rate,:roundingDepth) end) as Four_Child_Rate, ")
            .append("        (case when Five_Child_Rate < 0 then 0 else ROUND(Five_Child_Rate,:roundingDepth) end) as Five_Child_Rate, ")
            .append("        (case when Child_Age_1_Rate < 0 then 0 else ROUND(Child_Age_1_Rate,:roundingDepth) end) as Child_Age_1_Rate, ")
            .append("        (case when Child_Age_2_Rate < 0 then 0 else ROUND(Child_Age_2_Rate,:roundingDepth) end) as Child_Age_2_Rate, ")
            .append("        (case when Child_Age_3_Rate < 0 then 0 else ROUND(Child_Age_3_Rate,:roundingDepth) end) as Child_Age_3_Rate, ")
            .append("        (case when Child_Age_4_Rate < 0 then 0 else ROUND(Child_Age_4_Rate,:roundingDepth) end) as Child_Age_4_Rate ")
            .append("    from ")
            .append("        DecisionsWithTax ")
            .append(") ")
            .append("select * from FinalDecisions;").toString();

    String DIFFERENTIAL_DECISIONS_FOR_AGILE_RATES = new StringBuilder()
            .append("with AgileProduct as ( ")
            .append("    select  ")
            .append("        Product_ID, Name as Rate_Code_Name  ")
            .append("    from  ")
            .append("        Product ")
            .append("    where ")
            .append("        Is_Upload = 1 and Status_ID = 1 and Code <> 'BAR' ")
            .append("), ")
            .append("Decisions as ( ")
            .append("    select ")
            .append("        decisions.Occupancy_Date, ")
            .append("        roomtype.Accom_Type_ID, ")
            .append("        roomtype.Accom_Type_Code, ")
            .append("        agileProduct.Product_ID, ")
            .append("        agileProduct.Rate_Code_Name, ")
            .append("        decisions.Single_Rate, decisions.Double_Rate, decisions.Triple_Rate, decisions.Quad_Rate, decisions.Quint_Rate, ")
            .append("        decisions.Adult_Rate, decisions.Child_Rate, ")
            .append("        decisions.One_Child_Rate, decisions.Two_Child_Rate, decisions.Three_Child_Rate, decisions.Four_Child_Rate, decisions.Five_Child_Rate, ")
            .append("        decisions.Child_Age_1_Rate, decisions.Child_Age_2_Rate, decisions.Child_Age_3_Rate, decisions.Child_Age_4_Rate ")
            .append("    from ")
            .append("        Decision_Dailybar_Output decisions  ")
            .append("        join Accom_Type roomtype ")
            .append("        on decisions.Accom_Type_ID = roomtype.Accom_Type_ID ")
            .append("        join AgileProduct agileProduct ")
            .append("        on decisions.Product_ID = agileProduct.Product_ID ")
            .append("    where ")
            .append("        decisions.Occupancy_Date >= :decisionStartDate and decisions.Occupancy_Date <= :decisionEndDate  ")
            .append("), ")
            .append("Pace as ( ")
            .append("    select  ")
            .append("        Occupancy_Date, Accom_Type_ID, agileProduct.Product_ID, decision.Decision_ID,  ")
            .append("        Single_Rate, Double_Rate, Triple_Rate, Quad_Rate, Quint_Rate, ")
            .append("        Adult_Rate, Child_Rate, ")
            .append("        One_Child_Rate, Two_Child_Rate, Three_Child_Rate, Four_Child_Rate, Five_Child_Rate, ")
            .append("        Child_Age_1_Rate, Child_Age_2_Rate, Child_Age_3_Rate, Child_Age_4_Rate, ")
            .append("        rank() over (partition by occupancy_date,accom_type_ID,pace.product_Id order by decision.Decision_ID desc) as rank ")
            .append("    from  ")
            .append("        PACE_Dailybar_Output pace  ")
            .append("        join Decision decision ")
            .append("        on pace.Decision_ID = decision.Decision_ID ")
            .append("        join AgileProduct agileProduct ")
            .append("        on pace.Product_ID = agileProduct.Product_ID ")
            .append("   where ")
            .append("        pace.Occupancy_Date >= :decisionStartDate and pace.Occupancy_Date <= :decisionEndDate and ")
            .append("        decision.End_DTTM  <= :lastUploadedDate")
            .append("), ")
            .append("DecisionsAfterComparison as ( ")
            .append("    select  ")
            .append("        decisions.*  ")
            .append("    from  ")
            .append("        Decisions decisions ")
            .append("        left join Pace pace ")
            .append("        on  ")
            .append("        decisions.Accom_Type_ID = pace.Accom_Type_ID and ")
            .append("        decisions.Occupancy_Date = pace.Occupancy_Date and ")
            .append("        decisions.Product_ID = pace.Product_ID and ")
            .append("        pace.rank = 1 ")
            .append("    where ")
            .append("        decisions.Single_Rate <> pace.Single_Rate or ")
            .append("        decisions.Double_Rate <> pace.Double_Rate or ")
            .append("        isNull(decisions.Triple_Rate, -1) <> isNull(pace.Triple_Rate, -1) or ")
            .append("        isNull(decisions.Quad_Rate, -1) <> isNull(pace.Quad_Rate, -1) or ")
            .append("        isNull(decisions.Quint_Rate, -1) <> isNull(pace.Quint_Rate, -1) or ")
            .append("        decisions.Adult_Rate <> pace.Adult_Rate or ")
            .append("        decisions.Child_Rate <> pace.Child_Rate or ")
            .append("        isNull(decisions.One_Child_Rate, -1) <> isNull(pace.One_Child_Rate, -1) or ")
            .append("        isNull(decisions.Two_Child_Rate, -1) <> isNull(pace.Two_Child_Rate, -1) or ")
            .append("        isNull(decisions.Three_Child_Rate, -1) <> isNull(pace.Three_Child_Rate, -1) or ")
            .append("        isNull(decisions.Four_Child_Rate, -1) <> isNull(pace.Four_Child_Rate, -1) or ")
            .append("        isNull(decisions.Five_Child_Rate, -1) <> isNull(pace.Five_Child_Rate, -1) or ")
            .append("        isNull(decisions.Child_Age_1_Rate, -1) <> isNull(pace.Child_Age_1_Rate, -1) or  ")
            .append("        isNull(decisions.Child_Age_2_Rate, -1) <> isNull(pace.Child_Age_2_Rate, -1) or  ")
            .append("        isNull(decisions.Child_Age_3_Rate, -1) <> isNull(pace.Child_Age_3_Rate, -1) or  ")
            .append("        isNull(decisions.Child_Age_4_Rate, -1) <> isNull(pace.Child_Age_4_Rate, -1) or  ")
            .append("        pace.Occupancy_Date is null ")
            .append("), ")
            .append("DecisionsWithMiscAdjustment as ( ")
            .append("    select ")
            .append("        Occupancy_Date, Accom_Type_Code, Rate_Code_Name, ")
            .append("        (Single_Rate + :miscAdjustment) as Single_Rate,  ")
            .append("        (Double_Rate + :miscAdjustment) as Double_Rate,  ")
            .append("        (Triple_Rate + :miscAdjustment) as Triple_Rate,  ")
            .append("        (Quad_Rate + :miscAdjustment) as Quad_Rate,  ")
            .append("        (Quint_Rate + :miscAdjustment) as Quint_Rate, ")
            .append("        Adult_Rate, Child_Rate, ")
            .append("        One_Child_Rate, Two_Child_Rate, Three_Child_Rate, Four_Child_Rate, Five_Child_Rate, ")
            .append("        Child_Age_1_Rate, Child_Age_2_Rate, Child_Age_3_Rate, Child_Age_4_Rate ")
            .append("    from ")
            .append("        DecisionsAfterComparison decisions  ")
            .append("), ")
            .append("DecisionsWithTax as ( ")
            .append("    select ")
            .append("        Occupancy_Date, Accom_Type_Code, Rate_Code_Name, ")
            .append("        (Single_Rate + (Single_Rate * :taxFactor)) as Single_Rate,  ")
            .append("        (Double_Rate + (Double_Rate * :taxFactor)) as Double_Rate,  ")
            .append("        (Triple_Rate + (Triple_Rate * :taxFactor)) as Triple_Rate,  ")
            .append("        (Quad_Rate + (Quad_Rate * :taxFactor)) as Quad_Rate,  ")
            .append("        (Quint_Rate + (Quint_Rate * :taxFactor)) as Quint_Rate, ")
            .append("        (Adult_Rate + (Adult_Rate * :taxFactor)) as Adult_Rate,  ")
            .append("        (Child_Rate + (Child_Rate * :taxFactor)) as Child_Rate, ")
            .append("        (One_Child_Rate + (One_Child_Rate * :taxFactor)) as One_Child_Rate,  ")
            .append("        (Two_Child_Rate + (Two_Child_Rate * :taxFactor)) as Two_Child_Rate,  ")
            .append("        (Three_Child_Rate + (Three_Child_Rate * :taxFactor)) as Three_Child_Rate,  ")
            .append("        (Four_Child_Rate + (Four_Child_Rate * :taxFactor)) as Four_Child_Rate,  ")
            .append("        (Five_Child_Rate + (Five_Child_Rate * :taxFactor)) as Five_Child_Rate, ")
            .append("        (Child_Age_1_Rate + (Child_Age_1_Rate * :taxFactor)) as Child_Age_1_Rate,  ")
            .append("        (Child_Age_2_Rate + (Child_Age_2_Rate * :taxFactor)) as Child_Age_2_Rate,  ")
            .append("        (Child_Age_3_Rate + (Child_Age_3_Rate * :taxFactor)) as Child_Age_3_Rate, ")
            .append("        (Child_Age_4_Rate + (Child_Age_4_Rate * :taxFactor)) as Child_Age_4_Rate ")
            .append("    from ")
            .append("        DecisionsWithMiscAdjustment   ")
            .append("), ")
            .append("FinalDecisions as ( ")
            .append("    select ")
            .append("        Occupancy_Date, Accom_Type_Code, Rate_Code_Name, ")
            .append("        (case when Single_Rate < 0 then 0 else ROUND(Single_Rate,:roundingDepth) end) as Single_Rate, ")
            .append("        (case when Double_Rate < 0 then 0 else ROUND(Double_Rate,:roundingDepth) end) as Double_Rate, ")
            .append("        (case when Triple_Rate < 0 then 0 else ROUND(Triple_Rate,:roundingDepth) end) as Triple_Rate, ")
            .append("        (case when Quad_Rate < 0 then 0 else ROUND(Quad_Rate,:roundingDepth) end) as Quad_Rate, ")
            .append("        (case when Quint_Rate < 0 then 0 else ROUND(Quint_Rate,:roundingDepth) end) as Quint_Rate, ")
            .append("        (case when Adult_Rate < 0 then 0 else ROUND(Adult_Rate,:roundingDepth) end) as Adult_Rate, ")
            .append("        (case when Child_Rate < 0 then 0 else ROUND(Child_Rate,:roundingDepth) end) as Child_Rate, ")
            .append("        (case when One_Child_Rate < 0 then 0 else ROUND(One_Child_Rate,:roundingDepth) end) as One_Child_Rate, ")
            .append("        (case when Two_Child_Rate < 0 then 0 else ROUND(Two_Child_Rate,:roundingDepth) end) as Two_Child_Rate, ")
            .append("        (case when Three_Child_Rate < 0 then 0 else ROUND(Three_Child_Rate,:roundingDepth) end) as Three_Child_Rate, ")
            .append("        (case when Four_Child_Rate < 0 then 0 else ROUND(Four_Child_Rate,:roundingDepth) end) as Four_Child_Rate, ")
            .append("        (case when Five_Child_Rate < 0 then 0 else ROUND(Five_Child_Rate,:roundingDepth) end) as Five_Child_Rate, ")
            .append("        (case when Child_Age_1_Rate < 0 then 0 else ROUND(Child_Age_1_Rate,:roundingDepth) end) as Child_Age_1_Rate, ")
            .append("        (case when Child_Age_2_Rate < 0 then 0 else ROUND(Child_Age_2_Rate,:roundingDepth) end) as Child_Age_2_Rate, ")
            .append("        (case when Child_Age_3_Rate < 0 then 0 else ROUND(Child_Age_3_Rate,:roundingDepth) end) as Child_Age_3_Rate, ")
            .append("        (case when Child_Age_4_Rate < 0 then 0 else ROUND(Child_Age_4_Rate,:roundingDepth) end) as Child_Age_4_Rate ")
            .append("    from ")
            .append("        DecisionsWithTax ")
            .append(") ")
            .append("select * from FinalDecisions")
            .toString();

}
