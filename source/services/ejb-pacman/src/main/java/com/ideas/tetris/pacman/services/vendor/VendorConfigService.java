package com.ideas.tetris.pacman.services.vendor;

import java.util.List;

import org.springframework.aop.SpringProxy;
public interface VendorConfigService extends SpringProxy {
    void save(VendorConfig vendorConfig);

    void createPropertyConfig(String inboundVendorId, String chainCode, HotelConfigParams hotelConfigParams);

    void updatePropertyConfig(String inboundVendorId, String clientCode, HotelConfigParams hotelConfigParams);

    void deletePropertyConfig(String inboundVendorId, String clientCode, String propertyCode);

    void addFeatureToggles(String clientCode, HotelConfigParams hotel);

    List<VendorConfig> findAll();

    VendorConfig find(String inboundVendorId);

    VendorConfig find(String inboundVendorId, String clientCode, String propertyCode);
}
