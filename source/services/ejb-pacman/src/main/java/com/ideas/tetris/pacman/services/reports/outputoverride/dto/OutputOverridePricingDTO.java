package com.ideas.tetris.pacman.services.reports.outputoverride.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ideas.tetris.pacman.services.datafeed.util.LocalizingSerializer;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.annotations.ColumnHeader;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.datatypes.PropertyValueType;
import com.ideas.tetris.platform.common.rest.unmarshaller.DateTimeSerializer;

import java.util.Date;

public class OutputOverridePricingDTO {

    @ColumnHeader(titleKey = "report.dow", order = 2, type = PropertyValueType.class)
    private String dow;
    @ColumnHeader(titleKey = "common.arrivalDate", order = 3)
    private Date arrivalDate;
    @ColumnHeader(titleKey = "common.propertyName", order = 1)
    private String propertyName;
    @ColumnHeader(titleKey = "roomClass", order = 4)
    private String accomClassName;
    private String accomClassCode;
    private Integer los;
    @ColumnHeader(titleKey = "overrideCategory", order = 5, type = PropertyValueType.class)
    @JsonSerialize(using = LocalizingSerializer.class)
    private String newOverride;
    @ColumnHeader(titleKey = "userSelection", order = 7)
    private String rateCodeName;
    @ColumnHeader(titleKey = "report.overrideLastModifiedBy", order = 11)
    private String userName;
    private String userEmail;
    @ColumnHeader(titleKey = "report.overrideLastModifiedOn", order = 10)
    private Date createDate;
    @ColumnHeader(titleKey = "report.systemSelection", order = 8)
    private String oldRateCodeName;
    private String oldOverride;
    @ColumnHeader(titleKey = "notes.label", order = 9)
    private String notes;
    private String isLos;
    private String accomTypeName;
    private String accomTypeCode;

    public String getDow() {
        return dow;
    }

    public void setDow(String dow) {
        this.dow = dow;
    }

    public Date getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public String getAccomClassName() {
        return accomClassName;
    }

    public void setAccomClassName(String accomClassName) {
        this.accomClassName = accomClassName;
    }

    public Integer getLos() {
        return los;
    }

    public void setLos(Integer los) {
        this.los = los;
    }

    public String getNewOverride() {
        return newOverride;
    }

    public void setNewOverride(String newOverride) {
        this.newOverride = newOverride;
    }

    public String getRateCodeName() {
        return rateCodeName;
    }

    public void setRateCodeName(String rateCodeName) {
        this.rateCodeName = rateCodeName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @JsonSerialize(using = DateTimeSerializer.class)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getOldRateCodeName() {
        return oldRateCodeName;
    }

    public void setOldRateCodeName(String oldRateCodeName) {
        this.oldRateCodeName = oldRateCodeName;
    }

    public String getOldOverride() {
        return oldOverride;
    }

    public void setOldOverride(String oldOverride) {
        this.oldOverride = oldOverride;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getIsLos() {
        return isLos;
    }

    public void setIsLos(String isLos) {
        this.isLos = isLos;
    }

    public void setAccomClassCode(String accomClassCode) {
        this.accomClassCode = accomClassCode;
    }

    public String getAccomClassCode() {
        return accomClassCode;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getAccomTypeName() {
        return accomTypeName;
    }

    public void setAccomTypeName(String accomTypeName) {
        this.accomTypeName = accomTypeName;
    }

    public String getAccomTypeCode() {
        return accomTypeCode;
    }

    public void setAccomTypeCode(String accomTypeCode) {
        this.accomTypeCode = accomTypeCode;
    }
}
