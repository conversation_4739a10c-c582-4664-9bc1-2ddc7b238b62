package com.ideas.tetris.pacman.services.activity.converter;

import com.ideas.tetris.pacman.services.activity.converter.MarketSegmentActivityConverter.Qualifier;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ActivityEntity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceMktSegActivity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Qualifier
@Component
@Transactional
public class MarketSegmentActivityConverter extends PaceActivityConverter<MktSegAccomActivity, PaceMktSegActivity> {

    @Override
    public PaceMktSegActivity findExistingOrCreateNewPaceActivity(MktSegAccomActivity entity) {
        return null;
    }

    @Override
    public MktSegAccomActivity findExistingOrCreateNewActivity(Integer propertyId, Map<String, Object> dto, String correlationId, boolean isPast) {
        return null;
    }

    @Override
    public List<MktSegAccomActivity> findExistingOrCreateNewActivity(Integer propertyId, List<Map<String, Object>> dtos, String correlationId) {
        return Collections.emptyList();
    }

    @Override
    public Map<String, Object> convertFromEntity(ActivityEntity mktSegAccomActivity) {
        Map<String, Object> dto = super.convertFromEntity(mktSegAccomActivity);
        if (dto == null) {
            return null;
        }

        Integer mktSegId = null;
        if (mktSegAccomActivity instanceof MktSegAccomActivity) {
            mktSegId = ((MktSegAccomActivity) mktSegAccomActivity).getMktSegId();
        } else if (mktSegAccomActivity instanceof PaceMktSegActivity) {
            mktSegId = ((PaceMktSegActivity) mktSegAccomActivity).getMktSegId();
        }
        String marketSegmentCode = findMarketSegmentCodeForMarketSegmentId(mktSegId);
        dto.put(MARKET_SEGMENT_NAME, findMarketSegmentNameForCode(marketSegmentCode));

        dto.remove(MKT_SEG_ID);
        return dto;
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }


}
