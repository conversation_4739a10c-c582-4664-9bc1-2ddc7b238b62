package com.ideas.tetris.pacman.services.reports;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.entity.ScheduledReportDeliveryAudit;
import com.ideas.tetris.pacman.services.reports.userreport.ScheduledReportAuditExecutionType;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import javax.inject.Inject;
import java.util.Date;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class ScheduledReportAuditService {
    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    public ScheduledReportDeliveryAudit createAuditEntry(Integer scheduleId, ScheduledReportAuditStatus scheduledReportAuditStatus, String reportName, Date businessDate, String reason, ScheduledReportAuditExecutionType executionType) {
        ScheduledReportDeliveryAudit scheduledReportDeliveryAudit = findAuditByScheduleIdAndBusinessDT(scheduleId, businessDate);
        if (scheduledReportDeliveryAudit == null) {
            scheduledReportDeliveryAudit = new ScheduledReportDeliveryAudit();
            scheduledReportDeliveryAudit.setScheduleId(scheduleId);
            scheduledReportDeliveryAudit.setReportType(reportName);
            scheduledReportDeliveryAudit.setBusinessDate(businessDate);
        }
        scheduledReportDeliveryAudit.setFailedScheduleDTTM((reason != null) ? new Date() : null);
        scheduledReportDeliveryAudit.setReason(reason);
        scheduledReportDeliveryAudit.setStatus(scheduledReportAuditStatus.name());
        scheduledReportDeliveryAudit.setExecutionType(executionType.name());
        return crudService.save(scheduledReportDeliveryAudit);
    }

    public ScheduledReportDeliveryAudit findAuditByScheduleIdAndBusinessDT(Integer scheduleId, Date businessDate) {
        return crudService.findByNamedQuerySingleResult(ScheduledReportDeliveryAudit.GET_AUDIT_BY_SCHEDULE_ID_BUSINESS_DATE, QueryParameter.with("businessDate", businessDate).
                and("scheduleId", scheduleId).parameters());
    }

    public int deleteAuditEntriesForScheduleId(int scheduleId) {
        return crudService.executeUpdateByNamedQuery(ScheduledReportDeliveryAudit.DELETE_AUDIT_BY_SCHEDULE_ID,
                QueryParameter.with("scheduleId", scheduleId).parameters());
    }

    public Long getFailureStatusCount(Date businessDate) {
        return crudService.findByNamedQuerySingleResult(ScheduledReportDeliveryAudit.GET_FAILED_STATUS_COUNT, QueryParameter.with("businessDate", businessDate).parameters());
    }
}
