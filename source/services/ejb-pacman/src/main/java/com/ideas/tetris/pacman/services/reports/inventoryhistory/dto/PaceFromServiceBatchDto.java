package com.ideas.tetris.pacman.services.reports.inventoryhistory.dto;

import com.ideas.tetris.platform.common.crudservice.TableBatch;
import com.ideas.tetris.platform.common.crudservice.TableBatchAware;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.sql.Timestamp;
import java.sql.Types;
import java.util.Date;

@Data
@AllArgsConstructor
public class PaceFromServiceBatchDto implements TableBatchAware {
    public static final String PACE_FROM_SERVICE_INSERT = "usp_Pace_From_Service_Insert";
    private static final String PACE_FROM_SERVICE_BATCH = "Pace_From_Service_Batch";

    private Date arrivalDate;
    private Integer rateQualifiedId;
    private Integer accomTypeId;
    private String fplos;
    private Integer decisionId;
    private Date createDate;

    @Override
    public String getTableVariableName() {
        return PACE_FROM_SERVICE_BATCH;
    }

    @Override
    public void addTableBatchColumns(TableBatch tableBatch) {
        tableBatch.addColumn("Arrival_DT", Types.DATE);
        tableBatch.addColumn("Rate_Qualified_Id", Types.BIGINT);
        tableBatch.addColumn("Accom_Type_Id", Types.BIGINT);
        tableBatch.addColumn("FPLOS", Types.NVARCHAR);
        tableBatch.addColumn("Decision_Id", Types.BIGINT);
        tableBatch.addColumn("CreateDate_DTTM", Types.TIMESTAMP);
    }

    @Override
    public Object[] toTableBatchRow() {
        return new Object[]{
                getArrivalDate() != null ? new java.sql.Date(getArrivalDate().getTime()) : null,
                getRateQualifiedId(),
                getAccomTypeId(),
                getFplos(),
                getDecisionId(),
                new Timestamp(getCreateDate().getTime())
        };
    }
}