package com.ideas.tetris.pacman.services.reports.individualgroupwash.dto;

import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;

import java.util.ArrayList;
import java.util.List;

public class IndividualGroupMasterDto {
    private Integer id;
    private String name;

    private GroupBlockMaster groupMaster;

    private List<IndividualGroupBlockTableDto> individualGroupBlockTableDtos = new ArrayList<>();

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public GroupBlockMaster getGroupMaster() {
        return groupMaster;
    }

    public void setGroupMaster(GroupBlockMaster groupMaster) {
        this.groupMaster = groupMaster;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<IndividualGroupBlockTableDto> getIndividualGroupBlockTableDtos() {
        return individualGroupBlockTableDtos;
    }

    public void setIndividualGroupBlockTableDtos(List<IndividualGroupBlockTableDto> individualGroupBlockTableDtos) {
        this.individualGroupBlockTableDtos = individualGroupBlockTableDtos;
    }

}
