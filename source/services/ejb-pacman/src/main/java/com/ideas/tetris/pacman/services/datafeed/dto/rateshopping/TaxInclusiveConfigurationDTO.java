package com.ideas.tetris.pacman.services.datafeed.dto.rateshopping;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.DateSerializer;

import java.math.BigDecimal;
import java.util.Date;


public class TaxInclusiveConfigurationDTO {

    private String defaultRoomRevenueTax;
    private String category;
    private String seasonName;
    private Date seasonStartDate;
    private Date seasonEndDate;
    private BigDecimal tax;

    public TaxInclusiveConfigurationDTO() {

    }

    public TaxInclusiveConfigurationDTO(String defaultRoomRevenueTax, String category, String seasonName, Date seasonStartDate, Date seasonEndDate, BigDecimal tax) {
        this.defaultRoomRevenueTax = defaultRoomRevenueTax;
        this.category = category;
        this.seasonName = seasonName;
        this.seasonStartDate = seasonStartDate;
        this.seasonEndDate = seasonEndDate;
        this.tax = tax;
    }

    public String getDefaultRoomRevenueTax() {
        return defaultRoomRevenueTax;
    }

    public void setDefaultRoomRevenueTax(String defaultRoomRevenueTax) {
        this.defaultRoomRevenueTax = defaultRoomRevenueTax;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSeasonName() {
        return seasonName;
    }

    public void setSeasonName(String seasonName) {
        this.seasonName = seasonName;
    }


    @JsonSerialize(using = DateSerializer.class)
    public Date getSeasonStartDate() {
        return seasonStartDate;
    }

    public void setSeasonStartDate(Date seasonStartDate) {
        this.seasonStartDate = seasonStartDate;
    }

    @JsonSerialize(using = DateSerializer.class)
    public Date getSeasonEndDate() {
        return seasonEndDate;
    }

    public void setSeasonEndDate(Date seasonEndDate) {
        this.seasonEndDate = seasonEndDate;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }
}
