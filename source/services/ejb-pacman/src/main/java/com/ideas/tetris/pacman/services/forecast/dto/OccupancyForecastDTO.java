package com.ideas.tetris.pacman.services.forecast.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class OccupancyForecastDTO {

    private String currencyCode;

    private LocalDate occupancyDate;

    private BigDecimal lv0Decision;

    private BigDecimal forecastedADR;

    private BigDecimal masterClassForecastedADR;

    private BigDecimal forecastedOccupancyPercent;

    private boolean isLimitedDataBuild;
}
