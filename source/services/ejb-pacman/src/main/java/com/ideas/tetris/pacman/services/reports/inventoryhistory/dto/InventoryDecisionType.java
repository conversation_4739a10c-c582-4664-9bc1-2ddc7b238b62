package com.ideas.tetris.pacman.services.reports.inventoryhistory.dto;

public enum InventoryDecisionType {
    FPLOS(1, "FPLOS", "FPLOS"),
    OVERBOOKING(2, "overBooking", "Overbooking"),
    PRICING(3, "pricing", "Pricing"),
    ALL(4, "all", "all"),
    INVENTORY_LIMIT(5, "inventory.limit", "InventoryLimit");

    private int ordinal;
    private String caption;
    private String paramValue;

    InventoryDecisionType(int ordinal, String caption, String paramValue) {
        this.ordinal = ordinal;
        this.caption = caption;
        this.paramValue = paramValue;
    }

    public String getCaption() {
        return caption;
    }

    public String getParamValue() {
        return paramValue;
    }

    public boolean isAll() {
        return this.equals(ALL);
    }

    public boolean isPricing() {
        return this.equals(PRICING);
    }
}
