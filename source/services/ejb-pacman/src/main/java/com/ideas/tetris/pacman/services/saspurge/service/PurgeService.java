package com.ideas.tetris.pacman.services.saspurge.service;

import com.ideas.sas.service.SASClientService;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.xml.schema.ISASRequest;
import com.ideas.tetris.pacman.common.xml.schema.purge.request.v1.PurgeRequestType;
import com.ideas.tetris.pacman.common.xml.schema.purge.request.v1.RequestHeaderType;
import com.ideas.tetris.pacman.common.xml.schema.purge.request.v1.SASRequest;
import com.ideas.tetris.pacman.common.xml.schema.purge.response.v1.SASResponse;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.purge.SASDataSetLocator;
import com.ideas.tetris.pacman.services.sasinvocationbase.AbstractSasInvocation;
import com.ideas.tetris.pacman.util.jaxb.purge.request.PurgeRequestJAXBUtil.PurgeRequestQualifier;
import com.ideas.tetris.pacman.util.jaxb.purge.response.PurgeResponseJAXBUtil.PurgeResponseQualifier;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.util.jaxb.JAXBUtilLocal;
import com.ideas.tetris.platform.common.util.xml.XmlHelper;
import com.ideas.tetris.platform.common.util.xml.XmlHelperImpl;
import com.ideas.tetris.platform.common.utils.systemconfig.SASSettings;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.xml.datatype.XMLGregorianCalendar;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.getOutputDecisionsFilesRetentionDays;



@Component
@Transactional
public class PurgeService extends AbstractSasInvocation<PurgeRequestType> {
    private static final String OPERATION_NAME = "datapurge";
    private static final int HISTORY_LENGTH_DAYS = SystemConfig.getPurgeSasHistoryDays();
    private static final Logger LOGGER = Logger.getLogger(PurgeService.class);

    @PurgeRequestQualifier
	@Qualifier("purgeRequestJAXBUtil")
    @Autowired
    private JAXBUtilLocal requestJaxbUtil;
    @PurgeResponseQualifier
	@Qualifier("purgeResponseJAXBUtil")
    @Autowired
    private JAXBUtilLocal responseJaxbUtil;
    @Autowired
    private SASDataSetLocator sasDataSetLocator;
    @Autowired
    private SASClientService sasClientService;

    public static final String DELETED = "Deleted";

    @Override
    protected String getOperationName() {
        return OPERATION_NAME;
    }

    @Override
    protected String getStoredProc() {
        return SASSettings.getDataPurgeProcName();
    }

    @Override
    protected String getRequestMap() {
        return SASSettings.getPurgeRequestMap();
    }

    @Override
    protected JAXBUtilLocal getJaxbUtil() {
        return requestJaxbUtil;
    }

    public void setJaxbUtil(JAXBUtilLocal jaxbUtil) {
        this.requestJaxbUtil = jaxbUtil;
    }

    @Override
    protected JAXBUtilLocal getResponseJaxbUtil() {
        return responseJaxbUtil;
    }

    public SASResponse executeInSync(String date) {
        Date currentBusinessDate = null;
        try {
            DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            currentBusinessDate = formatter.parse(date);

        } catch (ParseException e) {
            LOGGER.error("Exception occurred when parsing date", e);
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "Service threw exception when parsing date", e);
        }
        PurgeRequestType requestType = createPurgeRequestType(currentBusinessDate);
        return (SASResponse) executeSync(requestType);
    }

    public void execute(Date currentBusinessDate) {
        PurgeRequestType requestType = createPurgeRequestType(currentBusinessDate);
        execute(requestType);
    }

    private PurgeRequestType createPurgeRequestType(Date currentBusinessDate) {
        XmlHelper xmlHelper = new XmlHelperImpl();
        PurgeRequestType requestType = new PurgeRequestType();
        XMLGregorianCalendar value = xmlHelper.convertDateToXMLGregorian(currentBusinessDate);
        requestType.setCurrentBusinessDate(value);
        requestType.setHistoryLength(HISTORY_LENGTH_DAYS);
        return requestType;
    }

    @Override
    @SuppressWarnings("rawtypes")
    protected ISASRequest getSASRequest(PurgeRequestType requestType, Decision decision) {
        SASRequest sasRequest = new SASRequest();
        sasRequest.setRequestHeader(new RequestHeaderType());
        sasRequest.setPurgeRequest(requestType);
        return sasRequest;
    }

    public void deleteSasOptCsvFolderData(int propertyId, LocalDate caughtUpDate) {
        LocalDate maxRetentionPeriodDate = caughtUpDate.minusDays(getOutputDecisionsFilesRetentionDays());
        String csvFolder = sasDataSetLocator.getSASOptCsvFolder(propertyId);
        sasClientService.executeOptDirPurge(csvFolder, maxRetentionPeriodDate);
    }

    public void deleteSasOptTempFolderData(int propertyId) {
        String folder = sasDataSetLocator.getSASOptTempFolder(propertyId);
        try {
            sasClientService.executeFileOps(DELETE_FILE_QUIETLY, new HashMap<>(Map.of(DELETE_PATH, folder)));
            LOGGER.info(folder + " deleted");
        } catch (Exception e) {
            LOGGER.info("Could not delete " + folder + " - " + e.getMessage());
        }
    }

}
