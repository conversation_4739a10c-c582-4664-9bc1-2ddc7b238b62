package com.ideas.tetris.pacman.services.reports;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.threadpool.NamedDefaultThreadFactory;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.apache.commons.io.IOUtils;
import org.apache.log4j.Logger;

import javax.ws.rs.container.AsyncResponse;
import javax.ws.rs.container.Suspended;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.StreamingOutput;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Transactional
public class ReportJasperService {
    private static final Logger LOGGER = Logger.getLogger(ReportJasperService.class);
    private static final int TIMEOUT = 600; // 10 minutes


    public void getExcelFileFromJasperServer(@Suspended final AsyncResponse response, String reportURL) {
        response.setTimeout(TIMEOUT, TimeUnit.SECONDS);
        Executors.newSingleThreadExecutor(new NamedDefaultThreadFactory("jasper-report-download")).execute(() -> {
            downLoadReportAndWriteResponse(response, reportURL);
        });
    }

    @VisibleForTesting
	public
    void downLoadReportAndWriteResponse(@Suspended AsyncResponse response, String reportURL) {
        try {
            LOGGER.info("reportURL " + reportURL);
            HttpURLConnection httpConn = getHttpURLConnection(reportURL);
            String contentDispositionHeader = httpConn.getHeaderField("content-disposition") != null ?
                    httpConn.getHeaderField("content-disposition").replace("inline", "attachment") : "";
            StreamingOutput stream = getStreamingOutput(httpConn);
            Response jasperResponse = Response.ok(stream, MediaType.WILDCARD_TYPE).header("content-disposition", contentDispositionHeader).build();
            LOGGER.info("Report generation complete");
            response.resume(jasperResponse);
        } catch (MalformedURLException ex) {
            LOGGER.error("Something wrong with the building the URL to call jasper server in pacman.js - function postReportDataUsingPacmanServiceEndPointAsync(...", ex);
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "MalformedURLException");
        } catch (IOException ex) {
            LOGGER.error("Oops! Something wrong happened while building excel", ex);
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "IOException");
        } catch (Exception ex) {
            LOGGER.error("Oops! Something went wrong during async jasper call", ex);
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "Async Jasper Call Exception");
        }
    }

    public StreamingOutput getStreamingOutput(HttpURLConnection httpConn) throws IOException {
        InputStream inputStream = httpConn.getInputStream();
        return output -> {
            try {
                output.write(IOUtils.toByteArray(inputStream));
            } catch (Exception ex) {
                LOGGER.error("Oops! Something wrong happened while building excel", ex);
            }
        };
    }

    public HttpURLConnection getHttpURLConnection(String reportURL) throws IOException {
        URL url = new URL(reportURL);
        return (HttpURLConnection) url.openConnection();
    }

}
