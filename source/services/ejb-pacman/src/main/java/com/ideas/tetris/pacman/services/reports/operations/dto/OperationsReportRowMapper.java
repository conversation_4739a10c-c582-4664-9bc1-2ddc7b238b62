package com.ideas.tetris.pacman.services.reports.operations.dto;


import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

public class OperationsReportRowMapper implements RowMapper<OperationsReportRowMapper> {
    private static final int OCCUPANCY_DATE = 0;
    private static final int OCCUPANCY_OTB = 1;
    private static final int ARRIVAL_OTB = 2;
    private static final int DEPARTURES_OTB = 3;
    private static final int STAYTHRU_OTB = 4;
    private static final int ARRIVAL_FCST = 5;
    private static final int DEPARTURE_FCST = 6;
    private static final int STAYTHRU_FCST = 7;
    private static final int OCCUPANCY_FCST = 8;
    private static final int TOTAL_ACCOM_CAPACITY = 9;
    private static final int OUT_OF_ORDER = 10;
    private static final int SPECIAL_EVENT_NAME = 11;
    private static final int NUMBER_OF_ADULTS_OTB = 12;
    private static final int NUMBER_OF_CHILDREN_OTB = 13;
    private static final int NUMBER_OF_ADULTS = 14;
    private static final int NUMBER_OF_CHILDREN = 15;

    private LocalDate occupancyDate;
    private BigDecimal occupancyOnBooks;
    private Integer arrivalsOnBooks;
    private Integer departuresOnBooks;
    private Integer stayThruOnBooks;
    private Integer arrivalsForecast;
    private Integer departuresForecast;
    private Integer stayThruForecast;
    private BigDecimal occupancyForecast;
    private BigDecimal totalAccomCapacity;
    private BigDecimal outOfOrder;
    private String specialEventName;
    private Integer numberOfAdultsOnBooks;
    private Integer numberOfChildrenOnBooks;
    private Integer numberOfAdults;
    private Integer numberOfChildren;


    public LocalDate getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(LocalDate occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public BigDecimal getOccupancyOnBooks() {
        return occupancyOnBooks;
    }

    public void setOccupancyOnBooks(BigDecimal occupancyOnBooks) {
        this.occupancyOnBooks = occupancyOnBooks;
    }

    public Integer getArrivalsOnBooks() {
        return arrivalsOnBooks;
    }

    public void setArrivalsOnBooks(Integer arrivalsOnBooks) {
        this.arrivalsOnBooks = arrivalsOnBooks;
    }

    public Integer getDeparturesOnBooks() {
        return departuresOnBooks;
    }

    public void setDeparturesOnBooks(Integer departuresOnBooks) {
        this.departuresOnBooks = departuresOnBooks;
    }

    public Integer getStayThruOnBooks() {
        return stayThruOnBooks;
    }

    public void setStayThruOnBooks(Integer stayThruOnBooks) {
        this.stayThruOnBooks = stayThruOnBooks;
    }

    public Integer getDeparturesForecast() {
        return departuresForecast;
    }

    public void setDeparturesForecast(Integer departuresForecast) {
        this.departuresForecast = departuresForecast;
    }

    public Integer getStayThruForecast() {
        return stayThruForecast;
    }

    public void setStayThruForecast(Integer stayThruForecast) {
        this.stayThruForecast = stayThruForecast;
    }

    public Integer getArrivalsForecast() {
        return arrivalsForecast;
    }

    public void setArrivalsForecast(Integer arrivalsForecast) {
        this.arrivalsForecast = arrivalsForecast;
    }


    public BigDecimal getOccupancyForecast() {
        return occupancyForecast;
    }

    public void setOccupancyForecast(BigDecimal occupancyForecast) {
        this.occupancyForecast = occupancyForecast;
    }

    public BigDecimal getTotalAccomCapacity() {
        return totalAccomCapacity;
    }

    public void setTotalAccomCapacity(BigDecimal totalAccomCapacity) {
        this.totalAccomCapacity = totalAccomCapacity;
    }


    public BigDecimal getOutOfOrder() {
        return outOfOrder;
    }

    public void setOutOfOrder(BigDecimal outOfOrder) {
        this.outOfOrder = outOfOrder;
    }

    public String getSpecialEventName() {
        return specialEventName;
    }

    public void setSpecialEventName(String specialEventName) {
        this.specialEventName = specialEventName;
    }

    public Integer getNumberOfAdultsOnBooks() {
        return numberOfAdultsOnBooks;
    }

    public void setNumberOfAdultsOnBooks(Integer numberOfAdultsOnBooks) {
        this.numberOfAdultsOnBooks = numberOfAdultsOnBooks;
    }

    public Integer getNumberOfChildrenOnBooks() {
        return numberOfChildrenOnBooks;
    }

    public void setNumberOfChildrenOnBooks(Integer numberOfChildrenOnBooks) {
        this.numberOfChildrenOnBooks = numberOfChildrenOnBooks;
    }

    public Integer getNumberOfAdults() {
        return numberOfAdults;
    }

    public void setNumberOfAdults(Integer numberOfAdults) {
        this.numberOfAdults = numberOfAdults;
    }

    public Integer getNumberOfChildren() {
        return numberOfChildren;
    }

    public void setNumberOfChildren(Integer numberOfChildren) {
        this.numberOfChildren = numberOfChildren;
    }

    @Override
    public OperationsReportRowMapper mapRow(Object[] row) {
        OperationsReportRowMapper mapper = new OperationsReportRowMapper();
        mapper.setOccupancyDate(DateUtil.convertJavaUtilDateToLocalDate((Date) row[OCCUPANCY_DATE], true));
        mapper.setOccupancyOnBooks((BigDecimal) row[OCCUPANCY_OTB]);
        mapper.setArrivalsOnBooks((Integer) row[ARRIVAL_OTB]);
        mapper.setDeparturesOnBooks((Integer) row[DEPARTURES_OTB]);
        mapper.setStayThruOnBooks((Integer) row[STAYTHRU_OTB]);
        mapper.setArrivalsForecast((Integer) row[ARRIVAL_FCST]);
        mapper.setDeparturesForecast((Integer) row[DEPARTURE_FCST]);
        mapper.setStayThruForecast((Integer) row[STAYTHRU_FCST]);
        mapper.setOccupancyForecast((BigDecimal) row[OCCUPANCY_FCST]);
        mapper.setTotalAccomCapacity((BigDecimal) row[TOTAL_ACCOM_CAPACITY]);
        mapper.setOutOfOrder((BigDecimal) row[OUT_OF_ORDER]);
        mapper.setSpecialEventName((String) row[SPECIAL_EVENT_NAME]);
        mapper.setNumberOfAdultsOnBooks((Integer) row[NUMBER_OF_ADULTS_OTB]);
        mapper.setNumberOfChildrenOnBooks((Integer) row[NUMBER_OF_CHILDREN_OTB]);
        mapper.setNumberOfAdults((Integer) row[NUMBER_OF_ADULTS]);
        mapper.setNumberOfChildren((Integer) row[NUMBER_OF_CHILDREN]);

        return mapper;
    }
}
