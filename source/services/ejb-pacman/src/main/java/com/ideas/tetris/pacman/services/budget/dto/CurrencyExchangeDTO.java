package com.ideas.tetris.pacman.services.budget.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class CurrencyExchangeDTO {

    private String clientCode;
    private String propertyCode;
    private String fromCurrency;
    private String toCurrency;
    private LocalDate fromDate;
    private LocalDate toDate;
    private BigDecimal exchangeRate;

}
