package com.ideas.tetris.pacman.services.property.dto;

import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyForecast;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValue;
import com.ideas.tetris.pacman.services.reports.operations.dto.OperationsReportDTO;

import java.math.BigDecimal;

public class ActivitySummary {
    private int capacity;
    private int outOfOrder;
    private int onBooksOccupancy;
    private int onBooksArrivals;
    private int onBooksDepartures;
    private int onBooksStayThrus;
    private BigDecimal onBooksRoomRevenue = BigDecimal.ZERO;
    private int forecastOccupancy;
    private int forecastArrivals;
    private int forecastDepartures;
    private int forecastStayThrus;
    private BigDecimal forecastRoomRevenue = BigDecimal.ZERO;
    private BigDecimal barDecision;
    private BigDecimal lrv;

    public ActivitySummary() {

    }

    public void addOperationsDto(OperationsReportDTO dto) {
        capacity += dto.getCapacity();
        outOfOrder += dto.getOutOfOrder();
        onBooksArrivals += dto.getOnBooksArrivals();
        onBooksDepartures += dto.getOnBooksDepartures();
        onBooksStayThrus += dto.getOnBooksStayThrus();
        onBooksOccupancy += dto.getOnBooksOccupancy();
        forecastArrivals += dto.getForecastArrivals();
        forecastDepartures += dto.getForecastDepartures();
        forecastStayThrus += dto.getForecastStayThrus();
        forecastOccupancy += dto.getForecastOccupancy();
    }

    public void addOccupancyForecast(OccupancyForecast forecast) {
        forecastRoomRevenue = forecastRoomRevenue.add(forecast.getRevenue());
    }

    public void addTotalActivity(TotalActivity activity) {
        onBooksRoomRevenue = onBooksRoomRevenue.add(activity.getRoomRevenue());
    }

    public void addBarDecision(CPDecisionBAROutput decision) {
        barDecision = decision.getFinalBAR();
    }

    public void addLrvDecision(LastRoomValue decision) {
        lrv = decision.getValue();
    }

    public int getCapacity() {
        return capacity;
    }

    public void setCapacity(int capacity) {
        this.capacity = capacity;
    }

    public int getOutOfOrder() {
        return outOfOrder;
    }

    public void setOutOfOrder(int outOfOrder) {
        this.outOfOrder = outOfOrder;
    }

    public int getOnBooksOccupancy() {
        return onBooksOccupancy;
    }

    public void setOnBooksOccupancy(int onBooksOccupancy) {
        this.onBooksOccupancy = onBooksOccupancy;
    }

    public int getOnBooksArrivals() {
        return onBooksArrivals;
    }

    public void setOnBooksArrivals(int onBooksArrivals) {
        this.onBooksArrivals = onBooksArrivals;
    }

    public int getOnBooksDepartures() {
        return onBooksDepartures;
    }

    public void setOnBooksDepartures(int onBooksDepartures) {
        this.onBooksDepartures = onBooksDepartures;
    }

    public int getOnBooksStayThrus() {
        return onBooksStayThrus;
    }

    public void setOnBooksStayThrus(int onBooksStayThrus) {
        this.onBooksStayThrus = onBooksStayThrus;
    }

    public BigDecimal getOnBooksRoomRevenue() {
        return onBooksRoomRevenue;
    }

    public void setOnBooksRoomRevenue(BigDecimal onBooksRoomRevenue) {
        this.onBooksRoomRevenue = onBooksRoomRevenue;
    }

    public int getForecastOccupancy() {
        return forecastOccupancy;
    }

    public void setForecastOccupancy(int forecastOccupancy) {
        this.forecastOccupancy = forecastOccupancy;
    }

    public int getForecastArrivals() {
        return forecastArrivals;
    }

    public void setForecastArrivals(int forecastArrivals) {
        this.forecastArrivals = forecastArrivals;
    }

    public int getForecastDepartures() {
        return forecastDepartures;
    }

    public void setForecastDepartures(int forecastDepartures) {
        this.forecastDepartures = forecastDepartures;
    }

    public int getForecastStayThrus() {
        return forecastStayThrus;
    }

    public void setForecastStayThrus(int forecastStayThrus) {
        this.forecastStayThrus = forecastStayThrus;
    }

    public BigDecimal getForecastRoomRevenue() {
        return forecastRoomRevenue;
    }

    public void setForecastRoomRevenue(BigDecimal forecastRoomRevenue) {
        this.forecastRoomRevenue = forecastRoomRevenue;
    }

    public BigDecimal getBarDecision() {
        return barDecision;
    }

    public void setBarDecision(BigDecimal barDecision) {
        this.barDecision = barDecision;
    }

    public BigDecimal getLrv() {
        return lrv;
    }

    public void setLrv(BigDecimal lrv) {
        this.lrv = lrv;
    }
}
