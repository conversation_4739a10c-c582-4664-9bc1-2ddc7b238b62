package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.DataFeedEndpointRequestDTO;
import com.ideas.tetris.pacman.services.datafeed.endpoint.DatafeedEndPointCriteria;
import com.ideas.tetris.pacman.services.datafeed.endpoint.Endpoint;
import com.ideas.tetris.pacman.services.datafeed.endpoint.EndpointBucket;
import com.ideas.tetris.pacman.services.datafeed.endpoint.EndpointFrequencyType;
import com.ideas.tetris.pacman.services.datafeed.entity.DataFeedEndpoint;
import com.ideas.tetris.pacman.services.datafeed.entity.DataFeedFrequencyType;
import com.ideas.tetris.pacman.services.datafeed.entity.Datafeed;
import com.ideas.tetris.pacman.services.datafeed.entity.DatafeedIncludeHistory;
import com.ideas.tetris.pacman.services.extendedevaluation.entity.ExtendedEvaluationSchedule;
import com.ideas.tetris.pacman.services.extendedevaluation.service.ExtendedEvaluationService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.inject.Inject;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.EXTENDED_WINDOW_DAYS_FOR_DATAFEED;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class DatafeedEndpointService {
    private static final String FREQUENCY_TYPES = "frequencyTypes";
    public static final String CLIENT_ID = "clientId";

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService crudService;

    @Autowired
	public
    ExtendedEvaluationService extendedEvaluationService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    public Map<String, List<String>> getEndPointsMapByCriteria(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        List<DataFeedEndpoint> endpoints = getEndpoints(datafeedEndPointCriteria);
        return endpoints.stream()
                .filter(endpoint -> Endpoint.valueOf(getDatafeedName(endpoint)).getCondition().apply(datafeedEndPointCriteria))
                .collect(Collectors.groupingBy(endpoint -> endpoint.getDataFeedFrequencyType().getFrequencyTypeValue(),
                        Collectors.mapping(endpoint -> getDatafeedName(endpoint), Collectors.toList())));
    }

    public Map<String, List<DataFeedEndpointRequestDTO>> getEndPointsDTOMapByCriteria(DatafeedEndPointCriteria datafeedEndPointCriteria, boolean includeEndpoints) {
        List<DataFeedEndpoint> endpoints = getEndpoints(datafeedEndPointCriteria);
        return endpoints.stream()
                .filter(endpoint -> Endpoint.valueOf(getDatafeedName(endpoint)).getCondition().apply(datafeedEndPointCriteria))
                .collect(Collectors.groupingBy(endpoint -> endpoint.getDataFeedFrequencyType().getFrequencyTypeValue(),
                        Collectors.mapping(endpoint -> getDatafeedRequestDTO(endpoint, includeEndpoints), Collectors.toList())));
    }

    private List<DataFeedEndpoint> getEndpoints(DatafeedEndPointCriteria datafeedEndPointCriteria) {
        Set<String> endpointBuckets = datafeedEndPointCriteria.getClientBuckets().stream().map(EndpointBucket::name).collect(Collectors.toSet());
        Set<String> frequencyTypes = datafeedEndPointCriteria.getFrequencies().stream().map(EndpointFrequencyType::name).collect(Collectors.toSet());
        return crudService.findByNamedQuery(DataFeedEndpoint.ENDPOINTS_FOR_BUCKETS_AND_FREQUENCY,
                QueryParameter.with("frequencyTypes", frequencyTypes).and("bucketsName", endpointBuckets).and("clientCode", datafeedEndPointCriteria.getClientCode()).parameters());

    }

    public Map<String, List<DataFeedEndpointRequestDTO>> getEndPointsDTOMapByCriteriaWithPaceFiles(DatafeedEndPointCriteria datafeedEndPointCriteria, boolean includeEndpoints) {
        List<DataFeedEndpoint> endpoints = getEndpoints(datafeedEndPointCriteria);
        Predicate<DataFeedEndpoint> allOptixFileEndPoints = dataFeedEndpoint -> Endpoint.valueOf(getDatafeedName(dataFeedEndpoint)).getCondition().apply(datafeedEndPointCriteria);
        Predicate<DataFeedEndpoint> optixPaceFileEndPoints = isIncludePaceFileEndpoints();
        return endpoints.stream().filter(allOptixFileEndPoints.or(optixPaceFileEndPoints)).collect(Collectors.groupingBy(endpoint -> endpoint.getDataFeedFrequencyType().getFrequencyTypeValue(),
                Collectors.mapping(endpoint -> getDatafeedRequestDTO(endpoint, includeEndpoints), Collectors.toList())));
    }

    private Predicate<DataFeedEndpoint> isIncludePaceFileEndpoints() {
        List<String> optixPaceFilesEndPoints = Arrays.asList("OPTIX_PACE_TOTAL_ACTIVITY", "OPTIX_PACE_GROUP_BLOCK", "OPTIX_PACE_GROUP_MASTER", "OPTIX_RESERVATION_NIGHT_CHANGE", "OPTIX_PACE_WEBRATE", "OPTIX_LRV_PACE");
        return endpoint -> (optixPaceFilesEndPoints.contains(endpoint.getDatafeed().getDatafeedName()) && shouldIncludeHistoryFor(endpoint.getDatafeed().getDatafeedType()));
    }

    private DataFeedEndpointRequestDTO getDatafeedRequestDTO(DataFeedEndpoint endpoint, boolean includeEndpoints) {
        if (includeEndpoints) {
            return new DataFeedEndpointRequestDTO(getDatafeedName(endpoint), endpoint.getPageSize(), endpoint.getStartDateOffset(), endpoint.getDatafeed().getMvcUrl());
        }
        return new DataFeedEndpointRequestDTO(getDatafeedName(endpoint), endpoint.getPageSize(), endpoint.getStartDateOffset());
    }

    private String getDatafeedName(DataFeedEndpoint endpoint) {
        return Optional.ofNullable(endpoint.getDatafeed()).map(Datafeed::getDatafeedName).orElse(StringUtils.EMPTY);
    }


    public boolean shouldIncludeHistoryFor(String datafeedType) {
        return isIncludeHistoryConfiguredInDBFor(datafeedType);
    }

    private boolean isIncludeHistoryConfiguredInDBFor(String datafeedType) {
        final Integer includeHistoryRecordCount = crudService.findByNamedQuerySingleResult(DatafeedIncludeHistory.COUNT_OF_DATAFEED_FOR_PROPERTY,
                QueryParameter.with(DatafeedIncludeHistory.DATAFEED_TYPE, datafeedType)
                        .and(DatafeedIncludeHistory.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .parameters());
        return includeHistoryRecordCount != null && 0 != includeHistoryRecordCount;
    }

    public boolean isHistoryDataOffsetApplicable(DatafeedQueryEndpoint datafeedQueryEndpoint) {
        final Datafeed datafeed = crudService.findByNamedQuerySingleResult(Datafeed.FETCH_DATAFEED_BY_TYPE,
                QueryParameter.with(Datafeed.DATAFEED_TYPE, datafeedQueryEndpoint.getEntityClass().getSimpleName()).parameters());
        return Optional.ofNullable(datafeed).map(Datafeed::isHistoryFeedOffsetApplicable).orElse(false);
    }

    public void resetIncludeHistoryFor(List<String> restEndpoints, String propertyCode) {
        if (CollectionUtils.isEmpty(restEndpoints)) {
            return;
        }
        crudService.executeUpdateByNamedQuery(DatafeedIncludeHistory.DELETE_DATAFEEDS_FOR_PROPERTY,
                QueryParameter.with(DatafeedIncludeHistory.DATAFEED_NAMES, restEndpoints)
                        .and(DatafeedIncludeHistory.PROPERTY_CODE, propertyCode).parameters());
    }

    public List<DataFeedEndpoint> getDataFeedEndpointsFor(Integer clientId, List<String> endpointFrequencyTypes) {
        return crudService.findByNamedQuery(DataFeedEndpoint.FETCH_DATAFEED_DETAILS,
                QueryParameter.with(FREQUENCY_TYPES, endpointFrequencyTypes).and(CLIENT_ID, clientId).parameters());
    }

    public void updateDataFeedFrequencies(Integer clientId, Map<String, DataFeedFrequencyType> endpointIdWithUpdatedFrequencyType) {
        List<DataFeedEndpoint> availableEndpointsForClient = getDataFeedEndpointsFor(clientId, getValidFrequencyTypes());
        List<String> validBuckets = Arrays.asList("CORE", "INFO_MANAGER", "SYS_CONFIG", "SYS_OVERRIDE", "USER_ACTIVITY");
        List<DataFeedEndpoint> endpointsToUpdate = availableEndpointsForClient.stream()
                .filter(dataFeedEndpoint -> validBuckets.contains(dataFeedEndpoint.getDatafeedEndpointBucket().getBucketName()))
                .filter(dataFeedEndpoint -> endpointIdWithUpdatedFrequencyType.containsKey(dataFeedEndpoint.getDatafeed().getDatafeedFileName()))
                .collect(Collectors.toList());
        endpointsToUpdate.forEach(dataFeedEndpoint -> dataFeedEndpoint.setDataFeedFrequencyType(endpointIdWithUpdatedFrequencyType.get(dataFeedEndpoint.getDatafeed().getDatafeedFileName())));
        crudService.save(endpointsToUpdate);
    }

    private List<String> getValidFrequencyTypes() {
        return Arrays.asList(
                EndpointFrequencyType.DAILY.name(),
                EndpointFrequencyType.WEEKLY.name(),
                EndpointFrequencyType.MONTHLY.name(),
                EndpointFrequencyType.NEVER.name(),
                EndpointFrequencyType.CLIENT_DAILY.name(),
                EndpointFrequencyType.CLIENT_WEEKLY.name(),
                EndpointFrequencyType.CLIENT_MONTHLY.name()
        );
    }

    public List<DataFeedFrequencyType> getDataFeedFrequencies(List<String> endpointFrequencyTypes) {
        return crudService.findByNamedQuery(DataFeedFrequencyType.FETCH_DATA_FEED_FREQUENCY_TYPES,
                QueryParameter.with(FREQUENCY_TYPES, endpointFrequencyTypes).parameters());
    }

    public boolean isExtendedWindowApplicable() {
        if (pacmanConfigParamsService.getBooleanParameterValue(EXTENDED_WINDOW_DAYS_FOR_DATAFEED)) {
            return extendedEvaluationService.isPropertyEligibleForExtendedWindowForDatafeed(PacmanWorkContextHelper.getClientId(), PacmanWorkContextHelper.getPropertyId());
        }
        return false;
    }

    public void updateLastDFRunForExtendedWindow() {
        extendedEvaluationService.updateLastSuccessfulDatafeedRunForExtendedWindow();
    }
}
