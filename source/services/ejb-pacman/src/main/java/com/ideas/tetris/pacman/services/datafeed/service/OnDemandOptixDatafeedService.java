package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.client.service.ClientConfigService;
import com.ideas.tetris.pacman.services.datafeed.dto.OnDemandOptixDatafeedDto;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;

import javax.inject.Inject;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ideas.tetris.platform.common.job.JobParameterKey.CLIENT_CODE;
import static com.ideas.tetris.platform.common.job.JobParameterKey.GENERATE_HISTORY_FILES;
import static com.ideas.tetris.platform.common.job.JobParameterKey.PROPERTY_CODE;
import static com.ideas.tetris.platform.common.job.JobParameterKey.PROPERTY_ID;
import static com.ideas.tetris.platform.common.job.JobParameterKey.TIMESTAMP;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class OnDemandOptixDatafeedService {

    @Autowired
	protected JobServiceLocal jobServiceLocal;

    @Autowired
	private ClientPropertyCacheService clientPropertyCacheService;

    @Autowired
	private ClientConfigService clientConfigService;

    public void sendDataFeedsFor(OnDemandOptixDatafeedDto optixDatafeedDto) {

        Set<Property> propertyList = optixDatafeedDto.getPropertyList();

        propertyList.forEach(property -> {
            optixDatafeedDto.setProperty(property);
            jobServiceLocal.startGuaranteedNewInstance(JobName.NGIGenerateOptixDatafeedOnDemand, getParameters(optixDatafeedDto));
        });

        if (optixDatafeedDto.isIncludeClientFiles()) {
            jobServiceLocal.startGuaranteedNewInstance(JobName.GenerateOptixClientLevelDatafeedFileOnDemand, getParametersForClientLevel(optixDatafeedDto));
        }
    }

    private Map<String, Object> getParameters(OnDemandOptixDatafeedDto optixDatafeedDto) {
        return MapBuilder.with(CLIENT_CODE, optixDatafeedDto.getProperty().getClient().getCode())
                .and(PROPERTY_CODE, optixDatafeedDto.getProperty().getCode())
                .and(GENERATE_HISTORY_FILES, optixDatafeedDto.isGenerateHistoryFiles())
                .and(TIMESTAMP, getCurrentTimeStamp())
                .get();
    }

    private Map<String, Object> getParametersForClientLevel(OnDemandOptixDatafeedDto optixDatafeedDto) {
        return MapBuilder.with(CLIENT_CODE, optixDatafeedDto.getProperty().getClient().getCode())
                .and(PROPERTY_CODE, "")
                .and(PROPERTY_ID, null)
                .and(GENERATE_HISTORY_FILES, optixDatafeedDto.isGenerateHistoryFiles())
                .and(TIMESTAMP, getCurrentTimeStamp())
                .get();
    }


    public List<Property> getPropertyCodes(Client client) {
        return clientPropertyCacheService.getClientProperties(client);
    }

    @ForTesting
    protected long getCurrentTimeStamp() {
        return System.currentTimeMillis();
    }

    public List<Client> getAllClients() {
        return clientConfigService.getAllClientDetails();
    }

}
