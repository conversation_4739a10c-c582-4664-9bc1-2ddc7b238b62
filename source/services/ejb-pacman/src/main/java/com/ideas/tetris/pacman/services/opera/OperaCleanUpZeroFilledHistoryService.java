package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.opera.entity.HistoryOccupancySummary;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.log4j.Logger;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;
import javax.inject.Inject;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class OperaCleanUpZeroFilledHistoryService {
    private static final Logger LOGGER = Logger.getLogger(OperaCleanUpZeroFilledHistoryService.class);

    @Autowired
	protected OperaUtilityService operaUtilityService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService crudService;

    @Transactional(propagation = Propagation.NEVER)
    public void cleanUp(String correlationId) {
        LOGGER.info("Opera trim history before first non-zero occupancy date started for correlation id : " + correlationId);
        Map<String, Integer> dataLoadMetadataIDMap = operaUtilityService.getDataLoadMetadataIDMap(correlationId);
        Integer dataLoadMetadataForFileTypePTId = dataLoadMetadataIDMap.get(OperaIncomingFile.PAST_HOTEL_SUMMARY.getFileTypeCode());
        Integer dataLoadMetadataForFileTypePTAT = dataLoadMetadataIDMap.get(OperaIncomingFile.PAST_HOTEL_AND_ROOM_TYPE_SUMMARY.getFileTypeCode());
        Integer dataLoadMetadataForFileTypePSTAT = dataLoadMetadataIDMap.get(OperaIncomingFile.PAST_SEGMENT_AND_ROOM_TYPE_SUMMARY.getFileTypeCode());
        Integer dataLoadMetadataForFileTypeMetadata = dataLoadMetadataIDMap.get(OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        LocalDateTime incomingMetaDataBusinessDate = operaUtilityService.getIncomingMetaDataBusinessDate(correlationId);
        String firstNonZeroOccupancyDateAcrossAllFeeds = getFirstNonZeroOccupancyDate();
        LOGGER.info("First non zero occupancy date across all the feeds : " + firstNonZeroOccupancyDateAcrossAllFeeds);
        trimZeroFillHistoryBeforeFirstNonZeroOccupancyDate(dataLoadMetadataForFileTypePTId, dataLoadMetadataForFileTypePTAT, dataLoadMetadataForFileTypePSTAT, firstNonZeroOccupancyDateAcrossAllFeeds, incomingMetaDataBusinessDate);
        updatePastDaysIntoIncomingMetadataTable(correlationId, dataLoadMetadataForFileTypePTId, dataLoadMetadataForFileTypeMetadata);
    }

    private void trimZeroFillHistoryBeforeFirstNonZeroOccupancyDate(Integer dataLoadMetadataForFileTypePTId, Integer dataLoadMetadataForFileTypePTAT,
                                                                    Integer dataLoadMetadataForFileTypePSTAT, String firstNonZeroOccupancyDateAcrossAllFeeds,
                                                                    LocalDateTime incomingMetaDataBusinessDate) {
        String firstNonZeroOccupancyDate = null == firstNonZeroOccupancyDateAcrossAllFeeds ? incomingMetaDataBusinessDate.toLocalDate().toString() : firstNonZeroOccupancyDateAcrossAllFeeds;
        int deletedPTRows = deleteHistoryBeforeOccupancyDateByMetadata(dataLoadMetadataForFileTypePTId, firstNonZeroOccupancyDate);
        LOGGER.info("Deleted PT rows : " + deletedPTRows);
        int deletedPTATRows = deleteHistoryBeforeOccupancyDateByMetadata(dataLoadMetadataForFileTypePTAT, firstNonZeroOccupancyDate);
        LOGGER.info("Deleted PTAT rows : " + deletedPTATRows);
        int deletedPSATRows = deleteHistoryBeforeOccupancyDateByMetadata(dataLoadMetadataForFileTypePSTAT, firstNonZeroOccupancyDate);
        LOGGER.info("Deleted PSAT rows : " + deletedPSATRows);
    }

    private int deleteHistoryBeforeOccupancyDateByMetadata(Integer dataLoadMetadataId, String firstNonZeroOccupancyDateAcrossAllFeeds) {
        return crudService.executeUpdateByNamedQuery(HistoryOccupancySummary.DELETE_BEFORE_OCCUPANCY_DATE_BY_METADATA_ID,
                QueryParameter.with("occupancyDate", firstNonZeroOccupancyDateAcrossAllFeeds)
                        .and("dataLoadMetadataId", dataLoadMetadataId).parameters());
    }

    private void updatePastDaysIntoIncomingMetadataTable(String correlationId, Integer dataLoadMetadataForFileTypePTId, Integer dataLoadMetadataForFileTypeMetadata) {
        LocalDateTime incomingMetaDataBusinessDate = operaUtilityService.getIncomingMetaDataBusinessDate(correlationId);
        LocalDate firstOccupancyDateForTheCurrentFeed = getFirstOccupancyDateForTheCurrentFeed(dataLoadMetadataForFileTypePTId);
        if (null != firstOccupancyDateForTheCurrentFeed) {
            Days days = Days.daysBetween(incomingMetaDataBusinessDate.toLocalDate(), firstOccupancyDateForTheCurrentFeed);
            int pastDays = days.getDays();
            crudService.executeUpdateByNativeQuery("update opera.History_Incoming_Metadata set Past_Days = :pastDays where Data_Load_Metadata_ID = :dataLoadMetadataId",
                    QueryParameter.with("pastDays", Math.abs(pastDays)).and("dataLoadMetadataId", dataLoadMetadataForFileTypeMetadata).parameters());
        }

    }

    private LocalDate getFirstOccupancyDateForTheCurrentFeed(Integer dataLoadMetadataForFileTypePT) {
        String firstOccupancyDate = crudService.findByNamedQuerySingleResult(HistoryOccupancySummary.GET_FIRST_OCCUPANCY_DATE_BY_METADATA_ID,
                QueryParameter.with("dataLoadMetadataId", dataLoadMetadataForFileTypePT).parameters());
        return firstOccupancyDate != null ? LocalDate.parse(firstOccupancyDate) : null;
    }

    private String getFirstNonZeroOccupancyDate() {
        return crudService.findByNamedQuerySingleResult(HistoryOccupancySummary.GET_FIRST_NON_ZERO_OCCUPANCY_DATE_ACROSS_ALL_FEEDS);
    }

}
