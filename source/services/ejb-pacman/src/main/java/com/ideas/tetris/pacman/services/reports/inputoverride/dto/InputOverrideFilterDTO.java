package com.ideas.tetris.pacman.services.reports.inputoverride.dto;

import java.time.LocalDate;

public class InputOverrideFilterDTO {

    protected LocalDate startDate;
    protected LocalDate endDate;
    protected Boolean isIncludeNoteEnable;
    protected Boolean isWashEnable;
    protected Boolean isDemandOccupancyDateEnable;
    protected Boolean isDemandArrivalByLosEnable;
    protected Boolean isGffOverrideFetch;

    protected Integer isRollingDate;
    protected String rollingStartDate;
    protected String rollingEndDate;

    private Integer propertyID;

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Boolean getIsIncludeNoteEnable() {
        return isIncludeNoteEnable;
    }

    public void setIsIncludeNoteEnable(Boolean isNoteEnable) {
        this.isIncludeNoteEnable = isNoteEnable;
    }

    public Boolean isGffOverrideFetch() {
        return isGffOverrideFetch;
    }

    public void setGffOverrideFetch(Boolean gffOverrideFetch) {
        isGffOverrideFetch = gffOverrideFetch;
    }

    public Integer getIsRollingDate() {
        return isRollingDate;
    }

    public void setIsRollingDate(Integer isRollingDate) {
        this.isRollingDate = isRollingDate;
    }

    public String getRollingStartDate() {
        return rollingStartDate;
    }

    public void setRollingStartDate(String rollingStartDate) {
        this.rollingStartDate = rollingStartDate;
    }

    public String getRollingEndDate() {
        return rollingEndDate;
    }

    public void setRollingEndDate(String rollingEndDate) {
        this.rollingEndDate = rollingEndDate;
    }

    public Boolean getIsWashEnable() {
        return isWashEnable;
    }

    public void setIsWashEnable(Boolean washEnable) {
        isWashEnable = washEnable;
    }

    public Boolean getIsDemandOccupancyDateEnable() {
        return isDemandOccupancyDateEnable;
    }

    public void setIsDemandOccupancyDateEnable(Boolean demandOccupancyDateEnable) {
        isDemandOccupancyDateEnable = demandOccupancyDateEnable;
    }

    public Boolean getIsDemandArrivalByLosEnable() {
        return isDemandArrivalByLosEnable;
    }

    public void setIsDemandArrivalByLosEnable(Boolean demandArrivalByLosEnable) {
        isDemandArrivalByLosEnable = demandArrivalByLosEnable;
    }

    public Integer getPropertyID() {
        return propertyID;
    }

    public void setPropertyID(Integer propertyID) {
        this.propertyID = propertyID;
    }
}
