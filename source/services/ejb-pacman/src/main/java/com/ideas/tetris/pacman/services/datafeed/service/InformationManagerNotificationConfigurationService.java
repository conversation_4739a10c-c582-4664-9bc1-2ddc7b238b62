package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.InformationManagerNotificationConfiguration;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity;
import com.ideas.tetris.pacman.services.informationmanager.enums.ExceptionSubType;
import com.ideas.tetris.pacman.services.informationmanager.enums.MetricType;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionConfigService;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class InformationManagerNotificationConfigurationService {

    @Autowired
    ExceptionConfigService exceptionConfigService;

    @Autowired
	private PacmanConfigParamsService configService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    public List<InformationManagerNotificationConfiguration> getInformationManagerNotificationConfigurations() {
        List<InformationManagerNotificationConfiguration> configurations = new ArrayList<>();
        List<Integer> propertyIds = new ArrayList<>();
        propertyIds.add(PacmanWorkContextHelper.getPropertyId());
        List<InformationMgrAlertConfigEntity> configEntities = exceptionConfigService.findAllExceptionsConfiguredForProperties(propertyIds);
        for (InformationMgrAlertConfigEntity configEntity : configEntities) {
            InformationManagerNotificationConfiguration configuration = convertConfigEntityToDTO(configEntity);
            configurations.add(configuration);
        }
        return configurations;
    }

    private InformationManagerNotificationConfiguration convertConfigEntityToDTO(final InformationMgrAlertConfigEntity configEntity) {
        Boolean agileRatesEnabled = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED);
        return new InformationManagerNotificationConfiguration() {
            {
                setDisable(configEntity.isDisabled() ? "common.yes" : "no");
                setType(configEntity.getAlertTypeEntity().getDescription());
                setSubType(configEntity.getExceptionSubType().getDescription());
                setLevel(configEntity.getExceptionLevel().getDescription());
                setSubLevel("PropertySubLevel".equalsIgnoreCase(configEntity.getSubLevelDisplayName()) ? null : configEntity.getSubLevelDisplayName());
                setWindowStart(configEntity.getStartDate());
                setWindowEnd(configEntity.getEndDate());
                String thresholdValue = configEntity.getThresholdValue().toString();
                if ((AlertType.DecisionAsOfLastNightlyOptimization.toString().equalsIgnoreCase(configEntity.getAlertTypeEntity().getName())
                        || AlertType.DecisionAsOfLastOptimization.toString().equalsIgnoreCase(configEntity.getAlertTypeEntity().getName()))
                        && ExceptionSubType.PRICING.getCode().equalsIgnoreCase(configEntity.getExceptionSubType().getName())) {
                    RateUnqualified rateByRank = tenantCrudService.findByNamedQuerySingleResult(RateUnqualified.FIND_BY_RANK, QueryParameter.with("rankLevel", configEntity.getThresholdValue().intValue()).parameters());
                    thresholdValue = null != rateByRank ? rateByRank.getName() : "";
                }
                setCondition(configEntity.getThresholdOperator() + " " + thresholdValue + (MetricType.PERCENT == configEntity.getThresholdMetricType() ? "%" : ""));
                if (agileRatesEnabled && "PRICING_BY_VALUE".equalsIgnoreCase(configEntity.getExceptionSubType().getName())) {
                    setProductName(null != configEntity.getProduct() ? configEntity.getProduct().getName() : null);
                }
            }
        };
    }
}
