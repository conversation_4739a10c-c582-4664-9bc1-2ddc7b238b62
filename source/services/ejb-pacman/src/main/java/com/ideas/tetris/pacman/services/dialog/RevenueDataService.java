package com.ideas.tetris.pacman.services.dialog;

import com.ideas.g3.dialog.dto.IntentType;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyForecast;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.LocalDateUtils;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class RevenueDataService {
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    public BigDecimal getStatisticValue(IntentType intentType, Boolean isForecast, LocalDate startDate, LocalDate endDate) {
        Date startDateDate = LocalDateUtils.toDate(startDate);
        Date endDateDate = LocalDateUtils.toDate(endDate);
        BigDecimal revenue = BigDecimal.ZERO;
        if (isForecast) {
            if (intentType.equals(IntentType.ROOM_REVENUE)) {
                List<OccupancyForecast> occupancyForecasts = tenantCrudService.findByNamedQuery(OccupancyForecast.FOR_DATE_RANGE, QueryParameter.with("startDate", startDateDate).and("endDate", endDateDate).parameters());
                for (OccupancyForecast forecast : occupancyForecasts) {
                    revenue = revenue.add(forecast.getRevenue());
                }
                return revenue;
            }
        } else { // on books
            if (intentType.equals(IntentType.ROOM_REVENUE)) {
                List<TotalActivity> totalActivities = tenantCrudService.findByNamedQuery(TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        QueryParameter.with("startDate", startDateDate).
                                and("endDate", endDateDate).
                                and("propertyId", PacmanWorkContextHelper.getPropertyId()).
                                parameters());
                for (TotalActivity activity : totalActivities) {
                    revenue = revenue.add(activity.getRoomRevenue());

                }
                return revenue;
            }
        }
        return BigDecimal.ZERO; // TODO:  handle error
    }
}
