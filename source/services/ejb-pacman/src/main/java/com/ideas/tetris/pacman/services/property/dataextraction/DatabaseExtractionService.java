package com.ideas.tetris.pacman.services.property.dataextraction;

import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.email.EmailService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.property.dto.ExtractType;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.rest.mapper.NGIServiceType;
import com.ideas.tetris.platform.common.util.jdbc.JpaJdbcUtil;
import com.ideas.tetris.platform.common.util.zip.ZipDirectory;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.DBLoc;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.io.*;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.MessageFormat;
import java.util.*;

@Transactional(propagation = Propagation.NOT_SUPPORTED, timeout = 240 * 60)
@Justification("DataBaseExtraction can take time to place data on FTP, we need to ensure that there are no transaction timeouts.")
@Component
public class DatabaseExtractionService extends ExtractionService {
    private static final Logger LOGGER = Logger.getLogger(DatabaseExtractionService.class);
    private static final String ZIPPED_DATABASE_FILE = "Database_{0}_Time-{1}.zip";
    private static final String SQL_BACKUP_DB = "backup database [{0}] to disk = ? with copy_only, COMPRESSION";
    private static final String SQL_PARAM_FILENAME = "{0}/{1}.bak";
    private static final String PARAM_DB_NAME = "dbName";
    static final String SQL_CHECK_DB = "select name from sys.databases where name = :" + PARAM_DB_NAME;
    static final String CONFIG_PARAMETER_CATEGORY_NAME_OUTBOUND_CONNECTION_DETAILS = "Outbound Connection Details";
    static final String CONFIG_PARAMETER_GROUP_NAME_DATAFEED = "%Datafeed%";
    static final String CONFIG_PARAMETER_GROUP_NAME_VENDOR = "Vendor";
    static final String CONFIG_PARAMETER_GROUP_NAME_DEFAULT_VENDOR = "DefaultVendor";

    private static final String DELETE_CONFIG_STMT = "DELETE from [dbo].[Config_Parameter_Value] WHERE  Context = ''{0}'' " +
            "and Config_Parameter_ID = (SELECT [Config_Parameter_ID] from [dbo].[Config_Parameter] where Name = ''{1}'');";
    private static final String INSERT_CONFIG_STMT = "INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID],[Context]," +
            "[FixedValue],[Config_Parameter_Predefined_Value_ID],[Created_By_User_ID],[Last_Updated_By_User_ID]) " +
            " VALUES ((SELECT [Config_Parameter_ID] from [dbo].[Config_Parameter] where Name = ''{1}''), ''{0}'', ''{2}'', null, 1, 1);";
    private static final String INSERT_CONFIG_STMT_FOR_VENDOR_GROUP = "INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID],[Context], [FixedValue],[Config_Parameter_Predefined_Value_ID],[Created_By_User_ID],[Last_Updated_By_User_ID])\n" +
            "VALUES ((SELECT [Config_Parameter_ID] from [dbo].[Config_Parameter] where Name = ''{0}''), ''{1}'', ''{2}'', (SELECT  cppv.Config_Parameter_Predefined_Value_ID  from  Config_Parameter_Predefined_Value cppv WHERE cppv.Value = ''{3}'' and cppv.Config_Parameter_Predefined_Value_Type_ID = (SELECT Config_Parameter_Predefined_Value_Type_ID from Config_Parameter where Config_Parameter.Name = ''{0}'')), 1, 1);\n";
    public static final String GET_PARAMETERS_EXCLUDING_CATEGORY_NAME_AND_GROUP_NAME = "SELECT cp.Name FROM [dbo].[Config_Parameter] cp \n" +
            "INNER JOIN [dbo].[Config_Parameter_Group] cpg ON cp.Group_ID = cpg.Group_ID \n" +
            "INNER JOIN [dbo].[Config_Parameter_Category] cpc ON cpg.Category_ID = cpc.Category_ID \n" +
            "WHERE cpc.Category_Name != :categoryName AND cpg.Group_Name not like :groupName_1 AND cpg.Group_Name not like :groupName_2 AND cpg.Group_Name not like :groupName_3";
    public static final String GET_PARAMETERS_FOR_GROUP_NAME_VENDOR = "select cp.Name ,cpv.Context ,cpv.FixedValue, cppv.Value  from Config_Parameter_Value cpv \n" +
            "JOIN [dbo].[Config_Parameter] cp ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID \n" +
            "JOIN [dbo].[Config_Parameter_Group] cpg ON cp.Group_ID = cpg.Group_ID \n" +
            "LEFT JOIN [dbo].Config_Parameter_Predefined_Value cppv ON cpv.Config_Parameter_Predefined_Value_ID = cppv.Config_Parameter_Predefined_Value_ID \n" +
            "AND cp.Config_Parameter_Predefined_Value_Type_ID = cppv.Config_Parameter_Predefined_Value_Type_ID \n" +
            "WHERE cpg.Group_Name = 'Vendor' and cpv.Context like :Context";

    private static final String CREATE_CLIENT = new StringBuilder()
            .append("IF NOT EXISTS (SELECT 1 FROM Client WHERE Client_Code = ''{0}'')")
            .append(" INSERT INTO Client (Client_Code, Client_Name, Status_ID, CreateDate)")
            .append(" VALUES (''{1}'', ''{2}'', 1, GETDATE());")
            .toString();

    private static final String CREATE_DB_PROPERTY = new StringBuilder()
            .append("SET IDENTITY_INSERT [global].[dbo].[Property] ON ")
            .append("INSERT INTO property (Property_ID, Client_ID, Property_Code, Property_Name,")
            .append("Status_ID, Created_dttm, DBLoc_ID, Stage, SFDC_Account_Number )")
            .append(" VALUES ({0}, (SELECT Client_ID FROM CLIENT WHERE Client_Code = ''{1}''), ''{2}'', ''{3}'', 1, GETDATE(),")
            .append("(SELECT MAX(DBLoc_ID) from [global].[dbo].[DBLoc]), ''ONE_WAY'', ''{4}'') ")
            .append("SET IDENTITY_INSERT [global].[dbo].[Property] OFF;")
            .toString();


    static final String RESPONSE_EXTRACT_EMBARGO = "Export embargo";

    @GlobalCrudServiceBean.Qualifier
	@Qualifier("globalCrudServiceBean")
    @Autowired
	private CrudService globalCrudService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	private CrudService tenantCrudService;
    @Autowired
	private EmailService emailService;
    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
	private PropertyService propertyService;
    @Autowired
	private ClientService clientService;


    // Testing
    public CrudService getGlobalCrudService() {
        return globalCrudService;
    }

    public CrudService getTenantCrudService() {
        return tenantCrudService;
    }

    public JpaJdbcUtil getJpaJdbcUtil() {
        // using injected JpsJdbcUtil causes org.hibernate.HibernateException: proxy handle is no longer valid
        return new JpaJdbcUtil();
    }

    public DatabaseExtractionService() {
        super();
    }

    @Async
    public void extractDatabase(String databaseName, String emailAddress, String clientCode, Property property) {
        setupWorkContext(clientCode, property);

        // This can NOT be within a transaction
        // Default database to global
        String dbName = null != databaseName ? databaseName : SystemConfig.getDbName();
        String failMessage = null;
        String ftpFileLink = null;
        long millis = Calendar.getInstance().getTimeInMillis();
        File dbFolder = getTempExtractFolder(ExtractType.DB_TENANT, millis);

        try {
            boolean wasSuccessful = backupDatabase(getOsBasedTempExtractFolderPath(ExtractType.DB_TENANT,millis), dbName);
            if (!wasSuccessful) {
                failMessage = RESPONSE_EXTRACT_EMBARGO;
            } else {
                String ftpFile = getTempZipDirectory() + "/" + MessageFormat.format(ZIPPED_DATABASE_FILE,
                        dbName, getNowFormattedForFile());
                ZipDirectory zipDirectory = new ZipDirectory(dbFolder, "", true);
                File zippedFile = createZipFile(ftpFile, zipDirectory);

                try {
                    createFtpDirectoryIfNotExists(emailAddress);
                    ftpFileLink = ftpZipFile(ftpFile, emailAddress);
                } catch (Exception e) {
                    failMessage = "Problem ftp'ing the backup";
                    LOGGER.error(getLogPreface() + "unable to create destination ftp folder", e);
                } finally {
                    // Clean up the local zip file. Unfortunately we have to create locally before ftp'ing
                    if (!zippedFile.delete()) {
                        LOGGER.error("Failed to delete database extraction zip file: " + zippedFile.getAbsolutePath());
                    }
                }
            }
        } finally {
            try {
                FileUtils.deleteDirectory(dbFolder);
            } catch (IOException ioe) {
                failMessage = null != failMessage ? "Backup database delete temp files go boom" : failMessage;
                LOGGER.error(getLogPreface() + failMessage, ioe);
            }
        }

        emailService.sendText(SystemConfig.getEmailCreateUserFrom(), emailAddress,
                "Backup database " + (null == failMessage ? "success" : "failed"),
                null != failMessage ? failMessage :
                        "Exported the database: " + dbName + "\nIt is available at FTP location: " + ftpFileLink);
    }

    private void setupWorkContext(String clientCode, Property property) {
        WorkContextType workContext = new WorkContextType();
        workContext.setClientCode(clientCode);
        workContext.setPropertyId(property.getId());
        workContext.setPropertyCode(property.getCode());
        // Async is new thread. We need ol' work context
        PacmanWorkContextHelper.setWorkContext(workContext);
    }

    public boolean backupDatabase(String folder, String databaseName) {
        boolean wasSuccessful = false;
        CrudService crudService = getCrudServiceForDatabase(databaseName);
        if (null != crudService) {
            String backupSql = MessageFormat.format(SQL_BACKUP_DB, databaseName);
            String backupName = MessageFormat.format(SQL_PARAM_FILENAME, folder, databaseName);
            Connection connection = null;
            PreparedStatement statement = null;
            JpaJdbcUtil jpaJdbcUtil = getJpaJdbcUtil();
            try {
                connection = jpaJdbcUtil.getJdbcConnection(crudService);
                statement = connection.prepareStatement(backupSql);
                statement.setString(1, backupName);
                statement.execute();
                wasSuccessful = true;
            } catch (SQLException e) {
                LOGGER.error(getLogPreface() + "unable to backup database " + databaseName, e);
            } finally {
                // Waiting for try-with-resources in Java 1.7
                jpaJdbcUtil.closeConnection(crudService, connection);
                try {
                    if (statement != null) {
                        statement.close();
                    }
                    // JpaJdbcUtil has a utility method but we need to close statement anyway
                } catch (Exception ex) {
                    LOGGER.error(getLogPreface() + "couldn't even cleanup sql resources", ex);
                }
            }
        }
        return wasSuccessful;
    }

    public boolean extractLimitedDatabaseData(String folder, DBLoc dbLoc, boolean excludeOperaTables, boolean excludePaceTables, boolean excludeRevenueStreamTables) {
        boolean wasTablesExtractSuccessful = true;

        if (SystemConfig.getAllowLimitedDataExtract()) {
            String extractCommand = getSchemaZenCommand(folder, dbLoc);
            LOGGER.info("Executing schemaZen ");

            String tablesToExtract = String.join(",", DatabaseExtractionInfo.BASE_TABLES_TO_EXTRACT);

            if (skipScheduleReportTableToggleEnabled()) {
                tablesToExtract = removeScheduledTableName(tablesToExtract);
            }

            if (!excludeOperaTables) {
                tablesToExtract = tablesToExtract + "," + String.join(",", DatabaseExtractionInfo.OPERA_TABLES_TO_EXTRACT);
            }

            if (!excludePaceTables) {
                tablesToExtract = tablesToExtract + "," + String.join(",", DatabaseExtractionInfo.PACE_TABLES_TO_EXTRACT);
            }

            if (!excludeRevenueStreamTables) {
                tablesToExtract = tablesToExtract + "," + String.join(",", DatabaseExtractionInfo.REVENUE_STREAM_TABLES_TO_EXTRACT);
            }

            wasTablesExtractSuccessful = executeSchemaZen(extractCommand +
                    tablesToExtract);

        } else {
            LOGGER.info("The Limited Data Extraction feature is not enabled");
        }
        return wasTablesExtractSuccessful;
    }

    protected String removeScheduledTableName(String tablesToExtract) {
        StringBuilder modifiedString = new StringBuilder();
        String[] items = tablesToExtract.split(",");

        for (String item : items) {
            if (!item.equals(DatabaseExtractionInfo.SCHEDULED_REPORTS)) {
                modifiedString.append(item).append(",");
            }
        }

        if (modifiedString.length() > 0) {
            modifiedString.setLength(modifiedString.length() - 1);
        }

        tablesToExtract = modifiedString.toString();
        return tablesToExtract;
    }

    protected boolean skipScheduleReportTableToggleEnabled() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.SKIP_SCHEDULED_REPORTS_TABLE);
    }

    public void extractGlobalConfigParameters(String folder, DBLoc dbLoc) {
        extractConfigParameterValues(folder, dbLoc);
        getClientPropertySql(folder);
    }

    public String getSchemaZenCommand(String folder, DBLoc dbLoc) {
        return SystemConfig.getSchemaZenPath() + " script -s " + dbLoc.getServerName() + "," + dbLoc.getPortNumber()
                + " -b " + dbLoc.getDbName() + " -u " + SystemConfig.getDatabaseUsername() + " -p " + SystemConfig.getDatabasePassword()
                + " -d " + folder + "/" + dbLoc.getDbName() + " --dataTables=";
    }

    public boolean executeSchemaZen(String extractCommand) {
        boolean wasSuccessful;
        StringBuilder output = new StringBuilder();

        Process p;
        BufferedReader reader = null;
        try {
            p = getRunTimeProcess(extractCommand);
            reader = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
            //the above while loop before .waitFor() avoids deadlock as it dont let output buffer from being filled completely by command o/p.
            p.waitFor();
            LOGGER.info("The Limited Data Extraction results :" + output);
            wasSuccessful = true;
            p.destroy();
        } catch (Exception e) {
            wasSuccessful = false;
            LOGGER.info("The Limited Data Extraction failed :" + output, e);
        } finally {
            if (reader != null) {
                IOUtils.closeQuietly(reader);
            }
        }
        return wasSuccessful;
    }

    private void extractConfigParameterValues(String folder, DBLoc dbLoc) {
        String globalConfigDumpFileDirectoryPath = folder + "/global-" + dbLoc.getDbName();
        File globalConfigDumpFileDirectory = new File(globalConfigDumpFileDirectoryPath);
        if (!globalConfigDumpFileDirectory.exists()) {
            globalConfigDumpFileDirectory.mkdir();
        }

        List<String> config = getConfigParameterSql();

        Path file = Paths.get(globalConfigDumpFileDirectoryPath, "global-config-params.sql");
        try (OutputStream outputStream = Files.newOutputStream(file, StandardOpenOption.CREATE, StandardOpenOption.APPEND)) {
            Files.write(file, config, Charset.forName("UTF-8"));
        } catch (IOException e) {
            LOGGER.error("Unable to extract Global Config Parameters:", e);
        }
        LOGGER.info("Limited Data Extract - Completed Extract of Config Params");
    }

    public String getConfigParameterSqlAsString() {
        return StringUtils.join(getConfigParameterSql(), "\n");
    }

    public List<String> getConfigParameterSql() {
        LOGGER.info("Limited Data Extract - Start Extract of Config Param");
        Map<String, String> paramWithValues = new HashMap<>();

        List<String> config = new ArrayList<>();
        List<String> allParameters = getAllParametersExcludingOutboundConnectionDetailsCategoryAndDataFeedGroup();
        for (NGIServiceType serviceType : NGIServiceType.values()) {
            String parameterName = serviceType.getConfigParam().getParameterName();
            allParameters.remove(parameterName);
        }
        for (String parameterName : allParameters) {
            paramWithValues.put(parameterName, configParamsService.getParameterValue(parameterName));
            if (paramWithValues.get(parameterName) != null && !"null".equalsIgnoreCase(paramWithValues.get(parameterName))) {
                StringBuilder configBuilder = new StringBuilder();
                configBuilder.append(MessageFormat.format(DELETE_CONFIG_STMT, "pacman." + PacmanWorkContextHelper.getClientCode() +
                        "." + PacmanWorkContextHelper.getPropertyCode(), parameterName)).append("\n");
                configBuilder.append(MessageFormat.format(INSERT_CONFIG_STMT, "pacman." + PacmanWorkContextHelper.getClientCode() +
                        "." + PacmanWorkContextHelper.getPropertyCode(), parameterName, paramWithValues.get(parameterName))).append("\n");
                config.add(configBuilder.toString());
            } else {
                LOGGER.info("No value found for : " + parameterName);
            }
        }
        addConfigParamSqlForVendorGroup().stream().forEach(cfg -> config.add(cfg));
        return config;
    }

    public List<String> addConfigParamSqlForVendorGroup() {
        String contextLike = "%." + PacmanWorkContextHelper.getClientCode() + "." + PacmanWorkContextHelper.getPropertyCode();
        Optional<List<VendorGroupConfigDetails>> vendorParametersOptional = Optional.ofNullable(getAllParametersForVendorGroup(contextLike));
        List<String> config = new ArrayList<>();

        if (!vendorParametersOptional.isPresent()) return config;

        vendorParametersOptional.get().forEach(parameter -> {
            String parameterName = parameter.getConfigParamName();
            String paramContext = parameter.getConfigParamContext();
            Object paramFixedValue = parameter.getConfigFixedValue();
            String paramValue = parameter.getConfigValue();

            StringBuilder configBuilder = new StringBuilder();
            configBuilder.append(MessageFormat.format(DELETE_CONFIG_STMT, paramContext, parameterName)).append("\n");
            configBuilder.append(MessageFormat.format(INSERT_CONFIG_STMT_FOR_VENDOR_GROUP, parameterName, paramContext, paramFixedValue, paramValue)).append("\n");
            String tempConfigBuilder = configBuilder.toString().replace("''", "null");
            config.add(tempConfigBuilder);
        });
        return config;
    }

    public VendorGroupConfigDetails getAllParametersForVendorGroupMapper(Object[] row) {
        VendorGroupConfigDetails result = new VendorGroupConfigDetails();
        result.setConfigParamName(row[0] != null ? (String) row[0] : "");
        result.setConfigParamContext(row[1] != null ? (String) row[1] : "");
        result.setConfigFixedValue(row[2] != null ? (String) row[2] : "");
        result.setConfigValue(row[3] != null ? (String) row[3] : "");
        return result;
    }

    public void getClientPropertySql(String folder) {
        LOGGER.info("Data Extract - Start Generation of Client and Property SQL");
        String clientAndPropertyPath = folder + "/clientProperty";

        File clientPropertyDir = new File(clientAndPropertyPath);

        if (!clientPropertyDir.exists()) {
            clientPropertyDir.mkdir();
        }

        String sql = buildClientPropertySql();
        Path file = Paths.get(clientAndPropertyPath, "client-property.sql");

        try (OutputStream outputStream = Files.newOutputStream(file, StandardOpenOption.CREATE, StandardOpenOption.APPEND)) {
            Files.write(file, Collections.singletonList(sql), Charset.forName("UTF-8"));
        } catch (IOException e) {
            LOGGER.error("Unable to Generate Global Client and Property SQL: ", e);
        }
    }

    protected String buildClientPropertySql() {
        Client client = clientService.getClientByCode(PacmanWorkContextHelper.getClientCode());
        Property property = propertyService.getPropertyByCode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());

        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(MessageFormat.format(CREATE_CLIENT, client.getCode(), client.getCode(), client.getName()));
        sqlBuilder.append("\n");
        sqlBuilder.append(MessageFormat.format(CREATE_DB_PROPERTY, Integer.toString(property.getId()), client.getCode(), property.getCode(), property.getName(), property.getSfdcAccountNo()));

        return sqlBuilder.toString();
    }

    protected Process getRunTimeProcess(String extractCommand) throws IOException {
        Process p;
        p = Runtime.getRuntime().exec(extractCommand);
        return p;
    }

    private List<String> getAllParametersExcludingOutboundConnectionDetailsCategoryAndDataFeedGroup() {
        Map<String, Object> parameters = QueryParameter.with("categoryName", CONFIG_PARAMETER_CATEGORY_NAME_OUTBOUND_CONNECTION_DETAILS)
                .and("groupName_1", CONFIG_PARAMETER_GROUP_NAME_DATAFEED).and("groupName_2", CONFIG_PARAMETER_GROUP_NAME_VENDOR).and("groupName_3", CONFIG_PARAMETER_GROUP_NAME_DEFAULT_VENDOR).parameters();
        return globalCrudService.findByNativeQuery(GET_PARAMETERS_EXCLUDING_CATEGORY_NAME_AND_GROUP_NAME, parameters);
    }

    private List<VendorGroupConfigDetails> getAllParametersForVendorGroup(String contextLike) {
        Map<String, Object> parameters = QueryParameter.with("Context", contextLike).parameters();
        return globalCrudService.findByNativeQuery(GET_PARAMETERS_FOR_GROUP_NAME_VENDOR, parameters, this::getAllParametersForVendorGroupMapper);
    }

    private CrudService getCrudServiceForDatabase(String databaseName) {
        CrudService crudService = null;
        if (doesDatabaseExistInCrudService(databaseName, getGlobalCrudService())) {
            crudService = getGlobalCrudService();
            LOGGER.info(getLogPreface() + "found database in global: " + databaseName);
        } else if (doesDatabaseExistInCrudService(databaseName, getTenantCrudService())) {
            crudService = getTenantCrudService();
            LOGGER.info(getLogPreface() + "found database in tenant: " + databaseName);
        }
        return crudService;
    }

    @SuppressWarnings("unchecked")
    private boolean doesDatabaseExistInCrudService(String databaseName, CrudService crudService) {
        Query query = crudService.getEntityManager().createNativeQuery(SQL_CHECK_DB);
        query.setParameter(PARAM_DB_NAME, databaseName);
        List<Object[]> results = query.getResultList();
        return !results.isEmpty();
    }
}
