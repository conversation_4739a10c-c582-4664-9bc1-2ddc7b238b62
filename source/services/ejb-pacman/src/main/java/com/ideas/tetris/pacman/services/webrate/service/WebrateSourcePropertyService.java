package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.webrate.dto.WebrateSourcePropertyDto;
import com.ideas.tetris.pacman.services.webrate.entity.Webrate;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateSource;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateSourceProperty;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.NotFoundException;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Transactional
public class WebrateSourcePropertyService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    private CrudService crudService;

    public static final String DELETE_FROM_PACE_WEBRATE_DIFFERENTIAL_BY_SOURCE_PROPERTY_ID = "delete from PACE_Webrate_Differential where Webrate_Source_Property_ID = :webrateSourcePropertyId";

    public List<WebrateSourcePropertyDto> getWebrateSourcePropertyDtos() {
        return crudService.findAll(WebrateSourceProperty.class).stream().map(this::convertToDto).collect(Collectors.toList());
    }

    public WebrateSourcePropertyDto getWebrateSourcePropertyDtoById(Integer id) {
        WebrateSourceProperty webrateSourceProperty = crudService.find(WebrateSourceProperty.class, id);

        if (null == webrateSourceProperty) {
            throw new NotFoundException("No webrate-source-property found with id " + id);
        }

        return convertToDto(webrateSourceProperty);
    }

    public WebrateSourceProperty getWebrateSourceProperty(Integer webrateSourceId, Integer propertyId) {
        return crudService.findByNamedQuerySingleResult(WebrateSourceProperty.BY_PROPERTY_ID_AND_WEBRATE_SOURCE_ID,
                QueryParameter.with("propertyId", propertyId)
                        .and("webrateSourceId", webrateSourceId)
                        .parameters()
        );
    }

    public WebrateSource getWebrateSourceProperty(String webrateSourceName, Integer propertyId) {
        WebrateSource webrateSource = new WebrateSource();
        webrateSource.setWebrateSourceName(webrateSourceName);
        webrateSource = crudService.findByExampleSingleResult(webrateSource);
        return webrateSource;
    }

    public void createWebrateSourceProperty(WebrateSourcePropertyDto webrateSourcePropertyDto) {
        WebrateSource webrateSource = crudService.find(WebrateSource.class, webrateSourcePropertyDto.getWebrateSourceId());
        if (null == webrateSource) {
            throw new NotFoundException("No webrate-source found with id " + webrateSourcePropertyDto.getWebrateSourceId());
        }

        WebrateSourceProperty webrateSourceProperty = crudService.findByNamedQuerySingleResult(WebrateSourceProperty.BY_WEBRATE_SOURCE_ID_AND_PROPERTY_ID,
                QueryParameter.with("webrateSourceId", webrateSourcePropertyDto.getWebrateSourceId())
                        .and("propertyId", webrateSourcePropertyDto.getPropertyId()).parameters());

        if (null != webrateSourceProperty) {
            throw new NotFoundException("Record already exists for the webrateSourceId " + webrateSourcePropertyDto.getWebrateSourceId() + " propertyId " + webrateSourcePropertyDto.getPropertyId());
        }
        crudService.save(convertToEntity(webrateSourcePropertyDto));
    }

    public void deleteWebrateSourcePropertyById(Integer id) {
        crudService.executeUpdateByNativeQuery(DELETE_FROM_PACE_WEBRATE_DIFFERENTIAL_BY_SOURCE_PROPERTY_ID, QueryParameter.with("webrateSourcePropertyId", id).parameters());
        crudService.executeUpdateByNamedQuery(Webrate.DELETE_BY_SOURCE_PROPERTY_ID, QueryParameter.with("webrateSourcePropertyId", id).parameters());
        crudService.executeUpdateByNamedQuery(WebrateSourceProperty.DELETE_BY_ID, QueryParameter.with("id", id).parameters());
    }

    private WebrateSourceProperty convertToEntity(WebrateSourcePropertyDto dto) {
        WebrateSourceProperty webrateSourceProperty = new WebrateSourceProperty();
        webrateSourceProperty.setPropertyId(dto.getPropertyId());
        webrateSourceProperty.setStatusId(dto.getStatusId());
        webrateSourceProperty.setWebrateSource(crudService.find(WebrateSource.class, dto.getWebrateSourceId()));

        return webrateSourceProperty;
    }

    private WebrateSourcePropertyDto convertToDto(WebrateSourceProperty webrateSourceProperty) {
        WebrateSourcePropertyDto webrateSourcePropertyDto = new WebrateSourcePropertyDto();
        webrateSourcePropertyDto.setWebrateSourcePropertyId(webrateSourceProperty.getId());
        webrateSourcePropertyDto.setWebrateSourceId(webrateSourceProperty.getWebrateSource().getId());
        webrateSourcePropertyDto.setPropertyId(webrateSourceProperty.getPropertyId());
        webrateSourcePropertyDto.setStatusId(webrateSourceProperty.getStatusId());
        webrateSourcePropertyDto.setCreateDate(webrateSourceProperty.getCreateDate());

        return webrateSourcePropertyDto;
    }
}
