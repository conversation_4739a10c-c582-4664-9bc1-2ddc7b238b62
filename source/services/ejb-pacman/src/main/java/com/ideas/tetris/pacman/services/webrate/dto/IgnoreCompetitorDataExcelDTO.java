package com.ideas.tetris.pacman.services.webrate.dto;

import lombok.*;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
@EqualsAndHashCode
public class IgnoreCompetitorDataExcelDTO {
    private String product;
    private String competitorName;
    private String accomClass;
    private String channel;
    private Date startDate;
    private Date endDate;
    private String sunday;
    private String monday;
    private String tuesday;
    private String wednesday;
    private String thursday;
    private String friday;
    private String saturday;
}
