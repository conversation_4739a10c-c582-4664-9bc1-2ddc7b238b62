package com.ideas.tetris.pacman.services.webrate.dto;

import com.ideas.tetris.platform.common.crudservice.TableBatch;
import com.ideas.tetris.platform.common.crudservice.TableBatchAware;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;
import java.text.ParseException;
import java.util.Date;

public class WebrateDTO implements TableBatchAware {

    private static final String WEBRATE_EXPORT_DATE_FORMAT = "yyyy-MM-dd";
    private String WEBRATE_GENERATIONDATE_DATE_FORMAT = "yyyy-MM-dd HH:mm";
    private static final String WEBRATE_TABLE_BATCH = "Webrate_Batch";

    public static final String USP_WEBRATE_BATCH_LOAD = "usp_webrate_batch_load";

    private Integer id;
    private Date createDate;
    private Integer los;
    private String occupancyDT;
    private String webrateGenerationDate;
    private String webrateCurrencyRate;
    private BigDecimal webrateRateValue;
    private BigDecimal webrateRateValueDisplay;
    private String webrateRemark;
    private String webrateStatus;
    private Integer webrateAccomType;
    private Integer webrateChannel;
    private Integer webrateCompetitor;
    private Integer webrateType;
    private Integer webrateSourceProperty;
    private String pageNumber;
    private String rank;
    private String rating;
    private BigDecimal webrateRateValueMax;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getLos() {
        return los;
    }

    public void setLos(Integer los) {
        this.los = los;
    }

    public String getOccupancyDT() {
        return occupancyDT;
    }

    public void setOccupancyDT(String occupancyDT) {
        this.occupancyDT = occupancyDT;
    }

    public String getWebrateGenerationDate() {
        return webrateGenerationDate;
    }

    public void setWebrateGenerationDate(String webrateGenerationDate) {
        this.webrateGenerationDate = webrateGenerationDate;
    }

    public String getWebrateCurrencyRate() {
        return webrateCurrencyRate;
    }

    public void setWebrateCurrencyRate(String webrateCurrencyRate) {
        this.webrateCurrencyRate = webrateCurrencyRate;
    }

    public BigDecimal getWebrateRateValue() {
        return webrateRateValue;
    }

    public void setWebrateRateValue(BigDecimal webrateRateValue) {
        this.webrateRateValue = webrateRateValue;
    }

    public BigDecimal getWebrateRateValueDisplay() {
        return webrateRateValueDisplay;
    }

    public void setWebrateRateValueDisplay(BigDecimal webrateRateValueDisplay) {
        this.webrateRateValueDisplay = webrateRateValueDisplay;
    }

    public String getWebrateRemark() {
        return webrateRemark;
    }

    public void setWebrateRemark(String webrateRemark) {
        this.webrateRemark = webrateRemark;
    }

    public String getWebrateStatus() {
        return webrateStatus;
    }

    public void setWebrateStatus(String webrateStatus) {
        this.webrateStatus = webrateStatus;
    }

    public Integer getWebrateAccomType() {
        return webrateAccomType;
    }

    public void setWebrateAccomType(Integer webrateAccomType) {
        this.webrateAccomType = webrateAccomType;
    }

    public Integer getWebrateChannel() {
        return webrateChannel;
    }

    public void setWebrateChannel(Integer webrateChannel) {
        this.webrateChannel = webrateChannel;
    }

    public Integer getWebrateCompetitor() {
        return webrateCompetitor;
    }

    public void setWebrateCompetitor(Integer webrateCompetitor) {
        this.webrateCompetitor = webrateCompetitor;
    }

    public Integer getWebrateType() {
        return webrateType;
    }

    public void setWebrateType(Integer webrateType) {
        this.webrateType = webrateType;
    }

    public Integer getWebrateSourceProperty() {
        return webrateSourceProperty;
    }

    public void setWebrateSourceProperty(Integer webrateSourceProperty) {
        this.webrateSourceProperty = webrateSourceProperty;
    }

    public String getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(String pageNumber) {
        this.pageNumber = pageNumber;
    }

    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    public String getRating() {
        return rating;
    }

    public void setRating(String rating) {
        this.rating = rating;
    }

    public BigDecimal getWebrateRateValueMax() {
        return webrateRateValueMax;
    }

    public void setWebrateRateValueMax(BigDecimal webrateRateValueMax) {
        this.webrateRateValueMax = webrateRateValueMax;
    }

    public void setWebrateGenerationDateFormat(String format) {
        WEBRATE_GENERATIONDATE_DATE_FORMAT = format;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }

    @Override
    public String getTableVariableName() {
        return WEBRATE_TABLE_BATCH;
    }

    @Override
    public void addTableBatchColumns(TableBatch tableBatch) {
        tableBatch.addColumn("Webrate_GenerationDate", Types.DATE);
        tableBatch.addColumn("Webrate_Source_Property_ID", Types.INTEGER);
        tableBatch.addColumn("Webrate_Competitors_ID", Types.INTEGER);
        tableBatch.addColumn("Webrate_Channel_ID", Types.INTEGER);
        tableBatch.addColumn("Webrate_Accom_Type_ID", Types.INTEGER);
        tableBatch.addColumn("Webrate_Type_ID", Types.INTEGER);
        tableBatch.addColumn("Occupancy_DT", Types.DATE);
        tableBatch.addColumn("LOS", Types.NUMERIC);
        tableBatch.addColumn("Webrate_Remark", Types.NVARCHAR);
        tableBatch.addColumn("Webrate_Status", Types.NVARCHAR);
        tableBatch.addColumn("Webrate_Currency", Types.NVARCHAR);
        tableBatch.addColumn("Webrate_RateValue", Types.NUMERIC);
        tableBatch.addColumn("Webrate_RateValue_Display", Types.NUMERIC);
        tableBatch.addColumn("Webrate_Page_Number", Types.NVARCHAR);
        tableBatch.addColumn("Webrate_Rank", Types.NVARCHAR);
        tableBatch.addColumn("Webrate_Rating", Types.NVARCHAR);
        tableBatch.addColumn("Webrate_RateValue_Max", Types.NUMERIC);
    }

    @Override
    public Object[] toTableBatchRow() {
        try {
            return new Object[]{new Timestamp(DateUtil.parseDate(webrateGenerationDate, WEBRATE_GENERATIONDATE_DATE_FORMAT).getTime()), webrateSourceProperty, webrateCompetitor, webrateChannel, webrateAccomType, webrateType, new java.sql.Date(DateUtil.parseDate(occupancyDT, WEBRATE_EXPORT_DATE_FORMAT).getTime()), los, webrateRemark, webrateStatus, webrateCurrencyRate, webrateRateValue, webrateRateValueDisplay, pageNumber, rank, rating, webrateRateValueMax != null ? webrateRateValueMax : webrateRateValue};
        } catch (ParseException pe) {
            throw new TetrisException(ErrorCode.WEB_RATE_DATA_PARSE_ERROR, "Unable to parse date in Webrate data", pe);
        }
    }
}
