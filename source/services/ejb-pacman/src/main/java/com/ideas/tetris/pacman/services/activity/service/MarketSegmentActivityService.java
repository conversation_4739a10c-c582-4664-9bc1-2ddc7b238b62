package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.activity.converter.MarketSegmentActivityConverter;
import com.ideas.tetris.pacman.services.activity.converter.PaceActivityConverter;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceMktSegActivity;
import com.ideas.tetris.pacman.services.bookingpace.dto.ForecastGroupSoldDto;
import com.ideas.tetris.pacman.services.dashboard.dto.MktSegAccomActivityBatchDto;
import com.ideas.tetris.pacman.services.datafeed.dto.RevplanMarketAccomActivity;
import com.ideas.tetris.pacman.services.datafeed.dto.RevplanMarketSegment;
import com.ideas.tetris.pacman.services.datafeed.dto.RevplanMarketSegmentPace;
import com.ideas.tetris.pacman.services.datafeed.rowmapper.RevplanMarketAccomActivityRowMapper;
import com.ideas.tetris.pacman.services.datafeed.rowmapper.RevplanMarketSegmentPaceRowMapper;
import com.ideas.tetris.pacman.services.datafeed.rowmapper.RevplanMarketSegmentRowMapper;
import com.ideas.tetris.pacman.services.groupfinalforecast.entity.GFFUniqueOverride;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.TableBatchAware;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Transactional
public class MarketSegmentActivityService extends PaceActivityService<MktSegAccomActivity, PaceMktSegActivity> {

    @MarketSegmentActivityConverter.Qualifier
    @Autowired
	@Qualifier("marketSegmentActivityConverter")
	private MarketSegmentActivityConverter marketSegmentActivityConverter;

    @Override
    protected Comparator<Map<String, Object>> getComparator() {
        return (lhs, rhs) -> {

            int ret = ((Date) lhs.get(MarketSegmentActivityConverter.OCCUPANCY_DATE)).compareTo((Date) rhs.get(MarketSegmentActivityConverter.OCCUPANCY_DATE));
            if (ret != 0) {
                return ret;
            }

            return ((String) lhs.get(MarketSegmentActivityConverter.MARKET_SEGMENT_NAME)).compareTo((String) rhs.get(MarketSegmentActivityConverter.MARKET_SEGMENT_NAME));

        };
    }

    @Override
    protected Class<MktSegAccomActivity> getEntityClass() {
        return MktSegAccomActivity.class;
    }

    @Override
    protected String getDateRangeQuery() {
        return MktSegAccomActivity.GROUPED_MKT_ACCOM_ACTIVITY_BY_PROPERTY_ID_AND_DATE_RANGE;
    }

    @Override
    protected String deleteDateRangeQuery() {
        return null;
    }


    @Override
    protected PaceActivityConverter<MktSegAccomActivity, PaceMktSegActivity> getConverter() {
        return marketSegmentActivityConverter;
    }

    @Override
    protected String getPaceDateRangeQuery() {
        return null;
    }

    @Override
    protected String deletePaceDateRangeQuery() {
        return null;
    }

    @Override
    protected List<TableBatchAware> convertToTableBatch(List<MktSegAccomActivity> entities, boolean isCDP) {
        return entities.stream().map(activity ->
                MktSegAccomActivityBatchDto.builder()
                        .mktSegAccomActivityId(activity.getId())
                        .accomTypeId(activity.getAccomTypeId())
                        .mktSegId(activity.getMktSegId())
                        .roomsSold(activity.getRoomsSold())
                        .arrivals(activity.getArrivals())
                        .departures(activity.getDepartures())
                        .cancellations(activity.getCancellations())
                        .noShows(activity.getNoShows())
                        .roomRevenue(activity.getRoomRevenue())
                        .foodRevenue(activity.getFoodRevenue())
                        .totalRevenue(activity.getTotalRevenue())
                        .totalProfit(activity.getTotalProfit())
                        .pseudoRoomRevenue(activity.getPseudoRoomRevenue())
                        .createDate(activity.getCreateDate())
                        .occupancyDate(activity.getOccupancyDate())
                        .snapShotDate(activity.getSnapShotDate())
                        .lastUpdatedDate(activity.getLastUpdatedDate())
                        .isCPD(isCDP)
                        .build()
        ).collect(Collectors.toList());
    }

    public Map<LocalDate, BigDecimal> findNonBlockOnBooksForOccupancyDateRange(Date startDate, Date endDate, boolean includeZeroCapacityRT) {
        List<Object[]> nonBlockOnBooksSummary = tenantCrudService.findByNamedQuery(
                MktSegAccomActivity.FIND_NON_BLOCK_ON_BOOKS_FOR_OCCUPANCY_DATE_RANGE,
                QueryParameter
                        .with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("startDate", DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT))
                        .and("endDate", DateUtil.formatDate(endDate, DateUtil.DEFAULT_DATE_FORMAT))
                        .and("includeZeroCapacityRT", includeZeroCapacityRT)
                        .parameters()
        );

        return nonBlockOnBooksSummary.stream().collect(Collectors.toMap(summary -> DateUtil.convertJavaUtilDateToLocalDate((Date) summary[0]), summary -> (BigDecimal) summary[1]));
    }

    public Map<GFFUniqueOverride, BigDecimal> findBlockOnBooksForOccupancyDateRangeAndFG(Date startDate, Date endDate, boolean includeZeroCapacityRT) {
        List<Object[]> blockOnBooksSummaryForFGAndOccDate = tenantCrudService.findByNamedQuery(
                MktSegAccomActivity.FIND_ON_BOOKS_FOR_BLOCK_AND_FG_AND_OCCUPANCY_DATE_RANGE,
                QueryParameter
                        .with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("startDate", DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT))
                        .and("endDate", DateUtil.formatDate(endDate, DateUtil.DEFAULT_DATE_FORMAT))
                        .and("includeZeroCapacityRT", includeZeroCapacityRT)
                        .parameters()
        );
        return blockOnBooksSummaryForFGAndOccDate.stream().collect(Collectors.toMap(summary -> new GFFUniqueOverride((Date) summary[0], (Integer) summary[1]), summary -> (BigDecimal) summary[2]));
    }

    public List<RevplanMarketSegmentPace> getMarketSegmentPaceData(Date startDate, Date endDate, int startPosition, int size) {
        List<Object[]> result = tenantCrudService.findByNamedQuery(PaceMktSegActivity.GET_MARKET_SEGMENT_PACE_DATA_BETWEEN, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters(), startPosition, size);
        return result.stream().map(row -> new RevplanMarketSegmentPaceRowMapper().mapRow(row)).collect(Collectors.toList());
    }

    public List<RevplanMarketSegment> getMarketSegmentNonPaceData(Date startDate, Date endDate, int startPosition, int size) {
        List<Object[]> result = tenantCrudService.findByNamedQuery(MktSegAccomActivity.GET_MARKET_SEGMENT_DATA_BETWEEN, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters(), startPosition, size);
        return result.stream().map(row -> new RevplanMarketSegmentRowMapper().mapRow(row)).collect(Collectors.toList());
    }

    public List<RevplanMarketAccomActivity> getMarketAccomActivityData(Date startDate, Date endDate, int startPosition, int size) {
        List<Object[]> result = tenantCrudService.findByNamedQuery(MktSegAccomActivity.GET_MARKET_ACCOM_ACTIVITY_DATA_BETWEEN, QueryParameter.with("startDate", startDate).parameters(), startPosition, size);
        return result.stream().map(row -> new RevplanMarketAccomActivityRowMapper().mapRow(row)).collect(Collectors.toList());
    }


    public List<ForecastGroupSoldDto> getSoldByForecastGroupByAccomClass(LocalDate startDate, LocalDate endDate) {
        return tenantCrudService.findByNamedQuery(MktSegAccomActivity.SOLDS_BETWEEN_BY_FORECAST_GROUP_BY_ACCOM_CLASS,
                QueryParameter.with("startDate", startDate.toString()).and("endDate", endDate.toString()).parameters());
    }
}
