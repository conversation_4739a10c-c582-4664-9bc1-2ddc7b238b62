package com.ideas.tetris.pacman.services.currency;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.ngi.dto.CurrencyExchange;
import com.ideas.tetris.pacman.services.ngi.dto.CurrencyExchangeResultsMapper;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterPredefinedValue;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterPredefinedValueType;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;

import javax.inject.Inject;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.ideas.tetris.pacman.common.configparams.GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE;
import static com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName.CORE_PROPERTY_APPLY_YIELD_CURRENCY;
import static com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE;
import static com.ideas.tetris.platform.common.rest.mapper.RestEndpoints.CURRENCY_EXCHANGE;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class CurrencyService {
    @Autowired
    PacmanConfigParamsService configParamsService;
    @Autowired
	private RestClient restClient;
    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService globalCrudService;

    public void ratchetToG3config(String ngiBaseCurrency) {
        String ratchetYieldCurrency =
                configParamsService.getParameterValue(IntegrationConfigParamName.YIELD_CURRENCY_CODE,
                        ExternalSystem.RATCHET);
        boolean applyYieldCurrency =
                ngiBaseCurrency != null && !StringUtils.equalsIgnoreCase(ngiBaseCurrency, ratchetYieldCurrency);
        var propertyYieldCurrency = ratchetYieldCurrency;
        String propertyBaseCurrency = applyYieldCurrency ? ngiBaseCurrency : null;

        configParamsService.updateParameterValue(CORE_PROPERTY_YIELD_CURRENCY_CODE.value(), propertyYieldCurrency);
        configParamsService.updateParameterValue(CORE_PROPERTY_BASE_CURRENCY_CODE.value(), propertyBaseCurrency);
        configParamsService.updateParameterValue(CORE_PROPERTY_APPLY_YIELD_CURRENCY.value(), applyYieldCurrency);
    }

    public String getBaseCurrency() {
        return configParamsService.getParameterValue(CORE_PROPERTY_BASE_CURRENCY_CODE);
    }

    public String getYieldCurrency() {
        return configParamsService.getParameterValue(CORE_PROPERTY_YIELD_CURRENCY_CODE);
    }

    public boolean applyYieldCurrency() {
        return configParamsService.getParameterValue(CORE_PROPERTY_APPLY_YIELD_CURRENCY);
    }

    public double yieldToBaseConversionFactor() {
        String baseCurrency = getBaseCurrency();
        String yieldCurrency = getYieldCurrency();

        if (applyYieldCurrency() && !StringUtils.equalsIgnoreCase(baseCurrency, yieldCurrency)) {
            return Optional.ofNullable(getExchangeRateFromRestClient(baseCurrency, yieldCurrency)).map(d -> 1 / d).orElse(1.0);
        }
        return 1.0;
    }

    private Double getExchangeRateFromRestClient(String fromCurrencyCode, String toCurrencyCode) {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("date", LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE));
        parameters.put("fromCurrencyCode", fromCurrencyCode);
        parameters.put("toCurrencyCode", toCurrencyCode);

        CurrencyExchange
                currencyExchange = restClient.getSingleResultFromEndpoint(CURRENCY_EXCHANGE, parameters, new CurrencyExchangeResultsMapper());
        return currencyExchange != null ? currencyExchange.getExchangeRate().doubleValue() : null;
    }

    private ConfigParameterPredefinedValueType getYieldCurrencyPredefinedValueType() {
        return configParamsService.getPredefinedValueTypeByCode(Constants.CONFIG_PARAM_CODE_YIELD_CURRENCY);
    }

    public List<String> getSupportedCurrencyList() {
        List<String> currencyList = new ArrayList<>();
        for (ConfigParameterPredefinedValue predefinedValue : getYieldCurrencyPredefinedValueType().getConfigParameterPredefinedValues()) {
            currencyList.add(predefinedValue.getValue());
        }
        return currencyList;
    }

    public void saveCurrency(String currency) {
        List<ConfigParameterPredefinedValue> configParameterPredefinedValues = globalCrudService.findByNamedQuery(ConfigParameterPredefinedValue.BY_TYPE_AND_VALUE,
                QueryParameter.with("type", getYieldCurrencyPredefinedValueType()).and("value", currency).parameters());

        if (configParameterPredefinedValues.isEmpty()) {
            ConfigParameterPredefinedValue configParameterPredefinedValue = new ConfigParameterPredefinedValue();
            configParameterPredefinedValue.setConfigParameterPredefinedValueType(getYieldCurrencyPredefinedValueType());
            configParameterPredefinedValue.setValue(currency);
            configParameterPredefinedValue.setCreateDate(new Date());
            configParameterPredefinedValue.setModifiedDate(new Date());
            globalCrudService.save(configParameterPredefinedValue);
        }
    }

    public double toYieldConversionFactor(String baseCurrency) {
        String yieldCurrency = getYieldCurrency();
        if (!StringUtils.equalsIgnoreCase(baseCurrency, yieldCurrency)) {
            return Optional.ofNullable(getExchangeRateFromRestClient(baseCurrency, yieldCurrency)).orElse(1.0);
        }
        return 1.0;
    }
}
