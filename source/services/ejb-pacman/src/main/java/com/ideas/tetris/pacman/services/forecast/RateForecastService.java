package com.ideas.tetris.pacman.services.forecast;

import com.ideas.tetris.pacman.services.activity.service.Pageable;
import com.ideas.tetris.pacman.services.forecast.dto.RateForecastDto;
import com.ideas.tetris.pacman.services.forecast.repository.RateForecastRepository;
import com.ideas.tetris.pacman.services.validation.dto.PageableDtos;
import com.ideas.tetris.pacman.services.validation.utils.PageableUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

@Component
public class RateForecastService {
    @Autowired
    private RateForecastRepository repository;

    public PageableDtos<RateForecastDto> retrieveResults (Integer propertyId, LocalDate startDate, LocalDate endDate, Pageable pageable){
        var allResult = repository.retrieveRateForecasts(propertyId,startDate,endDate);
        return PageableUtils.getPageResult(pageable,allResult);
    }
}
