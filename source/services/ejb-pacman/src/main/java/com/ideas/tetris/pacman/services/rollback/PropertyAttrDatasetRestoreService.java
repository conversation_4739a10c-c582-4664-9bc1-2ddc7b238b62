package com.ideas.tetris.pacman.services.rollback;

import com.ideas.sas.service.SASClientService;
import com.ideas.tetris.pacman.common.constants.Constants;

import javax.inject.Inject;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

import static com.ideas.tetris.pacman.common.constants.Constants.COPY_FILE;
import static com.ideas.tetris.pacman.common.constants.Constants.DELETE_FILE_QUIETLY;
import static com.ideas.tetris.pacman.common.constants.Constants.DELETE_PATH;
import static com.ideas.tetris.pacman.common.constants.Constants.DESC_PATH;
import static com.ideas.tetris.pacman.common.constants.Constants.SRC_PATH;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class PropertyAttrDatasetRestoreService {

    @Autowired
	protected RollbackHelper rollbackHelper;
    @Autowired
	private SASClientService sasClientService;

    public void restore(Integer propertyId) {
        String analyticsDatasetDirectory = rollbackHelper.getAnalyticalDatasetDirectory(propertyId);
        String propertyAttrDatasetBackupFolder = rollbackHelper.getPropertyAttrDatasetBackupFolder(propertyId);
        String propertyAttrDatasetBackupPath = propertyAttrDatasetBackupFolder + File.separator + Constants.PROPERTY_ATTR_DATASET_FILENAME;
        sasClientService.executeFileOps(COPY_FILE, new HashMap<>(Map.of(SRC_PATH, propertyAttrDatasetBackupPath, DESC_PATH, analyticsDatasetDirectory)));

        sasClientService.executeFileOps(DELETE_FILE_QUIETLY, new HashMap<>(Map.of(DELETE_PATH, propertyAttrDatasetBackupFolder)));
    }
}
