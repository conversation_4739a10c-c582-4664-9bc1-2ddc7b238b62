package com.ideas.tetris.pacman.services.forecast;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyForecast;
import com.ideas.tetris.pacman.services.dashboard.builder.AnalyticsBuilder;
import com.ideas.tetris.pacman.services.forecast.dto.OccupancyForecastByMarketSegmentDTO;
import com.ideas.tetris.pacman.services.forecast.dto.OccupancyForecastByRoomTypeDTO;
import com.ideas.tetris.pacman.services.forecast.dto.OccupancyForecastDTO;
import com.ideas.tetris.pacman.services.limiteddatabuild.dto.LDBProjectionDaySummary;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBProjection;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.service.MarketSegmentService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED;
import static com.ideas.tetris.pacman.common.constants.Constants.*;

@Slf4j
@Component
@Transactional
public class OccupancyForecastVirtualPropertyService extends AbstractOccupancyForecastService {

    @Autowired
	private AnalyticsBuilder analyticsBuilder;

    @Autowired
	private MarketSegmentService marketSegmentService;

    @Override
    public List<OccupancyForecastDTO> getOccupancyForecastForProperty(final String clientCode,
                                                                      final String physicalPropertyCode,
                                                                      final LocalDate startDate) {

        final List<OccupancyForecastDTO> occupancyForecasts =
                getOccupancyForecastInternal(clientCode, physicalPropertyCode, startDate);

        return !occupancyForecasts.isEmpty() ? occupancyForecasts
                : tryGetOccupancyForecastForLimitedDataBuild(clientCode, physicalPropertyCode, startDate);
    }

    private List<OccupancyForecastDTO> getOccupancyForecastInternal(String clientCode, String physicalPropertyCode, LocalDate startDate) {
        log.debug(">>> Fetching occupancy forecast for clientCode:{}, physicalPropertyCode:{}", clientCode, physicalPropertyCode);

        final Date start = LocalDateUtils.toDate(startDate);
        final Date end = dateService.getLatestOccupancyForecastDateByPhysicalPropertyCode(physicalPropertyCode);
        if (end == null || end.before(start)) {
            log.debug(">>> Latest occupancy forecast date is null or before start date for clientCode:{}, physicalPropertyCode:{} dates:{}-{}", clientCode, physicalPropertyCode,
                    start, end);
            return List.of();
        }
        final String startDateString = DateUtil.formatDate(start, "yyyy-MM-dd");
        final String endDateString = DateUtil.formatDate(end, "yyyy-MM-dd");

        log.debug(">>> Start and dates for occupancy forecast for clientCode:{}, physicalPropertyCode:{} are:{}-{}", clientCode, physicalPropertyCode,
                startDateString, endDateString);

        final Map<Date, BigDecimal> totalAccomCapacities = getTotalAccomCapacities(startDateString, endDateString, physicalPropertyCode);
        final Map<Date, BigDecimal> lv0Decisions = analyticsBuilder.buildHotelMasterRoomClassBARsForPhysicalProperty(start, end, physicalPropertyCode);
        final Map<Date, BigDecimal> adrForBaseRoomClass = getAverageDailyRateForBaseRoomClass(startDateString, endDateString, physicalPropertyCode);

        final List<Object[]> rawOccupancyForecasts = tenantCrudService.findByNamedQuery(OccupancyForecast.SUM_BY_OCCUPANCY_DATERANGE_PHYSICAL_PROPERTY_CODE,
                QueryParameter
                        .with(PHYSICAL_PROPERTY_CODE, physicalPropertyCode)
                        .and(START_DATE, startDateString)
                        .and(END_DATE, endDateString)
                        .parameters());
        log.debug(">>> Raw occupancy forecast size for clientCode:{}, physicalPropertyCode:{} {}-{} is {}", clientCode, physicalPropertyCode,
                startDateString, endDateString, rawOccupancyForecasts.size());

        return rawOccupancyForecasts
                .stream()
                .map(raw -> createOccupancyForecastDTO(raw, totalAccomCapacities, lv0Decisions, adrForBaseRoomClass))
                .collect(Collectors.toList());
    }

    private List<OccupancyForecastDTO> tryGetOccupancyForecastForLimitedDataBuild(String clientCode,
                                                                                  String physicalPropertyCode,
                                                                                  LocalDate startDate) {
        var virtualPropertyCode = PacmanWorkContextHelper.getPropertyCode();
        if (!pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED.value(), clientCode, virtualPropertyCode)) {
            log.debug(">>> Limited data build is not enabled for clientCode:{}, physicalPropertyCode:{}", clientCode, physicalPropertyCode);
            return List.of();
        }

        final String startDateString = DateUtil.formatDate(LocalDateUtils.toDate(startDate), "yyyy-MM-dd");
        log.warn(">>> Fetching occupancy forecast from limited data build for clientCode: {}, physicalPropertyCode: {}, startDate:{}", clientCode, physicalPropertyCode, startDateString);

        List<LDBProjectionDaySummary> hotelLdbProjectionDaySummaries = tenantCrudService.findByNamedQuery(LDBProjection.GET_AVERAGE_ROOM_REVENUE_FOR_HOTEL_FOR_PHYSICAL_PROPERTY,
                QueryParameter.with(START_DATE, LocalDateUtils.toDate(startDate))
                        .and(PHYSICAL_PROPERTY_CODE, physicalPropertyCode).parameters());

        if (hotelLdbProjectionDaySummaries.isEmpty()) {
            log.debug(">>> Average room revenue is empty for clientCode:{}, physicalPropertyCode:{} start date:{}", clientCode, physicalPropertyCode, startDateString);
            return List.of();
        }

        Date endDate = hotelLdbProjectionDaySummaries.get(hotelLdbProjectionDaySummaries.size() - 1).getOccupancyDate();
        final String endDateString = DateUtil.formatDate(endDate, "yyyy-MM-dd");
        log.debug(">>> End date for ldb for clientCode:{}, physicalPropertyCode:{} is:{}", clientCode, physicalPropertyCode, endDateString);

        Map<Date, BigDecimal> totalAccomCapacities = getTotalAccomCapacities(startDateString, endDateString, physicalPropertyCode);

        List<LDBProjectionDaySummary> barLdbProjectionDaySummaries = tenantCrudService.findByNamedQuery(LDBProjection.GET_AVERAGE_ROOM_REVENUE_FOR_BAR_SEGMENT_FOR_PHYSICAL_PROPERTY,
                QueryParameter
                        .with(START_DATE, LocalDateUtils.toDate(startDate))
                        .and(PHYSICAL_PROPERTY_CODE, physicalPropertyCode)
                        .parameters());

        Map<LocalDate, LDBProjectionDaySummary> hotelLdbProjectionDaySummaryByDate = hotelLdbProjectionDaySummaries.stream()
                .collect(Collectors.toMap(daySummary -> LocalDateUtils.toJavaLocalDate(daySummary.getOccupancyDate()), Function.identity(), (prev, next) -> next));

        Map<LocalDate, LDBProjectionDaySummary> barLdbProjectionDaySummaryByDate = barLdbProjectionDaySummaries.stream()
                .collect(Collectors.toMap(daySummary -> LocalDateUtils.toJavaLocalDate(daySummary.getOccupancyDate()), Function.identity(), (prev, next) -> next));

        return createOccupancyForecastLDB(startDate, LocalDateUtils.toJavaLocalDate(endDate),
                hotelLdbProjectionDaySummaryByDate, barLdbProjectionDaySummaryByDate, totalAccomCapacities);
    }

    @Override
    public List<OccupancyForecastByMarketSegmentDTO> getOccupancyForecastForPropertyByMarketSegment(final String marketSegmentCode,
                                                                                                    final String clientCode,
                                                                                                    final String physicalPropertyCode,
                                                                                                    final LocalDate startDate) {

        log.debug(">>> Fetching occupancy forecast by market segment for clientCode:{}, physicalPropertyCode:{}, marketSegment:{}", clientCode, physicalPropertyCode, marketSegmentCode);

        final Date start = LocalDateUtils.toDate(startDate);
        final Date end = dateService.getLatestOccupancyForecastDateByPhysicalPropertyCode(physicalPropertyCode);

        if (end == null || end.before(start)) {
            log.debug(">>> Latest occupancy forecast date by ms is null or before start date for clientCode:{}, physicalPropertyCode:{} dates:{}-{}", clientCode, physicalPropertyCode,
                    start, end);
            return List.of();
        }

        final String startDateString = DateUtil.formatDate(start, "yyyy-MM-dd");
        final String endDateString = DateUtil.formatDate(end, "yyyy-MM-dd");
        log.debug(">>> Start and end dates for occupancy forecast by ms for clientCode:{}, physicalPropertyCode:{} are:{}-{}", clientCode, physicalPropertyCode,
                startDateString, endDateString);

        return Optional.ofNullable(marketSegmentService.findByCode(marketSegmentCode))
                .map(MktSeg::getId)
                .map(marketSegmentId -> {
                    final Map<Date, BigDecimal> totalAccomCapacities = getTotalAccomCapacities(startDateString, endDateString, physicalPropertyCode);
                    final Map<Date, BigDecimal> msOccupancyCounts = getMSOccupancyCounts(startDateString, endDateString, physicalPropertyCode, marketSegmentId);

                    final List<Object[]> occupancyForecasts = tenantCrudService.findByNamedQuery(OccupancyForecast.SUM_BY_OCCUPANCY_DATERANGE_PHYSICAL_PROPERTY_CODE_AND_MARKET_SEGMENT_ID,
                            QueryParameter
                                    .with(START_DATE, startDateString)
                                    .and(END_DATE, endDateString)
                                    .and(PHYSICAL_PROPERTY_CODE, physicalPropertyCode)
                                    .and("marketSegmentId", marketSegmentId)
                                    .parameters());

                    log.debug(">>> {} Raw occupancy forecast found for clientCode:{}, physicalPropertyCode:{}, ms: {}, {}-{}", occupancyForecasts.size(),
                            clientCode, physicalPropertyCode, marketSegmentId, startDateString, endDateString);

                    return occupancyForecasts
                            .stream()
                            .map(raw -> createOccupancyForecastByMarketSegmentDTO(raw, totalAccomCapacities, msOccupancyCounts))
                            .collect(Collectors.toList());
                })
                .orElse(List.of());
    }

    @Override
    public List<OccupancyForecastByRoomTypeDTO> getOccupancyForecastForPropertyByRoomType(final String roomTypeCode,
                                                                                          final String clientCode,
                                                                                          final String propertyCode,
                                                                                          final LocalDate startDate) {
        final List<OccupancyForecastByRoomTypeDTO> occupancyForecasts = getOccupancyForecastForPropertyByRoomTypeInternal(
                roomTypeCode, clientCode, propertyCode, startDate);

        return !occupancyForecasts.isEmpty()
                ? occupancyForecasts
                : tryGetOccupancyForecastForLimitedDataBuild(roomTypeCode, clientCode, propertyCode, startDate);
    }

    private List<OccupancyForecastByRoomTypeDTO> getOccupancyForecastForPropertyByRoomTypeInternal(final String roomTypeCode,
                                                                                                   final String clientCode,
                                                                                                   final String physicalPropertyCode,
                                                                                                   final LocalDate startDate) {

        final String startDateString = DateUtil.formatDate(LocalDateUtils.toDate(startDate), "yyyy-MM-dd");
        log.debug(">>> Fetching occupancy forecast by room type for clientCode:{}, physicalPropertyCode:{}, roomType:{}, start date: {}",
                clientCode, physicalPropertyCode, roomTypeCode, startDateString);

        final Date start = LocalDateUtils.toDate(startDate);
        final Date end = dateService.getLatestOccupancyForecastDateByPhysicalPropertyCode(physicalPropertyCode);

        if (end == null || end.before(start)) {
            log.warn(">>> No occupancy forecast found for clientCode: {}, physicalPropertyCode: {} by room type {}, because end date '{}' is not after start date '{}'",
                    clientCode, physicalPropertyCode, roomTypeCode, end, start);
            return List.of();
        }

        final Map<LocalDate, OccupancyForecastByRoomTypeDTO> roomTypeForecasts = getOccupancyForecastForPropertyByRoomTypeInternal(clientCode, physicalPropertyCode, roomTypeCode, start, end);

        if (roomTypeForecasts.size() >= ChronoUnit.DAYS.between(startDate, LocalDateUtils.toJavaLocalDate(end))) {
            return new ArrayList<>(roomTypeForecasts.values());
        }

        final String baseRoomTypeCodeForPhysicalPropertyInventoryGroup = tenantCrudService.findByNamedQuerySingleResult(AccomType.BASE_ACCOMMODATION_TYPE_FOR_INVENTORY_GROUP,
                QueryParameter.with(PHYSICAL_PROPERTY_CODE, physicalPropertyCode).parameters());
        final Map<LocalDate, OccupancyForecastByRoomTypeDTO> baseRoomTypeForInventoryGroupForecasts = Optional.ofNullable(baseRoomTypeCodeForPhysicalPropertyInventoryGroup)
                .map(baseRoomTypeCode -> getOccupancyForecastForPropertyByRoomTypeInternal(clientCode, physicalPropertyCode, baseRoomTypeCode, start, end))
                .orElseGet(LinkedHashMap::new);

        final Map<LocalDate, OccupancyForecastByRoomTypeDTO> mergedForecasts = new TreeMap<>();
        mergedForecasts.putAll(baseRoomTypeForInventoryGroupForecasts);
        mergedForecasts.putAll(roomTypeForecasts);

        return new ArrayList<>(mergedForecasts.values());
    }

    private List<OccupancyForecastByRoomTypeDTO> tryGetOccupancyForecastForLimitedDataBuild(String roomTypeCode,
                                                                                            String clientCode,
                                                                                            String physicalPropertyCode,
                                                                                            LocalDate startDate) {

        if (!pacmanConfigParamsService.getBooleanParameterValue(CORE_LIMITED_DATA_BUILD_ENABLED.value(), clientCode, physicalPropertyCode)) {
            log.debug(">>> Limited data build is not enabled for clientCode:{}, physicalPropertyCode:{}", clientCode, physicalPropertyCode);
            return List.of();
        }

        final String startDateString = DateUtil.formatDate(LocalDateUtils.toDate(startDate), "yyyy-MM-dd");
        log.warn(">>> Fetching occupancy forecast by room type from limited data build for clientCode: {}, physicalPropertyCode: {}, roomType: {}, startDate: {}",
                clientCode, physicalPropertyCode, roomTypeCode, startDateString);

        final List<LDBProjectionDaySummary> avgRoomRevenues = tenantCrudService.findByNamedQuery(LDBProjection.GET_AVERAGE_ROOM_REVENUE_FOR_BAR_SEGMENT_FOR_PHYSICAL_PROPERTY,
                QueryParameter
                        .with(START_DATE, startDateString)
                        .and(PHYSICAL_PROPERTY_CODE, physicalPropertyCode)
                        .parameters());

        log.debug(">>> {} Average room revenues found for ldb clientCode:{}, physicalPropertyCode:{} start date:{}", avgRoomRevenues.size(),
                clientCode, physicalPropertyCode, startDateString);

        if (!avgRoomRevenues.isEmpty()) {
            return avgRoomRevenues.stream()
                    .map(daySummary -> createLDBOccupancyForecastByRoomTypeDTO(LocalDateUtils.toJavaLocalDate(daySummary.getOccupancyDate()),
                            roomTypeCode, daySummary.getAvgRate(), daySummary.getAvgRate()))
                    .collect(Collectors.toList());
        }
        return startDate.datesUntil(startDate.plusYears(1))
                .map(occupancyDate -> createLDBOccupancyForecastByRoomTypeDTO(occupancyDate, roomTypeCode, BigDecimal.ZERO, BigDecimal.ZERO))
                .collect(Collectors.toList());
    }

    private Map<Date, BigDecimal> getAverageDailyRateForBaseRoomClass(final String startDate, final String endDate, final String physicalPropertyCode) {

        final List<Object[]> adrForBaseClass = tenantCrudService.findByNamedQuery(OccupancyForecast.ADR_BY_OCCUPANCY_DATERANGE_PHYSICAL_PROPERTY_CODE,
                QueryParameter
                        .with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and(PHYSICAL_PROPERTY_CODE, physicalPropertyCode)
                        .parameters());

        return adrForBaseClass
                .stream()
                .collect(Collectors.toMap(
                        raw -> (Date) raw[0],
                        raw -> (BigDecimal) raw[1]
                ));
    }

    private Map<Date, BigDecimal> getTotalAccomCapacities(String startDate, String endDate, String physicalPropertyCode) {
        String query = "SELECT aa.* from Accom_Activity aa " +
                "INNER JOIN Accom_Type accT ON aa.Accom_Type_ID = accT.Accom_Type_ID " +
                "INNER JOIN Inventory_Group ig ON accT.Accom_Class_ID = ig.Base_Accom_Class_Id " +
                "WHERE ig.Inventory_Group_Name = :physicalPropertyCode " +
                "AND aa.Occupancy_DT >= :startDate and aa.Occupancy_DT <= :endDate order by aa.Occupancy_DT asc ";

        return tenantCrudService.findByNativeQuery(query,
                        QueryParameter.with(PHYSICAL_PROPERTY_CODE, physicalPropertyCode)
                                .and(START_DATE, startDate)
                                .and(END_DATE, endDate).parameters(), AccomActivity.class)
                .stream()
                .collect(Collectors.groupingBy(AccomActivity::getOccupancyDate, Collectors.mapping(AccomActivity::getAccomCapacity,
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }

    private Map<Date, BigDecimal> getMSOccupancyCounts(final String startDate, final String endDate,
                                                       final String physicalPropertyCode, final int mktSegId) {

        final List<Object[]> msOccupancyCountsByOccDate = tenantCrudService.findByNamedQuery(MktSegAccomActivity.SUM_ROOM_SOLD_BY_PHYSICAL_PROPERTY_CODE_MKT_SEG_ID_OCCUPANCY_DATE_RANGE,
                QueryParameter
                        .with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and(PHYSICAL_PROPERTY_CODE, physicalPropertyCode)
                        .and("mktSegId", mktSegId)
                        .parameters());

        return msOccupancyCountsByOccDate
                .stream()
                .collect(Collectors.toMap(
                        raw -> (Date) raw[0],
                        raw -> (BigDecimal) raw[1]
                ));
    }
}
