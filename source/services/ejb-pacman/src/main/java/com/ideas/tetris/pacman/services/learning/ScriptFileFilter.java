package com.ideas.tetris.pacman.services.learning;

import java.io.File;
import java.io.FilenameFilter;

public class ScriptFileFilter implements FilenameFilter {

    private String suffix;

    public ScriptFileFilter(LMSProperty lmsProperty) {
        if (lmsProperty != null) {
            this.suffix = "." + lmsProperty.name().toLowerCase() + ".sql";
        } else {
            this.suffix = ".global.sql";
        }
    }

    @Override
    public boolean accept(File dir, String name) {
        return name.toLowerCase().endsWith(this.suffix);
    }
}
