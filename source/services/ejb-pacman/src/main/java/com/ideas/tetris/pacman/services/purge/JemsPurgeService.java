package com.ideas.tetris.pacman.services.purge;

import com.ideas.tetris.pacman.services.problem.JobCrudServiceBean;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackDataBuilder;
import com.ideas.tetris.platform.common.businessservice.async.AsyncJobCallback;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;

import javax.annotation.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;
import javax.inject.Inject;
import javax.interceptor.Interceptors;
import javax.sql.DataSource;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.util.concurrent.Future;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class JemsPurgeService {
    private static final String SQL_PURGE_PROC = "{call dbo.usp_purge_old_job_data(?,?)}";
    @Resource(lookup = "java:jboss/datasources/JobDS")
    private DataSource jobDataSource;
    @JobCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("jobCrudServiceBean")
	private CrudService jobCrudService;

    @SuppressWarnings("unchecked")
    @Async
    @AsyncJobCallback
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Future<Object> purgeOldDataAsync(JobStepContext jobStepContext, WorkContextType workContextType, int daysToKeep) {
        callPurgeStoredProc(daysToKeep);
        return AsyncCallbackDataBuilder.buildFuture("Purged job data more than " + daysToKeep + " days old");
    }

    private void callPurgeStoredProc(int daysToKeep) {
        try (Connection connection = jobDataSource.getConnection();
             CallableStatement cstmt = connection.prepareCall(SQL_PURGE_PROC)) {
            cstmt.setInt(1, daysToKeep);
            cstmt.setInt(2, SystemConfig.getJemsPurgeBatchSize());
            cstmt.execute();
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Error occured calling stored proc for purging job db", e);
        }
    }
}
