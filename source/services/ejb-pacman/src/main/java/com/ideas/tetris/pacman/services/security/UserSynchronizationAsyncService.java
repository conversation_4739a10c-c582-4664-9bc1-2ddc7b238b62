package com.ideas.tetris.pacman.services.security;

import com.ideas.tetris.pacman.services.reports.ReportService;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.log4j.Logger;

import org.springframework.scheduling.annotation.Async;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.ideas.tetris.pacman.common.constants.Constants.ACTIVE_STATUS_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.INACTIVE_STATUS_ID;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Justification("TransactionTimeout is only temporary until JEMS job takes over")
@Component
@Transactional(timeout = 15 * 60)
public class UserSynchronizationAsyncService {
    private static final Logger LOGGER = Logger.getLogger(UserSynchronizationAsyncService.class);

    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
    ReportService reportService;

    @Async
    public void persistTenantUserAsyncWithEmail(WorkContextType workContext, List<Property> properties, int userID, String email, String userName, boolean isActive, boolean isCreationMode) {
        //async method, so it's thread needs current work context
        try {
            PacmanThreadLocalContextHolder.setWorkContext(workContext);
            persistTenantUserSynchronousByEmailID(properties, userID, email, userName, isActive, isCreationMode);
        } finally {
            PacmanThreadLocalContextHolder.setWorkContext(null);
        }
    }

    public void persistTenantUserSynchronousByEmailID(List<Property> properties, int userID, String email, String userName, boolean isActive, boolean isCreationMode) {
        LOGGER.info("persistTenantUser on thread: " + Thread.currentThread().getName());
   		// better be one or we're in a pickle
   		Integer firstPropertyId = (isCreationMode ? null : properties.get(0).getId());

   		Property activeProperty = null;
   		try {
   			if (!isCreationMode) {
   				LOGGER.info("Persisting tenant user " + userID + ". Using property " +
   						firstPropertyId + " as template.");
   			}

            User user = getTenantUser(isCreationMode, firstPropertyId, userID, userName, isActive);
            String oldEmail = user.getEmail();
            user.setScreenName(email);
            user.setEmail(email);

            for (Property property : properties) {
                activeProperty = property;
                updateUserInTenantDatabase(property, user, userID, isCreationMode, email, oldEmail);
            }
        } catch (Exception e) {
            // This is certainly not elegant but...
            // property dbs not getting cleaned up on bpd/bcd - crufty entries conflict with new user ids -
            // want explicit reminder that duplicate exists
            LOGGER.error("Unable to add user " + userID + " to property " +
                    " (" + (null != activeProperty ? activeProperty.getCode() : "NA") +
                    ") with template property " + ((null != firstPropertyId) ? firstPropertyId : "NA"));
            throw new TenantDatabaseOutOfSyncException(e);
        }
    }

    public User getTenantUser(Boolean isCreationMode, Integer firstPropertyId, Integer userID,
                       String userName, Boolean isActive) {
        User user = (isCreationMode ? new User() :
                multiPropertyCrudService.find(firstPropertyId, User.class, userID));
        // If we can't find user, switch to create mode
        if (null == user) {
            LOGGER.warn("Tenant user " + userID + " should have existed in property " +
                    firstPropertyId + " but did not. Let's create");
            user = new User();
        }
        user.setId(userID);
        user.setName(userName);
        user.setStatusId(isActive ? ACTIVE_STATUS_ID : INACTIVE_STATUS_ID);
        return user;
    }

    @Async
    public void persistTenantUserAsyncWithUniqueUserID(WorkContextType workContext, List<Property> properties, int userID, String uniqueUserID, String userName, boolean isActive, boolean isCreationMode) {
        //async method, so it's thread needs current work context
        try {
            PacmanThreadLocalContextHolder.setWorkContext(workContext);
            persistTenantUserSynchronousWithUniqueUserID(properties, userID, uniqueUserID, userName, isActive, isCreationMode);
        } finally {
            PacmanThreadLocalContextHolder.setWorkContext(null);
        }
    }

    public void persistTenantUserSynchronousWithUniqueUserID(List<Property> properties, int userID, String uniqueUserID, String userName, boolean isActive, boolean isCreationMode) {
        LOGGER.info("persistTenantUser on thread: " + Thread.currentThread().getName());
        // better be one or we're in a pickle
        Integer firstPropertyId = (isCreationMode ? null : properties.get(0).getId());

        Property activeProperty = null;
        try {
            if (!isCreationMode) {
                LOGGER.info("Persisting tenant user " + userID + ". Using property " +
                        firstPropertyId + " as template.");
            }
            User user = getTenantUser(isCreationMode, firstPropertyId, userID, userName, isActive);
            String oldUniqueUserID = user.getUniqueUserID();
            user.setScreenName(uniqueUserID);
            user.setUniqueUserID(uniqueUserID);

            for (Property property : properties) {
                activeProperty = property;
                updateUserInTenantDatabase(property, user, userID, isCreationMode, uniqueUserID, oldUniqueUserID);
            }
        } catch (Exception e) {
            // This is certainly not elegant but...
            // property dbs not getting cleaned up on bpd/bcd - crufty entries conflict with new user ids -
            // want explicit reminder that duplicate exists
            LOGGER.error("Unable to add user " + userID + " to property " +
                    " (" + (null != activeProperty ? activeProperty.getCode() : "NA") +
                    ") with template property " + ((null != firstPropertyId) ? firstPropertyId : "NA"));
            throw new TenantDatabaseOutOfSyncException(e);
        }
    }

    private void updateUserInTenantDatabase(Property property, User user, int userID, Boolean isCreationMode, String newEmail, String oldEmail) {
   		// TODO: 2 issues, 1. prop per db is making this an expensive iteration 2. Inconsistent seed
   		// data across environments make it non-deterministic so 2 calls per update - get followed by create/update
   		// HACK: DB maintain for prop dbs other than pune/paris (000005,000006) do not get scrubbed
   		// therefore tests / apps will quickly blow as we try to insert user uid that already exist
   		// If creating but user exists, update. If updating but user doesn't exist, create.
   		User existingTenantUser = multiPropertyCrudService.find(property.getId(), User.class, userID);
   		Boolean doesUserExist = (null != existingTenantUser);
   		if (isCreationMode && doesUserExist) { // we don't clean/rebuild seeded prop dbs except pune/paris
   			LOGGER.info("User " + userID + " already exists for property " + property.getId() + " so just updating");

   		} else if (!isCreationMode && !doesUserExist) { // we don't seed all prop dbs so some seed users missing
   			LOGGER.warn("User " + userID + " does not exist for property " + property.getId() + " so just creating");
   		}
   		// user is retrieved from first client property - kind of the template user
   		// however, it might not exist in first property (create data null) but exist in subsequent properties
   		if (!doesUserExist) {
   			// For create, let's pickup current - might be easier to spot this issues in hindsight then
   			user.setCreateDate((LocalDateTime) null);
   			user.setCreatedByUserId(null);
   		} else {
   			// Update expects create data to exist - keep existing
   			user.setCreateDate(existingTenantUser.getCreateDate());
   			user.setCreatedByUserId(existingTenantUser.getCreatedByUserId());
   		}
   		multiPropertyCrudService.save(property.getId(), user);

        if (isEmailModified(newEmail, oldEmail)) {
            reportService.updateScheduleReportMailId(property.getId(), oldEmail, newEmail);
        }
    }

    public boolean isEmailModified(String email, String oldEmail) {
        // if email id is modified and neither value is null
        return email != null && oldEmail != null && !email.equalsIgnoreCase(oldEmail);
    }
}
