package com.ideas.tetris.pacman.services.reports.outputoverride;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.IndependentOverride;
import com.ideas.tetris.pacman.services.datafeed.service.AgileRatesDataFeedService;
import com.ideas.tetris.pacman.services.datafeed.service.DatafeedRequest;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.product.ProductCodeID;
import com.ideas.tetris.pacman.services.reports.outputoverride.dto.AgileRatesProductPricingOverrideDTO;
import com.ideas.tetris.pacman.services.reports.outputoverride.dto.OutputOverrideInventoryLimitDTO;
import com.ideas.tetris.pacman.services.reports.outputoverride.dto.OutputOverrideOverbookingDTO;
import com.ideas.tetris.pacman.services.reports.outputoverride.dto.OutputOverridePricingDTO;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.util.CollectionUtils;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.STATUS;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.fromDate;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertLocalDateToJavaUtilDate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class OutputOverrideService {

    private static final Logger LOGGER = Logger.getLogger(OutputOverrideService.class.getName());
    public static final String COMMON_PROPERTY = "common.property";

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
	private AgileRatesDataFeedService agileRatesDataFeedService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;
    private static final String PERCENT_LITERAL = "%";

    private CrudService getCrudServiceBean() {
        return crudService;
    }

    public void setCrudServiceBean(CrudService crudService) {
        this.crudService = crudService;
    }

    public List<OutputOverrideInventoryLimitDTO> getOverrideInventoryLimitData(LocalDate startDate, LocalDate endDate, Integer isRollingDate,
                                                                               String rollingStartDate, String rollingEndDate, Language language) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("start_date", convertLocalDateToJavaUtilDate(startDate))
                .and("end_date", convertLocalDateToJavaUtilDate(endDate))
                .and("isRollingDate", isRollingDate)
                .and("rolling_start_date", rollingStartDate)
                .and("rolling_end_date", rollingEndDate);

        try {
            return getCrudServiceBean().findByNativeQuery("select * from dbo.ufn_get_output_override_inventory_limit_report(:property_id, :start_date, :end_date, " +
                            ":isRollingDate, :rolling_start_date, :rolling_end_date) order by Occupancy_DT ",
                    queryParameters.parameters(), row -> {
                        OutputOverrideInventoryLimitDTO data = new OutputOverrideInventoryLimitDTO();
                        data.setPropertyName((String) row[0]);
                        data.setAccomClassName(ResourceUtil.getText(COMMON_PROPERTY, language));
                        data.setOccupancyDate((Date) row[2]);
                        data.setDow(((String) row[3]).toLowerCase());
                        data.setOverrideValue((BigDecimal) row[4]);
                        data.setOverrideCategory((String) row[5]);
                        data.setUserName((String) row[6]);
                        data.setCreateDate((Date) (row[7]));
                        data.setNotes((String) row[8]);
                        data.setUserEmail((String) row[9]);
                        if (useUniqueUserIDInsteadOfEmailEnabled()) {
                            data.setUserEmail((String) row[6]);
                        }
                        return data;
                    });
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<>();
        }
    }

    public List<OutputOverrideOverbookingDTO> getOverrideOverbookingData(LocalDate startDate, LocalDate endDate, Integer isRollingDate, String rollingStartDate,
                                                                         String rollingEndDate, Language language) {

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("start_date", convertLocalDateToJavaUtilDate(startDate))
                .and("end_date", convertLocalDateToJavaUtilDate(endDate))
                .and("isRollingDate", isRollingDate)
                .and("rolling_start_date", rollingStartDate)
                .and("rolling_end_date", rollingEndDate);


        try {
            return getCrudServiceBean().findByNativeQuery("select * from dbo.ufn_get_output_override_ovbk_report(:property_id, :start_date, :end_date, " +
                            ":isRollingDate, :rolling_start_date, :rolling_end_date) order by Occupancy_DT ",
                    queryParameters.parameters(), new RowMapper<OutputOverrideOverbookingDTO>() {
                        @Override
                        public OutputOverrideOverbookingDTO mapRow(Object[] row) {
                            OutputOverrideOverbookingDTO data = new OutputOverrideOverbookingDTO();

                            data.setPropertyName((String) row[0]);
                            String accomClassName = (String) row[1];
                            data.setAccomClassName(accomClassName.equalsIgnoreCase(COMMON_PROPERTY) ? ResourceUtil.getText(COMMON_PROPERTY, language) : accomClassName);
                            data.setOverbookingTypeName((String) row[2]);

                            data.setOccupancyDate((Date) row[3]);
                            data.setCreateDate((Date) (row[9]));

                            data.setDow(((String) row[4]).toLowerCase());
                            data.setOverbookingOvr((BigDecimal) row[5]);
                            data.setCostofWalkValueOvr((BigDecimal) row[6]);
                            data.setOverrideCategory((String) row[7]);
                            data.setUserName((String) row[8]);
                            data.setNotes((String) row[10]);
                            data.setUserEmail((String) row[11]);
                            if (useUniqueUserIDInsteadOfEmailEnabled()) {
                                data.setUserEmail((String) row[8]);
                            }
                            return data;
                        }
                    });

        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<OutputOverrideOverbookingDTO>();
        }

    }

    private boolean useUniqueUserIDInsteadOfEmailEnabled() {
        return Boolean.parseBoolean(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.USE_UNIQUE_USERID_INSTEADOF_EMAILID.getParameterName()));
    }

    public List<OutputOverridePricingDTO> getOverridePricingData(LocalDate startDate, LocalDate endDate, Integer isRollingDate,
                                                                 String rollingStartDate, String rollingEndDate, Language language) {

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        final Boolean isContinuousPricingEnabled = isContinuousPricingEnabled();
        final Boolean isBarByDay = Constants.BAR_DECISION_VALUE_RATEOFDAY.equals(configParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value()));
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("start_date", convertLocalDateToJavaUtilDate(startDate))
                .and("end_date", convertLocalDateToJavaUtilDate(endDate))
                .and("isRollingDate", isRollingDate)
                .and("rolling_start_date", rollingStartDate)
                .and("rolling_end_date", rollingEndDate)
                .and("isContinuousPricing", Boolean.toString(isContinuousPricingEnabled))
                .and("isRestrictHighestBarEnabled", configParamsService.getParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value()))
                .and("products", "1");


        try {
            return getCrudServiceBean().findByNativeQuery("select * from dbo.ufn_get_output_override_pricing_report(:property_id, :start_date, :end_date, " +
                            ":isRollingDate, :rolling_start_date, :rolling_end_date, :isContinuousPricing, :isRestrictHighestBarEnabled, :products)",
                    queryParameters.parameters(), new RowMapper<OutputOverridePricingDTO>() {
                        @Override
                        public OutputOverridePricingDTO mapRow(Object[] row) {
                            OutputOverridePricingDTO data = new OutputOverridePricingDTO();

                            data.setDow(((String) row[0]).toLowerCase());
                            data.setPropertyName((String) row[2]);
                            String accomClassName = (String) row[3];
                            data.setAccomClassName(accomClassName.equalsIgnoreCase(COMMON_PROPERTY) ? ResourceUtil.getText(COMMON_PROPERTY, language) : accomClassName);

                            data.setArrivalDate((Date) row[1]);
                            data.setCreateDate((Date) (row[8]));
                            data.setLos((isBarByDay || isContinuousPricingEnabled) && !PacmanWorkContextHelper.isHiltonClientCode() ? 1 : (Integer) row[4]);
                            data.setNewOverride((String) row[5] == null ? null : ResourceUtil.getOptionalText(((String) row[5]).toLowerCase(), language).orElse((String) row[5]));
                            data.setRateCodeName((String) row[6]);
                            data.setUserName((String) row[7]);
                            data.setOldRateCodeName((String) row[9]);
                            data.setOldOverride((String) row[10] == null ? null : ResourceUtil.getOptionalText(((String) row[10]).toLowerCase(), language).orElse((String) row[10]));
                            data.setNotes((String) row[11]);
                            data.setIsLos((String) row[12]);
                            data.setAccomClassCode((String) row[14]);
                            data.setAccomTypeName((String) row[13]);
                            data.setAccomTypeCode((String) row[16]);
                            data.setUserEmail((String) row[15]);
                            if (useUniqueUserIDInsteadOfEmailEnabled()) {
                                data.setUserEmail((String) row[7]);
                            }
                            return data;
                        }
                    });

        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<OutputOverridePricingDTO>();
        }

    }

    public List<AgileRatesProductPricingOverrideDTO> fetchAgileRatesProductPricingOverrideData(DatafeedRequest datafeedRequest) {
        final String query = "select * from dbo.ufn_get_output_override_pricing_report(:property_id, :start_date, :end_date, " +
                ":isRollingDate, :rolling_start_date, :rolling_end_date, :isContinuousPricing, :isRestrictHighestBarEnabled, :products)";
        final List<AgileRatesProductPricingOverrideDTO> result = getCrudServiceBean().findByNativeQuery(
                query, getQueryParametersForAgileRatesPricingOverrides(datafeedRequest), datafeedRequest.getStartPosition(), datafeedRequest.getSize(),
                createAgileRateProductPricingOverrideDTORowMapper());
        return result != null ? result : Collections.emptyList();
    }

    private Map<String, Object> getQueryParametersForAgileRatesPricingOverrides(DatafeedRequest datafeedRequest) {
        final int NOT_ROLLING_DATE = 0;
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        return QueryParameter.with("property_id", propertyId)
                .and("start_date", datafeedRequest.getStartDate())
                .and("end_date", datafeedRequest.getEndDate())
                .and("isRollingDate", NOT_ROLLING_DATE)
                .and("rolling_start_date", StringUtils.EMPTY)
                .and("rolling_end_date", StringUtils.EMPTY)
                .and("isContinuousPricing", Boolean.toString(isContinuousPricingEnabled()))
                .and("isRestrictHighestBarEnabled", configParamsService.getParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED))
                .and("products", fetchCommaSeparatedAgileRateAndIndependentProductIds(datafeedRequest.getDatafeedName()))
                .parameters();
    }

    private RowMapper<AgileRatesProductPricingOverrideDTO> createAgileRateProductPricingOverrideDTORowMapper() {
        return row -> {
            AgileRatesProductPricingOverrideDTO dto = new AgileRatesProductPricingOverrideDTO();
            dto.setArrivalDate((Date) row[1]);
            dto.setAccomClassCode((String) row[14]);
            dto.setAccomTypeCode((String) row[16]);
            dto.setProductName((String) row[17]);
            dto.setOffsetType(getOffsetType(((String) row[6])));
            if (row[19] != null && row[20] != null) {
                dto.setOffsetValue((String) row[19] + "," + (String) row[20]);
            } else {
                dto.setOffsetValue(getOffsetValue((String) row[6]));
            }
            dto.setCreateDate((Date) (row[8]));
            dto.setNotes((String) row[11]);
            dto.setUserEmail((String) row[15]);
            if (useUniqueUserIDInsteadOfEmailEnabled()) {
                dto.setUserEmail((String) row[7]);
            }
            return dto;
        };
    }

    private String getOffsetValue(String offsetValue) {
        return offsetValue.contains(PERCENT_LITERAL) ? offsetValue.replace(PERCENT_LITERAL,
                StringUtils.EMPTY) : offsetValue;
    }

    private String getOffsetType(String offsetValue) {
        return offsetValue.contains(PERCENT_LITERAL) ? "Percentage" : "Fixed";
    }

    private boolean isContinuousPricingEnabled() {
        return configParamsService.getParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
    }

    private String fetchCommaSeparatedAgileRateAndIndependentProductIds(String datafeedName) {
        List<Product> products = crudService.findByNamedQuery(Product.GET_BY_STATUS, QueryParameter.with(STATUS, TenantStatusEnum.ACTIVE).parameters());
        List<Product> filteredProducts = filterProductBasedOnApplicableProductCode(datafeedName, products);
        return filteredProducts.stream().map(p -> p.getId().toString()).collect(Collectors.joining(","));
    }

    private List<Product> filterProductBasedOnApplicableProductCode(String datafeedName, List<Product> products){
        if(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)){
            List<String> productCodes = agileRatesDataFeedService.getApplicableNonSystemDefaultProductCodes(datafeedName);
            return products.stream().filter(product -> productCodes.contains(product.getProductCode().getProductCodeName()))
                    .filter(product -> !product.isSystemDefault())
                    .collect(Collectors.toList());
        }
        return products.stream().filter(AgileRatesDataFeedService.isProductCodeAgileRatesOrIndependentOrFixedAboveBarOrGroup).collect(Collectors.toList());
    }
    public List<IndependentOverride> getIndependentProductOverrideDetails(LocalDate startDate, LocalDate endDate, int isRollingDate,
                                                                          String rollingStartDate, String rollingEndDate, Language language, int startPosition, int size) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        final boolean isContinuousPricingEnabled = isContinuousPricingEnabled();
        String productIds = fetchCommaSeparatedIndependentProductIds();

        if (StringUtils.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("start_date", convertLocalDateToJavaUtilDate(startDate))
                .and("end_date", convertLocalDateToJavaUtilDate(endDate))
                .and("isRollingDate", isRollingDate)
                .and("rolling_start_date", rollingStartDate)
                .and("rolling_end_date", rollingEndDate)
                .and("isContinuousPricing", Boolean.toString(isContinuousPricingEnabled))
                .and("isRestrictHighestBarEnabled", configParamsService.getParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value()))
                .and("products", productIds);

        try {
            return getCrudServiceBean().findByNativeQuery("select * from dbo.ufn_get_output_override_pricing_report(:property_id, :start_date, :end_date, " +
                            ":isRollingDate, :rolling_start_date, :rolling_end_date, :isContinuousPricing, :isRestrictHighestBarEnabled, :products)",
                    queryParameters.parameters(), startPosition, size, getIndependentProductOverrideMapper(language));
        } catch (Exception e) {
            LOGGER.error("No result found for Independent Override Datafeed: client:: " + PacmanWorkContextHelper.getClientCode() +
                    "PropertyId:: " + propertyId + "Error: = " + e);
            return new ArrayList<>();
        }
    }

    private String fetchCommaSeparatedIndependentProductIds() {
        List<Product> products = crudService.findByNamedQuery(Product.GET_INDEPENDENT_PRODUCTS);
        return products.stream()
                .filter(p -> Objects.equals(p.getStatus(), TenantStatusEnum.ACTIVE))
                .map(p -> p.getId().toString())
                .collect(Collectors.joining(","));
    }

    private RowMapper<IndependentOverride> getIndependentProductOverrideMapper(Language language) {
        return row -> {
            IndependentOverride data = new IndependentOverride();

            data.setArrivalDate(convertLocalDateToJavaUtilDate(fromDate((java.sql.Date) row[1])));
            data.setOverrideLastModifiedOn(new java.util.Date(((java.sql.Timestamp) row[8]).getTime()));
            data.setOverrideType(row[5] == null ? null : ResourceUtil.getOptionalText(((String) row[5]).toLowerCase(), language).orElse((String) row[5]));
            data.setOverrideValue((String) row[6]);
            data.setOverrideLastModifiedBy((String) row[15]);
            data.setNotes((String) row[11]);
            data.setRoomClassCode((String) row[14]);
            data.setRoomTypeCode((String) row[16]);
            data.setRateProductName((String) row[17]);
            return data;
        };
    }
}
