package com.ideas.tetris.pacman.services.property.configuration.service.setup;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.PropertySetupPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecordStatus;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import com.ideas.tetris.pacman.services.property.dto.ParameterValue;
import com.ideas.tetris.pacman.services.property.dto.Property;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@PropertySetupConfigurationService.Qualifier
@Component
@Transactional
public class PropertySetupConfigurationService extends AbstractPropertyConfigurationService {
    private static final Logger LOGGER = Logger.getLogger(PropertySetupConfigurationService.class);
    private static final int PROPERTY_NAME_MAX_LENGTH = 150;
    private static final int DEFAULT_MARKET_SEGMENT_MAX_LENGTH = 250;
    private static final int CURRENCY_MAX_LENGTH = 3;

    @Override
    public PropertyConfigurationRecordType getPropertyConfigurationRecordType() {
        return PropertyConfigurationRecordType.PC;
    }

    @Override
    public List<PropertyConfigurationRecordFailure> doValidate(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId) {
        PropertySetupPropertyConfigurationDto propertySetupDto = (PropertySetupPropertyConfigurationDto) propertyConfigurationDto;

        List<PropertyConfigurationRecordFailure> exceptions = new ArrayList<PropertyConfigurationRecordFailure>();

        // Validate Property Name
        String propertyName = propertySetupDto.getName();
        if (StringUtils.isEmpty(propertyName)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Property Name is required"));
        } else if (propertyName.length() > PROPERTY_NAME_MAX_LENGTH) {
            propertySetupDto.setName(StringUtils.left(propertySetupDto.getName(), PROPERTY_NAME_MAX_LENGTH));
            exceptions.add(new PropertyConfigurationRecordFailure(ConfigurationFileRecordStatus.WARNING,
                    "Property Name cannot be longer than " + PROPERTY_NAME_MAX_LENGTH +
                            " characters. The value has been trimmed."));
        }

        // Validate PCRS
        if (propertySetupDto.isPcrs() == null) {
            exceptions.add(new PropertyConfigurationRecordFailure("PCRS must be 'Y' or 'N'"));
        }

        // Validate Property Timezone
        String propertyTimezone = propertySetupDto.getPropertyTimezone();
        if (StringUtils.isEmpty(propertyTimezone)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Property Timezone is required"));
        } else if (!isPredefinedValueForParameter(IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value(), propertyTimezone)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Property Timezone: " + propertyTimezone + " is not valid"));
        }

        // Validate default market segment
        String defaultMarketSegment = propertySetupDto.getDefaultMarketSegment();
        if (StringUtils.isEmpty(propertySetupDto.getDefaultMarketSegment())) {
            exceptions.add(new PropertyConfigurationRecordFailure("Default Market Segment is required"));
        } else if (defaultMarketSegment.length() > DEFAULT_MARKET_SEGMENT_MAX_LENGTH) {
            propertySetupDto.setDefaultMarketSegment(StringUtils.left(defaultMarketSegment, DEFAULT_MARKET_SEGMENT_MAX_LENGTH));
            exceptions.add(new PropertyConfigurationRecordFailure(ConfigurationFileRecordStatus.WARNING,
                    "Default Market Segment cannot be longer than " + DEFAULT_MARKET_SEGMENT_MAX_LENGTH +
                            " characters.  The value has been trimmed"));
        }

        // Validate Yield Currency Code
        String yieldCurrencyCode = propertySetupDto.getYieldCurrencyCode();
        if (StringUtils.isEmpty(yieldCurrencyCode)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Yield Currency Code is required"));
        } else if (yieldCurrencyCode.length() > CURRENCY_MAX_LENGTH) {
            exceptions.add(new PropertyConfigurationRecordFailure("Yield Currency Code cannot be longer than 3 characters"));
        }

        // Validate Competitive Price Option
        String competitivePriceOption = propertySetupDto.getCompetitivePriceOption();
        if (StringUtils.isEmpty(propertySetupDto.getCompetitivePriceOption())) {
            exceptions.add(new PropertyConfigurationRecordFailure("Competitive Price Option is required"));
        } else if (!isPredefinedValueForParameter(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value(), competitivePriceOption)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Competitor Price Option: " +
                    competitivePriceOption + " is not valid"));
        }

        // Validate Bar Decision Type
        String barDecisionType = propertySetupDto.getBarDecisionType();
        if (StringUtils.isEmpty(barDecisionType)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Bar Decision Type is required"));
        } else if (!isPredefinedValueForParameter(IPConfigParamName.BAR_BAR_DECISION.value(), barDecisionType)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Bar Decision Type: " + barDecisionType + " is not valid"));
        }

        // Validate Allow Min/Max LOS for Bar
        if (propertySetupDto.isAllowMinMaxLOSForBar() == null) {
            exceptions.add(new PropertyConfigurationRecordFailure("Allow Min/Max LOS For BAR must be 'Y' or 'N'"));
        }

        // Validate Allow Day Of Arrival Availability
        if (propertySetupDto.isAllowDayOfArrivalAvailability() == null) {
            exceptions.add(new PropertyConfigurationRecordFailure("Allow Day of Arrival Availability must be 'Y' or 'N'"));
        }

        return exceptions;
    }

    @Override
    public void doHandle(PropertyConfigurationDto pcd, Integer propertyId) {
        PropertySetupPropertyConfigurationDto propertySetupDto = (PropertySetupPropertyConfigurationDto) pcd;

        Property property = null;
        if (propertyId == null || isInactive(propertyId)) {
            property = new Property();
            property.setCode(pcd.getPropertyCode());
        } else {
            property = findProperty(propertyId);
        }

        property.setName(propertySetupDto.getName());
        property.setPropertyTimeZone(propertySetupDto.getPropertyTimezone());
        property.setExternalSystem(propertySetupDto.isPcrs() ? "PCRS" : "Hilstar");

        try {
            if (property.getId() == null) {
                LOGGER.info("Calling PropertyRollout Service to Add Property: " + property.getCode());
                propertyRolloutService.addProperties(Arrays.asList(property));
            } else {
                LOGGER.info("Calling PropertyRollout Service to Update Property: " + property.getCode());
                propertyRolloutService.updateProperties(Arrays.asList(property));
            }

            List<ParameterValue> parameterValues = new ArrayList<ParameterValue>();
            parameterValues.add(getParameterValue(IPConfigParamName.BAR_ALLOW_MIN_MAX_LOSOVERRIDE.value(),
                    propertySetupDto.isAllowMinMaxLOSForBar().toString().toLowerCase(), true));
            parameterValues.add(getParameterValue(IPConfigParamName.BAR_ALLOW_AVAILABLE_FOR_ARRIVAL.value(),
                    propertySetupDto.isAllowDayOfArrivalAvailability() ? "true" : "false", true));
            parameterValues.add(getParameterValue(IntegrationConfigParamName.DEFAULT_MARKET_SEGMENT.value(Constants.RATCHET),
                    propertySetupDto.getDefaultMarketSegment(), true));
            parameterValues.add(getParameterValue(IntegrationConfigParamName.YIELD_CURRENCY_CODE.value(Constants.RATCHET),
                    propertySetupDto.getYieldCurrencyCode(), true));
            parameterValues.add(getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value(),
                    propertySetupDto.getCompetitivePriceOption(), true));
            parameterValues.add(getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value(), propertySetupDto.getBarDecisionType(), true));

            // If these properties already exist, don't update them.
            parameterValues.add(getParameterValue(IntegrationConfigParamName.NEW_RM_COST_RAW.value(Constants.RATCHET), "true", false));
            propertyRolloutService.addParameterValues(pcd.getPropertyCode(), parameterValues);
        } catch (Exception e) {
            LOGGER.error("Unable to add Property: " + property.getCode(), e);
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Unable to add Property: " + property.getCode());
        }
    }

    private ParameterValue getParameterValue(String name, String value, boolean updateIfExists) {
        ParameterValue parameterValue = new ParameterValue();
        parameterValue.setName(name);
        parameterValue.setValue(value);
        parameterValue.setUpdateIfExists(updateIfExists);
        return parameterValue;
    }

    private boolean isInactive(Integer propertyId) {
        com.ideas.tetris.platform.services.daoandentities.entity.Property globalProperty = globalCrudService.find(com.ideas.tetris.platform.services.daoandentities.entity.Property.class, propertyId);
        if (globalProperty != null) {
            return !globalProperty.isActive();
        }
        return false;
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
