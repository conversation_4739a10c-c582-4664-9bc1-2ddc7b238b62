package com.ideas.tetris.pacman.services.runtask;

import com.ideas.tetris.pacman.common.workcontext.WorkContextHelper;
import com.ideas.tetris.pacman.services.client.service.ClientConfigService;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.PropertyCriteria;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class RunTaskJobService {

    @Autowired
	private JobServiceLocal jobService;

    @Autowired
	private ClientConfigService clientService;

    @Autowired
	private PropertyService propertyService;

    @Autowired
	private RunTaskOutputService outputService;

    private static final Logger LOGGER = Logger.getLogger(RunTaskJobService.class);

    public boolean startJob(RunTaskDto dto) {
        try {
            jobService.startJob(JobName.RunTaskJob, buildParamMap(dto));
        } catch (TetrisException te) {
            LOGGER.error("Error while starting job", te);
            return false;
        }
        return true;
    }

    private Map<String, Object> buildParamMap(RunTaskDto dto) {

        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put(JobParameterKey.TASK_NAME, dto.getRunTaskMacro().getTaskName());
        parameters.put(JobParameterKey.PROPERTY_LIST, getDelimitedProperties(dto));
        parameters.put(JobParameterKey.DATE_START, dto.getStartDate());
        parameters.put(JobParameterKey.DATE_END, dto.getEndDate());
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("MMddyyyy_HH-mm-ss");
        parameters.put(JobParameterKey.OUTPUTPATH, outputService.getOutputPath(dto.getRunTaskMacro().getOutputPath() + "/" + ZonedDateTime.now().format(dtf)));
        parameters.put(JobParameterKey.USER_ID, WorkContextHelper.getCurrent().getUserId());
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, dto.getParameters());
        return parameters;
    }

    public List<Property> retrieveProperties() {

        PropertyCriteria propertyCriteria = new PropertyCriteria();
        propertyCriteria.setClient(clientService.getClientByCode(WorkContextHelper.getCurrent().getClientCode()));
        List<Stage> stages = new ArrayList<Stage>();
        stages.add(Stage.ONE_WAY);
        stages.add(Stage.TWO_WAY);
        propertyCriteria.setStages(stages);
        return propertyService.getProperties(propertyCriteria);
    }

    public List<Property> retrievePropertiesWithCodeAndName() {
        return concatenatePropertyCodeAndName(retrieveProperties());
    }

    protected List<Property> concatenatePropertyCodeAndName(List<Property> properties) {
        return properties.stream()
                .map(property -> {
                    property.setDisplayLabelField(property.getCode() + " - " + property.getName());
                    return property;
                }).collect(Collectors.toList());
    }

    private String getDelimitedProperties(RunTaskDto dto) {
        String propertiesDelimited;
        if (dto.getRunAll()) {
            propertiesDelimited = delimitProperties(retrieveProperties());
        } else {
            propertiesDelimited = delimitProperties(dto.getProperties());
        }
        return propertiesDelimited;
    }

    private String delimitProperties(Collection<Property> properties) {

        StringBuilder stringBuilder = new StringBuilder();

        for (Property property : properties) {
            stringBuilder.append(property.getId());
            stringBuilder.append("|");
        }
        return StringUtils.removeEnd(stringBuilder.toString(), "|");
    }
}
