package com.ideas.tetris.pacman.services.datafeed.service;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.ReservationNightDTO;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import static com.ideas.tetris.pacman.common.constants.Constants.*;

@Component
public class ReservationNightDatafeedService {


    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
    DateService dateService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
	private DatafeedEndpointService endpointService;


    public List<ReservationNightDTO> getReservationNight(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        Date startDate = getStartDate(datafeedRequest, isFirstRun);
        Date endDate = datafeedRequest.getEndDate();

        List<ReservationNightDTO> result = new ArrayList<>();
        Map<Integer, String> marketSegIDAndCodes = getMarketAllSegments();
        Map<Integer, String> accomTypeIDAndCodes = getAllAccomTypes();
        boolean postDepartureRevenueAdjustmentEnabled = pacmanConfigParamsService.getBooleanParameterValue
                (FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED);
        if (postDepartureRevenueAdjustmentEnabled) {
            List<Object[]> reservationNights = tenantCrudService.findByNamedQuery(
                    ReservationNight.GET_BY_OCCUPANCY_DATE_RANGE_WITH_POST_DEPARTURE_REVENUE_DATA,
                    QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).parameters(),
                    datafeedRequest.getStartPosition(), datafeedRequest.getSize());
            reservationNights.forEach(reservationNight -> {
                ReservationNightDTO dto = getReservationNightDTO(reservationNight);
                dto.setAccomTypeCode(accomTypeIDAndCodes.get((Integer) reservationNight[10]));
                dto.setMarketSegCode(marketSegIDAndCodes.get((Integer) reservationNight[11]));
                result.add(dto);
            });
        } else {
            List<ReservationNight> reservationNights = tenantCrudService.findByNamedQuery(
                    ReservationNight.GET_BY_OCCUPANCY_DATE_RANGE,
                    QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).parameters(),
                    datafeedRequest.getStartPosition(), datafeedRequest.getSize());
            reservationNights.forEach(reservationNight -> {
                ReservationNightDTO dto = new ReservationNightDTO(reservationNight);
                dto.setAccomTypeCode(accomTypeIDAndCodes.get(reservationNight.getAccomTypeId()));
                dto.setMarketSegCode(marketSegIDAndCodes.get(reservationNight.getMarketSegId()));
                result.add(dto);
            });
        }
        return result;
    }

    public Stream<ReservationNightDTO> getReservationNightStreaming(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        Date startDate = getStartDate(datafeedRequest, isFirstRun);
        Date endDate = datafeedRequest.getEndDate();
        Date lastUpdatedDate = datafeedRequest.getLastSuccessDate();

        Map<Integer, String> marketSegIDAndCodes = getMarketAllSegments();
        Map<Integer, String> accomTypeIDAndCodes = getAllAccomTypes();
        boolean postDepartureRevenueAdjustmentEnabled = pacmanConfigParamsService.getBooleanParameterValue
                (FeatureTogglesConfigParamName.POST_DEPARTURE_REVENUE_ADJUSTMENT_ENABLED);

        if (postDepartureRevenueAdjustmentEnabled) {
            Stream<Object[]> reservationNights = tenantCrudService.findByNamedQueryStreaming(
                    ReservationNight.GET_BY_OCCUPANCY_DATE_RANGE_WITH_POST_DEPARTURE_REVENUE_DATA_DIFF,
                    QueryParameter.with(START_DATE, startDate)
                            .and(END_DATE, endDate)
                            .and(LAST_UPDATED_DATE, lastUpdatedDate).parameters(),
                    datafeedRequest.getStartPosition(), datafeedRequest.getSize());

            return reservationNights.map(reservationNight -> {
                ReservationNightDTO dto = getReservationNightDTO(reservationNight);
                dto.setAccomTypeCode(accomTypeIDAndCodes.get((Integer) reservationNight[10]));
                dto.setMarketSegCode(marketSegIDAndCodes.get((Integer) reservationNight[11]));
                return dto;
            });
        } else {
            Stream<ReservationNight> reservationNights = tenantCrudService.findByNamedQueryStreaming(
                    ReservationNight.GET_BY_OCCUPANCY_DATE_RANGE_DIFF,
                    QueryParameter.with(START_DATE, startDate)
                            .and(END_DATE, endDate)
                            .and(LAST_UPDATED_DATE, DateUtil.convertJavaUtilDateToLocalDateTime(lastUpdatedDate)).parameters(),
                    datafeedRequest.getStartPosition(), datafeedRequest.getSize());

            return reservationNights.map(reservationNight -> {
                ReservationNightDTO dto = new ReservationNightDTO(reservationNight);
                dto.setAccomTypeCode(accomTypeIDAndCodes.get(reservationNight.getAccomTypeId()));
                dto.setMarketSegCode(marketSegIDAndCodes.get(reservationNight.getMarketSegId()));
                return dto;
            });
        }
    }

    private Map<Integer, String> getMarketAllSegments() {
        Map<Integer, String> marketSegIDAndCodesMap = new HashMap<>();
        List<MarketSegmentSummary> allMarketSegs = tenantCrudService.findAll(MarketSegmentSummary.class);
        allMarketSegs.forEach(mSegment -> marketSegIDAndCodesMap.put(mSegment.getId(), mSegment.getCode()));
        return marketSegIDAndCodesMap;
    }

    private Map<Integer, String> getAllAccomTypes() {
        Map<Integer, String> accomTypeIDAndCodesMap = new HashMap<>();
        List<AccomType> allAccomTypes = tenantCrudService.findAll(AccomType.class);
        allAccomTypes.forEach(accomPype -> accomTypeIDAndCodesMap.put(accomPype.getId(), accomPype.getAccomTypeCode()));
        return accomTypeIDAndCodesMap;
    }

    private Date getStartDate(DatafeedRequest datafeedRequest, boolean isFirstRun) {
        Date startDate = datafeedRequest.getStartDate();
        Integer historyDataOffset = getHistoryDataOffsetValue(datafeedRequest.isOptixDatafeed());

        if ((isFirstRun && historyDataOffset > 0) || shouldIncludeHistoryData(datafeedRequest)) {
            startDate = dateService.getCaughtUpLocalDate().minusDays(historyDataOffset).toDate();
        }

        return startDate;
    }

    private ReservationNightDTO getReservationNightDTO(Object[] objArr) {
        ReservationNightDTO dto = new ReservationNightDTO();
        dto.setReservationIdentifier((String) objArr[3]);
        dto.setIndividualStatus((String) objArr[4]);
        dto.setArrivalDate((Date) objArr[5]);
        dto.setDepartureDate((Date) objArr[6]);
        dto.setBookingDate((Date) objArr[7]);
        dto.setCancellationDate((Date) objArr[8]);
        dto.setBookedAccomTypeCode((String) objArr[9]);
        dto.setRoomRevenue(objArr[12] != null ? ((BigDecimal) objArr[12]).setScale(2, RoundingMode.HALF_UP) : null);
        dto.setFoodRevenue(objArr[13] != null ? ((BigDecimal) objArr[13]).setScale(2, RoundingMode.HALF_UP) : null);
        dto.setBeverageRevenue(objArr[14] != null ? ((BigDecimal) objArr[14]).setScale(2, RoundingMode.HALF_UP) : null);
        dto.setTelecomRevenue(objArr[15] != null ? ((BigDecimal) objArr[15]).setScale(2, RoundingMode.HALF_UP) : null);
        dto.setOtherRevenue(objArr[16] != null ? ((BigDecimal) objArr[16]).setScale(2, RoundingMode.HALF_UP) : null);
        dto.setTotalRevenue(objArr[17] != null ? ((BigDecimal) objArr[17]).setScale(2, RoundingMode.HALF_UP) : null);
        dto.setSourceBooking((String) objArr[18]);
        dto.setNationality((String) objArr[19]);
        dto.setRateCode((String) objArr[20]);
        dto.setRateValue(objArr[21] != null ? ((BigDecimal) objArr[21]).setScale(2, RoundingMode.HALF_UP) : null);
        dto.setRoomNumber((String) objArr[22]);
        dto.setBookingType((String) objArr[23]);
        dto.setNumberChildren(((BigDecimal) objArr[24]).intValue());
        dto.setNumberAdults(((BigDecimal) objArr[25]).intValue());
        dto.setCreateDate((Date) objArr[26]);
        dto.setConfirmationNo((String) objArr[27]);
        dto.setChannel((String) objArr[28]);
        dto.setOccupancyDate((Date) objArr[30]);
        dto.setInvBlockCode((String) objArr[33]);
        dto.setMarketCode((String) objArr[34]);
        return dto;
    }

    public boolean shouldIncludeHistoryData(DatafeedRequest datafeedRequest) {
        return datafeedRequest == null ? Boolean.FALSE : endpointService.shouldIncludeHistoryFor(datafeedRequest.getDataFeedType());
    }

    private Integer getHistoryDataOffsetValue(boolean isOptixDatafeed) {
        return pacmanConfigParamsService.getIntegerParameterValue(
                isOptixDatafeed ? IntegrationConfigParamName.OPTIX_DATAFEED_HISTORY_DATA_OFFSET.value()
                        : IntegrationConfigParamName.DATAFEED_HISTORY_DATA_OFFSET.value());
    }

    @VisibleForTesting
    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }
}
