package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.activity.converter.ActivityConverter;
import com.ideas.tetris.pacman.services.activity.converter.RoomTypeMarketSegmentActivityConverter;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesProductConfigurationDTO;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDTARange;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.HotelMktSegAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceMktSegActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.dashboard.dto.MktSegAccomActivityBatchDto;
import com.ideas.tetris.pacman.services.datafeed.dto.AccommodationSales;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.marketsegment.component.MarketSegmentComponent;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.TableBatchAware;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * Provides REST access to the MktSegAccomActivity information. It extends the
 * ActivityService to provide the standard set of endpoints used by the activity
 * data.
 */
@Component
@Transactional
public class RoomTypeMarketSegmentActivityService extends ActivityService<MktSegAccomActivity> {
    private static final Logger LOGGER = Logger.getLogger(RoomTypeMarketSegmentActivityService.class);
    private static final String FILE_META_DATA_ID = "fileMetadataId";
    private static final String ACCOM_CODES = "accomCodes";
    @Autowired
	private FileMetadataService fileMetadataService;
    @Autowired
	private AccommodationService accommodationService;
    @Autowired
	private MarketSegmentComponent marketSegmentComponent;
    @RoomTypeMarketSegmentActivityConverter.Qualifier
    @Autowired
	@Qualifier("roomTypeMarketSegmentActivityConverter")
	private RoomTypeMarketSegmentActivityConverter roomTypeMarketSegmentActivityConverter;
    @Autowired
	private AnalyticalMarketSegmentService analyticalMarketSegmentService;
    @Autowired
	protected PacmanConfigParamsService configService;

    @Autowired
	private AgileRatesConfigurationService agileRatesConfigService;

    @Override
    protected Comparator<Map<String, Object>> getComparator() {
        return null;
    }

    @Override
    public List<Map<String, Object>> save(List<Map<String, Object>> dtos, String correlationId, boolean isPast) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Saving " + dtos + " as Entity: " + getEntityClass());
        }

        boolean isBulkEnabled = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ACTIVITY_TABLE_INSERT_UPDATE_BULKING_ENABLED);

        // Convert the DTOs to entities and save them
        if (isBulkEnabled)
            saveAsBatch(dtos, correlationId);
        else
            saveEntities(dtos, correlationId, isPast);

        // Convert the entities into DTOs
        return dtos;
    }

    private void saveAsBatch(List<Map<String, Object>> dtos, String correlationId) {
        LOGGER.info("Total number of MktSegment Activities : " + dtos.size());
        List<MktSegAccomActivity> entities = getConverter().convertFromMapAll(dtos, correlationId);
        LOGGER.info("About to bulk save entities to database: " + dtos.size());

        long bulkInsertionStartTime = System.currentTimeMillis();
        List<TableBatchAware> marketSegments = convertToTableBatch(entities);
        tenantCrudService.execute(marketSegments.get(0).getInsertStoredProcedureName(), marketSegments);

        LOGGER.info("Completed saving MktSegment entities in Batch: " + dtos.size() +
                " , Overall Time taken :" + (System.currentTimeMillis() - bulkInsertionStartTime));
    }

    @Override
    protected List<MktSegAccomActivity> saveEntities(List<Map<String, Object>> dtos, String correlationId, boolean isPast) {
        LOGGER.info("Starting save of entities: " + dtos.size());
        List<MktSegAccomActivity> entities = getConverter().convertFromMapAll(dtos, correlationId);

        LOGGER.info("About to save entities to database: " + dtos.size());

        tenantCrudService.save(entities);

        LOGGER.info("Completed save of entities: " + dtos.size());
        return entities;
    }


    protected List<TableBatchAware> convertToTableBatch(List<MktSegAccomActivity> mktSegAccomActivities) {
        return mktSegAccomActivities.stream().map(mktSegAccomActivity ->
                MktSegAccomActivityBatchDto.builder()
                        .mktSegAccomActivityId(mktSegAccomActivity.getId())
                        .accomTypeId(mktSegAccomActivity.getAccomTypeId())
                        .mktSegId(mktSegAccomActivity.getMktSegId())
                        .roomsSold(mktSegAccomActivity.getRoomsSold())
                        .arrivals(mktSegAccomActivity.getArrivals())
                        .departures(mktSegAccomActivity.getDepartures())
                        .cancellations(mktSegAccomActivity.getCancellations())
                        .noShows(mktSegAccomActivity.getNoShows())
                        .roomRevenue(mktSegAccomActivity.getRoomRevenue())
                        .foodRevenue(mktSegAccomActivity.getFoodRevenue())
                        .totalRevenue(mktSegAccomActivity.getTotalRevenue())
                        .totalProfit(mktSegAccomActivity.getTotalProfit())
                        .pseudoRoomRevenue(mktSegAccomActivity.getPseudoRoomRevenue())
                        .createDate(mktSegAccomActivity.getCreateDate())
                        .occupancyDate(mktSegAccomActivity.getOccupancyDate())
                        .snapShotDate(mktSegAccomActivity.getSnapShotDate())
                        .lastUpdatedDate(mktSegAccomActivity.getLastUpdatedDate())
                        .build()
        ).collect(Collectors.toList());
    }

    public int updateIndividualTransactions(FileMetadata fileMetadata) {
        Date startDate = DateUtil.addDaysToDate(fileMetadata.getSnapshotDt(), -fileMetadata.getPastWindowSize());
        int updated = tenantCrudService.executeUpdateByNamedQuery(ReservationNight.UPDATE_FILE_METADATA_ID_BY_START_DATE, QueryParameter.with(START_DATE, startDate).and(FILE_META_DATA_ID, fileMetadata.getId()).parameters());
        LOGGER.info("individual trans records updated" + updated);
        return updated;
    }

    public String zeroFilling(DateTime startOccupancyDate, DateTime endOccupancyDate, FileMetadata fileMetadata) {

        int countOfMktSegAccomActivity = tenantCrudService.executeUpdateByNamedQuery(MktSegAccomActivity.INSERT_ZERO_FILL_MKT_SEG_ACCOM_ACTIVITY,
                QueryParameter.with(START_DATE, startOccupancyDate.toString(DateTimeFormat.forPattern(YYYY_MM_DD)))
                        .and(END_DATE, endOccupancyDate.toString(DateTimeFormat.forPattern(YYYY_MM_DD)))
                        .and(FILE_META_DATA_ID_CAPTIALIZED_D, fileMetadata.getId())
                        .parameters());
        return "MktSegAccomActivity:" + countOfMktSegAccomActivity;
    }

    public String selectiveZeroFilling(List<String> accomTypeCodes, DateTime startOccupancyDate, DateTime endOccupancyDate, FileMetadata fileMetadata) {

        int countOfMktSegAccomActivity = tenantCrudService.executeUpdateByNamedQuery(MktSegAccomActivity.INSERT_ZERO_FILL_MKT_SEG_ACCOM_ACTIVITY_BY_ACCOM_TYPES,
                QueryParameter.with(START_DATE, startOccupancyDate.toString(DateTimeFormat.forPattern(YYYY_MM_DD)))
                        .and(END_DATE, endOccupancyDate.toString(DateTimeFormat.forPattern(YYYY_MM_DD)))
                        .and(FILE_META_DATA_ID_CAPTIALIZED_D, fileMetadata.getId())
                        .and(ACCOM_CODES, accomTypeCodes)
                        .parameters());
        return "MktSegAccomActivity:" + countOfMktSegAccomActivity;
    }

    public String zeroFilling(FileMetadata fileMetadata) {
        DateTime startOccupancyDate = getStartOccupancyDate(fileMetadata);
        DateTime endOccupancyDate = getEndOccupancyDate(fileMetadata);

        return zeroFilling(startOccupancyDate, endOccupancyDate, fileMetadata);
    }

    /*
        This is a separate method as fundamentally the rebuild zerofill query does not need to worry about multiple fileMetadataIds in the occupancy date window
        It will however update the window with the FileMetadataId that was chosen as the rebuild FileMetadata record.
     */
    public String zeroFillingAMSRebuild(FileMetadata fileMetadata) {
        int updated = updateIndividualTransactions(fileMetadata);
        LOGGER.info("individual trans records updated" + updated);

        DateTime startOccupancyDate = getStartOccupancyDate(fileMetadata);
        DateTime endOccupancyDate = getEndOccupancyDate(fileMetadata);
        int countOfMktSegAccomActivity = tenantCrudService.executeUpdateByNamedQuery(MktSegAccomActivity.INSERT_ZERO_FILL_MKT_SEG_ACCOM_ACTIVITY_REBUILD,
                QueryParameter.with(START_DATE, startOccupancyDate.toString(DateTimeFormat.forPattern(YYYY_MM_DD)))
                        .and(END_DATE, endOccupancyDate.toString(DateTimeFormat.forPattern(YYYY_MM_DD)))
                        .and(FILE_META_DATA_ID_CAPTIALIZED_D, fileMetadata.getId())
                        .and("snapshotDateTime", fileMetadata.getSnapshotDtTm())
                        .parameters());
        return "MktSegAccomActivity:" + countOfMktSegAccomActivity;
    }

    private DateTime getEndOccupancyDate(FileMetadata fileMetadata) {
        return new DateTime(fileMetadata.getSnapshotDt()).plusDays(fileMetadata.getFutureWindowSize() - 1);
    }

    private DateTime getStartOccupancyDate(FileMetadata fileMetadata) {
        return new DateTime(fileMetadata.getSnapshotDt()).plusDays(-fileMetadata.getPastWindowSize());
    }

    public Integer updatePace(String propertyId, Date startDate, Date endDate, FileMetadata fileMetadata) {
        LOGGER.debug("Started updatePace with " + PaceMktSegActivity.MERGE_PACE_SKIP_ZERO_FILL);

        int numberOfUpdatedRecords = tenantCrudService.executeUpdateByNamedQuery(PaceMktSegActivity.MERGE_PACE_SKIP_ZERO_FILL,
                QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and("propertyId", propertyId)
                        .and("snapshotDt", fileMetadata.getSnapshotDt())
                        .and("snapshotDtTm", fileMetadata.getSnapshotDtTm())
                        .and(FILE_META_DATA_ID_CAPTIALIZED_D, fileMetadata.getId())
                        .parameters());

        LOGGER.info("Finished save of pace, new pace records: " + numberOfUpdatedRecords);
        return numberOfUpdatedRecords;
    }

    public int updatePaceByMarketSegmentId(String propertyId, Date startDate, Date endDate, FileMetadata fileMetadata, Integer marketSegmentId) {
        return tenantCrudService.executeUpdateByNamedQuery(PaceMktSegActivity.MERGE_PACE_BY_MARKET_SEGMENTS_SKIP_ZERO_FILL,
                QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and("propertyId", propertyId)
                        .and("snapshotDt", fileMetadata.getSnapshotDt())
                        .and("snapshotDtTm", fileMetadata.getSnapshotDtTm())
                        .and(FILE_META_DATA_ID_CAPTIALIZED_D, fileMetadata.getId())
                        .and("mktSegId", marketSegmentId)
                        .parameters());
    }

    public List<Integer> fetchMarketSegmentIds(Date startDate, Date endDate) {
        return tenantCrudService.findByNativeQuery("SELECT DISTINCT Mkt_Seg_ID from Mkt_Accom_Activity " +
                        "WHERE Occupancy_DT between :startDate and :endDate " +
                        "ORDER by Mkt_Seg_ID ",
                QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate).parameters());
    }

    @Override
    protected Class<MktSegAccomActivity> getEntityClass() {
        return MktSegAccomActivity.class;
    }

    @Override
    protected ActivityConverter<MktSegAccomActivity> getConverter() {
        return roomTypeMarketSegmentActivityConverter;
    }

    @Override
    protected String getDateRangeQuery() {
        return MktSegAccomActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID;
    }

    @Override
    protected String deleteDateRangeQuery() {
        return MktSegAccomActivity.DELETE_BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID;
    }

    /*
    After populating NGI data into Mkt_Accom_Activity for an HTNG, OXI or FOLS property, this method should be called to aggregate
    the data from the Mkt_Accom_Activity table into the Hotel_Mkt_Accom_Activity table.  For OXI and FOLS, this method creates
    Hotel_Mkt_Accom_Activity for all market segments, since these integrations do not provide summary data at the Hotel MS / RT level.
    For HTNG properties, this method only creates Hotel_Mkt_Accom_Activity for straight market segments, since for split market segments
    the Mkt_Accom_Activity data will have been built from transactions, and actual summary data at the Hotel MS / RT level is available from
    NGI.

    For Opera agent properties, this method should not be used
    because for these integrations the Hotel MS / RT summary data is provided directly from the PMS, and should not be aggregated from
    reservation data, which is essentially what this method does.

    Note that the correlation ID that is passed into this method is the stats correlation ID that is supplied as an input parameter
    to the NGIDeferredDeliveryJob.

    Note this method retrieves the total list of Mkt_Accom_Activities created by the current feed, and creates and persists a list of
    Hotel_Mkt_Accom_Activities of similar size.  These lists may have tens of thousands of entries.  The code has been optimized to execute quickly
    for such sizes, but there is a cost in terms of memory required.  If the memory requirements are found to be excessive, then an alternative would be
    to chunk the data based on an occupancy date range.

    The REST endpoint for this method is provided for testing convenience only, since normally this method is called directly from
    the BuildHotelMktSegSummaryDataStep in the NGIDeferredDeliveryJob
     */


    public int createHotelMktSegSummaryData(String correlationId, boolean straightMarketSegmentsOnly) {
        FileMetadata fileMetadata = fileMetadataService.findByFileLocation(correlationId);
        // there is no File_Metadata for this stats correlation ID, which is not expected
        if (fileMetadata == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "unable to find File_Metadata for correlation ID " + correlationId);
        }
        return createHotelMktSummaryData(fileMetadata, straightMarketSegmentsOnly);
    }


    public int createTotalHotelMktSummaryData() {
        final LocalDateTime startDateTime = LocalDateTime.now();
        LOGGER.info("Hotel_mkt_accom_activity summary creation begins at " + startDateTime);
        truncateHotelMktAccomActivity();
        final int updatedRecords = createHotelMktAccomSummary();
        final LocalDateTime endDateTime = LocalDateTime.now();
        LOGGER.info("Hotel_mkt_accom_activity summary creation ends at " + endDateTime);
        return updatedRecords;
    }

    public void truncateHotelMktAccomActivity() {
        tenantCrudService.executeUpdateByNamedQuery(HotelMktSegAccomActivity.TRUNCATE);
    }


    public int mergeTotalHotelMktSummaryData(final Integer fileMetadataId) {
        final LocalDateTime startDateTime = LocalDateTime.now();
        LOGGER.info("Hotel_mkt_accom_activity summary merge begins at " + startDateTime);
        final int updatedRecords = mergeHotelMktAccomSummary(fileMetadataId);
        final LocalDateTime endDateTime = LocalDateTime.now();
        LOGGER.info("Hotel_mkt_accom_activity summary merge ends at " + endDateTime);
        return updatedRecords;
    }

    public int createHotelMktAccomSummary() {
        final int updatedResults =
                tenantCrudService.executeUpdateByNamedQuery(HotelMktSegAccomActivity.INSERT_HOTEL_MKT_SUMMARY);
        LOGGER.info("Inserted " + updatedResults + " Into Hotel_mkt_accom_activity for MS ");
        return updatedResults;
    }

    public int mergeHotelMktAccomSummary(int fileMetadataId) {
        final int updatedResults =
                tenantCrudService.executeUpdateByNamedQuery(HotelMktSegAccomActivity.MERGE_HOTEL_MKT_STRAIGHT_SUMMARY
                        , QueryParameter.with("fileMetadataId", fileMetadataId).parameters());
        LOGGER.info("Inserted " + updatedResults + " Into Hotel_mkt_accom_activity for MS ");
        return updatedResults;
    }

    public boolean isHotelMktAccomActivityPresent() {
        return tenantCrudService.findOne(HotelMktSegAccomActivity.class) != null;
    }

    private int createHotelMktSummaryData(final FileMetadata fileMetadata, final boolean straightMarketSegmentsOnly) {
        // because the Mkt_Accom_Activity table has not yet been zero-filled, we will only find records with current non-zero activity
        // If the memory required to process the entire list of Mkt_Accom_Activities proves excessive, we could modify the query to include an occupancy date range
        // and loop through the aggregation multiple times until all dates have been processed
        Set<MktSeg> straightMarketSegments = straightMarketSegmentsOnly ? marketSegmentComponent.findStraightMarketSegments() : null;
        List<MktSegAccomActivity> mktActivities = straightMarketSegmentsOnly ? tenantCrudService.findByNamedQuery(MktSegAccomActivity.FIND_BY_FILE_METADATA_ID_AND_MARKET_SEGMENT,
                QueryParameter.with(FILE_META_DATA_ID, fileMetadata.getId()).and("marketSegmentsToInclude", straightMarketSegments.stream().map(MktSeg::getId).collect(Collectors.toSet())).parameters())
                : tenantCrudService.findByNamedQuery(MktSegAccomActivity.FIND_BY_FILE_METADATA_ID,
                QueryParameter.with(FILE_META_DATA_ID, fileMetadata.getId()).parameters());
        int resultCount = 0;
        if (!mktActivities.isEmpty()) {
            Collection<HotelMktSegAccomActivity> entities = aggregate(mktActivities);
            tenantCrudService.save(entities);
            resultCount = entities.size();
        }
        cleanupPastActivityThatHasGoneToZero(fileMetadata, straightMarketSegments);
        return resultCount;
    }


    /*
    If there was an occupancy date/hotel MS/RT for which there was activity in the past, but for which the activity has gone to zero, then we would not have
    updated the Hotel_Mkt_Accom_Activity to reflect the zero activity, since no Mkt_Accom_Activity record was created with the current file metadata ID.
    Since the way we indicate zero activity for an occupancy date/hotel MS/RT in the Hotel_Mkt_Accom_Activity table is by not having an entry in the table,
    we need to delete any Hotel_Mkt_Accom_Activity records, within the date range of the current feed, that do not have the current file metatdata id
    (i.e. any records that were created during previous populations, but had zero activity in the current population).
     */
    private void cleanupPastActivityThatHasGoneToZero(FileMetadata fileMetadata, Set<MktSeg> marketSegmentsToInclude) {
        Date startOccupancyDate = getStartOccupancyDate(fileMetadata).toDate();
        Date endOccupancyDate = getEndOccupancyDate(fileMetadata).toDate();
        if (CollectionUtils.isEmpty(marketSegmentsToInclude)) {
            tenantCrudService.executeUpdateByNamedQuery(HotelMktSegAccomActivity.DELETE_BY_OCCUPANCY_DATERANGE_AND_FILE_METADATA_ID,
                    QueryParameter.with(START_DATE, startOccupancyDate)
                            .and(END_DATE, endOccupancyDate)
                            .and(FILE_META_DATA_ID, fileMetadata.getId())
                            .parameters());

        } else {
            tenantCrudService.executeUpdateByNamedQuery(HotelMktSegAccomActivity.DELETE_BY_OCCUPANCY_DATERANGE_AND_FILE_METADATA_ID_AND_MARKET_SEGMENT,
                    QueryParameter.with(START_DATE, startOccupancyDate)
                            .and(END_DATE, endOccupancyDate)
                            .and(FILE_META_DATA_ID, fileMetadata.getId())
                            .and("marketSegmentsToInclude", marketSegmentsToInclude.stream().map(MktSeg::getCode).collect(Collectors.toSet()))
                            .parameters());
        }

    }

    /*
    This method runs once per population.  So this is where we take the hit of fetching data from the Mkt_Seg, Accom_Type, and Hotel_Mkt_Accom_Activity
    tables and storing it in HashMaps.  These maps are passed to addtoMap() (which is called for each Mkt_Accom_Activity), which allows addToMap() to be
    very efficient.
     */
    private Collection<HotelMktSegAccomActivity> aggregate(List<MktSegAccomActivity> mktActivities) {
        Map<String, HotelMktSegAccomActivity> hotelActivityMap = new HashMap<>();
        Date startDate = mktActivities.get(0).getOccupancyDate();
        Date endDate = mktActivities.get(mktActivities.size() - 1).getOccupancyDate();
        Map<String, Integer> existingHotelActivitiesMap = buildExistingHotelActivitiesMap(startDate, endDate, mktActivities.get(0).getPropertyId());
        Map<Integer, String> roomTypeMap = accommodationService.getAllAccomTypeDetails().stream().collect(Collectors.toMap(AccomType::getId, AccomType::getAccomTypeCode));
        Map<Integer, String> marketSegmentMap = marketSegmentComponent.getMktSegByPropertyId().stream().collect(Collectors.toMap(MktSeg::getId, mktSeg -> toHotelMarketSegment(mktSeg.getCode())));
        mktActivities.forEach(mktActivity -> addToMap(mktActivity, hotelActivityMap, roomTypeMap, marketSegmentMap, existingHotelActivitiesMap));
        return hotelActivityMap.values();
    }

    /*
    Each population is likely to create tens of thousands of Mkt_Accom_Activity records (even before zero-filling).  Therefore, this method is likely to be
    called tens of thousands of times for a single population.  So it had better be fast, and specifically it should not include any SQL database queries.
    So all database data is "pre-fetched" once and stored in HashMaps (luckily, most of the required data is relatively small).  These maps are passed into this
    method, which allows this method to run very quickly.
     */
    private void addToMap(MktSegAccomActivity mktActivity, Map<String, HotelMktSegAccomActivity> hotelActivityMap, Map<Integer, String> roomTypeMap,
                          Map<Integer, String> marketSegmentMap, Map<String, Integer> existingHotelActivitiesMap) {
        String roomTypeCode = roomTypeMap.get(mktActivity.getAccomTypeId());
        String marketSegmentCode = marketSegmentMap.get(mktActivity.getMktSegId());
        Date occupancyDate = mktActivity.getOccupancyDate();
        String key = buildKey(occupancyDate, marketSegmentCode, roomTypeCode);
        HotelMktSegAccomActivity hotelActivity = hotelActivityMap.get(key);
        if (hotelActivity == null) {
            // we have not yet encountered in this population this combination of occupancy date, hotel MS, and RT
            hotelActivity = new HotelMktSegAccomActivity();
            hotelActivity.setId(existingHotelActivitiesMap.get(key));
            // if there is a record for this occupancy date, hotel MS, and RT that was created
            // during a previous population, we need to update this record rather than create a new one.  However, we disregard the solds, revenue, etc.
            // values from the previous population
            hotelActivity.setPropertyId(mktActivity.getPropertyId());
            hotelActivity.setAccomTypeCode(roomTypeCode);
            hotelActivity.setMktSegCode(marketSegmentCode);
            hotelActivity.setOccupancyDate(occupancyDate);
            hotelActivity.setRoomRevenue(BigDecimal.ZERO);
            hotelActivity.setFoodRevenue(BigDecimal.ZERO);
            hotelActivity.setTotalRevenue(BigDecimal.ZERO);
            hotelActivity.setRoomsSold(BigDecimal.ZERO);
            hotelActivity.setArrivals(BigDecimal.ZERO);
            hotelActivity.setDepartures(BigDecimal.ZERO);
            hotelActivity.setCancellations(BigDecimal.ZERO);
            hotelActivity.setNoShows(BigDecimal.ZERO);
            hotelActivity.setCreateDate(new Date());

            hotelActivity.setSnapShotDate(mktActivity.getSnapShotDate());
            hotelActivity.setFileMetadataId(mktActivity.getFileMetadataId());
            hotelActivityMap.put(key, hotelActivity);
        }
        addMtkActivity(mktActivity, hotelActivity);
    }

    /*
    we need to efficiently determine for a large number of Mkt_Accom_Activity records if there is already in the database a Hotel_Mkt_Accom_Activity
    record with matching occupancy date, hotel MS, and RT.  Querying the database individually for each  Mkt_Accom_Activity record is too slow.
    Therefore, fetch all of the existing Hotel_Mkt_Accom_Activity records for the date range of the feed, and create a map keyed by the
    occupancy date, hotel MS, and RT.  We can do thousands of lookups in this map in a tiny fraction of the time required to do thousands of database
    queries.  In order to save memory, only the ID of the Hotel_Mkt_Accom_Activity is saved in the map, since this is all we need to make sure the existing
    record is updated rather than adding a new record.
     */
    private Map<String, Integer> buildExistingHotelActivitiesMap(Date startDate, Date endDate, Integer propertyId) {
        Map<String, Integer> map = new HashMap<>();
        List<HotelMktSegAccomActivity> existingRecords = tenantCrudService.findByNamedQuery(
                HotelMktSegAccomActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and("propertyId", propertyId)
                        .parameters());
        existingRecords.forEach(record -> addToMap(record, map));
        return map;

    }

    private void addToMap(HotelMktSegAccomActivity hotelMktActivity, Map<String, Integer> map) {
        String key = buildKey(hotelMktActivity.getOccupancyDate(), hotelMktActivity.getMktSegCode(), hotelMktActivity.getAccomTypeCode());
        map.put(key, hotelMktActivity.getId());
    }


    private String buildKey(Date occupancyDate, String marketSegmentCode, String roomTypeCode) {
        return DateUtil.formatDate(occupancyDate, YYYY_MM_DD) + "::" + marketSegmentCode + "::" + roomTypeCode;
    }

    /*
    Add the solds, revenue, etc. from the Mkt_Accom_Activity record to the corresponding
    Hotel_Mkt_Accom_Activity record
     */
    private void addMtkActivity(MktSegAccomActivity mktActivity, HotelMktSegAccomActivity hotelActivity) {
        hotelActivity.setRoomsSold(hotelActivity.getRoomsSold().add(mktActivity.getRoomsSold()));
        hotelActivity.setArrivals(hotelActivity.getArrivals().add(mktActivity.getArrivals()));
        hotelActivity.setDepartures(hotelActivity.getDepartures().add(mktActivity.getDepartures()));
        hotelActivity.setCancellations(hotelActivity.getCancellations().add(mktActivity.getCancellations()));
        hotelActivity.setNoShows(hotelActivity.getNoShows().add(mktActivity.getNoShows()));
        hotelActivity.setRoomRevenue(hotelActivity.getRoomRevenue().add(mktActivity.getRoomRevenue()));
        hotelActivity.setFoodRevenue(hotelActivity.getFoodRevenue().add(mktActivity.getFoodRevenue()));
        hotelActivity.setTotalRevenue(hotelActivity.getTotalRevenue().add(mktActivity.getTotalRevenue()));
    }

    /*
    this method converts an AMS code like "CORP_QYL" to the corresponding Hotel MS code of "CORP",
    while also handling unusual Hotel MS codes like "CORP_I_LIKE_UNDERSCORES"
     */
    private String toHotelMarketSegment(String marketSegment) {
        List<String> suffixes = Arrays.stream(AnalyticalMarketSegmentAttribute.values())
                .map(AnalyticalMarketSegmentAttribute::getSuffix)
                .collect(Collectors.toList());
        suffixes.add("DEF");
        for (String suffix : suffixes) {
            if (marketSegment.endsWith("_" + suffix)) {
                return marketSegment.substring(0, marketSegment.lastIndexOf('_'));
            }
        }
        return marketSegment;
    }

    public Set<AccomType> getBaseRoomTypesByThresholdOccupancyPercentage(Set<String> productNames, BigDecimal cumulativePercentage, Integer businessTypeId) {
        Set<Product> products = getActiveProductsByName(productNames);
        if (CollectionUtils.isEmpty(products)) {
            return Collections.emptySet();
        }
        List<AccommodationSales> accommodationSales = getAccomSalesData(businessTypeId);
        List<AccommodationSales> accomSalesByOccupancyPercent = getAccomSalesByThresholdOccupancyPercentage(accommodationSales, cumulativePercentage);

        return getBaseAccomTypesByThresholdOccupancyPercentage(accomSalesByOccupancyPercent);
    }

    private void createProductAccomTypes(Set<Product> products, Set<AccomType> baseAccomTypes) {
        List<ProductAccomType> productAccomTypeList = new ArrayList<>();
        for (Product product : products) {
            for (AccomType accomType : baseAccomTypes) {
                ProductAccomType productAccomType = new ProductAccomType();
                productAccomType.setProduct(product);
                productAccomType.setAccomType(accomType);
                productAccomTypeList.add(productAccomType);
            }
        }
        if (CollectionUtils.isNotEmpty(productAccomTypeList)) {
            tenantCrudService.save(productAccomTypeList);
        }
    }

    public void deleteProductAccomTypes(Set<Product> products) {
        Map<String, Object> parameters = QueryParameter.with("productIds", products.stream().map(Product::getId).collect(Collectors.toList())).parameters();
        tenantCrudService.executeUpdateByNativeQuery("delete from Product_AT where Product_ID in ( :productIds )", parameters);
    }

    private Set<AccomType> getBaseAccomTypesByThresholdOccupancyPercentage(List<AccommodationSales> accomSales) {
        if (CollectionUtils.isEmpty(accomSales)) {
            return Collections.emptySet();
        }
        Set<AccomType> baseAccomTypes = new HashSet<>();
        List<AccomType> allBaseAccomTypes = getAllBaseRoomTypes();

        accomSales.forEach(accommodationSales -> {
            Optional<AccomType> matchingAccomType = allBaseAccomTypes.stream()
                    .filter(baseAccomType -> Objects.equals(baseAccomType.getAccomClass().getId(), accommodationSales.getAccomClassId()))
                    .findFirst();
            matchingAccomType.ifPresent(baseAccomTypes::add);
        });
        return baseAccomTypes;
    }

    private List<AccommodationSales> getAccomSalesData(Integer businessTypeId) {
        List<Object[]> result = tenantCrudService.findByNamedQuery(MktSegAccomActivity.GET_PERCENTAGE_OF_ROOMS_SOLD_BY_ROOM_TYPE_LEVEL,
                QueryParameter.with("businessTypeId", businessTypeId).parameters());

        return Optional.ofNullable(result)
                .orElse(Collections.emptyList()).stream()
                .map(AccommodationSales::new)
                .sorted(Comparator.comparing(AccommodationSales::getCumulativePercentage))
                .collect(Collectors.toList());
    }

    private List<AccommodationSales> getAccomSalesByThresholdOccupancyPercentage(List<AccommodationSales> accomSales, BigDecimal cumulativePercentage) {
        if (CollectionUtils.isEmpty(accomSales)) {
            return Collections.emptyList();
        }
        List<AccommodationSales> totalAccomTypesSold = new ArrayList<>();
        List<AccommodationSales> accomSalesLessThanThreshold = accomSales.stream()
                .filter(soldsData -> soldsData.getCumulativePercentage().compareTo(cumulativePercentage) < 0)
                .collect(Collectors.toList());

        Optional<AccommodationSales> accomSalesGreaterThanThreshold = accomSales.stream()
                .filter(soldsData -> soldsData.getCumulativePercentage().compareTo(cumulativePercentage) >= 0)
                .findFirst();
        accomSalesGreaterThanThreshold.ifPresent(totalAccomTypesSold::add);
        totalAccomTypesSold.addAll(accomSalesLessThanThreshold);
        return totalAccomTypesSold;
    }

    private List<AccomType> getAllBaseRoomTypes() {
        return tenantCrudService.findByNamedQuery(PricingAccomClass.DISTINCT_BASE_ROOM_TYPES_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public Set<String> getActiveSmallGroupProductNames() {
        List<Product> smallGroupProducts = tenantCrudService
                .findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters());
        return smallGroupProducts.stream().filter(Product::isActive).map(Product::getName).collect(Collectors.toSet());
    }

    public Set<Product> getActiveProductsByName(Set<String> names) {
        if (CollectionUtils.isNotEmpty(names)) {
            List<Product> products = tenantCrudService.findByNamedQuery(Product.GET_ALL_BY_NAMES, QueryParameter.with("names", names).parameters());
            return products.stream().filter(product -> Objects.equals(product.isActive(), Boolean.TRUE)).collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

    public boolean isConfigureBaseRoomTypeForSmallGroupEnabled() {
        return configService.getBooleanParameterValue(FeatureTogglesConfigParamName.CONFIGURE_BASE_ROOM_TYPES_FOR_SMALL_GROUP);
    }

    public void cleanupDecisionsAndConfigureOffsets(Set<AccomType> accomTypes) {
        List<Product> smallGroupProducts = agileRatesConfigService.findSmallGroupProducts();
        List<AgileRatesDTARange> agileRatesDTARanges = agileRatesConfigService.findAllDTARanges();

        for (Product product : smallGroupProducts) {
            AgileRatesProductConfigurationDTO productConfig = agileRatesConfigService.loadAgileRatesProductConfigurationByProductId(product.getId());
            setRoomTypesAndRoomClassesRemovedList(productConfig.getRoomTypes(), accomTypes, productConfig);
            productConfig.setRoomTypes(accomTypes);
            agileRatesConfigService.updateOffsetsAndOverrides(productConfig, agileRatesDTARanges);
            saveSmallGroupProducts(productConfig);
        }
    }

    private void setRoomTypesAndRoomClassesRemovedList(Collection<AccomType> originalSelectedRoomTypesList, Collection<AccomType> currentlySelectedRoomTypesList,
                                                       AgileRatesProductConfigurationDTO productConfig) {
        List<AccomType> unselectedRoomTypes = originalSelectedRoomTypesList.stream().filter(accomType -> !currentlySelectedRoomTypesList.contains(accomType)).collect(Collectors.toList());
        productConfig.setRoomTypesRemovedList(unselectedRoomTypes);

        List<AccomClass> currentlySelectedRoomClasses = currentlySelectedRoomTypesList.stream().map(AccomType::getAccomClass).collect(Collectors.toList());
        List<AccomClass> unselectedRoomClasses = unselectedRoomTypes.stream().map(AccomType::getAccomClass).filter(accomClass -> !currentlySelectedRoomClasses.contains(accomClass)).collect(Collectors.toList());
        productConfig.setRoomClassesRemovedList(unselectedRoomClasses);
    }

    private void saveSmallGroupProducts(AgileRatesProductConfigurationDTO productConfigDTO) {
        saveProductConfiguration(productConfigDTO);
        if (isSmallGroupProductMinMaxRoomPopUpEnabled()) {
            saveAllSmallGroupProducts(productConfigDTO);
        }
    }

    public void saveProductConfiguration(AgileRatesProductConfigurationDTO productConfiguration) {
        if (productConfiguration.getProduct().isLargestSGProduct()) {
            productConfiguration.setMaximumRooms(null);
        }
        agileRatesConfigService.saveSmallGroupProductConfiguration(productConfiguration);
    }

    private boolean isSmallGroupProductMinMaxRoomPopUpEnabled() {
        return configService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_SMALL_GRP_MIN_MAX_ROOM_POPUP);
    }

    public void saveAllSmallGroupProducts(AgileRatesProductConfigurationDTO productConfiguration) {
        List<Product> allSmallProducts = agileRatesConfigService.findSmallGroupProducts();
        if (allSmallProducts != null) {
            setMaxRoomsForLargestSmallGroupProductToNull(allSmallProducts, productConfiguration);
            agileRatesConfigService.saveProducts(allSmallProducts.stream().filter(product -> !product.getId().equals(productConfiguration.getProductId())).collect(Collectors.toList()));
            agileRatesConfigService.validateSmallGroupProductsWithProductId(productConfiguration.getProductId());
        }
    }

    private void setMaxRoomsForLargestSmallGroupProductToNull(List<Product> allSmallGroupProducts, AgileRatesProductConfigurationDTO productConfiguration) {
        for (Product product : allSmallGroupProducts) {
            if (product.equals(productConfiguration.getProduct()) && productConfiguration.getProduct().isLargestSGProduct()) {
                product.setLargestSGProduct(true);
                product.setMaxRooms(null);
            }
            if (product.equals(productConfiguration.getProduct()) && !productConfiguration.getProduct().isLargestSGProduct()) {
                product.setLargestSGProduct(false);
            }
        }
    }

    public List<AccomType> getActiveAccomTypes() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_ACTIVE, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }
}
