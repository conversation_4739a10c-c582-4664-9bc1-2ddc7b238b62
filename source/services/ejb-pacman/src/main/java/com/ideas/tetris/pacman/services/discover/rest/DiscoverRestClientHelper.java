package com.ideas.tetris.pacman.services.discover.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideas.tetris.pacman.services.discover.DiscoverConstants;
import com.ideas.tetris.pacman.services.discover.entity.DiscoverUser;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.apache.commons.collections.MapUtils;
import org.apache.log4j.Logger;
import org.jboss.resteasy.specimpl.ResteasyUriBuilder;
import org.jboss.resteasy.util.HttpResponseCodes;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class DiscoverRestClientHelper {

    private static final Logger LOGGER = Logger.getLogger(DiscoverRestClientHelper.class);

    private final Client client;
    private final ObjectMapper mapper;

    DiscoverRestClientHelper() {
        client = ClientBuilder.newBuilder().build();
        mapper = new ObjectMapper();
    }

    public DiscoverRestClientHelper(Client client, ObjectMapper mapper) {
        this.client = client;
        this.mapper = mapper;
    }

    private String populateQueryParameters(final Map<String, String> parameters, String apiKey, ResteasyUriBuilder uriBuilder) {
        final Map<String, String> params = MapUtils.isEmpty(parameters) ? new HashMap<>() : parameters;
        if (!Objects.isNull(apiKey)) {
            params.put("api_key", apiKey);
        }
        List<String> expectedQueryParameters = uriBuilder.getPathParamNamesInDeclarationOrder();
        params.keySet().forEach(key -> {
            if (!expectedQueryParameters.contains(key)) {
                uriBuilder.clientQueryParam(key, params.get(key));
            }
        });
        return uriBuilder.buildFromMap(params).toString();
    }

    String createPutUrl(DiscoverRestEndPoints discoverRestEndPoint, String discoverUserId, boolean isV3) {
        LOGGER.debug("Creating url for API version V3: " + isV3);
        DiscoverRestProperties discoverRestProperties = new DiscoverRestProperties();
        String urlToCall = discoverRestProperties.getUrl() + discoverRestEndPoint.getEndpoint();
        ResteasyUriBuilder uriBuilder = ((ResteasyUriBuilder) ResteasyUriBuilder.fromUri(urlToCall));
        Map<String, String> parameters = new HashMap<>();
        parameters.put(DiscoverConstants.USER_ID, discoverUserId);
        String apiKey = getApiKey(discoverRestProperties, isV3);
        return populateQueryParameters(parameters, apiKey, uriBuilder);
    }

    String createGetOrPostUrl(DiscoverRestEndPoints discoverRestEndPoints, Map<String, String> parameters, boolean isV3) {
        LOGGER.debug("Creating url for API version V3: " + isV3);
        checkForValidRestEndpoint(discoverRestEndPoints);
        DiscoverRestProperties discoverRestProperties = new DiscoverRestProperties();
        String urlToCall = discoverRestProperties.getUrl() + discoverRestEndPoints.getEndpoint();
        ResteasyUriBuilder uriBuilder = (ResteasyUriBuilder) ResteasyUriBuilder.fromUri(urlToCall);
        String apiKey = getApiKey(discoverRestProperties, isV3);
        return populateQueryParameters(parameters, apiKey, uriBuilder);
    }

    DiscoverUser getDiscoverUserFromResponse(JSONObject result) {
        DiscoverUser discoverUser;
        try {
            discoverUser = mapper.readValue(result.toString(), DiscoverUser.class);
            LOGGER.debug("Retrieved discover user from response");
        } catch (IOException e) {
            LOGGER.debug("Error converting json to DiscoverUser object", e);
            throw new TetrisException(ErrorCode.UNABLE_TO_CONVERT_OBJECT, "Error converting json to DiscoverUser object", e);
        }
        return discoverUser;
    }

    String makeRestCallAndReturnResponse(String urlToCall, MultivaluedHashMap headers) {
        Response response = null;
        try {
            WebTarget target = client.target(urlToCall);
            response = target.request().headers(headers).get();
            checkResponse(response);
            return response.readEntity(String.class);
        } catch (Exception e) {
            LOGGER.error("Error while fetching user from Discover", e);
            throw new TetrisException(ErrorCode.DISCOVER_REST_CLIENT_FAILURE, "discover.error.while.fetching.user.from.discover", e);
        } finally {
            close(response);
        }
    }

    void validateParams(DiscoverRestEndPoints discoverRestEndPoints, Entity<?> entity) {
        if (Objects.isNull(discoverRestEndPoints)) {
            throw new IllegalArgumentException("Rest Endpoint is null");
        }
        if (Objects.isNull(entity) || Objects.isNull(entity.getEntity())) {
            throw new IllegalArgumentException("Entity cannot be null");
        }
    }

    void checkResponse(Response response) {
        if (response.getStatusInfo().getStatusCode() >= HttpResponseCodes.SC_BAD_REQUEST) {
            throw new TetrisException(ErrorCode.DISCOVER_REST_CLIENT_FAILURE, "[" + response.getStatusInfo().getStatusCode() + "] " + response.getStatusInfo().getReasonPhrase());
        }
    }

    void close(Response response) {
        if (Objects.nonNull(response)) {
            response.close();
        }
    }

    private void checkForValidRestEndpoint(DiscoverRestEndPoints restEndpoint) {
        if (Objects.isNull(restEndpoint)) {
            throw new IllegalArgumentException("Must specify a non-null endpoint");
        }
    }

    private String getApiKey(DiscoverRestProperties discoverRestProperties, boolean isV3) {
        return isV3 ? null : discoverRestProperties.getApiKey();
    }
}

