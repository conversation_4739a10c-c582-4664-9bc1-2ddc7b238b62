package com.ideas.tetris.pacman.services.purge;

import static com.ideas.tetris.pacman.services.purge.PurgeConstants.DBO;

public enum DecisionPurgeEnum implements Purgable {

    DECISION("DECISION", "BUSINESS_DT");

    private String schema;
    private String tableToDelete;
    private String tableToCompare;
    private String fieldToCompare;

    DecisionPurgeEnum(String tableToCompare, String fieldToCompare) {
        this.schema = DBO;
        this.tableToDelete = this.name();
        this.tableToCompare = tableToCompare;
        this.fieldToCompare = fieldToCompare;
    }

    @Override
    public String getSchema() {
        return schema;
    }

    @Override
    public String getTableToDelete() {
        return tableToDelete;
    }

    @Override
    public String getTableToCompare() {
        return tableToCompare;
    }

    @Override
    public String getFieldToCompare() {
        return fieldToCompare;
    }

    @Override
    public boolean isFailedSilently() {
        return true;
    }
}

