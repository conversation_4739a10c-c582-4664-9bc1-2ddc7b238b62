package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientAttribute;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientPropertyAttributePairing;
import com.ideas.tetris.pacman.services.customattributeservice.service.CustomAttributeService;
import com.ideas.tetris.pacman.services.datafeed.dto.propertyattribute.PropertySpecificAttribute;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by idnekp on 2/19/2016.
 */
@Component
@Transactional
public class PropertyAttributeService {

    @Autowired
    CustomAttributeService customAttributeService;

    public List<ClientPropertyAttributePairing> getClientPropertyAttributePairingList(Integer propertyId) {
        return getClientPropertyAttributePairingListFor(propertyId);
    }

    private List<ClientPropertyAttributePairing> getClientPropertyAttributePairingListFor(Integer propertyId) {
        return customAttributeService.getAllClientPropertyAttributePairing(propertyId);
    }

    public List<PropertySpecificAttribute> getPropertySpecificAttributes(Integer propertyId) {
        List<PropertySpecificAttribute> propertySpecificAttributes = new ArrayList<>();
        Map<ClientAttribute, ClientPropertyAttributePairing> clientPropertyAttributePairingMap = customAttributeService.populateAttributesAndPairingMap(propertyId);
        for (ClientAttribute clientAttribute : clientPropertyAttributePairingMap.keySet()) {
            PropertySpecificAttribute propertySpecificAttribute = new PropertySpecificAttribute();
            propertySpecificAttribute.setAttributeName(clientAttribute.getClientAttributeName());
            ClientPropertyAttributePairing clientPropertyAttributePairing = clientPropertyAttributePairingMap.get(clientAttribute);
            propertySpecificAttribute.setAttributeValue(clientPropertyAttributePairing != null ? clientPropertyAttributePairing.getClientAttributeValue()
                    .getClientAttributeValue() : null);
            propertySpecificAttributes.add(propertySpecificAttribute);
        }

        return propertySpecificAttributes;
    }
}
