package com.ideas.tetris.pacman.services.forecast.mapper;

import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailForPastSevenDaysDTO;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

import static com.ideas.tetris.pacman.services.forecast.ExpectedForecastConstant.HIPHEN;

public class ExpectedForecastDetailForLastSevenDaysRowMapper implements RowMapper<ExpectedForecastDetailForPastSevenDaysDTO> {
    @Override
    public ExpectedForecastDetailForPastSevenDaysDTO mapRow(Object[] row) {

        ExpectedForecastDetailForPastSevenDaysDTO expectedForecastDetailForPastSevenDaysDTO = new ExpectedForecastDetailForPastSevenDaysDTO();
        expectedForecastDetailForPastSevenDaysDTO.setOccupancyDate((Date) row[0]);
        expectedForecastDetailForPastSevenDaysDTO.setTransientOnBooks(getValueFromRow(row[1]));
        expectedForecastDetailForPastSevenDaysDTO.setOtherTransientOnBooks(getValueFromRow(row[2]));
        expectedForecastDetailForPastSevenDaysDTO.setGroups(getValueFromRow(row[3]));
        return expectedForecastDetailForPastSevenDaysDTO;
    }

    private String getValueFromRow(Object row) {
        return row == null ? HIPHEN : row + StringUtils.EMPTY;
    }
}
