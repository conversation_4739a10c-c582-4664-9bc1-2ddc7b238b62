package com.ideas.tetris.pacman.services.reports.pricing;


import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.pricing.dto.PricingByLos;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.JasperReportDataConverter;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.PricingBarByLosConverter;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.PricingReportCriteria;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.scheduledreport.service.JasperReportService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;

import javax.inject.Inject;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PricingBarByLOSService extends JasperReportService<List<PricingByLos>, PricingReportCriteria> {

    @PricingBarByLosConverter.Qualifier
    @Autowired
	@Qualifier("pricingBarByLosConverter")
	private JasperReportDataConverter<List<PricingByLos>, PricingReportCriteria> pricingReportConverter;

    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService tenantCrudService;

    @Autowired
	private PricingService pricingService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Override
    protected JasperReportDataConverter<List<PricingByLos>, PricingReportCriteria> getJasperReportDataConverter() {
        return pricingReportConverter;
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    @Override
    public String getReportTitle(ScheduledReport<PricingReportCriteria> scheduledReport) {
        return ResourceUtil.getText("pricingReport.barByLos.title", scheduledReport.getLanguage());
    }

    @Override
    protected List<PricingByLos> retrieveData(ScheduledReport<PricingReportCriteria> scheduledReport) {

        List<PricingByLos> reportData = null;
        PricingReportCriteria reportCriteria = scheduledReport.getReportCriteria();

        LocalDate startDate = DateUtil.convertJodaToJavaLocalDate(reportCriteria.getStartDate());
        LocalDate endDate = DateUtil.convertJodaToJavaLocalDate(reportCriteria.getEndDate());
        Integer rolling = reportCriteria.getIsRollingDate();
        String rollingStartDate = reportCriteria.getRollingStartDate();
        String rollingEndDate = reportCriteria.getRollingEndDate();
        String roomClasses = reportCriteria.getRoomClasses();
        boolean isPhysicalCapacityEnabled = pacmanConfigParamsService.isEnablePhysicalCapacityConsideration();

        List<Integer> competitorIds = new ArrayList<>();
        if (reportCriteria.getCompList() != null) {
            String competitorId[] = reportCriteria.getCompList().replace("[", "").replace("]", "").split(",");
            if (competitorId.length != 15) {
                throw new RuntimeException("Competitor length should be 15, it is now " + competitorId.length);
            }
            for (int i = 0; i < 15; i++) {
                competitorIds.add(Integer.parseInt(competitorId[i].trim()));
            }
        }
        reportData = pricingService.getPricingByLosData(startDate, endDate, roomClasses, competitorIds, rolling, rollingStartDate, rollingEndDate, isPhysicalCapacityEnabled);
        populateReportCriteria(reportCriteria);
        return reportData;
    }

    public void populateReportCriteria(PricingReportCriteria reportCriteria) {

        String propertyId = reportCriteria.getPropertyId();
        String userId = reportCriteria.getUserId();
        String baseCurrency = reportCriteria.getCurrency();

        LocalDate startDate = DateUtil.convertJodaToJavaLocalDate(reportCriteria.getStartDate());
        LocalDate endDate = DateUtil.convertJodaToJavaLocalDate(reportCriteria.getEndDate());
        Integer rolling = reportCriteria.getIsRollingDate();
        String rollingStartDate = reportCriteria.getRollingStartDate();
        String rollingEndDate = reportCriteria.getRollingEndDate();

        String sql = " select * from dbo.ufn_get_filter_selection " +
                "('" + propertyId + "'," + userId + ",'" + baseCurrency + "'," +
                "'" + rolling + "'," +
                "'" + startDate + "'," +
                "'" + endDate + "','','','','','',''," +
                "'" + rollingStartDate + "'," +
                "'" + rollingEndDate + "','','','','','','' )";

        List<Object[]> resultList = tenantCrudService.findByNativeQuery(sql);
        if (resultList != null) {

            Property property = getGlobalCrudService().find(Property.class, Integer.parseInt(reportCriteria.getPropertyId()));
            TimeZone pTimeZone = getAlertService().getPropertyTimeZone(property);
            ZonedDateTime createDateTime = JavaLocalDateUtils.toZonedDateTime(ScheduledReportUtils.convertDateTimeToTimeZone(reportCriteria.getCreatedOn(), pTimeZone));

            Object[] result = resultList.get(0);
            reportCriteria.setPropertyName((String) result[0]);
            reportCriteria.setCreatedBy((String) result[1]);
            reportCriteria.setCreatedOn(JavaLocalDateUtils.toJodaDateTime(createDateTime));
            reportCriteria.setStartDate(JavaLocalDateUtils.toJodaLocalDate(DateUtil.convertJavaUtilDateToLocalDate((Date) result[3])));
            reportCriteria.setEndDate(JavaLocalDateUtils.toJodaLocalDate(DateUtil.convertJavaUtilDateToLocalDate((Date) result[4])));
        }
    }

}
