package com.ideas.tetris.pacman.services.security;

import com.ideas.infra.tetris.security.domain.*;
import com.ideas.tetris.pacman.services.fds.uas.PermissionDefinition;
import com.ideas.tetris.pacman.services.fds.uas.UASService;
import com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class FDSPermissionsService {
    private static final Logger LOGGER = Logger.getLogger(FDSPermissionsService.class.getName());
    @Autowired
	private MenuService menuService;
    @Autowired
	private UASService uasService;
    @Autowired
    AuthorizationService authorizationService;

    public List<Page> getAllPagesFDSMigration() {
        //Get the raw menu which contains all pages which are part of the new menu.
        List<Page> rawMenu = menuService.getRawMenuFDSMigration();

        //Add freeflowing pages into the proper order to the pages list before parsing the tree
        List<Page> allPages = menuService.getAllPages();
        List<com.ideas.infra.tetris.security.domain.Page> pagesWithExtraDepth = allPages
                .stream()
                .filter(item -> item.getIndependentPageParent() != null)
                .collect(Collectors.toList());

        for (Page page : pagesWithExtraDepth) {
            if (CollectionUtils.isNotEmpty(page.getMenuExcludedPages()) && CollectionUtils.isEmpty(page.getPages())) {
                page.setPages(page.getMenuExcludedPages());
            }
            menuService.addIndependentPageToParent(rawMenu, page);
        }

        return rawMenu;
    }

    public void savePagesToDefinitionTable() {
        List<Page> allPagesUniversalLogin = getAllPagesFDSMigration();

        List<PermissionDefinition> permissionDefinitions = new ArrayList<>(uasService.getPermissionDefinitions());
        Set<UUID> allSavedUUIDs = permissionDefinitions
                .stream()
                .map(PermissionDefinition::getId)
                .collect(Collectors.toSet());
        for (Page page : allPagesUniversalLogin) {
            getBasePermissionDefinitions(permissionDefinitions, page, allSavedUUIDs);
        }
        uasService.savePermissionDefinitions(permissionDefinitions);
    }

    public void mapPagesToHierarchy() {
        List<Page> allPagesUniversalLogin = getAllPagesFDSMigration();
        List<PermissionDefinition> savedDefinitions = uasService.getPermissionDefinitions();

        Map<String, PermissionDefinition> flatAllPermissions = savedDefinitions.stream().collect(Collectors.toMap(
                PermissionDefinition::getPageCode,
                Function.identity()
        ));
        for (Page page : allPagesUniversalLogin) {
            addParentAndChildrenAndIterate(flatAllPermissions, page, null);
        }
        uasService.savePermissionDefinitions(savedDefinitions);
    }

    private List<PermissionDefinition> getBasePermissionDefinitions(List<PermissionDefinition> definitions, Page currentPage, Set<UUID> allSavedUUIDs) {
        PermissionDefinition definition = getBasePageDefinitions(currentPage);
        PermissionDefinition existingDefinition = definitions
                .stream()
                .filter(item -> item.getPageCode().equals(definition.getPageCode()))
                .findFirst()
                .orElse(null);
        if (existingDefinition == null) {
            definitions.add(definition);
        } else {
            //Values to be updated depending on the deployment cycle
            existingDefinition.setAccessLevel(definition.getAccessLevel());
            existingDefinition.setProductEnvironmentIds(definition.getProductEnvironmentIds());
            existingDefinition.setConfigParamName(definition.getConfigParamName());
        }

        for (Functionality functionality : currentPage.getFunctionalities()) {
            PermissionDefinition functionalityDefinition = getBasePageDefinitions(definition, functionality.getCode(), functionality.getChoiceConstant());
            PermissionDefinition existingFunctionality = definitions
                    .stream()
                    .filter(item -> item.getPageCode().equals(functionalityDefinition.getPageCode()))
                    .findFirst()
                    .orElse(null);
            if (existingFunctionality == null) {
                definitions.add(functionalityDefinition);
            } else {
                existingFunctionality.setAccessLevel(functionalityDefinition.getAccessLevel());
                existingFunctionality.setProductEnvironmentIds(functionalityDefinition.getProductEnvironmentIds());
                existingFunctionality.setConfigParamName(functionalityDefinition.getConfigParamName());
            }
        }

        for (Page page : currentPage.getPages()) {
            getBasePermissionDefinitions(definitions, page, allSavedUUIDs);
        }
        return definitions;
    }

    private PermissionDefinition getBasePageDefinitions(Page page) {
        PermissionDefinition definition = new PermissionDefinition();
        definition.setPageCode(page.getCode());
        definition.setAccessLevel(getAccessLevelInteger(page.getAccessChoices()));
        if (isUniversalAdminPage(page)) {
            definition.setProductEnvironmentIds(Arrays.asList(SystemConfig.getUniversalAdminApplicationEnvironmentId()));
        } else if (isOptixPage(page)) {
            definition.setProductEnvironmentIds(Arrays.asList(SystemConfig.getOptixApplicationEnvironmentId()));
        } else if (isNavigatorPage(page)) {
            definition.setProductEnvironmentIds(Arrays.asList(SystemConfig.getNavigatorApplicationEnvironmentId()));
        } else if (SystemConfig.isSpecialEventsProductEnabled() && isSpecialEventsPage(page)) {
            definition.setProductEnvironmentIds(Arrays.asList(SystemConfig.getSpecialEventsApplicationEnvironmentId()));
        } else {
            definition.setProductEnvironmentIds(Arrays.asList(SystemConfig.getG3ApplicationEnvironmentIds().split(",")));
        }
        if (definition.getPageCode().equals("FUNCTION_SPACE")) {
            definition.setConfigParamName("pacman.feature.FunctionSpaceEnabled");
        } else if (definition.getPageCode().equals("decision-configuration-new")) {
            definition.setConfigParamName("pacman.PreProduction.enableNewDecisionConfigPage");
        } else {
            definition.setConfigParamName(authorizationService.getConfigParamForFeature(page.getCode()));
        }
        HashMap<String, Object> additionalProperties = new HashMap<>();
        additionalProperties.put("applicationId", "g3");
        definition.setAdditionalProperties(additionalProperties);
        return definition;
    }

    private PermissionDefinition getBasePageDefinitions(PermissionDefinition parentPerm, String code, int choiceConstant) {
        PermissionDefinition definition = new PermissionDefinition();
        definition.setPageCode(code);
        definition.setFunction(true);
        definition.setAccessLevel(choiceConstant);
        definition.setProductEnvironmentIds(parentPerm.getProductEnvironmentIds());
        definition.setConfigParamName(authorizationService.getConfigParamForFeature(code));
        HashMap<String, Object> additionalProperties = new HashMap<>();
        additionalProperties.put("applicationId", "g3");
        definition.setAdditionalProperties(additionalProperties);
        return definition;
    }

    private Integer getAccessLevelInteger(List<String> accessLevel) {
        int accessLevelNumber = 0;
        accessLevelNumber += accessLevel.contains(ActionKey.readWrite.name()) ? 4 : 0;
        accessLevelNumber += accessLevel.contains(ActionKey.readOnly.name()) ? 2 : 0;
        accessLevelNumber += accessLevel.contains(ActionKey.noAccess.name()) ? 1 : 0;
        return accessLevelNumber;
    }

    private void addParentAndChildrenAndIterate(Map<String, PermissionDefinition> allPermissions,
                                                Page currentPage, Page parentPage) {
        PermissionDefinition currentPermission = allPermissions.get(currentPage.getCode());
        if (currentPermission != null) {
            currentPermission.setAdditionalProperties(currentPage.createAdditionalProperties());
            if (parentPage != null) {
                PermissionDefinition parentPermission = allPermissions.get(parentPage.getCode());
                currentPermission.setParentId(parentPermission.getId());
            }

            for (Functionality functionality : currentPage.getFunctionalities()) {
                PermissionDefinition functionalityDef = allPermissions.get(functionality.getCode());
                functionalityDef.setAdditionalProperties(functionality.createAdditionalProperties());
                functionalityDef.setParentId(currentPermission.getId());
            }

            List<Page> childrenPages = currentPage.getPages();
            for (Page childPage : childrenPages) {
                addParentAndChildrenAndIterate(allPermissions, childPage, currentPage);
            }
        }
    }

    private boolean isUniversalAdminPage(Page page) {
        return page.getMenuSubCategory() != null && page.getMenuSubCategory().equals(PageSubCategory.CONFIGURE_PERMISSIONS)
                || page.getCode().equals(TetrisPermissionKey.PROPERTY_ATTRIBUTES)
                || page.getCode().equals(TetrisPermissionKey.PROPERTY_ATTRIBUTE_ASSIGNMENTS)
                || page.getCode().equals(TetrisPermissionKey.SYSTEM_ANNOUNCEMENTS);
    }

    private boolean isOptixPage(Page page) {
        return page.isExternalToG3() && page.getExternalIdentifier() != null && page.getExternalIdentifier().equalsIgnoreCase("Optix");
    }

    private boolean isNavigatorPage(Page page) {
        return page.isExternalToG3() && page.getCode().contains("portfolio-navigator");
    }

    private boolean isSpecialEventsPage(Page page) {
        return page.getCode().equals(TetrisPermissionKey.SPECIAL_EVENTS_MANAGEMENT)
                || page.getCode().equals(TetrisPermissionKey.SPECIAL_EVENT_UPLOAD);
    }
}
