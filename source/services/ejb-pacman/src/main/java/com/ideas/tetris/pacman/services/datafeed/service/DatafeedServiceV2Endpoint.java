package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.datafeed.dto.GroupBlockDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.WebrateDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.GroupMasterDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.D360CompCapacityDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.D360BookingSummaryDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.ReservationNightDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.WebrateDTO;

import java.util.stream.Stream;

public enum DatafeedServiceV2Endpoint {

    OPTIX_WEB_RATE(WebrateDTO.class) {
        @Override
        protected Stream<WebrateDTO> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getWebrateStreaming(datafeedRequest);
        }
    },

    OPTIX_GROUP_BLOCK(GroupBlockDTO.class){
        @Override
        protected Stream<GroupBlockDTO> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupBlockStreaming(datafeedRequest);

        }

    },
    OPTIX_RESERVATION_NIGHT(ReservationNightDTO.class) {
        @Override
        protected Stream<ReservationNightDTO> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getReservationNightStreaming(datafeedRequest);
        }
    }, OPTIX_GROUP_MASTER(GroupMasterDTO.class) {
        @Override
        protected Stream<GroupMasterDTO> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getGroupMasterStreaming(datafeedRequest);
        }
    },
    OPTIX_BOOKING_SUMMARY_DEMAND360(D360BookingSummaryDTO.class) {
        @Override
        protected Stream<D360BookingSummaryDTO> getData(DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getBookingSummaryDemand360Streaming(datafeedRequest);
        }
    },

    OPTIX_HOTEL_CAPACITY_DEMAND360(D360CompCapacityDTO.class) {
        @Override
        protected Stream<D360CompCapacityDTO> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest) {
            return datafeedService.getHotelCapacityDemand360Streaming(datafeedRequest);
        }
    };

    DatafeedServiceV2Endpoint(Class<?> entityClass) {
        this.entityClass = entityClass;
    }

    protected abstract <T> Stream<T> getData(final DatafeedService datafeedService, DatafeedRequest datafeedRequest);

    private final Class<?> entityClass;

    public Class<?> getEntityClass() {
        return entityClass;
    }

    public static DatafeedServiceV2Endpoint valueOfSimpleClassName(String simpleClassName) {
        for (DatafeedServiceV2Endpoint datafeedServiceEndpoint : values()) {
            if (simpleClassName.equals(datafeedServiceEndpoint.getEntityClass().getSimpleName())) {
                return datafeedServiceEndpoint;
            }
        }
        return null;
    }
}
