package com.ideas.tetris.pacman.services.reports.operations;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.reports.operations.dto.OperationsReportDTO;
import com.ideas.tetris.pacman.services.reports.operations.vo.OperationsReportSummaryVO;
import com.ideas.tetris.pacman.services.reports.operations.vo.OperationsReportVO;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;

@Component
@Transactional
public class OperationsReportRestService {
    @Autowired
    OperationsReportService operationsReportService;

    @Autowired
    private DateService dateService;
    public static final int NUM_DAYS_TO_GENERATE = 20;

    public OperationsReportSummaryVO generateOperationsReport() {
        LocalDate caughtUpDate = DateUtil.convertJavaUtilDateToLocalDate(dateService.getCaughtUpDate(PacmanWorkContextHelper.getPropertyId()));
        LocalDate endDate = caughtUpDate.plusDays(NUM_DAYS_TO_GENERATE);
        return generateOperationsReport(caughtUpDate, endDate);
    }

    private OperationsReportSummaryVO generateOperationsReport(LocalDate caughtUpDate, LocalDate endDate) {
        List<OperationsReportDTO> operationsReportDTOS = operationsReportService.generateReport(caughtUpDate, endDate);
        return populateOperationsReportVO(operationsReportDTOS);
    }

    private OperationsReportSummaryVO populateOperationsReportVO(List<OperationsReportDTO> operationsReportDTOS) {
        OperationsReportSummaryVO operationsReportSummaryVO = new OperationsReportSummaryVO();
        populateSpecialEvents(operationsReportDTOS, operationsReportSummaryVO);
        populateForecastData(operationsReportDTOS, operationsReportSummaryVO);
        populateOnBooksData(operationsReportDTOS, operationsReportSummaryVO);
        populateOperationalForecastData(operationsReportDTOS, operationsReportSummaryVO);
        if (operationsReportDTOS.isEmpty()) {
            operationsReportSummaryVO.setMessage("NODATA");
        } else {
            operationsReportSummaryVO.setMessage("SUCCESS");
        }
        return operationsReportSummaryVO;
    }

    private void populateOperationalForecastData(List<OperationsReportDTO> operationsReportDTOS, OperationsReportSummaryVO operationsReportSummaryVO) {
        SortedSet<OperationsReportVO> operationalForecastDataList = new TreeSet<>();
        operationsReportDTOS.forEach(vo -> {
            OperationsReportVO operationsReportVO = new OperationsReportVO();
            operationsReportVO.setOccupancyDate(DateUtil.formatDate(vo.getOccupancyDate(), DateUtil.DEFAULT_DATE_FORMAT));
            operationsReportVO.setDepartureForecast(vo.getForecastDepartures());
            operationsReportVO.setArrivalForecast(vo.getForecastArrivals());
            operationalForecastDataList.add(operationsReportVO);
        });
        operationsReportSummaryVO.setOperationalForecastData(operationalForecastDataList);
    }

    private void populateOnBooksData(List<OperationsReportDTO> operationsReportDTOS, OperationsReportSummaryVO operationsReportSummaryVO) {
        SortedSet<OperationsReportVO> onBooksDataList = new TreeSet<>();
        operationsReportDTOS.forEach(vo -> {
            OperationsReportVO operationsReportVO = new OperationsReportVO();
            operationsReportVO.setOccupancyDate(DateUtil.formatDate(vo.getOccupancyDate(), DateUtil.DEFAULT_DATE_FORMAT));
            operationsReportVO.setOoo(vo.getOutOfOrder());
            operationsReportVO.setRoomDepartures(vo.getOnBooksDepartures());
            operationsReportVO.setRoomsSold(vo.getOnBooksOccupancy());
            operationsReportVO.setRoomCapacity(vo.getCapacity());
            operationsReportVO.setRoomArrivals(vo.getOnBooksArrivals());
            operationsReportVO.setOnBooksStayThrus(vo.getOnBooksStayThrus());
            onBooksDataList.add(operationsReportVO);
        });
        operationsReportSummaryVO.setPMSOnBooksData(onBooksDataList);
    }

    private void populateForecastData(List<OperationsReportDTO> operationsReportDTOS, OperationsReportSummaryVO operationsReportSummaryVO) {
        SortedSet<OperationsReportVO> forecastDataList = new TreeSet<>();
        operationsReportDTOS.forEach(vo -> {
            OperationsReportVO operationsReportVO = new OperationsReportVO();
            operationsReportVO.setOccupancyDate(DateUtil.formatDate(vo.getOccupancyDate(), DateUtil.DEFAULT_DATE_FORMAT));
            operationsReportVO.setOccupancyforecast(vo.getForecastOccupancy());
            operationsReportVO.setForecastStayThrus(vo.getForecastStayThrus());
            forecastDataList.add(operationsReportVO);
        });
        operationsReportSummaryVO.setForecastData(forecastDataList);
    }

    private void populateSpecialEvents(List<OperationsReportDTO> operationsReportDTOS, OperationsReportSummaryVO operationsReportSummaryVO) {
        Set<OperationsReportVO> specialEventsList = new TreeSet<>();
        operationsReportDTOS.forEach(vo -> {
            OperationsReportVO operationsReportVO = new OperationsReportVO();
            operationsReportVO.setShortName(vo.getSpecialEvent());
            operationsReportVO.setOccupancyDate(DateUtil.formatDate(vo.getOccupancyDate(), DateUtil.DEFAULT_DATE_FORMAT));
            specialEventsList.add(operationsReportVO);
        });
        operationsReportSummaryVO.setSpecialEventsData(specialEventsList);
    }
}
