package com.ideas.tetris.pacman.services.budget.converter;


import com.ideas.tetris.pacman.services.budget.entity.BudgetData;
import com.ideas.tetris.pacman.services.budget.model.BudgetUserForecastEntityModel;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Map;

public class BudgetDataEntityConverter {
    private Map<String, Integer> businessGroupNameToIdMap;
    private Map<String, BudgetData> currentEntities;
    private static final String DELIMETER = "_";

    public BudgetDataEntityConverter(Map<String, BudgetData> currentEntities, Map<String, Integer> businessGroupNameToIdMap) {
        this.currentEntities = currentEntities;
        this.businessGroupNameToIdMap = businessGroupNameToIdMap;
    }

    public BudgetData convert(BudgetUserForecastEntityModel budgetUserForecastEntityModel) {

        BudgetData entity = getCurrentEntity(budgetUserForecastEntityModel);
        if (entity == null) {
            entity = createNewEntity(budgetUserForecastEntityModel);
        }

        return entity;
    }

    private BudgetData createNewEntity(BudgetUserForecastEntityModel budgetUserForecastEntityModel) {
        BudgetData budgetData = new BudgetData();
        budgetData.setSegmentID(businessGroupNameToIdMap.get(budgetUserForecastEntityModel.getSegmentName().toLowerCase()));
        budgetData.setOccupancyDate(budgetUserForecastEntityModel.getOccupancyDate());
        budgetData.setRoomsSold(budgetUserForecastEntityModel.getRooms());
        budgetData.setRoomRevenue(budgetUserForecastEntityModel.getRevenue().setScale(5, BigDecimal.ROUND_DOWN));
        return budgetData;
    }

    private BudgetData getCurrentEntity(BudgetUserForecastEntityModel budgetUserForecastEntityModel) {
        BudgetData currentEntity = currentEntities.get(getKey(budgetUserForecastEntityModel));
        if (currentEntity != null) {
            currentEntity.setRoomRevenue(budgetUserForecastEntityModel.getRevenue().setScale(5, BigDecimal.ROUND_DOWN));
            currentEntity.setRoomsSold(budgetUserForecastEntityModel.getRooms());
        }
        return currentEntity;
    }

    private String getKey(BudgetUserForecastEntityModel dataModel) {
        return dataModel.getOccupancyDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + DELIMETER + getBusinessGroupId(dataModel.getSegmentName());
    }

    private Integer getBusinessGroupId(String businessGroupName) {
        return businessGroupNameToIdMap.get(businessGroupName.toLowerCase());
    }
}
