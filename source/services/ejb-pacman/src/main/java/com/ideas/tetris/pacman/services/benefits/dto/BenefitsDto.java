package com.ideas.tetris.pacman.services.benefits.dto;

import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.daoandentities.entity.Benefits;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DATE_FORMAT_MONTH_YEAR;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DEFAULT_DATE_FORMAT;
import static java.math.BigDecimal.ZERO;
import static java.math.BigDecimal.valueOf;
import static java.math.RoundingMode.HALF_UP;
import static java.util.Objects.isNull;

public class BenefitsDto {
    private String propertyCode;
    private Integer propertyId;
    private Integer month;
    private Integer year;

    private BigDecimal heuristicOccupancy = ZERO;
    private BigDecimal heuristicRevenue = ZERO;
    private BigDecimal heuristicAdr = ZERO;
    private BigDecimal heuristicRevpar = ZERO;

    private BigDecimal actualOccupancy = ZERO;
    private BigDecimal actualRevenue = ZERO;
    private BigDecimal actualAdr = ZERO;
    private BigDecimal actualRevpar = ZERO;

    private BigDecimal benefitsRevenue = ZERO;
    private BigDecimal benefitsOccupancy = ZERO;
    private BigDecimal benefitsADR = ZERO;
    private BigDecimal benefitsRevpar = ZERO;
    private Integer capacity = 0;
    private Integer category;

    private BigDecimal ancillaryRevenue = ZERO;
    private BigDecimal ancillaryRevenueGain = ZERO;
    private BigDecimal ancillaryProfit = ZERO;
    private BigDecimal ancillaryProfitGain = ZERO;

    private BigDecimal ancillaryRevenueWithoutRms = ZERO;
    private BigDecimal ancillaryRevenueGainInPercent = ZERO;
    private BigDecimal ancillaryProfitWithoutRms = ZERO;
    private BigDecimal ancillaryProfitGainInPercentage = ZERO;

    private BigDecimal heuristicProfit = ZERO;
    private BigDecimal actualProfit = ZERO;
    private BigDecimal heuristicProPOR = ZERO;
    private BigDecimal actualProPOR = ZERO;
    private BigDecimal heuristicProPAR = ZERO;
    private BigDecimal actualProPAR = ZERO;
    private BigDecimal benefitProfitInPercent = ZERO;
    private BigDecimal benefitProPORInPercent = ZERO;
    private BigDecimal benefitProPARInPercent = ZERO;

    private boolean isRevenueNonNegative = true;

    private boolean isDataAvailable = true;

    public enum Category {
        GROUP(1),
        PROPERTY(2),
        MONTH(3);

        private final int id;

        public int getId() {
            return id;
        }

        Category(int id) {
            this.id = id;
        }
    }

    private List<BenefitsDto> children = new ArrayList<>();

    public boolean isRevenueNonNegative() {
        return isRevenueNonNegative;
    }

    public void setRevenueNonNegative(boolean revenueNonNegative) {
        isRevenueNonNegative = revenueNonNegative;
    }

    public String getPropertyCode() {
        return propertyCode;
    }

    public void setPropertyCode(String propertyCode) {
        this.propertyCode = propertyCode;
    }

    public Integer getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Integer propertyId) {
        this.propertyId = propertyId;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public BigDecimal getBenefitsRevenue() {
        return benefitsRevenue;
    }

    public void setBenefitsRevenue(BigDecimal benefitsRevenue) {
        this.benefitsRevenue = benefitsRevenue;
    }

    public BigDecimal getBenefitsOccupancy() {
        return benefitsOccupancy;
    }

    public void setBenefitsOccupancy(BigDecimal benefitsOccupancy) {
        this.benefitsOccupancy = benefitsOccupancy;
    }

    public List<BenefitsDto> getChildren() {
        return children;
    }

    public BigDecimal getHeuristicOccupancy() {
        return heuristicOccupancy;
    }

    public void setHeuristicOccupancy(BigDecimal heuristicOccupancy) {
        this.heuristicOccupancy = heuristicOccupancy;
    }

    public BigDecimal getHeuristicRevenue() {
        return heuristicRevenue;
    }

    public void setHeuristicRevenue(BigDecimal heuristicRevenue) {
        this.heuristicRevenue = heuristicRevenue;
    }

    public BigDecimal getHeuristicAdr() {
        return heuristicAdr;
    }

    public void setHeuristicAdr(BigDecimal heuristicAdr) {
        this.heuristicAdr = heuristicAdr;
    }

    public BigDecimal getHeuristicRevpar() {
        return heuristicRevpar;
    }

    public void setHeuristicRevpar(BigDecimal heuristicRevpar) {
        this.heuristicRevpar = heuristicRevpar;
    }

    public BigDecimal getActualOccupancy() {
        return actualOccupancy;
    }

    public void setActualOccupancy(BigDecimal actualOccupancy) {
        this.actualOccupancy = actualOccupancy;
    }

    public BigDecimal getActualRevenue() {
        return actualRevenue;
    }

    public void setActualRevenue(BigDecimal actualRevenue) {
        this.actualRevenue = actualRevenue;
    }

    public BigDecimal getActualAdr() {
        return actualAdr;
    }

    public void setActualAdr(BigDecimal actualAdr) {
        this.actualAdr = actualAdr;
    }

    public BigDecimal getActualRevpar() {
        return actualRevpar;
    }

    public void setActualRevpar(BigDecimal actualRevpar) {
        this.actualRevpar = actualRevpar;
    }

    public BigDecimal getBenefitsADR() {
        return benefitsADR;
    }

    public void setBenefitsADR(BigDecimal benefitsADR) {
        this.benefitsADR = benefitsADR;
    }

    public BigDecimal getBenefitsRevpar() {
        return benefitsRevpar;
    }

    public void setBenefitsRevpar(BigDecimal benefitsRevpar) {
        this.benefitsRevpar = benefitsRevpar;
    }

    public void setChildren(List<BenefitsDto> children) {
        this.children = children;
    }

    public Integer getCapacity() {
        return capacity;
    }

    public void setCapacity(Integer capacity) {
        this.capacity = capacity;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public BigDecimal getAncillaryRevenue() {
        return ancillaryRevenue;
    }

    public void setAncillaryRevenue(BigDecimal ancillaryRevenue) {
        this.ancillaryRevenue = ancillaryRevenue;
    }

    public BigDecimal getAncillaryRevenueGain() {
        return ancillaryRevenueGain;
    }

    public void setAncillaryRevenueGain(BigDecimal ancillaryRevenueGain) {
        this.ancillaryRevenueGain = ancillaryRevenueGain;
    }

    public BigDecimal getAncillaryProfit() {
        return ancillaryProfit;
    }

    public void setAncillaryProfit(BigDecimal ancillaryProfit) {
        this.ancillaryProfit = ancillaryProfit;
    }

    public BigDecimal getAncillaryProfitGain() {
        return ancillaryProfitGain;
    }

    public void setAncillaryProfitGain(BigDecimal ancillaryProfitGain) {
        this.ancillaryProfitGain = ancillaryProfitGain;
    }

    public BigDecimal getAncillaryRevenueWithoutRms() {
        return ancillaryRevenueWithoutRms;
    }

    public void setAncillaryRevenueWithoutRms(BigDecimal ancillaryRevenueWithoutRms) {
        this.ancillaryRevenueWithoutRms = ancillaryRevenueWithoutRms;
    }

    public BigDecimal getAncillaryRevenueGainInPercent() {
        return ancillaryRevenueGainInPercent;
    }

    public void setAncillaryRevenueGainInPercent(BigDecimal ancillaryRevenueGainInPercent) {
        this.ancillaryRevenueGainInPercent = ancillaryRevenueGainInPercent;
    }

    public BigDecimal getAncillaryProfitWithoutRms() {
        return ancillaryProfitWithoutRms;
    }

    public void setAncillaryProfitWithoutRms(BigDecimal ancillaryProfitWithoutRms) {
        this.ancillaryProfitWithoutRms = ancillaryProfitWithoutRms;
    }

    public BigDecimal getAncillaryProfitGainInPercentage() {
        return ancillaryProfitGainInPercentage;
    }

    public void setAncillaryProfitGainInPercentage(BigDecimal ancillaryProfitGainInPercentage) {
        this.ancillaryProfitGainInPercentage = ancillaryProfitGainInPercentage;
    }

    public BigDecimal getHeuristicProfit() {
        return heuristicProfit;
    }

    public void setHeuristicProfit(BigDecimal heuristicProfit) {
        this.heuristicProfit = heuristicProfit;
    }

    public BigDecimal getActualProfit() {
        return actualProfit;
    }

    public void setActualProfit(BigDecimal actualProfit) {
        this.actualProfit = actualProfit;
    }

    public BigDecimal getHeuristicProPOR() {
        return heuristicProPOR;
    }

    public void setHeuristicProPOR(BigDecimal heuristicProPOR) {
        this.heuristicProPOR = heuristicProPOR;
    }

    public BigDecimal getActualProPOR() {
        return actualProPOR;
    }

    public void setActualProPOR(BigDecimal actualProPOR) {
        this.actualProPOR = actualProPOR;
    }

    public BigDecimal getHeuristicProPAR() {
        return heuristicProPAR;
    }

    public void setHeuristicProPAR(BigDecimal heuristicProPAR) {
        this.heuristicProPAR = heuristicProPAR;
    }

    public BigDecimal getActualProPAR() {
        return actualProPAR;
    }

    public void setActualProPAR(BigDecimal actualProPAR) {
        this.actualProPAR = actualProPAR;
    }

    public BigDecimal getBenefitProfitInPercent() {
        return benefitProfitInPercent;
    }

    public void setBenefitProfitInPercent(BigDecimal benefitProfitInPercent) {
        this.benefitProfitInPercent = benefitProfitInPercent;
    }

    public BigDecimal getBenefitProPORInPercent() {
        return benefitProPORInPercent;
    }

    public void setBenefitProPORInPercent(BigDecimal benefitProPORInPercent) {
        this.benefitProPORInPercent = benefitProPORInPercent;
    }

    public BigDecimal getBenefitProPARInPercent() {
        return benefitProPARInPercent;
    }

    public void setBenefitProPARInPercent(BigDecimal benefitProPARInPercent) {
        this.benefitProPARInPercent = benefitProPARInPercent;
    }

    public boolean isDataAvailable() {
        return isDataAvailable;
    }

    public void setDataAvailable(boolean dataAvailable) {
        isDataAvailable = dataAvailable;
    }

    public BenefitsDto() {
    }

    public void addChild(BenefitsDto child, boolean shouldAddToSummary) {
        this.children.add(child);
        if (!shouldAddToSummary) {
            child.isRevenueNonNegative = false;
            return;
        }
        this.capacity = this.capacity + child.capacity;
        this.actualOccupancy = this.actualOccupancy.add(child.actualOccupancy);
        this.actualRevenue = this.actualRevenue.add(child.actualRevenue);
        this.actualAdr = BigDecimalUtil.divide(this.actualRevenue, this.actualOccupancy);
        this.actualRevpar = capacity == 0 ? ZERO : BigDecimalUtil.divide(this.actualRevenue, valueOf(capacity));
        this.heuristicOccupancy = this.heuristicOccupancy.add(child.heuristicOccupancy);
        this.heuristicRevenue = this.heuristicRevenue.add(child.heuristicRevenue);
        this.ancillaryRevenue = this.ancillaryRevenue.add(child.ancillaryRevenue);
        this.ancillaryProfit = this.ancillaryProfit.add(child.ancillaryProfit);
        this.ancillaryRevenueGain = this.ancillaryRevenueGain.add(child.ancillaryRevenueGain);
        this.ancillaryProfitGain = this.ancillaryProfitGain.add(child.ancillaryProfitGain);
        this.ancillaryRevenueWithoutRms = this.ancillaryRevenueWithoutRms.add(child.ancillaryRevenueWithoutRms);
        this.ancillaryProfitWithoutRms = this.ancillaryProfitWithoutRms.add(child.ancillaryProfitWithoutRms);
        this.ancillaryRevenueGainInPercent = calculateBenefitsPercentage(this.ancillaryRevenue, this.ancillaryRevenueWithoutRms);
        this.ancillaryProfitGainInPercentage = calculateBenefitsPercentage(this.ancillaryProfit, this.ancillaryProfitWithoutRms);
        this.heuristicAdr = BigDecimalUtil.divide(this.heuristicRevenue, this.heuristicOccupancy);
        this.heuristicRevpar = capacity == 0 ? ZERO : BigDecimalUtil.divide(this.heuristicRevenue, valueOf(capacity));
        this.benefitsRevenue = calculateBenefitsPercentage(this.actualRevenue, this.heuristicRevenue);
        this.benefitsOccupancy = calculateBenefitsPercentage(this.actualOccupancy, this.heuristicOccupancy);
        this.benefitsADR = calculateBenefitsPercentage(this.actualAdr, this.heuristicAdr);
        this.benefitsRevpar = calculateBenefitsPercentage(this.actualRevpar, this.heuristicRevpar);
        this.heuristicProfit = this.heuristicProfit.add(child.heuristicProfit);
        this.actualProfit = this.actualProfit.add(child.actualProfit);
        this.benefitProfitInPercent = calculateBenefitsPercentage(actualProfit, heuristicProfit);
        this.heuristicProPOR = getDivisionResult(this.heuristicProfit, this.heuristicOccupancy);
        this.actualProPOR = getDivisionResult(this.actualProfit, this.actualOccupancy);
        this.benefitProPORInPercent = calculateBenefitsPercentage(this.actualProPOR, this.heuristicProPOR);
        this.heuristicProPAR = capacity == 0 ? ZERO : getDivisionResult(this.heuristicProfit, valueOf(capacity));
        this.actualProPAR = capacity == 0 ? ZERO : getDivisionResult(this.actualProfit, valueOf(capacity));
        this.benefitProPARInPercent = calculateBenefitsPercentage(this.actualProPAR, this.heuristicProPAR);
    }

    protected static BigDecimal calculateBenefitsPercentage(BigDecimal actual, BigDecimal heuristic) {
        return getDivisionResult(actual.subtract(heuristic).multiply(valueOf(100)), heuristic);
    }

    private static BigDecimal getDivisionResult(BigDecimal dividend, BigDecimal divisor) {
        return isNull(divisor) || divisor.compareTo(BigDecimal.ZERO) == 0 ? ZERO : dividend.divide(divisor, 2, HALF_UP);
    }

    public BenefitsDto(Benefits entity) {
        this.setPropertyId(entity.getPropertyId());
        this.setMonth(entity.getMonth());
        this.setYear(entity.getYear());
        this.setCategory(Category.MONTH.getId());

        this.setHeuristicOccupancy(new BigDecimal(entity.getHeuristicOccupancy()));
        this.setHeuristicRevenue(entity.getHeuristicRevenue());
        this.setHeuristicAdr(entity.getHeuristicAdr());
        this.setHeuristicRevpar(entity.getHeuristicRevpar());

        this.setActualOccupancy(new BigDecimal(entity.getActualOccupancy()));
        this.setActualRevenue(entity.getActualRevenue());
        this.setActualAdr(entity.getActualAdr());
        this.setActualRevpar(entity.getActualRevpar());

        this.setBenefitsRevenue(entity.getBenefitRevenue());
        this.setBenefitsOccupancy(entity.getBenefitOccupancy());
        this.setBenefitsADR(entity.getBenefitADR());
        this.setBenefitsRevpar(entity.getBenefitRevpar());
        this.setCapacity(entity.getCapacity());

        this.setAncillaryRevenue(entity.getAncillaryRevenue());
        this.setAncillaryRevenueGain(entity.getAncillaryRevenueGain());
        this.setAncillaryProfit(entity.getAncillaryProfit());
        this.setAncillaryProfitGain(entity.getAncillaryProfitGain());

        this.setAncillaryRevenueWithoutRms(entity.getAncillaryRevenueWithoutRms());
        this.setAncillaryRevenueGainInPercent(entity.getAncillaryRevenueGainInPercent());
        this.setAncillaryProfitWithoutRms(entity.getAncillaryProfitWithoutRms());
        this.setAncillaryProfitGainInPercentage(entity.getAncillaryProfitGainInPercentage());

        this.setActualProfit(entity.getActualProfit());
        this.setHeuristicProfit(entity.getHeuristicProfit());
        this.setBenefitProfitInPercent(entity.getBenefitProfitInPercent());

        this.setActualProPOR(entity.getActualProPOR());
        this.setHeuristicProPOR(entity.getHeuristicProPOR());
        this.setBenefitProPORInPercent(entity.getBenefitProPORInPercent());

        this.setActualProPAR(entity.getActualProPAR());
        this.setHeuristicProPAR(entity.getHeuristicProPAR());
        this.setBenefitProPARInPercent(entity.getBenefitProPARInPercent());
    }

    public boolean hasChildren() {
        return CollectionUtils.isNotEmpty(children);
    }

    public String toMonthString() {
        String date = String.format("%04d", year) + "-" + String.format("%02d", month) + "-01";
        try {
            Date dt = DateUtil.parseDate(date, DEFAULT_DATE_FORMAT);
            return DateUtil.formatDate(dt, DATE_FORMAT_MONTH_YEAR);
        } catch (ParseException e) {
            throw new TetrisException(e.getMessage());
        }

    }

}
