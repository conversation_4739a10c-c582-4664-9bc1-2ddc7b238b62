package com.ideas.tetris.pacman.services.budget.currency;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.services.budget.dto.CurrencyExchangeDTO;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class OperaCurrencyExchangeService extends AbstractCurrencyExchangeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OperaCurrencyExchangeService.class);
    public static final String GET_OPERA_STAGE_YIELD_CURRENCY_FOR_DATE_RANGE = "select Base_Currency_Code,Currency_Code,Begin_DT,Exchange_Rate from opera.Stage_Yield_Currency where " +
            " Begin_DT >= cast(:startDate as datetime) and Begin_DT <=  cast(:endDate as datetime) and Base_Currency_Code=:baseCurrencyCode and Currency_Code=:currencyCode";
    public static final String GET_TOP_OPERA_STAGE_YIELD_CURRENCY_FOR_PREVIOUS_DATE = "select top 1 Base_Currency_Code,Currency_Code,Begin_DT,Exchange_Rate from opera.Stage_Yield_Currency " +
            "where Begin_DT < cast(:startDate as datetime) and Base_Currency_Code=:baseCurrencyCode and Currency_Code=:currencyCode order by Begin_DT desc";

    @Override
    protected List<CurrencyExchangeDTO> getCurrencyExchangeRateForDateRange(String clientCode, String propertyCode, String revPlanCurrencyCode, String rmsCurrencyCode, LocalDate startDate, LocalDate endDate) {
        LOGGER.info("RevPlan currency conversion: {} -> {} for OPERA external system", revPlanCurrencyCode, rmsCurrencyCode);

        return getCurrencyExchangeRatesForOpera(clientCode, propertyCode, revPlanCurrencyCode, rmsCurrencyCode, startDate, endDate);
    }

    private List<CurrencyExchangeDTO> getQueryResultFromStageYieldCurrency(String revPlanCurrencyCode, String rmsCurrencyCode, LocalDate startDate, LocalDate endDate) {
        return tenantCrudService.findByNativeQuery(GET_OPERA_STAGE_YIELD_CURRENCY_FOR_DATE_RANGE,
                QueryParameter.with("baseCurrencyCode", revPlanCurrencyCode)
                        .and("currencyCode", rmsCurrencyCode)
                        .and("startDate", startDate)
                        .and("endDate", endDate).parameters(),
                this::mapRowForCurrencyExchange);
    }

    @VisibleForTesting
    protected CurrencyExchangeDTO mapRowForCurrencyExchange(Object[] row) {
        CurrencyExchangeDTO currencyExchangeDTO = new CurrencyExchangeDTO();
        currencyExchangeDTO.setFromCurrency((String) row[0]);
        currencyExchangeDTO.setToCurrency((String) row[1]);
        currencyExchangeDTO.setFromDate(DateUtil.convertJavaUtilDateToLocalDate((Date) row[2]));
        currencyExchangeDTO.setToDate(DateUtil.convertJavaUtilDateToLocalDate((Date) row[2]));
        currencyExchangeDTO.setExchangeRate((BigDecimal) row[3]);
        return currencyExchangeDTO;
    }

    private List<CurrencyExchangeDTO> getCurrencyExchangeRatesForOpera(String clientCode, String propertyCode, String revPlanCurrencyCode, String rmsCurrencyCode, LocalDate startDate, LocalDate endDate) {
        List<CurrencyExchangeDTO> stageYieldCurrencyDTOS = getQueryResultFromStageYieldCurrency(revPlanCurrencyCode, rmsCurrencyCode, startDate, endDate);
        if (CollectionUtils.isNotEmpty(stageYieldCurrencyDTOS) && stageYieldCurrencyDTOS.get(0).getFromDate().isAfter(startDate)) {
            final CurrencyExchangeDTO stageYieldCurrencyDTO = getSingleQueryResultFromStageYieldCurrency(revPlanCurrencyCode, rmsCurrencyCode, startDate);
            if (ObjectUtils.isEmpty(stageYieldCurrencyDTO)) {
                LOGGER.info("Fetching the currency conversion factor for startDate:{} from NGI when previous latest date rate not available in OPERA Stage_Yield_Currency ", startDate);
                final List<CurrencyExchangeDTO> currencyExchangeDTOSFromNGI = genericCurrencyExchangeService.getCurrencyExchangeRatesForDateRange(clientCode, propertyCode, revPlanCurrencyCode, rmsCurrencyCode, startDate, startDate);
                stageYieldCurrencyDTOS.add(0, currencyExchangeDTOSFromNGI.get(0));
            } else {
                LOGGER.info("Fetching the currency conversion factor of previous latest date from OPERA Stage_Yield_Currency for startDate:{} ", startDate);
                stageYieldCurrencyDTOS.add(0, stageYieldCurrencyDTO);
            }
        }
        if (CollectionUtils.isEmpty(stageYieldCurrencyDTOS)) {
            stageYieldCurrencyDTOS = new ArrayList<>();
            final CurrencyExchangeDTO previousDateStageYieldCurrencyDTO = getSingleQueryResultFromStageYieldCurrency(revPlanCurrencyCode, rmsCurrencyCode, startDate);
            if (ObjectUtils.isEmpty(previousDateStageYieldCurrencyDTO)) {
                LOGGER.info("Fetching the currency conversion factor for date range:{} to {} from NGI when rate data not available in OPERA Stage_Yield_Currency ", startDate, endDate);
                stageYieldCurrencyDTOS = genericCurrencyExchangeService.getCurrencyExchangeRatesForDateRange(clientCode, propertyCode, revPlanCurrencyCode, rmsCurrencyCode, startDate, endDate);
            } else {
                LOGGER.info("Fetching the currency conversion factor of previous latest date from OPERA Stage_Yield_Currency for date:{}", startDate);
                stageYieldCurrencyDTOS.add(previousDateStageYieldCurrencyDTO);
            }
        }
        return stageYieldCurrencyDTOS;
    }

    private CurrencyExchangeDTO getSingleQueryResultFromStageYieldCurrency(String revPlanCurrencyCode, String rmsCurrencyCode, LocalDate startDate) {
        return tenantCrudService.findByNativeQuerySingleResult(GET_TOP_OPERA_STAGE_YIELD_CURRENCY_FOR_PREVIOUS_DATE,
                QueryParameter.with("baseCurrencyCode", revPlanCurrencyCode)
                        .and("currencyCode", rmsCurrencyCode)
                        .and("startDate", startDate).parameters(),
                this::mapRowForCurrencyExchange);
    }

}
