package com.ideas.tetris.pacman.services.minlos;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.Date;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public abstract class AbstractMinlosRecommendationService implements MinlosRecommendationServiceLocal {

    private static Logger LOGGER = Logger.getLogger(AbstractMinlosRecommendationService.class);
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService crudService;

    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    @Autowired
	protected DecisionService decisionService;
    @Autowired
    DateService dateService;

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    public static final String INSERT_DECISON_MINLOS = " MERGE Decision_MINLOS AS target " + " USING (select Decision_Qualified_FPLOS_ID, Accom_Type_ID, "
            + " Rate_Qualified_ID, Arrival_DT, MINLOS " + " from  (SELECT Decision_Qualified_FPLOS_ID, " + " Arrival_DT, "
            + " (CASE CHARINDEX('Y', FPLOS) WHEN 0 THEN :qualifliedMaxLos ELSE CHARINDEX('Y', FPLOS) END) AS MINLOS, " + " Accom_Type_ID , " + " rq.Rate_Qualified_ID  "
            + " FROM Decision_Qualified_FPLOS dqf inner join rate_qualified rq on dqf.rate_qualified_id=rq.rate_qualified_id "
            + " WHERE Arrival_DT >= :decisionStartDate AND Arrival_DT <= :decisionEndDate and rq.status_id=1 " + " ) as minlos  " + " ) AS source  "
            + " ON target.Arrival_DT = source.Arrival_DT AND target.Rate_Qualified_ID = source.Rate_Qualified_ID"
            + " AND target.Accom_Type_ID = source.Accom_Type_ID " + " WHEN MATCHED  AND target.Minlos <> source.minlos " + "  THEN  " + " UPDATE SET  "
            + " Decision_ID =:decisionId, " + " MINLOS = source.MINLOS, " + " Decision_Qualified_FPLOS_ID = source.Decision_Qualified_FPLOS_ID, "
            + " CreateDate_DTTM = CURRENT_TIMESTAMP " + " WHEN NOT MATCHED THEN  "
            + " INSERT(Decision_ID,Arrival_DT,Accom_Type_ID, Rate_Qualified_ID, Decision_Qualified_FPLOS_ID, MINLOS, CreateDate_DTTM) " + " VALUES(  "
            + " :decisionId,source.Arrival_DT, " + " source.Accom_Type_ID,  source.Rate_Qualified_ID, "
            + " source.Decision_Qualified_FPLOS_ID, source.MINLOS, " + " CURRENT_TIMESTAMP) ;";

    public static final String INSERT_PACE_DECISON_MINLOS = " insert into PACE_MINLOS " + " (Decision_ID,Arrival_DT , Accom_Type_ID,Rate_Qualified_ID, "
            + " Decision_Qualified_FPLOS_ID, MINLOS) "
            + " select Decision_ID,Arrival_DT, Accom_Type_ID, Rate_Qualified_ID,Decision_Qualified_FPLOS_ID, MINLOS "
            + " from Decision_MINLOS where decision_id= :decisionId ;";

    private static final String DELETE_DECISION_MINLOS = new StringBuilder(" delete Decision_MINLOS from  ")
            .append(" Decision_Qualified_FPLOS as a right join Decision_MINLOS as b on\n")
            .append(" a.Decision_Qualified_FPLOS_ID = b.Decision_Qualified_FPLOS_ID\n")
            .append(" where a.FPLOS is NULL;").toString();

    @Override
    public void createMinlosDecisions() {
        Decision decisionRecord = decisionService.createMINLOSDecision();
        Integer qualifliedMaxLos = pacmanConfigParamsService.getIntegerParameterValue(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value());
        int numRowsDeleted = crudService.executeUpdateByNativeQuery(DELETE_DECISION_MINLOS);
        LOGGER.debug("The number of invalid decisions deleted from decision minlos table are " + numRowsDeleted);
        int decisionMinosInsertCount = crudService.executeUpdateByNativeQuery(
                INSERT_DECISON_MINLOS,
                QueryParameter.with("decisionStartDate", dateService.getOptimizationWindowStartDate())
                        .and("decisionEndDate", getOptimizationWindowEndDate()).and("decisionId", decisionRecord.getId())
                        .and("qualifliedMaxLos", qualifliedMaxLos + 1).parameters());

        LOGGER.info("Number of records inserted into Decision_MINLOS table are " + decisionMinosInsertCount);
        int paceDecisionMinlosCount = crudService.executeUpdateByNativeQuery(INSERT_PACE_DECISON_MINLOS, QueryParameter.with("decisionId", decisionRecord.getId())
                .parameters());
        LOGGER.info("Number of records inserted into PACE_MINLOS table are " + paceDecisionMinlosCount);
        decisionService.updateDescisionProcessStatus(decisionRecord.getId(), Constants.PROCESS_STATUS_SUCCESSFUL);
    }

    abstract public Date getOptimizationWindowEndDate();

}
