package com.ideas.tetris.pacman.services.vendor;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.ideas.tetris.platform.common.ngi.propertymapping.ClientCode;
import com.ideas.tetris.platform.common.ngi.propertymapping.PropertyCode;
import com.ideas.tetris.platform.common.ngi.propertymapping.PropertyInfoEmbedded;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@EqualsAndHashCode
@JsonIgnoreProperties(ignoreUnknown = true)
@PropertyInfoEmbedded
@SuppressWarnings("squid:S1948")
public class QualifiedSetting implements Serializable {
    private static final long serialVersionUID = 438297755948432320L;
    private String vendorId;
    @ClientCode
    private String clientCode;
    @PropertyCode
    private String propertyCode;
    private String attribute;
    @EqualsAndHashCode.Exclude
    private Object value;
}
