package com.ideas.tetris.pacman.services.dashboard.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Key;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.MultiPropertyAggregate;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Sum;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

@MultiPropertyAggregate
public class TotalActivityByOccupancyDateDto {

    private static final int CALCULATED_SCALE = 2;

    @Key
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    private Date occupancyDate;

    @Sum
    private BigDecimal roomsSold;

    @Sum
    private BigDecimal totalCapacity;

    @Sum
    private BigDecimal cancellations;

    @Sum
    private BigDecimal arrivals;

    @Sum
    private BigDecimal noShows;

    @Sum
    private BigDecimal roomsLeftToSell;

    @Sum
    private BigDecimal ooo;

    @Sum
    private BigDecimal departures;

    @Sum
    private BigDecimal availableCapacity;

    @Sum
    private BigDecimal revenue;

    @Sum
    private BigDecimal occupancyNumber;

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public BigDecimal getRoomsSold() {
        return roomsSold;
    }

    public void setRoomsSold(BigDecimal roomsSold) {
        this.roomsSold = roomsSold;
    }

    public BigDecimal getTotalCapacity() {
        return totalCapacity;
    }

    public void setTotalCapacity(BigDecimal totalCapacity) {
        this.totalCapacity = totalCapacity;
    }

    public BigDecimal getCancellations() {
        return cancellations;
    }

    public void setCancellations(BigDecimal cancellations) {
        this.cancellations = cancellations;
    }

    public BigDecimal getArrivals() {
        return arrivals;
    }

    public void setArrivals(BigDecimal arrivals) {
        this.arrivals = arrivals;
    }

    public BigDecimal getNoShows() {
        return noShows;
    }

    public void setNoShows(BigDecimal noShows) {
        this.noShows = noShows;
    }

    public BigDecimal getRoomsLeftToSell() {
        return roomsLeftToSell;
    }

    public void setRoomsLeftToSell(BigDecimal roomsLeftToSell) {
        this.roomsLeftToSell = roomsLeftToSell;
    }

    public BigDecimal getOoo() {
        return ooo;
    }

    public void setOoo(BigDecimal ooo) {
        this.ooo = ooo;
    }

    public BigDecimal getDepartures() {
        return departures;
    }

    public void setDepartures(BigDecimal departures) {
        this.departures = departures;
    }

    public BigDecimal getAvailableCapacity() {
        return availableCapacity;
    }

    public void setAvailableCapacity(BigDecimal availableCapacity) {
        this.availableCapacity = availableCapacity;
    }

    public BigDecimal getRevenue() {
        return revenue;
    }

    public void setRevenue(BigDecimal revenue) {
        this.revenue = revenue;
    }

    public BigDecimal getOccupancyNumber() {
        return occupancyNumber;
    }

    public void setOccupancyNumber(BigDecimal occupancyNumber) {
        this.occupancyNumber = occupancyNumber;
    }

    public BigDecimal getOccupancyPercent() {
        if (availableCapacity == null || occupancyNumber == null || occupancyNumber.doubleValue() == 0) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(occupancyNumber.doubleValue() * 100.0 / availableCapacity.doubleValue()).setScale(CALCULATED_SCALE, RoundingMode.HALF_UP);
    }

    public BigDecimal getADR() {
        if (revenue == null || occupancyNumber == null || occupancyNumber.doubleValue() == 0) {
            return BigDecimal.ZERO;
        }

        return revenue.divide(occupancyNumber, CALCULATED_SCALE, RoundingMode.HALF_UP);
    }

    public BigDecimal getREVPAR(boolean usePhysicalCapacity) {
        BigDecimal revPAR;
        boolean availableCapacityCheck = !usePhysicalCapacity && (revenue == null || availableCapacity == null || availableCapacity.compareTo(BigDecimal.ZERO) == 0);
        boolean physicalCapacityCheck = usePhysicalCapacity && (revenue == null || totalCapacity == null || totalCapacity.compareTo(BigDecimal.ZERO) == 0);
        if (availableCapacityCheck || physicalCapacityCheck) {
            revPAR = BigDecimal.ZERO;
        } else {
            BigDecimal capacityToUse = usePhysicalCapacity ? totalCapacity : availableCapacity;
            revPAR = revenue.divide(capacityToUse, CALCULATED_SCALE, RoundingMode.HALF_UP);
        }
        return revPAR;
    }
}