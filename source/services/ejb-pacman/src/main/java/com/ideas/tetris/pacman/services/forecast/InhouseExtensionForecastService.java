package com.ideas.tetris.pacman.services.forecast;

import com.ideas.tetris.pacman.services.forecast.dto.InhouseExtensionForecastDto;
import com.ideas.tetris.pacman.services.forecast.dto.InhouseLosChgSummaryDto;
import com.ideas.tetris.pacman.services.forecast.repository.InhouseExtensionForecastRepository;
import com.ideas.tetris.pacman.services.forecast.repository.InhouseLosChgSummaryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

@Component
public class InhouseExtensionForecastService {
    @Autowired
    private InhouseExtensionForecastRepository repository;

    @Autowired
    private InhouseLosChgSummaryRepository inhouseLosChgSummaryRepository;

    public List<InhouseExtensionForecastDto> retrieveInhouseExtensionForecasts(Integer propertyId, LocalDate startDate, LocalDate endDate) {
        return repository.retrieveInhouseExtensionForecasts(propertyId,startDate,endDate);
    }
    public List<InhouseLosChgSummaryDto> retrieveInhouseLosChgSummaries(Integer propertyId){
        return inhouseLosChgSummaryRepository.retrieveInhouseLosChgSummaries(propertyId);
    }
}
