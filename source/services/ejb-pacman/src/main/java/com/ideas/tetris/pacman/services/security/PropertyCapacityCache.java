package com.ideas.tetris.pacman.services.security;

import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.platform.common.cache.AbstractCache;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.event.job.JobEvent;
import com.ideas.tetris.platform.common.event.property.PropertyStatusChangedEvent;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.collections.CollectionUtils;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;


@Component
@Transactional
public class PropertyCapacityCache extends AbstractCache<Integer, BigDecimal> {


    @Autowired
    private AbstractMultiPropertyCrudService multiPropertyCrudService;

    protected static final BigDecimal NO_VALUE = BigDecimal.valueOf(Integer.MIN_VALUE);

    @Override
    protected BigDecimal loadKey(Integer propertyId) {
        // Load the capacity into the cache
        loadCapacitiesNotInCache(Arrays.asList(propertyId));

        // Get the propertyId's value from the cache
        return get(propertyId);
    }

    // Refresh the cache value as it could have changed during the property's bde processing
    public void observeBDECompletedJobEvent(JobEvent event) {
        // Reload the key's value
        reloadKey(event.getPropertyId());
    }

    // Refresh the cache value as it could have changed during the property's cdp processing
    public void observeCDPCompletedJobEvent(JobEvent event) {
        // Reload the key's value
        reloadKey(event.getPropertyId());
    }

    // Refresh the cache value as it could have changed during the property's bde processing
    public void observeRefreshLMSCompletedJobEvent(JobEvent event) {
        // Reload the key's value
        reloadKey(event.getPropertyId());
    }

    public void observeDeletedPropertyStatusEvent(PropertyStatusChangedEvent propertyStatusChangedEvent) {
        remove(propertyStatusChangedEvent.getPropertyId());
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Map<Integer, BigDecimal> getCapacitiesForProperties(List<Property> properties) {
        if (CollectionUtils.isEmpty(properties)) {
            return null;
        }

        // Get propertyIds from the List of properties
        List<Integer> propertyIds = getPropertyIds(properties);

        // Get a List of the Property IDs that were not in the cache
        List<Integer> propertyIdsNotInCache = getPropertyIdsNotInCache(propertyIds);

        // If there are any PropertyIDs not in cache, load their capacities into the cache
        if (!CollectionUtils.isEmpty(propertyIdsNotInCache)) {
            loadCapacitiesNotInCache(propertyIdsNotInCache);
        }

        // Build a Map of PropertyId and Capacity values
        Map<Integer, BigDecimal> propertyCapacities = new HashMap<Integer, BigDecimal>();
        for (Integer propertyId : propertyIds) {
            propertyCapacities.put(propertyId, get(propertyId));
        }
        return propertyCapacities;
    }

    private List<Integer> getPropertyIds(List<Property> properties) {
        List<Integer> propertyIds = new ArrayList<Integer>();
        for (Property property : properties) {
            propertyIds.add(property.getId());
        }
        return propertyIds;
    }

    private List<Integer> getPropertyIdsNotInCache(List<Integer> propertyIds) {
        List<Integer> propertyIdsNotInCache = new ArrayList<Integer>();

        for (Integer propertyId : propertyIds) {
            if (!containsKey(propertyId)) {
                propertyIdsNotInCache.add(propertyId);
            }
        }

        return propertyIdsNotInCache;
    }

    @SuppressWarnings("unchecked")
    private void loadCapacitiesNotInCache(List<Integer> propertyIdsNotInCache) {
        // Query the database using the multiPropertyCrudService so it's multi-threaded
        List<BigDecimal> capacities =  multiPropertyCrudService.findByNamedQuerySingleResult(
                propertyIdsNotInCache,
                TotalActivity.CAPACITY_BY_CAUGHTUP_DATE_AND_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyIdsNotInCache).parameters());


        // For each PropertyId, get the capacity and add it to the cache and map being returned.
        for (int i = 0; i < propertyIdsNotInCache.size(); i++) {
            Integer propertyId = propertyIdsNotInCache.get(i);

            // Get the capacity
            BigDecimal capacity = null;
            if (!capacities.isEmpty()) {
                capacity = capacities.get(i);
            }

            // If there was no capacity, set the NO_VALUE object
            if (capacity == null) {
                capacity = NO_VALUE;
            }
            LOGGER.debug("loadCapacitiesNotInCache() - propertyId: " + propertyId + ", capacity: " + capacity);

            // Put the value on the cache
            put(propertyId, capacity);
        }
    }

    @Override
    protected boolean isAsync() {
        return false;
    }

    @Override
    protected Optional<Integer> getRedisLifespan() {
        return Optional.of(14400000);
    }
}
