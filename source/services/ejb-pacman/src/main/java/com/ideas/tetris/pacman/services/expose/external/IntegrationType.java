package com.ideas.tetris.pacman.services.expose.external;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.platform.common.job.JobName;

import java.util.ArrayList;
import java.util.List;

public enum IntegrationType {

    COMPETITOR_RATES(BusinessModule.RESERVATION, IPConfigParamName.BAR_WEB_RATE_ALIAS.value(), JobName.NGICompetitorRates, JobName.ProcessWebRateExtract),
    FUNCTION_SPACE_CLIENT(BusinessModule.FUNCTION_SPACE, IPConfigParamName.SALES_AND_CATERING_FUNCTION_SPACE_CLIENT_ALIAS.value(), JobName.NGIFunctionSpaceClientFile, JobName.FunctionSpaceDataLoadJob),
    AHWS_SC(BusinessModule.FUNCTION_SPACE, IPConfigParamName.SALES_AND_CATERING_FUNCTION_SPACE_CLIENT_ALIAS.value(), JobName.NGIFunctionSpaceClientFile, JobName.FunctionSpaceDataLoadJob),
    OXI_SC(BusinessModule.FUNCTION_SPACE, IPConfigParamName.SALES_AND_CATERING_FUNCTION_SPACE_CLIENT_ALIAS.value(), JobName.NGIFunctionSpaceClientFile, JobName.FunctionSpaceDataLoadJob),
    OPERA_SC(BusinessModule.FUNCTION_SPACE, IPConfigParamName.SALES_AND_CATERING_FUNCTION_SPACE_CLIENT_ALIAS.value(), JobName.NGIFunctionSpaceClientFile, JobName.FunctionSpaceDataLoadJob),
    EVALUATION_RESULTS(BusinessModule.FUNCTION_SPACE, IPConfigParamName.SALES_AND_CATERING_FUNCTION_SPACE_CLIENT_ALIAS.value(), JobName.NGIFunctionSpaceClientFile, JobName.NGIGroupEvalResults),
    UNQUALIFIED_RATES(BusinessModule.RESERVATION, Constants.HTNG_PROPERTY_ALIAS, JobName.NGIHtngRates, JobName.UnqualifiedRatesLoadJob),
    QUALIFIED_RATES(BusinessModule.RESERVATION, Constants.HTNG_PROPERTY_ALIAS, JobName.NGIHtngRates, JobName.QualifiedRatesLoadJob),
    GROUP_DATA(BusinessModule.RESERVATION, Constants.HTNG_PROPERTY_ALIAS, JobName.NGIHtngInvBlock, JobName.NGIDeferredDeliveryJob),
    TRANSACTION_DATA(BusinessModule.RESERVATION, Constants.HTNG_PROPERTY_ALIAS, JobName.NGIHtngReservation, JobName.NGIDeferredDeliveryJob),
    ACTIVITY_DATA(BusinessModule.RESERVATION, Constants.HTNG_PROPERTY_ALIAS, JobName.NGIHtngActivity, JobName.NGIDeferredDeliveryJob),
    ACTIVITY_DATA_CDP(BusinessModule.RESERVATION, Constants.HTNG_PROPERTY_ALIAS, JobName.NGIHtngActivity, JobName.NGICdpDeferredDeliveryJob),
    STR_WEEKLY(BusinessModule.STR, IPConfigParamName.STR_STR_ALIAS.value(), JobName.NGIStrWeekly, JobName.StrDataLoadWeekly),
    STR_MONTHLY(BusinessModule.STR, IPConfigParamName.STR_STR_ALIAS.value(), JobName.NGIStrMonthly, JobName.StrDataLoadMonthly),
    MARKET_PERFORMANCE_DAILY(BusinessModule.MARKET_PERFORMANCE, IPConfigParamName.MARKET_PERFORMANCE_ALIAS.value(), JobName.NGIMarketPerformanceDaily, JobName.MarketPerformanceDataLoadDailyJob),
    MARKET_PERFORMANCE_MONTHLY(BusinessModule.MARKET_PERFORMANCE, IPConfigParamName.MARKET_PERFORMANCE_ALIAS.value(), JobName.NGIMarketPerformanceMonthly, JobName.MarketPerformanceDataLoadMonthlyJob),
    DEMAND360_DATA_LOAD(BusinessModule.DEMAND360, FeatureTogglesConfigParamName.DEMAND360SUBSCRIBER_PROPERTY_ID.value(), JobName.NGIDemand360File, JobName.Demand360DataLoadJob),
    FOLS(BusinessModule.RESERVATION, Constants.FOLS_PROPERTY_ALIAS, JobName.NGIProcessFolsFile, JobName.NGIDeferredDeliveryJob),
    RRA(BusinessModule.RRA, Constants.RRA_PROPERTY_ALIAS, JobName.NGIProcessRRAMessage, JobName.RRADataLoadJob),
    SALES_AND_CATERING(BusinessModule.FUNCTION_SPACE, IPConfigParamName.SALES_AND_CATERING_FUNCTION_SPACE_CLIENT_ALIAS.value(), JobName.NGIFunctionSpaceClientFile, JobName.FunctionSpaceDataLoadJob);

    private final BusinessModule businessModule;
    private final String constant;
    private final JobName ngiJobName;
    private final JobName g3JobName;

    IntegrationType(BusinessModule businessModule, String constant, JobName ngiJobName, JobName g3JobName) {
        this.businessModule = businessModule;
        this.constant = constant;
        this.ngiJobName = ngiJobName;
        this.g3JobName = g3JobName;
    }

    public BusinessModule getBusinessModule() {
        return businessModule;
    }

    public String getConstant() {
        return constant;
    }

    public JobName getNGIJobName() {
        return ngiJobName;
    }

    public JobName getG3JobName() {
        return g3JobName;
    }

    public static IntegrationType valueOfNGIJobName(JobName jobName) {
        for (IntegrationType integrationType : values()) {
            if (jobName.equals(integrationType.ngiJobName)) {
                return integrationType;
            }
        }

        return null;
    }

    public static List<IntegrationType> valuesForBusinessModule(BusinessModule businessModule) {
        List<IntegrationType> integrationTypes = new ArrayList<IntegrationType>();
        for (IntegrationType integrationType : values()) {
            if (businessModule.equals(integrationType.getBusinessModule())) {
                integrationTypes.add(integrationType);
            }
        }
        return integrationTypes;
    }
}