package com.ideas.tetris.pacman.services.property.configuration.service.specialevent;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.SpecialEventPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecordStatus;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import com.ideas.tetris.pacman.services.specialevent.entity.Frequency;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEvent;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.pacman.services.specialevent.entity.SpecialEventType;
import com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService;
import com.ideas.tetris.pacman.services.syncflags.service.SyncDisplayNameService;
import com.ideas.tetris.pacman.util.CustomizedDisplayName;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@SpecialEventConfigurationService.Qualifier
@Component
@Transactional
public class SpecialEventConfigurationService extends AbstractPropertyConfigurationService {

    private static final Logger LOGGER = Logger.getLogger(SpecialEventConfigurationService.class);

    @Autowired
	private SyncEventAggregatorService syncEventAggregatorService;

    @Autowired
	private SpecialEventService specialEventService;

    @Autowired
	private SyncDisplayNameService syncDisplayNameService;

    private static final int EVENT_TYPE_MAX_LENGTH = 50;
    private static final int EVENT_NAME_MAX_LENGTH = 50;

    @Override
    public PropertyConfigurationRecordType getPropertyConfigurationRecordType() {
        return PropertyConfigurationRecordType.SE;
    }

    @Override
    public List<PropertyConfigurationRecordFailure> doValidate(
            PropertyConfigurationDto propertyConfigurationDto,
            Integer propertyId) {
        SpecialEventPropertyConfigurationDto specialEventPropertyConfigurationDto = (SpecialEventPropertyConfigurationDto) propertyConfigurationDto;

        List<PropertyConfigurationRecordFailure> exceptions = new ArrayList<PropertyConfigurationRecordFailure>();

        // Validate Event Type
        String eventType = specialEventPropertyConfigurationDto.getEventType();

        if (StringUtils.isEmpty(eventType)) {
            exceptions.add(new PropertyConfigurationRecordFailure(
                    "Event Type is required"));
        } else if (eventType.length() > EVENT_TYPE_MAX_LENGTH) {
            specialEventPropertyConfigurationDto.setEventType(StringUtils.left(
                    eventType, EVENT_TYPE_MAX_LENGTH));
            exceptions.add(new PropertyConfigurationRecordFailure(
                    ConfigurationFileRecordStatus.WARNING,
                    "Event Type cannot be longer than " + EVENT_TYPE_MAX_LENGTH
                            + " characters.  The value has been trimmed."));
        }

        // Validate Event Name
        String eventName = specialEventPropertyConfigurationDto.getEventName();

        if (StringUtils.isEmpty(eventName)) {
            exceptions.add(new PropertyConfigurationRecordFailure(
                    "Event Name is required"));
        } else if (eventName.length() > EVENT_NAME_MAX_LENGTH) {
            specialEventPropertyConfigurationDto.setEventName(StringUtils.left(
                    eventName, EVENT_NAME_MAX_LENGTH));
            exceptions.add(new PropertyConfigurationRecordFailure(
                    ConfigurationFileRecordStatus.WARNING,
                    "Event Name cannot be longer than " + EVENT_NAME_MAX_LENGTH
                            + " characters.  The value has been trimmed."));
        }

        // Validate Start Date is not null
        Date startDate = specialEventPropertyConfigurationDto.getStartDate();
        if (startDate == null) {
            exceptions.add(new PropertyConfigurationRecordFailure(
                    "Event Start Date must contain a date in the format: "
                            + PropertyConfigurationDto.DATE_FORMAT));
        }

        // Validate End Date is not null
        Date endDate = specialEventPropertyConfigurationDto.getEndDate();
        if (endDate == null) {
            exceptions.add(new PropertyConfigurationRecordFailure(
                    "Event End Date must contain a date in the format: "
                            + PropertyConfigurationDto.DATE_FORMAT));
        }

        // Validate End Date not before Start Date
        if (startDate != null && endDate != null && endDate.before(startDate)) {
            exceptions.add(new PropertyConfigurationRecordFailure(
                    "Event End Date cannot be before Event Start Date"));
        }

        Integer numberOfPreEventDays = specialEventPropertyConfigurationDto
                .getNumberOfPreEventDays();
        if (numberOfPreEventDays == null || numberOfPreEventDays.intValue() < 0) {
            exceptions.add(new PropertyConfigurationRecordFailure(
                    "Number of Pre-Event Days must contain a postive number"));
        }

        Integer numberOfPostEventDays = specialEventPropertyConfigurationDto
                .getNumberOfPostEventDays();
        if (numberOfPostEventDays == null
                || numberOfPostEventDays.intValue() < 0) {
            exceptions
                    .add(new PropertyConfigurationRecordFailure(
                            "Number of Post-Event Days must contain a positive number"));
        }

        // Validate the Impact on Forecast field
        String impactOnForecast = specialEventPropertyConfigurationDto
                .getImpactsForcast();
        List<String> validImpactOnForecastValues = Arrays.asList("FYI", "ON",
                "OFF");
        if (StringUtils.isEmpty(impactOnForecast)) {
            exceptions.add(new PropertyConfigurationRecordFailure(
                    "Impact on Forecast is required.  Must be one of: "
                            + StringUtils
                            .join(validImpactOnForecastValues, ",")));
        } else if (!validImpactOnForecastValues.contains(impactOnForecast)) {
            exceptions.add(new PropertyConfigurationRecordFailure(
                    "Impact on Forecast value: "
                            + impactOnForecast
                            + " is not valid.  Must be one of: "
                            + StringUtils
                            .join(validImpactOnForecastValues, ",")));
        }

        return exceptions;
    }

    @Override
    public void doHandle(PropertyConfigurationDto pcd, Integer propertyId) {
        SpecialEventPropertyConfigurationDto specialEventPropertyConfigurationDto = (SpecialEventPropertyConfigurationDto) pcd;

        SpecialEventType specialEventType = findSpecialEventType(specialEventPropertyConfigurationDto
                .getEventType());
        if (specialEventType == null) {
            specialEventType = new SpecialEventType();
            specialEventType.setName(specialEventPropertyConfigurationDto
                    .getEventType());
            specialEventType
                    .setDescription(specialEventPropertyConfigurationDto
                            .getEventType());
            specialEventType.setStatusId(Constants.ACTIVE_STATUS_ID);

            LOGGER.info("Creating SpecialEventType for event name: "
                    + specialEventType.getName());
            specialEventType = crudService.save(specialEventType);
        }

        PropertySpecialEvent propertySpecialEvent = createOrUpdatePropertySpecialEvent(specialEventType,
                specialEventPropertyConfigurationDto, propertyId);

        PropertySpecialEventInstance propertySpecialEventInstance = findPropertySpecialEventInstance(
                propertySpecialEvent,
                specialEventPropertyConfigurationDto.getStartDate());
        if (propertySpecialEventInstance == null) {
            propertySpecialEventInstance = new PropertySpecialEventInstance();
            propertySpecialEventInstance
                    .setPropertySpecialEvent(propertySpecialEvent);
            propertySpecialEventInstance.setIsEdited(0);
            propertySpecialEventInstance.setIsDeleted(0);
            propertySpecialEventInstance.setStatusId(findActiveTenantStatus()
                    .getId());
            propertySpecialEventInstance.setFrequency(propertySpecialEvent
                    .getFrequency());
            propertySpecialEventInstance.setRepeatable(propertySpecialEvent
                    .getRepeatable());
            propertySpecialEvent
                    .addPropertySpecialEventInstance(propertySpecialEventInstance);
        }

        if (propertySpecialEvent.getImpactOnForcast().equals(
                Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING)) {
            propertySpecialEventInstance.setEnableForecast(1);
        } else {
            propertySpecialEventInstance.setEnableForecast(0);
        }

        propertySpecialEventInstance
                .setStartDate(specialEventPropertyConfigurationDto
                        .getStartDate());
        propertySpecialEventInstance
                .setEndDate(specialEventPropertyConfigurationDto.getEndDate());
        propertySpecialEventInstance
                .setPreEventDays(specialEventPropertyConfigurationDto
                        .getNumberOfPreEventDays());
        propertySpecialEventInstance
                .setPostEventDays(specialEventPropertyConfigurationDto
                        .getNumberOfPostEventDays());
        propertySpecialEventInstance.setLastUpdatedDate(LocalDateTime.now());

        // Before Saving the PropertySpecialEvent, check to see if the potential
        // change requires a sync
        if (specialEventService
                .isSpecialEventSyncRequired(propertySpecialEvent)) {
            if (syncEventAggregatorService
                    .registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)) {
                syncDisplayNameService.addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                        CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
            }
        }

        // If the PropertySpecialEvent is not persisted, insert it otherwise
        // update it
        if (!propertySpecialEvent.isPersisted()) {
            LOGGER.info("Creating PropertySpecialEvent: "
                    + propertySpecialEvent.getName() + " for Property: "
                    + propertyId + " for Start Date: "
                    + propertySpecialEvent.getStartDate());
            crudService.save(propertySpecialEvent);
        } else {
            LOGGER.info("Updating PropertySpecialEvent: "
                    + propertySpecialEvent.getName() + " for Property: "
                    + propertyId + " for Start Date: "
                    + propertySpecialEvent.getStartDate());
            crudService.save(propertySpecialEvent);
        }
    }

    public PropertySpecialEvent createOrUpdatePropertySpecialEvent(
            SpecialEventType specialEventType, SpecialEventPropertyConfigurationDto sepcd, Integer propertyId) {
        PropertySpecialEvent propertySpecialEvent = findPropertySpecialEvent(
                sepcd.getEventName(), propertyId);

        if (propertySpecialEvent == null) {
            propertySpecialEvent = new PropertySpecialEvent();
            propertySpecialEvent.setName(sepcd.getEventName());
            propertySpecialEvent.setSpecialEventType(specialEventType);
            propertySpecialEvent.setPropertyId(propertyId);
            propertySpecialEvent.setTemplateDefault(1);
            propertySpecialEvent.setStatusId(findActiveTenantStatus().getId());
            propertySpecialEvent.setFrequency(findFrequency());
        }

        propertySpecialEvent.setStartDate(sepcd.getStartDate());
        propertySpecialEvent.setEndDate(sepcd.getEndDate());
        propertySpecialEvent.setLastUpdatedDate(LocalDateTime.now());

        // Handle Impacts Forecast
        String impactsForecast = sepcd.getImpactsForcast();

        setImpactForecast(propertySpecialEvent, impactsForecast);


        return propertySpecialEvent;
    }

    protected void setImpactForecast(PropertySpecialEvent propertySpecialEvent, String impactsForecast) {
        if (StringUtils.equalsIgnoreCase(impactsForecast, "ON")
                || StringUtils.equalsIgnoreCase(impactsForecast, "OFF")) {
            propertySpecialEvent
                    .setImpactOnForcast(Constants.SPECIAL_EVENT_USE_DATES_FOR_FORECASTING);
        } else {
            propertySpecialEvent
                    .setImpactOnForcast(Constants.SPECIAL_EVENT_INFORMATION_ONLY);
        }
    }

    public PropertySpecialEventInstance findPropertySpecialEventInstance(
            PropertySpecialEvent propertySpecialEvent, Date startDate) {
        if (!propertySpecialEvent.isPersisted()) {
            return null;
        }

        return (PropertySpecialEventInstance) crudService
                .findByNamedQuerySingleResult(
                        PropertySpecialEventInstance.BY_SPECIAL_EVENT_START_DATE,
                        QueryParameter
                                .with("propertySpecialEvent",
                                        propertySpecialEvent)
                                .and("startDate", startDate).parameters());
    }

    public Frequency findFrequency() {
        return crudService.find(Frequency.class, 1);
    }

    public PropertySpecialEvent findPropertySpecialEvent(String name,
                                                  Integer propertyId) {
        return (PropertySpecialEvent) crudService.findByNamedQuerySingleResult(
                PropertySpecialEvent.BY_PROPERTY_EVENT,
                QueryParameter.with("name", name)
                        .and("propertyId", propertyId).parameters());
    }

    public SpecialEventType findSpecialEventType(String name) {
        return (SpecialEventType) crudService.findByNamedQuerySingleResult(
                SpecialEventType.FIND_BY_NAME, QueryParameter
                        .with("name", name).parameters());
    }

    public void setSyncEventAggregatorService(
            SyncEventAggregatorService syncEventAggregatorService) {
        this.syncEventAggregatorService = syncEventAggregatorService;
    }

    public void setSpecialEventService(SpecialEventService specialEventService) {
        this.specialEventService = specialEventService;
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
