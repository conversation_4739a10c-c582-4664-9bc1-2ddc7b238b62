package com.ideas.tetris.pacman.services.datafeed.dto.pricingdata;

import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductPackage;

import java.io.Serializable;

public class PricingDataProductPackageElementAssignmentDTO implements Serializable {

    private String productName;

    private String packageElementAssigned;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPackageElementAssigned() {
        return packageElementAssigned;
    }

    public void setPackageElementAssigned(String packageElementAssigned) {
        this.packageElementAssigned = packageElementAssigned;
    }

    public PricingDataProductPackageElementAssignmentDTO() {
    }

    public PricingDataProductPackageElementAssignmentDTO(ProductPackage productPackage) {
        this.productName = productPackage.getProduct().getName();
        this.packageElementAssigned = productPackage.getAgileRatesPackage().getName();
    }
}
