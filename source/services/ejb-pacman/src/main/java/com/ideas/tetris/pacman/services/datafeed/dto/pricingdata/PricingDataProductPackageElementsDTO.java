package com.ideas.tetris.pacman.services.datafeed.dto.pricingdata;

import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesPackage;

import java.io.Serializable;
import java.math.BigDecimal;

public class PricingDataProductPackageElementsDTO implements Serializable {

    private String packageName;

    private String packageDescription;

    private String chargeType;

    private BigDecimal amountofCharge;

    private String adjustmentType;

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getPackageDescription() {
        return packageDescription;
    }

    public void setPackageDescription(String packageDescription) {
        this.packageDescription = packageDescription;
    }

    public String getChargeType() {
        return chargeType;
    }

    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    public BigDecimal getAmountofCharge() {
        return amountofCharge;
    }

    public void setAmountofCharge(BigDecimal amountofCharge) {
        this.amountofCharge = amountofCharge;
    }

    public String getAdjustmentType() {
        return adjustmentType;
    }

    public void setAdjustmentType(String adjustmentType) {
        this.adjustmentType = adjustmentType;
    }

    public PricingDataProductPackageElementsDTO() {
    }

    public PricingDataProductPackageElementsDTO(AgileRatesPackage agileRatesPackage) {
        this.packageName = agileRatesPackage.getName();
        this.packageDescription = agileRatesPackage.getDescription();
        this.chargeType = agileRatesPackage.getChargeType().getCaption();
        this.amountofCharge = agileRatesPackage.getOffsetValue();
        this.adjustmentType = AgileRatesOffsetMethod.PERCENTAGE.equals(agileRatesPackage.getOffsetMethod()) ? "Percentage" : agileRatesPackage.getOffsetMethod().getCaption();
    }
}
