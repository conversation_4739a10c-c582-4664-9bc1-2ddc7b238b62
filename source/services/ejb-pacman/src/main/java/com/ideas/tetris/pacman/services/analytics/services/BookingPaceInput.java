package com.ideas.tetris.pacman.services.analytics.services;

import com.ideas.tetris.pacman.services.analytics.dto.*;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDate;
import java.util.*;
import java.util.function.BiFunction;

@AllArgsConstructor
@Builder(toBuilder = true)
public class BookingPaceInput {
    private final LocalDate systemDate;
    private final LocalDate lastOccupancyDate;
    private final Set<Integer> forecastGroupIds;
    private final Set<Integer> accomClassIds;
    private final Map<ForecastAccomClassDateKey, Integer> solds;
    private final Map<ForecastAccomClassDateKey, Double> forecast;
    private final Map<BookingCurveKey, Double> bookingCurve;
    private final Map<ForecastGroupAccomClassKey, NavigableMap<LocalDate, List<BookingCurveOverride>>> bookingCurveOverrides;
    private final Map<BookingCurveEventKey, Double> bookingCurveEvent;
    private final Map<LocalDate, List<Integer>> specialEventIdsByDate;
    private final BiFunction<Integer, Integer, ProcessGroupConfigDetailResolver> pgcdResolveByFgByRc;

    public List<Integer> getSpecialEventIds(LocalDate date) {
        return this.specialEventIdsByDate.getOrDefault(date, List.of());
    }

    public Optional<Double> getBookingCurveValue(int forecastGroupId, int accomClassId, LocalDate occupancyDate, double dta) {
        return Optional.ofNullable(getOverridenBookingCurveValue(forecastGroupId, accomClassId, occupancyDate, dta)
                .orElseGet(() -> getSpecialEventBookingCurveValue(forecastGroupId, accomClassId, occupancyDate, dta)
                        .orElseGet(() -> getSasBookingCurveValue(forecastGroupId, accomClassId, occupancyDate, dta))));

    }

    private Optional<Double> getSpecialEventBookingCurveValue(int forecastGroupId, int accomClassId, LocalDate occupancyDate, double dta) {
        int horizonGroupNumber = this.pgcdResolveByFgByRc.apply(accomClassId, forecastGroupId).getGetHorizonNumber().apply(dta);
        return this.getSpecialEventIds(occupancyDate).stream()
                .map(eventId -> this.bookingCurveEvent.get(new BookingCurveEventKey(forecastGroupId, accomClassId, eventId, horizonGroupNumber)))
                .filter(Objects::nonNull)
                .mapToDouble(Double::doubleValue)
                .average()
                .stream()
                .boxed()
                .findFirst();
    }

    private Optional<Double> getOverridenBookingCurveValue(int forecastGroupId, int accomClassId, LocalDate occupancyDate, double dta) {
        Map.Entry<LocalDate, List<BookingCurveOverride>> floorEntry = this.bookingCurveOverrides
                .getOrDefault(new ForecastGroupAccomClassKey(forecastGroupId, accomClassId), new TreeMap<>())
                .floorEntry(occupancyDate);
        return Optional.ofNullable(floorEntry).stream()
                .flatMap(e -> e.getValue().stream())
                .filter(b -> !b.getEndDate().isBefore(occupancyDate) && b.getReadingGroupNumber() == (int) dta).map(BookingCurveOverride::getValue)
                .findFirst();
    }

    private Double getSasBookingCurveValue(int forecastGroupId, int accomClassId, LocalDate occupancyDate, double dta) {
        ProcessGroupConfigDetailResolver resolver = this.pgcdResolveByFgByRc.apply(accomClassId, forecastGroupId);
        int seasonGroupNumber = resolver.getGetSeasonNumber().apply(occupancyDate);
        int dowGroupNumber = resolver.getGetDowNumber().apply(occupancyDate);
        int horizonGroupNumber = resolver.getGetHorizonNumber().apply(dta);
        return this.bookingCurve
                .get(new BookingCurveKey(forecastGroupId, accomClassId, seasonGroupNumber, dowGroupNumber, horizonGroupNumber));
    }

    public LocalDate getSystemDate() {
        return this.systemDate;
    }

    public LocalDate getLastOccupancyDate() {
        return this.lastOccupancyDate;
    }

    public Set<Integer> getForecastGroupIds() {
        return this.forecastGroupIds;
    }

    public Set<Integer> getAccomClassIds() {
        return this.accomClassIds;
    }

    public Map<ForecastAccomClassDateKey, Integer> getSolds() {
        return this.solds;
    }

    public Map<ForecastAccomClassDateKey, Double> getForecast() {
        return this.forecast;
    }
}
