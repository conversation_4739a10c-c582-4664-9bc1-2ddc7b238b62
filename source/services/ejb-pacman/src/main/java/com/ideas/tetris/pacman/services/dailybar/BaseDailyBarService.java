package com.ideas.tetris.pacman.services.dailybar;


import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutput;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.decisiondelivery.DecisionDeliveryService;
import com.ideas.tetris.pacman.services.hospitalityrooms.service.HospitalityRoomsService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DecisionDeliveryType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Transactional;


@Component
@Transactional
public abstract class BaseDailyBarService implements DailyBarServiceLocal {

    private static final String GET_BASE_RATE_FOR_DAILYBAR = new StringBuilder()
            .append(" SELECT rq.Rate_Code_Name, at.Accom_Type_Code, at.Accom_Class_ID")
            .append(" 	,at.Accom_Type_ID,D.Arrival_DT as date_m")
            .append("     ,CASE (DATEPART(WEEKDAY, D.Arrival_DT)) ")
            .append(" 		WHEN 1 THEN (Sunday) ")
            .append(" 		WHEN 2 THEN (Monday) ")
            .append("         WHEN 3 THEN (Tuesday) ")
            .append("         WHEN 4 THEN (Wednesday) ")
            .append("         WHEN 5 THEN (Thursday) ")
            .append("         WHEN 6 THEN (Friday) ")
            .append("         WHEN 7 THEN (Saturday) ")
            .append("       END AS Rate_value ")
            .append("       into #Dailybarbaserate ")
            .append("   FROM")
            .append("   ( ")
            .append(" 	SELECT  Accom_Class_ID, Arrival_DT, Rate_Unqualified_ID,LOS ")
            .append("     FROM Decision_Bar_Output ")
            .append("     WHERE (Arrival_DT BETWEEN  :decisionStartDate AND :decisionEndDate) ")
            .append("     and (LOS = 1 OR LOS = - 1) ")
            .append("    ) AS D ")
            .append("    INNER JOIN  Rate_Unqualified AS rq ")
            .append("    on D.Rate_Unqualified_ID = rq.Rate_Unqualified_ID ")
            .append("    INNER JOIN  Rate_Unqualified_Details AS rqd ")
            .append("    ON rq.Rate_Unqualified_ID = rqd.Rate_Unqualified_ID ")
            .append("    INNER JOIN ")
            .append("    (")
            .append(" 	 SELECT  Accom_Class_ID, Accom_Type_Code, AT.Accom_Type_ID,AA.Occupancy_DT ")
            .append("      FROM Accom_Type AT ")
            .append("      inner join Accom_Activity as AA ")
            .append("      on AA.Accom_Type_ID = AT.Accom_Type_ID")
            .append("      WHERE  (AT.Accom_Type_Capacity > 0 zeroCapacityRTClause)  and AA.Occupancy_DT BETWEEN  :decisionStartDate AND :decisionEndDate and (AA.Accom_Capacity > 0 zeroCapacityRTClause)")
            .append("     ) AS at")
            .append("     ON rqd.Accom_Type_ID = at.Accom_Type_ID ")
            .append("     and D.Rate_Unqualified_ID = rq.Rate_Unqualified_ID ")
            .append("     AND D.Accom_Class_ID = at.Accom_Class_ID ")
            .append("     and AT.Occupancy_DT=D.Arrival_DT ")
            .append("     inner join ")
            .append("     (")
            .append(" 	  select Distinct room_type ")
            .append(" 	  from Daily_Bar_Config")
            .append(" 	 ) as DBR ")
            .append(" 	 on AT.Accom_Type_Code= DBR.Room_Type ")
            .append("      where D.Arrival_DT BETWEEN rqd.Start_Date_DT AND rqd.End_Date_DT ;")
            .toString();
    private static final String SELECT_RATE_ACCORDING_TO_DOW = new StringBuilder()
            .append("  			(select case  (DATEPART(WEEKDAY, date_m))")
            .append("  						when 1 then (Sunday_Rate )     ")
            .append("  						when 2 then (Monday_Rate )")
            .append("  						when 3 then (Tuesday_Rate  )  ")
            .append("  						when 4 then (Wednesday_Rate )")
            .append("  						when 5 then (Thursday_Rate)   ")
            .append("  						when 6 then (Friday_Rate)")
            .append("  						when 7 then (Saturday_Rate)  ")
            .append("  					 end ")
            .toString();
    public static final String GET_DAILYBAR_SCENARIO_MAP = new StringBuilder()
            .append("  select date_m,Rate_Code_Name,Rate_Plan,Rate_value,Accom_Type_Code")
            .append("  	,SingleOffsetType,SingleOffsetValue,DoubleOffsetType,DoubleOffsetValue")
            .append("      ,ExtraAdultOffsetType,ExtraAdultOffsetValue,ExtraChildOffsetType,ExtraChildOffsetValue,product_id")
            .append("  into #DailybarScenarioMap ")
            .append("  from")
            .append("  ( ")
            .append("    select date_m,Rate_Code_Name,Rate_Plan,Rate_value,Accom_Type_Code")
            .append("  	,SingleOffsetType,SingleOffsetValue,DoubleOffsetType,DoubleOffsetValue")
            .append("      ,ExtraAdultOffsetType,ExtraAdultOffsetValue,ExtraChildOffsetType,ExtraChildOffsetValue,product_id ")
            .append("      ,rank() over (PARTITION by date_m,rate_code_name,accom_type_code,product_id order by rate_plan desc) as pos ")
            .append("  	  from")
            .append("  	  (")
            .append("  		select Daily_Bar_Config_id,br.date_m,br.Rate_Code_Name")
            .append("  			,dbc.Rate_Plan,br.Rate_value,br.Accom_Type_Code,dbc.Product_ID,")
            .append(SELECT_RATE_ACCORDING_TO_DOW)
            .append("  			  from Daily_Bar_Rate_Chart ")
            .append("  			  where Daily_Bar_Rate_Chart_ID=dbc.Single_Rate_Chart_ID")
            .append("  			 ) as SingleOffsetValue")
            .append("  			,(select Offset_Type ")
            .append("  			  from Daily_Bar_Rate_Chart ")
            .append("  			  where Daily_Bar_Rate_Chart_ID=dbc.Single_Rate_Chart_ID")
            .append("  			 ) as SingleOffsetType,")
            .append(SELECT_RATE_ACCORDING_TO_DOW)
            .append("  			  from Daily_Bar_Rate_Chart ")
            .append("  			  where Daily_Bar_Rate_Chart_ID=dbc.Double_Rate_Chart_ID")
            .append("  			 ) as DoubleOffsetValue")
            .append("  			 ,(select Offset_Type  ")
            .append("  			   from Daily_Bar_Rate_Chart ")
            .append("  			   where Daily_Bar_Rate_Chart_ID=dbc.Double_Rate_Chart_ID")
            .append("  			  ) as DoubleOffsetType,")
            .append(SELECT_RATE_ACCORDING_TO_DOW)
            .append("  			  from Daily_Bar_Rate_Chart ")
            .append("  			  where Daily_Bar_Rate_Chart_ID=dbc.Extra_Adult_Rate_Chart_ID")
            .append("  			 )as ExtraAdultOffsetValue")
            .append("  			 ,(select Offset_Type ")
            .append("  			   from Daily_Bar_Rate_Chart ")
            .append("  			   where Daily_Bar_Rate_Chart_ID=dbc.Extra_Adult_Rate_Chart_ID")
            .append("  			  ) as ExtraAdultOffsetType,")
            .append(SELECT_RATE_ACCORDING_TO_DOW)
            .append("  			   from Daily_Bar_Rate_Chart ")
            .append("  			   where Daily_Bar_Rate_Chart_ID=dbc.Extra_Child_Rate_Chart_ID")
            .append("  			 ) as ExtraChildOffsetValue")
            .append("  			 ,(select Offset_Type ")
            .append("  			   from Daily_Bar_Rate_Chart ")
            .append("  			   where Daily_Bar_Rate_Chart_ID=dbc.Extra_Child_Rate_Chart_ID")
            .append("  			 ) as ExtraChildOffsetType ")
            .append("  	  from Daily_Bar_Config as dbc ")
            .append("  	  inner join   #Dailybarbaserate as br ")
            .append("  	  on Accom_Type_Code=dbc.Room_Type ")
            .append("  	  and  br.date_m between dbc.[Start_Date] and dbc.End_Date")
            .append("  	  and (Rate_Code_Name=dbc.Rate_Plan OR dbc.Rate_Plan is null )")
            .append("     and (dbc.product_id is null or  dbc.Product_ID not in (select Product_ID from Product where TYPE = 'Extended Stay'))")
            .append("  	) as dailybar")
            .append("  ) as a ")
            .append(" where a.pos=1;")
            .toString();
    public static final String CALCULATE_DAILYBAR_RATES = new StringBuilder()
            .append(" select date_m, rate_code_name,accom_type_code,singledailybarrate")
            .append(" 	,doubledailybarrate,extraadultdailybarrate,extrachilddailybarrate")
            .append(" 	, product_id")
            .append(" into  #Decision_Dailybar_Output_Temp ")
            .append(" from")
            .append(" ( ")
            .append("   select date_m, rate_code_name,accom_type_code")
            .append(" 	,singledailybarrate,doubledailybarrate,extraadultdailybarrate,product_id ")
            .append("     ,case :useExtraChildConfig  ")
            .append(" 		when 'false' then null ")
            .append("         when 'true' then ")
            .append(" 			case ExtraChildOffsetType ")
            .append(" 				when 'FIXED' then ExtraChildOffsetValue ")
            .append(" 				when 'PERCENTAGE' then (ExtraChildOffsetValue*0.01 *doubledailybarrate) ")
            .append(" 			end ")
            .append(" 	 end as extrachilddailybarrate ")
            .append(" 	from ")
            .append("     (")
            .append("       select date_m, rate_code_name,accom_type_code,singledailybarrate,doubledailybarrate")
            .append(" 		,ExtraChildOffsetValue,ExtraChildOffsetType,product_id")
            .append("         ,case :useExtraAdultConfig  ")
            .append("              when 'false' then null ")
            .append("              when 'true' then ")
            .append(" 				case ExtraAdultOffsetType ")
            .append(" 					when 'FIXED' then ExtraAdultOffsetValue ")
            .append(" 					when 'PERCENTAGE' then (ExtraAdultOffsetValue*0.01*doubledailybarrate) ")
            .append(" 				end  ")
            .append(" 		 end as extraadultdailybarrate")
            .append(" 		from  ")
            .append("         ( ")
            .append("           select date_m,rate_code_name,accom_type_code,Rate_value")
            .append(" 			,singledailybarrate, ExtraChildOffsetValue,ExtraChildOffsetType")
            .append("             ,ExtraAdultOffsetValue,ExtraAdultOffsetType,product_id")
            .append("             ,case :useSingleRateAsDoubleRate ")
            .append(" 				when 'true' then singledailybarrate ")
            .append(" 				when 'false' then ")
            .append(" 					case DoubleOffsetType ")
            .append(" 						when 'FIXED' then singledailybarrate+ DoubleOffsetValue ")
            .append(" 						when 'PERCENTAGE' then singledailybarrate + (DoubleOffsetValue*0.01 *singledailybarrate) ")
            .append(" 						when 'SET' then DoubleOffsetValue ")
            .append(" 					end ")
            .append(" 			  end as doubledailybarrate   ")
            .append("              from ")
            .append("              (")
            .append("                select  date_m, rate_code_name,accom_type_code,Rate_value")
            .append(" 				,DoubleOffsetValue, DoubleOffsetType, ExtraChildOffsetValue")
            .append(" 				,ExtraChildOffsetType,ExtraAdultOffsetValue,ExtraAdultOffsetType,product_id")
            .append(" 				, case :useBaseRateAsSingleRate  ")
            .append(" 					when 'true' then Rate_value ")
            .append(" 					when 'false' then ")
            .append(" 						case SingleOffsetType ")
            .append(" 							when 'FIXED' then Rate_value + SingleOffsetValue ")
            .append(" 							when 'PERCENTAGE' then Rate_value + (SingleOffsetValue*0.01 *Rate_value) ")
            .append(" 							when 'SET' then SingleOffsetValue ")
            .append(" 						end ")
            .append(" 				  end as singledailybarrate  ")
            .append(" 				from #DailybarScenarioMap")
            .append(" 			   ) as sdr ")
            .append(" 		 ) as ddr ")
            .append(" 	) as adr ")
            .append(" ) as cdr; ")
            .toString();
    public static final String INSERT_DECISON_DAILYBAR_OUTPUT = new StringBuilder()
            .append(" MERGE Decision_Dailybar_Output AS target ")
            .append(" USING")
            .append(" (")
            .append(" 	select  ddot.date_m,at.Accom_Type_ID,rq.Rate_Unqualified_ID")
            .append(" 		,ddot.singledailybarrate,ddot.doubledailybarrate")
            .append("         ,ddot.extraadultdailybarrate,ddot.extrachilddailybarrate ")
            .append("         ,product_id")
            .append("     from  #Decision_Dailybar_Output_Temp as ddot ")
            .append("     inner join  Accom_Type as at ")
            .append("     on ddot.Accom_Type_Code = at.Accom_Type_Code ")
            .append("     inner join Rate_Unqualified as rq ")
            .append("     on ddot.Rate_Code_Name = rq.Rate_Code_Name and rq.Status_ID = 1")
            .append(" ) AS source  ")
            .append(" ON target.Occupancy_Date = source.date_m ")
            .append(" AND target.Accom_Type_ID = source.Accom_Type_ID ")
            .append(" AND ((target.product_id is null and source.product_id is null) OR (target.product_id = source.product_id))")
            .append(" WHEN MATCHED  ")
            .append(" 		AND (target.Single_Rate <> (select CAST(round(source.singledailybarrate,5) as numeric(19,5))) OR ")
            .append(" 			target.Double_Rate <> (select CAST(round(source.doubledailybarrate,5) as numeric(19,5))) OR  ")
            .append(" 			target.Adult_Rate <> (select CAST(round(source.extraadultdailybarrate,5) as numeric(19,5))) OR ")
            .append(" 			target.Child_Rate <> (select CAST(round(source.extrachilddailybarrate,5) as numeric(19,5)))  OR ")
            .append(" 			target.Rate_Unqualified_ID <> source.Rate_Unqualified_ID ")
            .append(" 			) ")
            .append(" THEN  ")
            .append(" 	UPDATE SET  Decision_ID =:decisionId")
            .append("          ,Rate_Unqualified_ID = source.Rate_Unqualified_ID")
            .append("          ,Single_Rate = source.singledailybarrate")
            .append("          ,Double_Rate = source.doubledailybarrate ")
            .append("          ,Adult_Rate =source.extraadultdailybarrate")
            .append("          ,Child_Rate =source.extrachilddailybarrate ")
            .append("          ,CreateDate_DTTM = CURRENT_TIMESTAMP ")
            .append(" WHEN NOT MATCHED ")
            .append(" THEN  ")
            .append("     INSERT(Decision_ID,Occupancy_Date,Accom_Type_ID, Rate_Unqualified_ID,Single_Rate")
            .append(" 			, Double_Rate, Adult_Rate, Child_Rate, CreateDate_DTTM, product_id) ")
            .append("     VALUES(:decisionId,source.date_m,source.Accom_Type_ID,  source.Rate_Unqualified_ID")
            .append("              ,source.singledailybarrate, source.doubledailybarrate")
            .append("              ,source.extraadultdailybarrate, source.extrachilddailybarrate,CURRENT_TIMESTAMP,source.product_id")
            .append("            );")
            .toString();
    public static final String INSERT_PACE_DECISON_DAILYBAR_OUTPUT = new StringBuilder()
            .append(" insert into PACE_Dailybar_Output(")
            .append(" 	Decision_ID,Occupancy_Date, Accom_Type_ID,Rate_Unqualified_ID")
            .append(" 	,Single_Rate,Double_Rate,Adult_Rate,Child_Rate,Product_Id")
            .append(" 	) ")
            .append(" 	(select Decision_ID,Occupancy_Date, Accom_Type_ID")
            .append(" 		,Rate_Unqualified_ID,Single_Rate,Double_Rate")
            .append(" 		,Adult_Rate,Child_Rate,Product_Id ")
            .append(" 	 from Decision_Dailybar_Output ")
            .append(" 	 where decision_id= :decisionId);")
            .toString();
    private static final Logger LOGGER = Logger.getLogger(DailyBarServiceLocal.class.getName());
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService crudService;
    @Autowired
	protected DecisionService decisionService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    DateService dateService;
    @Autowired
	private DecisionDeliveryService decisionDeliveryService;
    @Autowired
	private HospitalityRoomsService hospitalityRoomsService;

    public void setPacmanConfigParamsService(
            PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    @Override
    public void createDailyBarDecisionsIfConfigured() {
        if (decisionDeliveryService.isDecisionTypeConfigured(DecisionDeliveryType.BAR_DAILY)) {
            createDailyBarDecisions();
        }
    }

    public void createDailyBarDecisions() {
        LOGGER.debug("DailyBarService createDailyBarDecisions ");
        LOGGER.info("createDailyBarDecisions");

        Decision decisionRecord = createDecisionEntry();
        LOGGER.debug("createDailyBarDecisionsIfConfigured decisionRecord " + decisionRecord);
        String zeroCapacityRTClauseToReplace = getZeroCapacityRTClauseToReplace();
        LOGGER.info("zeroCapacityRTClauseToReplace => " + zeroCapacityRTClauseToReplace);
        int decisionDailybarOutputCount = crudService.executeUpdateByNativeQuery(
                GET_BASE_RATE_FOR_DAILYBAR.replaceAll("zeroCapacityRTClause", zeroCapacityRTClauseToReplace)
                        + GET_DAILYBAR_SCENARIO_MAP + CALCULATE_DAILYBAR_RATES + INSERT_DECISON_DAILYBAR_OUTPUT,
                QueryParameter.with("decisionStartDate", dateService.getOptimizationWindowStartDate()).and("decisionEndDate", getOptimizationWindowEndDate())
                        .and("useBaseRateAsSingleRate", pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DAILY_BAR_USE_BASE_RATE_AS_SINGLE_RATE.value()))
                        .and("useSingleRateAsDoubleRate", pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DAILY_BAR_USE_SINGLE_RATE_AS_DOUBLE_RATE.value()))
                        .and("useExtraAdultConfig", pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DAILY_BAR_USE_EXTRA_ADULT_CONFIG.value()))
                        .and("useExtraChildConfig", pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DAILY_BAR_USE_EXTRA_CHILD_CONFIG.value()))
                        .and("decisionId", decisionRecord.getId())
                        .parameters());
        LOGGER.info("Number of records inserted into Decision_Dailybar_Output table are " + decisionDailybarOutputCount);

        // Insert the Pace and update the Decision
        int paceDecisionDailybarOutputCountcrudService = crudService.executeUpdateByNativeQuery(INSERT_PACE_DECISON_DAILYBAR_OUTPUT, QueryParameter.with("decisionId", decisionRecord.getId()).parameters());
        LOGGER.info("Number of records inserted into PACE_Dailybar_Output table are " + paceDecisionDailybarOutputCountcrudService);

        // remove 0 accom capacity decisions for future from Decision_Dailybar_Output table (non-pace)
        deleteFutureDecisionsForZeroCapacityRTs();
        decisionService.updateDescisionProcessStatus(decisionRecord.getId(), Constants.PROCESS_STATUS_SUCCESSFUL);
    }

    @VisibleForTesting
	public
    String getZeroCapacityRTClauseToReplace() {
        List<String> allTypesOfHospitalityRooms = hospitalityRoomsService.getAllTypesOfHospitalityRooms();
        String zeroCapacityRTClauseToReplace = "";
        if (CollectionUtils.isNotEmpty(allTypesOfHospitalityRooms)) {
            zeroCapacityRTClauseToReplace = " or AT.Accom_type_code in ('" + String.join("','", allTypesOfHospitalityRooms) + "')";
        }
        return zeroCapacityRTClauseToReplace;
    }

    @VisibleForTesting
	public
    void deleteFutureDecisionsForZeroCapacityRTs() {
        List<String> zeroCapacityRoomTypesExcludingHospitalityRooms = hospitalityRoomsService.getZeroCapacityRoomTypesExcludingHospitalityRooms();
        if (CollectionUtils.isEmpty(zeroCapacityRoomTypesExcludingHospitalityRooms)) {
            return;
        }
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("caughtUpDate", new LocalDate(dateService.getCaughtUpDate()));
        parameters.put("accomTypeCodes", zeroCapacityRoomTypesExcludingHospitalityRooms);

        crudService.executeUpdateByNamedQuery(DecisionDailybarOutput.DELETE_FUTURE_DECISIONS_FOR_ZERO_CAPACITY_ROOM_TYPES, parameters);
    }

    abstract public void createDecisions();

    abstract public Date getOptimizationWindowEndDate();

    public Decision createDecisionEntry() {
        return decisionService.createDailyBarDecision();
    }
}
