package com.ideas.tetris.pacman.services.security;


import com.ideas.infra.tetris.security.EncryptionDecryption;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.platform.common.errorhandling.LoginStatusCode;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.Days;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.sql.Timestamp;
import java.util.Date;

import static com.ideas.tetris.platform.common.errorhandling.LoginStatusCode.INVALID_USER;
import static com.ideas.tetris.platform.common.errorhandling.LoginStatusCode.SUCCESS;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class PasswordSecurityService {

    @Autowired
    UserGlobalDBService userGlobalDBService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    private static final Integer ZERO = 0;
    private static final Integer LAST_ATTEMPT = 1;


    public void deactivateGlobalUser(String email) {
        userGlobalDBService.deactivateGlobalUser(userGlobalDBService.getGlobalUserByEmail(email));
    }


    public LoginResultInfo authenticate(boolean loginSuccess, String email) {
        GlobalUser globalUser = userGlobalDBService.getGlobalUserByEmail(email);
        LoginResultInfo loginResultInfo = null;
        if (globalUser == null) {
            return buildResultInfoForInvalidUser();
        }

        if (!globalUser.isActive() && globalUser.getRemainingFailedAttemptsAllowed() > 0) {
            return buildResultInfoForDeactivatedUser(globalUser.getId());
        }

        if (globalUser.isInternal()) {
            return buildResultInfoForInternalUser(loginSuccess, globalUser.getId());
        }

        if (!globalUser.getPasswordNeverExpire() && !isSSOUser(globalUser.getClientCode())) {
            if (loginSuccess && null == globalUser.getLastPasswordChangeDttm()) {
                return buildResultInfoForFirstTimeLoggedIn(globalUser);
            }
        }
        loginResultInfo = applyRemainingAttemptsLeftPolicy(loginSuccess, globalUser);
        return loginResultInfo;
    }

    public boolean isSSOUser(String clientCode) {
        return Boolean.valueOf(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.SSOENABLED.value(), clientCode));
    }

    private String buildUserToken(GlobalUser globalUser) {
        String timeStamp = String.valueOf(new Date().getTime());
        StringBuilder textToEncode = new StringBuilder(
                globalUser.getEmail())
                .append("--")
                .append(globalUser.getId())
                .append("--")
                .append(timeStamp);
        return EncryptionDecryption.doStrongTextEncryption(textToEncode.toString());
    }

    private LoginResultInfo buildResultInfoForInternalUser(boolean loginSucess, Integer globalUserId) {
        return new LoginResultInfo(loginSucess ? SUCCESS.getId() : INVALID_USER.getId(), 5, globalUserId, "");
    }

    private LoginResultInfo buildResultInfoForInvalidUser() {
        return new LoginResultInfo(INVALID_USER.getId(), 0, -1, "");
    }

    private LoginResultInfo buildResultInfoForDeactivatedUser(Integer globalUserId) {
        return new LoginResultInfo(LoginStatusCode.ACCOUNT_DEACTIVED.getId(), 0, globalUserId, "");
    }

    private LoginResultInfo checkPasswordExpiration(boolean loginSucess, GlobalUser globalUser) {
        if (loginSucess) {
            Days dateDifference = Days.daysBetween(LocalDate.fromDateFields(LocalDateUtils.toDate(globalUser.getLastPasswordChangeDttm())), LocalDate.fromDateFields(DateUtil.getCurrentDate()));
            int passwordActiveDuration = Integer.parseInt(getPasswordActiveDurationForClientContext(globalUser.getClientCode()));
            if (dateDifference.getDays() + 1 > passwordActiveDuration) {
                LoginResultInfo loginResultInfo = new LoginResultInfo();
                loginResultInfo.setUserId(globalUser.getId());
                loginResultInfo.setStatusCode(LoginStatusCode.PASSWORD_EXPIRED.getId());
                loginResultInfo.setUserToken(buildUserToken(globalUser));
                return loginResultInfo;
            }
        }

        return null;
    }

    private String getPasswordActiveDurationForClientContext(String clientCode) {
        String clientContext = "pacman." + clientCode;
        return pacmanConfigParamsService.getValue(clientContext, GUIConfigParamName.CORE_PROPERTY_PASSWORD_ACTIVE_DURATION.value());
    }

    private LoginResultInfo buildResultInfoForFirstTimeLoggedIn(GlobalUser globalUser) {
        return new LoginResultInfo(LoginStatusCode.FIRST_TIME_USER_SHOULD_CHANGE_PASSWORD.getId(), 5, globalUser.getId(),
                buildUserToken(globalUser));
    }

    private LoginResultInfo applyRemainingAttemptsLeftPolicy(boolean loginSucess, GlobalUser globalUser) {
        LoginResultInfo loginResultInfo = new LoginResultInfo();
        Integer remainingFailedAttempts = globalUser.getRemainingFailedAttemptsAllowed();
        Integer maxFailedAttemptsAllowed = Integer.parseInt(pacmanConfigParamsService.getValue("pacman." + globalUser.getClientCode(), GUIConfigParamName.CORE_PROPERTY_MAX_FAILED_AUTHENTICATION_RETRIES_ALLOWED.value()));
        Integer statusCode = actOnRemainingFailedAttempts(loginSucess, globalUser);
        remainingFailedAttempts = getRemainingAttempts(remainingFailedAttempts, maxFailedAttemptsAllowed, statusCode);
        updateRemainingAttempts(globalUser, remainingFailedAttempts);
        loginResultInfo.setStatusCode(statusCode);
        loginResultInfo.setUserId(globalUser.getId());
        loginResultInfo.setRemainingFailedAttemptsCount(remainingFailedAttempts);
        return loginResultInfo;
    }

    private Integer getRemainingAttempts(Integer remainingFailedAttempts, Integer maxFailedAttemptsAllowed, Integer statusCode) {
        if (ZERO.equals(remainingFailedAttempts)) {
            return ZERO;
        }
        return (SUCCESS.getId().equals(statusCode)) ? maxFailedAttemptsAllowed : remainingFailedAttempts - 1;
    }

    private void updateRemainingAttempts(GlobalUser globalUser, Integer remainingFailedAttempts) {
        globalUser.setRemainingFailedAttemptsAllowed(remainingFailedAttempts);
        userGlobalDBService.updateGlobalUser(globalUser);
    }

    private Integer actOnRemainingFailedAttempts(boolean loginSucess, GlobalUser globalUser) {
        Integer remainingFailedAttemptsAllowed = globalUser.getRemainingFailedAttemptsAllowed();
        if (ZERO.equals(remainingFailedAttemptsAllowed)) {
            return LoginStatusCode.ACCOUNT_LOCKED_DUE_TO_WRONG_PASSWORD.getId();
        }
        if (loginSucess) {
            return SUCCESS.getId();
        }
        if (LAST_ATTEMPT.equals(remainingFailedAttemptsAllowed)) {
            userGlobalDBService.deactivateGlobalUser(globalUser);
            return LoginStatusCode.ACCOUNT_LOCKED_DUE_TO_WRONG_PASSWORD.getId();
        }
        return LoginStatusCode.INVALID_CREDENTIALS.getId();
    }


    public Integer validateToken(String token) {
        Integer noValidUser = Integer.valueOf(-1);
        Integer inactiveUser = Integer.valueOf(-2);
        Integer linkExpired = Integer.valueOf(-3);
        String decrypt = EncryptionDecryption.doStrongTextDecryption(token);
        String[] split = decrypt.split("--");
        GlobalUser user = userGlobalDBService.getGlobalUserByEmail(split[0]);
        if (null == user) {
            return noValidUser;
        }
        if (!user.isActive()) {
            return inactiveUser;
        }
        if (!isValidTimeStamp(user, split[2])) {
            return linkExpired;
        }
        Integer userId = user.getId();
        return userId.equals(Integer.valueOf(split[1])) ? userId : noValidUser;
    }

    private boolean isValidTimeStamp(GlobalUser globalUser, String timeStamp) {
        long linkTimeStamp = Long.valueOf(timeStamp);
        Timestamp lastPasswordChangeDttm = globalUser.getLastPasswordChangeDttm() != null ? Timestamp.valueOf(globalUser.getLastPasswordChangeDttm()) : null;
        long timeStampOfLastPasswordChanged = 0;
        if (null != lastPasswordChangeDttm) {
            timeStampOfLastPasswordChanged = lastPasswordChangeDttm.getTime();
        }
        return !(isLinkExpired(new Date().getTime(), linkTimeStamp)) && timeStampOfLastPasswordChanged < linkTimeStamp;
    }

    private boolean isLinkExpired(long currentTimeStamp, long linkTimeStamp) {
        double difference = (currentTimeStamp - linkTimeStamp) / (60 * 60 * 1000.0);
        return difference > 24;
    }

    public boolean shouldUserChangePassword(String email) {
        LoginResultInfo authenticate = authenticate(true, email);
        Integer statusCode = authenticate.getStatusCode();
        return (statusCode.equals(LoginStatusCode.FIRST_TIME_USER_SHOULD_CHANGE_PASSWORD.getId()) ||
                statusCode.equals(LoginStatusCode.PASSWORD_EXPIRED.getId()));

    }
}
