package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.datafeed.endpoint.DatafeedEndPointCriteria;
import com.ideas.tetris.pacman.services.datafeed.endpoint.EndpointBucket;
import com.ideas.tetris.pacman.services.datafeed.endpoint.EndpointFrequencyType;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;

import java.util.Set;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class DatafeedEndPointCriteriaService {

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    PricingConfigurationService pricingConfigurationService;

    public DatafeedEndPointCriteria getDatafeedEndPointCriteria(Set<EndpointFrequencyType> frequencyTypes, String clientCode) {
        return getDatafeedEndPointCriteria(IntegrationConfigParamName.DATAFEED_BUCKET.value(), frequencyTypes, clientCode);
    }

    public DatafeedEndPointCriteria getDatafeedEndPointCriteria(String configParamName, Set<EndpointFrequencyType> frequencyTypes, String clientCode) {
        final DatafeedEndPointCriteria.DatafeedEndPointCriteriaBuilder datafeedEndPointCriteriaBuilder = new DatafeedEndPointCriteria.DatafeedEndPointCriteriaBuilder(clientCode, frequencyTypes,
                getPropertyLevelBuckets(configParamName), isContinuousPricingEnabled(), isSystemHealthEnabled(), isSupplementsEnabled(), isPercentageColumnCPSupplementEnabled(),isIndependentProductsEnabled(), isSpecialUseRoomTypesDatafeedEnabled(),
                isInventoryLimitEnabled(), isAdditionalInformationalFieldsLastLDBUpdate());
        setCommonFields(datafeedEndPointCriteriaBuilder);
        return datafeedEndPointCriteriaBuilder.build();
    }

    private boolean isSpecialUseRoomTypesDatafeedEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SPECIAL_USE_ROOM_TYPES_DATAFEED_ENABLED);
    }

    private boolean isInventoryLimitEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_INVENTORY_LIMIT_DATAFEED);
    }

    private Boolean isPercentageColumnCPSupplementEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_COlUMN_FOR_CPSUPPLEMENT_DATAFEED);
    }
    private boolean isAdditionalInformationalFieldsLastLDBUpdate() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INCLUDE_ADDITIONAL_INFORMATION_DATAFEED_LIMITED_DATA_BUILD_UPDATE.value());
    }

    private boolean isIndependentProductsEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
    }

    public DatafeedEndPointCriteria getDatafeedEndPointCriteriaOptionalCore(String configParamName, Set<EndpointFrequencyType> frequencyTypes, String clientCode, boolean includeCore, boolean isFirstUpload) {
        final DatafeedEndPointCriteria.DatafeedEndPointCriteriaBuilder datafeedEndPointCriteriaBuilder = new DatafeedEndPointCriteria.DatafeedEndPointCriteriaBuilder(clientCode, frequencyTypes,
                getPropertyLevelBuckets(configParamName, includeCore), isContinuousPricingEnabled(), isSystemHealthEnabled(), isSupplementsEnabled(), isPercentageColumnCPSupplementEnabled(),
                isIndependentProductsEnabled(), isSpecialUseRoomTypesDatafeedEnabled(),
                isInventoryLimitEnabled(), isAdditionalInformationalFieldsLastLDBUpdate());
        setCommonFields(datafeedEndPointCriteriaBuilder);
        datafeedEndPointCriteriaBuilder.setFirstUpload(isFirstUpload);
        return datafeedEndPointCriteriaBuilder.build();
    }


    private void setCommonFields(DatafeedEndPointCriteria.DatafeedEndPointCriteriaBuilder datafeedEndPointCriteriaBuilder) {
        datafeedEndPointCriteriaBuilder.agileRatesEnabled(isAgileRatesEnabled());
        datafeedEndPointCriteriaBuilder.isLDBProjectionEnabled(isLDBProjectionEnabled());
        boolean savedGroupEvaluationEnabled = isSavedGroupEvaluationEnabled();
        boolean groupEvaluationIncludesBookingId = isGroupEvaluationIncludesBookingId();
        datafeedEndPointCriteriaBuilder.setGroupEvaluationEnabled(savedGroupEvaluationEnabled && !groupEvaluationIncludesBookingId);
        datafeedEndPointCriteriaBuilder.setGroupEvaluationIncludesBookingId(savedGroupEvaluationEnabled && groupEvaluationIncludesBookingId);
        datafeedEndPointCriteriaBuilder.setAgileRatesProductConfigurationEnabled(isAgileRatesProductConfigurationEnabled());
        datafeedEndPointCriteriaBuilder.setSendDecisionAdjustmentEnabled(isSendDecisionAdjustmentEnabled());
        datafeedEndPointCriteriaBuilder.setGroupPricingEnabled(isGroupPricingEnabled());
        datafeedEndPointCriteriaBuilder.setGroupPricingMinProfitEnabled(isGroupPricingMinProfitEnabled());
        datafeedEndPointCriteriaBuilder.setFunctionSpaceEnabled(isFunctionSpaceEnabled());
        datafeedEndPointCriteriaBuilder.setGroupFinalForecastEnabled(isGroupFinalForecastEnabled());
        datafeedEndPointCriteriaBuilder.setPropertySpecificNewColumnsEnabled(isPropertySpecificNewColumnsEnabled());
        datafeedEndPointCriteriaBuilder.setPropertySpecificUpdatedColumnsEnabled(isPropertySpecificUpdatedColumnsEnabled());
        datafeedEndPointCriteriaBuilder.setProfitPopulationEnabled(isProfitPopulationEnabled());
        datafeedEndPointCriteriaBuilder.setProfitMetricsDatafeedEnabled(isProfitMetricsDatafeedEnabled());
        datafeedEndPointCriteriaBuilder.setOutOfOrderOverridesEnabled(isOutOfOrderOverrideEnabled());
        datafeedEndPointCriteriaBuilder.setTaxInclusiveConfigurationEnabled(isTaxInclusiveConfigurationEnabled());
        datafeedEndPointCriteriaBuilder.setPaceWebRateEnabled(isPaceWebRateEnabled());
        datafeedEndPointCriteriaBuilder.setAgileProductOptimizationFilesEnabled(isAgileProductOptimizationFilesEnabled());
        datafeedEndPointCriteriaBuilder.setAgileIndependentProductHierarchyColumnEnabled(agileIndependentProductHierarchyColumnEnabled());
        datafeedEndPointCriteriaBuilder.setEnhancedInventoryHistoryEnabled(isEnhancedInventoryHistoryEnabled());
        datafeedEndPointCriteriaBuilder.setStrEnabled(isStrEnabled());
        datafeedEndPointCriteriaBuilder.setDemand360Enabled(isDemand360Enabled());
        datafeedEndPointCriteriaBuilder.setRateProtectEnabled(isRateProtectEnabled());
        datafeedEndPointCriteriaBuilder.setExtendedStayUnqualifiedRateManagementEnabled(isExtendedStayUnqualifiedRateManagementEnabled());
        datafeedEndPointCriteriaBuilder.setUseUniqueUserIdInsteadOfEmail(isUseUserIdInsteadOfEmailId());
        datafeedEndPointCriteriaBuilder.setVirtualPropertyMappingForHiltonEnabled(isVirtualPropertyMappingForHiltonEnabled());
        datafeedEndPointCriteriaBuilder.setSt19ForDatafeedMSRTEnabled(isSt19ForDatafeedMSRTEnabled());
        datafeedEndPointCriteriaBuilder.setAgeBasedPricingForHiltonEnabled(isAgeBasedPricingDFEnabledForHilton());
        datafeedEndPointCriteriaBuilder.setEnhancedProductPackageElementDFEnabled(isPackageElementsEnhancedDFEnabled());
        datafeedEndPointCriteriaBuilder.setProductRateShopDefinitionEnabled(isProductRateShopDefinitionEnabled());
        datafeedEndPointCriteriaBuilder.setProductRateShopDefinitionEnhancedEnabled(isProductRateShopDefinitionEnhancedEnabled());
        datafeedEndPointCriteriaBuilder.setProductClassificationEnabled(isProductClassificationEnabled());
        datafeedEndPointCriteriaBuilder.setBenefitMeasurementEnabled(isBenefitMeasurementEnabled());
        datafeedEndPointCriteriaBuilder.setBenefitMeasurementWithProfitColumnsEnabled(isBenefitMeasurementWithProfitColumnsEnabled());
        datafeedEndPointCriteriaBuilder.setScheduledReportsEnabled(isScheduledReportsEnabled());
        datafeedEndPointCriteriaBuilder.setChannelForecastEnabled(isChannelForecastEnabled());
        datafeedEndPointCriteriaBuilder.setHiltonConsortiaFreeNightEnabled(isHiltonConsortiaFreeNightEnabled());
        datafeedEndPointCriteriaBuilder.setProductFreeNightDefinitionEnabled(isProductFreeNightDefinitionEnabled());
        datafeedEndPointCriteriaBuilder.setProductGroupProductDefinitionEnabled(isProductGroupProductDefinitionEnabled());
        datafeedEndPointCriteriaBuilder.setGroupPricingSCMarketSegmentMappingEnabled(isGroupPricingSCRoomTypeAndMktSegMappingEnabled());
        datafeedEndPointCriteriaBuilder.setGroupPricingSCRoomTypeMappingEnabled(isGroupPricingSCRoomTypeAndMktSegMappingEnabled());
        datafeedEndPointCriteriaBuilder.setPercentageColumnForCPSupplementOfDatafeedEnabled(isPercentageColumnCPSupplementEnabled());
        datafeedEndPointCriteriaBuilder.setIsIndependentProductsEnabled(isIndependentProductsEnabled());
        datafeedEndPointCriteriaBuilder.setIsInventoryLimitEnabled(isInventoryLimitEnabled());
        datafeedEndPointCriteriaBuilder.setLastLDBUpdateColumnForPropertyBasicInformation(isAdditionalInformationalFieldsLastLDBUpdate());
        datafeedEndPointCriteriaBuilder.setIsSpecialUseRoomTypesEnabled(isSpecialUseRoomTypesDatafeedEnabled());
        datafeedEndPointCriteriaBuilder.setIsRateShoppingOccupancyBasedCMPCDatafeedEnabled(isRateShoppingOccupancyBasedCMPCDatafeedEnabled());
        datafeedEndPointCriteriaBuilder.setIsRateShoppingIgnoreChannelConfigDatafeedEnabled(isRateShoppingIgnoreChannelConfigDatafeedEnabled());
        datafeedEndPointCriteriaBuilder.setIsChannelColumnEnabledInIgnoreCompetitor(isChannelColumnEnabledInIgnoreCompetitor());
        datafeedEndPointCriteriaBuilder.setIsDiscontinuedRoomTypeInRoomClassDatafeedEnabled(isDiscontinuedRTEnabledInRoomClassConfigDataFeed());
        datafeedEndPointCriteriaBuilder.setIsMeetingPackagePricingDataFeedEnabled(isMeetingPackagePricingDataFeedEnabled());
        datafeedEndPointCriteriaBuilder.setIsWindowSettingsEnabledInInformationalDataFeed(isWindowSettingsEnabledInInformationalDataFeed());
    }

    public DatafeedEndPointCriteria getDatafeedEndPointCriteria(Set<EndpointFrequencyType> frequencyTypes, String clientCode, boolean isClientLevelRequest) {
        return getDatafeedEndPointCriteria(IntegrationConfigParamName.DATAFEED_BUCKET.value(), frequencyTypes, clientCode, isClientLevelRequest);
    }

    public DatafeedEndPointCriteria getDatafeedEndPointCriteria(String configParamName, Set<EndpointFrequencyType> frequencyTypes, String clientCode, boolean isClientLevelRequest) {
        DatafeedEndPointCriteria.DatafeedEndPointCriteriaBuilder datafeedEndPointCriteriaBuilder = new DatafeedEndPointCriteria.DatafeedEndPointCriteriaBuilder(isClientLevelRequest, clientCode, frequencyTypes, getClientLevelBuckets(configParamName, clientCode));
        datafeedEndPointCriteriaBuilder.setVirtualPropertyMappingForHiltonEnabled(isVirtualPropertyMappingForHiltonEnabled());
        datafeedEndPointCriteriaBuilder.setUseUniqueUserIdInsteadOfEmail(isUseUserIdInsteadOfEmailId());
        datafeedEndPointCriteriaBuilder.setRolePermissionWithRankColumnEnabled(isRolePermissionWithRankColumnEnabled(clientCode));
        datafeedEndPointCriteriaBuilder.setIsPropertyCodeEnabledInUserReportDatafeed(isPropertyCodeEnabledInUserReportDatafeed(clientCode));
        return datafeedEndPointCriteriaBuilder.build();
    }

    private boolean isContinuousPricingEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value());
    }

    private boolean isSendDecisionAdjustmentEnabled() {
        Boolean isHiltonOptionSendAdjustmentEnabled = pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED);
        Boolean isSendDecisionAdjustmentEnabled = pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_SEND_DECISION_ADJUSTMENT_DATAFEED_FILE);
        return isHiltonOptionSendAdjustmentEnabled && isSendDecisionAdjustmentEnabled;
    }

    private boolean isAgileRatesEnabled() {
        return pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED);
    }

    private boolean isSystemHealthEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SHOW_SYSTEM_HEALTH_FOR_EXTERNAL_USER.value());
    }

    private boolean isSupplementsEnabled() {
        return pricingConfigurationService.findCPConfiguration() != null && pricingConfigurationService.findCPConfiguration().isEnableSupplements();
    }

    private boolean isGroupFinalForecastEnabled() {
        return pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.GROUP_FINAL_FORECAST_OVERRIDE_ENABLED);
    }

    private boolean isOutOfOrderOverrideEnabled() {
        Boolean isOverrideDatafeedEnabled = pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.ENABLE_OOO_OVERRIDE_DATAFEED);
        Boolean isOutOfOrderOverrideEnabled = pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.OUT_OF_ORDER_OVERRIDES_ENABLED);
        return isOverrideDatafeedEnabled && isOutOfOrderOverrideEnabled;
    }

    private boolean isTaxInclusiveConfigurationEnabled() {
        Boolean isTaxValueEnabled = pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.ENABLE_TAX_VALUE_DATAFEED);
        Boolean isCPEnabled = pacmanConfigParamsService.getParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
        return isTaxValueEnabled && isCPEnabled;
    }

    private boolean isPaceWebRateEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.OPTIX_WEBRATE_DATAFEED_ENABLED);
    }

    private boolean isPropertySpecificNewColumnsEnabled() {
        return pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.ENABLE_PROPERTY_SPECIFIC_DATAFEED_NEW_COLUMNS);
    }

    private boolean isPropertySpecificUpdatedColumnsEnabled() {
        return pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.IS_PRICE_DROP_RESTRICTIONS_DATAFEED_ENABLED);
    }

    private boolean isLDBProjectionEnabled() {
        Boolean isLDBEnabled = pacmanConfigParamsService.getParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED);
        Boolean isLDBProjectionsDatafeedEnabled = pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.LDB_PROJECTION_DATAFEED_ENABLED);
        return isLDBEnabled && isLDBProjectionsDatafeedEnabled;
    }

    private boolean isSavedGroupEvaluationEnabled() {
        if (pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.ENABLE_GROUP_EVALUATION_IN_DATA_FEED))
            return pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED);
        return false;
    }

    private boolean isGroupEvaluationIncludesBookingId() {
        return pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.ENABLE_BOOKING_ID_COLUMN_IN_GROUP_EVALUATION_FILE);
    }

    private boolean isAgileRatesProductConfigurationEnabled() {
        return pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.AGILE_RATES_PRODUCT_CONFIGURATION_ENABLED);
    }

    private boolean isAgeBasedPricingDFEnabledForHilton() {
        Boolean isHiltonAgeBasedPricingDFEnabled = pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.HILTON_AGE_BASED_PRICING_DATAFEED_ENABLED);
        Boolean isHiltonAgeBasedPricingEnabled = pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_AGE_BASED_PRICING_ENABLED);
        return isHiltonAgeBasedPricingDFEnabled && isHiltonAgeBasedPricingEnabled;
    }

    private boolean isPackageElementsEnhancedDFEnabled() {
        return pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.ENHANCED_PACKAGE_ELEMENT_DF_ENABLED);
    }

    private boolean isGroupPricingEnabled() {
        return pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED);
    }

    private boolean isGroupPricingMinProfitEnabled() {
        return pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.GP_MIN_PROFIT_CONFIGURATION_DATAFEED_FILES);
    }

    private boolean isFunctionSpaceEnabled() {
        return pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED);
    }

    private boolean isProfitPopulationEnabled() {
        return pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.ENABLE_PROFIT_POPULATION);
    }

    private boolean isProfitMetricsDatafeedEnabled() {
        return pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.PROFIT_METRICS_DATAFEED_ENABLED);
    }

    private boolean isAgileProductOptimizationFilesEnabled() {
        return pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED);
    }

    private boolean agileIndependentProductHierarchyColumnEnabled() {
        return pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.HIERARCHY_INDEPENDENT_PRODUCT_COLUMN_ENABLED);
    }

    private boolean isVirtualPropertyMappingForHiltonEnabled() {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(PreProductionConfigParamName.VIRTUAL_PROPERTY_MAPPING_ENABLED.getParameterName()));
    }

    private boolean isSt19ForDatafeedMSRTEnabled() {
        return pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.ENABLE_ST19_FOR_DATAFEED_MSRT);
    }

    private boolean isEnhancedInventoryHistoryEnabled() {
        return pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.ENABLE_INVENTORY_HISTORY_FILE_ADJUSTMENT_COLUMNS);
    }

    private boolean isStrEnabled() {
        return pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.STRENABLED);
    }

    private boolean isDemand360Enabled() {
        return null != pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.DEMAND360SUBSCRIBER_PROPERTY_ID);
    }

    private boolean isRateProtectEnabled() {
        return pacmanConfigParamsService.getParameterValue(PreProductionConfigParamName.RATE_PROTECT_FOR_DATAFEED_ENABLED);
    }

    private boolean isExtendedStayUnqualifiedRateManagementEnabled() {
        return pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED);
    }

    private boolean isUseUserIdInsteadOfEmailId() {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.USE_UNIQUE_USERID_INSTEADOF_EMAILID.getParameterName()));
    }

    private boolean isProductRateShopDefinitionEnabled() {
        if (!pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_RATE_SHOP_DEFINITION)) {
            return false;
        }

        boolean isCPEnabled = pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
        return isCPEnabled;
    }

    private boolean isProductRateShopDefinitionEnhancedEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_RDL_RATE_TYPE_COLUMN_TO_PRSD_FILE);
    }

    private boolean isProductClassificationEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_CLASSIFICATION) &&
                pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
    }

    private boolean isBenefitMeasurementEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.BENEFIT_MEASUREMENT_DATAFEED_ENABLED);
    }

    private boolean isBenefitMeasurementWithProfitColumnsEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PROFIT_COLUMNS_TO_BM_FILE);
    }

    private boolean isScheduledReportsEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SCHEDULED_REPORTS_DATAFEED_ENABLED);
    }

    private boolean isChannelForecastEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_CHANNEL_FORECAST_DATAFEED_ENABLED);
    }

    private boolean isHiltonConsortiaFreeNightEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_CONSORTIA_FREE_NIGHT_ENABLED);
    }

    private boolean isProductFreeNightDefinitionEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_FREE_NIGHT_DEFINITION_DATAFEED_FILE);
    }

    private boolean isProductGroupProductDefinitionEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED) &&
                pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_GROUP_PRODUCT_DEFINITION_DATAFEED);
    }

    private boolean isGroupPricingSCRoomTypeAndMktSegMappingEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED) &&
                pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_SNC_MSRT_MAP_DATAFEED);
    }

    private boolean isRolePermissionWithRankColumnEnabled(String clientCode) {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(PreProductionConfigParamName.ENABLE_RANK_COLUMN_TO_ROLE_FILE.getParameterName(), clientCode));
    }

    public Set<EndpointBucket> getClientLevelBuckets(String clientCode) {
        return getClientLevelBuckets(IntegrationConfigParamName.DATAFEED_BUCKET.value(), clientCode);
    }

    public Set<EndpointBucket> getClientLevelBuckets(String configParamName, String clientCode) {
        String paramValue = pacmanConfigParamsService.getParameterValueByClientLevel(configParamName, clientCode);
        return EndpointBucket.getBuckets(paramValue);
    }


    public Set<EndpointBucket> getPropertyLevelBuckets() {
        return getPropertyLevelBuckets(IntegrationConfigParamName.DATAFEED_BUCKET.value());
    }

    public Set<EndpointBucket> getPropertyLevelBuckets(String configParamName) {
        String paramValue = pacmanConfigParamsService.getParameterValue(configParamName);
        return EndpointBucket.getBuckets(paramValue);
    }

    public Set<EndpointBucket> getPropertyLevelBuckets(String configParamName, boolean includeCore) {
        String paramValue = pacmanConfigParamsService.getParameterValue(configParamName);
        return EndpointBucket.getBuckets(paramValue, includeCore);
    }

    private boolean isRateShoppingOccupancyBasedCMPCDatafeedEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_RATE_SHOPPING_OCCUPANCY_BASED_CMPC_DATAFEED_ENABLED);
    }

    private boolean isPropertyCodeEnabledInUserReportDatafeed(String clientCode) {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(PreProductionConfigParamName.IS_PROPERTY_CODE_ENABLED_IN_USER_REPORT_DATAFEED.getParameterName(), clientCode));
    }

    public boolean isRateShoppingIgnoreChannelConfigDatafeedEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_RATE_SHOPPING_IGNORE_CHANNEL_CONFIG_DATAFEED_ENABLED);
    }
    public boolean isChannelColumnEnabledInIgnoreCompetitor() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_CHANNEL_ENABLED_IN_IGNORE_COMPETITOR_DATAFEED);
    }

    public boolean isDiscontinuedRTEnabledInRoomClassConfigDataFeed() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DISCONTINUED_ROOM_TYPE_ENABLED_IN_ROOM_CLASS_CONFIG_DATAFEED);
    }

    public boolean isMeetingPackagePricingDataFeedEnabled() {
        Boolean isMeetingPackagePricingEnabled = pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.MEETING_PACKAGE_PRICING_ENABLED);
        return isMeetingPackagePricingEnabled &&
                pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_MEETING_PACKAGE_PRICING_DATAFEED_ENABLED);
    }

    private boolean isWindowSettingsEnabledInInformationalDataFeed() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_WINDOW_SETTINGS_ENABLED_IN_INFORMATIONAL_DATAFEED);
    }
}