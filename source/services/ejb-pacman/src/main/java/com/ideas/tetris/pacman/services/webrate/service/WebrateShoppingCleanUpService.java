package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.fds.G3SNSService;
import com.ideas.tetris.pacman.common.fds.dto.EventType;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.extendedstayrateshopping.entity.ExtendedStayCompetitorMapping;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.notification.interfaces.ParameterType;
import com.ideas.tetris.pacman.services.informationmanager.service.AsyncInformationManagerCleanupService;
import com.ideas.tetris.pacman.services.informationmanager.service.InformationManagerCleanupService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.rateshoppingadjustment.entity.RateShoppingAdjustment;
import com.ideas.tetris.pacman.services.rdl.entity.RDLCustomRoomDescription;
import com.ideas.tetris.pacman.services.rdl.entity.RDLShopAttribute;
import com.ideas.tetris.pacman.services.rra.entity.RRACompetitor;
import com.ideas.tetris.pacman.services.webrate.entity.*;
import com.ideas.tetris.pacman.services.webrate.vo.WebRateDetailsVO;
import com.ideas.tetris.pacman.services.webrate.vo.WebrateAccomTypeVO;
import com.ideas.tetris.pacman.services.webrate.vo.WebrateChannelVO;
import com.ideas.tetris.pacman.services.webrate.vo.WebrateCompetitorsVO;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.entity.IdAware;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.regulator.service.RegulatorService;
import com.ideas.tetris.platform.services.regulator.service.spring.RegulatorSpringService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.CONTEXT_KEY;
import static com.ideas.tetris.pacman.common.constants.NotificationKeyConstants.VENDOR_ID_KEY;
import static com.ideas.tetris.pacman.services.rateshopper.RateShopperServiceQueryConstants.GET_LIST_OF_WEBRATE_GENERATION_TO_BE_MIGRATED;
import static com.ideas.tetris.pacman.services.webrate.service.WebrateShoppingVendorMappingService.ID_LIST;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.*;

@Slf4j
@Component
@Transactional
public class WebrateShoppingCleanUpService {

    public final static Logger LOGGER = Logger.getLogger(WebrateShoppingCleanUpService.class);

    private static final String PACE_WEBRATE_DISABLE_INDEX_QUERY = "ALTER INDEX %s ON PACE_Webrate DISABLE";
    private static final String PACE_WEBRATE_CHUNK_CLEANUP_QUERY = "delete top(%s) p from PACE_Webrate p where %s in (:idsList)";
    private static final String PACE_WEBRATE_DIFFERENTIAL_CHUNK_CLEANUP_QUERY = "delete top(%s) p from PACE_Webrate_Differential p where %s in (:idsList)";
    private static final String PACE_WEBRATE_CLEAN_UP_QUERY = "delete from PACE_Webrate where %s=:columnCondition";
    private static final String WEBRATE_CLEAN_UP_QUERY = "delete from Webrate where %s=:columnCondition";
    private static final String RDL_CUSTOM_ROOM_DESCRIPTION_CLEAN_UP_QUERY = "delete from RDL_Custom_Room_Description where %s=:columnCondition";
    public static final String WEBRATE_COMPETITORS_ID = "Webrate_Competitors_ID";
    public static final String WEBRATE_CHANNEL_ID = "Webrate_Channel_ID";
    public static final String WEBRATE_ACCOM_TYPE_ID = "Webrate_Accom_Type_ID";
    public static final String WEBRATE_ACCOM_TYPE_NAME = "Webrate_Accom_Type_Name";
    public static final String PACE_WEBRATE_COUNT_QUERY = "select COUNT(*) from PACE_Webrate where %s in (:idsList)";
    public static final String PACE_WEBRATE_DIFFERENTIAL_COUNT_QUERY = "select COUNT(*) from PACE_Webrate_Differential where %s in (:idsList)";
    public static final String RRA_COMPETITOR_ID = "RRA_Competitor_ID";
    public static final String RRA_SCORE_CLEANUP_QUERY = "delete from RRA_SCORE where %s=:columnCondition";
    public static final String RRA_SOURCE_PROPERTY_CLEANUP_QUERY = "delete from RRA_SOURCE_PROPERTY where %s=:columnCondition";
    private static final String WEBRATE_CHANNEL = "webrateChannel";

    @Autowired
    private InformationManagerCleanupService informationManagerCleanupService;

    @Lazy
    @Autowired
    private AsyncInformationManagerCleanupService asyncInformationManagerCleanupService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    protected CrudService tenantCrudService;

    @Autowired
    private PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    JobServiceLocal jobService;

    @Autowired
    RegulatorService regulatorService;
    @Autowired
    RegulatorSpringService regulatorSpringService;
    @Autowired
    CompetitorDataFilterService competitorDataFilterService;
    @Autowired
    WebrateShoppingDataService webrateShoppingDataService;

    @Autowired
    private SyncEventAggregatorService syncEventAggregatorService;

    @Autowired
    private PropertyService propertyService;

    @Autowired
    private G3SNSService g3SNSService;

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    public void cleanUpWebrateCompetitors(List<WebrateCompetitors> webrateCompetitors) {
        if (webrateCompetitors.isEmpty()) {
            return;
        }

        List listWebrateCompetitorIds = getWebrateIdsList(webrateCompetitors);
        startCleanupJob(JobName.WebrateCompetitorCleanupJob, WEBRATE_COMPETITORS_ID, listWebrateCompetitorIds);

    }


    public boolean isWebrateCleanupJobRunning(JobName jobName) {
        String regulatorContext = Constants.getRegulatorContext(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
        if (regulatorService.isSpringTXEnableRegulatorService(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())) {
            List<String> runningServiceNames = regulatorSpringService.getRunningServiceNamesForContextAndServiceNames(regulatorContext, Arrays.asList(jobName.name()));
            return CollectionUtils.isNotEmpty(runningServiceNames);
        } else {
            List<String> runningServiceNames = regulatorService.getRunningServiceNamesForContextAndServiceNames(regulatorContext, Arrays.asList(jobName.name()));
            return CollectionUtils.isNotEmpty(runningServiceNames);
        }

    }


    public Long startCleanupJob(JobName cleanupJob, String columnName, List listOfWebrateIds) {
        WebRateDetailsVO webRateDetailsVO = new WebRateDetailsVO();
        webRateDetailsVO.setWebrateIdsList(listOfWebrateIds);
        return startJob(cleanupJob, columnName, webRateDetailsVO);
    }

    public Long startCleanupAndUpdateJob(JobName cleanupJob, String columnName, List listOfWebrateIds, Map webrateChannelMap, Integer newWebrateSourcePropertyId) {
        WebRateDetailsVO webRateDetailsVO = new WebRateDetailsVO();
        webRateDetailsVO.setWebrateIdsList(listOfWebrateIds);
        webRateDetailsVO.setWebRateMap((HashMap) webrateChannelMap);
        webRateDetailsVO.setNewWebrateSourcePropertyId(newWebrateSourcePropertyId);
        return startJob(cleanupJob, columnName, webRateDetailsVO);
    }

    public void cleanupAndUpdateWebrateAccomTypesJob(List listOfAccomTypeToBeClean, Map<WebrateAccomTypeVO, WebrateAccomTypeVO> webrateAccomTypesMapToUpdated, Integer newWebrateSourcePropertyId) {
        if (!listOfAccomTypeToBeClean.isEmpty()) {
            startCleanupAndUpdateJob(JobName.WebrateAccomTypeCleanupJob, WEBRATE_ACCOM_TYPE_ID, listOfAccomTypeToBeClean, webrateAccomTypesMapToUpdated, newWebrateSourcePropertyId);
        }
    }

    public void cleanupAndUpdateWebrateCompetitorsJob(List listOfCompetitorIdsToBeClean, Map<WebrateCompetitorsVO, WebrateCompetitorsVO> webrateCompetitorsMapToUpdated, Integer newWebrateSourcePropertyId) {
        if (!listOfCompetitorIdsToBeClean.isEmpty()) {
            startCleanupAndUpdateJob(JobName.WebrateCompetitorCleanupJob, WEBRATE_COMPETITORS_ID, listOfCompetitorIdsToBeClean, webrateCompetitorsMapToUpdated, newWebrateSourcePropertyId);
        }
    }

    public void cleanupAndUpdateWebrateChannelsJob(List listOfChannelIdsToBeClean, Map<WebrateChannelVO, WebrateChannelVO> webrateChannelsMapToUpdated, Integer newWebrateSourcePropertyId) {
        if (!listOfChannelIdsToBeClean.isEmpty()) {
            startCleanupAndUpdateJob(JobName.WebrateChannelCleanupJob, WEBRATE_CHANNEL_ID, listOfChannelIdsToBeClean, webrateChannelsMapToUpdated, newWebrateSourcePropertyId);
        }
    }

    private Long startJob(JobName cleanupJob, String columnName, WebRateDetailsVO webRateDetailsVO) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.CLIENT_CODE, PacmanWorkContextHelper.getClientCode());
        parameters.put(JobParameterKey.PROPERTY_CODE, PacmanWorkContextHelper.getPropertyCode());
        parameters.put(JobParameterKey.CORRELATION_ID, UUID.randomUUID());
        parameters.put(JobParameterKey.COLUMN, columnName);
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, webRateDetailsVO);
        parameters.put(JobParameterKey.CHUNK_SIZE, SystemConfig.getDataCleanupJobChunkSize());
        return jobService.startJob(cleanupJob, parameters);
    }

    protected List getWebrateIdsList(List<? extends IdAware> idAwareEntities) {
        return idAwareEntities.stream()
                .map(IdAware::getId)
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    public void cleanUpWebrateCompetitorsBasedOnCompetitorId(List<String> webrateCompetitorIds) {
        webrateCompetitorIds.stream()
                .mapToInt(Integer::parseInt)
                .forEach(id -> {
                    WebrateCompetitors webrateCompetitor = tenantCrudService.find(WebrateCompetitors.class, id);
                    if (webrateCompetitor != null) {
                        cleanUpWebrateCompetitor(webrateCompetitor, true);
                    }
                });
        publishCompSetCRTMappingChangeSnsEvent();
    }

    private void publishCompSetCRTMappingChangeSnsEvent() {
        if (isRDLEnabled()) {
            publishCompSetChangeSnsEvent();
        }

        publishCRTMappingChangeSnsEvent();
    }

    private void publishCRTMappingChangeSnsEvent() {
        if (isRDLEnabled() && isRDLNormalizedRateProcessingEnabled()) {
            publishCompetitorCRTMappingUpdatedSnsEvent();
        }
    }

    public void cleanUpWebrateCompetitor(WebrateCompetitors webrateCompetitor, boolean asyncWebrateCleanupEnabled) {
        Integer webRateCompetitorID = webrateCompetitor.getId();
        try {
            cleanUpWebrateData(WEBRATE_COMPETITORS_ID, webRateCompetitorID);
            if (!asyncWebrateCleanupEnabled) {
                cleanUpPaceWebrateData(WEBRATE_COMPETITORS_ID, webRateCompetitorID);
            }
            List<ExtendedStayCompetitorMapping> extendedStayCompetitorMappings = tenantCrudService.findByNamedQuery(ExtendedStayCompetitorMapping.BY_COMPETITOR_ID, QueryParameter.with("webRateCompetitorID", webRateCompetitorID).parameters());
            List<RateShoppingAdjustment> rateShoppingAdjustments = tenantCrudService.findByNamedQuery(RateShoppingAdjustment.BY_COMPETITOR_ID, QueryParameter.with("competitorId", webRateCompetitorID).parameters());
            List<WebrateOverrideCompetitor> webrateOverrideCompetitors = tenantCrudService.findByNamedQuery(WebrateOverrideCompetitorDetails.COMP_OVRD_BY_COMP_ID,
                    QueryParameter.with("webrateCompetitorId", webRateCompetitorID).parameters());
            Set<WebrateOverrideCompetitor> uniqueWebrateOverrideCompetitors = new HashSet<>(webrateOverrideCompetitors);
            RRACompetitor rraCompetitor = tenantCrudService.findByNamedQuerySingleResult(RRACompetitor.BY_COMPETITOR_ID, QueryParameter.with("competitorId", webRateCompetitorID).parameters());
            List<RDLShopAttribute> rdlShopAttributes = tenantCrudService.findByNamedQuery(RDLShopAttribute.BY_WEBRATE_COMPETITOR_ID,
                    QueryParameter.with("webrateCompetitorId", webRateCompetitorID).parameters());
            List<RDLCustomRoomDescription> rdlCustomRoomDescriptions = tenantCrudService.findByNamedQuery(RDLCustomRoomDescription.BY_COMPETITOR,
                    QueryParameter.with("competitorId", webRateCompetitorID).parameters());
            tenantCrudService.delete(rdlCustomRoomDescriptions);
            tenantCrudService.delete(rdlShopAttributes);
            tenantCrudService.delete(extendedStayCompetitorMappings);
            tenantCrudService.delete(rateShoppingAdjustments);

            cleanupWebrateCompetitorChannelMappings(uniqueWebrateOverrideCompetitors);

            tenantCrudService.delete(uniqueWebrateOverrideCompetitors);
            tenantCrudService.executeUpdateByNativeQuery("Delete From Central_RMS_Comp_Outlier Where Webrate_Competitors_ID = " + webRateCompetitorID);
            if (rraCompetitor != null) {
                cleanupRRAScore(RRA_COMPETITOR_ID, rraCompetitor.getId());
                cleanupRRASourceProperty(RRA_COMPETITOR_ID, rraCompetitor.getId());
                tenantCrudService.delete(rraCompetitor);
            }
            resetBarOverrideCompetitor(webrateCompetitor);
            cleanUpInformationManagerData(webrateCompetitor);
            tenantCrudService.delete(webrateCompetitor);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting webrate competitor: " + webrateCompetitor.getWebrateCompetitorsName() + ". Reason: " + e.getMessage(), e);
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Error occurred while deleting webrate competitor: " + webrateCompetitor.getWebrateCompetitorsName() + ". Reason: " + e.getMessage());
        }
    }

    private void cleanupWebrateCompetitorChannelMappings(Set<WebrateOverrideCompetitor> uniqueWebrateOverrideCompetitors) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_UI_IGNORE_COMPETITOR) &&
                CollectionUtils.isNotEmpty(uniqueWebrateOverrideCompetitors)) {
            Set<Integer> webrateOverrideCompetitorIds = uniqueWebrateOverrideCompetitors.stream().map(WebrateOverrideCompetitor::getId).collect(Collectors.toSet());
            List<WebrateCompChannelMapping> mappings = tenantCrudService.findByNamedQuery(WebrateCompChannelMapping.BY_WEBRATE_OVR_COMPETITOR_IDS,
                    QueryParameter.with("webrateOverrideCompetitorIds", webrateOverrideCompetitorIds).parameters());
            webrateShoppingDataService.deleteCompChannelMappings(mappings);
        }
    }

    private void cleanupRRASourceProperty(String columnName, Integer columnCondition) {
        tenantCrudService.executeUpdateByNativeQuery(String.format(RRA_SOURCE_PROPERTY_CLEANUP_QUERY, columnName),
                QueryParameter.with("columnCondition", columnCondition).parameters());
    }

    private void cleanupRRAScore(String columnName, Integer columnCondition) {
        tenantCrudService.executeUpdateByNativeQuery(String.format(RRA_SCORE_CLEANUP_QUERY, columnName),
                QueryParameter.with("columnCondition", columnCondition).parameters());
    }

    private void cleanUpInformationManagerData(WebrateCompetitors webrateCompetitor) {
        informationManagerCleanupService.cleanUpConfigurationDataForTypeLevelAndSublevel(PacmanWorkContextHelper.getPropertyId(), AlertType.CompetitorPriceChange.toString(), ParameterType.COMPETITORS.getCode(), webrateCompetitor.getId());
        informationManagerCleanupService.cleanUpConfigurationDataForTypeLevelAndSublevel(PacmanWorkContextHelper.getPropertyId(), AlertType.CompetitorPriceAsOfLastNightlyOptimization.toString(), ParameterType.COMPETITORS.getCode(), webrateCompetitor.getId());
    }

    private void resetBarOverrideCompetitor(WebrateCompetitors webrateCompetitor) {
        String displayCompetitor = pacmanConfigParamsService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value());
        String absoluteCompetitor = pacmanConfigParamsService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_ABSOLUTE_COMPETITOR.value());
        if (Constants.BAR_OVRD_DISPLAY_COMPETITOR_VALUE_ABSOLUTE.equalsIgnoreCase(displayCompetitor) &&
                webrateCompetitor.getWebrateCompetitorsAlias().equalsIgnoreCase(absoluteCompetitor)) {
            pacmanConfigParamsService.deleteParameterValue(getCurrentConfigParamsContext(), IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value(), true);
            pacmanConfigParamsService.deleteParameterValue(getCurrentConfigParamsContext(), IPConfigParamName.BAR_BAR_OVRD_ABSOLUTE_COMPETITOR.value(), true);
        }
    }

    public void cleanUpWebrateChannels(List<WebrateChannel> webrateChannels) {
        if (webrateChannels.isEmpty()) {
            return;
        }
        List listWebrateChannelIds = getWebrateIdsList(webrateChannels);
        startCleanupJob(JobName.WebrateChannelCleanupJob, WEBRATE_CHANNEL_ID, listWebrateChannelIds);
    }

    public void cleanUpWebrateChannelsBasedOnChannelId(List<String> webrateChannelIds) {
        webrateChannelIds.stream()
                .mapToInt(Integer::parseInt)
                .forEach(id -> {
                    WebrateChannel webrateChannel = tenantCrudService.find(WebrateChannel.class, id);
                    if (webrateChannel != null) {
                        cleanUpWebrateChannel(webrateChannel, true);
                    }
                });
        publishCRTMappingChangeSnsEvent();
    }


    public void cleanUpWebrateChannel(WebrateChannel webrateChannel, boolean asyncWebrateCleanupEnabled) {
        cleanUpWebrateData(WEBRATE_CHANNEL_ID, webrateChannel.getId());
        if (!asyncWebrateCleanupEnabled) {
            cleanUpPaceWebrateData(WEBRATE_CHANNEL_ID, webrateChannel.getId());
        }
        List<WebrateDefaultChannel> webrateDefaultChannels = tenantCrudService.findByNamedQuery(WebrateDefaultChannel.BY_WEBRATE_CHANNEL_ID, QueryParameter.with("webrateChannelId", webrateChannel.getId()).parameters());
        List<WebrateOverrideChannel> webrateOverrideChannels = tenantCrudService.findByNamedQuery(WebrateOverrideChannel.BY_WEBRATE_CHANNEL_ID, QueryParameter.with("webrateChannelId", webrateChannel.getId()).parameters());
        List<RDLShopAttribute> rdlShopAttributes = tenantCrudService.findByNamedQuery(RDLShopAttribute.BY_WEBRATE_CHANNEL_ID, QueryParameter.with("webrateChannelId", webrateChannel.getId()).parameters());
        List<RDLCustomRoomDescription> rdlCustomRoomDescriptions = tenantCrudService.findByNamedQuery(RDLCustomRoomDescription.BY_CHANNEL, QueryParameter.with("channelId", webrateChannel.getId()).parameters());
        tenantCrudService.delete(rdlCustomRoomDescriptions);
        tenantCrudService.delete(webrateDefaultChannels);
        tenantCrudService.delete(webrateOverrideChannels);
        tenantCrudService.delete(rdlShopAttributes);

        cleanupWebrateChannelIgnoreCfg(webrateChannel);
        cleanupWebrateCompetitorChannelMappings(webrateChannel);

        tenantCrudService.delete(webrateChannel);
    }

    private boolean isRDLEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);
    }

    private boolean isRDLNormalizedRateProcessingEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_RDL_NORMALIZED_RATE_PROCESSING);
    }

    public void publishCompetitorCRTMappingUpdatedSnsEvent() {
        try {
            if (isTestProperty()) {
                log.info("Skip publishing COMPETITOR_CRT_MAPPING_UPDATED sns event for test property");
                return;
            }
            Property property = propertyService.getPropertyById(PacmanWorkContextHelper.getPropertyId());
            PacmanWorkContextHelper.getWorkContext().setUspId(property.getUpsId());
            g3SNSService.publishToSNS(EventType.COMPETITOR_CRT_MAPPING_UPDATED, prepareEventSource());
        } catch (Exception exception) {
            log.error("Error while publishing COMPETITOR_CRT_MAPPING_UPDATED sns event");
        }
    }

    private boolean isTestProperty() {
        return StringUtils.isNotBlank(pacmanConfigParamsService.getConfigParameterValueAtContext(
                Constants.getPropertyConfigContext(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())
                , FeatureTogglesConfigParamName.RDL_CLONED_FROM_PROPERTY_UPS_ID.value()));
    }

    public void publishCompSetChangeSnsEvent() {
        try {
            if (isTestProperty()) {
                log.info("Skip publishing COMPETITOR_SET_UPDATED sns event for test property");
                return;
            }
            Property property = propertyService.getPropertyById(PacmanWorkContextHelper.getPropertyId());
            PacmanWorkContextHelper.getWorkContext().setUspId(property.getUpsId());
            g3SNSService.publishToSNS(EventType.COMPETITOR_SET_UPDATED, prepareEventSource());
            log.info("COMPETITOR_SET_UPDATED SNS event sent for unifiedPropertyId : {}", property.getUpsId());
        } catch (Exception e) {
            log.error("Error while publishing COMPETITOR_SET_UPDATED SNS event to RDL: {}", e.getMessage());
            throw new TetrisException(e.getMessage());
        }
    }

    private Map<String, Object> prepareEventSource() {
        Map<String, Object> eventSource = new HashMap<>();
        eventSource.put(VENDOR_ID_KEY, pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.RDL_DEFAULT_VENDOR));
        return eventSource;
    }

    private void cleanupWebrateChannelIgnoreCfg(WebrateChannel webrateChannel) {
        if (pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_IGNORE_WEBRATE_CHANNEL)) {
            List<WebrateChannelIgnoreConfig> webrateChannelIgnoreCfg = tenantCrudService.findByNamedQuery(
                    WebrateChannelIgnoreConfig.GET_BY_CHANNEL, QueryParameter.with(WEBRATE_CHANNEL, webrateChannel).parameters());
            if (CollectionUtils.isNotEmpty(webrateChannelIgnoreCfg)) {
                tenantCrudService.delete(webrateChannelIgnoreCfg);
            }
        }
    }

    private void cleanupWebrateCompetitorChannelMappings(WebrateChannel webrateChannel) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_UI_IGNORE_COMPETITOR)) {
            List<WebrateCompChannelMapping> competitorChannelMappingsToDelete = tenantCrudService.findByNamedQuery(
                    WebrateCompChannelMapping.BY_WEBRATE_CHANNEL, QueryParameter.with(WEBRATE_CHANNEL, webrateChannel).parameters());
            deleteCompetitorChannelMappingsWithOvrCompetitorDetails(competitorChannelMappingsToDelete);
        }
    }

    private void deleteCompetitorChannelMappingsWithOvrCompetitorDetails(List<WebrateCompChannelMapping> competitorChannelMappingsToDelete) {
        if (CollectionUtils.isNotEmpty(competitorChannelMappingsToDelete)) {
            tenantCrudService.delete(competitorChannelMappingsToDelete);
            getOverrideCompetitorDetailsToBeDeleted(competitorChannelMappingsToDelete).stream()
                    .map(w -> w.getWebrateOverrideCompetitor().getId())
                    .forEach(competitorDataFilterService::deleteOverrideCompetitor);
        }
    }

    private Set<WebrateOverrideCompetitorDetails> getOverrideCompetitorDetailsToBeDeleted(List<WebrateCompChannelMapping> competitorChannelMappingsToDelete) {
        Set<WebrateOverrideCompetitorDetails> overrideCompetitorDetailsToDelete = getWebrateOverrideCompetitorDetails(competitorChannelMappingsToDelete);
        Set<WebrateOverrideCompetitorDetails> existingOverrideDetails = getWebrateOverrideCompetitorDetails(tenantCrudService.findAll(WebrateCompChannelMapping.class));
        overrideCompetitorDetailsToDelete.removeAll(existingOverrideDetails);
        return overrideCompetitorDetailsToDelete;
    }

    private Set<WebrateOverrideCompetitorDetails> getWebrateOverrideCompetitorDetails(List<WebrateCompChannelMapping> competitorChannelMappings) {
        return competitorChannelMappings.stream().map(WebrateCompChannelMapping::getWebrateOverrideCompetitorDetails).collect(Collectors.toSet());
    }

    public void cleanUpWebrateAccomType(WebrateAccomType webrateAccomType, boolean asyncWebrateCleanupEnabled) {
        cleanUpRDLCustomRoomDescription(WEBRATE_ACCOM_TYPE_NAME, webrateAccomType.getWebrateAccomName());
        cleanUpWebrateData(WEBRATE_ACCOM_TYPE_ID, webrateAccomType.getId());
        if (!asyncWebrateCleanupEnabled) {
            cleanUpPaceWebrateData(WEBRATE_ACCOM_TYPE_ID, webrateAccomType.getId());
        }
        if (isCPCNotificationByRoomClassEnabled()) {
            cleanUpCPCByRoomClassNotificationsConfig(webrateAccomType);
        }
        tenantCrudService.delete(webrateAccomType);
    }

    private boolean isCPCNotificationByRoomClassEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CPC_NOTIFICATION_BY_ROOM_CLASS);
    }

    private void cleanUpCPCByRoomClassNotificationsConfig(WebrateAccomType webrateAccomType) {
        WebrateAccomClassMapping mapping = CollectionUtils.isNotEmpty(webrateAccomType.getWebrateAccomClassMappings()) ?
                webrateAccomType.getWebrateAccomClassMappings().iterator().next() : null;
        if (mapping != null) {
            List<WebrateAccomType> webrateAccomTypes = tenantCrudService.findByNamedQuery(WebrateAccomClassMapping.BY_ACCOM_CLASS_ID,
                    QueryParameter.with("accomClassId", mapping.getAccomClass().getId()).parameters());
            if (CollectionUtils.isNotEmpty(webrateAccomTypes) &&
                    webrateAccomTypes.size() == 1 &&
                    mapping.getWebrateAccomType().getId().equals(webrateAccomTypes.get(0).getId())) {
                cleanUpCPCByRoomClassNotificationsConfig(mapping.getAccomClass());
            }
        }
    }

    public void cleanUpCPCByRoomClassNotificationsConfig(AccomClass accomClass) {
        Map<String, List<Integer>> changes = new HashMap<>();
        changes.put(Constants.IM_CLEANUP_ACCOM_CONFIG_CHANGED, Collections.singletonList(accomClass.getId()));
        asyncInformationManagerCleanupService.identifyConfigForCleanUp(PacmanWorkContextHelper.getPropertyId(), changes);
    }

    private void cleanUpWebrateData(String columnName, Integer columnCondition) {
        tenantCrudService.executeUpdateByNativeQuery(String.format(WEBRATE_CLEAN_UP_QUERY, columnName),
                QueryParameter.with("columnCondition", columnCondition).parameters());
    }

    private void cleanUpRDLCustomRoomDescription(String columnName, String columnCondition) {
        tenantCrudService.executeUpdateByNativeQuery(String.format(RDL_CUSTOM_ROOM_DESCRIPTION_CLEAN_UP_QUERY, columnName),
                QueryParameter.with("columnCondition", columnCondition).parameters());
    }

    private int cleanUpPaceWebrateData(String columnName, Integer columnCondition) {
        return tenantCrudService.executeUpdateByNativeQuery(String.format(PACE_WEBRATE_CLEAN_UP_QUERY, columnName),
                QueryParameter.with("columnCondition", columnCondition).parameters());
    }

    public int cleanUpPaceWebrateData(Integer chunkSize, String columnName, List<String> listOfWebrateIds) {
        return executePaceWebrateCleanUpQuery(chunkSize, columnName, listOfWebrateIds, PACE_WEBRATE_CHUNK_CLEANUP_QUERY);
    }

    public int cleanUpPaceWebrateDifferentialData(Integer chunkSize, String columnName, List<String> listOfWebrateIds) {
        return executePaceWebrateCleanUpQuery(chunkSize, columnName, listOfWebrateIds, PACE_WEBRATE_DIFFERENTIAL_CHUNK_CLEANUP_QUERY);
    }

    private int executePaceWebrateCleanUpQuery(Integer chunkSize, String columnName, List<String> listOfWebrateIds, String paceWebrateChunkCleanupQuery) {
        String format = String.format(paceWebrateChunkCleanupQuery, chunkSize, columnName);
        return tenantCrudService.executeUpdateByNativeQuery(format, MapBuilder.with("idsList", listOfWebrateIds).get());
    }

    public void disablePaceWebrateTableIndexes() {
        List<String> indexNames = Arrays.asList("IDX_PACE_Webrate_Channel_Competitor_LOS_Date", "IDX_PACE_Webrate_Competitor_Date_LOS_Status_Include_Channel"
                , "IDX_PACE_Webrate_Function_Web_Competitors1", "IDX_PACE_Webrate_Function_Web_Competitors2", "NC_CVRD_PACE_Webrate", "UQ_PACE_Webrate");
        indexNames.stream()
                .forEach(index -> tenantCrudService.executeUpdateByNativeQuery(String.format(PACE_WEBRATE_DISABLE_INDEX_QUERY, index)));
    }

    private String getCurrentConfigParamsContext() {
        String clientCode = ((WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY)).getClientCode();
        String propertyCode = ((WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY)).getPropertyCode();

        return "pacman." + clientCode + "." + propertyCode;
    }


    public void clearAndRegisterSyncEvent() {
        // If there was a webrate config change that a sync isn't required for, it needs to be removed
        syncEventAggregatorService.clearSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);

        // Register Sync Event
        syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED);
    }

    public void cleanUpWebrateAccomTypes(List<WebrateAccomType> webrateAccomTypes) {
        if (webrateAccomTypes.isEmpty()) {
            return;
        }
        List listOfAccomTypeIds = getWebrateIdsList(webrateAccomTypes);
        startCleanupJob(JobName.WebrateAccomTypeCleanupJob, WEBRATE_ACCOM_TYPE_ID, listOfAccomTypeIds);
    }

    public void cleanUpWebrateAccomTypesBasedOnAccomTypeId(List<String> webrateAccomTypeIds) {
        webrateAccomTypeIds.stream()
                .mapToInt(Integer::parseInt)
                .forEach(id -> {
                    WebrateAccomType webrateAccomType = tenantCrudService.find(WebrateAccomType.class, id);
                    if (webrateAccomType != null) {
                        cleanUpWebrateAccomType(webrateAccomType, true);
                    }
                });
        publishCRTMappingChangeSnsEvent();
    }


    public Long startWebrateSourceUpdateJob(WebrateSourceProperty oldWebrateSourceProperty, WebrateSourceProperty newWebrateSourceProperty) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.CLIENT_CODE, PacmanWorkContextHelper.getClientCode());
        parameters.put(JobParameterKey.PROPERTY_CODE, PacmanWorkContextHelper.getPropertyCode());
        parameters.put(JobParameterKey.CORRELATION_ID, UUID.randomUUID());
        parameters.put(JobParameterKey.OLD_WEBRATE_SOURCE_PROPERTY_ID, oldWebrateSourceProperty.getId());
        parameters.put(JobParameterKey.NEW_WEBRATE_SOURCE_PROPERTY_ID, newWebrateSourceProperty.getId());
        return jobService.startJob(JobName.WebrateSourceUpdateJob, parameters);
    }

    public Integer getTotalNumberOfRecordsFromPaceWebrateDifferential(String column, List<String> parameters) {
        return getTotalNumberOfPaceWebrateRecords(column, parameters, PACE_WEBRATE_DIFFERENTIAL_COUNT_QUERY);
    }

    public Integer getTotalNumberOfRecordsFromPaceWebrate(String column, List<String> parameters) {
        return getTotalNumberOfPaceWebrateRecords(column, parameters, PACE_WEBRATE_COUNT_QUERY);
    }

    private Integer getTotalNumberOfPaceWebrateRecords(String column, List<String> parameters, String paceWebrateCountQuery) {
        String format = String.format(paceWebrateCountQuery, column);
        return tenantCrudService.findByNativeQuerySingleResult(format, MapBuilder.with("idsList", parameters).get());
    }

    public String fetchWebrateCompetitorsNamesOfIds(List<String> listOfCompetitorsIds) {
        List idsList = listOfCompetitorsIds.stream().map(Integer::parseInt).collect(Collectors.toList());
        List<WebrateCompetitors> webrateCompetitorsList = tenantCrudService.findByNamedQuery(WebrateCompetitors.BY_IDS, MapBuilder.with(ID_LIST, idsList).get());
        return webrateCompetitorsList.stream().map(WebrateCompetitors::getWebrateCompetitorsName).collect(Collectors.joining(","));
    }

    public String fetchWebrateChannelNamesOfIds(List<String> listOfChannelIds) {
        List idsList = listOfChannelIds.stream().map(Integer::parseInt).collect(Collectors.toList());
        List<WebrateChannel> webrateChannelsList = tenantCrudService.findByNamedQuery(WebrateChannel.BY_IDS, MapBuilder.with(ID_LIST, idsList).get());
        return webrateChannelsList.stream().map(WebrateChannel::getWebrateChannelName).collect(Collectors.joining(","));
    }

    public String fetchWebrateAccomTypeNamesOfIds(List<String> listOfAccomTypeIds) {
        List idsList = listOfAccomTypeIds.stream().map(Integer::parseInt).collect(Collectors.toList());
        List<WebrateAccomType> webrateAccomTypesList = tenantCrudService.findByNamedQuery(WebrateAccomType.BY_IDS, MapBuilder.with(ID_LIST, idsList).get());
        return webrateAccomTypesList.stream().map(WebrateAccomType::getWebrateAccomName).collect(Collectors.joining(","));
    }

    public Queue<Date> getToBeMigratedWebrateGeneratedDates() {
        return tenantCrudService.findByNativeQuery(GET_LIST_OF_WEBRATE_GENERATION_TO_BE_MIGRATED)
                .stream().map(p -> (Date) p)
                .collect(Collectors.toCollection(ArrayDeque::new));
    }

    public void cleanupWebrateCompetitorChannelMappings(List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetailsList) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_UI_IGNORE_COMPETITOR) &&
                CollectionUtils.isNotEmpty(webrateOverrideCompetitorDetailsList)) {
            List<WebrateCompChannelMapping> mappings = tenantCrudService.findByNamedQuery(WebrateCompChannelMapping.BY_WEBRATE_OVR_COMP_DETAILS_LIST,
                    QueryParameter.with("webrateOverrideCompetitorDetailsList", webrateOverrideCompetitorDetailsList).parameters());
            webrateShoppingDataService.deleteCompChannelMappings(mappings);
        }
    }

    public void cleanupWebrateCompetitorChannelMappingsBasedOnAccomClass(List<AccomClass> accomClasses) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_UI_IGNORE_COMPETITOR) &&
                CollectionUtils.isNotEmpty(accomClasses)) {
            List<WebrateCompChannelMapping> mappings = tenantCrudService.findByNamedQuery(WebrateCompChannelMapping.BY_ACCOM_CLASSES,
                    QueryParameter.with("accomClasses", accomClasses).parameters());
            webrateShoppingDataService.deleteCompChannelMappings(mappings);
        }
    }

    public void performCleanupForIgnoredChannels(Set<DayOfWeek> ignoredDows, List<WebrateChannel> webrateChannels) {
        webrateChannels.forEach(channel -> {
            List<WebrateOverrideCompetitorDetails> webrateOvrCompetitorsDetails = tenantCrudService.findByNamedQuery(WebrateOverrideCompetitorDetails.BY_CHANNELS,
                    QueryParameter.with("webrateChannels", channel).parameters());
            if (CollectionUtils.isNotEmpty(webrateOvrCompetitorsDetails)) {
                webrateOvrCompetitorsDetails.stream()
                        .filter(details -> shouldDelete(ignoredDows, getAllowedDows(details.getWebrateOverrideCompetitor())))
                        .forEach(details -> cleanupCompetitorChannelMappings(details, channel));
            }
        });
    }

    private Set<DayOfWeek> getAllowedDows(WebrateOverrideCompetitor competitor) {
        return LocalDateUtils.getDayOfWeeks(convertJavaUtilDateToLocalDate(toDate(competitor.getStartDate().toFormattedString(DEFAULT_DATE_FORMAT))),
                convertJavaUtilDateToLocalDate(toDate(competitor.getEndDate().toFormattedString(DEFAULT_DATE_FORMAT))));
    }

    private boolean shouldDelete(Set<DayOfWeek> ignoredDows, Set<DayOfWeek> dows) {
        return ignoredDows.stream().anyMatch(dows::contains);
    }

    public void cleanupWebrateCompetitorChannelMappingsBasedOnCompAccomClassId(Integer compAccomClassId) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_UI_IGNORE_COMPETITOR)) {
            List<WebrateCompChannelMapping> mappings = tenantCrudService.findByNamedQuery(WebrateCompChannelMapping.BY_COMP_ACCOM_CLASS_ID,
                    QueryParameter.with("compAccomClassId", compAccomClassId).parameters());
            webrateShoppingDataService.deleteCompChannelMappings(mappings);
            tenantCrudService.flushAndClear();
        }
    }

    public void cleanupWebrateCompetitorChannelMappingsBasedOnProduct(Product product) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_UI_IGNORE_COMPETITOR)) {
            List<WebrateCompChannelMapping> mappings = tenantCrudService.findByNamedQuery(WebrateCompChannelMapping.BY_PRODUCT_ID,
                    QueryParameter.with("productId", product.getId()).parameters());
            webrateShoppingDataService.deleteCompChannelMappings(mappings);
            tenantCrudService.flushAndClear();
        }
    }

    private void cleanupCompetitorChannelMappings(WebrateOverrideCompetitorDetails ovrCompetitorDetails, WebrateChannel webrateChannel) {
        if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_UI_IGNORE_COMPETITOR)) {
            List<WebrateCompChannelMapping> competitorChannelMappingsToDelete = tenantCrudService.findByNamedQuery(
                    WebrateCompChannelMapping.BY_CHANNELS_AND_DETAILS_ID, QueryParameter.with(WEBRATE_CHANNEL, webrateChannel)
                            .and("ovrCompetitorDetails", ovrCompetitorDetails).parameters());
            deleteCompetitorChannelMappingsWithOvrCompetitorDetails(competitorChannelMappingsToDelete);
        }
    }
}