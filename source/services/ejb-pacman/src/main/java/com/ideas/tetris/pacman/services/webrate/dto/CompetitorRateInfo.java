package com.ideas.tetris.pacman.services.webrate.dto;

import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateRanking;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


public class CompetitorRateInfo {
    private Date occupancyDate;
    private String channelName;
    private String competitorName;
    private String accomClassName;
    private String roomTypeName;
    private String remarks;
    private boolean includedInDemand;
    private WebrateRanking includedInMarketConstraints;
    private Map<Integer, String> rateByLengthOfStay = new HashMap<Integer, String>();
    private BigDecimal rate;
    private DateParameter webrateGenerationDate;
    private String lastUpdatedDate;
    private Integer webRateId;
    private String productName;

    private BigDecimal dcmpcMaxMarketPercentile;

    private BigDecimal dcmpcOnBooksThresholdPercent;
    private String webrateTypeName;

    private boolean isRankingEnabledForDCMPC;

    private BigDecimal occupancyPercentage;

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getCompetitorName() {
        return competitorName;
    }

    public void setCompetitorName(String competitorName) {
        this.competitorName = competitorName;
    }

    public String getAccomClassName() {
        return accomClassName;
    }

    public void setAccomClassName(String accomClassName) {
        this.accomClassName = accomClassName;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public boolean isIncludedInDemand() {
        return includedInDemand;
    }

    public void setIncludedInDemand(boolean includedInDemand) {
        this.includedInDemand = includedInDemand;
    }

    public WebrateRanking getIncludedInMarketConstraints() {
        return includedInMarketConstraints;
    }

    public void setIncludedInMarketConstraints(WebrateRanking includedInMarketConstraints) {
        this.includedInMarketConstraints = includedInMarketConstraints;
    }

    public Map<Integer, String> getRateByLengthOfStay() {
        return rateByLengthOfStay;
    }

    public void setRateByLengthOfStay(Map<Integer, String> rateByLengthOfStay) {
        this.rateByLengthOfStay = rateByLengthOfStay;
    }

    public String getRoomTypeName() {
        return roomTypeName;
    }

    public void setRoomTypeName(String roomTypeName) {
        this.roomTypeName = roomTypeName;
    }

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public DateParameter getWebrateGenerationDate() {
        return webrateGenerationDate;
    }

    public void setWebrateGenerationDate(DateParameter webrateGenerationDate) {
        this.webrateGenerationDate = webrateGenerationDate;
    }

    public String getLastUpdatedOn() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(String lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public Integer getWebRateId() {
        return webRateId;
    }

    public void setWebRateId(Integer webRateId) {
        this.webRateId = webRateId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getWebrateTypeName() {
        return webrateTypeName;
    }

    public void setWebrateTypeName(String webrateTypeName) {
        this.webrateTypeName = webrateTypeName;
    }

    public BigDecimal getDcmpcMaxMarketPercentile() {
        return dcmpcMaxMarketPercentile;
    }

    public void setDcmpcMaxMarketPercentile(BigDecimal maxMarketPercentile) {
        this.dcmpcMaxMarketPercentile = maxMarketPercentile;
    }

    public boolean isRankingEnabledForDCMPC() {
        return isRankingEnabledForDCMPC;
    }

    public void setRankingEnabledForDCMPC(boolean rankingEnabledForDCMPC) {
        isRankingEnabledForDCMPC = rankingEnabledForDCMPC;
    }

    public BigDecimal getDcmpcOnBooksThresholdPercent() {
        return dcmpcOnBooksThresholdPercent;
    }

    public void setDcmpcOnBooksThresholdPercent(BigDecimal dcmpcOnBooksThresholdPercent) {
        this.dcmpcOnBooksThresholdPercent = dcmpcOnBooksThresholdPercent;
    }

    public BigDecimal getOccupancyPercentage() {
        return occupancyPercentage;
    }

    public void setOccupancyPercentage(BigDecimal occupancyPercentage) {
        this.occupancyPercentage = occupancyPercentage;
    }
}
