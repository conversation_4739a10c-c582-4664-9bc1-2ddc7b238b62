package com.ideas.tetris.pacman.services.reports;

public enum ScheduledJobExceptionState {
    ALL("all"), NONE("none"), REPORT_GENERATION("generate"), REPORT_DELIVERY("delivery");

    private String exceptionState;

    ScheduledJobExceptionState(String exceptionState) {
        this.exceptionState = exceptionState;
    }

    public static ScheduledJobExceptionState forExceptionState(String exceptionState) {
        for (ScheduledJobExceptionState scheduledJobExceptionState : ScheduledJobExceptionState.values()) {
            if (scheduledJobExceptionState.exceptionState.equalsIgnoreCase(exceptionState)) {
                return scheduledJobExceptionState;
            }
        }
        return ScheduledJobExceptionState.ALL;
    }
}
