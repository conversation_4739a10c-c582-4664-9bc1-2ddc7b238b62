package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.D360BookingSummaryDTO;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Transactional
public class D360BookingSummaryDataService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;


    public Boolean loadStrDailyDataIntoPacman(List<D360BookingSummaryDTO> data) {

        boolean isDataSaved = false;
        StringBuilder query = new StringBuilder();

        data.forEach(d360CapacityDto -> {
            query.append("INSERT into [dbo].[D360_Booking_Summary_Pace] ([D360_Comp_Set_ID], [D360_MKT_Seg_Detail_ID], [Capture_DT], [Occupancy_DT], [Arrivals], [Rooms_Sold], [Room_Revenue], [Mkt_Arrivals], [Mkt_Rooms_Sold], [Mkt_Room_Revenue], [Property_ID]) VALUES (");
            query.append("9999," + d360CapacityDto.getMarketSegmentDetailCode() + ", CAST(N'" + d360CapacityDto.getCaptureDate() +
                    "' AS Date), CAST(N'" + d360CapacityDto.getOccupancyDate() + "' AS Date)," + d360CapacityDto.getArrivals() + ", " + d360CapacityDto.getArrivals() + "" +
                    ", CAST(" + d360CapacityDto.getRoomRevenue() + " AS Numeric(19, 2)), " + d360CapacityDto.getMktArrivals() + "," + d360CapacityDto.getMktRoomSold() + "" +
                    ", CAST(" + d360CapacityDto.getMktRoomRevenue() + " AS Numeric(19, 2)), 010027);");


        });

        if (!query.toString().isEmpty()) {
            tenantCrudService.executeUpdateByNativeQuery(query.toString());
            isDataSaved = true;
        }
        return isDataSaved;
    }


    public Boolean deleteData() {
        tenantCrudService.executeUpdateByNativeQuery("Delete from [dbo].[D360_Booking_Summary_Pace] where D360_Comp_Set_ID=9999 ;");
        return true;
    }

}
