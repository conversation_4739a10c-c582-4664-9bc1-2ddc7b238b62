package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ConsolidatedPropertyView;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.property.dto.ExtractDetails;
import com.ideas.tetris.pacman.services.property.dto.PropertySearchCriteria;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Component
@Transactional
public class FilterMinExtracts {

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;
    @Autowired
	protected AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
	protected AuthorizationService authorizationService;
    @Autowired
	protected ExtractDetailsServiceLocal extractDetailsService;


    public List<ConsolidatedPropertyView> filter(final List<ConsolidatedPropertyView> properties, final PropertySearchCriteria searchCriteria) {
        List<ConsolidatedPropertyView> filteredProperties = properties;

        if (searchCriteria.getMinimumNumberOfExtracts() > 0) {
            filteredProperties = new ArrayList<ConsolidatedPropertyView>();
            for (ConsolidatedPropertyView property : properties) {
                ExtractDetails extractDetails = extractDetailsService.getExtractDetails(property.getPropertyId());
                if (extractDetails.getTotalExtracts() >= searchCriteria.getMinimumNumberOfExtracts()) {
                    filteredProperties.add(property);
                }

            }
        }

        return filteredProperties;
    }
}
