package com.ideas.tetris.pacman.services.property.configuration.service;

import com.google.common.io.Files;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.property.PropertyBuildType;
import com.ideas.tetris.pacman.services.property.PropertyStageChangeService;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.PropertyAttributePropertyNewAttributeConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.PropertySetupPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.RateShoppingPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFile;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecord;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecordStatus;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileStatus;
import com.ideas.tetris.pacman.services.property.configuration.file.PropertyConfigurationBatchProcessor;
import com.ideas.tetris.pacman.services.property.configuration.file.PropertyConfigurationFile;
import com.ideas.tetris.pacman.services.property.rollout.AddPropertyParams;
import com.ideas.tetris.pacman.services.property.rollout.PropertyRolloutServiceV2;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.event.TetrisEvent;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.VirtualPropertyMapping;
import com.ideas.tetris.platform.services.jmsclient.TetrisEventManager;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.*;

import static com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum.CANCEL_REBOOK_PCT;

@SuppressWarnings({"squid:S1200"})
@Component
@Transactional
public class PropertyConfigurationLoaderService {
    private static final Logger LOGGER = Logger.getLogger(PropertyConfigurationLoaderService.class.getName());
    private static final String DEFAULT_BATCH_SIZE = "1000";
    private static final int SMALL_BATCH_SIZE = 50;
    public static final String ERROR_MESSAGE_PREFIX = "ERROR: ";
    public static final int UNKNOWN_PROPERTY_ID = -1;

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;
    @Autowired
	private PropertyConfigurationBatchProcessor propertyConfigurationBatchProcessor;
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
	private PropertyStageChangeService propertyStageChangeService;
    @Autowired
	private DateService dateService;
    @Autowired
	private TetrisEventManager tetrisEventManager;
    @Autowired
	private PropertyRolloutServiceV2 propertyRolloutService;

    // Testing? Mockito?
    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    public void setPropertyConfigurationBatchProcessor(PropertyConfigurationBatchProcessor propertyConfigurationBatchProcessor) {
        this.propertyConfigurationBatchProcessor = propertyConfigurationBatchProcessor;
    }

    public void setTetrisEventManager(TetrisEventManager tetrisEventManager) {
        this.tetrisEventManager = tetrisEventManager;
    }

    public void setPropertyStageChangeService(PropertyStageChangeService propertyStageChangeService) {
        this.propertyStageChangeService = propertyStageChangeService;
    }

    public void setPropertyRolloutService(PropertyRolloutServiceV2 propertyRolloutService) {
        this.propertyRolloutService = propertyRolloutService;
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public void setValuesForOvrdPropertyAttributeTable() {
        int cancelRebookPercentageValue = getCancelRebookPercentageValue();
        if(cancelRebookPercentageValue > 0){
            tenantCrudService.executeUpdateByNativeQuery("INSERT INTO [ovrd_property_attribute]" +
                    "([IP_Cfg_Property_Attribute_ID],[value],[Status_ID],[Created_By_User_ID],[Last_Updated_By_User_ID])" +
                    "VALUES((select IP_Cfg_Property_Attribute_ID from IP_Cfg_Property_Attribute where Attribute_Name = '"+CANCEL_REBOOK_PCT.getAttributeName()+"'),"+cancelRebookPercentageValue+",1,11403,11403)");
        }
    }

    public void setIpConfigRuntimeParamTable() {
        tenantCrudService.executeUpdateByNativeQuery("update IP_Cfg_Runtime_Param_Name_List set Default_Value = 1 where Param_name in ('DOW_SEASON_INTERACTION_VALUE')");
        tenantCrudService.executeUpdateByNativeQuery("update IP_Cfg_Task_Runtime_Param_Map set Value = 1  where IP_Cfg_Runtime_Param_ID in (select IP_Cfg_Runtime_Param_ID from IP_Cfg_Runtime_Param_Name_List where Param_name in ('DOW_SEASON_INTERACTION_VALUE'))");
    }

    public void setBookingWindowStartDateValueForGroupAndNoFcst() {
        tenantCrudService.executeUpdateByNativeQuery("update IP_Cfg_Task_Runtime_Param_Map set  Value =  '-1' where ip_cfg_process_group_type_id in (3,4) and IP_Cfg_Runtime_Param_ID = (select IP_Cfg_Runtime_Param_ID from IP_Cfg_Runtime_Param_Name_List where Param_name = 'BK_WINDOW_START_DTA')  and Value = '180'");
    }

    @Transactional(propagation = Propagation.NEVER)


    public void addProperty(String propertyCode, Map<String, VirtualPropertyMapping> vpMappings, PropertyBuildType buildType) {
        boolean isLDB = false;
        if (vpMappings != null && !vpMappings.isEmpty()) {
            isLDB = vpMappings.values().stream().allMatch(VirtualPropertyMapping::isLDB);
        }
        addProperty(propertyCode, false, isLDB, vpMappings, buildType);
    }

    @Transactional(propagation = Propagation.NEVER)
    public void addLDBProperty(String propertyCode, Map<String, VirtualPropertyMapping> vpMappings, PropertyBuildType buildType) {
        addProperty(propertyCode, false, true, vpMappings, buildType);
    }

    public void addProperty(String propertyCode, boolean isRollback, boolean isLimitedDataBuild, Map<String, VirtualPropertyMapping> vpMappings, PropertyBuildType buildType) {
        String clientCode = getClientCode();
        Integer clientId = getClientId();
        int propertyId = UNKNOWN_PROPERTY_ID;

        try {
            updateConfigurationFileRecordStatusFailedToPending(propertyCode, clientId);

            ConfigurationFileRecord pcRecord = getConfigurationFileRecord(clientId, propertyCode,
                    PropertyConfigurationRecordType.PC);
            handlePCRecord(pcRecord);

            PropertySetupPropertyConfigurationDto setupDto = new PropertySetupPropertyConfigurationDto();
            setupDto.setFromPSVRecord(pcRecord.getRecord());

            ConfigurationFileRecord rsRecord = getConfigurationFileRecord(clientId, propertyCode, PropertyConfigurationRecordType.RS);
            RateShoppingPropertyConfigurationDto rateShoppingDto = new RateShoppingPropertyConfigurationDto();
            handleRCRecord(rsRecord, rateShoppingDto);

            ConfigurationFileRecord exstRecord = null;
            if (setupDto.isExtendedStay()) {
                // validate exst record exists
                exstRecord = getConfigurationFileRecord(clientId, propertyCode, PropertyConfigurationRecordType.EXST);
                if (exstRecord == null) {
                    throw new TetrisException(ErrorCode.ADD_PROPERTY_FAILED,
                            "Error adding property: missing EXST record when trying to add extended stay property");
                }
            }

            if (!isRollback) {
                //TODO: Have ConfigurationFileRecords being sent in also include estimated capacity in the record, but if it hasn't been set then let's populate a 0 for now
                AddPropertyParams addPropertyParams = new AddPropertyParams(
                        clientCode,
                        setupDto.getPropertyCode(),
                        setupDto.getName(),
                        setupDto.getPropertyTimezone(),
                        setupDto.getYieldCurrencyCode(),
                        setupDto.getEstimatedCapacity() == null ? 0 : setupDto.getEstimatedCapacity(),
                        setupDto.getSubscriptionType() == null ? "Not Defined" : setupDto.getSubscriptionType(),
                        setupDto.getConfigurationMethod() == null ? "Manual" : setupDto.getConfigurationMethod());
                addPropertyParams.setWebrateAlias(rateShoppingDto.getSubscribingPropertyCode());
                addPropertyParams.setIsLimitedDataBuild(isLimitedDataBuild);
                addPropertyParams.setBuildType(buildType);
                addPropertyParams.setExternalSystem(setupDto.isPcrs() ? ReservationSystem.PCRS.getConfigParameterValue() : ReservationSystem.HILSTAR.getConfigParameterValue());

                // this method get called for multiple properties from the UI
                // wait a second so that jobs queue in the regulator
                Thread.sleep(1000);
                propertyRolloutService.addProperty(addPropertyParams, vpMappings);

            } else {
                // process the PC record to add the property
                propertyConfigurationBatchProcessor.processConfigurationFileRecords(Collections.singletonList(pcRecord));
                // should now be able to get the actual property id
                propertyId = getPropertyId(propertyCode);

                // next process RS record...there should only be 1
                if (rsRecord != null) {
                    propertyConfigurationBatchProcessor.processConfigurationFileRecords(Collections.singletonList(rsRecord));
                }

                // process any SRP records
                Set<String> propertyCodes = new HashSet<>();
                propertyCodes.add(propertyCode);
                processConfigurationFileRecordTypes(clientId, propertyCodes,
                        Collections.singletonList(PropertyConfigurationRecordType.SRP.name()), SMALL_BATCH_SIZE);

                // finally process the EXST record if this is an extended stay property
                if (setupDto.isExtendedStay()) {
                    propertyConfigurationBatchProcessor.processConfigurationFileRecords(Collections.singletonList(exstRecord));
                }

                notifyBookkeepingAddPropertyCompleted(propertyId, propertyCode, clientCode);
            }
        } catch (TetrisException e) {
            throw e;
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.ADD_PROPERTY_FAILED, "Error adding property: ", e);
        }
    }

    public void updateConfigurationFileRecordStatusFailedToPending(String propertyCode, Integer clientId) {
        Set<String> recordTypes = new HashSet<>(Arrays.asList(
                PropertyConfigurationRecordType.PC.name(),
                PropertyConfigurationRecordType.EXST.name(),
                PropertyConfigurationRecordType.RS.name(),
                PropertyConfigurationRecordType.SRP.name()));
        propertyConfigurationBatchProcessor.updateFailedConfigurationFileRecordsToPending(clientId,
                propertyCode, recordTypes);
    }

    private void handleRCRecord(ConfigurationFileRecord rsRecord, RateShoppingPropertyConfigurationDto rateShoppingDto) {
        if (rsRecord != null) {
            rateShoppingDto.setFromPSVRecord(rsRecord.getRecord());
        }
    }

    private void handlePCRecord(ConfigurationFileRecord pcRecord) {
        if (pcRecord == null) {
            throw new TetrisException(ErrorCode.ADD_PROPERTY_FAILED,
                    "Error adding property: missing PC record when trying to add property");
        }
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public PropertySetupPropertyConfigurationDto getPropertySetupConfiguration(Integer clientId, String propertyCode) {
        PropertySetupPropertyConfigurationDto setupDto = null;
        ConfigurationFileRecord pcRecord = getConfigurationFileRecord(clientId, propertyCode, PropertyConfigurationRecordType.PC);
        if (pcRecord != null) {
            setupDto = new PropertySetupPropertyConfigurationDto();
            setupDto.setFromPSVRecord(pcRecord.getRecord());
        }
        return setupDto;
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public PropertyAttributePropertyNewAttributeConfigurationDto getPropertyNewAttributeConfiguration(Integer clientId, String propertyCode) {
        PropertyAttributePropertyNewAttributeConfigurationDto attributeDto = null;
        ConfigurationFileRecord paRecord = getConfigurationFileRecord(clientId, propertyCode, PropertyConfigurationRecordType.PA);
        if (paRecord != null) {
            attributeDto = new PropertyAttributePropertyNewAttributeConfigurationDto();
            attributeDto.setFromPSVRecord(paRecord.getRecord());
        }
        return attributeDto;
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public ConfigurationFileRecord getConfigurationFileRecord(Integer clientId, String propertyCode,
                                                              PropertyConfigurationRecordType recordType) {
        ConfigurationFileRecord configurationFileRecord = null;
        Set<String> propertyCodes = new HashSet<>();
        propertyCodes.add(propertyCode);
        List<ConfigurationFileRecord> configurationFileRecords =
                propertyConfigurationBatchProcessor.findPendingConfigurationFileRecordsByProperty(
                        clientId, propertyCodes, 0, Collections.singletonList(recordType.name()), 1);
        if (configurationFileRecords != null && !configurationFileRecords.isEmpty()) {
            configurationFileRecord = configurationFileRecords.get(0);
        }
        return configurationFileRecord;
    }

    public List<ConfigurationFileRecord> getConfigurationFileRecords(Integer clientId, String propertyCode,
                                                                     PropertyConfigurationRecordType recordType) {
        return propertyConfigurationBatchProcessor.findPendingConfigurationFileRecordsByProperty(clientId, Set.of(propertyCode), 0, List.of(recordType.name()));
    }

    private int getPropertyId(String propertyCode) {
        QueryParameter queryParameter = QueryParameter.with("propertyCode", propertyCode).and("clientId", getClientId());
        Property globalProperty = globalCrudService.findByNamedQuerySingleResult(Property.BY_CLIENT_ID_PROPERTY_CODE, queryParameter.parameters());
        if (globalProperty == null) {
            return -1;
        }
        return globalProperty.getId();
    }

    private void notifyBookkeepingAddPropertyCompleted(int propertyId, String propertyCode, String clientCode) {
        try {
            TetrisEvent te = tetrisEventManager.buildPropertyAddedEvent(propertyId, propertyCode, clientCode,
                    getUserId());
            tetrisEventManager.raiseEvent(te);
        } catch (Exception e) {
            LOGGER.error("BookKeeping Send Error in PropertyConfigurationLoaderService:addProperty" + e);
        }
    }

    private String getUserId() {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        return workContext != null ? workContext.getUserId() : "";
    }

    @Transactional(propagation = Propagation.NEVER)


    @SuppressWarnings({"checkstyle:com.puppycrawl.tools.checkstyle.checks.metrics.NPathComplexityCheck"})
    public List<String> configureProperty(String propertyCode) {
        int propertyId = getPropertyId(propertyCode);
        boolean webRateProcessingDone = isWebRateProcessingDone(propertyCode, propertyId);
        try {
            Set<String> recordTypes = new HashSet<>(Arrays.asList(
                    PropertyConfigurationRecordType.SE.name(),
                    PropertyConfigurationRecordType.PA.name(),
                    PropertyConfigurationRecordType.RC.name()));
            if (webRateProcessingDone) {
                recordTypes.add(PropertyConfigurationRecordType.DRC.name());
            }

            List<String> messages = new ArrayList<>();
            String message = applyConfiguration(propertyCode, PropertyConfigurationRecordType.SE, SMALL_BATCH_SIZE);
            if (message != null && message.startsWith(ERROR_MESSAGE_PREFIX)) {
                messages.add("Error loading Special Events: " + message);
            }
            message = applyConfiguration(propertyCode, PropertyConfigurationRecordType.PA, 1);
            if (message != null && message.startsWith(ERROR_MESSAGE_PREFIX)) {
                messages.add("Error loading Property Attributes: " + message);
            }

            message = applyConfiguration(propertyCode, PropertyConfigurationRecordType.RC, SMALL_BATCH_SIZE);
            if (message != null && message.startsWith(ERROR_MESSAGE_PREFIX)) {
                messages.add("Error loading Room Classes: " + message);
            }

            if (webRateProcessingDone) {
                message = applyConfiguration(propertyCode, PropertyConfigurationRecordType.DRC, SMALL_BATCH_SIZE);
                if (message != null && message.startsWith(ERROR_MESSAGE_PREFIX)) {
                    messages.add("Error loading Default Web Rate Channel: " + message);
                }
            }

            String context = Constants.CONFIG_PARAMS_NODE_PREFIX + "." +
                    getClientCode() + "." + propertyCode;

            if (messages.isEmpty()) {
                pacmanConfigParamsService.updateParameterValue(context, IntegrationConfigParamName.CORE_PROPERTY_CONFIGURATION_COMPLETE.value(), "true");
            }
            notifyBookkeepingConfigurePropertyCompleted(propertyId);

            propertyStageChangeService.changeStage(propertyId, Stage.POPULATION, null,
                    "changed to Population prior to Phase 2 configuration");
            return messages;
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.FILE_PROCESSING_ERROR, "Error configuring property: ", e);
        }
    }

    private void notifyBookkeepingConfigurePropertyCompleted(int propertyId) {
        try {
            TetrisEvent te = tetrisEventManager.buildPropertyConfiguredEvent(propertyId, getUserId());
            tetrisEventManager.raiseEvent(te);
        } catch (Exception e) {
            LOGGER.error("BookKeeping Send Error in PropertyConfigurationLoaderService:configureProperty" + e);
        }
    }



    public void applyConfiguration( String propertyCode,  String type) {
        PropertyConfigurationRecordType configurationRecordType = PropertyConfigurationRecordType.valueOf(type);
        applyConfiguration(propertyCode, configurationRecordType, 1);
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    @SuppressWarnings("squid:S1166")
    public String applyConfiguration(String propertyCode, PropertyConfigurationRecordType type, int batchSize) {
        String message;
        Set<String> propertyCodes = new HashSet<>();
        propertyCodes.add(propertyCode);
        Integer clientId = getClientId();
        try {
            propertyConfigurationBatchProcessor.updateFailedConfigurationFileRecordsToPending(clientId, propertyCode, new HashSet<>(Collections.singletonList(type.name())));

            int recordCount = processConfigurationFileRecordTypes(clientId, propertyCodes, Collections.singletonList(type.name()), batchSize);
            ConfigurationFileRecord entity = globalCrudService
                    .findByNamedQuerySingleResult(ConfigurationFileRecord.LATEST_BY_TYPE_AND_PROPERTY_AND_STATUS, QueryParameter
                            .with("propertyCode", propertyCode)
                            .and("clientId", clientId)
                            .and("recordType", type.toString())
                            .and("configurationRecordStatus", ConfigurationFileRecordStatus.FAILED).parameters());
            if (entity != null) {
                message = ERROR_MESSAGE_PREFIX + "Processing of record type " + type.toString() + " failed. Reason: " + entity.getFailureReason();
            } else {
                message = "Successfully processed " + recordCount + " " + type.toString() + " records";
            }
        } catch (Exception e) {
            LOGGER.error("Failed to apply configuration.", e);
            message = ERROR_MESSAGE_PREFIX + e.getMessage();
        }
        return message;
    }

    public void resetConfiguration(String propertyCode) {
        propertyConfigurationBatchProcessor.updateConfigurationFileRecordsToPending(getClientId(),
                new HashSet<>(Collections.singletonList(propertyCode)));
    }

    @Transactional(propagation = Propagation.NEVER)


    public int deleteConfiguration(String propertyCode) {
        return propertyConfigurationBatchProcessor.deleteRecordsForProperties(getClientId(),
                new HashSet<>(Collections.singletonList(propertyCode)));
    }

    private Integer getClientId() {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        return workContext.getClientId();
    }

    private String getClientCode() {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        return workContext.getClientCode();
    }

    // Uploads the configuration file and loads the records from the file, but does not process the records
    @Transactional(propagation = Propagation.NEVER)


    @SuppressWarnings({"squid:S1163", "squid:S1143"})
    public void uploadConfigurationFile(byte[] data, String filename) {
        File uploadedFile = uploadFile(data, filename);

        ConfigurationFile configurationFile = null;
        try {
            long start = System.currentTimeMillis();
            PropertyConfigurationFile propertyConfigurationFile = new PropertyConfigurationFile(uploadedFile);
            if (!propertyConfigurationFile.isValid()) {
                LOGGER.error(propertyConfigurationFile.getError());
                throw new TetrisException(ErrorCode.CONFIG_FILE_INVALID, propertyConfigurationFile.getError());
            }
            if (!propertyConfigurationFile.getMetaDataRecord().getClientCode().equals(getClientCode())) {
                LOGGER.error("Error loading property configuration file: client code in file (" +
                        propertyConfigurationFile.getMetaDataRecord().getClientCode() +
                        ") does not match context (" + getClientCode() + ")");
                throw new TetrisException(ErrorCode.CONFIG_FILE_CLIENT_NOT_SAME_AS_CONTEXT,
                        propertyConfigurationFile.getError());
            }
            configurationFile = loadConfigurationFileRecords(propertyConfigurationFile);
            LOGGER.debug("Finished loading records...total time: " + (System.currentTimeMillis() - start) + "ms");
        } finally {
            if (configurationFile == null) {
                try {
                    FileUtils.forceDelete(uploadedFile);
                } catch (IOException e) {
                    LOGGER.error("Error deleting property configuration file: " + uploadedFile, e);
                    throw new TetrisException(ErrorCode.FILE_PROCESSING_ERROR,
                            "Error deleting property configuration file: " + uploadedFile, e);
                }
            }
        }
    }

    @SuppressWarnings("squid:S1163")
    protected File uploadFile(byte[] data, String filename) {
        File uploadFolder = new File(SystemConfig.getPropertyConfigurationsFolder());
        File uploadedFile = new File(uploadFolder, filename);
        try {
            if (!uploadFolder.exists()) {
                FileUtils.forceMkdir(uploadFolder);
            }
            if (uploadedFile.exists()) {
                throw new TetrisException(ErrorCode.DUPLICATE_DATA, "File: " + uploadedFile + " already exists.");
            }
            FileUtils.writeByteArrayToFile(uploadedFile, data);
        } catch (IOException ioe) {
            LOGGER.error("Error writing property configuration file to: " + uploadedFile, ioe);
            throw new TetrisException(ErrorCode.FILE_PROCESSING_ERROR,
                    "Error writing property configuration file to: " + uploadedFile, ioe);
        }
        return uploadedFile;
    }

    private int getBatchSize() {
        return Integer.parseInt(System.getProperty("property.config.file.batch.size", DEFAULT_BATCH_SIZE));
    }

    protected ConfigurationFile loadConfigurationFileRecords(PropertyConfigurationFile propertyConfigurationFile) {
        String clientCode = propertyConfigurationFile.getMetaDataRecord().getClientCode();
        File file = propertyConfigurationFile.getConfigurationFile();

        // determine list of properties to load or exclude
        Set<String> propertiesToExclude = new HashSet<>();
        Set<String> propertiesToLoad = new HashSet<>();
        for (String propertyCode : propertyConfigurationFile.getPropertyCodes()) {
            if (isPropertyConfigured(clientCode, propertyCode)) {
                propertiesToExclude.add(propertyCode);
            } else {
                propertiesToLoad.add(propertyCode);
            }
        }

        ConfigurationFile configurationFile = propertyConfigurationBatchProcessor.createConfigurationFile(propertyConfigurationFile);
        BufferedReader bufferedReader = null;
        int batchSize = getBatchSize();
        LOGGER.debug("Current batch size is: " + batchSize);

        try {
            if (!propertiesToExclude.isEmpty()) {
                LOGGER.warn("The configuration records for the following properties will not be loaded because they are already configured: " +
                        StringUtils.join(propertiesToExclude, ","));
            }

            if (!propertiesToLoad.isEmpty()) {
                // clean up any previously loaded configuration records for the properties we are going to load
                propertyConfigurationBatchProcessor.deleteRecordsForProperties(configurationFile.getClientId(), propertiesToLoad);

                // process all the records from the file
                bufferedReader = Files.newReader(file, Charset.defaultCharset());
                List<String> currentBatchOfRecords = new ArrayList<>();
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    processBatchRecord(line, propertiesToExclude, currentBatchOfRecords, batchSize, configurationFile);
                }

                // process any remaining records
                propertyConfigurationBatchProcessor.createConfigurationFileRecordsForLines(configurationFile, currentBatchOfRecords);
            }

            // update ConfigurationFile status
            propertyConfigurationBatchProcessor.updateConfigurationFileToStatus(configurationFile, ConfigurationFileStatus.COMPLETED);
        } catch (Exception e) {
            LOGGER.error("Error occured loading configuration file " + file, e);
            // try to update the configuration file to Failure
            configurationFile.setFailureReason("Unable to load configuration file records from file: " + file + ". " + e.getMessage());
            configurationFile.setConfigurationFileStatus(ConfigurationFileStatus.FAILED);
            try {
                propertyConfigurationBatchProcessor.updateConfigurationFileToStatus(configurationFile, ConfigurationFileStatus.FAILED);
            } catch (Exception e2) {
                LOGGER.error("Failed to set configuration file status to Failure (but moving on): ", e2);
            }
            throw new TetrisException(ErrorCode.FILE_PROCESSING_ERROR, "Error occured loading configuration file " +
                    file + " : " + e.getMessage(), e);
        } finally {
            try {
                if (bufferedReader != null) {
                    bufferedReader.close();
                }
            } catch (IOException ioe) {
                LOGGER.error("Unable to close reader on file: " + file.getAbsolutePath(), ioe);
            }
        }

        return configurationFile;
    }

    private void processBatchRecord(String batchFileLine, Set<String> propertiesToExclude,
                                    List<String> currentBatchOfRecords, int batchSize, ConfigurationFile configurationFile) {
        if (!StringUtils.isEmpty(batchFileLine) && !propertiesToExclude.contains(getPropertyCodeForLine(batchFileLine))) {
            if (currentBatchOfRecords.size() == batchSize) {
                long start = System.currentTimeMillis();
                propertyConfigurationBatchProcessor.createConfigurationFileRecordsForLines(configurationFile, currentBatchOfRecords);
                LOGGER.debug("Batch processing took " + (System.currentTimeMillis() - start) + "ms");
                currentBatchOfRecords.clear();
            }
            currentBatchOfRecords.add(batchFileLine);
        }
    }

    protected boolean isPropertyConfigured(String clientCode, String propertyCode) {
        String context = Constants.CONFIG_PARAMS_NODE_PREFIX + "." +
                clientCode + "." + propertyCode;
        String value = pacmanConfigParamsService.getValue(context, IntegrationConfigParamName.CORE_PROPERTY_CONFIGURATION_COMPLETE.value(), true);
        return Boolean.parseBoolean(value != null ? value : "false");
    }

    private Object getPropertyCodeForLine(String line) {
        String propertyCode = "";
        String[] splitLine = StringUtils.splitPreserveAllTokens(line, PropertyConfigurationDto.DELIMITER);
        if (splitLine.length > 1) {
            propertyCode = splitLine[1];
        }
        return propertyCode;
    }

    /**
     * Process records using the provided finder. Execute until no records are left to
     * execute for the given record types.
     */
    public int processConfigurationFileRecordTypes(Integer clientId, Set<String> propertyCodes, List<String> recordTypes, int batchSize) {
        int recordCount = 0;
        int lastRecordIndex = 0;
        List<ConfigurationFileRecord> configurationFileRecords =
                propertyConfigurationBatchProcessor.findPendingConfigurationFileRecordsByProperty(
                        clientId, propertyCodes, lastRecordIndex, recordTypes, batchSize);

        while (configurationFileRecords != null && !configurationFileRecords.isEmpty()) {
            long start = System.currentTimeMillis();
            LOGGER.debug("Processing " + configurationFileRecords.size() + " ConfigurationFileRecords");
            recordCount += configurationFileRecords.size();
            // Process this set of ConfigurationFileRecords
            propertyConfigurationBatchProcessor.processConfigurationFileRecords(configurationFileRecords);

            if (!configurationFileRecords.isEmpty()) {
                lastRecordIndex = configurationFileRecords.get(configurationFileRecords.size() - 1).getId();
            }

            // See if there are any more records to process
            configurationFileRecords = propertyConfigurationBatchProcessor.findPendingConfigurationFileRecordsByProperty(
                    clientId, propertyCodes, lastRecordIndex, recordTypes, batchSize);

            LOGGER.debug("Total batch time " + ((System.currentTimeMillis() - start) / DateUtil.SECOND_IN_MILLISECONDS) + " seconds");
        }
        return recordCount;
    }

    private boolean isWebRateProcessingDone(String propertyCode, int propertyId) {
        boolean result = true;
        WorkContextType workContext = PacmanThreadLocalContextHolder.getWorkContext();
        workContext.setPropertyCode(propertyCode);
        workContext.setPropertyId(propertyId);
        Date successfulWebRateProcessDate = dateService.getWebRateProcessedDate();
        if (successfulWebRateProcessDate == null) {
            result = false;
        }
        return result;
    }

    public List<ConfigurationFileRecord> findAllByPropertyCode(String propertyCode) {
        return globalCrudService.findByNamedQuery(ConfigurationFileRecord.LATEST_BY_PROPERTY, QueryParameter.with(ConfigurationFileRecord.PARAM_CLIENT_ID, getClientId())
                .and(ConfigurationFileRecord.PARAM_PROPERTY_CODE, propertyCode).parameters());
    }

    public int updateConfigurationFileRecordsPropertyCode(String oldPropertyCode, String newPropertyCode) {
        List<ConfigurationFileRecord> configurationFileRecords = findAllByPropertyCode(oldPropertyCode);
        configurationFileRecords.forEach(configurationFileRecord -> {
            configurationFileRecord.setRecord(StringUtils.replaceOnce(configurationFileRecord.getRecord(), oldPropertyCode, newPropertyCode));
            configurationFileRecord.setPropertyCode(newPropertyCode);
        });
        Collection<ConfigurationFileRecord> updatedRecords = globalCrudService.save(configurationFileRecords);
        return updatedRecords.size();
    }

    private int getCancelRebookPercentageValue(){
        return Optional.ofNullable(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.CANCEL_REBOOK_PERCENTAGE_FOR_LDB.getParameterName()))
                .map(Integer::parseInt)
                .orElse(0);
    }

}
