package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * Created by idnakj on 9/2/2014.
 */
@Component
public class OperaCurrencyProcessingService {
    private static final String YIELD_CURRENCY_MESSAGE = "It is expected to Apply Yield Currency to Incoming data, Yield Currency Code configured: ";
    private static final String IS_VALID = " is valid.";
    private static final String IS_INVALID = " is invalid.";
    private static final String YIELD_CURRENCY_ERROR = " Feed processing failed because there was an error in yield currency configuration. ";
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;
    private static final Logger LOGGER = Logger.getLogger(OperaCurrencyProcessingService.class.getName());

    public int processCurrencyData(String correlationId) {
        LOGGER.info("Started processing currency data for feed : " + correlationId);
        //first delete any exchange rate which is blank in feed (zero in stage) or has a negative value
        crudService.executeUpdateByNativeQuery(" delete from opera.stage_yield_currency where exchange_rate <= 0 ");
        int numRows = applyYieldCurrencyIfRequired(correlationId);
        LOGGER.info("Completed processing currency data for feed : " + correlationId);
        return numRows;
    }

    public int applyYieldCurrencyIfRequired(String correlationId) {
        int numRows = 0;
        CurrencyDto currency = setupCurrency();
        if (currency.areValuesEqual()) {
            return numRows;
        }
        if (isValidYCConfigured(currency.getConfiguredYC())) {
            LOGGER.info(YIELD_CURRENCY_MESSAGE + currency.getConfiguredYC() + IS_VALID);
            createRequiredDataForYC();
            numRows += convertAllRevenueToYieldCurrency();
        } else {
            logAndThrowYCError(currency);
        }
        return numRows;
    }

    public int applyYieldCurrencyToHotelMktAccomActivity(Date startDate, Date endDate) {
        int numRows = 0;
        CurrencyDto currency = setupCurrency();
        if (currency.areValuesEqual()) {
            return numRows;
        }
        if (isValidYCConfigured(currency.getConfiguredYC())) {
            LOGGER.info(YIELD_CURRENCY_MESSAGE + currency.getConfiguredYC() + IS_VALID);
            numRows += convertHotelMktAccomActivityRevenue(startDate, endDate);
        } else {
            return logAndThrowYCError(currency);
        }
        return numRows;
    }

    private int logAndThrowYCError(CurrencyDto currency) {
        String message = YIELD_CURRENCY_MESSAGE + currency.getConfiguredYC() + IS_INVALID;
        LOGGER.error(message);
        throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, YIELD_CURRENCY_ERROR + message);
    }

    private int convertHotelMktAccomActivityRevenue(Date startDate, Date endDate) {
        return crudService.executeUpdateByNativeQuery(
                " update hotelMktAccom " +
                        " set Room_Revenue = Room_Revenue * Exchange_Rate, " +
                        " Food_Revenue = Food_Revenue * Exchange_Rate, " +
                        " Total_Revenue = Total_Revenue * Exchange_Rate " +
                        " from dbo.hotel_mkt_accom_activity as hotelMktAccom " +
                        " join (select CONVERT(DATE,begin_dt) as Begin_dt, CONVERT(DATE, min(end_dt)) as End_Dt, Exchange_rate" +
                        " from (select a.*, b.Begin_DT as end_dt from [opera].[Stage_Yield_Currency] as a" +
                        " join [opera].[Stage_Yield_Currency] as b" +
                        " on a.Begin_DT < b.Begin_DT ) as x" +
                        " group by begin_dt,Exchange_Rate) as b" +
                        " on hotelMktAccom.Occupancy_DT >= b.Begin_DT and hotelMktAccom.Occupancy_DT < b.End_Dt " +
                        " where Occupancy_DT between :startDate and :endDate ; ",
                QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
    }

    private CurrencyDto setupCurrency() {
        return new CurrencyDto(pacmanConfigParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE.value()),
                pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE.value()));
    }

    private class CurrencyDto {
        private String configuredYC;
        private String baseCurrency;

        public CurrencyDto(String configuredYC, String baseCurrency) {
            this.configuredYC = configuredYC;
            this.baseCurrency = baseCurrency;
        }

        public boolean areValuesEqual() {
            return StringUtils.equalsIgnoreCase(baseCurrency, configuredYC);
        }

        public String getConfiguredYC() {
            return configuredYC;
        }

        public String getBaseCurrency() {
            return baseCurrency;
        }
    }

    public boolean isValidYCConfigured(String configuredYC) {
        List exchangeRate = crudService.findByNativeQuery("select top 1 Exchange_Rate from opera.Stage_Yield_Currency where Currency_Code = :yc",
                QueryParameter.with("yc", configuredYC).parameters());
        return (exchangeRate.size() == 1);
    }

    protected void createRequiredDataForYC() {
        //for same begin date with multiple time stamps , use the data point with max time stamp
        crudService.executeUpdateByNativeQuery("delete from opera.Stage_Yield_Currency where Begin_DT not in " +
                "	(select MAX(Begin_DT) from opera.Stage_Yield_Currency group by CONVERT (DATE, Begin_DT))");
        crudService.executeUpdateByNativeQuery("insert into  [opera].[Stage_Yield_Currency] ([Resort]" +
                "      ,[Base_Currency_Code],[Currency_Code],[Begin_DT],[Exchange_Rate],[Data_Load_Metadata_ID])" +
                "       select [Resort],[Base_Currency_Code],[Currency_Code],'1980-01-01 00:00:00.000',[Exchange_Rate],[Data_Load_Metadata_ID] " +
                "			from [opera].[Stage_Yield_Currency] " +
                "			where Begin_DT = (select MIN(Begin_DT) from [opera].[Stage_Yield_Currency])");
        crudService.executeUpdateByNativeQuery("insert into  [opera].[Stage_Yield_Currency] ([Resort]" +
                "      ,[Base_Currency_Code],[Currency_Code],[Begin_DT],[Exchange_Rate],[Data_Load_Metadata_ID])" +
                "       select [Resort],[Base_Currency_Code],[Currency_Code],'2173-12-31 00:00:00.000',[Exchange_Rate],[Data_Load_Metadata_ID] " +
                "			from [opera].[Stage_Yield_Currency] " +
                "			where Begin_DT = (select MAX(Begin_DT) from [opera].[Stage_Yield_Currency])");
    }

    private int convertAllRevenueToYieldCurrency() {
        boolean excludeYieldCurrencyConversionForRateValue = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.EXCLUDE_YIELD_CURRENCY_CONVERSION_FOR_RATE_VALUE);
        String replaceRateConversionClause = excludeYieldCurrencyConversionForRateValue ? " " : "	, Rate_Amount = Rate_Amount * Exchange_Rate ";
        String queryToExecute = " select CONVERT(DATE,begin_dt) as begin_dt, CONVERT(DATE, min(end_dt)) as End_Dt, MIN(Exchange_rate) as Exchange_Rate" +
                " into #operaYCDateRange " +
                " from " +
                "	(select a.*, b.Begin_DT as end_dt from [opera].[Stage_Yield_Currency] as a " +
                "		join [opera].[Stage_Yield_Currency] as b" +
                "		on a.Begin_DT < b.Begin_DT ) as x" +
                " group by begin_dt;" +

                //occupancy summary
                " update occSummary" +
                " set Room_Revenue = Room_Revenue * Exchange_Rate" +
                "	, Food_Revenue = Food_Revenue * Exchange_Rate" +
                "	, Total_Revenue = Total_Revenue * Exchange_Rate" +
                " from" +
                " opera.stage_occupancy_summary as occSummary" +
                " join" +
                " #operaYCDateRange as b" +
                "	on" +
                "	occSummary.Occupancy_DT >= b.Begin_DT and occSummary.Occupancy_DT < b.End_Dt;" +

                //Transaction
                " update trans" +
                " set Room_Revenue = Room_Revenue * Exchange_Rate" +
                "	, Food_Beverage_Revenue = Food_Beverage_Revenue * Exchange_Rate" +
                "	, Total_Revenue = Total_Revenue * Exchange_Rate" +
                "	REPLACE_RATE_AMOUNT_CLAUSE " +
                "	, Other_Revenue = Other_Revenue * Exchange_Rate" +
                " from" +
                " opera.Stage_Transaction as trans" +
                "	join " +
                "	#operaYCDateRange as b" +
                "	on" +
                "	trans.Transaction_DT >= b.Begin_DT and trans.Transaction_DT < b.End_Dt;";

        queryToExecute = queryToExecute.replace("REPLACE_RATE_AMOUNT_CLAUSE", replaceRateConversionClause);

        boolean excludeYieldCurrencyConversionForRateValueForGroupData = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.EXCLUDE_YIELD_CURRENCY_CONVERSION_FOR_RATE_VALUE_FOR_GROUP_DATA);
        String groupBlockUpdateQuery =
                //Group Block
                " update grpBlk" +
                        " set Single_Rate = Single_Rate * b.Exchange_Rate" +
                        " ,Double_Rate = Double_Rate * b.Exchange_Rate" +
                        " ,Triple_Rate = Triple_Rate * b.Exchange_Rate" +
                        " ,Quadruple_Rate = Quadruple_Rate * b.Exchange_Rate" +
                        " ,Extra_Rate = Extra_Rate * b.Exchange_Rate " +
                        " ,G3_Rate_Value = G3_Rate_Value * b.Exchange_Rate " +
                        " from" +
                        " opera.Stage_Group_Block as grpBlk" +
                        " join" +
                        "	#operaYCDateRange as b" +
                        "	on" +
                        "	grpBlk.Block_DT >= b.Begin_DT and grpBlk.Block_DT < b.End_Dt;";

        if (!excludeYieldCurrencyConversionForRateValueForGroupData) {
            queryToExecute = queryToExecute + groupBlockUpdateQuery;
        }
        return crudService.executeUpdateByNativeQuery(queryToExecute);
    }

}
