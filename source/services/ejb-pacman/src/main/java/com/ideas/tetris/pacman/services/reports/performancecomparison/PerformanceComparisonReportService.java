package com.ideas.tetris.pacman.services.reports.performancecomparison;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.JasperReportDataConverter;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.PerformanceComparisonReportCriteria;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportData;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.scheduledreport.service.JasperReportService;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.*;

import static com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil.getText;

/**
 * Created by idnmal on 3/25/14.
 */
@Component
@Transactional
public class PerformanceComparisonReportService extends JasperReportService<ScheduledReportData, PerformanceComparisonReportCriteria> {

    private static final Logger LOGGER = Logger.getLogger(PerformanceComparisonReportService.class.getName());

    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Autowired
	protected DateService dateService;

    @Override
    protected ScheduledReportData retrieveData(ScheduledReport scheduledReport) {
        PerformanceComparisonReportCriteria criteria = (PerformanceComparisonReportCriteria) scheduledReport.getReportCriteria();
        populateReportCriteria(criteria);
        List<PerformanceComparisonOutputDTO> performanceComparisonOutputList = getPerformanceComparisonOutput(criteria);
        String title = getReportTitle(scheduledReport);
        ScheduledReportSheet sheet = new ScheduledReportSheet(title, performanceComparisonOutputList, PerformanceComparisonOutputDTO.class);
        List<ScheduledReportSheet> sheetList = new ArrayList<>(1);
        sheetList.add(sheet);
        return new ScheduledReportData(title, true, sheetList);
    }

    protected List<PerformanceComparisonOutputDTO> getPerformanceComparisonOutput(PerformanceComparisonReportCriteria criteria) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        String variableParameterKey1 = null;
        Integer variableParameterValue1 = null;
        String variableParameterKey2 = null;
        Integer variableParameterValue2 = null;

        Map<String, Integer> variableQueryParameter = getVariableQueryParameter(criteria);
        Iterator<Map.Entry<String, Integer>> entries = variableQueryParameter.entrySet().iterator();
        while (entries.hasNext()) {
            Map.Entry<String, Integer> entry = entries.next();
            if (null == variableParameterKey1) {
                variableParameterKey1 = entry.getKey();
                variableParameterValue1 = entry.getValue();
            } else {
                variableParameterKey2 = entry.getKey();
                variableParameterValue2 = entry.getValue();
            }
        }
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("pace_days", criteria.getPaceDays())
                .and("analysis_start_date", new Date(criteria.getAnalysisStartDate().toDate().getTime()))
                .and("analysis_end_date", new Date(criteria.getAnalysisEndDate().toDate().getTime()))
                .and("comparison_start_date", new Date(criteria.getComparisonStartDate().toDate().getTime()))
                .and("comparison_end_date", new Date(criteria.getComparisonEndDate().toDate().getTime()))
                .and("isRollingDate", criteria.isRollingDate() ? 1 : 0)
                .and("rollingAnalysisStartDate", null == criteria.getRollingAnalysisStartDate() ? "" : criteria.getRollingAnalysisStartDate())
                .and("rollingAnalysisEndDate", null == criteria.getRollingAnalysisEndDate() ? "" : criteria.getRollingAnalysisEndDate())
                .and("rollingComparisonStartDate", null == criteria.getRollingComparisonStartDate() ? "" : criteria.getRollingComparisonStartDate())
                .and("rollingComparisonEndDate", null == criteria.getRollingComparisonEndDate() ? "" : criteria.getRollingComparisonEndDate());

        queryParameters.and(variableParameterKey1, variableParameterValue1);
        if (null != variableParameterKey2) {
            queryParameters.and(variableParameterKey2, variableParameterValue2);
        }

        String functionName = getDBFunctionNameToBeExecuted(criteria);
        try {
            StringBuilder queryString = new StringBuilder();
            // some functions have been converted to stored procedures to perform better. Until this is completed for
            // all we have to craft the queries differently for each
            if (functionName != null && functionName.startsWith("dbo.usp_")) { // stored proc naming convention
                queryString.append("EXEC ").append(functionName);
            } else {
                queryString.append("select * from ").append(functionName).append("(");
            }
            queryString.append(" :property_id, :pace_days, :analysis_start_date, :analysis_end_date");
            queryString.append(", :comparison_start_date, :comparison_end_date");
            if (null != variableParameterKey2) {
                queryString.append(", :").append(variableParameterKey2);
            }
            queryString.append(", :").append(variableParameterKey1);
            queryString.append(", :isRollingDate, :rollingAnalysisStartDate, :rollingAnalysisEndDate, :rollingComparisonStartDate, :rollingComparisonEndDate");
            if (functionName != null && functionName.startsWith("dbo.usp_performance_comparision_end_date_analysis_report_hotel")) {
                queryString.append(",:isUseCompactWebratePaceEnabled");
                queryParameters.and("isUseCompactWebratePaceEnabled", criteria.getUseCompactWebratePaceEnabled());
                queryString.append(",:isContinuousPricingEnabled");
                queryParameters.and("isContinuousPricingEnabled", criteria.isContinuousPricing());
                queryString.append(",:useCpPaceDifferentialTableEnabled");
                queryParameters.and("useCpPaceDifferentialTableEnabled", criteria.getUseCpPaceDifferentialTableEnabled());
            }
            if (functionName != null && !functionName.startsWith("dbo.usp_")) {
                queryString.append(")");
            }

            return getResultByAppropriateNativeQuery(criteria, queryParameters, queryString);
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
        }
        return Collections.emptyList();
    }

    protected List<PerformanceComparisonOutputDTO> getResultByAppropriateNativeQuery(PerformanceComparisonReportCriteria critera, QueryParameter queryParameters, StringBuilder queryString) {
        if (PerformanceComparisonReportViewTypeEnum.TOTAL_PROPERTY.equals(critera.getReportViewType())) {
            return getCrudService().findByNativeQuery(queryString.toString(),
                    queryParameters.parameters(),
                    row -> {
                        PerformanceComparisonOutputDTO performanceComparisonOutputDTO = new PerformanceComparisonOutputDTO();
                        performanceComparisonOutputDTO.setDaystoArrival((Integer) row[0]);
                        performanceComparisonOutputDTO.setOccupancyOnBooksForAnalysis((BigDecimal) row[1]);
                        performanceComparisonOutputDTO.setOccupancyOnBooksForComparison((BigDecimal) row[2]);
                        performanceComparisonOutputDTO.setOccupancyForecastForAnalysis((BigDecimal) row[3]);
                        performanceComparisonOutputDTO.setCompetitorRate((BigDecimal) row[4]);
                        performanceComparisonOutputDTO.setCompetitorName((String) row[5]);
                        performanceComparisonOutputDTO.setRateName((String) row[6]);
                        performanceComparisonOutputDTO.setRate((BigDecimal) row[7]);
                        performanceComparisonOutputDTO.setBarOverride((String) row[8]);
                        performanceComparisonOutputDTO.setRoomClassName((String) row[9]);
                        return performanceComparisonOutputDTO;
                    });
        } else {
            return getCrudService().findByNativeQuery(queryString.toString(),
                    queryParameters.parameters(),
                    new RowMapper<PerformanceComparisonOutputDTO>() {
                        @Override
                        public PerformanceComparisonOutputDTO mapRow(Object[] row) {
                            PerformanceComparisonOutputDTO performanceComparisonOutputDTO = new PerformanceComparisonOutputDTO();
                            performanceComparisonOutputDTO.setDaystoArrival((Integer) row[0]);
                            performanceComparisonOutputDTO.setOccupancyOnBooksForAnalysis((BigDecimal) row[1]);
                            performanceComparisonOutputDTO.setOccupancyOnBooksForComparison((BigDecimal) row[2]);
                            performanceComparisonOutputDTO.setOccupancyForecastForAnalysis((BigDecimal) row[3]);
                            return performanceComparisonOutputDTO;
                        }
                    });
        }
    }

    private String getDBFunctionNameToBeExecuted(PerformanceComparisonReportCriteria criteria) {
        if (criteria.getCalculatePaceFromEndDate()) {
            switch (criteria.getReportViewType()) {
                case TOTAL_PROPERTY:
                    return "dbo.usp_performance_comparision_end_date_analysis_report_hotel";
                case TOTAL_TRANSIENT:
                case TOTAL_GROUP:
                    return "dbo.usp_performance_comparision_end_date_analysis_report_bt";
                case ROOM_CLASSES:
                    return "dbo.usp_performance_comparision_end_date_analysis_report_rc";
                case FORECAST_GROUPS:
                    return "dbo.usp_performance_comparision_end_date_analysis_report_fg";
                case MARKET_SEGMENTS:
                    return "dbo.usp_performance_comparision_end_date_analysis_report_ms";
                case BUSINESS_VIEW:
                    return "dbo.usp_performance_comparision_end_date_analysis_report_bv";
            }
        } else {
            switch (criteria.getReportViewType()) {
                case TOTAL_PROPERTY:
                    return "dbo.usp_performance_comparision_report_hotel";
                case TOTAL_TRANSIENT:
                case TOTAL_GROUP:
                    return "dbo.usp_performance_comparision_report_bt";
                case ROOM_CLASSES:
                    return "dbo.usp_performance_comparision_report_rc";
                case FORECAST_GROUPS:
                    return "dbo.usp_performance_comparision_report_fg";
                case MARKET_SEGMENTS:
                    return "dbo.usp_performance_comparision_report_ms";
                case BUSINESS_VIEW:
                    return "dbo.usp_performance_comparision_report_bv";
            }
        }
        return null;
    }

    private Map<String, Integer> getVariableQueryParameter(PerformanceComparisonReportCriteria criteria) {
        Map<String, Integer> variableQueryParameter = new HashMap<>();
        switch (criteria.getReportViewType()) {
            case TOTAL_PROPERTY:
                variableQueryParameter.put("comp_id_1", criteria.getCompetitor());
                variableQueryParameter.put("los", criteria.isBARByLOS() ? 1 : -1);
                break;
            case TOTAL_TRANSIENT:
                variableQueryParameter.put("business_type_id", BusinessTypeEnum.TOTAL_TRANSIENT.getId());
                break;
            case TOTAL_GROUP:
                variableQueryParameter.put("business_type_id", BusinessTypeEnum.TOTAL_GROUP.getId());
                break;
            case ROOM_CLASSES:
                variableQueryParameter.put("Accom_Class_ID", criteria.getAccomClassID());
                break;
            case FORECAST_GROUPS:
                variableQueryParameter.put("Forecast_Group_ID", criteria.getForecastGroupID());
                break;
            case MARKET_SEGMENTS:
                variableQueryParameter.put("MktSeg_ID", criteria.getMarketSegmentID());
                break;
            case BUSINESS_VIEW:
                variableQueryParameter.put("Business_Group_ID", criteria.getBusinessGroupID());
                break;
        }
        return variableQueryParameter;
    }

    @Override
    public JasperReportDataConverter getJasperReportDataConverter() {
        return null;
    }

    @Override
    public String getReportTitle(ScheduledReport<PerformanceComparisonReportCriteria> scheduledReport) {
        PerformanceComparisonReportCriteria criteria = scheduledReport.getReportCriteria();
        String genericTitle = getTitle(scheduledReport);

        switch (criteria.getReportViewType()) {
            case TOTAL_PROPERTY:
                return genericTitle + " - " + getText("total.hotel.level", scheduledReport.getLanguage());
            case TOTAL_TRANSIENT:
                return genericTitle + " - " + getText("total.transient.level", scheduledReport.getLanguage());
            case TOTAL_GROUP:
                return genericTitle + " - " + getText("total.group.level", scheduledReport.getLanguage());
            case ROOM_CLASSES:
                String roomTypeCode = crudService.find(AccomClass.class, criteria.getAccomClassID()).getCode();
                return genericTitle + " " + getText("roomClass", scheduledReport.getLanguage()) + " - " + roomTypeCode;
            case FORECAST_GROUPS:
                ForecastGroup group = crudService.find(ForecastGroup.class, criteria.getForecastGroupID());
                return genericTitle + " " + getText("forecast.group", scheduledReport.getLanguage()) + " - " + group.getName();
            case MARKET_SEGMENTS:
                MktSeg marketSegment = crudService.find(MktSeg.class, criteria.getMarketSegmentID());
                return genericTitle + " " + getText("common.ms", scheduledReport.getLanguage()) + " - " + marketSegment.getName();
            case BUSINESS_VIEW:
                BusinessGroup businessGroup = crudService.find(BusinessGroup.class, criteria.getBusinessGroupID());
                return genericTitle + " " + getText("businessview", scheduledReport.getLanguage()) + " - " + businessGroup.getName();
        }
        return null;
    }

    private String getTitle(ScheduledReport<PerformanceComparisonReportCriteria> scheduledReport) {
        if (scheduledReport.getReportType() != null && StringUtils.isNotBlank(scheduledReport.getReportType().getReportTitle())) {
            return ResourceUtil.getText(scheduledReport.getReportType().getReportTitle(), scheduledReport.getLanguage());
        }

        return "???Unknown???";
    }

    public PerformanceComparisonReportCriteria buildCriteria(Map<String, String> reportParamsMap, GlobalUser user, Property property, String currency) {
        PerformanceComparisonReportCriteria criteria = new PerformanceComparisonReportCriteria(
                new LocalDate(Long.valueOf(reportParamsMap.get("param_AnalysisStartDate"))),
                new LocalDate(Long.valueOf(reportParamsMap.get("param_AnalysisEndDate"))),
                new LocalDate(Long.valueOf(reportParamsMap.get("param_ComparisonStartDate"))),
                new LocalDate(Long.valueOf(reportParamsMap.get("param_ComparisonEndDate"))),
                reportParamsMap.get("param_RollingAnalysisStartDate"),
                reportParamsMap.get("param_RollingAnalysisEndDate"),
                reportParamsMap.get("param_RollingComparisionStartDate"),
                reportParamsMap.get("param_RollingComparisionEndDate"),
                "1".equals(reportParamsMap.get("param_isRollingDate")),
                Integer.valueOf(reportParamsMap.get("Comp1")),
                Integer.valueOf(reportParamsMap.get("param_PaceDays")),
                Boolean.valueOf(reportParamsMap.get("param_calculatePaceFromEndDate")),
                Boolean.valueOf(reportParamsMap.get("isContinuousPricingEnabled")),
                Boolean.valueOf(reportParamsMap.get("param_SheetForCriteria")),
                Boolean.valueOf(reportParamsMap.get("param_IsBARChecked")),
                Boolean.valueOf(reportParamsMap.get("IS_IGNORE_PAGINATION")),
                reportParamsMap.get("userLocale"),
                Integer.valueOf(reportParamsMap.get("param_Property_ID")),
                reportParamsMap.get("JNDI_NAME"),
                Boolean.valueOf(reportParamsMap.get("param_isBARByLOS")),
                user,
                property,
                currency
        );

        //Variable Paramaters
        if (reportParamsMap.get("param_AccomClass_ID") != null) {
            criteria.setReportViewType(PerformanceComparisonReportViewTypeEnum.ROOM_CLASSES);
            criteria.setAccomClassID(Integer.valueOf(reportParamsMap.get("param_AccomClass_ID")));
        } else if (reportParamsMap.get("param_BusinessViews") != null) {
            criteria.setReportViewType(PerformanceComparisonReportViewTypeEnum.BUSINESS_VIEW);
            criteria.setBusinessGroupID(Integer.valueOf(reportParamsMap.get("param_BusinessViews")));
        } else if (reportParamsMap.get("param_MarketSegments") != null) {
            criteria.setReportViewType(PerformanceComparisonReportViewTypeEnum.MARKET_SEGMENTS);
            criteria.setMarketSegmentID(Integer.valueOf(reportParamsMap.get("param_MarketSegments")));
        } else if (reportParamsMap.get("param_ForecastGroups") != null) {
            criteria.setReportViewType(PerformanceComparisonReportViewTypeEnum.FORECAST_GROUPS);
            criteria.setForecastGroupID(Integer.valueOf(reportParamsMap.get("param_ForecastGroups")));
        } else if (reportParamsMap.get("param_BusinessTypeID") != null && Integer.valueOf(reportParamsMap.get("param_BusinessTypeID")) == BusinessTypeEnum.TOTAL_GROUP.getId()) {
            criteria.setReportViewType(PerformanceComparisonReportViewTypeEnum.TOTAL_GROUP);
        } else if (reportParamsMap.get("param_BusinessTypeID") != null && Integer.valueOf(reportParamsMap.get("param_BusinessTypeID")) == BusinessTypeEnum.TOTAL_TRANSIENT.getId()) {
            criteria.setReportViewType(PerformanceComparisonReportViewTypeEnum.TOTAL_TRANSIENT);
        } else {
            criteria.setReportViewType(PerformanceComparisonReportViewTypeEnum.TOTAL_PROPERTY);
        }

        return criteria;
    }

    public PacmanConfigParamsService getPacmanConfigParamsService() {
        return pacmanConfigParamsService;
    }

    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    public CrudService getCrudService() {
        return crudService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    private void populateReportCriteria(PerformanceComparisonReportCriteria reportCriteria) {
        Integer propertyId = Integer.valueOf(reportCriteria.getPropertyID().toString());
        Property property = getGlobalCrudService().find(Property.class, propertyId);
        TimeZone timeZone = getAlertService().getPropertyTimeZone(property);
        DateTimeZone pZone = DateTimeZone.forTimeZone(timeZone);
        DateTimeFormatter dateFormatter = DateTimeFormat.forPattern(ScheduledReportUtils.REPORT_DATE_FORMAT).withLocale(reportCriteria.getLanguage().getLocale()).withZone(pZone);

        reportCriteria.setCreatedOn(ScheduledReportUtils.convertDateTimeToTimeZone(new DateTime(), timeZone));

        LocalDate systemDate = LocalDateUtils.fromDate(dateService.getCaughtUpDate());
        int activityStartDateDaysToAddOrRemove = getDifferenceInNumberOfDays(reportCriteria.getRollingAnalysisStartDate());
        reportCriteria.setAnalysisStartDate(systemDate.plusDays(activityStartDateDaysToAddOrRemove));
        reportCriteria.setLocaleSpecificAnalysisStartDateStr(systemDate.plusDays(activityStartDateDaysToAddOrRemove).toString(dateFormatter));

        int activityEndDateDaysToAddOrRemove = getDifferenceInNumberOfDays(reportCriteria.getRollingAnalysisEndDate());
        reportCriteria.setAnalysisEndDate(systemDate.plusDays(activityEndDateDaysToAddOrRemove));
        reportCriteria.setLocaleSpecificAnalysisEndDateStr(systemDate.plusDays(activityEndDateDaysToAddOrRemove).toString(dateFormatter));

        int comparisonStartDateDaysToAddOrRemove = getDifferenceInNumberOfDays(reportCriteria.getRollingComparisonStartDate());
        comparisonStartDateDaysToAddOrRemove = (-1 * comparisonStartDateDaysToAddOrRemove) + 364;
        reportCriteria.setComparisonStartDate(systemDate.minusDays(comparisonStartDateDaysToAddOrRemove));
        reportCriteria.setLocaleSpecificComparisonStartDateStr(systemDate.minusDays(comparisonStartDateDaysToAddOrRemove).toString(dateFormatter));

        int comparisonEndDateDaysToAddOrRemove = getDifferenceInNumberOfDays(reportCriteria.getRollingComparisonEndDate());
        comparisonEndDateDaysToAddOrRemove = (-1 * comparisonEndDateDaysToAddOrRemove) + 364;
        reportCriteria.setComparisonEndDate(systemDate.minusDays(comparisonEndDateDaysToAddOrRemove));
        reportCriteria.setLocaleSpecificComparisonEndDateStr(systemDate.minusDays(comparisonEndDateDaysToAddOrRemove).toString(dateFormatter));
    }

    private int getDifferenceInNumberOfDays(String rollingDate) {
        int numberOfDaysToAddOrRemove = 0;
        if (rollingDate.contains("+")) {
            numberOfDaysToAddOrRemove = Integer.valueOf(rollingDate.split("\\+")[1]);
        } else if (rollingDate.contains("-")) {
            numberOfDaysToAddOrRemove = Integer.valueOf(rollingDate.split("-")[1]) * -1;
        }
        return numberOfDaysToAddOrRemove;
    }
}
