package com.ideas.tetris.pacman.services.property.dto;

import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.io.Serializable;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class WebRateExtractDetails extends AbstractExtractDetails implements Extractable, Serializable {
    public static final String FILE_PART_WEBRATE = "RSS";
    public static final String FILE_PART_ZIP = "zip";
    public static final String FILE_PART_0 = "0";
    private static final long serialVersionUID = 1L;


    @Override
    public void copyIncomingExtractsToDirectory(Date startDate, Date endDate, File directory) throws IOException {
        for (File extract : getIncomingExtracts()) {
            FileFilter fileFilter = new WebRateExtractFileFilter(startDate, endDate);
            if (fileFilter.accept(extract)) {
                FileUtils.copyFileToDirectory(extract, directory);
            }
        }
    }

    @Override
    public void copyArchivedExtractsToDirectory(Date startDate, Date endDate, File directory) throws IOException {
        for (File extract : getArchivedExtracts()) {
            FileFilter fileFilter = new WebRateExtractFileFilter(startDate, endDate);
            if (fileFilter.accept(extract)) {
                FileUtils.copyFileToDirectory(extract, directory);
            }
        }
    }

    @Override
    public void postConstruct() {
        firstIncomingExtractDate = null;
        lastIncomingExtractDate = null;
        if (!incomingExtracts.isEmpty()) {
            List<Date> sortedDates = new ArrayList<Date>(incomingExtracts.keySet());
            Collections.sort(sortedDates);
            firstIncomingExtractDate = new DateParameter(sortedDates.get(0));
            lastIncomingExtractDate = new DateParameter(sortedDates.get(sortedDates.size() - 1));
            numberOfIncomingExtracts = getIncomingPaths().size();
        }
        if (!archivedExtracts.isEmpty()) {
            List<Date> sortedDates = new ArrayList<Date>(archivedExtracts.keySet());
            Collections.sort(sortedDates);
            numberOfArchivedExtracts = getArchivedPaths().size();
        }
    }

    @Override
    protected Date deriveDate(String fileName) {
        String[] segments = fileName.split("[_.]");
        if (segments.length < FILE_NAME_SEGMENTS_STANDARD) {
            return null;
        }
        try {
            return getDateFormat().parse(segments[1]);
        } catch (ParseException e) {
            return null;
        }
    }
}
