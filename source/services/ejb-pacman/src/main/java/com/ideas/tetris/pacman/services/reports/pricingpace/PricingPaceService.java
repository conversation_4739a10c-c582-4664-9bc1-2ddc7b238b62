package com.ideas.tetris.pacman.services.reports.pricingpace;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.pricingpace.dto.PricingPaceBarByLos;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PricingPaceService {

    private static final Logger LOGGER = Logger.getLogger(PricingPaceService.class.getName());

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    private CrudService getCrudServiceBean() {
        return crudService;
    }

    public void setCrudServiceBean(CrudService crudService) {
        this.crudService = crudService;
    }

    public List<PricingPaceBarByLos> getPricingPaceByLosData(LocalDate date, Integer paceDays, Integer accomClassId, ArrayList<Integer> competitorIds, final boolean isPhysicalCapacityEnabled) {

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("start_date", java.sql.Date.valueOf(date.minusDays(paceDays - 1)))
                .and("end_date", java.sql.Date.valueOf(date))
                .and("accom_class_id", accomClassId);

        StringBuilder compIdPramString = new StringBuilder();
        String compIdAttribute;

        if (competitorIds != null) {
            for (int i = 0; i < 15; i++) {
                compIdAttribute = "comp_id_" + (i + 1);
                compIdPramString.append(":").append(compIdAttribute).append(",");
                queryParameters.and(compIdAttribute, competitorIds.get(i) == null ? -1 : competitorIds.get(i));
            }
        }

        compIdPramString.setLength(compIdPramString.length() - 1);

        try {
            return getCrudServiceBean().findByNativeQuery("select * from dbo.ufn_get_pricing_pace_report(:property_id, :start_date, :end_date, :accom_class_id, " + compIdPramString + ")  order by businessdate DESC",
                    queryParameters.parameters(), new RowMapper<PricingPaceBarByLos>() {
                        @Override
                        public PricingPaceBarByLos mapRow(Object[] row) {
                            PricingPaceBarByLos data = new PricingPaceBarByLos();
                            data.setArrivalDate((Date) row[0]);
                            data.setBusinessDate((Date) row[1]);
                            data.setDaysToArrival((Integer) row[2]);
                            data.setDow((String) row[3]);
                            data.setLOS1((String) row[4]);
                            data.setLOS1Price((BigDecimal) row[5]);
                            data.setOverride1((String) row[6]);
                            data.setLOS2((String) row[7]);
                            data.setLOS2Price((BigDecimal) row[8]);
                            data.setOverride2((String) row[9]);
                            data.setLOS3((String) row[10]);
                            data.setLOS3Price((BigDecimal) row[11]);
                            data.setOverride3((String) row[12]);
                            data.setLOS4((String) row[13]);
                            data.setLOS4Price((BigDecimal) row[14]);
                            data.setOverride4((String) row[15]);
                            data.setLOS5((String) row[16]);
                            data.setLOS5Price((BigDecimal) row[17]);
                            data.setOverride5((String) row[18]);
                            data.setLOS6((String) row[19]);
                            data.setLOS6Price((BigDecimal) row[20]);
                            data.setOverride6((String) row[21]);
                            data.setLOS7((String) row[22]);
                            data.setLOS7Price((BigDecimal) row[23]);
                            data.setOverride7((String) row[24]);
                            data.setLOS8((String) row[25]);
                            data.setLOS8Price((BigDecimal) row[26]);
                            data.setOverride8((String) row[27]);
                            data.setAccomName((String) row[28]);
                            data.setRoomSold((BigDecimal) row[29]);
                            data.setPropertyRoomSold((BigDecimal) row[30]);
                            data.setPropertyForecast((BigDecimal) row[31]);
                            data.setAccomForecast((BigDecimal) row[32]);
                            if (isPhysicalCapacityEnabled) {
                                data.setPropertyForecastPercent((BigDecimal) row[65]);
                                data.setAccomForecastPercent((BigDecimal) row[66]);
                            } else {
                                data.setPropertyForecastPercent((BigDecimal) row[33]);
                                data.setAccomForecastPercent((BigDecimal) row[34]);
                            }


                            data.setWebrate1((BigDecimal) row[35]);
                            data.setCompName1((String) row[36]);
                            data.setWebrate2((BigDecimal) row[37]);
                            data.setCompName2((String) row[38]);
                            data.setWebrate3((BigDecimal) row[39]);
                            data.setCompName3((String) row[40]);
                            data.setWebrate4((BigDecimal) row[41]);
                            data.setCompName4((String) row[42]);
                            data.setWebrate5((BigDecimal) row[43]);
                            data.setCompName5((String) row[44]);
                            data.setWebrate6((BigDecimal) row[45]);
                            data.setCompName6((String) row[46]);
                            data.setWebrate7((BigDecimal) row[47]);
                            data.setCompName7((String) row[48]);
                            data.setWebrate8((BigDecimal) row[49]);
                            data.setCompName8((String) row[50]);
                            data.setWebrate9((BigDecimal) row[51]);
                            data.setCompName9((String) row[52]);
                            data.setWebrate10((BigDecimal) row[53]);
                            data.setCompName10((String) row[54]);
                            data.setWebrate11((BigDecimal) row[55]);
                            data.setCompName11((String) row[56]);
                            data.setWebrate12((BigDecimal) row[57]);
                            data.setCompName12((String) row[58]);
                            data.setWebrate13((BigDecimal) row[59]);
                            data.setCompName13((String) row[60]);
                            data.setWebrate14((BigDecimal) row[61]);
                            data.setCompName14((String) row[62]);
                            data.setWebrate15((BigDecimal) row[63]);
                            data.setCompName15((String) row[64]);

                            return data;
                        }
                    });
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<PricingPaceBarByLos>();
        }
    }

    public List<PricingPaceBarByLos> getPricingPaceBarByDayData(LocalDate date, Integer paceDays, Integer accomClassId, ArrayList<Integer> competitorIds, final boolean isPhysicalCapacityEnabled) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("start_date", java.sql.Date.valueOf(date.minusDays(paceDays - 1)))
                .and("end_date", java.sql.Date.valueOf(date))
                .and("accom_class_id", accomClassId);

        StringBuilder compIdPramString = new StringBuilder();
        String compIdAttribute;

        if (competitorIds != null) {
            for (int i = 0; i < 15; i++) {
                compIdAttribute = "comp_id_" + (i + 1);
                compIdPramString.append(":").append(compIdAttribute).append(",");
                queryParameters.and(compIdAttribute, competitorIds.get(i) == null ? -1 : competitorIds.get(i));
            }
        }

        compIdPramString.setLength(compIdPramString.length() - 1);

        try {
            return getCrudServiceBean().findByNativeQuery("select * from dbo.ufn_get_pricing_pace_barbyday_report(:property_id, :start_date, :end_date, :accom_class_id, " + compIdPramString + ")",
                    queryParameters.parameters(), new RowMapper<PricingPaceBarByLos>() {
                        @Override
                        public PricingPaceBarByLos mapRow(Object[] row) {
                            PricingPaceBarByLos data = new PricingPaceBarByLos();
                            data.setCaughtUpdate(DateUtil.convertJavaUtilDateToZonedDateTime((Date) row[0]));
                            data.setBusinessDate((Date) row[1]);

                            data.setDow((String) row[2]);
                            data.setOverride1((String) row[3]);
                            data.setLOS((Integer) row[4]);
                            data.setAccomName((String) row[5]);
                            data.setLOS1((String) row[6]);
                            data.setLOS1Price((BigDecimal) row[7]);
                            data.setRoomSold((BigDecimal) row[8]);
                            data.setPropertyRoomSold((BigDecimal) row[9]);
                            data.setPropertyForecast((BigDecimal) row[10]);
                            data.setAccomForecast((BigDecimal) row[11]);
                            data.setPropertyForecastPercent((BigDecimal) row[12]);
                            data.setAccomForecastPercent((BigDecimal) row[13]);
                            data.setWebrate1((BigDecimal) row[14]);
                            data.setCompName1((String) row[15]);
                            data.setWebrate2((BigDecimal) row[16]);
                            data.setCompName2((String) row[17]);
                            data.setWebrate3((BigDecimal) row[18]);
                            data.setCompName3((String) row[19]);
                            data.setWebrate4((BigDecimal) row[20]);
                            data.setCompName4((String) row[21]);
                            data.setWebrate5((BigDecimal) row[22]);
                            data.setCompName5((String) row[23]);
                            data.setWebrate6((BigDecimal) row[24]);
                            data.setCompName6((String) row[25]);
                            data.setWebrate7((BigDecimal) row[26]);
                            data.setCompName7((String) row[27]);
                            data.setWebrate8((BigDecimal) row[28]);
                            data.setCompName8((String) row[29]);
                            data.setWebrate9((BigDecimal) row[30]);
                            data.setCompName9((String) row[31]);
                            data.setWebrate10((BigDecimal) row[32]);
                            data.setCompName10((String) row[33]);
                            data.setWebrate11((BigDecimal) row[34]);
                            data.setCompName11((String) row[35]);
                            data.setWebrate12((BigDecimal) row[36]);
                            data.setCompName12((String) row[37]);
                            data.setWebrate13((BigDecimal) row[38]);
                            data.setCompName13((String) row[39]);
                            data.setWebrate14((BigDecimal) row[40]);
                            data.setCompName14((String) row[41]);
                            data.setWebrate15((BigDecimal) row[42]);
                            data.setCompName15((String) row[43]);

                            return data;
                        }
                    });
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<PricingPaceBarByLos>();
        }
    }


}
