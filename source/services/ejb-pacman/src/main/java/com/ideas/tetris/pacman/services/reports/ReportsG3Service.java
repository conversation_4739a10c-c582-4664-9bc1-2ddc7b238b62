package com.ideas.tetris.pacman.services.reports;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.infra.tetris.security.EncryptionDecryption;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.RatchetCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.reports.entity.ReportCriteria;
import com.ideas.tetris.pacman.services.reports.enums.G3Report;
import com.ideas.tetris.pacman.services.reports.individualgroupwash.IndividualGroupWashReportService;
import com.ideas.tetris.pacman.services.reports.individualgroupwash.dto.IndividualGroupWashDto;
import com.ideas.tetris.pacman.services.reports.mcatmapping.MCATMappingReportService;
import com.ideas.tetris.pacman.services.reports.mcatmapping.dto.MCATMappingDTO;
import com.ideas.tetris.pacman.services.reports.services.ReportGenerationService;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.*;
import com.ideas.tetris.platform.common.logging.LoggingContext;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.platform.reports.ReportSource;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import lombok.extern.log4j.Log4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.HeaderParam;
import javax.ws.rs.core.Response;
import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Supplier;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.TRANSACTION_ISOLATION_READ_UNCOMMITTED;
import static com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM;
import static com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.IS_ADDITIONAL_REPORT_CRITERIA_ENABLED;
import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.*;
import static com.ideas.tetris.platform.common.crudservice.QueryParameter.with;
import static com.ideas.tetris.platform.reports.jasperreports.constants.ReportsConstants.*;
import static org.apache.commons.lang.StringUtils.isEmpty;

@Log4j
@Justification("Using Jasper's library call to generate reports in G3, now that we have all report in G3's transaction this" +
        "will surely go above 5 minutes and we already have Data Extraction given 10 minutes transaction timeout in existing flow and with " +
        "this implementation we're also bringing DEReport in G3's transaction boundary so I'm increasing this timeout.")
@Component
@Transactional(timeout = 600)
public class ReportsG3Service {

    @Autowired
    ReportGenerationService reportGenerationService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService globalCrudService;

    @RatchetCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("ratchetCrudServiceBean")
    CrudService ratchetCrudService;

    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService tenantCrudService;

    @Autowired
    MCATMappingReportService mappingReportService;

    @Autowired
    IndividualGroupWashReportService individualGroupWashReportService;

    @Autowired
    DateService dateService;

    private static final String CRITERIA_QUERY = "select property_name,created_by," +
            "genration_date,param_BaseCurrency,start_date,end_date from dbo.ufn_get_filter_selection" +
            "( :propertyId ,:userID ,:currency,0,:param_StartDate,:param_EndDate,'','','','','','','','','','','',''," +
            "'','')";

    private static final String ACCOUNT_ID_QUERY = "SELECT isnull(nullif( SFDC_Account_Number,''),'---') " +
            "SFDC_Account_Number FROM property where Property_ID = :propertyId";
    private static final String REPORT_FILE_DOWNLOADING_EXCEPTION = "Report file downloading Exception";


    public String generateReport(Map<String, String> reportParameters,
                                 String reportDestination,
                                 String reportFileName,
                                 Supplier<String> readConfigValue) throws G3ReportGenerationException {
        addBaseCurrency(reportParameters);
        addAccountId(reportParameters);
        overrideDateFormatForExcel(reportParameters);
        addTimezone(reportParameters);
        addInventoryGroup(reportParameters);
        Map<String, Object> reportParams = new HashMap<>(reportParameters);
        buildExternalData(reportParams);
        setTransactionIsolationLevelParameter(reportParams, readConfigValue);
        return reportGenerationService.generateReport(reportParams, reportDestination, reportFileName);
    }

    private void setTransactionIsolationLevelParameter(Map<String, Object> reportParams,
                                                       Supplier<String> readConfigValue) {
        reportParams.put(TRANS_READ_UNCOMMITTED, readConfigValue.get());
    }

    public String generateOnlineReport(Map<String, String> reportParameters,
                                       String reportDestination,
                                       String reportFileName,
                                       ReportSource reportSource) {
        String reportUnit = reportParameters.get(REPORT_UNIT).replace("/public/Reports/", StringUtils.EMPTY);
        G3Report g3Report = G3Report.forReportUnitName(reportUnit);
        try {
            buildLoggerForReport(reportParameters, reportSource, g3Report, null, String.format(REPORT_GENERATION_STARTED, reportUnit));
            Integer propertyId = Integer.valueOf(reportParameters.get(PARAMETER_PROPERTY_ID));
            Property property = globalCrudService.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT,
                    QueryParameter.with(Property.PARAM_PROPERTY_ID, propertyId).parameters());
            String dateFolderForReports = DateUtil.formatDate(new Date(), "ddMMyyyy");
            reportDestination = reportDestination + File.separator + dateFolderForReports + File.separator + property.getClient().getCode() + File.separator + property.getCode();
            File sourceDir = new File(reportDestination);
            if (!sourceDir.exists()) {
                sourceDir.mkdirs();
            }
            String g3TempReportFilePath = generateReport(reportParameters, reportDestination, reportFileName, () -> String.valueOf(configParamsService.getBooleanParameterValue(TRANSACTION_ISOLATION_READ_UNCOMMITTED)));
            buildLoggerForReport(reportParameters, reportSource, g3Report, null, String.format(REPORT_GENERATION_COMPLETED, reportUnit, g3TempReportFilePath));
            return g3TempReportFilePath;
        } catch (G3ReportGenerationException e) {
            buildLoggerForReport(reportParameters, reportSource, g3Report, e, String.format("Report generation failed for report %s see exception details", reportUnit));
        }
        return StringUtils.EMPTY;

    }

    public void generateScheduledReport(Map<String, String> reportParameters,
                                        String reportDestination,
                                        String reportFileName) throws ScheduledReportException {
        try {
            generateReport(reportParameters, reportDestination, reportFileName, SystemConfig::readUncommittedForReports);
        } catch (G3ReportGenerationException e) {
            throw new ScheduledReportException(ErrorCode.REPORT_GENERATION_FAILED, ScheduleReportExceptionStepMessage.G3REPORT_ERROR, e);
        }
    }

    private void buildExternalData(Map<String, Object> reportParameters) {
        String reportUnit = (String) reportParameters.get(REPORT_UNIT);
        if (reportUnit.contains(MCAT_PLUS_REPORT)) {
            fetchMcatData(reportParameters);
        } else if (reportUnit.contains(INDIVIDUAL_GROUP_WASH_REPORT)) {
            fetchIndividualGroupWashData(reportParameters);
        }
    }

    private Map<String, Object> fetchMcatData(Map<String, Object> reportParameters) {
        Integer propertyId = Integer.valueOf((String) reportParameters.get(PARAMETER_PROPERTY_ID));
        Integer userID = Integer.valueOf((String) (reportParameters.get(PARAMETER_USER_ID)));
        String currency = String.valueOf(reportParameters.get(PARAM_BASE_CURRENCY));
        Collection<MCATMappingDTO> mcatData = getMcatData(propertyId);
        Collection<ReportCriteria> reportCriteria = getReportCriteria(propertyId, userID, currency, new Date(), new Date());
        reportParameters.put(MCAT_DATA, mcatData);
        reportParameters.put(MCAT_CRITERIA, reportCriteria);
        return reportParameters;
    }

    private Collection<MCATMappingDTO> getMcatData(Integer propertyId) {
        return buildMcatData(propertyId);
    }

    private Collection<MCATMappingDTO> buildMcatData(Integer propertyId) {
        String externalSystem = configParamsService.getParameterValue(CORE_PROPERTY_EXTERNAL_SYSTEM);
        List<MCATMappingDTO> mcatMappingDTOS = new ArrayList<>();
        if (Constants.REZVIEW.equalsIgnoreCase(externalSystem)) {
            mcatMappingDTOS = mappingReportService.getMcatMappingDTOS(ratchetCrudService, propertyId);
        } else if (!Constants.OPERA.equalsIgnoreCase(externalSystem)) {
            mcatMappingDTOS = mappingReportService.getMcatMappingDTOS(globalCrudService, propertyId);
        }
        return mcatMappingDTOS;
    }

    private void fetchIndividualGroupWashData(Map<String, Object> reportParameters) {
        Integer propertyId = Integer.valueOf((String) reportParameters.get(PARAMETER_PROPERTY_ID));
        Integer userID = Integer.valueOf((String) (reportParameters.get(PARAMETER_USER_ID)));
        String currency = String.valueOf(reportParameters.get(PARAM_BASE_CURRENCY));
        LocalDate startDate = getLocalDate(((String) reportParameters.get(PARAM_IS_ROLLING_DATE)), (String) reportParameters.get(PARAM_STARTDATE), (String) reportParameters.get(PARAM_ROLLING_START_DATE));
        LocalDate endDate = getLocalDate(((String) reportParameters.get(PARAM_IS_ROLLING_DATE)), (String) reportParameters.get(PARAM_ENDDATE), (String) reportParameters.get(PARAM_ROLLING_END_DATE));
        individualGroupWashReportService.setLanguage(String.valueOf(reportParameters.get(USER_LOCALE)));
        individualGroupWashReportService.setDateFormat(String.valueOf(reportParameters.get(USER_DATE_FORMAT_FOR_INDIVIDUAL_GROUP_WASH_REPORT)));
        Collection<IndividualGroupWashDto> reportData = getIndividualGroupWashData(startDate, endDate);
        Collection<ReportCriteria> reportCriteria = getReportCriteria(propertyId, userID, currency,
                Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()),
                Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        reportParameters.put(INDIVIDUAL_GROUP_WASH_DATA, reportData);
        reportParameters.put(INDIVIDUAL_GROUP_WASH_CRITERIA, reportCriteria);
    }

    private LocalDate getLocalDate(String isRollingDateFlag, String specificDateStr, String rollingDateStr) {
        LocalDate date;
        if ("0".equalsIgnoreCase(isRollingDateFlag)) {
            date = LocalDate.parse(specificDateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
        } else {
            date = Instant.ofEpochMilli(dateService.getSpecificDateFromRollingDate(rollingDateStr).getTime())
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
        }
        return date;
    }

    private Collection<IndividualGroupWashDto> getIndividualGroupWashData(LocalDate startDate, LocalDate endDate) {
        return individualGroupWashReportService.getIndividualGroupWashDtos(startDate, endDate);
    }

    private Collection<ReportCriteria> getReportCriteria(Integer propertyId, Integer userID, String currency, Date startDate, Date endDate) {
        Collection<ReportCriteria> criteria = new ArrayList<>();
        List<Object[]> criteriaData = tenantCrudService.findByNativeQuery(CRITERIA_QUERY,
                QueryParameter.with(PROPERTY_ID, propertyId)
                        .and(USER_ID, userID)
                        .and(CURRENCY, currency)
                        .and(PARAM_STARTDATE, startDate)
                        .and(PARAM_ENDDATE, endDate)
                        .parameters());
        ReportCriteria reportCriteria = new ReportCriteria();
        for (Object[] criteriaDatum : criteriaData) {
            reportCriteria.setProperty((String) criteriaDatum[0]);
            reportCriteria.setUser((String) criteriaDatum[1]);
            reportCriteria.setGenerationDate((String) criteriaDatum[2]);
            reportCriteria.setCurrency((String) criteriaDatum[3]);
            reportCriteria.setStartDate((Date) criteriaDatum[4]);
            reportCriteria.setEndDate((Date) criteriaDatum[5]);
            criteria.add(reportCriteria);
        }
        return criteria;
    }

    @VisibleForTesting


    public Response downloadOnlineReportExcel(@HeaderParam("baseReportFolder") String baseReportFolder, Report report) {
        try {
            Map<String, String> reportParamsMap = parseReportParams(report.getReportParams(), PARAM_SEPARATOR);
            reportParamsMap.get(REPORT_UNIT).replace("/public/Reports/", StringUtils.EMPTY);
            generateReport(reportParamsMap, baseReportFolder, report.getFileName(), () -> "false");
            return Response.ok().build();
        } catch (Exception e) {
            return Response.serverError().entity(ExceptionUtils.getMessage(e)).build();
        }
    }

    
    
    
    public ResponseEntity<FileSystemResource> getExcelFileFromGeneratedPath(String reportPath) {
         if(SystemConfig.isCredentialEncryptionEnabled()){
             try {
                 reportPath = EncryptionDecryption.doStrongTextDecryption(new String(Base64.getDecoder().decode(reportPath)));
             } catch (IllegalArgumentException e) {
                    throw new TetrisException(ErrorCode.SERVICE_ERROR, "Invalid report path");
             }
         }
        return readFileAndWriteResponse(reportPath);
    }

    @VisibleForTesting
	public
    ResponseEntity<FileSystemResource> readFileAndWriteResponse(String reportPath) {
        try {
            File file = new File(reportPath);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData(file.getName(), file.getName());
            FileSystemResource fileResource = new FileSystemResource(file);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fileResource);
        } catch (Exception ex) {
            log.error(REPORT_FILE_DOWNLOADING_EXCEPTION, ex);
            throw new TetrisException(ErrorCode.SERVICE_ERROR, REPORT_FILE_DOWNLOADING_EXCEPTION);
        }
    }

    public Map<String, String> parseReportParams(String reportParameters, String paramSeparator) {
        Map<String, String> reportParamsMap = new HashMap<>();
        for (String reportParam : reportParameters.split(paramSeparator)) {
            String[] reportParamNameValue = reportParam.split(REPORT_PARAM_VALUE_SEPARATOR);
            reportParamsMap.put(reportParamNameValue[0], reportParamNameValue.length > 1 ? reportParamNameValue[1] : StringUtils.EMPTY);
        }
        return reportParamsMap;
    }

    private void addTimezone(Map<String, String> reportParameters) {
        readParameter(reportParameters, CORE_PROPERTY_TIME_ZONE.getParameterName(),
                PROPERTY_TIME_ZONE);
    }

    private void readParameter(Map<String, String> reportParameters, String parameterName, String paramKey) {
        String context = PACMAN + DOT + getClientCode() + DOT + getPropertyCode();
        String paramValue = configParamsService.getValue(context, parameterName, true);
        if (isEmpty(paramValue)) {
            reportParameters.put(paramKey, StringUtils.EMPTY);
        } else {
            reportParameters.put(paramKey, paramValue);
        }
    }

    private void addAccountId(Map<String, String> reportParameters) {
        if (reportParameters.get(REPORT_UNIT).equalsIgnoreCase(DATA_EXTRACTION)) {
            String accountId = globalCrudService.findByNativeQuerySingleResult(ACCOUNT_ID_QUERY,
                    with(PROPERTY_ID, getPropertyId()).parameters());
            reportParameters.put(ACCOUNT_ID, accountId);
        }
    }

    private void overrideDateFormatForExcel(Map<String, String> parameters) {
        String output = parameters.get(OUTPUT);
        if (StringUtils.isNotEmpty(output) && output.equalsIgnoreCase(XLSX)) {
            parameters.put(USER_DATE_FORMAT, DD_MMM_YYYY);
        }
    }

    private void addBaseCurrency(Map<String, String> reportParameters) {
        readParameter(reportParameters, RATCHET_YIELD_CURRENCY_CODE, PARAM_BASE_CURRENCY);
    }

    private void buildLoggerForReport(Map<String, String> reportParameters, ReportSource reportSource, G3Report g3Report,
                                      Exception exception, String displayMessage) {
        Map<LoggingContext.LoggingContextKey, Object> context = new HashMap<>();
        context.put(LoggingContext.LoggingContextKey.REPORT_SOURCE, reportSource.name());
        context.put(LoggingContext.LoggingContextKey.START_DATE, reportParameters.get(PARAM_STARTDATE));
        context.put(LoggingContext.LoggingContextKey.END_DATE, reportParameters.get(PARAM_ENDDATE));
        context.put(LoggingContext.LoggingContextKey.ROLLING_START_DATE, reportParameters.get(PARAM_ROLLING_START_DATE));
        context.put(LoggingContext.LoggingContextKey.ROLLING_END_DATE, reportParameters.get(PARAM_ROLLING_END_DATE));
        if (Objects.nonNull(g3Report)) {
            context.put(LoggingContext.LoggingContextKey.REPORT_NAME, g3Report.getActualReportName());
            String reportUnitName = g3Report.getReportUnitName();
            context.put(LoggingContext.LoggingContextKey.REPORT_TYPE, reportUnitName);
        }
        context.put(LoggingContext.LoggingContextKey.REPORT_FORMAT, reportParameters.get(OUTPUT));
        if (exception != null) {
            String message = exception.getMessage();
            context.put(LoggingContext.LoggingContextKey.ERROR_MESSAGE, message);
            String problemDescription = ExceptionLogMessageBuilder.buildMessageWithCause(exception);
            LoggingContext.wrapWithContext(context, v -> log.error(displayMessage + NEXT_LINE + message + NEXT_LINE + " Details: " + problemDescription));
        } else {
            LoggingContext.wrapWithContext(context, v -> log.info(displayMessage));
        }
    }

    protected void addInventoryGroup(Map<String, String> parameters) {
        boolean isAdditionalReportCriteriaEnabled = configParamsService.getParameterValue(IS_ADDITIONAL_REPORT_CRITERIA_ENABLED);
        if (isAdditionalReportCriteriaEnabled && parameters.get(REPORT_UNIT).equalsIgnoreCase(DATA_EXTRACTION)
                && (Boolean.valueOf(parameters.get(IS_MS)) || Boolean.valueOf(parameters.get(IS_BV)) || Boolean.valueOf(parameters.get(IS_FG)))) {

            String inventoryGroupId = parameters.get(PARAM_INVENTORY_GROUP);
            String inventoryGroupName = StringUtils.isEmpty(inventoryGroupId) || "-1".equals(inventoryGroupId)
                    ? getText("common.property", String.valueOf(parameters.get(USER_LOCALE)))
                    : ((InventoryGroup) tenantCrudService.findByNamedQuerySingleResult(InventoryGroup.GET_BY_ID, QueryParameter.with("id", Integer.valueOf(inventoryGroupId)).parameters())).getName();

            parameters.put(PARAM_INVENTORY_GROUP_NAME, inventoryGroupName);
            parameters.put(PARAM_IS_ADDITIONAL_REPORT_CRITERIA_ENABLED, Boolean.toString(isAdditionalReportCriteriaEnabled));
        }
    }

    private String getText(String resourceKey, String language) {
        return ResourceUtil.getText(resourceKey, new Locale(language));
    }

}
