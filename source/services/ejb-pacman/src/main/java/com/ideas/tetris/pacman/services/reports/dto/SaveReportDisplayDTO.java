package com.ideas.tetris.pacman.services.reports.dto;


import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SaveReportDisplayDTO {

    private int id;
    private String name;
    private String actualReportName;
    private String reportParameters;
    private String pageCode;
    private Integer createdByUserId;
    private LocalDateTime createdDate;
    private Integer lastUpdatedByUser;
    private LocalDateTime lastUpdatedDate;
}
