package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.OutOfOrderDTO;
import com.ideas.tetris.pacman.services.outoforderoverride.entity.OutOfOrderOverride;
import com.ideas.tetris.pacman.services.security.User;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.*;

@Component
@Transactional
public class OutOfOrderOverrideService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    public List<OutOfOrderDTO> getOutOfOrderOverride(DatafeedRequest datafeedRequest) {

        Date startDate = datafeedRequest.getStartDate();
        Date endDate = datafeedRequest.getEndDate();

        int startPosition = datafeedRequest.getStartPosition();
        int resultLimit = datafeedRequest.getSize();

        List<AccomType> allAccomTypes = getAccomTypes();
        Map<Integer, String> accomIdToAccomTypeMap = getAccomTypeMap(allAccomTypes);
        Map<String, OutOfOrderOverride> outOfOrderOverrideDataAsMap = getOutOfOrderOverrideDataAsMap(startDate, endDate, startPosition, resultLimit);

        return getAccomActivitiesData(startDate, endDate, startPosition, resultLimit).stream()
                .map(accomActivity -> getOutOfOrderDTO(outOfOrderOverrideDataAsMap, accomActivity, accomIdToAccomTypeMap.get(accomActivity.getAccomTypeId())))
                .collect(Collectors.toCollection(ArrayList::new));
    }

    private Map<Integer, String> getAccomTypeMap(List<AccomType> allAccomTypes) {
        return allAccomTypes.stream().collect(Collectors.toMap(AccomType::getId, AccomType::getAccomTypeCode));
    }

    private List<AccomType> getAccomTypes() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_ACTIVE_VALID_CAPACITY,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    private OutOfOrderDTO getOutOfOrderDTO(Map<String, OutOfOrderOverride> overrideHashMap, AccomActivity accomActivity, String roomTypeCode) {
        final Date occupancyDate = accomActivity.getOccupancyDate();
        final String occupancyAccomDate = occupancyDate.toString() + accomActivity.getAccomTypeId().toString();
        if (overrideHashMap.containsKey(occupancyAccomDate) && accomActivity.getSnapShotDate().before(LocalDateUtils.toDate(overrideHashMap.get(occupancyAccomDate).getCreateDate()))) {
            OutOfOrderOverride outOfOverrideData = overrideHashMap.get(occupancyAccomDate);
            return new OutOfOrderDTO(occupancyDate, outOfOverrideData.getRoomType().getAccomTypeCode(), outOfOverrideData.getValue(),
                    LocalDateUtils.toDate(outOfOverrideData.getLastUpdatedDate()), getEmailForLastUpdatedBy(outOfOverrideData));
        }
        return new OutOfOrderDTO(occupancyDate, roomTypeCode, getOutOfOrder(accomActivity), null, null);
    }

    private String getEmailForLastUpdatedBy(OutOfOrderOverride outOfOverrideData) {
        if (useUniqueUserIDInsteadOfEmailEnabled()) {
            return getUsersNameForLastUpdatedBy(outOfOverrideData.getLastUpdatedByUserId());
        }
        return getEmailForLastUpdatedBy(outOfOverrideData.getLastUpdatedByUserId());
    }

    private int getOutOfOrder(AccomActivity accomActivity) {
        return accomActivity.getRoomsNotAvailableOther().intValue() + (accomActivity.getRoomsNotAvailableMaintenance().intValue());
    }

    private Map<String, OutOfOrderOverride> getOutOfOrderOverrideDataAsMap(Date startDate, Date endDate, int startPosition, int resultLimit) {
        List<OutOfOrderOverride> outOfOrderOverrideList = tenantCrudService.findByNamedQuery(OutOfOrderOverride.FIND_OUT_OF_ORDER_OVERRIDES_BY_DATES,
                QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).parameters(), startPosition, resultLimit);
        if (outOfOrderOverrideList.isEmpty()) {
            return Collections.emptyMap();
        }
        return outOfOrderOverrideList.stream()
                .collect(Collectors.toMap(this::getKey, Function.identity(), (a, b) -> b));
    }

    private String getKey(OutOfOrderOverride outOfOrderOverride) {
        return outOfOrderOverride.getOccupancyDate().toString().substring(0, 10) + outOfOrderOverride.getRoomType().getId().toString();
    }

    private List<AccomActivity> getAccomActivitiesData(Date startDate, Date endDate, int startPosition, int resultLimit) {
        return tenantCrudService.findByNamedQuery(
                AccomActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID_AND_ACCOM_TYPE_IDS_NON_COMPONENT_ROOM,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .parameters(), startPosition, resultLimit
        );
    }

    private boolean useUniqueUserIDInsteadOfEmailEnabled() {
        return Boolean.parseBoolean(configParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.USE_UNIQUE_USERID_INSTEADOF_EMAILID.getParameterName()));
    }

    private String getEmailForLastUpdatedBy(Integer userId) {
        return getUserById(userId)
                .map(User::getEmail)
                .orElse("-");
    }

    private Optional<User> getUserById(Integer userId) {
        return Optional.ofNullable(userId)
                .map(id -> tenantCrudService.find(User.class, id));
    }

    private String getUsersNameForLastUpdatedBy(Integer userId) {
        return getUserById(userId)
                .map(User::getName)
                .orElse("-");
    }
}
