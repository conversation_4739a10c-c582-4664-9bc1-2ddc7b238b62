package com.ideas.tetris.pacman.services.forecast.repository;

import com.ideas.tetris.pacman.services.forecast.dto.InhouseLosChgSummaryDto;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class InhouseLosChgSummaryRepository {
    @Autowired
    private SasDbToolService sasDbToolService;

    public List<InhouseLosChgSummaryDto> retrieveInhouseLosChgSummaries(Integer propertyId) {
        var query = "select mkt_seg_id as mktSegId, accom_type_id as accomTypeId, arrival_dt as arrivalDt format best12. , capture_dt as captureDt, los_ini as losIni, los_upd as losUpd, mkt_upd as mktUpd, accom_type_upd as accomTypeUpd, arrivals, rev_ini as revIni, rev_upd as revUpd from tenant.inhouse_los_chg_summary order by mktSegId, accomTypeId, arrivalDt;";
        var sasDbQueryResult = sasDbToolService.executeQueryTenant(propertyId,query);
        if (Objects.isNull(sasDbQueryResult)) {
            return List.of();
        }
        var data = sasDbQueryResult.getData();
        if (Objects.isNull(data)) {
            return List.of();
        }
        return data.stream()
                .map(list -> new InhouseLosChgSummaryDto(
                        ((Double)list.get(0)).intValue(),
                        ((Double)list.get(1)).intValue(),
                        LocalDateUtils.toJavaLocalDateFromSasDate(((Double) list.get(2)).longValue()),
                        LocalDateUtils.toJavaLocalDateFromSasDate(((Double) list.get(3)).longValue()),
                        ((Double)list.get(4)).intValue(),
                        ((Double)list.get(5)).intValue(),
                        ((Double)list.get(6)).intValue(),
                        ((Double)list.get(7)).intValue(),
                        ((Double)list.get(8)).intValue(),
                        ((Double)list.get(9)),
                        (Double) list.get(10)))
                .collect(Collectors.toList());
    }
}
