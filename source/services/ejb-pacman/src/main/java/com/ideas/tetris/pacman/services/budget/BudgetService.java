package com.ideas.tetris.pacman.services.budget;

import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.budget.dto.BudgetDataDto;
import com.ideas.tetris.pacman.services.budget.dto.BudgetType;
import com.ideas.tetris.pacman.services.budget.dto.PercentagePattern;
import com.ideas.tetris.pacman.services.budget.entity.BudgetConfig;
import com.ideas.tetris.pacman.services.budget.entity.BudgetData;
import com.ideas.tetris.pacman.services.budget.entity.BudgetLevel;
import com.ideas.tetris.pacman.services.budget.entity.UserForecastData;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dashboard.DashboardMetrics2Cache;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateInterval;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang3.mutable.MutableLong;
import org.apache.commons.lang3.mutable.MutableObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.util.Executor.computeIfNull;
import static com.ideas.tetris.pacman.util.Executor.executeIfFalse;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Component
@Transactional
public class BudgetService {
    public static final String START_DATE = "startDate";
    public static final String END_DATE = "endDate";
    @TenantCrudServiceBean.Qualifier
	@Qualifier("tenantCrudServiceBean")
    @Autowired
	protected CrudService tenantCrudService;

    @Autowired
	protected AbstractMultiPropertyCrudService multiPropertyCrudService;

    @Autowired
	private DashboardMetrics2Cache dashboardMetrics2Cache;

    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
	protected DateService dateService;

    public List<BudgetLevel> getBudgetLevels() {
        return tenantCrudService.findByNativeQuery(BudgetLevel.BY_MODULE_TYPE,
                QueryParameter.with("moduleType", ModuleType.BUDGET.getKey()).parameters(),
                this::mapRowForBudgetLevel
        ).stream().collect(Collectors.toList());
    }


    private BudgetLevel mapRowForBudgetLevel(Object[] row) {
        BudgetLevel level = new BudgetLevel();
        level.setId(((BigInteger) row[0]).intValue());
        level.setBudgetLevel((String) row[1]);
        level.setCreatedDTTM((Date) row[2]);
        return level;
    }

    public List<BudgetLevel> getUserForecastLevels() {
        List<BudgetLevel> budgetLevels = tenantCrudService.findByNativeQuery(BudgetLevel.BY_MODULE_TYPE,
                QueryParameter.with("moduleType", ModuleType.USER_FORECAST.getKey()).parameters(),
                this::mapRowForBudgetLevel
        );

        if(!isBusinessTypeEnabledForUserForecast()){
            budgetLevels.removeIf(budgetLevel -> budgetLevel.getId().equals(1));
        }
        return budgetLevels;
    }


    private boolean isBusinessTypeEnabledForUserForecast() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_BUSINESS_TYPE_FOR_USER_FORECAST);
    }

    public BudgetConfig updateBudgetConfig(BudgetLevel budgetLevel, String displayName) {
        executeIfFalse(isBudgetLevelSameAsSelected(budgetLevel), value -> deleteBudgetData());
        BudgetConfig entity = computeIfNull(getBudgetConfig(), BudgetConfig::new);
        entity.setBudgetLevel(budgetLevel);
        entity.setBudgetDisplayName(displayName);
        entity.setModuleName(ModuleType.BUDGET.getKey());
        tenantCrudService.save(entity);
        return entity;
    }

    public BudgetConfig updateUserForecastConfig(BudgetLevel userForecastLevel, String displayName) {
        executeIfFalse(isUserForecastLevelSameAsSelected(userForecastLevel), value -> deleteUserForecastDataAndRemoveKeysFromCache());
        BudgetConfig entity = computeIfNull(getUserForecastConfig(), BudgetConfig::new);
        entity.setBudgetLevel(userForecastLevel);
        entity.setBudgetDisplayName(displayName);
        entity.setModuleName(ModuleType.USER_FORECAST.getKey());
        tenantCrudService.save(entity);
        return entity;
    }

    public void deleteUserForecast() {
        deleteUserForecastConfiguration(getUserForecastConfig());
        deleteUserForecastDataAndRemoveKeysFromCache();
    }

    public void deleteUserForecastDataAndRemoveKeysFromCache() {
        deleteUserForecastData();
        dashboardMetrics2Cache.removeUserForecastRelatedKeys();
    }

    public void deleteUserForecastData() {
        tenantCrudService.deleteAll(UserForecastData.class);
    }

    public void deleteUserForecastData(Integer businessGroupId) {
        tenantCrudService.executeUpdateByNamedQuery(UserForecastData.DELETE_USERFORECAST_DATA_FOR_SEGMENT, QueryParameter.with("businessGroupId", businessGroupId).parameters());
        dashboardMetrics2Cache.removeUserForecastRelatedKeys();
    }


    public void deleteUserForecastConfiguration(BudgetConfig userForecastConfig) {
        if (userForecastConfig != null) {
            tenantCrudService.delete(userForecastConfig);
        }
    }

    public BudgetConfig getBudgetConfig() {
        return tenantCrudService.findByNamedQuerySingleResult(BudgetConfig.GET_BUDGET_BY_MODULE_NAME,
                QueryParameter.with("moduleName", ModuleType.BUDGET.getKey()).parameters());
    }

    public BudgetConfig getUserForecastConfig() {
        return tenantCrudService.findByNamedQuerySingleResult(BudgetConfig.GET_BUDGET_BY_MODULE_NAME,
                QueryParameter.with("moduleName", ModuleType.USER_FORECAST.getKey()).parameters());
    }

    public boolean isBudgetEnabled() {
        return getBudgetConfig() != null;
    }

    public boolean isUserForecastEnabled() {
        return getUserForecastConfig() != null;
    }

    public List<BudgetConfig> getBudgetConfigForProperties(List<Integer> propertyIds) {
        return getBudgetConfigs(propertyIds);
    }

    public String getBudgetDisplayName() {
        List<BudgetConfig> budgetConfigs = getBudgetConfigs(Arrays.asList(PacmanWorkContextHelper.getPropertyId()));
        return null != budgetConfigs && !budgetConfigs.isEmpty() && null != budgetConfigs.get(0) ? budgetConfigs.get(0).getBudgetDisplayName() : "";
    }

    public String getUserForecastDisplayName() {
        BudgetConfig userForecastConfig = getUserForecastConfig();
        return null != userForecastConfig ? userForecastConfig.getBudgetDisplayName() : "";
    }

    private List<BudgetConfig> getBudgetConfigs(List<Integer> propertyIds) {
        return multiPropertyCrudService.findByNamedQuerySingleResult(propertyIds,
                BudgetConfig.GET_BUDGET_BY_MODULE_NAME,
                QueryParameter.with("moduleName", ModuleType.BUDGET.getKey()).parameters());
    }

    public boolean isUserForecastEnabled(List<Integer> propertyIds) {
        List<BudgetConfig> userForecastConfigs = multiPropertyCrudService.findByNamedQuerySingleResult(propertyIds, BudgetConfig.GET_BUDGET_BY_MODULE_NAME,
                QueryParameter.with("moduleName", ModuleType.USER_FORECAST.getKey()).parameters());
        return userForecastConfigs.stream().allMatch(Objects::nonNull);
    }

    public void deleteBudgetConfiguration(BudgetConfig budgetConfig) {
        if (budgetConfig != null) {
            tenantCrudService.delete(budgetConfig);
            dashboardMetrics2Cache.removeBudgetRelatedKeys();
        }
    }

    public void deleteBudgetData() {
        tenantCrudService.deleteAll(BudgetData.class);
        dashboardMetrics2Cache.removeBudgetRelatedKeys();
    }

    public BigDecimal getUserForecastOccupancySummary(Date startDate, Date endDate, List<Integer> propertyIds) {
        List<Long> userForecastOccupancy = multiPropertyCrudService.findByNamedQuerySingleResult(propertyIds, UserForecastData.USER_FORECAST_OCCUPANCY, QueryParameter.with(START_DATE, new org.joda.time.LocalDate(startDate))
                .and(END_DATE, new org.joda.time.LocalDate(endDate)).parameters());
        MutableLong total = new MutableLong(0L);
        if (isNotEmpty(userForecastOccupancy)) {
            userForecastOccupancy.stream().filter(Objects::nonNull).forEach(total::add);
        }
        return BigDecimal.valueOf(total.getValue());
    }

    public BigDecimal getBudgetOccupancySummary(Date startDate, Date endDate, List<Integer> propertyIds) {

        List<Long> budgetOccupancy = multiPropertyCrudService.findByNamedQuerySingleResult(propertyIds, BudgetData.BUDGET_OCCUPANCY, QueryParameter.with(START_DATE, DateUtil.convertJavaUtilDateToLocalDate(startDate, true))
                .and(END_DATE, DateUtil.convertJavaUtilDateToLocalDate(endDate, true)).parameters());
        BigDecimal totalBudgetOccupancy = BigDecimal.ZERO;

        if (isNotEmpty(budgetOccupancy)) {
            for (Long occupancy : budgetOccupancy) {
                if (null != occupancy) {
                    totalBudgetOccupancy = totalBudgetOccupancy.add(BigDecimal.valueOf(occupancy));
                }
            }
        }
        return totalBudgetOccupancy;
    }

    public BigDecimal getUserForecastRevenueSummary(Date startDate, Date endDate, List<Integer> propertyIds) {
        List<BigDecimal> userForecastRevenue = multiPropertyCrudService.findByNamedQuerySingleResult(propertyIds, UserForecastData.USER_FORECAST_REVENUE, QueryParameter.with(START_DATE, new org.joda.time.LocalDate(startDate))
                .and(END_DATE, new org.joda.time.LocalDate(endDate)).parameters());
        MutableObject<BigDecimal> total = new MutableObject(BigDecimal.ZERO);
        if (isNotEmpty(userForecastRevenue)) {
            userForecastRevenue.stream().filter(Objects::nonNull).forEach(value -> total.setValue(total.getValue().add(value)));
        }
        return total.getValue();
    }

    public BigDecimal getBudgetRevenueSummary(Date startDate, Date endDate, List<Integer> propertyIds) {
        List<BigDecimal> budgetRevenue = multiPropertyCrudService.findByNamedQuerySingleResult(propertyIds, BudgetData.BUDGET_REVENUE, QueryParameter.with(START_DATE, DateUtil.convertJavaUtilDateToLocalDate(startDate, true))
                .and(END_DATE, DateUtil.convertJavaUtilDateToLocalDate(endDate, true)).parameters());
        BigDecimal totalBudgetRevenue = BigDecimal.ZERO;

        if (isNotEmpty(budgetRevenue)) {
            for (BigDecimal occupancy : budgetRevenue) {
                if (null != occupancy) {
                    totalBudgetRevenue = totalBudgetRevenue.add(occupancy);
                }
            }
        }
        return totalBudgetRevenue;
    }

    protected LocalDate getEndDateFor(LocalDate startDate) {
        return startDate.plusMonths(1).minusDays(1);
    }

    protected LocalDate getStartDateFor(LocalDate selectedDay) {
        return LocalDate.of(selectedDay.getYear(), selectedDay.getMonthValue(), 1);
    }

    protected List<BudgetDataDto> getRoomSoldsAndRevenueForTheDateRange(LocalDate startDate, LocalDate endDate, int segmentId, BudgetType budgetType) {
        List<Object[]> results = tenantCrudService.findByNamedQuery(budgetType.getQueryName(),
                QueryParameter.with(budgetType.getColumnName(), segmentId)
                        .and(START_DATE, startDate)
                        .and(END_DATE, endDate).parameters());

        return mapRowsToRoomSoldsRevenueDto(results);
    }

    private List<BudgetDataDto> mapRowsToRoomSoldsRevenueDto(List<Object[]> results) {
        List<BudgetDataDto> roomsSoldAndRevenues = new ArrayList<>();
        for (Object[] row : results) {
            BudgetDataDto budgetDataDto = new BudgetDataDto();
            budgetDataDto.setSegment((String) row[0]);
            budgetDataDto.setOccupancyDate(row[2] == null ? LocalDate.now() : LocalDate.parse(row[2].toString()));
            budgetDataDto.setRoomsSold(((BigDecimal) row[3]).intValue());
            budgetDataDto.setRoomRevenue((BigDecimal) row[4]);
            roomsSoldAndRevenues.add(budgetDataDto);
        }
        return roomsSoldAndRevenues;
    }

    protected List<BudgetDataDto> getRoomsSoldsAndRevenueFromSameMonthLastYear(JavaLocalDateInterval dateRange, int segmentId, BudgetType budgetType) {
        LocalDate startDate = dateRange.getStartDate().minusYears(1);
        LocalDate endDate = dateRange.getEndDate().minusYears(1);
        return getRoomSoldsAndRevenueForTheDateRange(startDate, endDate, segmentId, budgetType);
    }

    protected Integer getTotalOfRoomsSoldsFor(List<BudgetDataDto> roomsSoldsAndRevenue) {
        return roomsSoldsAndRevenue.stream().mapToInt(BudgetDataDto::getRoomsSold).sum();
    }

    protected BigDecimal getTotalOfRevenueFor(List<BudgetDataDto> roomsSoldsAndRevenue) {
        double total = roomsSoldsAndRevenue.stream().mapToDouble(budgetDataDto -> budgetDataDto.getRoomRevenue().doubleValue()).sum();
        return BigDecimal.valueOf(total);
    }

    protected int getTotalOfRoomsSoldsForDow(List<BudgetDataDto> roomsSoldsAndRevenue, DayOfWeek dayOfWeek) {
        return roomsSoldsAndRevenue.stream().filter(budgetDataDto ->
                        budgetDataDto.getOccupancyDate().getDayOfWeek().getValue() == dayOfWeek.getValue()
                )
                .mapToInt(BudgetDataDto::getRoomsSold).sum();
    }

    protected BigDecimal getTotalOfRevenueForDow(List<BudgetDataDto> roomsSoldsAndRevenue, DayOfWeek dayOfWeek) {
        double sum = roomsSoldsAndRevenue.stream().filter(budgetDataDto ->
                        budgetDataDto.getOccupancyDate().getDayOfWeek().getValue() == dayOfWeek.getValue()
                )
                .mapToDouble(budgetDataDto -> budgetDataDto.getRoomRevenue().doubleValue()).sum();
        return BigDecimal.valueOf(sum);
    }

    protected BigDecimal calculatePercentageForTheDow(BigDecimal total, BigDecimal totalOfPerDow) {
        BigDecimal hundred = BigDecimal.valueOf(100);
        return (totalOfPerDow.multiply(hundred)).divide(total, new MathContext(2, RoundingMode.HALF_EVEN));
    }

    public PercentagePattern getPercentagePatternForRoomsSolds(int segmentId, JavaLocalDateInterval localDateInterval, BudgetType budgetType) {
        List<BudgetDataDto> roomsSoldsAndRevenueForSameMonthLastYear = getRoomsSoldsAndRevenueFromSameMonthLastYear(localDateInterval, segmentId, budgetType);
        if (isAllDowsAvaialableForRoomSolds(roomsSoldsAndRevenueForSameMonthLastYear)) {
            return calculatePercentagePatternForRoomsSolds(roomsSoldsAndRevenueForSameMonthLastYear);
        }
        return extractRoomsSoldsPercentagePatternFromLastOneYear(segmentId, budgetType);
    }

    private PercentagePattern extractRoomsSoldsPercentagePatternFromLastOneYear(int segmentId, BudgetType budgetType) {
        LocalDate oneYearDateBeforeCaughtupDate = getOneYearDateBeforeCaughtupDate();
        LocalDate endDate = JavaLocalDateUtils.toJavaLocalDate(dateService.getCaughtUpLocalDate().minusDays(1));
        List<BudgetDataDto> roomsSoldsAndRevenueForLastOneYear = getRoomSoldsAndRevenueForTheDateRange(oneYearDateBeforeCaughtupDate, endDate, segmentId, budgetType);
        if (isAllDowsAvaialableForRoomSolds(roomsSoldsAndRevenueForLastOneYear)) {
            return calculatePercentagePatternForRoomsSolds(roomsSoldsAndRevenueForLastOneYear);
        }
        return defaultPercentagePattern();
    }

    private PercentagePattern defaultPercentagePattern() {
        return new PercentagePattern(0, 0, 0, 0, 0, 0, 0);
    }

    protected boolean isAllDowsAvaialableForRoomSolds(List<BudgetDataDto> roomsSoldsAndRevenue) {
        return Arrays.asList(DayOfWeek.values()).stream()
                .allMatch(dow -> isAvailableForRooms(roomsSoldsAndRevenue, dow));
    }

    private boolean isAvailableForRooms(List<BudgetDataDto> roomsSoldsAndRevenue, DayOfWeek dow) {
        return roomsSoldsAndRevenue.stream().anyMatch(budgetDataDto -> budgetDataDto.getOccupancyDate().getDayOfWeek().getValue() == dow.getValue() && budgetDataDto.getRoomsSold() > 0);
    }

    private PercentagePattern calculatePercentagePatternForRoomsSolds(List<BudgetDataDto> roomsSoldsAndRevenue) {
        Integer totalOfRoomsSolds = getTotalOfRoomsSoldsFor(roomsSoldsAndRevenue);
        int monday = getTotalOfRoomsSoldsForDow(roomsSoldsAndRevenue, DayOfWeek.MONDAY);
        int tuesday = getTotalOfRoomsSoldsForDow(roomsSoldsAndRevenue, DayOfWeek.TUESDAY);
        int wednesday = getTotalOfRoomsSoldsForDow(roomsSoldsAndRevenue, DayOfWeek.WEDNESDAY);
        int thursday = getTotalOfRoomsSoldsForDow(roomsSoldsAndRevenue, DayOfWeek.THURSDAY);
        int friday = getTotalOfRoomsSoldsForDow(roomsSoldsAndRevenue, DayOfWeek.FRIDAY);
        int saturday = getTotalOfRoomsSoldsForDow(roomsSoldsAndRevenue, DayOfWeek.SATURDAY);
        int sunday = getTotalOfRoomsSoldsForDow(roomsSoldsAndRevenue, DayOfWeek.SUNDAY);
        Integer mondayPercentage = calculatePercentageForTheDow(BigDecimal.valueOf(totalOfRoomsSolds), BigDecimal.valueOf(monday)).intValue();
        Integer tuesdayPercentage = calculatePercentageForTheDow(BigDecimal.valueOf(totalOfRoomsSolds), BigDecimal.valueOf(tuesday)).intValue();
        Integer wednesdayPercentage = calculatePercentageForTheDow(BigDecimal.valueOf(totalOfRoomsSolds), BigDecimal.valueOf(wednesday)).intValue();
        Integer thursdayPercentage = calculatePercentageForTheDow(BigDecimal.valueOf(totalOfRoomsSolds), BigDecimal.valueOf(thursday)).intValue();
        Integer fridayPercentage = calculatePercentageForTheDow(BigDecimal.valueOf(totalOfRoomsSolds), BigDecimal.valueOf(friday)).intValue();
        Integer saturdayPercentage = calculatePercentageForTheDow(BigDecimal.valueOf(totalOfRoomsSolds), BigDecimal.valueOf(saturday)).intValue();
        Integer sundayPercentage = calculatePercentageForTheDow(BigDecimal.valueOf(totalOfRoomsSolds), BigDecimal.valueOf(sunday)).intValue();
        PercentagePattern pattern = new PercentagePattern(mondayPercentage, tuesdayPercentage, wednesdayPercentage, thursdayPercentage,
                fridayPercentage, saturdayPercentage, sundayPercentage);
        return pattern.roundOf();
    }

    public PercentagePattern getPercentagePatternForRevenue(int segmentId, JavaLocalDateInterval localDateInterval, BudgetType budgetType) {
        List<BudgetDataDto> roomsSoldsAndRevenueForSameMonthLastYear = getRoomsSoldsAndRevenueFromSameMonthLastYear(localDateInterval, segmentId, budgetType);
        if (isAllDowsAvaialableForRevenue(roomsSoldsAndRevenueForSameMonthLastYear)) {
            return calculatePercentagePatternForRevenue(roomsSoldsAndRevenueForSameMonthLastYear);
        }
        return extractRevenuePercentagePatternFromLastOneYear(segmentId, budgetType);
    }

    private PercentagePattern extractRevenuePercentagePatternFromLastOneYear(int segmentId, BudgetType budgetType) {
        LocalDate oneYearDateBeforeCaughtupDate = getOneYearDateBeforeCaughtupDate();
        LocalDate endDate = JavaLocalDateUtils.toJavaLocalDate(dateService.getCaughtUpLocalDate().minusDays(1));
        List<BudgetDataDto> roomsSoldsAndRevenueForLastOneYear = getRoomSoldsAndRevenueForTheDateRange(oneYearDateBeforeCaughtupDate, endDate, segmentId, budgetType);
        if (isAllDowsAvaialableForRevenue(roomsSoldsAndRevenueForLastOneYear)) {
            return calculatePercentagePatternForRevenue(roomsSoldsAndRevenueForLastOneYear);
        }
        return defaultPercentagePattern();
    }

    private PercentagePattern calculatePercentagePatternForRevenue(List<BudgetDataDto> roomsSoldsAndRevenue) {
        BigDecimal totalOfRoomsSolds = getTotalOfRevenueFor(roomsSoldsAndRevenue);
        BigDecimal monday = getTotalOfRevenueForDow(roomsSoldsAndRevenue, DayOfWeek.MONDAY);
        BigDecimal tuesday = getTotalOfRevenueForDow(roomsSoldsAndRevenue, DayOfWeek.TUESDAY);
        BigDecimal wednesday = getTotalOfRevenueForDow(roomsSoldsAndRevenue, DayOfWeek.WEDNESDAY);
        BigDecimal thursday = getTotalOfRevenueForDow(roomsSoldsAndRevenue, DayOfWeek.THURSDAY);
        BigDecimal friday = getTotalOfRevenueForDow(roomsSoldsAndRevenue, DayOfWeek.FRIDAY);
        BigDecimal saturday = getTotalOfRevenueForDow(roomsSoldsAndRevenue, DayOfWeek.SATURDAY);
        BigDecimal sunday = getTotalOfRevenueForDow(roomsSoldsAndRevenue, DayOfWeek.SUNDAY);
        Integer mondayPercentage = calculatePercentageForTheDow(totalOfRoomsSolds, monday).intValue();
        Integer tuesdayPercentage = calculatePercentageForTheDow(totalOfRoomsSolds, tuesday).intValue();
        Integer wednesdayPercentage = calculatePercentageForTheDow(totalOfRoomsSolds, wednesday).intValue();
        Integer thursdayPercentage = calculatePercentageForTheDow(totalOfRoomsSolds, thursday).intValue();
        Integer fridayPercentage = calculatePercentageForTheDow(totalOfRoomsSolds, friday).intValue();
        Integer saturdayPercentage = calculatePercentageForTheDow(totalOfRoomsSolds, saturday).intValue();
        Integer sundayPercentage = calculatePercentageForTheDow(totalOfRoomsSolds, sunday).intValue();
        PercentagePattern pattern = new PercentagePattern(mondayPercentage, tuesdayPercentage, wednesdayPercentage, thursdayPercentage,
                fridayPercentage, saturdayPercentage, sundayPercentage);
        return pattern.roundOf();
    }

    protected LocalDate getOneYearDateBeforeCaughtupDate() {
        LocalDate caughtUpDate = JavaLocalDateUtils.toJavaLocalDate(dateService.getCaughtUpLocalDate());
        return caughtUpDate.minusYears(1);
    }

    public boolean isAllDowsAvaialableForRevenue(List<BudgetDataDto> roomsSoldsAndRevenue) {
        return Arrays.asList(DayOfWeek.values()).stream()
                .allMatch(dow -> isAvailableForRevenue(roomsSoldsAndRevenue, dow));
    }

    private boolean isAvailableForRevenue(List<BudgetDataDto> roomsSoldsAndRevenue, DayOfWeek dow) {
        return roomsSoldsAndRevenue.stream().anyMatch(budgetDataDto -> budgetDataDto.getOccupancyDate().getDayOfWeek().getValue() == dow.getValue() && null != budgetDataDto.getRoomRevenue() && budgetDataDto.getRoomRevenue().doubleValue() > 0);
    }

    public boolean isBudgetLevelSameAsSelected(BudgetLevel level) {
        return isBudgetEnabled() && Objects.nonNull(level) && getBudgetConfig().getBudgetLevel().getId().equals(level.getId());
    }

    public boolean isUserForecastLevelSameAsSelected(BudgetLevel level) {
        BudgetConfig userForecastConfig = getUserForecastConfig();
        return Objects.nonNull(userForecastConfig) && Objects.nonNull(level) && userForecastConfig.getBudgetLevel().getId().equals(level.getId());
    }

    public boolean isBudgetConfiguredAtBusinessViewLevel() {
        BudgetConfig budgetConfig = getBudgetConfig();
        return null != budgetConfig && budgetConfig.getBudgetLevel().checkIfBusinessView();
    }

    public boolean isUserConfiguredAtBusinessViewLevel() {
        BudgetConfig userForecastConfig = getUserForecastConfig();
        return null != userForecastConfig && userForecastConfig.getBudgetLevel().checkIfBusinessView();
    }

    public void deleteBudgetDataForSegment(Integer segmentId) {
        tenantCrudService.executeUpdateByNamedQuery(BudgetData.DELETE_BUDGET_DATA_FOR_SEGMENT, QueryParameter.with("segmentID", segmentId).parameters());
        dashboardMetrics2Cache.removeBudgetRelatedKeys();
    }
}
