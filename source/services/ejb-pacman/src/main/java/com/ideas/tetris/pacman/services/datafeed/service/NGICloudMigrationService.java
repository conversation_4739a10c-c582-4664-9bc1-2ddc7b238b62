package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.systemconfig.PacmanSystemConfigService;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterValue;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.NGI_CLOUD_MIGRATION_COMPLETION_DATE;
import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.NGI_CLOUD_MIGRATION_STABLE;
import static com.ideas.tetris.pacman.common.constants.Constants.GLOBAL_DATE_PARAM_FORMAT;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class NGICloudMigrationService {
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
	private JobServiceLocal jobServiceLocal;
    @Autowired
    PacmanSystemConfigService pacmanSystemConfigService;
    private static final String CONTEXT_DELIMITER = ".";
    static final int DAYS_TO_SET_MIGRATION_STABLE = 7;

    public Set<String> getReadyToBeStableProperties() {
        final Set<String> notYetStableProperties = new HashSet<>();
        final Set<String> readyProperties = new HashSet<>();

        LocalDateTime localDateTime = LocalDateTime.now();
        localDateTime = localDateTime.minusDays(DAYS_TO_SET_MIGRATION_STABLE);
        localDateTime = localDateTime.withHour(0);
        localDateTime = localDateTime.withMinute(0);
        String dateString = localDateTime.format(DateTimeFormatter.ofPattern(GLOBAL_DATE_PARAM_FORMAT));

        List<ConfigParameterValue> readyPropertiesConfigParameterValuesByUIDate =
                pacmanConfigParamsService.getParameterValueByParameterNameAndValue(NGI_CLOUD_MIGRATION_COMPLETION_DATE.value(), dateString);

        List<ConfigParameterValue> notYetStablePropertiesConfigParameterValues =
                pacmanConfigParamsService.getParameterValueByParameterNameAndPredefinedValueAndPredefinedValueType(NGI_CLOUD_MIGRATION_STABLE.value(), "false", "boolean");

        notYetStablePropertiesConfigParameterValues.forEach(configParameterValue -> {
            if (configParameterValue.getContext().contains(CONTEXT_DELIMITER)) {
                String[] context = configParameterValue.getContext().split("\\.");
                notYetStableProperties.add(context[1] + CONTEXT_DELIMITER + context[2]);
            }
        });

        readyPropertiesConfigParameterValuesByUIDate.forEach(configParameterValue -> {
            if (configParameterValue.getContext().contains(CONTEXT_DELIMITER)) {
                String[] context = configParameterValue.getContext().split("\\.");
                readyProperties.add(context[1] + CONTEXT_DELIMITER + context[2]);
            }
        });

        notYetStableProperties.retainAll(readyProperties);

        return notYetStableProperties;
    }

    public Date getJobCompletionDate(String clientCode, String propertyCode) {
        Object completionDate =
                pacmanConfigParamsService.getParameterValue(clientCode, propertyCode, NGI_CLOUD_MIGRATION_COMPLETION_DATE);

        return (Date) completionDate;
    }

    public boolean oxiJobLimitReached() {
        int limit = pacmanSystemConfigService.getOxiJobParallelLimit();

        List<Integer> propertiesWithActiveOxiJob = jobServiceLocal.findPropertyIdsWithActiveJobsOfType(JobName.OxiCloudMigrationUtilityJob);

        return propertiesWithActiveOxiJob != null && propertiesWithActiveOxiJob.size() >= limit;
    }

    public boolean htngJobLimitReached(JobName jobName) {
        int limit = pacmanSystemConfigService.getHtngJobParallelLimit();
        List<Integer> propertiesWithActiveHtngJob = jobServiceLocal.findPropertyIdsWithActiveJobsOfType(jobName);

        return propertiesWithActiveHtngJob != null && propertiesWithActiveHtngJob.size() >= limit;
    }

}
