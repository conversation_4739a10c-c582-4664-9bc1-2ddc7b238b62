package com.ideas.tetris.pacman.services.property.configuration.file;

import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import org.apache.commons.io.FileUtils;
import org.apache.log4j.Logger;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class PropertyConfigurationFileWriter {

    private static final Logger LOGGER = Logger.getLogger(PropertyConfigurationFileWriter.class.getName());

    public void writeFile(File outputFile, List<PropertyConfigurationDto> propertyConfigurationDtos) {
        if (propertyConfigurationDtos == null) {
            LOGGER.error("No records to add to file!!!");
            return;
        }

        // Delete file if it already exists
        if (outputFile.exists()) {
            outputFile.delete();
        }

        try {
            // Create new file
            outputFile.createNewFile();

            List<String> linesToWriteToFile = new ArrayList<String>();
            for (PropertyConfigurationDto propertyConfigurationDto : propertyConfigurationDtos) {
                linesToWriteToFile.add(propertyConfigurationDto.getPSVRecord());
            }

            // Write lines to file
            FileUtils.writeLines(outputFile, linesToWriteToFile);
        } catch (IOException ioe) {
            LOGGER.error("Unable to create file: " + outputFile.getAbsolutePath(), ioe);
        }
    }
}
