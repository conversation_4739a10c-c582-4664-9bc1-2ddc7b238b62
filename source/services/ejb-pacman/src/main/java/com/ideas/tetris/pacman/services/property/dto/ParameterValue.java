package com.ideas.tetris.pacman.services.property.dto;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

public class ParameterValue {
    private String name;
    private String value;
    private boolean updateIfExists;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public boolean isUpdateIfExists() {
        return updateIfExists;
    }

    public void setUpdateIfExists(boolean updateIfExists) {
        this.updateIfExists = updateIfExists;
    }

    @Override
    public boolean equals(Object obj) {
        return EqualsBuilder.reflectionEquals(this, obj);
    }

    @Override
    public int hashCode() {
        return HashCodeBuilder.reflectionHashCode(this);
    }


}
