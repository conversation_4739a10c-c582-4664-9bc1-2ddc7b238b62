package com.ideas.tetris.pacman.services.security.dto;

import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import org.apache.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;

import static com.ideas.tetris.pacman.services.security.dto.UserPreference.DEFAULT_DATE_FORMAT;
import static com.ideas.tetris.pacman.services.security.dto.UserPreference.DEFAULT_INVENTORY_GROUP;
import static com.ideas.tetris.pacman.services.security.dto.UserPreference.DEFAULT_LANDING_PAGE;
import static com.ideas.tetris.pacman.services.security.dto.UserPreference.DEFAULT_PROPERTY;
import static com.ideas.tetris.pacman.services.security.dto.UserPreference.DEFAULT_PROPERTY_GROUP;
import static com.ideas.tetris.pacman.services.security.dto.UserPreference.DEFAULT_PROPERTY_OR_GROUP;
import static com.ideas.tetris.pacman.services.security.dto.UserPreference.FISCAL_CALENDAR_ENABLED;
import static com.ideas.tetris.pacman.services.security.dto.UserPreference.LANGUAGE;
import static com.ideas.tetris.pacman.services.security.dto.UserPreference.VIEWING_PREFERENCE;

public class UserPreferencesUtil {
    public static final String DEFAULT_LANGUAGE = "en_US";

    private static final Logger LOGGER = Logger.getLogger(UserPreferencesUtil.class);

    private UserPreferencesUtil() {
    }

    public static void setUserPreferencesFromDB(GlobalUser globalUser, LDAPUser user) {
        JSONObject userPreferences = getJSONUserPreferences(globalUser.getUserPreferences());

        user.setViewingPreference(getPreferenceById(userPreferences, VIEWING_PREFERENCE.getPreferenceId()));
        user.setDefaultProperty(getPreferenceById(userPreferences, DEFAULT_PROPERTY.getPreferenceId()));
        user.setDefaultPropertyGroup(getPreferenceById(userPreferences, DEFAULT_PROPERTY_GROUP.getPreferenceId()));
        user.setDefaultPropertyOrGroup(getPreferenceById(userPreferences, DEFAULT_PROPERTY_OR_GROUP.getPreferenceId()));
        user.setDefaultLandingPage(getPreferenceById(userPreferences, DEFAULT_LANDING_PAGE.getPreferenceId()));
        user.setDefaultDateFormat(getPreferenceById(userPreferences, DEFAULT_DATE_FORMAT.getPreferenceId()));
        String language = getPreferenceById(userPreferences, LANGUAGE.getPreferenceId());
        user.setLanguage(language != null ? language : DEFAULT_LANGUAGE);
        user.setEnableFiscalCalendar(getFiscalCalendarUserPreference(globalUser.getUserPreferences()));
        user.setDefaultInventoryGroup(getPreferenceById(userPreferences, DEFAULT_INVENTORY_GROUP.getPreferenceId()));
    }

    private static String getPreferenceById(JSONObject userPreferences, String preferenceId) {
        if (userPreferences.containsKey(preferenceId)) {
            return (String) userPreferences.get(preferenceId);
        }
        return null;
    }

    private static JSONObject getJSONUserPreferences(String userPreferences) {
        String userPreferencesText = (null != userPreferences) ? userPreferences : getEmptyJsonString();
        JSONParser jsonParser = new JSONParser();
        try {
            return (JSONObject) jsonParser.parse(userPreferencesText);
        } catch (ParseException e) {
            LOGGER.error("Error parsing user preferences. Using default preferences.", e);
            return new JSONObject();
        }
    }

    public static boolean getFiscalCalendarUserPreference(String userPreferences) {
        JSONObject userPreferencesForUser = getJSONUserPreferences(userPreferences);
        if (userPreferencesForUser.containsKey(FISCAL_CALENDAR_ENABLED.getPreferenceId())) {
            return (Boolean) userPreferencesForUser.get(FISCAL_CALENDAR_ENABLED.getPreferenceId());
        }
        return false;
    }

    private static String getEmptyJsonString() {
        JSONObject jsonObject = new JSONObject();
        return jsonObject.toJSONString();
    }
}
