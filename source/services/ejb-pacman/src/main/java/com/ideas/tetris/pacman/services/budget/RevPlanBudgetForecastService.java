package com.ideas.tetris.pacman.services.budget;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.budget.dto.*;
import com.ideas.tetris.pacman.services.budget.entity.BudgetConfig;
import com.ideas.tetris.pacman.services.budget.model.BudgetUserForecastEntityModel;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.limiteddatabuild.LDBService;
import com.ideas.tetris.pacman.services.limiteddatabuild.dto.Projection;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.service.MarketSegmentService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Component
@Transactional
public class RevPlanBudgetForecastService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
	protected JobServiceLocal jobService;

    @Autowired
	private BudgetDataService budgetDataService;


    @Autowired
    @Qualifier("ldbService")
    private LDBService LDBService;


    @Autowired
	private MarketSegmentService marketSegmentService;

    private static final int MAX_ALLOWED_DAYS = 730;
    protected static final String START_DATE_AFTER_END_DATE_ERR = "Start Date cannot be after End Date";
    protected static final String SUBMISSION_TYPE_ERR_PREFIX = "Submission Type: ";
    protected static final String CONFIG_TYPE_ERR_PREFIX = "Config Type: ";
    protected static final String INVALID_INPUT_PARAMETER_MSG = "Invalid input parameters:\n - ";
    protected static final String DATE_RANGE_EXCEED_ERR = "The date range exceeds the allowed " + MAX_ALLOWED_DAYS + " days";

    protected static final String REVPLAN_CURRENCY_CODE_ERR_PREFIX = "RevPlan Currency Code: ";

    public enum SubmissionType {BUDGET, FORECAST}

    public enum JobStatus {SUCCESSFUL, FAILED}

    public ResponseEntity<String> validateSubmitRequest(RevPlanSubmitTypeRequestDTO submitTypeRequestDTO,
                                                        BindingResult result) {

        List<String> validationErrors = new ArrayList<>();
        if (result != null && result.hasErrors()) {
            // getting all the javax validation errors with @valid annotation
            result.getAllErrors().forEach(err -> validationErrors.add(
                    (err instanceof FieldError ? ((FieldError) err).getField() : "") + err.getDefaultMessage()));
        } else {
            log.info("Validating request json");

            LocalDate startDate = submitTypeRequestDTO.getStartDate();
            LocalDate endDate = submitTypeRequestDTO.getEndDate();
            Integer numOfDaysBetweenDates = JavaLocalDateUtils.daysBetween(startDate, endDate);

            // validating start date and end date after validations
            if (numOfDaysBetweenDates < 0) {
                validationErrors.add(START_DATE_AFTER_END_DATE_ERR);
            } else if (numOfDaysBetweenDates + 1 > MAX_ALLOWED_DAYS) {
                validationErrors.add(DATE_RANGE_EXCEED_ERR);
            }

            if (!EnumUtils.isValidEnum(SubmissionType.class, submitTypeRequestDTO.getSubmissionType())) {
                validationErrors.add(SUBMISSION_TYPE_ERR_PREFIX + submitTypeRequestDTO.getSubmissionType());
            }

            if (!EnumUtils.isValidEnum(BudgetConfigType.class, submitTypeRequestDTO.getConfigType())) {
                validationErrors.add(CONFIG_TYPE_ERR_PREFIX + submitTypeRequestDTO.getConfigType());
            }

            final String revPlanCurrencyCode = StringUtils.trim(submitTypeRequestDTO.getRevPlanCurrencyCode());
            if (StringUtils.isNotEmpty(revPlanCurrencyCode) && (!isCurrencyCodeValid(revPlanCurrencyCode))) {
                validationErrors.add(REVPLAN_CURRENCY_CODE_ERR_PREFIX + revPlanCurrencyCode);
            }

            submitTypeRequestDTO.setRevPlanCurrencyCode(revPlanCurrencyCode);

            // Set a default value if useRevplanExchangeRates is null / or not passed in body
            Boolean useRevplanExchangeRates = submitTypeRequestDTO.getUseRevplanExchangeRates() != null ? submitTypeRequestDTO.getUseRevplanExchangeRates() : false;
            submitTypeRequestDTO.setUseRevplanExchangeRates(useRevplanExchangeRates);
        }
        if (validationErrors.isEmpty()) {
            return null;
        }
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
                INVALID_INPUT_PARAMETER_MSG + StringUtils.join(validationErrors, "\n - "));
    }

    public ResponseEntity<String> startBudgetForecastJob(String clientCode, String propertyCode,
                                                         RevPlanSubmitTypeRequestDTO submitTypeRequestDTO) {
        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.put(JobParameterKey.CLIENT_CODE, clientCode);
        parameterMap.put(JobParameterKey.PROPERTY_CODE, propertyCode);
        parameterMap.put(JobParameterKey.INCOMING_SERIALIZABLE, submitTypeRequestDTO);
        jobService.startGuaranteedNewInstance(JobName.RevPlanBudgetForecastUpload, parameterMap);

        // return successful message
        return ResponseEntity.ok("Request accepted and job triggered");
    }

    public List<BudgetConfigTypeDTO> getBudgetForecastConfig(String clientCode, String propertyCode) {

        List<BudgetConfigTypeDTO> configurations = tenantCrudService.findByNamedQuery(BudgetConfig.GET_BUDGET_FORECAST_CONFIG_BY_LEVEL);

        if (pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value(), clientCode, propertyCode)) {
            configurations.add(BudgetConfigType.getLDBConfig());
        }

        return configurations;
    }

    public List<BudgetUserForecastEntityModel> queryRevPlanForData(RevPlanSubmitTypeRequestDTO requestDTO) {
        RevPlanResponseWrapper response = getRevPlanClient().getForObject(buildRevPlanUrl(requestDTO), RevPlanResponseWrapper.class);
        return (response == null || response.getResult() == null) ? List.of() : response.getResult().stream()
                .map(this::buildDto
                ).collect(Collectors.toList());
    }

    @VisibleForTesting
	public
    BudgetUserForecastEntityModel buildDto(RevPlanResponseWrapper.RevPlanResponse revPlanResponse) {
        BudgetUserForecastEntityModel dto = new BudgetUserForecastEntityModel();
        dto.setOccupancyDate(LocalDateUtils.toJavaLocalDateFromString(revPlanResponse.getOccupancyDate(), DateUtil.DEFAULT_DATE_FORMAT));
        dto.setSegmentCode(revPlanResponse.getSegmentCode());
        dto.setSegmentName(revPlanResponse.getSegmentName());
        dto.setRooms(revPlanResponse.getRn().intValue());
        dto.setRevenue(BigDecimal.valueOf(revPlanResponse.getRevenue()));
        return dto;
    }

    private String buildRevPlanUrl(RevPlanSubmitTypeRequestDTO requestDTO) {
        Map<String, Object> requestParameters = MapBuilder
                .with(BudgetService.START_DATE, requestDTO.getStartDate())
                .and(BudgetService.END_DATE, requestDTO.getEndDate())
                .and(Constants.CLIENT_CODE, PacmanWorkContextHelper.getClientCode())
                .and(Constants.PROPERTY_CODE, PacmanWorkContextHelper.getPropertyCode())
                .and("revplanDataPullId", requestDTO.getRevplanDataPullId())
                .and("submissionType", requestDTO.getSubmissionType()).get();
        return requestDTO.getRevPlanCallBackUrl()
                + new UriTemplate(RestEndpoints.REVPLAN_BUDGET_FORECAST.getEndpoint()).expand(requestParameters);
    }

    private String buildRevPlanJobStatusNotifyUrl(RevPlanJobNotificationDTO requestDTO) {
        Map<String, Object> requestParameters = MapBuilder
                .with(Constants.CLIENT_CODE, PacmanWorkContextHelper.getClientCode())
                .and(Constants.PROPERTY_CODE, PacmanWorkContextHelper.getPropertyCode()).get();
        return requestDTO.getRevPlanCallBackUrl()
                + new UriTemplate(RestEndpoints.REVPLAN_BUDGET_FORECAST_NOTIFICATION.getEndpoint()).expand(requestParameters);
    }

    public RestTemplate getRevPlanClient() {
        return new RestTemplateBuilder()
                .setConnectTimeout(SystemConfig.getConnectTimeout("revplan"))
                .setReadTimeout(SystemConfig.getReadTimeout("revplan"))
                .build();
    }

    public void loadDataInG3(List<BudgetUserForecastEntityModel> dataFromRevPlan, String configType) {
        switch (BudgetConfigType.valueOf(configType)) {
            case BUDGET:
                budgetDataService.loadBudgetDataFromRevplanIntoG3(dataFromRevPlan);
                break;
            case USER_FORECAST:
                budgetDataService.loadUserForecastDataFromRevplanIntoG3(dataFromRevPlan);
                break;
            case LDB:
                LDBService.loadProjectionsIntoPacman(dataFromRevPlan.stream().map(this::buildLdbData).collect(Collectors.toList()));
                break;

        }
    }


    private Projection buildLdbData(BudgetUserForecastEntityModel budgetDataDto) {
        Projection projection = new Projection();
        projection.setOccupancyDate(LocalDateUtils.toJodaLocalDate(budgetDataDto.getOccupancyDate()));
        projection.setMarketSegment(budgetDataDto.getSegmentCode());
        projection.setMarketSegmentName(budgetDataDto.getSegmentName());
        projection.setRoomsSold(budgetDataDto.getRooms());
        projection.setRoomRevenue(budgetDataDto.getRevenue());
        return projection;
    }

    public void notifyJobStatusToRevplan(RevPlanSubmitTypeRequestDTO revPlanSubmitTypeRequestDTO, List<String> errorMessages, Set<String> warningMessages) {

        String jobStatus = CollectionUtils.isEmpty(errorMessages) ? JobStatus.SUCCESSFUL.name() : JobStatus.FAILED.name();

        RevPlanJobNotificationDTO notificationDTO = new RevPlanJobNotificationDTO(revPlanSubmitTypeRequestDTO, jobStatus, errorMessages, warningMessages);

        ResponseEntity<String> responseEntity = getRevPlanClient().postForEntity(buildRevPlanJobStatusNotifyUrl(notificationDTO), notificationDTO, String.class);

        if (CollectionUtils.isNotEmpty(errorMessages)) {
            throw new TetrisException("Job failed : validation errors: please abandon the job and contact to RevPlan for new job request");
        }

        if (!responseEntity.getStatusCode().is2xxSuccessful()) {
            throw new TetrisException("Job failed : error occurred while notifying the job status to RevPlan");
        }
    }

    private boolean isCurrencyCodeValid(String currencyCode) {
        return Currency.getAvailableCurrencies().stream()
                .map(Currency::getCurrencyCode)
                .anyMatch(Predicate.isEqual(currencyCode));
    }

    public List<String> getAllActiveMktSegmentCodes() {
        final List<MktSeg> allActiveMktSegments = marketSegmentService.getAllActiveMktSegments();
        return allActiveMktSegments.stream()
                .map(mktSeg -> mktSeg.getCode().toLowerCase())
                .collect(Collectors.toList());
    }

    private String buildRevPlanCurrencyExchangeRatesUrl(RevPlanSubmitTypeRequestDTO revPlanSubmitTypeRequestDTO, String rmsCurrency) {
        Map<String, Object> requestParameters = MapBuilder
                .with(Constants.CLIENT_CODE, PacmanWorkContextHelper.getClientCode())
                .and(Constants.PROPERTY_CODE, PacmanWorkContextHelper.getPropertyCode())
                .and(BudgetService.START_DATE, revPlanSubmitTypeRequestDTO.getStartDate())
                .and(BudgetService.END_DATE, revPlanSubmitTypeRequestDTO.getEndDate())
                .and("fromCurrency", revPlanSubmitTypeRequestDTO.getRevPlanCurrencyCode())
                .and("toCurrency", rmsCurrency)
                .and("submissionType", revPlanSubmitTypeRequestDTO.getSubmissionType()).get();
        return revPlanSubmitTypeRequestDTO.getRevPlanCallBackUrl()
                + new UriTemplate(RestEndpoints.REVPLAN_CURRENCY_EXCHANGE_RATE_FOR_DATE_RANGE.getEndpoint()).expand(requestParameters);
    }


    public List<CurrencyExchangeDTO> getRevPlanCurrencyExchangeRates(RevPlanSubmitTypeRequestDTO revPlanSubmitTypeRequestDTO, String rmsCurrency){
        String url = buildRevPlanCurrencyExchangeRatesUrl(revPlanSubmitTypeRequestDTO, rmsCurrency);
        final RevPlanCurrencyExchangeResponseWrapper response = getRevPlanClient().getForObject(url, RevPlanCurrencyExchangeResponseWrapper.class);
        return (response == null || response.getResult() == null) ? List.of() : new ArrayList<>(response.getResult());
    }

}
