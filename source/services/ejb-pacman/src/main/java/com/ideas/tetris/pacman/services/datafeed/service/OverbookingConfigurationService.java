package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.entity.OverbookingConfiguration;
import com.ideas.tetris.pacman.services.datafeed.rowmapper.OverbookingConfigurationSpecialRTRowMapper;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingProperty;
import com.ideas.tetris.pacman.services.rms.overbooking.configurations.dto.AuditInfo;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.PROPERTY_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.START_DATE;
import static java.util.Objects.nonNull;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Component
@Transactional
public class OverbookingConfigurationService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @GlobalCrudServiceBean.Qualifier
    @Autowired
    protected CrudService globalCrudService;


    public List getOverbookingConfigurationDetails(DatafeedRequest datafeedRequest) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        parameters.put(START_DATE, datafeedRequest.getStartDate());
        return tenantCrudService.<Object[]>findByNamedQuery(
                OverbookingConfiguration.FIND_BY_START_DATE_FOR_SPECIAL_RT, parameters, datafeedRequest.getStartPosition(), datafeedRequest.getSize()
        ).stream().map(row -> new OverbookingConfigurationSpecialRTRowMapper().mapRow(row)).collect(Collectors.toList());
    }

    public OverbookingProperty findOverbookingProperty(Integer propertyId) {
        return (OverbookingProperty) tenantCrudService.findByNamedQuerySingleResult(OverbookingProperty.BY_PROPERTYID, QueryParameter.with("propertyId", propertyId).parameters());
    }

    public AuditInfo getAuditInfoOfOverbookingConfiguration(){
        List<Object[]> rows = tenantCrudService.findByNativeQuery("select * from \n" +
                "(\n" +
                "\tselect top 1 * from\n" +
                "\t(\n" +
                "\t\tselect top 1 Created_By_User_ID, Created_DTTM from Overbooking_Property_AUD order by Created_DTTM asc \n" +
                "\t\tunion\n" +
                "\t\tselect top 1 Created_By_User_ID, Created_DTTM from Overbooking_Accom_AUD order by Created_DTTM asc \n" +
                "\t\tunion\n" +
                "\t\tselect top 1 Created_By_User_ID, Created_DTTM from Overbooking_Accom_Season_AUD order by Created_DTTM asc\n" +
                "\t) as ovrBkAcc order by Created_DTTM asc\n" +
                ")as t1\n" +
                "cross join \n" +
                "(\n" +
                "\tselect top 1 * from\n" +
                "\t(\n" +
                "\t\tselect top 1 Last_Updated_By_User_ID, Last_Updated_DTTM from Overbooking_Property_AUD order by Last_Updated_DTTM desc \n" +
                "\t\tunion\n" +
                "\t\tselect top 1 Last_Updated_By_User_ID, Last_Updated_DTTM from Overbooking_Accom_AUD order by Last_Updated_DTTM desc \n" +
                "\t\tunion\n" +
                "\t\tselect top 1 Last_Updated_By_User_ID, Last_Updated_DTTM from Overbooking_Accom_Season_AUD order by Last_Updated_DTTM desc\n" +
                "\t) as ovrBkAcc order by Last_Updated_DTTM desc\n" +
                ") as t2");
        AuditInfo auditInfo = null;
        if(isNotEmpty(rows)){
            BigInteger createdByUserId = (BigInteger)rows.get(0)[0];
            GlobalUser createdByUser = globalCrudService.find(GlobalUser.class, createdByUserId.intValue());
            BigInteger lastUpdatedByByUserId = (BigInteger) rows.get(0)[2];
            GlobalUser lastUpdatedByUser = globalCrudService.find(GlobalUser.class, lastUpdatedByByUserId.intValue());
            Timestamp createdDate = (Timestamp)rows.get(0)[1];
            Timestamp lastUpdatedDate = (Timestamp)rows.get(0)[3];

            auditInfo = new AuditInfo(nonNull(createdByUser) ? createdByUser.getCognitoUserId() : null,
                    nonNull(createdDate) ? createdDate.toString() : null,
                    nonNull(lastUpdatedByUser) ? lastUpdatedByUser.getCognitoUserId() : null,
                    nonNull(lastUpdatedDate) ? lastUpdatedDate.toString() : null);
        }
        return auditInfo;
    }

}
