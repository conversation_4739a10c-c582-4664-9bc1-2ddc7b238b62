package com.ideas.tetris.pacman.services.webrate.dto;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class WebrateAccomTypeAverageRateDTO {
    private Integer webrateAccomTypeId;
    private BigDecimal averageRate;

    public WebrateAccomTypeAverageRateDTO(Object row) {
        Object[] columns = (Object[]) row;
        this.webrateAccomTypeId = (Integer) columns[0];
        this.averageRate = columns[1] instanceof BigDecimal ? (BigDecimal) columns[1] : BigDecimal.ZERO;
        this.averageRate = this.averageRate.setScale(2, RoundingMode.HALF_UP);
    }

    public Integer getWebrateAccomTypeId() {
        return webrateAccomTypeId;
    }

    public void setWebrateAccomTypeId(Integer webrateAccomTypeId) {
        this.webrateAccomTypeId = webrateAccomTypeId;
    }

    public BigDecimal getAverageRate() {
        return averageRate;
    }

    public void setAverageRate(BigDecimal averageRate) {
        this.averageRate = averageRate;
    }
}
