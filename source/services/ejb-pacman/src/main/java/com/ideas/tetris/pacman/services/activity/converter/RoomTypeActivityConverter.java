package com.ideas.tetris.pacman.services.activity.converter;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.activity.converter.RoomTypeActivityConverter.Qualifier;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ActivityEntity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceAccomActivity;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.roomtyperecoding.services.RoomTypeRecodingService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.logging.Logger;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Qualifier
@Component
@Transactional
public class RoomTypeActivityConverter extends PaceActivityConverter<AccomActivity, PaceAccomActivity> {

    private static final Logger LOGGER = Logger.getLogger(RoomTypeActivityConverter.class);
    private static final String PROPERTY_ID = "propertyId";

    @Autowired
	private PacmanConfigParamsService configParamsService;
    @Autowired
	private RoomTypeRecodingService roomTypeRecodingService;

    @Override
    public Map<String, Object> convertFromEntity(ActivityEntity accomActivity) {
        Map<String, Object> dto = super.convertFromEntity(accomActivity);
        if (dto == null) {
            return null;
        }

        Integer accomTypeId = null;
        if (accomActivity instanceof AccomActivity) {
            accomTypeId = ((AccomActivity) accomActivity).getAccomTypeId();
        } else if (accomActivity instanceof PaceAccomActivity) {
            accomTypeId = ((PaceAccomActivity) accomActivity).getAccomTypeId();
        }

        if (accomTypeId != null) {
            String roomTypeCodeForRoomTypeId = findRoomTypeCodeForRoomTypeId(accomTypeId);
            dto.put(ROOM_TYPE_CODE, roomTypeCodeForRoomTypeId);
        }

        // Shouldn't return ID since we are returning code
        dto.remove(ACCOM_TYPE_ID);
        return dto;
    }

    @Override
    public AccomActivity convertFromMap(Map<String, Object> dto, String correlationId, boolean isPast) {
        AccomActivity accomActivity = super.convertFromMap(dto, correlationId, isPast);
        if (accomActivity == null) {
            return null;
        }

        AccomType roomType = findOrCreateRoomTypeForCode(accomActivity.getPropertyId(), getString(dto, ROOM_TYPE_CODE));
        accomActivity.setAccomTypeId(roomType.getId());

        return accomActivity;
    }

    /**
     * Looks for an existing AccomActivity record based on the ID if it's present, if it isn't, then
     * it will attempt to use the Occupancy Date / Property ID combination, and if that doesn't find an
     * existing record, it will simply return a new AccomActivity object.
     */
    @Override
    public AccomActivity findExistingOrCreateNewActivity(Integer propertyId, Map<String, Object> dto, String correlationId, boolean isPast) {
        // Look up the AccomActivity record
        AccomActivity accomActivity;

        Integer id = getInteger(dto, ID);
        if (id != null) {
            accomActivity = tenantCrudService.find(AccomActivity.class, id);
        } else {
            accomActivity = tenantCrudService.findByNamedQuerySingleResult(AccomActivity.BY_OCCUPANCY_DATE_AND_ACCOMTYPE_CODE_AND_PROPERTY_ID, QueryParameter.with("occupancyDate", getDate(dto, OCCUPANCY_DATE)).and(PROPERTY_ID, propertyId).and("accomTypeCode", getString(dto, ROOM_TYPE_CODE)).parameters());
        }

        // If it doesn't exist, return create a new one
        if (accomActivity == null) {
            accomActivity = new AccomActivity();

        }

        FileMetadata metadata = findExistingFileMetadata(correlationId);
        accomActivity.setSnapShotDate(metadata.getSnapshotDtTm());
        accomActivity.setFileMetadataId(metadata.getId());

        return accomActivity;
    }

    private Set<Date> identifyUniqueOccupancyDates(List<Map<String, Object>> dtos, Integer propertyId) {

        LOGGER.info("Starting additions to unique sets");

        Set<Date> dtoOccupancyDateSet = new HashSet<>();
        Set<String> dtoOccupancyDateSetString = new HashSet<>();

        for (Map<String, Object> dto : dtos) {
            if (dto != null) {
                dtoOccupancyDateSetString.add((String) dto.get(OCCUPANCY_DATE));
            }
        }

        for (String dateString : dtoOccupancyDateSetString) {
            try {
                dtoOccupancyDateSet.add(DateUtil.parseDate(dateString, OCCUPANCY_DATE_FORMAT_YYYY_MM_DD));
            } catch (ParseException pe) {
                throw new TetrisException(String.format("Failed to parse: %s for propertyId: %s", dateString, propertyId));
            }
        }

        LOGGER.info("Completed additions to unique sets");
        return dtoOccupancyDateSet;
    }


    private String getKeyForPaceAccomActivity(Integer propertyId, Integer accomTypeId, Date occupancyDate, Date businessDayEndDate) {
        return String.valueOf(propertyId) + accomTypeId + DateUtil.formatDate(occupancyDate, OCCUPANCY_DATE_FORMAT_YYYY_MM_DD) + DateUtil.formatDate(businessDayEndDate, OCCUPANCY_DATE_FORMAT_YYYY_MM_DD);
    }

    @Override
    public List<AccomActivity> findExistingOrCreateNewActivity(Integer propertyId, List<Map<String, Object>> dtos, String correlationId) {

        ArrayList<AccomActivity> accomActivities = new ArrayList<>();
        Set<Date> dtoOccupancyDateSet = identifyUniqueOccupancyDates(dtos, propertyId);
        Map<String, TreeMap<Date, String>> mappingsForRenamedAccomTypes = initRoomTypeRecodingTransformer();
        List<AccomActivity> availableAccomActivities = tenantCrudService.findByNamedQuery(AccomActivity.BY_OCCUPANCY_DATE_LIST_AND_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, propertyId).and("occupancyDates", dtoOccupancyDateSet).parameters());
        tenantCrudService.flushAndClear();

        Map<String, AccomActivity> accomActivityMap = new HashMap<>();
        for (AccomActivity accomActivity : availableAccomActivities) {
            accomActivityMap.put(getKeyForAccomActivity(propertyId, accomActivity.getAccomTypeId(), accomActivity.getOccupancyDate()), accomActivity);
        }

        FileMetadata metadata = findExistingFileMetadata(correlationId);
        List<AccomType> roomTypesThatUpdated = new ArrayList<>();
        for (Map<String, Object> dto : dtos) {

            Date occupancyDate = getOccupancyDate(dto, propertyId);
            String roomTypeCode = renameRoomTypeCode(mappingsForRenamedAccomTypes, getString(dto, ROOM_TYPE_CODE), occupancyDate);
            AccomType roomType = findOrCreateRoomTypeForCode(propertyId, roomTypeCode);

            AccomActivity entity = buildEntity(propertyId, accomActivities, accomActivityMap, metadata, dto, roomType, occupancyDate);

            BigDecimal activityCapacity = entity.getAccomCapacity();
            if (activityCapacity == null) {
                activityCapacity = BigDecimal.ZERO;
            }

            Integer roomTypeCapacity = roomType.getAccomTypeCapacity();
            if (roomTypeCapacity == null) {
                roomTypeCapacity = 0;
            }

            if ((activityCapacity.intValue() > roomTypeCapacity &&
                    entity.getOccupancyDate().after(entity.getBusinessDayEndFromSnapShotDate())) ||
                    (Objects.equals(activityCapacity, BigDecimal.ZERO) && roomTypeCapacity == 0)) {
                roomType.setAccomTypeCapacity(activityCapacity.intValue());
                roomTypesThatUpdated.add(roomType);
                roomTypeCache.remove(roomType.getPropertyId(), roomType.getAccomTypeCode());
            }
        }
        if (CollectionUtils.isNotEmpty(roomTypesThatUpdated)) {
            tenantCrudService.save(roomTypesThatUpdated);
        }

        return accomActivities;
    }

    private AccomActivity buildEntity(Integer propertyId, List<AccomActivity> accomActivities, Map<String, AccomActivity> accomActivityMap, FileMetadata metadata, Map<String, Object> dto, AccomType roomType, Date occupancyDate) {
        AccomActivity entity = accomActivityMap.get(getKeyForAccomActivity(propertyId, roomType.getId(), occupancyDate));

        if (entity == null) {
            entity = new AccomActivity();
        }

        Integer id = entity.getId();
        // Reset the ID if there was one and if the copy properties removed it
        if (entity.getId() == null && id != null) {
            entity.setId(id);
        }
        entity.setOccupancyDate(occupancyDate);
        entity.setArrivals(getBigDecimal(dto, ARRIVALS));
        entity.setCancellations(getBigDecimalDefault(dto, CANCELLATIONS, BigDecimal.ZERO));
        entity.setDepartures(getBigDecimal(dto, DEPARTURES));
        entity.setFoodRevenue(getBigDecimal(dto, FOOD_REVENUE));
        entity.setNoShows(getBigDecimalDefault(dto, NO_SHOWS, BigDecimal.ZERO));
        entity.setRoomRevenue(getBigDecimal(dto, ROOM_REVENUE));
        entity.setRoomsSold(getBigDecimal(dto, ROOMS_SOLD));
        entity.setTotalRevenue(getBigDecimal(dto, TOTAL_REVENUE));
        entity.setAccomCapacity(getBigDecimal(dto, ACCOM_CAPACITY));
        entity.setRoomsNotAvailableMaintenance(getBigDecimal(dto, ROOMS_NOT_AVAILABLE_MAINTENANCE));
        entity.setRoomsNotAvailableOther(getBigDecimal(dto, ROOMS_NOT_AVAILABLE_OTHER));
        entity.setSnapShotDate(metadata.getSnapshotDtTm());
        entity.setFileMetadataId(metadata.getId());
        entity.setPropertyId(propertyId);
        entity.setAccomTypeId(roomType.getId());
        accomActivities.add(entity);
        return entity;
    }

    private Date getOccupancyDate(Map<String, Object> dto, Integer propertyId) {
        String occupancyDateString = (String) dto.get(OCCUPANCY_DATE);
        Date occupancyDate;

        try {
            occupancyDate = DateUtil.parseDate(occupancyDateString, OCCUPANCY_DATE_FORMAT_YYYY_MM_DD);
        } catch (ParseException pe) {
            throw new TetrisException(String.format("Failed to parse: %s for propertyId: %s", occupancyDateString, propertyId));
        }
        return occupancyDate;
    }

    private String getKeyForAccomActivity(Integer propertyId, Integer accomTypeId, Date occupancyDate) {
        return String.valueOf(propertyId) + accomTypeId + DateUtil.formatDate(occupancyDate, OCCUPANCY_DATE_FORMAT_YYYY_MM_DD);
    }

    private Map<String, TreeMap<Date, String>> initRoomTypeRecodingTransformer() {
        Map<String, TreeMap<Date, String>> roomTypeRecodingMappingsForRename = Collections.emptyMap();
        if (configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_ENABLED)) {
            roomTypeRecodingMappingsForRename = roomTypeRecodingService.fetchRenameConfigMappingsUptilNow();
        }
        return roomTypeRecodingMappingsForRename;
    }

    public String renameRoomTypeCode(Map<String, TreeMap<Date, String>> roomTypeRecodingMappingsForRename, String roomTypeCode, Date occupancyDate) {
        if (roomTypeRecodingMappingsForRename.containsKey(roomTypeCode)) {
            TreeMap<Date, String> effectiveDateNewRTMapping = roomTypeRecodingMappingsForRename.get(roomTypeCode);
            // Only rename if the activity's occupancy_dt is in past w.r.t. the effective date of the rename config
            final Map.Entry<Date, String> newAccomTypeCode = effectiveDateNewRTMapping.ceilingEntry(occupancyDate);
            if (newAccomTypeCode != null) {
                return newAccomTypeCode.getValue();
            }
        }
        return roomTypeCode;
    }

    @Override
    public List<PaceAccomActivity> convertToPaceEntities(List<AccomActivity> entities) {
        if (entities == null) {
            return Collections.emptyList();
        }
        LOGGER.info("Starting convert convertToPaceEntities: " + entities.size());
        Integer propertyId = entities.get(0).getPropertyId();
        Set<Date> occupancyDateSet = new HashSet<>();
        for (AccomActivity entity : entities) {
            occupancyDateSet.add(entity.getOccupancyDate());
        }

        List<PaceAccomActivity> paceAccomActivities = tenantCrudService.findByNamedQuery(PaceAccomActivity.BY_OCCUPANCY_DATE_LIST_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, propertyId).and("occupancyDates", occupancyDateSet).parameters());

        Map<String, PaceAccomActivity> accomActivityMap = new HashMap<>();
        for (PaceAccomActivity paceAccomActivity : paceAccomActivities) {
            accomActivityMap.put(getKeyForPaceAccomActivity(propertyId, paceAccomActivity.getAccomTypeId(), paceAccomActivity.getOccupancyDate(), paceAccomActivity.getBusinessDayEndDate()), paceAccomActivity);
        }

        List<PaceAccomActivity> paceEntities = new ArrayList<>();

        for (AccomActivity entity : entities) {
            // Look for an existing PaceActivityEntity for the DTO, if it doesn't exist, create a new one

            PaceAccomActivity paceEntity = accomActivityMap.get(getKeyForPaceAccomActivity(entity.getPropertyId(), entity.getAccomTypeId(), entity.getOccupancyDate(), entity.getBusinessDayEndFromSnapShotDate()));

            if (paceEntity == null) {
                paceEntity = new PaceAccomActivity();
            }
            // Get a reference to the Pace Entity's ID, so it won't be lost in the property copy
            Integer id = paceEntity.getId();
            paceEntity.setOccupancyDate(entity.getOccupancyDate());
            paceEntity.setArrivals(entity.getArrivals());
            paceEntity.setCancellations(entity.getCancellations());
            paceEntity.setDepartures(entity.getDepartures());
            paceEntity.setFoodRevenue(entity.getFoodRevenue());
            paceEntity.setNoShows(entity.getNoShows());
            paceEntity.setRoomRevenue(entity.getRoomRevenue());
            paceEntity.setRoomsSold(entity.getRoomsSold());
            paceEntity.setTotalRevenue(entity.getTotalRevenue());
            paceEntity.setAccomCapacity(entity.getAccomCapacity());
            paceEntity.setRoomsNotAvailableMaintenance(entity.getRoomsNotAvailableMaintenance());
            paceEntity.setRoomsNotAvailableOther(entity.getRoomsNotAvailableOther());
            paceEntity.setAccomTypeId(entity.getAccomTypeId());
            paceEntity.setPropertyId(entity.getPropertyId());
            paceEntity.setSnapShotDate(entity.getSnapShotDate());
            paceEntity.setLastUpdatedAuditInfo();

            paceEntity.setId(id);

            paceEntity.setBusinessDayEndDate(entity.getBusinessDayEndFromSnapShotDate());
            paceEntity.setFileMetadataId(entity.getFileMetadataId());
            paceEntities.add(paceEntity);
        }
        LOGGER.info("Completed convert convertToPaceEntities: " + entities.size());
        return paceEntities;
    }

    @Override
    public PaceAccomActivity findExistingOrCreateNewPaceActivity(AccomActivity accomActivity) {
        PaceAccomActivity paceAccomActivity = tenantCrudService.findByNamedQuerySingleResult(PaceAccomActivity.BY_OCCUPANCY_DATE_AND_PROPERTY_ID_AND_BDEDATE_AND_ACCOM_TYPE_ID, QueryParameter.with(PROPERTY_ID, accomActivity.getPropertyId()).and("occupancyDate", accomActivity.getOccupancyDate()).and("businessDayEndDate", accomActivity.getBusinessDayEndFromSnapShotDate()).and("accomTypeId", accomActivity.getAccomTypeId()).parameters());
        if (paceAccomActivity == null) {
            paceAccomActivity = new PaceAccomActivity();
        }
        return paceAccomActivity;
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
