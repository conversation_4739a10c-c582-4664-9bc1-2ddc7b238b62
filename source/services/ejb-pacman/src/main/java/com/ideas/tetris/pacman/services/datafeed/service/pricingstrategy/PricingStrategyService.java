package com.ideas.tetris.pacman.services.datafeed.service.pricingstrategy;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.pricestrategy.PriceStrategyRatePlanConfiguration;
import com.ideas.tetris.pacman.services.datafeed.dto.pricestrategy.RateCodeConfiguration;
import com.ideas.tetris.pacman.services.datafeed.entity.UserOverrideConfiguration;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedAccomClass;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedDetails;
import com.ideas.tetris.pacman.services.unqualifiedrate.serivce.RateUnqualifiedService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;

import javax.inject.Inject;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PricingStrategyService {

    public static final String MAX = "Max";
    public static final String MIN = "Min";
    @Autowired
	private RateUnqualifiedService rateService;
    @Autowired
	private PacmanConfigParamsService paramsService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    private boolean isConfiguredFor(String parameterName) {
        return paramsService.getBooleanParameterValue(parameterName);
    }

    public List<RateCodeConfiguration> getRateCodeConfigs() {
        return rateService.getRateUnqualifiedByProperty()
                .stream()
                .map(r -> new RateCodeConfiguration(
                        r.getName(), r.getDescription(), r.getStartDate(),
                        r.getEndDate(), isConfiguredFor(IPConfigParamName.CORE_IS_RANKING_ENABLED.value()) ? r.getRanking() : null)).collect(Collectors.toList());
    }

    public List<UserOverrideConfiguration> getUserOverrideConfigurations(Date date) {
        List<UserOverrideConfiguration> userconfigs = tenantCrudService.findByNamedQuery(UserOverrideConfiguration.FIND_BY_DATE,
                QueryParameter.with("startDate", DateUtil.formatDate(date, DateUtil.DEFAULT_DATE_FORMAT)).parameters());

        if (isConfiguredFor(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value())) {
            Map<String, String> masterList = userconfigs.stream().filter(u -> Constants.DEFAULT.equalsIgnoreCase(u.getCategory()) && u.getMasterClass() == 1)
                    .collect(Collectors.toMap(UserOverrideConfiguration::getRatePlanName, UserOverrideConfiguration::getUserOverrideOnly));
            userconfigs.stream().filter(u -> Constants.DEFAULT.equalsIgnoreCase(u.getCategory())).forEach(userconfig ->
                    userconfig.setUserOverrideOnly(masterList.get(userconfig.getRatePlanName()))
            );
        }
        return userconfigs;
    }

    public List<PriceStrategyRatePlanConfiguration> getAllCurrentAndFutureRatePlanConfigurationsAfter(final Date date) {
        Set<PriceStrategyRatePlanConfiguration> ratePlanConfigurations = new TreeSet<>();
        Map<Integer, String> accomTypes = new HashMap<>();
        rateService.getRateUnqualifiedByProperty()
                .stream()
                .filter(e -> e.getEndDate().after(date))
                .forEach(e ->
                        rateService.getAccomClassDetailsByRateUnqualified(e.getId())
                                .stream()
                                .forEach(getRateUnqualifiedAccomClassConsumer(date, ratePlanConfigurations, accomTypes, e))

                );
        return new ArrayList<>(ratePlanConfigurations);
    }

    private Consumer<RateUnqualifiedAccomClass> getRateUnqualifiedAccomClassConsumer(Date date, Set<PriceStrategyRatePlanConfiguration> ratePlanConfigurations, Map<Integer, String> accomTypes, RateUnqualified rateUnqualified) {
        return g -> {
            List<Integer> accomTypeIds = getAccomTypeIds(g.getAccomClassId());
            if (!accomTypeIds.isEmpty()) {
                getRateUnqualifiedDetails(accomTypeIds, rateUnqualified.getId()).stream()
                        .filter(h -> h.getEndDate().after(date))
                        .forEach(getRateUnqualifiedDetailsConsumer(ratePlanConfigurations, accomTypes, rateUnqualified.getName()));
            }
        };
    }

    private Consumer<RateUnqualifiedDetails> getRateUnqualifiedDetailsConsumer(Set<PriceStrategyRatePlanConfiguration> ratePlanConfigurations, Map<Integer, String> accomTypes, String ratePlanName) {
        return h -> {
            String accomTypeCode = getAccomTypeCode(accomTypes, h.getAccomTypeId());
            PriceStrategyRatePlanConfiguration ratePlanConfiguration = convertToPriceStrategyRatePlanConfigurationDto(h, ratePlanName, accomTypeCode);
            ratePlanConfigurations.add(ratePlanConfiguration);
        };
    }

    private List<RateUnqualifiedDetails> getRateUnqualifiedDetails(List<Integer> accomTypeIds, Integer rateUnqualifiedId) {
        return tenantCrudService.findByNamedQuery(RateUnqualifiedDetails.BY_RATE_UQ_ID_AND_ACCOM_TYPE,
                QueryParameter.with("rateUnqualifiedId", rateUnqualifiedId).and("accomTypeIdList", accomTypeIds).parameters());
    }

    private List getAccomTypeIds(Integer accomClassId) {
        return tenantCrudService.findByNamedQuery(AccomType.ID_BY_ACCOM_CLASS_ID, QueryParameter.with("accomClassId", accomClassId).parameters());
    }

    private String getAccomTypeCode(Map<Integer, String> accomTypes, Integer accomTypeId) {
        String accomTypeCode = accomTypes.get(accomTypeId);
        if (null == accomTypeCode) {
            accomTypeCode = tenantCrudService.findByNamedQuerySingleResult(AccomType.ACCOM_TYPE_CODE_BY_ACCOM_TYPE_ID, QueryParameter.with("id", accomTypeId).parameters());
            accomTypes.put(accomTypeId, accomTypeCode);
        }
        return accomTypeCode;
    }

    private PriceStrategyRatePlanConfiguration convertToPriceStrategyRatePlanConfigurationDto(final RateUnqualifiedDetails rateUnqualifiedDetails, final String ratePlanName, final String roomTypeCode) {
        PriceStrategyRatePlanConfiguration ratePlanConfiguration = new PriceStrategyRatePlanConfiguration();
        ratePlanConfiguration.setRatePlanName(ratePlanName);
        ratePlanConfiguration.setRoomTypeCode(roomTypeCode);
        ratePlanConfiguration.setStartDate(rateUnqualifiedDetails.getStartDate());
        ratePlanConfiguration.setEndDate(rateUnqualifiedDetails.getEndDate());
        ratePlanConfiguration.setSunday(rateUnqualifiedDetails.getSunday().setScale(2, RoundingMode.HALF_DOWN));
        ratePlanConfiguration.setMonday(rateUnqualifiedDetails.getMonday().setScale(2, RoundingMode.HALF_DOWN));
        ratePlanConfiguration.setTuesday(rateUnqualifiedDetails.getTuesday().setScale(2, RoundingMode.HALF_DOWN));
        ratePlanConfiguration.setWednesday(rateUnqualifiedDetails.getWednesday().setScale(2, RoundingMode.HALF_DOWN));
        ratePlanConfiguration.setThursday(rateUnqualifiedDetails.getThursday().setScale(2, RoundingMode.HALF_DOWN));
        ratePlanConfiguration.setFriday(rateUnqualifiedDetails.getFriday().setScale(2, RoundingMode.HALF_DOWN));
        ratePlanConfiguration.setSaturday(rateUnqualifiedDetails.getSaturday().setScale(2, RoundingMode.HALF_DOWN));
        return ratePlanConfiguration;
    }
}
