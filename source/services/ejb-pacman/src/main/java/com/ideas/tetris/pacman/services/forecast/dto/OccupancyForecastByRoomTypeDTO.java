package com.ideas.tetris.pacman.services.forecast.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class OccupancyForecastByRoomTypeDTO {

    private String currencyCode;

    private LocalDate occupancyDate;

    private String roomTypeCode;

    private BigDecimal singleRate;

    private BigDecimal doubleRate;

    private boolean isLimitedDataBuild;
}
