package com.ideas.tetris.pacman.services.decision;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.services.decision.entity.DecisionVolume;
import com.ideas.tetris.pacman.services.decision.repository.DecisionVolumeRepository;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.ngi.decision.hilton.HiltonDecisionType;
import com.ideas.tetris.pacman.services.ngi.dto.AbstractDecision;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Component
@Transactional
public class DecisionVolumeService {

    @Autowired
    private DecisionVolumeRepository decisionVolumeRepository;

    @Autowired
    private PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    private FileMetadataService fileMetadataService;

    public void saveDecisionVolume(Integer inputProcessingId, String correlationId, HiltonDecisionType decisionType, List<? extends AbstractDecision> decisions) {
        if(!pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.SAVE_DECISION_VOLUME)) {
            return;
        }
        DecisionVolume volume = new DecisionVolume();
        volume.setInputProcessingId(inputProcessingId);
        volume.setCorrelationId(correlationId);
        volume.setDecisionTypeName(decisionType.typeAsString());
        volume.setTotalDecisionVolume(decisions.size());
        volume.setCaughtUpDate(fileMetadataService.getCaughtUpLocalDate());
        volume.setCreateDTTM(LocalDateTime.now());
        decisionVolumeRepository.save(volume);
    }

    public List<DecisionVolume> getDecisionVolumesByDateRange(LocalDate startDate, LocalDate endDate) {
        return decisionVolumeRepository.getByDateRange(startDate, endDate);
    }

    public void updateDecisionVolume(Integer inputProcessingId, String correlationId, HiltonDecisionType decisionType, List<? extends AbstractDecision> decisions) {
        if(!pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.SAVE_DECISION_VOLUME)) {
            return;
        }
        List<DecisionVolume> volume = decisionVolumeRepository.getByInputProcessingIdCorrelationIdDecisionTypeName(inputProcessingId, correlationId, decisionType.typeAsString());
        if(volume.isEmpty()) {
            saveDecisionVolume(inputProcessingId, correlationId, decisionType, decisions);
            return;
        }
        volume.get(0).setTotalDecisionVolume(volume.get(0).getTotalDecisionVolume() + decisions.size());
        decisionVolumeRepository.save(volume.get(0));

    }
}
