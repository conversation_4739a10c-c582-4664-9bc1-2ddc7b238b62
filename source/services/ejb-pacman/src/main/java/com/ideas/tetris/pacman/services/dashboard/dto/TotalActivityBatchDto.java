package com.ideas.tetris.pacman.services.dashboard.dto;

import com.ideas.tetris.platform.common.crudservice.TableBatch;
import com.ideas.tetris.platform.common.crudservice.TableBatchAware;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.Date;
import java.util.Objects;

@Builder
@Data
public class TotalActivityBatchDto implements TableBatchAware {
    public static final String USP_ACTIVITY_RAW_INSERT = "usp_Total_Activity_Raw_Insert";
    public static final String ACTIVITY_RAW_BATCH = "Total_Activity_Raw_Batch";

    private Integer totalActivityId;
    private Integer propertyId;
    private Integer fileMetadataId;
    private BigDecimal totalAccomCapacity;
    private BigDecimal roomsSold;
    private BigDecimal roomsNotAvailableMaintenance;
    private BigDecimal roomsNotAvailableOther;
    private BigDecimal arrivals;
    private BigDecimal departures;
    private BigDecimal cancellations;
    private BigDecimal noShows;
    private BigDecimal roomRevenue;
    private BigDecimal foodRevenue;
    private BigDecimal totalRevenue;
    private BigDecimal totalProfit;
    private Date createDate;
    private Date occupancyDate;
    private Date snapShotDate;
    private Date lastUpdatedDate;
    private boolean isCPD;

    @Override
    public String getTableVariableName() {
        return ACTIVITY_RAW_BATCH;
    }

    @Override
    public void addTableBatchColumns(TableBatch tableBatch) {
        tableBatch.addColumn("Total_Activity_ID", Types.BIGINT);
        tableBatch.addColumn("Property_ID", Types.BIGINT);
        tableBatch.addColumn("File_Metadata_ID", Types.BIGINT);
        tableBatch.addColumn("Total_Accom_Capacity", Types.NUMERIC);
        tableBatch.addColumn("Rooms_Sold", Types.NUMERIC);
        tableBatch.addColumn("Rooms_Not_Avail_Maint", Types.NUMERIC);
        tableBatch.addColumn("Rooms_Not_Avail_Other", Types.NUMERIC);
        tableBatch.addColumn("Arrivals", Types.NUMERIC);
        tableBatch.addColumn("Departures", Types.NUMERIC);
        tableBatch.addColumn("Cancellations", Types.NUMERIC);
        tableBatch.addColumn("No_Shows", Types.NUMERIC);
        tableBatch.addColumn("Room_Revenue", Types.NUMERIC);
        tableBatch.addColumn("Food_Revenue", Types.NUMERIC);
        tableBatch.addColumn("Total_Revenue", Types.NUMERIC);
        tableBatch.addColumn("Total_Profit", Types.NUMERIC);
        tableBatch.addColumn("CreateDate", Types.TIMESTAMP);
        tableBatch.addColumn("Occupancy_DT", Types.DATE);
        tableBatch.addColumn("SnapShot_DTTM", Types.TIMESTAMP);
        tableBatch.addColumn("Last_Updated_DTTM", Types.TIMESTAMP);
        tableBatch.addColumn("Is_Cpd", Types.VARCHAR);
    }

    @Override
    public Object[] toTableBatchRow() {
        return new Object[]{getTotalActivityId(),
                getPropertyId(),
                getFileMetadataId(),
                getTotalAccomCapacity(),
                getRoomsSold(),
                getRoomsNotAvailableMaintenance(),
                getRoomsNotAvailableOther(),
                getArrivals(),
                getDepartures(),
                getCancellations(),
                getNoShows(),
                getRoomRevenue(),
                getFoodRevenue(),
                getTotalRevenue(),
                getTotalProfit(),
                Objects.nonNull(getCreateDate()) ? new Timestamp(getCreateDate().getTime()) : new Timestamp(System.currentTimeMillis()),
                Objects.nonNull(getOccupancyDate()) ? new java.sql.Date(getOccupancyDate().getTime()) : null,
                Objects.nonNull(getSnapShotDate()) ? new Timestamp(getSnapShotDate().getTime()) : new Timestamp(System.currentTimeMillis()),
                Objects.nonNull(getLastUpdatedDate()) ? new Timestamp(getLastUpdatedDate().getTime()) : new Timestamp(System.currentTimeMillis()),
                isCPD() ? "1" : "0"
        };
    }

    @Override
    public String getInsertStoredProcedureName() {
        return USP_ACTIVITY_RAW_INSERT;
    }
}
