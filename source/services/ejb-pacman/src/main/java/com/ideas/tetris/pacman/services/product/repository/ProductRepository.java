package com.ideas.tetris.pacman.services.product.repository;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Transactional
public class ProductRepository {
    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    protected CrudService tenantCrudService;

    public List<Integer> getMsIdsAgainstActiveFixedAboveBarProduct(List<Integer> msIds) {
        if (msIds.isEmpty()) {
            return List.of();
        }
        return tenantCrudService.findByNamedQuery(Product.GET_MS_IDS_AGAINST_ACTIVE_FIXED_ABOVE_BAR_PRODUCTS,
                QueryParameter.with("msIds", msIds).parameters());
    }
}
