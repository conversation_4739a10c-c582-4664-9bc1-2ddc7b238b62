package com.ideas.tetris.pacman.services.datafeed.dto.pricingdata;

import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateCode;

import java.io.Serializable;

public class PricingDataProductRateCodeAssignmentDTO implements Serializable {

    private String productName;

    private String rateCodeAssigned;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getRateCodeAssigned() {
        return rateCodeAssigned;
    }

    public void setRateCodeAssigned(String rateCodeAssigned) {
        this.rateCodeAssigned = rateCodeAssigned;
    }

    public PricingDataProductRateCodeAssignmentDTO() {
    }

    public PricingDataProductRateCodeAssignmentDTO(ProductRateCode prc) {
        this.productName = prc.getProduct().getName();
        this.rateCodeAssigned = prc.getRateCode();
    }
}
