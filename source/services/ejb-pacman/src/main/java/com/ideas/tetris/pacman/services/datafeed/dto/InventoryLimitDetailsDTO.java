package com.ideas.tetris.pacman.services.datafeed.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ideas.tetris.platform.common.rest.unmarshaller.DateSerializer;

import java.util.Date;

public class InventoryLimitDetailsDTO {
    @JsonSerialize(using = DateSerializer.class)
    private Date occupancyDate;
    private Long inventoryLimit;
    private Long overrideValue;

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public Long getInventoryLimit() {
        return inventoryLimit;
    }

    public void setInventoryLimit(Long inventoryLimit) {
        this.inventoryLimit = inventoryLimit;
    }

    public Long getOverrideValue() {
        return overrideValue;
    }

    public void setOverrideValue(Long overrideValue) {
        this.overrideValue = overrideValue;
    }
}
