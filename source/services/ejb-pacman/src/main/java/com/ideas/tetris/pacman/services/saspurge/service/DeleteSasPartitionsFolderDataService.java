package com.ideas.tetris.pacman.services.saspurge.service;

import com.ideas.sas.service.SASClientService;
import com.ideas.tetris.pacman.services.purge.SASDataSetLocator;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.HashMap;
import java.util.Map;

import static com.ideas.tetris.pacman.common.constants.Constants.DELETE_FILE_FORCELY;
import static com.ideas.tetris.pacman.common.constants.Constants.DELETE_PATH;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class DeleteSasPartitionsFolderDataService {

    @Autowired
	private SASDataSetLocator sasDataSetLocator;
    @Autowired
	private SASClientService sasClientService;
    @Autowired
	private JobServiceLocal jobService;

    private static final Logger LOGGER = Logger.getLogger(DeleteSasPartitionsFolderDataService.class);

    public static final String DELETED = "Deleted";
    public static final String NOT_READY_TO_DELETE = "Not Ready To Delete";
    public static final String READY_TO_DELETE = "Ready To Delete";

    public void deleteSasPartitionsFolderData(int propertyId) {
        String partitionDataSetsFolderPath = sasDataSetLocator.getSASPartitionDataSetsFolder(propertyId);
        String listOfPartitionFiles = sasDataSetLocator.getListOfFileNamesFromSASNode(partitionDataSetsFolderPath);
        String[] filenamesList = listOfPartitionFiles.split(",");
        deleteFilesExceptTemplates(partitionDataSetsFolderPath, filenamesList);
    }

    void deleteFilesExceptTemplates(String partitionDataSetsFolderPath, String[] filenamesList) {
        for (String filename : filenamesList) {
            if (filename.toLowerCase().contains("template")) {
                LOGGER.info(filename + " will not be deleted");
            } else {
                deleteFile(partitionDataSetsFolderPath, filename);
            }
        }
    }

    void deleteFile(String partitionDataSetsFolderPath, String filename) {
        try {
            sasClientService.executeFileOps(DELETE_FILE_FORCELY, new HashMap<>(Map.of(DELETE_PATH, partitionDataSetsFolderPath + filename)));
            LOGGER.info(filename + " deleted");
        } catch (Exception e) {
            LOGGER.info("Could not delete " + filename + " - " + e.getMessage());
        }
    }
}
