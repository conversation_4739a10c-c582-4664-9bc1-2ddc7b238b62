package com.ideas.tetris.pacman.services.purge;

import static com.ideas.tetris.pacman.services.purge.PurgeConstants.*;
import static java.lang.String.format;

public enum TenantPurgeEnum implements Purgable {

    ACCOM_ACTIVITY("ACCOM_ACTIVITY", OCCUPANCY_DT, KEEP_THREE_YEARS_IN_PAST, true),
    RESERVATION_NIGHT_CHANGE("RESERVATION_NIGHT", "Departure_DT", format("WHERE Persistent_Key IN (SELECT Persistent_Key FROM Reservation_Night %s)", KEEP_TWO_YEARS_IN_PAST), false),
    POST_DEPARTURE_REVENUE("RESERVATION_NIGHT", "DEPARTURE_DT", format("WHERE reservation_identifier IN (SELECT distinct reservation_identifier FROM Reservation_Night %s)", KEEP_TWO_YEARS_IN_PAST), false),
    RESERVATION_NIGHT("RESERVATION_NIGHT", "DEPARTURE_DT", KEEP_TWO_YEARS_IN_PAST, false),
    RESTORED_NO_SHOW_RESERVATION("RESTORED_NO_SHOW_RESERVATION", "DEPARTURE_DT", KEEP_TWO_YEARS_IN_PAST, false),
    OPERA_GROUP_BLOCK_CODE("GROUP_MASTER", END_DT, format("WHERE GROUP_ID IN (SELECT GROUP_ID FROM GROUP_MASTER GM %s)", KEEP_FIVE_YEARS_IN_PAST), false),
    PACE_GROUP_MASTER("GROUP_MASTER", END_DT, format("WHERE GROUP_ID IN (SELECT GROUP_ID FROM GROUP_MASTER GM %s)", KEEP_FIVE_YEARS_IN_PAST), false),
    PACE_GROUP_BLOCK("GROUP_MASTER", END_DT, format("WHERE GROUP_ID IN (SELECT GROUP_ID FROM GROUP_MASTER GM %s)", KEEP_FIVE_YEARS_IN_PAST), false),
    GROUP_BLOCK("GROUP_MASTER", END_DT, format("WHERE GROUP_ID IN (SELECT GROUP_ID FROM GROUP_MASTER GM %s)", KEEP_FIVE_YEARS_IN_PAST), false),
    GROUP_MASTER("GROUP_MASTER", END_DT, KEEP_FIVE_YEARS_IN_PAST, false),
    MKT_ACCOM_ACTIVITY("MKT_ACCOM_ACTIVITY", OCCUPANCY_DT, KEEP_THREE_YEARS_IN_PAST, false),    // Already indexed on Occupancy_DT
    PACE_ACCOM_ACTIVITY("PACE_ACCOM_ACTIVITY", OCCUPANCY_DT, KEEP_TWO_YEARS_IN_PAST, true),
    PACE_MKT_ACTIVITY("PACE_MKT_ACTIVITY", OCCUPANCY_DT, KEEP_TWO_YEARS_IN_PAST, false),    // Already indexed on Occupancy_DT
    PACE_TOTAL_ACTIVITY("PACE_TOTAL_ACTIVITY", OCCUPANCY_DT, KEEP_TWO_YEARS_IN_PAST, true),
    PACE_WEBRATE("PACE_WEBRATE", OCCUPANCY_DT, KEEP_TWO_YEARS_IN_PAST, false),
    PACE_WEBRATE_DIFFERENTIAL("PACE_WEBRATE_DIFFERENTIAL", OCCUPANCY_DT, KEEP_TWO_YEARS_IN_PAST,false),
    TOTAL_ACTIVITY("TOTAL_ACTIVITY", OCCUPANCY_DT, KEEP_THREE_YEARS_IN_PAST, true),
    CR_ACCOM_ACTIVITY("CR_ACCOM_ACTIVITY", OCCUPANCY_DT, KEEP_THREE_YEARS_IN_PAST, false),
    CR_MKT_ACCOM_ACTIVITY("CR_MKT_ACCOM_ACTIVITY", OCCUPANCY_DT, KEEP_THREE_YEARS_IN_PAST, true),
    CR_TOTAL_ACTIVITY("CR_TOTAL_ACTIVITY", OCCUPANCY_DT, KEEP_THREE_YEARS_IN_PAST, true),
    D360_BOOKING_SUMMARY_PACE("D360_BOOKING_SUMMARY_PACE", OCCUPANCY_DT, KEEP_TWO_YEARS_IN_PAST, false),
    D360_MKT_HIST_CAPACITY("D360_MKT_HIST_CAPACITY", OCCUPANCY_DT, KEEP_TWO_YEARS_IN_PAST, false),
    HD360_BOOKING_SUMMARY_PACE("HD360_BOOKING_SUMMARY_PACE", CAPTURE_DT, KEEP_TWO_YEARS_IN_PAST, false),
    HD360_TRANSITORY_BOOKING_SUMMARY_PACE("HD360_TRANSITORY_BOOKING_SUMMARY_PACE", CAPTURE_DT, KEEP_SEVEN_DAYS_IN_PAST, false),
    HD360_MKT_HIST_CAPACITY("HD360_MKT_HIST_CAPACITY", OCCUPANCY_DT, KEEP_TWO_YEARS_IN_PAST, false),
    HOTEL_MKT_ACCOM_ACTIVITY("HOTEL_MKT_ACCOM_ACTIVITY", OCCUPANCY_DT, KEEP_THREE_YEARS_IN_PAST, false), // Already indexed on Occupancy_DT
    PACE_CR_ACCOM_ACTIVITY("PACE_CR_ACCOM_ACTIVITY", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    WEBRATE("WEBRATE", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false), // Already indexed on Occupancy_DT
    DECISION_BAR_OUTPUT_OVR_DETAILS("Decision_Bar_Output_OVR", "Arrival_DT", format("WHERE Decision_Bar_Output_OVR_ID IN (SELECT Decision_Bar_Output_OVR_ID FROM Decision_Bar_Output_OVR %s)", KEEP_ONE_YEAR_IN_PAST), false),
    DECISION_ACK_STATUS("DECISION_ACK_STATUS", OCCUPANCY_DATE, KEEP_SEVEN_DAYS_IN_PAST, false),
    MP_DECISION_ACK_STATUS("MP_DECISION_ACK_STATUS", OCCUPANCY_DATE, KEEP_SEVEN_DAYS_IN_PAST, false),
    ARRIVAL_DEMAND_FCST_OVR("ARRIVAL_DEMAND_FCST_OVR", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    ARRIVAL_DEMAND_FCST("ARRIVAL_DEMAND_FCST", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, true),
    CP_UNQUALIFIED_DEMAND_FCST_PRICE("CP_UNQUALIFIED_DEMAND_FCST_PRICE", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    CP_DECISION_BAR_OUTPUT_OVR("CP_DECISION_BAR_OUTPUT_OVR", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    CP_DECISION_BAR_OUTPUT("CP_DECISION_BAR_OUTPUT", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, true),
    CP_DECISION_BAR_NOVR("CP_DECISION_BAR_NOVR", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, true),
    CP_DECISION_BAR_NOVR_DETAILS("CP_DECISION_BAR_NOVR_DETAILS", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_BAR_OUTPUT_OVR("DECISION_BAR_OUTPUT_OVR", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_BAR_OUTPUT("DECISION_BAR_OUTPUT", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, true),
    DECISION_DAILYBAR_OUTPUT("DECISION_DAILYBAR_OUTPUT", OCCUPANCY_DATE, KEEP_ONE_YEAR_IN_PAST, false), // Already indexed on Occupancy_DT
    DECISION_COW_VALUE_OVR("DECISION_COW_VALUE_OVR", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_FPLOS_BY_RANK("DECISION_FPLOS_BY_RANK", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_LRV("DECISION_LRV", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_OVRBK_ACCOM_OVR("DECISION_OVRBK_ACCOM_OVR", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_OVRBK_ACCOM("DECISION_OVRBK_ACCOM", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_OVRBK_PROPERTY_OVR("DECISION_OVRBK_PROPERTY_OVR", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_OVRBK_PROPERTY("DECISION_OVRBK_PROPERTY", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    VP_OVRBK_PROPERTY_SPLIT_RATIO("VP_OVRBK_PROPERTY_SPLIT_RATIO", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_QUALIFIED_FPLOS("DECISION_QUALIFIED_FPLOS", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_RESTRICT_HIGHEST_BAR_OVR("DECISION_RESTRICT_HIGHEST_BAR_OVR", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    NOTES("NOTES", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, true),
    OCCUPANCY_DEMAND_FCST_OVR("OCCUPANCY_DEMAND_FCST_OVR", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    OCCUPANCY_DEMAND_FCST("OCCUPANCY_DEMAND_FCST", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, true),
    OCCUPANCY_FCST("OCCUPANCY_FCST", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, true),
    OCCUPANCY_FCST_NOVR("OCCUPANCY_FCST_NOVR", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, true),
    PACE_ACCOM_OCCUPANCY_FCST("PACE_ACCOM_OCCUPANCY_FCST", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, true),
    CP_PACE_DECISION_BAR_OUTPUT("CP_PACE_DECISION_BAR_OUTPUT", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, true),
    PACE_BAR_OUTPUT("PACE_BAR_OUTPUT", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_BAR_OUTPUT_UPLOAD("PACE_BAR_OUTPUT_UPLOAD", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_DAILYBAR_OUTPUT("PACE_DAILYBAR_OUTPUT", OCCUPANCY_DATE, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_FPLOS_BY_RANK("PACE_FPLOS_BY_RANK", ARRIVAL_DT, KEEP_FIFTEEN_DAYS_IN_PAST, false),
    PACE_LRV("PACE_LRV", "Occupancy_DT", KEEP_ONE_YEAR_IN_PAST, false),
    PACE_MKT_OCCUPANCY_FCST("PACE_MKT_OCCUPANCY_FCST", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false), // Already indexed on Occupancy_DT
    PACE_OVRBK_ACCOM("PACE_OVRBK_ACCOM", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_OVRBK_ACCOM_UPLOAD("PACE_OVRBK_ACCOM_UPLOAD", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_OVRBK_PROPERTY("PACE_OVRBK_PROPERTY", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_OVRBK_PROPERTY_UPLOAD("PACE_OVRBK_PROPERTY_UPLOAD", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_QUALIFIED_FPLOS("PACE_QUALIFIED_FPLOS", ARRIVAL_DT, KEEP_FIFTEEN_DAYS_IN_PAST, true),
    UNQUALIFIED_DEMAND_FCST_PRICE("UNQUALIFIED_DEMAND_FCST_PRICE", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    WASH_FCST("WASH_FCST", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    WASH_FORECAST_GROUP_FCST_OVR("WASH_FORECAST_GROUP_FCST_OVR", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, true),
    WASH_FORECAST_GROUP_FCST("WASH_FORECAST_GROUP_FCST", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    WASH_IND_GROUP_FCST_OVR("WASH_IND_GROUP_FCST_OVR", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, true),
    WASH_IND_GROUP_FCST("WASH_IND_GROUP_FCST", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    WASH_PROPERTY_FCST("WASH_PROPERTY_FCST", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, true),
    ARR_DEP_FCST("ARR_DEP_FCST", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, true),
    DECISION_FPLOS_BY_HIERARCHY("DECISION_FPLOS_BY_HIERARCHY", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_FPLOS_BY_ROOMTYPE("DECISION_FPLOS_BY_ROOMTYPE", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_LRA_FPLOS("DECISION_LRA_FPLOS", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false), // Already indexed on Arrival_DT
    DECISION_LRA_MINLOS("DECISION_LRA_MINLOS", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false), // Already indexed on Arrival_DT
    DECISION_LRV_AT("DECISION_LRV", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_MINLOS("DECISION_MINLOS", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_UPLOAD_DATE_TO_EXTERNAL_SYSTEM("DECISION_UPLOAD_DATE_TO_EXTERNAL_SYSTEM", "LAST_UPLOAD_DTTM", KEEP_ONE_YEAR_IN_PAST, false),
    OCC_FCST_ORG("OCC_FCST_ORG", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_ACCOM_OCCUPANCY_FCST_NOTIFICATION("PACE_ACCOM_OCCUPANCY_FCST_NOTIFICATION", DECISION_ID, KEEP_TEN_DAYS_DECISION_ID_IN_PAST, false),
    PACE_BAR_OUTPUT_NOTIFICATION("PACE_BAR_OUTPUT_NOTIFICATION", DECISION_ID, KEEP_TEN_DAYS_DECISION_ID_IN_PAST, false),
    PACE_DECISION_LRA_FPLOS("PACE_DECISION_LRA_FPLOS", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_DECISION_LRA_MINLOS("PACE_DECISION_LRA_MINLOS", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_FPLOS_BY_HIERARCHY("PACE_FPLOS_BY_HIERARCHY", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_FPLOS_BY_ROOMTYPE("PACE_FPLOS_BY_ROOMTYPE", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_LRV_AT("PACE_LRV_AT", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_LRV_NOTIFICATION("PACE_LRV_NOTIFICATION", DECISION_ID, KEEP_TEN_DAYS_DECISION_ID_IN_PAST, false),
    PACE_MINLOS("PACE_MINLOS", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_MKT_OCCUPANCY_FCST_NOTIFICATION("PACE_MKT_OCCUPANCY_FCST_NOTIFICATION", DECISION_ID, KEEP_TEN_DAYS_DECISION_ID_IN_PAST, false),
    PACE_OVRBK_ACCOM_NOTIFICATION("PACE_OVRBK_ACCOM_NOTIFICATION", DECISION_ID, KEEP_TEN_DAYS_DECISION_ID_IN_PAST, false),
    PACE_OVRBK_PROPERTY_NOTIFICATION("PACE_OVRBK_PROPERTY_NOTIFICATION", DECISION_ID, KEEP_TEN_DAYS_DECISION_ID_IN_PAST, false),
    DECISION_RESTRICT_HIGHEST_BAR("DECISION_RESTRICT_HIGHEST_BAR", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    FS_FCST("FS_FCST", "Occupancy_DT", KEEP_ONE_YEAR_IN_PAST, false),
    FS_FCST_EVAL_OVERRIDE("FS_FCST_EVAL_OVERRIDE", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    FS_FCST_OVERRIDE("FS_FCST_OVERRIDE", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    REMOTE_TASK("REMOTE_TASK", "CREATED_DTTM", format("%s and TASK_STATUS NOT IN ('IN_PROGRESS', 'PENDING', 'FAILED')", KEEP_TEN_DAYS_IN_PAST), false),
    PP_OCCUPANCY_FCST("PP_OCCUPANCY_FCST", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false), // Already indexed on Occupancy_DT
    GROUP_FINAL_FORECAST_OVR("GROUP_FINAL_FORECAST_OVR", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    UNEXPECTED_DEMAND_NOTIFICATION_DETAILS("UNEXPECTED_DEMAND_NOTIFICATION_DETAILS", OCCUPANCY_DATE, KEEP_ONE_YEAR_IN_PAST, false),
    DECISIONS_DELIVERED("DECISIONS_DELIVERED", "CreateDate_DTTM", KEEP_TEN_DAYS_IN_PAST, false),
    SYNC_FLAGS_AUD("SYNC_FLAGS_AUD", "Last_Updated_DTTM", KEEP_FIVE_YEARS_IN_PAST, false),
    GROUP_FLOOR_OVR("GROUP_FLOOR_OVR", "OCCUPANCY_DT", KEEP_TWO_YEARS_IN_PAST, false),
    CP_CFG_BASE_AT("CP_Cfg_Base_AT", "End_Date", KEEP_ONE_YEAR_IN_PAST, false),
    CP_CFG_BASE_AT_DRAFT("CP_Cfg_Base_AT_Draft", "End_Date", KEEP_SIXTY_DAYS_IN_PAST, false),
    CP_CFG_OFFSET_AT("CP_Cfg_Offset_AT", "End_Date", KEEP_ONE_YEAR_IN_PAST, false),
    CP_CFG_OFFSET_AT_DRAFT("CP_CFG_OFFSET_AT_DRAFT", "End_Date", KEEP_SIXTY_DAYS_IN_PAST, false),
    CP_CFG_OFFSET_AT_FLOOR_CEILING("CP_Cfg_Offset_AT_FloorCeil", "Last_Updated_DTTM", KEEP_TWO_YEARS_IN_PAST, false),
    GROUP_FLOOR_OVR_ALERT_DETAILS("GROUP_FLOOR_OVR_ALERT_DETAILS", "OCCUPANCY_DT", KEEP_TWO_YEARS_IN_PAST, false),
    GROUP_FLOOR_OVR_CONSTRAINING_BAR("GROUP_FLOOR_OVR_CONSTRAINING_BAR", "OCCUPANCY_DT", KEEP_TWO_YEARS_IN_PAST, false),
    GRP_PRC_CFG_BASE_AT("Grp_Prc_Cfg_Base_AT", "End_Date", KEEP_ONE_YEAR_IN_PAST, false),
    ACCOM_TYPE_SUPPLEMENT("Accom_Type_Supplement", "End_Date", KEEP_ONE_YEAR_IN_PAST, false),
    GFF_FG_OVR("GFF_FG_OVR", "Created_DTTM", KEEP_ONE_YEAR_IN_PAST, false),
    GFF_FG_OVR_AUD("GFF_FG_OVR_AUD", "Created_DTTM", KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_ANOMALY_SMOKE_TEST_RESULT("Decision_Anomaly_Smoke_Test_Result", "Snapshot_Date", KEEP_ONE_YEAR_IN_PAST, false),
    MANUAL_RESTRICTION_PROPERTY_OVR("MANUAL_RESTRICTION_PROPERTY_OVR", "Created_DTTM", KEEP_ONE_YEAR_IN_PAST, false),
    PACE_MANUAL_RESTRICTION_PROPERTY_OVR("PACE_MANUAL_RESTRICTION_PROPERTY_OVR", "Created_DTTM", KEEP_ONE_YEAR_IN_PAST, false),
    MANUAL_RESTRICTION_ACCOM_OVR("MANUAL_RESTRICTION_ACCOM_OVR", "Created_DTTM", KEEP_ONE_YEAR_IN_PAST, false),
    PACE_MANUAL_RESTRICTION_ACCOM_OVR("PACE_MANUAL_RESTRICTION_ACCOM_OVR", "Created_DTTM", KEEP_ONE_YEAR_IN_PAST, false),
    REVENUE_STREAM_DETAIL("REVENUE_STREAM_DETAIL", "OCCUPANCY_DT", format("%s and Revenue_Stream_ID IN (SELECT Revenue_Stream_ID FROM Revenue_Stream)", KEEP_TWO_YEARS_IN_PAST), false),
    SCHEDULEDREPORT_DELIVERY_AUDIT("ScheduledReport_Delivery_Audit", "Last_Updated_DTTM", KEEP_THIRTY_DAYS_IN_PAST, false),
    ASYNC_REPORTS("Async_Reports", "Created_DTTM", KEEP_SIXTY_DAYS_IN_PAST, false),
    MVCR_RATE_AT("MVCR_Rate_AT", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_DAILYBAR_OUTPUT_NONHILTONCRS("DECISION_DAILYBAR_OUTPUT_NONHILTONCRS", OCCUPANCY_DATE, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_DAILYBAR_OUTPUT_NONHILTONCRS("PACE_DAILYBAR_OUTPUT_NONHILTONCRS", OCCUPANCY_DATE, KEEP_ONE_YEAR_IN_PAST, false),
    PRICE_TEST_CFG_SCALE("Price_Test_Cfg_Scale", "End_Date", KEEP_ONE_YEAR_IN_PAST, false),
    CENTRAL_RMS_PRICE_DATA("Central_RMS_Price_Data", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, true),
    CENTRAL_RMS_COMP_OUTLIER("Central_RMS_Comp_Outlier", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, true),
    OPERATIONAL_FCST("OPERATIONAL_FCST", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, true),
    CENTRAL_RMS_TRAN_DMD_SCALE("Central_RMS_Tran_DMD_Scale", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, true),
    CP_PACE_DECISION_BAR_OUTPUT_DIFFERENTIAL("CP_PACE_DECISION_BAR_OUTPUT_DIFFERENTIAL", ARRIVAL_DT, KEEP_TWO_YEARS_IN_PAST, false),
    OCCUPANCY_FCST_EXT("OCCUPANCY_FCST_EXT", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    CP_DECISION_BAR_OUT_EXT("CP_DECISION_BAR_OUT_EXT", ARRIVAL_DT, KEEP_ONE_YEAR_IN_PAST, false),
    OCCUPANCY_DEMAND_FCST_EXT("OCCUPANCY_DEMAND_FCST_EXT", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_FROM_SERVICE("PACE_From_Service", ARRIVAL_DT, KEEP_FIFTEEN_DAYS_IN_PAST, false),
    PACE_OPERATIONAL_MKT_FCST("Pace_Operational_Mkt_Fcst", FORECAST_AS_OF_DATE, KEEP_FIVE_YEARS_IN_PAST, false),
    PACE_PROFIT_ADJUSTMENT("Pace_Profit_Adjustment", "CreateDate_DTTM", KEEP_TWO_YEARS_IN_PAST, false),

    DECISION_GP_INV_LIMIT_OVR("DECISION_GP_INV_LIMIT_OVR", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    DECISION_GP_INV_LIMIT_OVR_AUD("DECISION_GP_INV_LIMIT_OVR_AUD", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    REFERENCE_PRICE_LATEST("reference_price_latest",ARRIVAL_DT,KEEP_ONE_YEAR_IN_PAST,false),
    DECISION_GP_INV_LIMIT("Decision_GP_Inv_Limit",OCCUPANCY_DT,KEEP_ONE_YEAR_IN_PAST,false),
    PACE_GP_INV_LIMIT_UPLOAD("Pace_GP_Inv_Limit_Upload", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    MP_DECISION_BAR_OVR("MP_Decision_Bar_Ovr", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    MP_DECISION_BAR("MP_Decision_Bar", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    MP_PACE_DECISION_BAR_DIFFERENTIAL("MP_Pace_Decision_Bar_Differential", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    VP_GP_INV_LIMIT_PROPERTY_SPLIT_RATIO("VP_GP_INV_LIMIT_PROPERTY_SPLIT_RATIO", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    PACE_BACKFILL_LOG("PACE_BACKFILL_LOG", "CREATED_DTTM", KEEP_THREE_YEARS_IN_PAST, false),
    IMMEDIATE_FULL_DECISIONS("Immediate_Full_Decisions", "CREATED_DTTM", KEEP_THREE_YEARS_IN_PAST, false),
    OCCUPANCY_FCST_PROP_OVR("Occupancy_FCST_Prop_OVR", OCCUPANCY_DT, KEEP_THREE_YEARS_IN_PAST, false),
    PEAK_OCCUPANCY_DMD_OVR("Peak_Occupancy_Dmd_OVR", OCCUPANCY_DT, KEEP_THREE_YEARS_IN_PAST, false),
    PRICING_SENSITIVITY("Pricing_Sensitivity", OCCUPANCY_DT, KEEP_THREE_YEARS_IN_PAST, false),
    DECISION_OVRBK_PROPERTY_AUTOSCALED_HISTORY("DECISION_OVRBK_PROPERTY_AUTOSCALED_HISTORY", OCCUPANCY_DT, KEEP_ONE_YEAR_IN_PAST, false),
    SAS_DB_QUERY_AUDIT("SAS_DB_QUERY_AUDIT", "CREATED_DTTM", KEEP_SIX_MONTHS_IN_PAST, false)
    ;

    private final String schema;
    private final String tableToDelete;
    private final String tableToCompare;
    private final String fieldToCompare;
    private final String whereClauseTemplate;
    private boolean propertyIdAppliedToWhereClause;

    TenantPurgeEnum(String tableName, String fieldToCompare, String whereClauseTemplate, boolean propertyIdAppliedToWhereClause) {
        this.schema = DBO;
        this.tableToDelete = this.name();
        this.tableToCompare = tableName;
        this.fieldToCompare = fieldToCompare;
        this.whereClauseTemplate = whereClauseTemplate;
        this.propertyIdAppliedToWhereClause = propertyIdAppliedToWhereClause;
    }

    @Override
    public String getSchema() {
        return schema;
    }

    @Override
    public String getTableToDelete() {
        return tableToDelete;
    }

    @Override
    public String getTableToCompare() {
        return tableToCompare;
    }

    @Override
    public String getFieldToCompare() {
        return fieldToCompare;
    }

    public String getWhereClauseTemplate() {
        return whereClauseTemplate;
    }

    @Override
    public boolean isFailedSilently() {
        return false;
    }

    @Override
    public boolean isPropertyIdAppliedToWhereClause() {
        return propertyIdAppliedToWhereClause;
    }
}
