package com.ideas.tetris.pacman.services.reports.operations.dto;

import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import java.time.LocalDate;
import java.util.Date;

public class OperationsReportDTO {
    private String dayOfWeek;
    private Date occupancyDate;
    private Integer capacity;
    private Integer outOfOrder;
    private String specialEvent;
    private Integer onBooksOccupancy;
    private Integer onBooksArrivals;
    private Integer onBooksDepartures;
    private Integer onBooksStayThrus;
    private Integer forecastOccupancy;
    private Integer forecastArrivals;
    private Integer forecastDepartures;
    private Integer forecastStayThrus;
    private Integer numberOfAdultsOnBooks;
    private Integer numberOfChildrenOnBooks;
    private Integer numberOfAdults;
    private Integer numberOfChildren;

    public Integer getNumberOfAdultsOnBooks() {
        return numberOfAdultsOnBooks;
    }

    public void setNumberOfAdultsOnBooks(Integer numberOfAdultsOnBooks) {
        this.numberOfAdultsOnBooks = numberOfAdultsOnBooks;
    }

    public Integer getNumberOfChildrenOnBooks() {
        return numberOfChildrenOnBooks;
    }

    public void setNumberOfChildrenOnBooks(Integer numberOfChildrenOnBooks) {
        this.numberOfChildrenOnBooks = numberOfChildrenOnBooks;
    }

    public Integer getNumberOfAdults() {
        return numberOfAdults;
    }

    public void setNumberOfAdults(Integer numberOfAdults) {
        this.numberOfAdults = numberOfAdults;
    }

    public Integer getNumberOfChildren() {
        return numberOfChildren;
    }

    public void setNumberOfChildren(Integer numberOfChildren) {
        this.numberOfChildren = numberOfChildren;
    }

    public String getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(String dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(LocalDate occupancyDate) {
        this.occupancyDate = DateUtil.convertLocalDateToJavaUtilDate(occupancyDate);
    }

    public Integer getCapacity() {
        return capacity;
    }

    public void setCapacity(Integer capacity) {
        this.capacity = capacity;
    }

    public String getSpecialEvent() {
        return specialEvent;
    }

    public void setSpecialEvent(String specialEvent) {
        this.specialEvent = specialEvent;
    }

    public Integer getOnBooksOccupancy() {
        return onBooksOccupancy;
    }

    public void setOnBooksOccupancy(Integer onBooksOccupancy) {
        this.onBooksOccupancy = onBooksOccupancy;
    }

    public Integer getOnBooksArrivals() {
        return onBooksArrivals;
    }

    public void setOnBooksArrivals(Integer onBooksArrivals) {
        this.onBooksArrivals = onBooksArrivals;
    }

    public Integer getOnBooksDepartures() {
        return onBooksDepartures;
    }

    public void setOnBooksDepartures(Integer onBooksDepartures) {
        this.onBooksDepartures = onBooksDepartures;
    }

    public Integer getOnBooksStayThrus() {
        return onBooksStayThrus;
    }

    public void setOnBooksStayThrus(Integer onBooksStayThrus) {
        this.onBooksStayThrus = onBooksStayThrus;
    }

    public Integer getForecastOccupancy() {
        return forecastOccupancy;
    }

    public void setForecastOccupancy(Integer forecastOccupancy) {
        this.forecastOccupancy = forecastOccupancy;
    }

    public Integer getForecastArrivals() {
        return forecastArrivals;
    }

    public void setForecastArrivals(Integer forecastArrivals) {
        this.forecastArrivals = forecastArrivals;
    }

    public Integer getForecastDepartures() {
        return forecastDepartures;
    }

    public void setForecastDepartures(Integer forecastDepartures) {
        this.forecastDepartures = forecastDepartures;
    }

    public Integer getForecastStayThrus() {
        return forecastStayThrus;
    }

    public void setForecastStayThrus(Integer forecastStayThrus) {
        this.forecastStayThrus = forecastStayThrus;
    }

    public Integer getOutOfOrder() {
        return outOfOrder;
    }

    public void setOutOfOrder(Integer outOfOrder) {
        this.outOfOrder = outOfOrder;
    }
}
