package com.ideas.tetris.pacman.services.reports.restrictionlevel;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.reports.restrictionlevel.dto.RestrictionLevelReportDTO;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;

import javax.inject.Inject;
import java.util.List;
import java.util.Map;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Transactional
@Component
public abstract class RestrictionLevelReport {

    @Autowired
	protected DateService dateService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    @Autowired
	protected PacmanConfigParamsService configService;

    public List<RestrictionLevelReportDTO> generate(RestrictionLevelSpec rspec) {
        String query = getQuery();
        Map<String, Object> parameters = buildQueryParams(rspec).parameters();
        return execute(query, parameters);
    }

    private List<RestrictionLevelReportDTO> execute(String query, Map<String, Object> parameters) {
        return tenantCrudService.findByNativeQuery(query, parameters, new RowMapper<RestrictionLevelReportDTO>() {
            @Override
            public RestrictionLevelReportDTO mapRow(Object[] row) {
                return getRowMappedFor(row);
            }
        });
    }

    protected QueryParameter buildQueryParams(RestrictionLevelSpec rspec) {
        String accomTypeIdsStr = rspec.getAccomTypeIds().toString().replaceAll("[ \\] \\[ ]", "");
        QueryParameter queryParameter = QueryParameter.with("start_date", rspec.getStartDate())
                .and("end_date", rspec.getEndDate())
                .and("caught_date", dateService.getCaughtUpJavaLocalDate())
                .and("accom_type_id", accomTypeIdsStr)
                .and("isRollingDate", rspec.getIsRollingDate())
                .and("rollingStartDate", rspec.getRollingStartDate())
                .and("rollingEndDate", rspec.getRollingEndDate())//;
                .and("isDecisionAtHotelLevel", rspec.isSrpFplosAtTotalLevelEnabled() ? 1 : 0);
        return queryParameter;
    }

    protected RestrictionLevelReportDTO getRowMappedFor(Object[] row) {
        String qualifiedFplosMaxLos = configService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value());
        RestrictionLevelReportDTO reportDTO = new RestrictionLevelReportDTO(Integer.valueOf(qualifiedFplosMaxLos));
        return reportDTO.newFpLosReportFor(row);
    }

    protected abstract String getQuery();
}