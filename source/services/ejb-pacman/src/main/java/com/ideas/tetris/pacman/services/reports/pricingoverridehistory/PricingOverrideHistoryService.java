package com.ideas.tetris.pacman.services.reports.pricingoverridehistory;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.pricingoverridehistory.dto.OverrideHistoryDto;
import com.ideas.tetris.pacman.services.reports.pricingoverridehistory.dto.PricingOverrideHistoryDTO;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.JasperReportDataConverter;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.PricingOverrideHistoryReportConverter;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.PricingOverrideHistoryReportCriteria;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportData;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.scheduledreport.service.JasperReportService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PricingOverrideHistoryService extends JasperReportService<ScheduledReportData, PricingOverrideHistoryReportCriteria> {

    private static final Logger LOGGER = Logger.getLogger(PricingOverrideHistoryService.class.getName());

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @PricingOverrideHistoryReportConverter.Qualifier
    @Autowired
	@Qualifier("pricingOverrideHistoryReportConverter")
	private JasperReportDataConverter<List<PricingOverrideHistoryDTO>, PricingOverrideHistoryReportCriteria> pricingOverrideHistoryReportConverter;

    public static final String OVERRIDE_HISTORY_QUERY_DIFFERENTIAL_TABLE = "WITH  " +
            "Output_Override as ( " +
            "  SELECT         " +
            "    ovr.New_Override, ovr.Old_Override, " +
            "        CASE WHEN supplements.Supplement_Method = 1 THEN ovr.New_BAR * ISNULL((supplements.Supplement_Value / 100.00 + 1), 1) ELSE ovr.New_BAR  + ISNULL(supplements.Supplement_Value, 0) END New_BAR, " +
            "        CASE WHEN supplements.Supplement_Method = 1 THEN ovr.Old_BAR * ISNULL((supplements.Supplement_Value / 100.00 + 1), 1) ELSE ovr.Old_BAR  + ISNULL(supplements.Supplement_Value, 0) END Old_BAR, " +
            "        CASE WHEN supplements.Supplement_Method = 1 THEN ovr.New_Ceil_Rate * ISNULL((supplements.Supplement_Value / 100.00 + 1), 1) ELSE ovr.New_Ceil_Rate  + ISNULL(supplements.Supplement_Value, 0) END New_Ceil_Rate, " +
            "        CASE WHEN supplements.Supplement_Method = 1 THEN ovr.Old_Ceil_Rate * ISNULL((supplements.Supplement_Value / 100.00 + 1), 1) ELSE ovr.Old_Ceil_Rate  + ISNULL(supplements.Supplement_Value, 0) END Old_Ceil_Rate, " +
            "        CASE WHEN supplements.Supplement_Method = 1 THEN ovr.New_Floor_Rate * ISNULL((supplements.Supplement_Value / 100.00 + 1), 1) ELSE ovr.New_Floor_Rate  + ISNULL(supplements.Supplement_Value, 0) END New_Floor_Rate, " +
            "        CASE WHEN supplements.Supplement_Method = 1 THEN ovr.Old_Floor_Rate * ISNULL((supplements.Supplement_Value / 100.00 + 1), 1) ELSE ovr.Old_Floor_Rate  + ISNULL(supplements.Supplement_Value, 0) END Old_Floor_Rate,  " +
            "        users.User_Name, Decision_Id, CreateDate " +
            "  FROM  " +
            "    CP_Decision_Bar_Output_OVR ovr  " +
            "  inner join Users users  on ovr.User_ID = users.User_ID " +
            "  LEFT JOIN ufn_get_accom_type_supplements(:propertyId,:arrivalDt,:arrivalDt) supplements " +
            "  	 ON ovr.Arrival_DT = supplements.Arrival_DT and ovr.Accom_Type_ID = supplements.Accom_Type_ID and supplements.Occupancy_Type = 1  and ovr.Product_ID = supplements.Product_ID" +
            "  where ovr.Arrival_DT = :arrivalDt and ovr.Accom_Type_ID = :accomTypeId and ovr.Product_ID = :productId " +
            "), " +
            "Decesions_For_Pending_Override as ( " +
            "  select  " +
            "    Decision_ID  " +
            "  from  Output_Override " +
            "  where New_Override = 'PENDING' " +
            "), " +
            "Pace_With_Target_Decision_Types as ( " +
            "  select  " +
            "    PACE.* " +
            "  from " +
            "    CP_Pace_Decision_Bar_Output_Differential pace  " +
            "  join Decision dc on dc.Decision_ID= pace.Decision_ID and dc.Decision_Type_ID in (1,2,5,29) " +
            "  where pace.Product_ID = :productId  " +
            "  and  pace.Arrival_DT = :arrivalDt and  pace.Accom_Type_ID = :accomTypeId " +
            "), " +
            "Final_Bar_For_Decision as ( " +
            "  select   " +
            "    decisions.Decision_ID as D1, " +
            "    pace.Decision_ID as DP, " +
            "    pace.Final_BAR, " +
            "    ROW_NUMBER() over (partition by decisions.Decision_ID order by pace.Decision_ID desc) as rank " +
            "  FROM  " +
            "    Decesions_For_Pending_Override decisions, Pace_With_Target_Decision_Types pace " +
            "  where  " +
            "    pace.Decision_ID < decisions.Decision_ID " +
            "), " +
            "Final_Bars_For_Pending_Override_From_Pace as ( " +
            "  select  " +
            "    D1 as Decision_Id, Final_BAR " +
            "  from  " +
            "    Final_Bar_For_Decision " +
            "  where " +
            "    rank = 1 " +
            ")       " +
            "select  " +
            "  ovr.New_Override, ovr.Old_Override,  ovr.New_BAR  New_BAR, " +
            "  ovr.Old_BAR  Old_BAR, ovr.New_Ceil_Rate New_Ceil_Rate, " +
            "  ovr.Old_Ceil_Rate Old_Ceil_Rate, ovr.New_Floor_Rate New_Floor_Rate, " +
            "  ovr.Old_Floor_Rate Old_Floor_Rate, " +
            "  ovr.User_Name, CreateDate,   final_bars.Final_BAR " +
            "from " +
            "  Output_Override ovr " +
            "left outer join  Final_Bars_For_Pending_Override_From_Pace final_bars " +
            "on " +
            "  ovr.Decision_ID = final_bars.Decision_Id" +
            "  order by CreateDate desc";

    private CrudService getCrudServiceBean() {
        return crudService;
    }

    public void setCrudServiceBean(CrudService crudService) {
        this.crudService = crudService;
    }

    public List<PricingOverrideHistoryDTO> getPricingOverrideHistoryData(LocalDate startDate, LocalDate endDate, Integer accomClassId, Integer isRollingDate, String rollingStartDate, String rollingEndDate) {

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("start_date", java.sql.Date.valueOf(startDate))
                .and("end_date", java.sql.Date.valueOf(endDate))
                .and("accom_class_id", accomClassId)
                .and("isRollingDate", isRollingDate)
                .and("rolling_start_date", rollingStartDate)
                .and("rolling_end_date", rollingEndDate)
                .and("isRestrictHighestBarEnabled", configParamsService.getParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value()));
        try {
            List<Object[]> reportDataByStaticDates = getCrudServiceBean().findByNativeQuery("select * from dbo.ufn_get_pricing_override_history_report(:property_id, :start_date, :end_date, :accom_class_id," +
                            ":isRollingDate, :rolling_start_date, :rolling_end_date, :isRestrictHighestBarEnabled) order by Arrival_DT, Accom_Class_Name, los,CreateDate_DTTM desc",
                    queryParameters.parameters());
            return prepareReportData(reportDataByStaticDates);
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<PricingOverrideHistoryDTO>();
        }
    }

    private List<PricingOverrideHistoryDTO> prepareReportData(List<Object[]> reportDataByStaticDates) {
        List<PricingOverrideHistoryDTO> pricingOverrideHistoryDTOs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(reportDataByStaticDates)) {
            for (Object[] row : reportDataByStaticDates) {
                PricingOverrideHistoryDTO data = new PricingOverrideHistoryDTO();
                data.setPropertyName((String) row[0]);
                data.setDow((String) row[1]);
                data.setArrivalDate((Date) row[2]);
                data.setAccomClassName((String) row[3]);
                data.setLos((Integer) row[4]);
                data.setNewOverride((String) row[5]);
                data.setRateCodeName((String) row[6]);
                data.setUserName((String) row[7]);
                data.setCreateDate(DateUtil.convertJavaUtilDateToZonedDateTime((Date) row[8]));
                data.setOldRateCodeName((String) row[9]);
                data.setOldOverride((String) row[10]);
                data.setNotes((String) row[11]);
                data.setNewBarRate((BigDecimal) row[12]);
                data.setOldBarRate((BigDecimal) row[13]);
                data.setNewCeilRateCodeName((String) row[14]);
                data.setOldCeilRateCodeName((String) row[15]);
                data.setNewCeilBarRate((BigDecimal) row[16]);
                data.setOldCeilBarRate((BigDecimal) row[17]);
                // Add newDecisionDisplay and oldDecisionDisplay column
                data.setNewDecisionDisplay(data.getDecisionForNewOverride());
                data.setOldDecisionDisplay(data.getDecisionForOldOverride());
                pricingOverrideHistoryDTOs.add(data);
            }
        }
        return pricingOverrideHistoryDTOs;
    }

    public void populateReportCriteria(PricingOverrideHistoryReportCriteria reportCriteria) {

        String barDecisionValue = configParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        if (Constants.BAR_DECISION_VALUE_LOS.equalsIgnoreCase(barDecisionValue)) {
            reportCriteria.setIsBarByLosProperty(true);
        }

        String propertyId = PacmanWorkContextHelper.getPropertyId().toString();
        String userId = "''";
        String baseCurrency = reportCriteria.getCurrency();

        LocalDate startDate = reportCriteria.getStartDate();
        LocalDate endDate = reportCriteria.getEndDate();
        Integer rolling = reportCriteria.getIsRollingDate();
        String rollingStartDate = reportCriteria.getRollingStartDate();
        String rollingEndDate = reportCriteria.getRollingEndDate();

        String sql = " select * from dbo.ufn_get_filter_selection " +
                "('" + propertyId + "'," + userId + ",'" + baseCurrency + "'," +
                "'" + rolling + "'," + "'" + startDate + "'," + "'" + endDate + "','','','','','',''," +
                "'" + rollingStartDate + "'," + "'" + rollingEndDate + "','','','','','','' )";

        List<Object[]> resultList = crudService.findByNativeQuery(sql);
        if (resultList != null) {
            Object[] result = resultList.get(0);
            reportCriteria.setPropertyName((String) result[0]);
            reportCriteria.setStartDate(DateUtil.convertJavaUtilDateToLocalDate((Date) result[3], true));
            reportCriteria.setEndDate(DateUtil.convertJavaUtilDateToLocalDate((Date) result[4], true));
        }
        org.joda.time.DateTime dt = reportCriteria.getCreatedOn();
        org.joda.time.DateTime dt1 = ScheduledReportUtils.convertDateTimeToTimeZone(dt, TimeZone.getTimeZone(configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value())));
        reportCriteria.setCreatedOn(dt1);
    }

    @Override
    protected JasperReportDataConverter<ScheduledReportData, PricingOverrideHistoryReportCriteria> getJasperReportDataConverter() {
        return null;
    }

    @Override
    public String getReportTitle(ScheduledReport<PricingOverrideHistoryReportCriteria> scheduledReport) {
        return ResourceUtil.getText("pricing-override-history-report", scheduledReport.getLanguage());
    }

    @Override
    protected ScheduledReportData retrieveData(ScheduledReport<PricingOverrideHistoryReportCriteria> scheduledReport) {
        final PricingOverrideHistoryReportCriteria reportCriteria = scheduledReport.getReportCriteria();
        List<PricingOverrideHistoryDTO> data = null;
        if (reportCriteria.isContinuousPricing()) { // means CP property
            data = getPricingOverrideHistoryDataForCP(reportCriteria);
        } else {
            data = getPricingOverrideHistoryData(reportCriteria.getStartDate(), reportCriteria.getEndDate(), reportCriteria.getAccomClassId(),
                    reportCriteria.getIsRollingDate(), reportCriteria.getRollingStartDate(), reportCriteria.getRollingEndDate());
        }
        populateReportCriteria(reportCriteria);

        ScheduledReportSheet sheet = new ScheduledReportSheet("pricing-override-history-report", data, PricingOverrideHistoryDTO.class);
        List<ScheduledReportSheet> sheetList = new ArrayList<ScheduledReportSheet>(1);
        sheetList.add(sheet);
        return new ScheduledReportData("pricing-override-history-report", sheetList);
    }

    public List<PricingOverrideHistoryDTO> getPricingOverrideHistoryDataForCP(PricingOverrideHistoryReportCriteria reportCriteria) {

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("start_date", java.sql.Date.valueOf(reportCriteria.getStartDate()))
                .and("end_date", java.sql.Date.valueOf(reportCriteria.getEndDate()))
                .and("room_types", reportCriteria.getAccomTypes())
                .and("isRollingDate", reportCriteria.getIsRollingDate())
                .and("rolling_start_date", reportCriteria.getRollingStartDate())
                .and("rolling_end_date", reportCriteria.getRollingEndDate())
                //added new parameter : hard coded as of now but it should take from report Criteria
                .and("highestRestrictedBarEnabled", "false");

        try {
            List<Object[]> reportDataByStaticDates = getCrudServiceBean().findByNativeQuery("select * from dbo.ufn_get_pricing_override_history_cp_report(:property_id, :start_date, :end_date, :room_types," +
                            ":isRollingDate, :rolling_start_date, :rolling_end_date ,:highestRestrictedBarEnabled) order by Arrival_DT, Accom_Class_Name, los,CreateDate_DTTM desc",
                    queryParameters.parameters());
            return prepareReportDataForCP(reportDataByStaticDates);
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<PricingOverrideHistoryDTO>();
        }
    }

    private List<PricingOverrideHistoryDTO> prepareReportDataForCP(List<Object[]> reportDataByStaticDates) {
        List<PricingOverrideHistoryDTO> pricingOverrideHistoryDTOs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(reportDataByStaticDates)) {
            for (Object[] row : reportDataByStaticDates) {
                PricingOverrideHistoryDTO data = new PricingOverrideHistoryDTO();
                data.setPropertyName((String) row[0]);
                data.setDow((String) row[1]);
                data.setArrivalDate((Date) row[2]);
                data.setAccomClassName((String) row[3]);
                data.setRoomTypeName((String) row[4]);
                data.setLos((Integer) row[5]);
                data.setNewOverride((String) row[6]);
                data.setOldOverride((String) row[7]);
                data.setNewBarRate((BigDecimal) row[8]);
                data.setOldBarRate((BigDecimal) row[9]);
                data.setNewCeilBarRate((BigDecimal) row[10]);
                data.setOldCeilBarRate((BigDecimal) row[11]);
                data.setNewFloorRate((BigDecimal) row[12]);
                data.setOldFloorRate((BigDecimal) row[13]);
                data.setNotes((String) row[14]);
                data.setUserName((String) row[15]);
                data.setCreateDate(DateUtil.convertJavaUtilDateToZonedDateTime((Date) row[16]));
                // Add newDecisionDisplay and oldDecisionDisplay column
                data.setNewDecisionDisplay(data.getDecisionForNewOverrideForCP());
                data.setOldDecisionDisplay(data.getDecisionForOldOverrideForCP());
                pricingOverrideHistoryDTOs.add(data);
            }
        }
        return pricingOverrideHistoryDTOs;
    }


    public List<OverrideHistoryDto> getPricingOverrideHistoryForCP(Date arrivalDate, int accomTypeId, Integer productId) {

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("arrivalDt", new java.sql.Date(arrivalDate.getTime()))
                .and("propertyId", propertyId)
                .and("accomTypeId", accomTypeId)
                .and("productId", productId);

        try {
            List<Object[]> reportDataByStaticDates;
            reportDataByStaticDates = getCrudServiceBean().findByNativeQuery(OVERRIDE_HISTORY_QUERY_DIFFERENTIAL_TABLE, queryParameters.parameters());
            return prepareOverrideDataForCP(reportDataByStaticDates);
        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return new ArrayList<OverrideHistoryDto>();
        }
    }


    private List<OverrideHistoryDto> prepareOverrideDataForCP(List<Object[]> reportDataByStaticDates) {
        List<OverrideHistoryDto> pricingOverrideHistoryDTOs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(reportDataByStaticDates)) {
            for (Object[] row : reportDataByStaticDates) {
                OverrideHistoryDto data = new OverrideHistoryDto();
                data.setNewOverride((String) row[0]);
                data.setOldOverride((String) row[1]);
                data.setNewBarRate((BigDecimal) row[2]);
                data.setOldBarRate((BigDecimal) row[3]);
                data.setNewCeilBarRate((BigDecimal) row[4]);
                data.setOldCeilBarRate((BigDecimal) row[5]);
                data.setNewFloorRate((BigDecimal) row[6]);
                data.setOldFloorRate((BigDecimal) row[7]);
                data.setUserName((String) row[8]);
                data.setCreateDate(DateUtil.convertJavaUtilDateToZonedDateTime((Date) row[9]));
                data.setOriginalDecision((BigDecimal) row[10]);
                pricingOverrideHistoryDTOs.add(data);
            }
        }
        return pricingOverrideHistoryDTOs;
    }
}
