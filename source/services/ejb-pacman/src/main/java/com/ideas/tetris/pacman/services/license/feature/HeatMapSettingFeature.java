package com.ideas.tetris.pacman.services.license.feature;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.heatmap.entity.HeatMapRangeAndColors;
import com.ideas.tetris.pacman.services.heatmap.entity.HeatMapRangeAndColorsConfig;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.license.entities.LicensePackage;
import com.ideas.tetris.platform.common.license.migration.actions.LicenseFeatureUpgradable;
import com.ideas.tetris.platform.common.license.util.LicenseFeatureConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class HeatMapSettingFeature extends LicenseFeatureUpgradable {
    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    private CrudService crudService;

    @Override
    public String getFeatureCode() {
        return LicenseFeatureConstants.HEATMAP_SETTINGS;
    }

    @Override
    protected void cleanUp(int propertyId, LicensePackage currentLicensePackage, LicensePackage newLicensePackage, Map<String, Object> featuresInputMapToDowngrade) {
        deleteHeatMapConfigs("SEASONAL");
        deleteHeatMapConfigs("CUSTOMIZE");
    }

    private void deleteHeatMapConfigs(String configType) {
        List<HeatMapRangeAndColorsConfig> heatMapRangeAndColorsConfigs = crudService.findByNamedQuery(HeatMapRangeAndColorsConfig.GET_BY_CONFIG_TYPE, QueryParameter.with("configType", configType).parameters());
        heatMapRangeAndColorsConfigs.forEach(config -> {
            crudService.executeUpdateByNamedQuery(HeatMapRangeAndColors.DELETE_BY_HEAT_MAP_CONFIG_ID,
                    QueryParameter.with("heatMapRangeAndColorsConfigId", config.getId()).parameters());
            crudService.executeUpdateByNamedQuery(HeatMapRangeAndColorsConfig.DELETE_BY_ID,
                    QueryParameter.with("id", config.getId()).parameters());
        });
    }
}
