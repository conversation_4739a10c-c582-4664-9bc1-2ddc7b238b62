package com.ideas.tetris.pacman.services.runtask;

import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.systemconfig.SASSettings;
import com.ideas.tetris.platform.services.compress.CompressUncompressService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static com.ideas.tetris.platform.services.compress.CompressionType.ZIP;

@Component
@Transactional
public class RunTaskOutputService {

    @Autowired
    @Qualifier("compressUncompressService")
    private CompressUncompressService compressService;


    public File getReport(String task, String timestamp) {

        File directory = new File(SASSettings.getRunTaskOutputPath() + File.separator + task + File.separator + timestamp + File.separator
                + task);

        if (!directory.exists()) {
            throw new TetrisException("directory does not exist!");
        }
        return compressService.compress(ZIP, getChildrenFiles(directory), task + ".zip");
    }

    private List<String> getChildrenFiles(File directory) {
        List<String> fileNames = new ArrayList<String>();
        for (File file : directory.listFiles()) {
            fileNames.add(file.getAbsolutePath());
        }

        if (CollectionUtils.isEmpty(fileNames)) {
            throw new TetrisException("no files to process");
        }
        return fileNames;
    }

    public String getOutputPath(String extension) {
        String path = getPath(extension);

        // creates directory if it is not there
        File datasetDirectory = new File(path);
        if (!datasetDirectory.exists()) {
            datasetDirectory.mkdirs();
        }

        return path;
    }

    public String getPath(String extension) {
        String path = SASSettings.getRunTaskOutputPath() + "/" + extension;
        return path;
    }

    public String getRunTaskOutputPath(String path) {
        File datasetDirectory = new File(path);
        if (!datasetDirectory.exists()) {
            datasetDirectory.mkdirs();
        }
        return path;
    }

}
