package com.ideas.tetris.pacman.services.reports.dto;

import com.ideas.tetris.pacman.services.reports.ReportType;

import java.io.Serializable;

public class ReportParamsDTO implements Serializable {

    private int propertyId;
    private ReportType reportType;
    private Boolean barByLOS = false;//property type: bar by LOS or bar by day
    private String exportType;

    public ReportParamsDTO() {
    }

    @Override
    public String toString() {
        return "";
    }

    public int getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(int propertyId) {
        this.propertyId = propertyId;
    }

    public ReportType getReportType() {
        return reportType;
    }

    public void setReportType(ReportType reportType) {
        this.reportType = reportType;
    }

    public Boolean getBarByLOS() {
        return barByLOS;
    }

    public void setBarByLOS(Boolean barByLOS) {
        this.barByLOS = barByLOS;
    }

    public String getExportType() {
        return exportType;
    }

    public void setExportType(String exportType) {
        this.exportType = exportType;
    }
}
