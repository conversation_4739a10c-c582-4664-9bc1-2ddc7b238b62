package com.ideas.tetris.pacman.services.datafeed.dto.rateshopping;

public class RateShoppingSchedule {

    private Integer scheduleCompetitorForTheNextXDays;
    private Integer scheduleDataArrivesEveryXDays;
    private Integer scheduleStopUsingDataAfterXDays;

    public RateShoppingSchedule() {
    }

    public Integer getScheduleCompetitorForTheNextXDays() {
        return scheduleCompetitorForTheNextXDays;
    }

    public void setScheduleCompetitorForTheNextXDays(Integer scheduleCompetitorForTheNextXDays) {
        this.scheduleCompetitorForTheNextXDays = scheduleCompetitorForTheNextXDays;
    }

    public Integer getScheduleDataArrivesEveryXDays() {
        return scheduleDataArrivesEveryXDays;
    }

    public void setScheduleDataArrivesEveryXDays(Integer scheduleDataArrivesEveryXDays) {
        this.scheduleDataArrivesEveryXDays = scheduleDataArrivesEveryXDays;
    }

    public Integer getScheduleStopUsingDataAfterXDays() {
        return scheduleStopUsingDataAfterXDays;
    }

    public void setScheduleStopUsingDataAfterXDays(Integer scheduleStopUsingDataAfterXDays) {
        this.scheduleStopUsingDataAfterXDays = scheduleStopUsingDataAfterXDays;
    }
}
