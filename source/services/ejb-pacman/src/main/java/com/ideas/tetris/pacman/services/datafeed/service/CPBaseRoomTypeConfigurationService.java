package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.datafeed.dto.CPBaseRoomTypeConfiguration;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class CPBaseRoomTypeConfigurationService {

    @Autowired
    PricingConfigurationService pricingConfigurationService;

    public List<CPBaseRoomTypeConfiguration> getCPBaseRoomTypeConfiguration() {
        List<CPBaseRoomTypeConfiguration> cpBaseRoomTypeConfigurationDTOS = new ArrayList<>();
        List<PricingAccomClass> pricingAccomClasses = pricingConfigurationService.getPricingAccomClasses();
        for (PricingAccomClass pricingAccomClass : pricingAccomClasses) {
            if (pricingAccomClass.getMinimumIncrementMethod() != null) {
                cpBaseRoomTypeConfigurationDTOS.add(new CPBaseRoomTypeConfiguration(pricingAccomClass.getAccomClass().getCode(), pricingAccomClass.getAccomType().getAccomTypeCode(),
                        pricingAccomClass.isPriceExcluded(), "FIXED_OFFSET".equalsIgnoreCase(pricingAccomClass.getMinimumIncrementMethod().toString()) ? 'F' : 'P', pricingAccomClass.getMinimumIncrementValue()));
            } else {
                cpBaseRoomTypeConfigurationDTOS.add(new CPBaseRoomTypeConfiguration(pricingAccomClass.getAccomClass().getCode(), pricingAccomClass.getAccomType().getAccomTypeCode(),
                        pricingAccomClass.isPriceExcluded(), ' ', pricingAccomClass.getMinimumIncrementValue()));
            }
        }
        return cpBaseRoomTypeConfigurationDTOS;
    }
}
