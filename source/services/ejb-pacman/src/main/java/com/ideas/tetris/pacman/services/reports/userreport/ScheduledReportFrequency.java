package com.ideas.tetris.pacman.services.reports.userreport;

import com.ideas.tetris.platform.common.errorhandling.TetrisException;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

public enum ScheduledReportFrequency {
    DAYS("Days") {
        @Override
        public boolean isScheduledReportApplicable(LocalDate scheduleStartDate, LocalDate now, int frequency) {
            int diffDays = Long.valueOf(ChronoUnit.DAYS.between(scheduleStartDate, now)).intValue();
            return (diffDays % frequency) == 0;
        }
    },
    WEEKS("Weeks") {
        @Override
        public boolean isScheduledReportApplicable(LocalDate scheduleStartDate, LocalDate now, int frequency) {
            int diffDays = Long.valueOf(ChronoUnit.DAYS.between(scheduleStartDate, now)).intValue();
            return (diffDays % (frequency * 7)) == 0;
        }
    };
    String unit;

    ScheduledReportFrequency(String unit) {
        this.unit = unit;
    }

    public static ScheduledReportFrequency forUnitName(String unit) {
        for (ScheduledReportFrequency frequency : ScheduledReportFrequency.values()) {
            if (frequency.unit.equalsIgnoreCase(unit)) {
                return frequency;
            }
        }
        throw new TetrisException("Invalid unit for report frequency");
    }

    public abstract boolean isScheduledReportApplicable(LocalDate scheduleStartDate, LocalDate now, int frequency);
}
