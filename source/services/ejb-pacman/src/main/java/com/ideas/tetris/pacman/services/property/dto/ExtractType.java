package com.ideas.tetris.pacman.services.property.dto;

/*
 * Types of extracts care team members request
 * Would love more detailed names so intimate domain knowledge not required
 */
public enum ExtractType {
	SAS_ANALYTIC_DATA_SET(1, "analytics", "Analytics Data Sets"),
	SAS_RATCHET_DATA_SET(2, "ratchet", "Ratchet Data Sets"),
	CRS_CUSTOMER_DATA(4, "crs", "CRS files"),
	RSS_WEB_RATE(8, "rss", "RSS Extracts"),
	T2SNAP_POST_ETL(16, "t2snap", "T2snaps"),
	DB_TENANT(32, "tenantdb", "Tenant database"),
	LIMITED_DB_TENANT(64, "limitedTenantdb", "Limited Tenant database"),
	LIMITED_DB_TENANT_EXCLUDE_OPERA(128, "limitedTenantdb", "Limited Tenant database without opera data"),
	LIMITED_DB_TENANT_EXCLUDE_PACE(256, "limitedTenantdb", "Limited Tenant database without pace data"),
	LIMITED_DB_TENANT_EXCLUDE_REVENUE_STREAM(512, "limitedTenantdb", "Limited Tenant database without revenue stream data"),
	GLOBAL_CONFIG_PARAMS(1024, "globalConfigParameters", "Global Config Parameters"),
	SAS_ANALYTIC_DATA_SET_EXCLUDE_PACE_DATA(2048, "analytics", "Analytics Data Sets Excluding Pace Data"),
	SAS_DATASETS_COMBINED(4096, "sasDatasets", "Analytics Data Sets Excluding Pace Data");

    private int value;
    private String folderName;
    private String description;

    private ExtractType(int value, String folderName, String description) {
        this.value = value;
        this.folderName = folderName;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getFolderName() {
        return folderName;
    }

    public String getDescription() {
        return description;
    }
}
