package com.ideas.tetris.pacman.services.dailybar;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.configsparam.service.ConfigParameterNameService;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dailybar.constants.AgileRatesConstants;
import com.ideas.tetris.pacman.services.dailybar.entity.DailyBarDecisions;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decisiontranslator.DecisionUtils;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PacmanAgileRatesDecisionService {

    private static final Logger LOGGER = Logger.getLogger(PacmanAgileRatesDecisionService.class.getName());
    private static final String DATES = "dates";
    private static final String TAX_FACTOR = "taxFactor";
    private static final String MISC_ADJUSTMENT = "miscAdjustment";
    private static final String ROUNDING_DEPTH = "roundingDepth";
    private static final String DECISION_START_DATE = "decisionStartDate";
    private static final String DECISION_END_DATE = "decisionEndDate";


    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService crudService;
    @Autowired
    DateService dateService;
    @Autowired
    ConfigParameterNameService configParameterNameService;


    public List<DailyBarDecisions> getAgileRatesDecision(
            boolean shouldApplyTax,
            Date lastUploadedDate,
            String externalSystem,
            Date uploadWindowStartDate,
            Date uploadWindowEndDate) {

        double taxFactor = deriveTaxFactor(shouldApplyTax, externalSystem);
        double miscAdjustmentValue = deriveMiscAdjustment(externalSystem);
        int precision = derivePrecisionForRoundOff(externalSystem);
        List<Object[]> decisionsFromDB = Objects.isNull(lastUploadedDate) ?
                getFullDecisions(taxFactor, miscAdjustmentValue, precision, uploadWindowStartDate, uploadWindowEndDate) :
                getDifferentialDecisions(lastUploadedDate, taxFactor, miscAdjustmentValue, precision, uploadWindowStartDate, uploadWindowEndDate);
        return decisionsFromDB.stream().map(row -> this.getDailyBarDecisions(row, precision == 0)).collect(Collectors.toList());
    }

    private List<Object[]> getDifferentialDecisions(Date lastUploadedDate, double taxFactor, double miscAdjustmentValue, int precision, Date uploadWindowStartDate, Date uploadWindowEndDate) {
        LOGGER.info("Fetching DIFFERENTIAL AgileRates Decisions.");
        return crudService.findByNativeQuery(AgileRatesConstants.DIFFERENTIAL_DECISIONS_FOR_AGILE_RATES,
                QueryParameter.with(TAX_FACTOR, taxFactor)
                        .and(DECISION_START_DATE, Optional.ofNullable(uploadWindowStartDate).orElse(dateService.getOptimizationWindowStartDate()))
                        .and(DECISION_END_DATE, Optional.ofNullable(uploadWindowEndDate).orElse(dateService.getDecisionUploadWindowEndDate()))
                        .and("lastUploadedDate", lastUploadedDate)
                        .and(MISC_ADJUSTMENT, miscAdjustmentValue)
                        .and(ROUNDING_DEPTH, precision)
                        .parameters());
    }

    private List<Object[]> getFullDecisions(double taxFactor, double miscAdjustmentValue, int precision, Date uploadWindowStartDate, Date uploadWindowEndDate) {
        LOGGER.info("Fetching FULL AgileRates Decisions.");
        String fullDecisionsForAgileRates = AgileRatesConstants.FULL_DECISIONS_FOR_AGILE_RATES;
        Date startDate = Optional.ofNullable(uploadWindowStartDate).orElse(dateService.getOptimizationWindowStartDate());
        Date endDate = Optional.ofNullable(uploadWindowEndDate).orElse(dateService.getDecisionUploadWindowEndDate());
        return crudService.findByNativeQuery(fullDecisionsForAgileRates,
                QueryParameter.with(TAX_FACTOR, taxFactor)
                        .and(DECISION_START_DATE, startDate)
                        .and(DECISION_END_DATE, endDate)
                        .and(MISC_ADJUSTMENT, miscAdjustmentValue)
                        .and(ROUNDING_DEPTH, precision)
                        .parameters());
    }

    public List<DailyBarDecisions> getAllAgileRatesDecision(final boolean shouldApplyTax,
                                                            final String externalSystem,
                                                            final List<Date> dates) {

        final double miscAdjustment = deriveMiscAdjustment(externalSystem);
        final int precision = derivePrecisionForRoundOff(externalSystem);
        final double taxFactor = deriveTaxFactor(shouldApplyTax, externalSystem);

        final List<Object[]> decisions = crudService.findByNativeQuery(
                AgileRatesConstants.getAllDecisionAgileRatesOutputFullForAllRoomClassesUpToDecimalForDates(precision),
                QueryParameter
                        .with(DATES, dates)
                        .and(TAX_FACTOR, taxFactor)
                        .and(MISC_ADJUSTMENT, miscAdjustment)
                        .parameters());

        return decisions
                .stream()
                .map(row -> getDailyBarDecisions(row, precision == 0))
                .collect(Collectors.toList());
    }

    private double deriveTaxFactor(boolean shouldApplyTax, String externalSystem) {
        LOGGER.info("Apply Tax on Daily Bar Decisions is : " + shouldApplyTax);
        double taxFactor = 0d;
        String applyTaxValueParameterName = configParameterNameService.getIntegrationParameterName(externalSystem, Constants.TAX_ADJUSTMENT_VALUE);
        if (shouldApplyTax) {
            String taxValueStr = getParameterValue(applyTaxValueParameterName);
            taxFactor = DecisionUtils.calculateTaxFactorForDecisions(taxValueStr);
        }
        return taxFactor;
    }

    private double deriveMiscAdjustment(String externalSystem) {
        double miscAdjustment = 0d;
        String miscAdjustmentValueParameterName = configParameterNameService.getIntegrationParameterName(externalSystem, Constants.MISC_ADJUSTMENT_VALUE);
        if (isExternalSystemEligibleForMiscAdjustment(externalSystem)) {
            miscAdjustment = Double.parseDouble(getParameterValue(miscAdjustmentValueParameterName));
        }
        return miscAdjustment;
    }

    private String getParameterValue(String parameterName) {
        return pacmanConfigParamsService.getParameterValue(parameterName);
    }

    private int derivePrecisionForRoundOff(String externalSystem) {
        String roundingOffParameterName = configParameterNameService.getIntegrationParameterName(externalSystem, Constants.ROUNDING_OFF);
        boolean applyRoundOFF = Boolean.parseBoolean(getParameterValue(roundingOffParameterName));
        return applyRoundOFF ? 0 : 2;
    }

    private boolean isExternalSystemEligibleForMiscAdjustment(String externalSystem) {
        return externalSystem.equalsIgnoreCase(Constants.OPERA) || externalSystem.equalsIgnoreCase(Constants.REZVIEW) || externalSystem.equalsIgnoreCase(Constants.ORS)
                || externalSystem.equalsIgnoreCase(Constants.MYFIDELIO);
    }

    private DailyBarDecisions getDailyBarDecisions(Object[] row, boolean applyRoundOFF) {
        DailyBarDecisions dailyBarDecisions = new DailyBarDecisions();
        dailyBarDecisions.setOccupancyDate((Date) row[0]);
        dailyBarDecisions.setRoomType((String) row[1]);
        dailyBarDecisions.setRatePlan((String) row[2]);
        dailyBarDecisions.setSingleRate(getValue(row[3], applyRoundOFF));
        dailyBarDecisions.setDoubleRate(getValue(row[4], applyRoundOFF));
        dailyBarDecisions.setTripleRate(getValue(row[5], applyRoundOFF));
        dailyBarDecisions.setQuadRate(getValue(row[6], applyRoundOFF));
        dailyBarDecisions.setQuintRate(getValue(row[7], applyRoundOFF));
        dailyBarDecisions.setAdultRate(getValue(row[8], applyRoundOFF));
        dailyBarDecisions.setChildRate(getValue(row[9], applyRoundOFF));
        dailyBarDecisions.setOneChildRate(getValue(row[10], applyRoundOFF));
        dailyBarDecisions.setTwoChildRate(getValue(row[11], applyRoundOFF));
        dailyBarDecisions.setThreeChildRate(getValue(row[12], applyRoundOFF));
        dailyBarDecisions.setFourChildRate(getValue(row[13], applyRoundOFF));
        dailyBarDecisions.setFiveChildRate(getValue(row[14], applyRoundOFF));
        dailyBarDecisions.setChildAgeOneRate(getValue(row[15], applyRoundOFF));
        dailyBarDecisions.setChildAgeTwoRate(getValue(row[16], applyRoundOFF));
        dailyBarDecisions.setChildAgeThreeRate(getValue(row[17], applyRoundOFF));
        dailyBarDecisions.setChildAgeFourRate(getValue(row[18], applyRoundOFF));
        return dailyBarDecisions;
    }

    private BigDecimal getValue(Object source, boolean applyRoundOFF) {
        return applyRoundOFF ? getBigDecimalWithRoundOff(source) : getBigDecimal(source);
    }

    private BigDecimal getBigDecimalWithRoundOff(Object o) {
        return null == o || o instanceof BigDecimal ? (BigDecimal) o : BigDecimal.valueOf((Double) o).setScale(0);
    }

    private BigDecimal getBigDecimal(Object o) {
        return null == o || o instanceof BigDecimal ? (BigDecimal) o : BigDecimal.valueOf((Double) o);
    }

    @VisibleForTesting
	public
    void setConfigParameterNameService(ConfigParameterNameService configParameterNameService) {
        this.configParameterNameService = configParameterNameService;
    }
}
