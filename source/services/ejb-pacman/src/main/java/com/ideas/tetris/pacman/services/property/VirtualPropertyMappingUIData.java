package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.platform.services.daoandentities.entity.VirtualPropertyMapping;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;

@Getter
@Setter
public class VirtualPropertyMappingUIData {

    public static final String BRAND_CODE = "BRAND CODE";
    public static final String GLOBAL_AREA = "GLOBAL AREA";
    private Map<String, VirtualPropertyMapping> availableMappings;
    private List<String> externalSystemValues;
    private List<String> brandCodes;
    private List<String> globalAreas;
    private List<String> recoveryStates;

    public VirtualPropertyMappingUIData() {
        this(emptyMap(), emptyList(), emptyList(), emptyList());
    }

    public VirtualPropertyMappingUIData(Map<String, VirtualPropertyMapping> availableMappings,
                                        List<String> brandCodes, List<String> globalAreas, List<String> recoveryStates) {
        this.availableMappings = availableMappings;
        this.externalSystemValues = Arrays.asList("Hilstar", "PCRS");
        if (brandCodes == null) {
            brandCodes = emptyList();
        }
        if (globalAreas == null) {
            globalAreas = emptyList();
        }
        this.brandCodes = brandCodes;
        this.globalAreas = globalAreas;
        this.recoveryStates = recoveryStates;
    }

    public List<String> getRecoveryStates() {
        return recoveryStates;
    }

    public Map<String, VirtualPropertyMapping> getAvailableMappings() {
        return availableMappings;
    }

    public List<String> getExternalSystemValues() {
        return externalSystemValues;
    }

    public List<String> getBrandCodes() {
        return brandCodes;
    }

    public List<String> getGlobalAreas() {
        return globalAreas;
    }
}
