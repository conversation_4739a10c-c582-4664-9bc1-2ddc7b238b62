package com.ideas.tetris.pacman.services.property.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import org.apache.commons.collections.CollectionUtils;

import java.io.File;
import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class AbstractExtractDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    // We have normal crs extracts (e.g. DALMC.20110225.0000.tar.Z) &
    // historical crs extract (DALMC.20110224.0000.history.tar.Z)
    public static final int FILE_NAME_SEGMENTS_STANDARD = 5;
    public static final int FILE_NAME_SEGMENTS_HISTORY = 6;
    public static final String INCOMING = "incoming";
    public static final String ARCHIVED = "archived";
    protected Map<Date, List<String>> incomingExtracts = new HashMap<Date, List<String>>();
    protected Map<Date, List<String>> archivedExtracts = new HashMap<Date, List<String>>();
    protected int numberOfIncomingExtracts = 0;
    protected int numberOfArchivedExtracts = 0;
    protected DateParameter firstIncomingExtractDate;
    protected DateParameter lastIncomingExtractDate;
    protected transient SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

    protected List<String> getConsolidatedPaths(Map<Date, List<String>> cachedMap) {
        List<String> extractPaths = new ArrayList<>();
        if (!cachedMap.isEmpty()) {
            List<Date> sortedDates = new ArrayList<Date>(cachedMap.keySet());
            Collections.sort(sortedDates);
            for (Date date : sortedDates) {
                extractPaths.addAll(cachedMap.get(date));
            }
        }
        return extractPaths;
    }

    public void cleanExtractMaps() {
        this.incomingExtracts = new HashMap<>();
        this.archivedExtracts = new HashMap<>();
    }

    public void addExtract(File file, String type) {
        if (type.equals(INCOMING)) {
            addIncomingExtract(file);
        } else if (type.equals(ARCHIVED)) {
            addArchivedExtract(file);
        }
    }

    public void addIncomingExtract(File file) {
        Date date = deriveDate(file.getName());
        if (date != null) {
            List<String> paths = incomingExtracts.get(date);
            if (paths == null) {
                paths = new ArrayList<String>();
            }
            paths.add(file.getAbsolutePath());
            Collections.sort(paths);
            incomingExtracts.put(date, paths);
        }
    }

    public void addArchivedExtract(File file) {
        Date date = deriveDate(file.getName());
        if (date != null) {
            List<String> paths = archivedExtracts.get(date);
            if (paths == null) {
                paths = new ArrayList<String>();
            }
            paths.add(file.getAbsolutePath());
            Collections.sort(paths);
            archivedExtracts.put(date, paths);
        }
    }

    public List<File> getIncomingExtracts() {
        List<File> files = new ArrayList<File>();
        for (String path : getIncomingPaths()) {
            files.add(new File(path));
        }
        return files;
    }

    @JsonIgnore
    public List<String> getIncomingPaths() {
        return getConsolidatedPaths(incomingExtracts);
    }

    public void setIncomingExtracts(Map<Date, List<String>> incomingExtracts) {  // do not call
        this.incomingExtracts = incomingExtracts;
    }

    public List<File> getArchivedExtracts() {
        List<File> files = new ArrayList<File>();
        for (String path : getArchivedPaths()) {
            files.add(new File(path));
        }
        return files;
    }

    @JsonIgnore
    public List<String> getArchivedPaths() {
        return getConsolidatedPaths(archivedExtracts);
    }

    protected Date deriveDate(String fileName) {
        String[] segments = fileName.split("[.]");
        if (segments.length < FILE_NAME_SEGMENTS_STANDARD) {
            return null;
        }
        try {
            return getDateFormat().parse(segments[1]);
        } catch (ParseException e) {
            return null;
        }
    }

    protected SimpleDateFormat getDateFormat() {
        if (dateFormat == null) {
            dateFormat = new SimpleDateFormat("yyyyMMdd");
        }
        return dateFormat;
    }

    public void moveIncomingExtractToArchive(File file) {
        moveFile(file);
        clearHelperVariables();

        // Need to call postConstruct to update dates and counts
        postConstruct();
    }

    protected void postConstruct() {
        //Nothing to process in this postConstruct, but the children may have some post processing

    }

    protected void clearHelperVariables() {
        numberOfIncomingExtracts = 0;
        numberOfArchivedExtracts = 0;
    }

    void moveFile(File file) {
        String filePath = file.getAbsolutePath();

        // Remove from the incoming extracts map
        Date date = deriveDate(file.getName());
        List<String> paths = incomingExtracts.get(date);
        if (CollectionUtils.isNotEmpty(paths)) {
            Iterator<String> datePathIter = paths.iterator();
            while (datePathIter.hasNext()) {
                String path = datePathIter.next();
                if (filePath.equals(path)) {
                    datePathIter.remove();
                }
            }

            // If there are no more paths for a particular date, remove the date
            if (paths.isEmpty()) {
                incomingExtracts.remove(date);
            }
        }

        // Add the file to the archived extracts
        addArchivedExtract(file);
    }

    public DateParameter getFirstIncomingExtractDate() {
        return firstIncomingExtractDate;
    }

    public void setFirstIncomingExtractDate(DateParameter firstIncomingExtractDate) {  // do not call
        this.firstIncomingExtractDate = firstIncomingExtractDate;
    }

    public DateParameter getLastIncomingExtractDate() {
        return lastIncomingExtractDate;
    }

    public void setLastIncomingExtractDate(DateParameter lastIncomingExtractDate) {  // do not call
        this.lastIncomingExtractDate = lastIncomingExtractDate;
    }

    public int getNumberOfIncomingExtracts() {
        return numberOfIncomingExtracts;
    }

    public int getNumberOfArchivedExtracts() {
        return numberOfArchivedExtracts;
    }

    public void setNumberOfIncomingExtracts(int numberOfIncomingExtracts) {
        this.numberOfIncomingExtracts = numberOfIncomingExtracts;
    }

    public void setNumberOfArchivedExtracts(int numberOfArchivedExtracts) {
        this.numberOfArchivedExtracts = numberOfArchivedExtracts;
    }
}
