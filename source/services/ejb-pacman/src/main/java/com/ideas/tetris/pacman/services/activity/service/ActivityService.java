package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.activity.converter.ActivityConverter;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ActivityEntity;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.rest.SimpleObjectRestWrapper;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * The ActivityService provides the base CRUD functionality for Activity data.
 * It will always use a converter to convert from a REST-specific Map DTO to
 * Entities.
 */

@Transactional
@Component
public abstract class ActivityService<E extends ActivityEntity> {
    private static final Logger LOGGER = Logger.getLogger(ActivityService.class);
    static final String START_DATE = "startDate";
    static final String PROPERTY_ID = "propertyId";
    static final String END_DATE = "endDate";
    static final String YYYY_MM_DD = "yyyy-MM-dd";
    static final String FILE_META_DATA_ID_CAPTIALIZED_D = "fileMetaDataId";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    /**
     * Returns all ActivityEntities
     */

    public List<Map<String, Object>> get(Pageable pageable) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Getting all " + getEntityClass() + " records");
        }
        return getConverter().convertFromEntities(tenantCrudService.findAllWithLimit(getEntityClass(), pageable.getStartPosition(), pageable.getSize()));
    }


    public List<Map<String, Object>> getByPage(Pageable pageable) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Getting by Query " + getDateRangeQuery());
        }
        return getConverter().convertFromEntities(tenantCrudService.findByNamedQuery(getDateRangeQuery(),
                QueryParameter.with(START_DATE, pageable.getStartDate())
                        .and(END_DATE, pageable.getEndDate())
                        .and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters(),
                pageable.getStartPosition(), pageable.getSize()
        ));
    }


    public List<Map<String, Object>> getByPageSorted(Pageable pageable) {
        List<Map<String, Object>> linkedHashMaps = getByPage(pageable);
        linkedHashMaps.sort(getComparator());
        return linkedHashMaps;
    }

    protected abstract Comparator<Map<String, Object>> getComparator();


    /**
     * Returns a specific ActivityEntity by it's ID
     */

    public Map<String, Object> get(Integer id) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Getting " + getEntityClass() + " record with ID: " + id);
        }
        return getConverter().convertFromEntity(tenantCrudService.find(getEntityClass(), id));
    }

    /**
     * Creates or updates ActivityEntities for the List of DTOs provided.
     */

    public List<Map<String, Object>> save(List<Map<String, Object>> dtos, String correlationId, boolean isPast) {
        return getConverter().convertFromEntities(saveEntities(dtos, correlationId, isPast));
    }

    /**
     * Creates or updates an ActivityEntity for the given ID.
     */

    public Map<String, Object> save(Integer id, Map<String, Object> dto, String correlationId, boolean isPast) {
        E entity = saveEntity(id, dto, correlationId, isPast);

        // Return the converted entity
        return getConverter().convertFromEntity(entity);
    }

    public E saveEntity(Integer id, Map<String, Object> dto, String correlationId, boolean isPast) {
        LOGGER.info("Saving " + dto + " as Entity: " + getEntityClass() + " with ID: " + id);
        // Convert the Entity and set the ID that was passed in

        E entity = getConverter().convertFromMap(dto, correlationId, isPast);
        entity.setId(id);

        // Save the entity
        entity = tenantCrudService.save(entity);
        return entity;
    }

    /**
     * Deletes all ActivityEntities for the EntityClass
     */

    public SimpleObjectRestWrapper<Integer> delete() {
        LOGGER.info("Deleting all " + getEntityClass());
        return new SimpleObjectRestWrapper<>(tenantCrudService.deleteAll(getEntityClass()));
    }

    /**
     * Deletes a specific ActivityEntity by the ID.
     */

    public SimpleObjectRestWrapper<Integer> delete(Integer id) {
        LOGGER.info("Deleting " + getEntityClass() + " with ID: " + id);
        boolean retVal = tenantCrudService.delete(getEntityClass(), id);
        if (retVal) {
            return new SimpleObjectRestWrapper<>(1);
        }

        return new SimpleObjectRestWrapper<>(0);
    }

    @SuppressWarnings("unchecked")

    public List<Map<String, Object>> get(Date startDate, Date endDate) {
        return getConverter().convertFromEntities(tenantCrudService.findByNamedQuery(getDateRangeQuery(), QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(START_DATE, startDate).and(END_DATE, endDate).parameters()));
    }


    public SimpleObjectRestWrapper<Integer> delete(Date startDate, Date endDate) {
        return new SimpleObjectRestWrapper<>(tenantCrudService.executeUpdateByNamedQuery(deleteDateRangeQuery(), QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(START_DATE, startDate).and(END_DATE, endDate).parameters()));
    }

    protected List<E> saveEntities(List<Map<String, Object>> dtos, String correlationId, boolean isPast) {
        List<E> entities = new ArrayList<>();


        int count = dtos.size();
        LOGGER.info("Starting save of entities: " + count);
        for (Map<String, Object> dto : dtos) {
            convertDtoToEntity(correlationId, isPast, entities, dto);
        }


        tenantCrudService.save(entities);
        LOGGER.info("Completed save of entities: " + count);

        return entities;
    }

    private void convertDtoToEntity(String correlationId, boolean isPast, List<E> entities, Map<String, Object> dto) {
        E entity = getConverter().convertFromMap(dto, correlationId, isPast);
        if (entity != null) {
            entities.add(entity);
        }
    }

    /**
     * TODO: I believe this method may be able to be removed if I can determine
     * the class based on the generic defined on the class. However, the normal
     * way to do this doesn't appear to work in the jboss container.
     */
    protected abstract Class<E> getEntityClass();

    /**
     * Returns the convert to be used by the ActivityService. This provides
     * basic argument copy functionality for DTOs and their corresponding
     * Entities.
     */
    protected abstract ActivityConverter<E> getConverter();

    protected abstract String getDateRangeQuery();

    protected abstract String deleteDateRangeQuery();

}
