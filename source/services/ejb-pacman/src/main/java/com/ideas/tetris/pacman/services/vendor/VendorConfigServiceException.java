package com.ideas.tetris.pacman.services.vendor;

import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;

public class VendorConfigServiceException extends TetrisException {
    public VendorConfigServiceException(String message) {
        super(ErrorCode.VENDOR_CONFIG_ERROR, message);
    }

    public VendorConfigServiceException(String message, Throwable cause) {
        super(ErrorCode.VENDOR_CONFIG_ERROR, message, cause);
    }
}
