package com.ideas.tetris.pacman.services.property.dto;

import org.apache.log4j.Logger;

import java.io.File;
import java.io.FileFilter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.ideas.tetris.pacman.services.property.dto.PacmanExtractDetails.FILE_DATE_FORMAT;

public class WebRateExtractFileFilter implements FileFilter {
    private static final Logger LOGGER = Logger.getLogger(WebRateExtractFileFilter.class.getName());

    private Date startDate;
    private Date endDate;

    public WebRateExtractFileFilter() {
    }

    public WebRateExtractFileFilter(Date startDate, Date endDate) {
        this.startDate = new Date(startDate.getTime());
        this.endDate = new Date(endDate.getTime());
    }

    @Override
    public boolean accept(File candidate) {
        // e.g. 1507-7711_20111101_2327_RSS.zip
        boolean shouldAccept = false;
        if (!candidate.isDirectory()) {
            String[] segments = candidate.getName().split("[_.]");
            if (segments.length == WebRateExtractDetails.FILE_NAME_SEGMENTS_STANDARD) {
                shouldAccept = WebRateExtractDetails.FILE_PART_WEBRATE.equals(segments[WebRateExtractDetails.FILE_NAME_SEGMENTS_STANDARD - 2]) &&
                        WebRateExtractDetails.FILE_PART_ZIP.equals(segments[WebRateExtractDetails.FILE_NAME_SEGMENTS_STANDARD - 1]);
            } else if (segments.length == WebRateExtractDetails.FILE_NAME_SEGMENTS_HISTORY) {
                shouldAccept = WebRateExtractDetails.FILE_PART_WEBRATE.equals(segments[WebRateExtractDetails.FILE_NAME_SEGMENTS_HISTORY - 3]) &&
                        WebRateExtractDetails.FILE_PART_ZIP.equals(segments[WebRateExtractDetails.FILE_NAME_SEGMENTS_HISTORY - 1]) &&
                        WebRateExtractDetails.FILE_PART_0.equals(segments[WebRateExtractDetails.FILE_NAME_SEGMENTS_HISTORY - 2]);
            }


            if (shouldAccept && null != startDate && null != endDate) {
                shouldAccept = isFileDateWithinRange(candidate, segments);
            }
        }
        return shouldAccept;
    }

    private boolean isFileDateWithinRange(File candidate, String[] dateParts) {
        boolean isWithinRange = true;
        Date date = null;
        try {
            date = new SimpleDateFormat(FILE_DATE_FORMAT).parse(dateParts[1]);
            if (date.before(startDate) || date.after(endDate)) {
                isWithinRange = false;
            }
        } catch (ParseException e) {
            isWithinRange = false;
            LOGGER.info("Could not parse date from web rate extract file: " + candidate.getName(), e);
        }
        return isWithinRange;
    }
}
