package com.ideas.tetris.pacman.services.security;

import org.apache.log4j.Logger;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

public class LogoutServlet extends HttpServlet {

    private static Logger logger = Logger.getLogger(LogoutServlet.class);
    public static final String SIGNOUT_IDENTIFIER = "signout";
    public static final String USER_PRINCIPAL = "userPrincipal";

    /*
    Called by Grails signout link to invalidate services session.  Note, this 
    does not invalidate the sso or grails sessions. This in turn will trigger
    the sessionDestroyed method in the SessionListener class.
     */

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        HttpSession session = req.getSession(false);
        if (session != null) {
            if (logger.isDebugEnabled()) {
                logger.debug("Invalidating session:" + session.getId());
            }

            session.setAttribute(SIGNOUT_IDENTIFIER, true);//set this on the session, so our SessionListener destroyed listener can tell if the session was destroyed due to a timeout or user sign-out
            session.invalidate();
        }
    }


}
