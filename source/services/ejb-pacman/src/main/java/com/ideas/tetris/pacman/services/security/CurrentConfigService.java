package com.ideas.tetris.pacman.services.security;

import com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig;
import org.apache.log4j.Logger;

import javax.ws.rs.GET;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Transactional
public class CurrentConfigService {

    private static Logger log = Logger.getLogger(CurrentConfigService.class);


    public String getAllSettings() {
        log.info("Great Success");

        String fullConfig = SystemConfig.getFullConfig(true);
        return fullConfig;
    }
}
