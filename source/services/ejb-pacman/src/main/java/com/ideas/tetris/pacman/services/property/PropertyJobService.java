package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.services.job.entity.ExecutionStatus;
import com.ideas.tetris.pacman.services.problem.JobCrudServiceBean;
import com.ideas.tetris.pacman.services.propertymigration.dto.MovePropertyDTO;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import javax.inject.Inject;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PropertyJobService {
    @JobCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("jobCrudServiceBean")
	private CrudService jobCrudService;

    public void setJobCrudService(CrudService jobCrudService) {
        this.jobCrudService = jobCrudService;
    }

    public void updateJobDB(MovePropertyDTO movePropertyDTO) {
        jobCrudService.executeUpdateByNativeQuery("update JOB_INSTANCE_WORK_CONTEXT set CLIENT_ID = "
                + movePropertyDTO.getTargetClient().getId()
                + ", CLIENT_CODE = '"
                + movePropertyDTO.getTargetClient().getCode()
                + "' where PROPERTY_ID = "
                + movePropertyDTO.getProperty().getId()
        );
    }

    public boolean isAnyJobInRunningStateFrom(List<String> jobsThatLockUIWhenRunning) {
        return 0 != (Integer) jobCrudService.findByNativeQuerySingleResult("select COUNT(*) from JOB_STATE where JOB_NAME in (:jobsThatLockUIWhenRunning) and Execution_Status = :executionStatus",
                QueryParameter.with("jobsThatLockUIWhenRunning", jobsThatLockUIWhenRunning).and("executionStatus", ExecutionStatus.RUNNING.name()).parameters());
    }
}
