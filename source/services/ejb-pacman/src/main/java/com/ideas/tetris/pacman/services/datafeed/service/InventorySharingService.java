package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.entity.InventorySharing;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import javax.inject.Inject;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class InventorySharingService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    public List<InventorySharing> getInventorySharingData(DatafeedRequest datafeedRequest) {
        final String inventorySharingNamedQuery = InventorySharing.INVENTORY_SHARING_NEW_ROOM_CLASS_CONFIG_BY_PROPERTY;
        return tenantCrudService.findByNamedQuery(inventorySharingNamedQuery,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters(), datafeedRequest.getStartPosition(), datafeedRequest.getSize());
    }
}

