package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.grouppricing.GroupPricingMinProfitConfigurationDTO;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationMinProfit;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class GroupPricingMinProfitConfigurationService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    private static final String SEASONAL = "Seasonal";
    private static final String DEFAULT = "Default";

    public List<GroupPricingMinProfitConfigurationDTO> getGroupPricingMinProfitConfiguration() {
        List<GroupPricingMinProfitConfigurationDTO> result = new ArrayList<>();
        List<GroupPricingConfigurationMinProfit> groupPricingConfigurationMinProfitList = tenantCrudService.findAll(GroupPricingConfigurationMinProfit.class);

        for (GroupPricingConfigurationMinProfit config : groupPricingConfigurationMinProfitList) {
            GroupPricingMinProfitConfigurationDTO groupPricingMinProfitConfigurationDTO = new GroupPricingMinProfitConfigurationDTO();
            groupPricingMinProfitConfigurationDTO.setDaysToArrival(config.getStartDay() + " - " + (config.getEndDay() == null
                    ? pacmanConfigParamsService.getIntegerParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value()) : config.getEndDay()));
            groupPricingMinProfitConfigurationDTO.setCategory(config.getSeasonStartDate() == null && config.getSeasonEndDate() == null ? DEFAULT : SEASONAL);
            groupPricingMinProfitConfigurationDTO.setMinProfitSunday(config.getSundayPct());
            groupPricingMinProfitConfigurationDTO.setMinProfitMonday(config.getMondayPct());
            groupPricingMinProfitConfigurationDTO.setMinProfitTuesday(config.getTuesdayPct());
            groupPricingMinProfitConfigurationDTO.setMinProfitWednesday(config.getWednesdayPct());
            groupPricingMinProfitConfigurationDTO.setMinProfitThursday(config.getThursdayPct());
            groupPricingMinProfitConfigurationDTO.setMinProfitFriday(config.getFridayPct());
            groupPricingMinProfitConfigurationDTO.setMinProfitSaturday(config.getSaturdayPct());
            groupPricingMinProfitConfigurationDTO.setSeasonName(config.getSeasonName());
            groupPricingMinProfitConfigurationDTO.setSeasonalStartDate(config.getSeasonStartDate() == null ? null : config.getSeasonStartDate().toDate());
            groupPricingMinProfitConfigurationDTO.setSeasonalEndDate(config.getSeasonEndDate() == null ? null : config.getSeasonEndDate().toDate());
            result.add(groupPricingMinProfitConfigurationDTO);
        }
        return result;
    }
}
