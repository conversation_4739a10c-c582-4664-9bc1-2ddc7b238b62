package com.ideas.tetris.pacman.services.upgrade;

import com.google.common.collect.Lists;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.cache.CacheService;
import com.ideas.tetris.pacman.services.database.DBMaintainService;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.JobViewCriteria;
import com.ideas.tetris.pacman.services.job.ScheduleService;
import com.ideas.tetris.pacman.services.job.entity.ExecutionStatus;
import com.ideas.tetris.pacman.services.job.entity.JobView;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.remoteAgent.RemoteAgentConfigService;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.StepGroup;
import com.ideas.tetris.platform.common.job.schedule.Schedule;
import com.ideas.tetris.platform.common.job.schedule.ScheduleStatus;
import com.ideas.tetris.platform.common.regulator.RegulatorConstants;
import com.ideas.tetris.platform.common.remoting.regulator.entities.RegulatorRequest;
import com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.platform.services.daoandentities.entity.DBLoc;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.SASFileLoc;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import com.ideas.tetris.platform.services.regulator.service.RegulatorService;
import com.ideas.tetris.platform.services.regulator.service.spring.RegulatorSpringService;
import com.ideas.tetris.platform.services.sas.log.SASNodeLocator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.BeanParam;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.job.entity.ExecutionStatus.RUNNING;
import static java.lang.String.format;

@Component
@Transactional
public class ApplicationUpgradeService {

    static final String FORCED_STOP = "forced stop";
    public static final String REGULATOR_REQUEST_ID = "regulatorRequestId";

    public enum LockType {
        NONE,
        PROPERTY,
        APPLICATION
    }

    private static final Logger LOGGER = Logger.getLogger(ApplicationUpgradeService.class.getName());
    @Autowired
	private RegulatorService regulatorService;
    @Autowired
    private RegulatorSpringService regulatorSpringService;

    @Autowired
	private ScheduleService scheduleService;

    @Autowired
	private JobMonitorService jobMonitorService;

    @Autowired
	private CacheService cacheService;

    @Autowired
	private RemoteAgentConfigService remoteAgentConfigService;

    @Autowired
	private PropertyService propertyService;

    @Autowired
	private DBMaintainService dbMaintainService;

    @Autowired
	private SASNodeLocator sasNodeLocator;

    static final List<ExecutionStatus> RUNNING_EXECUTION_STATUSES = Arrays.asList(ExecutionStatus.RUNNING, ExecutionStatus.STOPPING, ExecutionStatus.PENDING);

    /**
     * Used to stop all Quartz schedules from firing.
     *
     * @return boolean indicating the schedules were stopped.
     */
    public boolean stopQuartzSchedules() {
        LOGGER.info("Stopping All Quartz Schedules...");
        scheduleService.stopAll();
        LOGGER.info("All Quartz Schedules Stopped");
        return true;
    }
    public boolean shutdownSchedulerOnHost() {
            scheduleService.shutdown(); // Stop scheduler on this host
            LOGGER.info("Scheduler shutdown successfully on host: ");
        return true;
    }
    /**
     * Used to stop all report schedules from firing.
     *
     * @return boolean indicating the schedules were stopped.
     */
    public boolean stopReportSchedules() {
        return true;
    }


    public boolean stopReportSchedulesOriginalCall() {
        return true;
    }


    public boolean stopAllRemoteAgents() {
        LOGGER.info("Stopping All Remote Agents...");
        remoteAgentConfigService.suspendAllRemoteAgents();
        LOGGER.info("All Remote Agents Stopped");
        return true;
    }

    /**
     * Used to stop jobs after their current step
     *
     * @return boolean indicating the jobs were stopped.
     */
    public boolean stopAllJobsAfterCurrentStep(@BeanParam ApplicationActionCriteria actionCriteria) {
        LOGGER.info("Stopping All Jobs after their current step...");

        // Find all RUNNING Jobs that aren't waiting on the regulator and stop them
        JobViewCriteria criteria = new JobViewCriteria(true);
        excludeRegulatorSteps(criteria);
        RUNNING_EXECUTION_STATUSES.forEach(criteria::addStatus);
        apply(actionCriteria, criteria);

        // Stop all running jobs after their current step
        actionJobs(criteria, jobView -> jobMonitorService.stopJobExecution(jobView.getJobInstanceId()));

        LOGGER.info("All Jobs will be Stopped after their Current Step");
        return true;
    }


    /**
     * Used to immediately stop jobs that have built-in waits or known states that they can be stopped in
     *
     * @return boolean indicating the jobs were stopped.
     */
    public boolean stopAllImmediatelyStoppableJobs(@BeanParam ApplicationActionCriteria actionCriteria) {
        LOGGER.info("Stopping All Jobs that can be Immediately Stopped...");

        // Immediately stop all immediately stoppable jobs that are RUNNING
        actionImmediatelyStoppableJobsInStatus(RUNNING, actionCriteria, jobView -> jobMonitorService.forceStopJobExecution(jobView.getJobInstanceId()));

        LOGGER.info("All Immediately Stoppable Jobs were Stopped");
        return true;
    }

    public boolean forceStopAllRunningJobs(@BeanParam ApplicationActionCriteria actionCriteria) {
        LOGGER.info("Force Stopping All Running Jobs...");

        // Find all RUNNING Jobs that aren't waiting on the regulator and stop them
        JobViewCriteria criteria = new JobViewCriteria(true);
        excludeRegulatorSteps(criteria);
        RUNNING_EXECUTION_STATUSES.forEach(criteria::addStatus);
        apply(actionCriteria, criteria);

        // Force stopping all running jobs
        actionJobs(criteria, jobView -> jobMonitorService.forceStopJobExecution(jobView.getJobInstanceId()));

        LOGGER.info("All Running Jobs are being Force Stopped");
        return true;

    }

    /**
     * Locks the application's UI as well as stopping jobs from being unblocked/unthrottled.
     *
     * @return boolean indicating whether the application was locked.
     */
    public boolean lock(Integer propertyId) {
        if (propertyId == null) {
            return lockApplication();
        } else {
            return lockProperty(propertyId);
        }
    }

    private boolean lockProperty(Integer propertyId) {
        LOGGER.info("Locking property...");
        if (isLocked(null) == LockType.APPLICATION) {
            throw new TetrisException(ErrorCode.INVALID_REQUEST, "Could not apply lock to property" + propertyId + ". A global lock already exists");
        }

        String serviceName = RegulatorConstants.PROPERTY_UPGRADE;
        Property property = getProperty(propertyId);
        String constraint = Constants.getRegulatorContext(property.getClient().getCode(), property.getCode());
        boolean isSpringTXEnableRegulatorService = regulatorService.isSpringTXEnableRegulatorService(property.getClient().getCode(), property.getCode());
        RegulatorRequest regulatorRequest = null;
        if (isSpringTXEnableRegulatorService) {
            regulatorRequest = regulatorSpringService.findByServiceCategoryAndStatus(serviceName, constraint, RegulatorConstants.STATUS_CODE_RUNNING);
        } else {
            regulatorRequest = regulatorService.findByServiceCategoryAndStatus(serviceName, constraint, RegulatorConstants.STATUS_CODE_RUNNING);
        }
        if (regulatorRequest == null) {
            LOGGER.info(format("Adding a Regulator Request for Service: %s and Constraint: %s to lock the UI", serviceName, constraint));

            // Build and save a Regulator Request
            regulatorRequest = buildRequest(serviceName, constraint);
            if (isSpringTXEnableRegulatorService) {
                regulatorSpringService.save(regulatorRequest);
            } else {
                regulatorService.save(regulatorRequest);
            }
        } else {
            LOGGER.warn(format("A running Regulator Request for Service: %s and Constraint: %s already exists, so the property is already locked", serviceName, constraint));
        }

        LOGGER.info("Locked the property " + propertyId + "...");
        return true;
    }

    private boolean lockApplication() {
        String serviceName = RegulatorConstants.APPLICATION_UPGRADE;
        String constraint = Constants.APPLICATION_NAME;
        boolean isSpringTXEnableRegulatorService = regulatorService.isSpringTXEnableRegulatorService();
        RegulatorRequest activeRequest = null;
        if (isSpringTXEnableRegulatorService) {
            activeRequest = regulatorSpringService.findByServiceCategoryAndStatus(serviceName, constraint, RegulatorConstants.STATUS_CODE_RUNNING);
        } else {
            activeRequest = regulatorService.findByServiceCategoryAndStatus(serviceName, constraint, RegulatorConstants.STATUS_CODE_RUNNING);
        }
        LOGGER.info("Locking Application...");

        if (activeRequest == null) {
            LOGGER.info(format("Adding a Regulator Request for Service: %s and Constraint: %s to lock the UI", serviceName, constraint));
            // Build and save a Regulator Request
            if (isSpringTXEnableRegulatorService) {
                regulatorSpringService.save(buildRequest(serviceName, constraint));
            } else {
                regulatorService.save(buildRequest(serviceName, constraint));
            }
            LOGGER.info("Locked the Application...");
            return true;
        } else {
            LOGGER.warn(format("A running Regulator Request for Service: %s and Constraint: %s already exists, so the UI is already locked", serviceName, constraint));
            return false;
        }
    }

    private RegulatorRequest buildRequest(String serviceName, String constraint) {
        RegulatorRequest regulatorRequest = new RegulatorRequest();
        regulatorRequest.setServiceName(serviceName);
        regulatorRequest.setRequestConstraint(constraint);
        regulatorRequest.setRequestStatus(RegulatorConstants.STATUS_CODE_RUNNING);
        regulatorRequest.setPriority(Integer.MAX_VALUE);
        regulatorRequest.setIgnoreInThrottler(true);
        return regulatorRequest;
    }

    /**
     * Checks to see if there are any 'RUNNING' jobs.
     *
     * @return boolean indicating whether there are any jobs that are running.
     */
    public boolean hasNoRunningJobs(@BeanParam ApplicationActionCriteria actionCriteria) {
        LOGGER.info("Checking to see if there are no RUNNING Jobs...");

        int numberOfRunningJobs = findNumberOfRunningJobs(actionCriteria);

        LOGGER.info("Found no running jobs? " + (numberOfRunningJobs == 0));
        return numberOfRunningJobs == 0;
    }

    /**
     * Returns the number of 'RUNNING' jobs
     *
     * @return int representing the count of the number of runnings jobs
     */
    public int findNumberOfRunningJobs(@BeanParam ApplicationActionCriteria actionCriteria) {
        LOGGER.info("Checking to see how many jobs are RUNNING...");

        JobViewCriteria criteria = new JobViewCriteria(true);
        excludeRegulatorSteps(criteria);
        RUNNING_EXECUTION_STATUSES.forEach(criteria::addStatus);
        apply(actionCriteria, criteria);

        int numberOfRunningJobs = jobMonitorService.getJobsCount(criteria);
        LOGGER.info("Found " + numberOfRunningJobs + " RUNNING Jobs");
        return numberOfRunningJobs;
    }

    /**
     * Unlocks the application UI by releasing a regulator lock. Releasing this
     * regulator lock also allows new jobs to start running but does not restart jobs
     * that were blocked or throttled in the regulator.
     *
     * @return boolean indicating whether the unlock was successful
     */
    public boolean unlock(Integer propertyId) {
        if (propertyId == null) {
            return unlockApplication();
        } else {
            return unlockProperty(propertyId);
        }
    }

    private boolean unlockProperty(Integer propertyId) {
        LOGGER.info("Unlocking the property " + propertyId + "...");

        String serviceName = RegulatorConstants.PROPERTY_UPGRADE;
        Property property = getProperty(propertyId);
        String constraint = Constants.getRegulatorContext(property.getClient().getCode(), property.getCode());
        boolean isSpringTXEnableRegulatorService = regulatorService.isSpringTXEnableRegulatorService(property.getClient().getCode(), property.getCode());
        RegulatorRequest regulatorRequest = null;
        if (isSpringTXEnableRegulatorService) {
            regulatorRequest = regulatorSpringService.findByServiceCategoryAndStatus(serviceName, constraint, RegulatorConstants.STATUS_CODE_RUNNING);
        } else {
            regulatorRequest = regulatorService.findByServiceCategoryAndStatus(serviceName, constraint, RegulatorConstants.STATUS_CODE_RUNNING);
        }
        if (regulatorRequest == null) {
            LOGGER.warn(String.format("Unable to find a running Regulator Request for Service: %s and Constraint: %s , so the Application is already unlocked", serviceName, constraint));
            return false;
        } else {
            // Update the RegulatorRequest to 'COMPLETED', not using the queue mechanism as we do not want to unthrottle other work at this time.
            LOGGER.info(String.format("Completing Regulator Request for Service: %s and Constraint: %s to unlock the Application", serviceName, constraint));
            if (isSpringTXEnableRegulatorService) {
                regulatorSpringService.completeRequest(regulatorRequest.getId());
            } else {
                regulatorService.completeRequest(regulatorRequest.getId());
            }
            return true;
        }
    }

    private boolean unlockApplication() {
        String serviceName = RegulatorConstants.APPLICATION_UPGRADE;
        String constraint = Constants.APPLICATION_NAME;

        LOGGER.info("Unlocking the Application...");
        boolean isSpringTXEnableRegulatorService = regulatorService.isSpringTXEnableRegulatorService();
        RegulatorRequest regulatorRequest = null;
        if (isSpringTXEnableRegulatorService) {
            regulatorRequest = regulatorSpringService.findByServiceCategoryAndStatus(serviceName, constraint, RegulatorConstants.STATUS_CODE_RUNNING);
        } else {
            regulatorRequest = regulatorService.findByServiceCategoryAndStatus(serviceName, constraint, RegulatorConstants.STATUS_CODE_RUNNING);
        }
        if (regulatorRequest == null) {
            LOGGER.warn(String.format("Unable to find a running Regulator Request for Service: %s and Constraint: %s already exists, so the Application is already unlocked", serviceName, constraint));
            return false;
        } else {
            // Update the RegulatorRequest to 'COMPLETED', not using the queue mechanism as we do not want to unthrottle other work at this time.
            LOGGER.info(String.format("Completing Regulator Request for Service: %s and Constraint: %s to unlock the Application", serviceName, constraint));
            regulatorRequest.setRequestStatus(RegulatorConstants.STATUS_CODE_COMPLETED);
            if (isSpringTXEnableRegulatorService) {
                regulatorSpringService.save(regulatorRequest);
            } else {
                regulatorService.save(regulatorRequest);
            }
            LOGGER.info("Unlocked the Application");
            return true;
        }
    }

    /**
     * Used to unblock/unthrottle all regulator requests.
     *
     * @return boolean indicating the requests were unblocked
     */
    public boolean resumeBlockedJobs() {
        LOGGER.info("Resuming All Blocked/Throttled Jobs...");
        if (regulatorService.isSpringTXEnableRegulatorService()) {
            int numberOfBlockedRequests = regulatorSpringService.unblockAllRequests();
            LOGGER.info("Unblocked: " + numberOfBlockedRequests + " Regulator Requests");
            return true;
        } else {
            int numberOfBlockedRequests = regulatorService.unblockAllRequests();
            LOGGER.info("Unblocked: " + numberOfBlockedRequests + " Regulator Requests");
            return true;
        }
    }

    /**
     * Used to resume all report schedules so that they will fire at their next interval.
     *
     * @return boolean indicating the schedules were resumed
     */
    public boolean resumeReportSchedules() {
        return true;
    }

    public boolean resumeReportSchedulesOldCall() {
        return true;
    }

    /**
     * Used to resume all remote agents so that they will send data again
     *
     * @return boolean indicating the remote agent processing will be resumed
     */
    public boolean resumeAllRemoteAgents() {
        LOGGER.info("Resuming All Remote Agents...");
        remoteAgentConfigService.resumeAllRemoteAgents();
        LOGGER.info("All Remote Agents Resumed");
        return true;
    }

    /**
     * Used to resume all Quartz schedules so that they will fire at their next interval.
     *
     * @return boolean indicating the schedules were successfully resumed
     */
    public boolean resumeQuartzSchedules() {
        LOGGER.info("Resuming All Quartz Schedules...");
        scheduleService.resumeAll();
        LOGGER.info("All Quartz Schedules Resumed");
        return true;
    }

    /**
     * Used to resume all stopped jobs
     *
     * @return boolean indicating the jobs were resumed.
     */
    public boolean resumeAllStoppedJobs(@BeanParam ApplicationActionCriteria actionCriteria) {
        LOGGER.info("Resuming All Stopped Jobs...");

        // Build a criteria - include only running jobs
        JobViewCriteria criteria = new JobViewCriteria(true);
        criteria.addStatus(ExecutionStatus.STOPPED);
        apply(actionCriteria, criteria);

        // Resume stopped jobs
        actionJobs(criteria, jobView -> jobMonitorService.resumeJobExecution(jobView.getJobExecutionId()));

        LOGGER.info("All Stopped Jobs have been Resumed");
        return true;
    }

    /**
     * Resume all immediately stoppable jobs that were stopped immediately
     *
     * @return boolean indicating the jobs were resumed.
     */
    public boolean resumeAllImmediatelyStoppableJobs(@BeanParam ApplicationActionCriteria actionCriteria) {
        LOGGER.info("Resuming All Jobs that were Immediately Stopped...");

        int[] resumedImmediatelyStoppedJobs = new int[]{0};
        // Find all of the stopped job views and resume the ones that were force stopped
        actionImmediatelyStoppableJobsInStatus(ExecutionStatus.STOPPED, actionCriteria, jobView -> {
            // We only want to resume jobs that were forced stopped
            if (jobView.getJobContext().getSimpleAttributes().containsKey(FORCED_STOP)) {
                jobMonitorService.resumeJobExecutionByInstanceId(jobView.getJobInstanceId());
                resumedImmediatelyStoppedJobs[0]++;
            }
        });

        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("Resumed: " + Arrays.toString(resumedImmediatelyStoppedJobs) + " Immediately Stopped Jobs");
            LOGGER.info("All Immediately Stoppable Jobs were resumed");
        }
        return true;
    }

    /**
     * Used to determine whether all Quartz schedules are stopped.
     *
     * @return boolean indicating the schedules were stopped.
     */
    public boolean areQuartzSchedulesStopped() {
        LOGGER.info("Are Quartz Schedules Stopped...");

        List<Schedule> schedules = scheduleService.getSchedules();
        if (CollectionUtils.isNotEmpty(schedules)) {
            for (Schedule schedule : schedules) {
                if (ScheduleStatus.RUNNING.equals(schedule.getStatus())) {
                    LOGGER.info("All Quartz Schedules are NOT stopped...");
                    return false;
                }
            }
        }

        LOGGER.info("All Quartz Schedules are stopped...");
        return true;
    }

    /**
     * Move property datasets
     */
    public boolean moveProperty(MovePropertyRequestDTO movePropertyRequest) {
        // don't allow a property move if the property is not locked
        if (!isPropertyLocked(movePropertyRequest.getPropertyId())) {
            throw new TetrisException(ErrorCode.INVALID_REQUEST, "Cannot move property.  Property is not locked.");
        }
        LOGGER.info("Moving database for property " + movePropertyRequest.getPropertyId());

        // update the db loc for the property
        DBLoc propertyDbLoc = dbMaintainService.getDbLocForPropertyId(movePropertyRequest.getPropertyId());
        DBLoc dbLoc = dbMaintainService.moveDatabase(movePropertyRequest.getPropertyId(), propertyDbLoc, movePropertyRequest.getDatabaseServerName());

        // update the sas location for the property
        SASFileLoc propertySasFileLoc = sasNodeLocator.getSasLocForProperty(movePropertyRequest.getPropertyId());
        SASFileLoc sasFileLoc = sasNodeLocator.moveSasFileLoc(propertySasFileLoc, movePropertyRequest.getSasServerName());

        // update any active regulator requests with the new
        Property property = getProperty(movePropertyRequest.getPropertyId());
        String regulatorConstraint = Constants.getRegulatorContext(property.getClient().getCode(), property.getCode());
        if (regulatorService.isSpringTXEnableRegulatorService(property.getClient().getCode(), property.getCode())) {
            List<RegulatorRequest> activeRegulatorRequests = regulatorSpringService.getActiveRequests(regulatorConstraint);
            for (RegulatorRequest regulatorRequest : activeRegulatorRequests) {
                regulatorRequest.setDbServerName(dbLoc.getServerName());
                regulatorRequest.setSasServerName(sasFileLoc.getSasServerName());
                regulatorSpringService.save(regulatorRequest);
            }
            LOGGER.info("Move property completed for " + movePropertyRequest.getPropertyId() + " Updated " + activeRegulatorRequests.size() + " pending regulator requests");
        } else {
            List<RegulatorRequest> activeRegulatorRequests = regulatorService.getActiveRequests(regulatorConstraint);
            for (RegulatorRequest regulatorRequest : activeRegulatorRequests) {
                regulatorRequest.setDbServerName(dbLoc.getServerName());
                regulatorRequest.setSasServerName(sasFileLoc.getSasServerName());
                regulatorService.save(regulatorRequest);
            }
            LOGGER.info("Move property completed for " + movePropertyRequest.getPropertyId() + " Updated " + activeRegulatorRequests.size() + " pending regulator requests");
        }

        return true;
    }

    /**
     * Used to determine whether all Quartz schedules are stopped.
     *
     * @return boolean indicating the schedules were stopped.
     */
    public LockType isLocked(Integer propertyId) {
        if (propertyId != null && isPropertyLocked(propertyId)) {
            LOGGER.info("Property is locked...");
            return LockType.PROPERTY;
        }

        String serviceName = RegulatorConstants.APPLICATION_UPGRADE;
        String constraint = Constants.APPLICATION_NAME;

        LOGGER.info("Is Application Locked...");
        if (regulatorService.isSpringTXEnableRegulatorService()) {
            RegulatorRequest regulatorRequest = regulatorSpringService.findByServiceCategoryAndStatus(serviceName, constraint, RegulatorConstants.STATUS_CODE_RUNNING);
            if (regulatorRequest == null) {
                LOGGER.info("The Application is NOT locked...");
                return LockType.NONE;
            }
        } else {
            RegulatorRequest regulatorRequest = regulatorService.findByServiceCategoryAndStatus(serviceName, constraint, RegulatorConstants.STATUS_CODE_RUNNING);
            if (regulatorRequest == null) {
                LOGGER.info("The Application is NOT locked...");
                return LockType.NONE;
            }
        }

        LOGGER.info("The Application is locked...");
        return LockType.APPLICATION;
    }

    private boolean isPropertyLocked(Integer propertyId) {
        String serviceName = RegulatorConstants.PROPERTY_UPGRADE;
        Property property = getProperty(propertyId);
        String propertyConstraint = Constants.getRegulatorContext(property.getClient().getCode(), property.getCode());
        if (regulatorService.isSpringTXEnableRegulatorService(property.getClient().getCode(), property.getCode())) {
            RegulatorRequest propertyRegulatorRequest = regulatorSpringService.findByServiceCategoryAndStatus(serviceName, propertyConstraint, RegulatorConstants.STATUS_CODE_RUNNING);
            return propertyRegulatorRequest != null;
        } else {
            RegulatorRequest propertyRegulatorRequest = regulatorService.findByServiceCategoryAndStatus(serviceName, propertyConstraint, RegulatorConstants.STATUS_CODE_RUNNING);
            return propertyRegulatorRequest != null;
        }
    }

    /**
     * Used to clear all caches on the application server.
     *
     * @return boolean indicating that the caches were cleared
     */
    @Transactional(readOnly = true)
    public boolean clearAllCaches() {
        LOGGER.info("Clearing All Caches...");
        cacheService.clearAll();
        LOGGER.info("All Caches Were Cleared");
        return true;
    }

    /**
     * Recover jobs are in bad states due to unexpected system downtime
     * Also recover the state of the regulator requests
     */
    public Set<Long> recoverJobsAndRegulator(Date startTimeBefore, @BeanParam ApplicationActionCriteria actionCriteria) {
        LOGGER.info("Recovering Jobs Before: " + startTimeBefore + " for criteria: " + actionCriteria);

        // Force stop jobs before a particular time
        Set<Long> stoppedJobIds = forceStopJobsBeforeTime(startTimeBefore, actionCriteria);

        // Complete any regulator requests for already finished jobs
        completeRegulatorRequestsForAlreadyFinishedJobs(actionCriteria);

        return stoppedJobIds;
    }

    public Set<Long> forceStopJobsBeforeTime(Date startTimeBefore, String hostName) {
        return forceStopJobsBeforeTime(startTimeBefore, new ApplicationActionCriteria().hostName(hostName));
    }

    /**
     * Force Stop Jobs that were RUNNING before a particular date/time (possibly for a particular host too)
     */
    public Set<Long> forceStopJobsBeforeTime(Date startTimeBefore, @BeanParam ApplicationActionCriteria actionCriteria) {
        JobViewCriteria criteria = new JobViewCriteria(true);
        excludeRegulatorSteps(criteria);
        RUNNING_EXECUTION_STATUSES.forEach(criteria::addStatus);
        criteria.setStartTimeBefore(LocalDateTime.fromDateFields(startTimeBefore));
        criteria.setEndTimeIsNull(true);
        apply(actionCriteria, criteria);

        // Force Stop all jobs that started before the passed in date
        final Set<JobView> jobsThatFailedToBeForceStopped = new HashSet<>();
        final Set<Long> actionedJobInstanceIds = new HashSet<>();

        LOGGER.info("Force stopping jobs that were in a running state for the application server when it restarted");
        attemptToForceStopJobs(criteria, jobsThatFailedToBeForceStopped, actionedJobInstanceIds);

        // If some jobs couldn't be stopped - let's try to stop them a second time
        if (CollectionUtils.isNotEmpty(jobsThatFailedToBeForceStopped)) {
            LOGGER.warn("Force stop failed on some jobs - trying a 2nd time...");
            attemptToForceStopJobs(criteria, jobsThatFailedToBeForceStopped, actionedJobInstanceIds);
        }

        // If some jobs still couldn't be stopped - let's try to stop them a final time
        if (CollectionUtils.isNotEmpty(jobsThatFailedToBeForceStopped)) {
            LOGGER.warn("Force stop failed on some jobs - trying for a 3rd and final time...");
            attemptToForceStopJobs(criteria, jobsThatFailedToBeForceStopped, actionedJobInstanceIds);
        }

        // Log out the jobs that failed to be force stopped
        int numberOfJobsForceStopped = actionedJobInstanceIds.size();
        if (CollectionUtils.isNotEmpty(jobsThatFailedToBeForceStopped)) {
            List<String> jobMessages = jobsThatFailedToBeForceStopped.stream().map(jobView -> jobView.getJobInstanceId() + ": " + jobView.getJobName() + " for " + jobView.getClientCode() + "/" + jobView.getPropertyCode()).collect(Collectors.toList());
            LOGGER.error("Force Stop failed for the following " + jobsThatFailedToBeForceStopped.size() + " jobs: " + jobMessages);
        }

        LOGGER.info("Force Stopped " + numberOfJobsForceStopped + " RUNNING Jobs");
        return actionedJobInstanceIds;
    }

    private void attemptToForceStopJobs(JobViewCriteria criteria, Set<JobView> jobsThatFailedToBeForceStopped, Set<Long> actionedJobInstanceIds) {
        // Clear the jobs that failed list - going to try again
        jobsThatFailedToBeForceStopped.clear();

        // Try to force stop again...
        actionedJobInstanceIds.addAll(forceStopJobs(criteria, jobsThatFailedToBeForceStopped));

        // Remove the jobs that failed to be force stopped from the list of actioned job instances so we don't try to resume them
        actionedJobInstanceIds.removeAll(jobsThatFailedToBeForceStopped.stream().map(JobView::getJobInstanceId).collect(Collectors.toSet()));
    }

    private Set<Long> forceStopJobs(JobViewCriteria criteria, Set<JobView> jobsThatFailedToBeForceStopped) {
        return actionJobs(criteria, jobView -> {
            try {
                jobMonitorService.forceStopJobExecution(jobView.getJobInstanceId());
            } catch (RuntimeException re) {
                LOGGER.error("Unable to force stop job: " + jobView.getJobInstanceId(), re);
                jobsThatFailedToBeForceStopped.add(jobView);
            }
        });
    }

    public int completeRegulatorRequestsForAlreadyFinishedJobs(String hostName) {
        return completeRegulatorRequestsForAlreadyFinishedJobs(new ApplicationActionCriteria().hostName(hostName));
    }

    public int completeRegulatorRequestsForAlreadyFinishedJobs(@BeanParam ApplicationActionCriteria actionCriteria) {
        int[] regulatorRequestsForFinishedJobs = new int[]{0};
        boolean isSpringTXEnableRegulatorService = regulatorService.isSpringTXEnableRegulatorService();
        List<RegulatorRequest> activeRegulatorRequests = null;
        if (isSpringTXEnableRegulatorService) {
            activeRegulatorRequests = regulatorSpringService.getActiveRequests();
        } else {
            activeRegulatorRequests = regulatorService.getActiveRequests();
        }
        if (CollectionUtils.isNotEmpty(activeRegulatorRequests)) {

            // Get the JobInstanceIds for all Running RegulatorRequests
            List<Long> jobInstanceIds = activeRegulatorRequests.stream().map(RegulatorRequest::getJobInstanceId).collect(Collectors.toList());

            List<List<Long>> batchOfJobInstanceIds = Lists.partition(jobInstanceIds, 1000);
            batchOfJobInstanceIds.forEach(batch -> {
                JobViewCriteria jobViewCriteria = new JobViewCriteria(true);
                jobViewCriteria.setJobInstanceIds(batch);
                jobViewCriteria.setStatuses(Arrays.asList(ExecutionStatus.COMPLETED, ExecutionStatus.ABANDONED));
                apply(actionCriteria, jobViewCriteria);

                List<JobView> jobViews = jobMonitorService.getJobs(jobViewCriteria);
                if (CollectionUtils.isNotEmpty(jobViews)) {
                    jobViews.forEach(jobView -> {
                        Integer regulatorRequestId = (Integer) jobView.getFromExecutionContext(REGULATOR_REQUEST_ID);
                        if (regulatorRequestId != null) {
                            if (isSpringTXEnableRegulatorService) {
                                regulatorSpringService.completeRequest(regulatorRequestId);
                            } else {
                                regulatorService.completeRequest(regulatorRequestId);
                            }
                            regulatorRequestsForFinishedJobs[0]++;
                        }
                    });
                }
            });
        }

        LOGGER.info("Completed " + regulatorRequestsForFinishedJobs[0] + " Regulator Requests for Finished Jobs");
        return regulatorRequestsForFinishedJobs[0];
    }

    private Set<Long> actionJobs(JobViewCriteria criteria, JobViewAction action) {
        // Get all of the JobViews for the criteria and force stop each one
        List<JobView> jobViews = jobMonitorService.getJobs(criteria);
        if (CollectionUtils.isNotEmpty(jobViews)) {
            return jobViews.stream()
                    .peek(action::execute)
                    .map(JobView::getJobInstanceId)
                    .collect(Collectors.toSet());
        }

        return Collections.emptySet();
    }

    private void actionImmediatelyStoppableJobsInStatus(ExecutionStatus executionStatus, ApplicationActionCriteria actionCriteria, JobViewAction action) {
        for (ImmediatelyStoppableJobStep immediatelyStoppableJobStep : getImmediatelyStoppableJobSteps()) {
            String jobName = immediatelyStoppableJobStep.getJobName();
            String stepName = immediatelyStoppableJobStep.getStepName();

            LOGGER.info("Checking to see if there are jobs in " + executionStatus.name() + " for:" + jobName + " in step:" + stepName);

            // Build a criteria - include only running jobs
            JobViewCriteria criteria = new JobViewCriteria(true);
            criteria.addStatus(executionStatus);
            // apply user-supplied criteria
            apply(actionCriteria, criteria);

            if (StringUtils.isNotEmpty(jobName)) {
                criteria.setJobNames(Collections.singletonList(jobName));
            }

            if (StringUtils.isNotEmpty(stepName)) {
                criteria.addStepName(stepName);
            }

            actionJobs(criteria, action);
        }
    }

    private List<ImmediatelyStoppableJobStep> getImmediatelyStoppableJobSteps() {
        List<ImmediatelyStoppableJobStep> immediatelyStoppableJobSteps = new ArrayList<>();

        // Get the ImmediatelyStoppableJobStep from system properties
        String immediatelyStoppableJobStepsStr = SystemConfig.getImmediatelyStoppableJobSteps();
        if (StringUtils.isNotEmpty(immediatelyStoppableJobStepsStr)) {

            // Handle a comma-separated list of jobName.stepName
            String[] immediatelyStoppableJobStepStrs = StringUtils.split(immediatelyStoppableJobStepsStr, ",");
            for (String immediatelyStoppableJobStepStr : immediatelyStoppableJobStepStrs) {

                // String is a jobName.stepName representation - if no stepName set it to null
                String[] jobAndStep = StringUtils.split(immediatelyStoppableJobStepStr, ".");
                immediatelyStoppableJobSteps.add(new ImmediatelyStoppableJobStep(jobAndStep[0], jobAndStep.length > 1 ? jobAndStep[1] : null));
            }
        }

        return immediatelyStoppableJobSteps;
    }

    private void excludeRegulatorSteps(JobViewCriteria criteria) {
        // Exclude the regulator steps
        criteria.setExcludeStepNames(true);
        for (String regulatorStepName : StepGroup.REGULATOR.getAssociatedStepNames()) {
            criteria.addStepName(regulatorStepName);
        }
    }

    public interface JobViewAction {
        void execute(JobView jobView);
    }

    private void apply(ApplicationActionCriteria actionCriteria, JobViewCriteria criteria) {
        if (actionCriteria != null) {
            actionCriteria.validate();
            if (StringUtils.isNotBlank(actionCriteria.getHostName())) {
                criteria.setNodes(Collections.singletonList(actionCriteria.getHostName()));
            }

            if (actionCriteria.getPropertyId() != null) {
                criteria.setPropertyId(actionCriteria.getPropertyId());
            }

            if (StringUtils.isNotBlank(actionCriteria.getDbServerName())) {
                criteria.setDbServerName(actionCriteria.getDbServerName());
            }

            if (StringUtils.isNotBlank(actionCriteria.getSasServerName())) {
                criteria.setSasServerName(actionCriteria.getSasServerName());
            }
        }
    }

    private Property getProperty(Integer propertyId) {
        Property property = propertyService.getPropertyById(propertyId);
        if (property == null) {
            throw new TetrisException(ErrorCode.PROPERTY_NOT_FOUND, "No property found for id " + propertyId);
        } else if (property.getStatus() != Status.ACTIVE) {
            throw new TetrisException(ErrorCode.INVALID_REQUEST, "Property " + propertyId + " is inactive");
        }
        return property;
    }

    public void upgradeComplete(Integer propertyId) {
        String deploymentType = SystemConfig.getBlueGreenDeploymentStatus();
        Property property = getProperty(propertyId);
        property.setDeploymentStatus(deploymentType);
        propertyService.updateProperty(property);
    }
}
 