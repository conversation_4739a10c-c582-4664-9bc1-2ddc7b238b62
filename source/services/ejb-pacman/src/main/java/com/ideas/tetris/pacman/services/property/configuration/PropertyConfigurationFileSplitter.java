package com.ideas.tetris.pacman.services.property.configuration;

import com.google.common.io.Files;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.MetaDataPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.file.PropertyConfigurationFile;
import org.apache.commons.io.FilenameUtils;
import org.apache.log4j.Logger;

import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class PropertyConfigurationFileSplitter {

    private static final int FILE_NAME_SEGMENTS = 4;
    private static final int DEFAULT_PROPERTIES_PER_FILE = 4;
    private static final Logger LOGGER = Logger.getLogger(PropertyConfigurationFileSplitter.class.getName());

    private static final String[] EXCLUDED_BRANDS = {"HW", "HT", "HH", "DT", "ES", "WA", "CI", "GV"};

    public void splitFile(PropertyConfigurationFile configurationFile, int propertiesPerFile) throws IOException {
        int i = 1;
        List<String> propertyCodes = new ArrayList<String>();
        Iterator<String> iter = configurationFile.getPropertyCodes().iterator();
        while (iter.hasNext()) {
            String propertyCode = iter.next();
            Map<String, List<String>> recordMap = buildRecordMap(configurationFile.getRecordsForProperty(propertyCode));
            if (excludeProperty(recordMap.get(PropertyConfigurationRecordType.PA.getRecordType()))) {
                LOGGER.error("excluded property " + propertyCode);
            } else {
                propertyCodes.add(propertyCode);
                if (propertyCodes.size() == propertiesPerFile || !iter.hasNext()) {
                    writeToFile(propertyCodes, configurationFile, i++);
                    propertyCodes.clear();
                }
            }
        }
    }


    private boolean excludeProperty(List<String> propertyAttributeRecords) {
        if (propertyAttributeRecords == null || propertyAttributeRecords.isEmpty()) {
            return false;
        }
        String brand = getBrand(propertyAttributeRecords.get(0));
        return Arrays.asList(EXCLUDED_BRANDS).contains(brand);
    }

    private String getBrand(String propertyAttributeRecord) {
        String[] segments = propertyAttributeRecord.split("[|]");
        if (segments.length < FILE_NAME_SEGMENTS) {
            return "unknown";
        }
        return segments[FILE_NAME_SEGMENTS - 1];
    }

    private Map<String, List<String>> buildRecordMap(List<String> recordsForProperty) {
        Map<String, List<String>> map = new HashMap<String, List<String>>();
        for (String record : recordsForProperty) {
            String[] segments = record.split("[|]");
            String type = segments[0];
            List<String> records = map.get(type);
            if (records == null) {
                records = new ArrayList<String>();
                map.put(type, records);
            }
            records.add(record);
        }
        return map;
    }

    private void writeToFile(List<String> propertyCodes, PropertyConfigurationFile configurationFile, int i) throws IOException {
        String baseName = FilenameUtils.getBaseName(configurationFile.getConfigurationFile().getPath());
        File outputFile = new File(configurationFile.getConfigurationFile().getParentFile(), baseName + "_" + i + ".psv");
        outputFile.createNewFile();

        MetaDataPropertyConfigurationDto metaDataRecord = new MetaDataPropertyConfigurationDto();
        String[] metaRecordFields = configurationFile.getMetaDataRecord().getRecordFields();
        metaDataRecord.setRecordFields(metaRecordFields);
        metaDataRecord.setNumberOfProperties(propertyCodes.size());

        BufferedWriter bufferedWriter = null;
        try {
            bufferedWriter = Files.newWriter(outputFile, Charset.defaultCharset());
            bufferedWriter.write(metaDataRecord.getPSVRecord());
            bufferedWriter.newLine();
            for (String propertyCode : propertyCodes) {
                List<String> records = configurationFile.getRecordsForProperty(propertyCode);
                for (String record : records) {
                    bufferedWriter.write(record);
                    bufferedWriter.newLine();
                }
            }
        } finally {
            if (bufferedWriter != null) {
                bufferedWriter.close();
            }
        }
    }

    public static void main(String[] args) throws Exception {
        File file = new File(args[0]);
        if (!file.exists()) {
            throw new IOException("File " + file + " does not exist");
        }

        int propertiesPerFile = DEFAULT_PROPERTIES_PER_FILE;
        if (args.length > 1) {
            propertiesPerFile = Integer.parseInt(args[1]);
        }

        PropertyConfigurationFile configurationFile = new PropertyConfigurationFile(file, true);

        PropertyConfigurationFileSplitter splitter = new PropertyConfigurationFileSplitter();
        splitter.splitFile(configurationFile, propertiesPerFile);
    }
}
