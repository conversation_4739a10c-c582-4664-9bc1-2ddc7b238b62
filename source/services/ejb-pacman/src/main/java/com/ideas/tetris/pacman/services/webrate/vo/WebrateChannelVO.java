package com.ideas.tetris.pacman.services.webrate.vo;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateChannel;

import java.io.Serializable;
import java.time.LocalDateTime;


public class WebrateChannelVO implements Serializable {
    Integer webRateChannelId;
    String webrateChannelName;
    String fileChannelId;
    String webrateChannelAlias;
    String webrateChannelDescription;
    Boolean shouldDelete;
    Integer lastUpdatedByUserId;
    LocalDateTime lastUpdatedDate;

    public WebrateChannelVO() {

    }

    public WebrateChannelVO(WebrateChannel webrateChannel) {
        this.setWebRateChannelId(webrateChannel.getId());
        this.setWebrateChannelName(webrateChannel.getWebrateChannelName());
        this.setWebrateChannelAlias(webrateChannel.getWebrateChannelAlias());
        this.setWebrateChannelDescription(webrateChannel.getWebrateChannelDescription());
        this.setFileChannelId(webrateChannel.getFileChannelID());
        this.setShouldDelete(webrateChannel.getShouldDelete());
        this.setLastUpdatedByUserId(Integer.valueOf(PacmanWorkContextHelper.getUserId()));
        this.setLastUpdatedDate(LocalDateTime.now());
    }

    public String getWebrateChannelName() {
        return webrateChannelName;
    }

    public void setWebrateChannelName(String webrateChannelName) {
        this.webrateChannelName = webrateChannelName;
    }

    public Integer getWebRateChannelId() {
        return webRateChannelId;
    }

    public void setWebRateChannelId(Integer webRateChannelId) {
        this.webRateChannelId = webRateChannelId;
    }

    public String getWebrateChannelAlias() {
        return webrateChannelAlias;
    }

    public void setWebrateChannelAlias(String webrateChannelAlias) {
        this.webrateChannelAlias = webrateChannelAlias;
    }

    public String getWebrateChannelDescription() {
        return webrateChannelDescription;
    }

    public void setWebrateChannelDescription(String webrateChannelDescription) {
        this.webrateChannelDescription = webrateChannelDescription;
    }

    public Boolean getShouldDelete() {
        return shouldDelete;
    }

    public void setShouldDelete(Boolean shouldDelete) {
        this.shouldDelete = shouldDelete;
    }

    public Integer getLastUpdatedByUserId() {
        return lastUpdatedByUserId;
    }

    public void setLastUpdatedByUserId(Integer lastUpdatedByUserId) {
        this.lastUpdatedByUserId = lastUpdatedByUserId;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getFileChannelId() {
        return fileChannelId;
    }

    public void setFileChannelId(String fileChannelId) {
        this.fileChannelId = fileChannelId;
    }
}
