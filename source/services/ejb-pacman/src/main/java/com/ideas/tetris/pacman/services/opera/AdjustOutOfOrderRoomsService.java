package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.opera.constants.OperaSummaryServiceConstants;
import com.ideas.tetris.pacman.services.opera.metric.OperaMetrics;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.util.jdbc.JpaJdbcUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Component
@Transactional
public class AdjustOutOfOrderRoomsService {
    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    OperaUtilityService operaUtilityService;
    @Autowired
    JpaJdbcUtil jpaJdbcUtil;

    private static final Logger LOGGER = Logger.getLogger(AdjustOutOfOrderRoomsService.class.getName());

    private static final int ACCOM_LEVEL_CAPACITY = 2;
    private static final int ROOM_TYPE = 1;
    private static final int OCC_DATE = 0;
    private static final int HOTEL_LEVEL_OOO = 2;
    private static final int HOTEL_LEVEL_CAPACITY = 1;

    public enum AdjustOutOfOrderRoomsServiceMetricType {ADJUST_OUT_OF_ORDER}

    public static final OperaMetrics<AdjustOutOfOrderRoomsServiceMetricType> METRICS = new OperaMetrics<>();

    private static final String GET_PAST_HOTEL_LEVEL_OCCCUPANCY_DATE_CAPACITY_OOO = new StringBuilder().
            append("SELECT DISTINCT sos.Occupancy_DT,sos.Physical_Rooms,sos.Out_Of_Order_Rooms").
            append(" FROM         opera.Stage_Occupancy_Summary AS sos ").
            append(" WHERE     (sos.Occupancy_DT <").
            append(" (SELECT     CASE WHEN MIN(occupancy_date) IS NULL THEN :businessDt ELSE MIN(occupancy_date) END AS minOccDate").
            append(" FROM  opera.Adjusted_OOO))").
            append(" AND sos.Data_Load_Metadata_Id in (:dataLoadMetadataId)").
            append(" AND (sos.Physical_Rooms > 0)").
            append(" AND (sos.Out_Of_Order_Rooms > 0)").toString();

    private static final String GET_PAST_ROOM_TYPE_OCCUPANCY_DATE_LIST = new StringBuilder().
            append("INSERT INTO opera.Adjusted_OOO (Occupancy_Date,Room_Type,Adjusted_OOO) ").
            append("SELECT distinct sos.occupancy_dt,sos.room_type, 0 as Adjusted_OOO").
            append(" FROM         opera.Stage_Occupancy_Summary AS sos ").
            append(" WHERE     (sos.Occupancy_DT <").
            append(" (SELECT     CASE WHEN MIN(occupancy_date) IS NULL THEN :businessDt ELSE MIN(occupancy_date) END AS minOccDate").
            append(" FROM          opera.Adjusted_OOO))").
            append(" AND       sos.Data_Load_Metadata_Id in (:dataLoadMetadataId)").
            append(" ORDER BY sos.Occupancy_DT").toString();

    private static final String GET_OCCCUPANCY_DATE_ACCOMTYPE_CAPACITY_BY_OCCUPANCY_DATE =
            new StringBuilder().
                    append("SELECT distinct OCCUPANCY_DT,ROOM_Type,PHYSICAL_ROOMS,sos.Out_Of_Order_Rooms  ").
                    append("FROM opera.Stage_Occupancy_Summary sos join ").
                    append("opera.Data_Load_Metadata dlm on sos.Data_Load_Metadata_ID=dlm.Data_Load_Metadata_ID ").
                    append("where dlm.Incoming_File_Type_Code in ('CTAT','PTAT','PT','CT') ").
                    append("and sos.Physical_Rooms >0 ").
                    append("and Occupancy_DT >= :startDate").
                    append(" and Occupancy_DT <= :endDate").
                    append(" order by Occupancy_DT,Physical_Rooms desc").toString();

    private static String UPDATE_ADJUSTED_OOO = "UPDATE opera.Adjusted_OOO set Adjusted_OOO=? where Room_Type = ? and Occupancy_Date = ?";

    private static final String GET_FUTURE_ROOM_TYPE_OOO_LIST = new StringBuilder("SELECT DISTINCT sos.Room_Type,sos.Occupancy_DT, sos.Out_Of_Order_Rooms  ")
            .append(" into #FutureOOO FROM opera.Stage_Occupancy_Summary AS sos where ")
            .append(" sos.Occupancy_DT between :businessDate AND DATEADD(DAY, 30,:businessDate ) and ")
            .append(" sos.Data_Load_Metadata_Id = :dataLoadIDList ; ")
            .toString();

    private static final String DELETE_FUTURE_ROOM_TYPE_OOO = new StringBuilder(" delete FROM opera.Adjusted_OOO where ")
            .append(" Occupancy_Date between :businessDate AND DATEADD(DAY, 30,:businessDate ) ;")
            .toString();

    private static final String UPDATE_OOO_STAGE_OCCUPANCY = "  update s  " +
            " set s.Out_Of_Order_Rooms= adjOOO.Adjusted_OOO " +
            " from opera.Stage_Occupancy_Summary as s " +
            " join (select Occupancy_Date,Room_Type,Adjusted_OOO from opera.Adjusted_OOO where occupancy_date < :businessDate) as adjOOO on " +
            " s.Occupancy_DT=adjOOO.Occupancy_Date and " +
            " s.Room_Type = adjOOO.Room_Type ;   ";

    private static final String INSERT_FUTURE_ROOM_TYPE_OOO = new StringBuilder()
            .append("insert into [opera].[Adjusted_OOO] ([Room_Type],[Occupancy_Date],[Adjusted_OOO]) ")
            .append(" select Room_Type ,Occupancy_DT , Out_Of_Order_Rooms  from  ")
            .append(" #FutureOOO  ; ").toString();

    public int adjustOutOfOrder(String correlationId) {
        LOGGER.info("Started adjusting OOO for feed : " + correlationId);
        int totalAdjusted = 0;
        METRICS.start(AdjustOutOfOrderRoomsServiceMetricType.ADJUST_OUT_OF_ORDER);
        try {
            Map<String, Integer> dataLoadMetadataIDMap = operaUtilityService.getDataLoadMetadataIDMap(correlationId);
            if (isAdjustOOOCountsRequired(dataLoadMetadataIDMap)) {
                LOGGER.info("Adjusting OOO at room type level is required.");
                LocalDate btDate = operaUtilityService.getBusinessDateFromStage();
                totalAdjusted = handlePastOutOfOrders(dataLoadMetadataIDMap, btDate);
                totalAdjusted += handleFutureOutOfOrder(dataLoadMetadataIDMap, btDate);
                totalAdjusted += updateAdjustedintoStageOccupancy(btDate, dataLoadMetadataIDMap.get("PTAT"));
                METRICS.stop(AdjustOutOfOrderRoomsServiceMetricType.ADJUST_OUT_OF_ORDER);
            } else {
                LOGGER.info("Adjusting OOO at room type level is NOT required.");
            }
            return totalAdjusted;
        } catch (Exception e) {
            LOGGER.error("Error while adjusting Out of Order Handling", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "ERROR:Error while adjusting Out of Order Handling", e);
        } finally {
            LOGGER.info(new StringBuilder().
                    append("Completed adjusting OOO for feed : ").
                    append(correlationId).
                    append(" : Total adjustments :").
                    append(totalAdjusted).
                    append("  rows:\n").
                    append(METRICS.toString()).toString());
        }
    }

    private boolean isAdjustOOOCountsRequired(Map<String, Integer> dataLoadMetadataIDMap) {
        List<Object> byNativeQuerySingleResult = crudService.findByNativeQuery(OperaSummaryServiceConstants.IS_ADJUST_OOO_REQUIRED
                , QueryParameter.with("dataLoadPTAT", dataLoadMetadataIDMap.get("PTAT"))
                        .and("dataLoadPT", dataLoadMetadataIDMap.get("PT")).parameters());
        if (null == byNativeQuerySingleResult || byNativeQuerySingleResult.isEmpty()) {
            return false;
        } else {
            return Boolean.parseBoolean(byNativeQuerySingleResult.get(0).toString());
        }
    }

    private int handlePastOutOfOrders(Map<String, Integer> dataLoadMap, LocalDate btDate) {
        try {
            List<Integer> ptCtDataLoadIdList = Arrays.asList(dataLoadMap.get("PT"), dataLoadMap.get("CT"));
            List<Integer> ptatCtatPsatCsatDataLoadIdList = Arrays.asList(dataLoadMap.get("PTAT"), dataLoadMap.get("CTAT"), dataLoadMap.get("CSAT"), dataLoadMap.get("PSAT"));
            int pastOutofOrdersHandled = 0;
            List<Object[]> queryResults = crudService.findByNativeQuery(GET_PAST_HOTEL_LEVEL_OCCCUPANCY_DATE_CAPACITY_OOO,
                    QueryParameter.with("dataLoadMetadataId", ptCtDataLoadIdList)
                            .and("businessDt", btDate)
                            .parameters());
            pastOutofOrdersHandled = fillDefaultOutOfOrder(ptatCtatPsatCsatDataLoadIdList, btDate);

            if (!queryResults.isEmpty()) {
                Object[] firstRecord = queryResults.get(0);
                String startDate = firstRecord[0].toString();

                Object[] lastRecord = queryResults.get(queryResults.size() - 1);
                String endDate = lastRecord[0].toString();

                pastOutofOrdersHandled = adjustOutOfOrderAtEachOccupancyDate(pastOutofOrdersHandled, firstRecord, startDate, endDate);

            }
            return pastOutofOrdersHandled;
        } catch (Exception e) {
            LOGGER.error("Error while adjusting Past Out of Order ", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "ERROR:Error while adjusting Past Out of Order", e);
        }
    }

    @SuppressWarnings("unchecked")
    private int handleFutureOutOfOrder(Map<String, Integer> dataLoadMap, LocalDate btDate) {
        try {
            return crudService.executeUpdateByNativeQuery(GET_FUTURE_ROOM_TYPE_OOO_LIST + DELETE_FUTURE_ROOM_TYPE_OOO + INSERT_FUTURE_ROOM_TYPE_OOO,
                    QueryParameter.with("businessDate", btDate).and("dataLoadIDList", dataLoadMap.get("CTAT")).parameters());
        } catch (Exception e) {
            LOGGER.error("Error while adjusting Future Out of Order ", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "ERROR:Error while adjusting Future Out of Order", e);
        }
    }

    private int updateAdjustedintoStageOccupancy(LocalDate btDate, Integer ptatDataLoadId) {
        try {
            int rowsUpdated = 0;
            rowsUpdated = crudService.executeUpdateByNativeQuery(UPDATE_OOO_STAGE_OCCUPANCY,
                    QueryParameter.with("businessDate", btDate).parameters());
            String queryStr = new StringBuilder("update opera.stage_occupancy_summary set Out_Of_Order_Rooms = 0 ")
                    .append(" where Data_Load_Metadata_ID = :ptatDataLoadId")
                    .append(" and Physical_Rooms = 0").toString();
            crudService.executeUpdateByNativeQuery(queryStr, QueryParameter.with("ptatDataLoadId", ptatDataLoadId).parameters());
            return rowsUpdated;
        } catch (Exception e) {
            LOGGER.error("Error while updating Out of Order Count in Stage Occupancy Summary ", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "ERROR:Error while updating Out of Order Count in Stage Occupancy Summary ", e);
        }
    }

    @SuppressWarnings("unchecked")
    private int fillDefaultOutOfOrder(List<Integer> ptatCtatPsatCsatDataLoadIdList, Object btDate) {
        try {
            return crudService.executeUpdateByNativeQuery(GET_PAST_ROOM_TYPE_OCCUPANCY_DATE_LIST,
                    QueryParameter.with("dataLoadMetadataId", ptatCtatPsatCsatDataLoadIdList)
                            .and("businessDt", btDate)
                            .parameters());
        } catch (Exception e) {
            LOGGER.error("Error while filling Default  Out of Order Count  ", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "ERROR:Error while filling Default  Out of Order Count ", e);
        }
    }

    private int adjustOutOfOrderAtEachOccupancyDate(int totalAdjusted, Object[] dataRow, String startDate, String endDate) {
        try {
            List<Object[]> getOccDateAccomTypeCapacityResults = crudService.findByNativeQuery(
                    GET_OCCCUPANCY_DATE_ACCOMTYPE_CAPACITY_BY_OCCUPANCY_DATE,
                    QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
            Date currOccDate = null;
            int currHotelCapacity = 0;
            int currHotelOOO = 0;
            int i = 0;
            List<Object[]> occDateList = new ArrayList<Object[]>();
            for (Object[] operaOccupationSummary : getOccDateAccomTypeCapacityResults) {
                i++;
                Date occupancyDate = getDateFromString(String.valueOf(operaOccupationSummary[OCC_DATE]));
                if (null == currOccDate) {
                    // case when starting the adjusted OOO for first record
                    currOccDate = occupancyDate;
                    currHotelCapacity = Integer.parseInt(operaOccupationSummary[2].toString());
                    currHotelOOO = Integer.parseInt(operaOccupationSummary[3].toString());
                    addRoomTypeRecorsforAdjustment(occDateList, operaOccupationSummary);
                } else if (DateUtils.isSameDay(currOccDate, occupancyDate)) {
                    addRoomTypeRecorsforAdjustment(occDateList, operaOccupationSummary);
                } else {
                    assignCurrentOccupancyDateValues(dataRow, currOccDate, currHotelCapacity, currHotelOOO);
                    totalAdjusted += calculateAtEachOccDate(totalAdjusted, dataRow, occDateList);
                    currOccDate = occupancyDate;
                    currHotelCapacity = Integer.parseInt(operaOccupationSummary[2].toString());
                    currHotelOOO = Integer.parseInt(operaOccupationSummary[3].toString());
                    occDateList.clear();
                    addRoomTypeRecorsforAdjustment(occDateList, operaOccupationSummary);
                    dataRow = operaOccupationSummary;
                }
                if (i == getOccDateAccomTypeCapacityResults.size()) {
                    // case when all records are over and last date record needs to be adjusted
                    assignCurrentOccupancyDateValues(dataRow, currOccDate, currHotelCapacity, currHotelOOO);
                    totalAdjusted += calculateAtEachOccDate(totalAdjusted, dataRow, occDateList);
                }
            }
            return totalAdjusted;
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, e.getMessage(), e);
        }
    }

    private void addRoomTypeRecorsforAdjustment(List<Object[]> occDateList, Object[] operaOccupationSummary) {
        if (StringUtils.isNotEmpty((String) operaOccupationSummary[1])) {
            occDateList.add(operaOccupationSummary);
        }
    }

    private void assignCurrentOccupancyDateValues(Object[] dataRow, Date currOccDate, int currHotelCapacity, int currHotelOOO) {
        dataRow[0] = currOccDate;
        dataRow[1] = currHotelCapacity;
        dataRow[2] = currHotelOOO;
    }

    private int calculateAtEachOccDate(int totalAdjusted, Object[] dataRow, List<Object[]> getOccDateAccomTypeCapacityResults) {
        LOGGER.debug("Calculating for Occupancy_Date:" + dataRow[OCC_DATE].toString());
        if (getOccDateAccomTypeCapacityResults.size() > 1 && Integer.parseInt(dataRow[HOTEL_LEVEL_OOO].toString()) > 0) {
            if (Integer.parseInt(dataRow[HOTEL_LEVEL_OOO].toString()) > Integer.parseInt(dataRow[HOTEL_LEVEL_CAPACITY].toString())) {
                dataRow[HOTEL_LEVEL_OOO] = dataRow[HOTEL_LEVEL_CAPACITY];
            }
            totalAdjusted += calculateAdjustedOOO(Integer.parseInt(dataRow[HOTEL_LEVEL_CAPACITY].toString()),
                    Integer.parseInt(dataRow[HOTEL_LEVEL_OOO].toString()), getOccDateAccomTypeCapacityResults, true);
        } else {
            totalAdjusted += calculateAdjustedOOO(Integer.parseInt(dataRow[HOTEL_LEVEL_CAPACITY].toString()),
                    Integer.parseInt(dataRow[HOTEL_LEVEL_OOO].toString()), getOccDateAccomTypeCapacityResults, false);
        }
        return totalAdjusted;
    }

    private int calculateAdjustedOOO(int physicalRooms, int rawOORooms, List<Object[]> getOccDateAccomTypeCapacityResults, boolean adjust) {
        int distributedOOOCount = 0;
        int updatedCnt = 0;
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        int[] batchUpdatedCnt;
        try {
            connection = getJDBCConnection();
            preparedStatement = connection.prepareStatement(UPDATE_ADJUSTED_OOO);
            for (Object[] operaOccupationSummary : getOccDateAccomTypeCapacityResults) {
                Date occupancyDate = getDateFromString(String.valueOf(operaOccupationSummary[OCC_DATE]));
                int accomLevelCapacity = Integer.parseInt(operaOccupationSummary[ACCOM_LEVEL_CAPACITY].toString());
                if (adjust) {
                    if (distributedOOOCount < rawOORooms) {
                        int adjustedOOO = (int) Math.ceil((float) accomLevelCapacity / (float) physicalRooms * rawOORooms);
                        if (adjustedOOO > accomLevelCapacity) {
                            // Case where adjusted OO is exceeding the
                            adjustedOOO = accomLevelCapacity;
                        }
                        if (distributedOOOCount + adjustedOOO > rawOORooms) {
                            // Case where calculated OO is exceeding the total hotel level OO
                            adjustedOOO = rawOORooms - distributedOOOCount;
                        }
                        distributedOOOCount = distributedOOOCount + adjustedOOO;
                        preparedStatement.setInt(1, adjustedOOO);
                        preparedStatement.setString(2, String.valueOf(operaOccupationSummary[ROOM_TYPE]));
                        preparedStatement.setDate(3, new java.sql.Date(occupancyDate.getTime()));
                    }
                } else {
                    preparedStatement.setInt(1, rawOORooms);
                    preparedStatement.setString(2, String.valueOf(operaOccupationSummary[1]));
                    preparedStatement.setDate(3, new java.sql.Date(occupancyDate.getTime()));
                }
                preparedStatement.addBatch();
            }
            batchUpdatedCnt = preparedStatement.executeBatch();
            for (int element : batchUpdatedCnt) {
                updatedCnt += element;
            }
            return updatedCnt;
        } catch (Exception e) {
            LOGGER.error("Error while adjusting Out of Order Count  ", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "ERROR:Error while adjusting Out of Order Count  ", e);
        } finally {
            if (null != preparedStatement) {
                try {
                    preparedStatement.close();
                } catch (Exception ignore) {
                    LOGGER.error("Could not close prepared statement.", ignore);
                }
            }
            if (null != connection) {
                try {
                    jpaJdbcUtil.closeConnection(crudService, connection);
                } catch (Exception ignore) {
                    LOGGER.error("Could not close connection.", ignore);
                }
            }
        }
    }

    private Date getDateFromString(String dateToConvert) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateInString = String.valueOf(dateToConvert);
        return formatter.parse(dateInString);
    }

    private Connection getJDBCConnection() {
        return jpaJdbcUtil.getJdbcConnection(crudService);
    }
}

