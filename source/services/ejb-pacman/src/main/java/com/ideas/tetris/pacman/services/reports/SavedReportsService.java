package com.ideas.tetris.pacman.services.reports;

import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.dto.SavedReportsDTO;
import com.ideas.tetris.pacman.services.reports.entity.SavedReports;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

@Component
@Transactional
public class SavedReportsService {

    private static final Logger LOGGER = Logger.getLogger(SavedReportsService.class.getName());
    private static final String ERROR_MESSAGE_SAVE_REPORT_ERROR_OCCURRED = "Save Report - Error Occurred ..";
    private static final String REPORT_PARAM_VALUE_SEPARATOR = "=";
    public static final String SAVE_PARAM_SEPARATOR = "##";
    private static final String DEFAULT_DATE = "1970010100000";

    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    CrudService tenantCrudService;

    @Autowired
    private PacmanConfigParamsService pacmanConfigService;

    @Autowired
    private UserService userService;


    public List<SavedReportsDTO> getSavedReports() {
        List<SavedReports> savedReports = tenantCrudService.findByNativeQuery("Select * From SavedReports where propertyId=:propertyId",
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters(), SavedReports.class);
        return convertToDTOList(savedReports);
    }

    public int deleteSavedReportById(Integer id) {
        return tenantCrudService.executeUpdateByNativeQuery("DELETE FROM SavedReports WHERE ID IN ( :ids)", QueryParameter.with("ids", id).parameters());
    }

    public SavedReports getSavedReportsById(int id) {
        return tenantCrudService.find(SavedReports.class, id);
    }

    public void setCrudService(CrudService crudService) {
        this.tenantCrudService = crudService;
    }

    public boolean enableSaveConfigInReports() {
        return pacmanConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_SAVE_CONFIG_IN_REPORTS);
    }

    public SavedReportsDTO getSavedReport(int id) {
        SavedReports saveReport = getSavedReportsById(id);
        return convertToDTO(saveReport);
    }

    private SavedReportsDTO convertToDTO(SavedReports savedReports) {
        SavedReportsDTO dto = new SavedReportsDTO();
        dto.setId(savedReports.getId());
        dto.setActualReportName(savedReports.getActualReportName());
        dto.setReportParameters(savedReports.getReportParameters());
        Object reportParameters = savedReports.getReportParameters();
        dto.setReportParamsObject(reportParameters);
        dto.setPageCode(savedReports.getPageCode());
        dto.setName(savedReports.getName());
        dto.setCreatedByUserId(savedReports.getCreatedByUserId());
        dto.setCreatedDate(savedReports.getCreateDate());
        dto.setLastUpdatedDate(savedReports.getLastUpdatedDate());
        dto.setLastUpdatedByUser(savedReports.getLastUpdatedByUserId());
        dto.setPageCode(savedReports.getPageCode());
        return dto;
    }

    public boolean saveReport(SavedReportsDTO saveReportDTO) {
        try {
            boolean isNameChanged = true;
            if (saveReportDTO.getId() != null && saveReportDTO.getId() != 0) {
                List<SavedReports> savedReports = tenantCrudService.findByNamedQuery(SavedReports.BY_ID,
                        QueryParameter.with("id", saveReportDTO.getId()).parameters());
                if (savedReports.get(0).getName().equalsIgnoreCase(saveReportDTO.getName())) {
                    isNameChanged = false;
                }
            }
            if (isNameChanged && isReportExistsWithName(saveReportDTO.getName())) {
                return false;
            }
            populateSaveReport(saveReportDTO);
        } catch (Exception e) {
            LOGGER.error(ERROR_MESSAGE_SAVE_REPORT_ERROR_OCCURRED, e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, ERROR_MESSAGE_SAVE_REPORT_ERROR_OCCURRED, e);
        }
        return true;
    }

    public void populateSaveReport(SavedReportsDTO savedReportsDTO) {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        int userId = Integer.parseInt(workContext.getUserId());
        SavedReports savedReports = new SavedReports();
        if (savedReportsDTO.getId() == 0) {
            savedReports.setCreatedByUserId(userId);
            savedReports.setCreateDate(LocalDateTime.now());
            savedReports.setPropertyId(workContext.getPropertyId());
        } else {
            savedReports = getSavedReportsById(savedReportsDTO.getId());
        }
        assignBasicSaveInfo(savedReportsDTO, savedReports, userId);
        saveReportDetails(savedReports);
    }

    private boolean isReportExistsWithName(String reportName) {
        List<SavedReports> savedReports = tenantCrudService.findByNamedQuery(SavedReports.BY_NAME,
                QueryParameter.with("name", reportName).parameters());
        return !savedReports.isEmpty();
    }

    private void assignBasicSaveInfo(SavedReportsDTO savedReportsDTO, SavedReports savedReports, int userId) {
        savedReports.setActualReportName(savedReportsDTO.getActualReportName());
        savedReports.setName(savedReportsDTO.getName());
        savedReports.setReportParameters(getReportParameters(savedReportsDTO));
        savedReports.setPageCode(savedReportsDTO.getPageCode());
        savedReports.setLastUpdatedByUserId(userId);
        savedReports.setLastUpdatedDate(LocalDateTime.now());
    }

    private String getReportParameters(SavedReportsDTO savedReportsDTO) {
        StringBuilder params = new StringBuilder();
        Map<String, String> hm = (Map<String, String>) savedReportsDTO.getReportParamsObject();
        Set<Map.Entry<String, String>> entrySet = hm.entrySet();
        for (Map.Entry<String, String> entry : entrySet) {
            params.append(entry.getKey()).append(REPORT_PARAM_VALUE_SEPARATOR);
            if (reportParamContainsOnlyDateObject(entry)) {
                params.append(entry.getValue() != null ? getFormattedDate(entry.getValue()) : DEFAULT_DATE);
            } else {
                params.append(entry.getValue());
            }
            params.append(SAVE_PARAM_SEPARATOR);
        }
        return params.toString();
    }

    private Date getFormattedDate(String date) {
        try {
            return new SimpleDateFormat("yyyyMMdd").parse(date.substring(0, 8));
        } catch (ParseException e) {
            LOGGER.error("Unable to parse date: " + date, e);
        }
        return null;
    }

    private boolean reportParamContainsOnlyDateObject(Map.Entry<String, String> entry) {
        return entry.getKey().contains("Date") && !entry.getKey().contains("Rolling") && !entry.getKey().contains("userDateFormat") && !entry.getKey().contains("param_calculatePaceFromEndDate");
    }

    public SavedReports saveReportDetails(SavedReports savedReports) {
        return tenantCrudService.save(savedReports);
    }

    public List<SavedReportsDTO> convertToDTOList(List<SavedReports> reports) {
        List<SavedReportsDTO> savedReportsDTOS = new ArrayList<>();
        for (SavedReports report : reports) {
            savedReportsDTOS.add(convertToDTO(report));
        }
        return savedReportsDTOS;
    }

    public boolean hasPermission(String userId, Integer propertyId, String permission) {
        Set<String> userPagePermissions = userService.getAuthorizedPagePermissionsForProperty(userId, propertyId.toString());
        return userPagePermissions.stream().anyMatch(perm -> perm.contains(permission) || perm.contains(Role.ALL_PERMS_ID));
    }
}
