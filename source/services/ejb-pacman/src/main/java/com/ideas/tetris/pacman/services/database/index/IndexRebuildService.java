package com.ideas.tetris.pacman.services.database.index;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.database.index.entity.CommandLog;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.getPropertyId;


@Component
@Transactional
public class IndexRebuildService {
    private static final Logger LOGGER = Logger.getLogger(IndexRebuildService.class.getName());
    public static final int LAST_SEVEN_DAYS = -7;

    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    @Autowired
	private PropertyService propertyService;

    @Autowired
	private JobServiceLocal jobServiceLocal;


    @Transactional(propagation = Propagation.NEVER)

    public String updateIndexes() {
        String propertyIdString = "[" + StringUtils.leftPad(getPropertyId(true).toString(), 6, '0') + "]";
        LOGGER.info("Beginning the rebuild of Indexes for propertyID " + getPropertyId());
        String proc = "exec dbo.IndexOptimize_result :Databases";
        Query nativeQuery = crudService.getEntityManager().createNativeQuery(proc);
        nativeQuery.setParameter("Databases", propertyIdString);
        nativeQuery.getResultList();
        LOGGER.info(propertyIdString + "Completed the rebuild of Indexes for propertyID " + getPropertyId());
        return "Completed the rebuild of Indexes for property id " + getPropertyId();
    }

    public void deleteOldCommandLogEntries() {
        crudService.executeUpdateByNamedQuery(CommandLog.DELETE_OLD_ENTRIES,
                QueryParameter.with("date", DateUtils.addDays(new Date(), LAST_SEVEN_DAYS)).parameters());
    }

    public boolean isPerTenantIndexRebuildConfigured() {
        return propertyService.isPropertyConfiguredToRebuildIndexes();
    }

    public Long startIndexRebuildJob() {
        Property property = propertyService.getPropertyById(getPropertyId(true));
        Map<String, Object> jobParameters = new HashMap<>();
        jobParameters.put(JobParameterKey.PROPERTY_ID, property.getId());
        jobParameters.put(JobParameterKey.PROPERTY_CODE, property.getCode());
        jobParameters.put(JobParameterKey.CLIENT_CODE, property.getClient().getCode());
        jobParameters.put(JobParameterKey.DATE_START, new Date());
        jobParameters.put(JobParameterKey.DATE, new Date());
        return jobServiceLocal.startJob(JobName.BDEPostProcessingIndexRebuildJob, jobParameters);
    }

}
