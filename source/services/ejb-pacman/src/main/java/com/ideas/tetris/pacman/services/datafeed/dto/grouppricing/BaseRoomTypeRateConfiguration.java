package com.ideas.tetris.pacman.services.datafeed.dto.grouppricing;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.GroupPricingBaseAccomType;
import com.ideas.tetris.platform.common.rest.unmarshaller.DateSerializer;

import java.util.Date;

/**
 * Created by idnekp on 3/4/2016.
 */
public class BaseRoomTypeRateConfiguration extends GroupPricingBaseAccomType {

    private String roomTypeCode;
    private String groupPricingCategory;
    @JsonSerialize(using = DateSerializer.class)
    private Date seasonStartDate;
    @JsonSerialize(using = DateSerializer.class)
    private Date seasonEndDate;

    public String getRoomTypeCode() {
        return roomTypeCode;
    }

    public void setRoomTypeCode(String roomTypeCode) {
        this.roomTypeCode = roomTypeCode;
    }

    public String getGroupPricingCategory() {
        return groupPricingCategory;
    }

    public void setGroupPricingCategory(String groupPricingCategory) {
        this.groupPricingCategory = groupPricingCategory;
    }

    public Date getSeasonStartDate() {
        return seasonStartDate;
    }

    public void setSeasonStartDate(Date seasonStartDate) {
        this.seasonStartDate = seasonStartDate;
    }

    public Date getSeasonEndDate() {
        return seasonEndDate;
    }

    public void setSeasonEndDate(Date seasonEndDate) {
        this.seasonEndDate = seasonEndDate;
    }
}
