package com.ideas.tetris.pacman.services.reports;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.JobViewCriteria;
import com.ideas.tetris.pacman.services.job.entity.JobView;
import com.ideas.tetris.pacman.services.reports.entity.ScheduleReport;
import com.ideas.tetris.pacman.services.reports.userreport.ScheduledReportAuditExecutionType;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ScheduledReportException;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.reports.ReportSource;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class ScheduledReportImmediateDeliveryService {

    @Autowired
    JasperRESTExecutionService jasperRESTExecutionService;
    @Autowired
    ScheduledReportAuditService scheduledReportAuditService;
    @Autowired
	private JobServiceLocal jobServiceLocal;
    @Autowired
	private JobMonitorService jobMonitorService;
    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    @Autowired
    ReportsG3Service reportsG3Service;

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public CrudService getCrudService() {
        return crudService;
    }

    private static final Logger LOGGER = Logger.getLogger(ScheduledReportImmediateDeliveryService.class.getName());

    public void startingImmediateDeliveryJob(int scheduleReportId, long businessDate) {
        jobServiceLocal.startJob(JobName.ScheduledReportImmediateDeliveryJob,
                MapBuilder
                        .with(JobParameterKey.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(JobParameterKey.BUSINESS_DATE, businessDate)
                        .and(JobParameterKey.SCHEDULE_REPORT_ID, scheduleReportId)
                        .get());
    }

    public void generateImmediateReport(ScheduleReport scheduleReport, Date businessDate) {
        try {
            String fileName = jasperRESTExecutionService.getReportFileName(scheduleReport);
            reportsG3Service.generateScheduledReport(jasperRESTExecutionService.createReportParameterMap(scheduleReport.getRportParameters(), scheduleReport.getJasperReportURI(), scheduleReport.getOutputFormat()),
                    jasperRESTExecutionService.getLocalFileSourceFolderPath(scheduleReport.getPropertyId(), ReportSource.IMMEDIATE), fileName);
            jasperRESTExecutionService.deliverReport(scheduleReport, fileName, ReportSource.IMMEDIATE);
            scheduledReportAuditService.createAuditEntry(scheduleReport.getId(), ScheduledReportAuditStatus.SUCCESS,
                    scheduleReport.getActualReportName(), businessDate, null, ScheduledReportAuditExecutionType.IMMEDIATE);
        } catch (ScheduledReportException scheduledReportException) {
            LOGGER.error("Exception details : " + scheduledReportException.getMessage() + " " + scheduledReportException.getErrorCode() + " " + scheduledReportException);
            throw new TetrisException(scheduledReportException.getErrorCode(), scheduledReportException.getMessage(), scheduledReportException);
        }
    }

    public ScheduleReport loadScheduleById(int id) {
        return crudService.findByNamedQuerySingleResult(ScheduleReport.BY_SCHEDULE_ID, QueryParameter.with("id", id).parameters());
    }

    public Set<Integer> getScheduleIdsForActiveSendImmediatelyJob() {
        Set<Integer> scheduleIds = null;
        JobViewCriteria criteria = new JobViewCriteria();
        criteria.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        criteria.setActiveStatuses();
        criteria.setJobNames(Collections.singletonList(JobName.ScheduledReportImmediateDeliveryJob.toString()));
        final List<JobView> jobs = jobMonitorService.getJobs(criteria);
        scheduleIds = jobs.stream()
                .map(jobView -> jobView.getExecutionParameter(JobParameterKey.SCHEDULE_REPORT_ID))
                .map(Integer::parseInt)
                .collect(Collectors.toSet());
        return scheduleIds;

    }
}
