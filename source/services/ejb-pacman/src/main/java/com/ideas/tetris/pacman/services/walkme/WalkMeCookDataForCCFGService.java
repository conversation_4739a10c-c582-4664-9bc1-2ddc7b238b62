package com.ideas.tetris.pacman.services.walkme;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.DateTime;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.Date;

import static com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService.ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
public class WalkMeCookDataForCCFGService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    @Autowired
    WalkMeCookDataService walkMeCookDataService;

    @Autowired
    PacmanConfigParamsService configParamsServiceLocal;

    public void addMarketSegmentAttribution() {
        attributeMktSeg("Walk Me Group Market Segment", "Walk Me Group Market Segment", AnalyticalMarketSegmentAttribute.GROUP);
        attributeMktSeg("Walk Me Barter", "Walk Me Barter", AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
        attributeMktSeg("Walk Me Discount", "Walk Me Discount", AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_SEMIYIELDABLE);
        attributeMktSeg("Walk Me Rack", "Walk Me Rack", AnalyticalMarketSegmentAttribute.TRANSIENT_BLOCK_NON_LINKED);
    }

    public void addMarketSegmentMaster() {
        tenantCrudService.executeUpdateByNativeQuery("INSERT INTO [dbo].[Mkt_Seg_Master] VALUES ('Walk Me Group Market Segment','Walk Me Group Market Segment','Walk Me Group Market Segment',1,1,1,1,0,0,0,0,100,1,null);\n" +
                "INSERT INTO [dbo].[Mkt_Seg_Master] VALUES ('Walk Me Barter','Walk Me Barter','Walk Me Barter',2,1,1,0,0,0,0,1,0,1,null);\n" +
                "INSERT INTO [dbo].[Mkt_Seg_Master] VALUES ('Walk Me Discount','Walk Me Discount','Walk Me Discount',2,2,1,1,0,0,0,0,0,1,null);\n" +
                "INSERT INTO [dbo].[Mkt_Seg_Master] VALUES ('Walk Me Rack','Walk Me Rack','Walk Me Rack',2,1,2,1,0,0,0,0,0,1,null);");
    }

    public void addMktSegDetailsProposed() {
        tenantCrudService.executeUpdateByNativeQuery("INSERT INTO [dbo].[Mkt_Seg_Details_Proposed] VALUES((select mkt_seg_id from Mkt_Seg where Mkt_Seg_Code = 'Walk Me Group Market Segment'),1,1,1,1,100,0,0,0,0,0,10,null,null,1,GETDATE(),0,11403,GETDATE(),11403);\n" +
                "INSERT INTO [dbo].[Mkt_Seg_Details_Proposed] VALUES((select mkt_seg_id from Mkt_Seg where Mkt_Seg_Code = 'Walk Me Barter'),2,1,1,0,0,0,0,0,0,0,10,null,null,1,GETDATE(),1,11403,GETDATE(),11403);\n" +
                "INSERT INTO [dbo].[Mkt_Seg_Details_Proposed] VALUES((select mkt_seg_id from Mkt_Seg where Mkt_Seg_Code = 'Walk Me Discount'),2,2,1,1,0,0,0,0,0,0,10,null,null,1,GETDATE(),0,11403,GETDATE(),11403);\n" +
                "INSERT INTO [dbo].[Mkt_Seg_Details_Proposed] VALUES((select mkt_seg_id from Mkt_Seg where Mkt_Seg_Code = 'Walk Me Rack'),2,1,2,1,100,0,0,0,0,0,10,null,null,1,GETDATE(),0,11403,GETDATE(),11403);");
    }

    public void setAMSToTrue() {
        configParamsServiceLocal.addParameterValue(ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE, Boolean.TRUE.toString());
    }

    private void attributeMktSeg(String marketCode, String mappedMarketCode, AnalyticalMarketSegmentAttribute attribute) {
        AnalyticalMarketSegment analyticalMarketSegment = new AnalyticalMarketSegment();
        analyticalMarketSegment.setMarketCode(marketCode);
        analyticalMarketSegment.setMappedMarketCode(mappedMarketCode);
        analyticalMarketSegment.setAttribute(attribute);
        analyticalMarketSegment.setCreatedByUserId(11403);
        analyticalMarketSegment.setCreateDate(new Date());
        analyticalMarketSegment.setLastUpdatedByUserId(11403);
        analyticalMarketSegment.setLastUpdatedDate(new Date());
        analyticalMarketSegment.setRateCodeType(RateCodeTypeEnum.ALL);
        analyticalMarketSegment.setRank(1);
        tenantCrudService.save(analyticalMarketSegment);
    }

    public void addRequirementsForPaceDataAndHistoricalData() {
        disableLimitedDataBuild();
        disableAMSRebuild();
        addRequirementsForNumberOfFullPushExtracts();
        addRequirementsForDataLessThanMinDaysOfHistoricalData();
    }

    public void addRequirementsForNumberOfFullPushExtracts() {
        FileMetadata fileMetadata = tenantCrudService.findByNamedQuerySingleResult(FileMetadata.BY_FILE_NAME_AND_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("name", "UNASSIGNED").parameters());
        fileMetadata.setRecordTypeId(3);
        tenantCrudService.save(fileMetadata);
        setPaceDataAvailabilitydays();
    }

    public void addRequirementsForDataLessThanMinDaysOfHistoricalData() {
        disableCreateFGTransactionValidation();
        addRequirementsForGetExtractInfoForNHSSPProperties();
    }

    public void addRequirementsForGetExtractInfoForNHSSPProperties() {
        setHistoricalDataAvailabilityDays();
        DateTime dateTime = new DateTime();
        FileMetadata fileMetadata = tenantCrudService.findByNamedQuerySingleResult(FileMetadata.BY_FILE_NAME_AND_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("name", "UNASSIGNED").parameters());
        TotalActivity totalActivity = new TotalActivity();
        totalActivity.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        totalActivity.setTotalAccomCapacity(BigDecimal.valueOf(0));
        totalActivity.setRoomsSold(BigDecimal.valueOf(0));
        totalActivity.setRoomsNotAvailableMaintenance(BigDecimal.valueOf(0));
        totalActivity.setRoomsNotAvailableOther(BigDecimal.valueOf(0));
        totalActivity.setArrivals(BigDecimal.valueOf(0));
        totalActivity.setDepartures(BigDecimal.valueOf(0));
        totalActivity.setCancellations(BigDecimal.valueOf(0));
        totalActivity.setNoShows(BigDecimal.valueOf(0));
        totalActivity.setSnapShotDate(dateTime.toDate());
        totalActivity.setOccupancyDate(dateTime.toDate());
        totalActivity.setFileMetadataId(fileMetadata.getId());
        totalActivity.setRoomRevenue(BigDecimal.valueOf(0));
        totalActivity.setFoodRevenue(BigDecimal.valueOf(0));
        totalActivity.setTotalRevenue(BigDecimal.valueOf(0));
        tenantCrudService.save(totalActivity);
    }

    public void setHistoricalDataAvailabilityDays() {
        configParamsService.updateParameterValue(walkMeCookDataService.getContextPath(), IPConfigParamName.CORE_HISTORICAL_DATA_AVAILABILITY_DAYS.value(), "0");
    }

    public void setPaceDataAvailabilitydays() {
        configParamsService.updateParameterValue(walkMeCookDataService.getContextPath(), IPConfigParamName.CORE_PACE_DATA_AVAILABILITY_DAYS.value(), "0");
    }

    public void disableLimitedDataBuild() {
        configParamsService.updateParameterValue(walkMeCookDataService.getContextPath(), IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value(), Constants.FALSE);
    }

    public void disableAMSRebuild() {
        configParamsService.updateParameterValue(walkMeCookDataService.getContextPath(), FeatureTogglesConfigParamName.AMS_AMS_REBUILD_ENABLED.value(), Constants.FALSE);
    }

    public void disableCreateFGTransactionValidation() {
        configParamsService.updateParameterValue(walkMeCookDataService.getContextPath(), FeatureTogglesConfigParamName.CREATE_FGTRANSCACTION_VALIDATION_ENABLED.value(), Constants.FALSE);
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }
}
