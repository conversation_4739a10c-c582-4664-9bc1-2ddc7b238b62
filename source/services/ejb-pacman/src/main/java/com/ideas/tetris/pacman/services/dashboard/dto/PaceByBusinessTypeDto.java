package com.ideas.tetris.pacman.services.dashboard.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideas.tetris.pacman.services.reports.performancecomparison.BusinessTypeEnum;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Key;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.MultiPropertyAggregate;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Sum;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

@MultiPropertyAggregate
public class PaceByBusinessTypeDto {

    @Key
    private Integer propertyId;

    @Key
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    private Date occupancyDate;

    @Key
    private Date businessDayEndDate;

    @Key
    private Integer daysToArrival;

    @Key
    private Integer businessTypeId;  //  0 = Total of Group + Transient, 1 = Group, 2 = Transient

    @Sum(scale = 0)
    private BigDecimal roomsSold;

    @Sum(scale = 2)
    private BigDecimal roomRevenue;

    @Sum(scale = 0)
    private BigDecimal soldCount;

    @Sum(scale = 0)
    private BigDecimal roomsSoldFromComparison;

    @Sum(scale = 2)
    private BigDecimal roomRevenueFromComparison;

    @Sum(scale = 2)
    private BigDecimal soldCountFromComparison;

    @Sum(scale = 2)
    private BigDecimal occupancyNumber;

    @Sum(scale = 2)
    private BigDecimal forecastRevenue;

    @Sum(scale = 2)
    private BigDecimal occupancyNumberFromComparison;

    @Sum(scale = 2)
    private BigDecimal forecastRevenueFromComparison;

    @Sum(scale = 0)
    private BigDecimal forecastCount;

    @Sum(scale = 0)
    private BigDecimal budgetedRoomsSold;

    @Sum(scale = 2)
    private BigDecimal budgetedRoomRevenue;

    private String propertyCode;

    public PaceByBusinessTypeDto() {
        super();
    }

    public PaceByBusinessTypeDto(PaceByBusinessTypeDto paceByBusinessTypeDto) {
        propertyId = paceByBusinessTypeDto.propertyId;
        occupancyDate = paceByBusinessTypeDto.occupancyDate;
        businessDayEndDate = paceByBusinessTypeDto.businessDayEndDate;
        daysToArrival = paceByBusinessTypeDto.daysToArrival;
        businessTypeId = paceByBusinessTypeDto.businessTypeId;
        roomsSold = paceByBusinessTypeDto.roomsSold;
        roomRevenue = paceByBusinessTypeDto.roomRevenue;
        soldCount = paceByBusinessTypeDto.soldCount;
        roomsSoldFromComparison = paceByBusinessTypeDto.roomsSoldFromComparison;
        roomRevenueFromComparison = paceByBusinessTypeDto.roomRevenueFromComparison;
        soldCountFromComparison = paceByBusinessTypeDto.soldCountFromComparison;
        occupancyNumber = paceByBusinessTypeDto.occupancyNumber;
        forecastRevenue = paceByBusinessTypeDto.forecastRevenue;
        occupancyNumberFromComparison = paceByBusinessTypeDto.occupancyNumberFromComparison;
        forecastRevenueFromComparison = paceByBusinessTypeDto.forecastRevenueFromComparison;
        forecastCount = paceByBusinessTypeDto.forecastCount;
        budgetedRoomsSold = paceByBusinessTypeDto.budgetedRoomsSold;
        budgetedRoomRevenue = paceByBusinessTypeDto.budgetedRoomRevenue;
        propertyCode = paceByBusinessTypeDto.propertyCode;
    }

    public String getPropertyCode() {
        return propertyCode;
    }

    public void setPropertyCode(String propertyCode) {
        this.propertyCode = propertyCode;
    }

    public BigDecimal getRoomsSold() {
        return roomsSold;
    }

    public void setRoomsSold(BigDecimal roomsSold) {
        this.roomsSold = roomsSold;
    }

    public BigDecimal getAdr() {
        if (isZeroOrNull(roomsSold) || isZeroOrNull(roomRevenue)) {
            return BigDecimal.ZERO;
        }
        return roomRevenue.divide(roomsSold, 2, RoundingMode.HALF_UP);
    }

    public BigDecimal getRoomRevenue() {
        return roomRevenue;
    }

    public void setRoomRevenue(BigDecimal roomRevenue) {
        this.roomRevenue = roomRevenue;
    }

    // From Comparison period
    public BigDecimal getRoomsSoldFromComparison() {
        return roomsSoldFromComparison;
    }

    public void setRoomsSoldFromComparison(BigDecimal roomsSoldFromComparison) {
        this.roomsSoldFromComparison = roomsSoldFromComparison;
    }

    public BigDecimal getAdrFromComparison() {
        if (isZeroOrNull(roomsSoldFromComparison) || isZeroOrNull(roomRevenueFromComparison)) {
            return BigDecimal.ZERO;
        }
        return roomRevenueFromComparison.divide(roomsSoldFromComparison, 2, RoundingMode.HALF_UP);
    }

    public BigDecimal getRoomRevenueFromComparison() {
        return roomRevenueFromComparison;
    }

    public void setRoomRevenueFromComparison(BigDecimal roomRevenueFromComparison) {
        this.roomRevenueFromComparison = roomRevenueFromComparison;
    }

    private boolean isZeroOrNull(BigDecimal value) {
        return value == null || isZero(value);
    }

    private boolean isZero(BigDecimal value) {
        return value.signum() == 0;
    }

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public Integer getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Integer propertyId) {
        this.propertyId = propertyId;
    }

    public Integer getDaysToArrival() {
        return daysToArrival;
    }

    public void setDaysToArrival(Integer daysToArrival) {
        this.daysToArrival = daysToArrival;
    }

    public Date getBusinessDayEndDate() {
        return businessDayEndDate;
    }

    public void setBusinessDayEndDate(Date businessDayEndDate) {
        this.businessDayEndDate = businessDayEndDate;
    }

    public Integer getBusinessTypeId() {
        return businessTypeId;
    }

    public void setBusinessTypeId(Integer businessTypeId) {
        this.businessTypeId = businessTypeId;
    }

    public String getBusinessTypeName() {
        return (businessTypeId == null || businessTypeId == 0 ? "Total" : BusinessTypeEnum.getById(businessTypeId).getCaption());
    }

    public BigDecimal getSoldCount() {
        return soldCount;
    }

    public void setSoldCount(BigDecimal soldCount) {
        this.soldCount = soldCount;
    }

    public BigDecimal getOccupancyNumber() {
        return occupancyNumber;
    }

    public void setOccupancyNumber(BigDecimal occupancyNumber) {
        this.occupancyNumber = occupancyNumber;
    }

    public BigDecimal getForecastRevenue() {
        return forecastRevenue;
    }

    public void setForecastRevenue(BigDecimal forecastRevenue) {
        this.forecastRevenue = forecastRevenue;
    }

    public BigDecimal getOccupancyNumberFromComparison() {
        return occupancyNumberFromComparison;
    }

    public void setOccupancyNumberFromComparison(BigDecimal occupancyNumberFromComparison) {
        this.occupancyNumberFromComparison = occupancyNumberFromComparison;
    }

    public BigDecimal getForecastRevenueFromComparison() {
        return forecastRevenueFromComparison;
    }

    public void setForecastRevenueFromComparison(BigDecimal forecastRevenueFromComparison) {
        this.forecastRevenueFromComparison = forecastRevenueFromComparison;
    }

    public BigDecimal getForecastCount() {
        return forecastCount;
    }

    public void setForecastCount(BigDecimal forecastCount) {
        this.forecastCount = forecastCount;
    }

    public BigDecimal getSoldCountFromComparison() {
        return soldCountFromComparison;
    }

    public void setSoldCountFromComparison(BigDecimal soldCountFromComparison) {
        this.soldCountFromComparison = soldCountFromComparison;
    }

    public BigDecimal getBudgetedRoomsSold() {
        return budgetedRoomsSold;
    }

    public void setBudgetedRoomsSold(BigDecimal budgetedRoomsSold) {
        this.budgetedRoomsSold = budgetedRoomsSold;
    }

    public BigDecimal getBudgetedRoomRevenue() {
        return budgetedRoomRevenue;
    }

    public void setBudgetedRoomRevenue(BigDecimal budgetedRoomRevenue) {
        this.budgetedRoomRevenue = budgetedRoomRevenue;
    }
}
