package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.webrate.dto.DCMPCGenericValuesDTO;
import com.ideas.tetris.pacman.services.webrate.entity.dcmpc.DcmpcCfg;
import com.ideas.tetris.pacman.services.webrate.entity.dcmpc.DcmpcCfgDetail;
import com.ideas.tetris.pacman.services.webrate.entity.dcmpc.DcmpcCfgMapping;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.IdAware;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Transactional
public class DynamicCMPCService {
    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService tenantCrudService;

    private static final String START_DATE = "startDate";
    private static final String END_DATE = "endDate";

    private static final String FETCH_DEFAULT_AND_SEASON_VALUES_BY_PRODUCT_ACCOM_CLASS = "exec dbo.usp_fetch_values_for_occupancy_based_cmpc :productId, :accomClass, :occupancy";
    private static final String FETCH_DEFAULT_AND_SEASON_VALUES_BY_PRODUCT_ACCOM_CLASS_FIRST_HIGHER_TO_OCCUPANCY_PERCENTAGE = "exec dbo.USP_fetch_values_when_occupancy_based_cmpc_is_enabled_first_higher_to_occupancyPercent :productId, :accomClass, :occupancy, :occupancyPercentage";


    public DcmpcCfgDetail getDcmpcDetails(BigDecimal onBooksThreshold, BigDecimal percentile) {
        Map<String, Object> parameters = QueryParameter.with("onBooksThreshold", onBooksThreshold).and("percentile", percentile).parameters();
        return tenantCrudService.findByNamedQuerySingleResult(DcmpcCfgDetail.GET_DCMPC_DETAILS, parameters);
    }

    public List<DcmpcCfgMapping> getAllDefaultConfigMappings() {
        return tenantCrudService.findByNamedQuery(DcmpcCfgMapping.GET_DEFAULT_DCMPC_MAPPINGS);
    }

    public List<DcmpcCfgMapping> getSeasonDcmpcConfigMappings(LocalDate startDate, LocalDate endDate) {
        return tenantCrudService.findByNamedQuery(DcmpcCfgMapping.GET_SEASON_DCMPC_MAPPINGS, QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).parameters());
    }

    public DcmpcCfg getDefaultDcmpcCfgByAccomClassIdAndProductId(Integer accomClassId, Integer productId) {
        return tenantCrudService.findByNamedQuerySingleResult(DcmpcCfg.GET_DEFAULT_DCMPC_CFG_BY_AC_PRODUCT_ID, QueryParameter.with("accomClassId", accomClassId).and("productId", productId).parameters());
    }

    public DcmpcCfg getSeasonalDcmpcCfgByAccomClassIdAndProductId(Integer accomClassId, int productId, LocalDate startDate, LocalDate endDate) {
        return tenantCrudService.findByNamedQuerySingleResult(DcmpcCfg.GET_SEASON_DCMPC_CFG_BY_AC_PRODUCT_ID, QueryParameter.with("accomClassId", accomClassId).and("productId", productId).and(START_DATE, startDate).and(END_DATE, endDate).parameters());
    }

    public DcmpcCfgMapping getDcmpcCfgMapping(Integer dcmpcCfgId, Integer dcmpcCfgDetailId, int dowId) {
        return tenantCrudService.findByNamedQuerySingleResult(DcmpcCfgMapping.GET_UNIQUE_DCMPC_MAPPING, QueryParameter.with("dcmpcCfgId", dcmpcCfgId).and("dcmpcCfgDetailId", dcmpcCfgDetailId).and("dowId", dowId).parameters());
    }

    public DcmpcCfgMapping getDcmpcCfgMapping(Integer mappingId) {
        return tenantCrudService.find(DcmpcCfgMapping.class, mappingId);
    }

    public List<DcmpcCfgMapping> getAllDcmpcCfgMapping() {
        return tenantCrudService.findAll(DcmpcCfgMapping.class);
    }

    public void deleteDcmpcCfgMappings(List<DcmpcCfgMapping> mappingsToDelete) {
        tenantCrudService.delete(mappingsToDelete);
    }

    public List<DcmpcCfgMapping> getSeasonDcmpcCfgMappingsForAccomClass(AccomClass accomClass) {
        return tenantCrudService.findByNamedQuery(DcmpcCfgMapping.GET_SEASON_DCMPC_MAPPINGS_BY_ACCOM_CLASS, QueryParameter.with("accomClass", accomClass).parameters());
    }

    public List<DcmpcCfg> getSeasonalDcmpcCfgByDates(LocalDate startDate, LocalDate endDate) {
        return tenantCrudService.findByNamedQuery(DcmpcCfg.GET_SEASON_DCMPC_CFG_BY_DATES, QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).parameters());
    }

    public List<DcmpcCfgMapping> getDcmpcCfgMappingsByDcmpcCfg(DcmpcCfg dcmpcCfg) {
        return tenantCrudService.findByNamedQuery(DcmpcCfgMapping.GET_DCMPC_MAPPING_BY_CFGID, QueryParameter.with("dcmpcCfg", dcmpcCfg).parameters());
    }

    public void deleteDcmpcCfg(DcmpcCfg dcmpcCfg) {
        tenantCrudService.delete(dcmpcCfg);
    }

    public List<DcmpcCfg> getDcmpcCfg(List<Integer> ids) {
        return tenantCrudService.findByNamedQuery(DcmpcCfg.GET_DCMPC_BY_ID, QueryParameter.with("ids", ids).parameters());
    }

    public <T extends IdAware> void save(Collection<T> ts) {
        tenantCrudService.save(ts);
    }

    public <T extends IdAware> T save(T t) {
        return tenantCrudService.save(t);
    }

    public <T> void merge(T t) {
        tenantCrudService.getEntityManager().merge(t);
    }

    public long getCountOfDcmpcCfgs(List<Integer> productIds) {
        return tenantCrudService.findByNamedQuerySingleResult(DcmpcCfg.GET_DCMPC_COUNT, QueryParameter.with("productIds", productIds).parameters());
    }

    public List<DCMPCGenericValuesDTO> fetchDefaultAndSeasonValues(List<Integer> productIds, List<Integer> accomClassIds, LocalDate occupancyDate){
        return tenantCrudService.findByNativeQuery(FETCH_DEFAULT_AND_SEASON_VALUES_BY_PRODUCT_ACCOM_CLASS,
                QueryParameter.with("productId", productIds.stream().map(String::valueOf).collect(Collectors.joining(",")))
                        .and("accomClass", accomClassIds.stream().map(String::valueOf).collect(Collectors.joining(",")))
                        .and("occupancy", occupancyDate).parameters(), row -> {
            DCMPCGenericValuesDTO dto = new DCMPCGenericValuesDTO();
            dto.setDowId((Byte) row[0]);
            dto.setMaxPercentile((BigDecimal) row[1]);
            dto.setOnBooksThresholdPercent((BigDecimal) row[2]);
            dto.setProductName((String) row[3]);
            dto.setAccomClassName((String) row[4]);
            return dto;
        });
    }

    public List<DCMPCGenericValuesDTO> fetchDefaultAndSeasonValuesFirstHigherToOccupancyPercentage(List<Integer> productIds, List<Integer> accomClassIds, LocalDate occupancyDate, BigDecimal occupancyPercentage){
        return tenantCrudService.findByNativeQuery(FETCH_DEFAULT_AND_SEASON_VALUES_BY_PRODUCT_ACCOM_CLASS_FIRST_HIGHER_TO_OCCUPANCY_PERCENTAGE,
                QueryParameter.with("productId", productIds.stream().map(String::valueOf).collect(Collectors.joining(",")))
                        .and("accomClass", accomClassIds.stream().map(String::valueOf).collect(Collectors.joining(",")))
                        .and("occupancy", occupancyDate)
                        .and("occupancyPercentage", occupancyPercentage).parameters(), row -> {
                    DCMPCGenericValuesDTO dto = new DCMPCGenericValuesDTO();
                    dto.setDowId((Byte) row[0]);
                    dto.setMaxPercentile((BigDecimal) row[1]);
                    dto.setOnBooksThresholdPercent((BigDecimal) row[2]);
                    dto.setProductName((String) row[3]);
                    dto.setAccomClassName((String) row[4]);
                    return dto;
                });
    }
}
