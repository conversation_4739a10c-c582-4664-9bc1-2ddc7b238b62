package com.ideas.tetris.pacman.services.rollback;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.email.EmailService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import javax.inject.Inject;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

import static com.ideas.tetris.pacman.util.POIUtility.autoSizeColumn;
import static com.ideas.tetris.pacman.util.POIUtility.createColorForegroundCellStyle;
import static com.ideas.tetris.pacman.util.POIUtility.createColumn;
import static com.ideas.tetris.pacman.util.POIUtility.createRow;
import static com.ideas.tetris.pacman.util.POIUtility.createSheet;
import static com.ideas.tetris.pacman.util.POIUtility.createWorkbook;
import static com.ideas.tetris.pacman.util.POIUtility.populateRow;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class EmailNotificationService {

    private static final Logger LOGGER = Logger.getLogger(EmailNotificationService.class);
    private static final String SUBJECT = "Analytical Overrides Before Rollback";
    private static final int ROW_0 = 0;
    private static final int ROW_1 = 1;
    private static final String TABLE_NAME = "tableName";
    private static final String TEMP_FOLDER_NAME = "RollbackNotification";
    private static final String EXCEL_FILE_EXTENSION = ".xlsx";
    private static final String EXCEL_FILENAME = "PreRollbackAnalyticalOverrides";
    private static final String UNDERSCORE = "_";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;
    @Autowired
    EmailService emailService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    protected static final String[] analyticalOverrideTablesWithStatusId = new String[]{
            "IP_Cfg_Fcst_Group_Ovrd",
            "Ip_cfg_mark_property_date",
            "IP_Cfg_Runtime_Param_Override",
            "Ovrd_Booking_Curve_Pattern",
            "Ovrd_Noshows",
            "Ovrd_Process_Group_Config",
            "Ovrd_Rate_Forecast",
            "Ovrd_Reference_Price",
            "Ovrd_Special_Event_Pattern"
    };

    protected static final String[] analyticalOverridesTablesWithoutStatusId = new String[]{
            "IP_Cfg_Fcst_Group_Ovrd_Detail"
    };

    public void triggerNotification() {
        String[] emailRecipients = getEmailRecipients();
        if (emailRecipients.length != 0) {
            StringBuilder emailBody = buildEmailBody();
            Workbook workbook = createWorkbook();
            prepareExcelFile(workbook);
            File excelFile = writeToFile(workbook);
            sendEmail(emailBody, emailRecipients, workbook, excelFile);
            FileUtils.deleteQuietly(getTempDirectory());
        }
    }

    private void sendEmail(StringBuilder emailBody, String[] emailRecipients, Workbook workbook, File excelFile) {
        if (workbook.getNumberOfSheets() > 0) {
            Arrays.asList(emailRecipients).stream().forEach(
                    recipient -> emailService.sendText(SystemConfig.getRunTaskEmailFromAddress(), recipient, SUBJECT, emailBody.toString(), Arrays.asList(excelFile)));
        }
    }

    private void prepareExcelFile(Workbook workbook) {
        StringBuilder fetchRecordsQueryBuilder = new StringBuilder("select * from %s");
        String statusIdClause = " where Status_ID = 1";
        writeToWorkbook(analyticalOverridesTablesWithoutStatusId, workbook, fetchRecordsQueryBuilder.toString());
        writeToWorkbook(analyticalOverrideTablesWithStatusId, workbook, fetchRecordsQueryBuilder.append(statusIdClause).toString());
    }

    private void writeToWorkbook(String[] tableSource, Workbook workbook, String fetchRecordsQuery) {
        Arrays.asList(tableSource).stream().forEach(
                table -> {
                    List<Object[]> records = tenantCrudService.findByNativeQuery(String.format(fetchRecordsQuery, table));
                    if (!records.isEmpty()) {
                        List<String> headers = tenantCrudService.findByNativeQuery("select COLUMN_NAME from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME =:tableName",
                                QueryParameter.with(TABLE_NAME, table).parameters());
                        XSSFSheet sheet = getSheet(workbook, table, headers);
                        AtomicInteger rowNum = new AtomicInteger(ROW_1);
                        records.stream().forEach(record -> {
                            XSSFRow row = sheet.createRow(rowNum.getAndIncrement());
                            AtomicInteger columnNo = new AtomicInteger(-1);
                            for (Object cell : record) {
                                String cellValue = null == cell ? StringUtils.EMPTY : cell.toString();
                                createColumn(row, columnNo.incrementAndGet(), cellValue);
                            }
                        });
                        autoSizeColumns(headers.size(), sheet);
                    }
                }
        );
    }

    private File writeToFile(Workbook workbook) {
        File excelFile = new File(buildExcelFileAbsolutePath());
        try {
            FileUtils.cleanDirectory(getTempDirectory());
        } catch (IOException e) {
            LOGGER.error("failed cleaning directory", e);
        }
        try (FileOutputStream outputStream = new FileOutputStream(excelFile)) {
            workbook.write(outputStream);
        } catch (IOException e) {
            LOGGER.error("IO Exception while creating excel file for analytical overrides.", e);
        }
        return excelFile;
    }

    private String buildExcelFileAbsolutePath() {
        return getTempDirectoryPath() + File.separator + EXCEL_FILENAME + UNDERSCORE
                + PacmanWorkContextHelper.getClientCode() + UNDERSCORE + PacmanWorkContextHelper.getPropertyCode() + EXCEL_FILE_EXTENSION;
    }

    private File getTempDirectory() {
        String excelFileTempDirectoryPath = getTempDirectoryPath();
        File excelFileTempDirectory = new File(excelFileTempDirectoryPath);
        if (!excelFileTempDirectory.exists()) {
            excelFileTempDirectory.mkdirs();
        }
        return excelFileTempDirectory;
    }

    private XSSFSheet getSheet(Workbook workbook, String table, List<String> headers) {
        XSSFSheet sheet = createSheet(workbook, table);
        XSSFRow rowHead = createRow(sheet, ROW_0);
        populateRow(rowHead, createColorForegroundCellStyle(workbook, IndexedColors.LIGHT_GREEN.getIndex()), headers.toArray(new String[0]));
        return sheet;
    }

    private String[] getEmailRecipients() {
        String configuredEmailRecipients = configParamsService.getParameterValue(IPConfigParamName.CORE_ROLLBACK_EMAIL_RECIPIENTS.value());
        LOGGER.debug("Configured email recipients :" + configuredEmailRecipients);
        String[] emailRecipients = new String[]{};

        if (null != configuredEmailRecipients && !StringUtils.isEmpty(configuredEmailRecipients)) {
            emailRecipients = configuredEmailRecipients.split(";");
        }
        return emailRecipients;
    }

    private StringBuilder buildEmailBody() {
        StringBuilder emailBody = new StringBuilder();
        emailBody.append("Client: ").append(PacmanWorkContextHelper.getClientCode()).append("\n");
        emailBody.append("Property: ").append(PacmanWorkContextHelper.getPropertyCode());
        return emailBody;
    }

    private String getTempDirectoryPath() {
        return System.getProperty(Constants.TEMP_DIR_PROPERTY_NAME) + File.separator + TEMP_FOLDER_NAME + File.separator + PacmanWorkContextHelper.getPropertyId().toString();
    }

    private void autoSizeColumns(Integer numberOfColumns, XSSFSheet sheet) {
        IntStream.range(0, numberOfColumns).forEach(columnNo -> autoSizeColumn(sheet, columnNo));
    }
}
