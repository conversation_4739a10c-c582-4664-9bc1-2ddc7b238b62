package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.datafeed.dto.PropertyBasicInformation;
import com.ideas.tetris.pacman.services.datafeed.dto.PropertyBasicInformationEnhanced;
import com.ideas.tetris.pacman.services.datafeed.dto.PropertyBasicInformationEnhancedBookedStatus;
import com.ideas.tetris.pacman.services.datafeed.dto.PropertyBasicInformationEnhancedLimitedBuildDate;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.SystemDates;
import com.ideas.tetris.pacman.services.limiteddatabuild.LDBService;
import com.ideas.tetris.pacman.services.property.PropertyRolloutService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.property.dto.PropertySearchCriteria;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTimeZone;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
@Transactional
public class PropertyBasicInformationService {
    private static final Logger LOGGER = Logger.getLogger(PropertyBasicInformationService.class);
    @Autowired
	private PropertyService propertyService;

    @Autowired
	private DateService dateService;
    @Autowired
    @Qualifier("ldbService")
	private LDBService ldbService;

    @Autowired
	private PropertyRolloutService propertyRolloutService;
    @Autowired
	private PacmanConfigParamsService configParamsService;
    private static final String DEFAULT_UTC_TIME_ZONE = "UTC";

    public List<PropertyBasicInformationEnhanced> getEnhancedPropertyBasicInformation() {
        PropertyBasicInformationEnhanced propertyBasicInformationEnhanced = getPropertyBasicInformationEnhanced();
        return Collections.singletonList(propertyBasicInformationEnhanced);
    }

    public List<PropertyBasicInformationEnhancedBookedStatus> getEnhancedPropertyBasicInformationWithBookedStatus() {
        PropertyBasicInformationEnhancedBookedStatus propertyBasicInformationEnhancedBookedStatus = getPropertyBasicInformationEnhancedBookedStatus();
        return Collections.singletonList(propertyBasicInformationEnhancedBookedStatus);
    }

    private PropertyBasicInformationEnhancedBookedStatus getPropertyBasicInformationEnhancedBookedStatus() {
        PropertyBasicInformationEnhanced propertyBasicInformationEnhanced = getPropertyBasicInformationEnhanced();
        String virtualPropertyDisplayCode = propertyBasicInformationEnhanced.getVirtualPropertyDisplayCode();
        if (StringUtils.isNotEmpty(virtualPropertyDisplayCode)) {
            propertyBasicInformationEnhanced.setPropertyCode(virtualPropertyDisplayCode);
        }
        PropertyBasicInformationEnhancedBookedStatus informationEnhancedBookedStatus = new PropertyBasicInformationEnhancedBookedStatus(propertyBasicInformationEnhanced);
        String bookedStatus = propertyService.getBookedOrStayed();
        String bookedStatusValue = "1".equals(bookedStatus) ? Constants.YES : Constants.NO;
        informationEnhancedBookedStatus.setBookedStatus(bookedStatusValue);
        return informationEnhancedBookedStatus;
    }

    public List<PropertyInformationEnhancedBookedStatusAndLimitedBuildDateAndWindowSettings> getPropertyInformationEnhancedWithBookedStatusAndLdbAndWindowSettings() {
        PropertyBasicInformationEnhancedLimitedBuildDate propertyInfoEnhancedLimitedBuildDate = getEnhancedPropertyBasicInformationLastLDBUpdate();
        PropertyInformationEnhancedBookedStatusAndLimitedBuildDateAndWindowSettings dto = new PropertyInformationEnhancedBookedStatusAndLimitedBuildDateAndWindowSettings(propertyInfoEnhancedLimitedBuildDate);

        dto.setBdeForecastWindow(configParamsService.getIntegerParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value()));
        dto.setBdeDecisionWindow(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value()));
        dto.setIdpForecastWindow(configParamsService.getIntegerParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_CDP.value()));
        dto.setIdpOptimizationWindow(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_CDP.value()));
        if (configParamsService.getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND)) {
            dto.setGroupPricingExtendedWindow(configParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value()));
        }
        dto.setVariableDecisionWindow(configParamsService.getIntegerParameterValue(IntegrationConfigParamName.VARIABLE_DECISION_WINDOW_DAYS.value()));

        return Collections.singletonList(dto);
    }

    public List<PropertyBasicInformationEnhancedLimitedBuildDate> getEnhancedPropertyBasicInformationWithLastLDBUpdate() {
        PropertyBasicInformationEnhancedLimitedBuildDate propertyBasicInformationEnhancedLimitedBuildDate = getEnhancedPropertyBasicInformationLastLDBUpdate();
        return Collections.singletonList(propertyBasicInformationEnhancedLimitedBuildDate);
    }

    public PropertyBasicInformationEnhancedLimitedBuildDate getEnhancedPropertyBasicInformationLastLDBUpdate() {
        PropertyBasicInformationEnhancedBookedStatus informationEnhancedBookedStatus = getPropertyBasicInformationEnhancedBookedStatus();
        PropertyBasicInformationEnhancedLimitedBuildDate informationEnhancedLimitedBuildDate = new PropertyBasicInformationEnhancedLimitedBuildDate(informationEnhancedBookedStatus);
        LocalDateTime lastLDBUpdate = ldbService.getLastLimitedDataBuildDate();
        String modifiedFormattedDate = "";
        if (lastLDBUpdate != null) {
            ZoneId zoneId = ZoneId.systemDefault();
            Instant instant = lastLDBUpdate.atZone(zoneId).toInstant();
            Date ldbDate = Date.from(instant);
            String formattedDate = dateService.formatDateToPropertyTimeZone(ldbDate);
            String[] parts = formattedDate.split(" ");
            String timePart = parts[1];
            modifiedFormattedDate = parts[0] + " " + timePart.substring(0, 5);
        }
        informationEnhancedLimitedBuildDate.setLastLDBUpdate(modifiedFormattedDate);
        return informationEnhancedLimitedBuildDate;
    }

    private PropertyBasicInformationEnhanced getPropertyBasicInformationEnhanced() {
        Property propertyByCode = propertyService.getPropertyByCode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());

        PropertyBasicInformationEnhanced propertyBasicInformationEnhanced = new PropertyBasicInformationEnhanced(createPropertyBasicInformation(propertyByCode));
        setSystemDates(propertyByCode, propertyBasicInformationEnhanced);
        setAdditionalFields(propertyBasicInformationEnhanced, propertyByCode.getId());
        propertyBasicInformationEnhanced.setRateShoppingVendor(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SOURCE.value()));
        propertyBasicInformationEnhanced.setVirtualPropertyDisplayCode(propertyByCode.getVirtualPropertyDisplayCode());
        propertyBasicInformationEnhanced.setClientPropertyCode(propertyByCode.getClientPropertyCode());
        propertyBasicInformationEnhanced.setExternalSystem(getExternalSystem(propertyByCode));
        return propertyBasicInformationEnhanced;
    }

    private String getExternalSystem(Property propertyByCode) {
        String extSystemToCheck = configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM_SUB_SYSTEM, propertyByCode.getClientPropertyCode());
        return StringUtils.isNotEmpty(extSystemToCheck) && "None".equalsIgnoreCase(extSystemToCheck) ? configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM) : extSystemToCheck;
    }

    private void setSystemDates(Property propertyByCode, PropertyBasicInformationEnhanced propertyBasicInformationEnhanced) {
        SystemDates systemDates = dateService.getSystemDates();
        DateTimeZone propertyTimeZone = DateTimeZone.forTimeZone(systemDates.getTimeZone());

        propertyBasicInformationEnhanced.setSystemMode(parseJsonString(propertyService.getPropertySystemMode(propertyByCode.getId()), "result"));
        propertyBasicInformationEnhanced.setBuildType(parseJsonString(propertyService.getPropertyBuildType(), "buildType"));
        propertyBasicInformationEnhanced.setUnqualifiedRateProcessedDate(systemDates.isUnqualifiedProcessedEnabled() ? formatDate(systemDates.getUnqualifiedProcessed(), propertyTimeZone) : null);
        propertyBasicInformationEnhanced.setTransactionSystemDate(systemDates.getTransactionSystem());
        propertyBasicInformationEnhanced.setTransactionDataPopulationDate(formatDate(systemDates.getTransactionDataPopulation(), propertyTimeZone));
        propertyBasicInformationEnhanced.setForecastDate(formatDate(systemDates.getForecast(), propertyTimeZone));
        propertyBasicInformationEnhanced.setControlDate(formatDate(systemDates.getControl(), propertyTimeZone));
        propertyBasicInformationEnhanced.setLastRateShoppingProcessExtractDate(formatDate(systemDates.getLastRateShoppingExtract(), propertyTimeZone));
        propertyBasicInformationEnhanced.setLastReputationExtractDate(systemDates.isLastReputationExtractEnabled() ? formatDate(systemDates.getLastReputationExtract(), propertyTimeZone) : null);
        propertyBasicInformationEnhanced.setFunctionSpaceSystemDate(systemDates.isFunctionSpaceEnabled() ? formatDate(systemDates.getFunctionSpaceSystem(), propertyTimeZone) : null);
        propertyBasicInformationEnhanced.setDemand360LastDataPopulationDate(systemDates.isDemand360PopulationEnabled() ? systemDates.getLastDemand360PopulationDate() : null);
    }

    private void setAdditionalFields(PropertyBasicInformationEnhanced propertyBasicInformationEnhanced, Integer propertyId) {
        PropertySearchCriteria searchCriteria = new PropertySearchCriteria();
        searchCriteria.setPropertyId(propertyId);
        List<com.ideas.tetris.pacman.services.property.dto.Property> properties = propertyRolloutService.findProperties(searchCriteria);
        if (CollectionUtils.isNotEmpty(properties) && properties.get(0) != null) {
            com.ideas.tetris.pacman.services.property.dto.Property property = properties.get(0);
            propertyBasicInformationEnhanced.setDecisionDeliveryModeDate(property.getActualTwoWayDate() != null ? property.getActualTwoWayDate().getTime() : null);
            propertyBasicInformationEnhanced.setScheduledDecisionDeliveryModeDate(property.getScheduledTwoWayDate() != null ? property.getScheduledTwoWayDate().getTime() : null);
            propertyBasicInformationEnhanced.setHistoryExtractDate(property.getHistoricalExtractDate() != null ? property.getHistoricalExtractDate().getTime() : null);
            propertyBasicInformationEnhanced.setUnprocessedExtractCount(property.getNumberOfIncomingExtracts());
            propertyBasicInformationEnhanced.setProcessedExtractCount(property.getNumberOfArchivedExtracts());
            propertyBasicInformationEnhanced.setMissingDailyExtractCount(property.getMissingDates().size());
            propertyBasicInformationEnhanced.setUnprocessedRateShoppingExtractCount(property.getNumberOfIncomingWebRateExtracts());
            propertyBasicInformationEnhanced.setConfigurationFileLoaded(property.getConfigFileLoadDate() != null ? property.getConfigFileLoadDate().getTime() : null);
            propertyBasicInformationEnhanced.setOutOfOrder(property.isOutOfOrderRecordsHaveBeenLoaded() ? Constants.YES : Constants.NO);
        }
    }

    private String parseJsonString(String json, String key) {
        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(json);
            return jsonObject.getString(key);
        } catch (JSONException e) {
            LOGGER.error("Error parsing object:" + json, e);
        }
        return "";
    }

    private Date formatDate(Date date, DateTimeZone propertyTimeZone) {
        return DateUtil.getDateTimeByTimeZone(date, propertyTimeZone.toTimeZone());
    }

    private PropertyBasicInformation createPropertyBasicInformation(Property property) {
        return new PropertyBasicInformation(property.getCode(), property.getName(), dateService.getCaughtUpDate(property.getId()));
    }
}
