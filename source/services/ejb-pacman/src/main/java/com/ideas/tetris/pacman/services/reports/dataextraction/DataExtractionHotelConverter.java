package com.ideas.tetris.pacman.services.reports.dataextraction;

import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionHotel;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionReportDto;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionType;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.ReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.DataExtractionReportCriteria;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil.getText;


public class DataExtractionHotelConverter {

    public static ReportSheet getPropertyReportSheet(Map<DataExtractionType, List<DataExtractionReportDto>> records, ScheduledReport<DataExtractionReportCriteria> scheduledReport) {
        Language language = scheduledReport.getLanguage();
        DecimalFormat decimalFormat = ScheduledReportUtils.getLocaleDecimalFormat(language.getLocale());
        ReportSheet propertySheet = new ReportSheet(getText("common.property", scheduledReport.getLanguage()));
        propertySheet.setReportTitle(getText("dataExtractionReport.title.at.hotel.level", scheduledReport.getLanguage()));
        List<DataExtractionReportDto> dataExtractionReportDtoList = records.get(DataExtractionType.HOTEL);
        DataExtractionReportCriteria reportCriteria = scheduledReport.getReportCriteria();
        Object[] objects = getPropertyHeaderSet(scheduledReport.getLanguage(), reportCriteria, dataExtractionReportDtoList).toArray();
        for (int i = 0; i < objects.length; i++) {
            propertySheet.addColumn(String.class);
        }
        propertySheet.addHeaderRow(objects);

        dataExtractionReportDtoList.forEach(dto -> {
            List<Object> dataList = new ArrayList<Object>();
            DataExtractionHotel data = (DataExtractionHotel) dto;

            if (scheduledReport.getReportCriteria().isShowLastYearData()) {
                dataList.add(DataExtractionReportUtil.getStringValue(data.getPropertyName())); // Property Name
                if (data.getDayOfWeek() != null && !data.getDayOfWeek().isEmpty()) {
                    dataList.add(getText(DataExtractionReportUtil.getStringValue(data.getDayOfWeek()).toLowerCase(), language)); // Day of Week
                } else {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getDayOfWeek())); // Day of Week
                }

                dataList.add(ScheduledReportUtils.getDateString(data.getOccupancyDate())); // Occupancy Date

                dataList.add(ScheduledReportUtils.getDateString(data.getComparisonDateLastYear())); // Comparison Date Last Year

                if (reportCriteria.isHotelSpecialEvent()) {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getSpecialEventThisYear())); // Special Event This Year
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getSpecialEventLastYear())); // Special Event Last Year Actual
                }

                if (reportCriteria.isHotelCapacity()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCapacityThisYear())); // Physical Capacity This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCapacityLastYear())); // Physical Capacity Last Year Actual
                }

                if (reportCriteria.isHotelRoomsSold() && reportCriteria.isHotelRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); // Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); // Occupancy On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); // Occupancy On Books STLY
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getGroupRoomsSoldThisYear())); // Rooms Sold - Group This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getGroupRoomsSoldLastYear())); // Rooms Sold - Group Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getGroupRoomsSoldSTLY())); // Rooms Sold - Group STLY
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getTransientRoomsSoldThisYear())); // Rooms Sold - Transient This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getTransientRoomsSoldLastYear())); // Rooms Sold - Transient Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getTransientRoomsSoldSTLY())); // Rooms Sold - Transient STLY
                }

                if (reportCriteria.isHotelRoomsSold() && (!reportCriteria.isHotelRoomsSoldSTLY())) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); // Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); // Occupancy On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getGroupRoomsSoldThisYear())); // Rooms Sold - Group This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getGroupRoomsSoldLastYear())); // Rooms Sold - Group Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getTransientRoomsSoldThisYear())); // Rooms Sold - Transient This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getTransientRoomsSoldLastYear())); // Rooms Sold - Transient Last Year Actual
                }

                if ((!reportCriteria.isHotelRoomsSold()) && reportCriteria.isHotelRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldLastYear())); // Occupancy On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); // Occupancy On Books STLY
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getGroupRoomsSoldLastYear())); // Rooms Sold - Group Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getGroupRoomsSoldSTLY())); // Rooms Sold - Group STLY
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getTransientRoomsSoldLastYear())); // Rooms Sold - Transient Last Year Actual
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getTransientRoomsSoldSTLY())); // Rooms Sold - Transient STLY
                }

                if (reportCriteria.isHotelArrivals()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsThisYear())); // Arrivals This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsLastYear())); // Arrivals Last Year Actual
                }

                if (reportCriteria.isHotelDepartures()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesThisYear())); // Departures This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesLastYear())); // Departures Last Year Actual
                }


                if (reportCriteria.isHotelOutOfOrder()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getOutOfOrderThisYear())); // Rooms N/A - Out of Order This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getOutOfOrderLastYear())); // Rooms N/A - Out of Order Last Year Actual
                }
                if (reportCriteria.isHotelRoomsNotAvailableOutOfOrder()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getOthersThisYear())); // Rooms N/A - Other This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getOthersLastYear())); // Rooms N/A - Other Last Year Actual
                }

                if (reportCriteria.isHotelCancellations()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledThisYear())); // Cancelled This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledLastYear())); // Cancelled Last Year Actual
                }

                if (reportCriteria.isHotelNoShow()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowThisYear())); // No Show This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowLastYear())); // No Show Last Year Actual
                }

                if (reportCriteria.isHotelRevenue() && reportCriteria.isHotelRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); // Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); // Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); // Booked Room Revenue STLY
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); // Forecasted Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueLastYear(), decimalFormat)); // Forecasted Room Revenue Last Year Actual
                }

                if (reportCriteria.isHotelRevenue() && !reportCriteria.isHotelRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); // Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); // Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); // Forecasted Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueLastYear(), decimalFormat)); // Forecasted Room Revenue Last Year Actual
                }

                if (!reportCriteria.isHotelRevenue() && reportCriteria.isHotelRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueLastYear(), decimalFormat)); // Booked Room Revenue Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); // Booked Room Revenue STLY
                }

                if (reportCriteria.isHotelProfit()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getProfitThisYear(), decimalFormat));
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getProfitLastYearSTLY(), decimalFormat));
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getProfitLastYearActual(), decimalFormat));
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getProfitFcstThisYear(), decimalFormat));
                }

                if (reportCriteria.isHotelOccupancyForecast()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getTotalOccupancyThisYear(), decimalFormat)); // Occupancy Forecast - Total This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getTotalOccupancyLastYear(), decimalFormat)); // Occupancy Forecast - Total Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getGroupOccupancyThisYear(), decimalFormat)); // Occupancy Forecast - Group This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getGroupOccupancyLastYear(), decimalFormat)); // Occupancy Forecast - Group Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getTransientOccupancyThisYear(), decimalFormat)); // Occupancy Forecast - Transient This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getTransientOccupancyLastYear(), decimalFormat)); // Occupancy Forecast - Transient Last Year Actual
                }

                if (reportCriteria.isHotelSystemUnconstrainedDemand()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getTotalDemandThisYear(), decimalFormat)); // System Total Demand - Total This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getTotalDemandLastYear(), decimalFormat)); // System Total Demand - Total Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getGroupDemandThisYear(), decimalFormat)); // System Total Demand - Group This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getGroupDemandLastYear(), decimalFormat)); // System Total Demand - Group Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getTransientDemandThisYear(), decimalFormat)); // System Total Demand - Transient This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getTransientDemandLastYear(), decimalFormat)); // System Total Demand - Transient Last Year Actual
                }

                if (reportCriteria.isHotelUserUnconstrainedDemand()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserTotalDemandThisYear(), decimalFormat)); // User Total Demand - Total This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserTotalDemandLastYear(), decimalFormat)); // User Total Demand - Total Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserGroupDemandThisYear(), decimalFormat)); // User Constrained Total Demand - Group This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserGroupDemandLastYear(), decimalFormat)); // User Constrained Total Demand - Group Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserTransientDemandThisYear(), decimalFormat)); // User Unconstrained Total Demand - Transient This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserTransientDemandLastYear(), decimalFormat)); // User Unconstrained Total Demand - Transient Last Year Actual
                }

                if (reportCriteria.isHotelOverbooking()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getOverbookingThisYear())); // Overbooking This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getOverbookingLastYear())); // Overbooking Last Year Actual
                }

                if (reportCriteria.isHotelRevPAR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevPARThisYear(), decimalFormat)); // RevPAR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevPARLastYear(), decimalFormat)); // RevPAR On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRevparThisYear(), decimalFormat)); // RevPAR Forecast This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRevparLastYear(), decimalFormat)); // RevPAR Forecast Last Year Actual
                }

                if (reportCriteria.isHotelADR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRThisYear(), decimalFormat)); // ADR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRLastYear(), decimalFormat)); // ADR On Books Last Year Actual
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrThisYear(), decimalFormat)); // ADR Forecast This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrLastYear(), decimalFormat)); // ADR Forecast Last Year Actual
                }

                if (reportCriteria.isHotelLRV()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getLrvThisYear(), decimalFormat)); // Last Room Value This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getLrvLastYear(), decimalFormat)); // Last Room Value Last Year Actual
                }

                if (reportCriteria.isHotelWash()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserWashThisYear(), decimalFormat)); // Wash % This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserWashLastYear(), decimalFormat)); // Wash % Last Year Actual
                }

                if (reportCriteria.isHotelBAR()) {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getRatecodeLOSAll()) + "\n(" + ScheduledReportUtils.getI18NFormatedValue(data.getBarByDay(), decimalFormat) + ")"); // BAR by Day for Room Class DLX ()
                }

                if (reportCriteria.isCompetitor()) {

                    if (reportCriteria.getComp1() != -1) {
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp1RateThisYear(), decimalFormat));
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp1RateLastYear(), decimalFormat));
                    }

                    if (reportCriteria.getComp2() != -1) {
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp2RateThisYear(), decimalFormat));
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp2RateLastYear(), decimalFormat));
                    }

                    if (reportCriteria.getComp3() != -1) {
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp3RateThisYear(), decimalFormat));
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp3RateLastYear(), decimalFormat));
                    }

                    if (reportCriteria.getComp4() != -1) {
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp4RateThisYear(), decimalFormat));
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp4RateLastYear(), decimalFormat));
                    }

                    if (reportCriteria.getComp5() != -1) {
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp5RateThisYear(), decimalFormat));
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp5RateLastYear(), decimalFormat));
                    }
                }

            } else {
                dataList.add(DataExtractionReportUtil.getStringValue(data.getPropertyName())); // Property Name
                if (data.getDayOfWeek() != null && !data.getDayOfWeek().isEmpty()) {
                    dataList.add(getText(DataExtractionReportUtil.getStringValue(data.getDayOfWeek()).toLowerCase(), language)); // Day of Week
                } else {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getDayOfWeek())); // Day of Week
                }
                dataList.add(ScheduledReportUtils.getDateString(data.getOccupancyDate())); // Occupancy Date

                if (reportCriteria.isHotelSpecialEvent()) {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getSpecialEventThisYear())); // Special Event This Year
                }

                if (reportCriteria.isHotelCapacity()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCapacityThisYear())); // Physical Capacity This Year
                }

                if (reportCriteria.isHotelRoomsSold() && reportCriteria.isHotelRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); // Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); // Occupancy On Books STLY
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getGroupRoomsSoldThisYear())); // Rooms Sold - Group This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getGroupRoomsSoldSTLY())); // Rooms Sold - Group STLY
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getTransientRoomsSoldThisYear())); // Rooms Sold - Transient This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getTransientRoomsSoldSTLY())); // Rooms Sold - Transient STLY
                }

                if (reportCriteria.isHotelRoomsSold() && (!reportCriteria.isHotelRoomsSoldSTLY())) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldThisYear())); // Occupancy On Books This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getGroupRoomsSoldThisYear())); // Rooms Sold - Group This Year
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getTransientRoomsSoldThisYear())); // Rooms Sold - Transient This Year
                }

                if ((!reportCriteria.isHotelRoomsSold()) && reportCriteria.isHotelRoomsSoldSTLY()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getRoomsSoldSTLY())); // Occupancy On Books STLY
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getGroupRoomsSoldSTLY())); // Rooms Sold - Group STLY
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getTransientRoomsSoldSTLY())); // Rooms Sold - Transient STLY
                }

                if (reportCriteria.isHotelArrivals()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getArrivalsThisYear())); // Arrivals This Year
                }

                if (reportCriteria.isHotelDepartures()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getDeparturesThisYear())); // Departures This Year
                }

                if (reportCriteria.isHotelOutOfOrder()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getOutOfOrderThisYear())); // Rooms N/A - Out of Order This Year
                }
                if (reportCriteria.isHotelRoomsNotAvailableOutOfOrder()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getOthersThisYear())); // Rooms N/A - Other This Year
                }

                if (reportCriteria.isHotelCancellations()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getCancelledThisYear())); // Cancelled This Year
                }

                if (reportCriteria.isHotelNoShow()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getNoShowThisYear())); // No Show This Year
                }

                if (reportCriteria.isHotelRevenue() && reportCriteria.isHotelRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); // Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); // Booked Room Revenue STLY
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); // Forecasted Room Revenue This Year
                }

                if (reportCriteria.isHotelRevenue() && !reportCriteria.isHotelRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueThisYear(), decimalFormat)); // Booked Room Revenue This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRoomRevenueThisYear(), decimalFormat)); // Forecasted Room Revenue This Year
                }

                if (!reportCriteria.isHotelRevenue() && reportCriteria.isHotelRevenueSTLY()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevenueSTLY(), decimalFormat)); // Booked Room Revenue STLY
                }

                if (reportCriteria.isHotelProfit()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getProfitThisYear(), decimalFormat));
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getProfitFcstThisYear(), decimalFormat));
                }

                if (reportCriteria.isHotelOccupancyForecast()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getTotalOccupancyThisYear(), decimalFormat)); // Occupancy Forecast - Total This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getGroupOccupancyThisYear(), decimalFormat)); // Occupancy Forecast - Group This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getTransientOccupancyThisYear(), decimalFormat)); // Occupancy Forecast - Transient This Year
                }

                if (reportCriteria.isHotelSystemUnconstrainedDemand()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getTotalDemandThisYear(), decimalFormat)); // System Total Demand - Total This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getGroupDemandThisYear(), decimalFormat)); // System Total Demand - Group This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getTransientDemandThisYear(), decimalFormat)); // System Total Demand - Transient This Year
                }

                if (reportCriteria.isHotelUserUnconstrainedDemand()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserTotalDemandThisYear(), decimalFormat)); // User Total Demand - Total This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserGroupDemandThisYear(), decimalFormat)); // User Constrained Total Demand - Group This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserTransientDemandThisYear(), decimalFormat)); // User Unconstrained Total Demand - Transient This Year
                }

                if (reportCriteria.isHotelOverbooking()) {
                    dataList.add(ScheduledReportUtils.getIntegerString(data.getOverbookingThisYear())); // Overbooking This Year
                }

                if (reportCriteria.isHotelRevPAR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksRevPARThisYear(), decimalFormat)); // RevPAR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getRevparThisYear(), decimalFormat)); // RevPAR Forecast This Year
                }

                if (reportCriteria.isHotelADR()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getOnBooksADRThisYear(), decimalFormat)); // ADR On Books This Year
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getAdrThisYear(), decimalFormat)); // ADR Forecast This Year
                }

                if (reportCriteria.isHotelLRV()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getLrvThisYear(), decimalFormat)); // Last Room Value This Year
                }

                if (reportCriteria.isHotelWash()) {
                    dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getUserWashThisYear(), decimalFormat)); // Wash % This Year
                }

                if (reportCriteria.isHotelBAR()) {
                    dataList.add(DataExtractionReportUtil.getStringValue(data.getRatecodeLOSAll()) + "\n(" + ScheduledReportUtils.getI18NFormatedValue(data.getBarByDay(), decimalFormat) + ")"); // BAR by Day for Room Class DLX ()
                }


                if (reportCriteria.isCompetitor()) {

                    if (reportCriteria.getComp1() != -1) {
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp1RateThisYear(), decimalFormat));
                    }

                    if (reportCriteria.getComp2() != -1) {
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp2RateThisYear(), decimalFormat));
                    }

                    if (reportCriteria.getComp3() != -1) {
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp3RateThisYear(), decimalFormat));
                    }

                    if (reportCriteria.getComp4() != -1) {
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp4RateThisYear(), decimalFormat));
                    }

                    if (reportCriteria.getComp5() != -1) {
                        dataList.add(ScheduledReportUtils.getI18NFormatedValue(data.getComp5RateThisYear(), decimalFormat));
                    }
                }


            }

            propertySheet.addRow(dataList.toArray());
        });

        return propertySheet;

    }

    private static List<Object> getPropertyHeaderSet(Language language, DataExtractionReportCriteria reportCriteria, List<DataExtractionReportDto> dataExtractionReportDtoList) {
        DataExtractionHotel dataExtractionHotel = (DataExtractionHotel) dataExtractionReportDtoList.get(0);

        List<Object> headers = new ArrayList<>();
        if (reportCriteria.isShowLastYearData()) {
            headers.add(getText("common.propertyName", language));
            headers.add(getText("report.dow", language));
            headers.add(getText("occupancyDate", language));
            headers.add(getText("common.comparisonDateLastYear", language));

            if (reportCriteria.isHotelSpecialEvent()) {
                headers.add(getText("specialEvent", language) + " " + getText("common.thisYear", language));
                headers.add(getText("specialEvent", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelCapacity()) {
                headers.add(getText("report.column.hotelCapacity", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.hotelCapacity", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelRoomsSold() && reportCriteria.isHotelRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.roomsSoldGroup", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsSoldGroup", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.roomsSoldGroup", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.roomsSoldTransient", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsSoldTransient", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.roomsSoldTransient", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isHotelRoomsSold() && !reportCriteria.isHotelRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.roomsSoldGroup", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsSoldGroup", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.roomsSoldTransient", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsSoldTransient", language) + " " + getText("common.lastYearActual", language));
            }

            if (!reportCriteria.isHotelRoomsSold() && reportCriteria.isHotelRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.roomsSoldGroup", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.roomsSoldGroup", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.roomsSoldTransient", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.roomsSoldTransient", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isHotelArrivals()) {
                headers.add(getText("arrivals", language) + " " + getText("common.thisYear", language));
                headers.add(getText("arrivals", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelDepartures()) {
                headers.add(getText("common.departures", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.departures", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelOutOfOrder()) {
                headers.add(getText("report.column.roomsNAOutOfOrder", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsNAOutOfOrder", language) + " " + getText("common.lastYearActual", language));
            }
            if (reportCriteria.isHotelRoomsNotAvailableOutOfOrder()) {
                headers.add(getText("report.column.roomsNAOther", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsNAOther", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelCancellations()) {
                headers.add(getText("cancelled", language) + " " + getText("common.thisYear", language));
                headers.add(getText("cancelled", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelNoShow()) {
                headers.add(getText("noshow", language) + " " + getText("common.thisYear", language));
                headers.add(getText("noshow", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelRevenue() && reportCriteria.isHotelRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelRevenue() && !reportCriteria.isHotelRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
            }

            if (!reportCriteria.isHotelRevenue() && reportCriteria.isHotelRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isHotelProfit()) {
                headers.add(getText("report.column.profit", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.profit", language) + " " + getText("common.stly", language));
                headers.add(getText("report.column.profit", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.profit", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelOccupancyForecast()) {
                headers.add(getText("report.column.occupancyForecastTotal", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.occupancyForecastTotal", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.occupancyForecastGroup", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.occupancyForecastGroup", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.occupancyForecastTransient", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.occupancyForecastTransient", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelSystemUnconstrainedDemand()) {
                headers.add(getText("report.column.systemUnconstrainedTotalDemand", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.systemUnconstrainedTotalDemand", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.systemUnconstrainedTotalDemandGroup", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.systemUnconstrainedTotalDemandGroup", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.systemUnconstrainedTotalDemandTransient", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.systemUnconstrainedTotalDemandTransient", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelUserUnconstrainedDemand()) {
                headers.add(getText("report.column.userTotalDemandTotal", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.userTotalDemandTotal", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.userConstrainedTotalDemandGroup", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.userConstrainedTotalDemandGroup", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("report.column.userUnconstrainedTotalDemandTransient", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.userUnconstrainedTotalDemandTransient", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelOverbooking()) {
                headers.add(getText("common.overbooking", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.overbooking", language) + " " + getText("common.lastYearActual", language));
            }


            if (reportCriteria.isHotelRevPAR()) {
                headers.add(getText("report.column.bookedRevPar", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRevPar", language) + " " + getText("common.lastYearActual", language));

                headers.add(getText("revpar.forecast", language) + " " + getText("common.thisYear", language));
                headers.add(getText("revpar.forecast", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelADR()) {
                headers.add(getText("adr.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("adr.on.books", language) + " " + getText("common.lastYearActual", language));
                headers.add(getText("adr.forecast", language) + " " + getText("common.thisYear", language));
                headers.add(getText("adr.forecast", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelLRV()) {
                headers.add(getText("last.room.value", language) + " " + getText("common.thisYear", language));
                headers.add(getText("last.room.value", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelWash()) {
                headers.add(getText("common.wash.percentage", language) + " " + getText("common.thisYear", language));
                headers.add(getText("common.wash.percentage", language) + " " + getText("common.lastYearActual", language));
            }

            if (reportCriteria.isHotelBAR()) {
                headers.add(getText("report.column.barByDayForRC.dlx", language));
            }

            if (reportCriteria.isCompetitor()) {
                if (reportCriteria.getComp1() != -1) {
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp1Name()) + " " + getText("common.thisYear", language));
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp1Name()) + " " + getText("common.lastYearActual", language));
                }

                if (reportCriteria.getComp2() != -1) {
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp2Name()) + " " + getText("common.thisYear", language));
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp2Name()) + " " + getText("common.lastYearActual", language));
                }

                if (reportCriteria.getComp3() != -1) {
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp3Name()) + " " + getText("common.thisYear", language));
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp3Name()) + " " + getText("common.lastYearActual", language));
                }

                if (reportCriteria.getComp4() != -1) {
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp4Name()) + " " + getText("common.thisYear", language));
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp4Name()) + " " + getText("common.lastYearActual", language));
                }

                if (reportCriteria.getComp5() != -1) {
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp5Name()) + " " + getText("common.thisYear", language));
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp5Name()) + " " + getText("common.lastYearActual", language));
                }

            }

        } else {
            headers.add(getText("common.propertyName", language));
            headers.add(getText("report.dow", language));
            headers.add(getText("occupancyDate", language));

            if (reportCriteria.isHotelSpecialEvent()) {
                headers.add(getText("specialEvent", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelCapacity()) {
                headers.add(getText("report.column.hotelCapacity", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelRoomsSold() && reportCriteria.isHotelRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.roomsSoldGroup", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsSoldGroup", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.roomsSoldTransient", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsSoldTransient", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isHotelRoomsSold() && !reportCriteria.isHotelRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsSoldGroup", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.roomsSoldTransient", language) + " " + getText("common.thisYear", language));
            }

            if (!reportCriteria.isHotelRoomsSold() && reportCriteria.isHotelRoomsSoldSTLY()) {
                headers.add(getText("occupancy.on.books", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.roomsSoldGroup", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.roomsSoldTransient", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isHotelArrivals()) {
                headers.add(getText("arrivals", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelDepartures()) {
                headers.add(getText("common.departures", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelOutOfOrder()) {
                headers.add(getText("report.column.roomsNAOutOfOrder", language) + " " + getText("common.thisYear", language));
            }
            if (reportCriteria.isHotelRoomsNotAvailableOutOfOrder()) {
                headers.add(getText("report.column.roomsNAOther", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelCancellations()) {
                headers.add(getText("cancelled", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelNoShow()) {
                headers.add(getText("noshow", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelRevenue() && reportCriteria.isHotelRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelRevenue() && !reportCriteria.isHotelRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.forecastedRoomRevenue", language) + " " + getText("common.thisYear", language));
            }

            if (!reportCriteria.isHotelRevenue() && reportCriteria.isHotelRevenueSTLY()) {
                headers.add(getText("report.column.bookedRoomRevenue", language) + " " + getText("STLY", language));
            }

            if (reportCriteria.isHotelProfit()) {
                headers.add(getText("report.column.profit", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.profit", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelOccupancyForecast()) {
                headers.add(getText("report.column.occupancyForecastTotal", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.occupancyForecastGroup", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.occupancyForecastTransient", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelSystemUnconstrainedDemand()) {
                headers.add(getText("report.column.systemUnconstrainedTotalDemand", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.systemUnconstrainedTotalDemandGroup", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.systemUnconstrainedTotalDemandTransient", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelUserUnconstrainedDemand()) {
                headers.add(getText("report.column.userTotalDemandTotal", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.userConstrainedTotalDemandGroup", language) + " " + getText("common.thisYear", language));
                headers.add(getText("report.column.userUnconstrainedTotalDemandTransient", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelOverbooking()) {
                headers.add(getText("common.overbooking", language) + " " + getText("common.thisYear", language));
            }


            if (reportCriteria.isHotelRevPAR()) {
                headers.add(getText("report.column.bookedRevPar", language) + " " + getText("common.thisYear", language));
                headers.add(getText("revpar.forecast", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelADR()) {
                headers.add(getText("adr.on.books", language) + " " + getText("common.thisYear", language));
                headers.add(getText("adr.forecast", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelLRV()) {
                headers.add(getText("last.room.value", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelWash()) {
                headers.add(getText("common.wash.percentage", language) + " " + getText("common.thisYear", language));
            }

            if (reportCriteria.isHotelBAR()) {
                headers.add(getText("report.column.barByDayForRC.dlx", language));
            }

            if (reportCriteria.isCompetitor()) {
                if (reportCriteria.getComp1() != -1) {
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp1Name()) + " " + getText("common.thisYear", language));
                }

                if (reportCriteria.getComp2() != -1) {
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp2Name()) + " " + getText("common.thisYear", language));
                }

                if (reportCriteria.getComp3() != -1) {
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp3Name()) + " " + getText("common.thisYear", language));
                }

                if (reportCriteria.getComp4() != -1) {
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp4Name()) + " " + getText("common.thisYear", language));
                }

                if (reportCriteria.getComp5() != -1) {
                    headers.add(getText("report.competitorRateFor", language) + " " + DataExtractionReportUtil.getStringValue(dataExtractionHotel.getComp5Name()) + " " + getText("common.thisYear", language));
                }

            }


        }

        return headers;
    }
}
