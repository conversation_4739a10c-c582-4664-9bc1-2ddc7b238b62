package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.rateshopping.TaxInclusiveConfigurationDTO;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class TaxInclusiveConfigurationDataService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;


    public Boolean loadTaxInclusiveConfigurationDataIntoPacman(List<TaxInclusiveConfigurationDTO> data) {
        boolean isDataSaved = false;
        List<Tax> taxInclusiveConfigurationDTOList = new ArrayList<>();

        data.forEach(dataDTO -> {

            Tax taxDTO = new Tax();
            taxDTO.setRoomTaxRate(dataDTO.getTax());
            taxDTO.setSeasonName(dataDTO.getSeasonName());
            taxDTO.setStartDate(new LocalDate(2041, 01, 12));
            taxDTO.setEndDate(new LocalDate(2041, 01, 19));
            taxInclusiveConfigurationDTOList.add(taxDTO);
        });

        if (!taxInclusiveConfigurationDTOList.isEmpty()) {
            tenantCrudService.save(taxInclusiveConfigurationDTOList);
            isDataSaved = true;
        }
        return isDataSaved;
    }


    public Boolean deleteData() {

        List<Tax> taxList = tenantCrudService.findByNamedQuery(Tax.SEASONAL_TAX);
        taxList.forEach(t -> tenantCrudService.delete(t));
        return true;

    }
}
