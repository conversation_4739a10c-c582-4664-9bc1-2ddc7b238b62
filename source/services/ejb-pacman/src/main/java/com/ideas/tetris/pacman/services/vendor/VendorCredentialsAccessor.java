package com.ideas.tetris.pacman.services.vendor;

import com.ideas.tetris.platform.common.ngi.VendorCredentials;

import org.springframework.aop.SpringProxy;
public interface VendorCredentialsAccessor extends SpringProxy {

    void setInboundCredentials(VendorCredentials inboundCredentials);

    void setOutboundCredentials(VendorCredentials outboundCredentials);

    VendorCredentials getInboundCredentials();

    VendorCredentials getOutboundCredentials();

}
