package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.entity.UserActivityLog;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;

import javax.inject.Inject;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Component
@Transactional
public class UserActivityLogService {

    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService globalCrudService;


    public List<UserActivityLog> getUserAvtivityLog(String clientCode, Date startDate, Date endDate) {
        GlobalUser user = getAnyActiveExternalGlobalUser(clientCode);
        if (null == user) {
            return new ArrayList<UserActivityLog>();
        }
        int userID = user.getId();

        Integer propertyID = getPropertyIdOfAnyPropertyOfGivenClient(clientCode);
        if (null == propertyID) {
            return new ArrayList<UserActivityLog>();
        }

        return globalCrudService.findByNamedQuery(UserActivityLog.FIND_BY_DATES_BETWEEN, QueryParameter.with("userIDs", -1).and("startDate", startDate).and("endDate", endDate).and("loggedInUserID", userID).and("propertyID", propertyID).and("isRollingDate", false).and("rollingStartDate", null).and("rollingEndDate", null).parameters());
    }

    public Integer getPropertyIdOfAnyPropertyOfGivenClient(String clientCode) {
        List<Integer> properties = globalCrudService.findByNamedQuery(Property.GET_ID_BY_CLIENT_CODE, QueryParameter.with("clientCode", clientCode).parameters());
        return null == properties ? null : properties.get(0);
    }

    public GlobalUser getAnyActiveExternalGlobalUser(String clientCode) {
        List<GlobalUser> users = globalCrudService.findByNamedQuery(GlobalUser.ALL_ACTIVE_INTERNAL_OR_EXTERNAL_USERS,
                QueryParameter.with("isInternal", false).and("clientCode", clientCode).parameters());
        return null == users ? null : users.get(0);
    }

    public List<UserActivityLog> getUserActivityLogForSelectedUsers(String users, String propertyIDs, String rolesIds, LocalDate startDate, LocalDate endDate, int isPropertyGroup, int isRollingDate, String rollingStartDate, String rollingEndDate) {
        return globalCrudService.findByNamedQuery(UserActivityLog.FIND_BY_DATES_BETWEEN_AND_ROLES_AND_PROPERTIES, QueryParameter.with("userIDs", users)
                .and("roleIDs", rolesIds)
                .and("propertyOrGroupIDs", propertyIDs)
                .and("startDate", startDate)
                .and("endDate", endDate)
                .and("loggedInUserID", PacmanWorkContextHelper.getUserId())
                .and("propertyID", PacmanWorkContextHelper.getPropertyId())
                .and("isPropertyGroup", isPropertyGroup)
                .and("isRollingDate", isRollingDate)
                .and("rollingStartDate", rollingStartDate)
                .and("rollingEndDate", rollingEndDate)
                .parameters());
    }

}
