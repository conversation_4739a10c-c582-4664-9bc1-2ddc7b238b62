package com.ideas.tetris.pacman.services.dashboard.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Key;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.MultiPropertyAggregate;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Sum;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

@MultiPropertyAggregate
public class OccupancyPercentByDateAndGroupDto {

    @Key
    private String groupName;

    @Key
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    private Date occupancyDate;

    @Sum
    private BigDecimal availableCapacity;

    @Sum
    private BigDecimal occupancyNumber;

    public BigDecimal getPhysicalCapacity() {
        return physicalCapacity;
    }

    public void setPhysicalCapacity(BigDecimal physicalCapacity) {
        this.physicalCapacity = physicalCapacity;
    }

    @Sum
    private BigDecimal physicalCapacity;

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public BigDecimal getAvailableCapacity() {
        return availableCapacity;
    }

    public void setAvailableCapacity(BigDecimal availableCapacity) {
        this.availableCapacity = availableCapacity;
    }

    public BigDecimal getOccupancyNumber() {
        return occupancyNumber;
    }

    public void setOccupancyNumber(BigDecimal occupancyNumber) {
        this.occupancyNumber = occupancyNumber;
    }

    public BigDecimal getOccupancyPercentage() {
        if (availableCapacity == null || availableCapacity.intValue() == 0) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(occupancyNumber.doubleValue() * 100.0 / availableCapacity.doubleValue()).setScale(2, RoundingMode.HALF_UP);
    }
}