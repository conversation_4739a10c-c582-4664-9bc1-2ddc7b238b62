package com.ideas.tetris.pacman.services.reports.inventoryhistory;

import com.ideas.recommendation.compression.GzipCompressionHelper;
import com.ideas.recommendation.model.s3.RecommendationResponse;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.fplos.FplosService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyConfigParamService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.qualifiedrate.service.RateQualifiedService;
import com.ideas.tetris.pacman.services.reports.inventoryhistory.dto.*;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.serivce.RateUnqualifiedService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.sql.Date;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.ENABLE_PACE_REPORTING_FROM_SERVICE;

@Slf4j
@Component
@Transactional
public class InventoryHistoryService {

    private static final String SELECT = "select ";
    private static final String PLUS_OPEN = " +( ";
    private static final String OPEN = " ( ";
    private static final String CLOSE = " ) ";
    private static final String DECISION_TYPE = " where Decision_Type = ";
    private static final String RATE_UNQUALIFIED_NULL = " and Rate_Unqualified_id is null ";
    private static final String RATE_UNQUALIFIED_NOT_NULL = " and Rate_Unqualified_id is not null ";
    private static final String RATE_UNQUALIFIED_IN = " and Rate_Unqualified_ID in ";
    private static final String RATE_QUALIFIED_NOT_NULL = " and Rate_Qualified_ID is not null ";
    private static final String RATE_QUALIFIED_NULL = " and Rate_Qualified_ID is null ";
    private static final String ACCOM_TYPE_NULL = " and Accom_Type_ID is null ";
    private static final String ACCOM_TYPE_NOT_NULL = " and Accom_Type_ID is not null ";
    private static final String ACCOM_TYPE_IN = " and Accom_Type_ID in ";
    private static final String RATE_QUALIFIED_IN = " and Rate_Qualified_ID in ";
    private static final String RATE_QUALIFIED_NOT_IN = " and Rate_Qualified_ID != ";
    private static final String OCCUPANCY_DATE_BETWEEN = " and Occupancy_Date between ";
    private static final String FPLOS = "FPLOS";
    private static final String OVBK = "Overbooking";
    private static final String INVENTORY_LIMIT = "InventoryLimit";
    private static final String DAILY_BAR = "DailyBAR";
    private static final String ALL = "all";
    private static final String AND = "' and '";
    private static final int MAX_LIMIT_XLSX = 1048576;
    @Autowired
    PacmanConfigParamsService configParamsService;
    @Autowired
    PropertyConfigParamService propertyConfigParamService;
    @Autowired
    DateService dateService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    CrudService tenantCrudService;
    @Autowired
    PropertyService propertyService;
    @Autowired
    FplosService fplosService;
    @Inject
    DecisionService decisionService;
    @Autowired
    RateQualifiedService rateQualifiedService;
    @Autowired
    RateUnqualifiedService rateUnqualifiedService;
    @Autowired
    AccommodationService accommodationService;

    public InventoryHistoryCount isGeneratedDataExceedingExcelLimits(InventoryHistoryParams params) {
        truncatePaceFromServiceTable();
        if (isPaceReportingEnabledAndDecisionTypeFPLOS(params)) {
            var pace = fetchPaceDataFromService(params);
            if (pace.getDecisionCount() > MAX_LIMIT_XLSX) {
                return getInventoryHistoryCountDTO(pace.getDecisionCount());
            }
            var t1 = System.currentTimeMillis();
            log.info("Started populating records from the service into Pace_From_Service table.");
            populatePace(pace);
            log.info("Completed. Population from service took: {} milliSeconds.", System.currentTimeMillis() - t1);
        }
        Integer countOfRecords = getCountOfRecords(params);
        return getInventoryHistoryCountDTO(countOfRecords);
    }

    private InventoryHistoryCount getInventoryHistoryCountDTO(Integer countOfRecords) {
        InventoryHistoryCount inventoryHistoryCount = new InventoryHistoryCount();
        inventoryHistoryCount.setActualNoOfRecords(countOfRecords);
        inventoryHistoryCount.setExcelLimit(MAX_LIMIT_XLSX);
        inventoryHistoryCount.setLimitExceeding(countOfRecords > MAX_LIMIT_XLSX);
        return inventoryHistoryCount;
    }

    protected Integer getCountOfRecords(InventoryHistoryParams params) {
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();
        if (params.isRollingDate()) {
            LocalDate caughtUpDate = dateService.getCaughtUpLocalDate();
            startDate = caughtUpDate.plusDays(getDays(params.getRollingStartDate())).toString();
            endDate = caughtUpDate.plusDays(getDays(params.getRollingEndDate())).toString();
        }

        String lv0ID = "-1";
        if (params.isLV0Selected()) {
            lv0ID = tenantCrudService.findByNativeQuerySingleResult("select rate_qualified_id from rate_qualified where Rate_Code_Name = 'LV0'", null).toString();
        }
        String selectedSRP = params.getSelectedSRP();
        String selectedRTs = params.getSelectedRoomTypes();

        StringBuilder query = new StringBuilder();
        query.append(SELECT);
        if (StringUtils.equalsIgnoreCase(ALL, params.getInventoryDecisionType().getParamValue()) ||
                (StringUtils.equalsIgnoreCase(FPLOS, params.getInventoryDecisionType().getParamValue()))) {
            getQueryForFPLOS(params, startDate, endDate, lv0ID, selectedSRP, selectedRTs, query);
        }

        if (StringUtils.equalsIgnoreCase(ALL, params.getInventoryDecisionType().getParamValue()) || StringUtils.equalsIgnoreCase(OVBK, params.getInventoryDecisionType().getParamValue())) {
            getQueryForOVBK(params, startDate, endDate, selectedRTs, query);
        }

        if (StringUtils.equalsIgnoreCase(ALL, params.getInventoryDecisionType().getParamValue()) || StringUtils.equalsIgnoreCase(INVENTORY_LIMIT, params.getInventoryDecisionType().getParamValue())) {
            getQueryForInventoryLimit(params, startDate, endDate, selectedRTs, query);
        }

        if (StringUtils.equalsIgnoreCase(ALL, params.getInventoryDecisionType().getParamValue()) ||
                StringUtils.equalsIgnoreCase(InventoryDecisionType.PRICING.getParamValue(), params.getInventoryDecisionType().getParamValue())) {
            getQueryForPricing(params, startDate, endDate, selectedRTs, query);
        }
        return tenantCrudService.findByNativeQuerySingleResult(query.toString(), null);
    }

    private void getQueryForPricing(InventoryHistoryParams params, String startDate, String endDate, String selectedRTs, StringBuilder query) {
        query.append(PLUS_OPEN)
                .append(" select count(*) from ");
        queryForSelectingRowNumber(params, query);
        query.append(DECISION_TYPE)
                .append("'" + DAILY_BAR + "' ")
                .append(ACCOM_TYPE_NOT_NULL)
                .append(OCCUPANCY_DATE_BETWEEN)
                .append("'" + startDate + AND + endDate + "' ")
                .append(RATE_QUALIFIED_NULL)
                .append(RATE_UNQUALIFIED_NOT_NULL)
                .append(OCCUPANCY_DATE_BETWEEN)
                .append("'" + startDate + AND + endDate + "' ");
        appendClauseIfNeeded(params.getSelectedRateLevels(), query, RATE_UNQUALIFIED_IN);
        appendClauseIfNeeded(selectedRTs, query, ACCOM_TYPE_IN);
        filterClause(params, query);
        query.append(CLOSE);
    }

    private void queryForSelectingRowNumber(InventoryHistoryParams params, StringBuilder query) {
        if (params.isIncludePace()) {
            query.append(" Decision_Ack_Status ");
        } else {
            query.append(" (select *, ROW_NUMBER() over (PARTITION BY occupancy_date,rate_unqualified_id,rate_qualified_id,accom_type_id,decision_type " +
                    " order by Decision_Date_Time desc) \n" +
                    " as row_num from\n" +
                    " Decision_Ack_Status ");
        }
    }

    private void filterClause(InventoryHistoryParams params, StringBuilder query) {
        if (!params.isIncludePace()) {
            query.append(" ) base where row_num = 1 ");
        }
        query.append("and ((" + params.isIncludeSuccessfulControls() + " = 1 and Error_Code = 0) " +
                " or (" + params.isIncludeFailures() + " = 1 and Error_Code != 0))");
    }

    private void getQueryForOVBK(InventoryHistoryParams params, String startDate, String endDate, String selectedRTs, StringBuilder query) {
        if (shouldIncludePropertyOVBK(params)) {
            query.append(getOverBookingPropertyLevelQuery(startDate, endDate, params));
        }
        if (shpuldIncludeRTLevelOVBK(params)) {
            query.append(getOverBookingRTLevelQuery(startDate, endDate, selectedRTs, params));
        }
    }

    private void getQueryForFPLOS(InventoryHistoryParams params, String startDate, String endDate, String lv0ID, String selectedSRP, String selectedRTs, StringBuilder query) {
        if (shouldIncludeFPLOS(params)) {
            if (params.isSRPFPLOSAtTotalEnabled()) {
                query.append(getFPLOSTotalLevelQuery(lv0ID, startDate, endDate, selectedSRP, params));
            } else {
                query.append(getFPLOSRTLevelQueryForRTLevelSRPsAltered(startDate, endDate, selectedSRP, selectedRTs, params));
            }
            if (params.isLV0Selected()) {
                query.append(getFPLOSForLV0Query(lv0ID, startDate, endDate, selectedRTs, params));
            }
        }
        if (StringUtils.equalsIgnoreCase(InventoryDecisionType.ALL.getParamValue(), params.getInventoryDecisionType().getParamValue()) || StringUtils.equalsIgnoreCase(InventoryFPLOSLevel.RATE_LEVEL.getParamValue(), params.getLevel()) || StringUtils.equalsIgnoreCase(InventoryFPLOSLevel.RATE_LEVEL_AND_SRP.getParamValue(), params.getLevel())) {
            query.append(getFPLOSForUnqualifiedRatesQuery(startDate, endDate, params.getSelectedRateLevels(), params));
        }
    }

    private String getFPLOSRTLevelQueryForRTLevelSRPsAltered(String startDate, String endDate, String selectedSRP, String selectedRTs, InventoryHistoryParams params) {
        StringBuilder query = new StringBuilder();
        query.append(PLUS_OPEN);
        query.append("select COUNT (*) " +
                "            from " +
                "            ( " +
                "                select " +
                "                    Rate_Qualified_ID, Accom_Type_ID,Error_Code, " +
                "                    ROW_NUMBER() over (partition by Rate_Qualified_ID, Accom_Type_ID, Occupancy_Date order by Decision_Date_Time desc) rank " +
                "                from " +
                "                ( " +
                "                    select " +
                "                        ISNULL(VW.Rate_Qualified_ID, PACE.Rate_Qualified_ID) as Rate_Qualified_ID, " +
                "                        ISNULL(VW.Accom_Type_ID, PACE.Accom_Type_ID) as Accom_Type_ID, " +
                "                        ISNULL(VW.Decision_Date_Time, PACE.CreateDate_DTTM) as Decision_Date_Time, " +
                "                        ISNULL(VW.Occupancy_Date, PACE.Arrival_DT) as Occupancy_Date, " +
                "                        ISNULL(VW.Error_Code, 0) Error_Code " +
                "                    from " +
                "                    ( " +
                "                        select Decision_ID, Rate_Qualified_ID, Accom_Type_ID, Arrival_DT, FPLOS, CreateDate_DTTM " +
                "                        from Pace_Qualified_FPLOS " +
                "                        where Arrival_DT between " + "'" + startDate + AND + endDate + "' " +
                "                        and not exists (select 1 from Pace_From_Service " +
                "                                        where Decision_ID = Pace_Qualified_FPLOS.Decision_ID " +
                "                                        and Rate_Qualified_ID = Pace_Qualified_FPLOS.Rate_Qualified_ID " +
                "                                        and Accom_Type_ID = Pace_Qualified_FPLOS.Accom_Type_ID " +
                "                                        and Arrival_DT = Pace_Qualified_FPLOS.Arrival_DT " +
                "                                        and CreateDate_DTTM > Pace_Qualified_FPLOS.CreateDate_DTTM " +
                "                                        ) " +
                "                        union all " +
                "                        select Decision_ID, Rate_Qualified_ID, Accom_Type_ID, Arrival_DT, FPLOS, CreateDate_DTTM " +
                "                        from Pace_From_Service " +
                "                        where Arrival_DT between " + "'" + startDate + AND + endDate + "' " +
                "                        and not exists (select 1 from Pace_Qualified_FPLOS " +
                "                                        where Decision_ID = Pace_From_Service.Decision_ID " +
                "                                        and Rate_Qualified_ID = Pace_From_Service.Rate_Qualified_ID " +
                "                                        and Accom_Type_ID = Pace_From_Service.Accom_Type_ID " +
                "                                        and Arrival_DT = Pace_From_Service.Arrival_DT " +
                "                                        and CreateDate_DTTM > Pace_From_Service.CreateDate_DTTM " +
                "                                       ) " +

                "                    ) PACE " +
                "                    full outer join " +
                "                    ( " +
                "                        select Decision_ID, Rate_Qualified_ID, Accom_Type_ID, Occupancy_Date, " +
                "                            Error_Code, Decision_Date_Time " +
                "                        from Vw_Qualified_FPLOS_Ack_Status " +
                "                        where Occupancy_Date between " + "'" + startDate + AND + endDate + "' " +
                "                    ) VW " +
                "                    on PACE.Rate_Qualified_ID = VW.Rate_Qualified_ID " +
                "                    and PACE.Accom_Type_ID = VW.Accom_Type_ID " +
                "                    and PACE.Arrival_DT = VW.Occupancy_Date " +
                "                    and PACE.Decision_ID = VW.Decision_ID " +
                "                ) A " +
                "            ) Results " +
                "            join Rate_Qualified Rates on Rates.Rate_Qualified_ID = Results.Rate_Qualified_ID ");
        appendClauseIfNeeded(selectedSRP, query, " and Rates.Rate_Qualified_ID in ");
        query.append("       join Accom_Type AT on AT.Accom_Type_ID = Results.Accom_Type_ID ");
        appendClauseIfNeeded(selectedRTs, query, " and AT.Accom_Type_ID in ");
        query.append("       left join Limit_Total_Rate_Qualified limitTotalSRPs on Results.Rate_Qualified_ID = limitTotalSRPs.Limit_Total_Rate_Qualified_ID " +
                "            where " +
                "                (" + (params.isIncludePace() ? 1 : 0) + " = 1 or Results.rank = 1) " +
                "                and ((" + params.isIncludeSuccessfulControls() + " = 1 and Results.Error_Code = 0) or (" + params.isIncludeFailures() + " = 1 and Results.Error_Code <> 0)) " +
                "                and Rates.Rate_Code_Name <> 'LV0' ");
        query.append(CLOSE);
        return query.toString();
    }

    private void appendClauseIfNeeded(String selectedFieldValues, StringBuilder query, String field) {
        if (isNotAll(selectedFieldValues)) {
            query.append(field)
                    .append(OPEN + selectedFieldValues + CLOSE);
        }
    }

    private boolean shouldIncludeFPLOS(InventoryHistoryParams params) {
        return StringUtils.equalsIgnoreCase(InventoryDecisionType.ALL.getParamValue(), params.getInventoryDecisionType().getParamValue()) ||
                StringUtils.equalsIgnoreCase(InventoryFPLOSLevel.SRP.getParamValue(), params.getLevel()) ||
                StringUtils.equalsIgnoreCase(InventoryFPLOSLevel.RATE_LEVEL_AND_SRP.getParamValue(), params.getLevel());
    }

    private boolean shpuldIncludeRTLevelOVBK(InventoryHistoryParams params) {
        return StringUtils.equalsIgnoreCase(InventoryDecisionType.ALL.getParamValue(), params.getInventoryDecisionType().getParamValue()) || StringUtils.equalsIgnoreCase(InventoryOverbookingLevel.HOUSE_AND_ROOM_TYPE.getParamValue(), params.getLevel())
                || StringUtils.equalsIgnoreCase(InventoryOverbookingLevel.ROOM_TYPE.getParamValue(), params.getLevel());
    }

    private boolean shouldIncludePropertyOVBK(InventoryHistoryParams params) {
        return StringUtils.equalsIgnoreCase(InventoryDecisionType.ALL.getParamValue(), params.getInventoryDecisionType().getParamValue()) || StringUtils.equalsIgnoreCase(InventoryOverbookingLevel.HOUSE_AND_ROOM_TYPE.getParamValue(), params.getLevel())
                || StringUtils.equalsIgnoreCase(InventoryOverbookingLevel.HOUSE.getParamValue(), params.getLevel());
    }

    private Integer getDays(String date) {
        if (StringUtils.equalsIgnoreCase("TODAY", date)) {
            return 0;
        } else {
            return Integer.valueOf(date.replace("TODAY", ""));
        }
    }

    private boolean isNotAll(String selectedFields) {
        return !StringUtils.equalsIgnoreCase(ALL, selectedFields);
    }

    private String getFPLOSTotalLevelQuery(String lv0ID, String startDate, String endDate, String selectedSRP, InventoryHistoryParams params) {
        StringBuilder query = new StringBuilder();
        query.append(PLUS_OPEN)
                .append(" select count(*) from " +
                        "            (  " +
                        "                select " +
                        "                    Rate_Qualified_ID, Occupancy_Date, Error_Code, " +
                        "                    ROW_NUMBER() over (partition by Rate_Qualified_ID, Occupancy_Date order by Decision_Date_Time desc) rank " +
                        "                from " +
                        "                ( " +
                        "                    select " +
                        "                        ISNULL(VW.Rate_Qualified_ID, PACE.Rate_Qualified_ID) as Rate_Qualified_ID, " +
                        "                        ISNULL(VW.Occupancy_Date, PACE.Arrival_DT) as Occupancy_Date, " +
                        "                        ISNULL(VW.Error_Code, 0) Error_Code, " +
                        "                        ISNULL(VW.Decision_Date_Time, PACE.CreateDate_DTTM) as Decision_Date_Time " +
                        "                    from " +
                        "                    ( " +
                        "                        select Decision_ID, Rate_Qualified_ID, Arrival_DT, FPLOS, CreateDate_DTTM " +
                        "                        from Pace_Qualified_FPLOS " +
                        "                        where Arrival_DT between " + "'" + startDate + AND + endDate + "' " +
                        "                        and not exists (select 1 from Pace_From_Service " +
                        "                                                 where Decision_ID = Pace_Qualified_FPLOS.Decision_ID " +
                        "                                                 and Rate_Qualified_ID = Pace_Qualified_FPLOS.Rate_Qualified_ID " +
                        "                                                 and Arrival_DT = Pace_Qualified_FPLOS.Arrival_DT " +
                        "                                                 and CreateDate_DTTM > Pace_Qualified_FPLOS.CreateDate_DTTM " +
                        "                                        ) " +
                        "                        union all " +
                        "                        select Decision_ID, Rate_Qualified_ID, Arrival_DT, FPLOS, CreateDate_DTTM " +
                        "                        from Pace_From_Service " +
                        "                        where Arrival_DT between " + "'" + startDate + AND + endDate + "' " +
                        "                        and not exists (select 1 from Pace_Qualified_FPLOS " +
                        "                                                 where Decision_ID = Pace_From_Service.Decision_ID " +
                        "                                                 and Rate_Qualified_ID = Pace_From_Service.Rate_Qualified_ID " +
                        "                                                 and Arrival_DT = Pace_From_Service.Arrival_DT " +
                        "                                                 and CreateDate_DTTM > Pace_From_Service.CreateDate_DTTM " +
                        "                                       ) " +
                        "                    ) PACE " +
                        "                    full outer join " +
                        "                    ( " +
                        "                        select Decision_ID, Rate_Qualified_ID, Occupancy_Date, Decision_Date_Time, Error_Code  " +
                        "                        from Vw_Qualified_FPLOS_Ack_Status " +
                        "                        where Occupancy_Date between " + "'" + startDate + AND + endDate + "' " +
                        "                    ) VW " +
                        "                    on PACE.Rate_Qualified_ID = VW.Rate_Qualified_ID " +
                        "                    and PACE.Arrival_DT = VW.Occupancy_Date " +
                        "                    and PACE.Decision_ID = VW.Decision_ID " +
                        "                ) A " +
                        "            ) Results " +
                        "            join Rate_Qualified Rates on Rates.Rate_Qualified_ID = Results.Rate_Qualified_ID  ");
        appendClauseIfNeeded(selectedSRP, query, " and Rates.Rate_Qualified_ID in ");
        query.append("            where " +
                "                (" + (params.isIncludePace() ? 1 : 0) + " = 1 or Results.rank = 1) " +
                "                and ((" + params.isIncludeSuccessfulControls() + " = 1 and Results.Error_Code = 0) or (" + params.isIncludeFailures() + " = 1 and Results.Error_Code <> 0)) " +
                "                and Rates.Rate_Code_Name <> 'LV0' ");

        query.append(CLOSE);

        return query.toString();
    }

    private String getFPLOSRTLevelQueryForLimitTotalSRPs(String lv0ID, String startDate, String endDate, String selectedSRP, InventoryHistoryParams params) {
        StringBuilder query = new StringBuilder();
        query.append(PLUS_OPEN)
                .append(" select count(*) from ");
        if (params.isIncludePace()) {
            query.append(" decision_ack_status ");
        } else {
            query.append(" ( select *, ROW_NUMBER() over (PARTITION BY occupancy_date,rate_unqualified_id,rate_qualified_id,accom_type_id,decision_type  order by Decision_Date_Time desc) \n" +
                    " as row_num from decision_ack_status ");
        }
        query.append(" decisions ")
                .append(" left join Limit_Total_Rate_Qualified limitTotalSRPs ")
                .append(" on decisions.Rate_Qualified_ID = limitTotalSRPs.Limit_Total_Rate_Qualified_ID ")
                .append(DECISION_TYPE)
                .append("'" + FPLOS + "' ")
                .append(RATE_UNQUALIFIED_NULL)
                .append(RATE_QUALIFIED_NOT_IN)
                .append(lv0ID)
                .append(ACCOM_TYPE_NULL)
                .append(RATE_QUALIFIED_NOT_NULL)
                .append(OCCUPANCY_DATE_BETWEEN)
                .append("'" + startDate + AND + endDate + "' ")
                .append(" and limitTotalSRPs.Limit_Total_Rate_Qualified_ID is not null ");
        appendClauseIfNeeded(selectedSRP, query, RATE_QUALIFIED_IN);
        filterClause(params, query);
        query.append(CLOSE);
        return query.toString();
    }

    private String getFPLOSRTLevelQueryForRTLevelSRPs(String lv0ID, String startDate, String endDate, String selectedSRP, String selectedRTs, InventoryHistoryParams params) {
        StringBuilder query = new StringBuilder();
        query.append(PLUS_OPEN)
                .append(" select count(*) from ");
        if (params.isIncludePace()) {
            query.append(" decision_ack_status ");
        } else {
            query.append(" ( select *, ROW_NUMBER() over (PARTITION BY occupancy_date,rate_unqualified_id,rate_qualified_id,accom_type_id,decision_type  order by Decision_Date_Time desc) \n" +
                    " as row_num from decision_ack_status ");
        }
        query.append(" decisions ")
                .append(" left join Limit_Total_Rate_Qualified limitTotalSRPs ")
                .append(" on decisions.Rate_Qualified_ID = limitTotalSRPs.Limit_Total_Rate_Qualified_ID ")
                .append(DECISION_TYPE)
                .append("'" + FPLOS + "' ")
                .append(RATE_UNQUALIFIED_NULL)
                .append(RATE_QUALIFIED_NOT_IN)
                .append(lv0ID)
                .append(ACCOM_TYPE_NOT_NULL)
                .append(RATE_QUALIFIED_NOT_NULL)
                .append(OCCUPANCY_DATE_BETWEEN)
                .append("'" + startDate + AND + endDate + "' ")
                .append(" and limitTotalSRPs.Limit_Total_Rate_Qualified_ID is null ");
        appendClauseIfNeeded(selectedSRP, query, RATE_QUALIFIED_IN);
        appendClauseIfNeeded(selectedRTs, query, ACCOM_TYPE_IN);
        filterClause(params, query);
        query.append(CLOSE);
        return query.toString();
    }

    private String getFPLOSForUnqualifiedRatesQuery(String startDate, String endDate, String selectedRateLevels, InventoryHistoryParams params) {
        StringBuilder query = new StringBuilder();
        query.append(PLUS_OPEN)
                .append(" select count(*) from ");
        queryForSelectingRowNumber(params, query);
        query.append(DECISION_TYPE)
                .append("'" + FPLOS + "' ")
                .append(RATE_UNQUALIFIED_NOT_NULL)
                .append(RATE_QUALIFIED_NULL)
                .append(OCCUPANCY_DATE_BETWEEN)
                .append("'" + startDate + AND + endDate + "' ");
        appendClauseIfNeeded(selectedRateLevels, query, RATE_UNQUALIFIED_IN);
        filterClause(params, query);
        query.append(CLOSE);
        return query.toString();
    }

    private String getOverBookingRTLevelQuery(String startDate, String endDate, String selectedRTs, InventoryHistoryParams params) {
        StringBuilder query = new StringBuilder();
        query.append(PLUS_OPEN)
                .append(" select count(*) from ");
        queryForSelectingRowNumber(params, query);
        query.append(DECISION_TYPE)
                .append("'" + OVBK + "' ")
                .append(ACCOM_TYPE_NOT_NULL)
                .append(OCCUPANCY_DATE_BETWEEN)
                .append("'" + startDate + AND + endDate + "' ");
        appendClauseIfNeeded(selectedRTs, query, ACCOM_TYPE_IN);
        filterClause(params, query);
        query.append(CLOSE);
        return query.toString();
    }

    private String getOverBookingPropertyLevelQuery(String startDate, String endDate, InventoryHistoryParams params) {
        StringBuilder query = new StringBuilder();
        query.append(PLUS_OPEN)
                .append(" select count(*) from ");
        queryForSelectingRowNumber(params, query);
        query.append(DECISION_TYPE)
                .append("'" + OVBK + "' ")
                .append(ACCOM_TYPE_NULL)
                .append(OCCUPANCY_DATE_BETWEEN)
                .append("'" + startDate + AND + endDate + "' ");
        filterClause(params, query);
        query.append(CLOSE);
        return query.toString();
    }

    private String getFPLOSForLV0Query(String lv0ID, String startDate, String endDate, String selectedRTs, InventoryHistoryParams params) {
        StringBuilder query = new StringBuilder();
        query.append(PLUS_OPEN)
                .append(" select count(*) from ");
        queryForSelectingRowNumber(params, query);
        query.append(DECISION_TYPE)
                .append("'" + FPLOS + "' ")
                .append(RATE_UNQUALIFIED_NULL)
                .append(RATE_QUALIFIED_IN)
                .append(OPEN + lv0ID + CLOSE)
                .append(ACCOM_TYPE_NOT_NULL)
                .append(RATE_QUALIFIED_NOT_NULL)
                .append(OCCUPANCY_DATE_BETWEEN)
                .append("'" + startDate + AND + endDate + "' ");
        appendClauseIfNeeded(selectedRTs, query, ACCOM_TYPE_IN);
        filterClause(params, query);
        query.append(CLOSE);
        return query.toString();
    }

    public List<Product> getProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_OR_UPLOAD_ENABLED_PRODUCTS);
    }

    public List<Product> getAllProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_OR_UPLOAD_ENABLED_PRODUCTS);
    }

    public boolean isHiltonCP() {
        return propertyConfigParamService.isCP();
    }

    public String getHiltonCPSRPName() {
        return propertyConfigParamService.hiltonCPSRPName();
    }

    public String getVirtualPropertyDisplayCode(Integer propertyId) {
        return propertyService.getVirtualPropertyDisplayCode(propertyId);
    }

    public RecommendationResponse fetchPaceDataFromService(InventoryHistoryParams params) {
        var startDate = params.getStartDate();
        var endDate = params.getEndDate();

        if (params.isRollingDate()) {
            var caughtUpDate = dateService.getCaughtUpLocalDate();
            startDate = caughtUpDate.plusDays(getDays(params.getRollingStartDate())).toString();
            endDate = caughtUpDate.plusDays(getDays(params.getRollingEndDate())).toString();
        }

        var rateIdsAndRateLevelIds = getSelectedRateIdsAndRateLevelIds(params);
        var accomTypeIds = getSelectedAccomTypeIds(params);

        var pace = fplosService.retrievePace(Date.valueOf(startDate), Date.valueOf(endDate), rateIdsAndRateLevelIds, accomTypeIds);
        return pace;
    }

    public List<Integer> getSelectedAccomTypeIds(InventoryHistoryParams params) {
        var selectedAccomTypes = params.getSelectedRoomTypes();
        var selectedAccomTypeIds = ALL.equalsIgnoreCase(selectedAccomTypes) ? getAllAccomTypeIds() : getListOfIds(selectedAccomTypes);
        return selectedAccomTypeIds;
    }

    private List<Integer> getAllAccomTypeIds() {
        return accommodationService.getAllActiveAccomTypes()
                .stream()
                .map(AccomType::getId)
                .collect(Collectors.toList());
    }

    public List<Integer> getSelectedRateIdsAndRateLevelIds(InventoryHistoryParams params) {
        var combinedIds = new ArrayList<Integer>();
        var selectedSRPs = params.getSelectedSRP();
        var selectedRateLevels = params.getSelectedRateLevels();

        var selectedSRPIds = ALL.equalsIgnoreCase(selectedSRPs) ? getAllSRPIds() : getListOfIds(selectedSRPs);
        var selectedRateLevelIds = ALL.equalsIgnoreCase(selectedRateLevels) ? getAllRateLevelIds() : getListOfIds(selectedRateLevels);
        combinedIds.addAll(selectedSRPIds);
        combinedIds.addAll(selectedRateLevelIds);

        return combinedIds;
    }

    private List<Integer> getAllRateLevelIds() {
        return rateUnqualifiedService.getRatesForInventoryHistory()
                .stream()
                .map(RateUnqualified::getId)
                .collect(Collectors.toList());
    }

    private List<Integer> getAllSRPIds() {
        return rateQualifiedService.getRateQualifiedByProperty()
                .stream()
                .map(RateQualified::getId)
                .collect(Collectors.toList());
    }

    private List<Integer> getListOfIds(String selected) {
        return Stream.of(selected)
                .flatMap(s -> Arrays.stream(s.split(",")))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    public void truncatePaceFromServiceTable() {
        var queryStr = "Truncate table Pace_From_Service;";
        var t2 = System.currentTimeMillis();
        tenantCrudService.executeUpdateByNativeQuery(queryStr);
        log.info("Deletion of records from Pace_From_Service table took {} milliSeconds.", System.currentTimeMillis() - t2);
    }

    private boolean isPaceReportingEnabledAndDecisionTypeFPLOS(InventoryHistoryParams params) {
        return configParamsService.getBooleanParameterValue(ENABLE_PACE_REPORTING_FROM_SERVICE)
                && (params.getInventoryDecisionType().equals(InventoryDecisionType.FPLOS) || params.getInventoryDecisionType().equals(InventoryDecisionType.ALL));
    }

    public void populatePace(RecommendationResponse pace) {
        if (pace.getCompressed().isEmpty()) {
            return;
        }

        var batchDtos = new ArrayList<PaceFromServiceBatchDto>();
        var tempBatchDtos = new ArrayList<PaceFromServiceBatchDto>();
        var rateIterator = pace.getCompressed().entrySet().iterator();

        while (rateIterator.hasNext()) {
            tempBatchDtos.clear();
            var ratesEntry = rateIterator.next();
            var rateId = ratesEntry.getKey();
            var dates = GzipCompressionHelper.decompressFromBytes(ratesEntry.getValue()).getDatesMap();

            for (var dateEntry : dates.entrySet()) {
                var date = dateEntry.getKey();
                var accoms = dateEntry.getValue();
                var dateFrom = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());

                for (var accomsEntry : accoms.getAccomTypesMap().entrySet()) {
                    var accomId = accomsEntry.getKey();
                    var decisions = accomsEntry.getValue();

                    for (var decision : decisions) {
                        if (decision != null) {
                            tempBatchDtos.add(new PaceFromServiceBatchDto(
                                    dateFrom,
                                    rateId,
                                    accomId,
                                    decision.getFplos(),
                                    decision.getDecisionId(),
                                    getDecisionCreateDateTime(decision.getDecisionId())
                            ));
                        }
                    }
                }
            }
            batchDtos.addAll(tempBatchDtos);
        }
        tenantCrudService.execute(PaceFromServiceBatchDto.PACE_FROM_SERVICE_INSERT, batchDtos);
    }

    private java.util.Date getDecisionCreateDateTime(Integer decisionId) {
        var decision = decisionService.find(decisionId);
        return decision != null ? decision.getCreateDate() : new java.util.Date();
    }

    private String getInventoryLimitPropertyLevelQuery(String startDate, String endDate, InventoryHistoryParams params) {
        StringBuilder query = new StringBuilder();
        query.append(PLUS_OPEN)
                .append(" select count(*) from ");
        queryForSelectingRowNumber(params, query);
        query.append(DECISION_TYPE)
                .append("'" + INVENTORY_LIMIT + "' ")
                .append(ACCOM_TYPE_NULL)
                .append(OCCUPANCY_DATE_BETWEEN)
                .append("'" + startDate + AND + endDate + "' ");
        filterClause(params, query);
        query.append(CLOSE);
        return query.toString();
    }

    private boolean shouldIncludePropertyInventoryLimit(InventoryHistoryParams params) {
        return StringUtils.equalsIgnoreCase(InventoryDecisionType.ALL.getParamValue(), params.getInventoryDecisionType().getParamValue()) || StringUtils.equalsIgnoreCase(InventoryOverbookingLevel.HOUSE.getParamValue(), params.getLevel());
    }

    private void getQueryForInventoryLimit(InventoryHistoryParams params, String startDate, String endDate, String selectedRTs, StringBuilder query) {
        if (shouldIncludePropertyInventoryLimit(params)) {
            query.append(getInventoryLimitPropertyLevelQuery(startDate, endDate, params));
        }
    }
}
