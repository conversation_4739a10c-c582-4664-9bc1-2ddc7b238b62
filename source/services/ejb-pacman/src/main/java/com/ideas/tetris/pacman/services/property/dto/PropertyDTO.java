package com.ideas.tetris.pacman.services.property.dto;

import com.ideas.tetris.platform.services.daoandentities.entity.Property;

public class PropertyDTO {
    private final Integer id;
    private final String name;
    private final String code;
    private final String displayCode;
    private final String clientCode;
    private final Integer clientId;


    public PropertyDTO(Property property) {
        this.id = property.getId();
        this.name = property.getName();
        this.code = property.getCode();
        this.displayCode = property.isVirtualProperty() && null != property.getVirtualPropertyDisplayCode() ?
                property.getVirtualPropertyDisplayCode() : property.getCode();
        this.clientCode = property.getClient().getCode();
        this.clientId = property.getClient().getId();
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public String getClientCode() {
        return clientCode;
    }

    public Integer getClientId() {
        return clientId;
    }

    public String getDisplayCode() {
        return displayCode;
    }
}
