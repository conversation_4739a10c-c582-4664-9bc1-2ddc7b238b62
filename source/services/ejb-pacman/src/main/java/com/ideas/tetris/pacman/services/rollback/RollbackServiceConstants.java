package com.ideas.tetris.pacman.services.rollback;

final class RollbackServiceConstants {

    static final String TABLE_INFO_MGR_EXCEPTION_NOTIFICATION_CONFIG = "Info_Mgr_Excep_Notif_Config";
    static final String TABLE_IP_CONFIG_PROCESS_GROUP = "IP_Cfg_Process_Group";
    static final String TABLE_IP_CONFIG_HORIZON_GROUP = "IP_Cfg_Horizon_Group";
    static final String TABLE_IP_CONFIG_HORIZON_GROUP_DETAIL = "IP_Cfg_Horizon_Group_Detail";
    static final String TABLE_MARKET_SEGMENT = "Mkt_Seg";
    static final String TABLE_FILE_METADATA = "File_Metadata";
    static final String TABLE_RATE_UNQUALIFIED = "Rate_Unqualified";
    static final String TABLE_IP_CFG_PROPERTY_ATTRIBUTE = "IP_Cfg_Property_Attribute";
    static final String TABLE_MKT_SEG_DETAILS = "mkt_seg_details";
    static final String TABLE_MKT_SEG_DETAILS_PROPOSED = "mkt_seg_details_proposed";
    static final String TABLE_G3_GROUP_MASTER_LINK = "G3_Group_Master_Link";
    static final String TABLE_GROUP_MASTER = "Group_Master";
    static final String TABLE_PACE_GROUP_MASTER = "Pace_Group_Master";
    static final String TABLE_RESERVATION_NIGHT_CHANGE = "Reservation_Night_Change";
    static final String TABLE_RESERVATION_NIGHT = "Reservation_Night";
    static final String TABLE_POST_DEPARTURE_REVENUE = "POST_DEPARTURE_REVENUE";

    /*
     * Order matters due to foreign key constraints
     * Important foreign keys:
     * Rate_Qualified has on File_Metadata
     * Rate_Unqualified has on File_Metadata
     * Rate_Qualified_Mkt_Seg has on Mkt_Seg
     * Rate_Unqualified_Mkt_Seg has on Mkt_Seg
     * Arrival_Demand_FCST_OVR has on Rate_Unqualified
     * Decision_Bar_Output has on Rate_Unqualified
     */
    static final String[] TABLES_TENANT_GENERAL = new String[]{
            "ACTIVE_SRPS",
            "PACE_Accom_Occupancy_FCST",
            "PACE_Accom_Occupancy_FCST_NOTIFICATION",
            "PACE_Bar_Output",
            "PACE_Bar_Output_NOTIFICATION",
            "PACE_Bar_Output_Upload",
            "PACE_Dailybar_Output",
            "Pace_Dailybar_Output_NonHiltonCRS",
            "PACE_FPLOS_By_Rank",
            "PACE_FPLOS_By_Hierarchy",
            "PACE_FPLOS_By_RoomType",
            // No identity
            "PACE_LRV",
            "PACE_LRV_AT",
            "PACE_LRV_NOTIFICATION",
            "PACE_MINLOS",
            "PACE_Mkt_Activity",
            "Current_Mkt_Accom_activity",
            "PACE_Mkt_Occupancy_FCST",
            "PACE_Mkt_Occupancy_FCST_NOTIFICATION",
            "PACE_Ovrbk_Accom",
            "Pace_Ovrbk_Accom_NOTIFICATION",
            "PACE_Ovrbk_Accom_Upload",
            "PACE_Ovrbk_Property",
            "pace_Ovrbk_Property_NOTIFICATION",
            "PACE_Ovrbk_Property_Upload",
            "PACE_Qualified_FPLOS",

            "PACE_Decision_LRA_FPLOS",
            "PACE_Decision_LRA_minLOS",
            "PACE_Manual_Restriction_Accom_OVR",
            "PACE_Manual_Restriction_Property_OVR",
            // No identity
            "Arr_Dep_FCST",
            "Analytic_Mkt_Accom_Los_Inv",
            "Immediate_Full_Decisions",

            "CR_Mkt_Accom_Activity",
            "PACE_CR_Accom_Activity",
            "Temp_stage_occupancy_summary_PSAT_CSAT",
            "Arrival_Demand_FCST",
            "Arrival_Demand_FCST_OVR",
            "Business_Accom_FCST",
            "Channel_Src_FCST",
            "Channel_Src_Daily_FCST",
            "Decision_Ack_Status",
            "MP_Decision_Ack_Status",
            "Decision_Bar_Output",
            "Decision_COW_Value_OVR",
            "Decision_Bar_Output_OVR_Details",
            "Decision_Bar_Output_OVR",
            "Decision_Dailybar_Output",
            "Decision_Dailybar_Output_NonHiltonCRS",
            "Decision_FPLOS_By_Hierarchy",
            "Decision_FPLOS_By_RoomType",
            "Decision_FPLOS_By_Rank",
            "Decision_LRA_FPLOS",
            "Decision_LRA_minLOS",
            "Decision_LRV",
            "Decision_LRV_AT",
            "Decision_MINLOS",
            "Decision_Ovrbk_Accom_OVR",
            "Decision_Ovrbk_Accom",
            "Decision_Ovrbk_Property",
            "Decision_Ovrbk_Property_OVR",
            "Decision_Qualified_FPLOS",
            "Decision_Upload_Date_To_External_System",
            "Daily_Briefing",
            "VP_Ovrbk_Property_Split_Ratio",
            "Forecast_Group_Mkt_Seg_Proposed",
            "Forecast_Group_Proposed",
            "Monitor_Forecast_Accuracy",
            "Unexpected_Demand_Notification_Details",
            "VP_GP_Inv_Limit_Property_Split_Ratio",

            // Delete Continuous Pricing
            "CP_Pace_Decision_Bar_Output",
            "CP_Pace_Decision_Bar_Output_Differential",
            "CP_Unqualified_Demand_FCST_Price",
            "CP_Decision_Bar_Output_OVR",
            "CP_Decision_Bar_Output",
            "CP_Decision_Bar_NOVR",
            "CP_Decision_Bar_NOVR_Details",

            // Delete Limited Data Build
            "LDB_Generic_Pattern_Data_AUD",
            "LDB_Generic_Pattern_Data",
            "LDB_Config_AUD",
            "LDB_Config",
            "LDB_Hybrid_Accom_Type",
            "LDB_Build_History",
            "LDB_Clone_Pattern_Source_AUD",
            "LDB_Clone_Pattern_Source",
            "LDB_Generic_Booking_Curve_Point",
            "LDB_Generic_Booking_Curve",
            "LDB_Projection",
            "LDB_Projection_AUD",
            "LDB_Monthly_Projections",
            "LDB_MS_Monthly_Ratio",
            "LDB_Monthly_Ratio",
            "LDB_Total_Rooms_Sold_Revenue_Per_Month",
            "LDB_Total_Rooms_Sold_Revenue_Per_Month_AUD",
            "LDB_Total_Rooms_Sold_Revenue_Per_Month_Mkt_Seg",
            "LDB_Total_Rooms_Sold_Revenue_Per_Month_Mkt_Seg_AUD",
            "LDB_DOW_Projections",
            "LDB_ADR_BY_RC_Projections",

            // Delete Function Space
            "FS_Fcst_Eval_Override_AUD",
            "FS_Fcst_Eval_Override",
            "FS_Fcst_Override_AUD",
            "FS_Fcst_Override",
            "FS_Fcst",
            "FS_KPI",
            "FS_OTB",
            "Group_Final_Forecast_OVR",
            "Group_Final_Forecast_OVR_AUD",
            "FS_EVL_PKG_PRICING_DOS",
            "FS_EVL_PKG_PRICING_DOS_AUD",
            "FS_EVL_PKG_PRICING",
            "FS_EVL_PKG_PRICING_AUD",
            "Grp_Evl_Cost",
            "Grp_Evl_Cost_AUD",
            "Grp_Evl_Anc",
            "Grp_Evl_Anc_AUD",
            "Grp_Evl_Room_Type_Day_Of_Stay",
            "Grp_Evl_Room_Type_Day_Of_Stay_AUD",
            "Grp_Evl_Room_Type",
            "Grp_Evl_Room_Type_AUD",
            "Grp_Evl_Grp_Prc_Arr_DT_Pkg_AUD",
            "Grp_Evl_Grp_Prc_Arr_DT_Pkg",
            "Grp_Evl_Grp_Prc_Pkg_Revenue_Group_AUD",
            "Grp_Evl_Grp_Prc_Pkg_Revenue_Group",
            "Grp_Evl_Grp_Prc_Pkg_Detail_AUD",
            "Grp_Evl_Grp_Prc_Pkg_Detail",
            "Grp_Evl_Pkg_Pricing_DOS_AUD",
            "Grp_Evl_Pkg_Pricing_DOS",
            "Grp_Evl_Pkg_Pricing_AUD",
            "Grp_Evl_Pkg_Pricing",
            "Grp_Evl_On_Books_Current_BAR",
            "Grp_Evl_On_Books_Current_BAR_AUD",
            "Grp_Evl_On_Books",
            "Grp_Evl_On_Books_AUD",
            "Grp_Evl_Arr_DT_FG_DT_AC",
            "Grp_Evl_Arr_DT_FG_DT_AC_AUD",
            "Grp_Evl_Arr_DT_AT",
            "Grp_Evl_Arr_DT_AT_AUD",
            "Grp_Evl_Arr_DT_AC",
            "Grp_Evl_Arr_DT_AC_AUD",
            "Grp_Evl_Arr_DT_FG_DT",
            "Grp_Evl_Arr_DT_FG_DT_AUD",
            "Grp_Evl_Arr_DT_FG",
            "Grp_Evl_Arr_DT_FG_AUD",
            "Grp_Evl_Arr_DT_GR_Rates",
            "Grp_Evl_Arr_DT_GR_Rates_AUD",
            "Grp_Evl_Arr_DT_User_Adj",
            "Grp_Evl_Arr_DT_User_Adj_AUD",
            "Grp_Evl_Fct_Spc_Arr_DT_Day_Part",
            "Grp_Evl_Fct_Spc_Arr_DT_Day_Part_AUD",
            "Grp_Evl_Fct_Spc_Arr_DT",
            "Grp_Evl_Fct_Spc_Arr_DT_AUD",
            "Grp_Evl_Fct_Spc_Conf_Banq",
            "Grp_Evl_Fct_Spc_Conf_Banq_AUD",
            "Grp_Evl_Fct_Spc_Fct_Room",
            "Grp_Evl_Fct_Spc_Fct_Room_AUD",
            "Grp_Evl_Fct_Spc_Pkg_Revenue_Group",
            "Grp_Evl_Fct_Spc_Pkg_Revenue_Group_AUD",
            "Grp_Evl_Fct_Spc_Pkg_Day_Of_Stay",
            "Grp_Evl_Fct_Spc_Pkg_Day_Of_Stay_AUD",
            "Grp_Evl_Fct_Spc_Arr_DT_Pkg",
            "Grp_Evl_Fct_Spc_Arr_DT_Pkg_AUD",
            "Grp_Evl_Fct_Spc_Pkg_Detail",
            "Grp_Evl_Fct_Spc_Pkg_Detail_AUD",
            "Grp_Evl_Fct_Spc",
            "Grp_Evl_Fct_Spc_AUD",
            "Grp_Evl_Approval",
            "Grp_Evl_Approval_AUD",
            "Grp_Evl_Arr_DT",
            "Grp_Evl_Arr_DT_AUD",
            "Grp_Evl_Conf_Banq",
            "Grp_Evl_Conf_Banq_AUD",
            "Grp_Evl_Day_Of_Stay",
            "Grp_Evl_Day_Of_Stay_AUD",
            "Grp_Evl_AUD",
            "Grp_Evl",
            "GFF_FG_OVR",
            "GFF_FG_OVR_AUD",
            "Grp_Prc_Cfg_Anc_Assgn",
            "Grp_Prc_Cfg_Anc_Assgn_AUD",
            "Grp_Prc_Cfg_Anc_Assgn_Season",
            "Grp_Prc_Cfg_Anc_Assgn_Season_AUD",
            "Ovrd_Process_Group_Config",
            "IP_Cfg_DOW_Group_Detail",
            "IP_Cfg_Fcst_Group_Ovrd_Detail",
            "IP_Cfg_Fcst_Group_Ovrd",
            "IP_Cfg_Process_Group_Config",
            "IP_Cfg_Process_Group_Scope",
            "IP_Cfg_Process_Group_Stat",
            "IP_Cfg_Runtime_Param_Override",
            "IP_Cfg_Season_Group_Detail",
            "IP_Cfg_Season_Group",
            "LS_LOS_Group_UI_Mapping",
            "IP_Cfg_LOS_Group_Detail",
            "IP_Cfg_LOS_Group",
            "IP_Cfg_Mark_Property_Date",
            "Mkt_Accom_Activity",
            "Mkt_Seg_Forecast_Group",
            "Occupancy_Demand_FCST_OVR",
            "Occupancy_FCST_Prop_OVR",
            "Peak_Occupancy_Dmd_OVR",
            "Pricing_Sensitivity",
            "Occupancy_Demand_FCST",
            "Occupancy_FCST",
            "Occupancy_FCST_NOVR",
            "OPERATIONAL_FCST",
            "Ovrd_Booking_Curve_Pattern",
            "Ovrd_Noshows",
            "Ovrd_Rate_Forecast",
            "Ovrd_Reference_Price",
            "Ovrd_Special_Event_Pattern",

            "PP_Occupancy_FCST",
            "Unqualified_Demand_FCST_Price",
            "Wash_FCST",
            "Wash_Forecast_Group_FCST",
            "Wash_Forecast_Group_FCST_OVR",
            "Wash_Ind_Group_Fcst",
            "Wash_Ind_Group_Fcst_OVR",
            "Wash_Property_FCST",
            "IP_Cfg_DOW_Group",
            "ES_LongLos_Cfg",
            "Forecast_Group",
            "Decision_Restrict_Highest_Bar",
            "Decision_Restrict_Highest_Bar_OVR",
            "Decision",

            "Mkt_Seg_Process",
            "Info_Mgr_Job_Lookup",
            "Info_Mgr_Comments",
            "Info_Mgr_Eval_Execs",
            "Info_Mgr_Excep_Notif_Skip_dates",
            "Info_Mgr_Costly_Ooo_Excep_Snooze",
            "Info_Mgr_Instance_Step_state",
            "Info_Mgr_Sys_Health_Extended_Details",
            "Info_Mgr_Sys_Health_Details",
            "Info_Mgr_Sys_Health_Summary",
            "Info_Mgr_History",
            "Info_Mgr_Instance",
            "Vendor_Warning_Filter",

            //Delete Budget data
            "Budget_Config",
            "Budget_Config_AUD",
            "Budget_Data",
            "Budget_Data_AUD",
            "User_Forecast_Data",
            "User_Forecast_Data_AUD",
            "Occ_FCST_Org",
            "Last_Optimization_Total_Activity",
            "Last_Optimization_Accom_Activity",
            "Last_Optimization_Mkt_Accom_Activity",
            "property_onbooks_pace_alert",
            "Analytics_Trans",

            //LRA restriction mapping
            "LRA_Restriction_Mapping",
            "LRA_Restriction_Mapping_AUD",

            //Statistical Outlier Table Contains summary data
            "Main_Circuit_Breaker_Bar_Stats",
            "Main_Circuit_Breaker_Fcst_Solds_Rev_Stats",
            "Main_Circuit_Breaker_Lrv_Stats",
            "Main_Circuit_Breaker_Solds_Rev_Stats",
            "Main_Circuit_Breaker_Overbooking_Stats",
            "Decision_Anomaly_Smoke_Test_Result",
            "SYNC_DISPLAY_NAME",

            "Room_Type_Diff",

            "Profit_Adj_Fcst",

            "Business_Insights_Config_Param",

            "Pace_Operational_Mkt_Fcst",
            "Operational_Mkt_Fcst",
            "Operational_Mkt_Mapping",
            "Suboptml_Decision_Detail",
            "Pace_Profit_Adjustment",
            "Profit_Adjustment_Type",
            "reference_price_latest"
    };
    static final String[] PRESERVE_TABLES_AMS_REBUILD = new String[]{
            "PACE_Accom_Activity",
            "Pace_Group_Block",
            TABLE_PACE_GROUP_MASTER,
            "PACE_Total_Activity",
            "CR_Accom_Activity",
            "CR_Total_Activity",
            "Accom_Activity",
            "Mkt_Seg_Business_Group",
            "Group_Block",
            "Total_Activity",
            "Opera_Group_Block_Code",
            TABLE_GROUP_MASTER,
            "Hotel_Mkt_Accom_Activity",
            "Mkt_Seg_Details",
            "Mkt_Seg_Details_Proposed"
    };
    static final String[] TABLES_RATE_SHOPPING = new String[]{

            "Webrate",
            "PACE_Webrate",
            "PACE_Webrate_Differential",
            "Webrate_OVR_Competitor_DTLS",
            "Webrate_Default_Channel",
            "Webrate_Override_Channel",
            "Webrate_Competitors_Class",
            "Webrate_Override_Competitor",
            "Webrate_Ranking_Accom_Class",
            "Rate_Shopping_Adjustment",
            "ES_Competitor_Mapping",
            "Webrate_Competitors",
            "Webrate_Source_Property",
            "Webrate_Accom_Class_Mapping",
            "Webrate_Accom_Type",
            "Webrate_Channel",
            "Manual_Restriction_Accom_OVR",
            "Manual_Restriction_Property_OVR",
    };

    static final String[] TABLES_RDL_RATE_SHOPPING = new String[]{
            "Webrate",
            "PACE_Webrate",
            "PACE_Webrate_Differential",
            "Webrate_OVR_Competitor_DTLS",
            "Webrate_Default_Channel",
            "Webrate_Override_Channel",
            "Webrate_Competitors_Class",
            "Webrate_Override_Competitor",
            "Webrate_Ranking_Accom_Class",
            "Rate_Shopping_Adjustment",
            "ES_Competitor_Mapping",
            "Webrate_Source_Property",
            "Webrate_Accom_Class_Mapping",
            "Webrate_Accom_Type",
            "Manual_Restriction_Accom_OVR",
            "Manual_Restriction_Property_OVR",
    };

    static final String[] TABLES_DECISION = new String[]{
            "Decision_Ack_Status",
            "MP_Decision_Ack_Status",
            "Decision_Bar_Output",
            "Decision_COW_Value_OVR",
            "Decision_Bar_Output_OVR_Details",
            "Decision_Bar_Output_OVR",
            "Decision_Dailybar_Output",
            "Decision_FPLOS_By_Hierarchy",
            "Decision_FPLOS_By_RoomType",
            "Decision_FPLOS_By_Rank",
            "Decision_LRV",
            "Decision_MINLOS",
            "Decision_Ovrbk_Accom_OVR",
            "Decision_Ovrbk_Accom",
            "Decision_Ovrbk_Property",
            "Decision_Ovrbk_Property_OVR",
            "Decision_Qualified_FPLOS"
    };
    static final String[] TABLES_RATE_QUALIFIED = new String[]{
            "Limit_Total_Rate_Qualified",
            "Rate_Qualified_Fixed_Details",
            "Rate_Qualified_Fixed_Details_Stg",
            "Rate_Qualified_Fixed",
            "Rate_Qualified_Adjustment_final",
            "Rate_Qualified_Adjustment",
            "Rate_Qualified_Details",
            "Rate_Qualified_Mkt_Seg",
            "Rate_Qualified",
            "Semi_Yieldable_Rate_Details",
            "Rate_Protect_Config"

    };
    static final String[] TABLES_RATE_UNQUALIFIED = new String[]{
            "Rate_Unqualified_Closed",
            "Rate_Unqualified_Defaults",
            "Rate_Unqualified_Details",
            "Rate_Unqualified_LOS_Override",
            "Rate_Unqualified_Mkt_Seg",
            "Rate_Unqualified_User_Override",
            "Rate_Unqualified_Accom_Class"
    };
    static final String[] TABLES_OPERA = new String[]{
            "Adjusted_OOO",
            // No identity
            "Filtered_Transaction",
            TABLE_G3_GROUP_MASTER_LINK,
            "Raw_Group_Block",
            "Raw_Group_Master",
            "Raw_Incoming_Metadata",
            "Raw_Occupancy_Summary",
            "Raw_Transaction",
            "Raw_Yield_Currency",
            "Stage_Group_Block",
            "Stage_Group_Master",
            "Stage_Incoming_Metadata",
            "Stage_Occupancy_Summary",
            "Stage_Transaction",
            "Stage_Transaction_Change",
            "Stage_Yield_Currency",
            "Trans_RollUp_NonPace"
    };
    static final String YIELD_CATEGORY_RULE = "Yield_Category_Rule";
    public static final String TABLE_RESTORED_NO_SHOW_RESERVATION = "RESTORED_NO_SHOW_RESERVATION";
    static final String[] EXCLUDED_TABLES = new String[]{
            "Accom_Class",
            "Accom_Class_AUD",
            "Accom_Class_Inventory_Sharing",
            "Accom_Class_Inventory_Sharing_AUD",
            "Accom_Class_MinDiff",
            "Accom_Class_MinDiff_AUD",
            "Accom_Class_MinDiff_Season",
            "Accom_Class_MinDiff_Season_AUD",
            "Accom_Class_PriceRank_Network_Arrow",
            "Accom_Class_PriceRank_Path",
            "Accom_Class_PriceRank_Path_AUD",
            "Accom_Class_Sharing_Group",
            "Accom_Class_Sharing_Group_AUD",
            "Max_Allowed_Occupancy_AT",
            "Max_Allowed_Occupancy_AT_AUD",
            "Accom_Type",
            "Accom_Type_AUD",
            "Accom_Type_OverBooking",
            "Accom_Type_Vendor_Mapping",
            "Accom_Type_Vendor_Mapping_AUD",
            "Agile_Rates_DTA_Range",
            "Agile_Rates_DTA_Range_AUD",
            "Agile_Rates_Package",
            "Agile_Rates_Package_AUD",
            "Agile_Rates_Package_Charge_Type",
            "Agile_Rates_Package_Charge_Type_AUD",
            "Agile_Rates_Product_Group",
            "Agile_Rates_Product_Group_AUD",
            "Airbnb_Calendar",
            "Airbnb_Listing",
            "Vacation_Rental_Properties_Temp",
            "Ams_Occupancy_Summary",
            "Analytical_Mkt_Seg",
            "Analytical_Mkt_Seg_AUD",
            "AMS_Composition_Change",
            "Arrival_Demand_FCST_OVR_AUD",
            "Benefit_Mkt_Accom_Results",
            "Benefit_Occupancy_Results",
            "BM_OVRBK_Benefit",
            "BM_Policy",
            "BM_Request",
            "BM_Results",
            "BM_Results_Busy",
            "BM_Results_FG",
            "BM_Status",
            "Business_Group",
            "Business_Group_AUD",
            "Business_Type",
            "calendar_dim",
            "CDP_Schedule",
            "CommandLog",
            "Configuration_Automation_Activity_Details",
            "Configuration_Automation_Activity_Details_AUD",
            "Configuration_Automation_Details",
            "Configuration_Automation_Details_AUD",
            "CostofWalk_Default",
            "CostofWalk_Default_AUD",
            "CostofWalk_Season",
            "CostofWalk_Season_AUD",
            "CHANNEL_RESTRICTION_ADJUSTMENT",
            "CHANNEL_RESTRICTION_ADJUSTMENT_AUD",
            "CHANNEL_RESTRICTION_ADJUSTMENT_EXCLUDED_SRP",
            "CHANNEL_RESTRICTION_ADJUSTMENT_EXCLUDED_SRP_AUD",
            "Channel_Restriction_Adjustment_Currex",
            "CHANNEL_SRP_GROUP_INCLUDED_SRP_MAPPING",
            "Channel_Dmd_channel",
            "Channel_Dmd_Source",
            "Channel_Source_Mapping",
            "Channel_Source_Mapping_AUD",
            "CP_Cfg_AC",
            "CP_Cfg_AC_AUD",
            "CP_Cfg_Base_AT",
            "CP_Cfg_Base_AT_AUD",
            "CP_Cfg_Offset_AT",
            "CP_Cfg_Offset_AT_Draft",
            "CP_Cfg_Offset_AT_AUD",
            "CP_Cfg_Offset_AT_FloorCeil_AUD",
            "CP_Cfg_Base_AT_Draft",
            "CP_CFG_OFFSET_AT_FLOORCEIL",
            "CR_PROBABLE_ACCOM_TYPES",
            "CR_Mapping_Room_Numbers",
            "CR_Accom_Type_Mapping",
            "CR_Mapping_Room_Numbers_History",
            "CR_Accom_Type_Mapping_History",
            "CR_Orphan_Mapping",
            "CR_Out_Of_Order",
            "CR_Out_Of_Order_AUD",
            "Daily_Bar_Config",
            "Daily_Bar_Config_AUD",
            "Daily_Bar_Rate_Chart",
            "Decision_Migration_Status",
            "Revenue_Stream",
            "Revenue_Stream_Cost",
            "Revenue_Stream_Detail",
            "Daily_Bar_Rate_Chart_AUD",
            "date_list",
            "dbmaintain_scripts",
            "Decision_Ovrbk_Accom_OVR_AUD",
            "Decision_Ovrbk_Property_OVR_AUD",
            "Decision_Ovrbk_Property_Autoscaled_History",
            "Decision_Reason_Type",
            "Decision_Type",
            "DQI_Master",
            "DynaCMPC_CFG",
            "DynaCMPC_Profile",
            "DynaCMPC_Profile_Detail",
            "ES_Competitor_Mapping_AUD",
            "ES_Mkt_Seg_Product_Mapping",
            "ES_Product_Definition",
            "ES_Rate_Unqualified",
            "ES_Rate_Unqualified_Details",
            "ES_Rate_Unqualified_Override",
            "ES_Rate_Unqualified_Override_AUD",
            "ES_LongLos_Cfg_AUD",
            "ES_LongLos_Price_Season",
            "ES_LongLos_Price_Season_AUD",
            "ES_LongLos_Price_MinDiff",
            "ES_LongLos_Price_MinDiff_AUD",
            "ES_LongLos_Price_Cfg",
            "ES_LongLos_Price_Cfg_AUD",
            "ES_Recommended_Price",
            "FED_ROOMS_SRP",
            "Fiscal_Calendar",
            "flyway_schema_history",
            "Forecast_Activity_Type",
            "Forecast_Group_AUD",
            "Forecast_Type",
            "Frequency",
            "Frequency_AUD",
            "FS_Event_Revenue",
            "FS_Event",
            "FS_Booking_Guest_Room_Pace",
            "FS_Booking_Guest_Room",
            "FS_Booking_Pace",
            "FS_Booking_Revenue",
            "FS_Booking_Sub_Block_Code",
            "FS_Booking",
            "FS_Cfg_Revenue_Type",
            "FS_Cfg_Revenue_Group_AUD",
            "FS_Cfg_Func_Room_Setup",
            "FS_Cfg_Booking_Type",
            "FS_Cfg_Event_Type_AUD",
            "FS_Cfg_Event_Type",
            "FS_Cfg_Mkt_Seg",
            "FS_MS_PERFORMANCE",
            "FS_Cfg_Mkt_Seg_AUD",
            "FS_Cfg_Guest_Room_Category",
            "FS_Cfg_Guest_Room_Category_AUD",
            "FS_Cfg_Revenue_Group",
            "FS_Cfg_Revenue_Group_AUD",
            "FS_Cfg_Func_Room_MAR_Season",
            "FS_Cfg_Func_Room_MAR_Season_AUD",
            "FS_Cfg_Func_Room_Rental_MAR_Season_Details",
            "FS_Cfg_Func_Room_Rental_MAR_Season_Details_AUD",
            "FS_Cfg_Func_Room_Limit",
            "FS_Cfg_Func_Room_Limit_AUD",
            "FS_Cfg_Func_Room_Config_Room_Rental_Details",
            "FS_Cfg_Func_Room_Config_Room_Rental_Details_AUD",
            "FS_Cfg_Func_Room_Part",
            "FS_Cfg_Func_Room_Part_AUD",
            "FS_Cfg_Func_Room",
            "FS_Cfg_Func_Room_AUD",
            "FS_Cfg_Func_Room_Type",
            "FS_Cfg_Day_Part",
            "FS_Cfg_Day_Part_AUD",
            "FS_CFG_FUNCTION_ROOM_DEFAULT_TIME",
            "FS_CFG_FUNCTION_ROOM_DEFAULT_TIME_AUD",
            "FS_Cfg_Fcst_Level",
            "FS_Cfg_Fcst_Level_AUD",
            "FS_CFG_PKG",
            "FS_CFG_PKG_AUD",
            "FS_CFG_PKG_ELM",
            "FS_CFG_PKG_ELM_AUD",
            "FS_CFG_PKG_ELM_MAP",
            "FS_CFG_PKG_ELM_MAP_AUD",
            "FS_CFG_PKG_TYP",
            "FS_CFG_PKG_TYP_AUD",
            "FS_Cfg_Price_Tier",
            "FS_Cfg_Resource_Type",
            "FS_Cfg_Status",
            "FS_Cfg_Status_AUD",
            "FS_Room_Utilization",
            "GFF_Cfg",
            "GFF_Cfg_AUD",
            "Group_Floor_ACCOM_CLASS_CFG",
            "GROUP_FLOOR_ACCOM_CLASS_CFG_AUD",
            "Group_Floor_MFN_Cfg",
            "Group_Floor_MFN_Cfg_AUD",
            "Group_Floor_MKT_SEG_CFG",
            "GROUP_FLOOR_MKT_SEG_CFG_AUD",
            "GROUP_FLOOR_OVR",
            "GROUP_FLOOR_OVR_REASON",
            "Group_Floor_OVR_Alert_Details",
            "Group_Floor_OVR_Constraining_BAR",
            "Group_Evaluation_Request",
            "Grp_Prc_Cfg",
            "Grp_Prc_Cfg_Accom_Type",
            "Grp_Prc_Cfg_Accom_Type_AUD",
            "Grp_Prc_Cfg_Anc_Stream",
            "Grp_Prc_Cfg_Anc_Stream_AUD",
            "Grp_Prc_Cfg_AUD",
            "Grp_Prc_Cfg_Base_AT",
            "Grp_Prc_Cfg_Base_AT_AUD",
            "GP_Cfg_Bar_Ceiling_Floor_Details",
            "GP_Cfg_Bar_Ceiling_Floor_Details_AUD",
            "Grp_Prc_Cfg_Conf_Banq",
            "Grp_Prc_Cfg_Conf_Banq_AUD",
            "Grp_Prc_Cfg_Pkg_Elm",
            "Grp_Prc_Cfg_Pkg_Elm_AUD",
            "Grp_Prc_Cfg_Pkg",
            "Grp_Prc_Cfg_Pkg_AUD",
            "Grp_Prc_Cfg_Pkg_Elm_Map",
            "Grp_Prc_Cfg_Pkg_Elm_Map_AUD",
            "Grp_Prc_Cfg_Limit",
            "Grp_Prc_Cfg_Limit_AUD",
            "Grp_Prc_Cfg_MAR_Season",
            "Grp_Prc_Cfg_MAR_Season_AUD",
            TABLE_RESERVATION_NIGHT,
            TABLE_RESTORED_NO_SHOW_RESERVATION,
            TABLE_POST_DEPARTURE_REVENUE,
            "Info_Mgr_Email_Notification",
            "Info_Mgr_Excep_Notif_Config_AUD",
            "Info_Mgr_Excep_Notif_Level",
            "Info_Mgr_Excep_Notif_Metric_Type",
            "Info_Mgr_Excep_Notif_Sub_Level",
            "Info_Mgr_Excep_Notif_Sub_Type",
            "Info_Mgr_Excep_Notif_SubType_Level_Mapping",
            "Info_Mgr_Excep_Notif_Type_SubType_Mapping",
            "Info_Mgr_Excep_Notif_Snooze_Params_Values",
            "Info_Mgr_History_Type",
            "Info_Mgr_Status",
            "Info_Mgr_Steps",
            "Info_Mgr_Sys_Health_Condition",
            "Info_Mgr_Sys_Health_Extended_Details_Name_Lookup",
            "Info_Mgr_Type",
            "Inventory_Sharing_Rank",
            "Inventory_Sharing_Rank_AUD",
            "Ip_Cfg_Execution_Position",
            "ip_cfg_Fcmbn",
            "IP_Cfg_Fcst_Task_name_List",
            "IP_Cfg_Process_Group_Fcst_Type",
            "IP_Cfg_Process_Group_Type",
            "IP_Cfg_Property_Task",
            "IP_Cfg_Runtime_Param_Name_List",
            "IP_Cfg_Task_Category_Type",
            "IP_Cfg_Task_Runtime_Param_Map",
            "IP_CFG_Mark_Date_Data_Type",
            "IP_CFG_TS_Class_Config",
            "Decision_GP_Inv_Limit",
            "Decision_GP_Inv_Limit_Ovr",
            "Decision_GP_Inv_Limit_Ovr_AUD",
            "Pace_GP_Inv_Limit_Upload",
            "Decision_GP_Inv_Limit",
            "MP_Decision_Bar_Ovr",
            "MP_Decision_Bar",
            "MP_Pace_Decision_Bar_Differential",
            "Channel",
            "Channel_Analytics",
            "Channel_Cost_Cfg",
            "Channel_Cost",
            "Channel_Cost_AUD",
            "Channel_Cost_Excluded_Rate",
            "Channel_Cost_Excluded_Rate_AUD",
            "Channel_Cost_Simplified",
            "Channel_Cost_Simplified_AUD",
            "Channel_Cost_Hierarchical",
            "Channel_Cost_Hierarchical_AUD",
            "DOW_Type",
            "LOS_Range_AC",
            "LOS_Range_AC_AUD",
            "los_values",
            "Mkt_Seg_AUD",
            "Mkt_Seg_Business_Group_AUD",
            "Mkt_Seg_Details_AUD",
            "Mkt_Seg_Forecast_Group_AUD",
            "Mkt_Seg_Master",
            "Mkt_Seg_Product_Mapping",
            "Mkt_Seg_Product_Mapping_AUD",
            "Mkt_Seg_Template_Grouping",
            "SMALL_GRP_CFG_EXCLUDE_MKT_SEG",
            "SMALL_GRP_CFG_EXCLUDE_MKT_SEG_AUD",
            "Monitor_Process_Scores",
            "Month",
            "Net_Value_Type",
            "Notes",
            "Occupancy_Bucket",
            "Occupancy_Bucket_AUD",
            "Occupancy_Demand_FCST_OVR_AUD",
            "Occupancy_Type",
            "Offset_Type",
            "Out_Of_Order_Override",
            "Out_Of_Order_Override_AUD",
            "Overbooking_Accom",
            "Overbooking_Accom_AUD",
            "Overbooking_Accom_Season",
            "Overbooking_Accom_Season_AUD",
            "Overbooking_Property",
            "Overbooking_Property_AUD",
            "Overbooking_Property_Season",
            "Overbooking_Property_Season_AUD",
            "Overbooking_Type",
            "OVR_Overbooking_Type",
            "ovrd_property_attribute",
            "Ovrd_Property_Attribute_AUD",
            "Posting_Rule_Type",
            "Pretty_Pricing",
            "Pretty_Pricing_AUD",
            "Price_Change_Range_Configuration",
            "Price_Test_Cfg_Scale",
            "Price_Test_Cfg_Scale_AUD",
            "Central_RMS_Price_Data",
            "Central_RMS_Comp_Outlier",
            "Central_RMS_Tran_DMD_Scale",
            "Central_RMS_Tran_DMD_Scale_AUD",
            "Central_RMS_Ovrbk_Optimal",
            "Process_Status",
            "Product",
            "Product_Code",
            "Product_AUD",
            "Product_FlatRate_Season",
            "Product_FlatRate_Season_AUD",
            "Product_Group",
            "Product_Group_AUD",
            "Product_Hierarchy",
            "Product_Hierarchy_AUD",
            "Product_MinDiff",
            "Product_MinDiff_AUD",
            "Product_Rate_Code",
            "Product_Rate_Code_AUD",
            "Product_Rate_Offset",
            "Product_Rate_Offset_AUD",
            "Product_Rate_Offset_OVR",
            "Product_Rate_Offset_OVR_AUD",
            "Product_AT",
            "Product_AT_AUD",
            "Product_Package",
            "Product_Package_AUD",
            "MP_Product",
            "MP_Product_AUD",
            "MP_Cfg_Day_Part",
            "MP_Cfg_Day_Part_AUD",
            "MP_BASE_MR",
            "MP_BASE_MR_AUD",
            "MP_Product_MR",
            "MP_Product_MR_AUD",
            "MP_Cfg_Offset_MR",
            "MP_Cfg_Offset_MR_AUD",
            "MP_Cfg_Base_MR",
            "MP_Cfg_Base_MR_AUD",
            "MP_Product_Rate_Offset",
            "MP_Product_Rate_Offset_AUD",
            "MP_PRODUCT_RATE_OFFSET_OVR",
            "MP_PRODUCT_RATE_OFFSET_OVR_AUD",
            "MP_Pkg_Elm",
            "MP_Pkg_Elm_AUD",
            "MP_Product_MP_Pkg_Elm",
            "MP_Product_MP_Pkg_Elm_AUD",
            "Property",
            "Property_AUD",
            "Property_Business_GRP_Mapping_Rules",
            "Property_Business_GRP_Mapping_Rules_AUD",
            "Property_Special_Event",
            "Property_Special_Event_AUD",
            "Property_Special_Event_Instance",
            "Property_Special_Event_Instance_AUD",
            "Property_Special_Event_Instance_Notes",
            "Property_Special_Event_Instance_Notes_AUD",
            "Projection_Builder_Metadata",
            "Projection_Builder_Metadata_AUD",
            "Rate_Code_Market_Segment_Mappings",
            "Rate_Qualified_AUD",
            "Rate_Qualified_Details_AUD",
            "Rate_Qualified_Type",
            "Rate_Shopping_Adjustment_AUD",
            "Rate_Unqualified_Accom_Class_AUD",
            "Rate_Unqualified_AUD",
            "Rate_Unqualified_Closed_AUD",
            "Rate_Unqualified_Defaults_AUD",
            "Rate_Unqualified_Details_AUD",
            "Rate_Unqualified_LOS_Override_AUD",
            "Rate_Unqualified_User_Override_AUD",
            "Record_Type",
            "reservation_status",
            TABLE_RESERVATION_NIGHT_CHANGE,
            "Run_Task_Macros",
            "SavedReports",
            "SavedReports_AUD",
            "ScheduledReports",
            "ScheduledReports_AUD",
            "Special_Event_Type",
            "Special_Event_Type_AUD",
            "Status",
            "Sas_Macros_Parameters",
            "Temp_CR_Accom_Type_Mapping",
            "TetrisRevisionEntity",
            "time",
            "Users",
            "Users_AUD",
            "User_Filter_Options",
            "Wash_Forecast_Group_FCST_OVR_AUD",
            "Vrbo_Calendar",
            "Vrbo_Listing",
            "Vacation_Rental_Tracker",
            "Wash_Ind_Group_Fcst_OVR_AUD",
            "Webrate_Accom_Class_Mapping_AUD",
            "Webrate_Accom_Type_AUD",
            "Webrate_Channel_AUD",
            "Webrate_Competitors_AUD",
            "Webrate_Competitors_Class_AUD",
            "Webrate_Default_Channel_AUD",
            "Webrate_Override_Channel_AUD",
            "Webrate_Override_Competitor_AUD",
            "Webrate_OVR_Competitor_DTLS_AUD",
            "Webrate_Ranking",
            "Webrate_Ranking_AC_OVR",
            "Webrate_Ranking_AC_OVR_AUD",
            "Webrate_Ranking_Accom_Class_AUD",
            "Webrate_Ranking_AUD",
            "Webrate_Shopping_Config",
            "Webrate_Shopping_Config_AUD",
            "Webrate_Source",
            "Webrate_Type",
            "Year",
            "Yield_Type",
            "G3_Group_Status_Map",
            "CP_Cfg_AUD",
            "G3_Group_Status_Type",
            "Accom_Type_Supplement",
            "G3_Group_Status_Type_Map",
            "Accom_Type_Supplement_AUD",
            "Tax",
            "Tax_AUD",
            "STR_Monthly",
            "STR_Weekly",
            "STR_Daily",
            "Market_Performance_Daily",
            "Market_Performance_Monthly",
            "Data_Load_Metadata",
            "Grp_Prc_Cfg_Min_Profit",
            "Grp_Prc_Cfg_Min_Profit_AUD",
            "Info_Mgr_Job_Lookup",
            "Decision_Update_Date_To_External_System",
            "Rate_Category_Code_Mapping",
            "History_Group_Block",
            "History_Group_Master",
            "History_Yield_Currency",
            "History_Transaction",
            "History_Incoming_Metadata",
            "IP_cfg_Process_Group_Stat",
            "History_Occupancy_Summary",
            YIELD_CATEGORY_RULE,
            "CP_Cfg",
            "Sharers_AdjustedCnxAndNSSharedTrx",
            "Sharers_AdjustedShareTransactions",
            "Sharers_OnlyCnxOrNoShow",
            "Sharers_OnlySharedTrans",
            "Sharers_ShareWithFullSharers",
            "ShareWithFullSharersMultipleTrans",
            "D360_MKT_Seg_detail",
            "D360_Booking_Summary_Pace",
            "D360_MKT_Hist_Capacity",
            "HD360_Booking_Summary_Pace",
            "HD360_MKT_Hist_Capacity",
            "HD360_TRANSITORY_BOOKING_SUMMARY_PACE",
            "HD360_Property_Comp_Set_AUD",
            "Budget_Level",
            "Module_Type_To_Budget_Level",
            "Rate_Code_Vendor_Mapping",
            "Rate_Code_Vendor_Mapping_AUD",
            "Inventory_Group",
            "Inventory_Group_Details",
            "Inventory_Group_AUD",
            "Inventory_Group_Details_AUD",
            "RRA_Source",
            "RRA_Score",
            "RRA_Channel",
            "RRA_Competitor_AUD",
            "RRA_Competitor",
            "RRA_Source_Property",
            "Room_Type_Recoding_Config",
            "Room_Type_Recoding_Config_AUD",
            "Room_Type_Recoding_Config_Mapping",
            "Room_Type_Recoding_Config_Mapping_AUD",
            "PMS_Migration_Mapping",
            "RC_RT_MAPPING",
            "PMS_MIGRATION_RATE_CODE_MKT_SEG_MAPPING",
            "Temp_Webrate_Pace",
            "Monitoring_Dashboard_Email_Status",
            "Competitor_Price_Change_Notification",
            "Servicing_Cost_Cfg",
            "Servicing_Cost_Cfg_AUD",
            "Task_Parameter",
            "Remote_Task",
            "Main_Circuit_Breaker_Fcst_Calib_Sync_State",
            "Decisions_Delivered",
            "Opera_Performance_Monitor",
            "Sync_Flags_AUD",
            "Sync_Flags",
            "OVRD_OCC_REFERENCE_PRICE",
            "Lrv_Drop_Restriction_Cfg",
            "Lrv_Drop_Restriction_Cfg_AUD",
            "All_Full_Sorted_Sharers",
            "CP_Recommended_Floor_Ceiling",
            "ACCOM_TYPE_ADR_FINAL",
            "Linked_SRP_Delta",
            "Linked_SRP_Mappings",
            "Linked_SRP_Header",
            "Linked_SRP_Details",
            "Linked_SRP_Details_Output",
            "Linked_SRP_Details_Output_Temp",
            "Linked_SRP_Details_LV0",
            "Linked_SRP_Details_Offset_Seasons",
            "Linked_SRP_Adjustment",
            "Linked_SRP_Details_Raw",
            "Linked_SRP_RT_Threshold_Value",
            "SCHEDULEDREPORT_DELIVERY_AUDIT",
            "ASYNC_REPORTS",
            "Agile_Product_Restriction_Association",
            "Agile_Product_Restriction_Association_Aud",
            "Hospitality_rooms_config",
            "Hospitality_rooms_config_UI",
            "Manual_Restriction_Type",
            "Heat_Map_Range_and_Colors",
            "Heat_Map_Range_and_Colors_Config",
            "Heat_Map_Range_and_Colors_Config_Type",
            "Accom_Class_Proposed",
            "SYNC_DISPLAY_NAME_AUD",
            "PROBABLE_DISCONTINUED_MKT_SEG",
            "Extended_Stay_Rate_Mapping",
            "Recovery_State_Transition",
            "Shelf_State_Transition",
            "Inventory_History_data_SRPFPLOS",
            "Market_Segment_St2y_Details",
            "Market_Segment_Missing_Extract_St2y_Details",
            "MVCR_Rate_AT",
            "MVCR_Rate_AT_AUD",
            "Zero_Diet_Records_Migration_Activity",
            "PI_Decision_Support",
            "RDL_Shop_Attribute",
            "RDL_Shop_Attribute_AUD",
            "RDL_Custom_Room_Description",
            "Webrate_Type_Product",
            "Webrate_Type_Product_AUD",
            "Fs_Booking_Guest_Room_NGI_Pace",
            "PRICE_ACCELERATOR_CFG",
            "PRICE_ACCELERATOR_CFG_AUD",
            "CP_Decision_Bar_Out_Ext",
            "Occupancy_FCST_Ext",
            "Occupancy_Demand_FCST_Ext",
            "Prc_Drp_Restriction_Cfg",
            "Prc_Drp_Restriction_Cfg_AUD",
            "PACE_From_Service",
            "Dcmpc_Cfg",
            "Dcmpc_Cfg_Details",
            "Dcmpc_Cfg_Mapping",
            "Dcmpc_Cfg_AUD",
            "Dcmpc_Cfg_Mapping_AUD",
            "Suboptml_Type",
            "Webrate_Channel_Ignore_Config",
            "Webrate_Channel_Ignore_Config_AUD",
            "Sas_Purgable_Datasets",
            "Webrate_OVR_Comp_Channel_Mapping",
            "Webrate_OVR_Comp_Channel_Mapping_AUD",
            "Pace_Backfill_Log",
            "Accom_Type_Vacation_Rental_Config",
            "Accom_Type_Vacation_Rental_Config_AUD",
            "Decision_Volume",
            "Analytics_Config",
            "Analytics_Config_AUD",
            "SAS_DB_Query_Audit"
    };

    //AGILE PRODUCT FPLOS ASSOCIATION
    static final String[] TABLES_AGILE_PRODUCT_QUALIFIED_FPLOS_ASSOCIATION = new String[]{
            "Agile_Product_Restriction_Association",
            "Agile_Product_Restriction_Association_Aud"
    };

    static final String SCHEMA_DEFAULT = "dbo";
    static final String SCHEMA_OPERA = "opera";
    static final String PARAM_TABLE_NAME = "tableName";
    static final String PARAM_SEED_START = "seedStart";
    static final String SQL_TABLE_ROW_COUNT = "select count(1) from {0}";
    static final String SQL_TABLE_TRUNCATE = "truncate table {0}";
    static final String SQL_TABLE_DELETE_ALL = "delete from {0}";
    static final Integer SEED_START_DEFAULT = 1;
    static final String SQL_TABLE_RESEED = "if ((select objectproperty(object_id(:" + PARAM_TABLE_NAME +
            "), 'TableHasIdentity')) = 1) dbcc checkident(:" + PARAM_TABLE_NAME + ", reseed, :" + PARAM_SEED_START + ")";
    static final String SQL_FK_CHECK =
            "select object_name(f.parent_object_id) TableName, col_name(fc.parent_object_id,fc.parent_column_id) ColName " +
                    "from sys.foreign_keys AS f " +
                    "inner join sys.foreign_key_columns AS fc " +
                    "on f.OBJECT_ID = fc.constraint_object_id " +
                    "inner join sys.tables t " +
                    "on t.OBJECT_ID = fc.referenced_object_id " +
                    "where object_name(f.referenced_object_id) = :" + PARAM_TABLE_NAME;
    static final String SET_PACE_GROUP_MASTER_DUMMY_FILE_METADATA_ID = "" +
            " UPDATE Pace_Group_Master " +
            " SET File_Metadata_ID = ( " +
            "     SELECT File_Metadata_ID " +
            "     FROM File_Metadata " +
            "     WHERE File_Location='DUMMY_FILE_LOCATION' AND File_Name='UNASSIGNED'); ";
    static final String MOVE_MKT_SEG_DETAILS_TO_PROPOSED = "MERGE mkt_seg_details_proposed AS target\n" +
            "USING mkt_seg_details AS SOURCE ON source.mkt_seg_id = target.mkt_seg_id\n" +
            "WHEN NOT matched AND source.mkt_seg_id IN\n" +
            "  (SELECT mkt_seg_id\n" +
            "   FROM group_master\n" +
            "   UNION SELECT mkt_seg_id\n" +
            "   FROM pace_group_master) THEN\n" +
            "INSERT ([Mkt_Seg_ID] ,\n" +
            "        [Business_Type_ID] ,\n" +
            "        [Yield_Type_ID] ,\n" +
            "        [Forecast_Activity_Type_ID] ,\n" +
            "        [Qualified] ,\n" +
            "        [Booking_Block_Pc] ,\n" +
            "        [Fenced] ,\n" +
            "        [Package] ,\n" +
            "        [Link] ,\n" +
            "        [Template_ID] ,\n" +
            "        [Template_Default] ,\n" +
            "        [Process_Status_ID] ,\n" +
            "        [Offset_Type_ID] ,\n" +
            "        [Offset_Value] ,\n" +
            "        [Status_ID] ,\n" +
            "        [Last_Updated_DTTM] ,\n" +
            "        [Priced_By_BAR] ,\n" +
            "        [Created_By_User_ID] ,\n" +
            "        [Created_DTTM] ,\n" +
            "        [Last_Updated_By_User_ID])\n" +
            "VALUES (source.Mkt_Seg_ID ,source.Business_Type_ID ,source.Yield_Type_ID ,source" +
            ".Forecast_Activity_Type_ID ,source.Qualified ,source.Booking_Block_Pc ,source.Fenced ,source.Package ," +
            "source.Link ,source.Template_ID ,source.Template_Default ,10 ,2 ,source.Offset_Value ,source.Status_ID ," +
            "source.Last_Updated_DTTM ,source.Priced_By_BAR ,source.Created_By_User_ID ,source.Created_DTTM ,source" +
            ".Last_Updated_By_User_ID);";
    static final String UPDATE_MS_ID_TRANSIENT_MS = "\n" +
            "UPDATE target\n" +
            "SET target.mkt_seg_id = updateableTrans.defaultMsId\n" +
            "FROM %s target\n" +
            "INNER JOIN\n" +
            "  (SELECT DISTINCT transms.mkt_seg_id,\n" +
            "                   ams.Market_Code,\n" +
            "                   ams1.Mapped_Market_Code,\n" +
            "                   ms1.Mkt_Seg_ID AS defaultMsId\n" +
            "   FROM\n" +
            "     (SELECT mkt_seg_id\n" +
            "      FROM mkt_seg_details\n" +
            "      WHERE business_type_id = :businessTypeId\n" +
            "      UNION SELECT mkt_seg_id\n" +
            "      FROM mkt_seg_details_proposed\n" +
            "      WHERE business_type_id = :businessTypeId ) AS transms\n" +
            "   JOIN mkt_seg ms ON ms.Mkt_Seg_ID = transms.Mkt_Seg_ID\n" +
            "   LEFT JOIN Analytical_Mkt_Seg ams ON ms.mkt_seg_code = ams.Mapped_Market_Code\n" +
            "   LEFT JOIN Analytical_Mkt_Seg ams1 ON ams.Market_Code = ams1.market_code\n" +
            "   AND ams1.Rate_Code_Type = 'DEFAULT'\n" +
            "   INNER JOIN mkt_seg ms1 ON ms1.Mkt_Seg_Code = ams1.Mapped_Market_Code) AS updateableTrans ON target" +
            ".Mkt_Seg_ID = updateableTrans.Mkt_Seg_ID;\n";
    static final String UPDATE_MKT_SEG_CODE_STRAIGHT_TO_SPLIT = "UPDATE ms\n" +
            "SET Mkt_Seg_Code = ams.mapped_market_code\n" +
            "FROM mkt_seg ms\n" +
            "INNER JOIN %s target ON target.Mkt_Seg_ID = ms.Mkt_Seg_ID\n" +
            "INNER JOIN Analytical_Mkt_Seg ams ON ams.Market_Code = ms.Mkt_Seg_Code\n" +
            "AND ams.rank=:rank \n" +
            "LEFT JOIN analytical_mkt_seg ams1 ON ams1.Mapped_Market_Code = ms.Mkt_Seg_Code\n" +
            "WHERE ams1.Mapped_Market_Code IS NULL";
    static final String PROP_BK_HISTORY_START_DT = "PROP_BK_HISTORY_START_DT";
    static final String ATTRIBUTE_NAME = "attributeName";
    static final String SQL_TABLE_UPDATE = "update {0} set Value = NULL where Attribute_Name = :" + ATTRIBUTE_NAME;

    private RollbackServiceConstants() {
    }
}
