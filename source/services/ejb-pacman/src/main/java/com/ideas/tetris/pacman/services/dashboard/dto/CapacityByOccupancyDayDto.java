package com.ideas.tetris.pacman.services.dashboard.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Key;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.MultiPropertyAggregate;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Sum;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import java.math.BigDecimal;
import java.util.Date;

@MultiPropertyAggregate
public class CapacityByOccupancyDayDto {

    @Key
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    private Date occupancyDate;

    @Sum(scale = 0)
    private BigDecimal availableCapacity;

    @Sum(scale = 0)
    private BigDecimal physicalCapacity;


    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public BigDecimal getPhysicalCapacity() {
        return physicalCapacity;
    }

    public void setPhysicalCapacity(BigDecimal physicalCapacity) {
        this.physicalCapacity = physicalCapacity;
    }

    public BigDecimal getAvailableCapacity() {
        return availableCapacity;
    }

    public void setAvailableCapacity(BigDecimal availableCapacity) {
        this.availableCapacity = availableCapacity;
    }

}