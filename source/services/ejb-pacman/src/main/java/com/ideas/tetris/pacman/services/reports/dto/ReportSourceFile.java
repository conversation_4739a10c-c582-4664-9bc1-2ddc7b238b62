package com.ideas.tetris.pacman.services.reports.dto;

import net.schmizz.sshj.xfer.InMemorySourceFile;

import java.io.IOException;
import java.io.InputStream;

public class ReportSourceFile extends InMemorySourceFile {

    String fileName;
    InputStream file;
    long length;

    public ReportSourceFile(String fileName, long length, InputStream file) {
        this.fileName = fileName;
        this.length = length;
        this.file = file;
    }

    @Override
    public String getName() {
        return fileName;
    }

    @Override
    public long getLength() {
        return length;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return file;
    }
}
