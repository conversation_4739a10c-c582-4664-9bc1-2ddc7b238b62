package com.ideas.tetris.pacman.services.security.login.mapper;

import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.login.vo.PropertyVO;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class PropertyDetailsMapper {

    public static final String LOW_OCC_CUT_OFF = "50";
    public static final String HIGH_OCC_CUT_OFF = "80";

    @Autowired
    AuthorizationService authorizationService;

    @Autowired
    DateService dateService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    private static final Logger LOGGER = Logger.getLogger(PropertyDetailsMapper.class.getName());

    public Map<Integer, PropertyVO> getAssociatedPropertiesMap() {
        Map<Integer, PropertyVO> associatedPropertiesObjectMap = new HashMap<>();
        List<Property> activeAuthorizedProperties = authorizationService.retrieveActiveAuthorizedProperties();
        Map<Integer, BigDecimal> propertyCapacityMap = getPropertyCapacityMap(activeAuthorizedProperties);
        List<Integer> propertyIds = activeAuthorizedProperties.stream().map(Property::getId).collect(Collectors.toList());
        Map<Integer, Date> dateMap = getDateMap(propertyIds);
        final boolean displayPropertyByCode = getDisplayPropertyByCode();
        for (Property property : activeAuthorizedProperties) {
            associatedPropertiesObjectMap.put(property.getId(),
                    new PropertyVO(property
                            , DateUtil.formatDate(dateMap.getOrDefault(property.getId(), dateService.getCurrentDate()), DateUtil.DEFAULT_DATE_FORMAT)
                            , getPropertyCapacity(propertyCapacityMap, property)
                            , HIGH_OCC_CUT_OFF
                            , LOW_OCC_CUT_OFF).changePropertyNameByCode(displayPropertyByCode, property.getCode(), property.getName()));
        }
        return associatedPropertiesObjectMap;
    }

    private boolean getDisplayPropertyByCode() {
        return "Code".equalsIgnoreCase(pacmanConfigParamsService.getParameterValueByClientLevel(GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value()));
    }

    private Map<Integer, Date> getDateMap(List<Integer> propertyIds) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Map<Integer, Date> dateMap = dateService.getPropertyCaughtUpDateMap(propertyIds);
        stopWatch.stop();
        LOGGER.info("Time for fetching property systemToday :" + stopWatch.toString() + " ms for size:=" + propertyIds.size());
        return dateMap;
    }


    private Map<Integer, BigDecimal> getPropertyCapacityMap(List<Property> propertyList) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Map<Integer, BigDecimal> propertyCapacityMap = authorizationService.getCapacityForAuthorizedProperties(propertyList);
        stopWatch.stop();
        LOGGER.info("Time for fetching property capacity :" + stopWatch.toString() + " ms for size:=" + propertyList.size());
        return propertyCapacityMap;
    }

    public Map<Integer, PropertyVO> getAssociatedPropertiesMapOptimized(Property defaultProperty,
                                                                        List<Property> activeAuthorizedProperties) {
        if (defaultProperty == null) {
            return Collections.emptyMap();
        }
        Map<Integer, PropertyVO> associatedPropertiesObjectMap = new HashMap<>();
        Map<Integer, BigDecimal> propertyCapacityMap = getPropertyCapacityMap(Collections.singletonList(defaultProperty));
        Map<Integer, Date> dateMap = getDateMap(Collections.singletonList(defaultProperty.getId()));
        final boolean displayPropertyByCode = getDisplayPropertyByCode();
        for (Property property : activeAuthorizedProperties) {
            PropertyVO propertyVO;
            if (property.getId().intValue() == defaultProperty.getId().intValue()) {
                propertyVO = new PropertyVO(property
                        , DateUtil.formatDate(dateMap.getOrDefault(property.getId(), dateService.getCurrentDate()), DateUtil.DEFAULT_DATE_FORMAT)
                        , getPropertyCapacity(propertyCapacityMap, property)
                        , HIGH_OCC_CUT_OFF
                        , LOW_OCC_CUT_OFF,
                        true);
            } else {
                propertyVO = new PropertyVO(property);

            }
            associatedPropertiesObjectMap.put(property.getId(),
                    propertyVO.changePropertyNameByCode(displayPropertyByCode, property.getCode(), property.getName()));
        }
        return associatedPropertiesObjectMap;
    }

    public int getPropertyCapacity(Map<Integer, BigDecimal> propertyCapacityMap, Property property) {
        BigDecimal capacity = propertyCapacityMap.get(property.getId());
        return capacity.intValue() == Integer.MIN_VALUE ? 0 : capacity.intValue();
    }

}
