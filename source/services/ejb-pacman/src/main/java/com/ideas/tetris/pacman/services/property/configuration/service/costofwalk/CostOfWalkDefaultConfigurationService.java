package com.ideas.tetris.pacman.services.property.configuration.service.costofwalk;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.costofwalk.entity.CostofWalkDefault;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.CostOfWalkPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@CostOfWalkDefaultConfigurationService.Qualifier
@Component
@Transactional
public class CostOfWalkDefaultConfigurationService extends AbstractPropertyConfigurationService {

    private static final Logger LOGGER = Logger.getLogger(CostOfWalkDefaultConfigurationService.class.getName());

    @Override
    public PropertyConfigurationRecordType getPropertyConfigurationRecordType() {
        return PropertyConfigurationRecordType.CW;
    }

    @Override
    public List<PropertyConfigurationRecordFailure> doValidate(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId) {
        CostOfWalkPropertyConfigurationDto costOfWalkPropertyConfigurationDto = (CostOfWalkPropertyConfigurationDto) propertyConfigurationDto;

        List<PropertyConfigurationRecordFailure> exceptions = new ArrayList<PropertyConfigurationRecordFailure>();

        // Validate Room Type
        String roomType = costOfWalkPropertyConfigurationDto.getRoomType();
        if (StringUtils.isEmpty(roomType)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Room Type is required"));
        } else if (findAccomType(propertyId, roomType) == null) {
            exceptions.add(new PropertyConfigurationRecordFailure("Room Type: " + roomType + " not found"));
        }

        // Validate Sunday
        validateDay(exceptions, costOfWalkPropertyConfigurationDto.getSunday(), "Sunday");

        // Validate Monday
        validateDay(exceptions, costOfWalkPropertyConfigurationDto.getMonday(), "Monday");

        // Validate Tuesday
        validateDay(exceptions, costOfWalkPropertyConfigurationDto.getTuesday(), "Tuesday");

        // Validate Wednesday
        validateDay(exceptions, costOfWalkPropertyConfigurationDto.getWednesday(), "Wednesday");

        // Validate Thursday
        validateDay(exceptions, costOfWalkPropertyConfigurationDto.getThursday(), "Thursday");

        // Validate Friday
        validateDay(exceptions, costOfWalkPropertyConfigurationDto.getFriday(), "Friday");

        // Validate Saturday
        validateDay(exceptions, costOfWalkPropertyConfigurationDto.getSaturday(), "Saturday");

        return exceptions;
    }

    @Override
    public void doHandle(PropertyConfigurationDto pcd, Integer propertyId) {
        CostOfWalkPropertyConfigurationDto costOfWalkPropertyConfigurationDto = (CostOfWalkPropertyConfigurationDto) pcd;

        AccomType accomType = findAccomType(propertyId, costOfWalkPropertyConfigurationDto.getRoomType());

        CostofWalkDefault costOfWalkDefault = findCostofWalkDefault(accomType);
        if (costOfWalkDefault == null) {
            costOfWalkDefault = new CostofWalkDefault();
            costOfWalkDefault.setPropertyId(propertyId);
            costOfWalkDefault.setAccomTypeId(accomType.getId());
        }

        costOfWalkDefault.setCreatedByUserId(findUserId());
        costOfWalkDefault.setSundayValue(costOfWalkPropertyConfigurationDto.getSunday());
        costOfWalkDefault.setMondayValue(costOfWalkPropertyConfigurationDto.getMonday());
        costOfWalkDefault.setTuesdayValue(costOfWalkPropertyConfigurationDto.getTuesday());
        costOfWalkDefault.setWednesdayValue(costOfWalkPropertyConfigurationDto.getWednesday());
        costOfWalkDefault.setThursdayValue(costOfWalkPropertyConfigurationDto.getThursday());
        costOfWalkDefault.setFridayValue(costOfWalkPropertyConfigurationDto.getFriday());
        costOfWalkDefault.setSaturdayValue(costOfWalkPropertyConfigurationDto.getSaturday());

        if (costOfWalkDefault.getCreateDate() == null) {
            LOGGER.info("Creating CostofWalkDefault for Property: " + pcd.getPropertyCode() + " and Room Type: " + costOfWalkPropertyConfigurationDto.getRoomType());
            crudService.save(costOfWalkDefault);
            crudService.flush();
            crudService.refresh(costOfWalkDefault);
        } else {
            LOGGER.info("Updating CostofWalkDefault for Property: " + pcd.getPropertyCode() + " and Room Type: " + costOfWalkPropertyConfigurationDto.getRoomType());
            crudService.save(costOfWalkDefault);
        }
    }

    public void validateDay(List<PropertyConfigurationRecordFailure> exceptions, BigDecimal value, String day) {
        if (value == null || value.doubleValue() <= 0.0) {
            exceptions.add(new PropertyConfigurationRecordFailure(day + " must contain a positive number"));
        }
    }

    public CostofWalkDefault findCostofWalkDefault(AccomType accomType) {
        return (CostofWalkDefault) crudService.findByNamedQuerySingleResult(CostofWalkDefault.BY_ACCOMTYPEID, QueryParameter.with("accomTypeId", accomType.getId()).parameters());
    }

    public AccomType findAccomType(Integer propertyId, String name) {
        return (AccomType) crudService.findByNamedQuerySingleResult(AccomType.BY_PROPERTY_ID_TYPE, QueryParameter.with("propertyId", propertyId).and("accomTypeCode", name).parameters());
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
