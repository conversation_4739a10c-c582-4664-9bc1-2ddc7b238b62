package com.ideas.tetris.pacman.services.reports.pricingoverridehistory.dto;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

public class OverrideHistoryDto {
    private String newOverride;
    private String userName;
    private ZonedDateTime createDate;
    private String oldOverride;
    private BigDecimal newBarRate;
    private BigDecimal oldBarRate;
    private BigDecimal newCeilBarRate;
    private BigDecimal oldCeilBarRate;
    private BigDecimal newFloorRate;
    private BigDecimal oldFloorRate;
    private BigDecimal originalDecision;

    public String getNewOverride() {
        return newOverride;
    }

    public void setNewOverride(String newOverride) {
        this.newOverride = newOverride;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public ZonedDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(ZonedDateTime createDate) {
        this.createDate = createDate;
    }

    public String getOldOverride() {
        return oldOverride;
    }

    public void setOldOverride(String oldOverride) {
        this.oldOverride = oldOverride;
    }

    public BigDecimal getNewBarRate() {
        return newBarRate;
    }

    public void setNewBarRate(BigDecimal newBarRate) {
        this.newBarRate = newBarRate;
    }

    public BigDecimal getOldBarRate() {
        return oldBarRate;
    }

    public void setOldBarRate(BigDecimal oldBarRate) {
        this.oldBarRate = oldBarRate;
    }

    public BigDecimal getNewCeilBarRate() {
        return newCeilBarRate;
    }

    public void setNewCeilBarRate(BigDecimal newCeilBarRate) {
        this.newCeilBarRate = newCeilBarRate;
    }

    public BigDecimal getOldCeilBarRate() {
        return oldCeilBarRate;
    }

    public void setOldCeilBarRate(BigDecimal oldCeilBarRate) {
        this.oldCeilBarRate = oldCeilBarRate;
    }

    public BigDecimal getNewFloorRate() {
        return newFloorRate;
    }

    public void setNewFloorRate(BigDecimal newFloorRate) {
        this.newFloorRate = newFloorRate;
    }

    public BigDecimal getOldFloorRate() {
        return oldFloorRate;
    }

    public void setOldFloorRate(BigDecimal oldFloorRate) {
        this.oldFloorRate = oldFloorRate;
    }

    public BigDecimal getOriginalDecision() {
        return originalDecision;
    }

    public void setOriginalDecision(BigDecimal originalDecision) {
        this.originalDecision = originalDecision;
    }
}
