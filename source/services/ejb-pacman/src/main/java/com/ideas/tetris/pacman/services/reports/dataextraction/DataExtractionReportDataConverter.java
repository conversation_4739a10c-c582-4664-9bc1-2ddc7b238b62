package com.ideas.tetris.pacman.services.reports.dataextraction;

import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionReportDto;
import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionType;
import com.ideas.tetris.pacman.services.scheduledreport.domain.ReportData;
import com.ideas.tetris.pacman.services.scheduledreport.domain.ReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.JasperReportDataConverter;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.DataExtractionReportCriteria;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@DataExtractionReportDataConverter.Qualifier
@Component
@Transactional
public class DataExtractionReportDataConverter implements JasperReportDataConverter<Map<DataExtractionType, List<DataExtractionReportDto>>, DataExtractionReportCriteria> {

    @Override
    public ReportData createReportData(Map<DataExtractionType, List<DataExtractionReportDto>> records, ScheduledReport<DataExtractionReportCriteria> scheduledReport) {
        List<ReportSheet> reportSheets = new ArrayList<>();

        DataExtractionReportCriteria criteria = scheduledReport.getReportCriteria();
        Language language = scheduledReport.getLanguage();


        if (criteria.isHotel()) {
            reportSheets.add(DataExtractionHotelConverter.getPropertyReportSheet(records, scheduledReport));
        }

        if (criteria.isRoomClass()) {
            reportSheets.add(DataExtractionRoomClassConverter.getRoomClassReportSheet(records, scheduledReport));
        }

        if (criteria.isRoomType()) {
            reportSheets.add(DataExtractionRoomTypeConverter.getRoomTypeReportSheet(records, scheduledReport));
        }

        if (criteria.isForecastGroup()) {
            reportSheets.add(DataExtractionForecastGroupConverter.getForecastGroupReportSheet(records, scheduledReport));
        }

        if (criteria.isMarketSegment()) {
            reportSheets.add(DataExtractionMarketSegmentConverter.getMarketSegmentReportSheet(records, scheduledReport));
        }

        if (criteria.isBusinessView()) {
            reportSheets.add(DataExtractionBusinessViewConverter.getBusinessViewReportSheet(records, scheduledReport));
        }

        return new ReportData(reportSheets);
    }


    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
