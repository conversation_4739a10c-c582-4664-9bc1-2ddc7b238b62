package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;

import javax.inject.Inject;
import java.util.HashMap;
import java.util.Map;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.SKIP_DATAFEED_RENAME_WITH_TEMPORARY_SUFFIX;
import static com.ideas.tetris.pacman.common.constants.Constants.SHOULD_SKIP_DATAFEED_RENAME_WITH_TEMPORARY_SUFFIX;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class SftpConfigurationService {
    private static final String REQUIRED_CREDENTIALS = "host, port, remotedirectory and username must be set";

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    RestClient restClient;

    @Autowired
    PropertyService propertyService;

    public String uploadTestFile(Integer propertyId) {
        Property property = propertyService.getPropertyById(propertyId);
        String clientCode = property.getClient().getCode();
        String propertyCode = property.getCode();
        String host = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DATAFEED_FTP_HOST.value(), clientCode, propertyCode);
        String port = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DATAFEED_FTP_PORT.value(), clientCode, propertyCode);
        String remoteDirectory = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DATAFEED_FTP_REMOTE_DIRECTORY.value(), clientCode, propertyCode);
        String username = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DATAFEED_FTP_USERNAME.value(), clientCode, propertyCode);
        String password = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DATAFEED_FTP_PASSWORD.value(), clientCode, propertyCode);
        String certificate = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DATAFEED_SFTP_CERTIFICATE_FILE_NAME.value(), clientCode, propertyCode);
        String passphrase = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DATAFEED_SFTP_PRIVATE_KEY_PASS_PHRASE.value(), clientCode, propertyCode);
        String encryption = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DATAFEED_ENCRYPTION_ENABLED.value(), clientCode, propertyCode);
        String encryptionKeyFileName = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DATAFEED_ENCRYPTION_KEY_FILE_NAME.value(), clientCode, propertyCode);
        if (host == null || port == null || remoteDirectory == null || username == null) {
            return REQUIRED_CREDENTIALS;
        }
        Boolean shouldSkipDatafeedRenameWithTemporarySuffix = pacmanConfigParamsService.getParameterValue(clientCode, propertyCode, SKIP_DATAFEED_RENAME_WITH_TEMPORARY_SUFFIX);
        Map<String, String> configurationparameters = new HashMap<>();
        configurationparameters.put("host", host);
        configurationparameters.put("port", port);
        configurationparameters.put("remotedirectory", remoteDirectory);
        configurationparameters.put("username", username);
        configurationparameters.put("password", (password != null) ? password : "");
        configurationparameters.put("certificate", (certificate != null) ? certificate : "");
        configurationparameters.put("passphrase", (passphrase != null) ? passphrase : "");
        configurationparameters.put("encryption", (encryption != null) ? encryption : "");
        configurationparameters.put("encryptionKeyFileName", (encryptionKeyFileName != null) ? encryptionKeyFileName : "");
        configurationparameters.put(SHOULD_SKIP_DATAFEED_RENAME_WITH_TEMPORARY_SUFFIX, String.valueOf(shouldSkipDatafeedRenameWithTemporarySuffix));
        return restClient.getJsonFromEndpoint(RestEndpoints.DATAFEED_SFTP_CONFIGURATION_CHECK,
                configurationparameters, 0);
    }
}