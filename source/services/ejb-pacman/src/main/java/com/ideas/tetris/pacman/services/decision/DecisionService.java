package com.ideas.tetris.pacman.services.decision;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.pmsmigration.purge.DecisionPurgable;
import com.ideas.tetris.pacman.services.property.PropertyConfigParamService;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static java.math.BigInteger.valueOf;
import static org.apache.commons.collections.CollectionUtils.isEmpty;

@Component
@Transactional
public class DecisionService {

    public static final String DECISION_TYPE_ID = "decisionTypeId";
    private static final String PROPERTY_ID = "propertyId";
    private static final String LAST_UPLOAD_DATE = "lastUploadDate";
    private static final String PROCESS_STATUS_ID = "processStatusId";

    @Autowired
    DateService dateServiceLocal;

    @TenantCrudServiceBean.Qualifier
    @Qualifier("tenantCrudServiceBean")
    @Autowired
    private CrudService crudService;

    @Autowired
    PropertyConfigParamService propertyConfigParamService;

    public void setDateServiceLocal(DateService dateServiceLocal) {
        this.dateServiceLocal = dateServiceLocal;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setPropertyConfigParamService(PropertyConfigParamService propertyConfigParamService) {
        this.propertyConfigParamService = propertyConfigParamService;
    }

    @VisibleForTesting
    public static DecisionService createTestInstance() {
        return new DecisionService();
    }

    public Decision createBdeDecision() {
        return createDecision(DECISION_TYPE_BDE, ProcessStatus.ETL_FILE_ARRIVED_AND_VALIDATED);
    }

    public Decision createLtBdeDecision() {
        return createDecision(DECISION_TYPE_LTBDE, ProcessStatus.ETL_FILE_ARRIVED_AND_VALIDATED);
    }

    public Decision createCdpDecision() {
        return createDecision(DECISION_TYPE_CDP, ProcessStatus.ETL_FILE_ARRIVED_AND_VALIDATED);
    }

    public Decision createOndemandDecision() {
        return createDecision(DECISION_TYPE_ON_DEMAND, ProcessStatus.ETL_FILE_ARRIVED_AND_VALIDATED);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Decision createWhatIfDecision() {
        return createDecision(DECISION_TYPE_WHAT_IF, ProcessStatus.COMMITTED);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Decision createBAROverrideDecision() {
        return createDecision(DECISION_TYPE_BAR_OVERRIDE, ProcessStatus.COMMITTED);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Decision createBAROverrideDecision(Date caughtUpDate, Date businessDate, Date rateUnqualifiedCaughtUpDate, Date webRateShoppingDate) {
        return createDecision(DECISION_TYPE_BAR_OVERRIDE, ProcessStatus.COMMITTED, caughtUpDate, businessDate, rateUnqualifiedCaughtUpDate, webRateShoppingDate);
    }

    public Collection<Decision> createBAROverrideDecisions(Date caughtUpDate, Date businessDate, Date rateUnqualifiedCaughtUpDate, Date webRateShoppingDate, int decisionCount) {
        return createDecisions(DECISION_TYPE_BAR_OVERRIDE, ProcessStatus.COMMITTED, caughtUpDate, businessDate, rateUnqualifiedCaughtUpDate, webRateShoppingDate, decisionCount);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Decision createDemandOverrideDecision() {
        return createDecision(DECISION_TYPE_DEMAND_OVERRIDE, ProcessStatus.COMMITTED);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Decision createWashOverrideDecision() {
        return createDecision(DECISION_TYPE_WASH_OVERRIDE, ProcessStatus.COMMITTED);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Decision createWashOverrideByGroupDecision() {
        return createDecision(DECISION_TYPE_WASH_OVERRIDE_BY_GROUP, ProcessStatus.COMMITTED);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Decision createCostofWalkOverrideDecision() {
        return createDecision(DECISION_TYPE_COW_VALUE_OVERRIDE, ProcessStatus.COMMITTED);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Decision createOverbookingOverrideDecision() {
        return createDecision(DECISION_TYPE_DECISION_OVERBOOKING_OVERRIDE, ProcessStatus.COMMITTED);
    }

    public Decision createBarFplosByRoomTypeDecision() {
        return createDecision(DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE, ProcessStatus.IN_PROGRESS);
    }

    public Decision createBarFplosByRoomClassDecision() {
        return createDecision(DECISION_TYPE_BAR_FPLOS_BY_ROOM_CLASS, ProcessStatus.IN_PROGRESS);
    }

    public Decision createDailyBarDecision() {
        return createDecision(DECISION_TYPE_DAILYBAR, ProcessStatus.IN_PROGRESS);
    }

    public Decision createDailyBarFullRefreshDecision() {
        return createDecision(DECISION_TYPE_DAILYBAR_FULL_REFRESH, ProcessStatus.IN_PROGRESS);
    }

    public Decision createESDailyBarDecision() {
        return createDecision(DECISION_TYPE_ES_DAILYBAR, ProcessStatus.IN_PROGRESS);
    }

    public Decision createMINLOSDecision() {
        return createDecision(DECISION_TYPE_MINLOS, ProcessStatus.IN_PROGRESS);
    }


    public Decision createQualifiedFPLOSDecision() {
        return createDecision(DECISION_TYPE_QUALIFIED_FPLOS, ProcessStatus.IN_PROGRESS);
    }

    public Decision createCalibrationDecision() {
        return createDecision(DECISION_TYPE_CALIBRATION, ProcessStatus.SUBMITTED_TO_SAS);
    }

    public Decision createRevisionDecision() {
        return createDecision(DECISION_TYPE_REVISION, ProcessStatus.SUBMITTED_TO_SAS);
    }

    public Decision createReportsDecision() {
        return createDecision(DECISION_TYPE_REPORT, ProcessStatus.SUBMITTED_TO_SAS);
    }

    public Decision createLRAFPLOSDecision() {
        return createDecision(DECISION_TYPE_LRA_FPLOS, ProcessStatus.IN_PROGRESS);
    }

    public Decision createLRAMinLOSDecision() {
        return createDecision(DECISION_TYPE_LRA_MINLOS, ProcessStatus.IN_PROGRESS);
    }

    public Decision createBarFplosByHierarchyDecision() {
        return createDecision(DECISION_TYPE_BAR_FLOS_BY_HIERARCHY, ProcessStatus.IN_PROGRESS);
    }

    public Decision createBarFplosByHierarchyByRoomClassDecision() {
        return createDecision(DECISION_TYPE_BAR_FLOS_BY_HIERARCHY_BY_ROOM_CLASS, ProcessStatus.IN_PROGRESS);
    }

    public Decision createHotelClosureDecision() {
        return createDecision(DECISION_TYPE_HOTEL_CLOSURE, ProcessStatus.SUCCESSFUL);
    }

    public Decision createHotelCloseToArrivalDecision() {
        return createDecision(DECISION_TYPE_HOTEL_CLOSE_TO_ARRIVAL, ProcessStatus.SUCCESSFUL);
    }

    public Decision createGroupFloorOverrideDecision() {
        return createDecision(DECISION_TYPE_GROUP_FLOOR_OVERRIDE, ProcessStatus.SUCCESSFUL);
    }

    public Decision createDecisionForLastGoodDecisions(Integer decisionType) {
        return createDecision(decisionType, ProcessStatus.IN_PROGRESS);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Decision createGroupFinalForecastOverrideDecision() {
        return createDecision(DECISION_TYPE_GROUP_FINAL_FORECAST_OVERRIDE, ProcessStatus.COMMITTED);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Decision createManualRestrictionDecision() {
        return createDecision(DECISION_TYPE_MANUAL_RESTRICTIONS, ProcessStatus.SUCCESSFUL);
    }

    public Decision createManualRestrictionsUploadDecision() {
        return createDecision(DECISION_TYPE_MANUAL_RESTRICTIONS_UPLOAD, ProcessStatus.SUCCESSFUL);
    }

    public Decision createMinMaxLosDecision() {
        return createDecision(DECISION_TYPE_MIN_MAX_LOS, ProcessStatus.IN_PROGRESS);
    }

    public Decision createManualBARUploadDecision() {
        return createDecision(DECISION_TYPE_MANUAL_BAR_UPLOAD, ProcessStatus.COMMITTED);
    }

    public Decision createInventoryLimitOverrideDecision() {
        return createDecision(DECISION_TYPE_INVENTORY_LIMIT_OVERRIDE, ProcessStatus.COMMITTED);
    }

    public Decision createMeetingPackagePricingOverrideDecision() {
        return createDecision(DECISION_TYPE_MEETING_PACKAGE_PRICING_OVERRIDE, ProcessStatus.COMMITTED,
                dateServiceLocal.getCaughtUpDate(), dateServiceLocal.getBusinessDate(), null, null);
    }

    private Decision createDecision(Integer decisionTypeId, Integer processStatusId) {
        Decision decision = new Decision();
        decision.setWebRateDate(dateServiceLocal.getWebRateShoppingDate());
        decision.setRateUnqualifiedDate(dateServiceLocal.getUnqualifiedRateCaughtUpDate());
        decision.setBusinessDate(dateServiceLocal.getBusinessDate());
        decision.setCaughtUpDate(dateServiceLocal.getCaughtUpDate());
        decision.setPropertyID(PacmanThreadLocalContextHolder.getWorkContext().getPropertyId());
        decision.setDecisionTypeId(decisionTypeId);
        decision.setStartDate(DateUtil.getCurrentDate());
        decision.setEndDate(DateUtil.getCurrentDate());
        decision.setProcessStatusId(processStatusId);
        return crudService.save(decision);
    }

    private Decision createDecision(Integer decisionTypeId, Integer processStatusId, Date caughtUpDate, Date businessDate, Date unqualifiedRateCaughtUpDate, Date webRateShoppingDate) {
        Decision decision = new Decision();
        decision.setWebRateDate(webRateShoppingDate);
        decision.setRateUnqualifiedDate(unqualifiedRateCaughtUpDate);
        decision.setBusinessDate(businessDate);
        decision.setCaughtUpDate(caughtUpDate);
        decision.setPropertyID(PacmanThreadLocalContextHolder.getWorkContext().getPropertyId());
        decision.setDecisionTypeId(decisionTypeId);
        decision.setStartDate(DateUtil.getCurrentDate());
        decision.setEndDate(DateUtil.getCurrentDate());
        decision.setProcessStatusId(processStatusId);
        return crudService.save(decision);
    }

    private Collection<Decision> createDecisions(Integer decisionTypeId, Integer processStatusId, Date caughtUpDate,
                                                 Date businessDate, Date unqualifiedRateCaughtUpDate, Date webRateShoppingDate, int decisionCount) {
        List<Decision> decisions = new ArrayList<>();
        for (int i = 0; i < decisionCount; i++) {
            Decision decision = new Decision();
            decision.setWebRateDate(webRateShoppingDate);
            decision.setRateUnqualifiedDate(unqualifiedRateCaughtUpDate);
            decision.setBusinessDate(businessDate);
            decision.setCaughtUpDate(caughtUpDate);
            decision.setPropertyID(PacmanThreadLocalContextHolder.getWorkContext().getPropertyId());
            decision.setDecisionTypeId(decisionTypeId);
            decision.setStartDate(DateUtil.getCurrentDate());
            decision.setEndDate(DateUtil.getCurrentDate());
            decision.setProcessStatusId(processStatusId);
            decisions.add(decision);
        }
        if (CollectionUtils.isNotEmpty(decisions)) {
            return crudService.save(decisions);
        } else {
            return new ArrayList<>();
        }
    }

    public Decision find(Integer decisionId) {
        return crudService.find(Decision.class, decisionId);
    }

    @ForTesting
    @Transactional(propagation = Propagation.REQUIRED)
    public Decision updateDescisionProcessStatus(final int decisionId, final int statusCode, boolean updateEndDTTM) {
        Decision decision = crudService.find(Decision.class, decisionId);
        decision.setProcessStatusId(statusCode);
        if (updateEndDTTM) {
            decision.setEndDate(new Date());
        }
        return crudService.save(decision);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Decision updateDescisionProcessStatusWithNewTransaction(final int decisionId, final int statusCode, boolean updateEndDTTM) {
        return updateDescisionProcessStatus(decisionId, statusCode, updateEndDTTM);
    }

    public Integer getCurrentMaxDecisionId() {
        return (Integer) crudService.findByNamedQuerySingleResult(Decision.GET_MAX_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<Integer> getAllDecisionIds(Integer propertyId) {
        return crudService.findByNamedQuery(Decision.GET_ALL_DECISION_ID, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }


    public List<Integer> getAllDecisionIdsByDecisionType(Integer propertyId, Integer decisionTypeId) {
        return crudService.findByNamedQuery(Decision.GET_ALL_DECISION_ID_BY_DECISION_TYPE,
                QueryParameter.with(PROPERTY_ID, propertyId)
                        .and(DECISION_TYPE_ID, decisionTypeId).parameters());
    }


    @ForTesting
    public List<String> getAllDecisionReferencesListedInDecisionPurgable() {
        return Arrays.stream(DecisionPurgable.values()).map(dp -> dp.name().toUpperCase()).collect(Collectors.toList());
    }

    @Transactional(propagation = Propagation.SUPPORTS)
    public Decision updateDescisionProcessStatus(int decisionId, int statusCode) {
        return updateDescisionProcessStatus(decisionId, statusCode, false);
    }

    public Integer getMaxDecisionIdForDecisionTypes(List<Integer> decisionTypeIds) {
        return (Integer) crudService.findByNamedQuerySingleResult(Decision.GET_MAX_DECISION_ID_FOR_DECISION_TYPES, QueryParameter.with("decisionTypeIDs", decisionTypeIds).parameters());
    }

    public BigInteger getMaxDecisionIdForDecisionTypesExternalSystem(List<Integer> decisionTypeIds) {
        return crudService.findByNamedQuerySingleResult(Decision.GET_MAX_DECISION_ID_FOR_DECISION_TYPES_EXTERNAL_SYSTEM,
                QueryParameter.with("decisionTypeIDs", decisionTypeIds).parameters());
    }

    public BigInteger getFullRefreshDecisionToCompare() {
        if (propertyConfigParamService.isDailyBarFullRefreshEnabled()) {
            final List<Object[]> lastLatestFullRefreshDecision = crudService.findByNativeQuery("select top 1 " +
                    " Decision_ID as latestFullRefresh, " +
                    " isNull(LEAD(Decision_ID, 1) OVER (ORDER by decision_ID desc),-1) as lastFullRefresh " +
                    "       from Decision  " +
                    " where Decision_Type_ID=37");
            return isEmpty(lastLatestFullRefreshDecision) ? valueOf(-1)
                    : getFullRefreshDecision(lastLatestFullRefreshDecision.get(0));
        }
        return valueOf(-1);
    }

    private BigInteger getFullRefreshDecision(Object[] lastLatestFullRefreshDecision) {
        final BigInteger currentDecisionID = crudService.findByNativeQuerySingleResult("select max(Decision_ID) from Decision where Decision_Type_ID in (18,21,37) ", Map.of());
        final BigInteger latestFullRefreshDecision = (BigInteger) lastLatestFullRefreshDecision[0];
        return currentDecisionID.equals(latestFullRefreshDecision) ? (BigInteger) lastLatestFullRefreshDecision[1] : latestFullRefreshDecision;
    }

    public Integer getLastUploadedDecisionIdByDecisionTypeAndProcessStatusId(Date lastUploadedDate, Integer decisionTypeId, Integer processStatusId) {
        return crudService.findByNamedQuerySingleResult(Decision.GET_LAST_UPLOADED_DECISION_ID_BY_DECISION_TYPE_AND_PROCESS_STATUS_ID, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                .and(LAST_UPLOAD_DATE, lastUploadedDate)
                .and(DECISION_TYPE_ID, decisionTypeId)
                .and(PROCESS_STATUS_ID, processStatusId).parameters());
    }

    public Integer getLastDecisionIdByDecisionTypeAndProcessStatusId(Integer decisionTypeId, Integer processStatusId) {
        return crudService.findByNamedQuerySingleResult(Decision.GET_LAST_DECISION_ID_BY_DECISION_TYPE_AND_PROCESS_STATUS_ID, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                .and(DECISION_TYPE_ID, decisionTypeId)
                .and(PROCESS_STATUS_ID, processStatusId).parameters());
    }

    public Integer getLastBDEDecisionId() {
        return crudService.findByNamedQuerySingleResult(Decision.GET_LAST_BDE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public Integer getLastCDPDecisionId() {
        return crudService.findByNamedQuerySingleResult(Decision.GET_LAST_CDP,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public Integer getLastDecisionIdSinceDate(Date changesSinceDate) {
        return crudService.findByNamedQuerySingleResult(Decision.GET_FIRST_BEFORE_DATE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and("selectedDate", changesSinceDate).parameters());
    }
}
