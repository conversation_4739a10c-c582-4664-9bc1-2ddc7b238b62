package com.ideas.tetris.pacman.services.security;

import com.ideas.tetris.platform.common.license.entities.Feature;
import com.ideas.tetris.platform.common.license.manage.LicenseFeature;
import com.ideas.tetris.platform.common.license.repository.LicenseRepository;
import lombok.extern.slf4j.Slf4j;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;

@Component
@Slf4j
public class LicenseFeatureCreator {

    @Autowired
    LicenseRepository licenseRepository;

    @PostConstruct
    public void addNewlyCreatedFeaturesInLicenseFramework() {
        Reflections REFLECTIONS_ON_COM_IDEAS = new Reflections("com.ideas");
        Set<Class<?>> typesAnnotatedWith = REFLECTIONS_ON_COM_IDEAS.getTypesAnnotatedWith(LicenseFeature.class);
        List<String> licenseFeatures = licenseRepository.getAllFeatures().stream().map(Feature::getFeatureCode).collect(Collectors.toList());

        typesAnnotatedWith.forEach(c -> {
            LicenseFeature annotation = c.getAnnotation(LicenseFeature.class);
            if (!licenseFeatures.contains(annotation.code())) {
                try {
                    licenseRepository.createFeature(annotation.type(), annotation.code().toLowerCase(), annotation.name(), annotation.configParameter());
                    // add to list to avoid duplicate entries and re-fetching for methods call below
                    licenseFeatures.add(annotation.code().toLowerCase());
                } catch (Exception e) {
                    log.error("Error while saving feature: " + e.getMessage());
                }
            }
        });

        Set<Method> methodsAnnotatedWith = REFLECTIONS_ON_COM_IDEAS.getMethodsAnnotatedWith(LicenseFeature.class);

        methodsAnnotatedWith.forEach(c -> {
            LicenseFeature annotation = c.getAnnotation(LicenseFeature.class);
            if (!licenseFeatures.contains(annotation.code())) {
                try {
                    licenseRepository.createFeature(annotation.type(), annotation.code().toLowerCase(), isNull(annotation.name()) ? annotation.code() : annotation.name(), annotation.configParameter());
                    licenseFeatures.add(annotation.code().toLowerCase());
                } catch (Exception e) {
                    log.error("Error while saving feature: " + e.getMessage());
                }
            }
        });
    }

}
