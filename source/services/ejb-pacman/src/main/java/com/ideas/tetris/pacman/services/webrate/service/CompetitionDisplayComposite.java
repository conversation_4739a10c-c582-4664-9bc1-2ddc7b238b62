package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.platform.common.configparams.entities.ParameterPredefinedValue;

/**
 * These two are exposed as a common entity in the UI. This minimizes the chatter with
 * the server since they would always be CRUD'ed in pairs.
 */

// As per discussion with PO, replacing WebrateChannel with WebrateCompetitors.

public class CompetitionDisplayComposite {
    private ParameterPredefinedValue competitionChoice;
    private WebrateCompetitors absoluteCompetitors;
    private boolean doesExistAtPropertyLevel = false;

    public ParameterPredefinedValue getCompetitionChoice() {
        return competitionChoice;
    }

    public void setCompetitionChoice(ParameterPredefinedValue competitionChoice) {
        this.competitionChoice = competitionChoice;
    }

    public WebrateCompetitors getAbsoluteCompetitors() {
        return absoluteCompetitors;
    }

    public void setAbsoluteCompetitors(WebrateCompetitors absoluteCompetitors) {
        this.absoluteCompetitors = absoluteCompetitors;
    }

    public boolean getDoesExistAtPropertyLevel() {
        return doesExistAtPropertyLevel;
    }

    public void setDoesExistAtPropertyLevel(boolean doesExistAtPropertyLevel) {
        this.doesExistAtPropertyLevel = doesExistAtPropertyLevel;
    }
}
