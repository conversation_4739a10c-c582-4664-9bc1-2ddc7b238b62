package com.ideas.tetris.pacman.services.database;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.DBLoc;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * This class determines which database server and database instance a particular tenant database belongs too.
 */
@Component
@Transactional
public class DBInstanceDeterminer {
    private static final Logger LOGGER = Logger.getLogger(DBInstanceDeterminer.class);

    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;

    public DBLoc determineInstance() {
        DBLoc dbLoc;
        dbLoc = getDefaultDatabaseInstance();
        LOGGER.info("Determined DBLoc by default instance: " + dbLoc);

        return dbLoc;
    }

    private DBLoc getDefaultDatabaseInstance() {
        DBLoc dbLoc = new DBLoc();
        dbLoc.setJndiName(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DEFAULT_JNDI).toString());
        dbLoc.setJndiNameForReports(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DEFAULT_JNDI_FOR_REPORTS).toString());
        // Set Default Values
        dbLoc.setServerName(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DB_PROPERTY_NODE).toString());
        dbLoc.setServerInst(SystemConfig.getServerInstance());
        dbLoc.setPortNumber(Integer.valueOf(SystemConfig.getServerPort()));
        return dbLoc;
    }
}
