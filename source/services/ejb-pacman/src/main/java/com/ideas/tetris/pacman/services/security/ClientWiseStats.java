package com.ideas.tetris.pacman.services.security;

public class ClientWiseStats {
    private String clientCode;
    private Integer totalUsersFoundInBoth = 0;
    private Integer missingGlobalUsersInLDAP = 0;

    public ClientWiseStats(String clientCode) {
        this.clientCode = clientCode;
    }

    public ClientWiseStats() {
        //empty constructor for bean container
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public Integer getMissingGlobalUsersInLDAP() {
        return missingGlobalUsersInLDAP;
    }

    public void setMissingGlobalUsersInLDAP(Integer missingGlobalUsersInLDAP) {
        this.missingGlobalUsersInLDAP = missingGlobalUsersInLDAP;
    }

    public Integer getTotalUsersFoundInBoth() {
        return totalUsersFoundInBoth;
    }

    public void setTotalUsersFoundInBoth(Integer totalUsersFoundInBoth) {
        this.totalUsersFoundInBoth = totalUsersFoundInBoth;
    }

    public Integer getTotalUsersInGlobalDB() {
        return totalUsersFoundInBoth + missingGlobalUsersInLDAP;
    }
}
