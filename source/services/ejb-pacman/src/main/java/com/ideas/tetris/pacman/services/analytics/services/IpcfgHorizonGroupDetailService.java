package com.ideas.tetris.pacman.services.analytics.services;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.roa.referenceprice.entity.HorizonGroupDetail;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Service
public class IpcfgHorizonGroupDetailService {
    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    private CrudService tenantCrudService;

    public List<HorizonGroupDetail> getHorizonGroupDetails(Collection<Integer> horizonGroupIds) {
        return tenantCrudService.findByNamedQuery(HorizonGroupDetail.FIND_BY_HORIZON_IDS, QueryParameter
                .with("horizonGroupIds", horizonGroupIds)
                .parameters());
    }

}
