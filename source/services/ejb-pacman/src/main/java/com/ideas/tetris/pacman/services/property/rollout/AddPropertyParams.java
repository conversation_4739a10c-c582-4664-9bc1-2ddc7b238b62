package com.ideas.tetris.pacman.services.property.rollout;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ideas.tetris.pacman.services.property.PropertyBuildType;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.math.BigDecimal;

public class AddPropertyParams {
    // these are required to add a property
    private String clientCode;
    private String propertyCode;
    private String sFDCAccountNo;
    private String propertyName;
    private String propertyTimezone;
    private String yieldCurrency;
    private String subscriptionType;
    private String configurationMethod;
    private String baseCurrency;
    private boolean enableYC;
    private Integer estimatedCapacity;

    @JsonProperty("propertyBuildType")
    private PropertyBuildType buildType = PropertyBuildType.STANDARD;

    // these are optional
    private String webrateAlias;
    private String crsTimezone;
    private String externalSystem;
    private String externalSubSystem;
    private String onboardOptions;
    private String ngiEnvironment;
    private Integer remoteAgentId;
    private boolean isLimitedDataBuild = false;
    private boolean isPropertyMigratedFromG2 = false;
    private BigDecimal defaultRoomRevenueTaxInRateOutput;


    @JsonCreator
    public AddPropertyParams(
            @JsonProperty("clientCode") String clientCode,
            @JsonProperty("propertyCode") String propertyCode,
            @JsonProperty("propertyName") String propertyName,
            @JsonProperty("propertyTimezone") String propertyTimezone,
            @JsonProperty("yieldCurrency") String yieldCurrency,
            @JsonProperty("estimatedCapacity") Integer estimatedCapacity,
            @JsonProperty("subscriptionType") String subscriptionType,
            @JsonProperty("configurationMethod") String configurationMethod) {
        if (StringUtils.isBlank(clientCode)) {
            throw new IllegalArgumentException("clientCode is required");
        }
        this.clientCode = clientCode;
        if (StringUtils.isBlank(propertyCode)) {
            throw new IllegalArgumentException("propertyCode is required");
        }
        this.propertyCode = propertyCode;
        if (StringUtils.isBlank(propertyName)) {
            throw new IllegalArgumentException("propertyName is required");
        }
        this.propertyName = propertyName;
        if (StringUtils.isBlank(propertyTimezone)) {
            throw new IllegalArgumentException("propertyTimezone is required");
        }
        this.propertyTimezone = propertyTimezone;
        if (StringUtils.isBlank(yieldCurrency)) {
            throw new IllegalArgumentException("yieldCurrency is required");
        }
        this.yieldCurrency = yieldCurrency;
        if (StringUtils.isBlank(subscriptionType)) {
            throw new IllegalArgumentException("subscriptionType is required");
        }
        this.subscriptionType = subscriptionType;

        if (StringUtils.isBlank(configurationMethod)) {
            throw new IllegalArgumentException("configurationMethod is required");
        }
        this.configurationMethod = configurationMethod;

        if (estimatedCapacity == null) {
            throw new IllegalArgumentException("estimatedCapacity is required");
        }
        this.estimatedCapacity = estimatedCapacity;
    }

    public String getClientCode() {
        return clientCode;
    }

    public String getPropertyCode() {
        return propertyCode;
    }

    public String getSFDCAccountNo() {
        return sFDCAccountNo;
    }

    public void setSFDCAccountNo(String sFDCAccountNo) {
        this.sFDCAccountNo = sFDCAccountNo;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public String getPropertyTimezone() {
        return propertyTimezone;
    }

    public String getYieldCurrency() {
        return yieldCurrency;
    }

    public String getSubscriptionType() {
        return subscriptionType;
    }

    public String getBaseCurrency() {
        return baseCurrency;
    }

    public String getWebrateAlias() {
        return webrateAlias;
    }

    public void setWebrateAlias(String webrateAlias) {
        this.webrateAlias = webrateAlias;
    }

    public String getCrsTimezone() {
        return crsTimezone;
    }

    public void setCrsTimezone(String crsTimezone) {
        this.crsTimezone = crsTimezone;
    }

    public Integer getRemoteAgentId() {
        return remoteAgentId;
    }

    public void setRemoteAgentId(Integer remoteAgentId) {
        this.remoteAgentId = remoteAgentId;
    }

    public String getExternalSystem() {
        return externalSystem;
    }

    public void setExternalSystem(String externalSystem) {
        this.externalSystem = externalSystem;
    }

    public boolean isEnableYC() {
        return enableYC;
    }

    public void setEnableYC(boolean enableYC) {
        this.enableYC = enableYC;
    }

    public boolean isLimitedDataBuild() {
        return isLimitedDataBuild;
    }

    public void setIsLimitedDataBuild(boolean flag) {
        isLimitedDataBuild = flag;
    }

    public boolean isPropertyMigratedFromG2() {
        return isPropertyMigratedFromG2;
    }

    public void setIsPropertyMigratedFromG2(boolean propertyMigratedFromG2) {
        isPropertyMigratedFromG2 = propertyMigratedFromG2;
    }

    public BigDecimal getDefaultRoomRevenueTaxInRateOutput() {
        return defaultRoomRevenueTaxInRateOutput;
    }

    public void setDefaultRoomRevenueTaxInRateOutput(BigDecimal defaultRoomRevenueTaxInRateOutput) {
        this.defaultRoomRevenueTaxInRateOutput = defaultRoomRevenueTaxInRateOutput;
    }

    public String getExternalSubSystem() {
        return externalSubSystem;
    }

    public String getOnboardOptions() {
        return onboardOptions;
    }

    public String getNgiEnvironment() {
        return ngiEnvironment;
    }

    public void setExternalSubSystem(String externalSubSystem) {
        this.externalSubSystem = externalSubSystem;
    }

    public void setOnboardOptions(String onboardOptions) {
        this.onboardOptions = onboardOptions;
    }

    public void setNgiEnvironment(String ngiEnvironment) {
        this.ngiEnvironment = ngiEnvironment;
    }

    public void setSubscriptionType(String subscriptionType) {
        this.subscriptionType = subscriptionType;
    }

    public String getConfigurationMethod() {
        return configurationMethod;
    }

    public void setConfigurationMethod(String configurationMethod) {
        this.configurationMethod = configurationMethod;
    }

    public void setBaseCurrency(String baseCurrency) {
        if (enableYC && StringUtils.isBlank(baseCurrency)) {
            throw new IllegalArgumentException("baseCurrency is required");
        }
        this.baseCurrency = baseCurrency;
    }

    public PropertyBuildType getBuildType() {
        return buildType;
    }

    public void setBuildType(PropertyBuildType buildType) {
        this.buildType = buildType;
    }

    public Integer getEstimatedCapacity() {
        return estimatedCapacity;
    }

    public void setEstimatedCapacity(Integer estimatedCapacity) {
        this.estimatedCapacity = estimatedCapacity;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj == this) {
            return true;
        }
        if (obj.getClass() != getClass()) {
            return false;
        }
        AddPropertyParams params = (AddPropertyParams) obj;
        return new EqualsBuilder()
                .append(clientCode, params.getClientCode())
                .append(propertyCode, params.getPropertyCode())
                .append(propertyName, params.getPropertyName())
                .append(propertyTimezone, params.getPropertyTimezone())
                .append(yieldCurrency, params.getYieldCurrency())
                .append(subscriptionType, params.getSubscriptionType())
                .append(configurationMethod, params.getConfigurationMethod())
                .append(baseCurrency, params.getBaseCurrency())
                .append(webrateAlias, params.getWebrateAlias())
                .append(crsTimezone, params.getCrsTimezone())
                .append(externalSystem, params.getExternalSystem())
                .append(externalSubSystem, params.getExternalSubSystem())
                .append(onboardOptions, params.getOnboardOptions())
                .append(ngiEnvironment, params.getNgiEnvironment())
                .append(remoteAgentId, params.getRemoteAgentId())
                .append(defaultRoomRevenueTaxInRateOutput, params.getDefaultRoomRevenueTaxInRateOutput())
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder()
                .append(clientCode)
                .append(propertyCode)
                .append(propertyName)
                .append(propertyTimezone)
                .append(yieldCurrency)
                .append(subscriptionType)
                .append(configurationMethod)
                .append(baseCurrency)
                .append(webrateAlias)
                .append(crsTimezone)
                .append(externalSystem)
                .append(externalSubSystem)
                .append(onboardOptions)
                .append(ngiEnvironment)
                .append(remoteAgentId)
                .append(defaultRoomRevenueTaxInRateOutput)
                .hashCode();
    }
}
