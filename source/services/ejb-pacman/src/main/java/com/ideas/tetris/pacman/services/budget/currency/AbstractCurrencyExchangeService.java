package com.ideas.tetris.pacman.services.budget.currency;


import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.services.budget.dto.CurrencyExchangeDTO;
import com.ideas.tetris.pacman.services.budget.dto.RevPlanSubmitTypeRequestDTO;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Qualifier;

public abstract class AbstractCurrencyExchangeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractCurrencyExchangeService.class);

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    @Autowired
	protected PacmanConfigParamsService configService;

    @Autowired
	private DateService dateService;

    @Autowired
    protected GenericCurrencyExchangeService genericCurrencyExchangeService;


    public String getRMSCurrencyCode(String revPlanCurrencyCode) {
        String yieldCurrencyCode = configService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE);
        String baseCurrencyCode = configService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE);

        String currencyCode = Objects.nonNull(yieldCurrencyCode) ? yieldCurrencyCode : baseCurrencyCode;

        if (StringUtils.isBlank(currencyCode)) {
            LOGGER.info("RMS Yield Currency/Base Currency is not set");
            currencyCode = revPlanCurrencyCode;
        }

        return currencyCode;
    }


    protected LocalDate getSystemDate() {
        return JavaLocalDateUtils.fromDate(dateService.getCaughtUpDate());
    }


    public boolean isCurrencyConversionRequired(String revPlanCurrencyCode) {
        return !StringUtils.equals(revPlanCurrencyCode, getRMSCurrencyCode(revPlanCurrencyCode));
    }

    public List<CurrencyExchangeDTO> getCurrencyExchangeRates(String clientCode, String propertyCode, RevPlanSubmitTypeRequestDTO revPlanSubmitTypeRequestDTO) {

        final LocalDate startDate = revPlanSubmitTypeRequestDTO.getStartDate();
        final LocalDate endDate = revPlanSubmitTypeRequestDTO.getEndDate();
        final String revPlanCurrencyCode = revPlanSubmitTypeRequestDTO.getRevPlanCurrencyCode();
        final String rmsCurrencyCode = getRMSCurrencyCode(revPlanCurrencyCode);

        return getCurrencyExchangeDTOS(clientCode, propertyCode, startDate, endDate, revPlanCurrencyCode, rmsCurrencyCode);
    }

    public List<CurrencyExchangeDTO> getCurrencyExchangeDTOS(String clientCode, String propertyCode, LocalDate startDate, LocalDate endDate, String fromCurrencyCode, String toCurrencyCode) {
        final LocalDate systemDate = getSystemDate();
        List<CurrencyExchangeDTO> currencyExchangeDTOS = new ArrayList<>();

        if ((startDate.isEqual(systemDate) || startDate.isAfter(systemDate)) && endDate.isAfter(systemDate)) {
            currencyExchangeDTOS = getCurrencyExchangeRateForDateRange(clientCode, propertyCode, fromCurrencyCode, toCurrencyCode, startDate, startDate);
        } else if (startDate.isBefore(systemDate) && endDate.isAfter(systemDate)) {
            final List<CurrencyExchangeDTO> currencyExchangeRateForPastDateRange = getCurrencyExchangeRateForDateRange(clientCode, propertyCode, fromCurrencyCode, toCurrencyCode, startDate, systemDate.minusDays(1));
            final List<CurrencyExchangeDTO> currencyExchangeRateForFutureDateRange = getCurrencyExchangeRateForDateRange(clientCode, propertyCode, fromCurrencyCode, toCurrencyCode, systemDate, systemDate);
            currencyExchangeDTOS.addAll(currencyExchangeRateForPastDateRange);
            currencyExchangeDTOS.addAll(currencyExchangeRateForFutureDateRange);
        } else {
            currencyExchangeDTOS = getCurrencyExchangeRateForDateRange(clientCode, propertyCode, fromCurrencyCode, toCurrencyCode, startDate, endDate);
        }
        return currencyExchangeDTOS;
    }

    protected abstract List<CurrencyExchangeDTO> getCurrencyExchangeRateForDateRange(String clientCode, String propertyCode, String revPlanCurrencyCode, String rmsCurrencyCode, LocalDate startDate, LocalDate minusDays);


}
