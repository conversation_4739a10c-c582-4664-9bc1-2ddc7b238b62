package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.datafeed.dto.grouppricing.GroupPricingConfig;
import com.ideas.tetris.pacman.services.datafeed.dto.grouppricing.GroupPricingPreferredRoomTypeConfig;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfiguration;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationService;
import com.ideas.tetris.pacman.services.servicingcostbylos.service.ServicingCostByLOSService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Component
@Transactional
public class GroupPricingConfigService {

    @Autowired
    GroupPricingConfigurationService groupPricingConfigurationService;
    @Autowired
    ServicingCostByLOSService servicingCostByLOSService;

    public List<GroupPricingConfig> getGroupPricingConfigDetails(Integer propertyId) {
        List<GroupPricingConfig> groupPricingConfigs = new ArrayList<>();

        List<GroupPricingConfiguration> groupPricingConfigsByRoomClass = servicingCostByLOSService.isProfitOptimizationEnabled() ?
                servicingCostByLOSService.getGroupPricingConfigDetails(propertyId) :
                groupPricingConfigurationService.getGroupPricingConfigurationsPerRoomClass();

        populateGroupPricingConfigs(groupPricingConfigs, groupPricingConfigsByRoomClass);

        return groupPricingConfigs;
    }

    public List getGroupPricingPreferredRoomTypeConfigurations() {
        List<GroupPricingPreferredRoomTypeConfig> groupPricingPreferredRoomTypeConfigs = new ArrayList<>();
        groupPricingConfigurationService.getAllConfigAccomType()
                .stream()
                .forEach(e -> {
                    if (e.isActive()) {
                        groupPricingPreferredRoomTypeConfigs.add(new GroupPricingPreferredRoomTypeConfig(e.getAccomType().getAccomTypeCode()));
                    }
                });
        return groupPricingPreferredRoomTypeConfigs;
    }

    private void populateGroupPricingConfigs(List<GroupPricingConfig> groupPricingConfigs, List<GroupPricingConfiguration> groupPricingConfigsByType) {
        groupPricingConfigsByType.stream().forEach(e -> {
            GroupPricingConfig groupPricingConfig = new GroupPricingConfig(getAccomClass(e.getAccomClass()), e.getPerRoomServicingCost());
            groupPricingConfigs.add(groupPricingConfig);
        });
    }

    private String getAccomClass(AccomClass accomClass) {
        if (null != accomClass) {
            return accomClass.getCode();
        }
        return Constants.RUN_OF_HOUSE;
    }
}
