package com.ideas.tetris.pacman.services.reports.individualgroupwash;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockDetail;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockExtendedDTO;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;
import com.ideas.tetris.pacman.services.groupwash.IndividualGroupForWashOverrideService;
import com.ideas.tetris.pacman.services.groupwash.PickupTypeCode;
import com.ideas.tetris.pacman.services.groupwash.SystemWashByGroup;
import com.ideas.tetris.pacman.services.groupwash.WashOverrideByGroup;
import com.ideas.tetris.pacman.services.notes.NotesService;
import com.ideas.tetris.pacman.services.notes.entity.DateNote;
import com.ideas.tetris.pacman.services.reports.individualgroupwash.dto.IndividualGroupBlockTableDto;
import com.ideas.tetris.pacman.services.reports.individualgroupwash.dto.IndividualGroupMasterDto;
import com.ideas.tetris.pacman.services.reports.individualgroupwash.dto.IndividualGroupWashDto;
import com.ideas.tetris.pacman.services.reports.individualgroupwash.dto.IndividualGroupWashOverrideHistoryDto;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateInterval;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.time.FastDateFormat;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class IndividualGroupWashReportService {

    @Autowired
    IndividualGroupForWashOverrideService individualGroupForWashOverrideService;

    @Autowired
	private NotesService noteService;

    @Autowired
    DateService dateService;

    private List<IndividualGroupMasterDto> individualGroupMasterDtos = Collections.synchronizedList(new ArrayList<>());
    private String language;
    private String dateFormat;

    public synchronized List<IndividualGroupWashDto> getIndividualGroupWashDtos(LocalDate date1, LocalDate date2) {
        List<IndividualGroupWashDto> individualGroupWashDtoList = new ArrayList<>();
        for (IndividualGroupWashOverrideHistoryDto individualGroupWashOverrideHistoryDto : getIndividualGroupWashOverrideHistoryDtos(date1, date2)) {
            IndividualGroupWashDto individualGroupWashDto = new IndividualGroupWashDto();
            individualGroupWashDto.setGroupCode(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getGroupCode());
            individualGroupWashDto.setName(individualGroupWashOverrideHistoryDto.getIndividualGroupMasterDto().getName());
            individualGroupWashDto.setCode(individualGroupWashOverrideHistoryDto.getIndividualGroupMasterDto().getGroupMaster().getMarketSegment().getCode());
            individualGroupWashDto.setForecastGroupName(individualGroupWashOverrideHistoryDto.getIndividualGroupMasterDto().getGroupMaster().getMarketSegment().getActiveMktSegForecastGroup().getForecastGroup().getName());
            individualGroupWashDto.setStartDate(individualGroupWashOverrideHistoryDto.getIndividualGroupMasterDto().getGroupMaster().getStartDate());
            individualGroupWashDto.setEndDate(individualGroupWashOverrideHistoryDto.getIndividualGroupMasterDto().getGroupMaster().getEndDate());
            individualGroupWashDto.setSalesPerson(individualGroupWashOverrideHistoryDto.getIndividualGroupMasterDto().getGroupMaster().getSalesPerson());
            individualGroupWashDto.setGroupStatusCode(individualGroupWashOverrideHistoryDto.getIndividualGroupMasterDto().getGroupMaster().getGroupStatusCode());
            individualGroupWashDto.setPickupTypeAsText(individualGroupWashOverrideHistoryDto.getPickupTypeAsText());
            individualGroupWashDto.setCutoffDate(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getCutoffDate() != null ? JavaLocalDateUtils.toDate(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getCutoffDate()) : null);
            individualGroupWashDto.setOverrideStatusAsText(individualGroupWashOverrideHistoryDto.getOverrideStatusAsText());
            individualGroupWashDto.setOverrideDate(individualGroupWashOverrideHistoryDto.getOverrideDate());
            individualGroupWashDto.setUserName(individualGroupWashOverrideHistoryDto.getWashOverrideByGroup().getUserName());
            individualGroupWashDto.setOccupancyDate(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getOccupancyDate() != null ? JavaLocalDateUtils.toDate(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getOccupancyDate()) : null);
            individualGroupWashDto.setDow(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getOccupancyDate() != null ? getText(JavaLocalDateUtils.getDayOfWeek((individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getOccupancyDate())).getCaption().toLowerCase()) : null);
            individualGroupWashDto.setGroupArrivals(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getGroupArrivals());
            individualGroupWashDto.setGroupDepartures(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getGroupDepartures());
            individualGroupWashDto.setBlock(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getBlock());
            individualGroupWashDto.setPickup(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getPickup());
            individualGroupWashDto.setAvailableBlock(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getAvailableBlock());
            individualGroupWashDto.setPickupVariance(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getPickupVariance());
            individualGroupWashDto.setRateValue(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getRateValue());
            individualGroupWashDto.setSystemWashValue(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getSystemWashValue());
            individualGroupWashDto.setSystemWashPercent(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getSystemWashPercent());
            individualGroupWashDto.setUserWashValue(individualGroupWashOverrideHistoryDto.getUserWashValue());
            individualGroupWashDto.setUserWashPercent(individualGroupWashOverrideHistoryDto.getUserWashPercent());
            individualGroupWashDto.setDaysToArrival(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getDaysToArrival());
            individualGroupWashDto.setExpirationDate(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getExpirationDate() != null ? JavaLocalDateUtils.toDate(individualGroupWashOverrideHistoryDto.getIndividualGroupBlockTableDto().getExpirationDate()) : null);
            individualGroupWashDto.setNotesAsText(individualGroupWashOverrideHistoryDto.getNotesAsText());

            individualGroupWashDtoList.add(individualGroupWashDto);
        }
        return individualGroupWashDtoList;
    }

    private synchronized List<IndividualGroupWashOverrideHistoryDto> getIndividualGroupWashOverrideHistoryDtos(LocalDate date1, LocalDate date2) {


        List<IndividualGroupMasterDto> groupMasters = getGroupMasters(getGroupMasters(date1, date2));
        List<Integer> groupMasterIds = groupMasters.stream()
                .map(IndividualGroupMasterDto::getId)
                .collect(Collectors.toList());
        if (groupMasterIds.isEmpty()) {
            return new ArrayList<>();
        }

        LocalDate caughtUpDate = DateUtil.convertJavaUtilDateToLocalDate(dateService.getCaughtUpDate());
        LocalDate startDate = caughtUpDate.minusYears(1);
        LocalDate endDate = caughtUpDate.plusDays(365);

        LocalDateInterval groupBlockInterval = new LocalDateInterval(JavaLocalDateUtils.toJodaLocalDate(startDate), JavaLocalDateUtils.toJodaLocalDate(endDate));
        List<GroupBlockDetail> allGroupMastersGroupBlocks = individualGroupForWashOverrideService.getGroupBlockData(groupMasterIds, groupBlockInterval.getStartDate(), groupBlockInterval.getEndDate());

        List<GroupBlockExtendedDTO> groupData = individualGroupForWashOverrideService.getGroupWashData(groupMasterIds, groupBlockInterval.getStartDate(), groupBlockInterval.getEndDate(), true);

        Map<String, GroupBlockExtendedDTO> groupBlockExtendedDTOMap = convertGroupWashListToMap(groupData);

        Map<Integer, List<GroupBlockDetail>> group = allGroupMastersGroupBlocks.stream()
                .collect(Collectors.groupingBy(detail -> detail.getGroupBlockMaster().getId(),
                        Collectors.mapping(item -> item, Collectors.toList())));
        ArrayList<IndividualGroupBlockTableDto> individualGroupBlockTableDtos = new ArrayList<>();
        for (Map.Entry<Integer, List<GroupBlockDetail>> entry : group.entrySet()) {
            //this method only works for blocks from a single group master
            individualGroupBlockTableDtos.addAll(toGroupBlockBeanViewWrapper(entry.getValue()));
        }

        List<Date> occupancyDates = allGroupMastersGroupBlocks.stream()
                .map(item -> item.getOccupancyDate().toDate())
                .collect(Collectors.toList());

        List<DateNote> notes = noteService.getNotes(occupancyDates);

        List<WashOverrideByGroup> userWashes = getWashOverrideByGroups(groupMasterIds);
        List<IndividualGroupWashOverrideHistoryDto> individualGroupWashOverrideHistoryDtos = new ArrayList<>();
        for (IndividualGroupBlockTableDto individualGroupBlockTableDto : individualGroupBlockTableDtos) {
            IndividualGroupMasterDto individualGroupMasterDto = getGroupMaster(individualGroupBlockTableDto.getGroupMasterId());
            GroupBlockExtendedDTO groupWashData = getGroupWashExtendedData(individualGroupBlockTableDto.getOccupancyDate(),
                    individualGroupBlockTableDto.getGroupId(), individualGroupMasterDto.getGroupMaster().getMarketSegment().getId(), groupBlockExtendedDTOMap);
            individualGroupBlockTableDto.setGroupArrivals(groupWashData.getGroupArrivals());
            individualGroupBlockTableDto.setGroupCode(groupWashData.getGroupRateCode());
            individualGroupBlockTableDto.setGroupDepartures(groupWashData.getGroupDepartures());
            individualGroupBlockTableDto.setPickupVariance(groupWashData.getPickupVariance());
            individualGroupBlockTableDto.setRateValue(groupWashData.getRateValue());
            List<WashOverrideByGroup> singleBlockWashOverrides = getWashOverrides(individualGroupBlockTableDto, userWashes);
            boolean overrideExists;
            if (singleBlockWashOverrides.isEmpty()) {
                WashOverrideByGroup emptyOverride = new WashOverrideByGroup();
                //add empty for a "no override" row
                singleBlockWashOverrides.add(emptyOverride);
                overrideExists = false;
            } else {
                overrideExists = true;
            }
            for (WashOverrideByGroup singleBlockWashOverride : singleBlockWashOverrides) {
                IndividualGroupWashOverrideHistoryDto individualGroupWashOverrideHistoryDto = new IndividualGroupWashOverrideHistoryDto();
                individualGroupWashOverrideHistoryDtos.add(individualGroupWashOverrideHistoryDto);

                individualGroupWashOverrideHistoryDto.setWashOverrideByGroup(singleBlockWashOverride);
                individualGroupWashOverrideHistoryDto.setIndividualGroupMasterDto(individualGroupMasterDto);
                individualGroupWashOverrideHistoryDto.setIndividualGroupBlockTableDto(individualGroupBlockTableDto);
                individualGroupWashOverrideHistoryDto.setNotesAsText(getNotesAsText(individualGroupBlockTableDto.getOccupancyDate(), notes));
                individualGroupWashOverrideHistoryDto.setOverrideStatusAsText(getOverrideStatusAsText(singleBlockWashOverride));
                individualGroupWashOverrideHistoryDto.setPickupTypeAsText(getPickupTypeAsText(individualGroupMasterDto));
                setHistoryOverrideValues(individualGroupWashOverrideHistoryDto, overrideExists, singleBlockWashOverride, individualGroupBlockTableDto);
            }
        }

        individualGroupWashOverrideHistoryDtos = individualGroupWashOverrideHistoryDtos.stream()
                .sorted(Comparator.comparing(a -> a.getWashOverrideByGroup().getCreateDate() == null ? LocalDateTime.now() : a.getWashOverrideByGroup().getCreateDate()))
                .collect(Collectors.toList());

        Collections.reverse(individualGroupWashOverrideHistoryDtos);

        Comparator<IndividualGroupWashOverrideHistoryDto> comparator = Comparator.comparing(a ->
                a.getIndividualGroupMasterDto().getGroupMaster().getStartDate().toString().concat(
                        a.getIndividualGroupMasterDto().getGroupMaster().getEndDate().toString()
                ));
        comparator = comparator.thenComparing(a -> a.getIndividualGroupMasterDto().getGroupMaster().getGroupStatusCode());
        comparator = comparator.thenComparing(a -> a.getIndividualGroupBlockTableDto().getOccupancyDate());
        comparator = comparator.thenComparing(a -> a.getIndividualGroupMasterDto().getName().toLowerCase());
        individualGroupWashOverrideHistoryDtos = individualGroupWashOverrideHistoryDtos.stream()
                .sorted(comparator)
                .collect(Collectors.toList());

        return individualGroupWashOverrideHistoryDtos;
    }

    private Map<String, GroupBlockExtendedDTO> convertGroupWashListToMap(List<GroupBlockExtendedDTO> groupArrivalsData) {
        Map<String, GroupBlockExtendedDTO> groupBlockExtendedDTOMap = new HashMap<>();
        for (GroupBlockExtendedDTO extendedDTO : groupArrivalsData) {
            groupBlockExtendedDTOMap.put("" + extendedDTO.getGroupId() + extendedDTO.getOccupancyDate() + extendedDTO.getMktSegId(), extendedDTO);
        }
        return groupBlockExtendedDTOMap;
    }

    private List<WashOverrideByGroup> getWashOverrideByGroups(List<Integer> groupMasterIds) {
        List<WashOverrideByGroup> existingWashesWithAccomTypeEntries = individualGroupForWashOverrideService.getUserWashes(groupMasterIds, true);
        //only need one per "value change", but result include per change and per accom type, filter out extra accom type entries
        //first sort by create date then occ date then group, so we can eliminate "value change entries"
        //Is impossible to tell two different inactive entries since there is no single key that represents them

        existingWashesWithAccomTypeEntries = existingWashesWithAccomTypeEntries.stream()
                .sorted(Comparator.comparing(WashOverrideByGroup::getCreateDate)
                        .thenComparing(WashOverrideByGroup::getOccupancyDate)
                        .thenComparing(group -> group.getGroupMaster().getId()))
                .collect(Collectors.toList());

        List<WashOverrideByGroup> userWashes = new ArrayList<>();
        Integer lastGroupMasterId = null;
        BigDecimal lastOverrideValue = null;
        LocalDate lastOccupancyDate = null;
        for (WashOverrideByGroup existingWashesWithAccomTypeEntry : existingWashesWithAccomTypeEntries) {
            BigDecimal overrideValue = existingWashesWithAccomTypeEntry.getOverrideValue();
            LocalDate occupancyDate = DateUtil.convertJodaToJavaLocalDate(existingWashesWithAccomTypeEntry.getOccupancyDate());
            Integer groupMasterId = existingWashesWithAccomTypeEntry.getGroupMaster().getId();
            if (!ObjectUtils.equals(groupMasterId, lastGroupMasterId) || !ObjectUtils.equals(occupancyDate, lastOccupancyDate) || !ObjectUtils.equals(overrideValue, lastOverrideValue)) {
                userWashes.add(existingWashesWithAccomTypeEntry);
                lastOverrideValue = overrideValue;
                lastOccupancyDate = occupancyDate;
                lastGroupMasterId = groupMasterId;
            }
        }
        return userWashes;
    }

    private IndividualGroupMasterDto getGroupMaster(Integer groupMasterId) {
        synchronized (individualGroupMasterDtos) {
            for (IndividualGroupMasterDto individualGroupMasterDto : individualGroupMasterDtos) {
                if (individualGroupMasterDto.getId().equals(groupMasterId)) {
                    return individualGroupMasterDto;
                }
            }
        }
        return new IndividualGroupMasterDto();
    }

    private String getNotesAsText(LocalDate occupancyDate, List<DateNote> notes) {
        List<DateNote> occupancyDateNotes = new ArrayList<>();
        for (DateNote note : notes) {
            if (note.getArrivalDate().equals(JavaLocalDateUtils.toDate(occupancyDate))) {
                occupancyDateNotes.add(note);
            }
        }
        if (occupancyDateNotes.isEmpty()) {
            return null;
        } else {
            StringBuilder stringBuilder = new StringBuilder();
            for (Iterator<DateNote> iterator = occupancyDateNotes.iterator(); iterator.hasNext(); ) {
                DateNote occupancyDateNote = iterator.next();
                stringBuilder.append("--").append(FastDateFormat.getInstance(getDateFormat(), new Locale(getLanguage())).format(occupancyDateNote.getArrivalDate())).append(" (").append(occupancyDateNote.getUserName()).append("):\n");
                stringBuilder.append(occupancyDateNote.getText());
                stringBuilder.append("\n");
                if (iterator.hasNext()) {
                    //Also add a separator between entries
                    stringBuilder.append("\n");
                }
            }
            return stringBuilder.toString();
        }
    }

    private String getOverrideStatusAsText(WashOverrideByGroup userWash) {
        if (userWash == null || userWash.getStatusId() == null) {
            return null;
        }
        if (Constants.ACTIVE_STATUS_ID.equals(userWash.getStatusId())) {
            return "Active";
        } else {
            return "Inactive";
        }
    }

    private String getPickupTypeAsText(IndividualGroupMasterDto individualGroupMasterDto) {
        PickupTypeCode pickupTypeCode = PickupTypeCode.fromString(individualGroupMasterDto.getGroupMaster().getPickupTypeCode());
        if (pickupTypeCode == null) {
            return null;
        }
        return getText(pickupTypeCode.getMessageKey());
    }

    private GroupBlockExtendedDTO getGroupWashExtendedData(LocalDate occupancyDate, Integer groupId, Integer mktSegID, Map<String, GroupBlockExtendedDTO> groupArrivalsData) {
        return groupArrivalsData.get("" + groupId + occupancyDate + mktSegID);
    }

    private List<WashOverrideByGroup> getWashOverrides(IndividualGroupBlockTableDto individualGroupBlockTableDto, List<WashOverrideByGroup> userWashes) {
        ArrayList<WashOverrideByGroup> washOverrideByGroups = new ArrayList<>();
        for (WashOverrideByGroup userWash : userWashes) {
            if (individualGroupBlockTableDto.getOccupancyDate().equals(JavaLocalDateUtils.toJavaLocalDate(userWash.getOccupancyDate())) && individualGroupBlockTableDto.getGroupId().equals(userWash.getGroupMaster().getId())) {
                washOverrideByGroups.add(userWash);
            }
        }
        return washOverrideByGroups;
    }

    private void setHistoryOverrideValues(IndividualGroupWashOverrideHistoryDto individualGroupWashOverrideHistoryDto, boolean overrideExists, WashOverrideByGroup washOverrideByGroup, IndividualGroupBlockTableDto individualGroupBlockTableDto) {
        if (overrideExists) {
            BigDecimal overrideValue = washOverrideByGroup.getOverrideValue();
            individualGroupWashOverrideHistoryDto.setUserWashPercent(overrideValue);
            individualGroupWashOverrideHistoryDto.setUserWashValue(getCalculatedWashValue(overrideValue, individualGroupBlockTableDto.getPickup(), individualGroupBlockTableDto.getBlock()));
        }
    }

    private String getText(String resourceKey) {
        return ResourceUtil.getText(resourceKey, new Locale(getLanguage()));
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getDateFormat() {
        return dateFormat;
    }

    public void setDateFormat(String dateFormat) {
        this.dateFormat = dateFormat;
    }

    private List<IndividualGroupMasterDto> getGroupMasters(List<GroupBlockMaster> groupMasters) {
        individualGroupMasterDtos = sortIndividualGroupMasterDtos(toGroupMasterDtos(groupMasters));
        return individualGroupMasterDtos;
    }

    protected List<IndividualGroupMasterDto> sortIndividualGroupMasterDtos(List<IndividualGroupMasterDto> individualGroupMasterDtos) {
        individualGroupMasterDtos.sort((individualGroupMasterDto, t1) -> {
            if (individualGroupMasterDto.getGroupMaster().getStartDate().equals(t1.getGroupMaster().getStartDate())) {
                return individualGroupMasterDto.getName().toLowerCase().compareTo(t1.getName().toLowerCase());
            } else {
                return individualGroupMasterDto.getGroupMaster().getStartDate().compareTo(t1.getGroupMaster().getStartDate());
            }
        });

        return individualGroupMasterDtos;
    }

    private List<IndividualGroupMasterDto> toGroupMasterDtos(List<GroupBlockMaster> groupMasters) {
        ArrayList<IndividualGroupMasterDto> individualGroupMasterDtosLocal = new ArrayList<>();
        for (GroupBlockMaster groupMaster : groupMasters) {
            IndividualGroupMasterDto individualGroupMasterDto = new IndividualGroupMasterDto();
            individualGroupMasterDto.setGroupMaster(groupMaster);
            individualGroupMasterDto.setId(groupMaster.getId());
            individualGroupMasterDto.setName(groupMaster.getName());
            individualGroupMasterDtosLocal.add(individualGroupMasterDto);
        }

        return individualGroupMasterDtosLocal;
    }

    protected List<GroupBlockMaster> getGroupMasters(LocalDate startDate, LocalDate endDate) {
        List<GroupBlockMaster> groupMasterData;
        LocalDateInterval localDateInterval = new LocalDateInterval(JavaLocalDateUtils.toJodaLocalDate(startDate), JavaLocalDateUtils.toJodaLocalDate(endDate));
        groupMasterData = individualGroupForWashOverrideService.getGroupMasterData(localDateInterval, true);
        return groupMasterData;
    }

    ArrayList<IndividualGroupBlockTableDto> toGroupBlockBeanViewWrapper(List<GroupBlockDetail> groupBlocks) {
        ArrayList<IndividualGroupBlockTableDto> individualGroupBlockTableDtos = new ArrayList<>(groupBlocks.size());

        LocalDate currentOccupancyDate = null;
        IndividualGroupBlockTableDto previousIndividualGroupBlockTableDto = null;
        IndividualGroupBlockTableDto dto = null;
        int numBlocks = 0;

        Map<org.joda.time.LocalDate, Integer> blockPerOccupancyDate = groupBlocks.stream()
                .collect(Collectors.groupingBy(GroupBlockDetail::getOccupancyDate, Collectors.summingInt(GroupBlockDetail::getBlocks)));
        Map<org.joda.time.LocalDate, Integer> pickupPerOccupancyDate = groupBlocks.stream()
                .collect(Collectors.groupingBy(GroupBlockDetail::getOccupancyDate, Collectors.summingInt(GroupBlockDetail::getPickup)));

        for (GroupBlockDetail groupBlock : groupBlocks) {
            if (dto == null || !groupBlock.getOccupancyDate().equals(JavaLocalDateUtils.toJodaLocalDate(currentOccupancyDate))) {
                if (dto != null) {
                    individualGroupBlockTableDtos.add(dto);
                }
                currentOccupancyDate = JavaLocalDateUtils.toJavaLocalDate(groupBlock.getOccupancyDate());
                dto = new IndividualGroupBlockTableDto();
                numBlocks = 0;
            }
            numBlocks++;
            dto.setId(groupBlock.getId());

            SystemWashByGroup systemWashByGroup = groupBlock.getSystemWashByGroupOrNull();
            Integer groupId = groupBlock.getGroupBlockMaster().getId();
            dto.setGroupId(groupId);
            dto.setGroupMasterId(groupBlock.getGroupBlockMaster().getId());
            dto.setWashOverrideAllowed(groupBlock.isWashOverrideAllowed());

            previousIndividualGroupBlockTableDto = dto;

            dto.setOccupancyDate(JavaLocalDateUtils.toJavaLocalDate(groupBlock.getOccupancyDate()));
            dto.setGroupName(groupBlock.getGroupBlockMaster().getName());

            dto.setCutoffDate(null != groupBlock.getGroupBlockMaster().getCutoffDate() ? Instant.ofEpochMilli(groupBlock.getGroupBlockMaster().getCutoffDate().getTime())
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate() : null);
            //dto.setCutoffDate(JavaLocalDateUtils.fromDate(groupBlock.getGroupBlockMaster().getCutoffDate()));
            dto.setBlock(groupBlock.getBlocks() + (dto.getBlock() == null ? 0 : dto.getBlock()));
            dto.setPickup(groupBlock.getPickup() + (dto.getPickup() == null ? 0 : dto.getPickup()));
            dto.setAvailableBlock(Math.max(0, dto.getBlock() - dto.getPickup()));
            dto.setHasExistingForecastGroupWashOverride(groupBlock.getHasExistingForecastGroupWashOverride());

            int totalBlocksForOccupancyDate = blockPerOccupancyDate.get(JavaLocalDateUtils.toJodaLocalDate(currentOccupancyDate));
            int totalPickupsForOccupancyDate = pickupPerOccupancyDate.get(JavaLocalDateUtils.toJodaLocalDate(currentOccupancyDate));
            int actualBlocksForOccupancyDate = Math.max(totalBlocksForOccupancyDate, totalPickupsForOccupancyDate);

            mapSystemWashPercent(dto, systemWashByGroup, groupBlock.getBlocks(), groupBlock.getPickup(), actualBlocksForOccupancyDate);
            addSystemWashByGroup(dto, numBlocks, groupBlock, systemWashByGroup);

            //dto.setReadOnly(!isWritable(dto));
        }

        checkPreviousIndividualGroupBlockTableDto(individualGroupBlockTableDtos, previousIndividualGroupBlockTableDto, dto);

        return individualGroupBlockTableDtos;
    }

    private void addSystemWashByGroup(IndividualGroupBlockTableDto dto, int numBlocks, GroupBlockDetail groupBlock, SystemWashByGroup systemWashByGroup) {
        if (systemWashByGroup != null) {
            WashOverrideByGroup activeWashOverrideByGroup = systemWashByGroup.getActiveWashOverrideByGroup();
            mapUserWashPercent(dto, numBlocks, activeWashOverrideByGroup);
            if (activeWashOverrideByGroup != null) {
                dto.setExpirationDate(JavaLocalDateUtils.toJavaLocalDate(activeWashOverrideByGroup.getExpirationDate()));
                dto.setDaysToArrival(getCalculatedDaysToArrival(
                        JavaLocalDateUtils.toJavaLocalDate(activeWashOverrideByGroup.getExpirationDate()),
                        JavaLocalDateUtils.toJavaLocalDate(groupBlock.getOccupancyDate())));
            }
        }
    }


    private void checkPreviousIndividualGroupBlockTableDto(ArrayList<IndividualGroupBlockTableDto> individualGroupBlockTableDtos, IndividualGroupBlockTableDto previousIndividualGroupBlockTableDto, IndividualGroupBlockTableDto dto) {
        if (previousIndividualGroupBlockTableDto != null) {
            individualGroupBlockTableDtos.add(dto);
        }
    }

    private BigDecimal getCalculatedWashValue(BigDecimal washPercent, Integer pickup, Integer block) {
        int pickupBlockMax = getPickupBlockMax(pickup, block);
        return round(BigDecimal.valueOf(pickupBlockMax * washPercent.doubleValue() / 100d));
    }

    private int getPickupBlockMax(Integer pickup, Integer block) {
        return Math.max(pickup, block);
    }

    private BigDecimal round(BigDecimal bigDecimal) {
        return bigDecimal.setScale(2, RoundingMode.HALF_UP);
    }

    private void mapSystemWashPercent(IndividualGroupBlockTableDto dto, SystemWashByGroup systemWashByGroup, Integer blocks, Integer pickup, Integer totalBlocks) {
        BigDecimal systemWashPercent;
        if (systemWashByGroup != null) {
            systemWashPercent = BigDecimal.valueOf(systemWashByGroup.getSystemWash().doubleValue() * 100); //DB holds a fraction
        } else {
            systemWashPercent = null;
        }
        BigDecimal currentSystemWashPercent = dto.getSystemWashPercent();
        if (currentSystemWashPercent != null || systemWashPercent != null) {
            double currentPercentDouble = currentSystemWashPercent == null ? 0d : currentSystemWashPercent.doubleValue();
            double systemWashPercentDouble = systemWashPercent == null ? 0d : systemWashPercent.doubleValue();
            int blocksRemaining = blocks > pickup ? blocks : pickup;
            dto.setSystemWashPercent(round((BigDecimal.valueOf(blocksRemaining * systemWashPercentDouble / totalBlocks)).add(BigDecimal.valueOf(currentPercentDouble))));

            dto.setSystemWashValue(getCalculatedSystemWashValue(dto));
        }
    }

    private BigDecimal getCalculatedSystemWashValue(IndividualGroupBlockTableDto individualGroupBlockTableDto) {
        if (individualGroupBlockTableDto.getSystemWashPercent() == null || individualGroupBlockTableDto.getPickup() == null || individualGroupBlockTableDto.getBlock() == null) {
            return null;
        }
        return getCalculatedWashValue(individualGroupBlockTableDto.getSystemWashPercent(), individualGroupBlockTableDto.getPickup(), individualGroupBlockTableDto.getBlock());
    }

    private int getCalculatedDaysToArrival(java.time.LocalDate expirationDate, java.time.LocalDate occupancyDate) {
        return (int) Duration.between(expirationDate.atStartOfDay(), occupancyDate.atStartOfDay()).toDays();
    }

    private void mapUserWashPercent(IndividualGroupBlockTableDto dto, int numBlocks, WashOverrideByGroup wash) {
        BigDecimal washPercent = wash != null ? wash.getOverrideValue() : null;
        BigDecimal currentWashPercent = dto.getUserWashPercent();
        if (currentWashPercent != null || washPercent != null) {
            if (null != wash) {
                double currentPercentDouble = currentWashPercent == null ? 0d : currentWashPercent.doubleValue();
                double washPercentDouble = washPercent == null ? 0d : washPercent.doubleValue();
                dto.setUserWashPercent(round(BigDecimal.valueOf((washPercentDouble + ((numBlocks - 1) * currentPercentDouble)) / numBlocks)));
            }

            dto.setUserWashValue(getCalculatedUserWashValue(dto));
        }
    }

    private BigDecimal getCalculatedUserWashValue(IndividualGroupBlockTableDto individualGroupBlockTableDto) {
        if (individualGroupBlockTableDto.getUserWashPercent() == null || individualGroupBlockTableDto.getPickup() == null || individualGroupBlockTableDto.getBlock() == null) {
            return null;
        }
        return getCalculatedWashValue(individualGroupBlockTableDto.getUserWashPercent(), individualGroupBlockTableDto.getPickup(), individualGroupBlockTableDto.getBlock());
    }

}
