package com.ideas.tetris.pacman.services.forecast.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDate;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoomTypeLevelOccupancyForecastDTO {
    private Integer accomTypeID;
    private LocalDate occupancyDate;
    private BigDecimal occFcst;
    private BigDecimal revFcst;

    public RoomTypeLevelOccupancyForecastDTO(Integer accomTypeID, Date date, BigDecimal occFcst, BigDecimal revFcst) {

        this.accomTypeID = accomTypeID;
        this.occupancyDate = date.toLocalDate();
        this.occFcst = occFcst;
        this.revFcst = revFcst;
    }
}
