package com.ideas.tetris.pacman.services.benefits.service;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.platform.services.daoandentities.entity.Benefits;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Map;

import static java.math.BigDecimal.ZERO;
import static java.math.BigDecimal.valueOf;
import static java.math.RoundingMode.HALF_UP;
import static java.util.Objects.isNull;

public class BenefitMeasurementXMLParser {

    private static final int TABLE_INDEX = 3;
    private static final int DATA_INDEX = 0;
    private static final int FG_INDEX = 0;
    private static final int ACTUAL_OCC_INDEX = 2;
    private static final int ACTUAL_REV_INDEX = 3;
    private static final int TOTAL_CAPACITY_INDEX = 4;
    private static final int HEURISTIC_OCC_INDEX = 6;
    private static final int HEURISTIC_REV_INDEX = 7;
    private static final int ANCILLARY_REV_INDEX = 13;
    private static final int ANCILLARY_PROFIT_INDEX = 14;
    private static final int ANCILLARY_REV_GAIN_INDEX = 15;
    private static final int ANCILLARY_PROFIT_GAIN_INDEX = 16;

    private static final int BENEFIT_WORKSHEET_ACTUAL_PROFIT_INDEX = 14;
    private static final int BENEFIT_WORKSHEET_HEURISTIC_PROFIT_INDEX = 15;
    private static final int BENEFIT_WORKSHEET_ACTUAL_PROPOR_INDEX = 20;
    private static final int BENEFIT_WORKSHEET_HEURISTIC_PROPOR_INDEX = 21;
    private static final int BENEFIT_WORKSHEET_ACTUAL_PROPAR_INDEX = 22;
    private static final int BENEFIT_WORKSHEET_HEURISTIC_PROPAR_INDEX = 23;
    private static final int BENEFIT_WORKSHEET_PROFIT_IN_PERCENT_INDEX = 26;
    private static final int BENEFIT_WORKSHEET_PROPOR_IN_PERCENT_INDEX = 27;

    private static final Logger LOGGER = Logger.getLogger(BenefitMeasurementXMLParser.class);
    private static final String BENEFIT = "Benefit";
    private static final String PERFORMANCE_RESULTS_BY_FG = "Performance Results by FG";
    private static final String EMPTY_BENEFIT_WORKSHEET_DATA_RETURN_ZERO_FILL_DATA = "Benefit data not found, returning zero fill data";
    protected static final String MSG_RETURNING_ZERO_FILL_DATA_INSTEAD_OF_FAILING_THE_JOB = ". Returning zero fill data, instead of failing the job";
    protected static final String MSG_INVALID_DATA_FOUND_IN_XML_FILE = "Invalid data found in xmlFile:- ";
    protected static final String MSG_UNABLE_TO_READ_PARSE_XML_FILE = "unable to read/parse xmlFile:- ";
    private static final String EMPTY_WORKSHEET_RETURN_ZERO_FILL_DATA = "Worksheet is empty, returning zero fill data";

    private boolean isProfitInBenefitEnabled;

    public void setProfitInBenefitEnabled(boolean profitInBenefitEnabled) {
        isProfitInBenefitEnabled = profitInBenefitEnabled;
    }

    public Benefits parseXML(File xmlFile, int month, int year, Integer propertyId, Map<Integer, Boolean> forecastGroupBusinessTypeMap, int capacity) {
        DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
        DocumentBuilder dBuilder;
        Benefits benefits = new Benefits();
        benefits.setPropertyId(propertyId);
        benefits.setMonth(month);
        benefits.setYear(year);
        try {
            dBuilder = dbFactory.newDocumentBuilder();
            Document doc = dBuilder.parse(xmlFile);
            doc.getDocumentElement().normalize();
            Node worksheet = getWorksheetByName(doc, PERFORMANCE_RESULTS_BY_FG);
            if (isNull(worksheet)) {
                LOGGER.warn(EMPTY_WORKSHEET_RETURN_ZERO_FILL_DATA);
                return benefits;
            }
            Node tableItem = worksheet.getChildNodes().item(TABLE_INDEX);
            Element tableItem1 = (Element) tableItem;

            BigDecimal totalCapacity = ZERO;
            BigDecimal ancillaryRevenue = ZERO;
            BigDecimal ancillaryProfit = ZERO;
            BigDecimal ancillaryRevenueGain = ZERO;
            BigDecimal ancillaryProfitGain = ZERO;
            NodeList rows = tableItem1.getElementsByTagName("Row");
            int rowsLength = rows.getLength();
            for (int i = 1; i < rowsLength; i++) {
                Node node = rows.item(i);
                Element element = (Element) node;
                if (forecastGroupBusinessTypeMap.get(getCellValue(element, FG_INDEX).intValue())) {
                    benefits.setGroupActualOccupancy(addBigDecimal(valueOf(benefits.getGroupActualOccupancy()), element, ACTUAL_OCC_INDEX).intValue());
                    benefits.setGroupActualRevenue(addBigDecimal(benefits.getGroupActualRevenue(), element, ACTUAL_REV_INDEX));
                    benefits.setGroupHeuristicOccupancy(addBigDecimal(valueOf(benefits.getGroupHeuristicOccupancy()), element, HEURISTIC_OCC_INDEX).intValue());
                    benefits.setGroupHeuristicRevenue(addBigDecimal(benefits.getGroupHeuristicRevenue(), element, HEURISTIC_REV_INDEX));
                } else {
                    benefits.setTransientActualOccupancy(addBigDecimal(valueOf(benefits.getTransientActualOccupancy()), element, ACTUAL_OCC_INDEX).intValue());
                    benefits.setTransientActualRevenue(addBigDecimal(benefits.getTransientActualRevenue(), element, ACTUAL_REV_INDEX));
                    benefits.setTransientHeuristicOccupancy(addBigDecimal(valueOf(benefits.getTransientHeuristicOccupancy()), element, HEURISTIC_OCC_INDEX).intValue());
                    benefits.setTransientHeuristicRevenue(addBigDecimal(benefits.getTransientHeuristicRevenue(), element, HEURISTIC_REV_INDEX));
                }
                totalCapacity = addBigDecimal(totalCapacity, element, TOTAL_CAPACITY_INDEX);
                ancillaryRevenue = addBigDecimal(ancillaryRevenue, element, ANCILLARY_REV_INDEX);
                ancillaryProfit = addBigDecimal(ancillaryProfit, element, ANCILLARY_PROFIT_INDEX);
                ancillaryRevenueGain = addBigDecimal(ancillaryRevenueGain, element, ANCILLARY_REV_GAIN_INDEX);
                ancillaryProfitGain = addBigDecimal(ancillaryProfitGain, element, ANCILLARY_PROFIT_GAIN_INDEX);
            }
            benefits.setCapacity(capacity);
            benefits.setActualOccupancy(benefits.getTransientActualOccupancy() + benefits.getGroupActualOccupancy());
            benefits.setActualRevenue(benefits.getTransientActualRevenue().add(benefits.getGroupActualRevenue()));
            benefits.setActualAdr(getDivisionResult(benefits.getActualRevenue(), benefits.getActualOccupancy()));
            benefits.setActualRevpar(getDivisionResult(benefits.getActualRevenue(), capacity));
            benefits.setHeuristicOccupancy(benefits.getTransientHeuristicOccupancy() + benefits.getGroupHeuristicOccupancy());
            benefits.setHeuristicRevenue(benefits.getTransientHeuristicRevenue().add(benefits.getGroupHeuristicRevenue()));
            benefits.setHeuristicAdr(getDivisionResult(benefits.getHeuristicRevenue(), benefits.getHeuristicOccupancy()));
            benefits.setHeuristicRevpar(getDivisionResult(benefits.getHeuristicRevenue(), capacity));

            // removed big decimal to int conversion as it gives negative value if exceeds Int range
            // e.g. BigDecimal.valueOf(2543939174.24).intValue() = -1751028122
            benefits.setBenefitRevenue(getDivisionResult(benefits.getActualRevenue().subtract(benefits.getHeuristicRevenue()).multiply(valueOf(100)), benefits.getHeuristicRevenue()));
            benefits.setBenefitOccupancy(getDivisionResult(valueOf(benefits.getActualOccupancy()).subtract(valueOf(benefits.getHeuristicOccupancy())).multiply(valueOf(100)), benefits.getHeuristicOccupancy(), 2));
            benefits.setBenefitADR(calculatePercentage(benefits.getActualAdr(), benefits.getHeuristicAdr()));
            benefits.setBenefitRevpar(calculatePercentage(benefits.getActualRevpar(), benefits.getHeuristicRevpar()));

            benefits.setTransientActualAdr(getDivisionResult(benefits.getTransientActualRevenue(), benefits.getTransientActualOccupancy()));
            benefits.setTransientHeuristicAdr(getDivisionResult(benefits.getTransientHeuristicRevenue(), benefits.getTransientHeuristicOccupancy()));

            benefits.setTransientBenefitRevenue(getDivisionResult(benefits.getTransientActualRevenue().subtract(benefits.getTransientHeuristicRevenue()).multiply(valueOf(100)), benefits.getTransientHeuristicRevenue()));
            benefits.setTransientBenefitOccupancy(calculatePercentage(valueOf(benefits.getTransientActualOccupancy()), valueOf(benefits.getTransientHeuristicOccupancy())));

            benefits.setGroupActualAdr(getDivisionResult(benefits.getGroupActualRevenue(), benefits.getGroupActualOccupancy()));
            benefits.setGroupHeuristicAdr(getDivisionResult(benefits.getGroupHeuristicRevenue(), benefits.getGroupHeuristicOccupancy()));

            benefits.setGroupBenefitRevenue(getDivisionResult(benefits.getGroupActualRevenue().subtract(benefits.getGroupHeuristicRevenue()).multiply(valueOf(100)), benefits.getGroupHeuristicRevenue()));
            benefits.setGroupBenefitOccupancy(calculatePercentage(valueOf(benefits.getGroupActualOccupancy()), valueOf(benefits.getGroupHeuristicOccupancy())));

            benefits.setAncillaryRevenue(ancillaryRevenue);
            benefits.setAncillaryProfit(ancillaryProfit);
            benefits.setAncillaryRevenueGain(ancillaryRevenueGain);
            benefits.setAncillaryProfitGain(ancillaryProfitGain);

            BigDecimal ancillaryRevenueWithoutRms = ancillaryRevenue.subtract(ancillaryRevenueGain);
            benefits.setAncillaryRevenueWithoutRms(ancillaryRevenueWithoutRms);
            benefits.setAncillaryRevenueGainInPercent(getDivisionResult(ancillaryRevenueGain.multiply(BigDecimal.valueOf(100)), ancillaryRevenueWithoutRms));

            BigDecimal ancillaryProfitWithoutRms = ancillaryProfit.subtract(ancillaryProfitGain);
            benefits.setAncillaryProfitWithoutRms(ancillaryProfitWithoutRms);
            benefits.setAncillaryProfitGainInPercentage(getDivisionResult(ancillaryProfitGain.multiply(BigDecimal.valueOf(100)), ancillaryProfitWithoutRms));

            if (isProfitInBenefitEnabled) {
                addBenefitWorksheetProfitData(doc, benefits);
            }
        } catch (Exception e) {
            LOGGER.warn(getLogMessage(xmlFile, e), e);
        }
        return benefits;
    }

    @VisibleForTesting
    String getLogMessage(File xmlFile, Exception e) {
        String message = (e instanceof ParserConfigurationException) || (e instanceof IOException)
                ? MSG_UNABLE_TO_READ_PARSE_XML_FILE
                : MSG_INVALID_DATA_FOUND_IN_XML_FILE;
        return message + xmlFile.getAbsolutePath() + MSG_RETURNING_ZERO_FILL_DATA_INSTEAD_OF_FAILING_THE_JOB;
    }

    private Node getWorksheetByName(Document doc, String worksheetName) {
        NodeList nList = doc.getElementsByTagName("Worksheet");
        int length = nList.getLength();
        for (int i = 0; i < length; i++) {
            Node worksheet = nList.item(i);
            Node node = worksheet.getAttributes().getNamedItem("ss:Name");
            if (worksheetName.equals(node.getNodeValue())) {
                return worksheet;
            }
        }
        return null;
    }

    private void addBenefitWorksheetProfitData(Document doc, Benefits benefits) {
        Node benefitWorksheet = getWorksheetByName(doc, BENEFIT);
        if (isNull(benefitWorksheet)) {
            LOGGER.warn(BENEFIT + " " + EMPTY_WORKSHEET_RETURN_ZERO_FILL_DATA);
            return;
        }
        Node benefitTableItem = benefitWorksheet.getChildNodes().item(TABLE_INDEX);
        Element benefitTableElement = (Element) benefitTableItem;

        NodeList benefitRows = benefitTableElement.getElementsByTagName("Row");
        if (benefitRows.getLength() <= 1) {
            LOGGER.warn(EMPTY_BENEFIT_WORKSHEET_DATA_RETURN_ZERO_FILL_DATA);
            return;
        }
        Node node = benefitRows.item(1);
        Element element = (Element) node;

        benefits.setActualProfit(getCellValue(element, BENEFIT_WORKSHEET_ACTUAL_PROFIT_INDEX));
        benefits.setHeuristicProfit(getCellValue(element, BENEFIT_WORKSHEET_HEURISTIC_PROFIT_INDEX));
        benefits.setActualProPOR(getCellValue(element, BENEFIT_WORKSHEET_ACTUAL_PROPOR_INDEX));
        benefits.setHeuristicProPOR(getCellValue(element, BENEFIT_WORKSHEET_HEURISTIC_PROPOR_INDEX));
        benefits.setActualProPAR(getCellValue(element, BENEFIT_WORKSHEET_ACTUAL_PROPAR_INDEX));
        benefits.setHeuristicProPAR(getCellValue(element, BENEFIT_WORKSHEET_HEURISTIC_PROPAR_INDEX));

        benefits.setBenefitProfitInPercent(BigDecimal.valueOf(100).multiply(getCellValue(element, BENEFIT_WORKSHEET_PROFIT_IN_PERCENT_INDEX)));
        benefits.setBenefitProPORInPercent(BigDecimal.valueOf(100).multiply(getCellValue(element, BENEFIT_WORKSHEET_PROPOR_IN_PERCENT_INDEX)));
        // calculated benefitProPARInPercent value as it was missing in xml
        benefits.setBenefitProPARInPercent(calculatePercentage(benefits.getActualProPAR(), benefits.getHeuristicProPAR()));
    }

    private BigDecimal calculatePercentage(BigDecimal actual, BigDecimal heuristic) {
        return getDivisionResult((actual.subtract(heuristic)).multiply(BigDecimal.valueOf(100)), heuristic);
    }

    private BigDecimal getDivisionResult(BigDecimal dividend, Integer divisor) {
        return isNull(divisor) || divisor == 0 ? ZERO : dividend.divide(valueOf(divisor), HALF_UP);
    }

    private BigDecimal getDivisionResult(BigDecimal dividend, BigDecimal divisor) {
        return isNull(divisor) || divisor.compareTo(ZERO) == 0 ? ZERO : dividend.divide(divisor, HALF_UP);
    }

    private BigDecimal getDivisionResult(BigDecimal dividend, Integer divisor, int scale) {
        return isNull(divisor) || divisor == 0 ? ZERO : dividend.divide(valueOf(divisor), scale, HALF_UP);
    }

    private BigDecimal addBigDecimal(BigDecimal value, Element element, int index) {
        return value.add(getCellValue(element, index));
    }

    private BigDecimal getCellValue(Element element, int index) {
        Node cell = element.getElementsByTagName("Cell").item(index).getChildNodes().item(DATA_INDEX);
        Element cell1 = (Element) cell;
        String attribute = cell1.getAttribute("ss:Type");
        return new BigDecimal(StringUtils.equalsIgnoreCase("Number", attribute) ? cell.getChildNodes().item(0).getTextContent() : "0");
    }
}
