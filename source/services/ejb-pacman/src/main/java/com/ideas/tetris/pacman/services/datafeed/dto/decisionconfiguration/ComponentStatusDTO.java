package com.ideas.tetris.pacman.services.datafeed.dto.decisionconfiguration;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ComponentStatusDTO {
    private boolean isSrpFPLOSVisible;
    private boolean isDecisionUploadWindowBDEEnabled;
    private boolean isIDPScheduleVisible;
    private boolean isZeroCapacityRTFieldVisible;
    private boolean isZeroCapacityRTRestrictionVisible;
    private boolean isOperaDailyBARRateVisible;
    private boolean isCurtiscBARRateVisible;
    private boolean isCurtisPropertyCodeVisible;
    private boolean isAddTaxOptionVisible;
    private boolean isTaxVisible;
    private boolean isTaxEnabled;
    private boolean isPropertyTypeVisible;
    private boolean isContinuousPricingEnabled;
    private boolean isRequestFullUploadVisible;
    private boolean isUploadBySellingSystemEnabled;
    private boolean isUploadAtBDEIsVisible;
    private boolean isUploadAtBDEIsEnabled;
    private boolean isUploadNowIsVisible;
    private boolean isUploadNowIsEnabled;
    private boolean isScheduleDecisionDeliveryByDecisionTypeEnabled;
    private String immediateUploadDisabledReason;
    private boolean isScheduledTwoWayDateEnabled;
}
