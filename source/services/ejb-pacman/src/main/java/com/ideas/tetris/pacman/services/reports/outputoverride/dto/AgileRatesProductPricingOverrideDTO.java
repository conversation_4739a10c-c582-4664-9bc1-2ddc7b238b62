package com.ideas.tetris.pacman.services.reports.outputoverride.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ideas.tetris.platform.common.rest.unmarshaller.DateTimeSerializer;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

public class AgileRatesProductPricingOverrideDTO {

    private Date arrivalDate;
    private String accomClassCode;
    private String accomTypeCode;
    private String productName;
    private String offsetType;
    private String offsetValue;
    private Date createDate;
    private String userEmail;
    private String notes;
    private String userName;

    public Date getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    @JsonSerialize(using = DateTimeSerializer.class)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }


    public String getNotes() {
        return notes == null ? StringUtils.EMPTY : notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getAccomClassCode() {
        return accomClassCode;
    }

    public void setAccomClassCode(String accomClassCode) {
        this.accomClassCode = accomClassCode;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getAccomTypeCode() {
        return accomTypeCode;
    }

    public void setAccomTypeCode(String accomTypeCode) {
        this.accomTypeCode = accomTypeCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getOffsetType() {
        return offsetType;
    }

    public void setOffsetType(String offsetType) {
        this.offsetType = offsetType;
    }

    public String getOffsetValue() {
        return offsetValue;
    }

    public void setOffsetValue(String offsetValue) {
        this.offsetValue = offsetValue;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
