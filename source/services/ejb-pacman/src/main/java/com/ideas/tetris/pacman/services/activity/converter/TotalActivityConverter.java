package com.ideas.tetris.pacman.services.activity.converter;

import com.ideas.tetris.pacman.common.utils.pojo.Pair;
import com.ideas.tetris.pacman.services.activity.converter.TotalActivityConverter.Qualifier;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceTotalActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Qualifier
@Component
@Transactional
public class TotalActivityConverter extends PaceActivityConverter<TotalActivity, PaceTotalActivity> {

    /**
     * Looks for an existing TotalActivity record based on the ID if it's present, if it isn't, then
     * it will attempt to use the Occupancy Date / Property ID combination, and if that doesn't find an
     * existing record, it will simply return a new TotalActivity object.
     */
    @Override
    public TotalActivity findExistingOrCreateNewActivity(Integer propertyId, Map<String, Object> dto, String correlationId, boolean isPast) {
        TotalActivity totalActivity = getTotalActivity(propertyId, dto);
        FileMetadata existingFileMetadata = findExistingFileMetadata(correlationId);

        totalActivity.setSnapShotDate(existingFileMetadata.getSnapshotDtTm());
        totalActivity.setFileMetadataId(existingFileMetadata.getId());
        return totalActivity;
    }

    private TotalActivity getTotalActivity(Integer propertyId, Map<String, Object> dto) {
        TotalActivity totalActivity = findExistingActivity(propertyId, dto);
        if (totalActivity == null) {
            totalActivity = new TotalActivity();
        }
        return totalActivity;
    }

    private TotalActivity findExistingActivity(Integer propertyId, Map<String, Object> dto) {
        TotalActivity totalActivity;
        Integer id = getInteger(dto, ID);
        if (id != null) {
            totalActivity = tenantCrudService.find(TotalActivity.class, id);
        } else {
            totalActivity = tenantCrudService.findByNamedQuerySingleResult(TotalActivity.BY_OCCUPANCY_DATE_AND_PROPERTY_ID, QueryParameter.with("occupancyDate", getDate(dto, OCCUPANCY_DATE)).and("propertyId", propertyId).parameters());
        }
        return totalActivity;
    }

    @Override
    public List<TotalActivity> findExistingOrCreateNewActivity(Integer propertyId, List<Map<String, Object>> dtos, String correlationId) {
        List<TotalActivity> totalActivities = new ArrayList<>();
        List<Date> dates = getDates(dtos);
        Pair<Date, Date> dateRange = getDateRange(dates);
        Map<Date, TotalActivity> totalActivitiesFromDb = getTotalActivitiesByDate(propertyId, dateRange);
        FileMetadata existingFileMetadata = findExistingFileMetadata(correlationId);
        for (Map<String, Object> dto : dtos) {
            TotalActivity totalActivity = buildEntity(totalActivitiesFromDb, dto, propertyId, existingFileMetadata);
            totalActivities.add(totalActivity);
        }
        return totalActivities;
    }

    private TotalActivity buildEntity(Map<Date, TotalActivity> totalActivitiesFromDb, Map<String, Object> dto, Integer propertyId, FileMetadata existingFileMetadata) {
        Date occupancyDate = getDate(dto, OCCUPANCY_DATE);
        TotalActivity totalActivity = totalActivitiesFromDb.getOrDefault(occupancyDate, new TotalActivity());
        totalActivity.setOccupancyDate(occupancyDate);
        totalActivity.setTotalAccomCapacity(getBigDecimalDefault(dto, TOTAL_ACCOM_CAPACITY, BigDecimal.ZERO));
        totalActivity.setRoomsSold(getBigDecimalDefault(dto, ROOMS_SOLD, BigDecimal.ZERO));
        totalActivity.setRoomsNotAvailableMaintenance(getBigDecimalDefault(dto, ROOMS_NOT_AVAILABLE_MAINTENANCE, BigDecimal.ZERO));
        totalActivity.setRoomsNotAvailableOther(getBigDecimalDefault(dto, ROOMS_NOT_AVAILABLE_OTHER, BigDecimal.ZERO));
        totalActivity.setArrivals(getBigDecimalDefault(dto, ARRIVALS, BigDecimal.ZERO));
        totalActivity.setDepartures(getBigDecimalDefault(dto, DEPARTURES, BigDecimal.ZERO));
        totalActivity.setCancellations(getBigDecimalDefault(dto, CANCELLATIONS, BigDecimal.ZERO));
        totalActivity.setNoShows(getBigDecimalDefault(dto, NO_SHOWS, BigDecimal.ZERO));
        totalActivity.setRoomRevenue(getBigDecimalDefault(dto, ROOM_REVENUE, BigDecimal.ZERO));
        totalActivity.setFoodRevenue(getBigDecimalDefault(dto, FOOD_REVENUE, BigDecimal.ZERO));
        totalActivity.setTotalRevenue(getBigDecimalDefault(dto, TOTAL_REVENUE, BigDecimal.ZERO));
        totalActivity.setPropertyId(propertyId);
        totalActivity.setSnapShotDate(existingFileMetadata.getSnapshotDtTm());
        totalActivity.setFileMetadataId(existingFileMetadata.getId());
        return totalActivity;
    }

    private List<Date> getDates(List<Map<String, Object>> dtos) {
        return dtos.stream().map(dto -> getDate(dto, OCCUPANCY_DATE)).collect(Collectors.toList());
    }

    private Pair<Date, Date> getDateRange(List<Date> dates) {
        Date maxDate = dates.stream().max(Date::compareTo).get();
        Date minDate = dates.stream().min(Date::compareTo).get();
        return new Pair<>(minDate, maxDate);
    }

    private Map<Date, TotalActivity> getTotalActivitiesByDate(Integer propertyId, Pair<Date, Date> dateRange) {
        List<TotalActivity> totalActivities = tenantCrudService.findByNamedQuery(TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId)
                        .and("startDate", dateRange.getFirst())
                        .and("endDate", dateRange.getSecond()).parameters());
        return totalActivities.stream()
                .collect(Collectors.toMap(TotalActivity::getOccupancyDate, x -> x));
    }

    @Override
    public PaceTotalActivity findExistingOrCreateNewPaceActivity(TotalActivity totalActivity) {
        PaceTotalActivity paceTotalActivity;
        Date businessDayEndDate = totalActivity.getBusinessDayEndFromSnapShotDate();

        paceTotalActivity = tenantCrudService.findByNamedQuerySingleResult(PaceTotalActivity.BY_OCCUPANCY_DATE_AND_PROPERTY_ID_AND_BDEDATE, QueryParameter.with("propertyId", totalActivity.getPropertyId()).and("occupancyDate", totalActivity.getOccupancyDate()).and("businessDayEndDate", businessDayEndDate).parameters());

        if (paceTotalActivity == null) {
            paceTotalActivity = new PaceTotalActivity();
        }

        return paceTotalActivity;
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
