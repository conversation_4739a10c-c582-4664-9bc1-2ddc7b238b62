package com.ideas.tetris.pacman.services.forecast.dto;

import java.util.Date;
import java.util.List;
import java.util.Map;

public class ExpectedForecastDetailGraphPastSevenDaysChartDTO {

    private Date occupancyDate;
    private List<Integer> pacePoints;
    private Map<Integer, Integer> transientOnBooks;
    private Map<Integer, Integer> stly;
    private Map<Integer, Integer> st2y;
    private Map<Integer, Integer> averageDow;

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public List<Integer> getPacePoints() {
        return pacePoints;
    }

    public void setPacePoints(List<Integer> pacePoints) {
        this.pacePoints = pacePoints;
    }

    public Map<Integer, Integer> getTransientOnBooks() {
        return transientOnBooks;
    }

    public void setTransientOnBooks(Map<Integer, Integer> transientOnBooks) {
        this.transientOnBooks = transientOnBooks;
    }

    public Map<Integer, Integer> getStly() {
        return stly;
    }

    public void setStly(Map<Integer, Integer> stly) {
        this.stly = stly;
    }

    public Map<Integer, Integer> getSt2y() {
        return st2y;
    }

    public void setSt2y(Map<Integer, Integer> st2y) {
        this.st2y = st2y;
    }

    public Map<Integer, Integer> getAverageDow() {
        return averageDow;
    }

    public void setAverageDow(Map<Integer, Integer> averageDow) {
        this.averageDow = averageDow;
    }
}