package com.ideas.tetris.pacman.services.problem;

import com.ideas.tetris.pacman.services.problem.entity.ProblemClassification;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.ErrorType;

import javax.inject.Inject;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class ProblemClassificationService {
    @JobCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("jobCrudServiceBean")
	private CrudService jobCrudService;

    public ProblemClassificationService(CrudService jobCrudService) {
        this.jobCrudService = jobCrudService;
    }

    @SuppressWarnings("unused")
    public ProblemClassificationService() { /* For injection */}

    public List<ProblemClassification> findAll() {

        Map<ErrorCode, ProblemClassification> problemClassifications = jobCrudService.findAll(ProblemClassification.class)
                .stream()
                .collect(Collectors.toMap(c -> ErrorCode.valueForId(c.getErrorCode()), Function.identity()));

        return Arrays.stream(ErrorCode.values())
                .map(e -> Optional.ofNullable(problemClassifications.get(e)).orElse(ProblemClassification.create(e, ErrorType.getDefault())))
                .collect(Collectors.toList());
    }

    public ErrorType getErrorType(ErrorCode errorCode) {
        ProblemClassification classification = findOne(errorCode);
        return classification == null ? ErrorType.getDefault() : classification.getErrorType();
    }

    public void updateClassification(ErrorCode errorCode, ErrorType errorType) {
        ProblemClassification classification = findOne(errorCode);
        if (classification == null) {
            classification = ProblemClassification.create(errorCode, errorType);
        } else {
            classification.setErrorType(errorType);
        }

        jobCrudService.save(classification);
    }

    private ProblemClassification findOne(ErrorCode errorCode) {
        return jobCrudService.findByNamedQuerySingleResult(
                ProblemClassification.GET_BY_ERROR_CODE,
                QueryParameter.with("errorCode", errorCode.getId()).parameters());
    }
}