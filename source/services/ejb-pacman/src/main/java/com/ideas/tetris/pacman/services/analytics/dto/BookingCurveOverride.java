package com.ideas.tetris.pacman.services.analytics.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BookingCurveOverride {
    private int forecastGroupId;
    private int accomClassId;
    private LocalDate startDate;
    private LocalDate endDate;
    private int los;
    private int readingGroupNumber;
    private double value;
}
