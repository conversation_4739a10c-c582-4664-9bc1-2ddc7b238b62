package com.ideas.tetris.pacman.services.datafeed.dto.pricingdata;

public class ProductGroupProductDefinition {
    private String productName;
    private String usedInSmallGroupEvaluation;
    private Integer minRooms;
    private Integer maxRooms;

    public ProductGroupProductDefinition() {
    }

    public ProductGroupProductDefinition(String productName, boolean usedInSmallGroupEvaluation, Integer minRooms, Integer maxRooms) {
        this.productName = productName;
        this.usedInSmallGroupEvaluation = usedInSmallGroupEvaluation ? "Yes" : "No";
        this.minRooms = minRooms;
        this.maxRooms = maxRooms;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getUsedInSmallGroupEvaluation() {
        return usedInSmallGroupEvaluation;
    }

    public void setUsedInSmallGroupEvaluation(String usedInSmallGroupEvaluation) {
        this.usedInSmallGroupEvaluation = usedInSmallGroupEvaluation;
    }

    public Integer getMinRooms() {
        return minRooms;
    }

    public void setMinRooms(Integer minRooms) {
        this.minRooms = minRooms;
    }

    public Integer getMaxRooms() {
        return maxRooms;
    }

    public void setMaxRooms(Integer maxRooms) {
        this.maxRooms = maxRooms;
    }
}
