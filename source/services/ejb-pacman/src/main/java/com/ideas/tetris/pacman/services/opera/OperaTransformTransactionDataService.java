package com.ideas.tetris.pacman.services.opera;

import com.google.common.base.Function;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Multimaps;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants;
import com.ideas.tetris.pacman.services.opera.dto.SharerDetails;
import com.ideas.tetris.pacman.services.opera.metric.OperaMetrics;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackData;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackDataBuilder;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobCallback;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.common.util.jdbc.JpaJdbcUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import thirdparty.org.apache.commons.dbutils.DbUtils;

import java.sql.Connection;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import thirdparty.org.apache.commons.dbutils.DbUtils;

import java.sql.Date;
import java.sql.*;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.OPTIMIZE_OPERA_SHARERS_SQL;
import static com.ideas.tetris.pacman.common.constants.Constants.OPERA;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.ADJUST_CHECKOUT_DT_FOR_CNX_NS_SHARERS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.ADJUST_RESERVATIONS_FOR_SHARERS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.CLEAR_SHARERS_TRANSACTIONS_FROM_STAGE_TABLE;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.CREATE_NC_INDEX_SHARERS_ADJUSTED_SHARE_TRANSACTIONS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.CREATE_NC_INDEX_SHARERS_SHARE_WITH_FULL_SHARERS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.DELETE_CNX_AND_NS_FROM_SHARERS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.DROP_NC_INDEX_SHARERS_ADJUSTED_SHARE_TRANSACTIONS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.DROP_NC_INDEX_SHARERS_SHARE_WITH_FULL_SHARERS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.GET_ALL_SHARED_TRANSACTIONS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.GET_CANCELLED_AND_NS_TRX_FROM_SHARERS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.GET_CANCELLED_AND_NS_TRX_WITH_REVENUE_FROM_SHARERS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.GET_CURRENT_TRANS_INCOMING_FEED_ID;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.GET_ONLY_SHARED_TRANSACTIONS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.INVOKE_SPLIT_TRANSACTIONS_ON_MS_RT_CHANGES;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.REMOVE_PARTIAL_CNX_AND_NS_SHARERS;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.TRUNCATE_CANX_AND_NOSHOW_SHARERS_TABLE;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.TRUNCATE_ONLY_SHARED_TRANS_TABLE;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.TRUNCATE_SHARERS_LIST_TABLE;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.UPDATE_CNX_NS_SHARED_TRX_SHARERS_LIST;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.UPDATE_CNX_NS_SHARED_TRX_WITH_SHARERS_LIST;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.UPDATE_ONLY_SHARED_TRX_WITH_SHARERS_LIST;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.UPDATE_SHARED_TRX_SHARERS_LIST;
import org.springframework.beans.factory.annotation.Qualifier;
import static com.ideas.tetris.pacman.services.opera.constants.OperaTransactionServiceConstants.*;

@Service
@Justification("Until (if ever) updates performed by this service are made more performant, a 60 minute timeout is required")
public class OperaTransformTransactionDataService {

    public static final String INSERT_SHARERS_TO_TEMP = new StringBuilder().append("INSERT INTO #tempSharers (Confirmation_Number, sharer_list )")
            .append(" VALUES (?,?);").toString();
    public static final String OPERA_SHARERS_ADJUSTED_SHARE_TRANSACTIONS = "opera.Sharers_AdjustedShareTransactions";
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    OperaUtilityService operaUtilityService;
    @Autowired
    JpaJdbcUtil jpaJdbcUtil;

    @Autowired
    OperaTransformTransactionDataUtilityService utils;

    private static final int BATCH_SIZE = 10000;

    private static final Logger LOGGER = Logger.getLogger(OperaTransformTransactionDataService.class.getName());


    public enum TransformTransactionDataMetricType {
        TRANSFORM_STAGE_DATA_TX
    }

    public static final OperaMetrics<TransformTransactionDataMetricType> metricsProcessTransaction = new OperaMetrics<>();

    @Async
    public Future<Integer> transformTransactionStageData(String correlationId, boolean runYieldCategoryByRuleAndCreateMarketSegments
            , JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            numberOfRows = transformTransactionStageData(correlationId, runYieldCategoryByRuleAndCreateMarketSegments);
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    @Async
    public Future<Integer> updateDates(JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            numberOfRows = updateCancellationAndBookingDatesWhenIncorrect();
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    @Async
    public Future<Integer> getShares(JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            if (isResolveSharersWithRtChanges()) {
                numberOfRows = getAllSharedTransactions();
            } else {
                numberOfRows = getOnlySharedTransactions();
            }
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    @Async
    public Future<Integer> createSortedSharers(JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            numberOfRows = createFullSortedSharersListForEveryShare("opera.Sharers_OnlySharedTrans");
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    @Async
    public Future<Integer> updateOnlySharedTransactions(JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            numberOfRows = updateOnlySharedTransactionWithFullSortedSharer();
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    @Async
    public Future<Integer> getCancelsAndNoShows(JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            numberOfRows = getCancelledAndNoShowTransactionsFromSharers();
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    @Async
    public Future<Integer> removePartialCancelsAndNoShows(JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            numberOfRows = removePartialCancelledAndNoShowSharers();
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    @Async
    public Future<Integer> clearSharedTransactions(JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            numberOfRows = clearSharedTransactionsFromStage();
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    @Async
    public Future<Integer> dealWithStayShares(String correlationId, JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            int dataLoadMetaDataIDTrans = getDataLoadMetadataIdForCTRANS(correlationId);
            if (!isResolveSharersWithRtChanges()) {
                clearSharedTransactionsFromStageTransactions();
            }
            numberOfRows = dealWithStaySharesTransactions(dataLoadMetaDataIDTrans);
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    @Async
    public Future<Integer> updateCancelsAndNoShows(String correlationId, JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            int dataLoadMetaDataIDTrans = getDataLoadMetadataIdForCTRANS(correlationId);
            numberOfRows = dealWithCancelledAndNoShowSharesTransaction(dataLoadMetaDataIDTrans);
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    @Async
    public Future<Integer> adjustRevenueForCancels(JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            numberOfRows = adjustRevenueforCancelledAndNoShowReservationStatuses();
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    @Async
    public Future<Integer> splitReservations(String correlationId, JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            numberOfRows = splitReservationsOnMSAndRT(correlationId);
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    @Async
    public Future<Integer> updateForeignKeys(JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            numberOfRows = updateForeignKeys();
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<>(numberOfRows);
    }

    public JobCallback getJobCallback() {
        return new JobCallback();
    }

    public int smoothRoomRevenue() {
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(OperaTransaction.SMOOTH_ROOM_REVENUE_BY_ARRIVAL_DATE));
    }

    public int transformTransactionStageData(String correlationId, boolean runYieldCategoryByRuleAndcreateMarketSegments) {
        LOGGER.info("Started transforming stage data for feed : " + correlationId);
        int numRows = 0;
        try {
            metricsProcessTransaction.start(TransformTransactionDataMetricType.TRANSFORM_STAGE_DATA_TX);
            numRows += transformTransactionStageData(correlationId);
            if (runYieldCategoryByRuleAndcreateMarketSegments) {
                numRows += updateForeignKeys();
            }
            metricsProcessTransaction.stop(TransformTransactionDataMetricType.TRANSFORM_STAGE_DATA_TX);
        } finally {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug(new StringBuilder().append("Finished transforming all stage Transaction data ")
                        .append(numRows).append("  rows:\n").append(metricsProcessTransaction.toString()).toString());
            }
        }
        LOGGER.info("Completed transforming stage data for feed : " + correlationId);
        return numRows;
    }

    private int updateForeignKeys() {
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(OperaTransaction.UPDATE_FOREIGN_KEYS_SQL));
    }

    protected String forceLegacyCardinalityEstimator(String sqlString) {
        return utils.forceLegacyCardinalityEstimator(sqlString);
    }

    public int transformTransactionStageData(String correlationId) {
        //update cancellation dt and booking date if date before SAS minimum date. This creates a missing date value when used to create pace point
        int numRowsUpdated = updateCancellationAndBookingDatesWhenIncorrect();

        // adjust shares
        if (isResolveSharersWithRtChanges()) {
            LOGGER.info("Room type changes will be honored for share resolution as " + IntegrationConfigParamName.RESOLVESHARERSWITH_RTCHANGES.value(OPERA)
                    + " is set to true");
            numRowsUpdated += adjustSharesWithRTChangesInTransaction(correlationId);
        } else {
            LOGGER.info("Room type changes are not honored for share resolution as " + IntegrationConfigParamName.RESOLVESHARERSWITH_RTCHANGES.value(OPERA)
                    + " is set to false");
            numRowsUpdated += adjustSharesInTransaction(correlationId);
            // for splitting reservation on MS and RT
            numRowsUpdated += splitReservationsOnMSAndRT(correlationId);
            LOGGER.debug(new StringBuilder().append("splitReservationsOnMSAndRT : ").append(numRowsUpdated).toString());
        }
        return numRowsUpdated;
    }

    private int updateCancellationAndBookingDatesWhenIncorrect() {
        LocalDate minCalenderDate = getMinimumCalenderDate();
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(OperaTransactionServiceConstants.UPDATE_TRANS_CNX_AND_BKG_DATE), QueryParameter.with("minCalenderDate", minCalenderDate).parameters());
    }

    private LocalDate getMinimumCalenderDate() {
        return crudService.findByNativeQuerySingleResult("select min(calendar_date) from dbo.calendar_dim", null, new RowMapper<LocalDate>() {
            @Override
            public LocalDate mapRow(Object[] row) {
                return new LocalDate(row[0]);
            }
        });
    }

    protected int adjustSharesWithRTChangesInTransaction(String correlationId) {
        int numRowsUpdated = 0;
        long startTime = System.currentTimeMillis();
        numRowsUpdated += getAllSharedTransactions();
        LOGGER.info(" After getAllSharedTransactions : " + (System.currentTimeMillis() - startTime));
        int dataLoadMetaDataIDTrans = getDataLoadMetadataIdForCTRANS(correlationId);
        LOGGER.info(" After getDataLoadMetadataIdForCTRANS : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += clearSharedTransactionsFromStage();
        LOGGER.info(" After clearSharedTransactionsFromStage : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += splitReservationsOnMSAndRT(correlationId);
        numRowsUpdated += getCancelledAndNoShowTransactionsFromSharers();
        LOGGER.info(" After getCancelledAndNoShowTransactionsFromSharers : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += removePartialCancelledAndNoShowSharers();
        LOGGER.info(" After removePartialCancelledAndNoShowSharers : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += dealWithStaySharesTransactions(dataLoadMetaDataIDTrans);
        LOGGER.info(" After dealWithStaySharesTransactions : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += dealWithCancelledAndNoShowSharesTransaction(dataLoadMetaDataIDTrans);
        LOGGER.info(" After dealWithCancelledAndNoShowSharesTransaction : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += adjustRevenueforCancelledAndNoShowReservationStatuses();
        LOGGER.info(new StringBuilder().append("After adjustSharesInTransaction : ").append(numRowsUpdated).toString());
        return numRowsUpdated;
    }

    protected int adjustSharesInTransaction(String correlationId) {
        int numRowsUpdated = 0;
        long startTime = System.currentTimeMillis();
        numRowsUpdated += getOnlySharedTransactions();
        LOGGER.info(" After getOnlySharedTransactions : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += createFullSortedSharersListForEveryShare("opera.Sharers_OnlySharedTrans");
        LOGGER.info(" After createFullSortedSharersListForEveryShare : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += updateOnlySharedTransactionWithFullSortedSharer();
        LOGGER.info(" After updateOnlySharedTransactionWithFullSortedSharer : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += getCancelledAndNoShowTransactionsFromSharers();
        LOGGER.info(" After getCancelledAndNoShowTransactionsFromSharers : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += removePartialCancelledAndNoShowSharers();
        LOGGER.info(" After removePartialCancelledAndNoShowSharers : " + (System.currentTimeMillis() - startTime));
        int dataLoadMetaDataIDTrans = getDataLoadMetadataIdForCTRANS(correlationId);
        LOGGER.info(" After getDataLoadMetadataIdForCTRANS : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += clearSharedTransactionsFromStageTransactions();
        LOGGER.info(" After clearSharedTransactionsFromStageTransactions : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += dealWithStaySharesTransactions(dataLoadMetaDataIDTrans);
        LOGGER.info(" After dealWithStaySharesTransactions : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += dealWithCancelledAndNoShowSharesTransaction(dataLoadMetaDataIDTrans);
        LOGGER.info(" After dealWithCancelledAndNoShowSharesTransaction : " + (System.currentTimeMillis() - startTime));
        numRowsUpdated += adjustRevenueforCancelledAndNoShowReservationStatuses();
        LOGGER.info(new StringBuilder().append("After adjustSharesInTransaction : ").append(numRowsUpdated).toString());
        return numRowsUpdated;
    }

    int adjustRevenueforCancelledAndNoShowReservationStatuses() {
        if (!pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INCLUDE_NO_SHOW_CANCELLATION_REVENUE)) {
            String queryStr = "update opera.Stage_Transaction set Room_Revenue=0.0 , Food_Beverage_Revenue=0.0 , Other_Revenue=0.0 , Total_Revenue = 0.0 where Reservation_Status='CANCELLED' OR Reservation_Status='NO SHOW'";
            return crudService.executeUpdateByNativeQuery(queryStr);
        }
        return 0;
    }

    private int getOnlySharedTransactions() {
        int rowCount = 0;
        if (operaUtilityService.isOperaTransformTransactionsMultiStep()) {
            utils.dropNonClusteredIndexes();
            utils.truncateTable("opera.Sharers_OnlySharedTrans");
            rowCount = utils.getOnlySharedTransactionsWithNewTransaction();
        } else {
            String queryStr = "if object_id('opera.Sharers_OnlySharedTrans') is not NULL TRUNCATE TABLE opera.Sharers_OnlySharedTrans";
            crudService.executeUpdateByNativeQuery(queryStr);
            rowCount = crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(GET_ONLY_SHARED_TRANSACTIONS));
        }
        if (operaUtilityService.isOperaTransformTransactionsMultiStep()) {
            utils.createNonClusteredIndexes();
        }
        return rowCount;
    }

    private int getAllSharedTransactions() {
        int rowCount = 0;
        if (operaUtilityService.isOperaTransformTransactionsMultiStep()) {
            utils.dropNonClusteredIndexes();
            utils.truncateTable("opera.Sharers_OnlySharedTrans");
            rowCount = utils.getAllSharedTransactionsWithNewTransaction();
        } else {
            crudService.executeUpdateByNativeQuery(TRUNCATE_ONLY_SHARED_TRANS_TABLE);
            rowCount = crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(GET_ALL_SHARED_TRANSACTIONS));
        }
        if (operaUtilityService.isOperaTransformTransactionsMultiStep()) {
            utils.createNonClusteredIndexes();
        }
        return rowCount;
    }

    private int createFullSortedSharersListForEveryShare(String sourceDBName) {
        String createFullySortedSharersList;
        if (isOptimizeSharersSQL()) {
            LOGGER.info("Using optimized sort function: ufn_sort_string_with_delimiter");
            createFullySortedSharersList = new StringBuilder(" Insert into opera.Sharers_ShareWithFullSharers ")
                    .append("select distinct s.StringSorted as FULL_SORTED_SHARERS,Transaction_DT,Confirmation_Number  ")
                    .append(" from ").append(sourceDBName)
                    .append(" cross apply opera.[ufn_sort_string_with_delimiter](sharers, ',') as S")
                    .toString();
        } else {
            createFullySortedSharersList = new StringBuilder(" Insert into opera.Sharers_ShareWithFullSharers ")
                    .append("select  distinct s.partStr as FULL_SORTED_SHARERS,Transaction_DT,Confirmation_Number  ")
                    .append(" from ").append(sourceDBName)
                    .append(" cross apply opera.[ufn_split_string_with_deliminator](sharers, ',') as S")
                    .toString();
        }

        int executeInsertUpdate = 0;
        if (operaUtilityService.isOperaTransformTransactionsMultiStep()) {
            operaUtilityService.dropNcIndex(DROP_NC_INDEX_SHARERS_SHARE_WITH_FULL_SHARERS, "opera.Sharers_ShareWithFullSharers");
            operaUtilityService.truncateTable("opera.Sharers_ShareWithFullSharers");
            executeInsertUpdate = operaUtilityService.executeInsertUpdate(createFullySortedSharersList, "opera.Sharers_ShareWithFullSharers");
            operaUtilityService.createNcIndex(CREATE_NC_INDEX_SHARERS_SHARE_WITH_FULL_SHARERS, "opera.Sharers_ShareWithFullSharers");
        } else {
            crudService.executeUpdateByNativeQuery(TRUNCATE_SHARERS_LIST_TABLE);
            executeInsertUpdate = crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(createFullySortedSharersList));
        }
        return executeInsertUpdate;
    }

    private int updateOnlySharedTransactionWithFullSortedSharer() {
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(UPDATE_ONLY_SHARED_TRX_WITH_SHARERS_LIST));
    }

    private int updateSharedTransactionFullSortedSharer() {
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(UPDATE_SHARED_TRX_SHARERS_LIST));
    }


    private int updateCanAndNSTransactionWithFullSortedSharer() {
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(UPDATE_CNX_NS_SHARED_TRX_WITH_SHARERS_LIST));
    }

    private int updateCanAndNSTransactionFullSortedSharer() {
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(UPDATE_CNX_NS_SHARED_TRX_SHARERS_LIST));
    }

    private int getCancelledAndNoShowTransactionsFromSharers() {
        boolean isIncludeNoShowCancellationRevenue = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INCLUDE_NO_SHOW_CANCELLATION_REVENUE);
        if (operaUtilityService.isOperaTransformTransactionsMultiStep()) {
            utils.dropSharersOnlyCnxOrNoShowIndex();
            utils.truncateTable("opera.Sharers_OnlyCnxOrNoShow");
            utils.getCanceledAndNsTrxFromSharers(isIncludeNoShowCancellationRevenue);
        } else {
            crudService.executeUpdateByNativeQuery(TRUNCATE_CANX_AND_NOSHOW_SHARERS_TABLE);
            crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(isIncludeNoShowCancellationRevenue ? GET_CANCELLED_AND_NS_TRX_WITH_REVENUE_FROM_SHARERS : GET_CANCELLED_AND_NS_TRX_FROM_SHARERS));
        }
        if (operaUtilityService.isOperaTransformTransactionsMultiStep()) {
            utils.createSharersOnlyCnxOrNoShowIndex();
        }
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(DELETE_CNX_AND_NS_FROM_SHARERS));
    }

    private int removePartialCancelledAndNoShowSharers() {
        if (isOptimizeSharersSQL()) {
            LOGGER.info("Using optimized Store Procedure: usp_opera_sharers_delete_onlyCnxOrNoShow");
            String deleteOnlyCnxOrNoShow = "{call opera.usp_opera_sharers_delete_onlyCnxOrNoShow()}";
            return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(deleteOnlyCnxOrNoShow));
        } else {
            return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(REMOVE_PARTIAL_CNX_AND_NS_SHARERS));
        }
    }

    private int clearSharedTransactionsFromStageTransactions() {
        String queryStr = "DELETE FROM opera.Stage_Transaction where len(sharers) > 0";
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(queryStr));
    }

    private int clearSharedTransactionsFromStage() {
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(CLEAR_SHARERS_TRANSACTIONS_FROM_STAGE_TABLE));
    }

    private int getDataLoadMetadataIdForCTRANS(String correlationId) {
        return crudService.findByNativeQuerySingleResult(forceLegacyCardinalityEstimator(GET_CURRENT_TRANS_INCOMING_FEED_ID)
                , QueryParameter.with("correlationId", correlationId).parameters()
                , new RowMapper<Integer>() {
                    @Override
                    public Integer mapRow(Object[] row) {
                        return (Integer) row[0];
                    }
                });
    }

    private int dealWithStaySharesTransactions(int dataLoadMetaDataIDTrans) {
        String sourceDBName = "opera.Sharers_OnlySharedTrans";
        if (isResolveSharersWithRtChanges()) {
            return adjustAndMergeSharesTransactionsConsiderRTChanges(sourceDBName, OPERA_SHARERS_ADJUSTED_SHARE_TRANSACTIONS, dataLoadMetaDataIDTrans, false);
        } else {
            return adjustAndMergeSharesTransactions(sourceDBName, OPERA_SHARERS_ADJUSTED_SHARE_TRANSACTIONS, dataLoadMetaDataIDTrans, false);
        }
    }

    private int dealWithCancelledAndNoShowSharesTransaction(int dataLoadMetaDataIDTrans) {
        String sourceDBName = "opera.Sharers_OnlyCnxOrNoShow";
        String toDBName = "opera.Sharers_AdjustedCnxAndNSSharedTrx";
        boolean cancelOrNoShowSharerPresent = isCancelOrNoShowSharerPresent();
        if (cancelOrNoShowSharerPresent) {
            if (isResolveSharersWithRtChanges()) {
                return adjustAndMergeSharesTransactionsConsiderRTChanges(sourceDBName, toDBName, dataLoadMetaDataIDTrans, cancelOrNoShowSharerPresent);
            } else {
                return adjustAndMergeSharesTransactions(sourceDBName, toDBName, dataLoadMetaDataIDTrans, cancelOrNoShowSharerPresent);
            }
        }
        return 0;
    }

    private boolean isCancelOrNoShowSharerPresent() {
        LOGGER.info("Start isCancelOrNoShowSharerPresent");
        String queryStr = "select count(1) from opera.Sharers_OnlyCnxOrNoShow";
        Integer countRows = crudService.findByNativeQuerySingleResult(queryStr, null, new RowMapper<Integer>() {
            @Override
            public Integer mapRow(Object[] row) {
                return (Integer) row[0];
            }
        });
        LOGGER.info("Start isCancelOrNoShowSharerPresent : " + countRows);
        return countRows > 0;
    }

    private int adjustAndMergeSharesTransactions(String sourceDBName, String toDBName, int dataLoadMetaDataIDTrans, boolean cancelOrNoShowSharerPresent) {
        LOGGER.info(" Starting adjustAndMergeSharesTransactions  ");
        int numRowsUpdated = 0;
        numRowsUpdated += identifyPerfectSharersUsingArrivalAndDepartureDate(sourceDBName);
        LOGGER.info(new StringBuilder().append("identifyPerfectSharersUsingArrivalAndDepartureDate : ").append(numRowsUpdated).toString());
        numRowsUpdated += createFullSortedSharersListForEveryShare(sourceDBName);
        if (cancelOrNoShowSharerPresent) {
            numRowsUpdated += updateCanAndNSTransactionWithFullSortedSharer();
        } else {
            numRowsUpdated += updateOnlySharedTransactionWithFullSortedSharer();
        }
        numRowsUpdated += identifyPrimaryShareAndApplyStaticValuesToSecondaries(sourceDBName);
        numRowsUpdated += adjustDatesForSharers(sourceDBName);
        numRowsUpdated += rollUpSharersPerStayDate(sourceDBName, toDBName);
        if (cancelOrNoShowSharerPresent) {
            numRowsUpdated += adjustReservationStatusAndDatesForCancAndNOShowSharers();
        } else {
            numRowsUpdated += adjustReservationStatusAndDatesForSharersThatStay();
        }
        numRowsUpdated += mergeSharedTransactionsInStageTransactions(toDBName, dataLoadMetaDataIDTrans);
        return numRowsUpdated;
    }

    private Map<String, Collection<SharerDetails>> fetchAllReservationsPerSharer(String sourceDBName) {
        String queryStr = new StringBuilder("select distinct [Sharers], [Confirmation_Number] ,[ARRIVAL_DT] ,[DEPARTURE_DT]   from ").append(sourceDBName)
                .append(" order by ARRIVAL_DT").toString();
        List<SharerDetails> shareReservationsPerSharer = new ArrayList<>();
        List<Object[]> sharedTransaction = crudService.findByNativeQuery(forceLegacyCardinalityEstimator(queryStr));
        for (Object[] row : sharedTransaction) {
            SharerDetails operaTrans = storeOperaTransactionPerShare(row);
            shareReservationsPerSharer.add(operaTrans);
        }
        ListMultimap<String, SharerDetails> allReservationsPerSharer = Multimaps.index(shareReservationsPerSharer,
                new Function<SharerDetails, String>() {
                    @Override
                    public String apply(SharerDetails myObject) {
                        return myObject.getSharerKey();
                    }
                });
        return allReservationsPerSharer.asMap();
    }


    private int identifyPerfectSharersUsingArrivalAndDepartureDate(String sourceDBName) {
        Map<String, Collection<SharerDetails>> allReservationsPerSharer = fetchAllReservationsPerSharer(sourceDBName);
        return updateSharers(sourceDBName, allReservationsPerSharer);
    }

    private SharerDetails storeOperaTransactionPerShare(Object[] row) {
        SharerDetails operaTrans = new SharerDetails();
        String confNo = getStringValue(row[1]);
        operaTrans.setConfirmationNumber(confNo);
        operaTrans.setArrivalDate(new LocalDate(row[2]));
        operaTrans.setDepartureDate(new LocalDate(row[3]));
        operaTrans.setSharers(confNo);
        operaTrans.setSharerKey(getStringValue(row[0]));
        return operaTrans;
    }

    private String getStringValue(Object row) {
        String value = "";
        if (null != row) {
            value = row.toString();
        }
        return value;
    }

    private boolean isIncludePseudoInRevenueEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INCLUDE_PSEUDO_IN_REVENUE);
    }

    // This query has been changed to use a join in sharesWithFullSharers so as to get the minimum confirmation number
    // and the minimum transaction date for that minimum confirmation number
    // Earlier we were getting the minimum transaction date for entire share. Thus select returned multiple rows.
    // Also, only min conf no will not work as it gives a Cartesian product.

    private int identifyPrimaryShareAndApplyStaticValuesToSecondaries(String sourceDBName) {

        if (isIncludePseudoInRevenueEnabled()) {
            return identifyAndInsertNonPseudoPrimaryTransaction(sourceDBName);
        }

        String identifyFinalSharersForStaticValueApplication =
                new StringBuilder(" UPDATE os1 SET  ")
                        .append(" [Confirmation_Number] = os2.Confirmation_Number")
                        .append(",[Reservation_Status]=os2.Reservation_Status,")
                        .append(" Is_Shared = os2.Is_Shared,[RATE_CODE]= os2.RATE_CODE,[RATE_AMOUNT]=os2.RATE_AMOUNT,")
                        .append(" [MARKET_CODE]=os2.MARKET_CODE,")
                        .append(" [HOTEL_MARKET_CODE]=os2.HOTEL_MARKET_CODE,")
                        .append(" [ROOM]=os2.ROOM,")
                        .append(" [ROOM_TYPE]=os2.ROOM_TYPE,")
                        .append(" [SOURCE_CODE]=os2.SOURCE_CODE,[CHANNEL]=os2.CHANNEL,[BOOKED_ROOM_TYPE]=os2.BOOKED_ROOM_TYPE,")
                        .append(" [NATIONALITY]=os2.NATIONALITY,[Reservation_Type]=os2.Reservation_Type,")
                        .append(" [Reservation_Name_ID] =os2.Reservation_Name_ID,")
                        .append(" [Booking_TM]=os2.Booking_TM,[Booking_DT]=os2.Booking_DT,[Rate_Category]=os2.[Rate_Category]  FROM " + sourceDBName)
                        .append(" os1  JOIN  ")
                        .append(" (select [Confirmation_Number],[Reservation_Status],Is_Shared,[Sharers],")
                        .append(" [RATE_CODE],[RATE_AMOUNT],[MARKET_CODE],[HOTEL_MARKET_CODE],[ROOM],")
                        .append(" [ROOM_TYPE],[SOURCE_CODE],[CHANNEL],[BOOKED_ROOM_TYPE],[NATIONALITY],[Reservation_Type],[Reservation_Name_ID],[Booking_TM],[Booking_DT],[Rate_Category]")
                        .append(" from " + sourceDBName)
                        .append(" as os3 join ")
                        .append("     (select ss1.conf_no,ss2.trx_dt from")
                        .append("        (")
                        .append("            select min(confirmation_number) as conf_no,full_sorted_sharers")
                        .append("            from opera.Sharers_ShareWithFullSharers")
                        .append("            group by full_sorted_sharers")
                        .append("        ) as ss1 ")
                        .append("        join ")
                        .append("        ( ")
                        .append("            select confirmation_number,min(transaction_dt) as trx_dt,full_sorted_sharers")
                        .append("            from opera.Sharers_ShareWithFullSharers")
                        .append("            group by full_sorted_sharers,confirmation_number")
                        .append("        ) as ss2")
                        .append("    on ss1.conf_no = ss2.confirmation_number and ss1.full_sorted_sharers = ss2.full_sorted_sharers")
                        .append("    ) as os4")
                        .append(" on os3.Confirmation_Number = os4.conf_no and os3.Transaction_DT = os4.trx_dt  ) as os2  on os1.Sharers = os2.Sharers")
                        .toString();
        int numRowsUpdated = crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(identifyFinalSharersForStaticValueApplication));
        LOGGER.info("Adjusted all sharers transactions in line with primary sharer. " + numRowsUpdated);
        return numRowsUpdated;
    }

    private int identifyAndInsertNonPseudoPrimaryTransaction(String sourceDBName) {
        String roomTypes = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.PSEUDO_ROOM_TYPE_CODES);
        List<String> pseudoRooms = Arrays.stream(roomTypes.split(",")).collect(Collectors.toList());

        String identifyPrimary = new StringBuilder(" With sharedTransWithIsPseudoColumn as (")
                .append("        select confirmation_number,transaction_dt, Sharers,")
                .append("       (case when room_type in (:psList) then 0 else 1 end) as isPseudo, ")
                .append("       [Reservation_Status],Is_Shared, ")
                .append("        [RATE_CODE],[RATE_AMOUNT],[MARKET_CODE],[HOTEL_MARKET_CODE],[ROOM],   ")
                .append("        [ROOM_TYPE],[SOURCE_CODE],[CHANNEL],[BOOKED_ROOM_TYPE],[NATIONALITY],[Reservation_Type],[Reservation_Name_ID],[Booking_TM],[Booking_DT],[Rate_Category]   ")
                .append("        from " + sourceDBName)
                .append(" ), orderedSharedTrans as ( ")
                .append("       select confirmation_number,transaction_dt, Sharers, ")
                .append("       [Reservation_Status],Is_Shared, ")
                .append("       [RATE_CODE],[RATE_AMOUNT],[MARKET_CODE],[HOTEL_MARKET_CODE],[ROOM],   ")
                .append("       [ROOM_TYPE],[SOURCE_CODE],[CHANNEL],[BOOKED_ROOM_TYPE],[NATIONALITY],[Reservation_Type],[Reservation_Name_ID],[Booking_TM],[Booking_DT],[Rate_Category] , ")
                .append("       ROW_NUMBER() OVER (partition by Sharers ORDER BY isPseudo DESC,  confirmation_number asc, transaction_dt asc ) as row_num ")
                .append("       from sharedTransWithIsPseudoColumn  ")
                .append(" ), primaryTransaction as ( ")
                .append("       select confirmation_number,transaction_dt, Sharers, ")
                .append("       [Reservation_Status],Is_Shared, ")
                .append("        [RATE_CODE],[RATE_AMOUNT],[MARKET_CODE],[HOTEL_MARKET_CODE],[ROOM],   ")
                .append("        [ROOM_TYPE],[SOURCE_CODE],[CHANNEL],[BOOKED_ROOM_TYPE],[NATIONALITY],[Reservation_Type],[Reservation_Name_ID],[Booking_TM],[Booking_DT],[Rate_Category]  ")
                .append("        from orderedSharedTrans where row_num = 1 ")
                .append(") ")
                .append("UPDATE sharedTransactionTable SET     ")
                .append("    [Confirmation_Number] = primaryTransaction.Confirmation_Number  ")
                .append("    ,[Reservation_Status]=primaryTransaction.Reservation_Status,  ")
                .append("    Is_Shared = primaryTransaction.Is_Shared,[RATE_CODE]= primaryTransaction.RATE_CODE,[RATE_AMOUNT]=primaryTransaction.RATE_AMOUNT,  ")
                .append("    [MARKET_CODE]=primaryTransaction.MARKET_CODE,  ")
                .append("    [HOTEL_MARKET_CODE]=primaryTransaction.HOTEL_MARKET_CODE,  ")
                .append("    [ROOM]=primaryTransaction.ROOM,  ")
                .append("    [ROOM_TYPE]=primaryTransaction.ROOM_TYPE,  ")
                .append("    [SOURCE_CODE]=primaryTransaction.SOURCE_CODE,[CHANNEL]=primaryTransaction.CHANNEL,[BOOKED_ROOM_TYPE]=primaryTransaction.BOOKED_ROOM_TYPE,  ")
                .append("    [NATIONALITY]=primaryTransaction.NATIONALITY,[Reservation_Type]= primaryTransaction.Reservation_Type,  ")
                .append("    [Reservation_Name_ID] =primaryTransaction.Reservation_Name_ID,  ")
                .append("    [Booking_TM]=primaryTransaction.Booking_TM,[Booking_DT]=primaryTransaction.Booking_DT,[Rate_Category]=primaryTransaction.[Rate_Category]  FROM  " + sourceDBName + " as sharedTransactionTable ")
                .append("      join  primaryTransaction on sharedTransactionTable.[Sharers] = primaryTransaction.Sharers;   ").toString();

        int numRowsUpdated = crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(identifyPrimary), QueryParameter.with("psList", pseudoRooms).parameters());
        LOGGER.info("Adjusted all sharers transactions in line with non-pseudo primary sharer. " + numRowsUpdated);
        return numRowsUpdated;
    }


    private int adjustDatesForSharers(String sourceDBName) {
        String adjustDatesForSharers = new StringBuilder("update os1 set arrival_dt = os2.arrival_dt,departure_dt = os2.departure_dt,booking_dt = os2.booking_dt,")
                .append(" Cancellation_DT = os2.Cancellation_DT,Checkout_DT=os2.Checkout_DT  from " + sourceDBName + " os1 join")
                .append(" ( select sharers, MIN(arrival_dt) as arrival_dt,MAX(departure_dt) as departure_dt, MIN(booking_dt) as booking_dt")
                .append(" ,max(Checkout_DT) as Checkout_DT,max(Cancellation_DT) as Cancellation_DT  from " + sourceDBName + " group by Sharers) as os2")
                .append(" on os1.Sharers = os2.Sharers").toString();
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(adjustDatesForSharers));
    }

    private int adjustDatesForSharersWithTempTable(String sourceDBName) {
        crudService.executeUpdateByNativeQuery("IF OBJECT_ID('tempdb..#tempSharersOnlySharedTrans') IS NOT NULL DROP TABLE #tempSharersOnlySharedTrans;");
        String adjustDatesForSharers = new StringBuilder()
                .append("select Hash_Value, MIN(arrival_dt) as arrival_dt,MAX(departure_dt) as departure_dt, MIN(booking_dt) as booking_dt ")
                .append("                 ,max(Checkout_DT) as Checkout_DT,max(Cancellation_DT) as Cancellation_DT  ")
                .append("  into #tempSharersOnlySharedTrans")
                .append(" from ")
                .append(sourceDBName)
                .append(" group by Hash_Value; ")
                .append(" update os1 set arrival_dt = os2.arrival_dt,departure_dt = os2.departure_dt,booking_dt = os2.booking_dt, ")
                .append(" Cancellation_DT = os2.Cancellation_DT,Checkout_DT=os2.Checkout_DT  ")
                .append(" from ").append(sourceDBName).append(" os1 join ")
                .append(" #tempSharersOnlySharedTrans").append(" as os2 ")
                .append(" on os1.Hash_Value = os2.Hash_Value").toString();
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(adjustDatesForSharers));
    }

    private int rollUpSharersPerStayDate(String sourceDBName, String toDBName) {
        String queryStr = "if object_id('" + toDBName + "') is not NULL TRUNCATE TABLE " + toDBName;
        crudService.executeUpdateByNativeQuery(queryStr);
        String rollUpSharersPerStayDate = new StringBuilder(" Insert into ").append(toDBName)
                .append("   (  [Confirmation_Number],[Reservation_Status],Is_Shared,[Sharers],")
                .append("             [Transaction_DT],[ARRIVAL_DT],[DEPARTURE_DT],[Checkout_DT],[Cancellation_DT],")
                .append("             [Booking_DT],[RATE_CODE],[RATE_AMOUNT],[MARKET_CODE],[HOTEL_MARKET_CODE],[ROOM],[room_revenue],[Food_Beverage_Revenue],[OTHER_REVENUE],[TOTAL_REVENUE],")
                .append("             [ROOM_TYPE],[SOURCE_CODE],[CHANNEL],[BOOKED_ROOM_TYPE],[NATIONALITY],[Reservation_Type],[Number_Children],")
                .append("             [Number_Adults] ,[Reservation_Name_ID],[Booking_TM],[Rate_Category])")

                .append(" select min(Confirmation_Number) as confirmation_number,Reservation_Status,Is_Shared,sharers,transaction_dt, ")
                .append(" ARRIVAL_DT,DEPARTURE_DT, Checkout_DT, Cancellation_DT, ")
                .append(" Booking_DT,RATE_CODE,RATE_AMOUNT,MARKET_CODE,[HOTEL_MARKET_CODE],ROOM, sum(ROOM_REVENUE) as room_revenue, ")
                .append(" SUM(Food_Beverage_Revenue) as Food_Beverage_Revenue,SUM(other_revenue) as other_revenue,SUM(total_revenue) as total_revenue, ")
                .append(" ROOM_TYPE,SOURCE_CODE,CHANNEL,BOOKED_ROOM_TYPE,NATIONALITY,Reservation_Type, ")
                .append(" SUM(number_Children) as number_children,SUM(Number_Adults) as Number_Adults,Reservation_Name_ID,[Booking_TM],[Rate_Category]   ")
                .append(" from  ").append(sourceDBName).append(" group by Sharers,Transaction_DT,Confirmation_Number,Reservation_Status,Is_Shared, ")
                .append(" ARRIVAL_DT,DEPARTURE_DT,Checkout_DT,Cancellation_DT,Booking_DT,RATE_CODE,RATE_AMOUNT,MARKET_CODE,hotel_market_code,ROOM,ROOM_TYPE,SOURCE_CODE,CHANNEL, ")
                .append(" BOOKED_ROOM_TYPE,NATIONALITY,Reservation_Type,Reservation_Name_ID,[Booking_TM],[Rate_Category]")
                .toString();
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(rollUpSharersPerStayDate));
    }


    private int adjustReservationStatusAndDatesForSharersThatStay() {
        String businessDate = getBusinessDateFromMetadata();
        int numRowsUpdated = crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(ADJUST_RESERVATIONS_FOR_SHARERS),
                QueryParameter.with("businessDate", businessDate).parameters());
        LOGGER.info("Adjusted reservationstatus for all sharers. " + numRowsUpdated);
        return numRowsUpdated;
    }

    private int adjustReservationStatusAndDatesForCancAndNOShowSharers() {
        int numRowsUpdated = crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(ADJUST_CHECKOUT_DT_FOR_CNX_NS_SHARERS));
        LOGGER.info("Adjusted reservation status for all sharers. " + numRowsUpdated);
        return numRowsUpdated;
    }

    private String getBusinessDateFromMetadata() {
        String queryStr = "select Business_DT from opera.Stage_Incoming_Metadata";
        Object busDtObj = crudService.findByNativeQuerySingleResult(queryStr, null, new RowMapper<Object>() {
            @Override
            public Object mapRow(Object[] row) {
                return row[0];
            }
        });
        String busDt = null;
        if (null != busDtObj) {
            busDt = busDtObj.toString();
        }
        return busDt;
    }

    private int mergeSharedTransactionsInStageTransactions(String fromDBName, int dataLoadMetaDataIDTrans) {
        String mergeSharersWithStageTransactions = new StringBuilder(" INSERT INTO opera.Stage_Transaction ( [Confirmation_Number],[Reservation_Status],Is_Shared,[Sharers],")
                .append(" [Transaction_DT],[ARRIVAL_DT],[DEPARTURE_DT],[Checkout_DT],[Cancellation_DT],")
                .append(" [Booking_DT],[RATE_CODE],[RATE_AMOUNT],[MARKET_CODE],[HOTEL_MARKET_CODE],[ROOM],[ROOM_REVENUE],[Food_Beverage_Revenue],[OTHER_REVENUE],[TOTAL_REVENUE],")
                .append(" [ROOM_TYPE],[SOURCE_CODE],[CHANNEL],[BOOKED_ROOM_TYPE],[NATIONALITY],[Reservation_Type],[Number_Children],")
                .append(" [Number_Adults] ,[Reservation_Name_ID],[Data_Load_Metadata_ID] ,[Booking_TM] ,[Rate_Category])")
                .append(" SELECT  [Confirmation_Number],[Reservation_Status],Is_Shared ,LEFT([Sharers],2000),")
                .append(" [Transaction_DT],[ARRIVAL_DT],[DEPARTURE_DT],[Checkout_DT],[Cancellation_DT],")
                .append(" [Booking_DT],[RATE_CODE],[RATE_AMOUNT],[MARKET_CODE],[HOTEL_MARKET_CODE],[ROOM],[ROOM_REVENUE],[Food_Beverage_Revenue],[OTHER_REVENUE],[TOTAL_REVENUE],")
                .append(" [ROOM_TYPE],[SOURCE_CODE],[CHANNEL],[BOOKED_ROOM_TYPE],[NATIONALITY],[Reservation_Type],[Number_Children],")
                .append(" [Number_Adults] ,[Reservation_Name_ID], :dataLoadMetaDataIDTrans ,[Booking_TM],[Rate_Category] from " + fromDBName)
                .toString();
        int numRowsUpdated = crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(mergeSharersWithStageTransactions),
                QueryParameter.with("dataLoadMetaDataIDTrans", dataLoadMetaDataIDTrans).parameters());
        LOGGER.info("Merged all sharers transactions. " + numRowsUpdated);
        return numRowsUpdated;
    }


    protected int splitReservationsOnMSAndRT(String correlationId) {
        int numRowsUpdated = 0;
        LOGGER.info("Processing reservations with RT or MS split for feed : " + correlationId);
        numRowsUpdated += crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(INVOKE_SPLIT_TRANSACTIONS_ON_MS_RT_CHANGES));
        LOGGER.info("Completed processing reservations with RT or MS split for feed : " + correlationId
                + " Rows Updated : " + numRowsUpdated);
        return numRowsUpdated;
    }

    @SuppressWarnings("squid:S2095")
    public int updateSharers(String sourceDBName, Map<String, Collection<SharerDetails>> allReservationsPerSharer) {
        LOGGER.info("Starting execution updateSharers");
        int numRowsUpdated = 0;
        Long start = System.currentTimeMillis();
        Connection connection = null;
        Map<String, String> updatedSharerMap = new HashMap<>();
        Statement statement = null;
        PreparedStatement preparedStatementInsertSharers = null;
        try {
            connection = getJDBCConnection();
            statement = connection.createStatement();
            statement.executeUpdate("drop table if exists #tempsharers;");
            statement.executeUpdate(OperaTransactionServiceConstants.CREATE_TEMP_SHARERS_TABLE);
            preparedStatementInsertSharers = connection.prepareStatement(INSERT_SHARERS_TO_TEMP);
            Iterator itr = allReservationsPerSharer.keySet().iterator();
            generateSharerMap(allReservationsPerSharer, updatedSharerMap, itr);
            for (Map.Entry<String, String> entry : updatedSharerMap.entrySet()) {
                numRowsUpdated++;
                preparedStatementInsertSharers.setString(1, entry.getKey());
                preparedStatementInsertSharers.setString(2, entry.getValue());
                preparedStatementInsertSharers.addBatch();
                if (numRowsUpdated % BATCH_SIZE == 0) {
                    preparedStatementInsertSharers.executeBatch();
                }
            }
            preparedStatementInsertSharers.executeBatch();
            updateSharerTempTable(sourceDBName, statement);
        } catch (SQLException sqlEx) {
            LOGGER.error("Error while updating sharer information  ", sqlEx);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "ERROR:Error while updating sharer information  ", sqlEx);
        } finally {
            try {
                DbUtils.close(preparedStatementInsertSharers);
                DbUtils.close(statement);
            } catch (Exception ignore) {
                LOGGER.error("Could not close statement.", ignore);
            }
            if (null != connection) {
                try {
                    jpaJdbcUtil.closeConnection(crudService, connection);
                } catch (Exception ignore) {
                    LOGGER.error("Could not close connection.", ignore);
                }
            }
        }
        LOGGER.info("Completed updateSharers in " + (System.currentTimeMillis() - start));
        return numRowsUpdated;
    }

    private void generateSharerMap(Map<String, Collection<SharerDetails>> allReservationsPerSharer, Map<String, String> updatedSharerMap, Iterator itr) {
        while (itr.hasNext()) {
            Map<String, SharerDetails> allTransactionsWithinAShare = new HashMap<>();
            List<SharerDetails> sharedTransactions = (List<SharerDetails>) allReservationsPerSharer.get(itr.next());
            for (SharerDetails sharedTransaction : sharedTransactions) {
                if (isResolveSharersWithRtChanges()) {
                    allTransactionsWithinAShare = rectifySharerInOperaTransPerShareUsingStayOverlap(allTransactionsWithinAShare, sharedTransaction);
                } else {
                    allTransactionsWithinAShare = rectifySharerInOperaTransPerShare(allTransactionsWithinAShare, sharedTransaction);
                }
            }
            Iterator<String> itrTrans = allTransactionsWithinAShare.keySet().iterator();
            while (itrTrans.hasNext()) {
                SharerDetails tempTran = allTransactionsWithinAShare.get(itrTrans.next());
                updatedSharerMap.put(tempTran.getConfirmationNumber(), tempTran.getSharers());
            }
        }
    }

    private Map<String, SharerDetails> rectifySharerInOperaTransPerShare(Map<String, SharerDetails> allTransactionsWithinAShare,
                                                                         SharerDetails operaTrans) {
        // iterate through the map and check this arrival against all existing records in the map
        String confNo = operaTrans.getConfirmationNumber();
        Iterator<String> itr = allTransactionsWithinAShare.keySet().iterator();
        while (itr.hasNext()) {
            SharerDetails tempTran = allTransactionsWithinAShare.get(itr.next());
            if (operaTrans.getArrivalDate().compareTo(tempTran.getArrivalDate()) >= 0
                    && operaTrans.getArrivalDate().compareTo(tempTran.getDepartureDate()) <= 0) {
                tempTran.setSharers(tempTran.getSharers() + "," + confNo);
                operaTrans.setSharers(tempTran.getSharers());
                // US9714 - Shares get split when first sharers departure date is before last sharers arrival date
                if (operaTrans.getDepartureDate().isAfter(tempTran.getDepartureDate())) {
                    tempTran.setDepartureDate(operaTrans.getDepartureDate());
                }
                if (operaTrans.getDepartureDate().isBefore(tempTran.getDepartureDate())) {
                    operaTrans.setDepartureDate(tempTran.getDepartureDate());
                }
            } // if
        }// while
        allTransactionsWithinAShare.put(confNo, operaTrans);
        return allTransactionsWithinAShare;
    }

    private Map<String, SharerDetails> rectifySharerInOperaTransPerShareUsingStayOverlap(Map<String, SharerDetails> allTransactionsWithinAShare,
                                                                                         SharerDetails operaTrans) {
        // iterate through the map and check this arrival against all existing records in the map
        String confNo = operaTrans.getConfirmationNumber();
        Iterator<String> itr = allTransactionsWithinAShare.keySet().iterator();
        while (itr.hasNext()) {
            SharerDetails tempTran = allTransactionsWithinAShare.get(itr.next());
            if (operaTrans.getArrivalDate().compareTo(tempTran.getArrivalDate()) >= 0
                    && operaTrans.getArrivalDate().compareTo(tempTran.getDepartureDate()) < 0) {
                tempTran.setSharers(tempTran.getSharers() + "," + confNo);
                operaTrans.setSharers(tempTran.getSharers());
                // US9714 - Shares get split when first sharers departure date is before last sharers arrival date
                if (operaTrans.getDepartureDate().isAfter(tempTran.getDepartureDate())) {
                    tempTran.setDepartureDate(operaTrans.getDepartureDate());
                }
                if (operaTrans.getDepartureDate().isBefore(tempTran.getDepartureDate())) {
                    operaTrans.setDepartureDate(tempTran.getDepartureDate());
                }
            } // if
        }// while
        allTransactionsWithinAShare.put(confNo, operaTrans);
        return allTransactionsWithinAShare;
    }

    private Connection getJDBCConnection() {
        return jpaJdbcUtil.getJdbcConnection(crudService);
    }

    private int updateSharerTempTable(String sourceDBName, Statement statement) throws SQLException {
        String updateSharers = new StringBuilder("UPDATE ").append(sourceDBName).append(" set  ").append(sourceDBName)
                .append(".sharers = #tempSharers.sharer_list FROM ").append(sourceDBName)
                .append(" INNER JOIN #tempSharers ON #tempSharers.Confirmation_Number = ")
                .append(sourceDBName).append(".Confirmation_Number;").toString();
        return statement.executeUpdate(forceLegacyCardinalityEstimator(updateSharers));
    }

    private int identifyPrimaryShareAndApplyStaticValuesConsiderRTChanges(String sourceDBName) {
        String identifyPrimarySharersForStaticValueApplicationWithRTChanges = "";
        if (isOptimizeSharersSQL()) {
            if ("opera.Sharers_OnlySharedTrans".equals(sourceDBName)) {
                LOGGER.info("Using optimized Store Procedure: usp_opera_sharers_update_onlySharedTransWithRTChanges");
                identifyPrimarySharersForStaticValueApplicationWithRTChanges = "{call opera.usp_opera_sharers_update_onlySharedTransWithRTChanges()}";
            } else if ("opera.Sharers_OnlyCnxOrNoShow".equals(sourceDBName)) {
                LOGGER.info("Using optimized Store Procedure: usp_opera_sharers_update_onlyCXNSWithRTChanges");
                identifyPrimarySharersForStaticValueApplicationWithRTChanges = "{call opera.usp_opera_sharers_update_onlyCXNSWithRTChanges()}";
            }
        } else {
            identifyPrimarySharersForStaticValueApplicationWithRTChanges =
                    new StringBuilder(" UPDATE os1 SET  ")
                            .append("[Reservation_Status]=os2.Reservation_Status,")
                            .append(" Is_Shared = os2.Is_Shared,[RATE_CODE]= os2.RATE_CODE,[RATE_AMOUNT]=os2.RATE_AMOUNT,")
                            .append(" [MARKET_CODE]=os2.MARKET_CODE,")
                            .append(" [HOTEL_MARKET_CODE]=os2.HOTEL_MARKET_CODE,")
                            .append(" [ROOM]=os2.ROOM,")
                            .append(" [SOURCE_CODE]=os2.SOURCE_CODE,[CHANNEL]=os2.CHANNEL,[BOOKED_ROOM_TYPE]=os2.BOOKED_ROOM_TYPE,")
                            .append(" [NATIONALITY]=os2.NATIONALITY,[Reservation_Type]=os2.Reservation_Type,")
                            .append(" [Booking_TM]=os2.Booking_TM,[Booking_DT]=os2.Booking_DT,[Rate_Category]=os2.[Rate_Category]  FROM " + sourceDBName)
                            .append(" os1  JOIN  ")
                            .append(" (select [Confirmation_Number],[Reservation_Status],Is_Shared,[Sharers],")
                            .append(" [RATE_CODE],[RATE_AMOUNT],[MARKET_CODE],[HOTEL_MARKET_CODE],[ROOM],")
                            .append(" [ROOM_TYPE],[SOURCE_CODE],[CHANNEL],[BOOKED_ROOM_TYPE],[NATIONALITY],[Reservation_Type],[Reservation_Name_ID],[Booking_TM],[Booking_DT],[Rate_Category]")
                            .append(" from " + sourceDBName)
                            .append(" as os3 join ")
                            .append("     (select ss1.conf_no,ss2.trx_dt from")
                            .append("        (")
                            .append("            select min(confirmation_number) as conf_no,full_sorted_sharers")
                            .append("            from opera.Sharers_ShareWithFullSharers")
                            .append("            group by full_sorted_sharers")
                            .append("        ) as ss1 ")
                            .append("        join ")
                            .append("        ( ")
                            .append("            select confirmation_number,min(transaction_dt) as trx_dt,full_sorted_sharers")
                            .append("            from opera.Sharers_ShareWithFullSharers")
                            .append("            group by full_sorted_sharers,confirmation_number")
                            .append("        ) as ss2")
                            .append("    on ss1.conf_no = ss2.confirmation_number and ss1.full_sorted_sharers = ss2.full_sorted_sharers")
                            .append("    ) as os4")
                            .append(" on os3.Confirmation_Number = os4.conf_no and os3.Transaction_DT = os4.trx_dt  ")
                            .append(" where os3.sharers in(select distinct sharers from " + sourceDBName + " group by sharers having count(distinct room_type) > 1)")
                            .append(") as os2  on os1.Sharers = os2.Sharers")
                            .toString();
        }
        int numRowsUpdated = crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(identifyPrimarySharersForStaticValueApplicationWithRTChanges));
        numRowsUpdated += identifyPrimarySharersForStaticValueApplicationWithoutRTChanges(sourceDBName);
        LOGGER.info("Adjusted all sharers transactions in line with primary sharer. " + numRowsUpdated);
        return numRowsUpdated;
    }

    private int identifyPrimarySharersForStaticValueApplicationWithoutRTChanges(String sourceDBName) {
        String query = getQueryForOnlyCnxOrNoShow(sourceDBName);
        if ("opera.Sharers_OnlySharedTrans".equals(sourceDBName)) {
            query = getQueryForOnlySharedTrans(sourceDBName);
        }
        return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(query));
    }

    private String getQueryForOnlyCnxOrNoShow(String sourceDBName) {
        if (isOptimizeSharersSQL()) {
            LOGGER.info("Using optimized Store Procedure: usp_opera_sharers_update_onlyCnxOrNoShow");
            return "{call opera.usp_opera_sharers_update_onlyCnxOrNoShow()}";
        }

        return new StringBuilder(" UPDATE os1 SET  ")
                .append(" [Confirmation_Number] = os2.Confirmation_Number,")
                .append("[Reservation_Status]=os2.Reservation_Status,")
                .append(" Is_Shared = os2.Is_Shared,[RATE_CODE]= os2.RATE_CODE,[RATE_AMOUNT]=os2.RATE_AMOUNT,")
                .append(" [MARKET_CODE]=os2.MARKET_CODE,")
                .append(" [HOTEL_MARKET_CODE]=os2.HOTEL_MARKET_CODE,")
                .append(" [ROOM]=os2.ROOM,")
                .append(" [ROOM_TYPE]=os2.ROOM_TYPE,")
                .append(" [SOURCE_CODE]=os2.SOURCE_CODE,[CHANNEL]=os2.CHANNEL,[BOOKED_ROOM_TYPE]=os2.BOOKED_ROOM_TYPE,")
                .append(" [NATIONALITY]=os2.NATIONALITY,[Reservation_Type]=os2.Reservation_Type,")
                .append(" [Reservation_Name_ID] =os2.Reservation_Name_ID,")
                .append(" [Booking_TM]=os2.Booking_TM,[Booking_DT]=os2.Booking_DT,[Rate_Category]=os2.[Rate_Category]  FROM " + sourceDBName)
                .append(" os1  JOIN  ")
                .append(" (select [Confirmation_Number],[Reservation_Status],Is_Shared,[Sharers],")
                .append(" [RATE_CODE],[RATE_AMOUNT],[MARKET_CODE],[HOTEL_MARKET_CODE],[ROOM],")
                .append(" [ROOM_TYPE],[SOURCE_CODE],[CHANNEL],[BOOKED_ROOM_TYPE],[NATIONALITY],[Reservation_Type],[Reservation_Name_ID],[Booking_TM],[Booking_DT],[Rate_Category]")
                .append(" from " + sourceDBName)
                .append(" as os3 join ")
                .append("     (select ss1.conf_no,ss2.trx_dt from")
                .append("        (")
                .append("            select min(confirmation_number) as conf_no,full_sorted_sharers")
                .append("            from opera.Sharers_ShareWithFullSharers")
                .append("            group by full_sorted_sharers")
                .append("        ) as ss1 ")
                .append("        join ")
                .append("        ( ")
                .append("            select confirmation_number,min(transaction_dt) as trx_dt,full_sorted_sharers")
                .append("            from opera.Sharers_ShareWithFullSharers")
                .append("            group by full_sorted_sharers,confirmation_number")
                .append("        ) as ss2")
                .append("    on ss1.conf_no = ss2.confirmation_number and ss1.full_sorted_sharers = ss2.full_sorted_sharers")
                .append("    ) as os4")
                .append(" on os3.Confirmation_Number = os4.conf_no and os3.Transaction_DT = os4.trx_dt ")
                .append(" where os3.sharers in(select distinct sharers from " + sourceDBName + " group by sharers having count(distinct room_type) = 1)")
                .append(" ) as os2  on os1.Sharers = os2.Sharers")
                .toString();
    }

    private String getQueryForOnlySharedTrans(String sourceDBName) {
        String updateHash = "update opera.Sharers_OnlySharedTrans set Hash_Value= CONVERT(NVARCHAR(32),HashBytes('MD5', sharers),2)";
        crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(updateHash));

        if (isOptimizeSharersSQL()) {
            LOGGER.info("Using optimized Store Procedure: usp_opera_sharers_update_primary");
            return "{call opera.usp_opera_sharers_update_primary()}";
        }

        return new StringBuilder(" UPDATE os1 SET  ")
                .append(" [Confirmation_Number] = os2.Confirmation_Number,")
                .append("[Reservation_Status]=os2.Reservation_Status,")
                .append(" Is_Shared = os2.Is_Shared,[RATE_CODE]= os2.RATE_CODE,[RATE_AMOUNT]=os2.RATE_AMOUNT,")
                .append(" [MARKET_CODE]=os2.MARKET_CODE,")
                .append(" [HOTEL_MARKET_CODE]=os2.HOTEL_MARKET_CODE,")
                .append(" [ROOM]=os2.ROOM,")
                .append(" [ROOM_TYPE]=os2.ROOM_TYPE,")
                .append(" [SOURCE_CODE]=os2.SOURCE_CODE,[CHANNEL]=os2.CHANNEL,[BOOKED_ROOM_TYPE]=os2.BOOKED_ROOM_TYPE,")
                .append(" [NATIONALITY]=os2.NATIONALITY,[Reservation_Type]=os2.Reservation_Type,")
                .append(" [Reservation_Name_ID] =os2.Reservation_Name_ID,")
                .append(" [Booking_TM]=os2.Booking_TM,[Booking_DT]=os2.Booking_DT,[Rate_Category]=os2.[Rate_Category]  FROM " + sourceDBName)
                .append(" os1  JOIN  ")
                .append(" (select [Confirmation_Number],[Reservation_Status],Is_Shared,[Sharers],")
                .append(" [RATE_CODE],[RATE_AMOUNT],[MARKET_CODE],[hotel_market_code],[ROOM],")
                .append(" [ROOM_TYPE],[SOURCE_CODE],[CHANNEL],[BOOKED_ROOM_TYPE],[NATIONALITY],[Reservation_Type],[Reservation_Name_ID],[Booking_TM],[Booking_DT],[Rate_Category],[HASH_VALUE]")
                .append(" from " + sourceDBName)
                .append(" as os3 join ")
                .append("     (select ss1.conf_no,ss2.trx_dt from")
                .append("        (")
                .append("            select min(confirmation_number) as conf_no,full_sorted_sharers")
                .append("            from opera.Sharers_ShareWithFullSharers")
                .append("            group by full_sorted_sharers")
                .append("        ) as ss1 ")
                .append("        join ")
                .append("        ( ")
                .append("            select confirmation_number,min(transaction_dt) as trx_dt,full_sorted_sharers")
                .append("            from opera.Sharers_ShareWithFullSharers")
                .append("            group by full_sorted_sharers,confirmation_number")
                .append("        ) as ss2")
                .append("    on ss1.conf_no = ss2.confirmation_number and ss1.full_sorted_sharers = ss2.full_sorted_sharers")
                .append("    ) as os4")
                .append(" on os3.Confirmation_Number = os4.conf_no and os3.Transaction_DT = os4.trx_dt ")
                .append("     where CONVERT(NVARCHAR(32),HashBytes('MD5', os3.sharers),2) in(select distinct hash_value from " + sourceDBName +
                        " group by hash_value having count(distinct room_type) = 1) " +
                        "                          ) as os2  on os1.hash_value = os2.hash_value ")
                .toString();
    }


    private int rollUpSharersPerStayDateConsiderRTChanges(String sourceDBName, String toDBName) {
        LOGGER.info(String.format("rollUpSharersPerStayDateConsiderRTChanges with source DB %s and target DB %s ", sourceDBName, toDBName));
        String rollUpSharersPerStayDate = new StringBuilder().append("insert into ").append(toDBName)
                .append("   (  [Confirmation_Number],[Reservation_Status],Is_Shared,[Sharers],")
                .append("             [Transaction_DT],[ARRIVAL_DT],[DEPARTURE_DT],[Checkout_DT],[Cancellation_DT],")
                .append("             [Booking_DT],[RATE_CODE],[RATE_AMOUNT],[MARKET_CODE],[ROOM],[room_revenue],[Food_Beverage_Revenue],[OTHER_REVENUE],[TOTAL_REVENUE],")
                .append("             [ROOM_TYPE],[SOURCE_CODE],[CHANNEL],[BOOKED_ROOM_TYPE],[NATIONALITY],[Reservation_Type],[Number_Children],")
                .append("             [Number_Adults] ,[Reservation_Name_ID],[Booking_TM],[Rate_Category],hotel_market_code)")
                .append(" select min(Confirmation_Number) as confirmation_number,min(Reservation_Status) as Reservation_Status,min(Is_Shared) as Is_Shared,sharers,transaction_dt,")
                .append(" min(ARRIVAL_DT) as ARRIVAL_DT ,max(DEPARTURE_DT) as departure_dt, max(Checkout_DT) as Checkout_DT, max(Cancellation_DT) as Cancellation_DT,")
                .append(" min(Booking_DT) as Booking_DT,min(RATE_CODE) as RATE_CODE,min(RATE_AMOUNT) as RATE_AMOUNT,min(MARKET_CODE) as MARKET_CODE,min(ROOM) as ROOM, sum(ROOM_REVENUE) as room_revenue,")
                .append(" SUM(Food_Beverage_Revenue) as Food_Beverage_Revenue,SUM(other_revenue) as other_revenue,SUM(total_revenue) as total_revenue,")
                .append(" ROOM_TYPE,min(SOURCE_CODE) as SOURCE_CODE, min(CHANNEL) as CHANNEL,min(BOOKED_ROOM_TYPE) as BOOKED_ROOM_TYPE,min(NATIONALITY) as NATIONALITY,min(Reservation_Type) as Reservation_Type,")
                .append(" SUM(number_Children) as number_children,SUM(Number_Adults) as Number_Adults,min(Reservation_Name_ID)")
                .append(" as Reservation_Name_ID, min(Booking_TM) as [Booking_TM], min(Rate_Category) as [Rate_Category],min(HOTEL_MARKET_CODE) as HOTEL_MARKET_CODE ")
                .append(" from ").append(sourceDBName).append(" group by Sharers,Transaction_DT,ROOM_TYPE").toString();

        if (operaUtilityService.isOperaTransformTransactionsMultiStep() && StringUtils.equals(OPERA_SHARERS_ADJUSTED_SHARE_TRANSACTIONS, toDBName)) {
            operaUtilityService.dropNcIndex(DROP_NC_INDEX_SHARERS_ADJUSTED_SHARE_TRANSACTIONS, toDBName);
            operaUtilityService.truncateTable(toDBName);
            int executeInsertUpdate = operaUtilityService.executeInsertUpdate(rollUpSharersPerStayDate, toDBName);
            operaUtilityService.createNcIndex(CREATE_NC_INDEX_SHARERS_ADJUSTED_SHARE_TRANSACTIONS, toDBName);
            return executeInsertUpdate;
        } else {
            String queryStr = "if object_id('" + toDBName + "') is not NULL TRUNCATE TABLE " + toDBName;
            crudService.executeUpdateByNativeQuery(queryStr);
            return crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(rollUpSharersPerStayDate));
        }
    }

    private int adjustAndMergeSharesTransactionsConsiderRTChanges(String sourceDBName, String toDBName, int dataLoadMetaDataIDTrans, boolean cancelOrNoShowSharerPresent) {
        LOGGER.info(" Starting adjustAndMergeSharesTransactions  ");
        int numRowsUpdated = 0;
        numRowsUpdated += identifyPerfectSharersUsingArrivalAndDepartureDate(sourceDBName);
        LOGGER.info(new StringBuilder().append("identifyPerfectSharersUsingArrivalAndDepartureDate : ").append(numRowsUpdated).toString());
        numRowsUpdated += createFullSortedSharersListForEveryShare(sourceDBName);
        if (cancelOrNoShowSharerPresent) {
            numRowsUpdated += updateCanAndNSTransactionFullSortedSharer();
        } else {
            numRowsUpdated += updateSharedTransactionFullSortedSharer();
        }
        // roll up revenue of post departure sharers so it does not create noise in resolving sharers
        // update a set Food_Beverage_Revenue = a.Food_Beverage_Revenue + b.Food_Beverage_Revenue, room_revenue = a.room_revenue + b.room_revenue,  total_revenue = a.total_revenue +b.total_revenue, Other_Revenue = a.Other_Revenue + b.Other_Revenue
        // from sourceDBName as a join sourceDBName as b on a.confirmation_number = b.confirmation_number and a.transaction_dt >= b.departure_dt
        //delete from sourceDBName where transaction_dt > = departure_dt

        String queryPostDepartAdjustment = "  update a set Food_Beverage_Revenue = a.Food_Beverage_Revenue + b.Food_Beverage_Revenue, room_revenue = a.room_revenue + b.room_revenue, " +
                " total_revenue = a.total_revenue +b.total_revenue, Other_Revenue = a.Other_Revenue + b.Other_Revenue " +
                " from " + sourceDBName + " as a join " + sourceDBName + " as b on a.confirmation_number = b.confirmation_number " +
                " where a.transaction_dt = (select min(transaction_dt) from " + sourceDBName + " where confirmation_number = a.confirmation_number ) " +
                " and b.transaction_dt >= b.departure_dt ;" +
                " delete from " + sourceDBName + " where transaction_dt > = departure_dt ";
        numRowsUpdated += crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(queryPostDepartAdjustment));

        //changes for RT split sharers starts here
        numRowsUpdated += identifyPrimaryShareAndApplyStaticValuesConsiderRTChanges(sourceDBName);
        // adjust dates for sharers that dont split
        if (isOptimizeSharersSQL() && StringUtils.equals("opera.Sharers_OnlySharedTrans", sourceDBName)) {
            numRowsUpdated += adjustDatesForSharersWithTempTable(sourceDBName);
            LOGGER.info(String.format("adjustDatesForSharersWithTempTable::Number of rows updated in %s are %d", sourceDBName, numRowsUpdated));
        } else {
            numRowsUpdated += adjustDatesForSharers(sourceDBName);
            LOGGER.info(String.format("adjustDatesForSharers::Number of rows updated in %s are %d", sourceDBName, numRowsUpdated));
        }
        numRowsUpdated += rollUpSharersPerStayDateConsiderRTChanges(sourceDBName, toDBName);
        //adjust conf numbers for sharers to create one primary for continuity
        //consider sharer that splits for RT change, arrival and departures will be wrong in this case
        List<OperaTransaction> splitSharersWithAdjustedDates = adjustDatesForSharersThatSplit(toDBName);
        //now use these adjusted dates to update reservations
        if (isOptimizeSharersSQL()) {
            updateDatesForResolvedSharersBatch(toDBName, splitSharersWithAdjustedDates);
        } else {
            updateDatesForResolvedSharers(toDBName, splitSharersWithAdjustedDates);
        }
        if (cancelOrNoShowSharerPresent) {
            numRowsUpdated += adjustReservationStatusAndDatesForCancAndNOShowSharers();
        } else {
            numRowsUpdated += adjustReservationStatusAndDatesForSharersThatStay();
        }
        numRowsUpdated += mergeSharedTransactionsInStageTransactions(toDBName, dataLoadMetaDataIDTrans);
        return numRowsUpdated;
    }

    //Convert to batch query 26065

    private void updateDatesForResolvedSharers(String toDBName, List<OperaTransaction> splitSharersWithAdjustedDates) {
        String updateSplitSharersQuery = "update " + toDBName + " set arrival_dt = :arrivalDt , departure_dt = :deptDate where confirmation_number = :confNo and transaction_dt = :transDt ";
        for (OperaTransaction operaTransaction : splitSharersWithAdjustedDates) {
            crudService.executeUpdateByNativeQuery(forceLegacyCardinalityEstimator(updateSplitSharersQuery)
                    , QueryParameter.with("arrivalDt", operaTransaction.getArrivalDate())
                            .and("deptDate", operaTransaction.getDepartureDate())
                            .and("confNo", operaTransaction.getConfirmationNumber())
                            .and("transDt", operaTransaction.getTransactionDate()).parameters());
        }
    }

    private void updateDatesForResolvedSharersBatch(String toDBName, List<OperaTransaction> splitSharersWithAdjustedDates) {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            String updateSplitSharersQuery = "update " + toDBName + " set arrival_dt = ? , departure_dt = ? where confirmation_number = ? and transaction_dt = ? ";
            try (PreparedStatement preparedStatement = connection.prepareStatement(updateSplitSharersQuery)) {
                for (int i = 0; i < splitSharersWithAdjustedDates.size(); i++) {
                    OperaTransaction operaTransaction = splitSharersWithAdjustedDates.get(i);
                    preparedStatement.setDate(1, new Date(operaTransaction.getArrivalDate().toDate().getTime()));
                    preparedStatement.setDate(2, new Date(operaTransaction.getDepartureDate().toDate().getTime()));
                    preparedStatement.setString(3, operaTransaction.getConfirmationNumber());
                    preparedStatement.setDate(4, new Date(operaTransaction.getTransactionDate().toDate().getTime()));
                    preparedStatement.addBatch();
                    if (i > 0 && i % BATCH_SIZE == 0) {
                        preparedStatement.executeBatch();
                        LOGGER.info(String.format("Executed batch updateSplitSharersQuery %d", i));
                    }
                }
                preparedStatement.executeBatch();
                LOGGER.info("Executed batch updateSplitSharersQuery ");
            }
        } catch (Exception exception) {
            LOGGER.error("Error while updating Split Sharers information  ", exception);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "ERROR:Error while updating Split Sharers  information  ", exception);
        } finally {
            if (null != connection) {
                try {
                    jpaJdbcUtil.closeConnection(crudService, connection);
                } catch (Exception ignore) {
                    LOGGER.error("Could not close connection.", ignore);
                }
            }
        }
    }

    private List<OperaTransaction> adjustDatesForSharersThatSplit(String toDBName) {
        String findRTChangeSharers = "select sharers,confirmation_number,Reservation_Name_ID,transaction_dt,room_type from " + toDBName + " where sharers in (select distinct sharers from " + toDBName + " group by sharers having count(distinct room_type) >1) order by sharers,confirmation_number,transaction_dt";
        List<Object[]> allSharersWithRTChanges = crudService.findByNativeQuery(forceLegacyCardinalityEstimator(findRTChangeSharers));
        List<OperaTransaction> allSharerTransactionsWithRtChanges = new ArrayList<>();
        List<OperaTransaction> sharerTransactionsPerConfAndRT = new LinkedList<>();
        for (Object[] sharerWithRTChange : allSharersWithRTChanges) {
            OperaTransaction ot = new OperaTransaction();
            ot.setSharers(getStringValue(sharerWithRTChange[0]));
            ot.setConfirmationNumber(getStringValue(sharerWithRTChange[1]));
            ot.setReservationNameId(Integer.parseInt(getStringValue(sharerWithRTChange[2])));
            LocalDate transDate = new LocalDate(sharerWithRTChange[3]);
            ot.setTransactionDate(transDate);
            ot.setRoomType(getStringValue(sharerWithRTChange[4]));
            ot.setArrivalDate(transDate);
            ot.setDepartureDate(transDate.plusDays(1));
            int sharersWithSameRT = sharerTransactionsPerConfAndRT.size();
            if (sharersWithSameRT > 0) {
                OperaTransaction existingTransListWithSameRT = sharerTransactionsPerConfAndRT.get(sharersWithSameRT - 1);
                if (existingTransListWithSameRT.getConfirmationNumber().equalsIgnoreCase(ot.getConfirmationNumber())
                        && existingTransListWithSameRT.getRoomType().equalsIgnoreCase(ot.getRoomType())
                        && ot.getTransactionDate().compareTo(existingTransListWithSameRT.getTransactionDate().plusDays(1)) == 0
                ) {
                    ot.setArrivalDate(existingTransListWithSameRT.getArrivalDate());
                    LocalDate newDepartureDate = ot.getTransactionDate().plusDays(1);
                    ot.setDepartureDate(newDepartureDate);
                    //correct departure date for all existing trans in list
                    for (OperaTransaction operaTransaction : sharerTransactionsPerConfAndRT) {
                        operaTransaction.setDepartureDate(newDepartureDate);
                    }

                } else {
                    ot.setArrivalDate(ot.getTransactionDate());
                    ot.setDepartureDate(ot.getTransactionDate().plusDays(1));
                    //correct departure date for all existing trans in list
                    LocalDate newDepartureDate = existingTransListWithSameRT.getTransactionDate().plusDays(1);
                    for (OperaTransaction operaTransaction : sharerTransactionsPerConfAndRT) {
                        operaTransaction.setDepartureDate(newDepartureDate);
                    }
                    //add old RT transactions to the permanant list
                    allSharerTransactionsWithRtChanges.addAll(sharerTransactionsPerConfAndRT);
                    sharerTransactionsPerConfAndRT = new LinkedList<>();
                }
            }
            sharerTransactionsPerConfAndRT.add(ot);
        }
        allSharerTransactionsWithRtChanges.addAll(sharerTransactionsPerConfAndRT);
        return allSharerTransactionsWithRtChanges;
    }

    private boolean isOptimizeSharersSQL() {
        return pacmanConfigParamsService.getBooleanParameterValue(OPTIMIZE_OPERA_SHARERS_SQL);
    }

    private boolean isResolveSharersWithRtChanges() {
        return pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.RESOLVESHARERSWITH_RTCHANGES.value(OPERA));
    }
}
