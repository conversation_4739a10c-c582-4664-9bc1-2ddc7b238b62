package com.ideas.tetris.pacman.services.opera;

import com.ideas.g3.integration.opera.agent.dto.AccomTypeManualRestrictionDecision;
import com.ideas.g3.integration.opera.agent.dto.PropertyManualRestrictionDecision;
import com.ideas.g3.integration.opera.dto.AccomTypeManualRestrictionDecisionForOpera;
import com.ideas.g3.integration.opera.dto.PropertyManualRestrictionDecisionForOpera;

import java.math.BigDecimal;

public class ManualRestrictionDecisionConverter {
    private ManualRestrictionDecisionConverter() {
        // util class
    }

    public static PropertyManualRestrictionDecision convertToAgentSpec(PropertyManualRestrictionDecisionForOpera commonOperaDecision) {
        PropertyManualRestrictionDecision agentDecision = new PropertyManualRestrictionDecision();
        agentDecision.setUnSerializedDate(commonOperaDecision.getDate());
        agentDecision.setRestrictionCode(commonOperaDecision.getRestrictionCode());
        agentDecision.setValue(commonOperaDecision.getValue() == null ? null : BigDecimal.valueOf(commonOperaDecision.getValue()));
        agentDecision.setClearsPreviousRestriction(commonOperaDecision.isClearsPreviousRestriction());
        return agentDecision;
    }

    public static AccomTypeManualRestrictionDecision convertToAgentSpec(AccomTypeManualRestrictionDecisionForOpera commonOperaDecision) {
        AccomTypeManualRestrictionDecision agentDecision = new AccomTypeManualRestrictionDecision();
        agentDecision.setUnSerializedDate(commonOperaDecision.getDate());
        agentDecision.setAccomType(commonOperaDecision.getAccomType());
        agentDecision.setRestrictionCode(commonOperaDecision.getRestrictionCode());
        agentDecision.setValue(commonOperaDecision.getValue() == null ? null : BigDecimal.valueOf(commonOperaDecision.getValue()));
        agentDecision.setClearsPreviousRestriction(commonOperaDecision.isClearsPreviousRestriction());
        return agentDecision;
    }
}
