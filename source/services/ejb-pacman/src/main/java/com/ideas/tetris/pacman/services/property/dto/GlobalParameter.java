package com.ideas.tetris.pacman.services.property.dto;

import java.util.ArrayList;
import java.util.List;

public class GlobalParameter {
    private Integer entityId;
    private String name;
    private String nodeName;
    private String value;
    private String defaultValue;
    private Integer defaultPredefinedValueId;
    private Integer predefinedValueId;
    private ParameterValueType valueType;
    private List<PredefinedValue> predefinedValues;
    private ConfigOperation operation = ConfigOperation.VIEW;

    public Integer getEntityId() {
        return entityId;
    }

    public void setEntityId(Integer entityId) {
        this.entityId = entityId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public List<PredefinedValue> getPredefinedValues() {
        return predefinedValues;
    }

    public void setPredefinedValues(List<PredefinedValue> predefinedValues) {
        this.predefinedValues = predefinedValues;
    }

    public void addPredefinedValue(PredefinedValue predefinedValue) {
        if (predefinedValues == null) {
            predefinedValues = new ArrayList<PredefinedValue>();
        }
        predefinedValues.add(predefinedValue);
    }

    public ConfigOperation getOperation() {
        return operation;
    }

    public void setOperation(ConfigOperation operation) {
        this.operation = operation;
    }

    public ParameterValueType getValueType() {
        return valueType;
    }

    public void setValueType(ParameterValueType valueType) {
        this.valueType = valueType;
    }

    public Integer getPredefinedValueId() {
        return predefinedValueId;
    }

    public void setPredefinedValueId(Integer predefinedValueId) {
        this.predefinedValueId = predefinedValueId;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Integer getDefaultPredefinedValueId() {
        return defaultPredefinedValueId;
    }

    public void setDefaultPredefinedValueId(Integer defaultPredefinedValueId) {
        this.defaultPredefinedValueId = defaultPredefinedValueId;
    }
}
