package com.ideas.tetris.pacman.services.dialog;

import com.ideas.infra.tetris.security.EncryptionDecryption;
import com.ideas.infra.tetris.security.Password;
import com.ideas.infra.tetris.security.TetrisSecurityService;
import com.ideas.infra.tetris.security.sso.OpenAMException;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.LoginResultInfo;
import com.ideas.tetris.pacman.services.security.PasswordSecurityService;
import com.ideas.tetris.pacman.services.security.UserGlobalDBService;
import com.ideas.tetris.platform.common.errorhandling.LoginStatusCode;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import javax.xml.bind.DatatypeConverter;

import static com.ideas.tetris.pacman.common.constants.Constants.DB_REALM_SUFFIX;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class AccountLinkingService {

    @Autowired
	private UserGlobalDBService userGlobalDBService;

    @Autowired
	private PasswordSecurityService passwordSecurityService;

    private static final Logger LOGGER = Logger.getLogger(AccountLinkingService.class);

    public GlobalUser findGlobalUser(String username, String password) {
        GlobalUser user = userGlobalDBService.getGlobalUserByEmail(username);
        if (user == null) {
            return null;
        }
        String realm = user.getClientCode().toLowerCase();
        Integer userId = user.getId();
        String salt = userGlobalDBService.getSalt(userId.toString());
        Password encryptedPassword = EncryptionDecryption.encrypt(password, DatatypeConverter.parseHexBinary(salt));
        try {
            TetrisSecurityService.getOpenAMClient().authenticate(userId.toString(), encryptedPassword.getSaltedHash(), realm + DB_REALM_SUFFIX);
            return user;
        } catch (OpenAMException e) {
            LOGGER.error("Dialog authentication failed: ", e);
            return null;
        }
    }

    public String createToken(GlobalUser user) {
        final String token = EncryptionDecryption.doStrongTextEncryption(user.getId().toString() + "|" + user.getClientCode());
        LOGGER.info("AccountLinking UserId ====> " + user.getId().toString());
        LOGGER.info("AccountLinking Token ====> " + token);
        return token;
    }

    public String getErrorMessage(String user) {
        return getErrorType(passwordSecurityService.authenticate(false, user));
    }

    private String getErrorType(LoginResultInfo resultInfo) {
        switch (getLoginCode(resultInfo)) {
            case INVALID_CREDENTIALS:
                return "invalid.User";
            case INVALID_USER:
                return "login.invalidLogin";
            case ACCOUNT_DEACTIVED:
                return "accountDeactivated";
            case ACCOUNT_LOCKED_DUE_TO_WRONG_PASSWORD:
                return "accountDeactivatedWrongPassword";
            default:
                break;
        }
        return "";
    }

    private LoginStatusCode getLoginCode(LoginResultInfo resultInfo) {
        return LoginStatusCode.valueForId(resultInfo.getStatusCode());
    }
}


