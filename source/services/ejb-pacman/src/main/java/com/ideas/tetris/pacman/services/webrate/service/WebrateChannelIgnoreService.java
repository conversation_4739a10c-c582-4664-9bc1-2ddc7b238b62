package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateChannelIgnoreConfig;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateDefaultChannel;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.ideas.tetris.pacman.services.webrate.entity.WebrateDefaultChannel.ALL_WEBRATE_DEFAULT_CHANNEL_WITH_PRODUCT_NAME;
import static com.ideas.tetris.pacman.services.webrate.entity.WebrateOverrideChannel.ALL_WEBRATE_OVERRIDE_CHANNEL_WITH_PRODUCT_NAME;
import static com.ideas.tetris.platform.common.entity.DayOfWeek.*;
import static org.apache.commons.collections.CollectionUtils.isEmpty;

@Component
@Transactional
public class WebrateChannelIgnoreService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    private CrudService tenantCrudService;

    public List<WebrateChannelIgnoreConfig> getAllWebrateChannelsIgnored() {
        return tenantCrudService.findAll(WebrateChannelIgnoreConfig.class);
    }
    public List<Object[]> getAllWebrateDefaultChannel() {
        return tenantCrudService.findByNamedQuery(ALL_WEBRATE_DEFAULT_CHANNEL_WITH_PRODUCT_NAME);
    }
    public List<Object[]> getAllWebrateOverrideChannel() {
        return tenantCrudService.findByNamedQuery(ALL_WEBRATE_OVERRIDE_CHANNEL_WITH_PRODUCT_NAME);

    }

    public void save(List<WebrateChannelIgnoreConfig> webrateChannelIgnoreConfigs){
        tenantCrudService.save(webrateChannelIgnoreConfigs);
    }

    public void delete(WebrateChannelIgnoreConfig webrateChannelIgnoreConfigs) {
        tenantCrudService.delete(webrateChannelIgnoreConfigs);
    }

    public void delete(List<WebrateChannelIgnoreConfig> webrateChannelIgnoreConfigs) {
        tenantCrudService.delete(webrateChannelIgnoreConfigs);
    }

    public List<WebrateDefaultChannel> getDefaultChannelCfg() {
        return tenantCrudService.findByNamedQuery(WebrateDefaultChannel.ALL_CHANNELS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public int findLatestGroupId() {
        return (int) Optional.ofNullable(tenantCrudService.findByNamedQuerySingleResult(WebrateChannelIgnoreConfig.GET_LATEST_GROUP_ID)).orElse(1);
    }

    public Map<DayOfWeek, List<String>> getDayOfWeekWiseIgnoredChannels() {
        List<WebrateChannelIgnoreConfig> allActiveIgnoredWebrateChannels = tenantCrudService.findAll(WebrateChannelIgnoreConfig.class);
        if (isEmpty(allActiveIgnoredWebrateChannels)) {
            return Collections.emptyMap();
        }
        Map<DayOfWeek, List<String>> dayOfWeekWiseIgnoredChannels = new HashMap<>();
        allActiveIgnoredWebrateChannels.forEach(channel -> addChannelToMap(dayOfWeekWiseIgnoredChannels, channel));
        return dayOfWeekWiseIgnoredChannels;
    }

    private static void addChannelFor(Boolean ignoreChannelOnMonday, DayOfWeek dayOfWeek, String webrateChannelName, Map<DayOfWeek, List<String>> dayOfWeekWiseIgnoredChannels) {
        if (ignoreChannelOnMonday) {
            dayOfWeekWiseIgnoredChannels.computeIfAbsent(dayOfWeek, k -> new ArrayList<>()).add(webrateChannelName);
        }
    }

    private static void addChannelToMap(Map<DayOfWeek, List<String>> dayOfWeekWiseIgnoredChannels, WebrateChannelIgnoreConfig channel) {
        String webrateChannelName = channel.getWebrateChannel().getWebrateChannelName();
        addChannelFor(channel.getIgnoreChannelOnMonday(), MONDAY, webrateChannelName, dayOfWeekWiseIgnoredChannels);
        addChannelFor(channel.getIgnoreChannelOnTuesday(), TUESDAY, webrateChannelName, dayOfWeekWiseIgnoredChannels);
        addChannelFor(channel.getIgnoreChannelOnWednesday(), WEDNESDAY, webrateChannelName, dayOfWeekWiseIgnoredChannels);
        addChannelFor(channel.getIgnoreChannelOnThursday(), THURSDAY, webrateChannelName, dayOfWeekWiseIgnoredChannels);
        addChannelFor(channel.getIgnoreChannelOnFriday(), FRIDAY, webrateChannelName, dayOfWeekWiseIgnoredChannels);
        addChannelFor(channel.getIgnoreChannelOnSaturday(), SATURDAY, webrateChannelName, dayOfWeekWiseIgnoredChannels);
        addChannelFor(channel.getIgnoreChannelOnSunday(), SUNDAY, webrateChannelName, dayOfWeekWiseIgnoredChannels);
    }
}