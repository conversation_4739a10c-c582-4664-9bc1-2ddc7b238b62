package com.ideas.tetris.pacman.services.datafeed.rowmapper;

import com.ideas.tetris.pacman.services.datafeed.entity.OverbookingConfigurationEnhanced;
import com.ideas.tetris.platform.common.crudservice.RowMapper;

import java.math.BigDecimal;
import java.util.Date;

public class OverbookingConfigurationSpecialRTRowMapper implements RowMapper<OverbookingConfigurationEnhanced> {
    @Override
    public OverbookingConfigurationEnhanced mapRow(Object[] row) {
        OverbookingConfigurationEnhanced config = new OverbookingConfigurationEnhanced();

        config.setLevel((String) row[0]);
        config.setCategory((String) row[1]);
        config.setStartDate((Date) row[2]);
        config.setEndDate((Date) row[3]);
        config.setRoomTypeCode((String) row[4]);

        config.setSundayOverbookingType((String) row[5]);
        config.setMondayOverbookingType((String) row[6]);
        config.setTuesdayOverbookingType((String) row[7]);
        config.setWednesdayOverbookingType((String) row[8]);
        config.setThursdayOverbookingType((String) row[9]);
        config.setFridayOverbookingType((String) row[10]);
        config.setSaturdayOverbookingType((String) row[11]);

        config.setSundayLimit(getBigDecimalValue(row[12]));
        config.setMondayLimit(getBigDecimalValue(row[13]));
        config.setTuesdayLimit(getBigDecimalValue(row[14]));
        config.setWednesdayLimit(getBigDecimalValue(row[15]));
        config.setThursdayLimit(getBigDecimalValue(row[16]));
        config.setFridayLimit(getBigDecimalValue(row[17]));
        config.setSaturdayLimit(getBigDecimalValue(row[18]));

        config.setSpecialUseRoomType((String) row[19]);
        config.setDistributeUnsoldCapacity((String) row[20]);
        return config;
    }

    private BigDecimal getBigDecimalValue(Object rowValue) {
        return rowValue == null ? null : ((BigDecimal) rowValue);
    }

}
