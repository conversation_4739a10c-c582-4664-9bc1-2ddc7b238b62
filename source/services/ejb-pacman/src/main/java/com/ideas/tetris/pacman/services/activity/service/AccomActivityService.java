package com.ideas.tetris.pacman.services.activity.service;

import com.ideas.api.PmsInboundApiV2Client;
import com.ideas.api.client.inventory.InventoryV2Api;
import com.ideas.api.client.inventory.model.Inventory;
import com.ideas.api.client.inventory.model.RoomTypeInventory;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.accommodation.dto.AccomActivityDto;
import com.ideas.tetris.pacman.services.accommodation.dto.AccomClassSummaryDto;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.AccomAvailableCapacityDto;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.util.CollectionUtils;
import com.ideas.tetris.pacman.util.csv.CsvFileWriter;
import com.ideas.tetris.platform.common.rest.mapper.PlatformNGIRestClient;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.core.Response;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;

@Component
@Transactional
public class AccomActivityService {
    private static final Logger LOGGER = Logger.getLogger(AccomActivityService.class);
    @Autowired
	private AccomActivityRepository accomActivityRepository;
    @Autowired
	private AccommodationService accommodationService;
    @Autowired
	private DateService dateService;
    @Autowired
	private PlatformNGIRestClient platformNgiRestClient;

    public List<Inventory> transferRoomTypeActivity(LocalDate startDate, LocalDate endDate, String clientCode,
                                                    String propertyCode, String correlationId) {
        List<AccomActivity> activities = accomActivityRepository.findByDateRange(startDate, endDate);
        Map<Integer, String> accomTypeIdToAccomTypeCodeMapping =
                accommodationService.findAccomTypeIdToAccomTypeCodeMapping();
        var activityByOccupancyDate =
                activities.stream().collect(Collectors.groupingBy(AccomActivity::getOccupancyDate));
        var transactionDate = dateService.getCaughtUpLocalDateTime().toString();
        var inventories = activityByOccupancyDate.entrySet().stream()
                .map(e -> toInventory(e, clientCode, propertyCode, accomTypeIdToAccomTypeCodeMapping, transactionDate,
                        correlationId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(inventories)) {
            return Collections.emptyList();
        }
        PmsInboundApiV2Client clientV2API = platformNgiRestClient.getClientV2API();
        InventoryV2Api api2 = new InventoryV2Api(clientV2API);
        api2.postInventoriesForAProperty(clientCode, propertyCode, inventories);
        return inventories;
    }

    private Inventory toInventory(Map.Entry<Date, List<AccomActivity>> perDay, String clientCode,
                                  String propertyCode, Map<Integer, String> accomIdToCode, String transactionDate,
                                  String correlationId) {
        var occupancyDate = perDay.getKey();
        var accomActivities = perDay.getValue();
        var aggregate = accomActivities.stream()
                .reduce(RoomTypeInventoryInfo.IDENTITY, RoomTypeInventoryInfo::add, RoomTypeInventoryInfo::add);
        Function<ToIntFunction<RoomTypeInventoryInfo>, BigDecimal> toAggregateVal =
                f -> BigDecimal.valueOf(f.applyAsInt(aggregate));

        var inventory = new Inventory();
        inventory.clientCode(clientCode).propertyCode(propertyCode)
                .occupancyDate(LocalDateUtils.toJavaLocalDate(occupancyDate).toString())
                .correlationId(correlationId)
                .statisticsCorrelationId(correlationId)
                .transactionDate(transactionDate)
                .roomsNotAvailableMaintenance(toAggregateVal.apply(RoomTypeInventoryInfo::getRoomsNotAvailableMaint))
                .roomsNotAvailableOther(toAggregateVal.apply(RoomTypeInventoryInfo::getRoomsNotAvailableOther))
                .totalAccomCapacity(toAggregateVal.apply(RoomTypeInventoryInfo::getRoomCapacity))
                .roomSold(toAggregateVal.apply(RoomTypeInventoryInfo::getRoomsSold))
                .isFiscalDateEligible(Boolean.TRUE);
        inventory.setVirtualRoomTypeInventories(List.of());

        accomActivities.stream()
                .map(at -> toRoomTypeInventory(at, accomIdToCode))
                .forEach(inventory::addRoomTypeInventoriesItem);
        return inventory;
    }

    private RoomTypeInventory toRoomTypeInventory(AccomActivity accomActivity,
                                                  Map<Integer, String> accomCodeById) {
        RoomTypeInventory roomTypeInventory = new RoomTypeInventory();
        roomTypeInventory.setRoomTypeCode(accomCodeById.get(accomActivity.getAccomTypeId()));
        roomTypeInventory.setRoomSold(accomActivity.getRoomsSold());
        roomTypeInventory.setRoomsNotAvailableMaintenance(accomActivity.getRoomsNotAvailableMaintenance());
        roomTypeInventory.setRoomsNotAvailableOther(accomActivity.getRoomsNotAvailableOther());
        roomTypeInventory.setTotalAccomCapacity(accomActivity.getAccomCapacity());
        return roomTypeInventory;
    }

    public List<AccomClassSummaryDto> getAccomClassSummary(LocalDate startDate, LocalDate endDate) {
        List<AccomActivity> accomActivityList = accomActivityRepository.findByDateRange(startDate, endDate);
        List<AccomType> accomTypeList = accommodationService.getAllAccomTypeDetails();
        Map<Integer, Integer> accomTypeIdToClassIdMap = accomTypeList.stream()
                .collect(Collectors.toMap(AccomType::getId, accomType -> accomType.getAccomClass().getId()));

        return accomActivityList.stream()
                .map(act -> new AccomClassSummaryDto(
                        accomTypeIdToClassIdMap.get(act.getAccomTypeId()),
                        act.getAccomTypeId(),
                        LocalDateUtils.toJavaLocalDate(act.getOccupancyDate()),
                        act.getAccomCapacity().subtract(act.getRoomsNotAvailableOther()).subtract(act.getRoomsNotAvailableMaintenance()),
                        act.getAccomCapacity(),
                        act.getRoomsSold(),
                        act.getRoomRevenue()
                        ))
                .collect(Collectors.toList());
    }

    public List<AccomActivityDto> getAccomActivity(LocalDate startDate, LocalDate endDate) {
        List<AccomActivity> accomActivityList = accomActivityRepository.findByDateRange(startDate, endDate);

        return accomActivityList.stream()
                .map(aa -> new AccomActivityDto(
                        LocalDateUtils.toJavaLocalDateFromSQLDate(aa.getOccupancyDate()),
                        aa.getAccomTypeId(),
                        aa.getAccomCapacity(),
                        aa.getRoomsSold(),
                        aa.getRoomsNotAvailableMaintenance(),
                        aa.getRoomsNotAvailableOther(),
                        aa.getArrivals(),
                        aa.getNoShows(),
                        aa.getRoomRevenue()
                ))
                .collect(Collectors.toList());
    }

    public Response getAccomAvailableCapacity(LocalDate startDate, LocalDate endDate, Integer page, Integer size) {
        List<AccomAvailableCapacityDto> result = accomActivityRepository.getAccomActivityByOccupancyDate(startDate, endDate, page, size);
        try {
            File file = CsvFileWriter.write(result);
            Path path = Paths.get(file.getAbsolutePath());
            byte[] data = Files.readAllBytes(path);
            Response response = Response.ok(data, "text/csv")
                    .header("Content-Disposition", "attachment; filename=" + "accom_available_capacity.csv")
                    .build();
            Files.deleteIfExists(path);
            return response;
        } catch (IOException e) {
            LOGGER.error("Error While Downloading accom_available_capacity.csv", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).build();
        }
    }


    @AllArgsConstructor
    @Getter
    static class RoomTypeInventoryInfo {
        static final RoomTypeInventoryInfo IDENTITY = new RoomTypeInventoryInfo(0, 0, 0, 0);
        final int roomsSold;
        final int roomsNotAvailableMaint;
        final int roomsNotAvailableOther;
        final int roomCapacity;

        public RoomTypeInventoryInfo add(RoomTypeInventoryInfo other) {
            return new RoomTypeInventoryInfo(this.roomsSold + other.roomsSold,
                    this.roomsNotAvailableMaint + other.roomsNotAvailableMaint, this.roomsNotAvailableOther + other.roomsNotAvailableOther, this.roomCapacity + other.roomCapacity);
        }

        public RoomTypeInventoryInfo add(AccomActivity other) {
            return new RoomTypeInventoryInfo(this.roomsSold + other.getRoomsSold().intValue(),
                    this.roomsNotAvailableMaint + other.getRoomsNotAvailableMaintenance().intValue(),
                    this.roomsNotAvailableOther + other.getRoomsNotAvailableOther().intValue(),
                    this.roomCapacity + other.getAccomCapacity().intValue());
        }


    }

}
