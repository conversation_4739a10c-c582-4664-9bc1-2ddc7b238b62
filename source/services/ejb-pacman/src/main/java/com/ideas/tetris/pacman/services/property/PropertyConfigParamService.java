package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.Service;
import com.ideas.tetris.pacman.common.configparams.*;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.linkedsrp.service.LinkedSRPRateCalculationForStreamService;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.ideas.tetris.pacman.util.Runner.getOrDefault;

@Service
@Component
public class PropertyConfigParamService {

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    public boolean isCP() {
        return pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
    }

    public boolean isSPOptimize() {
        return pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_OPTIMIZED_PRICING_ENABLED);
    }

    public boolean isOptimizedST19DataFeedFetch() {
        return pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_OPTIMIZED_ST19_DATAFEED_FETCH_ENABLED);
    }

    public boolean isBADSPOptimize() {
        return pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_OPTIMIZED_BAD_ENABLED);
    }

    public boolean isMinLOSDecisionCreationEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.MIN_LOS_DECISION_CREATION_ENABLED);
    }

    public boolean useMonitoringReportServiceV2Enabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_MONITORING_REPORT_SERVICE_V2);
    }

    public boolean populateReferencePriceLatest() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.POPULATE_REFERENCE_PRICE_LATEST);
    }

    public String hiltonCPSRPName() {
        return pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.HILTON_CP_SRP_NAME);
    }

    public boolean isFedRoomsSrpEvaluationEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FED_ROOMS_SRP_EVALUTION_ENABLED);
    }

    public boolean isIncludeCancellationNoShowRevenueEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INCLUDE_NO_SHOW_CANCELLATION_REVENUE);
    }

    public boolean isIncludePseudoRevenueEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INCLUDE_PSEUDO_IN_REVENUE);
    }

    public boolean isDayUsePopulationEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.DAY_USE_POPULATION_ENABLED);
    }

    public boolean isDayUseRevenueAtAllLevel() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ADJUST_DAY_USE_REVENUE_ALL_LEVEL);
    }

    public boolean isExtendedStayProductConfigurationEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.EXTENDED_STAY_PRODUCT_CONFIGURATION_ENABLED);
    }

    public boolean isExtendedStayProductPricingRecommendationEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.EXTENDED_STAY_PRODUCT_PRICING_RECOMMENDATION_ENABLED);
    }

    public boolean isLDB() {
        return pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED);
    }

    public boolean isBenefitMeasurementEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.BENEFIT_MEASUREMENT_ENABLED);
    }

    public boolean isChannelForecastDashboardEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FORECAST_DASHBOARD_ENABLED);
    }

    public boolean isChannelRestrictionAdjustmentEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.CHANNEL_RESTRICTIONS_ADJUSTMENT);
    }

    public boolean isDailyBarFullRefreshEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.DAILY_BAR_FULL_REFRESH_ENABLED);
    }

    public boolean isHiltonRateProtectAutomationEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_RATE_PROTECT_AUTOMATION_ENABLED);
    }

    public boolean isIndependentProductsEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
    }

    public boolean isESA() {
        String externalSystem = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM);
        return ReservationSystem.REZVIEW.getConfigParameterValue().equalsIgnoreCase(externalSystem);
    }

    public boolean useS3RefreshableConnection() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_S3_REFRESHABLE_CONNECTION);
    }

    public boolean useZipCompression() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_ZIP_COMPRESSION_FOR_CRS);
    }

    public boolean multiUnitCutOffDateEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.MULTI_UNIT_CUT_OFF_DATE_ENABLED);
    }

    public Integer getMaxIndependentProducts() {
        return getOrDefault(
                () -> pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_INDEPENDENT_PRODUCTS.getParameterName()),
                () -> 4
        );
    }

    public boolean isHiltonStreamingPopulationEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_POPULATION_ENABLED);
    }

    public boolean isLinkedSrpCalculationChunkingEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.LINKED_SRP_STREAMING_CALCULATION_CHUNKING_ENABLED);
    }

    public boolean shouldGenerateRestrictionsInOneWay() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERATE_RESTRICTIONS_IN_ONE_WAY);
    }

    public boolean isReConfigureSelectiveAutomationEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RE_CONFIGURE_SELECTIVE_AUTOMATION_ENABLED);
    }

    public boolean isNewDataSelectionEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_DATA_SELECTION);
    }

    public boolean isMaxRateCodeSelectionEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MAX_RATE_CODE_SELECTION);
    }

    public boolean isPricePaceEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRICE_PACE);
    }

    public boolean isSpecialUseRoomTypesForDatafeedEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SPECIAL_USE_ROOM_TYPES_DATAFEED_ENABLED);
    }

    public boolean fixedDetailsContainsOffsets() {
        return !getOffsetBasedResolutionLevel().equals(LinkedSRPRateCalculationForStreamService.OffsetBasedResolutionLevel.NONE);
    }

    public Enum<LinkedSRPRateCalculationForStreamService.OffsetBasedResolutionLevel> getOffsetBasedResolutionLevel() {
        String offsetStatus = pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR);
        return LinkedSRPRateCalculationForStreamService.OffsetBasedResolutionLevel.valueOf(offsetStatus);
    }

    public boolean isNewDecisionConfigPageIsEnabled(){
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_DECISION_CONFIG_PAGE);
    }

    public boolean isHiltonChannelRestrictionGlobalAdjustmentsEnabled() {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.HILTON_CHANNEL_RESTRICTION_GLOBAL_ADJUSTMENTS.value()));
    }

    public boolean isOptimizedMarketSegmentST2yDataFeedFetch() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_OPTIMISED_MARKET_SEGMENT_ST2_DATAFEED_ENABLED);
    }
}
