package com.ideas.tetris.pacman.services.datafeed.dto.grouppricing;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.services.functionspace.evaluation.results.adjustedoutput.AdjustmentOutputUtil;
import com.ideas.tetris.pacman.services.functionspace.evaluation.results.adjustedoutput.UserAdjustmentArrivalDateWrapper;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingEvaluationMethod;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.GroupEvaluationArrivalDateDisplacementAndForecastDetail;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.GroupEvaluationArrivalDateDisplacementAndForecastDetailBuilder;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluation;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationArrivalDate;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationArrivalDateForecastGroupDateAC;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.rest.unmarshaller.DateSerializer;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupPricingMaterializationStatus.getCaptionFor;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.round;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.zeroIfNull;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.toDate;
import static java.lang.Boolean.parseBoolean;
import static java.lang.Integer.parseInt;
import static org.apache.commons.lang.StringUtils.EMPTY;
import static org.apache.commons.lang.StringUtils.isEmpty;

public class GroupEvaluationData {

    private String groupName;
    private int uniqueGroupId;
    private Date evaluatedOn;
    private String salesperson;
    private String materialization;
    private boolean multiPropertyEvaluation;
    private boolean preferredDate;
    private String marketSegmentCode;
    private Date arrivalDate;
    private int numberOfNights;
    private String evaluationMethod;
    private String roomTypeCode;
    private int numberOfRooms;
    private BigDecimal recommendedRate;
    private BigDecimal adjustedRate;
    private BigDecimal breakEvenRate;
    private BigDecimal averageMAR;
    private BigDecimal displacedRooms;
    private BigDecimal incrementalRooms;
    private BigDecimal grossRevenueRooms;
    private BigDecimal costRooms;
    private BigDecimal netRevenueRooms;
    private BigDecimal grossProfitRooms;
    private BigDecimal displacedRevenueRooms;
    private BigDecimal displacedProfitRooms;
    private BigDecimal costOfWalk;
    private BigDecimal netProfitRooms;
    private BigDecimal netProfitPercentRooms;
    private BigDecimal grossRevenueAncillary;
    private BigDecimal netRevenueAncillary;
    private BigDecimal grossProfitAncillary;
    private BigDecimal displacedRevenueAncillary;
    private BigDecimal displacedProfitAncillary;
    private BigDecimal netProfitAncillary;
    private BigDecimal netProfitPercentAncillary;
    private BigDecimal grossRevenueConferenceAndBanquet;
    private BigDecimal totalCostConferenceAndBanquet;
    private BigDecimal netRevenueConferenceAndBanquet;
    private BigDecimal grossProfitConferenceAndBanquet;
    private BigDecimal netProfitConferenceAndBanquet;
    private BigDecimal netProfitPercentConferenceAndBanquet;
    private BigDecimal grossRevenueTotal;
    private BigDecimal costTotal;
    private BigDecimal netRevenueTotal;
    private BigDecimal grossProfitTotal;
    private BigDecimal displacedRevenueTotal;
    private BigDecimal displacedProfitTotal;
    private BigDecimal netProfitTotal;
    private BigDecimal netProfitPercent;
    private BigDecimal adjustGrossRevenueTotal;
    private BigDecimal adjustGrossProfitTotal;
    private BigDecimal adjustNetProfitTotal;
    private BigDecimal adjustNetProfitPercentTotal;
    private BigDecimal rateContracted;
    private String notes;

    private String salesCateringBookingId;

    // fields for internal use - not exposed
    private GroupEvaluation groupEvaluation;
    private GroupEvaluationArrivalDate groupEvaluationArrivalDate;

    public GroupEvaluationData() {
    }

    public void setUniqueGroupId(int uniqueGroupId) {
        this.uniqueGroupId = uniqueGroupId;
    }

    public void setEvaluatedOn(Date evaluatedOn) {
        this.evaluatedOn = evaluatedOn;
    }

    public void setMaterialization(String materialization) {
        this.materialization = materialization;
    }

    public void setMultiPropertyEvaluation(boolean multiPropertyEvaluation) {
        this.multiPropertyEvaluation = multiPropertyEvaluation;
    }

    public void setPreferredDate(boolean preferredDate) {
        this.preferredDate = preferredDate;
    }

    public void setMarketSegmentCode(String marketSegmentCode) {
        this.marketSegmentCode = marketSegmentCode;
    }

    public void setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public void setNumberOfNights(int numberOfNights) {
        this.numberOfNights = numberOfNights;
    }

    public void setBreakEvenRate(BigDecimal breakEvenRate) {
        this.breakEvenRate = breakEvenRate;
    }

    public void setAverageMAR(BigDecimal averageMAR) {
        this.averageMAR = averageMAR;
    }

    public void setDisplacedRooms(BigDecimal displacedRooms) {
        this.displacedRooms = displacedRooms;
    }

    public void setGrossRevenueRooms(BigDecimal grossRevenueRooms) {
        this.grossRevenueRooms = grossRevenueRooms;
    }

    public void setNetRevenueRooms(BigDecimal netRevenueRooms) {
        this.netRevenueRooms = netRevenueRooms;
    }

    public void setGrossProfitRooms(BigDecimal grossProfitRooms) {
        this.grossProfitRooms = grossProfitRooms;
    }

    public void setDisplacedRevenueRooms(BigDecimal displacedRevenueRooms) {
        this.displacedRevenueRooms = displacedRevenueRooms;
    }

    public void setDisplacedProfitRooms(BigDecimal displacedProfitRooms) {
        this.displacedProfitRooms = displacedProfitRooms;
    }

    public void setCostOfWalk(BigDecimal costOfWalk) {
        this.costOfWalk = costOfWalk;
    }

    public void setNetProfitRooms(BigDecimal netProfitRooms) {
        this.netProfitRooms = netProfitRooms;
    }

    public void setNetProfitPercentRooms(BigDecimal netProfitPercentRooms) {
        this.netProfitPercentRooms = netProfitPercentRooms;
    }

    public void setGrossRevenueAncillary(BigDecimal grossRevenueAncillary) {
        this.grossRevenueAncillary = grossRevenueAncillary;
    }

    public void setNetRevenueAncillary(BigDecimal netRevenueAncillary) {
        this.netRevenueAncillary = netRevenueAncillary;
    }

    public void setGrossProfitAncillary(BigDecimal grossProfitAncillary) {
        this.grossProfitAncillary = grossProfitAncillary;
    }

    public void setDisplacedRevenueAncillary(BigDecimal displacedRevenueAncillary) {
        this.displacedRevenueAncillary = displacedRevenueAncillary;
    }

    public void setDisplacedProfitAncillary(BigDecimal displacedProfitAncillary) {
        this.displacedProfitAncillary = displacedProfitAncillary;
    }

    public void setNetProfitAncillary(BigDecimal netProfitAncillary) {
        this.netProfitAncillary = netProfitAncillary;
    }

    public void setNetProfitPercentAncillary(BigDecimal netProfitPercentAncillary) {
        this.netProfitPercentAncillary = netProfitPercentAncillary;
    }

    public void setGrossRevenueConferenceAndBanquet(BigDecimal grossRevenueConferenceAndBanquet) {
        this.grossRevenueConferenceAndBanquet = grossRevenueConferenceAndBanquet;
    }

    public void setTotalCostConferenceAndBanquet(BigDecimal totalCostConferenceAndBanquet) {
        this.totalCostConferenceAndBanquet = totalCostConferenceAndBanquet;
    }

    public void setNetRevenueConferenceAndBanquet(BigDecimal netRevenueConferenceAndBanquet) {
        this.netRevenueConferenceAndBanquet = netRevenueConferenceAndBanquet;
    }

    public void setGrossProfitConferenceAndBanquet(BigDecimal grossProfitConferenceAndBanquet) {
        this.grossProfitConferenceAndBanquet = grossProfitConferenceAndBanquet;
    }

    public void setNetProfitConferenceAndBanquet(BigDecimal netProfitConferenceAndBanquet) {
        this.netProfitConferenceAndBanquet = netProfitConferenceAndBanquet;
    }

    public void setNetProfitPercentConferenceAndBanquet(BigDecimal netProfitPercentConferenceAndBanquet) {
        this.netProfitPercentConferenceAndBanquet = netProfitPercentConferenceAndBanquet;
    }

    public void setGrossRevenueTotal(BigDecimal grossRevenueTotal) {
        this.grossRevenueTotal = grossRevenueTotal;
    }

    public void setNetRevenueTotal(BigDecimal netRevenueTotal) {
        this.netRevenueTotal = netRevenueTotal;
    }

    public void setGrossProfitTotal(BigDecimal grossProfitTotal) {
        this.grossProfitTotal = grossProfitTotal;
    }

    public void setDisplacedRevenueTotal(BigDecimal displacedRevenueTotal) {
        this.displacedRevenueTotal = displacedRevenueTotal;
    }

    public void setDisplacedProfitTotal(BigDecimal displacedProfitTotal) {
        this.displacedProfitTotal = displacedProfitTotal;
    }

    public void setNetProfitTotal(BigDecimal netProfitTotal) {
        this.netProfitTotal = netProfitTotal;
    }

    public void setNetProfitPercent(BigDecimal netProfitPercent) {
        this.netProfitPercent = netProfitPercent;
    }

    public void setRateContracted(BigDecimal rateContracted) {
        this.rateContracted = rateContracted;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    private BigDecimal getBigDecimalWithPrecisionTwo(BigDecimal value) {
        return BigDecimalUtil.round(value, 2);
    }

    public GroupEvaluationData(GroupEvaluationArrivalDate groupEvaluationArrivalDate, String fullName) {
        // set fields for internal use
        this.groupEvaluationArrivalDate = groupEvaluationArrivalDate;
        this.groupEvaluation = groupEvaluationArrivalDate.getGroupEvaluation();

        setCoreFields(fullName);
        setRoomLevelCosts();
        setAncillaryCosts();
        setConferenceAndBanquetCosts();
        setTotalCosts();
        setUserAdjustedCosts();
    }

    @VisibleForTesting
    void setCoreFields(String fullName) {
        salesperson = fullName;
        groupName = groupEvaluation.getGroupName();
        uniqueGroupId = groupEvaluation.getId();
        evaluatedOn = groupEvaluation.getEvaluationDate().toDate();
        materialization = groupEvaluation.getMaterializationStatus().getCaption();
        multiPropertyEvaluation = groupEvaluation.getGroupEvaluationMultiId() != null;
        preferredDate = groupEvaluationArrivalDate.isPreferredDate();
        marketSegmentCode = groupEvaluation.getMarketSegmentCode();
        arrivalDate = groupEvaluationArrivalDate.getArrivalDate().toDate();
        numberOfNights = groupEvaluation.getNumberOfNights();
        if (GroupPricingEvaluationMethod.ROH == groupEvaluation.getEvaluationMethod()) {
            numberOfRooms = groupEvaluation.getTotalNumberOfRooms();
        }
        breakEvenRate = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getBreakEvenRate());
        averageMAR = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getAverageWeightedMAR());
        rateContracted = getBigDecimalWithPrecisionTwo(groupEvaluation.getContractedRate());
        notes = groupEvaluation.getNotes();
        salesCateringBookingId = groupEvaluation.getSalesCateringBookingId();
    }

    private void setTotalCosts() {
        grossRevenueTotal = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getTotalGrossRevenue());
        netRevenueTotal = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getTotalNetRevenue());
        grossProfitTotal = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getTotalGrossProfit());
        displacedRevenueTotal = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getTotalDisplacedRevenue());
        displacedProfitTotal = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getTotalDisplacedProfit(true));
        netProfitTotal = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getTotalNetProfit(true));
        netProfitPercent = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getTotalNetProfitPercentage(true));
        costTotal = computeCostTotal();
    }

    private void setRoomLevelCosts() {
        grossRevenueRooms = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getRoomGrossRevenue());
        netRevenueRooms = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getRoomNetRevenue());
        grossProfitRooms = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getRoomGrossProfit());
        displacedRevenueRooms = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getRoomDisplacedRevenue());
        displacedProfitRooms = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getRoomDisplacedProfit(true));
        costOfWalk = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getCostOfWalk());
        netProfitRooms = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getRoomNetProfit(true));
        netProfitPercentRooms = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getRoomNetProfitPercentage(true));
        costRooms = computeCostRooms();
    }

    private void setUserAdjustedCosts() {
        adjustGrossRevenueTotal = computeAdjustGrossRevenueTotal(grossRevenueTotal);
        adjustGrossProfitTotal = computeAdjustGrossProfitTotal(grossProfitTotal);
        adjustNetProfitTotal = computeAdjustNetProfitTotal(netProfitTotal);
        adjustNetProfitPercentTotal = computeUserAdjustedNetProfitPercentage(netProfitPercent);
    }

    private void setConferenceAndBanquetCosts() {
        grossRevenueConferenceAndBanquet = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getConferenceAndBanquetGrossRevenue());
        totalCostConferenceAndBanquet = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getGroupPricingConferenceAndBanquetCommissionRevenue());
        netRevenueConferenceAndBanquet = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getConferenceAndBanquetNetRevenue());
        grossProfitConferenceAndBanquet = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getConferenceAndBanquetGrossProfit());
        netProfitConferenceAndBanquet = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getConferenceAndBanquetNetProfit());
        netProfitPercentConferenceAndBanquet = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getConferenceAndBanquetProfitNetPercentage());
    }

    private void setAncillaryCosts() {
        grossRevenueAncillary = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getAncillaryGrossRevenue());
        netRevenueAncillary = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getAncillaryNetRevenue());
        grossProfitAncillary = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getAncillaryGrossProfit());
        displacedRevenueAncillary = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getDisplacedAncillaryRevenue());
        displacedProfitAncillary = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getDisplacedAncillaryProfit());
        netProfitAncillary = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getAncillaryNetProfit());
        netProfitPercentAncillary = getBigDecimalWithPrecisionTwo(groupEvaluationArrivalDate.getAncillaryNetProfitPercentage());
    }


    public void setSalesperson(String salesperson) {
        this.salesperson = salesperson;
    }

    public void setEvaluationMethod(String evaluationMethod) {
        this.evaluationMethod = evaluationMethod;
    }

    public void setRoomTypeCode(String roomTypeCode) {
        this.roomTypeCode = roomTypeCode;
    }

    public void setRecommendedRate(BigDecimal recommendedRate) {
        this.recommendedRate = recommendedRate;
    }

    public void setAdjustedRate(BigDecimal adjustedRate) {
        this.adjustedRate = adjustedRate;
    }

    public void setIncrementalRooms(BigDecimal incrementalRooms) {
        this.incrementalRooms = incrementalRooms;
    }

    public void setCostRooms(BigDecimal costRooms) {
        this.costRooms = costRooms;
    }

    public void setCostTotal(BigDecimal costTotal) {
        this.costTotal = costTotal;
    }

    public void setAdjustGrossRevenueTotal(BigDecimal adjustGrossRevenueTotal) {
        this.adjustGrossRevenueTotal = adjustGrossRevenueTotal;
    }

    public void setAdjustGrossProfitTotal(BigDecimal adjustGrossProfitTotal) {
        this.adjustGrossProfitTotal = adjustGrossProfitTotal;
    }

    public void setAdjustNetProfitTotal(BigDecimal adjustNetProfitTotal) {
        this.adjustNetProfitTotal = adjustNetProfitTotal;
    }

    public void setAdjustNetProfitPercentTotal(BigDecimal adjustNetProfitPercentTotal) {
        this.adjustNetProfitPercentTotal = adjustNetProfitPercentTotal;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public int getUniqueGroupId() {
        return uniqueGroupId;
    }

    @JsonSerialize(using = DateSerializer.class)
    public Date getEvaluatedOn() {
        return evaluatedOn;
    }

    public String getSalesperson() {
        return salesperson;
    }

    public String getMaterialization() {
        return materialization;
    }

    public boolean isMultiPropertyEvaluation() {
        return multiPropertyEvaluation;
    }

    public boolean isPreferredDate() {
        return preferredDate;
    }

    public String getMarketSegmentCode() {
        return marketSegmentCode;
    }

    @JsonSerialize(using = DateSerializer.class)
    public Date getArrivalDate() {
        return arrivalDate;
    }

    public int getNumberOfNights() {
        return numberOfNights;
    }

    public String getEvaluationMethod() {
        return evaluationMethod;
    }

    public String getRoomTypeCode() {
        return roomTypeCode;
    }

    public int getNumberOfRooms() {
        return numberOfRooms;
    }

    public void setNumberOfRooms(int numberOfRooms) {
        this.numberOfRooms = numberOfRooms;
    }

    public BigDecimal getRecommendedRate() {
        return recommendedRate;
    }

    public BigDecimal getAdjustedRate() {
        return adjustedRate;
    }

    public BigDecimal getBreakEvenRate() {
        return breakEvenRate;
    }

    public BigDecimal getAverageMAR() {
        return averageMAR;
    }

    public BigDecimal getDisplacedRooms() {
        return displacedRooms;
    }

    public BigDecimal getIncrementalRooms() {
        return incrementalRooms;
    }

    public BigDecimal getGrossRevenueRooms() {
        return grossRevenueRooms;
    }

    public BigDecimal getCostRooms() {
        return costRooms;
    }

    public BigDecimal getNetRevenueRooms() {
        return netRevenueRooms;
    }

    public BigDecimal getGrossProfitRooms() {
        return grossProfitRooms;
    }

    public BigDecimal getDisplacedRevenueRooms() {
        return displacedRevenueRooms;
    }

    public BigDecimal getDisplacedProfitRooms() {
        return displacedProfitRooms;
    }

    public BigDecimal getCostOfWalk() {
        return costOfWalk;
    }

    public BigDecimal getNetProfitRooms() {
        return netProfitRooms;
    }

    public BigDecimal getNetProfitPercentRooms() {
        return netProfitPercentRooms;
    }

    public BigDecimal getGrossRevenueAncillary() {
        return grossRevenueAncillary;
    }

    public BigDecimal getNetRevenueAncillary() {
        return netRevenueAncillary;
    }

    public BigDecimal getGrossProfitAncillary() {
        return grossProfitAncillary;
    }

    public BigDecimal getDisplacedRevenueAncillary() {
        return displacedRevenueAncillary;
    }

    public BigDecimal getDisplacedProfitAncillary() {
        return displacedProfitAncillary;
    }

    public BigDecimal getNetProfitAncillary() {
        return netProfitAncillary;
    }

    public BigDecimal getNetProfitPercentAncillary() {
        return netProfitPercentAncillary;
    }

    public BigDecimal getGrossRevenueConferenceAndBanquet() {
        return grossRevenueConferenceAndBanquet;
    }

    public BigDecimal getTotalCostConferenceAndBanquet() {
        return totalCostConferenceAndBanquet;
    }

    public BigDecimal getNetRevenueConferenceAndBanquet() {
        return netRevenueConferenceAndBanquet;
    }

    public BigDecimal getGrossProfitConferenceAndBanquet() {
        return grossProfitConferenceAndBanquet;
    }

    public BigDecimal getNetProfitConferenceAndBanquet() {
        return netProfitConferenceAndBanquet;
    }

    public BigDecimal getNetProfitPercentConferenceAndBanquet() {
        return netProfitPercentConferenceAndBanquet;
    }

    public BigDecimal getGrossRevenueTotal() {
        return grossRevenueTotal;
    }

    public BigDecimal getCostTotal() {
        return costTotal;
    }

    public BigDecimal getNetRevenueTotal() {
        return netRevenueTotal;
    }

    public BigDecimal getGrossProfitTotal() {
        return grossProfitTotal;
    }

    public BigDecimal getDisplacedRevenueTotal() {
        return displacedRevenueTotal;
    }

    public BigDecimal getDisplacedProfitTotal() {
        return displacedProfitTotal;
    }

    public BigDecimal getNetProfitTotal() {
        return netProfitTotal;
    }

    public BigDecimal getNetProfitPercent() {
        return netProfitPercent;
    }

    public BigDecimal getAdjustGrossRevenueTotal() {
        return adjustGrossRevenueTotal;
    }

    public BigDecimal getAdjustGrossProfitTotal() {
        return adjustGrossProfitTotal;
    }

    public BigDecimal getAdjustNetProfitTotal() {
        return adjustNetProfitTotal;
    }

    public BigDecimal getAdjustNetProfitPercentTotal() {
        return adjustNetProfitPercentTotal;
    }

    public BigDecimal getRateContracted() {
        return rateContracted;
    }

    public String getNotes() {
        return notes;
    }

    private BigDecimal computeCostRooms() {
        return getBigDecimalWithPrecisionTwo(AdjustmentOutputUtil.getTotalCostWithoutPerRoomServicingCostBeforeAdjustment(new UserAdjustmentArrivalDateWrapper(groupEvaluationArrivalDate)));
    }

    @VisibleForTesting
    public BigDecimal computeCostTotal() {
        BigDecimal totalCommissionRevenue = groupEvaluationArrivalDate.getFunctionSpaceCommissionRevenue()
                .add(groupEvaluationArrivalDate.getGroupPricingConferenceAndBanquetCommissionRevenue())
                .add(groupEvaluationArrivalDate.getFunctionSpaceCost());
        BigDecimal totalConcessionRevenue = groupEvaluationArrivalDate.getConferenceAndBanquetConcessionRevenue();

        BigDecimal totalCost = totalCommissionRevenue.add(totalConcessionRevenue)
                .add(AdjustmentOutputUtil.getTotalCostWithoutPerRoomServicingCostBeforeAdjustment(
                        new UserAdjustmentArrivalDateWrapper(groupEvaluationArrivalDate)));

        return getBigDecimalWithPrecisionTwo(totalCost);
    }

    private BigDecimal computeAdjustGrossRevenueTotal(BigDecimal systemValue) {
        if (groupEvaluationArrivalDate.hasUserAdjustedOutput()) {
            UserAdjustmentArrivalDateWrapper wrapper = new UserAdjustmentArrivalDateWrapper(groupEvaluationArrivalDate);
            return getBigDecimalWithPrecisionTwo(wrapper.getUserAdjustedTotalGrossRevenue());
        }
        return systemValue;
    }

    private BigDecimal computeAdjustGrossProfitTotal(BigDecimal systemValue) {
        if (groupEvaluationArrivalDate.hasUserAdjustedOutput()) {
            UserAdjustmentArrivalDateWrapper wrapper = new UserAdjustmentArrivalDateWrapper(groupEvaluationArrivalDate);
            return getBigDecimalWithPrecisionTwo(wrapper.getTotalUserAdjustedGrossProfit());
        }
        return systemValue;
    }

    private BigDecimal computeAdjustNetProfitTotal(BigDecimal systemValue) {
        if (groupEvaluationArrivalDate.hasUserAdjustedOutput()) {
            return getBigDecimalWithPrecisionTwo(AdjustmentOutputUtil.getUserAdjustedNetProfit(groupEvaluationArrivalDate));
        }
        return systemValue;
    }

    private BigDecimal computeUserAdjustedNetProfitPercentage(BigDecimal systemValue) {
        if (groupEvaluationArrivalDate.hasUserAdjustedOutput()) {
            return getBigDecimalWithPrecisionTwo(AdjustmentOutputUtil.getUserAdjustedNetProfitPercentage(groupEvaluationArrivalDate));
        }
        return systemValue;
    }

    public static List<GroupEvaluationArrivalDateDisplacementAndForecastDetail> computeDisplacedAndForecastDetailsForROH(
            GroupEvaluationArrivalDate groupEvaluationArrivalDate) {
        List<GroupEvaluationArrivalDateDisplacementAndForecastDetail> displacementAndForecastDetails =
                new ArrayList<>(groupEvaluationArrivalDate.buildDayOfStayGroupEvaluationArrivalDateDisplacementAndForecastDetails());
        displacementAndForecastDetails.add(
                groupEvaluationArrivalDate.buildRolledUpPreStayGroupEvaluationArrivalDateDisplacementAndForecastDetail());
        displacementAndForecastDetails.add(
                groupEvaluationArrivalDate.buildRolledUpPostStayGroupEvaluationArrivalDateDisplacementAndForecastDetail());
        return displacementAndForecastDetails;
    }

    public static List<GroupEvaluationArrivalDateDisplacementAndForecastDetail> computeDisplacedAndForecastDetailsForRC(
            GroupEvaluationArrivalDate groupEvaluationArrivalDate, List<GroupEvaluationArrivalDateForecastGroupDateAC> groupEvaluationArrivalDateForecastGroupDates) {
        List<GroupEvaluationArrivalDateDisplacementAndForecastDetail> displacementAndForecastDetails =
                new ArrayList<>(GroupEvaluationArrivalDateDisplacementAndForecastDetailBuilder.
                        buildByDayOfStayForDataFeedRoomClass(groupEvaluationArrivalDate, groupEvaluationArrivalDateForecastGroupDates));
        displacementAndForecastDetails.add(GroupEvaluationArrivalDateDisplacementAndForecastDetailBuilder.
                buildPreStayRollUpForDataFeedRoomClass(groupEvaluationArrivalDate, groupEvaluationArrivalDateForecastGroupDates));
        displacementAndForecastDetails.add(GroupEvaluationArrivalDateDisplacementAndForecastDetailBuilder.
                buildPostStayRollUpDataFeedRoomClass(groupEvaluationArrivalDate, groupEvaluationArrivalDateForecastGroupDates));
        return displacementAndForecastDetails;
    }

    public void computeDisplacedRooms(List<GroupEvaluationArrivalDateDisplacementAndForecastDetail> displacementAndForecastDetails) {
        BigDecimal totalDisplacedRooms = BigDecimal.ZERO;
        for (GroupEvaluationArrivalDateDisplacementAndForecastDetail detail : displacementAndForecastDetails) {
            totalDisplacedRooms = totalDisplacedRooms.add(BigDecimalUtil.zeroIfNull(detail.getNumberOfDisplacedRooms()));
        }
        displacedRooms = getBigDecimalWithPrecisionTwo(totalDisplacedRooms);
    }

    public void computeIncrementalRooms(List<GroupEvaluationArrivalDateDisplacementAndForecastDetail> displacementAndForecastDetails) {
        BigDecimal totalIncrementalRooms = BigDecimal.ZERO;
        for (GroupEvaluationArrivalDateDisplacementAndForecastDetail detail : displacementAndForecastDetails) {
            totalIncrementalRooms = totalIncrementalRooms.add(BigDecimalUtil.zeroIfNull(detail.getNumberOfIncrementalRooms()));
        }
        incrementalRooms = getBigDecimalWithPrecisionTwo(totalIncrementalRooms);
    }

    public static GroupEvaluationData getGroupEvaluationDataRowMapper(Object[] row) {
        final GroupEvaluationData ge = new GroupEvaluationData();
        int i = 0;

        ge.setGroupName(getStringAt(row, i));
        ge.setUniqueGroupId(parseInt(getStringAt(row, ++i)));
        ge.setEvaluatedOn(toDate(getStringAt(row, ++i)));
        ge.setMaterialization(getCaptionFor(parseInt(getStringAt(row, ++i))));

        ge.setMultiPropertyEvaluation(parseBoolean(getStringAt(row, ++i)));
        ge.setPreferredDate(parseBoolean(getStringAt(row, ++i)));
        ge.setMarketSegmentCode(getStringAt(row, ++i));
        ge.setArrivalDate(toDate(getStringAt(row, ++i)));
        ge.setEvaluationMethod(getStringAt(row, ++i));

        ge.setRoomTypeCode(getStringAt(row, ++i));
        ge.setRecommendedRate(getBigDecimalAt(row, ++i));
        ge.setAdjustedRate(zeroIfNull(getBigDecimalAt(row, ++i)));
        ge.setBreakEvenRate(getBigDecimalAt(row, ++i));
        ge.setAverageMAR(getBigDecimalAt(row, ++i));


        ge.setGrossRevenueRooms(getBigDecimalAt(row, ++i));
        ge.setNetRevenueRooms(getBigDecimalAt(row, ++i));
        ge.setGrossProfitRooms(getBigDecimalAt(row, ++i));
        ge.setDisplacedRevenueRooms(getBigDecimalAt(row, ++i));
        ge.setDisplacedProfitRooms(getBigDecimalAt(row, ++i));

        ge.setCostOfWalk(getBigDecimalAt(row, ++i));
        ge.setNetProfitRooms(getBigDecimalAt(row, ++i));
        ge.setNetProfitPercentRooms(getBigDecimalAt(row, ++i));
        ge.setGrossRevenueAncillary(getBigDecimalAt(row, ++i));
        ge.setNetRevenueAncillary(getBigDecimalAt(row, ++i));

        ge.setGrossProfitAncillary(getBigDecimalAt(row, ++i));
        ge.setDisplacedRevenueAncillary(getBigDecimalAt(row, ++i));
        ge.setDisplacedProfitAncillary(getBigDecimalAt(row, ++i));
        ge.setNetProfitAncillary(getBigDecimalAt(row, ++i));
        ge.setNetProfitPercentAncillary(getBigDecimalAt(row, ++i));

        ge.setGrossRevenueConferenceAndBanquet(getBigDecimalAt(row, ++i));
        ge.setGrossProfitConferenceAndBanquet(getBigDecimalAt(row, ++i));
        ge.setNetProfitConferenceAndBanquet(getBigDecimalAt(row, ++i));
        ge.setNetProfitPercentConferenceAndBanquet(getBigDecimalAt(row, ++i));
        ge.setRateContracted(zeroIfNull(getBigDecimalAt(row, ++i)));
        ge.setNotes(getStringAt(row, ++i));
        ge.setSalesCateringBookingId(getStringAt(row, ++i));

        return ge;
    }

    public static BigDecimal getBigDecimalAt(Object[] row, int i) {
        final String value = getStringAt(row, i);
        return isEmpty(value) ? null : round(new BigDecimal(value), 2);
    }

    public static String getStringAt(Object[] row, int i) {
        return Optional.ofNullable(row[i]).map(String::valueOf).orElse(EMPTY);
    }

    public String getSalesCateringBookingId() {
        return salesCateringBookingId;
    }

    public void setSalesCateringBookingId(String salesCateringBookingId) {
        this.salesCateringBookingId = salesCateringBookingId;
    }
}
