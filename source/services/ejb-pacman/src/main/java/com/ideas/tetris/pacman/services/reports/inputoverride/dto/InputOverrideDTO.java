package com.ideas.tetris.pacman.services.reports.inputoverride.dto;

import com.ideas.tetris.pacman.services.scheduledreport.reportapi.annotations.ColumnHeader;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.datatypes.PropertyValueType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

public class InputOverrideDTO {

    @ColumnHeader(titleKey = "property", order = 1)
    private String propertyName;
    @ColumnHeader(titleKey = "roomClass", order = 2)
    private String accomClassName;
    @ColumnHeader(titleKey = "forecast.group", order = 3)
    private String forecastGroupName;
    private String forecstGroupCode;
    @ColumnHeader(titleKey = "report.dow", order = 4, type = PropertyValueType.class)
    private String dow;
    @ColumnHeader(titleKey = "occupancyDate", order = 5)
    private LocalDate occupancyDate;
    @ColumnHeader(titleKey = "inputOverrideReport.category", order = 6, type = PropertyValueType.class)
    private String overrideCategory;
    @ColumnHeader(titleKey = "los", order = 7, condition = "isDemandOvrideByArrDtAndLOS", useDashes = true)
    private Integer los;
    @ColumnHeader(titleKey = "inputOverrideReport.column.userDemandOverride", order = 8,
            condition = "showDemandOverride", useDashes = true)
    private BigDecimal userDemandOverride;
    @ColumnHeader(titleKey = "inputOverrideReport.column.userWashOverridePercent", order = 9,
            condition = "isWashOverride", useDashes = true)
    private BigDecimal userWashOverridePerc;
    @ColumnHeader(titleKey = "inputOverrideReport.column.userWashExpirationDate", order = 10,
            condition = "isWashOverride", useDashes = true)
    private LocalDate userWashExpirationDate;
    @ColumnHeader(titleKey = "inputOverrideReport.column.gffOverride", order = 11,
            condition = "isGffOverrideFetch", useDashes = true)
    private BigDecimal grpfinalfcstoverride;
    @ColumnHeader(titleKey = "inputOverrideReport.column.gffOverrideExpDt", order = 12,
            condition = "isGffOverrideFetch", useDashes = true)
    private LocalDate grpfinalfcstexpirationdate;
    @ColumnHeader(titleKey = "report.overrideLastModifiedOn", order = 13)
    private Date overrideLastModifiedOn;
    @ColumnHeader(titleKey = "report.overrideLastModifiedBy", order = 14)
    private String overrideLastModifiedBy;
    @ColumnHeader(titleKey = "notes.label", order = 15, condition = "isNotesChecked")
    private String notes;
    private String userEmail;
    private String userName;

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public String getAccomClassName() {
        return accomClassName;
    }

    public void setAccomClassName(String accomClassName) {
        this.accomClassName = accomClassName;
    }

    public String getForecastGroupName() {
        return forecastGroupName;
    }

    public String getForecstGroupCode() {
        return forecstGroupCode;
    }

    public void setForecastGroupName(String forecastGroupName) {
        this.forecastGroupName = forecastGroupName;
    }

    public void setForecstGroupCode(String forecstGroupCode) {
        this.forecstGroupCode = forecstGroupCode;
    }

    public String getDow() {
        return dow;
    }

    public void setDow(String dow) {
        this.dow = dow;
    }

    public LocalDate getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(LocalDate occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public String getOverrideCategory() {
        return overrideCategory;
    }

    public void setOverrideCategory(String overrideCategory) {
        this.overrideCategory = overrideCategory;
    }

    public Integer getLos() {
        return los;
    }

    public void setLos(Integer los) {
        this.los = los;
    }

    public BigDecimal getUserDemandOverride() {
        return userDemandOverride;
    }

    public void setUserDemandOverride(BigDecimal userDemandOverride) {
        this.userDemandOverride = userDemandOverride;
    }

    public BigDecimal getUserWashOverridePerc() {
        return userWashOverridePerc;
    }

    public void setUserWashOverridePerc(BigDecimal userWashOverridePerc) {
        this.userWashOverridePerc = userWashOverridePerc;
    }

    public LocalDate getUserWashExpirationDate() {
        return userWashExpirationDate;
    }

    public void setUserWashExpirationDate(LocalDate userWashExpirationDate) {
        this.userWashExpirationDate = userWashExpirationDate;
    }

    public BigDecimal getGrpfinalfcstoverride() {
        return grpfinalfcstoverride;
    }

    public void setGrpfinalfcstoverride(BigDecimal grpfinalfcstoverride) {
        this.grpfinalfcstoverride = grpfinalfcstoverride;
    }

    public LocalDate getGrpfinalfcstexpirationdate() {
        return grpfinalfcstexpirationdate;
    }

    public void setGrpfinalfcstexpirationdate(LocalDate grpfinalfcstexpirationdate) {
        this.grpfinalfcstexpirationdate = grpfinalfcstexpirationdate;
    }

    public Date getOverrideLastModifiedOn() {
        return overrideLastModifiedOn;
    }

    public void setOverrideLastModifiedOn(Date overrideLastModifiedOn) {
        this.overrideLastModifiedOn = overrideLastModifiedOn;
    }

    public String getOverrideLastModifiedBy() {
        return overrideLastModifiedBy;
    }

    public void setOverrideLastModifiedBy(String overrideLastModifiedBy) {
        this.overrideLastModifiedBy = overrideLastModifiedBy;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
