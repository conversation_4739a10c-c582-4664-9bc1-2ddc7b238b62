package com.ideas.tetris.pacman.services.datafeed.rowmapper;

import com.ideas.tetris.pacman.services.datafeed.dto.RevplanMarketSegment;
import com.ideas.tetris.platform.common.crudservice.RowMapper;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class RevplanMarketSegmentRowMapper implements RowMapper<RevplanMarketSegment> {
    @Override
    public RevplanMarketSegment mapRow(Object[] row) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date occupancyDate;
        try {
            occupancyDate = dateFormat.parse(row[0].toString());
        } catch (ParseException e) {
            occupancyDate = null;
        }
        return new RevplanMarketSegment(occupancyDate, row[1].toString(), ((BigDecimal) row[2]).intValue(), (BigDecimal) row[3]);
    }
}