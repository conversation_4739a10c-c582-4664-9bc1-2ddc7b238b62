package com.ideas.tetris.pacman.services.reports.rateplanproduction.dto;

import java.math.BigDecimal;
import java.util.Date;

public class RatePlanProductionReportDTO {

    private Date arrivalDate;
    private String dayOfWeek;
    private Integer roomsOnBooksSRP1;
    private Integer roomsOnBooksDiffSRP1;
    private Integer roomsOnBooksSRP2;
    private Integer roomsOnBooksDiffSRP2;
    private Integer roomsOnBooksSRP3;
    private Integer roomsOnBooksDiffSRP3;
    private Integer roomsOnBooksSRP4;
    private Integer RoomsOnBooksDiffSRP4;
    private Integer roomsOnBooksSRP5;
    private Integer roomsOnBooksDiffSRP5;
    private Integer roomsOnBooksSRP6;
    private Integer roomsOnBooksDiffSRP6;
    private Integer roomsOnBooksSRP7;
    private Integer roomsOnBooksDiffSRP7;
    private Integer roomsOnBooksSRP8;
    private Integer roomsOnBooksDiffSRP8;
    private Integer roomsOnBooksSRP9;
    private Integer roomsOnBooksDiffSRP9;
    private Integer roomsOnBooksSRP10;
    private Integer roomsOnBooksDiffSRP10;
    private Integer roomsOnBooksSRP11;
    private Integer roomsOnBooksDiffSRP11;
    private Integer roomsOnBooksSRP12;
    private Integer RoomsOnBooksDiffSRP12;
    private Integer roomsOnBooksSRP13;
    private Integer roomsOnBooksDiffSRP13;
    private Integer roomsOnBooksSRP14;
    private Integer roomsOnBooksDiffSRP14;
    private Integer roomsOnBooksSRP15;
    private Integer roomsOnBooksDiffSRP15;
    private Integer roomsOnBooksSRP16;
    private Integer roomsOnBooksDiffSRP16;
    private Integer roomsOnBooksSRP17;
    private Integer roomsOnBooksDiffSRP17;
    private Integer roomsOnBooksSRP18;
    private Integer roomsOnBooksDiffSRP18;
    private Integer roomsOnBooksSRP19;
    private Integer roomsOnBooksDiffSRP19;
    private Integer roomsOnBooksSRP20;
    private Integer RoomsOnBooksDiffSRP20;
    private Integer roomsOnBooksSRP21;
    private Integer roomsOnBooksDiffSRP21;
    private Integer roomsOnBooksSRP22;
    private Integer roomsOnBooksDiffSRP22;
    private Integer roomsOnBooksSRP23;
    private Integer roomsOnBooksDiffSRP23;
    private Integer roomsOnBooksSRP24;
    private Integer roomsOnBooksDiffSRP24;
    private Integer roomsOnBooksSRP25;
    private Integer roomsOnBooksDiffSRP25;

    private BigDecimal revenueSRP1;
    private BigDecimal revenueDiffSRP1;
    private BigDecimal revenueSRP2;
    private BigDecimal revenueDiffSRP2;
    private BigDecimal revenueSRP3;
    private BigDecimal revenueDiffSRP3;
    private BigDecimal revenueSRP4;
    private BigDecimal revenueDiffSRP4;
    private BigDecimal revenueSRP5;
    private BigDecimal revenueDiffSRP5;
    private BigDecimal revenueSRP6;
    private BigDecimal revenueDiffSRP6;
    private BigDecimal revenueSRP7;
    private BigDecimal revenueDiffSRP7;
    private BigDecimal revenueSRP8;
    private BigDecimal revenueDiffSRP8;
    private BigDecimal revenueSRP9;
    private BigDecimal revenueDiffSRP9;
    private BigDecimal revenueSRP10;
    private BigDecimal revenueDiffSRP10;
    private BigDecimal revenueSRP11;
    private BigDecimal revenueDiffSRP11;
    private BigDecimal revenueSRP12;
    private BigDecimal revenueDiffSRP12;
    private BigDecimal revenueSRP13;
    private BigDecimal revenueDiffSRP13;
    private BigDecimal revenueSRP14;
    private BigDecimal revenueDiffSRP14;
    private BigDecimal revenueSRP15;
    private BigDecimal revenueDiffSRP15;
    private BigDecimal revenueSRP16;
    private BigDecimal revenueDiffSRP16;
    private BigDecimal revenueSRP17;
    private BigDecimal revenueDiffSRP17;
    private BigDecimal revenueSRP18;
    private BigDecimal revenueDiffSRP18;
    private BigDecimal revenueSRP19;
    private BigDecimal revenueDiffSRP19;
    private BigDecimal revenueSRP20;
    private BigDecimal revenueDiffSRP20;
    private BigDecimal revenueSRP21;
    private BigDecimal revenueDiffSRP21;
    private BigDecimal revenueSRP22;
    private BigDecimal revenueDiffSRP22;
    private BigDecimal revenueSRP23;
    private BigDecimal revenueDiffSRP23;
    private BigDecimal revenueSRP24;
    private BigDecimal revenueDiffSRP24;
    private BigDecimal revenueSRP25;
    private BigDecimal revenueDiffSRP25;

    private BigDecimal adrSRP1;
    private BigDecimal adrDiffSRP1;
    private BigDecimal adrSRP2;
    private BigDecimal adrDiffSRP2;
    private BigDecimal adrSRP3;
    private BigDecimal adrDiffSRP3;
    private BigDecimal adrSRP4;
    private BigDecimal adrDiffSRP4;
    private BigDecimal adrSRP5;
    private BigDecimal adrDiffSRP5;
    private BigDecimal adrSRP6;
    private BigDecimal adrDiffSRP6;
    private BigDecimal adrSRP7;
    private BigDecimal adrDiffSRP7;
    private BigDecimal adrSRP8;
    private BigDecimal adrDiffSRP8;
    private BigDecimal adrSRP9;
    private BigDecimal adrDiffSRP9;
    private BigDecimal adrSRP10;
    private BigDecimal adrDiffSRP10;
    private BigDecimal adrSRP11;
    private BigDecimal adrDiffSRP11;
    private BigDecimal adrSRP12;
    private BigDecimal adrDiffSRP12;
    private BigDecimal adrSRP13;
    private BigDecimal adrDiffSRP13;
    private BigDecimal adrSRP14;
    private BigDecimal adrDiffSRP14;
    private BigDecimal adrSRP15;
    private BigDecimal adrDiffSRP15;
    private BigDecimal adrSRP16;
    private BigDecimal adrDiffSRP16;
    private BigDecimal adrSRP17;
    private BigDecimal adrDiffSRP17;
    private BigDecimal adrSRP18;
    private BigDecimal adrDiffSRP18;
    private BigDecimal adrSRP19;
    private BigDecimal adrDiffSRP19;
    private BigDecimal adrSRP20;
    private BigDecimal adrDiffSRP20;
    private BigDecimal adrSRP21;
    private BigDecimal adrDiffSRP21;
    private BigDecimal adrSRP22;
    private BigDecimal adrDiffSRP22;
    private BigDecimal adrSRP23;
    private BigDecimal adrDiffSRP23;
    private BigDecimal adrSRP24;
    private BigDecimal adrDiffSRP24;
    private BigDecimal adrSRP25;
    private BigDecimal adrDiffSRP25;

    public Date getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public String getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(String dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public Integer getRoomsOnBooksSRP1() {
        return roomsOnBooksSRP1;
    }

    public void setRoomsOnBooksSRP1(Integer roomsOnBooksSRP1) {
        this.roomsOnBooksSRP1 = roomsOnBooksSRP1;
    }

    public Integer getRoomsOnBooksDiffSRP1() {
        return roomsOnBooksDiffSRP1;
    }

    public void setRoomsOnBooksDiffSRP1(Integer roomsOnBooksDiffSRP1) {
        this.roomsOnBooksDiffSRP1 = roomsOnBooksDiffSRP1;
    }

    public Integer getRoomsOnBooksSRP2() {
        return roomsOnBooksSRP2;
    }

    public void setRoomsOnBooksSRP2(Integer roomsOnBooksSRP2) {
        this.roomsOnBooksSRP2 = roomsOnBooksSRP2;
    }

    public Integer getRoomsOnBooksDiffSRP2() {
        return roomsOnBooksDiffSRP2;
    }

    public void setRoomsOnBooksDiffSRP2(Integer roomsOnBooksDiffSRP2) {
        this.roomsOnBooksDiffSRP2 = roomsOnBooksDiffSRP2;
    }

    public Integer getRoomsOnBooksSRP3() {
        return roomsOnBooksSRP3;
    }

    public void setRoomsOnBooksSRP3(Integer roomsOnBooksSRP3) {
        this.roomsOnBooksSRP3 = roomsOnBooksSRP3;
    }

    public Integer getRoomsOnBooksDiffSRP3() {
        return roomsOnBooksDiffSRP3;
    }

    public void setRoomsOnBooksDiffSRP3(Integer roomsOnBooksDiffSRP3) {
        this.roomsOnBooksDiffSRP3 = roomsOnBooksDiffSRP3;
    }

    public Integer getRoomsOnBooksSRP4() {
        return roomsOnBooksSRP4;
    }

    public void setRoomsOnBooksSRP4(Integer roomsOnBooksSRP4) {
        this.roomsOnBooksSRP4 = roomsOnBooksSRP4;
    }

    public Integer getRoomsOnBooksDiffSRP4() {
        return RoomsOnBooksDiffSRP4;
    }

    public void setRoomsOnBooksDiffSRP4(Integer roomsOnBooksDiffSRP4) {
        RoomsOnBooksDiffSRP4 = roomsOnBooksDiffSRP4;
    }

    public Integer getRoomsOnBooksSRP5() {
        return roomsOnBooksSRP5;
    }

    public void setRoomsOnBooksSRP5(Integer roomsOnBooksSRP5) {
        this.roomsOnBooksSRP5 = roomsOnBooksSRP5;
    }

    public Integer getRoomsOnBooksDiffSRP5() {
        return roomsOnBooksDiffSRP5;
    }

    public void setRoomsOnBooksDiffSRP5(Integer roomsOnBooksDiffSRP5) {
        this.roomsOnBooksDiffSRP5 = roomsOnBooksDiffSRP5;
    }

    public Integer getRoomsOnBooksSRP6() {
        return roomsOnBooksSRP6;
    }

    public void setRoomsOnBooksSRP6(Integer roomsOnBooksSRP6) {
        this.roomsOnBooksSRP6 = roomsOnBooksSRP6;
    }

    public Integer getRoomsOnBooksDiffSRP6() {
        return roomsOnBooksDiffSRP6;
    }

    public void setRoomsOnBooksDiffSRP6(Integer roomsOnBooksDiffSRP6) {
        this.roomsOnBooksDiffSRP6 = roomsOnBooksDiffSRP6;
    }

    public Integer getRoomsOnBooksSRP7() {
        return roomsOnBooksSRP7;
    }

    public void setRoomsOnBooksSRP7(Integer roomsOnBooksSRP7) {
        this.roomsOnBooksSRP7 = roomsOnBooksSRP7;
    }

    public Integer getRoomsOnBooksDiffSRP7() {
        return roomsOnBooksDiffSRP7;
    }

    public void setRoomsOnBooksDiffSRP7(Integer roomsOnBooksDiffSRP7) {
        this.roomsOnBooksDiffSRP7 = roomsOnBooksDiffSRP7;
    }

    public Integer getRoomsOnBooksSRP8() {
        return roomsOnBooksSRP8;
    }

    public void setRoomsOnBooksSRP8(Integer roomsOnBooksSRP8) {
        this.roomsOnBooksSRP8 = roomsOnBooksSRP8;
    }

    public Integer getRoomsOnBooksDiffSRP8() {
        return roomsOnBooksDiffSRP8;
    }

    public void setRoomsOnBooksDiffSRP8(Integer roomsOnBooksDiffSRP8) {
        this.roomsOnBooksDiffSRP8 = roomsOnBooksDiffSRP8;
    }

    public BigDecimal getRevenueSRP1() {
        return revenueSRP1;
    }

    public void setRevenueSRP1(BigDecimal revenueSRP1) {
        this.revenueSRP1 = revenueSRP1;
    }

    public BigDecimal getRevenueDiffSRP1() {
        return revenueDiffSRP1;
    }

    public void setRevenueDiffSRP1(BigDecimal revenueDiffSRP1) {
        this.revenueDiffSRP1 = revenueDiffSRP1;
    }

    public BigDecimal getRevenueSRP2() {
        return revenueSRP2;
    }

    public void setRevenueSRP2(BigDecimal revenueSRP2) {
        this.revenueSRP2 = revenueSRP2;
    }

    public BigDecimal getRevenueDiffSRP2() {
        return revenueDiffSRP2;
    }

    public void setRevenueDiffSRP2(BigDecimal revenueDiffSRP2) {
        this.revenueDiffSRP2 = revenueDiffSRP2;
    }

    public BigDecimal getRevenueSRP3() {
        return revenueSRP3;
    }

    public void setRevenueSRP3(BigDecimal revenueSRP3) {
        this.revenueSRP3 = revenueSRP3;
    }

    public BigDecimal getRevenueDiffSRP3() {
        return revenueDiffSRP3;
    }

    public void setRevenueDiffSRP3(BigDecimal revenueDiffSRP3) {
        this.revenueDiffSRP3 = revenueDiffSRP3;
    }

    public BigDecimal getRevenueSRP4() {
        return revenueSRP4;
    }

    public void setRevenueSRP4(BigDecimal revenueSRP4) {
        this.revenueSRP4 = revenueSRP4;
    }

    public BigDecimal getRevenueDiffSRP4() {
        return revenueDiffSRP4;
    }

    public void setRevenueDiffSRP4(BigDecimal revenueDiffSRP4) {
        this.revenueDiffSRP4 = revenueDiffSRP4;
    }

    public BigDecimal getRevenueSRP5() {
        return revenueSRP5;
    }

    public void setRevenueSRP5(BigDecimal revenueSRP5) {
        this.revenueSRP5 = revenueSRP5;
    }

    public BigDecimal getRevenueDiffSRP5() {
        return revenueDiffSRP5;
    }

    public void setRevenueDiffSRP5(BigDecimal revenueDiffSRP5) {
        this.revenueDiffSRP5 = revenueDiffSRP5;
    }

    public BigDecimal getRevenueSRP6() {
        return revenueSRP6;
    }

    public void setRevenueSRP6(BigDecimal revenueSRP6) {
        this.revenueSRP6 = revenueSRP6;
    }

    public BigDecimal getRevenueDiffSRP6() {
        return revenueDiffSRP6;
    }

    public void setRevenueDiffSRP6(BigDecimal revenueDiffSRP6) {
        this.revenueDiffSRP6 = revenueDiffSRP6;
    }

    public BigDecimal getRevenueSRP7() {
        return revenueSRP7;
    }

    public void setRevenueSRP7(BigDecimal revenueSRP7) {
        this.revenueSRP7 = revenueSRP7;
    }

    public BigDecimal getRevenueDiffSRP7() {
        return revenueDiffSRP7;
    }

    public void setRevenueDiffSRP7(BigDecimal revenueDiffSRP7) {
        this.revenueDiffSRP7 = revenueDiffSRP7;
    }

    public BigDecimal getRevenueSRP8() {
        return revenueSRP8;
    }

    public void setRevenueSRP8(BigDecimal revenueSRP8) {
        this.revenueSRP8 = revenueSRP8;
    }

    public BigDecimal getRevenueDiffSRP8() {
        return revenueDiffSRP8;
    }

    public void setRevenueDiffSRP8(BigDecimal revenueDiffSRP8) {
        this.revenueDiffSRP8 = revenueDiffSRP8;
    }

    public BigDecimal getAdrSRP1() {
        return adrSRP1;
    }

    public void setAdrSRP1(BigDecimal adrSRP1) {
        this.adrSRP1 = adrSRP1;
    }

    public BigDecimal getAdrDiffSRP1() {
        return adrDiffSRP1;
    }

    public void setAdrDiffSRP1(BigDecimal adrDiffSRP1) {
        this.adrDiffSRP1 = adrDiffSRP1;
    }

    public BigDecimal getAdrSRP2() {
        return adrSRP2;
    }

    public void setAdrSRP2(BigDecimal adrSRP2) {
        this.adrSRP2 = adrSRP2;
    }

    public BigDecimal getAdrDiffSRP2() {
        return adrDiffSRP2;
    }

    public void setAdrDiffSRP2(BigDecimal adrDiffSRP2) {
        this.adrDiffSRP2 = adrDiffSRP2;
    }

    public BigDecimal getAdrSRP3() {
        return adrSRP3;
    }

    public void setAdrSRP3(BigDecimal adrSRP3) {
        this.adrSRP3 = adrSRP3;
    }

    public BigDecimal getAdrDiffSRP3() {
        return adrDiffSRP3;
    }

    public void setAdrDiffSRP3(BigDecimal adrDiffSRP3) {
        this.adrDiffSRP3 = adrDiffSRP3;
    }

    public BigDecimal getAdrSRP4() {
        return adrSRP4;
    }

    public void setAdrSRP4(BigDecimal adrSRP4) {
        this.adrSRP4 = adrSRP4;
    }

    public BigDecimal getAdrDiffSRP4() {
        return adrDiffSRP4;
    }

    public void setAdrDiffSRP4(BigDecimal adrDiffSRP4) {
        this.adrDiffSRP4 = adrDiffSRP4;
    }

    public BigDecimal getAdrSRP5() {
        return adrSRP5;
    }

    public void setAdrSRP5(BigDecimal adrSRP5) {
        this.adrSRP5 = adrSRP5;
    }

    public BigDecimal getAdrDiffSRP5() {
        return adrDiffSRP5;
    }

    public void setAdrDiffSRP5(BigDecimal adrDiffSRP5) {
        this.adrDiffSRP5 = adrDiffSRP5;
    }

    public BigDecimal getAdrSRP6() {
        return adrSRP6;
    }

    public void setAdrSRP6(BigDecimal adrSRP6) {
        this.adrSRP6 = adrSRP6;
    }

    public BigDecimal getAdrDiffSRP6() {
        return adrDiffSRP6;
    }

    public void setAdrDiffSRP6(BigDecimal adrDiffSRP6) {
        this.adrDiffSRP6 = adrDiffSRP6;
    }

    public BigDecimal getAdrSRP7() {
        return adrSRP7;
    }

    public void setAdrSRP7(BigDecimal adrSRP7) {
        this.adrSRP7 = adrSRP7;
    }

    public BigDecimal getAdrDiffSRP7() {
        return adrDiffSRP7;
    }

    public void setAdrDiffSRP7(BigDecimal adrDiffSRP7) {
        this.adrDiffSRP7 = adrDiffSRP7;
    }

    public BigDecimal getAdrSRP8() {
        return adrSRP8;
    }

    public void setAdrSRP8(BigDecimal adrSRP8) {
        this.adrSRP8 = adrSRP8;
    }

    public BigDecimal getAdrDiffSRP8() {
        return adrDiffSRP8;
    }

    public void setAdrDiffSRP8(BigDecimal adrDiffSRP8) {
        this.adrDiffSRP8 = adrDiffSRP8;
    }

    public Integer getRoomsOnBooksSRP9() {
        return roomsOnBooksSRP9;
    }

    public void setRoomsOnBooksSRP9(Integer roomsOnBooksSRP9) {
        this.roomsOnBooksSRP9 = roomsOnBooksSRP9;
    }

    public Integer getRoomsOnBooksDiffSRP9() {
        return roomsOnBooksDiffSRP9;
    }

    public void setRoomsOnBooksDiffSRP9(Integer roomsOnBooksDiffSRP9) {
        this.roomsOnBooksDiffSRP9 = roomsOnBooksDiffSRP9;
    }

    public Integer getRoomsOnBooksSRP10() {
        return roomsOnBooksSRP10;
    }

    public void setRoomsOnBooksSRP10(Integer roomsOnBooksSRP10) {
        this.roomsOnBooksSRP10 = roomsOnBooksSRP10;
    }

    public Integer getRoomsOnBooksDiffSRP10() {
        return roomsOnBooksDiffSRP10;
    }

    public void setRoomsOnBooksDiffSRP10(Integer roomsOnBooksDiffSRP10) {
        this.roomsOnBooksDiffSRP10 = roomsOnBooksDiffSRP10;
    }

    public Integer getRoomsOnBooksSRP11() {
        return roomsOnBooksSRP11;
    }

    public void setRoomsOnBooksSRP11(Integer roomsOnBooksSRP11) {
        this.roomsOnBooksSRP11 = roomsOnBooksSRP11;
    }

    public Integer getRoomsOnBooksDiffSRP11() {
        return roomsOnBooksDiffSRP11;
    }

    public void setRoomsOnBooksDiffSRP11(Integer roomsOnBooksDiffSRP11) {
        this.roomsOnBooksDiffSRP11 = roomsOnBooksDiffSRP11;
    }

    public Integer getRoomsOnBooksSRP12() {
        return roomsOnBooksSRP12;
    }

    public void setRoomsOnBooksSRP12(Integer roomsOnBooksSRP12) {
        this.roomsOnBooksSRP12 = roomsOnBooksSRP12;
    }

    public Integer getRoomsOnBooksDiffSRP12() {
        return RoomsOnBooksDiffSRP12;
    }

    public void setRoomsOnBooksDiffSRP12(Integer roomsOnBooksDiffSRP12) {
        RoomsOnBooksDiffSRP12 = roomsOnBooksDiffSRP12;
    }

    public Integer getRoomsOnBooksSRP13() {
        return roomsOnBooksSRP13;
    }

    public void setRoomsOnBooksSRP13(Integer roomsOnBooksSRP13) {
        this.roomsOnBooksSRP13 = roomsOnBooksSRP13;
    }

    public Integer getRoomsOnBooksDiffSRP13() {
        return roomsOnBooksDiffSRP13;
    }

    public void setRoomsOnBooksDiffSRP13(Integer roomsOnBooksDiffSRP13) {
        this.roomsOnBooksDiffSRP13 = roomsOnBooksDiffSRP13;
    }

    public Integer getRoomsOnBooksSRP14() {
        return roomsOnBooksSRP14;
    }

    public void setRoomsOnBooksSRP14(Integer roomsOnBooksSRP14) {
        this.roomsOnBooksSRP14 = roomsOnBooksSRP14;
    }

    public Integer getRoomsOnBooksDiffSRP14() {
        return roomsOnBooksDiffSRP14;
    }

    public void setRoomsOnBooksDiffSRP14(Integer roomsOnBooksDiffSRP14) {
        this.roomsOnBooksDiffSRP14 = roomsOnBooksDiffSRP14;
    }

    public Integer getRoomsOnBooksSRP15() {
        return roomsOnBooksSRP15;
    }

    public void setRoomsOnBooksSRP15(Integer roomsOnBooksSRP15) {
        this.roomsOnBooksSRP15 = roomsOnBooksSRP15;
    }

    public Integer getRoomsOnBooksDiffSRP15() {
        return roomsOnBooksDiffSRP15;
    }

    public void setRoomsOnBooksDiffSRP15(Integer roomsOnBooksDiffSRP15) {
        this.roomsOnBooksDiffSRP15 = roomsOnBooksDiffSRP15;
    }

    public Integer getRoomsOnBooksSRP16() {
        return roomsOnBooksSRP16;
    }

    public void setRoomsOnBooksSRP16(Integer roomsOnBooksSRP16) {
        this.roomsOnBooksSRP16 = roomsOnBooksSRP16;
    }

    public Integer getRoomsOnBooksDiffSRP16() {
        return roomsOnBooksDiffSRP16;
    }

    public void setRoomsOnBooksDiffSRP16(Integer roomsOnBooksDiffSRP16) {
        this.roomsOnBooksDiffSRP16 = roomsOnBooksDiffSRP16;
    }

    public Integer getRoomsOnBooksSRP17() {
        return roomsOnBooksSRP17;
    }

    public void setRoomsOnBooksSRP17(Integer roomsOnBooksSRP17) {
        this.roomsOnBooksSRP17 = roomsOnBooksSRP17;
    }

    public Integer getRoomsOnBooksDiffSRP17() {
        return roomsOnBooksDiffSRP17;
    }

    public void setRoomsOnBooksDiffSRP17(Integer roomsOnBooksDiffSRP17) {
        this.roomsOnBooksDiffSRP17 = roomsOnBooksDiffSRP17;
    }

    public Integer getRoomsOnBooksSRP18() {
        return roomsOnBooksSRP18;
    }

    public void setRoomsOnBooksSRP18(Integer roomsOnBooksSRP18) {
        this.roomsOnBooksSRP18 = roomsOnBooksSRP18;
    }

    public Integer getRoomsOnBooksDiffSRP18() {
        return roomsOnBooksDiffSRP18;
    }

    public void setRoomsOnBooksDiffSRP18(Integer roomsOnBooksDiffSRP18) {
        this.roomsOnBooksDiffSRP18 = roomsOnBooksDiffSRP18;
    }

    public Integer getRoomsOnBooksSRP19() {
        return roomsOnBooksSRP19;
    }

    public void setRoomsOnBooksSRP19(Integer roomsOnBooksSRP19) {
        this.roomsOnBooksSRP19 = roomsOnBooksSRP19;
    }

    public Integer getRoomsOnBooksDiffSRP19() {
        return roomsOnBooksDiffSRP19;
    }

    public void setRoomsOnBooksDiffSRP19(Integer roomsOnBooksDiffSRP19) {
        this.roomsOnBooksDiffSRP19 = roomsOnBooksDiffSRP19;
    }

    public Integer getRoomsOnBooksSRP20() {
        return roomsOnBooksSRP20;
    }

    public void setRoomsOnBooksSRP20(Integer roomsOnBooksSRP20) {
        this.roomsOnBooksSRP20 = roomsOnBooksSRP20;
    }

    public Integer getRoomsOnBooksDiffSRP20() {
        return RoomsOnBooksDiffSRP20;
    }

    public void setRoomsOnBooksDiffSRP20(Integer roomsOnBooksDiffSRP20) {
        RoomsOnBooksDiffSRP20 = roomsOnBooksDiffSRP20;
    }

    public Integer getRoomsOnBooksSRP21() {
        return roomsOnBooksSRP21;
    }

    public void setRoomsOnBooksSRP21(Integer roomsOnBooksSRP21) {
        this.roomsOnBooksSRP21 = roomsOnBooksSRP21;
    }

    public Integer getRoomsOnBooksDiffSRP21() {
        return roomsOnBooksDiffSRP21;
    }

    public void setRoomsOnBooksDiffSRP21(Integer roomsOnBooksDiffSRP21) {
        this.roomsOnBooksDiffSRP21 = roomsOnBooksDiffSRP21;
    }

    public Integer getRoomsOnBooksSRP22() {
        return roomsOnBooksSRP22;
    }

    public void setRoomsOnBooksSRP22(Integer roomsOnBooksSRP22) {
        this.roomsOnBooksSRP22 = roomsOnBooksSRP22;
    }

    public Integer getRoomsOnBooksDiffSRP22() {
        return roomsOnBooksDiffSRP22;
    }

    public void setRoomsOnBooksDiffSRP22(Integer roomsOnBooksDiffSRP22) {
        this.roomsOnBooksDiffSRP22 = roomsOnBooksDiffSRP22;
    }

    public Integer getRoomsOnBooksSRP23() {
        return roomsOnBooksSRP23;
    }

    public void setRoomsOnBooksSRP23(Integer roomsOnBooksSRP23) {
        this.roomsOnBooksSRP23 = roomsOnBooksSRP23;
    }

    public Integer getRoomsOnBooksDiffSRP23() {
        return roomsOnBooksDiffSRP23;
    }

    public void setRoomsOnBooksDiffSRP23(Integer roomsOnBooksDiffSRP23) {
        this.roomsOnBooksDiffSRP23 = roomsOnBooksDiffSRP23;
    }

    public Integer getRoomsOnBooksSRP24() {
        return roomsOnBooksSRP24;
    }

    public void setRoomsOnBooksSRP24(Integer roomsOnBooksSRP24) {
        this.roomsOnBooksSRP24 = roomsOnBooksSRP24;
    }

    public Integer getRoomsOnBooksDiffSRP24() {
        return roomsOnBooksDiffSRP24;
    }

    public void setRoomsOnBooksDiffSRP24(Integer roomsOnBooksDiffSRP24) {
        this.roomsOnBooksDiffSRP24 = roomsOnBooksDiffSRP24;
    }

    public Integer getRoomsOnBooksSRP25() {
        return roomsOnBooksSRP25;
    }

    public void setRoomsOnBooksSRP25(Integer roomsOnBooksSRP25) {
        this.roomsOnBooksSRP25 = roomsOnBooksSRP25;
    }

    public Integer getRoomsOnBooksDiffSRP25() {
        return roomsOnBooksDiffSRP25;
    }

    public void setRoomsOnBooksDiffSRP25(Integer roomsOnBooksDiffSRP25) {
        this.roomsOnBooksDiffSRP25 = roomsOnBooksDiffSRP25;
    }

    public BigDecimal getRevenueSRP9() {
        return revenueSRP9;
    }

    public void setRevenueSRP9(BigDecimal revenueSRP9) {
        this.revenueSRP9 = revenueSRP9;
    }

    public BigDecimal getRevenueDiffSRP9() {
        return revenueDiffSRP9;
    }

    public void setRevenueDiffSRP9(BigDecimal revenueDiffSRP9) {
        this.revenueDiffSRP9 = revenueDiffSRP9;
    }

    public BigDecimal getRevenueSRP10() {
        return revenueSRP10;
    }

    public void setRevenueSRP10(BigDecimal revenueSRP10) {
        this.revenueSRP10 = revenueSRP10;
    }

    public BigDecimal getRevenueDiffSRP10() {
        return revenueDiffSRP10;
    }

    public void setRevenueDiffSRP10(BigDecimal revenueDiffSRP10) {
        this.revenueDiffSRP10 = revenueDiffSRP10;
    }

    public BigDecimal getRevenueSRP11() {
        return revenueSRP11;
    }

    public void setRevenueSRP11(BigDecimal revenueSRP11) {
        this.revenueSRP11 = revenueSRP11;
    }

    public BigDecimal getRevenueDiffSRP11() {
        return revenueDiffSRP11;
    }

    public void setRevenueDiffSRP11(BigDecimal revenueDiffSRP11) {
        this.revenueDiffSRP11 = revenueDiffSRP11;
    }

    public BigDecimal getRevenueSRP12() {
        return revenueSRP12;
    }

    public void setRevenueSRP12(BigDecimal revenueSRP12) {
        this.revenueSRP12 = revenueSRP12;
    }

    public BigDecimal getRevenueDiffSRP12() {
        return revenueDiffSRP12;
    }

    public void setRevenueDiffSRP12(BigDecimal revenueDiffSRP12) {
        this.revenueDiffSRP12 = revenueDiffSRP12;
    }

    public BigDecimal getRevenueSRP13() {
        return revenueSRP13;
    }

    public void setRevenueSRP13(BigDecimal revenueSRP13) {
        this.revenueSRP13 = revenueSRP13;
    }

    public BigDecimal getRevenueDiffSRP13() {
        return revenueDiffSRP13;
    }

    public void setRevenueDiffSRP13(BigDecimal revenueDiffSRP13) {
        this.revenueDiffSRP13 = revenueDiffSRP13;
    }

    public BigDecimal getRevenueSRP14() {
        return revenueSRP14;
    }

    public void setRevenueSRP14(BigDecimal revenueSRP14) {
        this.revenueSRP14 = revenueSRP14;
    }

    public BigDecimal getRevenueDiffSRP14() {
        return revenueDiffSRP14;
    }

    public void setRevenueDiffSRP14(BigDecimal revenueDiffSRP14) {
        this.revenueDiffSRP14 = revenueDiffSRP14;
    }

    public BigDecimal getRevenueSRP15() {
        return revenueSRP15;
    }

    public void setRevenueSRP15(BigDecimal revenueSRP15) {
        this.revenueSRP15 = revenueSRP15;
    }

    public BigDecimal getRevenueDiffSRP15() {
        return revenueDiffSRP15;
    }

    public void setRevenueDiffSRP15(BigDecimal revenueDiffSRP15) {
        this.revenueDiffSRP15 = revenueDiffSRP15;
    }

    public BigDecimal getRevenueSRP16() {
        return revenueSRP16;
    }

    public void setRevenueSRP16(BigDecimal revenueSRP16) {
        this.revenueSRP16 = revenueSRP16;
    }

    public BigDecimal getRevenueDiffSRP16() {
        return revenueDiffSRP16;
    }

    public void setRevenueDiffSRP16(BigDecimal revenueDiffSRP16) {
        this.revenueDiffSRP16 = revenueDiffSRP16;
    }

    public BigDecimal getRevenueSRP17() {
        return revenueSRP17;
    }

    public void setRevenueSRP17(BigDecimal revenueSRP17) {
        this.revenueSRP17 = revenueSRP17;
    }

    public BigDecimal getRevenueDiffSRP17() {
        return revenueDiffSRP17;
    }

    public void setRevenueDiffSRP17(BigDecimal revenueDiffSRP17) {
        this.revenueDiffSRP17 = revenueDiffSRP17;
    }

    public BigDecimal getRevenueSRP18() {
        return revenueSRP18;
    }

    public void setRevenueSRP18(BigDecimal revenueSRP18) {
        this.revenueSRP18 = revenueSRP18;
    }

    public BigDecimal getRevenueDiffSRP18() {
        return revenueDiffSRP18;
    }

    public void setRevenueDiffSRP18(BigDecimal revenueDiffSRP18) {
        this.revenueDiffSRP18 = revenueDiffSRP18;
    }

    public BigDecimal getRevenueSRP19() {
        return revenueSRP19;
    }

    public void setRevenueSRP19(BigDecimal revenueSRP19) {
        this.revenueSRP19 = revenueSRP19;
    }

    public BigDecimal getRevenueDiffSRP19() {
        return revenueDiffSRP19;
    }

    public void setRevenueDiffSRP19(BigDecimal revenueDiffSRP19) {
        this.revenueDiffSRP19 = revenueDiffSRP19;
    }

    public BigDecimal getRevenueSRP20() {
        return revenueSRP20;
    }

    public void setRevenueSRP20(BigDecimal revenueSRP20) {
        this.revenueSRP20 = revenueSRP20;
    }

    public BigDecimal getRevenueDiffSRP20() {
        return revenueDiffSRP20;
    }

    public void setRevenueDiffSRP20(BigDecimal revenueDiffSRP20) {
        this.revenueDiffSRP20 = revenueDiffSRP20;
    }

    public BigDecimal getRevenueSRP21() {
        return revenueSRP21;
    }

    public void setRevenueSRP21(BigDecimal revenueSRP21) {
        this.revenueSRP21 = revenueSRP21;
    }

    public BigDecimal getRevenueDiffSRP21() {
        return revenueDiffSRP21;
    }

    public void setRevenueDiffSRP21(BigDecimal revenueDiffSRP21) {
        this.revenueDiffSRP21 = revenueDiffSRP21;
    }

    public BigDecimal getRevenueSRP22() {
        return revenueSRP22;
    }

    public void setRevenueSRP22(BigDecimal revenueSRP22) {
        this.revenueSRP22 = revenueSRP22;
    }

    public BigDecimal getRevenueDiffSRP22() {
        return revenueDiffSRP22;
    }

    public void setRevenueDiffSRP22(BigDecimal revenueDiffSRP22) {
        this.revenueDiffSRP22 = revenueDiffSRP22;
    }

    public BigDecimal getRevenueSRP23() {
        return revenueSRP23;
    }

    public void setRevenueSRP23(BigDecimal revenueSRP23) {
        this.revenueSRP23 = revenueSRP23;
    }

    public BigDecimal getRevenueDiffSRP23() {
        return revenueDiffSRP23;
    }

    public void setRevenueDiffSRP23(BigDecimal revenueDiffSRP23) {
        this.revenueDiffSRP23 = revenueDiffSRP23;
    }

    public BigDecimal getRevenueSRP24() {
        return revenueSRP24;
    }

    public void setRevenueSRP24(BigDecimal revenueSRP24) {
        this.revenueSRP24 = revenueSRP24;
    }

    public BigDecimal getRevenueDiffSRP24() {
        return revenueDiffSRP24;
    }

    public void setRevenueDiffSRP24(BigDecimal revenueDiffSRP24) {
        this.revenueDiffSRP24 = revenueDiffSRP24;
    }

    public BigDecimal getRevenueSRP25() {
        return revenueSRP25;
    }

    public void setRevenueSRP25(BigDecimal revenueSRP25) {
        this.revenueSRP25 = revenueSRP25;
    }

    public BigDecimal getRevenueDiffSRP25() {
        return revenueDiffSRP25;
    }

    public void setRevenueDiffSRP25(BigDecimal revenueDiffSRP25) {
        this.revenueDiffSRP25 = revenueDiffSRP25;
    }

    public BigDecimal getAdrSRP9() {
        return adrSRP9;
    }

    public void setAdrSRP9(BigDecimal adrSRP9) {
        this.adrSRP9 = adrSRP9;
    }

    public BigDecimal getAdrDiffSRP9() {
        return adrDiffSRP9;
    }

    public void setAdrDiffSRP9(BigDecimal adrDiffSRP9) {
        this.adrDiffSRP9 = adrDiffSRP9;
    }

    public BigDecimal getAdrSRP10() {
        return adrSRP10;
    }

    public void setAdrSRP10(BigDecimal adrSRP10) {
        this.adrSRP10 = adrSRP10;
    }

    public BigDecimal getAdrDiffSRP10() {
        return adrDiffSRP10;
    }

    public void setAdrDiffSRP10(BigDecimal adrDiffSRP10) {
        this.adrDiffSRP10 = adrDiffSRP10;
    }

    public BigDecimal getAdrSRP11() {
        return adrSRP11;
    }

    public void setAdrSRP11(BigDecimal adrSRP11) {
        this.adrSRP11 = adrSRP11;
    }

    public BigDecimal getAdrDiffSRP11() {
        return adrDiffSRP11;
    }

    public void setAdrDiffSRP11(BigDecimal adrDiffSRP11) {
        this.adrDiffSRP11 = adrDiffSRP11;
    }

    public BigDecimal getAdrSRP12() {
        return adrSRP12;
    }

    public void setAdrSRP12(BigDecimal adrSRP12) {
        this.adrSRP12 = adrSRP12;
    }

    public BigDecimal getAdrDiffSRP12() {
        return adrDiffSRP12;
    }

    public void setAdrDiffSRP12(BigDecimal adrDiffSRP12) {
        this.adrDiffSRP12 = adrDiffSRP12;
    }

    public BigDecimal getAdrSRP13() {
        return adrSRP13;
    }

    public void setAdrSRP13(BigDecimal adrSRP13) {
        this.adrSRP13 = adrSRP13;
    }

    public BigDecimal getAdrDiffSRP13() {
        return adrDiffSRP13;
    }

    public void setAdrDiffSRP13(BigDecimal adrDiffSRP13) {
        this.adrDiffSRP13 = adrDiffSRP13;
    }

    public BigDecimal getAdrSRP14() {
        return adrSRP14;
    }

    public void setAdrSRP14(BigDecimal adrSRP14) {
        this.adrSRP14 = adrSRP14;
    }

    public BigDecimal getAdrDiffSRP14() {
        return adrDiffSRP14;
    }

    public void setAdrDiffSRP14(BigDecimal adrDiffSRP14) {
        this.adrDiffSRP14 = adrDiffSRP14;
    }

    public BigDecimal getAdrSRP15() {
        return adrSRP15;
    }

    public void setAdrSRP15(BigDecimal adrSRP15) {
        this.adrSRP15 = adrSRP15;
    }

    public BigDecimal getAdrDiffSRP15() {
        return adrDiffSRP15;
    }

    public void setAdrDiffSRP15(BigDecimal adrDiffSRP15) {
        this.adrDiffSRP15 = adrDiffSRP15;
    }

    public BigDecimal getAdrSRP16() {
        return adrSRP16;
    }

    public void setAdrSRP16(BigDecimal adrSRP16) {
        this.adrSRP16 = adrSRP16;
    }

    public BigDecimal getAdrDiffSRP16() {
        return adrDiffSRP16;
    }

    public void setAdrDiffSRP16(BigDecimal adrDiffSRP16) {
        this.adrDiffSRP16 = adrDiffSRP16;
    }

    public BigDecimal getAdrSRP17() {
        return adrSRP17;
    }

    public void setAdrSRP17(BigDecimal adrSRP17) {
        this.adrSRP17 = adrSRP17;
    }

    public BigDecimal getAdrDiffSRP17() {
        return adrDiffSRP17;
    }

    public void setAdrDiffSRP17(BigDecimal adrDiffSRP17) {
        this.adrDiffSRP17 = adrDiffSRP17;
    }

    public BigDecimal getAdrSRP18() {
        return adrSRP18;
    }

    public void setAdrSRP18(BigDecimal adrSRP18) {
        this.adrSRP18 = adrSRP18;
    }

    public BigDecimal getAdrDiffSRP18() {
        return adrDiffSRP18;
    }

    public void setAdrDiffSRP18(BigDecimal adrDiffSRP18) {
        this.adrDiffSRP18 = adrDiffSRP18;
    }

    public BigDecimal getAdrSRP19() {
        return adrSRP19;
    }

    public void setAdrSRP19(BigDecimal adrSRP19) {
        this.adrSRP19 = adrSRP19;
    }

    public BigDecimal getAdrDiffSRP19() {
        return adrDiffSRP19;
    }

    public void setAdrDiffSRP19(BigDecimal adrDiffSRP19) {
        this.adrDiffSRP19 = adrDiffSRP19;
    }

    public BigDecimal getAdrSRP20() {
        return adrSRP20;
    }

    public void setAdrSRP20(BigDecimal adrSRP20) {
        this.adrSRP20 = adrSRP20;
    }

    public BigDecimal getAdrDiffSRP20() {
        return adrDiffSRP20;
    }

    public void setAdrDiffSRP20(BigDecimal adrDiffSRP20) {
        this.adrDiffSRP20 = adrDiffSRP20;
    }

    public BigDecimal getAdrSRP21() {
        return adrSRP21;
    }

    public void setAdrSRP21(BigDecimal adrSRP21) {
        this.adrSRP21 = adrSRP21;
    }

    public BigDecimal getAdrDiffSRP21() {
        return adrDiffSRP21;
    }

    public void setAdrDiffSRP21(BigDecimal adrDiffSRP21) {
        this.adrDiffSRP21 = adrDiffSRP21;
    }

    public BigDecimal getAdrSRP22() {
        return adrSRP22;
    }

    public void setAdrSRP22(BigDecimal adrSRP22) {
        this.adrSRP22 = adrSRP22;
    }

    public BigDecimal getAdrDiffSRP22() {
        return adrDiffSRP22;
    }

    public void setAdrDiffSRP22(BigDecimal adrDiffSRP22) {
        this.adrDiffSRP22 = adrDiffSRP22;
    }

    public BigDecimal getAdrSRP23() {
        return adrSRP23;
    }

    public void setAdrSRP23(BigDecimal adrSRP23) {
        this.adrSRP23 = adrSRP23;
    }

    public BigDecimal getAdrDiffSRP23() {
        return adrDiffSRP23;
    }

    public void setAdrDiffSRP23(BigDecimal adrDiffSRP23) {
        this.adrDiffSRP23 = adrDiffSRP23;
    }

    public BigDecimal getAdrSRP24() {
        return adrSRP24;
    }

    public void setAdrSRP24(BigDecimal adrSRP24) {
        this.adrSRP24 = adrSRP24;
    }

    public BigDecimal getAdrDiffSRP24() {
        return adrDiffSRP24;
    }

    public void setAdrDiffSRP24(BigDecimal adrDiffSRP24) {
        this.adrDiffSRP24 = adrDiffSRP24;
    }

    public BigDecimal getAdrSRP25() {
        return adrSRP25;
    }

    public void setAdrSRP25(BigDecimal adrSRP25) {
        this.adrSRP25 = adrSRP25;
    }

    public BigDecimal getAdrDiffSRP25() {
        return adrDiffSRP25;
    }

    public void setAdrDiffSRP25(BigDecimal adrDiffSRP25) {
        this.adrDiffSRP25 = adrDiffSRP25;
    }
}
