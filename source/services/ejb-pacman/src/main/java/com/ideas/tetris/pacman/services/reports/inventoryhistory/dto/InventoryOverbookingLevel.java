package com.ideas.tetris.pacman.services.reports.inventoryhistory.dto;

public enum InventoryOverbookingLevel {
    HOUSE(1, "houseProperty", "HOUSE"),
    ROOM_TYPE(2, "roomType", "RT"),
    HOUSE_AND_ROOM_TYPE(3, "houseAndRoomType", "RTHOUSE");

    private int ordinal;
    private String caption;
    private String paramValue;

    InventoryOverbookingLevel(int ordinal, String caption, String paramValue) {
        this.ordinal = ordinal;
        this.caption = caption;
        this.paramValue = paramValue;
    }

    public String getCaption() {
        return caption;
    }

    public String getParamValue() {
        return paramValue;
    }
}
