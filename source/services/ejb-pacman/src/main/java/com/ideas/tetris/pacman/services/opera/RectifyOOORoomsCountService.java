package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;

@Component
@Transactional
public class RectifyOOORoomsCountService {
    @Autowired
    JobServiceLocal jobService;
    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    RectifyOperaOOOSASService rectifyOperaOOOSASService;

    private static final Logger LOGGER = Logger.getLogger(RectifyOOORoomsCountService.class.getName());

    public static final String RECTIFY_ACCOM_ACTIVITY_OOO_ROOM_COUNTS_QUERY = new StringBuilder()
            .append(" update acoac ")
            .append(" set Rooms_Not_Avail_Maint = histpast.Out_Of_Order_Rooms")
            .append(" 	, Rooms_Not_Avail_Other = histpast.Out_Of_Service_Rooms ")
            .append(" From Accom_activity as acoac ")
            .append(" inner join")
            .append(" (")
            .append(" 	select Occupancy_DT, Out_Of_Order_Rooms, Out_Of_Service_Rooms, Room_Type, Accom_Type_ID ")
            .append(" 	from opera.History_Occupancy_Summary as aocsum ")
            .append(" 	inner join Accom_Type as accom ")
            .append(" 	on aocsum.Room_Type = accom.Accom_Type_Code")
            .append(" 	where Data_Load_Metadata_ID = ")
            .append(" 	(")
            .append(" 		select Data_Load_Metadata_ID from opera.Data_Load_Metadata ")
            .append(" 		where Incoming_File_Type_Code = 'PTAT' and Correlation_ID =")
            .append(" 		(")
            .append(" 			select Correlation_ID from opera.Data_Load_Metadata ")
            .append(" 			where Data_Load_Metadata_ID =")
            .append(" 			(")
            .append(" 				select max(Data_Load_Metadata_ID) from opera.History_Incoming_Metadata")
            .append(" 				where Past_Days > 700")
            .append(" 			)")
            .append(" 		)")
            .append(" 	)")
            .append(" ) as histpast on histpast.Occupancy_DT = acoac.Occupancy_DT")
            .append(" and  histpast.Accom_Type_ID = acoac.Accom_Type_ID;")
            .toString();

    public static final String RECTIFY_PACE_ACCOM_ACTIVITY_OOO_ROOM_COUNTS_QUERY = new StringBuilder()
            .append(" update pacoac ")
            .append(" set Rooms_Not_Avail_Maint = histpast.Out_Of_Order_Rooms")
            .append(" 	, Rooms_Not_Avail_Other = histpast.Out_Of_Service_Rooms ")
            .append(" From PACE_Accom_Activity as pacoac ")
            .append(" inner join")
            .append(" (")
            .append(" 	select Occupancy_DT, Out_Of_Order_Rooms, Out_Of_Service_Rooms, Room_Type, Accom_Type_ID ")
            .append(" 	from opera.History_Occupancy_Summary as aocsum ")
            .append(" 	inner join Accom_Type as accom ")
            .append(" 	on aocsum.Room_Type = accom.Accom_Type_Code")
            .append(" 	where Data_Load_Metadata_ID = ")
            .append(" 	(")
            .append(" 		select Data_Load_Metadata_ID from opera.Data_Load_Metadata ")
            .append(" 		where Incoming_File_Type_Code = 'PTAT' ")
            .append(" 		and Correlation_ID =")
            .append(" 		(")
            .append(" 			select Correlation_ID from opera.Data_Load_Metadata ")
            .append(" 			where Data_Load_Metadata_ID =")
            .append(" 			(")
            .append(" 				select  max(Data_Load_Metadata_ID) from opera.History_Incoming_Metadata ")
            .append(" 				where Past_Days > 700")
            .append(" 			)")
            .append(" 		)")
            .append(" 	)")
            .append(" ) as histpast ")
            .append(" on histpast.Occupancy_DT = pacoac.Occupancy_DT")
            .append(" and  histpast.Accom_Type_ID = pacoac.Accom_Type_ID;")
            .toString();

    public Long startOOORectificationJob() {
        String date = DateTime.now().toString(ISODateTimeFormat.basicDateTimeNoMillis());
        HashMap parameters = new HashMap<String, Object>();
        parameters.put(JobParameterKey.CLIENT_ID, PacmanWorkContextHelper.getClientId());
        parameters.put(JobParameterKey.CLIENT_CODE, PacmanWorkContextHelper.getClientCode());
        parameters.put(JobParameterKey.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId().toString());
        parameters.put(JobParameterKey.PROPERTY_CODE, PacmanWorkContextHelper.getPropertyCode());
        parameters.put(JobParameterKey.DATE, date);
        return jobService.startJob(JobName.RectifyOperaOOORoomsCountJob, parameters);
    }

    public int rectifyPacmanOOORoomsCount() {
        LOGGER.info("Started Updating Pacman for OOO rooms count ");
        int numRowsUpdated = crudService.executeUpdateByNativeQuery(RECTIFY_ACCOM_ACTIVITY_OOO_ROOM_COUNTS_QUERY);
        numRowsUpdated += crudService.executeUpdateByNativeQuery(RECTIFY_PACE_ACCOM_ACTIVITY_OOO_ROOM_COUNTS_QUERY);
        LOGGER.info("Completed Updating Pacman for OOO rooms count ");
        return numRowsUpdated;
    }

    public void toggleOFFOperaAdjustOOOFlag() {
        pacmanConfigParamsService.addParameterValue(Constants.OPERA_ADJUST_OOO_ROOM_COUNT, Boolean.FALSE.toString());
    }

    public void rectifySASOOORoomsCount() {
        List<Object> histIncomingMetadatas = crudService.findByNamedQuery(OperaIncomingMetadata.GET_BY_PAST_DAYS
                , QueryParameter.with("pastDays", "700").parameters());
        OperaIncomingMetadata incomingMetadata = (OperaIncomingMetadata) histIncomingMetadatas.get(0);
        LocalDate minPastOccDt = null;
        LocalDate maxPastOccDt = null;
        LocalDate histBusinessDt = new LocalDate(incomingMetadata.getBusinessDate());
        Integer pastDays = Integer.parseInt(incomingMetadata.getPastDays());
        minPastOccDt = histBusinessDt.plusDays(-1 * pastDays);
        maxPastOccDt = histBusinessDt.plusDays(-1);
        LOGGER.info("Rectifying OOO using data from feed with business date = " + histBusinessDt);
        LOGGER.info("Rectifying OOO using data for occupancy date starting = " + minPastOccDt);
        LOGGER.info("Rectifying OOO using data for occupancy date ending = " + maxPastOccDt);
        rectifyOperaOOOSASService.rectifyOOORoomsCountInSAS(minPastOccDt, maxPastOccDt);
    }

}
