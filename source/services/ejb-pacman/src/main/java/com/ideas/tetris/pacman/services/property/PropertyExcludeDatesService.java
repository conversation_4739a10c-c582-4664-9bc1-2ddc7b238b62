package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.property.dto.ConfigOperation;
import com.ideas.tetris.pacman.services.property.dto.Property;
import com.ideas.tetris.pacman.services.property.dto.PropertyExcludedDates;
import com.ideas.tetris.pacman.services.sas.entity.MarkDateDataType;
import com.ideas.tetris.pacman.services.sas.entity.MarkPropertyDate;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PropertyExcludeDatesService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;
    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;
    @Autowired
	protected PacmanConfigParamsService configParamsService;
    @Autowired
	protected AbstractMultiPropertyCrudService multiPropertyCrudService;

    private static final Logger LOGGER = Logger.getLogger(PropertyExcludeDatesService.class.getName());


    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public void setMultiPropertyCrudService(AbstractMultiPropertyCrudService multiPropertyCrudService) {
        this.multiPropertyCrudService = multiPropertyCrudService;
    }

    public void setConfigParamsService(PacmanConfigParamsService configParamsService) {
        this.configParamsService = configParamsService;
    }

    public void processExcludedDates(Integer propertyId, List<PropertyExcludedDates> excludedDatesList) {
        if (propertyId == null || excludedDatesList == null || excludedDatesList.isEmpty()) {
            return;
        }
        excludedDatesList = moveDeletesToFrontOfList(excludedDatesList);
        for (PropertyExcludedDates excludedDates : excludedDatesList) {
            switch (excludedDates.getOperation()) {
                case VIEW:
                    // do nothing
                    break;
                case CREATE:
                    createExcludedDates(propertyId, excludedDates);
                    break;
                case UPDATE:
                    updateExcludedDates(propertyId, excludedDates);
                    break;
                case DELETE:
                    deleteExcludedDates(propertyId, excludedDates);
                    break;
                default:
                    throw new TetrisException(ErrorCode.INVALID_INPUT,
                            "Invalid operation: " + excludedDates.getOperation(), null);
            }
        }
    }

    public List<PropertyExcludedDates> moveDeletesToFrontOfList(List<PropertyExcludedDates> excludedDatesList) {
        List<PropertyExcludedDates> sortedList = new ArrayList<PropertyExcludedDates>();
        for (PropertyExcludedDates item : excludedDatesList) {
            if (item.getOperation().equals(ConfigOperation.DELETE)) {
                sortedList.add(0, item);
            } else {
                sortedList.add(item);
            }
        }
        return sortedList;
    }

    @SuppressWarnings("unchecked")
    public void updateExcludedDates(Integer propertyId, PropertyExcludedDates excludedDates) {
        Date startDate = stripTime(excludedDates.getStartDate());
        Date endDate = stripTime(excludedDates.getEndDate());
        Integer entityId = excludedDates.getEntityId();
        if (startDate == null || endDate == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Start Date and/or End Date missing or invalid", null);
        }
        if (entityId == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "entityID missing", null);
        }
        if (startDate.after(endDate)) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Start Date cannot be after End Date", null);
        }
        MarkPropertyDate entity = tenantCrudService.find(MarkPropertyDate.class, entityId);
        if (entity == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "No MarkPropertyDate entity with ID: " + entityId, null);
        }
        if (!entity.getPropertyId().equals(propertyId)) {
            throw new TetrisException(ErrorCode.INVALID_INPUT,
                    "Invalid MarkPropertyDate entityId for property with ID: " + propertyId, null);
        }
        if (datesOverlap(propertyId, startDate, endDate, entityId)) {
            throw new TetrisException(ErrorCode.INVALID_INPUT,
                    "Start and End Date overlap with existing excluded date range", null);
        }
        List<MarkPropertyDate> entities = tenantCrudService.findByNamedQuery(
                MarkPropertyDate.BY_PROPERTY_ID_AND_DATE_RANGE,
                QueryParameter.with("propertyId", propertyId).and("startDate", startDate).and("endDate", endDate)
                        .parameters());
        if (entities != null && !entities.isEmpty()) {
            entity = entities.get(0);
            if (entity.getStatusId().equals(Constants.ACTIVE_STATUS_ID)) {
                if (!entity.getId().equals(entityId)) {
                    throw new TetrisException(ErrorCode.DUPLICATE_DATA, "Duplicate Start and End Date", null);
                }
            } else {
                entity.setStatusId(Constants.ACTIVE_STATUS_ID);
                entity.setModifiedDate(LocalDateTime.now());
                entity.setIpConfigMarkDateDataType(excludedDates.getConfigDataID());
                tenantCrudService.save(entity);
                return;
            }
        }
        entity.setStartDate(startDate);
        entity.setEndDate(endDate);
        entity.setModifiedDate(LocalDateTime.now());
        entity.setStatusId(Constants.ACTIVE_STATUS_ID);
        entity.setIpConfigMarkDateDataType(excludedDates.getConfigDataID());
        entity.setNotes(excludedDates.getNotes());
        tenantCrudService.save(entity);
    }

    @SuppressWarnings("unchecked")
    public void createExcludedDates(Integer propertyId, PropertyExcludedDates excludedDates) {
        Date startDate = stripTime(excludedDates.getStartDate());
        Date endDate = stripTime(excludedDates.getEndDate());
        if (startDate == null || endDate == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Start Date and/or End Date missing or invalid", null);
        }
        if (startDate.after(endDate)) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Start Date cannot be after End Date", null);
        }
        if (datesOverlap(propertyId, startDate, endDate, null)) {
            throw new TetrisException(ErrorCode.INVALID_INPUT,
                    "Start and End Date overlap with existing excluded date range", null);
        }
        MarkPropertyDate entity;
        List<MarkPropertyDate> entities = tenantCrudService.findByNamedQuery(
                MarkPropertyDate.BY_PROPERTY_ID_AND_DATE_RANGE,
                QueryParameter.with("propertyId", propertyId).and("startDate", startDate).and("endDate", endDate)
                        .parameters());
        if (entities != null && !entities.isEmpty()) {
            entity = entities.get(0);
            if (entity.getStatusId().equals(Constants.ACTIVE_STATUS_ID)) {
                throw new TetrisException(ErrorCode.DUPLICATE_DATA, "Duplicate Start and End Date", null);
            } else {
                entity.setStatusId(Constants.ACTIVE_STATUS_ID);
                entity.setIpConfigMarkDateDataType(excludedDates.getConfigDataID());
                tenantCrudService.save(entity);
                return;
            }
        }
        entity = new MarkPropertyDate();
        entity.setPropertyId(propertyId);
        entity.setStatusId(Constants.ACTIVE_STATUS_ID);
        entity.setStartDate(startDate);
        entity.setEndDate(endDate);
        entity.setNotes(excludedDates.getNotes());
        entity.setIpConfigMarkDateDataType(excludedDates.getConfigDataID());
        tenantCrudService.save(entity);
    }

    private void deleteExcludedDates(Integer propertyId, PropertyExcludedDates excludedDates) {
        Integer entityId = excludedDates.getEntityId();
        if (entityId == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Must provide entityId", null);
        }
        MarkPropertyDate entity = tenantCrudService.find(MarkPropertyDate.class, entityId);
        if (entity == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "No MarkPropertyDate entity with ID: " + entityId, null);
        }
        if (!entity.getPropertyId().equals(propertyId)) {
            throw new TetrisException(ErrorCode.INVALID_INPUT,
                    "Invalid MarkPropertyDate entityId for property with ID: " + propertyId, null);
        }
        entity.setStatusId(Constants.INACTIVE_STATUS_ID);
        entity.setModifiedDate(LocalDateTime.now());
        tenantCrudService.save(entity);
    }
    public void disableMarkPropertyDates(MarkPropertyDate markPropertyDate) {
        MarkPropertyDate entity = tenantCrudService.find(MarkPropertyDate.class, markPropertyDate.getId());
        if (entity != null) {
            entity.setStatusId(2);
            tenantCrudService.save(entity);
        }
    }

    private String buildContext(String propertyCode, String clientCode) {

        return "pacman." + clientCode + "." + propertyCode;
    }

    private Date stripTime(Date dateTime) {
        if (dateTime == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateTime);
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    @SuppressWarnings("unchecked")
    private boolean datesOverlap(Integer propertyId, Date startDate, Date endDate, Integer excludeEntityId) {
        List<MarkPropertyDate> entities = getAlreadyMarkedPropertyDate(propertyId);
        for (MarkPropertyDate entity : entities) {
            if (excludeEntityId == null || !entity.getId().equals(excludeEntityId)) {
                if (dateOverlap(startDate, endDate, entity.getStartDate(), entity.getEndDate())) {
                    return true;
                }
            }
        }
        return false;
    }

    public List<MarkPropertyDate> getAlreadyMarkedPropertyDate(Integer propertyId) {
        return tenantCrudService.findByNamedQuery(
                MarkPropertyDate.BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
    }

    public List<MarkDateDataType> getMarkedDateDataType() {
        return tenantCrudService.findByNamedQuery(
                MarkDateDataType.GET_ALL_DATATYPE, null);
    }

    public boolean dateOverlap(Date startDate, Date endDate, Date startDate2, Date endDate2) {
        return !endDate.before(startDate2) && !startDate.after(endDate2);
    }

    public PropertyExcludedDates createPropertyExcludedDate(Date startDate, Date endDate, String notes) {
        PropertyExcludedDates propertyExcludedDateNew = new PropertyExcludedDates();
        propertyExcludedDateNew.setStartDate(startDate);
        propertyExcludedDateNew.setEndDate(endDate);
        propertyExcludedDateNew.setNotes(notes);
        return propertyExcludedDateNew;
    }


}
