package com.ideas.tetris.pacman.services.rates;

import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.util.Date;

public class RateDetailKey {
    private final Date startDate;
    private final Date endDate;
    private final Integer accomTypeId;

    public RateDetailKey(Date startDate, Date endDate, Integer accomTypeId) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.accomTypeId = accomTypeId;
    }

    public boolean isEligibleForSplitMerge(RateDetailKey toBeAdded) {
        return isSameAccomType(toBeAdded) && isOvelapping(toBeAdded);
    }

    private boolean isSameAccomType(RateDetailKey toBeAdded) {
        return this.accomTypeId.equals(toBeAdded.accomTypeId);
    }

    private boolean isOvelapping(RateDetailKey toBeAdded) {
        return DateUtil.isDateBetween(toBeAdded.startDate, toBeAdded.endDate, this.startDate) ||
                DateUtil.isDateBetween(this.startDate, this.endDate, toBeAdded.startDate);
    }

    public Integer getAccomTypeId() {
        return accomTypeId;
    }

    @Override
    public boolean equals(Object keyToCompare) {
        if (this == keyToCompare) {
            return true;
        }

        if (keyToCompare == null) {
            return false;
        }

        if (!(keyToCompare.getClass() == this.getClass())) {
            return false;
        }

        RateDetailKey that = (RateDetailKey) keyToCompare;

        return new EqualsBuilder()
                .append(startDate, that.startDate)
                .append(endDate, that.endDate)
                .append(accomTypeId, that.accomTypeId)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(startDate)
                .append(endDate)
                .append(accomTypeId)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "RateDetailKey{" +
                "startDate=" + startDate +
                ", endDate=" + endDate +
                ", accomTypeId=" + accomTypeId +
                '}';
    }
}
