package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.archive.ArchiveService;
import com.ideas.tetris.pacman.services.extract.CrsFileUtil;
import com.ideas.tetris.pacman.services.property.dto.*;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.log4j.Logger;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;
import javax.inject.Inject;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.util.Runner.runIfTrue;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class ExtractMapperService implements ExtractMapperServiceLocal {
    private static final Logger LOGGER = Logger.getLogger(ExtractMapperService.class.getName());

    @Autowired
	protected ClientPropertyCacheService clientPropertyCacheService;

    @Autowired
	protected PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
	protected ArchiveService archiveService;

    public Map<Integer, ExtractDetails> mapExtractsOnDisk(Integer propertyId) {
        return mapExtractsOnDisk(getPropertyById(propertyId));
    }

    public Map<Integer, ExtractDetails> mapExtractsOnDisk(Property property) {
        if (property == null) {
            return null;
        }
        List<Property> properties = new ArrayList<>();
        properties.add(property);
        Map<Integer, ExtractDetails> map = mapExtractsOnDisk(properties);
        postConstructDetails(map);
        return map;
    }

    // used for newly added properties that won't show up in the ConsolidatedPropertyView yet
    public Map<Integer, ExtractDetails> mapExtractsOnDisk(Integer propertyId, String clientCode, String propertyCode) {
        Property property = new Property();
        property.setId(propertyId);
        property.setClient(new Client());
        property.getClient().setCode(clientCode);
        property.setCode(propertyCode);
        List<Property> properties = new ArrayList<>();
        properties.add(property);
        Map<Integer, ExtractDetails> map = mapExtractsOnDisk(properties);
        postConstructDetails(map);
        return map;
    }

    public Map<Integer, WebRateExtractDetails> mapWebRateExtractsOnDisk(Integer propertyId) {
        return mapWebRateExtractsOnDisk(getPropertyById(propertyId));
    }

    public Map<Integer, WebRateExtractDetails> mapWebRateExtractsOnDisk(Property property) {
        if (property == null) {
            return null;
        }
        List<Property> properties = new ArrayList<>();
        properties.add(property);
        Map<Integer, WebRateExtractDetails> map = mapWebRateExtractsOnDisk(properties);
        postConstructWebRateDetails(map);
        return map;
    }

    // used for newly added properties that won't show up in the ConsolidatedPropertyView yet
    public Map<Integer, WebRateExtractDetails> mapWebRateExtractsOnDisk(Integer propertyId, String clientCode, String propertyCode) {
        Property property = new Property();
        property.setId(propertyId);
        property.setClient(new Client());
        property.getClient().setCode(clientCode);
        property.setCode(propertyCode);
        List<Property> properties = new ArrayList<>();
        properties.add(property);
        Map<Integer, WebRateExtractDetails> map = mapWebRateExtractsOnDisk(property);
        postConstructWebRateDetails(map);
        return map;
    }

    private void postConstructDetails(Map<Integer, ExtractDetails> map) {
        for (ExtractDetails details : map.values()) {
            details.postConstruct();
        }
    }

    private void postConstructWebRateDetails(Map<Integer, WebRateExtractDetails> map) {
        for (WebRateExtractDetails details : map.values()) {
            details.postConstruct();
        }
    }

    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.NEVER)
    public Map<Integer, ExtractDetails> mapExtractsOnDiskAllProperties(List<Property> properties) {
        Map<Integer, ExtractDetails> map = mapExtractsOnDisk(properties);
        postConstructDetails(map);
        return map;
    }

    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.NEVER)
    public Map<Integer, WebRateExtractDetails> mapWebRateExtractsOnDiskAllProperties(List<Property> properties) {
        Map<Integer, WebRateExtractDetails> map = mapWebRateExtractsOnDisk(properties);
        postConstructWebRateDetails(map);
        return map;
    }

    private Map<Integer, ExtractDetails> mapExtractsOnDisk(List<Property> properties) {
        Map<Integer, ExtractDetails> map = new HashMap<Integer, ExtractDetails>();
        File incomingRoot = new File(SystemConfig.getCrsIncomingFolder());
        mapExtractsOnDisk(map, incomingRoot, AbstractExtractDetails.INCOMING, properties);
        File archivedRoot = new File(SystemConfig.getCrsArchiveFolder());
        mapExtractsOnDisk(map, archivedRoot, AbstractExtractDetails.ARCHIVED, properties);
        return map;
    }

    private Map<Integer, WebRateExtractDetails> mapWebRateExtractsOnDisk(List<Property> properties) {
        Map<Integer, WebRateExtractDetails> map = new HashMap<Integer, WebRateExtractDetails>();
        File incomingRoot = new File(SystemConfig.getRssIncomingFolder());
        mapWebRateExtractsOnDisk(map, incomingRoot, WebRateExtractDetails.INCOMING, properties);
        File archivedRoot = new File(SystemConfig.getRssArchiveFolder());
        mapWebRateExtractsOnDisk(map, archivedRoot, WebRateExtractDetails.ARCHIVED, properties);
        return map;
    }

    private void mapExtractsOnDisk(Map<Integer, ExtractDetails> map, File rootDirectory, String type, List<Property> properties) {
        if (!rootDirectory.exists()) {
            LOGGER.error("extract root directory does not exist: " + rootDirectory.getAbsolutePath());
            return;
        }
        if (!rootDirectory.isDirectory()) {
            LOGGER.error("extract root directory is not a directory: " + rootDirectory.getAbsolutePath());
            return;
        }
        addPropertyExtractDetails(map, properties);

        addExtractFiles(map, rootDirectory, type, properties);

        runIfTrue(AbstractExtractDetails.ARCHIVED.equals(type), () -> addExtractFilesUsingCloud(map, rootDirectory, type, properties));

    }

    private void addExtractFilesUsingCloud(Map<Integer, ExtractDetails> map, File rootDirectory, String type, List<Property> properties) {
        List<Property> cloudEnabledProperties = properties.stream().filter(this::isEligibleForCloudQuery).collect(Collectors.toList());
        LOGGER.info("Cloud enabled properties : " + cloudEnabledProperties);
        if (CollectionUtils.isNotEmpty(cloudEnabledProperties)) {
            ExtractFileFilter fileFilter = new ExtractFileFilter();
            cloudEnabledProperties
                    .forEach(property ->
                            queryCloudAndBuildExtract(map, type, property, fileFilter, rootDirectory.getAbsolutePath()));
        }
    }

    private void queryCloudAndBuildExtract(Map<Integer, ExtractDetails> map, String type, Property property, ExtractFileFilter fileFilter, String rootDir) {
        File[] monthlyDirectories = archiveService.getLastNMonthsPath(rootDir, property.getId());
        if (ArrayUtils.isNotEmpty(monthlyDirectories)) {
            boolean useS3RefreshableConnection = pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_S3_REFRESHABLE_CONNECTION.getParameterName(), property.getClient().getCode(), property.getCode());
            Arrays.stream(monthlyDirectories)
                    .forEach(monthlyDirectory -> {
                        ExtractDetails details = map.get(property.getId());
                        String propertyRootDir = CrsFileUtil.buildPropertyExtractPath(monthlyDirectory.getAbsolutePath(), property);
                        LOGGER.info("Scanning S3 Objects for Property Root Directory : " + propertyRootDir);
                        addExtract(type, details, archiveService.listFilesFromCloud(propertyRootDir, fileFilter, useS3RefreshableConnection));
                    });
        }
    }

    private boolean isEligibleForCloudQuery(Property property) {
        return pacmanConfigParamsService.getBooleanParameterValue(
                PreProductionConfigParamName.USE_S3_FOR_ARCHIVE_EXTRACTS.getParameterName(),
                property.getClient().getCode(),
                property.getCode());
    }

    private void addExtractFiles(Map<Integer, ExtractDetails> map, File rootDirectory, String type, List<Property> properties) {
        ExtractFileFilter fileFilter = new ExtractFileFilter();
        File[] monthlyDirectories = rootDirectory.listFiles(new MonthFolderFileFilter());
        for (File monthlyDirectory : monthlyDirectories) {
            properties.parallelStream().forEach(property -> {
                ExtractDetails details = map.get(property.getId());
                File propertyDirectory = new File(monthlyDirectory.getAbsolutePath() + "/" +
                        property.getClient().getCode() + "/" + property.getCode());
                if (propertyDirectory.exists() && propertyDirectory.isDirectory()) {
                    addExtract(type, fileFilter, details, propertyDirectory);
                }
            });
        }
    }

    private void addExtract(String type, ExtractFileFilter fileFilter, ExtractDetails details, File propertyDirectory) {
        File[] extracts = propertyDirectory.listFiles(fileFilter);
        if (extracts != null) {
            for (File extract : extracts) {
                details.addExtract(extract, type);
            }
        }
    }

    private void addExtract(String type, ExtractDetails details, File[] extracts) {
        if (extracts != null) {
            for (File extract : extracts) {
                if (details.checkIfNotExists(extract)) {
                    LOGGER.info("Adding extract : " + extract);
                    details.addExtract(extract, type);
                }
            }
        }
    }


    private void addPropertyExtractDetails(Map<Integer, ExtractDetails> map, List<Property> properties) {
        for (Property property : properties) {
            ExtractDetails details = map.get(property.getId());
            if (details == null) {
                details = new ExtractDetails();
                map.put(property.getId(), details);
            }
        }
    }

    private void mapWebRateExtractsOnDisk(Map<Integer, WebRateExtractDetails> map, File rootDirectory, String type, List<Property> properties) {
        if (!rootDirectory.exists()) {
            LOGGER.error("web rate extract root directory does not exist: " + rootDirectory.getAbsolutePath());
            return;
        }
        if (!rootDirectory.isDirectory()) {
            LOGGER.error("web rate extract root directory is not a directory: " + rootDirectory.getAbsolutePath());
            return;
        }
        addWebrateExtractPropertyDetails(map, properties);

        addWebrateExtractFiles(map, rootDirectory, type, properties);
    }

    private void addWebrateExtractFiles(Map<Integer, WebRateExtractDetails> map, File rootDirectory, String type, List<Property> properties) {
        WebRateExtractFileFilter fileFilter = new WebRateExtractFileFilter();
        File[] monthlyDirectories = rootDirectory.listFiles(new MonthFolderFileFilter());
        for (File monthlyDirectory : monthlyDirectories) {
            properties.parallelStream().forEach(property -> {
                WebRateExtractDetails details = map.get(property.getId());
                File propertyDirectory = new File(monthlyDirectory.getAbsolutePath() + "/" +
                        property.getClient().getCode() + "/" + property.getCode());
                if (propertyDirectory.exists() && propertyDirectory.isDirectory()) {
                    addAllExtract(type, fileFilter, details, propertyDirectory);
                }
            });
        }
    }

    private void addAllExtract(String type, WebRateExtractFileFilter fileFilter, WebRateExtractDetails details, File propertyDirectory) {
        File[] extracts = propertyDirectory.listFiles(fileFilter);
        if (extracts != null) {
            for (File extract : extracts) {
                details.addExtract(extract, type);
            }
        }
    }

    private void addWebrateExtractPropertyDetails(Map<Integer, WebRateExtractDetails> map, List<Property> properties) {
        for (Property property : properties) {
            WebRateExtractDetails details = map.get(property.getId());
            if (details == null) {
                details = new WebRateExtractDetails();
                map.put(property.getId(), details);
            }
        }
    }

    private Property getPropertyById(Integer propertyId) {
        return clientPropertyCacheService.getProperty(propertyId);
    }

    @Override
    public Map<Integer, PacmanExtractDetails> mapPacmanExtractsOnDisk(Integer propertyId) {
        Property property = getPropertyById(propertyId);
        if (property == null) {
            return null;
        }
        List<Property> properties = new ArrayList<>();
        properties.add(property);
        return mapPacmanExtractsOnDisk(properties);
    }

    private Map<Integer, PacmanExtractDetails> mapPacmanExtractsOnDisk(List<Property> properties) {
        Map<Integer, PacmanExtractDetails> map = new HashMap<Integer, PacmanExtractDetails>();
        File archivedRoot = new File(SystemConfig.getExtractArchiveFolder());
        mapPacmanExtractsOnDisk(map, archivedRoot, properties);
        return map;
    }

    private void mapPacmanExtractsOnDisk(Map<Integer, PacmanExtractDetails> map, File rootDirectory, List<Property> properties) {
        if (!rootDirectory.exists()) {
            LOGGER.error("pacman extract root directory does not exist: " + rootDirectory.getAbsolutePath());
            return;
        }
        if (!rootDirectory.isDirectory()) {
            LOGGER.error("pacman extract root directory is not a directory: " + rootDirectory.getAbsolutePath());
            return;
        }
        addPacmanExtractPropertyDetails(map, properties);

        addPacmanExtractFileDetails(map, rootDirectory, properties);
    }

    private void addPacmanExtractFileDetails(Map<Integer, PacmanExtractDetails> map, File rootDirectory, List<Property> properties) {
        PacmanExtractFileFilter fileFilter = new PacmanExtractFileFilter();
        File[] monthlyDirectories = rootDirectory.listFiles(new MonthFolderFileFilter());
        for (File monthlyDirectory : monthlyDirectories) {
            for (Property property : properties) {
                PacmanExtractDetails details = map.get(property.getId());
                File propertyDirectory = new File(monthlyDirectory.getAbsolutePath() + "/" +
                        property.getClient().getCode() + "/" + property.getCode());
                if (propertyDirectory.exists() && propertyDirectory.isDirectory()) {
                    addPacmanExtract(fileFilter, details, propertyDirectory);
                }
            }
        }
    }

    private void addPacmanExtract(PacmanExtractFileFilter fileFilter, PacmanExtractDetails details, File propertyDirectory) {
        File[] extracts = propertyDirectory.listFiles(fileFilter);
        if (extracts != null) {
            for (File extract : extracts) {
                details.addExtract(extract);
            }
        }
    }

    private void addPacmanExtractPropertyDetails(Map<Integer, PacmanExtractDetails> map, List<Property> properties) {
        for (Property property : properties) {
            PacmanExtractDetails details = map.get(property.getId());
            if (details == null) {
                details = new PacmanExtractDetails();
                map.put(property.getId(), details);
            }
        }
    }
}
