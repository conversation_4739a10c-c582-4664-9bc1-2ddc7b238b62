package com.ideas.tetris.pacman.services.reports.restrictionlevel;

import com.ideas.tetris.pacman.services.reports.restrictionlevel.dto.RestrictionLevelReportDTO;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Transactional
public class MinMaxLosFullReport extends RestrictionLevelReport {

    @Override
    protected RestrictionLevelReportDTO getRowMappedFor(Object[] row) {
        return RestrictionLevelReportDTO.newMinLosReportFor(row);
    }

    @Override
    protected String getQuery() {
        return "exec dbo.usp_restriction_level_report_by_minlos_full :start_date, :end_date, :caught_date, :accom_type_id, :isRollingDate, :rollingStartDate, :rollingEndDate, :isDecisionAtHotelLevel";
    }
}
