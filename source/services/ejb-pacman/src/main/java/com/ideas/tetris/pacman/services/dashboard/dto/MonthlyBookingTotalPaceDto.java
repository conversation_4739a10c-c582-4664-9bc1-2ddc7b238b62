package com.ideas.tetris.pacman.services.dashboard.dto;

import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Key;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.MultiPropertyAggregate;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.annotation.Sum;

import java.math.BigDecimal;

@MultiPropertyAggregate
public class MonthlyBookingTotalPaceDto {

    @Key
    private String businessDayEnd;

    @Sum
    private BigDecimal roomsSold;

    @Sum
    private Integer countOfRoomsSold;

    @Sum
    private BigDecimal roomsRevenue;

    @Sum
    private BigDecimal onBooksAdr;

    public String getBusinessDayEnd() {
        return businessDayEnd;
    }

    public void setBusinessDayEnd(String businessDayEnd) {
        this.businessDayEnd = businessDayEnd;
    }

    public BigDecimal getRoomsSold() {
        return roomsSold;
    }

    public void setRoomsSold(BigDecimal roomsSold) {
        this.roomsSold = roomsSold;
    }

    public Integer getCountOfRoomsSold() {
        return countOfRoomsSold;
    }

    public void setCountOfRoomsSold(Integer countOfRoomsSold) {
        this.countOfRoomsSold = countOfRoomsSold;
    }

    public BigDecimal getRoomsRevenue() {
        return roomsRevenue;
    }

    public void setRoomsRevenue(BigDecimal roomsRevenue) {
        this.roomsRevenue = roomsRevenue;
    }

    public BigDecimal getOnBooksAdr() {
        return onBooksAdr;
    }

    public void setOnBooksAdr(BigDecimal onBooksAdr) {
        this.onBooksAdr = onBooksAdr;
    }
}
