package com.ideas.tetris.pacman.services.license.feature;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity;
import com.ideas.tetris.pacman.services.informationmanager.service.InformationManagerCleanupService;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.license.LicenseService;
import com.ideas.tetris.platform.common.license.entities.LicensePackage;
import com.ideas.tetris.platform.common.license.migration.actions.LicenseFeatureUpgradable;
import com.ideas.tetris.platform.common.license.util.LicenseFeatureConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class InformationManagerNotificationFeature extends LicenseFeatureUpgradable {
    @Autowired
    protected InformationManagerCleanupService informationManagerCleanUpService;

    @Autowired
    private AbstractMultiPropertyCrudService multiPropertyCrudService;

    @Autowired
    private LicenseService licenseService;
    private Set<String> currentPackageNotificationTypes;
    private Set<String> newPackageNotificationTypes;
    private boolean isCleanUpRequired;

    @Override
    public String getFeatureCode() {
        return LicenseFeatureConstants.INFO_MGR_NOTIFICATIONS;
    }

    @Override
    protected boolean isCleanUpRequired(LicensePackage currentLicensePackage, LicensePackage newLicensePackage, Map<String, Object> featuresInputMapToDowngrade) {
        currentPackageNotificationTypes = licenseService.getLicenseFeatureCodes(Constants.INFO_MANAGER_NOTIFICATION_TYPE, List.of(currentLicensePackage));
        newPackageNotificationTypes = licenseService.getLicenseFeatureCodes(Constants.INFO_MANAGER_NOTIFICATION_TYPE, List.of(newLicensePackage));
        currentPackageNotificationTypes.removeAll(newPackageNotificationTypes);
        return (isCleanUpRequired = super.isCleanUpRequired(currentLicensePackage, newLicensePackage, featuresInputMapToDowngrade)) || !currentPackageNotificationTypes.isEmpty();
    }

    @Override
    protected void cleanUp(int propertyId, LicensePackage currentLicensePackage, LicensePackage newLicensePackage, Map<String, Object> featuresInputMapToDowngrade) {
        if (isCleanUpRequired) {
            deleteNotificationsAndConfigs(propertyId);
        } else if (!currentPackageNotificationTypes.isEmpty()) {
            retainNewPackageNotifications(propertyId);
        }
    }

    private void retainNewPackageNotifications(int propertyId) {
        Map<String, Object> params = Map.of("notificationTypes", currentPackageNotificationTypes);
        List<InformationMgrAlertConfigEntity> notificationConfigs = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, InformationMgrAlertConfigEntity.FIND_BY_TYPE, params);
        notificationConfigs.forEach(config -> informationManagerCleanUpService.deleteExceptionConfiguration(propertyId, config.getId()));
    }

    private void deleteNotificationsAndConfigs(int propertyId) {
        List<InformationMgrAlertConfigEntity> notificationConfigs = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, InformationMgrAlertConfigEntity.FIND_BY_PROPERTY_ID_ALERT_CATEGORY,
                QueryParameter.with(Constants.PROPERTY_ID, propertyId).and("category", Constants.EXCEPTION_CATEGORY).parameters());
        notificationConfigs.forEach(config -> informationManagerCleanUpService.deleteExceptionConfiguration(propertyId, config.getId()));
    }

}
