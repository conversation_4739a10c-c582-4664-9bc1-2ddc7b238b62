package com.ideas.tetris.pacman.services.security.dto;

public class Name {
    private String firstName;
    private String middleName;
    private String lastName;

    public Name() {

    }

    public Name(String firstName, String middleName, String lastName) {
        setFirstName(firstName);
        setMiddleName(middleName);
        setLastName(lastName);
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFullName() {
        StringBuilder buffer = new StringBuilder();
        if (firstName != null) {
            buffer.append(firstName);
        }
        if (middleName != null) {
            buffer.append(" ").append(middleName);
        }
        if (lastName != null) {
            buffer.append(" ").append(lastName);
        }
        return buffer.toString();
    }

}
