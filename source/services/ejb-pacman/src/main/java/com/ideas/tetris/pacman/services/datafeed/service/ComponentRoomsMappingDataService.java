package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.ComponentRoomMappingDTO;
import com.ideas.tetris.platform.common.crudservice.CrudService;

import javax.inject.Inject;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class ComponentRoomsMappingDataService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    public boolean loadComponentRoomsMappingDataIntoPacman(List<ComponentRoomMappingDTO> data) {

        boolean isDataSaved = false;
        StringBuilder query = new StringBuilder();
        data.forEach(componentRoomMappingDTO -> {
            query.append("INSERT [dbo].[CR_Accom_Type_Mapping] ( [Property_ID], [CR_Accom_Type_ID], [CP_Accom_Type_ID], [CP_Accom_Type_Quantity]) VALUES  (010027,1,2," + componentRoomMappingDTO.getCpRoomTypeQuantity() + ");");
            query.append("INSERT [dbo].[CR_Accom_Type_Mapping] ( [Property_ID], [CR_Accom_Type_ID], [CP_Accom_Type_ID], [CP_Accom_Type_Quantity]) VALUES  (010027,3,4," + componentRoomMappingDTO.getCpRoomTypeQuantity() + ");");
        });

        if (!query.toString().isEmpty()) {
            tenantCrudService.executeUpdateByNativeQuery(query.toString());
            isDataSaved = true;
        }
        return isDataSaved;
    }

    public boolean deleteData() {
        tenantCrudService.executeUpdateByNativeQuery("Delete from [dbo].[CR_Accom_Type_Mapping] where property_id=010027 and CP_Accom_Type_Quantity = 111");
        return true;
    }

}
