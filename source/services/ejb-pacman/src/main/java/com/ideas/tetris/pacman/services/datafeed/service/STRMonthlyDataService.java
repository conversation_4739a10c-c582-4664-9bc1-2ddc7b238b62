package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.str.dto.STRMonthlyDTO;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Transactional
public class STRMonthlyDataService {

    @TenantCrudServiceBean.Qualifier
	@Qualifier("tenantCrudServiceBean")
    @Autowired
	private CrudService tenantCrudService;

    public Boolean loadStrDailyDataIntoPacman(List<STRMonthlyDTO> data) {

        boolean isDataSaved = false;
        StringBuilder query = new StringBuilder();

        data.forEach(strDailyDTO -> {
            query.append("insert into STR_Monthly ([Property_ID],[Chain_ID], [Mgmt_ID], [Owner_ID], [Hotel_Name], [Date], [Property_Avail], [Property_Sold], [Property_Rev], [Comp_Set_Avail], [Comp_Set_Sold], [Comp_Set_Rev]) values(");
            query.append("010027,7777, 0, 0," + "'" + "Sample Hotel Name" + "'");
            query.append("," + "'" + strDailyDTO.getOccupancyDate() + "'");
            query.append("," + strDailyDTO.getPropertyAvailable());
            query.append("," + strDailyDTO.getPropertySold());
            query.append("," + strDailyDTO.getPropertyRevenue());
            query.append("," + strDailyDTO.getCompetitiveSetAvailable());
            query.append("," + strDailyDTO.getCompetitiveSetSold());
            query.append("," + strDailyDTO.getCompetitiveSetRevenue() + ");");

        });

        if (!query.toString().isEmpty()) {
            tenantCrudService.executeUpdateByNativeQuery(query.toString());
            isDataSaved = true;
        }
        return isDataSaved;
    }

    public Boolean deleteData() {
        tenantCrudService.executeUpdateByNativeQuery("Delete from [dbo].[STR_Monthly] where chain_id=7777");
        return true;
    }

}
