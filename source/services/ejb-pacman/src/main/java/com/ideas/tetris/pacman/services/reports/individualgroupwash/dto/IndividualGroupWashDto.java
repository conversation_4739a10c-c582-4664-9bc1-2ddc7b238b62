package com.ideas.tetris.pacman.services.reports.individualgroupwash.dto;

import java.math.BigDecimal;
import java.util.Date;

public class IndividualGroupWashDto {
    private String groupCode;
    private String name;
    private String code;
    private String forecastGroupName;
    private Date startDate;
    private Date endDate;
    private String salesPerson;

    private String groupStatusCode;

    private String pickupTypeAsText;
    private Date cutoffDate;
    private String overrideStatusAsText;
    private Date overrideDate;
    private String userName;
    private String dow;

    private Date occupancyDate;

    private int groupArrivals;
    private int groupDepartures;
    private Integer block;
    private Integer pickup;
    private Integer availableBlock;
    private int pickupVariance;
    private BigDecimal rateValue;
    private BigDecimal systemWashValue;
    private BigDecimal systemWashPercent;
    private BigDecimal userWashValue;
    private BigDecimal userWashPercent;
    private Integer daysToArrival;
    private Date expirationDate;
    private String notesAsText;

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getForecastGroupName() {
        return forecastGroupName;
    }

    public void setForecastGroupName(String forecastGroupName) {
        this.forecastGroupName = forecastGroupName;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getSalesPerson() {
        return salesPerson;
    }

    public void setSalesPerson(String salesPerson) {
        this.salesPerson = salesPerson;
    }

    public String getGroupStatusCode() {
        return groupStatusCode;
    }

    public void setGroupStatusCode(String groupStatusCode) {
        this.groupStatusCode = groupStatusCode;
    }

    public String getPickupTypeAsText() {
        return pickupTypeAsText;
    }

    public void setPickupTypeAsText(String pickupTypeAsText) {
        this.pickupTypeAsText = pickupTypeAsText;
    }

    public Date getCutoffDate() {
        return cutoffDate;
    }

    public void setCutoffDate(Date cutoffDate) {
        this.cutoffDate = cutoffDate;
    }

    public String getOverrideStatusAsText() {
        return overrideStatusAsText;
    }

    public void setOverrideStatusAsText(String overrideStatusAsText) {
        this.overrideStatusAsText = overrideStatusAsText;
    }

    public Date getOverrideDate() {
        return overrideDate;
    }

    public void setOverrideDate(Date overrideDate) {
        this.overrideDate = overrideDate;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public int getGroupArrivals() {
        return groupArrivals;
    }

    public void setGroupArrivals(int groupArrivals) {
        this.groupArrivals = groupArrivals;
    }

    public int getGroupDepartures() {
        return groupDepartures;
    }

    public void setGroupDepartures(int groupDepartures) {
        this.groupDepartures = groupDepartures;
    }

    public Integer getBlock() {
        return block;
    }

    public void setBlock(Integer block) {
        this.block = block;
    }

    public Integer getPickup() {
        return pickup;
    }

    public void setPickup(Integer pickup) {
        this.pickup = pickup;
    }

    public Integer getAvailableBlock() {
        return availableBlock;
    }

    public void setAvailableBlock(Integer availableBlock) {
        this.availableBlock = availableBlock;
    }

    public int getPickupVariance() {
        return pickupVariance;
    }

    public void setPickupVariance(int pickupVariance) {
        this.pickupVariance = pickupVariance;
    }

    public BigDecimal getRateValue() {
        return rateValue;
    }

    public void setRateValue(BigDecimal rateValue) {
        this.rateValue = rateValue;
    }

    public BigDecimal getSystemWashValue() {
        return systemWashValue;
    }

    public void setSystemWashValue(BigDecimal systemWashValue) {
        this.systemWashValue = systemWashValue;
    }

    public BigDecimal getSystemWashPercent() {
        return systemWashPercent;
    }

    public void setSystemWashPercent(BigDecimal systemWashPercent) {
        this.systemWashPercent = systemWashPercent;
    }

    public BigDecimal getUserWashValue() {
        return userWashValue;
    }

    public void setUserWashValue(BigDecimal userWashValue) {
        this.userWashValue = userWashValue;
    }

    public BigDecimal getUserWashPercent() {
        return userWashPercent;
    }

    public void setUserWashPercent(BigDecimal userWashPercent) {
        this.userWashPercent = userWashPercent;
    }

    public Integer getDaysToArrival() {
        return daysToArrival;
    }

    public void setDaysToArrival(Integer daysToArrival) {
        this.daysToArrival = daysToArrival;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getNotesAsText() {
        return notesAsText;
    }

    public void setNotesAsText(String notesAsText) {
        this.notesAsText = notesAsText;
    }

    public String getDow() {
        return dow;
    }

    public void setDow(String dow) {
        this.dow = dow;
    }

}
