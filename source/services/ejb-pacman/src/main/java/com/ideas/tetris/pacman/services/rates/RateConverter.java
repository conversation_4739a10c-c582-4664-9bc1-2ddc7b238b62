package com.ideas.tetris.pacman.services.rates;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.converter.WeeklySeasonsByAccomType;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractDetail;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractRate;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.map.MapUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

@Transactional
@Component
public abstract class RateConverter {
    public static final String NAME = "name";
    public static final String END_DATE = "endDate";
    public static final String START_DATE = "startDate";
    private static final String RATE_DETAIL_IDENTIFIER = "rates";
    private static final String ORIGINAL_RATES = "originalRates";
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    static final String POSTING_RULES = "postingRules";
    static final String AMOUNT = "Amount";
    private static final String PSEUDO_ROOM_DELIMITER = ",";

    private static final BigDecimal NEGATIVE_ONE = new BigDecimal("-1.00");
    private static final Logger LOGGER = Logger.getLogger(RateConverter.class);
    private static final String OVERLAY = "OVERLAY";
    private static final String NEW = "NEW";
    private static final String DELTA = "DELTA";
    public static final int ACTIVE = 1;
    public static final int INACTIVE = 2;
    private static final int ONE_DAY = 1;
    private static final int SIX_DAYS = 6;
    public static final String INV_TYPE_CODE = "invTypeCode";
    @Autowired
	protected DateService dateService;

    @Autowired
	protected AccommodationService accommodationService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    @Autowired
	protected RateAmountCalculator rateAmountCalculator;

    @Autowired
	protected PacmanConfigParamsService configParamsService;

    protected abstract void deleteDetailsStartingFrom(List<Integer> rateHeaderIds, Date endDate);

    protected abstract AbstractRate getHeader(String name);

    protected abstract List<AbstractDetail> getDetails(List<Integer> ids);

    protected abstract AbstractRate createNewHeader();

    protected abstract AbstractDetail createNewDetail();

    protected abstract AbstractDetail createNewDetail(AbstractDetail detail);

    protected abstract void convertSpecificFields(Map<String, Object> dto, AbstractRate rateHeader);

    protected abstract void setHeaderReferenceOnDetails(AbstractRate id, AbstractDetail detail);

    protected abstract FileMetadata createFileMetadata(Map<String, Object> dto);

    protected abstract void softDeleteHeader(Integer fileMetaDataId, AbstractRate rateHeader, Date caughtUpDate);

    protected abstract void saveRateDetailsInBatch(List<AbstractDetail> detailsToSave);

    public List<AbstractRate> convert(List<? extends Map<String, Object>> listOfRateMaps) {
        LOGGER.info("Rate conversion started");
        boolean convertByMergeSplit= configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DISABLE_WEEKLY_RATES_POPULATION_LOGIC.value());

        var minDates = new ConcurrentHashMap<Integer, Date>();

        List<RateHeaderAndDetail> ratesWithinAcceptableRange = transform(listOfRateMaps, convertByMergeSplit, minDates);
        List<RateHeaderAndDetail> ratesWithCurrentOrFutureSeason = filter(ratesWithinAcceptableRange);
        if (CollectionUtils.isNotEmpty(ratesWithCurrentOrFutureSeason)) {
            persistRateHeaderAndDetails(ratesWithCurrentOrFutureSeason, minDates);
        }

        LOGGER.info("Rate conversion completed");
        return ratesWithCurrentOrFutureSeason.stream().map(RateHeaderAndDetail::getHeader).collect(Collectors.toList());
    }

    private void persistRateHeaderAndDetails(List<RateHeaderAndDetail> ratesWithCurrentOrFutureSeason, Map<Integer, Date> minDates) {
        LOGGER.info("Persisting Rate Headers, Truncating and Deleting existing season details, " +
                "and Setting detail having start date in past as caughtUp date, for new details to be saved");
        saveHeaders(ratesWithCurrentOrFutureSeason);
        preservePastRateDetails(ratesWithCurrentOrFutureSeason, minDates);

        List<AbstractDetail> detailsToSave = new ArrayList<>();
        updateStartDateForDetailsStaringInPastAndPrepareToSave(ratesWithCurrentOrFutureSeason, detailsToSave);

        LOGGER.info(String.format("Persisting %d rate details and season in batch", detailsToSave.size()));
        saveRateDetailsInBatch(detailsToSave);
    }

    private void updateStartDateForDetailsStaringInPastAndPrepareToSave(List<RateHeaderAndDetail> ratesWithCurrentOrFutureSeason, List<AbstractDetail> detailsToSave) {
        ratesWithCurrentOrFutureSeason
                .forEach(rateHeaderAndDetail -> rateHeaderAndDetail.getHeader().getDetails()
                        .forEach(abstractDetail -> {
                            if (abstractDetail.getStartDate().before(DateUtil.removeTimeFromDate(rateHeaderAndDetail.getCaughtUpDate()))) {
                                abstractDetail.setStartDate(DateUtil.removeTimeFromDate(rateHeaderAndDetail.getCaughtUpDate()));
                                abstractDetail.clearOutOfRangeDOWsWithOutRangeCheck();
                            }

                            setHeaderReferenceOnDetails(rateHeaderAndDetail.getHeader(), abstractDetail);
                            abstractDetail.setSkipAudit(true); // don't want to audit rates from NGI
                            detailsToSave.add(abstractDetail);
                        }));
    }

    private List<RateHeaderAndDetail> filter(List<RateHeaderAndDetail> ratesWithinAcceptableRange) {
        filterOutPastDetails(ratesWithinAcceptableRange);
        return filterOutHeadersWithoutDetails(ratesWithinAcceptableRange);
    }

    private List<RateHeaderAndDetail> transform(List<? extends Map<String, Object>> listOfRateMaps, boolean disableWeeklyRatePopulation, Map<Integer, Date> minDates) {
        var invTypeCodeIdMap = new ConcurrentHashMap<Object, Integer>();
        //placing incoming data into a POJO for method sanity and interdependency nightmares
        var ratesList = transformToDto(listOfRateMaps);
        List<RateHeaderAndDetail> transformedHeaders = transformAndSetHeaders(ratesList);
        // if no headers were transformed return early
        List<RateHeaderAndDetail> ratesWithinAcceptableRange = transformedHeaders.stream()
                .filter((header) -> !header.rateHeaderIsOutsideOfAcceptableRange())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ratesWithinAcceptableRange)) {
            return new ArrayList<>();
        }
        transformRateDetails(ratesWithinAcceptableRange, disableWeeklyRatePopulation, minDates, invTypeCodeIdMap);
        return ratesWithinAcceptableRange;
    }

    private void filterOutPastDetails(List<RateHeaderAndDetail> filtered) {
        filtered.forEach(rateHeaderAndDetail -> {
            List<AbstractDetail> currentAndFutureDetails = rateHeaderAndDetail.getHeader().getDetails().stream()
                    .filter(abstractDetail -> !rateHeaderAndDetail.rateDetailsAreOutsideOfAcceptableRange(abstractDetail))
                    .collect(Collectors.toList());
            rateHeaderAndDetail.getHeader().setDetails(currentAndFutureDetails);
        });
    }

    private Set<String> getPseudoRooms() {
        String pseudoRoomsValue = configParamsService.getParameterValue(IntegrationConfigParamName.PSEUDO_ROOM_TYPE_CODES.value());
        if (StringUtils.isBlank(pseudoRoomsValue)) {
            return Collections.emptySet();
        } else {
            return Arrays.stream(pseudoRoomsValue.split(PSEUDO_ROOM_DELIMITER)).collect(Collectors.toSet());
        }
    }

    private List<RateHeaderAndDetail> transformToDto(List<? extends Map<String, Object>> listOfRateMaps) {
        List<RateHeaderAndDetail> potentialRecords = new ArrayList<>();
        FileMetadata fileMetadata = createFileMetadata(listOfRateMaps.get(0));
        Date caughtUpDate = dateService.getCaughtUpDate();
        for (Map<String, Object> rate : listOfRateMaps) {
            RateHeaderAndDetail potentialRecord = new RateHeaderAndDetail(rate, getDetails(rate));
            potentialRecords.add(potentialRecord);
            potentialRecord.setFileMetadataId(fileMetadata.getId());
            potentialRecord.setCaughtUpDate(caughtUpDate);
        }
        return potentialRecords;
    }

    public List<AbstractRate> saveOriginalSemiYieldableRates(List<? extends Map<String, Object>> qualifiedRatesMap, List<AbstractRate> qualifiedRateHeaders) {
        List<RateHeaderAndDetail> originalRatesDTOs = transformOriginalRatesToDto(qualifiedRatesMap);
        var minDates = new ConcurrentHashMap<Integer, Date>();

        prepareRateHeadersAndDetails(qualifiedRateHeaders, originalRatesDTOs, minDates);
        if (!originalRatesDTOs.isEmpty()) {
            persistRateHeaderAndDetails(originalRatesDTOs, minDates);
        }

        return originalRatesDTOs.stream().map(RateHeaderAndDetail::getHeader).collect(Collectors.toList());
    }

    private void prepareRateHeadersAndDetails(List<AbstractRate> qualifiedRateHeaders, List<RateHeaderAndDetail> originalRatesDTOs, ConcurrentHashMap<Integer, Date> minDates) {
        Set<String> pseudoRooms = getPseudoRooms();
        var invTypeCodeIdMap = new ConcurrentHashMap<Object, Integer>();
        originalRatesDTOs.removeIf(record -> {
            String srpName = getName(record.getHeaderDto());
            Optional<AbstractRate> rate = getAbstractRate(qualifiedRateHeaders, srpName);

            if (rate.isPresent()) {
                record.setHeader(cloneRateHeader(rate.get()));
                record.getHeader().setDetails(createSeasonsDetailsBySplitMergeIntersection(record, pseudoRooms, minDates, invTypeCodeIdMap));
                return false;
            }
            LOGGER.info("Rate Qualified Not Found for : " + srpName);
            return true;
        });
    }

    private Optional<AbstractRate> getAbstractRate(List<AbstractRate> qualifiedRateHeaders, String srp) {
        return qualifiedRateHeaders.stream().filter(r -> Objects.equals(r.getName(), srp)).findAny();
    }

    private AbstractRate cloneRateHeader(AbstractRate abstractRate) {
        AbstractRate headerCopy = new RateQualified();
        headerCopy.setName(abstractRate.getName());
        headerCopy.setStartDate(abstractRate.getStartDate());
        headerCopy.setEndDate(abstractRate.getEndDate());
        headerCopy.setPropertyId(abstractRate.getPropertyId());
        headerCopy.setStatusId(abstractRate.getStatusId());

        return headerCopy;
    }

    private List<RateHeaderAndDetail> transformOriginalRatesToDto(List<? extends Map<String, Object>> listOfRateMaps) {
        Date caughtUpDate = dateService.getCaughtUpDate();

        return listOfRateMaps.stream()
                .filter(rate -> rate.containsKey(ORIGINAL_RATES) && rate.get(ORIGINAL_RATES) != null)
                .map(rate -> {
                    RateHeaderAndDetail potentialRecord = new RateHeaderAndDetail(rate, getOriginalDetails(rate));
                    potentialRecord.setCaughtUpDate(caughtUpDate);
                    return potentialRecord;
                }).collect(Collectors.toList());
    }

    private List<Map<String, Object>> getOriginalDetails(Map<String, Object> rate) {
        Object originalRates = rate.get(ORIGINAL_RATES);
        if (originalRates instanceof List) {
            return (List<Map<String, Object>>) originalRates;
        } else {
            LOGGER.debug("Invalid rate list on DTO object for Rate population");
            return Collections.emptyList();
        }
    }

    private List<Map<String, Object>> getDetails(Map<String, Object> rate) {
        try {
            return (List<Map<String, Object>>) rate.get(RATE_DETAIL_IDENTIFIER);
        } catch (ClassCastException cce) {
            LOGGER.debug("Invalid rate list on dto object for Rate population", cce);
        }
        return new ArrayList<>();
    }

    protected abstract void saveHeaders(List<RateHeaderAndDetail> records);

    private void preservePastRateDetails(List<RateHeaderAndDetail> completeRates, Map<Integer, Date> minDates) {
        LOGGER.info("Deleting details and season..");
        Date caughtUpDate = DateUtil.removeTimeFromDate(dateService.getCaughtUpDate());
        boolean deleteFutureSeasons = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_FUTURE_SEASON_DELETION_ON_UPDATE);
        for (RateHeaderAndDetail rate : completeRates) {
            Integer headerId = rate.getHeader().getId();
            if (ACTIVE == rate.getHeader().getStatusId() && headerId != null) {
                var startDate = minDates.get(rate.getHeader().getId());
                Date dateToDeleteDetailsFrom = deleteFutureSeasons ? caughtUpDate : DateUtil.max(caughtUpDate, startDate == null ? caughtUpDate : startDate);
                truncateEndDateOfSeasonsWitinRangeOfGivenDate(headerId, dateToDeleteDetailsFrom);
                deleteDetailsStartingFrom(List.of(headerId), dateToDeleteDetailsFrom);
            } else if (INACTIVE == rate.getHeader().getStatusId() && headerId != null && CollectionUtils.isEmpty(rate.getHeader().getDetails())) {
                LOGGER.debug("Deleting all details of inactive rate");
                deleteDetailsStartingFrom(List.of(headerId), ofNullable(rate.getMinimumDate()).orElse(caughtUpDate));
            }
        }
    }

    protected abstract void truncateEndDateOfSeasonsWitinRangeOfGivenDate(Integer headerId, Date dateToTruncateDetailsFrom);

    private List<RateHeaderAndDetail> filterOutHeadersWithoutDetails(List<RateHeaderAndDetail> potentialRecords) {
        List<RateHeaderAndDetail> validRecords = new ArrayList<>();
        for (RateHeaderAndDetail potentialRecord : potentialRecords) {
            AbstractRate header = potentialRecord.getHeader();
            List<AbstractDetail> details = header.getDetails();
            //if any details are in the valid range we will add it to results or the header is inactive
            if (header.getStatusId() == INACTIVE || CollectionUtils.isNotEmpty(details)) {
                validRecords.add(potentialRecord);
            }
        }
        return validRecords;
    }

    private List<AbstractDetail> createSeasons(RateHeaderAndDetail potentialRecord, final Set<String> pseudoRooms, boolean disableWeeklyRatePopulation, Map<Integer, Date> minDates, Map<Object, Integer> invTypeCodeIdMap) {
        List<AbstractDetail> seasons = new ArrayList<>();
        AbstractRate header = potentialRecord.getHeader();
        if (header.getStatusId().equals(ACTIVE)) {
            seasons.addAll(
                    disableWeeklyRatePopulation ? createSeasonsDetailsBySplitMergeIntersection(potentialRecord, pseudoRooms, minDates, invTypeCodeIdMap) : createSeasonsDetails(potentialRecord, pseudoRooms, minDates, invTypeCodeIdMap));
        }
        return seasons;
    }

    private List<AbstractDetail> createSeasonsDetailsBySplitMergeIntersection(RateHeaderAndDetail potentialRecord, final Set<String> pseudoRooms, Map<Integer, Date> minDates, Map<Object, Integer> invTypeCodeIdMap) {

        Map<Integer, List<AbstractDetail>> detailByAccomTypes = new LinkedHashMap<>();
        //This list should hold final data with split and merged season details
        potentialRecord.getDetailDtos().stream()
                .filter(stringObjectMap -> !pseudoRooms.contains(stringObjectMap.get(INV_TYPE_CODE).toString()))
                .forEach(stringObjectMap -> {
                    AbstractDetail newlyCreatedDetail = populateDayOfWeekData(potentialRecord, createNewDetail(), stringObjectMap, invTypeCodeIdMap);

                    populateMinRateMap(potentialRecord, minDates, newlyCreatedDetail);

                    if (!potentialRecord.rateDetailsAreOutsideOfAcceptableRange(newlyCreatedDetail)) {
                        newlyCreatedDetail.clearOutOfRangeDOWsWithRangeCheck();
                        detailByAccomTypes.computeIfAbsent(newlyCreatedDetail.getAccomTypeId(),
                                key -> new ArrayList<>()).add(newlyCreatedDetail);
                    }
                });
        if (detailByAccomTypes == null || detailByAccomTypes.isEmpty()) {
            return Collections.emptyList();
        }
        return new RateSeasonSimplifier(detailByAccomTypes).simplifyRateDetailsForView();
    }

    private void populateMinRateMap(RateHeaderAndDetail potentialRecord, Map<Integer, Date> minDates, AbstractDetail newlyCreatedDetail) {
        if (potentialRecord.getHeader() != null && potentialRecord.getHeader().getId() != null) {
            var minDate = minDates.get(potentialRecord.getHeader().getId());
            if (newlyCreatedDetail.getStartDate() != null && (minDate == null || minDate.after(newlyCreatedDetail.getStartDate()))) {
                minDates.put(potentialRecord.getHeader().getId(), newlyCreatedDetail.getStartDate());
            }
        }
    }

    private List<AbstractDetail> createSeasonsDetails(RateHeaderAndDetail potentialRecord, final Set<String> pseudoRooms, Map<Integer, Date> minDates, Map<Object, Integer> invTypeCodeIdMap) {
        WeeklySeasonsByAccomType detailsByAccom = new WeeklySeasonsByAccomType();
        List<Map<String, Object>> detailDtos = potentialRecord.getDetailDtos();
        for (Map<String, Object> detailMap : detailDtos) {
            AbstractDetail detail = createNewDetail();
            populateMinRateMap(potentialRecord, minDates, detail);

            String invTypeCode = (String) detailMap.get(INV_TYPE_CODE);
            if (!pseudoRooms.contains(invTypeCode)) {
                populateDayOfWeekData(potentialRecord, detail, detailMap, invTypeCodeIdMap);
                if (!potentialRecord.rateDetailsAreOutsideOfAcceptableRange(detail)) {
                    List<AbstractDetail> weeks = new ArrayList<>();
                    Date seasonStart = detail.getStartDate();
                    Date seasonEnd = detail.getEndDate();
                    int dayOfWeek = DateUtil.getDayOfWeekInteger(seasonStart);

                    Date newEndDate = createFirstWeek(seasonStart, dayOfWeek, detail, weeks);
                    if (newEndDate.before(seasonEnd)) {
                        createMiddleWeeks(newEndDate, seasonEnd, detail, weeks);
                    }
                    cleanUpLastWeek(seasonEnd, weeks.get(weeks.size() - 1));
                    detailsByAccom.insertWeeks(detail.getAccomTypeId(), weeks);
                }
            }
        }
        return detailsByAccom.getAllWeeks();

    }

    public boolean isRemove(String notifType) {
        return "REMOVE".equals(notifType);
    }

    private List<RateHeaderAndDetail> transformAndSetHeaders(List<RateHeaderAndDetail> ratesList) {
        LOGGER.info("transformAndSetHeaders started");
        List<RateHeaderAndDetail> recordList = new ArrayList<>();
        for (RateHeaderAndDetail potentialRecord : ratesList) {

            AbstractRate rateHeader = convertHeader(potentialRecord);
            if (rateHeader != null) {
                potentialRecord.setHeader(rateHeader);
                recordList.add(potentialRecord);
            }
        }
        LOGGER.info("transformAndSetHeaders Completed");
        return recordList;
    }

    private AbstractRate convertHeader(RateHeaderAndDetail potentialRecord) {
        Map<String, Object> headerDto = potentialRecord.getHeaderDto();
        String name = getName(headerDto);
        AbstractRate existingHeader = getHeader(name);
        String notifType = getNotifType(headerDto);

        if (existingHeader != null && existingHeader.getManagedInG3()) {
            return null;
        }

        if (isNewOrOverlayOrDelta(notifType)) {
            existingHeader = createOrUpdate(potentialRecord, existingHeader, name);
        } else if (isRemove(notifType)) {
            softDeleteHeader(potentialRecord.getFileMetadataId(), existingHeader, potentialRecord.getCaughtUpDate());
        } else {
            throw new TetrisException("Unsupported NotifType: [" + notifType + "]");
        }
        LOGGER.debug("Header: " + existingHeader);
        return existingHeader;
    }

    private AbstractRate createOrUpdate(RateHeaderAndDetail potentialRecord, AbstractRate existingRate, String name) {
        boolean hasDetails = CollectionUtils.isNotEmpty(potentialRecord.getDetailDtos());

        AbstractRate updatedRate = null;
        if (existingRate != null) {
            updatedRate = updateExistingRate(potentialRecord, name, existingRate);
            if (!hasDetails) {
                // incoming rate does not have details but has existing: soft delete existing
                softDeleteHeader(potentialRecord.getFileMetadataId(), existingRate, potentialRecord.getCaughtUpDate());
            }
        } else if (hasDetails) {
            // incoming rate has details, create new
            updatedRate = createNewRate(potentialRecord, name);
        }

        return updatedRate;
    }

    private AbstractRate createNewRate(RateHeaderAndDetail potentialRecord, String name) {
        AbstractRate newRateHeader = createNewHeader();
        return convertHeaderFields(potentialRecord, name, newRateHeader);
    }

    private AbstractRate updateExistingRate(RateHeaderAndDetail potentialRecord,
                                            String name,
                                            AbstractRate existingRateHeader) {

        convertHeaderFields(potentialRecord, name, existingRateHeader);
        return existingRateHeader;
    }

    private AbstractRate convertHeaderFields(RateHeaderAndDetail potentialRecord,
                                             String name,
                                             AbstractRate rateHeader) {
        Map<String, Object> headerDto = potentialRecord.getHeaderDto();
        convertSpecificFields(headerDto, rateHeader);
        return convertCommonHeaderFields(rateHeader, name, potentialRecord);
    }

    private boolean isNewOrOverlayOrDelta(String notifType) {
        return OVERLAY.equalsIgnoreCase(notifType)
                || NEW.equalsIgnoreCase(notifType)
                || DELTA.equalsIgnoreCase(notifType);
    }


    private AbstractRate convertCommonHeaderFields(AbstractRate rateHeader,
                                                   String name,
                                                   RateHeaderAndDetail potentialRecord) {
        Map<String, Object> headerDto = potentialRecord.getHeaderDto();
        rateHeader.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        rateHeader.setFileMetadataId(potentialRecord.getFileMetadataId());
        rateHeader.setName(name);
        String description = ObjectUtils.firstNonNull((String) headerDto.get("longDescription"), (String) headerDto.get("shortDescription"), name);
        rateHeader.setDescription(StringUtils.left(description, 150));
        rateHeader.setCurrency((String) headerDto.get("currency"));
        rateHeader.setStatusId(ACTIVE);
        rateHeader.setPriceRelative(getIntBoolean((Boolean) headerDto.get("priceRelative")));
        rateHeader.setYieldable(getIntBoolean((Boolean) headerDto.get("yieldable")));
        rateHeader.setIncludesPackage(getIntBoolean((Boolean) headerDto.get("includesPackage")));
        rateHeader.setStartDate(parseDateFromString((String) headerDto.get(START_DATE)));
        rateHeader.setEndDate(getEndDate(headerDto, potentialRecord.getMaximumDate()));
        return rateHeader;
    }

    protected void transformRateDetails(List<RateHeaderAndDetail> potentialRecords, boolean disableWeeklyRatePopulation, Map<Integer, Date> minDates, Map<Object, Integer> invTypeCodeIdMap) {
        Set<String> pseudoRooms = getPseudoRooms();
        for (RateHeaderAndDetail record : potentialRecords) {
            AbstractRate abstractRate = record.getHeader();
            if (abstractRate != null) {
                abstractRate.setDetails(createSeasons(record, pseudoRooms, disableWeeklyRatePopulation, minDates, invTypeCodeIdMap));
            }
        }
    }

    protected Date getEndDate(Map<String, Object> dto, Date maximumDate) {
        Date potentialEndDate = parseDateFromString((String) dto.get(END_DATE));

        if (potentialEndDate.after(maximumDate)) {
            return maximumDate;
        } else {
            return potentialEndDate;
        }
    }

    protected Date getStartDate(Map<String, Object> dto, Date minimumDate) {
        Date potentialStartDate = parseDateFromString((String) dto.get(START_DATE));

        if (potentialStartDate.before(minimumDate)) {
            return minimumDate;
        } else {
            return potentialStartDate;
        }
    }

    public void cleanUpLastWeek(Date seasonEnd,
                         AbstractDetail abstractDetail) {
        abstractDetail.setEndDate(seasonEnd);

        int dayOfWeek = DateUtil.getDayOfWeekInteger(seasonEnd);

        if (dayOfWeek < 7) {
            abstractDetail.setSaturday(NEGATIVE_ONE);
        }
        if (dayOfWeek < 6) {
            abstractDetail.setFriday(NEGATIVE_ONE);
        }
        if (dayOfWeek < 5) {
            abstractDetail.setThursday(NEGATIVE_ONE);
        }
        if (dayOfWeek < 4) {
            abstractDetail.setWednesday(NEGATIVE_ONE);
        }
        if (dayOfWeek < 3) {
            abstractDetail.setTuesday(NEGATIVE_ONE);
        }
        if (dayOfWeek < 2) {
            abstractDetail.setMonday(NEGATIVE_ONE);
        }
    }

    private void createMiddleWeeks(Date lastEndDate,
                                   Date seasonEnd,
                                   AbstractDetail detail,
                                   List<AbstractDetail> weeks) {

        Date indexDate = lastEndDate;

        while (indexDate.before(seasonEnd)) {
            Date newStartDate = DateUtil.addDaysToDate(indexDate, ONE_DAY);
            indexDate = DateUtil.addDaysToDate(newStartDate, SIX_DAYS);

            AbstractDetail newSeason = createWeek(detail, newStartDate,
                    indexDate);
            weeks.add(newSeason);
        }
    }

    private Date createFirstWeek(Date seasonStart, int dayOfWeek, AbstractDetail detail, List<AbstractDetail> weeks) {
        Date newEndDate = DateUtil.addDaysToDate(DateUtil.addDaysToDate(seasonStart, -(dayOfWeek - ONE_DAY)), SIX_DAYS);

        AbstractDetail firstWeek = createWeek(detail, seasonStart,
                newEndDate);

        if (dayOfWeek > 1) {
            firstWeek.setSunday(NEGATIVE_ONE);
        }
        if (dayOfWeek > 2) {
            firstWeek.setMonday(NEGATIVE_ONE);
        }
        if (dayOfWeek > 3) {
            firstWeek.setTuesday(NEGATIVE_ONE);
        }
        if (dayOfWeek > 4) {
            firstWeek.setWednesday(NEGATIVE_ONE);
        }
        if (dayOfWeek > 5) {
            firstWeek.setThursday(NEGATIVE_ONE);
        }
        if (dayOfWeek > 6) {
            firstWeek.setFriday(NEGATIVE_ONE);
        }
        weeks.add(firstWeek);
        return newEndDate;
    }

    private AbstractDetail createWeek(AbstractDetail detail,
                                      Date newStartDate, Date newEndDate) {
        AbstractDetail newSeason = createNewDetail(detail);

        try {
            BeanUtils.copyProperties(newSeason, detail);
            adjustDailyValues(detail, newSeason);
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new TetrisException(ErrorCode.BEAN_PROPERTY_COPY_FAILED, "Error copying rate object", e);
        }

        newSeason.setStartDate(newStartDate);
        newSeason.setEndDate(newEndDate);
        return newSeason;
    }

    private void adjustDailyValues(AbstractDetail detail, AbstractDetail newSeason) {
        if (detail.getSunday() == null) {
            newSeason.setSunday(NEGATIVE_ONE);
        }
        if (detail.getMonday() == null) {
            newSeason.setMonday(NEGATIVE_ONE);
        }
        if (detail.getTuesday() == null) {
            newSeason.setTuesday(NEGATIVE_ONE);
        }
        if (detail.getWednesday() == null) {
            newSeason.setWednesday(NEGATIVE_ONE);
        }
        if (detail.getThursday() == null) {
            newSeason.setThursday(NEGATIVE_ONE);
        }
        if (detail.getFriday() == null) {
            newSeason.setFriday(NEGATIVE_ONE);
        }
        if (detail.getSaturday() == null) {
            newSeason.setSaturday(NEGATIVE_ONE);
        }
    }

    private Integer getIntBoolean(Boolean bool) {
        if (bool != null && bool) {
            return 1;
        } else {
            return 0;
        }
    }

    private AbstractDetail populateDayOfWeekData(RateHeaderAndDetail potentialRecord, AbstractDetail rateDetail, Map<String, Object> dayOfWeekData, Map<Object, Integer> invTypeCodeIdMap) {

        var invTypeCode = dayOfWeekData.get(INV_TYPE_CODE);
        if (invTypeCodeIdMap.get(invTypeCode) == null) {
            var accomType = accommodationService.findOrCreateRoomTypeForCode(potentialRecord.getHeader().getPropertyId(), invTypeCode.toString());
            invTypeCodeIdMap.put(invTypeCode, accomType.getId());
        }

        rateDetail.setAccomTypeId(invTypeCodeIdMap.get(dayOfWeekData.get(INV_TYPE_CODE)));
        rateDetail.setStartDate(getStartDate(dayOfWeekData, potentialRecord.getHeader().getStartDate()));
        rateDetail.setEndDate(getEndDate(dayOfWeekData, DateUtil.min(potentialRecord.getMaximumDate(), potentialRecord.getHeader().getEndDate())));

        Map<DayOfWeek, BigDecimal> ratesDataForAllWeeks = new MapUtil().getRateValues(dayOfWeekData, rateAmountCalculator.getAmountTypesForAllDOWs());

        rateDetail.setMonday(ratesDataForAllWeeks.get(DayOfWeek.MONDAY));
        rateDetail.setTuesday(ratesDataForAllWeeks.get(DayOfWeek.TUESDAY));
        rateDetail.setWednesday(ratesDataForAllWeeks.get(DayOfWeek.WEDNESDAY));
        rateDetail.setThursday(ratesDataForAllWeeks.get(DayOfWeek.THURSDAY));
        rateDetail.setFriday(ratesDataForAllWeeks.get(DayOfWeek.FRIDAY));
        rateDetail.setSaturday(ratesDataForAllWeeks.get(DayOfWeek.SATURDAY));
        rateDetail.setSunday(ratesDataForAllWeeks.get(DayOfWeek.SUNDAY));
        return rateDetail;
    }

    public Date parseDateFromString(String dateString) {
        DateTime dateTime = DateTime.parse(dateString, DateTimeFormat.forPattern(DATE_FORMAT));
        return dateTime.toDate();
    }

    private String getNotifType(Map<String, Object> rateHeaderDto) {
        return (String) rateHeaderDto.get("notifType");
    }

    private String getName(Map<String, Object> dto) {
        return dto.get(NAME).toString().trim();
    }


    private AbstractRate selectFromMultipleRates(List<Object> ratesWithGivenName) {
        List<AbstractRate> abstractRates = ratesWithGivenName.stream().map(obj -> (AbstractRate) obj).collect(Collectors.toList());
        Optional<AbstractRate> activeRate = abstractRates.stream().filter(rate -> rate.getStatusId() == ACTIVE).findAny();
        if (activeRate.isPresent()) {
            return activeRate.get();
        }
        return null;
    }

    private AbstractRate selectFromSingleRate(AbstractRate rateFoundInDB) {
        if ((!rateFoundInDB.getManagedInG3()) || (rateFoundInDB.getManagedInG3() && rateFoundInDB.getStatusId() == ACTIVE)) {
            return rateFoundInDB;
        } else {
            return null;
        }
    }

    public AbstractRate findEligibleRate(String namedQuery, String rateName) {
        List<Object> ratesWithGivenName = tenantCrudService.findByNamedQuery(namedQuery, QueryParameter.with("names", Collections.singletonList(rateName)).parameters());
        if (ratesWithGivenName.isEmpty()) {
            return null;
        }
        if (ratesWithGivenName.size() == 1) {
            return selectFromSingleRate((AbstractRate) ratesWithGivenName.get(0));
        } else {
            return selectFromMultipleRates(ratesWithGivenName);
        }
    }

}
