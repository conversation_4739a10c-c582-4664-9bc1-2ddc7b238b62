package com.ideas.tetris.pacman.services.decision.repository;

import com.ideas.tetris.pacman.Repository;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.decision.entity.DecisionVolume;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

@Repository
@Component
public class DecisionVolumeRepository {
    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    public void save(DecisionVolume decisionVolume) {
        crudService.save(decisionVolume);
    }

    public List<DecisionVolume> getByDateRange(LocalDate startDate, LocalDate endDate) {
        return crudService.findByNamedQuery(DecisionVolume.DECISION_VOLUME_BY_DATE_RANGE,
                QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());
    }

    public List<DecisionVolume> getByInputProcessingIdCorrelationIdDecisionTypeName(Integer inputProcessingId, String correlationId, String decisionTypeName) {
        return crudService.findByNamedQuery(DecisionVolume.DECISION_VOLUME_BY_INPUT_PROCESSING_ID_CORRELATION_ID_DECISION_TYPE,
                QueryParameter.with("inputProcessingId", inputProcessingId)
                        .and("correlationId", correlationId)
                        .and("decisionTypeName", decisionTypeName).parameters());
    }
}
