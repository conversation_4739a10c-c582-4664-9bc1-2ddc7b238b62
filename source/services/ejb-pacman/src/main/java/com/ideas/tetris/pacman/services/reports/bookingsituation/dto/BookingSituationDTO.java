package com.ideas.tetris.pacman.services.reports.bookingsituation.dto;

import com.ideas.tetris.pacman.services.scheduledreport.reportapi.annotations.ColumnHeader;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.annotations.ReportRepeatColumns;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.datatypes.PropertyValueType;
import org.joda.time.LocalDate;

@ReportRepeatColumns(condition = "getCategories",
        columns = {"occupancyOnBooksAnalysis", "occupancyOnBooksComparison", "occupancyOnBooksVariance",
                "revenuOnBooksAnalysis", "revenuOnBooksComparison", "revenuOnBooksVariance",
                "adrOnBooksAnalysis", "adrOnBooksComparison", "adrOnBooksVariance"})
public class BookingSituationDTO {

    @ColumnHeader(titleKey = "common.dow", order = 1, type = PropertyValueType.class)
    private String dayOfWeek;
    @ColumnHeader(titleKey = {"day.of.arrival", "analysis.period"}, order = 2)
    private LocalDate dayOfArrivalAnalysis;
    @ColumnHeader(titleKey = {"day.of.arrival", "comparision.period"}, order = 3)
    private LocalDate dayOfArrivalComparison;

    @ColumnHeader(titleKey = {"performanceComparisonReport.output.column.occupancyOnBooks", "analysis.period", "as.of"},
            order = 6, condition = "isRoomsSoldChecked", negativeAllowed = true)
    private Integer occupancyOnBooksAnalysis;
    @ColumnHeader(titleKey = {"performanceComparisonReport.output.column.occupancyOnBooks", "comparision.period", "as.of"},
            order = 7, condition = "isRoomsSoldChecked", negativeAllowed = true)
    private Integer occupancyOnBooksComparison;
    @ColumnHeader(titleKey = "variance.occupancy.on.books", order = 8, condition = "isRoomsSoldChecked", negativeAllowed = true)
    private Integer occupancyOnBooksVariance;

    @ColumnHeader(titleKey = {"revenue.on.books", "analysis.period", "as.of"},
            order = 9, condition = "isRoomRevenueChecked", pattern = "#0.00", negativeAllowed = true)
    private Double revenuOnBooksAnalysis;
    @ColumnHeader(titleKey = {"revenue.on.books", "comparision.period", "as.of"},
            order = 10, condition = "isRoomRevenueChecked", pattern = "#0.00", negativeAllowed = true)
    private Double revenuOnBooksComparison;
    @ColumnHeader(titleKey = {"variance.revenue.on.books"}, order = 11, condition = "isRoomRevenueChecked", pattern = "#0.00", negativeAllowed = true)
    private Double revenuOnBooksVariance;

    @ColumnHeader(titleKey = {"adr.on.books", "analysis.period", "as.of"}, order = 12,
            condition = "isAdrChecked", pattern = "#0.00", negativeAllowed = true)
    private Double adrOnBooksAnalysis;
    @ColumnHeader(titleKey = {"adr.on.books", "comparision.period", "as.of"}, order = 13,
            condition = "isAdrChecked", pattern = "#0.00", negativeAllowed = true)
    private Double adrOnBooksComparison;
    @ColumnHeader(titleKey = {"variance.revenue.on.books"}, order = 14,
            condition = "isAdrChecked", pattern = "#0.00", negativeAllowed = true)
    private Double adrOnBooksVariance;

    @ColumnHeader(titleKey = {"revpar.on.books", "analysis.period", "as.of"}, order = 15, condition = "isRevParChecked", pattern = "#0.00", negativeAllowed = true)
    private Double revParOnBooksAnalysis;
    @ColumnHeader(titleKey = {"revpar.on.books", "comparision.period", "as.of"}, order = 16, condition = "isRevParChecked", pattern = "#0.00", negativeAllowed = true)
    private Double revParOnBooksComparison;
    @ColumnHeader(titleKey = {"variance.revpar.on.books"}, order = 17, condition = "isRevParChecked", pattern = "#0.00", negativeAllowed = true)
    private Double revParOnBooksVariance;

    @ColumnHeader(titleKey = {"specialEvent", "analysis.period"}, order = 4, condition = "isSpecialEventChecked")
    private String specialEventAnalysis;
    @ColumnHeader(titleKey = {"specialEvent", "comparision.period"}, order = 5, condition = "isSpecialEventChecked")
    private String specialEventComparison;

    String businessViewName;
    String forecastGroupName;

    public String getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(String dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public LocalDate getDayOfArrivalAnalysis() {
        return dayOfArrivalAnalysis;
    }

    public void setDayOfArrivalAnalysis(LocalDate dayOfArrivalAnalysis) {
        this.dayOfArrivalAnalysis = dayOfArrivalAnalysis;
    }

    public LocalDate getDayOfArrivalComparison() {
        return dayOfArrivalComparison;
    }

    public void setDayOfArrivalComparison(LocalDate dayOfArrivalComparison) {
        this.dayOfArrivalComparison = dayOfArrivalComparison;
    }

    public Integer getOccupancyOnBooksAnalysis() {
        return occupancyOnBooksAnalysis;
    }

    public void setOccupancyOnBooksAnalysis(Integer occupancyOnBooksAnalysis) {
        this.occupancyOnBooksAnalysis = occupancyOnBooksAnalysis;
    }

    public Integer getOccupancyOnBooksComparison() {
        return occupancyOnBooksComparison;
    }

    public void setOccupancyOnBooksComparison(Integer occupancyOnBooksComparison) {
        this.occupancyOnBooksComparison = occupancyOnBooksComparison;
    }

    public Integer getOccupancyOnBooksVariance() {
        return occupancyOnBooksVariance;
    }

    public void setOccupancyOnBooksVariance(Integer occupancyOnBooksVariance) {
        this.occupancyOnBooksVariance = occupancyOnBooksVariance;
    }

    public Double getRevenuOnBooksAnalysis() {
        return revenuOnBooksAnalysis;
    }

    public void setRevenuOnBooksAnalysis(Double revenuOnBooksAnalysis) {
        this.revenuOnBooksAnalysis = revenuOnBooksAnalysis;
    }

    public Double getRevenuOnBooksComparison() {
        return revenuOnBooksComparison;
    }

    public void setRevenuOnBooksComparison(Double revenuOnBooksComparison) {
        this.revenuOnBooksComparison = revenuOnBooksComparison;
    }

    public Double getRevenuOnBooksVariance() {
        return revenuOnBooksVariance;
    }

    public void setRevenuOnBooksVariance(Double revenuOnBooksVariance) {
        this.revenuOnBooksVariance = revenuOnBooksVariance;
    }

    public Double getAdrOnBooksAnalysis() {
        return adrOnBooksAnalysis;
    }

    public void setAdrOnBooksAnalysis(Double adrOnBooksAnalysis) {
        this.adrOnBooksAnalysis = adrOnBooksAnalysis;
    }

    public Double getAdrOnBooksComparison() {
        return adrOnBooksComparison;
    }

    public void setAdrOnBooksComparison(Double adrOnBooksComparison) {
        this.adrOnBooksComparison = adrOnBooksComparison;
    }

    public Double getAdrOnBooksVariance() {
        return adrOnBooksVariance;
    }

    public void setAdrOnBooksVariance(Double adrOnBooksVariance) {
        this.adrOnBooksVariance = adrOnBooksVariance;
    }

    public Double getRevParOnBooksAnalysis() {
        return revParOnBooksAnalysis;
    }

    public void setRevParOnBooksAnalysis(Double revParOnBooksAnalysis) {
        this.revParOnBooksAnalysis = revParOnBooksAnalysis;
    }

    public Double getRevParOnBooksComparison() {
        return revParOnBooksComparison;
    }

    public void setRevParOnBooksComparison(Double revParOnBooksComparison) {
        this.revParOnBooksComparison = revParOnBooksComparison;
    }

    public Double getRevParOnBooksVariance() {
        return revParOnBooksVariance;
    }

    public void setRevParOnBooksVariance(Double revParOnBooksVariance) {
        this.revParOnBooksVariance = revParOnBooksVariance;
    }

    public String getSpecialEventAnalysis() {
        return specialEventAnalysis;
    }

    public void setSpecialEventAnalysis(String specialEventAnalysis) {
        this.specialEventAnalysis = specialEventAnalysis;
    }

    public String getSpecialEventComparison() {
        return specialEventComparison;
    }

    public void setSpecialEventComparison(String specialEventComparison) {
        this.specialEventComparison = specialEventComparison;
    }

    public String getBusinessViewName() {
        return businessViewName;
    }

    public void setBusinessViewName(String businessViewName) {
        this.businessViewName = businessViewName;
    }

    public String getForecastGroupName() {
        return forecastGroupName;
    }

    public void setForecastGroupName(String forecastGroupName) {
        this.forecastGroupName = forecastGroupName;
    }
}
