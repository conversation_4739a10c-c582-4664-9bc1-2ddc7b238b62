package com.ideas.tetris.pacman.services.opera;

import com.ideas.g3.integration.opera.dto.OperaDataLoadTypeCode;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.List;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Created by idnrbk on 4/23/2015.
 */
@OperaMSRTSummaryFeedValidationService.Qualifier
@Component
@Transactional
public class OperaMSRTSummaryFeedValidationService implements OperaFeedValidationService {

    private static final Logger LOGGER = Logger.getLogger(OperaMSRTSummaryFeedValidationService.class.getName());

    @Autowired
    OperaOccupancySummaryFeedValidationService occupancySummaryFeedValidationService;

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }

    @TenantCrudServiceBean.Qualifier
	@Autowired
    @org.springframework.beans.factory.annotation.Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    public static final String QUERY_DUPLICATE_MSRT_SUMMARY_RECORDS = "Select Occupancy_DT ,Room_Type ,Market_Code from opera.History_Occupancy_Summary " +
            "where Data_Load_Metadata_ID in (:dlmIds) group by Occupancy_DT,Room_Type,Market_Code having count(Occupancy_DT) > 1";

    public static final String PRIMARY_ATTRIBUTE = "Occupancy Date";
    public static final String ENTITY = "MS RT Summary";

    public static final String MSG_MSRT_SUMMARY_VALIDATION_FAILED = "Feed validation failed for " + ENTITY + ".\n";
    public static final String MSG_MISSING_PRIMARY_ATTR = ENTITY + " Record(s) have missing " + PRIMARY_ATTRIBUTE;

    public static final String MSG_DUPLICATE_RECORDS = "Duplicate Records (Occupancy Date, Room Type, Market Segment)";
    public static final String MSG_DUPLICATE = " are present in " + ENTITY;

    public static final String[] OPERA_DATALOAD_TYPE_CODE = new String[]{OperaDataLoadTypeCode.CSAT.name(), OperaDataLoadTypeCode.PSAT.name()};

    @Override
    public String[] getDataLoadTypes() {
        return OPERA_DATALOAD_TYPE_CODE;
    }

    @Override
    public String validateFeed(String correlationId, List<Integer> operaDataLoadTypeCodes) {
        boolean flagMissingOccupancyDate = occupancySummaryFeedValidationService.isOccupancySummaryMissingOccupancyDate(correlationId, operaDataLoadTypeCodes, MSG_MISSING_PRIMARY_ATTR);
        boolean flagMissingMarketSegment = occupancySummaryFeedValidationService.isOccupancySummaryMissingMarketSegment(correlationId, operaDataLoadTypeCodes);
        boolean flagMissingRoomType = occupancySummaryFeedValidationService.isOccupancySummaryMissingRoomType(correlationId, operaDataLoadTypeCodes);
        boolean flagDuplicateMSRTSummary = isDuplicateMSRTSummaryPresent(correlationId, operaDataLoadTypeCodes);

        if (flagMissingOccupancyDate || flagMissingRoomType || flagMissingMarketSegment ||
                flagDuplicateMSRTSummary) {
            return MSG_MSRT_SUMMARY_VALIDATION_FAILED;
        }
        return StringUtils.EMPTY;
    }

    private boolean isDuplicateMSRTSummaryPresent(String correlationId, List<Integer> operaDataLoadTypeCodes) {
        List<String> queryResults = crudService.findByNativeQuery(QUERY_DUPLICATE_MSRT_SUMMARY_RECORDS,
                QueryParameter.with("dlmIds", operaDataLoadTypeCodes).parameters(), new RowMapper<String>() {
                    @Override
                    public String mapRow(Object[] row) {
                        return (new StringBuffer("{").append(row[0]).append(";").append(row[1]).append(";").append(row[2]).append("}")).toString();
                    }
                });
        return occupancySummaryFeedValidationService.logMissingDataError(queryResults, MSG_DUPLICATE, MSG_DUPLICATE_RECORDS);
    }

}
