package com.ideas.tetris.pacman.services.dailybar;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Component
@Transactional
public class DailyBarService extends BaseDailyBarService {

    @Override
    public void createDecisions() {
        createDailyBarDecisionsIfConfigured();
    }

    @Override
	public
    Date getOptimizationWindowEndDate() {
        return dateService.getDecisionUploadWindowEndDate();
    }
}
