package com.ideas.tetris.pacman.services.security;

import com.ideas.infra.tetris.security.domain.*;
import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.fds.uas.UASService;
import com.ideas.tetris.pacman.services.property.PropertyConfigParamService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.license.LicenseService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.FEATURE_LICENSING_ENABLED;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Component
@Transactional
public class MenuService {
    private static final Logger LOGGER = Logger.getLogger(MenuService.class.getName());
    private static final String BENEFIT_MEASUREMENT = "Benefit Measurement";
    private static final String HILTON_VIRTUAL_PROPERTY_MAPPING = "Hilton Virtual Property Mapping";
    private static final String DECISION_CONFIGURATION = "Decision Configuration";
    private static final String DECISION_CONFIGURATION_NEW = "Decision Configuration New";

    @Autowired
	private AuthorizationService authorizationService;

    @Autowired
	private PropertyConfigParamService propertyConfigParamService;

    @Autowired
	private PropertyService propertyService;

    @Autowired
	private UASService uasService;

    @Autowired
	private LicenseService licenseService;

    @Autowired
	private UserGlobalDBService userGlobalDBService;
    @Autowired
	private PacmanConfigParamsService configParamsService;

    protected TetrisPrincipal getPrincipal() {
        return PacmanThreadLocalContextHolder.getPrincipal();
    }

    public List<Page> getMenu(boolean isInternalUser) {
        final List<Page> menus = getMenu(isInternalUser, false);
        removeFeatureDisabledPages(menus);
        return menus;
    }

    /**
     * Retrieves the menu represented by a hierarchy of pages for a user with both permissions and feature toggles applied
     * <p>
     * isInternalContext represents if we want to load the menu in the "Internal Context". For example, in Role Management
     * when a internal user wants to create an external user we would call getMenu(false, true) to make
     * sure getMenu returns the menu from a external users perspective
     * Another example is we call getMenu from the shell so we can create the main menu for the user, if the user is an internal user,
     * we want to make sure we call getMenu(true, false). For an external user, we would call getMenu(false, false).
     * <p>
     * readFeaturesAtClientLevel represents if we want to check the feature toggles against our pages at the client level instead
     * of at the individual property level.  For example, if we call getMenu from the Role Management module, there is no selected property
     * as Role Management only supports a selected client.  In this case, we would call getMenu(false/true, true)
     * as we want to make sure we check the feature toggles across all properties for the selected client and if one of the properties has a toggle on,
     * we make sure to show that page in the menu hierarchy.  For a module that has property scope (the property dropdown in the UI is visible)
     * we would call getMenu(false/true, false) as we want to make sure the feature toggles are applied at the selected property level
     *
     * @param isInternalContext         boolean that specifies whether we get the menu from an external users perspective
     * @param readFeaturesAtClientLevel boolean that if true, we check feature toggles at the selected client level. If false,
     *                                  we check the toggles at the selected property level
     * @return the hierarchical page menu with permissions and feature toggles applied to it
     */


    public List<Page> getMenu(boolean isInternalContext, boolean readFeaturesAtClientLevel) {
        TetrisPrincipal principal = getPrincipal();

        //Get the raw menu which contains all pages which are part of the new menu.
        List<Page> rawMenu = getRawMenu();

        //Add freeflowing pages into the proper order to the pages list before parsing the tree
        List<Page> allPages = getAllPages();
        List<com.ideas.infra.tetris.security.domain.Page> pagesWithExtraDepth = allPages
                .stream()
                .filter(item -> item.getIndependentPageParent() != null)
                .collect(Collectors.toList());

        for (Page page : pagesWithExtraDepth) {
            //Clear out the pages that are beyond 2 depth which will cause the menu icon to disappear
            page.setPages(new ArrayList<>());
            addIndependentPageToParent(rawMenu, page);
        }

        //Get the pages that this user has access to.  This list is a flat list of all pages the user has permission to see.
        List<Page> accessiblePages = getAuthorizedPages(readFeaturesAtClientLevel);

        //Convert our accessiblePages to a flat map structure for convenience so we can cut down on our looping
        //in the checkPermissions method which is called below
        Map<String, Page> accessiblePagesMap = convertToFlatMap(accessiblePages);

        //Now loop through the raw menu taking away anything the user doesn't have permission to see
        return checkPermissions(rawMenu, accessiblePagesMap, isInternalContext, principal.isInternalUser());
    }

    public List<Page> getMenuWithIndependentPages(boolean isInternalContext, boolean readFeaturesAtClientLevel) {
        TetrisPrincipal principal = getPrincipal();

        //Get the raw menu which contains all pages which are part of the new menu.
        List<Page> rawMenu = getRawMenu();

        //Add freeflowing pages into the proper order to the pages list before parsing the tree
        List<Page> allPages = getAllPages();
        List<com.ideas.infra.tetris.security.domain.Page> pagesWithExtraDepth = allPages
                .stream()
                .filter(item -> item.getIndependentPageParent() != null)
                .collect(Collectors.toList());

        for (Page page : pagesWithExtraDepth) {
            if (CollectionUtils.isNotEmpty(page.getMenuExcludedPages()) && CollectionUtils.isEmpty(page.getPages())) {
                page.setPages(page.getMenuExcludedPages());
            }
            addIndependentPageToParent(rawMenu, page);
        }

        //Get the pages that this user has access to.  This list is a flat list of all pages the user has permission to see.
        List<Page> accessiblePages = getAuthorizedPages(readFeaturesAtClientLevel);

        //Convert our accessiblePages to a flat map structure for convenience so we can cut down on our looping
        //in the checkPermissions method which is called below
        Map<String, Page> accessiblePagesMap = convertToFlatMap(accessiblePages);

        //Now loop through the raw menu taking away anything the user doesn't have permission to see
        return checkPermissions(rawMenu, accessiblePagesMap, isInternalContext, principal.isInternalUser());
    }

    public void addIndependentPageToParent(List<Page> rawMenu, Page page) {
        Page parentPage = rawMenu
                .stream()
                .filter(item -> item.getCode().equalsIgnoreCase(page.getIndependentPageParent().getCode()))
                .findFirst()
                .orElse(null);
        if (page.getMenuSubCategory() != null && parentPage != null) {
            Page subcategoryPage = parentPage
                    .getPages()
                    .stream()
                    .filter(item -> item.getCode().equalsIgnoreCase(page.getMenuSubCategory().name()))
                    .findFirst()
                    .orElse(null);
            if (CollectionUtils.isNotEmpty(page.getDuplicatedRoleManagementPages()) && subcategoryPage != null) {
                subcategoryPage.getPages().removeAll(page.getDuplicatedRoleManagementPages());
            }

            if (subcategoryPage != null) {
                subcategoryPage.getPages().add(page);
                subcategoryPage.sortMenu();
            }
        } else {
            if (CollectionUtils.isNotEmpty(page.getDuplicatedRoleManagementPages()) && parentPage != null) {
                parentPage.getPages().removeAll(page.getDuplicatedRoleManagementPages());
            }

            if (parentPage != null) {
                parentPage.getPages().add(page);
                parentPage.sortMenu();
            }
        }
    }

    /**
     * Returns the users menu, but flattened down into a sorted list.  This is useful for displaying the pages you
     * have permission to view in a drop down list such as the default landing page drop down in the user preferences UI.
     * The includeCategories param lets you include/exclude the page categories/groups in your flattened list.
     *
     * @param isInternalUser
     * @param includeCategories
     * @return
     */
    public List<Page> getMenuFlattened(boolean isInternalUser, boolean includeCategories) {
        List<Page> pagesInMenuHierarchy = getMenu(isInternalUser);
        List<Page> flattenedPages = flattenMenuHierarchy(pagesInMenuHierarchy, new ArrayList<>(), includeCategories);
        Collections.sort(flattenedPages);
        return flattenedPages;
    }

    /**
     * checkPermissions is a recursive method that will loop over the raw menu hierarchy checking each of its categories and pages
     * removing any category and page that the user doesn't have permission to see.
     *
     * @param pages
     * @param accessiblePages
     * @param isInternalContext
     * @param isInternalUser
     * @return ---Sonar ignores---
     * S134: Control flow statements "if", "for", "while", "switch" and "try" should not be nested too deeply
     * Normally I agree with this, but in this case extracting out some of this code to its own methods would actually in my opinion
     * make it harder to understand/debug what this method is doing
     */
    @SuppressWarnings({"squid:S134"})
    private List<Page> checkPermissions(List<Page> pages, Map<String, Page> accessiblePages, boolean isInternalContext, boolean isInternalUser) {
        ArrayList<Page> acceptedPageList = new ArrayList<>();

        for (Page page : pages) {
            removeFunctionalitiesOnlyVisibleToInternalUsers(page, isInternalContext);
            if (page.hasChildren()) {
                List<Page> acceptedChildren = checkPermissions(page.getPages(), accessiblePages, isInternalContext, isInternalUser);
                if (!acceptedChildren.isEmpty()) {
                    page.setPages(acceptedChildren);
                    acceptedPageList.add(page);
                } else {
                    //I have no children that were accepted, but lets check myself to see if I am in the authorized list
                    //We have to do this for instances of the OPTIX page as it's sort of special as we want the OPTIX page
                    //included in the OTHER main category, but we don't care about its children
                    if (shouldAcceptPage(page, accessiblePages, isInternalContext, isInternalUser)) {
                        acceptedPageList.add(page);
                    }
                }
            } else {
                if (!isInternalUser &&
                        ((page.getName().equalsIgnoreCase(BENEFIT_MEASUREMENT) && !propertyConfigParamService.isBenefitMeasurementEnabled()) ||
                                (page.getName().equalsIgnoreCase(HILTON_VIRTUAL_PROPERTY_MAPPING) && !propertyService.isPropertyVirtual()))) {
                    continue;
                }

                if(!propertyConfigParamService.isNewDecisionConfigPageIsEnabled() && page.getName().equalsIgnoreCase(DECISION_CONFIGURATION_NEW)){
                    continue;
                }else if (propertyConfigParamService.isNewDecisionConfigPageIsEnabled() && page.getName().equalsIgnoreCase(DECISION_CONFIGURATION)) {
                    continue;
                }

                //The page doesn't have any children, so just simply check to see if we have permission to view it
                if (shouldAcceptPage(page, accessiblePages, isInternalContext, isInternalUser)) {
                    acceptedPageList.add(page);
                }
            }
        }

        return acceptedPageList;
    }

    private void removeFunctionalitiesOnlyVisibleToInternalUsers(Page page, boolean isInternalContext) {
        //External roles should not display functionalities that are isOnlyVisibleToInternalUsers = True
        if (!page.getFunctionalities().isEmpty() && !isInternalContext) {
            List<Functionality> functionalities = new ArrayList<>();
            for (Functionality functionality : page.getFunctionalities()) {
                if (!functionality.isOnlyVisibleToInternalUsers()) {
                    functionalities.add(functionality);
                }
            }
            page.setFunctionalities(functionalities);
        }
    }


    private boolean shouldAcceptPage(Page page, Map<String, Page> accessiblePages, boolean isInternalContext, boolean isInternalUser) {

        //Check to see if the user has permission to see this page and also do the internal vs external user check as some
        //pages are meant for internal users only
        return hasPermission(accessiblePages, page) && isPageVisibleInMenu(page, isInternalContext, isInternalUser);
    }

    private void removeFeatureDisabledPages(List<Page> pages) {
        List<Page> pagesToRemove = new LinkedList<>();
        pages.forEach(page -> {
            if (!isFeatureEnabled(page) || !isPageIncludedInLicensePackage(page)) {
                pagesToRemove.add(page);
            } else if (page.hasChildren()) {
                removeFeatureDisabledPages(page.getPages());
            }
        });
        pages.removeAll(pagesToRemove);
    }

    private boolean isPageIncludedInLicensePackage(Page page) {
        return (!configParamsService.getBooleanParameterValue(FEATURE_LICENSING_ENABLED) ||
                (PlatformThreadLocalContextHolder.getUserSwitch() == null  && isInternalUser() && (page.isInternal() || page.getName().equalsIgnoreCase("Support")) ) ||
                licenseService.isLicenseEnabledFor(getPropertyId(), page.getCode()));
    }
    private boolean isInternalUser() {
        String userId = PacmanWorkContextHelper.getUserId();
        GlobalUser globalUser = userGlobalDBService.getGlobalUserById(Integer.parseInt(userId));
        return nonNull(globalUser) && globalUser.isInternal();
    }

    private Integer getPropertyId() {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        return isNull(propertyId) ? Integer.valueOf(propertyService.getPropertyId(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())) : propertyId;
    }

    private boolean isFeatureEnabled(Page page) {
        switch (page.getCode()) {
            case TetrisPermissionKey.CHANNEL_FORECAST:
                return propertyConfigParamService.isChannelForecastDashboardEnabled();
            case TetrisPermissionKey.CHANNEL_RESTRICTION_ADJUSTMENTS_PROPERTY:
                return propertyConfigParamService.isChannelRestrictionAdjustmentEnabled();
            case TetrisPermissionKey.CHANNEL_RESTRICTION_ADJUSTMENTS_CLIENT:
                return propertyConfigParamService.isHiltonChannelRestrictionGlobalAdjustmentsEnabled();
            default:
                return true;
        }
    }

    private boolean hasPermission(Map<String, Page> accessiblePages, Page page) {
        //If the page is in our map of accessible pages, then we have permission to see the page
        return accessiblePages.get(page.getCode()) != null;
    }

    private Map<String, Page> convertToFlatMap(List<Page> accessiblePages) {
        //key = page.code and value = the page itself
        return accessiblePages.stream().collect(
                Collectors.toMap(Page::getCode, Function.identity())
        );
    }

    protected boolean isPageVisibleInMenu(Page page, boolean isInternalContext, boolean isInternalUser) {
        return (!page.isInternal() || (isInternalContext && isInternalUser));
    }

    public List<Page> getAuthorizedPages(boolean readFeatureAtClientLevel) {
        TetrisPrincipal principal = getPrincipal();
        List<Page> accessiblePages = new ArrayList<>();

        // Start with all pages and filter out otherwise you won't obtain certain things like
        // isExposedAsPermission or isPublic (if not internal user, omit internal pages)
        List<Page> allPagesCache = getAllPages();
        List<Page> allNonInternalPagesCache = getExternalPages();
        List<Page> pages = principal.isInternalUser() ? allPagesCache : allNonInternalPagesCache;

        // Build through permissions
        Set<String> perms = getPermsForUser(principal, readFeatureAtClientLevel);
        if (perms.contains(Role.ALL_PERMS_ID) && principal.isInternalUser()) {
            accessiblePages = pages;
        } else if (perms.contains(Role.NO_CLIENT_PROPS_ID)) {
            for (Page p : pages) {
                if (p.isAccessibleToNewClient() || p.isInternal()) {
                    accessiblePages.add(p);
                }
            }
        } else {
            String pageCode;
            Page page;
            for (String perm : perms) {
                if (perm.equals(Role.NO_PERMISSIONS)) {
                    LOGGER.warn("User has no permissions");
                } else {
                    PagePermission permittedPage = new PagePermission(perm);
                    pageCode = permittedPage.getPageCode();
                    page = PagesBuilder.getInstance().getPage(pageCode);
                    if (page != null) {
                        // Even if they have permission (not sure how), only internal users permitted to see internal pages
                        if (!page.isInternal() || principal.isInternalUser()) {
                            accessiblePages.add(page);
                        }
                    } else {
                        LOGGER.debug("Server returned permission to non-existent page: " + pageCode);
                    }
                }
            }

            // Add any public pages the user was not explicitly granted permission to
            List<Page> publicPages = getPublicPages();
            accessiblePages.addAll(publicPages);
        }
        //workaround to remove duplicates while not worrying about type
        return accessiblePages.stream().distinct().collect(Collectors.toList());
    }

    public List<Page> getAllPages() {
        return PagesBuilder.getInstance().getAllPages();
    }

    public List<Page> getExternalPages() {
        return PagesBuilder.getInstance().getExternalPages();
    }

    protected List<Page> getPublicPages() {
        return PagesBuilder.getInstance().getPublicPages();
    }

    protected Set<String> getPermsForUser(TetrisPrincipal principal, boolean readFeatureAtClientLevel) {
        boolean isInternal = principal.isInternalUser();
        Set<String> perms;

        Set<String> permsForContext = authorizationService.getPermsForContext(readFeatureAtClientLevel);
        String userSwitch = PlatformThreadLocalContextHolder.getUserSwitch();

        //check to see if the permissions contain the marker for System CEO/All Permissions
        if (permsForContext.contains(Role.ALL_PERMS_ID) && (!isInternal || userSwitch != null)) {
            List<Page> allNonInternalPagesCache = getExternalPages();
            perms = new HashSet<>();
            for (Page page : allNonInternalPagesCache) {
                perms.add(createPagePermissionString(page.getCode(), ActionKey.readWrite, page.getFunctionalities(), ActionKey.readWrite));
            }

            Map<String, Boolean> pagePermsForClient = authorizationService.getPermsFeatureMappingForContext(readFeatureAtClientLevel);
            permsForContext = removeFeaturePermissions(perms, pagePermsForClient);
            return permsForContext;
        } else {
            return permsForContext;
        }
    }

    public String createPagePermissionString(String pageCode, ActionKey actionKey, List<Functionality> functionalities, ActionKey functionAccess) {
        String pagePermission = "pageCode=" + pageCode + "&access=" + actionKey.toString();
        if (null != functionalities && !functionalities.isEmpty()) {
            String functionPermission = "&functions={";
            for (Functionality functionality : functionalities) {
                functionPermission += (functionality.getCode() + ":" + functionAccess.toString() + ",");
            }
            functionPermission = functionPermission.substring(0, functionPermission.length() - 1); // strip off extra comma
            functionPermission += "}";
            pagePermission += functionPermission;
        }
        return pagePermission;
    }

    public Set<String> removeFeaturePermissions(Set<String> allPermission, Map<String, Boolean> featurePermForClient) {
        Set<String> modifiedPerm = new HashSet<String>();
        modifiedPerm.addAll(allPermission);
        for (Map.Entry<String, Boolean> entry : featurePermForClient.entrySet()) {
            for (String permissionStr : allPermission) {
                if (new PagePermission(permissionStr).getPageCode().equalsIgnoreCase(new PagePermission(entry.getKey()).getPageCode()) && (entry.getValue().equals(false))) {
                    modifiedPerm.remove(permissionStr);
                }
            }
        }
        return modifiedPerm;
    }

    /**
     * This will return the menu hierarchy with no permissions or toggles applied to it.
     * For example, the hierarchy will look like the below structure:
     * <p>
     * Dashboards
     * At a Glance
     * Business Insights
     * Reputation Management
     * Manage
     * Demand and Wash Management
     * Group Wash By Group
     * Monitor
     * Information Manager
     * Investigator
     * Reports
     * Arrival By LOS Report
     * Booking Pace Report
     * Pricing Report
     * Configure
     * Inventory
     * Component Rooms
     * Rooms Configuration
     * Forecasts
     * Special Event Management
     * Limited Data Build
     * Market Segments
     * Permissions
     * Role Management
     * Function Space
     * Evaluation
     * Support
     * Admin Tools
     * Installation
     * Other
     * OPTIX
     * Nsight
     *
     * @return
     */


    public List<Page> getRawMenu() {
        List<Page> newMenu = PagesBuilder.getInstance().getNewMenu();
        //clone the menu as we don't want somebody accidentally modifying the original pages
        List<Page> clonedMenu = new ArrayList<>();
        for (Page page : newMenu) {
            clonedMenu.add(new Page(page));
        }
        return clonedMenu;
    }

    public List<Page> getRawMenuFDSMigration() {
        List<Page> newMenu = PagesBuilder.getInstance().getNewMenu();
        Map<String, List<Page>> systemConfigBasedPages = PagesBuilder.getInstance().getSystemConfigBasedPages();
        //clone the menu as we don't want somebody accidentally modifying the original pages
        List<Page> clonedMenu = new ArrayList<>();
        for (Page page : newMenu) {
            clonedMenu.add(new Page(page));
            //Top level addition of pages as standalone pages are typically the only ones modified by system configurations
            if (systemConfigBasedPages.containsKey(page.getCode())) {
                List<Page> pagesToAdd = systemConfigBasedPages.get(page.getCode());
                pagesToAdd.forEach(pageToAdd -> page.addToPages(new Page(pageToAdd)));
            }
        }
        return clonedMenu;
    }

    public List<Page> flattenMenuHierarchy(List<Page> pages, List<Page> flattenedList, boolean includeCategories) {
        for (Page page : pages) {
            if (!page.isCategory() || (page.isCategory() && includeCategories)) {
                flattenedList.add(page);
            }

            if (page.hasChildren()) {
                flattenedList = flattenMenuHierarchy(page.getPages(), flattenedList, includeCategories);
            }
        }

        return flattenedList;
    }
}
