package com.ideas.tetris.pacman.services.reports.inputoverride;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.inputoverride.dto.InputOverrideCategoryEnum;
import com.ideas.tetris.pacman.services.reports.inputoverride.dto.InputOverrideDTO;
import com.ideas.tetris.pacman.services.reports.inputoverride.dto.InputOverrideFilterDTO;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.JasperReportDataConverter;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.InputOverrideReportCriteria;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportData;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.scheduledreport.service.JasperReportService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class InputOverrideService extends JasperReportService<ScheduledReportData, InputOverrideReportCriteria> {

    private static final Logger LOGGER = Logger.getLogger(InputOverrideService.class.getName());

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Override
    protected ScheduledReportData retrieveData(ScheduledReport<InputOverrideReportCriteria> scheduledReport) {
        InputOverrideReportCriteria reportCriteria = scheduledReport.getReportCriteria();
        InputOverrideFilterDTO filterDTO = new InputOverrideFilterDTO();
        filterDTO.setStartDate(DateUtil.convertJavaUtilDateToLocalDate(reportCriteria.getStartDate()));
        filterDTO.setEndDate(DateUtil.convertJavaUtilDateToLocalDate(reportCriteria.getEndDate()));
        Set<InputOverrideCategoryEnum> inputOverrideCategories = new HashSet<>();
        if (reportCriteria.isDemandOverrideByOccDt()) {
            inputOverrideCategories.add(InputOverrideCategoryEnum.DEMAND_OCCUPANCY_DATE);
        }
        if (reportCriteria.isDemandOvrideByArrDtAndLOS()) {
            inputOverrideCategories.add(InputOverrideCategoryEnum.DEMAND_ARRIVAL_BY_LOS);
        }
        if (reportCriteria.isWashOverride()) {
            inputOverrideCategories.add(InputOverrideCategoryEnum.WASH);
        }
        if (reportCriteria.isGffOverrideFetch()) {
            inputOverrideCategories.add(InputOverrideCategoryEnum.GFFOVERRIDE);
        }

        filterDTO.setIsWashEnable(reportCriteria.isWashEnable());
        filterDTO.setIsDemandOccupancyDateEnable(reportCriteria.isDemandOccupancyDateEnable());
        filterDTO.setIsDemandArrivalByLosEnable(reportCriteria.isDemandArrivalByLosEnable());
        filterDTO.setGffOverrideFetch(reportCriteria.isGffOverrideFetch());

        filterDTO.setIsIncludeNoteEnable(reportCriteria.isNotesChecked());
        filterDTO.setIsRollingDate(reportCriteria.getIsRollingDate());
        filterDTO.setRollingStartDate(reportCriteria.getRollingStartDate());
        filterDTO.setRollingEndDate(reportCriteria.getRollingEndDate());

        populateReportCriteria(reportCriteria);
        List<InputOverrideDTO> dataList = getOverrideData(filterDTO);
        ScheduledReportSheet sheet = new ScheduledReportSheet("input-override-report", dataList, InputOverrideDTO.class);
        List<ScheduledReportSheet> sheetList = new ArrayList<ScheduledReportSheet>(1);
        sheetList.add(sheet);
        return new ScheduledReportData("input-override-report", sheetList);
    }

    private void populateReportCriteria(InputOverrideReportCriteria reportCriteria) {
        {
            String propertyId = reportCriteria.getPropertyId().toString();
            String userId = reportCriteria.getUserId().toString();
            String baseCurrency = reportCriteria.getCurrency();

            LocalDate startDate = DateUtil.convertJavaUtilDateToLocalDate(reportCriteria.getStartDate());
            LocalDate endDate = DateUtil.convertJavaUtilDateToLocalDate(reportCriteria.getEndDate());
            Integer rolling = reportCriteria.getIsRollingDate();
            String rollingStartDate = reportCriteria.getRollingStartDate();
            String rollingEndDate = reportCriteria.getRollingEndDate();

            String sql = " select * from dbo.ufn_get_filter_selection " +
                    "('" + propertyId + "'," + userId + ",'" + baseCurrency + "'," +
                    "'" + rolling + "'," +
                    "'" + startDate + "'," +
                    "'" + endDate + "','','','','','',''," +
                    "'" + rollingStartDate + "'," +
                    "'" + rollingEndDate + "','','','','','','' )";

            List<Object[]> resultList = getCrudServiceBean().findByNativeQuery(sql);
            if (resultList != null) {
                Object[] result = resultList.get(0);
                reportCriteria.setPropertyName((String) result[0]);
                reportCriteria.setCreatedBy((String) result[1]);
                reportCriteria.setCreatedOn(JavaLocalDateUtils.toJodaDateTime(ZonedDateTime.now()));
                LocalDate localStartDate = DateUtil.convertJavaUtilDateToLocalDate((Date) result[3], true);
                reportCriteria.setStartDate(localStartDate);
                LocalDate localEndDate = DateUtil.convertJavaUtilDateToLocalDate((Date) result[4], true);
                reportCriteria.setEndDate(localEndDate);
            }
        }
    }

    public List<InputOverrideDTO> getOverrideData(InputOverrideFilterDTO filterDto) {

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        QueryParameter queryParameters = QueryParameter.with("property_id", propertyId)
                .and("start_date", java.sql.Date.valueOf(filterDto.getStartDate()))
                .and("end_date", java.sql.Date.valueOf(filterDto.getEndDate()))
                .and("IsDemandOvrideByOccDate", filterDto.getIsDemandOccupancyDateEnable() ? "true" : "false")
                .and("IsDemandOvrideByArrDateAndLOS", filterDto.getIsDemandArrivalByLosEnable() ? "true" : "false")
                .and("IsWashOverride", filterDto.getIsWashEnable() ? "true" : "false")
                .and("IsNoteEnable", filterDto.getIsIncludeNoteEnable().toString())
                .and("isRollingDate", filterDto.getIsRollingDate())
                .and("rolling_start_date", filterDto.getRollingStartDate())
                .and("rolling_end_date", filterDto.getRollingEndDate())
                .and("isGffOverrideFetch", filterDto.isGffOverrideFetch() ? "true" : "false");


        try {
            return getCrudServiceBean().findByNativeQuery("select * from dbo.ufn_get_input_override_report(:property_id, :start_date, :end_date, :IsDemandOvrideByOccDate, " +
                            ":IsDemandOvrideByArrDateAndLOS, :IsWashOverride, :IsNoteEnable, :isRollingDate, :rolling_start_date, :rolling_end_date, :isGffOverrideFetch)",
                    queryParameters.parameters(), row -> {
                        InputOverrideDTO data = new InputOverrideDTO();

                        data.setPropertyName((String) row[0]);
                        data.setAccomClassName((String) row[1]);
                        data.setForecastGroupName((String) row[2]);
                        data.setDow(((String) row[3]).toLowerCase());

                        data.setOccupancyDate(DateUtil.convertJavaUtilDateToLocalDate((Date) row[4]));
                        data.setUserWashExpirationDate(DateUtil.convertJavaUtilDateToLocalDate((Date) row[9]));
                        data.setOverrideLastModifiedOn((Date) (row[12]));

                        data.setGrpfinalfcstoverride((BigDecimal) row[10]);
                        data.setGrpfinalfcstexpirationdate(DateUtil.convertJavaUtilDateToLocalDate((Date) row[11]));

                        data.setOverrideCategory((String) row[5]);
                        data.setLos((Integer) row[6]);
                        data.setUserDemandOverride((BigDecimal) row[7]);
                        data.setUserWashOverridePerc((BigDecimal) row[8]);
                        data.setOverrideLastModifiedBy((String) row[13]);
                        data.setNotes((String) row[14]);
                        data.setUserEmail((String) row[15]);
                        data.setForecstGroupCode((String) row[16]);
                        data.setUserName((String) row[17]);
                        return data;
                    });

        } catch (Exception e) {
            LOGGER.warn("No result found. PropertyId = " + propertyId + ".", e);
            return Collections.EMPTY_LIST;
        }
    }

    @Override
    public JasperReportDataConverter<ScheduledReportData, InputOverrideReportCriteria> getJasperReportDataConverter() {
        return null;
    }

    @Override
    public String getReportTitle(ScheduledReport<InputOverrideReportCriteria> scheduledReport) {
        return ResourceUtil.getText("input-override-report", scheduledReport.getLanguage());
    }

    private CrudService getCrudServiceBean() {
        return crudService;
    }

    public void setCrudServiceBean(CrudService crudService) {
        this.crudService = crudService;
    }
}
