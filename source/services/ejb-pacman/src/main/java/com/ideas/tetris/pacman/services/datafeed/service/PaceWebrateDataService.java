package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.optix.PaceWebrateDTO;
import com.ideas.tetris.platform.common.crudservice.CrudService;

import javax.inject.Inject;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PaceWebrateDataService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;


    public Boolean loadPaceWebrateDataIntoPacman(List<PaceWebrateDTO> data) {

        boolean isDataSaved = false;
        StringBuilder query = new StringBuilder();

        data.forEach(paceWebrateDTO -> {
            query.append("insert into PACE_Webrate_Differential values( \t");
            query.append(paceWebrateDTO.getWebrateSourceProperty() + ",\t");
            query.append("'" + getFormatedDate(paceWebrateDTO.getFirstWebrateGenerationDate()) + "'" + ",\t");
            query.append("'" + getFormatedDate(paceWebrateDTO.getWebrateGenerationDate()) + "'" + ",\t");
            query.append(paceWebrateDTO.getCount() + ",\t");
            query.append(paceWebrateDTO.getCompetitorsName() + ",\t");
            query.append(paceWebrateDTO.getWebrateChannel() + ",\t");
            query.append(paceWebrateDTO.getWebrateRoomType() + ",\t");
            query.append(paceWebrateDTO.getWebrateTypeName() + ",\t");
            query.append("'" + getFormatedDate(paceWebrateDTO.getOccupancyDate()) + "'" + ",\t");
            query.append(paceWebrateDTO.getLos() + ",\t");
            query.append("'" + paceWebrateDTO.getWebrateStatus() + "'" + ",\t");
            query.append("'" + paceWebrateDTO.getWebrateCurrency() + "'" + ",\t");
            query.append(paceWebrateDTO.getWebrateRateValue() + ",\t");
            query.append("'" + paceWebrateDTO.getWebrateRank() + "'" + ",\t");
            query.append("'" + paceWebrateDTO.getWebrateRating() + "'" + ",\t");
            query.append("SYSDATETIME()" + ",\t");
            query.append(paceWebrateDTO.getWebrateRateValueDisplay() + "\t");
            query.append(")\n");
        });

        if (!query.toString().isEmpty()) {
            tenantCrudService.executeUpdateByNativeQuery(query.toString());
            isDataSaved = true;
        }

        return isDataSaved;
    }


    public Boolean deleteData() {
        tenantCrudService.executeUpdateByNativeQuery("delete from PACE_Webrate_Differential where Occupancy_DT >= '2015-03-01'");
        return true;
    }

    private String getFormatedDate(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(date);
    }
}
