package com.ideas.tetris.pacman.services.security.dto;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public class ImportUser {
    private Integer userId;
    private String screenName;
    private String emailAddress;
    private String locale;
    private String password;
    private Name name;
    private Sex sex;
    private Calendar birthDate;
    private String jobTitle;
    private boolean autoPassword = false;
    private List<String> organizationNames = new ArrayList<String>();
    private List<PropertyRole> propertyRoles = new ArrayList<PropertyRole>();

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getUserId() {
        return userId;
    }

    public String getScreenName() {
        return screenName;
    }

    public void setScreenName(String screenName) {
        this.screenName = screenName;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getLocale() {
        return locale;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPassword() {
        return password;
    }

    public void setName(Name name) {
        this.name = name;
    }

    public Name getName() {
        return name;
    }

    public void setSex(Sex sex) {
        this.sex = sex;
    }

    public Sex getSex() {
        return sex;
    }

    public void setBirthDate(Calendar birthDate) {
        this.birthDate = birthDate;
    }

    public Calendar getBirthDate() {
        return birthDate;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public void setAutoPassword(boolean autoPassword) {
        this.autoPassword = autoPassword;
    }

    public boolean isAutoPassword() {
        return autoPassword;
    }

    public void setOrganizationNames(List<String> organizationNames) {
        this.organizationNames = organizationNames;
    }

    public List<String> getOrganizationNames() {
        return organizationNames;
    }

    public void addOrganizationName(String organizationName) {
        organizationNames.add(organizationName);
    }

    public void clearOrganizationNames() {
        organizationNames.clear();
    }

    public void setPropertyRoles(List<PropertyRole> propertyRoles) {
        this.propertyRoles = propertyRoles;
    }

    public List<PropertyRole> getPropertyRoles() {
        return propertyRoles;
    }

    public void addPropertyRole(PropertyRole propertyRole) {
        propertyRoles.add(propertyRole);
    }

    public void clearPropertyRoles() {
        propertyRoles.clear();
    }

}
