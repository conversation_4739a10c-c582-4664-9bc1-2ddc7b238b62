package com.ideas.tetris.pacman.services.minlos;

import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.minlos.entity.MinlosDecisions;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;


import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.OPTIMIZE_MIN_LOS_DECSION_SQL;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
public class MinlosDecisionService {
    private static final Logger LOGGER = Logger.getLogger(MinlosDecisionService.class.getName());

    private static final String SQL_PARAM_DATE_CAUGHTUP = "caughtupdate";
    private static final String SQL_PARAM_CHUNK_SIZE = "chunksize";
    private static final String DELETE_DECISIONS_WITH_ARRIVAL_GREATER_THAN_DATE = "delete top(:chunksize) r from dbo.Decision_MINLOS r where DATEDIFF(day, Arrival_DT, CONVERT(date, :caughtupdate)) <= 0";
    private static final String DELETE_DECISIONS_FROM_PACE_WITH_ARRIVAL_GREATER_THAN_DATE = "delete top(:chunksize) r from PACE_MINLOS r where DATEDIFF(day, Arrival_DT, CONVERT(date, :caughtupdate)) <= 0";
    private static final String GET_NUMBER_OF_DECISIONS_WITH_ARRIVAL_GREATER_THAN_DATE = "select count(*) from Decision_MINLOS where DATEDIFF(day, Arrival_DT, CONVERT(date, :caughtupdate)) <= 0";
    private static final String GET_NUMBER_OF_DECISIONS_FROM_PACE_WITH_ARRIVAL_GREATER_THAN_DATE = "select count(*) from PACE_MINLOS where DATEDIFF(day, Arrival_DT, CONVERT(date, :caughtupdate)) <= 0";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService crudService;

    @Autowired
	protected DateService dateService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    public List<MinlosDecisions> getDifferentialMinLosDecisionForRateCode(Date lastUploadedDate) {
        List<Object[]> decisionsFromDB = new ArrayList<>();

        if (null != lastUploadedDate) {
            if (pacmanConfigParamsService.getBooleanParameterValue(OPTIMIZE_MIN_LOS_DECSION_SQL)) {
                decisionsFromDB = crudService.findByNativeQuery(MinLosDecisionsQueries.GET_DIFFERENTIAL_MINLOS_DECISIONS_FOR_RATE_CODE_ROOM_TYPE_OPTIMIZED_SQL, QueryParameter.with(MinLosDecisionsQueries.DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                        .and(MinLosDecisionsQueries.DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate()).and(MinLosDecisionsQueries.LAST_UPLOADED_DATE, lastUploadedDate).parameters());
            } else {
                decisionsFromDB = crudService.findByNativeQuery(MinLosDecisionsQueries.GET_DIFFERENTIAL_MINLOS_DECISIONS_FOR_RATE_CODE_ROOM_TYPE, QueryParameter.with(MinLosDecisionsQueries.DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                        .and(MinLosDecisionsQueries.DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate()).and(MinLosDecisionsQueries.LAST_UPLOADED_DATE, lastUploadedDate).parameters());
            }

            LOGGER.info("DP:: getDifferentialMinLosDecisionForRateCode Calling differential min LOS decisionsFromDB " + decisionsFromDB.size());
        }

        return setMinlosDecisionsAsPerContract(decisionsFromDB);
    }

    public boolean checkDifferentialMinLosDecisionForRateCode(Date lastUploadedDate) {
        Integer count = null;
        if (null != lastUploadedDate) {
            count = crudService.findByNativeQuerySingleResult(MinLosDecisionsQueries.GET_COUNT_DIFFERENTIAL_MINLOS_DECISIONS_FOR_RATE_CODE_ROOM_TYPE_OPTIMIZED_SQL, QueryParameter.with(MinLosDecisionsQueries.DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                    .and(MinLosDecisionsQueries.DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate()).and(MinLosDecisionsQueries.LAST_UPLOADED_DATE, lastUploadedDate).parameters(), row -> (Integer) row[0]);
            LOGGER.info("Check differentialMinLosDecisionForRateCode decisions are available " + count);
        }
        return count != null;
    }

    public List<MinlosDecisions> getMinLOSDecisions(Date lastUploadedDate) {

        return retrieveAllMinlosDecisions(lastUploadedDate);
    }

    @SuppressWarnings("unchecked")
    private List<MinlosDecisions> retrieveAllMinlosDecisions(Date lastUploadedDate) {
        List<Object[]> decisionsFromDB;

        if (null == lastUploadedDate) {
            decisionsFromDB = crudService.findByNativeQuery(MinLosDecisionsQueries.GET_FULL_MINLOS_DECISIONS, QueryParameter.with(MinLosDecisionsQueries.DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                    .and(MinLosDecisionsQueries.DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate()).parameters());
            LOGGER.info("DP:: retrieveAllMinlosDecisions Calling full min LOS decisionsFromDB " + decisionsFromDB.size());
        } else {
            if (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.OPTIMIZE_MIN_LOS_DECSION_SQL)) {
                decisionsFromDB = crudService.findByNativeQuery(MinLosDecisionsQueries.GET_DIFFERENTIAL_MINLOS_DECISIONS_OPTIMIZED_SQL, QueryParameter.with(MinLosDecisionsQueries.DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                        .and(MinLosDecisionsQueries.DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate()).and(MinLosDecisionsQueries.LAST_UPLOADED_DATE, lastUploadedDate).parameters());
            } else {
                decisionsFromDB = crudService.findByNativeQuery(MinLosDecisionsQueries.GET_DIFFERENTIAL_MINLOS_DECISIONS, QueryParameter.with(MinLosDecisionsQueries.DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                        .and(MinLosDecisionsQueries.DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate()).and(MinLosDecisionsQueries.LAST_UPLOADED_DATE, lastUploadedDate).parameters());
            }

            LOGGER.info("DP:: retrieveAllMinlosDecisions Calling Differential min LOS decisionsFromDB " + decisionsFromDB.size());
        }
        return setMinlosDecisionsAsPerContract(decisionsFromDB);
    }

    public boolean checkMinlosDecisionsAvailable(Date lastUploadedDate) {
        Integer count;
        if (null == lastUploadedDate) {
            count = crudService.findByNativeQuerySingleResult(MinLosDecisionsQueries.GET_COUNT_FULL_MINLOS_DECISIONS, QueryParameter.with(MinLosDecisionsQueries.DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                    .and(MinLosDecisionsQueries.DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate()).parameters(), row -> (Integer) row[0]);
            LOGGER.info("Check Full decisions are available " + count);
        } else {
            count = crudService.findByNativeQuerySingleResult(MinLosDecisionsQueries.GET_COUNT_DIFFERENTIAL_MINLOS_DECISIONS_OPTIMIZED_SQL, QueryParameter.with(MinLosDecisionsQueries.DECISION_START_DATE, dateService.getOptimizationWindowStartDate())
                    .and(MinLosDecisionsQueries.DECISION_END_DATE, dateService.getDecisionUploadWindowEndDate()).and(MinLosDecisionsQueries.LAST_UPLOADED_DATE, lastUploadedDate).parameters(), row -> (Integer) row[0]);
            LOGGER.info("Check differential decisions are available " + count);
        }
        return count != null;
    }

    private List<MinlosDecisions> setMinlosDecisionsAsPerContract(List<Object[]> decisionsFromDB) {
        List<MinlosDecisions> minlosDecisionList = new ArrayList<MinlosDecisions>();
        for (int i = 0; i < decisionsFromDB.size(); i++) {
            Object[] row = decisionsFromDB.get(i);
            MinlosDecisions minlosDecisions = new MinlosDecisions();
            minlosDecisions.setArrivalDate((Date) row[0]);
            minlosDecisions.setRoomType((String) row[1]);
            minlosDecisions.setRatePlan((String) row[2]);
            minlosDecisions.setMinlos(((Integer) row[3]).intValue());
            minlosDecisionList.add(minlosDecisions);

        }
        return minlosDecisionList;
    }

    public Integer deleteMinlosDecisionsFromPaceAfterDate(Integer chunkSize, Date date) {
        Query q;
        String queryToRun = DELETE_DECISIONS_FROM_PACE_WITH_ARRIVAL_GREATER_THAN_DATE;
        q = crudService.getEntityManager().createNativeQuery(queryToRun);
        q.setParameter(SQL_PARAM_DATE_CAUGHTUP, date);
        q.setParameter(SQL_PARAM_CHUNK_SIZE, chunkSize);
        return q.executeUpdate();
    }

    public Integer deleteMinlosDecisionsAfterDate(Integer chunkSize, Date date) {
        Query q;
        String queryToRun = DELETE_DECISIONS_WITH_ARRIVAL_GREATER_THAN_DATE;
        q = crudService.getEntityManager().createNativeQuery(queryToRun);
        q.setParameter(SQL_PARAM_DATE_CAUGHTUP, date);
        q.setParameter(SQL_PARAM_CHUNK_SIZE, chunkSize);
        return q.executeUpdate();
    }

    public Integer getTotalNumberOfRecordsFromPaceAfterDate(Date date) {
        Query q;
        String queryToRun = GET_NUMBER_OF_DECISIONS_FROM_PACE_WITH_ARRIVAL_GREATER_THAN_DATE;
        q = crudService.getEntityManager().createNativeQuery(queryToRun);
        q.setParameter(SQL_PARAM_DATE_CAUGHTUP, date);
        return (Integer) q.getSingleResult();
    }

    public Integer getTotalNumberOfRecordsAfterDate(Date date) {
        Query q;
        String queryToRun = GET_NUMBER_OF_DECISIONS_WITH_ARRIVAL_GREATER_THAN_DATE;
        q = crudService.getEntityManager().createNativeQuery(queryToRun);
        q.setParameter(SQL_PARAM_DATE_CAUGHTUP, date);
        return (Integer) q.getSingleResult();
    }
}
