package com.ideas.tetris.pacman.services.webrate.dto;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

import static java.math.RoundingMode.HALF_UP;

public class WebRateCompetitorAverageRateDTO {

    private Integer competitorId;
    private Integer accomClassId;
    private BigDecimal averageRate;
    private BigDecimal absoluteDiff;
    private BigDecimal average;
    private Double percentageDiff;

    public WebRateCompetitorAverageRateDTO(Object row, Map<Integer, BigDecimal> avgHistoricalAdrByWebRateAccomTypes) {
        Object[] columns = (Object[]) row;
        this.competitorId = (Integer) columns[0];
        this.accomClassId = (Integer) columns[1];
        this.averageRate = columns[2] instanceof BigDecimal ? (BigDecimal) columns[2] : BigDecimal.ZERO;
        this.averageRate = this.averageRate.setScale(2, RoundingMode.HALF_UP);
        BigDecimal avgHistoricalAdr = initializeZeroIfNotPresent(avgHistoricalAdrByWebRateAccomTypes.get(accomClassId));
        this.absoluteDiff = avgHistoricalAdr.subtract(averageRate).abs();
        this.average = avgHistoricalAdr.add(averageRate).divide(new BigDecimal(2), HALF_UP);
        this.percentageDiff = BigDecimal.ZERO.equals(average) ? 0 : absoluteDiff.divide(average, HALF_UP).doubleValue() * 100;
    }

    public Integer getCompetitorId() {
        return competitorId;
    }

    public void setCompetitorId(Integer competitorId) {
        this.competitorId = competitorId;
    }

    public Integer getAccomClassId() {
        return accomClassId;
    }

    public void setAccomClassId(Integer accomClassId) {
        this.accomClassId = accomClassId;
    }

    public BigDecimal getAverageRate() {
        return averageRate;
    }

    public void setAverageRate(BigDecimal averageRate) {
        this.averageRate = averageRate;
    }

    public BigDecimal getAverage() {
        return average;
    }

    public void setAverage(BigDecimal average) {
        this.average = average;
    }

    public BigDecimal getAbsoluteDiff() {
        return absoluteDiff;
    }

    public void setAbsoluteDiff(BigDecimal absoluteDiff) {
        this.absoluteDiff = absoluteDiff;
    }

    public Double getPercentageDiff() {
        return percentageDiff;
    }

    public void setPercentageDiff(Double percentageDiff) {
        this.percentageDiff = percentageDiff;
    }

    private BigDecimal initializeZeroIfNotPresent(BigDecimal avgHistoricalAdr) {
        return null == avgHistoricalAdr ? BigDecimal.ZERO : avgHistoricalAdr;
    }
}
