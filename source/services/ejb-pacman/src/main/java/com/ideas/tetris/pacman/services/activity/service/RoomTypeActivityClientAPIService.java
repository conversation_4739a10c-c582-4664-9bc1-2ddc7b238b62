package com.ideas.tetris.pacman.services.activity.service;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.api.client.activityRoomType.RoomTypeActivityApi;
import com.ideas.api.client.activityRoomType.RoomTypeActivityV2Api;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.roomtyperecoding.entity.RTRecodingRoomTypesDto;
import com.ideas.tetris.platform.common.rest.mapper.PlatformNGIRestClient;
import com.ideas.tetris.platform.services.client.ClientCodePropertyCodeMappingService;
import com.ideas.tetris.platform.services.client.ClientProperty;

import javax.inject.Inject;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class RoomTypeActivityClientAPIService {
    @Autowired
	private PlatformNGIRestClient platformNgiRestClient;
    @Autowired
    ClientCodePropertyCodeMappingService clientCodePropertyCodeMappingService;

    public Map<String, Map<String, Integer>> getUniqueRoomTypeCodesWithRoomTypeCapacityForProperties(final String clientCode, final List<String> propertyCodes) {
        final Map<String, Map<String, Integer>> uniqueRoomTypeCodesWithRoomTypeCapacityForProperties = new HashMap<>();
        final String convertedClientCode = clientCodePropertyCodeMappingService.getOnlyClientMappingForNgi(clientCode);
        for (String g3PropertyCode : propertyCodes) {
            List<String> convertedPropertyCodes = clientCodePropertyCodeMappingService.getPropertyCodesMappingForNgi(clientCode, Collections.singletonList(g3PropertyCode));
            Map<String, Map<String, Integer>> uniqueRoomTypeCodesWithRoomTypeCapacityForProperty = SystemConfig.isPmsInboundV2() ?
                    getApiV2Instance(clientCode, g3PropertyCode).getUniqueRoomTypeCodesWithRoomTypeCapacityForProperties(convertedClientCode, new HashSet<>(convertedPropertyCodes)) :
                    getApiInstance(clientCode, g3PropertyCode).getUniqueRoomTypeCodesWithRoomTypeCapacityForProperties(convertedClientCode, convertedPropertyCodes);

            uniqueRoomTypeCodesWithRoomTypeCapacityForProperties.putAll(uniqueRoomTypeCodesWithRoomTypeCapacityForProperty);
        }

        return convertToG3PropertiesMap(convertedClientCode, uniqueRoomTypeCodesWithRoomTypeCapacityForProperties);
    }

    public List<RTRecodingRoomTypesDto> getUniqueRoomTypeCodesWithRoomTypeCapacity(final String clientCode, final String propertyCode, final String correlationId) {
        ClientProperty clientProperty = clientCodePropertyCodeMappingService.translateG3ClientPropertyCodes(clientCode, propertyCode);

        final Map<String, Integer> uniqueRoomTypeCodesWithRoomTypeCapacity = SystemConfig.isPmsInboundV2() ?
                getApiV2Instance(clientCode, propertyCode).getUniqueRoomTypeCodesWithRoomTypeCapacity(clientProperty.getClientCode(), clientProperty.getPropertyCode(), correlationId) :
                getApiInstance(clientCode, propertyCode).getUniqueRoomTypeCodesWithRoomTypeCapacity(clientProperty.getClientCode(), clientProperty.getPropertyCode(), correlationId);

        return uniqueRoomTypeCodesWithRoomTypeCapacity.entrySet().stream()
                .map(entry -> new RTRecodingRoomTypesDto(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
    }

    public Set<String> getUniqueRoomTypeCodes(final String clientCode, final String propertyCode) {
        ClientProperty clientProperty = clientCodePropertyCodeMappingService.translateG3ClientPropertyCodes(clientCode, propertyCode);

        return SystemConfig.isPmsInboundV2() ?
                getApiV2Instance(clientCode, propertyCode).getUniqueRoomTypeCodes(clientProperty.getClientCode(), clientProperty.getPropertyCode()) :
                new HashSet<>(getApiInstance(clientCode, propertyCode).getUniqueRoomTypeCodes(clientProperty.getClientCode(), clientProperty.getPropertyCode()));
    }

    @VisibleForTesting
    RoomTypeActivityApi getApiInstance(String clientCode, String propertyCode) {
        return new RoomTypeActivityApi(platformNgiRestClient.getClientAPI(platformNgiRestClient.getPMSInboundNGIBaseUrl(clientCode, propertyCode)));
    }

    @VisibleForTesting
    RoomTypeActivityV2Api getApiV2Instance(String clientCode, String propertyCode) {
        return new RoomTypeActivityV2Api(platformNgiRestClient.getClientV2API(platformNgiRestClient.getPMSInboundNGIBaseUrl(clientCode, propertyCode)));
    }

    private Map<String, Map<String, Integer>> convertToG3PropertiesMap(final String ngiClientCode, final Map<String, Map<String, Integer>> uniqueRoomTypeCodesWithRoomTypeCapacityForProperties) {
        Map<String, Map<String, Integer>> convertedPropertiesMap = new HashMap<>();
        uniqueRoomTypeCodesWithRoomTypeCapacityForProperties
                .entrySet()
                .stream()
                .forEach(
                        mapEntry -> convertedPropertiesMap.put(clientCodePropertyCodeMappingService.translateNGIClientPropertyCodes(ngiClientCode, mapEntry.getKey()).getPropertyCode(), mapEntry.getValue())
                );
        return convertedPropertiesMap;
    }
}
