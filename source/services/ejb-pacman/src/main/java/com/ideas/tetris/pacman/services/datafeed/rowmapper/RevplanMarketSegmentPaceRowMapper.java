package com.ideas.tetris.pacman.services.datafeed.rowmapper;

import com.ideas.tetris.pacman.services.datafeed.dto.RevplanMarketSegmentPace;
import com.ideas.tetris.platform.common.crudservice.RowMapper;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class RevplanMarketSegmentPaceRowMapper implements RowMapper<RevplanMarketSegmentPace> {
    @Override
    public RevplanMarketSegmentPace mapRow(Object[] row) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date asOfDate;
        Date occupancyDate;
        try {
            asOfDate = dateFormat.parse(row[0].toString());
            occupancyDate = dateFormat.parse(row[1].toString());
        } catch (ParseException e) {
            asOfDate = null;
            occupancyDate = null;
        }
        return new RevplanMarketSegmentPace(asOfDate, occupancyDate, row[2].toString(), ((BigDecimal) row[3]).intValue(), (BigDecimal) row[4]);
    }
}