package com.ideas.tetris.pacman.services.reports.userreport.dto;

import java.util.Set;

public class FilterDto {
    private int propertiesAllSelected;
    private Set<Integer> propertiesSelectedItems;

    private int authorizationGroupAllSelected;
    private Set<Integer> authorizationGroupSelectedItems;

    private int rolesAllSelected;
    private Set<String> rolesSelectedItems;

    private int usersAllSelected;
    private Set<Integer> usersSelectedItems;

    private Integer status;
    private Integer isUserExternal;
    private String userDefindedDateFormat;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsUserExternal() {
        return isUserExternal;
    }

    public void setIsUserExternal(Integer isUserExternal) {
        this.isUserExternal = isUserExternal;
    }

    public int isPropertiesAllSelected() {
        return propertiesAllSelected;
    }

    public void setPropertiesAllSelected(int propertiesAllSelected) {
        this.propertiesAllSelected = propertiesAllSelected;
    }

    public Set<Integer> getPropertiesSelectedItems() {
        return propertiesSelectedItems;
    }

    public void setPropertiesSelectedItems(Set<Integer> propertiesSelectedItems) {
        this.propertiesSelectedItems = propertiesSelectedItems;
    }

    public int isAuthorizationGroupAllSelected() {
        return authorizationGroupAllSelected;
    }

    public void setAuthorizationGroupAllSelected(
            int authorizationGroupAllSelected) {
        this.authorizationGroupAllSelected = authorizationGroupAllSelected;
    }

    public Set<Integer> getAuthorizationGroupSelectedItems() {
        return authorizationGroupSelectedItems;
    }

    public void setAuthorizationGroupSelectedItems(
            Set<Integer> authorizationGroupSelectedItems) {
        this.authorizationGroupSelectedItems = authorizationGroupSelectedItems;
    }

    public int isRolesAllSelected() {
        return rolesAllSelected;
    }

    public void setRolesAllSelected(int rolesAllSelected) {
        this.rolesAllSelected = rolesAllSelected;
    }

    public Set<String> getRolesSelectedItems() {
        return rolesSelectedItems;
    }

    public void setRolesSelectedItems(Set<String> rolesSelectedItems) {
        this.rolesSelectedItems = rolesSelectedItems;
    }

    public int isUsersAllSelected() {
        return usersAllSelected;
    }

    public void setUsersAllSelected(int usersAllSelected) {
        this.usersAllSelected = usersAllSelected;
    }

    public Set<Integer> getUsersSelectedItems() {
        return usersSelectedItems;
    }

    public void setUsersSelectedItems(Set<Integer> usersSelectedItems) {
        this.usersSelectedItems = usersSelectedItems;
    }

    public String getUserDefindedDateFormat() {
        return userDefindedDateFormat;
    }

    public void setUserDefindedDateFormat(String userDefindedDateFormat) {
        this.userDefindedDateFormat = userDefindedDateFormat;
    }
}
