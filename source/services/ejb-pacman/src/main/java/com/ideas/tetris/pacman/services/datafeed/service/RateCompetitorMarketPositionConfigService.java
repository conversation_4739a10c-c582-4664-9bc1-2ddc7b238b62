package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.datafeed.dto.rateshopping.RateCompetitorMarketPositionConfig;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateRankingAccomClass;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateRankingAccomClassOverride;
import com.ideas.tetris.pacman.services.webrate.service.AccommodationMappingService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class RateCompetitorMarketPositionConfigService {

    @Autowired
	private AccommodationMappingService accommodationMappingService;

    @Autowired
    AgileRatesConfigurationService agileRatesConfigurationService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    public List<RateCompetitorMarketPositionConfig> getRateCompetitorMarketPositionConfiguration(final Integer propertyId, final Date startDate) {

        final List<WebrateRankingAccomClass> webrateRankingAccomClasses = accommodationMappingService.getWebrateRankingAccomClassForProperty(propertyId);
        Map<Integer, String> independentSystemDefaultProductsMap = Collections.emptyMap();
        if (CollectionUtils.isEmpty(webrateRankingAccomClasses)) {
            return Collections.emptyList();
        }
        List<Product> independentSystemDefaultProducts = agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts();
        independentSystemDefaultProductsMap = independentSystemDefaultProducts.stream().collect(Collectors.toMap(Product::getId, Product::getName));

        final List<RateCompetitorMarketPositionConfig> rateCompetitorMarketPositionConfigs = new ArrayList<>();
        for (WebrateRankingAccomClass webrateRankingAccomClass : webrateRankingAccomClasses) {
            RateCompetitorMarketPositionConfig defaultRateCompetitorMarketPositionConfig = new RateCompetitorMarketPositionConfig();
            populateDefaultRateCompetitorMarketPositionConfigData(webrateRankingAccomClass, defaultRateCompetitorMarketPositionConfig);
            setProductName(independentSystemDefaultProductsMap, defaultRateCompetitorMarketPositionConfig, webrateRankingAccomClass.getProductID());
            rateCompetitorMarketPositionConfigs.add(defaultRateCompetitorMarketPositionConfig);
            final Set<WebrateRankingAccomClassOverride> webrateRankingAccomClassOverrides = webrateRankingAccomClass.getWebrateRankingACOverrides();
            if (CollectionUtils.isNotEmpty(webrateRankingAccomClassOverrides)) {
                final Set<WebrateRankingAccomClassOverride> webrateRankingAccomClassOverrideSet = webrateRankingAccomClassOverrides.stream().filter(
                        webrateRankingAccomClassOverride -> LocalDate.fromDateFields(webrateRankingAccomClassOverride.getWebrateRankingEndDT()).isAfter(LocalDate.fromDateFields(startDate))
                                || LocalDate.fromDateFields(webrateRankingAccomClassOverride.getWebrateRankingEndDT()).isEqual(LocalDate.fromDateFields(startDate))).collect(Collectors.toSet());
                for (WebrateRankingAccomClassOverride webrateRankingAccomClassOverride : webrateRankingAccomClassOverrideSet) {
                    RateCompetitorMarketPositionConfig seasonedRateCompetitorMarketPositionConfig = new RateCompetitorMarketPositionConfig();
                    populateSeasonedRateCompetitorMarketPositionConfigData(webrateRankingAccomClassOverride, seasonedRateCompetitorMarketPositionConfig);
                    setProductName(independentSystemDefaultProductsMap, seasonedRateCompetitorMarketPositionConfig, webrateRankingAccomClassOverride.getProductID());
                    rateCompetitorMarketPositionConfigs.add(seasonedRateCompetitorMarketPositionConfig);
                }
            }
        }
        Comparator<RateCompetitorMarketPositionConfig> rateCompetitorMarketPositionConfigComparator = Comparator.comparing((RateCompetitorMarketPositionConfig rateCompetitorMarketPositionConfig) -> rateCompetitorMarketPositionConfig.getConfigCategory())
                .thenComparing((RateCompetitorMarketPositionConfig rateCompetitorMarketPositionConfig) -> rateCompetitorMarketPositionConfig.getAccomClassCode());
        Stream<RateCompetitorMarketPositionConfig> rateCompetitorMarketPositionConfigStream = rateCompetitorMarketPositionConfigs.stream().sorted(rateCompetitorMarketPositionConfigComparator);
        return rateCompetitorMarketPositionConfigStream.collect(Collectors.toList());
    }

    private void setProductName(Map<Integer, String> independentSystemDefaultProductsMap, RateCompetitorMarketPositionConfig defaultRateCompetitorMarketPositionConfig, Integer productID) {
        if (productID != null && independentSystemDefaultProductsMap.containsKey(productID)) {
            defaultRateCompetitorMarketPositionConfig.setProductName(independentSystemDefaultProductsMap.get(productID));
        }
    }

    private void populateDefaultRateCompetitorMarketPositionConfigData(final WebrateRankingAccomClass webrateRankingAccomClass, RateCompetitorMarketPositionConfig defaultRateCompetitorMarketPositionConfig) {
        defaultRateCompetitorMarketPositionConfig.setAccomClassCode(webrateRankingAccomClass.getAccomClass().getCode());
        defaultRateCompetitorMarketPositionConfig.setConfigCategory(Constants.DEFAULT);
        defaultRateCompetitorMarketPositionConfig.setCompMktPosConstraintFriday(webrateRankingAccomClass.getWebrateRankingFriday() == null ? null : webrateRankingAccomClass.getWebrateRankingFriday().getWebrateRankingName());
        defaultRateCompetitorMarketPositionConfig.setCompMktPosConstraintMonday(webrateRankingAccomClass.getWebrateRankingMonday() == null ? null : webrateRankingAccomClass.getWebrateRankingMonday().getWebrateRankingName());
        defaultRateCompetitorMarketPositionConfig.setCompMktPosConstraintSaturday(webrateRankingAccomClass.getWebrateRankingSaturday() == null ? null : webrateRankingAccomClass.getWebrateRankingSaturday().getWebrateRankingName());
        defaultRateCompetitorMarketPositionConfig.setCompMktPosConstraintSunday(webrateRankingAccomClass.getWebrateRankingSunday() == null ? null : webrateRankingAccomClass.getWebrateRankingSunday().getWebrateRankingName());
        defaultRateCompetitorMarketPositionConfig.setCompMktPosConstraintThursday(webrateRankingAccomClass.getWebrateRankingThursday() == null ? null : webrateRankingAccomClass.getWebrateRankingThursday().getWebrateRankingName());
        defaultRateCompetitorMarketPositionConfig.setCompMktPosConstraintTuesday(webrateRankingAccomClass.getWebrateRankingTuesday() == null ? null : webrateRankingAccomClass.getWebrateRankingTuesday().getWebrateRankingName());
        defaultRateCompetitorMarketPositionConfig.setCompMktPosConstraintWednesday(webrateRankingAccomClass.getWebrateRankingWednesday() == null ? null : webrateRankingAccomClass.getWebrateRankingWednesday().getWebrateRankingName());
    }

    private void populateSeasonedRateCompetitorMarketPositionConfigData(final WebrateRankingAccomClassOverride webrateRankingAccomClassOverride, RateCompetitorMarketPositionConfig seasonedRateCompetitorMarketPositionConfig) {
        seasonedRateCompetitorMarketPositionConfig.setAccomClassCode(webrateRankingAccomClassOverride.getAccomClass().getCode());
        seasonedRateCompetitorMarketPositionConfig.setConfigCategory(Constants.SEASONAL);
        seasonedRateCompetitorMarketPositionConfig.setCompMktPosConstraintFriday(webrateRankingAccomClassOverride.getWebrateRankingOvrFriday() == null ? null : webrateRankingAccomClassOverride.getWebrateRankingOvrFriday().getWebrateRankingName());
        seasonedRateCompetitorMarketPositionConfig.setCompMktPosConstraintMonday(webrateRankingAccomClassOverride.getWebrateRankingOvrMonday() == null ? null : webrateRankingAccomClassOverride.getWebrateRankingOvrMonday().getWebrateRankingName());
        seasonedRateCompetitorMarketPositionConfig.setCompMktPosConstraintSaturday(webrateRankingAccomClassOverride.getWebrateRankingOvrSaturday() == null ? null : webrateRankingAccomClassOverride.getWebrateRankingOvrSaturday().getWebrateRankingName());
        seasonedRateCompetitorMarketPositionConfig.setCompMktPosConstraintSunday(webrateRankingAccomClassOverride.getWebrateRankingOvrSunday() == null ? null : webrateRankingAccomClassOverride.getWebrateRankingOvrSunday().getWebrateRankingName());
        seasonedRateCompetitorMarketPositionConfig.setCompMktPosConstraintThursday(webrateRankingAccomClassOverride.getWebrateRankingOvrThursday() == null ? null : webrateRankingAccomClassOverride.getWebrateRankingOvrThursday().getWebrateRankingName());
        seasonedRateCompetitorMarketPositionConfig.setCompMktPosConstraintTuesday(webrateRankingAccomClassOverride.getWebrateRankingOvrTuesday() == null ? null : webrateRankingAccomClassOverride.getWebrateRankingOvrTuesday().getWebrateRankingName());
        seasonedRateCompetitorMarketPositionConfig.setCompMktPosConstraintWednesday(webrateRankingAccomClassOverride.getWebrateRankingOvrWednesday() == null ? null : webrateRankingAccomClassOverride.getWebrateRankingOvrWednesday().getWebrateRankingName());
        seasonedRateCompetitorMarketPositionConfig.setStartDate(webrateRankingAccomClassOverride.getWebrateRankingStartDT());
        seasonedRateCompetitorMarketPositionConfig.setEndDate(webrateRankingAccomClassOverride.getWebrateRankingEndDT());
    }

    public void setAccommodationMappingService(AccommodationMappingService accommodationMappingService) {
        this.accommodationMappingService = accommodationMappingService;
    }
}
