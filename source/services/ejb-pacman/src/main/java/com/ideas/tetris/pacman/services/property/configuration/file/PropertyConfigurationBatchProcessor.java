package com.ideas.tetris.pacman.services.property.configuration.file;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.PropertyAttributePropertyNewAttributeConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFile;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecord;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecordStatus;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileStatus;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import com.ideas.tetris.pacman.services.property.configuration.service.costofwalk.CostOfWalkDefaultConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.extendedstay.ExtendedStayConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.inventorysharing.InventorySharingConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.metadata.MetaDataConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.overbooking.PropertyOverbookingConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.overbooking.RoomTypeOverbookingConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.propertyattribute.PropertyAttributeConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.rateshopping.RateShoppingConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.roomclass.RoomClassConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.roomtype.RoomTypeConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.setup.PropertySetupConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.specialevent.SpecialEventConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.srp.SpecialRatePlanConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.user.UserConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.webrate.WebRateDefaultConfigurationService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import javax.persistence.Query;
import java.io.File;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PropertyConfigurationBatchProcessor {
    private static final Logger LOGGER = Logger.getLogger(PropertyConfigurationBatchProcessor.class.getName());
    private static final String CONFIGURATION_FILE = "configurationFile";
    private static final String CLIENT_ID = "clientId";

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;
    @MetaDataConfigurationService.Qualifier
    @Autowired
	@Qualifier("metaDataConfigurationService")
	private AbstractPropertyConfigurationService metaDataConfigurationService;
    @InventorySharingConfigurationService.Qualifier
    @Autowired
	@Qualifier("inventorySharingConfigurationService")
	private AbstractPropertyConfigurationService inventorySharingConfigurationService;
    @PropertyOverbookingConfigurationService.Qualifier
    @Autowired
	@Qualifier("propertyOverbookingConfigurationService")
	private AbstractPropertyConfigurationService propertyOverbookingConfigurationService;
    @RoomTypeOverbookingConfigurationService.Qualifier
    @Autowired
	@Qualifier("roomTypeOverbookingConfigurationService")
	private AbstractPropertyConfigurationService roomTypeOverbookingConfigurationService;
    @PropertyAttributeConfigurationService.Qualifier
    @Autowired
	@Qualifier("propertyAttributeConfigurationService")
	private AbstractPropertyConfigurationService propertyAttributeConfigurationService;
    @RoomClassConfigurationService.Qualifier
    @Autowired
	@Qualifier("roomClassConfigurationService")
	private AbstractPropertyConfigurationService roomClassConfigurationService;
    @RoomTypeConfigurationService.Qualifier
    @Autowired
	@Qualifier("roomTypeConfigurationService")
	private AbstractPropertyConfigurationService roomTypeConfigurationService;
    @PropertySetupConfigurationService.Qualifier
    @Autowired
	@Qualifier("propertySetupConfigurationService")
	private AbstractPropertyConfigurationService propertySetupConfigurationService;
    @SpecialEventConfigurationService.Qualifier
    @Autowired
	@Qualifier("specialEventConfigurationService")
	private AbstractPropertyConfigurationService specialEventConfigurationService;
    @WebRateDefaultConfigurationService.Qualifier
    @Autowired
	@Qualifier("webRateDefaultConfigurationService")
	private AbstractPropertyConfigurationService webRateDefaultConfigurationService;
    @CostOfWalkDefaultConfigurationService.Qualifier
    @Autowired
	@Qualifier("costOfWalkDefaultConfigurationService")
	private AbstractPropertyConfigurationService costOfWalkDefaultConfigurationService;
    @RateShoppingConfigurationService.Qualifier
    @Autowired
	@Qualifier("rateShoppingConfigurationService")
	private AbstractPropertyConfigurationService rateShoppingConfigurationService;
    @UserConfigurationService.Qualifier
    @Autowired
	@Qualifier("userConfigurationService")
	private AbstractPropertyConfigurationService userConfigurationService;
    @SpecialRatePlanConfigurationService.Qualifier
    @Autowired
	@Qualifier("specialRatePlanConfigurationService")
	private AbstractPropertyConfigurationService specialRatePlanConfigurationService;
    @ExtendedStayConfigurationService.Qualifier
    @Autowired
	@Qualifier("extendedStayConfigurationService")
	private AbstractPropertyConfigurationService extendedStayConfigurationService;
    @Autowired
	private PacmanConfigParamsService configService;

    public List<AbstractPropertyConfigurationService> getPropertyConfigurationServices() {
        return Arrays.asList(metaDataConfigurationService, propertySetupConfigurationService,
                roomClassConfigurationService, roomTypeConfigurationService, propertyOverbookingConfigurationService,
                roomTypeOverbookingConfigurationService, inventorySharingConfigurationService,
                webRateDefaultConfigurationService, specialEventConfigurationService,
                propertyAttributeConfigurationService, costOfWalkDefaultConfigurationService,
                rateShoppingConfigurationService, userConfigurationService, specialRatePlanConfigurationService,
                extendedStayConfigurationService);

    }

    // Mockito these away
    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public void setConfigService(PacmanConfigParamsService configService) {
        this.configService = configService;
    }

    public void setPropertySetupConfigurationService(AbstractPropertyConfigurationService propertySetupConfigurationService) {
        this.propertySetupConfigurationService = propertySetupConfigurationService;
    }

    public ConfigurationFile createConfigurationFile(PropertyConfigurationFile file) {
        ConfigurationFile configurationFile = null;
        if (file.isValid()) {
            String clientCode = file.getMetaDataRecord().getClientCode();
            Integer clientId = getClientId(clientCode);
            if (clientId == null) {
                throw new TetrisException(ErrorCode.CONFIG_FILE_INVALID, "Unable to create ConfigurationFile. Client id not found for client code: " + clientCode);
            }
            configurationFile = new ConfigurationFile();
            configurationFile.setConfigurationFileStatus(ConfigurationFileStatus.PENDING);
            configurationFile.setFileName(file.getConfigurationFile().getName());
            configurationFile.setClientId(clientId);
            configurationFile.setNumberOfProperties(file.getMetaDataRecord().getNumberOfProperties());
            configurationFile.setSnapshotDate(file.getMetaDataRecord().getSnapShotDateTime());
            configurationFile.setPreparedDate(file.getMetaDataRecord().getPreparedDateTime());
            configurationFile = globalCrudService.save(configurationFile);
        }
        return configurationFile;
    }

    public int deleteRecordsForProperties(Integer clientId, Set<String> propertyCodes) {
        Query query = globalCrudService.getEntityManager().createNamedQuery(ConfigurationFileRecord.DELETE_BY_PROPERTY);
        query.setParameter(CLIENT_ID, clientId);
        query.setParameter("propertyCodes", propertyCodes);
        return query.executeUpdate();
    }

    /**
     * Loads the header file information to the ConfigurationFile table. If
     * anything in the Configuration Metadata Record is invalid, don't load the file.
     */
    public ConfigurationFile findOrCreateConfigurationFile(File file) {
        Integer clientId = PlatformThreadLocalContextHolder.getWorkContext().getClientId();

        ConfigurationFile configurationFile = globalCrudService.findByNamedQuerySingleResult(ConfigurationFile.FIND_BY_FILE_NAME, QueryParameter.with("fileName", file.getName()).and(CLIENT_ID, clientId).parameters());
        if (configurationFile == null) {
            configurationFile = new ConfigurationFile();
            configurationFile.setClientId(clientId);
            configurationFile.setFileName(file.getName());
            configurationFile.setConfigurationFileStatus(ConfigurationFileStatus.PENDING);

            String failureReason = null;

            PropertyConfigurationFile propertyConfigurationFile = new PropertyConfigurationFile(file);
            if (propertyConfigurationFile.isValid()) {
                failureReason = validConfigurationFile(configurationFile, propertyConfigurationFile, failureReason);
            } else {
                failureReason = propertyConfigurationFile.getError();
            }

            // If there is any failure reason, don't load the file.
            if (StringUtils.isNotEmpty(failureReason)) {
                configurationFile.setFailureReason(failureReason);
                configurationFile.setConfigurationFileStatus(ConfigurationFileStatus.FAILED);
            }

            configurationFile = globalCrudService.save(configurationFile);
        }

        return configurationFile;
    }

    private String validConfigurationFile(ConfigurationFile configurationFile, PropertyConfigurationFile propertyConfigurationFile, String failureReason) {
        configurationFile.setSnapshotDate(propertyConfigurationFile.getMetaDataRecord().getSnapShotDateTime());
        configurationFile.setPreparedDate(propertyConfigurationFile.getMetaDataRecord().getPreparedDateTime());
        configurationFile.setNumberOfProperties(propertyConfigurationFile.getMetaDataRecord().getNumberOfProperties());

        if (StringUtils.isEmpty(propertyConfigurationFile.getMetaDataRecord().getClientCode())) {
            failureReason = "Client Code cannot be empty";
        } else if (!StringUtils.equalsIgnoreCase(propertyConfigurationFile.getMetaDataRecord().getClientCode(), findClientCode())) {
            failureReason = "File is for different Client Code than in the Context";
        } else {
            Long count = findCountOfConfigurationFilesWithMoreRecentSnapshotDates(propertyConfigurationFile.getMetaDataRecord().getSnapShotDateTime());
            if (count != null && count.longValue() > 0) {
                failureReason = "A previously loaded Configuration File has a more recent snapshot date.";
            }
        }

        if (failureReason == null && configurationFile.getNumberOfProperties() != null && configurationFile.getNumberOfProperties().intValue() > 0) {
            int numberOfProperties = configurationFile.getNumberOfProperties().intValue();
            if (propertyConfigurationFile.getTotalProperties() == 0) {
                failureReason = "Configuration Metadata record indicates " + numberOfProperties + " properties, but none were found in file";
            } else if (propertyConfigurationFile.getTotalProperties() != numberOfProperties) {
                failureReason = "Configuration Metadata record indicates " + numberOfProperties + " properties, but " + propertyConfigurationFile.getTotalProperties() + " were found";
            }
        }
        return failureReason;
    }

    public long findCountOfConfigurationFileRecords(ConfigurationFile configurationFile) {
        return ((Long) globalCrudService.findByNamedQuerySingleResult(ConfigurationFileRecord.FIND_COUNT_RECORDS_FOR_FILE, QueryParameter.with(CONFIGURATION_FILE, configurationFile).parameters())).longValue();
    }

    public int findCountOfConfigurationFileRecordsInStatus(ConfigurationFile configurationFile, ConfigurationFileRecordStatus recordStatus) {
        return ((Long) globalCrudService.findByNamedQuerySingleResult(ConfigurationFileRecord.FIND_COUNT_RECORDS_FOR_FILE_IN_STATUS, QueryParameter.with(CONFIGURATION_FILE, configurationFile).and("configurationFileRecordStatus", recordStatus).parameters())).intValue();
    }

    public void updateConfigurationFileToStatus(ConfigurationFile configurationFile, ConfigurationFileStatus configurationFileStatus) {
        configurationFile.setConfigurationFileStatus(configurationFileStatus);
        globalCrudService.save(configurationFile);
    }

    public void updatePendingRecordToStatusWithMessageConfigurationFileToStatus(ConfigurationFile configurationFile, ConfigurationFileRecordStatus configurationFileRecordStatus, String failureReason) {
        Query query = globalCrudService.getEntityManager().createNamedQuery(ConfigurationFileRecord.UPDATE_PENDING_RECORDS_FOR_FILE_TO_STATUS);
        query.setParameter(CONFIGURATION_FILE, configurationFile);
        query.setParameter("configurationFileRecordStatus", configurationFileRecordStatus);
        query.setParameter("failureReason", failureReason);
        query.executeUpdate();
    }

    public void updateConfigurationFileRecordsToPending(Integer clientId, Set<String> propertyCodes) {
        Query query = globalCrudService.getEntityManager().createNamedQuery(ConfigurationFileRecord.UPDATE_TO_PENDING_FOR_PROPERTY);
        query.setParameter(CLIENT_ID, clientId);
        query.setParameter("propertyCodes", propertyCodes);
        query.executeUpdate();
    }

    public void updateConfigurationFileRecordStatus(Integer clientId, String propertyCode, PropertyConfigurationRecordType recordType, ConfigurationFileRecordStatus status) {
        Query query = globalCrudService.getEntityManager().createNamedQuery(ConfigurationFileRecord.UPDATE_STATUS_FOR_PROPERTY_AND_RECORD_TYPE);
        query.setParameter(CLIENT_ID, clientId);
        query.setParameter("propertyCode", propertyCode);
        query.setParameter("recordType", recordType.name());
        query.setParameter("status", status);
        query.executeUpdate();
    }

    public void updateFailedConfigurationFileRecordsToPending(Integer clientId, String propertyCode, Set<String> recordTypes) {
        Query query = globalCrudService.getEntityManager().createNamedQuery(ConfigurationFileRecord.UPDATE_FAILED_TO_PENDING_FOR_PROPERTY);
        query.setParameter(CLIENT_ID, clientId);
        query.setParameter("propertyCode", propertyCode);
        query.setParameter("recordTypes", recordTypes);
        query.executeUpdate();
    }

    /**
     * Create a ConfigurationFileRecord for each line of the file. If the
     * Property Code cannot be determined, set the status to failed.
     */
    public void createConfigurationFileRecordsForLines(ConfigurationFile configurationFile, List<String> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        String[] pipedLine = null;
        ConfigurationFileRecord configurationFileRecord = null;

        for (String record : records) {
            if (record.equals(PropertyConfigurationFile.HEADER) || record.equals(PropertyConfigurationFile.FOOTER)) {
                continue;
            }
            pipedLine = StringUtils.splitPreserveAllTokens(record, PropertyConfigurationDto.DELIMITER);
            configurationFileRecord = new ConfigurationFileRecord();
            configurationFileRecord.setConfigurationFile(configurationFile);
            configurationFileRecord.setConfigurationRecordStatus(ConfigurationFileRecordStatus.PENDING);
            configurationFileRecord.setRecordType(pipedLine[0].replaceAll("_", ""));

            if (!StringUtils.equalsIgnoreCase(PropertyConfigurationRecordType.CONFIG_META.getRecordType(), pipedLine[0])) {
                if (pipedLine.length > 1) {
                    configurationFileRecord.setPropertyCode(pipedLine[1]);
                } else {
                    configurationFileRecord.setConfigurationRecordStatus(ConfigurationFileRecordStatus.FAILED);
                    configurationFileRecord.setFailureReason("Property Code not found in Record");
                }
            } else {
                configurationFileRecord.setConfigurationRecordStatus(ConfigurationFileRecordStatus.PASSED);
            }

            configurationFileRecord.setRecord(record);
            globalCrudService.save(configurationFileRecord);
        }
        globalCrudService.getEntityManager().flush();
        globalCrudService.getEntityManager().clear();
    }

    @SuppressWarnings("unchecked")
    public List<ConfigurationFileRecord> findPendingConfigurationFileRecordsByFile(ConfigurationFile configurationFile, int lastProcessedConfigurationFileRecordId, List<String> recordTypes, int batchSize) {
        return globalCrudService.findByNamedQuery(ConfigurationFileRecord.FIND_PENDING_RECORDS_FOR_FILE_GREATER_THAN_ID, QueryParameter.with(CONFIGURATION_FILE, configurationFile).and("recordTypes", recordTypes).and("configurationFileRecordId", lastProcessedConfigurationFileRecordId).parameters(), batchSize);
    }

    @SuppressWarnings("unchecked")
    public List<ConfigurationFileRecord> findPendingConfigurationFileRecordsByProperty(Integer clientId, Set<String> propertyCodes, int lastProcessedConfigurationFileRecordId, List<String> recordTypes, int batchSize) {
        return globalCrudService.findByNamedQuery(ConfigurationFileRecord.FIND_PENDING_RECORDS_FOR_CLIENT_AND_PROPERTIES_GREATER_THAN_ID, QueryParameter.with(CLIENT_ID, clientId).and("propertyCodes", propertyCodes).and("recordTypes", recordTypes).and("configurationFileRecordId", lastProcessedConfigurationFileRecordId).parameters(), batchSize);
    }

    public void processConfigurationFileRecords(List<ConfigurationFileRecord> configurationFileRecords) {
        if (configurationFileRecords == null || configurationFileRecords.isEmpty()) {
            return;
        }

        Map<String, Integer> propertyIdMap = new HashMap<String, Integer>();
        for (ConfigurationFileRecord configurationFileRecord : configurationFileRecords) {
            try {
                // If the ConfigurationFileRecord doesn't have a record value, nothing to do...
                String record = configurationFileRecord.getRecord();
                if (StringUtils.isEmpty(record)) {
                    LOGGER.warn("ConfigurationFileRecord: " + configurationFileRecord.getId() + " is missing Record value");
                    configurationFileRecord.setConfigurationRecordStatus(ConfigurationFileRecordStatus.FAILED);
                    configurationFileRecord.setFailureReason("No Record to Process");
                    globalCrudService.save(configurationFileRecord);
                    continue;
                }

                // Parse the pipe separated record
                String[] pipedLine = StringUtils.splitPreserveAllTokens(record, PropertyConfigurationDto.DELIMITER);

                // Verify that the RecordType value of the record is of a known type
                PropertyConfigurationRecordType recordType = PropertyConfigurationRecordType.valueOfRecordType(pipedLine[0]);
                if (recordType == null) {
                    LOGGER.warn("Unsupported Record Type: " + pipedLine[0] + " in line: " + record);
                    configurationFileRecord.setConfigurationRecordStatus(ConfigurationFileRecordStatus.FAILED);
                    configurationFileRecord.setFailureReason("Unsupported Record Type:" + pipedLine[0]);
                    globalCrudService.save(configurationFileRecord);
                    continue;
                }

                // Build the appropriate PropertyConfigurationDto and call service to handle it.
                PropertyConfigurationDto propertyConfigurationDto;
                if (recordType.equals(PropertyConfigurationRecordType.PA) && configService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_PROPERTY_ATTRIBUTE_PAIRING_CHANGES_FROM_FDS.value())) {
                    propertyConfigurationDto = new PropertyAttributePropertyNewAttributeConfigurationDto();
                } else {
                    propertyConfigurationDto = recordType.getDtoClass().newInstance();
                }

                try {
                    propertyConfigurationDto.setRecordFields(pipedLine);
                } catch (Exception e) {
                    LOGGER.error("Unable to Parse Record: " + configurationFileRecord.getId(), e);
                    configurationFileRecord.setConfigurationRecordStatus(ConfigurationFileRecordStatus.FAILED);
                    configurationFileRecord.setFailureReason("Unable to Parse Record: " + record + "Details: " + ExceptionUtils.getRootCauseMessage(e));
                    globalCrudService.save(configurationFileRecord);
                    continue;
                }

                // Call Service for PropertyConfigurationDtoType
                // Get a list of all known handlers
                // For each PropertyConfigurationDto, see what hander can handle each record
                List<AbstractPropertyConfigurationService> configurationServices = getPropertyConfigurationServices();
                iterateEachRecord(configurationServices, propertyIdMap, propertyConfigurationDto, configurationFileRecord);

                // If service doesn't fail, set the record status to passed
                globalCrudService.save(configurationFileRecord);
            } catch (Exception e) {
                LOGGER.error("An Unexpected Error Occurred", e);
                throw new TetrisException(ErrorCode.CONFIG_FILE_INVALID, "Unexpected Exception Occurred", e);
            }
        }
        globalCrudService.getEntityManager().flush();
        globalCrudService.getEntityManager().clear();
    }

    private void iterateEachRecord(List<AbstractPropertyConfigurationService> configurationServices, Map<String, Integer> propertyIdMap, PropertyConfigurationDto propertyConfigurationDto, ConfigurationFileRecord configurationFileRecord) {
        for (AbstractPropertyConfigurationService service : configurationServices) {
            // If the handler can handle that record type, let it handle it.
            if (service != null && service.canHandle(propertyConfigurationDto)) {
                handleRecord(propertyIdMap, propertyConfigurationDto, service, configurationFileRecord);
                break;
            }
        }
    }

    private void handleRecord(Map<String, Integer> propertyIdMap, PropertyConfigurationDto propertyConfigurationDto, AbstractPropertyConfigurationService service, ConfigurationFileRecord configurationFileRecord) {
        try {
            Integer propertyId = propertyIdMap.get(propertyConfigurationDto.getPropertyCode());
            if (propertyId == null) {
                propertyId = findPropertyIdForCode(propertyConfigurationDto.getPropertyCode());
                propertyIdMap.put(propertyConfigurationDto.getPropertyCode(), propertyId);
            }

            // Set the Property Information in the WorkContext
            WorkContextType workContext = PacmanThreadLocalContextHolder.getWorkContext();
            workContext.setPropertyCode(propertyConfigurationDto.getPropertyCode());
            workContext.setPropertyId(propertyId);

            // Validate the dto, if invalid will throw PropertyConfigurationServiceException
            List<PropertyConfigurationRecordFailure> exceptions = service.validate(propertyConfigurationDto, propertyId);
            boolean hasErrors = hasErrors(exceptions);

            // Call handler when valid
            if (!hasErrors) {
                service.handle(propertyConfigurationDto, propertyId);
            }

            // If there were exceptions during validation, write all of the messages to the record's failure reason and update status.
            StringBuilder message = new StringBuilder();

            // Iterate through the Exceptions to see if there are any failures (leaves room for adding warnings later.)
            // Comma-delimit all exceptions in the failure reason column.
            for (PropertyConfigurationRecordFailure exception : exceptions) {
                if (message.length() != 0) {
                    message.append(",");
                }

                message.append(exception.getMessage());
            }

            if (message.length() != 0) {
                LOGGER.error("An Exception Occurred attempting to update Record: " + configurationFileRecord.getId() + " - " + message.toString());
                configurationFileRecord.setFailureReason(message.toString());
            }

            if (hasErrors) {
                configurationFileRecord.setConfigurationRecordStatus(ConfigurationFileRecordStatus.FAILED);
            } else {
                configurationFileRecord.setConfigurationRecordStatus(ConfigurationFileRecordStatus.PASSED);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to process record: " + configurationFileRecord.getRecord(), e);
            configurationFileRecord.setFailureReason("Unable to process Record: " + e.getMessage());
            configurationFileRecord.setConfigurationRecordStatus(ConfigurationFileRecordStatus.FAILED);
        }
    }

    public Integer findPropertyIdForCode(String propertyCode) {
        Integer clientId = PlatformThreadLocalContextHolder.getWorkContext().getClientId();
        Integer propertyId = globalCrudService.findByNamedQuerySingleResult(Property.GET_ID, QueryParameter.with("code", propertyCode).and("clientId", clientId).parameters());
        if (propertyId == null) {
            LOGGER.warn("Unable to find Property ID for code: " + propertyCode);
            return null;
        }

        return propertyId;
    }

    public boolean hasErrors(List<PropertyConfigurationRecordFailure> exceptions) {
        if (exceptions != null) {
            for (PropertyConfigurationRecordFailure exception : exceptions) {
                if (ConfigurationFileRecordStatus.FAILED.equals(exception.getStatus())) {
                    return true;
                }
            }
        }

        return false;
    }

    public Long findCountOfConfigurationFilesWithMoreRecentSnapshotDates(Date snapshotDate) {
        return (Long) globalCrudService.findByNamedQuerySingleResult(ConfigurationFile.FIND_COUNT_WITH_MORE_RECENT_SNAPSHOT_DATE, QueryParameter.with("snapshotDate", snapshotDate).and(CLIENT_ID, PlatformThreadLocalContextHolder.getWorkContext().getClientId()).parameters());
    }

    public String findClientCode() {
        WorkContextType workContext = PacmanThreadLocalContextHolder.getWorkContext();
        if (workContext != null) {
            return workContext.getClientCode();
        }

        return null;
    }

    public Integer getClientId(String clientCode) {
        Integer clientId = null;
        if (!StringUtils.isBlank(clientCode)) {
            clientId = globalCrudService.findByNamedQuerySingleResult(Client.GET_ID_BY_CODE, QueryParameter.with(Client.CODE_CONSTANT, clientCode).parameters());
        }
        return clientId;
    }

    public List<ConfigurationFileRecord> findPendingConfigurationFileRecordsByProperty(Integer clientId,
                                                                                       Set<String> propertyCodes,
                                                                                       int lastProcessedConfigurationFileRecordId,
                                                                                       List<String> recordTypes) {
        final List<ConfigurationFileRecord> records = globalCrudService.findByNamedQuery(ConfigurationFileRecord.FIND_PENDING_RECORDS_FOR_CLIENT_AND_PROPERTIES_GREATER_THAN_ID,
                QueryParameter.with(CLIENT_ID, clientId).and("propertyCodes", propertyCodes).and("recordTypes", recordTypes)
                        .and("configurationFileRecordId", lastProcessedConfigurationFileRecordId).parameters());
        return CollectionUtils.isEmpty(records) ? Collections.emptyList() : records;
    }
}
