package com.ideas.tetris.pacman.services.forecast;

import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dailybar.entity.DailyBarDecisions;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.forecast.dto.OccupancyForecastByMarketSegmentDTO;
import com.ideas.tetris.pacman.services.forecast.dto.OccupancyForecastByRoomTypeDTO;
import com.ideas.tetris.pacman.services.forecast.dto.OccupancyForecastDTO;
import com.ideas.tetris.pacman.services.limiteddatabuild.dto.LDBProjectionDaySummary;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.divide;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.toDate;

@Component
@Transactional
@Slf4j
public abstract class AbstractOccupancyForecastService {

    static final String LV0_PRODUCT_NAME = "LV0";
    private static final BigDecimal HUNDRED = new BigDecimal("100.00");

    @Autowired
    protected DateService dateService;

    @Autowired
    protected PacmanConfigParamsService pacmanConfigParamsService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    protected CrudService tenantCrudService;

    public abstract List<OccupancyForecastDTO> getOccupancyForecastForProperty(final String clientCode,
                                                                               final String propertyCode,
                                                                               final LocalDate startDate);

    public abstract List<OccupancyForecastByRoomTypeDTO> getOccupancyForecastForPropertyByRoomType(final String roomTypeCode,
                                                                                                   final String clientCode,
                                                                                                   final String propertyCode,
                                                                                                   final LocalDate startDate);

    public abstract List<OccupancyForecastByMarketSegmentDTO> getOccupancyForecastForPropertyByMarketSegment(final String marketSegmentCode,
                                                                                                             final String clientCode,
                                                                                                             final String propertyCode,
                                                                                                             final LocalDate startDate);

    protected OccupancyForecastDTO createOccupancyForecastDTO(final Object[] occupancyForecast,
                                                              final Map<Date, BigDecimal> totalAccomCapacities,
                                                              final Map<Date, BigDecimal> lv0Decisions,
                                                              final Map<Date, BigDecimal> adrForMasterRoomClasses) {
        final Date occupancyDate = (Date) occupancyForecast[0];
        final BigDecimal totalForecastedRevenue = (BigDecimal) occupancyForecast[1];
        final BigDecimal totalForecastedOccupancy = (BigDecimal) occupancyForecast[2];

        final OccupancyForecastDTO dto = new OccupancyForecastDTO();

        dto.setOccupancyDate(LocalDateUtils.toJavaLocalDateFromSQLDate(occupancyDate));
        dto.setLv0Decision(lv0Decisions.getOrDefault(occupancyDate, BigDecimal.ZERO));
        dto.setForecastedADR(divide(totalForecastedRevenue, totalForecastedOccupancy));
        dto.setMasterClassForecastedADR(adrForMasterRoomClasses.getOrDefault(occupancyDate, BigDecimal.ZERO));
        dto.setCurrencyCode(getCurrencyCode());

        dto.setForecastedOccupancyPercent(getForecastedOccupancyPercent(occupancyDate, totalAccomCapacities, totalForecastedOccupancy));

        return dto;
    }

    protected BigDecimal getForecastedOccupancyPercent(final Date occupancyDate, final Map<Date, BigDecimal> totalAccomCapacities, final BigDecimal totalForecastedOccupancy) {
        final BigDecimal totalAccomCapacity = totalAccomCapacities.getOrDefault(occupancyDate, BigDecimal.ZERO);

        return BigDecimal.ZERO.equals(totalAccomCapacity)
                ? BigDecimal.ZERO
                : totalForecastedOccupancy
                .divide(totalAccomCapacity, MathContext.DECIMAL32)
                .multiply(BigDecimal.valueOf(100))
                .setScale(1, RoundingMode.HALF_UP);
    }

    protected OccupancyForecastByRoomTypeDTO createOccupancyForecastByRoomTypeDTO(final Object[] decision,
                                                                                  final String roomTypeCode) {

        final var dto = new OccupancyForecastByRoomTypeDTO();
        dto.setOccupancyDate(LocalDateUtils.toJavaLocalDateFromSQLDate((Date) decision[0]));
        dto.setRoomTypeCode(roomTypeCode);
        dto.setSingleRate((BigDecimal) decision[1]);
        dto.setDoubleRate((BigDecimal) decision[2]);
        dto.setCurrencyCode(getCurrencyCode());

        return dto;
    }


    protected OccupancyForecastByMarketSegmentDTO createOccupancyForecastByMarketSegmentDTO(final Object[] occupancyForecast,
                                                                                            final Map<Date, BigDecimal> totalAccomCapacities,
                                                                                            final Map<Date, BigDecimal> msOccupancyCounts) {

        final Date occupancyDate = (Date) occupancyForecast[0];
        final BigDecimal totalForecastedOccupancy = (BigDecimal) occupancyForecast[1];

        final OccupancyForecastByMarketSegmentDTO dto = new OccupancyForecastByMarketSegmentDTO();

        dto.setOccupancyDate(LocalDateUtils.toJavaLocalDateFromSQLDate(occupancyDate));
        dto.setMsOccupancyCount(msOccupancyCounts.getOrDefault(occupancyDate, BigDecimal.ZERO));

        final BigDecimal totalAccomCapacity = totalAccomCapacities.getOrDefault(occupancyDate, BigDecimal.ZERO);
        dto.setForecastedMSOccupancyPercent(BigDecimal.ZERO.equals(totalAccomCapacity)
                ? BigDecimal.ZERO
                : totalForecastedOccupancy
                .divide(totalAccomCapacity, MathContext.DECIMAL32)
                .multiply(BigDecimal.valueOf(100))
                .setScale(1, RoundingMode.HALF_UP));

        return dto;
    }

    protected List<OccupancyForecastDTO> createOccupancyForecastLDB(LocalDate startDate, LocalDate endDate,
                                                                    Map<LocalDate, LDBProjectionDaySummary> hotelLdbProjectionDaySummaryByDate,
                                                                    Map<LocalDate, LDBProjectionDaySummary> barLdbProjectionDaySummaryByDate,
                                                                    Map<Date, BigDecimal> totalAccomCapacities) {
        return startDate.datesUntil(endDate.plusDays(1))
                .map(occupancyDate -> {
                    final BigDecimal forecastedADR = Optional.ofNullable(hotelLdbProjectionDaySummaryByDate.get(occupancyDate))
                            .map(LDBProjectionDaySummary::getAvgRate)
                            .filter(avgRate -> avgRate.compareTo(BigDecimal.ZERO) > 0)
                            .orElse(BigDecimal.ZERO);

                    final BigDecimal lv0Decision = Optional.ofNullable(barLdbProjectionDaySummaryByDate.get(occupancyDate))
                            .filter(summary -> summary.getAvgRate() != null && summary.getAvgRate().compareTo(BigDecimal.ZERO) > 0)
                            .or(() -> Optional.ofNullable(hotelLdbProjectionDaySummaryByDate.get(occupancyDate)))
                            .map(LDBProjectionDaySummary::getAvgRate)
                            .filter(avgRate -> avgRate.compareTo(BigDecimal.ZERO) > 0)
                            .orElse(BigDecimal.ZERO);

                    final BigDecimal forecastedOccupancyPercent = Optional.ofNullable(totalAccomCapacities.get(toDate(occupancyDate)))
                            .filter(totalHotelOccupancy -> totalHotelOccupancy.compareTo(BigDecimal.ZERO) > 0)
                            .flatMap(totalHotelOccupancy ->
                                    Optional.ofNullable(hotelLdbProjectionDaySummaryByDate.get(occupancyDate))
                                            .map(LDBProjectionDaySummary::getDayRoomSold)
                                            .map(BigDecimal::valueOf)
                                            .map(forecastedOccupancy -> divide(forecastedOccupancy, totalHotelOccupancy).multiply(HUNDRED).setScale(2, RoundingMode.HALF_UP)))
                            .orElse(BigDecimal.ZERO);

                    final OccupancyForecastDTO occupancyForecast = new OccupancyForecastDTO();
                    occupancyForecast.setOccupancyDate(occupancyDate);
                    occupancyForecast.setLv0Decision(lv0Decision);
                    occupancyForecast.setForecastedADR(forecastedADR);
                    occupancyForecast.setMasterClassForecastedADR(forecastedADR);
                    occupancyForecast.setForecastedOccupancyPercent(forecastedOccupancyPercent);
                    occupancyForecast.setLimitedDataBuild(true);
                    occupancyForecast.setCurrencyCode(getCurrencyCode());

                    return occupancyForecast;
                })
                .collect(Collectors.toList());
    }

    protected Map<LocalDate, OccupancyForecastByRoomTypeDTO> getOccupancyForecastForPropertyByRoomTypeInternal(
            String clientCode, String propertyCode, String roomTypeCode, Date start, Date end) {

        log.debug(">>> Fetching occupancy forecast by room type for clientCode: {}, propertyCode: {}, roomType: {}, from {} to {}",
                clientCode, propertyCode, roomTypeCode, start, end);

        return Optional.ofNullable(end)
                .map(endDate -> {
                    List<Object[]> raw = tenantCrudService.findByNamedQuery(DailyBarDecisions.GET_BY_START_END_DATE_PRODUCT_NAME_AND_ACCOM_TYPE,
                            QueryParameter.with(START_DATE, start)
                                    .and(END_DATE, end)
                                    .and(ACCOM_TYPE_CODE, roomTypeCode)
                                    .and(PRODUCT_NAME, LV0_PRODUCT_NAME)
                                    .parameters());

                    log.debug(">>> Found occupancy forecast(s) by room type, count {}, clientCode: {}, propertyCode: {}, roomType: {}",
                            raw.size(), clientCode, propertyCode, roomTypeCode);

                    return raw;
                })
                .map(List::stream)
                .map(forecastsStream -> forecastsStream
                        .map(raw -> createOccupancyForecastByRoomTypeDTO(raw, roomTypeCode))
                        .collect(Collectors.toMap(OccupancyForecastByRoomTypeDTO::getOccupancyDate, Function.identity(), (prev, next) -> next, LinkedHashMap::new)))
                .orElseGet(LinkedHashMap::new);
    }

    protected String getCurrencyCode() {
        return pacmanConfigParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE);
    }

    protected OccupancyForecastByRoomTypeDTO createLDBOccupancyForecastByRoomTypeDTO(LocalDate occupancyDate, String roomTypeCode,
                                                                                  BigDecimal singleRate, BigDecimal doubleRate) {
        final OccupancyForecastByRoomTypeDTO forecast = new OccupancyForecastByRoomTypeDTO();
        forecast.setOccupancyDate(occupancyDate);
        forecast.setRoomTypeCode(roomTypeCode);
        forecast.setSingleRate(singleRate);
        forecast.setDoubleRate(doubleRate);
        forecast.setLimitedDataBuild(true);
        forecast.setCurrencyCode(getCurrencyCode());
        return forecast;
    }
}
