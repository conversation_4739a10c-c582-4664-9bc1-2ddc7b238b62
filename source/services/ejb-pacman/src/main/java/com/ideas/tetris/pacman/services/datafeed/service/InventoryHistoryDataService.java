package com.ideas.tetris.pacman.services.datafeed.service;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.entity.InventoryHistory;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.ideas.tetris.pacman.common.constants.Constants.END_DATE;
import static com.ideas.tetris.pacman.common.constants.Constants.START_DATE;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class InventoryHistoryDataService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
	private DateService dateService;

    public List<InventoryHistory> getInventoryHistoryData(DatafeedRequest datafeedRequest) {
        List<InventoryHistory> result;
        if (isInventoryHistoryDatafeedOptimizedStoreProcEnabled()) {
            result = tenantCrudService.findByNamedQuery(InventoryHistory.GET_OPTIMIZED_INVENTORY_HISTORY_DATA_WHEN_DECISION_ACK_SUCCESS_STATUS_SKIPPED_FOR_SRPFPLOS, getParametersWhenDecisionAckSuccessStatusSkippedForSRPFPLOS(datafeedRequest));
        }
        else {
            result = tenantCrudService.findByNamedQuery(InventoryHistory.GET_INVENTORY_HISTORY_DATA_WHEN_DECISION_ACK_SUCCESS_STATUS_SKIPPED_FOR_SRPFPLOS, getParametersWhenDecisionAckSuccessStatusSkippedForSRPFPLOS(datafeedRequest));
        }
        return Optional.ofNullable(result)
                .orElse(Collections.emptyList());
    }

    private Map<String, Object> getParameters(DatafeedRequest datafeedRequest) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(START_DATE, new LocalDate(datafeedRequest.getStartDate()));
        parameters.put(END_DATE, new LocalDate(datafeedRequest.getEndDate()));
        return parameters;
    }

    private Map<String, Object> getParametersWhenDecisionAckSuccessStatusSkippedForSRPFPLOS(DatafeedRequest datafeedRequest) {
        LocalDate systemDate = LocalDateUtils.fromDate(dateService.getCaughtUpDate());
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(START_DATE, new LocalDate(datafeedRequest.getStartDate()));
        parameters.put(END_DATE, new LocalDate(datafeedRequest.getEndDate()));
        parameters.put("startDecisionDate", systemDate.minusDays(1));
        parameters.put("isSRPFPLOSAtTotalLevel", getSRPFPLOSAtTotalLevel());
        parameters.put("propertyId", PacmanWorkContextHelper.getPropertyId());
        parameters.put("pageOffset", datafeedRequest.getStartPosition());
        parameters.put("pageSize", datafeedRequest.getSize());
        return parameters;
    }

    private boolean getSRPFPLOSAtTotalLevel() {
        return configParamsService.getBooleanParameterValue(IntegrationConfigParamName.SRP_FPLOS_AT_TOTAL_LEVEL);
    }

    @VisibleForTesting
    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    private boolean isInventoryHistoryDatafeedOptimizedStoreProcEnabled() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INVENTORY_HISTORY_DATAFEED_OPTIMIZED_STORED_PROC_ENABLED);
    }
}