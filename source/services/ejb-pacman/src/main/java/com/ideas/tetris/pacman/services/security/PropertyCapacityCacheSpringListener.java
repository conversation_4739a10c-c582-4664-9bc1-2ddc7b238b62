package com.ideas.tetris.pacman.services.security;


import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.platform.common.event.job.JobEvent;
import com.ideas.tetris.platform.common.event.property.PropertyStatusChangedEvent;
import org.apache.log4j.Logger;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class PropertyCapacityCacheSpringListener {

    private static final Logger LOGGER = Logger.getLogger(PropertyCapacityCacheSpringListener.class);


    @Autowired
	private PropertyCapacityCache propertyCapacityCache;

    @EventListener(condition = Constants.BDE_COMPLETED_CONDITION)
    public void observeBDECompletedJobEvent(JobEvent event) {
        if(SystemConfig.getIsLoggerInfoEnabledForSpringListeners())
            LOGGER.info("Logger info for spring event listener: "+event.toString());
        propertyCapacityCache.observeBDECompletedJobEvent(event);
    }

    @EventListener(condition = Constants.CDP_COMPLETED_CONDITION)
    public void observeCDPCompletedJobEvent(JobEvent event) {
        if(SystemConfig.getIsLoggerInfoEnabledForSpringListeners())
            LOGGER.info("Logger info for spring event listener: "+event.toString());
        propertyCapacityCache.observeCDPCompletedJobEvent(event);
    }

    @EventListener(condition = Constants.RLMS_COMPLETED_CONDITION)
    public void observeRefreshLMSCompletedJobEvent(JobEvent event) {
        if(SystemConfig.getIsLoggerInfoEnabledForSpringListeners())
            LOGGER.info("Logger info for spring event listener: "+event.toString());
        propertyCapacityCache.observeRefreshLMSCompletedJobEvent(event);
    }

    @EventListener(condition = Constants.DELETED_CONDITION)
    public void observeDeletedPropertyStatusEvent(PropertyStatusChangedEvent event) {
        if(SystemConfig.getIsLoggerInfoEnabledForSpringListeners())
            LOGGER.info("Logger info for spring event listener: "+event.toString());
        propertyCapacityCache.observeDeletedPropertyStatusEvent(event);
    }
}
