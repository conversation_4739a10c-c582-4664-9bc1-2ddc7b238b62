package com.ideas.tetris.pacman.services.analytics.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@NoArgsConstructor
public class BookingCurve {
    private Double processGroupId;
    private Double forecastGroupId;
    private Double roomClassId;
    private Double seasonGroupNumber;
    private Double dowGroupNumber;
    private Double horizonGroupNumber;
    private Double value;
}
