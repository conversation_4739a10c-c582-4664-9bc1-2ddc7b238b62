package com.ideas.tetris.pacman.services.database;

import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.apache.log4j.Logger;
import thirdparty.org.apache.commons.dbutils.QueryRunner;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.Callable;

public class TenantMaintainanceCallable implements Callable {

    private static final Logger LOGGER = Logger.getLogger(TenantMaintainanceCallable.class.getName());

    private Connection connection;
    private QueryRunner runner;
    private String sql;
    private Object[][] params;

    public TenantMaintainanceCallable(Connection connection, QueryRunner runner, String sql, Object[][] params) {
        this.connection = connection;
        this.runner = runner;
        this.sql = sql;
        this.params = params;
    }

    @Override
    public Object call() {
        int[] batch = null;
        try {
            long startTime = System.currentTimeMillis();

            batch = runner.batch(connection, sql, params);

            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;
            LOGGER.info(String.format("Executed %s - %s", sql, totalTime));
        } catch (SQLException e) {
            LOGGER.error(String.format("Error occured while executing - %s", sql));
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, String.format("Error occured while executing - %s : %s", sql, e.getMessage()));
        } finally {
            try {
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                LOGGER.error(String.format("Error occured while closing connection : %s", e.getMessage()));
                throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, String.format("Error occured while closing connection : %s", e.getMessage()));
            }
        }
        return batch;
    }
}
