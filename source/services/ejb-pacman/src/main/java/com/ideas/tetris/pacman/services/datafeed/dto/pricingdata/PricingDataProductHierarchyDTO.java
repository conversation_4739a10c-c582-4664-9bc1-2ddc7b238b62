package com.ideas.tetris.pacman.services.datafeed.dto.pricingdata;

import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductHierarchy;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ProductMinPriceDiff;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class PricingDataProductHierarchyDTO implements Serializable {

    private String productName;
    private String relatedProduct;
    private String minDifferenceMethod;
    private BigDecimal minDifferenceValue;

    public PricingDataProductHierarchyDTO(ProductHierarchy productHierarchy) {
        this.productName = productHierarchy.getFromProduct().getName();
        this.relatedProduct = productHierarchy.getToProduct().getName();
        this.minDifferenceValue = BigDecimal.ZERO;
        List<ProductMinPriceDiff> productMinPriceDiffList = productHierarchy.getProductMinPriceDiffList();
        if (CollectionUtils.isNotEmpty(productMinPriceDiffList)) {
            this.minDifferenceValue = productMinPriceDiffList.get(0).getSundayDiffWithTax();
        }
        this.minDifferenceMethod = AgileRatesOffsetMethod.PERCENTAGE.toString();
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getRelatedProduct() {
        return relatedProduct;
    }

    public void setRelatedProduct(String relatedProduct) {
        this.relatedProduct = relatedProduct;
    }

    public String getMinDifferenceMethod() {
        return minDifferenceMethod;
    }

    public void setMinDifferenceMethod(String minDifferenceMethod) {
        this.minDifferenceMethod = minDifferenceMethod;
    }

    public BigDecimal getMinDifferenceValue() {
        return minDifferenceValue;
    }

    public void setMinDifferenceValue(BigDecimal minDifferenceValue) {
        this.minDifferenceValue = minDifferenceValue;
    }
}
