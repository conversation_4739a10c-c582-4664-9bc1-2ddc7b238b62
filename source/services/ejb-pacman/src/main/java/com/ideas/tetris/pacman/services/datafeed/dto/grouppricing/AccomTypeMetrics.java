package com.ideas.tetris.pacman.services.datafeed.dto.grouppricing;

import static com.ideas.tetris.pacman.services.datafeed.dto.grouppricing.GroupEvaluationData.getStringAt;
import static java.lang.Integer.parseInt;

public class AccomTypeMetrics {
    private int uniqueGroupId;
    private String accomTypeCode;
    private int metrics;

    public int getUniqueGroupId() {
        return uniqueGroupId;
    }

    private void setUniqueGroupId(int uniqueGroupId) {
        this.uniqueGroupId = uniqueGroupId;
    }

    public String getAccomTypeCode() {
        return accomTypeCode;
    }

    private void setAccomTypeCode(String accomTypeCode) {
        this.accomTypeCode = accomTypeCode;
    }

    public int getMetrics() {
        return metrics;
    }

    private void setMetrics(int metrics) {
        this.metrics = metrics;
    }

    public static AccomTypeMetrics mapRow(Object[] row) {
        final AccomTypeMetrics metric = new AccomTypeMetrics();
        metric.setUniqueGroupId(parseInt(getStringAt(row, 0)));
        metric.setAccomTypeCode((getStringAt(row, 1)));
        metric.setMetrics(parseInt(getStringAt(row, 2)));
        return metric;
    }
}
