package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.RoomTypePropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecord;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.jmsclient.TetrisEventManager;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PropertyCapacityChangeService {
    private static final Logger LOGGER = Logger.getLogger(PropertyCapacityChangeService.class);

    @Autowired
	protected TetrisEventManager tetrisEventManager;
    @Autowired
	protected DateService dateService;
    @Autowired
	protected AbstractMultiPropertyCrudService multiPropertyCrudService;
    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;

    public void executeFromJEMS(int propertyId, boolean isHistoryExtract) {
        processCore(propertyId, isHistoryExtract);
    }

    private void processCore(int propertyId, boolean isHistoryExtract) {
        Date caughtUpDate = dateService.getCaughtUpDate();
        try {
            Date today = DateUtil.removeTimeFromDate(caughtUpDate);
            Date yesterday = DateUtil.addDaysToDate(today, -1);
            BigDecimal todaysCapacity = getCapacityForOccupancyDate(propertyId, today);

            if (isHistoryExtract) {
                triggerCapacityChangeEvent(propertyId, todaysCapacity.intValue());
            } else {
                BigDecimal yesterdaysCapacity = getCapacityForOccupancyDate(
                        propertyId, yesterday);
                if (yesterdaysCapacity == null || todaysCapacity.compareTo(yesterdaysCapacity) != 0) {
                    triggerCapacityChangeEvent(propertyId, todaysCapacity.intValue());
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while raising CapacityChange event for propertyId: " + propertyId + ", caughtUpDate: " + caughtUpDate, e);
        }
    }

    private void triggerCapacityChangeEvent(int propertyId, int newCapacity) {
        tetrisEventManager.raiseEvent(tetrisEventManager.buildCapacityChangeEvent(propertyId,
                String.valueOf(newCapacity)));
    }

    public BigDecimal getCapacityForOccupancyDate(int propertyId, Date occupancyDate) {
        TotalActivity totalActivity = (TotalActivity) multiPropertyCrudService
                .findByNamedQuerySingleResultForSingleProperty(propertyId,
                        TotalActivity.BY_OCCUPANCY_DATE_AND_PROPERTY_ID,
                        QueryParameter.with("propertyId", propertyId)
                                .and("occupancyDate", occupancyDate).parameters());
        return totalActivity != null ? totalActivity.getTotalAccomCapacity() : null;
    }

    public void raiseCapacityChangeForAddProperty(int propertyId, String propertyCode, int clientId) {
        try {
            int capacity = getInitialCapacityFromRTRecords(propertyCode, clientId);
            if (capacity > 0) {
                triggerCapacityChangeEvent(propertyId, capacity);
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while raising CapacityChange event for add Property", e);
        }
    }

    @SuppressWarnings("unchecked")
    private int getInitialCapacityFromRTRecords(String propertyCode, int clientId) {
        int initialCapacity = 0;
        List<ConfigurationFileRecord> configurationFileRecords = globalCrudService
                .findByNamedQuery(ConfigurationFileRecord.BY_TYPE_CLIENT_AND_PROPERTY,
                        QueryParameter
                                .with("propertyCode", propertyCode)
                                .and("recordType", PropertyConfigurationRecordType.RT.name())
                                .and("clientId", clientId).parameters());
        RoomTypePropertyConfigurationDto roomTypePropertyConfigurationDto;

        for (ConfigurationFileRecord record : configurationFileRecords) {
            String[] pipedLine = StringUtils.splitPreserveAllTokens(record.getRecord(), PropertyConfigurationDto.DELIMITER);
            roomTypePropertyConfigurationDto = new RoomTypePropertyConfigurationDto();
            roomTypePropertyConfigurationDto.setRecordFields(pipedLine);
            initialCapacity = initialCapacity + roomTypePropertyConfigurationDto.getCapacityAsInt();
        }

        return initialCapacity;
    }
}
