package com.ideas.tetris.pacman.services.purge;

import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;

import org.springframework.aop.SpringProxy;
public interface Purgable extends SpringProxy {
    String getSchema();

    String getTableToDelete();

    String getTableToCompare();

    String getFieldToCompare();

    boolean isFailedSilently();

    default boolean isPropertyIdAppliedToWhereClause() {
        return false;
    }

    default int getBatchSize() {
        return SystemConfig.getStoredProcedureBatchSize();
    }

    default boolean isUseRowLock() {
        return SystemConfig.useStoredProcedureRowLock();
    }
}
