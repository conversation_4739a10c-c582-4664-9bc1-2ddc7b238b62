package com.ideas.tetris.pacman.services.property;

import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.sas.service.SASClientService;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.integration.ratchet.services.RatchetService;
import com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.ClientSrpMapping;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.RecoveryState;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.RecoveryStateTransitionService;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientAttribute;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientPropertyAttributePairing;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ConsolidatedPropertyView;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantProperty;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.database.DBMaintainService;
import com.ideas.tetris.pacman.services.datasourceswitching.DataSourceCacheService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.fds.ups.UPSService;
import com.ideas.tetris.pacman.services.gftservice.service.GftPropertyManagementService;
import com.ideas.tetris.pacman.services.marketsegment.MarketSegmentAttributeSet;
import com.ideas.tetris.pacman.services.marketsegment.MarketSegmentAttributeSet.MarketSegmentBusinessType;
import com.ideas.tetris.pacman.services.marketsegment.MarketSegmentAttributeSet.MarketSegmentForecastActivityType;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentMaster;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingProperty;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecord;
import com.ideas.tetris.pacman.services.property.dto.Property;
import com.ideas.tetris.pacman.services.property.dto.*;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.reports.ReportService;
import com.ideas.tetris.pacman.services.sas.entity.MarkDateDataType;
import com.ideas.tetris.pacman.services.sas.entity.MarkPropertyDate;
import com.ideas.tetris.pacman.services.sas.entity.ProcessGroup;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.UserAuthorizedPropertyCache;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.updateddlseed.UpdateDDLSeedServiceUtilityBean;
import com.ideas.tetris.pacman.services.useractivity.UserActivityService;
import com.ideas.tetris.pacman.services.virtualproperty.service.VirtualPropertyMappingSeedDataService;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameter;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterPredefinedValue;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterPredefinedValueType;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterValue;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.event.TetrisEvent;
import com.ideas.tetris.platform.common.event.property.PropertyStatusChangedEventManager;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.*;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import com.ideas.tetris.platform.services.jmsclient.TetrisEventManager;
import com.ideas.tetris.platform.services.sas.log.SASNodeLocator;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.DELETE_FILE_FORCELY;
import static com.ideas.tetris.pacman.common.constants.Constants.DELETE_PATH;
import static com.ideas.tetris.pacman.util.Runner.runIfTrue;
import static java.util.stream.Collectors.toMap;

@SuppressWarnings({"squid:S1200"})
@Component
@Transactional
public class PropertyRolloutService {
    private static final Logger LOGGER = Logger.getLogger(PropertyRolloutService.class.getName());
    static final String DESCRIPTION_STAGE_CHANGE = "Manual change through UI (Installation Status)";
    public static final String FORECAST_OPTIMIZATION_WINDOW_BDE_FOR_PCRS = "358";
    public static final int UNKNOWN_PROPERTY_ID = -1;
    private static final String PROPERTY_ID = "propertyId";
    public static final String PROPERTY_IDS_FROM_CODES = "select Property_ID from Property where Property_Code in (:propertyCodes)";

    @TenantCrudServiceBean.Qualifier
    @Qualifier("tenantCrudServiceBean")
    @Autowired
    protected CrudService tenantCrudService;
    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;
    @Autowired
    protected PacmanConfigParamsService configService;
    @Autowired
    protected GftPropertyManagementService gftPropertyManagementService;
    @Autowired
    protected UpdateDDLSeedServiceUtilityBean updateDDLSeedService;
    @Autowired
    protected DBMaintainService dbMaintainService;
    @Autowired
    protected UserService userService;
    @Autowired
    protected PropertyService propertyService;
    @Autowired
    protected PropertyExcludeDatesService propertyExcludeDatesService;
    @Autowired
    protected PropertyRolloutPropertyFinder propertyRolloutPropertyFinder;
    @Autowired
    protected ExtractDetailsServiceLocal extractDetailsService;
    @Autowired
    protected TetrisEventManager tetrisEventManager;
    @Autowired
    protected UserActivityService userActivityService;
    @Autowired
    protected PropertyGroupService propertyGroupService;
    @Autowired
    protected RatchetService ratchetService;
    @Autowired
    protected AuthorizationService authorizationService;
    @Autowired
    protected PropertyStageChangeService propertyStageChangeService;
    @Autowired
    protected PropertyCapacityChangeService propertyCapacityChangeService;
    @Autowired
    protected ReportService reportService;
    @Autowired
    protected SASNodeLocator sasNodeLocator;
    @Autowired
    protected PropertyStatusChangedEventManager propertyStatusChangedEventManager;
    @Autowired
    private DataSourceCacheService dataSourceCacheService;
    @Autowired
    private ClientPropertyCacheService clientPropertyCacheService;
    @Autowired
    private UserAuthorizedPropertyCache userAuthorizedPropertyCache;
    @Autowired
    private RecoveryStateTransitionService recoveryStateTransitionService;
    @Autowired
    protected UPSService upsService;

    @Autowired
    private VirtualPropertyMappingSeedDataService virtualPropertyMappingSeedDataService;
    @Autowired
    protected SASClientService sasClientService;

    @Autowired
    private ApplicationEventPublisher publisher;

    private static final String CONFIG_PARAM_DATE_FORMAT = "MM/dd/yyyy";
    public static final String UNASSIGNED_ACCOM_CLASS = "Unassigned";
    public static final String UNASSIGNED_FILE_METATDATA = "UNASSIGNED";
    public static final String UNASSIGNED_FILE_LOCATION = "DUMMY_FILE_LOCATION";
    public static final String UNASSIGNED_RATE_CURRENCY = "USD";
    public static final String UNASSIGNED_RATE_NAME = "None";
    public static final String UNASSIGNED_RATE_DESCRIPTION = "No Rate Plan";
    public static final Integer UNASSIGNED_RECORD_TYPE = 1;
    public static final Integer UNASSIGNED_PROCESS_STATUS = 13;
    protected static final String PAST_DAYS_VALUE = "365";
    protected static final String FUTURE_DAYS_VALUE = "365";
    protected static final Integer DEFAULT_PROCESS_GROUP_ID = Integer.valueOf(-1);
    protected static final String UPLOADED_PROPERTIES_KEY = "Uploaded";

    public List<Property> addProperties(List<Property> properties) {
        if (properties != null) {
            for (Property property : properties) {
                addProperty(property);
            }
        }
        return properties;
    }

    // this method can be called in a couple contexts; either when adding a
    // property for
    // the first time, or re-adding it after a rollback
    Property addProperty(Property property) {
        String externalSystem = configService.getParameterValueByClientLevel(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value());
        ReservationSystem reservationSystem = ReservationSystem.valueForConfigParameterValue(externalSystem);
        String clientCode = getClientCode();
        boolean hiltonClientCode = isHiltonClientCode();
        long start = System.currentTimeMillis();
        com.ideas.tetris.platform.services.daoandentities.entity.Property globalProperty = getExistingGlobalProperty(
                property.getCode(), false);

        boolean propertyRollback = false;
        if (globalProperty != null) {
            if (globalProperty.isActive()) {
                throw new TetrisException(ErrorCode.DUPLICATE_DATA, "Active Property with code " + property.getCode()
                                                                            + " already exists. Add property failed", null);
            } else {
                propertyRollback = true;
            }
        }

        try {
            // For Opera we cannot create a ratchet Dataset
            // we also need to set the externalsystem to be opera, which
            // is not an available option in the config file
            if (reservationSystem.getIsRatchet()) {
                globalProperty = addPropertySharedStepOne(property, clientCode, globalProperty, hiltonClientCode);
                addPropertyToSasFileLoc(property);
                ratchetService.createProperty(clientCode, property.getCode(), property.getName());
                populateMarketSegmentMaster(clientCode);
                if (property.getExternalSystem() == null) {
                    property.setExternalSystem(reservationSystem.getConfigParameterValue());
                }
                addPropertySharedStepTwo(property, clientCode, globalProperty, start);
            } else {
                globalProperty = addPropertySharedStepOne(property, clientCode, globalProperty, hiltonClientCode);
                property.setExternalSystem(ReservationSystem.OPERA.getConfigParameterValue());
                addPropertyToSasFileLoc(property);
                addPropertySharedStepTwo(property, clientCode, globalProperty, start);
            }
        } catch (Exception e) {
            LOGGER.error("Error occured adding property", e);
            if (!propertyRollback && globalProperty != null && globalProperty.getDbLocId() != null) {
                // we make our best effort here to clean up the tenant db
                // otherwise it will be orphaned
                try {
                    DBLoc dbLoc = dataSourceCacheService.getDBLoc(globalProperty.getId());
                    dbMaintainService.dropDatabase(dbLoc);
                } catch (Exception dropDatabaseException) {
                    LOGGER.error(
                            "Error occured trying to clean up orphaned database for property " + globalProperty.getId(),
                            dropDatabaseException);
                }
            }
            throw new TetrisException(ErrorCode.ADD_PROPERTY_FAILED, "Error occured adding property", e);
        }

        return property;
    }

    private void populateMarketSegmentMaster(String clientCode) {
        Collection<ClientSrpMapping> amsList = ratchetService.getAnalyticMarketSegments(clientCode);
        for (ClientSrpMapping ams : amsList) {
            MarketSegmentMaster master = buildMarketSegmentMaster(ams);
            tenantCrudService.save(master);
        }

    }

    @SuppressWarnings("squid:S3776")
    private MarketSegmentMaster buildMarketSegmentMaster(ClientSrpMapping mapping) {
        MarketSegmentMaster marketSegmentMaster = new MarketSegmentMaster();
        marketSegmentMaster.setCode(mapping.getAnalyticMarketSegmentCode());
        marketSegmentMaster.setDescription(mapping.getAnalyticMarketSegmentDescription());
        marketSegmentMaster.setName(mapping.getAnalyticMarketSegmentCode());
        if (mapping.getAnalyticMarketSegmentCode().endsWith("_DEFAULT")
                    || mapping.getAnalyticMarketSegmentCode().endsWith("_DEF")) {
            marketSegmentMaster.setIsEditable(1);
            marketSegmentMaster.setPriceByBar(null);
        } else {
            marketSegmentMaster.setBookingBlockPc("Y".equalsIgnoreCase(mapping.getBlock()) ? 100 : 0);
            marketSegmentMaster.setFenced("Y".equalsIgnoreCase(mapping.getFenced()) ? 1 : 0);
            marketSegmentMaster.setLink("Y".equalsIgnoreCase(mapping.getLinked()) ? 1 : 0);
            marketSegmentMaster.setPackageValue("Y".equalsIgnoreCase(mapping.getPackageValue()) ? 1 : 0);
            marketSegmentMaster.setPriceByBar(mapping.isPriceByBar() ? 1 : 0);
            marketSegmentMaster.setQualified("Y".equalsIgnoreCase(mapping.getQualified()) ? 1 : 0);
            MarketSegmentAttributeSet.MarketSegmentYieldType yieldType = getYieldType(mapping.getYieldable());
            if (yieldType != null) {
                marketSegmentMaster.setYieldTypeId(yieldType.getId());
            }
            marketSegmentMaster.setForecastActivityTypeId(MarketSegmentForecastActivityType.DEMAND_AND_WASH.getId());
            marketSegmentMaster
                    .setBusinessTypeId("G".equalsIgnoreCase(mapping.getBusinessType()) ? MarketSegmentBusinessType.GROUP
                                                                                                 .getId() : MarketSegmentBusinessType.TRANSIENT.getId());
            marketSegmentMaster.setIsEditable(0);
        }
        return marketSegmentMaster;
    }

    private MarketSegmentAttributeSet.MarketSegmentYieldType getYieldType(String yieldable) {
        if ("Y".equalsIgnoreCase(yieldable)) {
            return MarketSegmentAttributeSet.MarketSegmentYieldType.YIELDABLE;
        } else if ("S".equalsIgnoreCase(yieldable)) {
            return MarketSegmentAttributeSet.MarketSegmentYieldType.SEMIYIELDABLE;
        } else if ("N".equalsIgnoreCase(yieldable)) {
            return MarketSegmentAttributeSet.MarketSegmentYieldType.NONYIELDABLE;
        } else {
            return null;
        }
    }

    private void addPropertySharedStepTwo(Property property, String clientCode,
                                          com.ideas.tetris.platform.services.daoandentities.entity.Property globalProperty, long start) {
        addGlobalParameters(property);
        updateDDLSeedService.populateAnalyticsDataSetWithSeedData(globalProperty);

        gftPropertyManagementService.addProperty(property);

        FileMetadata fileMetadata = addUnassignedFileMetadata(property);
        addUnassignedRateUnqualified(property, fileMetadata.getId());
        addUnassignedAccomClass(property);
        addOverbookingProperty(property);
        addProcessGroup(property);

        userService.createUsersForNewProperty();

        globalProperty.setStatus(Status.ACTIVE);
        globalCrudService.save(globalProperty);

        propertyStageChangeService.setInitialStage(globalProperty, Stage.DATA_CAPTURE,
                "stage initialized to DataCapture");
        propertyCapacityChangeService.raiseCapacityChangeForAddProperty(globalProperty.getId(), property.getCode(),
                globalProperty.getClient().getId());

        extractDetailsService.rebuildExtractDetails(globalProperty.getId(), clientCode, globalProperty.getCode());
        extractDetailsService.rebuildWebRateExtractDetails(property.getId(), clientCode, globalProperty.getCode());
        LOGGER.info("Adding property took " + (System.currentTimeMillis() - start) + " ms");

        cacheNewProperty(property);
    }

    private com.ideas.tetris.platform.services.daoandentities.entity.Property addPropertySharedStepOne(
            Property property, String clientCode,
            com.ideas.tetris.platform.services.daoandentities.entity.Property globalProperty, boolean isHiltonClientCode) {
        if (globalProperty != null && globalProperty.isActive()) {
            throw new TetrisException(ErrorCode.DUPLICATE_DATA, "Active Property with code " + property.getCode()
                                                                        + " already exists. Add property failed", null);
        }

        if (globalProperty == null) {
            globalProperty = addPropertyToGlobalDatabase(property);
        }
        globalProperty.setStage(Stage.DATA_CAPTURE);

        property.setId(globalProperty.getId());

        PacmanWorkContextHelper.setPropertyId(globalProperty.getId());
        PacmanWorkContextHelper.setPropertyCode(globalProperty.getCode());

        DBLoc existingDBLoc = null;
        if (null != globalProperty.getDbLocId()) {
            existingDBLoc = dataSourceCacheService.getDBLoc(globalProperty.getId());
        }

        if (existingDBLoc == null) {
            DBLoc dbLoc = dbMaintainService.createDBLoc(globalProperty.getId());
            LOGGER.info("#ExtraLogsForDBLocID : PropertyRolloutService - Logging DBLocId : " + dbLoc.getId() + " from global property for property: " + globalProperty.getId());
            globalProperty.setDbLocId(dbLoc.getId());
            globalCrudService.save(globalProperty);
            globalCrudService.getEntityManager().flush();

            dbMaintainService.createDatabase(globalProperty.getId(), dbLoc);
        }

        addPropertyToTenantDatabase(property, clientCode);
        // only do this for non-hilton properties
        // this hopefully is temporary until it is decide how
        // MarketSegmentMaster will be managed for all clients
        if (!isHiltonClientCode) {
            cleanUpMarketSegmentMaster();
        }

        return globalProperty;
    }

    public void cacheNewProperty(Integer propertyId) {
        Property property = new Property();
        property.setId(propertyId);
        cacheNewProperty(property);
    }

    void cacheNewProperty(Property property) {
        Client client = authorizationService.getClient(PacmanWorkContextHelper.getClientId());
        if (client != null) {

            // Check to see if the cache has the client/property - if it doesn't we need to remove the client from the
            // user authorized property cache
            boolean hasClientAndProperty = clientPropertyCacheService.hasClientAndProperty(client.getCode(), property.getCode());

            // Reload the property in the client/property cache
            clientPropertyCacheService.reloadProperty(property.getId());

            // if this is being called when re-adding a property after a
            // rollback, it already exists in the cache
            if (!hasClientAndProperty) {
                userAuthorizedPropertyCache.remove(client.getId(), Integer.valueOf(PacmanWorkContextHelper.getUserId()));
            }
        } else {
            LOGGER.warn("Unable to cache property " + property.getId() + " because unable to get client "
                                + PacmanWorkContextHelper.getClientId() + " from AuthorizationService");
        }
    }

    public void updateProperties(List<Property> properties) {
        if (properties != null) {
            for (Property property : properties) {
                updateProperty(property);
            }
        }
    }

    private void updateProperty(Property property) {
        if (property == null || property.getId() == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Cannot update Property with null ID", null);
        }
        com.ideas.tetris.platform.services.daoandentities.entity.Property globalProperty = globalCrudService.find(
                com.ideas.tetris.platform.services.daoandentities.entity.Property.class, property.getId());
        if (globalProperty == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Cannot update non-existant property having ID = "
                                                                       + property.getId(), null);
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat(CONFIG_PARAM_DATE_FORMAT);
        SimpleDateFormat scheduledDecisionDeliveryDateFormat = new SimpleDateFormat(Constants.GLOBAL_DATE_PARAM_FORMAT);

        String dateString = null;
        if (property.getScheduledTwoWayDate() != null) {
            dateString = scheduledDecisionDeliveryDateFormat.format(property.getScheduledTwoWayDate().getTime());
        }
        String paramContext = getConfigParamsContext(property.getCode());
        addConfigParameterValue(IntegrationConfigParamName.CORE_PROPERTY_SCHEDULED_TWO_WAY_DATE.value(), dateString, paramContext, true);
        dateString = null;
        if (property.getSrpAttributionSubmittedDate() != null) {
            dateString = dateFormat.format(property.getSrpAttributionSubmittedDate().getTime());
        }
        addConfigParameterValue(IntegrationConfigParamName.CORE_PROPERTY_SRP_ATTRIBUTION_SUBMITTED_DATE.value(), dateString, paramContext, true);
        dateString = null;
        if (property.getSrpAttributionExtractDate() != null) {
            dateString = dateFormat.format(property.getSrpAttributionExtractDate().getTime());
            ExtractDetails extractDetails = extractDetailsService.getExtractDetailsWithFilePaths(property.getId());
            extractDetails.setSrpAttributeExtractDateAvailability(property.getSrpAttributionExtractDate().getTime());
            extractDetailsService.putExtractDetails(property.getId(), extractDetails);
        }
        addConfigParameterValue(IntegrationConfigParamName.CORE_PROPERTY_SCHEDULED_EXTRACT_DATE.value(), dateString, paramContext, true);
    }

    @SuppressWarnings({"squid:S2143"})
    private FileMetadata addUnassignedFileMetadata(Property property) {
        FileMetadata entity = new FileMetadata();
        entity.setRecordTypeId(UNASSIGNED_RECORD_TYPE);
        entity.setBde(0);
        entity.setFileName(UNASSIGNED_FILE_METATDATA);
        entity.setFileLocation(UNASSIGNED_FILE_LOCATION);
        entity.setTenantPropertyId(property.getId());
        Date now = new Date();
        entity.setSnapshotDt(now);
        entity.setSnapshotTm(now);
        entity.setPreparedDt(now);
        entity.setPreparedTm(now);
        entity.setProcessStatusId(UNASSIGNED_PROCESS_STATUS);
        return tenantCrudService.save(entity);
    }

    @SuppressWarnings({"squid:S2143"})
    private RateUnqualified addUnassignedRateUnqualified(Property property, Integer fileMetadataId) {
        RateUnqualified entity = new RateUnqualified();
        entity.setFileMetadataId(fileMetadataId);
        entity.setName(UNASSIGNED_RATE_NAME);
        entity.setDescription(UNASSIGNED_RATE_DESCRIPTION);
        entity.setStatusId(Constants.ACTIVE_STATUS_ID);
        entity.setPropertyId(property.getId());
        entity.setSystemDefault(1);
        Date now = new Date();
        entity.setStartDate(now);
        entity.setEndDate(now);
        entity.setYieldable(1);
        entity.setPriceRelative(0);
        entity.setDerivedRateCode("0");
        entity.setCurrency(UNASSIGNED_RATE_CURRENCY);
        entity.setIncludesPackage(0);
        entity.setRanking(0);
        return tenantCrudService.save(entity);
    }

    private AccomClass addUnassignedAccomClass(Property property) {
        AccomClass entity = new AccomClass();
        entity.setCode(UNASSIGNED_ACCOM_CLASS);
        entity.setName(UNASSIGNED_ACCOM_CLASS);
        entity.setDescription(UNASSIGNED_ACCOM_CLASS);
        entity.setStatusId(Constants.ACTIVE_STATUS_ID);
        entity.setPropertyId(property.getId());
        entity.setSystemDefault(1);
        entity.setMasterClass(0);
        entity.setCreatedByUserId(Constants.SYSTEM_USER_ID);
        entity.setLastUpdatedByUserId(Constants.SYSTEM_USER_ID);
        return tenantCrudService.save(entity);
    }

    private OverbookingProperty addOverbookingProperty(Property property) {
        OverbookingProperty overbookingProperty = new OverbookingProperty();
        overbookingProperty.setPropertyId(property.getId());
        overbookingProperty.setSundayCeiling(Constants.OVERBOOKING_NO_LIMIT);
        overbookingProperty.setMondayCeiling(Constants.OVERBOOKING_NO_LIMIT);
        overbookingProperty.setTuesdayCeiling(Constants.OVERBOOKING_NO_LIMIT);
        overbookingProperty.setWednesdayCeiling(Constants.OVERBOOKING_NO_LIMIT);
        overbookingProperty.setThursdayCeiling(Constants.OVERBOOKING_NO_LIMIT);
        overbookingProperty.setFridayCeiling(Constants.OVERBOOKING_NO_LIMIT);
        overbookingProperty.setSaturdayCeiling(Constants.OVERBOOKING_NO_LIMIT);
        overbookingProperty.setCreatedByUserId(Constants.SYSTEM_USER_ID);
        return tenantCrudService.save(overbookingProperty);
    }

    private ProcessGroup addProcessGroup(Property property) {
        ProcessGroup processGroup = new ProcessGroup();
        processGroup.setPropertyId(property.getId());
        processGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        processGroup.setProcessGroupId(DEFAULT_PROCESS_GROUP_ID);
        return tenantCrudService.save(processGroup);
    }

    private void cleanUpMarketSegmentMaster() {
        tenantCrudService.executeUpdateByNamedQuery(MarketSegmentMaster.DELETE_ALL);
    }

    private SASFileLoc addPropertyToSasFileLoc(Property property) {
        SASFileLoc existingEntity = getExistingSasFileLocation(property.getId());
        if (existingEntity != null) {
            return existingEntity;
        }
        SASFileLoc entity = new SASFileLoc();
        entity.setAnalyticsDataSetPath(SystemConfig.getSasFileLoc() + property.getId());
        entity.setPropertyId(property.getId());
        entity.setSasServerName(sasNodeLocator.determineDefaultSASNodeForAddProperty());
        entity.setStatusId(Constants.ACTIVE_STATUS_ID);
        entity = globalCrudService.save(entity);
        return entity;
    }

    private SASFileLoc getExistingSasFileLocation(Integer propertyId) {
        return (SASFileLoc) globalCrudService.findByNamedQuerySingleResult(SASFileLoc.BY_PROPERTY_ID, QueryParameter
                                                                                                              .with(PROPERTY_ID, propertyId).parameters());
    }

    @SuppressWarnings("unchecked")
    private TenantProperty getExistingTenantProperty(String propertyCode) {
        List<TenantProperty> tenantProperties = tenantCrudService.findByNamedQuery(
                TenantProperty.BY_CODE, QueryParameter.with("code", propertyCode).parameters());
        return (tenantProperties == null || tenantProperties.isEmpty()) ? null : tenantProperties.get(0);
    }

    private com.ideas.tetris.platform.services.daoandentities.entity.Property getExistingGlobalProperty(
            String propertyCode, boolean activeOnly) {
        String query = activeOnly ? com.ideas.tetris.platform.services.daoandentities.entity.Property.BY_CLIENT_ID_PROPERTY_CODE_ACTIVE
                               : com.ideas.tetris.platform.services.daoandentities.entity.Property.BY_CLIENT_ID_PROPERTY_CODE;
        QueryParameter queryParameter = QueryParameter.with(
                        com.ideas.tetris.platform.services.daoandentities.entity.Property.PARAM_PROPERTY_CODE, propertyCode)
                                                .and(com.ideas.tetris.platform.services.daoandentities.entity.Property.PARAM_CLIENT_ID, getClientId());
        return globalCrudService
                       .findByNamedQuerySingleResult(query, queryParameter.parameters());
    }

    public void addParameterValue(String propertyCode, String parameterName, String parameterValue,
                                  boolean updateIfExists) {
        String context = getConfigParamsContext(propertyCode);
        addConfigParameterValue(parameterName, parameterValue, context, updateIfExists);
    }

    public void addParameterValues(String propertyCode,
                                   List<com.ideas.tetris.pacman.services.property.dto.ParameterValue> parameterValues) {
        for (com.ideas.tetris.pacman.services.property.dto.ParameterValue parameterValueDto : parameterValues) {
            addParameterValue(propertyCode, parameterValueDto.getName(), parameterValueDto.getValue(),
                    parameterValueDto.isUpdateIfExists());
        }
    }

    private void addGlobalParameters(Property property) {
        ReservationSystem reservationSystem = ReservationSystem.valueForConfigParameterValue(property
                                                                                                     .getExternalSystem());
        String context = getConfigParamsContext(property.getCode());

        // set these for every property
        addConfigParameterValue(IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value(), property.getPropertyTimeZone(), context, true);
        addConfigParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value(), property.getWebRateAlias(), context, true);

        // Default all SyncEvent parameters to false
        for (SyncEvent syncEvent : SyncEvent.values()) {
            addConfigParameterValue(syncEvent.getGlobalParameter(), "false", context, true);
        }

        // set these for non-opera properties
        if (!ReservationSystem.OPERA.equals(reservationSystem)) {
            addConfigParameterValue(IntegrationConfigParamName.CORE_PROPERTY_CONFIGURATION_COMPLETE.value(), "false", context, true);
            // only set these for Hilton
            if (ReservationSystem.HILSTAR.equals(reservationSystem) || ReservationSystem.PCRS.equals(reservationSystem)) {
                addConfigParameterValue(IntegrationConfigParamName.RECEIVING_SYSTEMS.value(), property.getExternalSystem(), context, true);
                addConfigParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value(), property.getExternalSystem(), context,
                        true);
                addConfigParameterValue(IntegrationConfigParamName.PAST_DAYS.value(Constants.RATCHET), PAST_DAYS_VALUE, context, true);
                addConfigParameterValue(IntegrationConfigParamName.FUTURE_DAYS.value(), FUTURE_DAYS_VALUE, context, true);

            }

            if (ReservationSystem.PCRS.equals(reservationSystem)) {
                addConfigParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value(), FORECAST_OPTIMIZATION_WINDOW_BDE_FOR_PCRS,
                        context, true);
                addConfigParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value(), FORECAST_OPTIMIZATION_WINDOW_BDE_FOR_PCRS,
                        context, true);
                addConfigParameterValue(IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value(), FORECAST_OPTIMIZATION_WINDOW_BDE_FOR_PCRS,
                        context, true);
            }
        }
    }

    private String getConfigParamsContext(String propertyCode) {
        return Constants.CONFIG_PARAMS_NODE_PREFIX + "." + getClientCode() + "." + propertyCode;
    }

    private void addConfigParameterValue(String parameterName, String value, String context, boolean updateIfExists) {
        if (parameterName == null) {
            return;
        }
        String existingValue = configService.getValue(context, parameterName, true);
        if (existingValue != null && !updateIfExists) {
            return;
        }
        configService.addParameterValue(context, parameterName, value);
    }

    private com.ideas.tetris.platform.services.daoandentities.entity.Property addPropertyToGlobalDatabase(
            Property property) {
        com.ideas.tetris.platform.services.daoandentities.entity.Property globalProperty = new com.ideas.tetris.platform.services.daoandentities.entity.Property();
        globalProperty.setCode(property.getCode());
        globalProperty.setName(property.getName());
        globalProperty.setClient(globalCrudService.find(Client.class, getClientId()));
        globalProperty.setStatus(Status.INACTIVE);
        globalProperty.setEstimatedCapacity(0);
        return globalCrudService.save(globalProperty);
    }

    private TenantProperty addPropertyToTenantDatabase(Property property, String clientCode) {
        String propertyCode = property.getCode();
        String propertyName = property.getName();
        Integer propertyId = property.getId();
        return addPropertyToTenantDatabase(propertyId, propertyCode, propertyName, clientCode);
    }

    private TenantProperty addPropertyToTenantDatabase(Integer propertyId, String propertyCode, String propertyName, String clientCode) {
        TenantProperty existingProperty = getExistingTenantProperty(propertyCode);
        if (existingProperty != null) {
            return existingProperty;
        }
        TenantProperty entity = new TenantProperty();
        entity.setCode(propertyCode);
        entity.setName(propertyName);
        entity.setStatus(Status.ACTIVE);
        entity.setId(propertyId);
        entity.setClientCode(clientCode);
        entity = tenantCrudService.save(entity);
        return entity;
    }

    public List<String> findDBServerName(PropertySearchCriteria searchCriteria) {
        List<Property> pList = propertyRolloutPropertyFinder.findProperties(searchCriteria);

        if (pList.isEmpty()) {
            return new ArrayList<>();
        }
        List<Integer> pid = pList.stream().map(Property::getId).collect(Collectors.toList());
        List<DBLoc> locs = globalCrudService.findByNamedQuery(DBLoc.BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, pid).parameters());

        return locs.stream().map(DBLoc::getServerName).collect(Collectors.toList());
    }

    public List<String> findJNDIName(PropertySearchCriteria searchCriteria) {
        List<Property> pList = propertyRolloutPropertyFinder.findProperties(searchCriteria);

        if (pList.isEmpty()) {
            return new ArrayList<>();
        }
        List<Integer> pid = pList.stream().map(Property::getId).collect(Collectors.toList());
        List<DBLoc> locs = globalCrudService.findByNamedQuery(DBLoc.BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, pid).parameters());

        return locs.stream().map(DBLoc::getJndiName).collect(Collectors.toList());
    }

    public List<String> findJNDINameForReports(PropertySearchCriteria searchCriteria) {
        List<Property> pList = propertyRolloutPropertyFinder.findProperties(searchCriteria);

        if (pList.isEmpty()) {
            return new ArrayList<>();
        }
        List<Integer> pid = pList.stream().map(Property::getId).collect(Collectors.toList());
        List<DBLoc> locs = globalCrudService.findByNamedQuery(DBLoc.BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, pid).parameters());

        return locs.stream().map(DBLoc::getJndiNameForReports).collect(Collectors.toList());
    }

    public List<String> findSASServerName(PropertySearchCriteria searchCriteria) {
        List<Property> pList = propertyRolloutPropertyFinder.findProperties(searchCriteria);

        if (pList.isEmpty()) {
            return new ArrayList<>();
        }
        List<Integer> pid = pList.stream().map(Property::getId).collect(Collectors.toList());
        List<SASFileLoc> locs = globalCrudService.findByNamedQuery(SASFileLoc.BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, pid).parameters());

        return locs.stream().map(SASFileLoc::getSasServerName).collect(Collectors.toList());
    }

    public List<Property> findProperties(PropertySearchCriteria searchCriteria) {
        return propertyRolloutPropertyFinder.findProperties(searchCriteria);
    }

    @Transactional(propagation = Propagation.REQUIRED, timeout = 15 * 60)
    @Justification("Since there are a huge number of records being processed, we need to ensure that there are no transaction timeouts.")
    public List<Property> findPropertiesWithCustomTransactionSettings(PropertySearchCriteria searchCriteria) {
        return propertyRolloutPropertyFinder.findProperties(searchCriteria);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void inactivateProperty(Property property) {
        if (property != null && property.getId() != null) {
            com.ideas.tetris.platform.services.daoandentities.entity.Property globalProperty = globalCrudService.find(
                    com.ideas.tetris.platform.services.daoandentities.entity.Property.class, property.getId());
            if (globalProperty == null) {
                LOGGER.error("Unable to inactivate global property " + property.getCode() + " with id "
                                     + property.getId() + " : property was not found");
                return;
            }
            globalProperty.setStatus(Status.INACTIVE);
            globalCrudService.save(globalProperty);
        }
    }

    public void deleteProperty(Integer propertyId, String sfdcCaseNumber) {
        if (propertyId == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Cannot delete Property with null ID");
        }

        com.ideas.tetris.platform.services.daoandentities.entity.Property globalProperty = globalCrudService.find(
                com.ideas.tetris.platform.services.daoandentities.entity.Property.class, propertyId);
        if (globalProperty == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Cannot delete non-existant property having ID = "
                                                                       + propertyId);
        }

        deleteProperty(globalProperty.getId(), globalProperty.getCode(), sfdcCaseNumber);
    }

    private void deleteProperty(Integer propertyId, String propertyCode, String sfdcCaseNumber) {
        String userId = getUserId();
        String clientCode = getClientCode();

        // Make sure work context is set for down stream service calls.
        PacmanWorkContextHelper.setPropertyId(propertyId);
        PacmanWorkContextHelper.setPropertyCode(propertyCode);

        try {
            if (!propertyId.equals(UNKNOWN_PROPERTY_ID)) {
                LOGGER.info("Cleaning property " + propertyId);
                cleanProperty(propertyId, propertyCode, false);

                LOGGER.info("Deleting user activity for property " + propertyId);
                userActivityService.deleteActivityForProperty(propertyId);

                LOGGER.info("Dropping database for property " + propertyId);
                dbMaintainService.dropDatabase(propertyId);

                LOGGER.info("Deleting global property for property " + propertyId);
                globalCrudService.delete(com.ideas.tetris.platform.services.daoandentities.entity.Property.class,
                        propertyId);

                LOGGER.info("Deleting files for property " + propertyId);
                extractDetailsService.deleteExtracts(clientCode, propertyCode);
            }

            LOGGER.info("Deleting configuration records for property " + propertyCode);
            deleteConfigurationRecords(propertyCode);

            TetrisEvent te = tetrisEventManager.buildDeletePropertyEvent(propertyId, propertyCode, clientCode, userId,
                    sfdcCaseNumber);
            tetrisEventManager.raiseEvent(te);

            // Send event to indicate that the property was deleted
            propertyStatusChangedEventManager.firePropertyDeletedEvent(propertyId, propertyCode);
        } catch (TetrisException e) {
            throw (TetrisException) e;
        } catch (Exception e) {
            String errorMsg = String.format("An error occured deleting property %s : %s", propertyCode, e.getMessage());
            throw new TetrisException(ErrorCode.SERVICE_ERROR, errorMsg, e);
        } finally {
            removePropertyFromCache(propertyId);
        }
    }

    void removePropertyFromCache(Integer propertyId) {
        Client client = authorizationService.getClient(PacmanWorkContextHelper.getClientId());
        clientPropertyCacheService.removePropertyFromCache(client, propertyId);
    }

    public void cleanProperty(Property property) {
        Integer propertyId = property != null ? property.getId() : null;

        if (propertyId == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Cannot clean Property with null ID");
        }

        com.ideas.tetris.platform.services.daoandentities.entity.Property globalProperty = globalCrudService.find(
                com.ideas.tetris.platform.services.daoandentities.entity.Property.class, propertyId);
        if (globalProperty == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Cannot clean non-existant property having ID = "
                                                                       + propertyId);
        }

        String propertyCode = globalProperty.getCode();

        // Make sure work context is set for down stream service calls.
        PacmanWorkContextHelper.setPropertyId(propertyId);
        PacmanWorkContextHelper.setPropertyCode(propertyCode);

        cleanAndRecreateProperty(propertyId, propertyCode, true);
    }

    private void cleanAndRecreateProperty(Integer propertyId, String propertyCode, boolean isRollback) {
        long start = System.currentTimeMillis();
        try {
            cleanProperty(propertyId, propertyCode, isRollback);
            dbMaintainService.recreateDatabase(propertyId);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "An error occurred trying to clean property "
                                                                       + propertyCode + " : " + e.getMessage(), e);
        }
        LOGGER.info("Cleaning property took " + (System.currentTimeMillis() - start) + " ms");
    }

    // This method is currently used for Opera rebuilding of a property. It
    // offers a more minimal cleanup prior to rebuilding.
    public void cleanProperty() {
        long start = System.currentTimeMillis();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        String propertyCode = PacmanWorkContextHelper.getPropertyCode();

        TenantProperty tenantProperty = tenantCrudService.find(TenantProperty.class, propertyId);
        String propertyName = tenantProperty.getName();

        tenantCrudService.getEntityManager().clear();

        if (propertyId == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Cannot clean Property with null ID");
        }

        try {
            // Remove SAS dataset
            deleteAnalyticsDataSetDirectory(propertyId);
            removePropertyFromSasFileLoc(propertyId);

            // Rebuild tenant database
            dbMaintainService.recreateDatabase(propertyId);
            createOperaProperty(propertyId, propertyCode, propertyName, tenantProperty.getClientCode());
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "An error occured trying to clean property "
                                                                       + propertyCode + " : " + e.getMessage(), e);
        }
        LOGGER.info("Cleaning property took " + (System.currentTimeMillis() - start) + " ms");
    }

    private void createOperaProperty(Integer propertyId, String propertyCode, String propertyName, String clientCode) {
        addPropertyToTenantDatabase(propertyId, propertyCode, propertyName, clientCode);
        Property property = new Property();
        property.setId(propertyId);
        property.setCode(propertyCode);
        property.setName(propertyName);
        FileMetadata fileMetadata = addUnassignedFileMetadata(property);
        addUnassignedRateUnqualified(property, fileMetadata.getId());
        addUnassignedAccomClass(property);
        addOverbookingProperty(property);
        addProcessGroup(property);
        userService.createUsersForNewProperty();

        // Recreate SAS dataset
        addPropertyToSasFileLoc(property);
        updateDDLSeedService.populateAnalyticsDataSetWithSeedData(getExistingGlobalProperty(propertyCode, false));
    }

    @SuppressWarnings({"squid:RedundantThrowsDeclarationCheck"})
    private void cleanProperty(Integer propertyId, String propertyCode, boolean isRollback) throws IOException {
        String clientCode = getClientCode();
        String externalSystem = configService.getParameterValueByClientLevel(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value());
        ReservationSystem reservationSystem = ReservationSystem.valueForConfigParameterValue(externalSystem);
        if (reservationSystem.getIsRatchet()) {
            ratchetService.deleteProperty(clientCode, propertyCode);
        }

        deleteAnalyticsDataSetDirectory(propertyId);

        if (!isRollback) {
            removePropertyFromSasFileLoc(propertyId);
        }

        // Cleaning up mappings made with property
        propertyGroupService.removePropertyFromPropertyGroups(propertyId);
        authorizationService.deletePropertyFromAuthorizationGroups(propertyId);
        authorizationService.deletePropertyFromPermissions(propertyId, clientCode);

        // get empty auth groups
        // clean those all from User_Auth_Group_Role
        // clean also from LDAP
        authorizationService.cleanUpEmptyAuthGroups(getClientId());

        removeClientPropertyAttributes(propertyId);

        removePropertyFromGFTServiceXML(propertyCode);

        unassociatePropertyFromUserRole(propertyId);

        configService.deleteParameterValueNode(getConfigParamsContext(propertyCode));
    }

    private void unassociatePropertyFromUserRole(Integer propertyId) {
        propertyService.removePropertyAssociationWithUserRole(propertyId);
    }

    public Map<String, Integer> getPropertyCountsByStage() {
        TetrisPrincipal principal = PacmanThreadLocalContextHolder.getPrincipal();
        HashMap<String, Integer> stageCounts = new LinkedHashMap<>();
        stageCounts.put(UPLOADED_PROPERTIES_KEY, getUploadedPropertyCount());
        Map<String, Integer> dbStageCounts = null;

        if (principal != null && principal.isInternalUser()) {
            dbStageCounts = propertyRolloutPropertyFinder.getCountsByStage();
        } else if (principal != null) {
            List<com.ideas.tetris.platform.services.daoandentities.entity.Property> authorizedProperties = authorizationService
                                                                                                                   .retrieveAuthorizedProperties();
            List<Integer> authPropertyIds = new ArrayList<>();
            for (com.ideas.tetris.platform.services.daoandentities.entity.Property authProperty : authorizedProperties) {
                authPropertyIds.add(authProperty.getId());
            }
            if (!authorizedProperties.isEmpty()) {
                dbStageCounts = propertyRolloutPropertyFinder.getCountsByStageAndPropertyCodes(authPropertyIds);
            } else {
                LOGGER.info("User has no authorized properties so just zeroing out stage count");
            }
        }

        // propertyRolloutPropertyFinder gets the stages in ordered fashion but
        // Map.putAll does not seem to maintain
        // so perform the addition more deterministically
        // (stageCounts.putAll(dbStageCounts))
        Stage[] stages = Stage.orderedValues();
        for (Stage stage : stages) {
            stageCounts.put(stage.getCode(), null != dbStageCounts ? dbStageCounts.get(stage.getCode()) == null ? 0 : dbStageCounts.get(stage.getCode()) : 0);
        }

        return stageCounts;
    }

    private Integer getUploadedPropertyCount() {
        Query query = globalCrudService.getEntityManager().createNamedQuery(
                ConfigurationFileRecord.ALL_UPLOADED_PROPERTIES_COUNT);
        query.setParameter(ConfigurationFileRecord.PARAM_CLIENT_ID, getClientId());
        return ((Long) query.getSingleResult()).intValue();
    }

    void removePropertyFromGFTServiceXML(String propertyCode) {
        gftPropertyManagementService.removeProperty(propertyCode);
    }

    private void removePropertyFromSasFileLoc(Integer propertyId) {
        SASFileLoc existingEntity = getExistingSasFileLocation(propertyId);
        if (existingEntity != null) {
            globalCrudService.delete(SASFileLoc.class, existingEntity.getId());
        }
    }

    private void removeClientPropertyAttributes(Integer propertyId) {
        Query query = globalCrudService.getEntityManager().createNamedQuery(
                ClientPropertyAttributePairing.DELETE_BY_PROPERTY_ID);
        query.setParameter(PROPERTY_ID, propertyId);
        query.executeUpdate();

        if (configService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_CLIENT_ATTRIBUTES_ENABLED)) {
            //Remove attribute assignments for this property
            upsService.updateCustomAttributeAssignmentsForProperties(Arrays.asList(propertyId));
        }
    }

    private void deleteAnalyticsDataSetDirectory(Integer propertyId) {
        sasClientService.executeFileOps(DELETE_FILE_FORCELY,
                new HashMap<>(Map.of(DELETE_PATH, sasNodeLocator.determineSasAnalyticsDataSetLocationForSASNodes(propertyId))));
    }

    private String getClientCode() {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        return workContext.getClientCode();
    }

    private boolean isHiltonClientCode() {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        return workContext.isHiltonClientCode();
    }

    private Integer getClientId() {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        return workContext.getClientId();
    }

    private String getUserId() {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        return workContext != null ? workContext.getUserId() : "";
    }

    public PropertyConfig getPropertyConfig(Integer propertyId) {
        if (propertyId == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Property Id can not be null", null);
        }
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        workContext.setPropertyId(propertyId);
        if (tenantCrudService.find(TenantProperty.class, propertyId) == null) {
            throw getPropertyNoExistException(propertyId);
        }
        PropertyConfig config = new PropertyConfig();
        config.setPropertyId(propertyId);
        config.setExcludedDates(getExcludedDates(propertyId));
        config.setGlobalParameters(getGlobalParameters(propertyId));
        return config;
    }

    private TetrisException getPropertyNoExistException(Integer propertyId) {
        return new TetrisException(ErrorCode.INVALID_INPUT, "Property with id " + propertyId + " does not exist");
    }

    @SuppressWarnings({"squid:S1168"})
    private List<PropertyExcludedDates> getExcludedDates(Integer propertyId) {
        List<MarkPropertyDate> entities = findExcludedDates(propertyId);
        if (entities == null || entities.isEmpty()) {
            return null;
        }
        List<PropertyExcludedDates> results = new ArrayList<>();
        for (MarkPropertyDate entity : entities) {
            PropertyExcludedDates excludedDates = new PropertyExcludedDates();
            excludedDates.setEntityId(entity.getId());
            excludedDates.setStartDate(entity.getStartDate());
            excludedDates.setEndDate(entity.getEndDate());
            excludedDates.setOperation(ConfigOperation.VIEW);
            MarkDateDataType configDataType = getConfigDataType(entity);
            excludedDates.setConfigDataID(configDataType.getId());
            excludedDates.setConfigDataType(configDataType);
            excludedDates.setNotes(entity.getNotes());
            results.add(excludedDates);
        }
        return results;
    }

    private MarkDateDataType getConfigDataType(MarkPropertyDate entity) {
        return tenantCrudService.find(MarkDateDataType.class, entity.getIpConfigMarkDateDataType());
    }

    private List<GlobalParameter> getGlobalParameters(Integer propertyId) {
        String propertyCode = getPropertyCode(propertyId);
        return getGlobalParameters(propertyCode);
    }

    private List<GlobalParameter> getGlobalParameters(String propertyCode) {
        List<GlobalParameter> pacmanParameters = getAllParametersForNode(Constants.CONFIG_PARAMS_NODE_PREFIX);
        List<GlobalParameter> clientParameters = getAllParametersForNode(Constants.CONFIG_PARAMS_NODE_PREFIX + "."
                                                                                 + getClientCode());
        List<GlobalParameter> propertyParameters = getAllParametersForNode(getConfigParamsContext(propertyCode));

        Map<String, GlobalParameter> parameterMap = populateParameterMap(propertyParameters);
        mergeParameters(parameterMap, clientParameters);
        mergeParameters(parameterMap, pacmanParameters);
        List<GlobalParameter> results = new ArrayList<>();
        results.addAll(parameterMap.values());
        return results;
    }

    private Map<String, GlobalParameter> populateParameterMap(List<GlobalParameter> parameters) {
        Map<String, GlobalParameter> map = new HashMap<>();
        if (parameters == null) {
            return map;
        }
        for (GlobalParameter parameter : parameters) {
            map.put(parameter.getName(), parameter);
        }
        return map;
    }

    private void mergeParameters(Map<String, GlobalParameter> parameterMap, List<GlobalParameter> parentParameters) {
        if (parameterMap == null || parentParameters == null) {
            return;
        }
        for (GlobalParameter parentParameter : parentParameters) {
            GlobalParameter lowerValue = parameterMap.get(parentParameter.getName());
            if (lowerValue == null) {
                parameterMap.put(parentParameter.getName(), parentParameter);
            } else if (lowerValue.getDefaultValue() == null && lowerValue.getDefaultPredefinedValueId() == null) {
                lowerValue.setDefaultValue(parentParameter.getValue());
                lowerValue.setDefaultPredefinedValueId(parentParameter.getPredefinedValueId());
            }
        }
    }

    private List<GlobalParameter> getAllParametersForNode(String context) {
        List<GlobalParameter> results = new ArrayList<>();

        List<ConfigParameterValue> configParameterValues = configService
                                                                   .getConfigParameterValuesForNode(context, false);
        for (ConfigParameterValue configParameterValue : configParameterValues) {
            GlobalParameter parameter = buildGlobalParameter(configParameterValue, context);
            results.add(parameter);
        }
        return results;
    }

    private GlobalParameter buildGlobalParameter(ConfigParameterValue configParameterValue, String context) {
        if (configParameterValue == null) {
            return null;
        }
        GlobalParameter parameter = new GlobalParameter();
        parameter.setEntityId(configParameterValue.getId());
        parameter.setValue(configParameterValue.getValue());
        if (configParameterValue.getConfigParameterPredefinedValue() != null) {
            parameter.setPredefinedValueId(configParameterValue.getConfigParameterPredefinedValue().getId());
        }
        parameter.setOperation(ConfigOperation.VIEW);
        ConfigParameter configParameter = configParameterValue.getConfigParameter();
        parameter.setName(configParameter.getName());
        parameter.setValueType(ParameterValueType.STRING);
        ConfigParameterPredefinedValueType configParameterPredefinedValueType = configParameter
                                                                                        .getConfigParameterPredefinedValueType();
        if (configParameterPredefinedValueType != null) {
            Set<ConfigParameterPredefinedValue> configParameterPredefinedValues = configParameterPredefinedValueType
                                                                                          .getConfigParameterPredefinedValues();
            for (ConfigParameterPredefinedValue configParameterPredefinedValue : configParameterPredefinedValues) {
                PredefinedValue value = new PredefinedValue();
                value.setEntityId(configParameterPredefinedValue.getId());
                value.setValue(configParameterPredefinedValue.getValue());
                parameter.addPredefinedValue(value);
            }
        }
        String[] contextChunks = StringUtils.split(context, '.');
        String lastChunk = contextChunks[contextChunks.length - 1];
        parameter.setNodeName(lastChunk);
        return parameter;
    }

    private String getPropertyCode(Integer propertyId) {
        if (propertyId == null) {
            return null;
        }
        TenantProperty entity = tenantCrudService.find(TenantProperty.class, propertyId);
        if (entity == null) {
            return null;
        }
        return entity.getCode();
    }

    @SuppressWarnings({"squid:CommentedOutCodeLine"})
    public void updatePropertyConfig(PropertyConfig config) {
        Integer propertyId = config.getPropertyId();
        if (propertyId == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Property Id can not be null", null);
        }

        PacmanWorkContextHelper.setPropertyId(propertyId);

        if (tenantCrudService.find(TenantProperty.class, propertyId) == null) {
            throw getPropertyNoExistException(propertyId);
        }

        propertyExcludeDatesService.processExcludedDates(propertyId, config.getExcludedDates());
        processGlobalParameters(propertyId, config.getGlobalParameters());
        extractDetailsService.rebuildExtractDetails(propertyId); // ensure that
        // extract
        // counts
        // are in
        // sync;
        // this is
        // not
        // strictly
        // speaking
        // necessary,
        // but it is
        // a
        // convenient
        // way to
        // resync
        // the cache
        extractDetailsService.rebuildWebRateExtractDetails(propertyId); // ensure
        // that
        // extract
        // counts
        // are
        // in
        // sync;
        // this
        // is
        // not
        // strictly
        // speaking
        // necessary,
        // but
        // it is
        // a
        // convenient
        // way
        // to
        // resync
        // the
        // cache
        return;
    }

    void processGlobalParameters(Integer propertyId, List<GlobalParameter> globalParameters) {
        if (propertyId == null || globalParameters == null || globalParameters.isEmpty()) {
            return;
        }
        String propertyCode = getPropertyCode(propertyId);
        if (null == propertyCode) {
            return;
        }
        for (GlobalParameter parameter : globalParameters) {
            switch (parameter.getOperation()) {
                case VIEW:
                    // do nothing
                    break;
                case CREATE:
                    createGlobalParameter(propertyCode, parameter, getConfigParamsContext(propertyCode));
                    break;
                case UPDATE:
                    updateGlobalParameter(propertyId, propertyCode, parameter);
                    break;
                case DELETE:
                    deleteGlobalParameter(propertyCode, parameter);
                    break;
                default:
                    throw new TetrisException(ErrorCode.INVALID_INPUT, "Invalid operation: " + parameter.getOperation(),
                            null);
            }
        }
    }

    private void deleteGlobalParameter(String propertyCode, GlobalParameter parameter) {
        if (!propertyCode.equals(parameter.getNodeName())) {
            throw new TetrisException(ErrorCode.INVALID_INPUT,
                    "Can not delete global parameter at higher than property level ", null);
        }
        Integer entityId = parameter.getEntityId();
        if (entityId == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Must provide entityId", null);
        }
        ConfigParameterValue parameterValue = configService.getParameterValue(parameter.getEntityId());
        if (parameterValue == null) {
            throw new TetrisException("No ConfigParameterValue entity with ID: " + parameter.getEntityId());
        }
        configService.deleteParameterValue(parameterValue.getContext(), parameterValue.getConfigParameter().getName(),
                false);
    }

    private void updateGlobalParameter(Integer propertyId, String propertyCode, GlobalParameter parameter) {
        if (!propertyCode.equals(parameter.getNodeName())) {
            throw new TetrisException(ErrorCode.INVALID_INPUT,
                    "Can not update global parameter at higher than property level ", null);
        }
        Integer entityId = parameter.getEntityId();
        if (entityId == null) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Must provide entityId", null);
        }

        String value = getConfigParameterValue(parameter);
        ConfigParameterValue configParameterValue = configService.getParameterValue(parameter.getEntityId());
        configParameterValue.setValue(value);
        configService.updateParameterValue(configParameterValue);

        propertyService.invalidateCache(propertyId);
    }

    private String getConfigParameterValue(GlobalParameter parameter) {
        String value;
        Integer predefinedValueId = parameter.getPredefinedValueId();
        if (predefinedValueId != null && predefinedValueId != 0) {
            ConfigParameterPredefinedValue configParameterPredefinedValue = configService
                                                                                    .getParameterPredefinedValue(predefinedValueId);
            value = configParameterPredefinedValue.getValue();
        } else {
            value = parameter.getValue();
        }
        return value;
    }

    void createGlobalParameter(String propertyCode, GlobalParameter parameter, String context) {
        if (!propertyCode.equals(parameter.getNodeName())) {
            throw new TetrisException(ErrorCode.INVALID_INPUT,
                    "Can not create global parameter at higher than property level ", null);
        }

        String value = getConfigParameterValue(parameter);
        configService.addParameterValue(context, parameter.getName(), value);
    }

    @SuppressWarnings("unchecked")
    private List<MarkPropertyDate> findExcludedDates(Integer propertyId) {
        return tenantCrudService.findByNamedQuery(MarkPropertyDate.BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    private void deleteConfigurationRecords(String propertyCode) {
        Query query = globalCrudService.getEntityManager().createNamedQuery(ConfigurationFileRecord.DELETE_BY_PROPERTY);
        query.setParameter(ConfigurationFileRecord.PARAM_CLIENT_ID, getClientId());
        query.setParameter("propertyCodes", new HashSet<>(Arrays.asList(propertyCode)));
        query.executeUpdate();
    }

    public void setClientPropertyCacheService(ClientPropertyCacheService clientPropertyCacheService) {
        this.clientPropertyCacheService = clientPropertyCacheService;
    }

    public void removePropertyAssociations(Integer propertyId, String propertyCode) {
        try {
            removeClientPropertyAttributes(propertyId);
            removePropertyFromGFTServiceXML(propertyCode);
            unassociatePropertyFromUserRole(propertyId);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "An error occurred trying to clean property "
                                                                       + propertyCode + " : " + e.getMessage(), e);
        }
    }

    public VirtualPropertyMappingUIData buildVirtualPropertyUIDataForProperties(List<String> propertyCodes) {
        List<Integer> propertyIds = globalCrudService.findByNativeQuery(PROPERTY_IDS_FROM_CODES, QueryParameter.with("propertyCodes", propertyCodes).parameters());
        // set default property id which is not present in G3 if empty list,
        // so that query runs and gets left table values
        if (CollectionUtils.isEmpty(propertyIds)) {
            propertyIds = List.of(UNKNOWN_PROPERTY_ID);
        }
        Set<String> brandCodes = new HashSet<>();
        Set<String> globalAreas = new HashSet<>();
        Map<String, Map<String, String>> propertyAttributes = populatePropertyAttributesForProperties(propertyIds, brandCodes, globalAreas);
        Set<String> recoveryStates = new HashSet<>();
        Map<Integer, CPMigrationData> cpMigrationDataMap = transformPropertyCPConfigurationDataForProperties(propertyIds, recoveryStates);
        Map<String, VirtualPropertyMapping> existingMappings = getVirtualPropertyMappingMapForProperties(propertyIds, propertyAttributes, cpMigrationDataMap);
        return new VirtualPropertyMappingUIData(existingMappings, new ArrayList<>(brandCodes), new ArrayList<>(globalAreas), new ArrayList<>(recoveryStates));
    }

    private Map<Integer, CPMigrationData> transformPropertyCPConfigurationDataForProperties(List<Integer> propertyIds, Set<String> recoveryStates) {
        Map<Integer, CPMigrationData> cpMigrationDataMap = new HashMap<>();
        List<Object[]> resultSet = globalCrudService.findByNativeQuery(RecoveryState.RECOVERY_STATES_BY_PROPERTY_IDS,
                QueryParameter.with("propertyIds", propertyIds).and("clientId", PacmanWorkContextHelper.getClientId()).parameters());
        resultSet.forEach(row -> transformRecoveryStates(recoveryStates, row, cpMigrationDataMap));
        cpMigrationDataMap.forEach((propertyId, cpMigrationData) -> {
            String recoveryState = recoveryStateTransitionService.getRecoveryStateFromTransitionTable(propertyId);
            runIfTrue(StringUtils.isNotBlank(recoveryState), () -> cpMigrationData.setRecoveryState(recoveryState));
        });
        return cpMigrationDataMap;
    }

    private Object[] transformRecoveryStates(Set<String> recoveryStates, Object[] row, Map<Integer, CPMigrationData> cpMigrationDataMap) {
        String recoveryState = null;
        LocalDate cpMigrationDate = null;
        if (null != row[0]) {
            recoveryState = (String) row[0];
            recoveryStates.add(recoveryState);
        }

        if (null != row[2]) {
            cpMigrationDate = JavaLocalDateUtils.fromDate((Date) row[2]);
        }

        if (null != row[1]) {
            Integer propertyId = (Integer) row[1];
            CPMigrationData propertyCPData = cpMigrationDataMap.getOrDefault(propertyId, new CPMigrationData());
            propertyCPData.setRecoveryState(recoveryState);
            propertyCPData.setCpMigrationDate(cpMigrationDate);
            cpMigrationDataMap.putIfAbsent(propertyId, propertyCPData);
        }
        return row;
    }

    private Map<String, VirtualPropertyMapping> getVirtualPropertyMappingMapForProperties(List<Integer> propertyIds, Map<String, Map<String, String>> propertyAttributes, Map<Integer, CPMigrationData> cpMigrationDataMap) {
        List<ConsolidatedPropertyView> consolidatedPropertyViews = globalCrudService.findByNamedQuery(ConsolidatedPropertyView.BY_PROPERTY_IDS, QueryParameter.with("propertyIds", propertyIds).parameters());
        return consolidatedPropertyViews.stream()
                       .map(property -> createVPM(property, propertyAttributes.get(property.getPropertyCode()), cpMigrationDataMap))
                       .collect(toMap(VirtualPropertyMapping::getPhysicalPropertyCode, Function.identity()));
    }

    private Map<String, Map<String, String>> populatePropertyAttributesForProperties(List<Integer> propertyIdList, Set<String> brandCodes, Set<String> globalAreas) {
        Map<String, Map<String, String>> propertyAttributes = new HashMap<>();
        globalCrudService.findByNativeQuery(ClientAttribute.BY_PROPERTIES_AND_ATTRIBUTES_NAMES, QueryParameter.with("propertyIds", propertyIdList).and("attributeNames",
                                Arrays.asList(VirtualPropertyMappingUIData.BRAND_CODE, VirtualPropertyMappingUIData.GLOBAL_AREA))
                                                                                                        .and("clientId", PacmanWorkContextHelper.getClientId()).parameters(),
                result -> transformPropertyAttributes(propertyAttributes, result, brandCodes, globalAreas));
        return propertyAttributes;
    }

    protected Object[] transformPropertyAttributes(Map<String, Map<String, String>> propertyAttributes, Object[] result, Set<String> brandCodes, Set<String> globalAreas) {
        String propertyCode = "DUMMY";
        if (null != result[0]) {
            propertyCode = (String) result[0];
        }
        String attributeName = StringUtils.upperCase((String) result[1]);
        String value = (String) result[2];
        if (StringUtils.equalsIgnoreCase(VirtualPropertyMappingUIData.BRAND_CODE, attributeName)) {
            brandCodes.add(value);
        }
        if (StringUtils.equalsIgnoreCase(VirtualPropertyMappingUIData.GLOBAL_AREA, attributeName)) {
            globalAreas.add(value);
        }
        if (propertyAttributes.containsKey(propertyCode)) {
            Map<String, String> propertyAttr = propertyAttributes.get(propertyCode);
            propertyAttr.put(attributeName, value);
        } else {
            HashMap<String, String> newMap = new HashMap<>();
            newMap.put(attributeName, value);
            propertyAttributes.put(propertyCode, newMap);
        }
        return result;
    }

    private VirtualPropertyMapping createVPM(ConsolidatedPropertyView property, Map<String, String> attributeDetailsMap, Map<Integer, CPMigrationData> cpMigrationDataMap) {
        VirtualPropertyMapping vpm = new VirtualPropertyMapping();
        vpm.setPhysicalPropertyCode(property.getPropertyCode());
        vpm.setExternalSystem(property.getExternalSystem());
        vpm.setLDB(property.isLdbEnabled());
        if (attributeDetailsMap != null) {
            vpm.setGlobalArea(attributeDetailsMap.get(VirtualPropertyMappingUIData.GLOBAL_AREA));
            vpm.setBrandCode(attributeDetailsMap.get(VirtualPropertyMappingUIData.BRAND_CODE));
        }
        CPMigrationData propertyCPData = cpMigrationDataMap.getOrDefault(property.getPropertyId(), new CPMigrationData());
        vpm.setRecoveryState(propertyCPData.getRecoveryState());
        vpm.setCpMigrationDate(propertyCPData.getCpMigrationDate());
        return vpm;
    }

    public VirtualPropertyMappingUIData getVirtualPropertySeedDataFor(List<String> physicalPropertyCodes, Set<String> extendedStayProperties) {
        Map<String, VirtualPropertyMappingSeedData> seedDataMap = virtualPropertyMappingSeedDataService.getSeedDataBy(physicalPropertyCodes)
                                                                          .stream().collect(toMap(VirtualPropertyMappingSeedData::getPropertyCode, Function.identity()));
        VirtualPropertyMappingUIData uiData = buildVirtualPropertyUIDataForProperties(physicalPropertyCodes);
        Map<String, VirtualPropertyMapping> availableMappings = uiData.getAvailableMappings();
        for (String physicalPropertyCode : physicalPropertyCodes) {
            VirtualPropertyMapping currentMapping = getCurrentVirtualPropertyMapping(availableMappings, physicalPropertyCode);
            VirtualPropertyMappingSeedData seedData = seedDataMap.getOrDefault(physicalPropertyCode, null);
            currentMapping.setExtendedStay(extendedStayProperties.contains(physicalPropertyCode));
            if (null == seedData) {
                LOGGER.info(String.format("No seed data found for %s property", physicalPropertyCode));
                continue;
            }
            currentMapping.setGlobalArea(seedData.getRegion());
        }
        return uiData;
    }

    private VirtualPropertyMapping getCurrentVirtualPropertyMapping(Map<String, VirtualPropertyMapping> availableMappings, String physicalPropertyCode) {
        VirtualPropertyMapping currentMapping = availableMappings.getOrDefault(physicalPropertyCode, getVirtualPropertyMapping(physicalPropertyCode));
        availableMappings.put(physicalPropertyCode, currentMapping);
        return currentMapping;
    }

    private VirtualPropertyMapping getVirtualPropertyMapping(String physicalPropertyCode) {
        VirtualPropertyMapping virtualPropertyMapping = new VirtualPropertyMapping();
        virtualPropertyMapping.setPhysicalPropertyCode(physicalPropertyCode);
        return virtualPropertyMapping;
    }

    @Getter
    @Setter
    private class CPMigrationData {
        private String recoveryState;
        private LocalDate cpMigrationDate;
    }
}
