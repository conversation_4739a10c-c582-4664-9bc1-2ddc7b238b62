package com.ideas.tetris.pacman.services.security;

import com.ideas.tetris.platform.common.cache.AbstractCache;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


@Component
@Transactional
public class UserAuthorizedPropertyCache extends AbstractCache<String, List<Integer>> {

    public List<Integer> get(Integer clientId, Integer userId) {
        return get(buildKey(clientId, userId));
    }

    public boolean put(Integer clientId, Integer userId, List<Integer> propertyIds) {
        return put(buildKey(clientId, userId), propertyIds);
    }

    public void remove(Integer clientId, Integer userId) {
        remove(buildKey(clientId, userId));
    }

    public void remove(Integer clientId) {
        List<String> cacheKeys = entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(clientId + "-"))
                .map(entry -> entry.getKey())
                .collect(Collectors.toList());
        cacheKeys.forEach(key -> remove(key));
    }

    private String buildKey(Integer clientId, Integer userId) {
        // cache key is client id - user id to deal with users that have access
        // to multiple clients
        return clientId.toString() + "-" + userId.toString();
    }

    @Override
    protected boolean isAsync() {
        return false;
    }

    @Override
    protected Optional<Integer> getRedisLifespan() {
        return Optional.of(600000);
    }
}
