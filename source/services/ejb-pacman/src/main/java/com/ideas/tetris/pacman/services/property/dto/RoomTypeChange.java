package com.ideas.tetris.pacman.services.property.dto;

import java.util.Date;

public class RoomTypeChange {

    private String RoomTypeID;
    private String RoomTypeCode;
    private String OldRoomCapacity;
    private String NewRoomCapacity;
    private String OldRoomClassCode;
    private String NewRoomClassCode;
    private String OldStatus;
    private String NewStatus;
    private String OldDisplayStatus;
    private String NewDisplayStatus;
    private String ChangeType;
    private String UpdatedByUser;
    private Date UpdatedDate;

    public String getRoomTypeID() {
        return RoomTypeID;
    }

    public void setRoomTypeID(String roomTypeID) {
        RoomTypeID = roomTypeID;
    }

    public String getRoomTypeCode() {
        return RoomTypeCode;
    }

    public void setRoomTypeCode(String roomTypeCode) {
        RoomTypeCode = roomTypeCode;
    }

    public String getOldRoomCapacity() {
        return OldRoomCapacity;
    }

    public void setOldRoomCapacity(String oldRoomCapacity) {
        OldRoomCapacity = oldRoomCapacity;
    }

    public String getNewRoomCapacity() {
        return NewRoomCapacity;
    }

    public void setNewRoomCapacity(String newRoomCapacity) {
        NewRoomCapacity = newRoomCapacity;
    }

    public String getOldRoomClassCode() {
        return OldRoomClassCode;
    }

    public void setOldRoomClassCode(String oldRoomClassCode) {
        OldRoomClassCode = oldRoomClassCode;
    }

    public String getNewRoomClassCode() {
        return NewRoomClassCode;
    }

    public void setNewRoomClassCode(String newRoomClassCode) {
        NewRoomClassCode = newRoomClassCode;
    }

    public String getOldStatus() {
        return OldStatus;
    }

    public void setOldStatus(String oldStatus) {
        OldStatus = oldStatus;
    }

    public String getNewStatus() {
        return NewStatus;
    }

    public void setNewStatus(String newStatus) {
        NewStatus = newStatus;
    }

    public String getOldDisplayStatus() {
        return OldDisplayStatus;
    }

    public void setOldDisplayStatus(String oldDisplayStatus) {
        OldDisplayStatus = oldDisplayStatus;
    }

    public String getNewDisplayStatus() {
        return NewDisplayStatus;
    }

    public void setNewDisplayStatus(String newDisplayStatus) {
        NewDisplayStatus = newDisplayStatus;
    }

    public String getChangeType() {
        return ChangeType;
    }

    public void setChangeType(String changeType) {
        ChangeType = changeType;
    }

    public String getUpdatedByUser() {
        return UpdatedByUser;
    }

    public void setUpdatedByUser(String updatedByUser) {
        UpdatedByUser = updatedByUser;
    }

    public Date getUpdatedDate() {
        return UpdatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        UpdatedDate = updatedDate;
    }
}
