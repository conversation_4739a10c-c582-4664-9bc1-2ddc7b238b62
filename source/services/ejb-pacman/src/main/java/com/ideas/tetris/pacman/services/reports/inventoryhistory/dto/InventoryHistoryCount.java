package com.ideas.tetris.pacman.services.reports.inventoryhistory.dto;

public class InventoryHistoryCount {

    private boolean isLimitExceeding;
    private int actualNoOfRecords;
    private int excelLimit;

    public boolean isLimitExceeding() {
        return isLimitExceeding;
    }

    public void setLimitExceeding(boolean limitExceeding) {
        isLimitExceeding = limitExceeding;
    }

    public int getActualNoOfRecords() {
        return actualNoOfRecords;
    }

    public void setActualNoOfRecords(int actualNoOfRecords) {
        this.actualNoOfRecords = actualNoOfRecords;
    }

    public int getExcelLimit() {
        return excelLimit;
    }

    public void setExcelLimit(int excelLimit) {
        this.excelLimit = excelLimit;
    }
}
