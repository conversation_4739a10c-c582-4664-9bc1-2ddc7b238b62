package com.ideas.tetris.pacman.services.property.dto;

import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

public class RoomTypeChangeRowMapper implements RowMapper<RoomTypeChange> {
    @Override
    public RoomTypeChange mapRow(Object[] row) {
        RoomTypeChange rowMapper = new RoomTypeChange();
        rowMapper.setRoomTypeID(String.valueOf(row[0]));
        rowMapper.setRoomTypeCode(String.valueOf(row[1]));
        rowMapper.setOldRoomCapacity(String.valueOf(row[2]));
        rowMapper.setNewRoomCapacity(String.valueOf(row[3]));
        rowMapper.setOldRoomClassCode(String.valueOf(row[4]));
        rowMapper.setNewRoomClassCode(String.valueOf(row[5]));
        rowMapper.setOldStatus(String.valueOf(row[6]));
        rowMapper.setNewStatus(String.valueOf(row[7]));
        rowMapper.setOldDisplayStatus(String.valueOf(row[8]));
        rowMapper.setNewDisplayStatus(String.valueOf(row[9]));
        rowMapper.setUpdatedByUser(String.valueOf(row[10]));
        rowMapper.setChangeType(String.valueOf(row[11]));
        rowMapper.setUpdatedDate(DateUtil.toDate(String.valueOf(row[12])));
        return rowMapper;
    }
}
