package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.datafeed.dto.AccomClassMapping;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateAccomClassMapping;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateAccomType;
import com.ideas.tetris.pacman.services.webrate.service.AccommodationMappingService;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class RateShoppingAccomMappingService {

    @Autowired
	private AccommodationMappingService accommodationMappingService;

    public List getAllAccomMappingDetails() {
        List<WebrateAccomType> webrateAccomTypes = accommodationMappingService.getAccomodationMappingByProperty();

        return getAccomMappingDetails(webrateAccomTypes);
    }

    private List getAccomMappingDetails(List<WebrateAccomType> webrateAccomTypes) {
        List<AccomClassMapping> accomClassMappings = new ArrayList<>();
        for (WebrateAccomType webrateAccomType : webrateAccomTypes) {
            AccomClassMapping accomClassMapping;
            for (WebrateAccomClassMapping webrateAccomClassMapping : webrateAccomType.getWebrateAccomClassMappings()) {
                accomClassMapping = new AccomClassMapping();
                accomClassMapping.setCompetitiveAccomType(webrateAccomType.getWebrateAccomName());
                accomClassMapping.setCompetitiveAccomTypeDisplayName(webrateAccomType.getWebrateAccomAlias());
                accomClassMapping.setAccomClassCode(webrateAccomClassMapping.getAccomClass().getCode());
                accomClassMappings.add(accomClassMapping);
            }
        }
        return accomClassMappings;
    }
}
