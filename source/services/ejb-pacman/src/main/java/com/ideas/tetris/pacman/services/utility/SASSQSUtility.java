package com.ideas.tetris.pacman.services.utility;

import com.ideas.sas.core.MacroRequest;
import com.ideas.sas.core.SASClientRequest;
import com.ideas.sas.core.SASRequestQueueActions;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import io.awspring.cloud.messaging.core.QueueMessagingTemplate;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class SASSQSUtility {

    public static final String SQS_HEADER_CONTROLLER_TYPE = "controllerType";
    public static final String SQS_HEADER_USE_SAS_REQUEST_QUEUE_FOR = "useSasRequestQueueFor";
    public static final String SQS_HEADER_MESSAGE_GROUP_ID = "message-group-id";
    public static final String SQS_HEADER_MESSAGE_DEDUPLICATION_ID = "message-deduplication-id";

    private SASSQSUtility() {
    }

    public static final String EXCEPTION_MESSAGE_SQS_CONFIG_ERROR = "QueueMessagingTemplate is null even though " +
            "useSasRequestQueueFor is set to %s. Please check the sqs configuration " +
            "or set useSasRequestQueueFor to NONE and " +
            "try again";
    public static final String EXCEPTION_MESSAGE_SAS_QUEUE_SEND_ERROR = "Exception occurred when sending request to sas sqs queue. " +
            "Please check the sqs configuration or set useSasRequestQueueFor to NONE and try again";

    public static boolean useSasQueue(String useSasRequestQueueFor) {
        return SASRequestQueueActions.PROCESS.name().equals(useSasRequestQueueFor) || SASRequestQueueActions.LOG.name().equals(useSasRequestQueueFor);
    }

    public static boolean shouldExecuteUsingClientService(String useSasRequestQueueFor) {
        return !SASRequestQueueActions.PROCESS.name().equals(useSasRequestQueueFor);
    }

    public static void sendRequestToQueue(SASClientRequest clientRequest, String useSasRequestQueueFor,
                                          QueueMessagingTemplate queueMessagingTemplate) {
        try {
            Map<String,Object> headers = buildRequestHeader(clientRequest, useSasRequestQueueFor);
            queueMessagingTemplate.convertAndSend(SystemConfig.getSasRequestQueueName(), clientRequest,headers);
        }catch (Exception e){
            if(queueMessagingTemplate == null){
                throw new TetrisException(ErrorCode.SAS_INTEGRATION_ERROR, String.format(EXCEPTION_MESSAGE_SQS_CONFIG_ERROR,useSasRequestQueueFor));
            }
            if(SASRequestQueueActions.PROCESS.name().equals(useSasRequestQueueFor)){
                throw new TetrisException(ErrorCode.SAS_INTEGRATION_ERROR, EXCEPTION_MESSAGE_SAS_QUEUE_SEND_ERROR, e);
            }
            log.warn("Exception occurred when sending request to queue. The useSasRequestQueueFor is set to LOG only," +
                    " Hence not failing the job but the sas sqs configuration needs to be looked into.", e);
        }
    }

    private static Map<String, Object> buildRequestHeader(SASClientRequest clientRequest, String useSasRequestQueueFor) {
        return Map.of(SQS_HEADER_CONTROLLER_TYPE, clientRequest instanceof MacroRequest ? "MACRO" : "TK"
                , SQS_HEADER_USE_SAS_REQUEST_QUEUE_FOR, useSasRequestQueueFor,
                SQS_HEADER_MESSAGE_GROUP_ID, clientRequest.getRequestId(),
                SQS_HEADER_MESSAGE_DEDUPLICATION_ID, clientRequest.getRequestId());
    }
}