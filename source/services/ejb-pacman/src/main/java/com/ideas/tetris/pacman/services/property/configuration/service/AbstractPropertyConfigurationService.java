package com.ideas.tetris.pacman.services.property.configuration.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatus;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.property.PropertyRolloutService;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.dto.Property;
import com.ideas.tetris.pacman.services.property.dto.PropertySearchCriteria;
import com.ideas.tetris.pacman.services.security.User;
import com.ideas.tetris.platform.common.configparams.entities.ParameterPredefinedValue;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Transactional
@Component
public abstract class AbstractPropertyConfigurationService {

    private static final Logger LOGGER = Logger.getLogger(AbstractPropertyConfigurationService.class);

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService crudService;

    @Autowired
	protected PropertyRolloutService propertyRolloutService;

    @Autowired
	protected PacmanConfigParamsService configService;

    private static final int NUMBER_OF_MILLISECONDS_IN_A_SECOND = 1000;

    public boolean canHandle(PropertyConfigurationDto propertyConfigurationDto) {
        return propertyConfigurationDto.getRecordType().equals(getPropertyConfigurationRecordType());
    }

    public List<PropertyConfigurationRecordFailure> validate(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId) {
        Long start = System.currentTimeMillis();
        List<PropertyConfigurationRecordFailure> failures = doValidate(propertyConfigurationDto, propertyId);
        LOGGER.debug("Validation took " + (System.currentTimeMillis() - start) / NUMBER_OF_MILLISECONDS_IN_A_SECOND + " seconds");
        return failures;
    }

    public void handle(PropertyConfigurationDto pcd, Integer propertyId) {
        long start = System.currentTimeMillis();
        doHandle(pcd, propertyId);
        LOGGER.debug("Handle took " + (System.currentTimeMillis() - start) / NUMBER_OF_MILLISECONDS_IN_A_SECOND + " seconds");
    }

    protected boolean isPredefinedValueForParameter(String parameterName, String value) {
        ParameterPredefinedValue parameterPredefinedValueAsLegacy = configService.getParameterPredefinedValue(parameterName, value);
        if (parameterPredefinedValueAsLegacy != null) {
            return true;
        }

        return false;
    }

    protected Property findProperty(Integer propertyId) {
        PropertySearchCriteria searchCriteria = new PropertySearchCriteria();
        searchCriteria.setPropertyId(propertyId);

        List<Property> properties = propertyRolloutService.findProperties(searchCriteria);
        if (properties != null && !properties.isEmpty()) {
            return properties.get(0);
        }

        return null;
    }

    public Integer findPropertyIdForCode(String propertyCode) {
        Integer propertyId = globalCrudService.findByNamedQuerySingleResult(com.ideas.tetris.platform.services.daoandentities.entity.Property.GET_ID_BY_CLIENT_CODE_PROPERTY_CODE, QueryParameter.with("clientCode", PacmanWorkContextHelper.getClientCode()).and("propertyCode", propertyCode).parameters());
        if (propertyId == null) {
            LOGGER.warn("Unable to find Property ID for code: " + propertyCode);
            return null;
        }

        return propertyId;
    }

    public TenantStatus findActiveTenantStatus() {
        return (TenantStatus) crudService.findByNamedQuerySingleResult(TenantStatus.BY_NAME, QueryParameter.with("name", "Active").parameters());
    }

    public Integer findUserId() {
        String userId = null;

        User user = null;
        WorkContextType workContext = PacmanThreadLocalContextHolder.getWorkContext();
        if (workContext != null) {
            userId = workContext.getUserId();

            if (StringUtils.isNotEmpty(userId)) {
                user = crudService.findByNamedQuerySingleResult(User.BY_SCREEN_NAME, QueryParameter.with("screenName", userId).parameters());
            }
        }

        if (user != null) {
            return user.getId();
        }

        return 1;
    }

    public abstract PropertyConfigurationRecordType getPropertyConfigurationRecordType();

    public abstract List<PropertyConfigurationRecordFailure> doValidate(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId);

    public abstract void doHandle(PropertyConfigurationDto pcd, Integer propertyId);

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setPropertyRolloutService(PropertyRolloutService propertyRolloutService) {
        this.propertyRolloutService = propertyRolloutService;
    }

    public void setConfigService(PacmanConfigParamsService configService) {
        this.configService = configService;
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

}
