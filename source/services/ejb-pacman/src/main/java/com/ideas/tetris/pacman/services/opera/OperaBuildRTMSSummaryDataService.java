package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.services.opera.metric.OperaMetrics;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackData;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackDataBuilder;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.job.JobCallback;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * Created by idnpak on 2/23/2015.
 */
@Justification("Since there is a lot of extensive data churning, we need to ensure that there are no transaction timeouts.")
@Component
@Transactional(timeout = 3600)
public class OperaBuildRTMSSummaryDataService {

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    YCByRuleBuildSummaryService ycByRuleBuildSummaryService;

    private static final Logger LOGGER = Logger.getLogger(OperaBuildRTMSSummaryDataService.class.getName());

    @Async
    public Future<Integer> buildRTMSSummaryData(String correlationId, JobStepContext jobStepContext, WorkContextType workContext) {
        int numberOfRows = 0;
        PlatformThreadLocalContextHolder.setJobStepContext(jobStepContext);
        PlatformThreadLocalContextHolder.setWorkContext(workContext);
        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        JobCallback jobCallback = getJobCallback();
        try {
            numberOfRows = buildRTMSSummaryData(correlationId);
            asyncCallbackData.setWasSuccessful(true);
            asyncCallbackData.setResponse(numberOfRows);
        } catch (Exception e) {
            asyncCallbackData.setWasSuccessful(false);
            asyncCallbackData.setResponse(e);
        } finally {
            jobCallback.execute(asyncCallbackData);
        }
        return new AsyncResult<Integer>(new Integer(numberOfRows));
    }

    public JobCallback getJobCallback() {
        return new JobCallback();
    }

    public enum BuildRTMSSummaryMetricType {
        BUILD_MS_AND_RT_SUMMARY_FROM_TRANS
    }

    public static final OperaMetrics<BuildRTMSSummaryMetricType> metricsBuildRTMSSummary = new OperaMetrics<BuildRTMSSummaryMetricType>();

    public int buildRTMSSummaryData(String correlationId) {
        LOGGER.info("Started building RTMS summary for feed : " + correlationId);
        int numRows = 0;
        try {
            // Process transaction transformations prior to occupancy
            // summary.
            // This is required as occupancy summary transformations may
            // create
            // new records
            // based on aggregated transaction data.
            metricsBuildRTMSSummary.start(BuildRTMSSummaryMetricType.BUILD_MS_AND_RT_SUMMARY_FROM_TRANS);
            numRows += this.ycByRuleBuildSummaryService
                    .aggregateAnalyticalMarketSegmentReservations(correlationId);
            metricsBuildRTMSSummary.stop(BuildRTMSSummaryMetricType.BUILD_MS_AND_RT_SUMMARY_FROM_TRANS);
        } finally {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug(new StringBuilder().append("Finished building RT and MS Summary ").append(numRows)
                        .append("  rows:\n").append(metricsBuildRTMSSummary.toString()).toString());
            }
        }
        LOGGER.info("Completed building RTMS summary for feed : " + correlationId);
        return numRows;
    }
}
