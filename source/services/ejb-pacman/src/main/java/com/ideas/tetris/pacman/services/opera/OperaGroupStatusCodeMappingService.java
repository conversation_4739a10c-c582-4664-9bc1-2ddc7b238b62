package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.services.groupblock.GenericGroupStatusCodeMappingService;
import com.ideas.tetris.pacman.services.groupblock.dto.G3GroupStatusTypeDto;
import com.ideas.tetris.pacman.services.groupblock.dto.GroupStatusCode;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertCreationService;
import com.ideas.tetris.pacman.services.opera.constants.OperaGroupServiceConstants;
import com.ideas.tetris.pacman.services.opera.entity.OperaG3GroupStatusType;
import com.ideas.tetris.pacman.services.opera.entity.OperaG3GroupStatusTypeMap;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by idnais on 7/22/2015.
 */
@OperaGroupStatusCodeMappingService.Qualifier
@Component
@Transactional
public class OperaGroupStatusCodeMappingService extends GenericGroupStatusCodeMappingService {
    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }

    private static final Logger LOGGER = Logger.getLogger(OperaGroupStatusCodeMappingService.class.getName());


    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    @Override
    public List<GroupStatusCode> fetchAllUnmappedG3GroupStatusCodes() {
        List<GroupStatusCode> groupStatusCodes = new ArrayList<GroupStatusCode>();
        List<String> unmappedOperaStatusCodesFromFeed = getUnmappedOperaStatusCodesFromFeed(OperaGroupServiceConstants.GET_UNMAPPED_OPERA_STATUS_CODES_FROM_HISTORY);
        for (String unMappedOperaStatusCode : unmappedOperaStatusCodesFromFeed) {
            GroupStatusCode groupStatusCode = new GroupStatusCode(unMappedOperaStatusCode);
            groupStatusCodes.add(groupStatusCode);
        }
        return groupStatusCodes;
    }

    @Override
    public List<G3GroupStatusTypeDto> getG3GroupStatusTypes() {
        List<OperaG3GroupStatusType> operaG3GroupStatusType = crudService.findByNamedQuery(OperaG3GroupStatusType.FIND_ALL_G3_GROUP_STATUS_TYPES);

        //TODO: use streams here
        List<G3GroupStatusTypeDto> g3GroupStatusTypeDtos = new ArrayList<>();
        for (OperaG3GroupStatusType groupStatusType : operaG3GroupStatusType) {
            g3GroupStatusTypeDtos.add(new G3GroupStatusTypeDto(groupStatusType.getId(), groupStatusType.getG3GroupStatusCode(),
                    groupStatusType.getGetG3GroupStatusType()));
        }
        return g3GroupStatusTypeDtos;
    }

    @Override
    public List<GroupStatusCode> fetchAllMappedG3GroupStatusCodes() {
        List<GroupStatusCode> groupStatusCodes = new ArrayList<GroupStatusCode>();
        Map<Integer, G3GroupStatusTypeDto> g3GroupStatusTypesAsMap = getG3GroupStatusTypesAsMap();
        List<OperaG3GroupStatusTypeMap> mappedGroupStatusCodes = getMappedGroupStatusCodes(OperaG3GroupStatusTypeMap.GET_MAPPED_GROUP_STATUS_TYPES);

        for (OperaG3GroupStatusTypeMap g3GroupStatusTypeMap : mappedGroupStatusCodes) {
            GroupStatusCode groupStatusCode = new GroupStatusCode();
            Integer g3GroupStatusTypeId = g3GroupStatusTypeMap.getG3GroupStatusTypeId();
            G3GroupStatusTypeDto operaG3GroupStatusTypeDto = g3GroupStatusTypesAsMap.get(g3GroupStatusTypeId);

            groupStatusCode.setGroupStatusCode(cleanUpString(g3GroupStatusTypeMap.getOperaGroupStatusCode()));
            groupStatusCode.setG3GroupStatusTypeId(g3GroupStatusTypeId);
            groupStatusCode.setG3GroupStatusTypeMapID(g3GroupStatusTypeMap.getId());
            groupStatusCode.setG3GroupStatusTypeDto(operaG3GroupStatusTypeDto);
            groupStatusCodes.add(groupStatusCode);
        }

        return groupStatusCodes;
    }

    @Override
    public void saveGroupStatusCodeMapping(List<GroupStatusCode> groupStatusCodes) {
        LOGGER.info("OPERA group status mapping save called : " + groupStatusCodes.size());
        List<OperaG3GroupStatusTypeMap> operaGroupStatusTypeMappingList = new ArrayList<OperaG3GroupStatusTypeMap>();
        List<String> newlyMappedGroupStatusCodes = new ArrayList<String>();
        for (GroupStatusCode groupStatusCode : groupStatusCodes) {
            String operaGroupStatusCode = groupStatusCode.getGroupStatusCode();
            newlyMappedGroupStatusCodes.add(operaGroupStatusCode);
            OperaG3GroupStatusTypeMap operaGroupStatusTypeMap = new OperaG3GroupStatusTypeMap();
            operaGroupStatusTypeMap.setOperaGroupStatusCode(operaGroupStatusCode);
            operaGroupStatusTypeMap.setG3GroupStatusTypeId(groupStatusCode.getG3GroupStatusTypeId());
            operaGroupStatusTypeMap.setId(groupStatusCode.getG3GroupStatusTypeMapID());
            operaGroupStatusTypeMappingList.add(operaGroupStatusTypeMap);
        }
        try {
            crudService.save(operaGroupStatusTypeMappingList);
        } catch (Exception sqle) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error saving group status mapping to db. " + sqle.getMessage(), sqle);
        }

        crudService.executeUpdateByNativeQuery(
                OperaGroupServiceConstants.UPDATE_UNMAPPED_OPERA_GROUP_STAUS_CODES_PACMAN_GROUP_MASTER,
                QueryParameter.with(NEWLY_MAPPED_GROUP_STATUS_CODES, newlyMappedGroupStatusCodes).parameters());

        resloveUnmappedGroupStatusCodesAlert();
    }

    @Override
    public void createAlertUnmappedGroupStatusCodes() {
        List<String> unmappedGroupStatusCodes = crudService.findByNativeQuery(OperaGroupServiceConstants.GET_UNMAPPED_OPERA_STATUS_CODES_FROM_RAW);
        if (unmappedGroupStatusCodes.isEmpty()) {
            LOGGER.info("There are no unmapped group status codes found for alert generation");
        } else {
            Set<String> unMappedGroupStatusCodesSet = new HashSet<String>(unmappedGroupStatusCodes);
            sendAlert(StringUtils.join(unMappedGroupStatusCodesSet, ','));
        }
    }

    @Override
    public void deleteAllGroupStatusCodeMapping() {
        try {
            crudService.deleteAll(OperaG3GroupStatusTypeMap.class);
        } catch (Exception sqle) {
            LOGGER.debug("Error deleting group status mapping to db. " + sqle.getMessage());
        }
    }

    @Override
    public boolean canHandle() {
        return canHandleReservationSystem(ReservationSystem.OPERA);
    }

    public void setAlertCreationService(AlertCreationService alertCreationService) {
        this.alertCreationService = alertCreationService;
    }

    protected Map<Integer, G3GroupStatusTypeDto> getG3GroupStatusTypesAsMap() {
        List<G3GroupStatusTypeDto> g3GroupStatusTypeDtos = getG3GroupStatusTypes();
        Map<Integer, G3GroupStatusTypeDto> g3GroupStatusTypesAsMap = new HashMap<>();

        for (G3GroupStatusTypeDto g3GroupStatusTypeDto : g3GroupStatusTypeDtos) {
            g3GroupStatusTypesAsMap.put(g3GroupStatusTypeDto.getId(), g3GroupStatusTypeDto);
        }
        return g3GroupStatusTypesAsMap;
    }
}

