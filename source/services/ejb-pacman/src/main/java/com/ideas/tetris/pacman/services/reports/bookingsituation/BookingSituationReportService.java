package com.ideas.tetris.pacman.services.reports.bookingsituation;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.RatchetCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.bookingsituation.dto.BookingSituationDTO;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.JasperReportDataConverter;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.BookingSituationReportCriteria;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportData;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.components.ScheduledReportSheet;
import com.ideas.tetris.pacman.services.scheduledreport.service.JasperReportService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class BookingSituationReportService extends JasperReportService<ScheduledReportData, BookingSituationReportCriteria> {

    private static final String BUSINESS_TYPE_GROUP = "group";
    private static final String BUSINESS_TYPE_TRANSIENT = "transient";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService tenantCrudService;

    @RatchetCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("ratchetCrudServiceBean")
    CrudService ratchetCrudService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }


    public void setCrudService(CrudService crudService) {
        this.tenantCrudService = crudService;
    }

    @Override
    protected JasperReportDataConverter<ScheduledReportData, BookingSituationReportCriteria> getJasperReportDataConverter() {
        return null;
    }

    @Override
    public String getReportTitle(ScheduledReport<BookingSituationReportCriteria> scheduledReport) {
        BookingSituationReportCriteria criteria = scheduledReport.getReportCriteria();
        if (criteria.isHotelChecked()) {
            return "booking.situation.report.at.hotel.level";
        } else if (criteria.isTransientChecked()) {
            return "booking.situation.report.at.transient.level";
        } else if (criteria.isGroupChecked()) {
            return "booking.situation.report.at.group.level";
        } else if (criteria.isFgChecked()) {
            return "booking.situation.report.at.forecast.group.level";
        } else if (criteria.isBvChecked()) {
            return "booking.situation.report.at.business.view.level";
        } else {
            return "booking-situation-report";
        }
    }

    private List<?> getBookingSituationDTOsFromResultList(List<Object[]> resultList, boolean isBusinessView, boolean isForecastGroup,
                                                          boolean isTransient, boolean isGroup, boolean isHotel, ScheduledReport<BookingSituationReportCriteria> scheduledReport) {
        BookingSituationReportCriteria reportCriteria = scheduledReport.getReportCriteria();
        boolean isSpecialEvent = reportCriteria.isSpecialEventChecked();
        List<BookingSituationDTO> dataList = populateData(resultList, reportCriteria, isBusinessView, isForecastGroup, isTransient, isGroup, isSpecialEvent);
        if (isHotel || isTransient || isGroup) {
            return dataList;
        }
        return createDataMap(reportCriteria.getCategories().length, dataList);
    }

    private List<BookingSituationDTO> populateData(List<Object[]> resultList, BookingSituationReportCriteria reportCriteria,
                                                   boolean isBusinessView, boolean isForecastGroup,
                                                   boolean isTransient, boolean isGroup, boolean isSpecialEvent) {
        List<BookingSituationDTO> dataList = new ArrayList<BookingSituationDTO>(resultList.size());
        resultList.stream().forEach(objects -> {
            int counter = 0;
            BookingSituationDTO dtoObject = new BookingSituationDTO();
            dtoObject.setDayOfWeek(convertObjectToString(objects[counter++]));
            if (isBusinessView) {
                dtoObject.setBusinessViewName(convertObjectToString(objects[counter++]));
                if (dtoObject.getBusinessViewName() != null)
                    reportCriteria.getBusinessViewsNames().add(dtoObject.getBusinessViewName());
            } else if (isForecastGroup) {
                dtoObject.setForecastGroupName(convertObjectToString(objects[counter++]));
                if (dtoObject.getForecastGroupName() != null)
                    reportCriteria.getForecastGroupsNames().add(dtoObject.getForecastGroupName());
            }
            if (!"TOTAL".equalsIgnoreCase(dtoObject.getDayOfWeek())) {
                Object occupancyDate = objects[counter++];
                if (occupancyDate != null)
                    dtoObject.setDayOfArrivalAnalysis(new LocalDate(ScheduledReportUtils.convertDateFormat(occupancyDate.toString(), YYYY_MM_DD)));
                Object comparisonDate = objects[counter++];
                if (comparisonDate != null)
                    dtoObject.setDayOfArrivalComparison(new LocalDate(ScheduledReportUtils.convertDateFormat(comparisonDate.toString(), YYYY_MM_DD)));
            } else {
                counter += 2;
            }
            dtoObject.setOccupancyOnBooksAnalysis(ScheduledReportUtils.createIntegerFromObject(objects[counter++]));
            dtoObject.setOccupancyOnBooksComparison(ScheduledReportUtils.createIntegerFromObject(objects[counter++]));
            dtoObject.setOccupancyOnBooksVariance(ScheduledReportUtils.createIntegerFromObject(objects[counter++]));
            dtoObject.setRevenuOnBooksAnalysis(ScheduledReportUtils.createDoubleFromObject(objects[counter++]));
            dtoObject.setRevenuOnBooksComparison(ScheduledReportUtils.createDoubleFromObject(objects[counter++]));
            dtoObject.setRevenuOnBooksVariance(ScheduledReportUtils.createDoubleFromObject(objects[counter++]));
            dtoObject.setAdrOnBooksAnalysis(ScheduledReportUtils.createDoubleFromObject(objects[counter++]));
            dtoObject.setAdrOnBooksComparison(ScheduledReportUtils.createDoubleFromObject(objects[counter++]));
            dtoObject.setAdrOnBooksVariance(ScheduledReportUtils.createDoubleFromObject(objects[counter++]));

            if (!isBusinessView && !isForecastGroup && !isTransient && !isGroup) {
                dtoObject.setRevParOnBooksAnalysis(ScheduledReportUtils.createDoubleFromObject(objects[counter++]));
                dtoObject.setRevParOnBooksComparison(ScheduledReportUtils.createDoubleFromObject(objects[counter++]));
                dtoObject.setRevParOnBooksVariance(ScheduledReportUtils.createDoubleFromObject(objects[counter++]));
            }
            if (isSpecialEvent) {
                dtoObject.setSpecialEventAnalysis(convertObjectToString(objects[counter++]));
                dtoObject.setSpecialEventComparison(convertObjectToString(objects[counter++]));
            }
            dataList.add(dtoObject);
        });
        return dataList;
    }

    private List<Map<String, Object>> createDataMap(int totalCategories, List<BookingSituationDTO> rowList) {
        if (CollectionUtils.isEmpty(rowList)) return Collections.EMPTY_LIST;
        Map<String, List<BookingSituationDTO>> rowMap = new LinkedHashMap<String, List<BookingSituationDTO>>(rowList.size() / totalCategories);
        for (BookingSituationDTO dtoObject : rowList) {
            String keyDate = dtoObject.getDayOfArrivalAnalysis().toString();
            List<BookingSituationDTO> list = rowMap.get(keyDate);
            if (list == null) {
                list = new ArrayList<>(totalCategories);
                rowMap.put(keyDate, list);
            }
            list.add(dtoObject);
        }
        List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>(rowMap.size());
        int counter;
        for (List<BookingSituationDTO> dtoList : rowMap.values()) {
            counter = 0;
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataList.add(dataMap);
            for (BookingSituationDTO dtoObject : dtoList) {
                if (counter == 0) {
                    dataMap.put("dayOfArrivalAnalysis", dtoObject.getDayOfArrivalAnalysis());
                    dataMap.put("dayOfArrivalComparison", dtoObject.getDayOfArrivalComparison());
                    dataMap.put("dayOfWeek", dtoObject.getDayOfWeek());
                    dataMap.put("specialEventAnalysis", dtoObject.getSpecialEventAnalysis());
                    dataMap.put("specialEventComparison", dtoObject.getSpecialEventComparison());
                }
                dataMap.put("occupancyOnBooksAnalysis" + counter, dtoObject.getOccupancyOnBooksAnalysis());
                dataMap.put("occupancyOnBooksComparison" + counter, dtoObject.getOccupancyOnBooksComparison());
                dataMap.put("occupancyOnBooksVariance" + counter, dtoObject.getOccupancyOnBooksVariance());
                dataMap.put("revenuOnBooksAnalysis" + counter, dtoObject.getRevenuOnBooksAnalysis());
                dataMap.put("revenuOnBooksComparison" + counter, dtoObject.getRevenuOnBooksComparison());
                dataMap.put("revenuOnBooksVariance" + counter, dtoObject.getRevenuOnBooksVariance());
                dataMap.put("adrOnBooksAnalysis" + counter, dtoObject.getAdrOnBooksAnalysis());
                dataMap.put("adrOnBooksComparison" + counter, dtoObject.getAdrOnBooksComparison());
                dataMap.put("adrOnBooksVariance" + counter, dtoObject.getAdrOnBooksVariance());
                dataMap.put("revParOnBooksAnalysis" + counter, dtoObject.getRevParOnBooksAnalysis());
                dataMap.put("revParOnBooksComparison" + counter, dtoObject.getRevenuOnBooksComparison());
                dataMap.put("revParOnBooksVariance" + counter, dtoObject.getRevParOnBooksVariance());
                counter++;
            }
        }
        return dataList;
    }

    private String convertObjectToString(Object value) {
        return value == null ? null : value.toString();
    }

    @SuppressWarnings("unchecked")
    @Override
    protected ScheduledReportData retrieveData(ScheduledReport<BookingSituationReportCriteria> scheduledReport) {

        BookingSituationReportCriteria reportCriteria = scheduledReport.getReportCriteria();

        boolean isHotel = reportCriteria.isHotelChecked();
        boolean isTransient = reportCriteria.isTransientChecked();
        boolean isGroup = reportCriteria.isGroupChecked();
        boolean isForecastGroup = reportCriteria.isFgChecked();
        boolean isBusinessView = reportCriteria.isBvChecked();
        String businessType = getBusinessType(isTransient, isGroup);

        String propertyId = scheduledReport.getReportCriteria().getPropertyId().toString();
        String businessViews = StringUtils.join(scheduledReport.getReportCriteria().getBusinessViews(), ",");
        String forecastGroups = StringUtils.join(scheduledReport.getReportCriteria().getForecastGroups(), ",");
        String analysisStartDate = reportCriteria.getAnalysisStartDate().toString();
        String analysisEndDate = reportCriteria.getAnalysisEndDate().toString();
        String analysisAsOfDate = reportCriteria.getAnalysisAsOfDate().toString();
        String comparisonStartDate = reportCriteria.getComparisonStartDate().toString();
        String comparisonEndDate = reportCriteria.getComparisonEndDate().toString();
        String comparisonAsOfDate = reportCriteria.getComparisonAsOfDate().toString();
        String rolling = scheduledReport.getReportCriteria().getRollingDate();
        String rollingAnalysisStartDate = scheduledReport.getReportCriteria().getRollingAnalysisStartDate();
        String rollingAnalysisEndDate = scheduledReport.getReportCriteria().getRollingAnalysisEndDate();
        String rollingAnalysisBusinessDate = scheduledReport.getReportCriteria().getRollingAnalysisBusinessDate();
        String rollingComparisonStartDate = scheduledReport.getReportCriteria().getRollingComparisonStartDate();
        String rollingComparisonBusinessDate = scheduledReport.getReportCriteria().getRollingComparisonBusinessDate();
        Integer usePhysicalCapacity = Boolean.parseBoolean(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.ENABLE_PHYSICAL_CAPACITY_CONSIDERATION.value()))
                ? 1 : 0;
        List<BookingSituationDTO> bookingSituationDTOs = new ArrayList<>();

        List<Object[]> reportData = null;

        if (isBusinessView) {
            reportData = tenantCrudService.findByNativeQuery("exec usp_businessView_booking_situation_single_property_daily_report " +
                    propertyId + ",'" + businessViews + "'," +
                    "'" + analysisStartDate + "','" + analysisEndDate + "','" + analysisAsOfDate + "'," +
                    "'" + comparisonStartDate + "','" + comparisonEndDate + "','" + comparisonAsOfDate + "'," +
                    "" + rolling + ",'" + rollingAnalysisStartDate + "','" + rollingAnalysisEndDate + "','" +
                    rollingAnalysisBusinessDate + "','" + rollingComparisonStartDate + "','" + rollingComparisonBusinessDate + "'");

        } else if (isForecastGroup) { // exec dbo.usp_forecastGroup_booking_situation_single_property_daily_report

            reportData = tenantCrudService.findByNativeQuery("exec dbo.usp_forecastGroup_booking_situation_single_property_daily_report " +
                    propertyId + ",'" + forecastGroups + "'," +
                    "'" + analysisStartDate + "','" + analysisEndDate + "','" + analysisAsOfDate + "'," +
                    "'" + comparisonStartDate + "','" + comparisonEndDate + "','" + comparisonAsOfDate + "'," +
                    "" + rolling + ",'" + rollingAnalysisStartDate + "','" + rollingAnalysisEndDate + "','" +
                    rollingAnalysisBusinessDate + "','" + rollingComparisonStartDate + "','" + rollingComparisonBusinessDate + "' ");

        } else if (isHotel) {

            reportData = tenantCrudService.findByNativeQuery("exec dbo.usp_hotel_booking_situation_single_property_daily_report " +
                    propertyId + "," +
                    "'" + analysisStartDate + "','" + analysisEndDate + "','" + analysisAsOfDate + "'," +
                    "'" + comparisonStartDate + "','" + comparisonEndDate + "','" + comparisonAsOfDate + "'," +
                    "" + rolling + ",'" + rollingAnalysisStartDate + "','" + rollingAnalysisEndDate + "','" +
                    rollingAnalysisBusinessDate + "','" + rollingComparisonStartDate + "','" + rollingComparisonBusinessDate + "','" + usePhysicalCapacity + "' ");

        } else if (isGroup || isTransient) {
            reportData = tenantCrudService.findByNativeQuery("exec dbo.usp_businessType_booking_situation_single_property_daily_report " +
                    propertyId + "," + "'" + businessType + "'," +
                    "'" + analysisStartDate + "','" + analysisEndDate + "','" + analysisAsOfDate + "'," +
                    "'" + comparisonStartDate + "','" + comparisonEndDate + "','" + comparisonAsOfDate + "'," +
                    "" + rolling + ",'" + rollingAnalysisStartDate + "','" + rollingAnalysisEndDate + "','" +
                    rollingAnalysisBusinessDate + "','" + rollingComparisonStartDate + "','" + rollingComparisonBusinessDate + "' ");

        }
        ScheduledReportSheet sheet = null;
        String title = getReportTitle(scheduledReport);
        if (reportData != null) {
            List<?> dataList = getBookingSituationDTOsFromResultList(reportData, isBusinessView, isForecastGroup, isTransient, isGroup, isHotel, scheduledReport);
            sheet = new ScheduledReportSheet(title, dataList, BookingSituationDTO.class);
        } else {
            sheet = new ScheduledReportSheet(title, Collections.emptyList(), BookingSituationDTO.class);
        }
        populateReportCriteria(reportCriteria);
        List<ScheduledReportSheet> sheetList = new LinkedList<ScheduledReportSheet>();
        sheetList.add(sheet);
        return new ScheduledReportData(title, sheetList);
    }

    private void populateReportCriteria(BookingSituationReportCriteria reportCriteria) {
        {
            String propertyId = reportCriteria.getPropertyId().toString();
            String userId = reportCriteria.getUserId().toString();
            String baseCurrency = reportCriteria.getCurrency();

            LocalDate startDate = reportCriteria.getAnalysisStartDate();
            LocalDate endDate = reportCriteria.getAnalysisEndDate();
            Integer rolling = new Integer(reportCriteria.getRollingDate());
            String rollingStartDate = reportCriteria.getRollingAnalysisStartDate();
            String rollingEndDate = reportCriteria.getRollingAnalysisEndDate();
            LocalDate analysisStartDate = reportCriteria.getAnalysisStartDate();
            LocalDate analysisEndDate = reportCriteria.getAnalysisEndDate();
            LocalDate analysisAsOfDate = reportCriteria.getAnalysisAsOfDate();
            LocalDate comparisonStartDate = reportCriteria.getComparisonStartDate();
            LocalDate comparisonEndDate = reportCriteria.getComparisonEndDate();
            LocalDate comparisonAsOfDate = reportCriteria.getComparisonAsOfDate();
            String rollingAnalysisStartDate = reportCriteria.getRollingAnalysisStartDate();
            String rollingAnalysisEndDate = reportCriteria.getRollingAnalysisEndDate();
            String rollingAnalysisBusinessDate = reportCriteria.getRollingAnalysisBusinessDate();
            String rollingComparisonStartDate = reportCriteria.getRollingComparisonStartDate();
            String rollingComparisonEndDate = reportCriteria.getRollingComparisonEndDate();
            String rollingComparisonBusinessDate = reportCriteria.getRollingComparisonBusinessDate();

            String sql = "select * from dbo.ufn_get_filter_selection " +
                    "('" + propertyId + "'," + userId + ",'" + baseCurrency + "'," +
                    "'" + rolling + "'," +
                    "'" + startDate + "'," +
                    "'" + endDate + "'," +
                    "'" + analysisStartDate + "'," +
                    "'" + analysisEndDate + "'," +
                    "'" + analysisAsOfDate + "'," +
                    "'" + comparisonStartDate + "'," +
                    "'" + comparisonEndDate + "'," +
                    "'" + comparisonAsOfDate + "'," +
                    "'" + rollingStartDate + "'," +
                    "'" + rollingEndDate + "'," +
                    "'" + rollingAnalysisStartDate + "'," +
                    "'" + rollingAnalysisEndDate + "'," +
                    "'" + rollingAnalysisBusinessDate + "'," +
                    "'" + rollingComparisonStartDate + "'," +
                    "'" + rollingComparisonEndDate + "'," +
                    "'" + rollingComparisonBusinessDate + "')";

            List<Object[]> resultList = tenantCrudService.findByNativeQuery(sql);
            if (resultList != null) {
                Object[] result = resultList.get(0);
                reportCriteria.setPropertyName((String) result[0]);
                reportCriteria.setCreatedBy((String) result[1]);

                reportCriteria.setCreatedOn(new DateTime());

                LocalDate analysisStartDateIn = new LocalDate(result[5]);
                reportCriteria.setAnalysisStartDate(analysisStartDateIn);

                LocalDate analysisEndDateIn = new LocalDate(result[6]);
                reportCriteria.setAnalysisEndDate(analysisEndDateIn);

                LocalDate analysisAsOfDateIn = new LocalDate(result[7]);
                reportCriteria.setAnalysisAsOfDate(analysisAsOfDateIn);

                LocalDate comparisonStartDateIn = new LocalDate(result[8]);
                reportCriteria.setComparisonStartDate(comparisonStartDateIn);

                LocalDate comparisonEndDateIn = new LocalDate(result[9]);
                reportCriteria.setComparisonEndDate(comparisonEndDateIn);

                LocalDate comparisonAsOfDateIn = new LocalDate(result[10]);
                reportCriteria.setComparisonAsOfDate(comparisonAsOfDateIn);
            }
        }
    }

    private String getBusinessType(Boolean isTransient, Boolean isGroup) {
        String businessType = null;

        if (isTransient) {
            businessType = BUSINESS_TYPE_TRANSIENT;
        } else if (isGroup) {
            businessType = BUSINESS_TYPE_GROUP;
        }

        return businessType;
    }
}
