package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductGroup;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;

import javax.inject.Inject;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Component
@Transactional
public class PricingDataProductGroupsDataService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;


    public Boolean loadProductGroupsDataIntoPacman(List<ProductGroup> data) {
        StringBuilder queryToInsertData = new StringBuilder();

        queryToInsertData.append("INSERT INTO [dbo].[Agile_Rates_Product_Group] VALUES ('" + data.get(0).getAgileRatesProductGroup().getName() + "',11403,'2021-07-22 07:22:07.110',11403,'2021-07-22 07:22:07.110');");
        data.forEach(productGroupDTO -> {
            queryToInsertData.append("INSERT INTO [dbo].[Product_Group] VALUES (" + productGroupDTO.getAgileRatesProductGroup().getId() + "," + productGroupDTO.getProduct().getId() + ",11403,'2021-07-22 07:22:07.110',11403,'2021-07-22 07:22:07.110',null);");
        });
        if (!queryToInsertData.toString().isEmpty()) {
            tenantCrudService.executeUpdateByNativeQuery(queryToInsertData.toString());
            return true;
        }
        return false;
    }


    public Boolean deleteData() {

        StringBuilder dataToDelete = new StringBuilder("delete from Product_Group where Product_ID in (1,2) and Created_DTTM = '2021-07-22 07:22:07.110';");
        dataToDelete.append("delete from Agile_Rates_Product_Group where Name = 'Test Group';");
        tenantCrudService.executeUpdateByNativeQuery(dataToDelete.toString());
        return true;
    }

}
