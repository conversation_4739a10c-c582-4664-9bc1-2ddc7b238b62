package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.eventaggregator.SystemComponent;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.webrate.dto.WebrateDefaultChannelDto;
import com.ideas.tetris.pacman.services.webrate.dto.WebrateOverrideChannelDto;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateChannel;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateDefaultChannel;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateOverrideChannel;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import javax.ws.rs.NotFoundException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.webrate.entity.WebrateDefaultChannel.PARAM_ID;
import static com.ideas.tetris.pacman.services.webrate.entity.WebrateDefaultChannel.PARAM_PRODUCT_ID;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Component
@Transactional
public class DefaultReferenceChannelService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Autowired
	private SyncEventAggregatorService syncEventAggregatorService;

    @Autowired
    AlertService alertService;

    private static final Logger LOGGER = Logger.getLogger(DefaultReferenceChannelService.class);

    @SuppressWarnings("unchecked")
    public List<WebrateChannel> getAllChannelsByProperty(Integer propertyId) {
        return crudService.findByNamedQuery(WebrateChannel.BY_STATUS_AND_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).and("statusId", 2).parameters());
    }


    public WebrateDefaultChannel getDefaultChannelByPropertyId(Integer propertyId) {
        return (WebrateDefaultChannel) crudService.findByNamedQuerySingleResult(
                WebrateDefaultChannel.BY_PROPERTY_ID_FOR_BAR_ONLY,
                QueryParameter.with("propertyId", propertyId).parameters());
    }

    public List<WebrateDefaultChannel> getAllDefaultChannelsByPropertyId(Integer propertyId) {
        return crudService.findByNamedQuery(
                WebrateDefaultChannel.ALL_CHANNELS_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<WebrateOverrideChannel> getBarOverrideChannelByPropertyId(Integer propertyId) {
        return crudService.findByNamedQuery(WebrateOverrideChannel.BY_PROPERTY_ID_FOR_BAR_ONLY,
                QueryParameter.with("propertyId", propertyId).parameters());
    }

    public List<WebrateOverrideChannel> getAllOverrideChannelsByPropertyId(Integer propertyId) {
        return crudService.findByNamedQuery(WebrateOverrideChannel.GET_ALL_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());
    }

    public List<WebrateDefaultChannelDto> getAllWebrateDefaultChannels() {
        return getAllDefaultChannelsByPropertyId(PacmanWorkContextHelper.getPropertyId())
                .stream().map(this::convertToDto).collect(Collectors.toList());
    }

    public WebrateDefaultChannelDto getWebrateDefaultChannelById(Integer id) {
        WebrateDefaultChannel webrateDefaultChannel = crudService.findByNamedQuerySingleResult(WebrateDefaultChannel.BY_ID,
                QueryParameter.with(PARAM_ID, id).parameters());

        if (null == webrateDefaultChannel) {
            throw new NotFoundException("No webrate default channel found with id " + id);
        }
        return convertToDto(webrateDefaultChannel);
    }

    public void createWebrateDefaultChannel(WebrateDefaultChannelDto webrateDefaultChannelDto) {
        Integer productID = webrateDefaultChannelDto.getProductId();
        WebrateDefaultChannel webrateDefaultChannel = crudService.findByNamedQuerySingleResult(WebrateDefaultChannel.BY_PRODUCT_ID,
                QueryParameter.with(PARAM_PRODUCT_ID, productID).parameters());

        saveDefaultChannel(convertToEntity(webrateDefaultChannelDto, webrateDefaultChannel));
    }

    private WebrateDefaultChannelDto convertToDto(WebrateDefaultChannel webrateDefaultChannel) {
        WebrateDefaultChannelDto dto = new WebrateDefaultChannelDto();
        dto.setWebrateDefaultChannelId(webrateDefaultChannel.getId());
        dto.setMondayChannelId(webrateDefaultChannel.getWebrateChannelMon().getId());
        dto.setTuesdayChannelId(webrateDefaultChannel.getWebrateChannelTues().getId());
        dto.setWednesdayChannelId(webrateDefaultChannel.getWebrateChannelWed().getId());
        dto.setThursdayChannelId(webrateDefaultChannel.getWebrateChannelThurs().getId());
        dto.setFridayChannelId(webrateDefaultChannel.getWebrateChannelFri().getId());
        dto.setSaturdayChannelId(webrateDefaultChannel.getWebrateChannelSat().getId());
        dto.setSundayChannelId(webrateDefaultChannel.getWebrateChannelSun().getId());
        dto.setCreatedByUserId(webrateDefaultChannel.getCreatedByUserId());
        dto.setProductId(webrateDefaultChannel.getProductID());
        dto.setPropertyId(webrateDefaultChannel.getPropertyId());
        dto.setCreatedDttm(webrateDefaultChannel.getCreateDate());
        dto.setLastUpdatedByUserId(webrateDefaultChannel.getLastUpdatedByUserId());
        dto.setLastUpdatedDttm(webrateDefaultChannel.getLastUpdatedDate());
        return dto;
    }

    private WebrateDefaultChannel convertToEntity(WebrateDefaultChannelDto dto, WebrateDefaultChannel webrateDefaultChannel) {
        if (null == webrateDefaultChannel) {
            webrateDefaultChannel = new WebrateDefaultChannel();
        }

        Map<Integer, WebrateChannel> webrateChannelMap = getWebrateChannelMap();
        webrateDefaultChannel.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        webrateDefaultChannel.setProductID(dto.getProductId());
        webrateDefaultChannel.setWebrateChannelMon(webrateChannelMap.get(dto.getMondayChannelId()));
        webrateDefaultChannel.setWebrateChannelTues(webrateChannelMap.get(dto.getTuesdayChannelId()));
        webrateDefaultChannel.setWebrateChannelWed(webrateChannelMap.get(dto.getWednesdayChannelId()));
        webrateDefaultChannel.setWebrateChannelThurs(webrateChannelMap.get(dto.getThursdayChannelId()));
        webrateDefaultChannel.setWebrateChannelFri(webrateChannelMap.get(dto.getFridayChannelId()));
        webrateDefaultChannel.setWebrateChannelSat(webrateChannelMap.get(dto.getSaturdayChannelId()));
        webrateDefaultChannel.setWebrateChannelSun(webrateChannelMap.get(dto.getSundayChannelId()));
        return webrateDefaultChannel;
    }

    public void deleteWebrateDefaultChannelById(Integer id) {
        crudService.executeUpdateByNamedQuery(WebrateDefaultChannel.DELETE_BY_ID, QueryParameter.with(PARAM_ID, id).parameters());
    }

    public Map<Integer, WebrateChannel> getWebrateChannelMap() {
        List<WebrateChannel> webrateChannels = crudService.findByNamedQuery(WebrateChannel.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());

        Map<Integer, WebrateChannel> webrateChannelMap = new HashMap<>();

        for (WebrateChannel webrateChannel : webrateChannels) {
            webrateChannelMap.putIfAbsent(webrateChannel.getId(), webrateChannel);
        }

        return webrateChannelMap;
    }

    public boolean saveDefaultChannel(WebrateDefaultChannel defaultChannel) {
        // Determine if a sync is required, if so dirty the webrate config flag
        if (isSyncRequired(defaultChannel) && !syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
        }

        crudService.save(defaultChannel);

        return true;
    }

    public boolean saveDefaultChannels(List<WebrateDefaultChannel> defaultChannels) {
        // Determine if a sync is required, if so dirty the webrate config flag
        if (isSyncRequired(defaultChannels) && !syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
        }

        crudService.save(defaultChannels);

        return true;
    }

    public boolean isSyncRequired(WebrateDefaultChannel webrateDefaultChannel) {
        // A new WebrateDefaultChannel required a sync
        if (webrateDefaultChannel.getId() == null) {
            return true;
        }

        WebrateDefaultChannel existingWebrateDefaultChannel = crudService.find(WebrateDefaultChannel.class, webrateDefaultChannel.getId());

        if (existingWebrateDefaultChannel == null) {
            return true;
        }

        // If the previously saved WebrateDefaultChannel is different on any day - a sync is required
        EqualsBuilder equalsBuilder = new EqualsBuilder();
        equalsBuilder.append(webrateDefaultChannel.getWebrateChannelSun(), existingWebrateDefaultChannel.getWebrateChannelSun());
        equalsBuilder.append(webrateDefaultChannel.getWebrateChannelMon(), existingWebrateDefaultChannel.getWebrateChannelMon());
        equalsBuilder.append(webrateDefaultChannel.getWebrateChannelTues(), existingWebrateDefaultChannel.getWebrateChannelTues());
        equalsBuilder.append(webrateDefaultChannel.getWebrateChannelWed(), existingWebrateDefaultChannel.getWebrateChannelWed());
        equalsBuilder.append(webrateDefaultChannel.getWebrateChannelThurs(), existingWebrateDefaultChannel.getWebrateChannelThurs());
        equalsBuilder.append(webrateDefaultChannel.getWebrateChannelFri(), existingWebrateDefaultChannel.getWebrateChannelFri());
        equalsBuilder.append(webrateDefaultChannel.getWebrateChannelSat(), existingWebrateDefaultChannel.getWebrateChannelSat());
        return !equalsBuilder.isEquals();
    }

    public boolean isSyncRequired(List<WebrateDefaultChannel> webrateDefaultChannels) {
        for (WebrateDefaultChannel webrateDefaultChannel : webrateDefaultChannels) {
            // A new WebrateDefaultChannel required a sync
            if (webrateDefaultChannel.getId() == null) {
                return true;
            }

            WebrateDefaultChannel existingWebrateDefaultChannel = crudService.find(WebrateDefaultChannel.class, webrateDefaultChannel.getId());

            if (existingWebrateDefaultChannel == null) {
                return true;
            }

            // If the previously saved WebrateDefaultChannel is different on any day - a sync is required
            EqualsBuilder equalsBuilder = new EqualsBuilder();
            equalsBuilder.append(webrateDefaultChannel.getWebrateChannelSun(), existingWebrateDefaultChannel.getWebrateChannelSun());
            equalsBuilder.append(webrateDefaultChannel.getWebrateChannelMon(), existingWebrateDefaultChannel.getWebrateChannelMon());
            equalsBuilder.append(webrateDefaultChannel.getWebrateChannelTues(), existingWebrateDefaultChannel.getWebrateChannelTues());
            equalsBuilder.append(webrateDefaultChannel.getWebrateChannelWed(), existingWebrateDefaultChannel.getWebrateChannelWed());
            equalsBuilder.append(webrateDefaultChannel.getWebrateChannelThurs(), existingWebrateDefaultChannel.getWebrateChannelThurs());
            equalsBuilder.append(webrateDefaultChannel.getWebrateChannelFri(), existingWebrateDefaultChannel.getWebrateChannelFri());
            equalsBuilder.append(webrateDefaultChannel.getWebrateChannelSat(), existingWebrateDefaultChannel.getWebrateChannelSat());
            if (!equalsBuilder.isEquals()) {
                return true;
            }
        }
        return false;
    }

    public WebrateOverrideChannel saveOverrideChannel(WebrateOverrideChannel overrideChannel, Integer propertyId) {
        if (overrideChannel.getId() == null) {
            overrideChannel.setPropertyId(propertyId);
        }

        boolean syncRequired = isSyncRequired(overrideChannel);

        overrideChannel = crudService.save(overrideChannel);

        if (syncRequired && !syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
        }

        return overrideChannel;
    }

    public boolean saveOverrideChannel(List<WebrateOverrideChannel> overrideChannelList, Integer propertyId) {
        boolean syncRequired = false;
        for (WebrateOverrideChannel overrideChannel : overrideChannelList) {
            if (overrideChannel.getId() == null) {
                overrideChannel.setPropertyId(propertyId);
            }

            if (!syncRequired && isSyncRequired(overrideChannel)) {
                syncRequired = true;
            }

            crudService.save(overrideChannel);
        }

        if (syncRequired && !syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
        }
        return true;
    }

    public boolean isSyncRequired(WebrateOverrideChannel webrateOverrideChannel) {
        // If the override channel is new, a sync is required
        if (webrateOverrideChannel.getId() == null) {
            return true;
        }

        WebrateOverrideChannel existingWebrateOverrideChannel = crudService.find(WebrateOverrideChannel.class, webrateOverrideChannel.getId());

        // If any of the day overrides or start/end dates changed, a sync is required
        EqualsBuilder equalsBuilder = new EqualsBuilder();
        equalsBuilder.append(webrateOverrideChannel.getChannelOverrideStartDT(), existingWebrateOverrideChannel.getChannelOverrideStartDT());
        equalsBuilder.append(webrateOverrideChannel.getChannelOverrideEndDT(), existingWebrateOverrideChannel.getChannelOverrideEndDT());
        equalsBuilder.append(webrateOverrideChannel.getWebrateChannelSun(), existingWebrateOverrideChannel.getWebrateChannelSun());
        equalsBuilder.append(webrateOverrideChannel.getWebrateChannelMon(), existingWebrateOverrideChannel.getWebrateChannelMon());
        equalsBuilder.append(webrateOverrideChannel.getWebrateChannelTues(), existingWebrateOverrideChannel.getWebrateChannelTues());
        equalsBuilder.append(webrateOverrideChannel.getWebrateChannelWed(), existingWebrateOverrideChannel.getWebrateChannelWed());
        equalsBuilder.append(webrateOverrideChannel.getWebrateChannelThurs(), existingWebrateOverrideChannel.getWebrateChannelThurs());
        equalsBuilder.append(webrateOverrideChannel.getWebrateChannelFri(), existingWebrateOverrideChannel.getWebrateChannelFri());
        equalsBuilder.append(webrateOverrideChannel.getWebrateChannelSat(), existingWebrateOverrideChannel.getWebrateChannelSat());
        return !equalsBuilder.isEquals();
    }

    public boolean saveChannelConfigAndOverrideDetails(WebrateDefaultChannel defaultChannel, List<WebrateOverrideChannel> overrideChannelList) {
        boolean saveChannelDetails = false;
        Integer propertyId = PacmanThreadLocalContextHolder.getWorkContext().getPropertyId();

        boolean saveDefaultChannelSetting = saveDefaultChannel(defaultChannel);
        boolean saveOverrideChannelDetails = saveOverrideChannel(overrideChannelList, propertyId);

        if (saveDefaultChannelSetting && saveOverrideChannelDetails) {
            saveChannelDetails = true;
        }
        updateAllNewStatusToActive();
        resolveAlert();
        return saveChannelDetails;
    }

    public boolean deleteOverrideChannel(Integer webrateOverrideChannelId) {
        crudService.delete(WebrateOverrideChannel.class, webrateOverrideChannelId);

        // Deleting an override channel requires a sync
        if (!syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
        }

        return true;
    }

    public void setSyncEventAggregatorService(SyncEventAggregatorService syncEventAggregatorService) {
        this.syncEventAggregatorService = syncEventAggregatorService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void updateAllNewStatusToActive() {
        crudService.getEntityManager().createNamedQuery(WebrateChannel.UPDATE_ALL_NEW_TO_ACTIVE_BY_PROPERTY).setParameter("propertyId", PacmanWorkContextHelper.getPropertyId()).executeUpdate();
    }

    public void resolveAlert() {
        try {
            alertService.resolveAllAlerts(AlertType.NewWebRateChannelFound, PacmanWorkContextHelper.getPropertyId());
        } catch (Exception e) {
            LOGGER.error("Exception occurred when trying to resolve alerts", e);
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "Service threw error when trying to resolve alerts", e);
        }

    }

    public boolean updateOverrideWebrateChannels(List<WebrateOverrideChannelDto> webrateOverrideChannelDtos) {
        webrateOverrideChannelDtos.forEach(webrateOverrideChannelDto -> {
            if (webrateOverrideChannelDto.isShouldDelete()) {
                deleteOverrideChannel(webrateOverrideChannelDto.getId());
            }
        });
        if (CollectionUtils.isNotEmpty(webrateOverrideChannelDtos)) {
            List<WebrateOverrideChannel> toBeCreatedOrModified = new ArrayList<>();
            Map<Integer, WebrateOverrideChannel> existingWebrateOverrideChannelMap = getAllOverrideChannelsByPropertyId(PacmanWorkContextHelper.getPropertyId())
                    .stream()
                    .collect(Collectors.toMap(WebrateOverrideChannel::getId, Function.identity(), (existingWoc, newWoc) -> existingWoc));
            webrateOverrideChannelDtos.stream()
                    .filter(webrateOverrideChannelDto -> !webrateOverrideChannelDto.isShouldDelete())
                    .forEach(woc -> {
                        WebrateOverrideChannel webrateOverrideChannel = existingWebrateOverrideChannelMap.get(woc.getId());
                        if (null == webrateOverrideChannel) {
                            webrateOverrideChannel = new WebrateOverrideChannel();
                        }
                        toBeCreatedOrModified.add(WebrateOverrideChannelDto.mapDtoToEntity(webrateOverrideChannel, woc));
                    });
            if (CollectionUtils.isNotEmpty(toBeCreatedOrModified)) {
                saveOverrideChannel(toBeCreatedOrModified, PacmanWorkContextHelper.getPropertyId());
            }
        }
        return true;
    }
}
