package com.ideas.tetris.pacman.services.upgrade;

import javax.ws.rs.QueryParam;

/**
 * This DTO encapsulates the list of optional criteria that can be used in most
 * app upgrade api calls.  Each parameter is optional and should be able to be
 * combined with other parameters. If there are any restrictions, those should be
 * enforced in the {@link #validate()} method.
 */
public class ApplicationActionCriteria {
    @QueryParam("hostName")
    private String hostName;

    @QueryParam("dbServerName")
    private String dbServerName;

    @QueryParam("sasServerName")
    private String sasServerName;

    @QueryParam("targetPropertyId")
    private Integer propertyId;

    public ApplicationActionCriteria hostName(String hostName) {
        this.hostName = hostName;
        return this;
    }

    public ApplicationActionCriteria propertyId(Integer propertyId) {
        this.propertyId = propertyId;
        return this;
    }

    public ApplicationActionCriteria dbServerName(String dbServerName) {
        this.dbServerName = dbServerName;
        return this;
    }

    public ApplicationActionCriteria sasServerName(String sasServerName) {
        this.sasServerName = sasServerName;
        return this;
    }

    public String getHostName() {
        return hostName;
    }

    public Integer getPropertyId() {
        return propertyId;
    }

    public String getDbServerName() {
        return dbServerName;
    }

    public String getSasServerName() {
        return sasServerName;
    }

    public void setPropertyId(Integer propertyId) {
        this.propertyId = propertyId;
    }

    public void validate() {
        //Reserved for future use
    }
}
