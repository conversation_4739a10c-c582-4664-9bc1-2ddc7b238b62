package com.ideas.tetris.pacman.services.rates;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static java.math.BigDecimal.ONE;
import static java.math.BigDecimal.ZERO;
import static java.util.Objects.requireNonNullElse;

@Data
@Builder
public class RateAmountCalculatorContext {
    private BigDecimal rate;
    private BigDecimal doubleRate;
    private BigDecimal tripleRate;
    private BigDecimal quadRate;
    private BigDecimal singleOccupancy;
    private BigDecimal doubleOccupancy;
    private BigDecimal tripleOccupancy;
    private BigDecimal quadOccupancy;


    public BigDecimal calculateRatesBasedOnOccupancy() {
        BigDecimal revenue = requireNonNullElse(rate, ZERO).multiply(requireNonNullElse(singleOccupancy, ZERO))
                .add(requireNonNullElse(doubleRate, ZERO).multiply(requireNonNullElse(doubleOccupancy, ZERO)))
                .add(requireNonNullElse(tripleRate, ZERO).multiply(requireNonNullElse(tripleOccupancy, ZERO)))
                .add(requireNonNullElse(quadRate, ZERO).multiply(requireNonNullElse(quadOccupancy, ZERO)));

        BigDecimal occupancy = requireNonNullElse(singleOccupancy, ZERO).multiply(rate == null ? ZERO : ONE)
                .add(requireNonNullElse(doubleOccupancy, ZERO).multiply(doubleRate == null ? ZERO : ONE))
                .add(requireNonNullElse(tripleOccupancy, ZERO).multiply(tripleRate == null ? ZERO : ONE))
                .add(requireNonNullElse(quadOccupancy, ZERO).multiply(quadRate == null ? ZERO : ONE));

        return occupancy.compareTo(ZERO) > 0 && revenue.compareTo(ZERO) > 0 ?
                revenue.divide(occupancy, 2, RoundingMode.HALF_UP) :
                null;
    }
}
