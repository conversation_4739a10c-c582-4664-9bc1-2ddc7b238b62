package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.datafeed.dto.ExtendedStayCompetitor;
import com.ideas.tetris.pacman.services.extendedstayrateshopping.ExtendedStayCompetitorMappingService;
import com.ideas.tetris.pacman.services.extendedstayrateshopping.entity.ExtendedStayCompetitorMapping;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class ExtendedStayCompetitorConfigurationService {
    @Autowired
    ExtendedStayCompetitorMappingService extendedStayCompetitorMappingService;

    public List<ExtendedStayCompetitor> getExtendedStayCompetitorConfiguration() {
        List<ExtendedStayCompetitor> extendedStayCompetitorList = new ArrayList<>();
        List<Object[]> competitorList = extendedStayCompetitorMappingService.allExtendedStayCompetitors();
        for (Object[] webrateCompetitorArray : competitorList) {
            if (webrateCompetitorArray[0] != null) {
                ExtendedStayCompetitorMapping extendedStayCompetitorMapping = (ExtendedStayCompetitorMapping) webrateCompetitorArray[0];
                if (extendedStayCompetitorMapping.getUseExtendedStay()) {
                    extendedStayCompetitorList.add(new ExtendedStayCompetitor(webrateCompetitorArray[2].toString(), extendedStayCompetitorMapping.getCompetitorInfluence()));
                }
            }
        }
        return extendedStayCompetitorList;
    }
}
