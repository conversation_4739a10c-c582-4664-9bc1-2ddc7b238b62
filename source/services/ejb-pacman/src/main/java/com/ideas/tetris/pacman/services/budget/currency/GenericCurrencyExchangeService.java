package com.ideas.tetris.pacman.services.budget.currency;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.budget.dto.CurrencyExchangeDTO;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.rest.mapper.NGIServiceType;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.services.ngi.NGIUrlLookUpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriTemplate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.platform.common.errorhandling.ErrorCode.UNEXPECTED_ERROR;

@Service
public class GenericCurrencyExchangeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(GenericCurrencyExchangeService.class);

    @Autowired
	private NGIUrlLookUpService ngiUrlLookUpService;


    public List<CurrencyExchangeDTO> getCurrencyExchangeRatesForDateRange(String clientCode, String propertyCode, String revPlanCurrencyCode, String rmsCurrencyCode, LocalDate startDate, LocalDate endDate) {
        List<CurrencyExchangeDTO> currencyExchangeDTOS = new ArrayList<>();

        try {
            LOGGER.info("Fetching the currency conversion factor for date range:{} to {} from NGI", startDate, endDate);
            Map<String, Object> requestParameters = MapBuilder
                    .with(Constants.CLIENT_CODE, clientCode)
                    .and(Constants.PROPERTY_CODE, propertyCode)
                    .and("fromCurrency", revPlanCurrencyCode)
                    .and("toCurrency", rmsCurrencyCode)
                    .and("startDate", startDate.format(DateTimeFormatter.ISO_DATE))
                    .and("endDate", endDate.format(DateTimeFormatter.ISO_DATE)).get();
            String url = buildURL(RestEndpoints.GET_EXCHANGE_RATE_FOR_DATE_RANGE, requestParameters);
            final CurrencyExchangeDTO[] exchangeDTOS = getRevPlanRestClient().getForObject(url, CurrencyExchangeDTO[].class);
            currencyExchangeDTOS = Objects.nonNull(exchangeDTOS) ? Arrays.asList(exchangeDTOS) : currencyExchangeDTOS;
            return currencyExchangeDTOS.stream().map(exchangeRateDto -> updateClientAndPropertyCode(clientCode, propertyCode, exchangeRateDto)).collect(Collectors.toList());
        } catch (Exception ex) {
            throw new TetrisException(UNEXPECTED_ERROR, "Unable to connect to the NGI server", ex);
        }
    }

    private String buildURL(RestEndpoints restEndpoint, Map<String, Object> requestParameters) {
        return getPMSInboundNGIBaseUrl((String) requestParameters.get(Constants.CLIENT_CODE), (String) requestParameters.get(Constants.PROPERTY_CODE))
                + new UriTemplate(restEndpoint.getEndpoint()).expand(requestParameters);
    }

    private String getPMSInboundNGIBaseUrl(String clientCode, String propertyCode) {
        return ngiUrlLookUpService.getNGIBaseUrlFor(clientCode, propertyCode, NGIServiceType.PMS_INBOUND);
    }

    @VisibleForTesting
    protected RestTemplate getRevPlanRestClient() {
        return new RestTemplateBuilder()
                .setConnectTimeout(SystemConfig.getConnectTimeout("revplan"))
                .setReadTimeout(SystemConfig.getReadTimeout("revplan"))
                .build();
    }

    private CurrencyExchangeDTO updateClientAndPropertyCode(String clientCode, String propertyCode, CurrencyExchangeDTO currencyExchangeDto) {
        currencyExchangeDto.setClientCode(clientCode);
        currencyExchangeDto.setPropertyCode(propertyCode);
        return currencyExchangeDto;
    }

}
