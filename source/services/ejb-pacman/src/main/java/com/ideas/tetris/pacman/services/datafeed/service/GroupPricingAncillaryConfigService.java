package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.datafeed.dto.grouppricing.AncillaryConfiguration;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationAncillaryAssignmentSeason;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationAncillaryStream;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationAncillaryService;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by idnekp on 3/10/2016.
 */
@Component
@Transactional
public class GroupPricingAncillaryConfigService {

    private final String categorySeasonal = "Seasonal";
    private final String categoryDefault = "Default";

    @Autowired
    GroupPricingConfigurationAncillaryService groupPricingConfigurationAncillaryService;

    public List<AncillaryConfiguration> getAncillaryConfiguration(Date startDate) {
        List<AncillaryConfiguration> ancillaryConfigurations = new ArrayList<>();
        Map<Integer, GroupPricingConfigurationAncillaryStream> ancillaryStreamMap = new HashMap<>();
        List<GroupPricingConfigurationAncillaryStream> ancillaryStreams = groupPricingConfigurationAncillaryService.getGroupPricingConfigurationAncillaries();
        if (ancillaryStreams != null) {
            ancillaryStreams.forEach(stream -> ancillaryStreamMap.put(stream.getId(), stream));

            List<GroupPricingConfigurationAncillaryAssignmentSeason> ancillaryAssignmentSeasons =
                    groupPricingConfigurationAncillaryService.getSeasons(true);
            ancillaryAssignmentSeasons.stream().filter(ancillaryAssignmentSeason -> ancillaryAssignmentSeason.getEndDate() == null ||
                    ancillaryAssignmentSeason.getEndDate().isAfter(new org.joda.time.LocalDate(startDate).minusDays(1))).forEach(ancillaryAssignmentSeason -> {
                ancillaryAssignmentSeason.getAncillaryAssignments().forEach(ancillaryAssignment -> {
                    AncillaryConfiguration ancillaryConfiguration = new AncillaryConfiguration();
                    ancillaryConfiguration.setRevenueStreamName(ancillaryAssignment.getGroupPricingConfigurationAncillaryStream().getRevenueStream());
                    ancillaryConfiguration.setProfitPercentage(ancillaryAssignment.getGroupPricingConfigurationAncillaryStream().getProfitPercentage());
                    ancillaryConfiguration.setAncillaryCategory(categoryDefault);
                    if (ancillaryAssignment.getGroupPricingConfigurationAncillaryAssignmentSeason().getStartDate() != null) {
                        ancillaryConfiguration.setStartDate(ancillaryAssignment.getGroupPricingConfigurationAncillaryAssignmentSeason().getStartDate().toDate());
                        ancillaryConfiguration.setEndDate(ancillaryAssignment.getGroupPricingConfigurationAncillaryAssignmentSeason().getEndDate().toDate());
                        ancillaryConfiguration.setAncillaryCategory(categorySeasonal);
                    }
                    ancillaryConfiguration.setMarketSegmentCode(ancillaryAssignment.getMarketSegment().getCode());
                    ancillaryConfiguration.setRevenuePerRoomNight(ancillaryAssignment.getRevenue());
                    ancillaryConfigurations.add(ancillaryConfiguration);
                    ancillaryStreamMap.remove(ancillaryAssignment.getGroupPricingConfigurationAncillaryStream().getId());
                });
            });

            ancillaryStreamMap.values().forEach(stream -> {
                AncillaryConfiguration ancillaryConfiguration = new AncillaryConfiguration();
                ancillaryConfiguration.setRevenueStreamName(stream.getRevenueStream());
                ancillaryConfiguration.setProfitPercentage(stream.getProfitPercentage());
                ancillaryConfigurations.add(ancillaryConfiguration);
            });
        }
        return ancillaryConfigurations;
    }
}
