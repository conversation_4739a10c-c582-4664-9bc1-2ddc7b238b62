package com.ideas.tetris.pacman.services.discover.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.annotations.VisibleForTesting;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.discover.DiscoverConstants;
import com.ideas.tetris.pacman.services.discover.entity.DiscoverUser;
import com.ideas.tetris.pacman.services.discover.rest.DiscoverRestClient;
import com.ideas.tetris.pacman.services.discover.rest.DiscoverRestClientV3;
import com.ideas.tetris.pacman.services.discover.rest.DiscoverRestEndPoints;
import com.ideas.tetris.pacman.services.email.EmailService;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.util.string.EmailUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.isDiscoverLMSV3APIEnabled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class DiscoverService {
    private static final Logger LOGGER = Logger.getLogger(DiscoverService.class);
    private static final String CHANGE_ME = "ChangeMe!#";
    private static final int MAX = 99;
    private static final int MIN = 10;
    private final ObjectMapper mapper = new ObjectMapper();
    private static String UPDATE_LEARNING_ACCESS_OF_INACTIVE_USERS_BY_CLIENT_CODE = "update Users set learning_access = :grantOrRevokeAccess where status_id <> 1 and client_code = :clientCode";

    @Autowired
	private DiscoverRestClient discoverRestClient;
    @Autowired
	private UserService userService;
    @Autowired
	private EmailService emailService;
    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;
    @Autowired
    PacmanConfigParamsService configParamsService;
    @Autowired
    DiscoverRestClientV3 discoverRestClientV3;


    public Map<String, List<LDAPUser>> grantOrRevokeDiscoverAccessOfUsersForClient(String client, boolean grantAccess, boolean inactiveToo) {
        Set<LDAPUser> activeLDAPUsers = userService.listLdapUsersForClient(client);
        if (CollectionUtils.isEmpty(activeLDAPUsers)) {
            return new HashMap<>();
        }
        return grantOrInvokeDiscoverAccess(client, activeLDAPUsers, grantAccess, inactiveToo);
    }


    public Map<String, List<LDAPUser>> grantOrRevokeDiscoverAccessOfUsers(boolean grantAccess, boolean inactiveToo, List<String> usersEmail) {
        Set<LDAPUser> activeLDAPUsers = userService.getGlobalUsersByEmailIds(usersEmail);
        if (CollectionUtils.isEmpty(activeLDAPUsers)) {
            return new HashMap<>();
        }
        return grantOrInvokeDiscoverAccess(null, activeLDAPUsers, grantAccess, inactiveToo);
    }

    public Map<String, List<LDAPUser>> grantOrInvokeDiscoverAccessOfLDAPUsers(Set<LDAPUser> users, boolean grantAccess) {
        return grantOrInvokeDiscoverAccess(null, users, grantAccess, false);
    }

    private Map<String, List<LDAPUser>> grantOrInvokeDiscoverAccess(String clientCode, Set<LDAPUser> activeLDAPUsers, boolean grantAccess, boolean inactiveToo) {
        List<LDAPUser> usersWhoCouldNotBeGivenAccess = new ArrayList<>();
        List<LDAPUser> usersWhoAreGivenAccess = new ArrayList<>();
        Map<String, List<LDAPUser>> response = new HashMap<>();
        for (LDAPUser ldapUser : activeLDAPUsers) {
            try {
                if (isIDeaSUser(ldapUser) || isSASUser(ldapUser)) {
                    usersWhoAreGivenAccess.add(ldapUser);
                    continue;
                }
                ldapUser.setHasLearningAccess(grantAccess);
                createOrUpdateDiscoverUser(ldapUser);
                userService.update(ldapUser.getUid(), ldapUser);
                usersWhoAreGivenAccess.add(ldapUser);
            } catch (Exception ex) {
                LOGGER.error("Error while granting / revoking Discover access of user : " + ldapUser.getMail(), ex);
                usersWhoCouldNotBeGivenAccess.add(ldapUser);
            }
        }

        if (inactiveToo && StringUtils.isNotEmpty(clientCode)) {
            globalCrudService.executeUpdateByNativeQuery(UPDATE_LEARNING_ACCESS_OF_INACTIVE_USERS_BY_CLIENT_CODE,
                    QueryParameter.with("grantOrRevokeAccess", grantAccess).and("clientCode", clientCode).parameters());
        }
        response.put(DiscoverConstants.SUCCESS, usersWhoAreGivenAccess);
        response.put(DiscoverConstants.FAILURE, usersWhoCouldNotBeGivenAccess);
        return response;
    }

    private boolean isIDeaSUser(LDAPUser user) {
        return EmailUtil.isEmailMatchForDomain(user.getMail(), "ideas.com");
    }

    private boolean isSASUser(LDAPUser user) {
        return EmailUtil.isEmailMatchForDomain(user.getMail(), "sas.com");
    }

    public void createOrUpdateDiscoverUser(LDAPUser user) {
        if (isIDeaSUser(user) || isSASUser(user)) {
            return;
        }
        DiscoverUser discoverUser = getDiscoverUserByEmail(user.getMail());
        if (Objects.isNull(discoverUser)) {
            if (user.getHasLearningAccess()) {
                createDiscoverUserAndSendEmail(user);
            }
            return;
        }
        if (user.getActive()) {
            if (user.getHasLearningAccess()) {
                updateProductCodeOfDiscoverUser(true, discoverUser);
                updateActivationStatusOfDiscoverUser(true, discoverUser);
            } else {
                if (discoverUser.getIsActive()) {
                    updateProductCodeOfDiscoverUser(false, discoverUser);
                    updateActivationStatusOfDiscoverUser(false, discoverUser);
                }
            }
        } else if (discoverUser.getIsActive()) {
            // inactivate discover user.
            updateProductCodeOfDiscoverUser(false, discoverUser);
            updateActivationStatusOfDiscoverUser(false, discoverUser);
        }
    }

    private void updateProductCodeOfDiscoverUser(boolean isActivateG3, DiscoverUser discoverUser) {
        if (isActivateG3) {
            updateDiscoverUserProductCodeForActivation(discoverUser);
        } else if (discoverUser.getProduct() != null && discoverUser.getProduct().contains(DiscoverConstants.G3_PRODUCT_NAME)) {
            updateDiscoverUserProductCodeForDeactivation(discoverUser, DiscoverConstants.G3_PRODUCT_NAME);
        }
    }

    private void updateDiscoverUserProductCodeForActivation(DiscoverUser discoverUser) {
        String product = discoverUser.getProduct();
        if (StringUtils.isEmpty(product) || "null".equalsIgnoreCase(product)) {
            discoverUser.setProduct(DiscoverConstants.G3_PRODUCT_NAME);
        } else if (!product.contains(DiscoverConstants.G3_PRODUCT_NAME)) {
            if (product.contains(DiscoverConstants.REVPLAN_PRODUCT_NAME)) {
                discoverUser.setProduct(generateProductStringWithG3AtSecondToLastPosition(discoverUser));
            } else {
                discoverUser.setProduct(product + DiscoverConstants.PRODUCT_SEPARATOR + DiscoverConstants.G3_PRODUCT_NAME);
            }
        }
    }

    private String generateProductStringWithG3AtSecondToLastPosition(DiscoverUser discoverUser) {
        String product = discoverUser.getProduct();
        product = product.replace(DiscoverConstants.REVPLAN_PRODUCT_NAME, DiscoverConstants.G3_PRODUCT_NAME);
        product = product + DiscoverConstants.PRODUCT_SEPARATOR + DiscoverConstants.REVPLAN_PRODUCT_NAME;
        return product;
    }

    private void updateDiscoverUserProductCodeForDeactivation(DiscoverUser discoverUser, String productName) {
        List<String> productList = new ArrayList<>(Arrays.asList(discoverUser.getProduct().split(DiscoverConstants.PRODUCT_SEPARATOR)));
        productList.remove("null");
        if (productList.size() == 1) {
            discoverUser.setIsActive(false);
            discoverUser.setProduct(null);
        } else {
            productList.remove(productName);
            discoverUser.setProduct(String.join(DiscoverConstants.PRODUCT_SEPARATOR, productList));
        }
    }

    private void updateActivationStatusOfDiscoverUser(boolean activate, DiscoverUser discoverUser) {
        if (!activate && StringUtil.isNotEmpty(discoverUser.getProduct())) {
            discoverUser.setIsActive(true);
        } else {
            discoverUser.setIsActive(activate);
        }
        updateActivationStatusOfDiscoverUser(discoverUser);
    }

    private DiscoverUser getDiscoverUserByEmail(String email) {
        Map<String, String> parameters = new HashMap<>();
        parameters.put(DiscoverConstants.USER_EMAIL, email.toLowerCase());
        if (enableLMSV3API()) {
            return discoverRestClientV3.get(DiscoverRestEndPoints.FETCH_SINGLE_USER_BASED_ON_EMAIL_V3, parameters);
        }
        return discoverRestClient.get(DiscoverRestEndPoints.FETCH_SINGLE_USER_BASED_ON_EMAIL, parameters);
    }

    private void updateActivationStatusOfDiscoverUser(DiscoverUser discoverUser) {
        try {
            Map<String, String> fieldsToUpdate = new HashMap<>();
            fieldsToUpdate.put(DiscoverConstants.USER_IS_ACTIVE, String.valueOf(discoverUser.getIsActive()));
            fieldsToUpdate.put(DiscoverConstants.USER_PRODUCT, discoverUser.getProduct());
            updateDiscoverUser(discoverUser.getId(), fieldsToUpdate);
        } catch (Exception e) {
            LOGGER.error("Error while updating activation status of discover.error.while.updating.activation.statususer : " + discoverUser.getEmail(), e);
            throw new TetrisException(ErrorCode.DISCOVER_REST_CLIENT_FAILURE, "discover.error.while.updating.activation.status", e);
        }
    }

    private void updateDiscoverUser(String accountId, Map<String, String> fieldsToUpdate) throws JsonProcessingException {
        Map<String, Map<String, String>> finalMap = new HashMap<>();
        finalMap.put(DiscoverConstants.USER, fieldsToUpdate);
        String fieldsToUpdateJson = mapper.writeValueAsString(finalMap);
        if (enableLMSV3API()) {
            discoverRestClientV3.put(DiscoverRestEndPoints.UPDATE_SINGLE_USER_V3, accountId, Entity.entity(fieldsToUpdateJson, MediaType.APPLICATION_JSON_TYPE));
        } else {
            discoverRestClient.put(DiscoverRestEndPoints.UPDATE_SINGLE_USER, accountId, Entity.entity(fieldsToUpdateJson, MediaType.APPLICATION_JSON_TYPE));
        }
    }

    public void createDiscoverUserAndSendEmail(LDAPUser ldapUser) {
        DiscoverUser discoverUser = createDiscoverUserFromLDAPUser(ldapUser);
        String password = CHANGE_ME + calculateRandomNumber();
        discoverUser.setPassword(password);
        DiscoverUser createdUser = createDiscoverUser(discoverUser);
        if (createdUser != null) {
            LOGGER.info("CreatedUser email: " + createdUser.getEmail() + " code: " + createdUser.getCode() + " id: " + createdUser.getId());
        }
        // Notify the users of their password
        try {
            String text = generateEmailText(ldapUser.getCn(), createdUser.getEmail(), password);
            emailService.sendHtml(SystemConfig.getEmailCreateUserFrom(), createdUser.getEmail(),
                    SystemConfig.getEmailCreateDiscoverUserSubject(), text);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.EMAIL_FAILED, "discover.error.occurred.while.sending.mail.to.new.user", e);
        }
    }

    private int calculateRandomNumber() {
        int range = MAX - MIN + 1;
        return (int) (Math.random() * range) + MIN;
    }

    @VisibleForTesting
	public
    Entity<String> createDiscoverUserEntity(DiscoverUser discoverUser) throws JsonProcessingException {
        Map<String, DiscoverUser> discoverUserMap = new HashMap<>();
        discoverUserMap.put(DiscoverConstants.USER, discoverUser);
        String fieldsToUpdateJson;
        if (enableLMSV3API()) {
            ObjectNode jsonNode = mapper.valueToTree(discoverUserMap);
            ((ObjectNode) jsonNode.get(DiscoverConstants.USER)).remove("id");
            ((ObjectNode) jsonNode.get(DiscoverConstants.USER)).remove("full_name");
            fieldsToUpdateJson = mapper.writeValueAsString(jsonNode);
        } else {
            fieldsToUpdateJson = mapper.writeValueAsString(discoverUserMap);
        }
        return Entity.entity(fieldsToUpdateJson, MediaType.APPLICATION_JSON_TYPE);
    }

    private DiscoverUser createDiscoverUser(DiscoverUser discoverUser) {
        Entity discoverUserEntity;
        try {
            discoverUserEntity = createDiscoverUserEntity(discoverUser);
        } catch (JsonProcessingException e) {
            LOGGER.error("Error while converting discover user object to json", e);
            throw new TetrisException(ErrorCode.UNABLE_TO_CONVERT_OBJECT,
                    "Error while converting discover user object to json", e);
        }
        if (enableLMSV3API()) {
            LOGGER.info("Creating user in discover using V3 API");
            return discoverRestClientV3.post(DiscoverRestEndPoints.CREATE_SINGLE_USER_V3, discoverUserEntity);
        }
        LOGGER.info("Creating user in discover using V2 API");
        return discoverRestClient.post(DiscoverRestEndPoints.CREATE_SINGLE_USER, discoverUserEntity);
    }

    private DiscoverUser createDiscoverUserFromLDAPUser(LDAPUser ldapUser) {
        DiscoverUser discoverUser = new DiscoverUser();
        discoverUser.setEmail(ldapUser.getMail().toLowerCase());
        discoverUser.setLogin(ldapUser.getMail().toLowerCase());
        discoverUser.setCode(ldapUser.getMail().toLowerCase());
        discoverUser.setFullName(ldapUser.getCn());
        discoverUser.setFirstName(ldapUser.getGivenName());
        discoverUser.setLastName(ldapUser.getSn());
        discoverUser.setIsActive(true);
        discoverUser.setLocale(Locale.ENGLISH.getLanguage());
        discoverUser.setProduct("G3");
        discoverUser.setAccountName(ldapUser.getClient());
        discoverUser.setIsPassResetRequired(true);
        return discoverUser;
    }

    public String generateEmailText(String commonName, String email, String password) {
        return MessageFormat.format(SystemConfig.getEmailCreateDiscoverUserBody(), commonName, email, password, SystemConfig.getLearningAccessUrl());
    }

    public boolean enableLMSV3API() {
        boolean result = isDiscoverLMSV3APIEnabled();
        LOGGER.info("Using Discover V3 API: " + result);
        return result;
    }

    private Map<String, Object> updateActivationStatus(String email, String product, String status) {
        DiscoverUser discoverUser = generateDiscoverUserForProductStatusUpdate(email, product, status);
        if (discoverUser == null) {
            return Collections.emptyMap();
        }
        updateActivationStatusOfDiscoverUser(discoverUser);
        return generateProductActivationStatusResponseMap(discoverUser);
    }

    private Map<String, Object> generateProductActivationStatusResponseMap(DiscoverUser discoverUser) {
        Map<String, Object> map = new HashMap<>();
        map.put(DiscoverConstants.PRODUCT_NAME_RESPONSE_KEY, discoverUser.getProduct());
        map.put(DiscoverConstants.IS_ACTIVE_RESPONSE_KEY, discoverUser.getIsActive());
        return map;
    }

    private DiscoverUser generateDiscoverUserForProductStatusUpdate(String email, String product, String status) {
        DiscoverUser discoverUser = getDiscoverUserByEmail(email);
        if (discoverUser == null) {
            return null;
        }

        if ("activate".equalsIgnoreCase(status) &&
                (null == discoverUser.getProduct() || !discoverUser.getProduct().contains(product))) {
            if (product.equalsIgnoreCase(DiscoverConstants.G3_PRODUCT_NAME)) {
                updateDiscoverUserProductCodeForActivation(discoverUser);
            } else if (product.equalsIgnoreCase(DiscoverConstants.REVPLAN_PRODUCT_NAME)) {
                discoverUser.setProduct(discoverUser.getProduct() + DiscoverConstants.PRODUCT_SEPARATOR + product);
            } else {
                discoverUser.setProduct(product + DiscoverConstants.PRODUCT_SEPARATOR + discoverUser.getProduct());
            }
            discoverUser.setIsActive(true);
        } else if ("deactivate".equalsIgnoreCase(status) && discoverUser.getProduct().contains(product)) {
            updateDiscoverUserProductCodeForDeactivation(discoverUser, product);
            discoverUser.setIsActive(false);
        }
        return discoverUser;
    }

    @ForTesting


    public String generateV3AccessToken() {
        return discoverRestClientV3.generateAccessToken();
    }

    @ForTesting


    public Map<String, Object> updateActivationStatusForRequestedUser(String email,
                                                                      String product,
                                                                      String status) {
        if (StringUtils.isEmpty(email) || StringUtils.isEmpty(product) || StringUtils.isEmpty(status)) {
            return Collections.emptyMap();
        }
        return updateActivationStatus(email, product, status);
    }
}
