package com.ideas.tetris.pacman.services.extract;

import com.ideas.tetris.platform.services.daoandentities.entity.Property;

import java.io.File;
import java.io.FileFilter;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class CrsFileUtil {

    private CrsFileUtil() {
        throw new IllegalStateException("Utility class");
    }


    public static String buildPropertyExtractPath(String rootDir, Property property) {
        return rootDir + File.separator +
                property.getClient().getCode() + File.separator + property.getCode();
    }

    public static List<File> applyFilter(List<File> s3Resources, FileFilter fileFilter) {
        return s3Resources.stream().filter(getPredicate(fileFilter)).collect(Collectors.toList());
    }

    private static Predicate<File> getPredicate(FileFilter fileFilter) {
        return fileFilter != null
                ? fileFilter::accept
                : file -> true;
    }
}
