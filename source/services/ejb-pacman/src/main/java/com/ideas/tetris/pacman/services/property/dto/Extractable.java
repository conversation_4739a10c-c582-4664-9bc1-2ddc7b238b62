package com.ideas.tetris.pacman.services.property.dto;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;

import org.springframework.aop.SpringProxy;
public interface Extractable extends SpringProxy {
    List<File> getIncomingExtracts();

    List<File> getArchivedExtracts();

    void copyIncomingExtractsToDirectory(Date startDate, Date endDate, File directory) throws IOException;

    void copyArchivedExtractsToDirectory(Date startDate, Date endDate, File directory) throws IOException;
}
