package com.ideas.tetris.pacman.services.webrate.enums;

import com.ideas.tetris.platform.common.errorhandling.TetrisException;

public enum CompetitiveMarketPositionConstraintEnum {

    NONE(0, "None"),
    ABOVE_ALL_COMPETITORS(1, "Above All Competitors"),
    HIGH_RANGE(2, "High Range"),
    MID_RANGE(4, "Mid Range"),
    LOW_RANGE(2, "Low Range"),
    NOT_LOW(4, "Not Low");
    private int minimumCompetitorsRequired;
    private String webrateRanking;

    public int getMinimumCompetitorsRequired() {
        return minimumCompetitorsRequired;
    }

    public String getWebrateRanking() {
        return webrateRanking;
    }

    CompetitiveMarketPositionConstraintEnum(int minimumCompetitorsRequired, String webrateRanking) {
        this.minimumCompetitorsRequired = minimumCompetitorsRequired;
        this.webrateRanking = webrateRanking;
    }

    public static int forWebrateRanking(String webrateRanking) {
        for (CompetitiveMarketPositionConstraintEnum competitiveMarketPositionConstraintEnum : values()) {
            if (competitiveMarketPositionConstraintEnum.getWebrateRanking().equalsIgnoreCase(webrateRanking)) {
                return competitiveMarketPositionConstraintEnum.getMinimumCompetitorsRequired();
            }
        }
        throw new TetrisException("Invalid value");
    }
}
