package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.client.service.ClientConfigService;
import com.ideas.tetris.pacman.services.datafeed.dto.DataFeedEndpointRequestDTO;
import com.ideas.tetris.pacman.services.datafeed.dto.OnDemandDatafeedDto;
import com.ideas.tetris.pacman.services.datafeed.endpoint.EndpointFrequencyType;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.entity.ExecutionStatus;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ideas.tetris.pacman.common.constants.Constants.INCLUDE_HISTORY_DATA;
import static com.ideas.tetris.pacman.common.constants.Constants.USE_EXTENDED_WINDOW_DECISION;
import static com.ideas.tetris.platform.common.job.JobParameterKey.CLIENT_CODE;
import static com.ideas.tetris.platform.common.job.JobParameterKey.GENERATE_HISTORY_FILES;
import static com.ideas.tetris.platform.common.job.JobParameterKey.INCLUDE_MONTHLY_FILES;
import static com.ideas.tetris.platform.common.job.JobParameterKey.INCLUDE_WEEKLY_FILES;
import static com.ideas.tetris.platform.common.job.JobParameterKey.PROPERTY_CODE;
import static com.ideas.tetris.platform.common.job.JobParameterKey.TIMESTAMP;

@Component
@Transactional
public class OnDemandDatafeedService {

    @Autowired
    protected JobServiceLocal jobServiceLocal;

    @Autowired
    private ClientPropertyCacheService clientPropertyCacheService;

    @Autowired
	private ClientConfigService clientConfigService;
    @Autowired
    DatafeedService datafeedService;
    @Autowired
    DateService dateService;
    @Autowired
	private JobMonitorService jobMonitorService;

    private static final Logger LOGGER = Logger.getLogger(OnDemandDatafeedService.class);

    public void sendDataFeedsFor(OnDemandDatafeedDto datafeedDto) {
        final Map<String, Object> parameters = getParameters(datafeedDto);
        triggerOnDemandDatafeedGenerationJobFor(parameters);
    }

    public Map<String, Object> prepareOnDemandRequestDetailsMap(String clientCode, String propertyCode, boolean generateHistoryFiles, boolean includeWeeklyFiles, boolean includeMonthlyFiles, boolean useExtendedWindowDecision) {
        Date caughtUpDate = dateService.getCaughtUpDate();
        Property property = clientPropertyCacheService.getProperty(clientCode, propertyCode);
        String clientPropertyCode = property.getClientPropertyCode();
        Date datafeedLastSuccessfulRun = jobMonitorService.getMostRecentJobState(property.getId(), JobName.NGIGenerateDatafeedFile.toString(), ExecutionStatus.COMPLETED.name());
        Map<String, List<DataFeedEndpointRequestDTO>> restEndpoints = datafeedService.getPermissibleEndpointsForProperty(getApplicableEndpointFrequencyTypes(includeWeeklyFiles, includeMonthlyFiles), clientCode, true);
        Map<String, Object> parameters = datafeedService.buildParameterMap(caughtUpDate, clientCode, restEndpoints, datafeedLastSuccessfulRun, propertyCode, clientPropertyCode, useExtendedWindowDecision);
        parameters.put(INCLUDE_HISTORY_DATA, generateHistoryFiles);
        return parameters;
    }

    public Set<EndpointFrequencyType> getApplicableEndpointFrequencyTypes(boolean includeWeeklyFiles, boolean includeMonthlyFiles) {
        Set<EndpointFrequencyType> frequencyTypes = new HashSet<>();
        frequencyTypes.add(EndpointFrequencyType.DAILY);
        if (includeWeeklyFiles) {
            frequencyTypes.add(EndpointFrequencyType.WEEKLY);
        }
        if (includeMonthlyFiles) {
            frequencyTypes.add(EndpointFrequencyType.MONTHLY);
        }
        return frequencyTypes;
    }

    public String generateDatafeedOnDemand(String clientCode,
                                           String propertyCode,
                                           boolean generateHistoryFiles,
                                           boolean includeWeeklyFiles,
                                           boolean includeMonthlyFiles) {
        try {
            triggerOnDemandDatafeedGenerationJobFor(getParameterMapFor(clientCode, propertyCode, generateHistoryFiles, includeWeeklyFiles, includeMonthlyFiles));
            final String successMsg = "Job for generating datafeed triggered successfully for Client : " + clientCode + ", Property : " + propertyCode;
            LOGGER.info(successMsg);
            return successMsg;
        } catch (Exception e) {
            final String errorMsg = "Error while generating datafeed for Client : " + clientCode + ", Property : " + propertyCode;
            LOGGER.error(errorMsg, e);
            return errorMsg;
        }
    }

    private Map<String, Object> getParameterMapFor(String clientCode, String propertyCode, boolean generateHistoryFiles, boolean includeWeeklyFiles, boolean includeMonthlyFiles) {
        return MapBuilder.with(CLIENT_CODE, clientCode)
                .and(PROPERTY_CODE, propertyCode)
                .and(GENERATE_HISTORY_FILES, generateHistoryFiles)
                .and(INCLUDE_WEEKLY_FILES, includeWeeklyFiles)
                .and(INCLUDE_MONTHLY_FILES, includeMonthlyFiles)
                .and(TIMESTAMP, getCurrentTimeStamp())
                .get();
    }

    private void triggerOnDemandDatafeedGenerationJobFor(Map<String, Object> parameters) {
        jobServiceLocal.startGuaranteedNewInstance(JobName.NGIGenerateDatafeedOnDemand, parameters);
    }

    private Map<String, Object> getParameters(OnDemandDatafeedDto datafeedDto) {
        return MapBuilder.with(CLIENT_CODE, datafeedDto.getProperty().getClient().getCode())
                .and(PROPERTY_CODE, datafeedDto.getProperty().getCode())
                .and(GENERATE_HISTORY_FILES, datafeedDto.isGenerateHistoryFiles())
                .and(INCLUDE_MONTHLY_FILES, datafeedDto.isIncludeMonthlyFiles())
                .and(INCLUDE_WEEKLY_FILES, datafeedDto.isIncludeWeeklyFiles())
                .and(USE_EXTENDED_WINDOW_DECISION, datafeedDto.isIncludeExtendedWindowData())
                .and(TIMESTAMP, getCurrentTimeStamp())
                .get();
    }

    public List<Property> getPropertyCodes(Client client) {
        return clientPropertyCacheService.getClientProperties(client);
    }

    @ForTesting
    protected long getCurrentTimeStamp() {
        return System.currentTimeMillis();
    }

    public List<Client> getAllClients() {
        return clientConfigService.getAllClientDetails();
    }
}
