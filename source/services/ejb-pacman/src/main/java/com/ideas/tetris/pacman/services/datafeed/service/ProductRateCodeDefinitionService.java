package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.ProductRateShopDefinitionDTO;
import com.ideas.tetris.pacman.services.product.Product;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Transactional
public class ProductRateCodeDefinitionService {

    @Autowired
	private AgileRatesConfigurationService agileRatesConfigurationService;


    public List<ProductRateShopDefinitionDTO> getProductRateShopDefinitions() {
        return getProductRateShopDefinitionDTOS(agileRatesConfigurationService.findSystemDefaultProductAndIndependentProducts());
    }

    private List<ProductRateShopDefinitionDTO> getProductRateShopDefinitionDTOS(final List<Product> products) {
        if (CollectionUtils.isEmpty(products)) {
            return Collections.emptyList();
        }

        final List<ProductRateShopDefinitionDTO> productRateShopDefinitionDTOS = new ArrayList<>(products.size());

        final Optional<Product> primaryProduct = products.stream().filter(Product::isSystemDefault).findFirst();
        primaryProduct.ifPresent(value -> productRateShopDefinitionDTOS.add(getProductRateShopDefinitionDTO(value)));

        productRateShopDefinitionDTOS.addAll(
                products.stream().filter(product -> !product.isSystemDefault())
                        .map(this::getProductRateShopDefinitionDTO)
                        .collect(Collectors.toList())
        );

        return productRateShopDefinitionDTOS;
    }

    private ProductRateShopDefinitionDTO getProductRateShopDefinitionDTO(final Product product) {
        return new ProductRateShopDefinitionDTO(product.getName(), product.getRateShoppingLOSMin(), product.getRateShoppingLOSMax(),
                agileRatesConfigurationService.findWebrateTypeNameByProductId(product.getId()));
    }

}
