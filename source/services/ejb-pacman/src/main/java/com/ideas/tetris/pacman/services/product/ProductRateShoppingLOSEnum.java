package com.ideas.tetris.pacman.services.product;

import com.ideas.tetris.platform.common.entity.enumeration.PersistentEnum;

public enum ProductRateShoppingLOSEnum implements PersistentEnum {
    ALL_LOS(0, "All LOS", "agile.rates.rate.shopping.los.all"),
    CUSTOM_LOS_RANGE(1, "Custom LOS Range", "agile.rates.rate.shopping.los.custom");

    private final int id;
    private final String key;
    private final String captionKey;

    ProductRateShoppingLOSEnum(int id, String key, String captionKey) {
        this.id = id;
        this.key = key;
        this.captionKey = captionKey;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getKey() {
        return key;
    }

    public String getCaptionKey() {
        return captionKey;
    }

    public static ProductRateShoppingLOSEnum valueOfId(int id) {
        for (ProductRateShoppingLOSEnum obj : values()) {
            if (obj.getId() == id) {
                return obj;
            }
        }

        throw new IllegalArgumentException("Id: " + id + " is not a valid Product Rate Shopping LOS Type");
    }
}
