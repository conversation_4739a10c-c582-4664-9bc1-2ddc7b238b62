package com.ideas.tetris.pacman.services.webrate.dto;

import com.ideas.tetris.pacman.common.utils.pojo.Pair;
import com.ideas.tetris.pacman.services.webrate.entity.DynamicCMPCConfigDto;
import com.ideas.tetris.pacman.services.webrate.entity.dcmpc.DcmpcCfg;
import com.ideas.tetris.pacman.services.webrate.entity.dcmpc.DcmpcCfgDetail;
import com.ideas.tetris.pacman.services.webrate.entity.dcmpc.DcmpcCfgMapping;
import com.ideas.tetris.pacman.services.webrate.service.DynamicCMPCService;
import com.ideas.tetris.platform.common.entity.DOW;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class DCMPCDtoToEntityConverter {
    @Autowired
	private DynamicCMPCService dynamicCMPCService;

    @Transactional
    public DcmpcCfg saveDynamicCMPCConfigDetails(DynamicCMPCConfigDto dynamicCMPCConfigDto) {
        Optional<DcmpcCfg> dcmpcCfgOptional = getDcmpcCfg(dynamicCMPCConfigDto);
        updateDcmpcCfgDates(dynamicCMPCConfigDto, dcmpcCfgOptional);
        DcmpcCfg dcmpcCfg = dcmpcCfgOptional.orElse(DcmpcCfg.builder().accomClass(dynamicCMPCConfigDto.getAccomClass()).productId(1).startDate(dynamicCMPCConfigDto.getStartDate()).endDate(dynamicCMPCConfigDto.getEndDate()).build());

        List<DcmpcCfgMapping> dcmpcCfgMappingList = new ArrayList<>();
        populateDcmpcConfigMapping(dcmpcCfgMappingList, dynamicCMPCConfigDto.getSunday_OnB_Thrld_Max_Perc(), dynamicCMPCConfigDto.getSunday_Max_Percentile(), DOW.SUNDAY.getCalendarDOW(), dcmpcCfg, dynamicCMPCConfigDto.getSundayMappingId());
        populateDcmpcConfigMapping(dcmpcCfgMappingList, dynamicCMPCConfigDto.getMonday_OnB_Thrld_Max_Perc(), dynamicCMPCConfigDto.getMonday_Max_Percentile(), DOW.MONDAY.getCalendarDOW(), dcmpcCfg, dynamicCMPCConfigDto.getMondayMappingId());
        populateDcmpcConfigMapping(dcmpcCfgMappingList, dynamicCMPCConfigDto.getTuesday_OnB_Thrld_Max_Perc(), dynamicCMPCConfigDto.getTuesday_Max_Percentile(), DOW.TUESDAY.getCalendarDOW(), dcmpcCfg, dynamicCMPCConfigDto.getTuesdayMappingId());
        populateDcmpcConfigMapping(dcmpcCfgMappingList, dynamicCMPCConfigDto.getWednesday_OnB_Thrld_Max_Perc(), dynamicCMPCConfigDto.getWednesday_Max_Percentile(), DOW.WEDNESDAY.getCalendarDOW(), dcmpcCfg, dynamicCMPCConfigDto.getWednesdayMappingId());
        populateDcmpcConfigMapping(dcmpcCfgMappingList, dynamicCMPCConfigDto.getThursday_OnB_Thrld_Max_Perc(), dynamicCMPCConfigDto.getThursday_Max_Percentile(), DOW.THURSDAY.getCalendarDOW(), dcmpcCfg, dynamicCMPCConfigDto.getThursdayMappingId());
        populateDcmpcConfigMapping(dcmpcCfgMappingList, dynamicCMPCConfigDto.getFriday_OnB_Thrld_Max_Perc(), dynamicCMPCConfigDto.getFriday_Max_Percentile(), DOW.FRIDAY.getCalendarDOW(), dcmpcCfg, dynamicCMPCConfigDto.getFridayMappingId());
        populateDcmpcConfigMapping(dcmpcCfgMappingList, dynamicCMPCConfigDto.getSaturday_OnB_Thrld_Max_Perc(), dynamicCMPCConfigDto.getSaturday_Max_Percentile(), DOW.SATURDAY.getCalendarDOW(), dcmpcCfg, dynamicCMPCConfigDto.getSaturdayMappingId());

        dynamicCMPCService.save(dcmpcCfgMappingList);
        updateMappingIdsOfDynamicCMPCConfig(dynamicCMPCConfigDto, dcmpcCfgMappingList);
        return dcmpcCfg;
    }

    private void updateDcmpcCfgDates(DynamicCMPCConfigDto dynamicCMPCConfigDto, Optional<DcmpcCfg> dcmpcCfgOptional) {
        dcmpcCfgOptional.ifPresent(dcmpcCfg -> {
            Optional.ofNullable(dynamicCMPCConfigDto.getStartDate()).ifPresent(dcmpcCfg::setStartDate);
            Optional.ofNullable(dynamicCMPCConfigDto.getEndDate()).ifPresent(dcmpcCfg::setEndDate);
            dynamicCMPCService.merge(dcmpcCfg);
        });
    }

    private void updateMappingIdsOfDynamicCMPCConfig(DynamicCMPCConfigDto dynamicCMPCConfigDto, List<DcmpcCfgMapping> dcmpcCfgMappingList) {
        dcmpcCfgMappingList.forEach(dcmpcCfgMapping -> {
            populateDynamicCMPCConfig(dynamicCMPCConfigDto, dcmpcCfgMapping.getDowId(), dcmpcCfgMapping.getId(), dcmpcCfgMapping.getDcmpcCfgDetail());
        });
    }

    private Optional<DcmpcCfg> getDcmpcCfg(DynamicCMPCConfigDto dynamicCMPCConfigDto) {
        Optional<DcmpcCfg> dcmpcCfgOptional;
        if (isDefaultDynamicCMPCConfig(dynamicCMPCConfigDto)) {
            dcmpcCfgOptional = Optional.ofNullable(dynamicCMPCService.getDefaultDcmpcCfgByAccomClassIdAndProductId(dynamicCMPCConfigDto.getAccomClass().getId(), 1));
        } else {
            dcmpcCfgOptional = getSeasonalDcmpcCfg(dynamicCMPCConfigDto);
        }
        return dcmpcCfgOptional;
    }

    private Optional<DcmpcCfg> getSeasonalDcmpcCfg(DynamicCMPCConfigDto dynamicCMPCConfigDto) {
        Optional<DcmpcCfg> dcmpcCfgOptional = Optional.empty();
        if (dynamicCMPCConfigDto.getId() != null) {
            Optional<List<DcmpcCfg>> optionalDcmpcCfgs = Optional.ofNullable(dynamicCMPCService.getDcmpcCfg(Stream.of(dynamicCMPCConfigDto.getId()).collect(Collectors.toList())));
            if (optionalDcmpcCfgs.isPresent()) {
                dcmpcCfgOptional = optionalDcmpcCfgs.get().isEmpty() ? Optional.empty() : Optional.ofNullable(optionalDcmpcCfgs.get().get(0));
            }
        } else {
            dcmpcCfgOptional = Optional.ofNullable(dynamicCMPCService.getSeasonalDcmpcCfgByAccomClassIdAndProductId(dynamicCMPCConfigDto.getAccomClass().getId(), 1, dynamicCMPCConfigDto.getStartDate(), dynamicCMPCConfigDto.getEndDate()));
        }
        return dcmpcCfgOptional;
    }

    private static boolean isDefaultDynamicCMPCConfig(DynamicCMPCConfigDto dynamicCMPCConfigDto) {
        return dynamicCMPCConfigDto.getStartDate() == null && dynamicCMPCConfigDto.getEndDate() == null;
    }

    private void populateDcmpcConfigMapping(List<DcmpcCfgMapping> dcmpcCfgMappingList, BigDecimal onBooksThreshold, BigDecimal percentile, int dowId, DcmpcCfg dcmpcCfg, Integer mappingId) {
        removeDcmpcCfgMapping(onBooksThreshold, percentile, dcmpcCfg, mappingId);
        if (onBooksThreshold != null && percentile != null) {
            Optional<DcmpcCfgDetail> dcmpcCfgDetailOptional = Optional.ofNullable(dynamicCMPCService.getDcmpcDetails(onBooksThreshold, percentile));
            dcmpcCfgDetailOptional.ifPresent(dcmpcCfgDetail -> dynamicCMPCService.merge(dcmpcCfgDetail));
            DcmpcCfgDetail dcmpcCfgDetail = dcmpcCfgDetailOptional.orElse(DcmpcCfgDetail.builder().onBooksThreshold(onBooksThreshold).percentile(percentile).build());
            saveDcmpcCfgDetail(dcmpcCfgDetailOptional, dcmpcCfgDetail);
            if (isDcmpcCfgMappingExistsInDB(dowId, dcmpcCfg, dcmpcCfgDetailOptional, dcmpcCfgDetail)) {
                return;
            }
            DcmpcCfgMapping dcmpcCfgMapping = getDcmpcCfgMapping(dowId, dcmpcCfg, mappingId, dcmpcCfgDetail);
            dcmpcCfgMappingList.add(dcmpcCfgMapping);
        }
    }

    private DcmpcCfgMapping getDcmpcCfgMapping(int dowId, DcmpcCfg dcmpcCfg, Integer mappingId, DcmpcCfgDetail dcmpcCfgDetail) {
        DcmpcCfgMapping dcmpcCfgMapping;
        if (mappingId != null) {
            dcmpcCfgMapping = dynamicCMPCService.getDcmpcCfgMapping(mappingId);
            dcmpcCfgMapping.setDcmpcCfg(dcmpcCfg);
            dcmpcCfgMapping.setDcmpcCfgDetail(dcmpcCfgDetail);
            dcmpcCfgMapping.setDowId(dowId);
        } else {
            dcmpcCfgMapping = DcmpcCfgMapping.builder().dcmpcCfg(dcmpcCfg).dcmpcCfgDetail(dcmpcCfgDetail).dowId(dowId).build();
        }
        return dcmpcCfgMapping;
    }

    private boolean isDcmpcCfgMappingExistsInDB(int dowId, DcmpcCfg dcmpcCfg, Optional<DcmpcCfgDetail> dcmpcCfgDetailOptional, DcmpcCfgDetail dcmpcCfgDetail) {
        if (dcmpcCfgDetailOptional.isPresent() && dcmpcCfg.isPersisted()) {
            Optional<DcmpcCfgMapping> dcmpcCfgMappingOptional = Optional.ofNullable(dynamicCMPCService.getDcmpcCfgMapping(dcmpcCfg.getId(), dcmpcCfgDetail.getId(), dowId));
            return dcmpcCfgMappingOptional.isPresent();
        }
        return false;
    }

    private void saveDcmpcCfgDetail(Optional<DcmpcCfgDetail> dcmpcCfgDetailOptional, DcmpcCfgDetail dcmpcCfgDetail) {
        if (dcmpcCfgDetailOptional.isEmpty()) {
            dynamicCMPCService.save(dcmpcCfgDetail);
            dynamicCMPCService.merge(dcmpcCfgDetail);
        }
    }

    private void removeDcmpcCfgMapping(BigDecimal onBooksThreshold, BigDecimal percentile, DcmpcCfg dcmpcCfg, Integer mappingId) {
        if (mappingId != null && onBooksThreshold == null && percentile == null) {
            Optional<DcmpcCfgMapping> dcmpcCfgMappingOptional = Optional.ofNullable(dynamicCMPCService.getDcmpcCfgMapping(mappingId));
            dcmpcCfgMappingOptional.ifPresent(dcmpcCfgMapping -> dynamicCMPCService.deleteDcmpcCfgMappings(Stream.of(dcmpcCfgMapping).collect(Collectors.toList())));
            if (dynamicCMPCService.getDcmpcCfgMappingsByDcmpcCfg(dcmpcCfg).isEmpty()) {
                dynamicCMPCService.deleteDcmpcCfg(dcmpcCfg);
            }
        }
    }

    public List<DynamicCMPCConfigDto> convertToPresentation(List<DcmpcCfgMapping> dcmpcCfgMappingList) {
        List<DynamicCMPCConfigDto> dynamicCMPCConfigDtoList = new ArrayList<>();
        //map of: dcmpcCfg -> map of: dowId -> [(mappingId, DcmpcCfgDetail),..]
        Map<DcmpcCfg, Map<Integer, List<Pair<Integer, DcmpcCfgDetail>>>> dcmpcCfgToDOWIdToThresholdDetailsMap = buildCfgDetailsByDowIdMap(dcmpcCfgMappingList);
        dcmpcCfgToDOWIdToThresholdDetailsMap.forEach((dcmpcCfg, dowToDetailsListMap) -> {
            int sizeOfDtos = dowToDetailsListMap.values().stream().mapToInt(List::size).max().orElse(0);
            List<DynamicCMPCConfigDto> tempList = getDynamicCMPCConfigs(dcmpcCfg, sizeOfDtos);
            processDowToDetailsMap(dowToDetailsListMap, tempList);
            dynamicCMPCConfigDtoList.addAll(tempList);
        });

        return dynamicCMPCConfigDtoList;
    }

    private void processDowToDetailsMap(Map<Integer, List<Pair<Integer, DcmpcCfgDetail>>> dowToDetailsListMap, List<DynamicCMPCConfigDto> tempList) {
        dowToDetailsListMap.forEach((dow, detailsPairList) -> {
            detailsPairList.sort(Comparator.comparing(pair -> pair.getSecond().getOnBooksThreshold()));
            for (int i = 0; i < detailsPairList.size(); i++) {
                populateDynamicCMPCConfig(tempList.get(i), dow, detailsPairList.get(i).getFirst(), detailsPairList.get(i).getSecond());
            }
        });
    }

    private static List<DynamicCMPCConfigDto> getDynamicCMPCConfigs(DcmpcCfg dcmpcCfg, int sizeOfDtos) {
        List<DynamicCMPCConfigDto> tempList = new ArrayList<>();
        for (int i = 0; i < sizeOfDtos; i++) {
            tempList.add(DynamicCMPCConfigDto.builder().id(dcmpcCfg.getId()).accomClass(dcmpcCfg.getAccomClass()).startDate(dcmpcCfg.getStartDate()).endDate(dcmpcCfg.getEndDate()).build());
        }
        return tempList;
    }

    private static Map<DcmpcCfg, Map<Integer, List<Pair<Integer, DcmpcCfgDetail>>>> buildCfgDetailsByDowIdMap(List<DcmpcCfgMapping> dcmpcCfgMappingList) {
        return dcmpcCfgMappingList.stream().collect(Collectors.groupingBy(DcmpcCfgMapping::getDcmpcCfg, Collectors.groupingBy(DcmpcCfgMapping::getDowId, Collectors.mapping(dcmpcCfgMapping -> new Pair<>(dcmpcCfgMapping.getId(), dcmpcCfgMapping.getDcmpcCfgDetail()), Collectors.toList()))));
    }

    private void populateDynamicCMPCConfig(DynamicCMPCConfigDto dynamicCMPCConfigDto, Integer dow, Integer mappingId, DcmpcCfgDetail dcmpcCfgDetail) {
        switch (DOW.valueOf(dow)) {
            case SUNDAY:
                dynamicCMPCConfigDto.setSundayMappingId(mappingId);
                dynamicCMPCConfigDto.setSunday_OnB_Thrld_Max_Perc(dcmpcCfgDetail.getOnBooksThreshold());
                dynamicCMPCConfigDto.setSunday_Max_Percentile(dcmpcCfgDetail.getPercentile());
                break;
            case MONDAY:
                dynamicCMPCConfigDto.setMondayMappingId(mappingId);
                dynamicCMPCConfigDto.setMonday_OnB_Thrld_Max_Perc(dcmpcCfgDetail.getOnBooksThreshold());
                dynamicCMPCConfigDto.setMonday_Max_Percentile(dcmpcCfgDetail.getPercentile());
                break;
            case TUESDAY:
                dynamicCMPCConfigDto.setTuesdayMappingId(mappingId);
                dynamicCMPCConfigDto.setTuesday_OnB_Thrld_Max_Perc(dcmpcCfgDetail.getOnBooksThreshold());
                dynamicCMPCConfigDto.setTuesday_Max_Percentile(dcmpcCfgDetail.getPercentile());
                break;
            case WEDNESDAY:
                dynamicCMPCConfigDto.setWednesdayMappingId(mappingId);
                dynamicCMPCConfigDto.setWednesday_OnB_Thrld_Max_Perc(dcmpcCfgDetail.getOnBooksThreshold());
                dynamicCMPCConfigDto.setWednesday_Max_Percentile(dcmpcCfgDetail.getPercentile());
                break;
            case THURSDAY:
                dynamicCMPCConfigDto.setThursdayMappingId(mappingId);
                dynamicCMPCConfigDto.setThursday_OnB_Thrld_Max_Perc(dcmpcCfgDetail.getOnBooksThreshold());
                dynamicCMPCConfigDto.setThursday_Max_Percentile(dcmpcCfgDetail.getPercentile());
                break;
            case FRIDAY:
                dynamicCMPCConfigDto.setFridayMappingId(mappingId);
                dynamicCMPCConfigDto.setFriday_OnB_Thrld_Max_Perc(dcmpcCfgDetail.getOnBooksThreshold());
                dynamicCMPCConfigDto.setFriday_Max_Percentile(dcmpcCfgDetail.getPercentile());
                break;
            case SATURDAY:
                dynamicCMPCConfigDto.setSaturdayMappingId(mappingId);
                dynamicCMPCConfigDto.setSaturday_OnB_Thrld_Max_Perc(dcmpcCfgDetail.getOnBooksThreshold());
                dynamicCMPCConfigDto.setSaturday_Max_Percentile(dcmpcCfgDetail.getPercentile());
                break;
        }
    }
}
