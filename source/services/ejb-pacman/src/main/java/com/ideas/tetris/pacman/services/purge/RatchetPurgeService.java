package com.ideas.tetris.pacman.services.purge;

import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.formatDate;


@Component
@Transactional
public class RatchetPurgeService {
    public static final String NOT_A_RATCHET_PROPERTY = "Not a Ratchet Property";
    static final int YEARS_TO_RETAIN = 3;
    static final String DELETE_FROM_RATCHET_BOOKS_SQL = "delete from Ratchet.books where dod < '%s'd";
    @Autowired
	private SasDbToolService sasDbToolService;
    @Autowired
	private PropertyService propertyService;
    @Autowired
	private DateService dateService;


    public Integer purgeRatchetDataset() {
        final Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Date businessDate = dateService.getCaughtUpDate();
        final Date purgeDate = DateUtil.addDaysToDate(businessDate, -1 * SystemConfig.getPurgeRatchetDatasetRetainDays());
        final Property property = propertyService.getPropertyById(propertyId);
        final String sql = String.format(DELETE_FROM_RATCHET_BOOKS_SQL, formatDate(purgeDate, DateUtil.DATE_FORMAT_SAS));
        return sasDbToolService.executeUpdate(property.getClient().getCode(), propertyId, property.getCode(), sql);
    }
}