package com.ideas.tetris.pacman.services.forecast.mapper;

import com.ideas.tetris.pacman.services.forecast.dto.ExpectedForecastDetailDTO;
import com.ideas.tetris.platform.common.crudservice.RowMapper;

import java.util.Date;

public class ExpectedForecastDetailRowMapper implements RowMapper<ExpectedForecastDetailDTO> {
    @Override
    public ExpectedForecastDetailDTO mapRow(Object[] row) {

        ExpectedForecastDetailDTO expectedForecastDetailDTO = new ExpectedForecastDetailDTO();
        expectedForecastDetailDTO.setOccupancyDate((Date) row[0]);
        expectedForecastDetailDTO.setActualOnbooks((Integer) row[1]);
        expectedForecastDetailDTO.setExpectedOnbooks((Integer) row[2]);
        expectedForecastDetailDTO.setExpectedOnbooksLowerBound((Integer) row[3]);
        expectedForecastDetailDTO.setExpectedOnbooksUpperBound((Integer) row[4]);
        return expectedForecastDetailDTO;
    }
}
