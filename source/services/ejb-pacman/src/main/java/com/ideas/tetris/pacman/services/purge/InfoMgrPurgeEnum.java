package com.ideas.tetris.pacman.services.purge;

import static com.ideas.tetris.pacman.services.purge.PurgeConstants.DBO;
import static com.ideas.tetris.pacman.services.purge.PurgeConstants.INFO_MGR_INSTANCE_ID;
import static com.ideas.tetris.pacman.services.purge.PurgeConstants.WHERE_FIELD_TO_COMPARE_COMMENTS_INSTANCEID;
import static com.ideas.tetris.pacman.services.purge.PurgeConstants.WHERE_FIELD_TO_COMPARE_INSTANCEID;

public enum InfoMgrPurgeEnum implements Purgable {

    INFO_MGR_COMMENTS("INFO_MGR_COMMENTS", DBO, INFO_MGR_INSTANCE_ID, WHERE_FIELD_TO_COMPARE_COMMENTS_INSTANCEID),
    INFO_MGR_HISTORY("INFO_MGR_HISTORY", DBO, INFO_MGR_INSTANCE_ID, WHERE_FIELD_TO_COMPARE_INSTANCEID),
    INFO_MGR_INSTANCE_STEP_STATE("INFO_MGR_INSTANCE_STEP_STATE", DBO, INFO_MGR_INSTANCE_ID, WHERE_FIELD_TO_COMPARE_INSTANCEID),
    INFO_MGR_INSTANCE("INFO_MGR_INSTANCE", DBO, INFO_MGR_INSTANCE_ID, WHERE_FIELD_TO_COMPARE_INSTANCEID);
    private final String schema;
    private final String tableToDelete;
    private final String tableToCompare;
    private final String fieldToCompare;
    private final String whereClauseTemplate;

    InfoMgrPurgeEnum(String tableName, String schema, String fieldToCompare, String whereClauseTemplate) {
        this.schema = schema;
        this.tableToDelete = this.name();
        this.tableToCompare = tableName;
        this.fieldToCompare = fieldToCompare;
        this.whereClauseTemplate = whereClauseTemplate;
    }

    public String getWhereClauseTemplate() {
        return whereClauseTemplate;
    }

    @Override
    public String getSchema() {
        return schema;
    }

    @Override
    public String getTableToDelete() {
        return tableToDelete;
    }

    @Override
    public String getTableToCompare() {
        return tableToCompare;
    }

    @Override
    public String getFieldToCompare() {
        return fieldToCompare;
    }

    @Override
    public boolean isFailedSilently() {
        return false;
    }

}
