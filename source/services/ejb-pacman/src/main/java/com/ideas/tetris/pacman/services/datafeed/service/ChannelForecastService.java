package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.ChannelForecastDTO;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.ideas.tetris.pacman.common.constants.Constants.END_DATE;
import static com.ideas.tetris.pacman.common.constants.Constants.START_DATE;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.round;
import static org.apache.commons.lang.StringUtils.EMPTY;
import static org.apache.commons.lang.StringUtils.isEmpty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Component
@Transactional
public class ChannelForecastService {

    @Autowired
    DateService dateService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    public List<ChannelForecastDTO> getChannelForecastDtos(DatafeedRequest datafeedRequest) {

        LocalDate startDate = dateService.getCaughtUpJavaLocalDate();
        LocalDate endDate = startDate.plusDays(dateService.getForecastWindowOffsetBDE());

        String query = "SELECT r.Occupancy_DT AS Occupancy_Date \n" +
                "    ,COALESCE(r.Channel, '') AS Channel \n" +
                "    ,COALESCE(r.Source_Booking, '') AS Source  \n" +
                "    ,count(*) AS Onbooks \n" +
                "    ,sum(r.Room_Revenue) AS Onbooks_Revenue \n" +
                "    ,sum(r.Net_Revenue) AS Net_Onbooks_Revenue \n" +
                "    ,sum(r.Room_Revenue_Acquisition_Cost) AS Cost \n" +
                "    ,NULL AS Cost_Fcst \n" +
                "    ,NULL AS Average_Cost_Fcst \n" +
                "    ,NULL AS Occupancy_Fcst \n" +
                "    ,NULL AS Revenue_Fcst \n" +
                "    ,NULL AS Net_Revenue_Fcst \n" +
                "    ,NULL AS ADR_Fcst \n" +
                "    ,NULL AS Net_ADR_Fcst \n" +
                "    ,NULL AS RevPAR_Fcst \n" +
                "    ,NULL AS Net_RevPAR_Fcst \n" +
                "FROM Reservation_Night r \n" +
                "WHERE r.Occupancy_DT = DATEADD(DAY, -1, :startDate) AND r.Number_Adults > 0 \n" +
                "GROUP BY r.Occupancy_DT, COALESCE(r.Channel, ''), COALESCE(r.Source_Booking, '')\n" +
                "UNION\n" +
                "SELECT a.Occupancy_DT AS Occupancy_Date \n" +
                "    ,c.Channel_Name AS Channel \n" +
                "    ,s.Source_Name AS Source \n" +
                "    ,a.Rooms_Sold_Onbooks AS Onbooks \n" +
                "    ,a.Room_Revenue_Onbooks AS Onbooks_Revenue \n" +
                "    ,a.Room_Revenue_Onbooks - a.Channel_Cost_Onbooks AS Net_Onbooks_Revenue \n" +
                "    ,a.Channel_Cost_Onbooks AS Cost \n" +
                "    ,a.Channel_Cost_Fcst AS Cost_Fcst \n" +
                "    ,iif(a.Rooms_Sold_Fcst = 0, 0, a.Channel_Cost_Fcst / a.Rooms_Sold_Fcst) AS Average_Cost_Fcst \n" +
                "    ,cast(a.Rooms_Sold_Fcst as int) AS Occupancy_Fcst \n" +
                "    ,a.Room_Revenue_Fcst + a.Channel_Cost_Fcst AS Revenue_Fcst \n" +
                "    ,a.Room_Revenue_Fcst AS Net_Revenue_Fcst \n" +
                "    ,iif(a.Rooms_Sold_Fcst = 0, 0, (a.Room_Revenue_Fcst + a.Channel_Cost_Fcst) / a.Rooms_Sold_Fcst) AS ADR_Fcst \n" +
                "    ,iif(a.Rooms_Sold_Fcst = 0, 0, a.Room_Revenue_Fcst / a.Rooms_Sold_Fcst) AS Net_ADR_Fcst \n" +
                "    ,iif(ta.Total_Accom_Capacity = 0, 0, (a.Room_Revenue_Fcst + a.Channel_Cost_Fcst) / ta.Total_Accom_Capacity) AS RevPAR_Fcst \n" +
                "    ,iif(ta.Total_Accom_Capacity = 0, 0, a.Room_Revenue_Fcst / ta.Total_Accom_Capacity) AS Net_RevPAR_Fcst \n" +
                "FROM Channel_SrC_Daily_FCST a \n" +
                "INNER JOIN Total_Activity ta ON a.Occupancy_DT = ta.Occupancy_DT \n" +
                "LEFT JOIN Channel_Dmd_Channel c ON a.Channel_Dmd_Channel_ID = c.Channel_Dmd_Channel_ID \n" +
                "LEFT JOIN Channel_Dmd_Source s ON a.Channel_Dmd_Source_ID = s.Channel_Dmd_Source_ID \n" +
                "WHERE a.Occupancy_DT BETWEEN :startDate AND :endDate \n" +
                "ORDER BY Occupancy_Date, Channel, Source \n";

        List<ChannelForecastDTO> result = tenantCrudService.findByNativeQuery(query,
                QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize(), channelForecastDTORowMapper());
        return result != null ? result : Collections.emptyList();
    }

    private RowMapper<ChannelForecastDTO> channelForecastDTORowMapper() {
        return row -> {
            ChannelForecastDTO channelForecastDTO = new ChannelForecastDTO();
            channelForecastDTO.setOccupancyDate((Date) row[0]);
            channelForecastDTO.setChannel((String) row[1]);
            channelForecastDTO.setSource((String) row[2]);
            channelForecastDTO.setOnBooks(getIntegerAt(row, 3));
            channelForecastDTO.setOnBooksRevenue(getBigDecimalAt(row, 4));
            channelForecastDTO.setNetOnBooksRevenue(getBigDecimalAt(row, 5));
            channelForecastDTO.setCost(getBigDecimalAt(row, 6));
            channelForecastDTO.setCostForecast(getBigDecimalAt(row, 7));
            channelForecastDTO.setAverageCostForecast(getBigDecimalAt(row, 8));
            channelForecastDTO.setOccupancyForecast(getIntegerAt(row, 9));
            channelForecastDTO.setRevenueForecast(getBigDecimalAt(row, 10));
            channelForecastDTO.setNetRevenueForecast(getBigDecimalAt(row, 11));
            channelForecastDTO.setAdrForecast(getBigDecimalAt(row, 12));
            channelForecastDTO.setNetADRForecast(getBigDecimalAt(row, 13));
            channelForecastDTO.setRevPARForecast(getBigDecimalAt(row, 14));
            channelForecastDTO.setNetRevPARForecast(getBigDecimalAt(row, 15));
            return channelForecastDTO;
        };
    }

    public static BigDecimal getBigDecimalAt(Object[] row, int i) {
        final String value = getStringAt(row, i);
        return isEmpty(value) ? null : round(new BigDecimal(value), 2);
    }

    public static Integer getIntegerAt(Object[] row, int i) {
        final String value = getStringAt(row, i);
        return isEmpty(value) ? null : Integer.valueOf(value);
    }

    public static String getStringAt(Object[] row, int i) {
        return Optional.ofNullable(row[i]).map(String::valueOf).orElse(EMPTY);
    }

}
