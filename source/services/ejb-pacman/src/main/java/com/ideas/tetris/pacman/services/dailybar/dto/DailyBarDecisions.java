package com.ideas.tetris.pacman.services.dailybar.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideas.tetris.platform.common.rest.unmarshaller.JaxbDateSerializer;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;
import java.util.List;

@XmlRootElement(name = "PMSDecisions")
@XmlType(propOrder = {"decisionDate", "generationDate", "isDifferential", "dailyBarDecisionPerOccupancyTypes", "propertyCode"})

public class DailyBarDecisions {

    //Attributes for JAXB
    private String propertyCode;
    private Date decisionDate;
    private String generationDate;
    private String isDifferential;
    private List<DailyBarDecisionPerOccupancyType> dailyBarDecisionPerOccupancyTypes;

    public DailyBarDecisions() {
        super();
    }

    @XmlAttribute(name = "PropertyCode")
    public String getPropertyCode() {
        return propertyCode;
    }

    public void setPropertyCode(String propertyCode) {
        this.propertyCode = propertyCode;
    }

    @XmlAttribute(name = "DecisionDate")
    @XmlJavaTypeAdapter(JaxbDateSerializer.class)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    public Date getDecisionDate() {
        return decisionDate;
    }

    public void setDecisionDate(Date decisionDate) {
        this.decisionDate = decisionDate;
    }

    @XmlAttribute(name = "GenerationDate")
    public String getGenerationDate() {
        return generationDate;
    }

    public void setGenerationDate(String generationDate) {
        this.generationDate = generationDate;
    }

    @XmlAttribute(name = "IsDifferential")
    public String getIsDifferential() {
        return isDifferential;
    }

    public void setIsDifferential(String isDifferential) {
        this.isDifferential = isDifferential;
    }

    @XmlElement(name = "DailyBARRateReport")
    public List<DailyBarDecisionPerOccupancyType> getDailyBarDecisionPerOccupancyTypes() {
        return dailyBarDecisionPerOccupancyTypes;
    }

    public void setDailyBarDecisionPerOccupancyTypes(List<DailyBarDecisionPerOccupancyType> dailyBarDecisionPerOccupancyTypes) {
        this.dailyBarDecisionPerOccupancyTypes = dailyBarDecisionPerOccupancyTypes;
    }

}
