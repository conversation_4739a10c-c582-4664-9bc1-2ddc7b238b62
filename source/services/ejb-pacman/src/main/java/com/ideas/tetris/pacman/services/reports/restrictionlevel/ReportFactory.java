package com.ideas.tetris.pacman.services.reports.restrictionlevel;

import javax.inject.Inject;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class ReportFactory {

    @Autowired
	private MinMaxLosFullReport minMaxLosFullReport;

    @Autowired
	private MinMaxLosDifferentialReport minMaxLosDifferentialReport;

    @Autowired
	private FPLosFullReport fpLosFullReport;

    @Autowired
	private FPLosDifferentialReport fpLosDifferentialReport;

    public RestrictionLevelReport getReportFor(boolean isFull, boolean isMinMaxLos) {
        if (isFull) {
            return isMinMaxLos ? minMaxLosFullReport : fpLosFullReport;
        } else {
            return isMinMaxLos ? minMaxLosDifferentialReport : fpLosDifferentialReport;
        }
    }
}
