package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.PropertySpecificConfiguration;
import com.ideas.tetris.pacman.services.datafeed.dto.PropertySpecificConfigurationEnhanced;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttribute;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum;
import com.ideas.tetris.pacman.services.roa.attribute.service.ROAPropertyAttributeService;
import com.ideas.tetris.pacman.services.roa.dto.PropertyAttributeRevision;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static com.ideas.tetris.pacman.common.constants.Constants.NO;
import static com.ideas.tetris.pacman.common.constants.Constants.YES;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PropertySpecificConfigurationService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
    PropertyService propertyService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
	private ROAPropertyAttributeService roaPropertyAttributeService;

    private static final String PRICE_DROP_MIN_REV_GAIN = "PRICE_DROP_MIN_REV_GAIN";
    private static final String PRICE_DROP_MAX_VALUE = "PRICE_DROP_MAX_VALUE";
    private static final String PRICE_DROP_MIN_DTA = "PRICE_DROP_MIN_DTA";

    public List<PropertySpecificConfiguration> getPropertySpecificConfiguration(final Integer propertyId) {
        final List<PropertySpecificConfiguration> propertySpecificConfigurations = new ArrayList<>();
        PropertySpecificConfiguration propertySpecificConfiguration = getPropertySpecificConfig(propertyId);
        propertySpecificConfigurations.add(propertySpecificConfiguration);
        return propertySpecificConfigurations;
    }

    public List<PropertySpecificConfigurationEnhanced> getPropertySpecificConfigurationEnhanced(final Integer propertyId, final boolean isPriceDropApplicable) {
        final List<PropertySpecificConfigurationEnhanced> configurationEnhancedList = new ArrayList<>();
        PropertySpecificConfigurationEnhanced propertySpecificConfigurationEnhanced = new PropertySpecificConfigurationEnhanced(getPropertySpecificConfig(propertyId));
        setPropertyOptimizationSettings(propertySpecificConfigurationEnhanced, isPriceDropApplicable);
        configurationEnhancedList.add(propertySpecificConfigurationEnhanced);
        return configurationEnhancedList;
    }

    public PropertySpecificConfiguration getPropertySpecificConfig(final Integer propertyId) {
        final PropertySpecificConfiguration propertySpecificConfiguration = new PropertySpecificConfiguration();

        final String nodeName = buildContext();
        final String competitorCategory = getCompetitorCategory(nodeName);
        propertySpecificConfiguration.setCompetitorCategory(competitorCategory);

        final String specificCompetitor = getLocaleBasedValue("competitionDisplay." + Constants.BAR_OVRD_DISPLAY_COMPETITOR_VALUE_ABSOLUTE);
        if (StringUtils.equals(specificCompetitor, competitorCategory)) {
            propertySpecificConfiguration.setSpecificCompetitorName(getSpecificCompetitorName(nodeName));
        }

        propertySpecificConfiguration.setPropertyName(getPropertyName(propertyId));
        propertySpecificConfiguration.setPropertyTimeZone(getPropertyTimeZone(nodeName));
        propertySpecificConfiguration.setPropertyYieldCurrencyCode(getPropertyYieldCurrencyCode(nodeName));
        propertySpecificConfiguration.setIsCloseLV0Enabled(Boolean.parseBoolean(pacmanConfigParamsService.getValue(nodeName, FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value())) ? YES : NO);

        final PropertyAttribute propertyAttribute = roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.OPT_DYNAMIC_PRICE);
        propertySpecificConfiguration.setOptimizationSettings(getOptimizationSettings(propertyAttribute));

        final PropertyAttributeRevision propertyAttributeRevision = getLatestOptimizationSettingsHistory(propertyAttribute);
        if (propertyAttributeRevision != null) {
            propertySpecificConfiguration.setOptimizationUserEmail(propertyAttributeRevision.getUserEmail());
            propertySpecificConfiguration.setOptimizationEffectiveDate(propertyAttributeRevision.getLastUpdatedDateTime() == null ? null : propertyAttributeRevision.getLastUpdatedDateTime().toDate());
        }

        propertySpecificConfiguration.setRunOfHouse(getRunOfHouse(nodeName));
        propertySpecificConfiguration.setRunOfHouseAccomTypeCode(getRunOfHouseAccomTypeCode(propertyId));

        return propertySpecificConfiguration;
    }

    private String getCompetitorCategory(final String nodeName) {
        final String competitorCategory = pacmanConfigParamsService.getValue(nodeName, IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value());
        return (StringUtils.isBlank(competitorCategory) ? null : getLocaleBasedValue("competitionDisplay." + competitorCategory));
    }

    public String getLocaleBasedValue(final String key) {
        final String value = ResourceUtil.getText(key, Locale.ENGLISH);
        return StringUtils.startsWith(value, "!!!") ? StringUtils.replace(value, "!!!", "") : value;
    }

    private String getOptimizationSettings(final PropertyAttribute propertyAttribute) {
        final String attributeValue = roaPropertyAttributeService.getAttributeValue(propertyAttribute.getId());
        return (attributeValue == null || "0".equals(attributeValue)) ? "Disable" : "Enable";
    }


    private String buildContext() {
        final StringBuilder nodeNameBuilder = new StringBuilder();
        nodeNameBuilder.append(Constants.CONFIG_PARAMS_NODE_PREFIX).append(".").append(PacmanWorkContextHelper.getWorkContext().getClientCode()).append(".").append(PacmanWorkContextHelper.getWorkContext().getPropertyCode());
        return nodeNameBuilder.toString();
    }

    private String getSpecificCompetitorName(final String nodeName) {
        final String specificCompetitorName = pacmanConfigParamsService.getValue(nodeName, IPConfigParamName.BAR_BAR_OVRD_ABSOLUTE_COMPETITOR.value());
        return (StringUtils.isBlank(specificCompetitorName) ? null : specificCompetitorName);
    }

    private PropertyAttributeRevision getLatestOptimizationSettingsHistory(final PropertyAttribute propertyAttribute) {
        List<PropertyAttributeRevision> propertyAttributeRevisions = roaPropertyAttributeService.fetchAttributeRevisions(propertyAttribute.getId());
        return (CollectionUtils.isEmpty(propertyAttributeRevisions)) ? null : propertyAttributeRevisions.get(0);
    }

    private String getPropertyName(final Integer propertyId) {
        final Property property = propertyService.getPropertyById(propertyId);
        return (property == null) ? null : property.getName();
    }

    private String getPropertyYieldCurrencyCode(final String nodeName) {
        final String yieldCurrencyCode = pacmanConfigParamsService.getValue(nodeName, IntegrationConfigParamName.YIELD_CURRENCY_CODE.value(Constants.RATCHET));
        return (StringUtils.isBlank(yieldCurrencyCode) ? null : yieldCurrencyCode);
    }


    private String getPropertyTimeZone(final String nodeName) {
        final String propertyTimeZone = pacmanConfigParamsService.getValue(nodeName, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        return (StringUtils.isBlank(propertyTimeZone) ? null : propertyTimeZone);
    }

    private String getRunOfHouse(final String nodeName) {
        String isROHEnabled = pacmanConfigParamsService.getValue(nodeName, IPConfigParamName.ROH_ROHENABLED.value());
        return (StringUtils.equalsIgnoreCase(isROHEnabled, "false")) ? "No" : "Yes";
    }

    private String getRunOfHouseAccomTypeCode(final Integer propertyId) {
        AccomType rohAccomType = tenantCrudService.findByNamedQuerySingleResult(AccomType.GET_ROH_TYPE, QueryParameter.with("propertyId", propertyId).parameters());
        return (rohAccomType == null) ? null : rohAccomType.getAccomTypeCode();
    }

    public void setPropertyService(PropertyService propertyService) {
        this.propertyService = propertyService;
    }

    public void setROAPropertyAttributeService(ROAPropertyAttributeService roaPropertyAttributeService) {
        this.roaPropertyAttributeService = roaPropertyAttributeService;
    }

    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    private void setPropertyOptimizationSettings(PropertySpecificConfigurationEnhanced propertySpecificConfigurationEnhanced, boolean isPriceDropApplicable) {

        propertySpecificConfigurationEnhanced.setCancelRebook(getCancelRebookValue());

        if (isPriceDropApplicable) {
            Map<String, String> attributeValueWithName =
                    roaPropertyAttributeService.getAttributeValueWithNameByAttributeNames(getPriceDropAttributeNames());
            if (!attributeValueWithName.isEmpty()) {
                propertySpecificConfigurationEnhanced.setEnablePriceDropProtection(YES);
                if (null != attributeValueWithName.get(PRICE_DROP_MIN_DTA)) {
                    propertySpecificConfigurationEnhanced.setPriceDropProtectionDaysToArrival(Long.valueOf(attributeValueWithName.get(PRICE_DROP_MIN_DTA)));
                }
                if (null != attributeValueWithName.get(PRICE_DROP_MAX_VALUE)) {
                    propertySpecificConfigurationEnhanced.setPriceDropProtectionMaximumPriceDrop(new BigDecimal(attributeValueWithName.get(PRICE_DROP_MAX_VALUE)));
                }
                if (null != attributeValueWithName.get(PRICE_DROP_MIN_REV_GAIN)) {
                    propertySpecificConfigurationEnhanced.setPriceDropProtectionRevenueThreshold(new BigDecimal(attributeValueWithName.get(PRICE_DROP_MIN_REV_GAIN)));
                }
            } else {
                propertySpecificConfigurationEnhanced.setEnablePriceDropProtection(NO);
            }
        }
    }

    private List<String> getPriceDropAttributeNames() {
        return Arrays.asList(PRICE_DROP_MIN_DTA, PRICE_DROP_MIN_REV_GAIN, PRICE_DROP_MAX_VALUE);
    }

    public String getCancelRebookValue() {
        final String nodeName = buildContext();
        String cancelRebookPercentageValue = "";
        if (Boolean.parseBoolean(pacmanConfigParamsService.getValue(nodeName, FeatureTogglesConfigParamName.DISPLAY_CANCEL_REBOOK_PERCENTAGE_OPTION.value()))) {
            PropertyAttribute cancelRebookPercentage = roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT);
            cancelRebookPercentageValue = roaPropertyAttributeService.getAttributeValue(cancelRebookPercentage.getId());
        }
        return cancelRebookPercentageValue;
    }

    public void setCrudService(CrudService crudService) {
        this.tenantCrudService = crudService;
    }
}
