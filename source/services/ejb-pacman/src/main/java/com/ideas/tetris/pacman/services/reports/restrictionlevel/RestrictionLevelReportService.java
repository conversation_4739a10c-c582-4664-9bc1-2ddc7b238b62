package com.ideas.tetris.pacman.services.reports.restrictionlevel;

import com.ideas.tetris.pacman.services.reports.restrictionlevel.dto.RestrictionLevelReportDTO;

import javax.inject.Inject;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class RestrictionLevelReportService {

    @Autowired
	private ReportFactory reportFactory;

    public List<RestrictionLevelReportDTO> filterBy(String reportStyle, String reportType,
                                                    LocalDate startDate, LocalDate endDate, LocalDate changesSince,
                                                    List<Integer> accomTypeIds, int isRollingDate, String rollingStartDate,
                                                    String rollingEndDate, String rollingBusinessDate, boolean isSrpFplosAtTotalLevelEnabled) {

        RestrictionLevelSpec rspec = new RestrictionLevelSpec();
        rspec.setStartDate(startDate);
        rspec.setEndDate(endDate);
        rspec.setChangesSince(changesSince);
        rspec.setAccomTypeIds(accomTypeIds);
        rspec.setIsRollingDate(isRollingDate);
        rspec.setRollingStartDate(rollingStartDate);
        rspec.setRollingEndDate(rollingEndDate);
        rspec.setRollingBusinessDate(rollingBusinessDate);
        rspec.setReportStyleKey(reportStyle);
        rspec.setReportTypeKey(reportType);
        rspec.setSrpFplosAtTotalLevelEnabled(isSrpFplosAtTotalLevelEnabled);

        return reportFactory.getReportFor(rspec.isFull(), rspec.isMinMaxLos()).generate(rspec);
    }

}
