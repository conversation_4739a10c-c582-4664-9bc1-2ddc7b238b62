package com.ideas.tetris.pacman.services.discover.rest;

public enum DiscoverRestEndPoints {

    // V2 APIs
    FETCH_SINGLE_USER_BASED_ON_EMAIL("api/v2/users?&page=1&api_key={api_key}&user[login]={user_email}"),
    CREATE_SINGLE_USER("api/v2/users?api_key={api_key}"),
    UPDATE_SINGLE_USER("api/v2/users/{user_id}?api_key={api_key}"),

    // V3 APIs
    GET_ACCESS_TOKEN_V3("oauth2/token.json"),
    FETCH_SINGLE_USER_BASED_ON_EMAIL_V3("api/v3/users?&page=1&login={user_email}"),
    CREATE_SINGLE_USER_V3("api/v3/users"),
    UPDATE_SINGLE_USER_V3("api/v3/users/{user_id}");

    private final String endpoint;

    DiscoverRestEndPoints(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getEndpoint() {
        return endpoint;
    }
}
