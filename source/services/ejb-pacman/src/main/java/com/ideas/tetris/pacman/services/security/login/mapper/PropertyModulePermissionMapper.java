package com.ideas.tetris.pacman.services.security.login.mapper;

import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.datafeed.dto.RolePermission;
import com.ideas.tetris.pacman.services.datafeed.service.RolePermissionService;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.RoleService;
import com.ideas.tetris.pacman.services.security.login.util.RoleModulePermissionMapperUtil;
import com.ideas.tetris.pacman.services.security.login.vo.ModulePermissionVO;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

import static com.ideas.tetris.pacman.services.security.login.mapper.RoleModulePermissionMapper.getModuleUrlMapping;

@Component
@Transactional
public class PropertyModulePermissionMapper {
    private static final Logger LOGGER = Logger.getLogger(PropertyModulePermissionMapper.class.getName());
    @Autowired
	private RoleService roleService;
    @Autowired
	private PropertyRoleMapper propertyRoleMapper;
    @Autowired
	private RoleModulePermissionMapper roleModulePermissionMapper;
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    ClientPropertyCacheService clientPropertyCacheService;

    @Autowired
    RolePermissionService rolePermissionService;
    private static String ALL_ACCESS = "-666";

    public Map<Integer, Set<ModulePermissionVO>> getPropertyModulePermissionMap(GlobalUser globalUser) {
        Map<Integer, Set<ModulePermissionVO>> propertyBasedModulePermissionsMap = new HashMap<>();
        Set<Role> roles = getAllRolesForClient(PacmanWorkContextHelper.getClientCode());
        Map<Integer, String> propertyRoleMapping = propertyRoleMapper.getPropertyRoleMapping(globalUser, PacmanWorkContextHelper.getClientId());
        Map<String, Set<ModulePermissionVO>> roleModulePermissionMap = roleModulePermissionMapper.getRoleModulePermissionMap();
        final Client client = clientPropertyCacheService.getClient(globalUser.getClientCode());
        List<Property> activePropertiesForClientCode = clientPropertyCacheService.getClientProperties(client);
        List<RolePermission> rolePermissions = rolePermissionService.getAccessibleRolePermissions();

        for (Map.Entry<Integer, String> entry : propertyRoleMapping.entrySet()) {
            Integer propertyId = entry.getKey();
            Optional<Property> property = activePropertiesForClientCode.stream().filter(propertyItem -> Objects.equals(propertyItem.getId(), propertyId)).findFirst();
            if (property.isPresent()) {
                String roleId = propertyRoleMapping.get(propertyId);
                String roleName = getRoleNameById(roles, roleId);
                Set<ModulePermissionVO> newSetOfRoleModulePermissions = new HashSet<>();
                addPermissions(property.get(), propertyBasedModulePermissionsMap, roleModulePermissionMap, rolePermissions, propertyId, roleName, newSetOfRoleModulePermissions);
            } else {
                LOGGER.info(String.format("Property ID - %d not found in active properties list for User ID - %d with Client Code - %s in PropertyModulePermissionMapper::getPropertyModulePermissionMap() ", propertyId, globalUser.getId(), globalUser.getClientCode()));
            }
        }

        return propertyBasedModulePermissionsMap;
    }

    public Map<Integer, Set<ModulePermissionVO>> getPropertyModulePermissionMapOptimized(GlobalUser globalUser,
                                                                                         Property defaultProperty) {
        return getPropertyModulePermissionMapOptimizedBy(globalUser, defaultProperty, true,
                PacmanWorkContextHelper.getClientCode(),
                PacmanWorkContextHelper.getClientId(),
                getModuleUrlMapping());
    }

    public Map<Integer, Set<ModulePermissionVO>> getPropertyModulePermissionMapOptimizedBy(GlobalUser globalUser, Property defaultProperty,
                                                                                           boolean isForMobile,
                                                                                           String clientCode,
                                                                                           Integer clientId,
                                                                                           Map<String, String> moduleUrlMap) {
        if (defaultProperty == null) {
            return Collections.emptyMap();
        }
        Map<Integer, Set<ModulePermissionVO>> propertyBasedModulePermissionsMap = new HashMap<>();
        Set<Role> roles = getAllRolesForClient(clientCode);
        Map<Integer, String> propertyRoleMapping = propertyRoleMapper.getPropertyRoleMapping(globalUser, clientId);
        Map<String, Set<ModulePermissionVO>> roleModulePermissionMap = roleModulePermissionMapper.getRoleModulePermissionMapBy(isForMobile, globalUser.isInternal(), moduleUrlMap);
        List<RolePermission> rolePermissions = rolePermissionService.getAccessibleRolePermissions(globalUser.isInternal());
        Integer propertyId = defaultProperty.getId();
        String roleId = propertyRoleMapping.get(propertyId);
        String roleName = getRoleName(isForMobile, roles, roleId);
        Set<ModulePermissionVO> newSetOfRoleModulePermissions = new HashSet<>();
        addPermissions(defaultProperty, propertyBasedModulePermissionsMap, roleModulePermissionMap, rolePermissions, propertyId, roleName, newSetOfRoleModulePermissions);
        return propertyBasedModulePermissionsMap;
    }

    protected String getRoleName(boolean isForMobile, Set<Role> roles, String roleId) {
        String roleName = getRoleNameById(roles, roleId);
        if(!isForMobile && null == roleName && ALL_ACCESS.equals(roleId)){
            roleName = RoleModulePermissionMapperUtil.PERMISSION_READ_WRITE;
        }
        return roleName;
    }

    private void addPermissions(Property defaultProperty, Map<Integer, Set<ModulePermissionVO>> propertyBasedModulePermissionsMap, Map<String, Set<ModulePermissionVO>> roleModulePermissionMap, List<RolePermission> rolePermissions, Integer propertyId, String roleName, Set<ModulePermissionVO> newSetOfRoleModulePermissions) {
        if (StringUtils.isNotBlank(roleName) && !roleModulePermissionMap.isEmpty() && null != roleModulePermissionMap.get(roleName)) {
            newSetOfRoleModulePermissions.addAll(roleModulePermissionMap.get(roleName));
            propertyBasedModulePermissionsMap.put(propertyId, newSetOfRoleModulePermissions);
            List<RolePermission> rolePermissionList = rolePermissions.stream().filter(rolePermission -> rolePermission.getRoleName().equals(roleName)).collect(Collectors.toList());
            addBarOverridePermission(defaultProperty, rolePermissionList, propertyBasedModulePermissionsMap);
        }
    }

    public void addBarOverridePermission(Property property, List<RolePermission> rolePermissions, Map<Integer, Set<ModulePermissionVO>> propertyBasedModulePermissionsMap) {
        Map<String, String> accessLabelMap = RoleModulePermissionMapperUtil.getAccessLabelMap();
        boolean isContinuousPricingEnabled = pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value(), property.getClient().getCode(), property.getCode());
        Optional<RolePermission> rolePermissionCP = rolePermissions.stream().filter(rolePermission -> "Pricing".equalsIgnoreCase(rolePermission.getModuleName())).findFirst();
        Optional<RolePermission> rolePermissionNonCP = rolePermissions.stream().filter(rolePermission -> "Pricing Management".equalsIgnoreCase(rolePermission.getModuleName())).findFirst();
        if (isContinuousPricingEnabled) {
            rolePermissionCP.ifPresent(rolePermission -> propertyBasedModulePermissionsMap.get(property.getId()).add(new ModulePermissionVO("BarOverride", accessLabelMap.get(rolePermission.getPermission()))));
        } else {
            //Non-CP
            rolePermissionNonCP.ifPresent(rolePermission -> propertyBasedModulePermissionsMap.get(property.getId()).add(new ModulePermissionVO("BarOverride", accessLabelMap.get(rolePermission.getPermission()))));
        }

    }

    public String getRoleNameById(Set<Role> roles, String roleId) {
        Role matchingRole = roles.stream().filter(role -> role.getUniqueIdentifier().equals(roleId)).findFirst().orElse(null);
        return matchingRole == null ? null : matchingRole.getRoleName();
    }

    public Set<Role> getAllRolesForClient(String clientCode) {
        return roleService.getAllRoles(clientCode);
    }
}
