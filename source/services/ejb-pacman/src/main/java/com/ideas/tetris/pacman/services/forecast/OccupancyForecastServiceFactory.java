package com.ideas.tetris.pacman.services.forecast;

import org.apache.commons.lang.StringUtils;

import javax.inject.Inject;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;


@Component
@Transactional
public class OccupancyForecastServiceFactory {

    @Autowired
	private OccupancyForecastService occupancyForecastService;

    @Autowired
	private OccupancyForecastVirtualPropertyService occupancyForecastVirtualPropertyService;


    public AbstractOccupancyForecastService getOccupancyForecastService(String physicalPropertyCode) {
        if (StringUtils.isNotBlank(physicalPropertyCode)) {
            return occupancyForecastVirtualPropertyService;
        }
        return occupancyForecastService;
    }
}
