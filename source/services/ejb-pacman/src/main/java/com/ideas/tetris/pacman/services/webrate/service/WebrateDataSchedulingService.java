package com.ideas.tetris.pacman.services.webrate.service;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.eventaggregator.SystemComponent;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateShoppingConfig;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.builder.EqualsBuilder;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class WebrateDataSchedulingService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
	private SyncEventAggregatorService syncEventAggregatorService;


    @SuppressWarnings("unchecked")
    public List<WebrateShoppingConfig> getAllWebrateShoppingConfigsForProperty() {

        return crudService
                .findByNamedQuery(WebrateShoppingConfig.ALL);
    }

    public boolean createOrModifyWebrateShoppingConfig(List<WebrateShoppingConfig> webrateShoppingConfigList) {

        List<WebrateShoppingConfig> tobeCreatedOrModified = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(webrateShoppingConfigList)) {
            Map<Integer, WebrateShoppingConfig> existingWebrateShoppingConfigMap = getAllWebrateShoppingConfigsForProperty()
                    .stream()
                    .collect(Collectors.toMap(WebrateShoppingConfig::getId, Function.identity(), (existingWsc, newWsc) -> existingWsc));
            webrateShoppingConfigList.forEach(wsc -> {
                WebrateShoppingConfig webrateShoppingConfig = existingWebrateShoppingConfigMap.get(wsc.getId());
                if (null == webrateShoppingConfig) {
                    webrateShoppingConfig = new WebrateShoppingConfig();
                }
                webrateShoppingConfig.setRollingDaysToShop(wsc.getRollingDaysToShop());
                webrateShoppingConfig.setWebrateShoppingFrequency(wsc.getWebrateShoppingFrequency());
                webrateShoppingConfig.setWebrateShoppingThreshold(wsc.getWebrateShoppingThreshold());
                webrateShoppingConfig.setIsDeleted(wsc.getIsDeleted());
                webrateShoppingConfig.setIsModified(wsc.getIsModified());
                tobeCreatedOrModified.add(webrateShoppingConfig);
            });
        }
        return CollectionUtils.isEmpty(tobeCreatedOrModified) || saveWebrateShoppingConfigs(tobeCreatedOrModified);
    }

    public boolean saveWebrateShoppingConfigs(List<WebrateShoppingConfig> webrateShoppingConfigList) {
        if (webrateShoppingConfigList != null) {
            boolean isDirty = false;
            for (WebrateShoppingConfig webrateShoppingConfig : webrateShoppingConfigList) {
                // Check to see if a flag is dirty
                if (!isDirty && isDirty(webrateShoppingConfig)) {
                    isDirty = true;
                }

                if (webrateShoppingConfig.getIsDeleted() != null
                        && webrateShoppingConfig.getIsDeleted().intValue() == 1) {
                    if (webrateShoppingConfig.getId() != null) {
                        crudService.delete(WebrateShoppingConfig.class, webrateShoppingConfig.getId());
                        crudService.getEntityManager().flush();
                    }
                } else {
                    crudService.save(webrateShoppingConfig);
                }
            }
        }

        return true;
    }

    public boolean isDirty(WebrateShoppingConfig webrateShoppingConfig) {
        // New WebrateShoppingConfig requires a sync
        if (webrateShoppingConfig.getId() == null) {
            // If there was a WebrateShoppingConfig change that a sync isn't required for, it needs to be removed
            if (!syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
                syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
            }
            return true;
        }

        // If the WebrateShoppingConfig is deleted, a sync is required
        if (webrateShoppingConfig.getIsDeleted() != null && webrateShoppingConfig.getIsDeleted() == 1) {
            // If there was a WebrateShoppingConfig change that a sync isn't required for, it needs to be removed
            if (!syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
                syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
            }

            return true;
        }

        // Get the one that was last saved in the database
        WebrateShoppingConfig existingWebrateShoppingConfig = crudService.find(WebrateShoppingConfig.class,
                webrateShoppingConfig.getId());

        // Check to see if Rolling Days To Shop is different, if so a sync is required
        EqualsBuilder webrateShoppingConfigEqualsBuilder = new EqualsBuilder();
        webrateShoppingConfigEqualsBuilder.append(existingWebrateShoppingConfig.getRollingDaysToShop(),
                webrateShoppingConfig.getRollingDaysToShop());
        // Check to see if Webrate Shopping Frequency is different, if so a sync is required
        webrateShoppingConfigEqualsBuilder.append(existingWebrateShoppingConfig.getWebrateShoppingFrequency(),
                webrateShoppingConfig.getWebrateShoppingFrequency());
        // Check to see if Webrate Shopping Threshold is different, if so a sync is required
        webrateShoppingConfigEqualsBuilder.append(existingWebrateShoppingConfig.getWebrateShoppingThreshold(),
                webrateShoppingConfig.getWebrateShoppingThreshold());
        if (!webrateShoppingConfigEqualsBuilder.isEquals()) {
            // If there was a WebrateShoppingConfig change that a sync isn't required for, it needs to be removed
            if (!syncEventAggregatorService.isSystemComponentDirty(SystemComponent.WEBRATE)) {
                syncEventAggregatorService.registerSyncEvent(SyncEvent.WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED);
            }

            return true;
        }

        return false;
    }

    /**
     * This method returns the Minimum configured staleness threshold. It is getting used in CompetitorRatesInfoIsStale
     * alert evaluation
     *
     * @return
     */
    public Integer getConfiguredMinThreshold() {
        Integer stalenessThreshold = crudService
                .findByNamedQuerySingleResult(WebrateShoppingConfig.MIN_THRESHOLD);
        return null != stalenessThreshold ? stalenessThreshold : 0;
    }

    public int deleteAllWebrateShoppingConfig() {
        return crudService.deleteAll(WebrateShoppingConfig.class);
    }

    public void setSyncEventAggregatorService(SyncEventAggregatorService syncEventAggregatorService) {
        this.syncEventAggregatorService = syncEventAggregatorService;
    }

    public void setConfigParamsService(PacmanConfigParamsService configParamsService) {
        this.configParamsService = configParamsService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

}
