package com.ideas.tetris.pacman.services.dashboard.vo;

import java.io.Serializable;

public class BasicPricingDailyCalendarVO implements Serializable {

    private String occupancy;
    private String occupancyPercentage;
    private String roomsSold;
    private String roomsSoldPercentage;
    private String BARRate;
    private Integer BARRateId;
    private String BARRateName;
    private Boolean isUserOverride = Boolean.FALSE;
    private Boolean isFloorOverride = Boolean.FALSE;
    private Boolean isCeilingOverride = Boolean.FALSE;
    private Boolean isBARDecisionUploaded = Boolean.FALSE;
    private Boolean changedSincePreviousProcessing = Boolean.FALSE;
    private String colorHexCode;
    private String errorMessage;
    private String lastBARRate;
    private Boolean isBarValueMorethanLastProcessing = Boolean.FALSE;

    public Boolean getIsCeilingOverride() {
        return isCeilingOverride;
    }

    public void setIsCeilingOverride(Boolean ceilingOverride) {
        isCeilingOverride = ceilingOverride;
    }

    public Integer getBARRateId() {
        return BARRateId;
    }

    public void setBARRateId(Integer bARRateId) {
        BARRateId = bARRateId;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getBARRate() {
        return BARRate;
    }

    public void setBARRate(String bARRate) {
        BARRate = bARRate;
    }

    public Boolean getIsUserOverride() {
        return isUserOverride;
    }

    public void setIsUserOverride(Boolean isUserOverride) {
        this.isUserOverride = isUserOverride;
    }

    public Boolean getIsFloorOverride() {
        return isFloorOverride;
    }

    public void setIsFloorOverride(Boolean isFloorOverride) {
        this.isFloorOverride = isFloorOverride;
    }

    public String getColorHexCode() {
        return colorHexCode;
    }

    public void setColorHexCode(String colorHexCode) {
        this.colorHexCode = colorHexCode;
    }

    public String getOccupancy() {
        return occupancy;
    }

    public void setOccupancy(String occupancy) {
        this.occupancy = occupancy;
    }

    public String getOccupancyPercentage() {
        return occupancyPercentage;
    }

    public void setOccupancyPercentage(String occupancyPercentage) {
        this.occupancyPercentage = occupancyPercentage;
    }

    public String getRoomsSold() {
        return roomsSold;
    }

    public void setRoomsSold(String roomsSold) {
        this.roomsSold = roomsSold;
    }

    public String getRoomsSoldPercentage() {
        return roomsSoldPercentage;
    }

    public void setRoomsSoldPercentage(String roomsSoldPercentage) {
        this.roomsSoldPercentage = roomsSoldPercentage;
    }

    public Boolean getIsBARDecisionUploaded() {
        return isBARDecisionUploaded;
    }

    public void setIsBARDecisionUploaded(Boolean isBARDecisionUploaded) {
        this.isBARDecisionUploaded = isBARDecisionUploaded;
    }

    public Boolean isChangedSincePreviousProcessing() {
        return changedSincePreviousProcessing;
    }

    public void setChangedSincePreviousProcessing(Boolean changedSincePreviousProcessing) {
        this.changedSincePreviousProcessing = changedSincePreviousProcessing;
    }

    public String getBARRateName() {
        return BARRateName;
    }

    public void setBARRateName(String bARRateName) {
        BARRateName = bARRateName;
    }

    public String getLastBARRate() {
        return lastBARRate;
    }

    public void setLastBARRate(String lastBARRate) {
        this.lastBARRate = lastBARRate;
    }

    public Boolean getIsBarValueMorethanLastProcessing() {
        return isBarValueMorethanLastProcessing;
    }

    public void setIsBarValueMorethanLastProcessing(
            Boolean isBarValueMorethanLastProcessing) {
        this.isBarValueMorethanLastProcessing = isBarValueMorethanLastProcessing;
    }

}
