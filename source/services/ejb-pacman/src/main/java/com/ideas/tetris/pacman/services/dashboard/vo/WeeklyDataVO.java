/**
 *
 */
package com.ideas.tetris.pacman.services.dashboard.vo;

import java.io.Serializable;
import java.util.Map;

public class WeeklyDataVO implements Serializable {

    private PerformanceTrendsChartsVO performanceTrendsChartsVO;
    private Double roomCapacity;
    private Map<String, DailyViewDataVO> dailyViewDataVOMap;

    public PerformanceTrendsChartsVO getPerformanceTrendsChartsVO() {
        return performanceTrendsChartsVO;
    }

    public void setPerformanceTrendsChartsVO(PerformanceTrendsChartsVO performanceTrendsChartsVO) {
        this.performanceTrendsChartsVO = performanceTrendsChartsVO;
    }

    public Map<String, DailyViewDataVO> getDailyViewDataVOMap() {
        return dailyViewDataVOMap;
    }

    public void setDailyViewDataVOMap(Map<String, DailyViewDataVO> dailyViewDataVOMap) {
        this.dailyViewDataVOMap = dailyViewDataVOMap;
    }

    public Double getRoomCapacity() {
        return roomCapacity;
    }

    public void setRoomCapacity(Double roomCapacity) {
        this.roomCapacity = roomCapacity;
    }

}
