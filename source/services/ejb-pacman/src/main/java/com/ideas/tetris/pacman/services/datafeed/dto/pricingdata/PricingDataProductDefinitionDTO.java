package com.ideas.tetris.pacman.services.datafeed.dto.pricingdata;

import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Locale;

public class PricingDataProductDefinitionDTO implements Serializable {

    private String productname;

    private String productDescription;

    private String productType;

    private boolean active;

    private boolean upload;

    private String baseProduct;

    private boolean seasonalProductOnly;

    private boolean optimized;

    private String roundingRule;

    private BigDecimal floor;

    private BigDecimal minimumPriceChange;

    private boolean publiclyAvailable;

    private boolean adjustmentAppliesExtraAdult;

    private boolean adjustmentAppliesExtraChild;

    private Integer fencingAdvanceBookingMinDays;

    private Integer fencingAdvanceBookingMaxDays;

    private Integer fencingLOSMinDays;

    private Integer fencingLOSMaxDays;

    private String rateProtect;

    private String centrallyManaged;

    private String floorType;

    private BigDecimal floorPercentage;

    private String overrideType;

    public String getRateProtect() {
        return rateProtect;
    }

    public void setRateProtect(String rateProtect) {
        this.rateProtect = rateProtect;
    }

    public String getProductname() {
        return productname;
    }

    public void setProductname(String productname) {
        this.productname = productname;
    }

    public String getProductDescription() {
        return productDescription;
    }

    public void setProductDescription(String productDescription) {
        this.productDescription = productDescription;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public boolean isUpload() {
        return upload;
    }

    public void setUpload(boolean upload) {
        this.upload = upload;
    }

    public String getBaseProduct() {
        return baseProduct;
    }

    public void setBaseProduct(String baseProduct) {
        this.baseProduct = baseProduct;
    }

    public boolean isSeasonalProductOnly() {
        return seasonalProductOnly;
    }

    public void setSeasonalProductOnly(boolean seasonalProductOnly) {
        this.seasonalProductOnly = seasonalProductOnly;
    }

    public String getRoundingRule() {
        return roundingRule;
    }

    public void setRoundingRule(String roundingRule) {
        this.roundingRule = roundingRule;
    }

    public BigDecimal getFloor() {
        return floor;
    }

    public void setFloor(BigDecimal floor) {
        this.floor = floor;
    }

    public BigDecimal getMinimumPriceChange() {
        return minimumPriceChange;
    }

    public void setMinimumPriceChange(BigDecimal minimumPriceChange) {
        this.minimumPriceChange = minimumPriceChange;
    }

    public Integer getFencingAdvanceBookingMinDays() {
        return fencingAdvanceBookingMinDays;
    }

    public void setFencingAdvanceBookingMinDays(Integer fencingAdvanceBookingMinDays) {
        this.fencingAdvanceBookingMinDays = fencingAdvanceBookingMinDays;
    }

    public Integer getFencingAdvanceBookingMaxDays() {
        return fencingAdvanceBookingMaxDays;
    }

    public void setFencingAdvanceBookingMaxDays(Integer fencingAdvanceBookingMaxDays) {
        this.fencingAdvanceBookingMaxDays = fencingAdvanceBookingMaxDays;
    }

    public Integer getFencingLOSMinDays() {
        return fencingLOSMinDays;
    }

    public void setFencingLOSMinDays(Integer fencingLOSMinDays) {
        this.fencingLOSMinDays = fencingLOSMinDays;
    }

    public Integer getFencingLOSMaxDays() {
        return fencingLOSMaxDays;
    }

    public void setFencingLOSMaxDays(Integer fencingLOSMaxDays) {
        this.fencingLOSMaxDays = fencingLOSMaxDays;
    }

    public boolean isOptimized() {
        return optimized;
    }

    public void setOptimized(boolean optimized) {
        this.optimized = optimized;
    }

    public boolean isPubliclyAvailable() {
        return publiclyAvailable;
    }

    public void setPubliclyAvailable(boolean publiclyAvailable) {
        this.publiclyAvailable = publiclyAvailable;
    }

    public boolean isAdjustmentAppliesExtraAdult() {
        return adjustmentAppliesExtraAdult;
    }

    public void setAdjustmentAppliesExtraAdult(boolean adjustmentAppliesExtraAdult) {
        this.adjustmentAppliesExtraAdult = adjustmentAppliesExtraAdult;
    }

    public boolean isAdjustmentAppliesExtraChild() {
        return adjustmentAppliesExtraChild;
    }

    public void setAdjustmentAppliesExtraChild(boolean adjustmentAppliesExtraChild) {
        this.adjustmentAppliesExtraChild = adjustmentAppliesExtraChild;
    }

    public String getCentrallyManaged() {
        return centrallyManaged;
    }

    public void setCentrallyManaged(String centrallyManaged) {
        this.centrallyManaged = centrallyManaged;
    }

    public String getFloorType() {
        return floorType;
    }

    public void setFloorType(String floorType) {
        this.floorType = floorType;
    }

    public BigDecimal getFloorPercentage() {
        return floorPercentage;
    }

    public void setFloorPercentage(BigDecimal floorPercentage) {
        this.floorPercentage = floorPercentage;
    }

    public String getOverrideType() {
        return overrideType;
    }

    public void setOverrideType(String overrideType) {
        this.overrideType = overrideType;
    }

    public PricingDataProductDefinitionDTO() {
    }

    public PricingDataProductDefinitionDTO(Product product, String baseProduct) {
        this.productname = product.getName();
        this.productDescription = product.getDescription();
        this.active = product.isActive();
        this.upload = product.isUpload();
        this.baseProduct = baseProduct;
        this.seasonalProductOnly = product.isDefaultInactive();
        this.optimized = product.isOptimized();
        this.roundingRule = product.getRoundingRule().name();
        this.floor = product.getFloor();
        this.minimumPriceChange = product.getMinimumPriceChange();
        this.publiclyAvailable = product.isPubliclyAvailable();
        this.adjustmentAppliesExtraAdult = product.isOffsetForExtraAdult();
        this.adjustmentAppliesExtraChild = product.isOffsetForExtraChild();
        this.fencingAdvanceBookingMinDays = product.getMinDTA();
        this.fencingAdvanceBookingMaxDays = product.getMaxDTA();
        this.fencingLOSMinDays = product.getMinLOS();
        this.fencingLOSMaxDays = product.getMaxLOS();
        this.rateProtect = product.getIsFixedAboveBar() ? Boolean.TRUE.toString() : Boolean.FALSE.toString();
        this.centrallyManaged = product.isCentrallyManaged() ? Boolean.TRUE.toString() : Boolean.FALSE.toString();
        this.floorType = ResourceUtil.getText(product.getFloorType().getCaptionKey(), Locale.US);
        this.floorPercentage = product.getFloorPercentage();
        this.overrideType = ResourceUtil.getText(product.getIsOverridable().getCaptionKey(), Locale.US);
    }
}
