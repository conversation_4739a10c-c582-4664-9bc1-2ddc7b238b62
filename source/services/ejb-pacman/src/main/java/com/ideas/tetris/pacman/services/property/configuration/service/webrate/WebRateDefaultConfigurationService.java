package com.ideas.tetris.pacman.services.property.configuration.service.webrate;

import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.WebRateDefaultChannelPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateChannel;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateDefaultChannel;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.List;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@WebRateDefaultConfigurationService.Qualifier
@Component
@Transactional
public class WebRateDefaultConfigurationService extends AbstractPropertyConfigurationService {

    private static final Logger LOGGER = Logger.getLogger(WebRateDefaultConfigurationService.class.getName());

    @Override
    public PropertyConfigurationRecordType getPropertyConfigurationRecordType() {
        return PropertyConfigurationRecordType.DRC;
    }

    @Override
    public List<PropertyConfigurationRecordFailure> doValidate(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId) {
        WebRateDefaultChannelPropertyConfigurationDto wrdcpcd = (WebRateDefaultChannelPropertyConfigurationDto) propertyConfigurationDto;

        List<PropertyConfigurationRecordFailure> exceptions = new ArrayList<PropertyConfigurationRecordFailure>();

        validateWebrateChannel(exceptions, propertyId, wrdcpcd.getSunday(), "Sunday");
        validateWebrateChannel(exceptions, propertyId, wrdcpcd.getMonday(), "Monday");
        validateWebrateChannel(exceptions, propertyId, wrdcpcd.getTuesday(), "Tuesday");
        validateWebrateChannel(exceptions, propertyId, wrdcpcd.getWednesday(), "Wednesday");
        validateWebrateChannel(exceptions, propertyId, wrdcpcd.getThursday(), "Thursday");
        validateWebrateChannel(exceptions, propertyId, wrdcpcd.getFriday(), "Friday");
        validateWebrateChannel(exceptions, propertyId, wrdcpcd.getSaturday(), "Saturday");

        return exceptions;
    }

    @Override
    public void doHandle(PropertyConfigurationDto pcd, Integer propertyId) {
        WebRateDefaultChannelPropertyConfigurationDto wrdcpcd = (WebRateDefaultChannelPropertyConfigurationDto) pcd;

        WebrateDefaultChannel webrateDefaultChannel = findWebrateDefaultChannel(propertyId);
        if (webrateDefaultChannel == null) {
            webrateDefaultChannel = new WebrateDefaultChannel();
            webrateDefaultChannel.setPropertyId(propertyId);
            //Defaulting to BAR for now; this will need to be updated once we start supporting Independent products
            webrateDefaultChannel.setProductID(1);
        }

        webrateDefaultChannel.setWebrateChannelSun(findWebrateChannel(propertyId, wrdcpcd.getSunday()));
        webrateDefaultChannel.setWebrateChannelMon(findWebrateChannel(propertyId, wrdcpcd.getMonday()));
        webrateDefaultChannel.setWebrateChannelTues(findWebrateChannel(propertyId, wrdcpcd.getTuesday()));
        webrateDefaultChannel.setWebrateChannelWed(findWebrateChannel(propertyId, wrdcpcd.getWednesday()));
        webrateDefaultChannel.setWebrateChannelThurs(findWebrateChannel(propertyId, wrdcpcd.getThursday()));
        webrateDefaultChannel.setWebrateChannelFri(findWebrateChannel(propertyId, wrdcpcd.getFriday()));
        webrateDefaultChannel.setWebrateChannelSat(findWebrateChannel(propertyId, wrdcpcd.getSaturday()));

        if (webrateDefaultChannel.getCreateDate() == null) {
            LOGGER.info("Creating WebrateDefaultChannel for Property: " + pcd.getPropertyCode());
            crudService.save(webrateDefaultChannel);
        } else {
            LOGGER.info("Updating WebrateDefaultChannel for Property: " + pcd.getPropertyCode());
            crudService.save(webrateDefaultChannel);
        }
    }

    public void validateWebrateChannel(List<PropertyConfigurationRecordFailure> exceptions, Integer propertyId, String fileChannelId, String day) {
        if (StringUtils.isEmpty(fileChannelId)) {
            exceptions.add(new PropertyConfigurationRecordFailure(day + " Web Rate Default Channel is required"));
        } else if (!channelExists(propertyId, fileChannelId)) {
            exceptions.add(new PropertyConfigurationRecordFailure("Unable to create " + day + " Webrate Default Channel. Unable to find Webrate Channel with channel id: " + fileChannelId));
        }
    }

    public boolean channelExists(Integer propertyId, String fileChannelId) {
        WebrateChannel webrateChannel = findWebrateChannel(propertyId, fileChannelId);
        return webrateChannel != null;
    }

    public WebrateChannel findWebrateChannel(Integer propertyId, String fileChannelID) {
        return (WebrateChannel) crudService.findByNamedQuerySingleResult(WebrateChannel.BY_CHANNEL_ID, QueryParameter.with("propertyId", propertyId).and("fileChannelID", fileChannelID).parameters());
    }

    public WebrateDefaultChannel findWebrateDefaultChannel(Integer propertyId) {
        return (WebrateDefaultChannel) crudService.findByNamedQuerySingleResult(WebrateDefaultChannel.BY_PROPERTY_ID_FOR_BAR_ONLY, QueryParameter.with("propertyId", propertyId).parameters());
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
