package com.ideas.tetris.pacman.services.dialog;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.ideas.g3.dialog.dto.DialogIntentRequest;
import com.ideas.g3.dialog.dto.DialogIntentResponse;
import com.ideas.g3.dialog.dto.DialogLaunchRequest;
import com.ideas.g3.dialog.dto.DialogLaunchResponse;
import com.ideas.g3.dialog.dto.DialogResponse;
import com.ideas.g3.dialog.dto.DialogSession;
import com.ideas.g3.dialog.dto.DialogSessionStartedRequest;
import com.ideas.g3.dialog.dto.DialogSessionStartedResponse;
import com.ideas.g3.dialog.dto.GoogleContext;
import com.ideas.g3.dialog.dto.GoogleIntent;
import com.ideas.g3.dialog.dto.GoogleRequest;
import com.ideas.g3.dialog.dto.GoogleResponse;
import com.ideas.infra.tetris.security.EncryptionDecryption;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import org.apache.log4j.Logger;
import org.json.JSONArray;

import javax.inject.Inject;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class GoogleAssistantService {
    private static final Logger LOGGER = Logger.getLogger(GoogleAssistantService.class);

    protected static final String INTENT = "intent";
    protected static final String DATE = "date";
    protected static final String DATE_PERIOD = "date-period";
    protected static final String ON_SESSION_STARTED = "onSessionStarted";
    protected static final String ON_LAUNCH = "onLaunch";
    protected static final String ON_INTENT = "onIntent";
    protected static final String SESSION_CONTEXT = "/contexts/g3session";
    protected static final String G3_SESSION = "g3Session";
    protected static final String LANGUAGE_CODE = "languageCode";
    protected static final String OUTPUT_CONTEXTS = "outputContexts";
    protected static final String PARAMETERS = "parameters";

    @Autowired
    DialogService dialogService;


    public GoogleResponse googleGateway(GoogleRequest request) {

        GoogleIntent intent = getIntentFromRequest(request);
        DialogSession inboundSession = getSessionFromRequest(request);

        switch (intent.getDisplayName()) {
            case ON_SESSION_STARTED:
                return onSessionStarted(request, inboundSession);
            case ON_LAUNCH:
                return onLaunch(request, inboundSession);
            default:
                return onIntent(request, inboundSession, intent);
        }
    }

    protected GoogleIntent getIntentFromRequest(GoogleRequest request) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            //For debugging purposes
            LOGGER.info("Request: " + mapper.writeValueAsString(request));

            //get intent
            return mapper.readValue(mapper.writeValueAsString(request.getQueryResultField(INTENT)), GoogleIntent.class);
        } catch (IOException e) {
            LOGGER.info(e);
            //TODO: Handle exception more gracefully
            return new GoogleIntent();
        }
    }

    protected DialogSession getSessionFromRequest(GoogleRequest request) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            JSONArray contextArray = new JSONArray(mapper.writeValueAsString(request.getQueryResultField(OUTPUT_CONTEXTS)));
            for (int index = 0; index < contextArray.length(); index++) {
                GoogleContext context = mapper.readValue(contextArray.getJSONObject(index).toString(), GoogleContext.class);
                if (context.getName().equals(request.getSession() + SESSION_CONTEXT)) {
                    return mapper.readValue(mapper.writeValueAsString(context.getParameters().get(G3_SESSION)), DialogSession.class);
                }
            }
        } catch (Exception e) {
            LOGGER.info("Session Not Found" + e);
        }
        //TODO: Handle exception.
        return new DialogSession();
    }

    protected Map<String, Object> getParametersFromRequest(GoogleRequest request) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(mapper.writeValueAsString(request.getQueryResultField(PARAMETERS)), Map.class);
        } catch (Exception e) {
            LOGGER.info(e);
            //TODO: Handle exception
            return new HashMap<>();
        }
    }

    protected GoogleResponse onSessionStarted(GoogleRequest request, DialogSession inboundSession) {
        GoogleResponse response = new GoogleResponse();

        //Create g3 session started request
        DialogSessionStartedRequest sessionStartedRequest = new DialogSessionStartedRequest();
        sessionStartedRequest.setLocale(request.getQueryResultField(LANGUAGE_CODE).toString());
        sessionStartedRequest.setSession(inboundSession);

        //Receive g3 response and forward content
        DialogSessionStartedResponse sessionStartedResponse = dialogService.onSessionStarted(sessionStartedRequest);
        response.setFulfillmentText(sessionStartedResponse.getText());

        response.setOutputContexts(Arrays.asList(createResponseSessionContext(request, sessionStartedResponse)));

        //For debugging
        try {
            ObjectMapper mapper = new ObjectMapper();
            LOGGER.info(mapper.writeValueAsString(response));
        } catch (Exception e) {
            LOGGER.info(e);
        }
        return response;
    }

    private boolean setCurrentUserIdInPacmanWorkContextHelper(GoogleRequest request) {
        final HashMap<String, Object> payload = (HashMap<String, Object>) request.getOriginalDetectIntentRequest().get("payload");
        if (payload != null) {
            final HashMap<String, Object> user = (HashMap<String, Object>) payload.get("user");
            final String[] userDetails = EncryptionDecryption.doStrongTextDecryption((String) user.get("accessToken")).split("\\|");
            if (!PacmanWorkContextHelper.getUserId().equalsIgnoreCase(userDetails[0])) {
                PacmanWorkContextHelper.setUserId(userDetails[0]);
                return true;
            }
        }
        return false;
    }

    private void removePropertyVOFromSession(DialogSession inboundSession) {
        inboundSession.setAttribute(DialogSession.PROPERTY, null);
    }

    protected GoogleResponse onLaunch(GoogleRequest request, DialogSession inboundSession) {
        GoogleResponse response = new GoogleResponse();

        //Create g3 launch request
        DialogLaunchRequest launchRequest = new DialogLaunchRequest();
        launchRequest.setLocale(request.getQueryResultField(LANGUAGE_CODE).toString());
        launchRequest.setSession(inboundSession);

        //Receive g3 launch response and forward content
        if (setCurrentUserIdInPacmanWorkContextHelper(request)) {
            removePropertyVOFromSession(inboundSession);
        }
        DialogLaunchResponse launchResponse = dialogService.onLaunch(launchRequest);
        response.setFulfillmentText(launchResponse.getText());
        LOGGER.info("SESSION: " + request.getSession());

        response.setOutputContexts(Arrays.asList(createResponseSessionContext(request, launchResponse)));

        //For debugging
        try {
            ObjectMapper mapper = new ObjectMapper();
            LOGGER.info(mapper.writeValueAsString(response));
        } catch (Exception e) {
            LOGGER.info(e);
        }
        return response;
    }

    protected GoogleResponse onIntent(GoogleRequest request, DialogSession inboundSession, GoogleIntent intent) {
        GoogleResponse response = new GoogleResponse();

        DialogIntentRequest intentRequest = new DialogIntentRequest();
        intentRequest.setLocale(request.getQueryResultField(LANGUAGE_CODE).toString());
        intentRequest.setSession(inboundSession);
        intentRequest.setVendorIntent(intent.getDisplayName());
        setCurrentUserIdInPacmanWorkContextHelper(request);
        Map<String, Object> parameters = getParametersFromRequest(request);

        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            if (entry.getKey().equals(DATE)) {
                String date = getDateFromParameter((String) parameters.get(DATE));
                if (!Strings.isNullOrEmpty(date)) {
                    intentRequest.setParameter(DialogIntentRequest.START_DATE_PARAMETER, date);
                    intentRequest.setParameter(DialogIntentRequest.END_DATE_PARAMETER, date);
                }
            } else if (entry.getKey().equals(DATE_PERIOD)) {
                Map<String, Object> period = parameters.get(DATE_PERIOD) instanceof Map ? (Map<String, Object>) parameters.get(DATE_PERIOD) : null;
                if (period != null) {
                    intentRequest.setParameter(DialogIntentRequest.START_DATE_PARAMETER, getDateFromParameter((String) period.get(DialogIntentRequest.START_DATE_PARAMETER)));
                    intentRequest.setParameter(DialogIntentRequest.END_DATE_PARAMETER, getDateFromParameter((String) period.get(DialogIntentRequest.END_DATE_PARAMETER)));
                }
            } else {
                intentRequest.setParameter(entry.getKey(), parameters.get(entry.getKey()));
            }
        }

        DialogIntentResponse intentResponse = dialogService.onIntent(intentRequest);
        response.setFulfillmentText(intentResponse.getText());

        response.setOutputContexts(Arrays.asList(createResponseSessionContext(request, intentResponse)));

        //for debugging
        try {
            ObjectMapper mapper = new ObjectMapper();
            LOGGER.info(mapper.writeValueAsString(response));
        } catch (Exception e) {
            LOGGER.info(e);
        }
        return response;
    }

    protected String getDateFromParameter(String date) {
        if (Strings.isNullOrEmpty(date)) {
            return null;
        } else if (date.length() >= 10) {
            return date.substring(0, 10);
        } else {
            return null;
        }
    }

    protected GoogleContext createResponseSessionContext(GoogleRequest request, DialogResponse response) {
        GoogleContext sessionContext = new GoogleContext();
        sessionContext.setName(request.getSession() + SESSION_CONTEXT);
        sessionContext.setLifespanCount(3);
        sessionContext.setParameter(G3_SESSION, response.getSession());
        return sessionContext;
    }
}
