package com.ideas.tetris.pacman.services.vendor.service;

import com.ideas.tetris.pacman.services.vendor.entity.InfoMgrJobLookupEntity;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import javax.inject.Inject;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class InfoManagerJobLookupService {
    @Autowired
	private AbstractMultiPropertyCrudService multiPropertyCrudService;

    public void save(int propertyId, InfoMgrJobLookupEntity entity) {
        multiPropertyCrudService.save(propertyId, entity);
    }

    public InfoMgrJobLookupEntity findByInfoMgrId(int propertyId, int infoMgrInstanceId, int jobInstanceId, String vendorName, String warningMsg) {
        return (InfoMgrJobLookupEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId,
                InfoMgrJobLookupEntity.FIND_BY_INFO_MGR_ID,
                QueryParameter.with("vendorName", vendorName)
                        .and("warningMessage", warningMsg).parameters());
    }

    public Integer findJobInstanceIdByInfoMgrId(int propertyId, int infoMgrInstanceId) {
        return (Integer) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId,
                InfoMgrJobLookupEntity.FIND_JOB_BY_INFO_MGR_ID,
                QueryParameter.with("infoInstanceId", infoMgrInstanceId).parameters());
    }

    public List<InfoMgrJobLookupEntity> findAll(int propertyId) {
        return multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId,
                InfoMgrJobLookupEntity.FIND_ALL, null);
    }

    public void deleteAllByProperty(int propertyId) {
        List<InfoMgrJobLookupEntity> entityList = findAll(propertyId);

        for (InfoMgrJobLookupEntity entity : entityList) {
            multiPropertyCrudService.delete(propertyId, InfoMgrJobLookupEntity.class, entity.getId());

        }
    }
}
