package com.ideas.tetris.pacman.services.reports.individualgroupwash.dto;

import java.math.BigDecimal;
import java.time.LocalDate;

public class IndividualGroupBlockTableDto {

    private Integer id;
    private Integer groupId;
    private Integer groupMasterId;

    private BigDecimal userWashValue;
    private BigDecimal userWashPercent;

    private String groupName;

    private String groupCode;

    private LocalDate occupancyDate;
    private Integer availableBlock;
    private BigDecimal systemWashValue;
    private BigDecimal systemWashPercent;
    private Integer block;
    private Integer pickup;

    private Integer daysToArrival;
    private LocalDate expirationDate;
    private LocalDate cutoffDate;

    private boolean hasExistingForecastGroupWashOverride;
    private boolean isWashOverrideAllowed;

    private BigDecimal rateValue;

    private int groupArrivals;

    public BigDecimal getRateValue() {
        return rateValue;
    }

    public void setRateValue(BigDecimal rateValue) {
        this.rateValue = rateValue;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public int getGroupArrivals() {
        return groupArrivals;
    }

    public void setGroupArrivals(int groupArrivals) {
        this.groupArrivals = groupArrivals;
    }


    private int groupDepartures;

    public int getGroupDepartures() {
        return groupDepartures;
    }

    public void setGroupDepartures(int groupDepartures) {
        this.groupDepartures = groupDepartures;
    }

    private int pickupVariance;

    public int getPickupVariance() {
        return pickupVariance;
    }

    public void setPickupVariance(int pickupVariance) {
        this.pickupVariance = pickupVariance;
    }

    public LocalDate getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(LocalDate occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    public Integer getId() {
        return id;
    }


    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Integer getGroupMasterId() {
        return groupMasterId;
    }

    public void setGroupMasterId(Integer groupMasterId) {
        this.groupMasterId = groupMasterId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public void setAvailableBlock(Integer availableBlock) {
        this.availableBlock = availableBlock;
    }

    public Integer getAvailableBlock() {
        return availableBlock;
    }

    public void setSystemWashValue(BigDecimal systemWashValue) {
        this.systemWashValue = systemWashValue;
    }

    public BigDecimal getSystemWashValue() {
        return systemWashValue;
    }

    public void setSystemWashPercent(BigDecimal systemWashPercent) {
        this.systemWashPercent = systemWashPercent;
    }

    public BigDecimal getSystemWashPercent() {
        return systemWashPercent;
    }

    public void setBlock(Integer block) {
        this.block = block;
    }

    public Integer getBlock() {
        return block;
    }

    public void setPickup(Integer pickup) {
        this.pickup = pickup;
    }

    public Integer getPickup() {
        return pickup;
    }

    public BigDecimal getUserWashValue() {
        return userWashValue;
    }

    public void setUserWashValue(BigDecimal userWashValue) {
        this.userWashValue = userWashValue;
    }

    public BigDecimal getUserWashPercent() {
        return userWashPercent;
    }

    public void setUserWashPercent(BigDecimal userWashPercent) {
        this.userWashPercent = userWashPercent;
    }

    public Integer getDaysToArrival() {
        return daysToArrival;
    }

    public void setDaysToArrival(Integer daysToArrival) {
        this.daysToArrival = daysToArrival;
    }

    public LocalDate getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDate expirationDate) {
        this.expirationDate = expirationDate;
    }

    public LocalDate getCutoffDate() {
        return cutoffDate;
    }

    public void setCutoffDate(LocalDate cutoffDate) {
        this.cutoffDate = cutoffDate;
    }

    public boolean isHasExistingForecastGroupWashOverride() {
        return hasExistingForecastGroupWashOverride;
    }

    public void setHasExistingForecastGroupWashOverride(boolean hasExistingForecastGroupWashOverride) {
        this.hasExistingForecastGroupWashOverride = hasExistingForecastGroupWashOverride;
    }

    public boolean isWashOverrideAllowed() {
        return isWashOverrideAllowed;
    }

    public void setWashOverrideAllowed(boolean isWashOverrideAllowed) {
        this.isWashOverrideAllowed = isWashOverrideAllowed;
    }

}
