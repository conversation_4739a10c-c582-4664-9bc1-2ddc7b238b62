package com.ideas.tetris.pacman.services.dashboard.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
public class PricingDecision implements Serializable {
    private String productName;
    private Integer displayOrder;
    private List<ProductDecision> productDecisions;

    public PricingDecision() {
        productDecisions = new LinkedList<>();
    }
}
