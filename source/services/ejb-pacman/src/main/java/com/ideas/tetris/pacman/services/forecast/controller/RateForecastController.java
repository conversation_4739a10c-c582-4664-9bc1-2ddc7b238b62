package com.ideas.tetris.pacman.services.forecast.controller;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.activity.service.Pageable;
import com.ideas.tetris.pacman.services.forecast.RateForecastService;
import com.ideas.tetris.pacman.services.forecast.dto.RateForecastDto;
import com.ideas.tetris.pacman.services.validation.dto.PageableDtos;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@RequestMapping("/data/analytics/mr")
public class RateForecastController {
    @Autowired
    private RateForecastService service;

    @GetMapping("/rate_fcst/{startDt}/{endDt}/v1")
    public PageableDtos<RateForecastDto> getRateForecasts(@PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDt, @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDt, @ModelAttribute Pageable pageable) {
        var propertyId = PacmanWorkContextHelper.getPropertyId();
        return service.retrieveResults(propertyId,startDt,endDt,pageable);
    }
}
