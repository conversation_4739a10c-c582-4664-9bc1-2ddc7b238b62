package com.ideas.tetris.pacman.services.vendor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class VendorConfigServiceImpl implements VendorConfigService {
    @Autowired
	protected PacmanConfigParamsService configParamsService;
    private RestClient restClient;

    @Autowired
    public void setRestClient(RestClient restClient) {
        this.restClient = restClient;
    }

    @Override
    public void save(VendorConfig vendorConfig) {
        vendorConfig.getChains().forEach(this::addFeatureToggles);
        restClient.put(RestEndpoints.VENDOR_CONFIG_PARAMS_PUT, getVendorConfigId(vendorConfig),
                Entity.entity(vendorConfig, MediaType.APPLICATION_JSON_TYPE));
    }

    @Override
    public void createPropertyConfig(String inboundVendorId, String clientCode, HotelConfigParams hotelConfigParams) {
        addFeatureToggles(clientCode, hotelConfigParams);
        restClient.post(
                RestEndpoints.HOTEL_CONFIG_PARAMS_POST,
                Entity.entity(hotelConfigParams, MediaType.APPLICATION_JSON_TYPE),
                inboundVendorId, clientCode);
    }

    @Override
    public void updatePropertyConfig(String inboundVendorId, String clientCode, HotelConfigParams hotelConfigParams) {
        addFeatureToggles(clientCode, hotelConfigParams);
        restClient.put(
                RestEndpoints.HOTEL_CONFIG_PARAMS_PUT,
                Entity.entity(hotelConfigParams, MediaType.APPLICATION_JSON_TYPE),
                inboundVendorId, clientCode);
    }

    @Override
    public void deletePropertyConfig(String inboundVendorId, String clientCode, String propertyCode) {
        restClient.delete(
                RestEndpoints.HOTEL_CONFIG_PARAMS_DELETE,
                inboundVendorId, clientCode, propertyCode);
    }

    private void addFeatureToggles(ChainConfigParams chain) {
        List<HotelConfigParams> hotels = chain.getHotels();
        hotels.forEach(hotel -> addFeatureToggles(chain.getChainCode(), hotel));
    }

    @Override
    public void addFeatureToggles(String clientCode, HotelConfigParams hotel) {
        String context = Constants.getPropertyConfigContext(clientCode, hotel.getHotelCode());
        addCalculateNonPickedUpBlocksUsingSummaryData(context, hotel);
        // any future feature toggles that we need to send to NGI can be added here
    }

    private void addCalculateNonPickedUpBlocksUsingSummaryData(String context, HotelConfigParams hotel) {
        String value = configParamsService.getValue(context, FeatureTogglesConfigParamName.AMS_CALCULATE_NON_PICKED_UP_BLOCKS_USING_SUMMARY_DATA.value());
        hotel.setCalculateNonPickedUpBlocksUsingSummaryData(value != null && value.equalsIgnoreCase(Boolean.TRUE.toString()));
    }

    private String getVendorConfigId(VendorConfig vendorConfig) {
        return vendorConfig.getInboundVendorId();
    }

    @Override
    public List<VendorConfig> findAll() {
        try {
            List<VendorConfig> vendorConfigs = mapJsonResponseToVendorConfigList(findVendors());

            // sort all the lists
            if (vendorConfigs != null) {
                vendorConfigs.sort(Comparator.comparing(VendorConfig::getInboundVendorId));
                for (VendorConfig vendorConfig : vendorConfigs) {
                    if (vendorConfig.getChains() != null) {
                        vendorConfig.getChains().sort(Comparator.comparing(ChainConfigParams::getChainCode));
                        for (ChainConfigParams chainConfigParams : vendorConfig.getChains()) {
                            if (chainConfigParams.getHotels() != null) {
                                chainConfigParams.getHotels().sort(Comparator.comparing(HotelConfigParams::getHotelCode));
                            }
                        }
                    }
                }
            }

            return vendorConfigs;
        } catch (IOException e) {
            throw new VendorConfigServiceException("Failed to get list of vendors", e);
        }
    }

    @Override
    public VendorConfig find(String inboundVendorId) {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("vendorConfigId", inboundVendorId);
        return restClient.getSingleResultFromEndpoint(RestEndpoints.VENDOR_CONFIG_PARAMS_GET,
                parameters, jsonObject -> {
                    try {
                        return new ObjectMapper().readValue(jsonObject.toString(), VendorConfig.class);
                    } catch (IOException e) {
                        throw new VendorConfigServiceException("Failed to get vendor for inboundVendorId: " + inboundVendorId, e);
                    }
                });
    }

    @Override
    public VendorConfig find(String inboundVendorId, String clientCode, String propertyCode) {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("vendorConfigId", inboundVendorId);
        parameters.put("clientCode", clientCode);
        parameters.put("propertyCode", propertyCode);
        return restClient.getSingleResultFromEndpoint(RestEndpoints.VENDOR_CONFIG_PARAMS_GET,
                parameters, jsonObject -> {
                    try {
                        return new ObjectMapper().readValue(jsonObject.toString(), VendorConfig.class);
                    } catch (IOException e) {
                        throw new VendorConfigServiceException("Failed to get vendor for inboundVendorId: " + inboundVendorId, e);
                    }
                });
    }

    private List<JSONObject> findVendors() {
        return restClient.getDataFromEndpoint(RestEndpoints.VENDOR_CONFIG_PARAMS, new HashMap<>());
    }

    private List<VendorConfig> mapJsonResponseToVendorConfigList(List<JSONObject> jsonObjects) throws IOException {
        List<VendorConfig> vendorConfigs = new ArrayList<>(jsonObjects.size());
        ObjectMapper objMapper = new ObjectMapper();

        for (JSONObject json : jsonObjects) {
            vendorConfigs.add(objMapper.readValue(json.toString(), VendorConfig.class));
        }
        return vendorConfigs;
    }
}
