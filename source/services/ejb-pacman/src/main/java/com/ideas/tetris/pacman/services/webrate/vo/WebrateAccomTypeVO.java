package com.ideas.tetris.pacman.services.webrate.vo;


import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateAccomType;

import java.io.Serializable;
import java.time.LocalDateTime;

public class WebrateAccomTypeVO implements Serializable {
    Integer webRateAccomTypeId;
    String webrateAccomName;
    String webrateAccomAlias;
    Boolean shouldDelete;
    Integer lastUpdatedByUserId;
    LocalDateTime lastUpdatedDate;

    public WebrateAccomTypeVO() {
    }

    public WebrateAccomTypeVO(WebrateAccomType webrateAccomType) {
        this.setWebRateAccomTypeId(webrateAccomType.getId());
        this.setWebrateAccomName(webrateAccomType.getWebrateAccomName());
        this.setWebrateAccomAlias(webrateAccomType.getWebrateAccomAlias());
        this.setShouldDelete(webrateAccomType.getShouldDelete());
        this.setLastUpdatedByUserId(Integer.valueOf(PacmanWorkContextHelper.getUserId()));
        this.setLastUpdatedDate(LocalDateTime.now());
    }

    public Integer getWebRateAccomTypeId() {
        return webRateAccomTypeId;
    }

    public void setWebRateAccomTypeId(Integer webRateAccomTypeId) {
        this.webRateAccomTypeId = webRateAccomTypeId;
    }

    public String getWebrateAccomName() {
        return webrateAccomName;
    }

    public void setWebrateAccomName(String webrateAccomName) {
        this.webrateAccomName = webrateAccomName;
    }

    public String getWebrateAccomAlias() {
        return webrateAccomAlias;
    }

    public void setWebrateAccomAlias(String webrateAccomAlias) {
        this.webrateAccomAlias = webrateAccomAlias;
    }

    public Boolean getShouldDelete() {
        return shouldDelete;
    }

    public void setShouldDelete(Boolean shouldDelete) {
        this.shouldDelete = shouldDelete;
    }

    public Integer getLastUpdatedByUserId() {
        return lastUpdatedByUserId;
    }

    public void setLastUpdatedByUserId(Integer lastUpdatedByUserId) {
        this.lastUpdatedByUserId = lastUpdatedByUserId;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }
}
