package com.ideas.tetris.pacman.services.runtask;

import com.ideas.tetris.platform.services.daoandentities.entity.Property;

import java.util.Date;

/**
 * Created by idnsru on 7/27/2016.
 */
public class BackendRunTaskDto {
    Property property;
    Date startDate;
    Date endDate;

    public BackendRunTaskDto(Property property, Date startDate, Date endDate) {
        this.property = property;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public Property getProperty() {
        return property;
    }

    public void setProperty(Property property) {
        this.property = property;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Override
    public String toString() {
        return "BackendRunTaskDto{" +
                "propertyCode=" + property.getCode() +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                '}';
    }
}
