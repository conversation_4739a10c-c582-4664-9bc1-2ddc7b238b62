package com.ideas.tetris.pacman.services.property;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ConsolidatedPropertyCriteria;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ConsolidatedPropertyView;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.property.configuration.dto.CatchupStatus;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecord;
import com.ideas.tetris.pacman.services.property.dto.CatchupData;
import com.ideas.tetris.pacman.services.property.dto.DateRange;
import com.ideas.tetris.pacman.services.property.dto.ExtractDetails;
import com.ideas.tetris.pacman.services.property.dto.Property;
import com.ideas.tetris.pacman.services.property.dto.PropertySearchCriteria;
import com.ideas.tetris.pacman.services.property.dto.WebRateExtractDetails;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.virtualproperty.service.VirtualPropertyMappingService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.IllegalFieldValueException;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType.WEBRATE_RECORD_TYPE_ID;
import static com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus.SUCCESSFUL;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PropertyRolloutPropertyFinder {
    private static final Logger LOGGER = Logger.getLogger(PropertyRolloutPropertyFinder.class.getName());
    private static final int PROPERTY_NAME_SEGMENT = 2;
    private static final int PROPERTY_TIME_ZONE_SEGMENT = 3;
    private static final int YIELD_CURRENCY_SEGMENT = 4;
    private static final int CRS_TIME_ZONE_SEGMENT = 5;
    private static final int WEB_RATE_ALIAS_SEGMENT = 6;

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;
    @Autowired
	protected AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
	protected AuthorizationService authorizationService;
    @Autowired
	protected ExtractDetailsServiceLocal extractDetailsService;
    @Autowired
	protected FilterAuthorized filterAuthorized;
    @Autowired
	protected FilterReadyForCatchup filterReadyForCatchup;
    @Autowired
	protected FilterReadyForPopulation filterReadyForPopulation;
    @Autowired
	protected FilterMinExtracts filterMinExtracts;
    @Autowired
	protected DateService dateService;
    @Autowired
	protected VirtualPropertyMappingService virtualPropertyMappingService;

    public Map<String, Integer> getCountsByStage() {
        return getStageCounts(null);
    }

    public Map<String, Integer> getCountsByStageAndPropertyCodes(List<Integer> propertyIds) {
        // NOTE: Previous implementation did not add entry if no hits for stage, now we add with count 0
        if (null == propertyIds || propertyIds.isEmpty()) {
            throw new IllegalArgumentException("Need to provide property ids.");
        }

        return getStageCounts(propertyIds);
    }

    private Map<String, Integer> getStageCounts(List<Integer> propertyIds) {
        // NOTE: Previous implementation did not add entry if no hits for stage, now we add with count 0
        Map<String, Integer> countsByStage = new HashMap<>();
        ConsolidatedPropertyCriteria criteria = new ConsolidatedPropertyCriteria();
        criteria.setClientId(getClientId());
        if (null != propertyIds) {
            criteria.setPropertyIds(propertyIds);
        }

        for (Stage stage : Stage.orderedValues()) {
            Set<Stage> stages = new HashSet<>();
            stages.add(stage);
            criteria.setStages(stages);
            countsByStage.put(stage.getCode(), globalCrudService.findCountByCriteria(criteria));
        }
        return countsByStage;
    }

    public List<Property> findProperties(PropertySearchCriteria searchCriteria) {
        List<Property> properties;

        long start = System.currentTimeMillis();
        if (searchCriteria.isUploadedPropertiesOnly()) {
            properties = findUploadedProperties(searchCriteria.parsePropertyCodes());
        } else {
            List<ConsolidatedPropertyView> matchingProperties = findMatchingProperties(searchCriteria);
            matchingProperties = filterMatchingProperties(matchingProperties, searchCriteria);
            properties = createPropertyDtosFromMatchingProperties(matchingProperties);
        }

        if (properties != null) {
            Collections.sort(properties);
        }

        LOGGER.info("findProperties took " + (System.currentTimeMillis() - start) + " ms");
        return properties;
    }

    @SuppressWarnings("unchecked")
    public List<Property> findUploadedProperties(List<String> propertyCodes) {
        List<Property> properties = new ArrayList<>();
        List<ConfigurationFileRecord> entities = new ArrayList<>();

        if (propertyCodes == null || propertyCodes.isEmpty()) {
            entities = globalCrudService.findByNamedQuery(
                    ConfigurationFileRecord.ALL_UPLOADED_PROPERTIES,
                    QueryParameter.with(ConfigurationFileRecord.PARAM_CLIENT_ID, getClientId()).parameters());
        } else {
            for (String propertyCode : propertyCodes) {
                ConfigurationFileRecord entity = globalCrudService.findByNamedQuerySingleResult(
                        ConfigurationFileRecord.UPLOADED_PROPERTY_BY_PROPERTY_CODE,
                        QueryParameter.with(ConfigurationFileRecord.PARAM_CLIENT_ID, getClientId()).
                                and(ConfigurationFileRecord.PARAM_PROPERTY_CODE, propertyCode).parameters());
                if (entity != null) {
                    entities.add(entity);
                }
            }
        }

        for (ConfigurationFileRecord entity : entities) {
            properties.add(createDto(entity));
        }

        return properties;
    }

    @SuppressWarnings("unchecked")
	public
    List<ConsolidatedPropertyView> findMatchingProperties(PropertySearchCriteria searchCriteria) {
        long start = System.currentTimeMillis();

        List<ConsolidatedPropertyView> matchingProperties = new ArrayList<>();
        if (searchCriteria.isEmpty()) {
            matchingProperties = globalCrudService.findByNamedQuery(ConsolidatedPropertyView.BY_CLIENT_ID,
                    QueryParameter.with(ConsolidatedPropertyView.PARAM_CLIENT_ID, getClientId()).parameters());
        } else if (searchCriteria.getPropertyId() != null) {
            ConsolidatedPropertyView matchingProperty = globalCrudService.find(ConsolidatedPropertyView.class, searchCriteria.getPropertyId());
            if (matchingProperty != null) {
                matchingProperties.add(matchingProperty);
            }
        } else {
            List<String> propertyCodes = searchCriteria.parsePropertyCodes();
            if (propertyCodes != null && !propertyCodes.isEmpty()) {
                matchingProperties = findByPropertyCode(propertyCodes);
            } else {
                matchingProperties = findByCriteria(searchCriteria);
            }
        }

        LOGGER.info("Querying for properties in findMatchingProperties took " + (System.currentTimeMillis() - start) + " ms");
        return matchingProperties;
    }

    public List<ConsolidatedPropertyView> filterMatchingProperties(List<ConsolidatedPropertyView> matchingProperties, PropertySearchCriteria searchCriteria) {
        if (matchingProperties == null || matchingProperties.isEmpty()) {
            return matchingProperties;
        }

        List<ConsolidatedPropertyView> filteredProperties = filterAuthorized.filter(matchingProperties, searchCriteria);
        filteredProperties = filterMinExtracts.filter(filteredProperties, searchCriteria);
        filteredProperties = filterReadyForCatchup.filter(filteredProperties, searchCriteria);
        filteredProperties = filterReadyForPopulation.filter(filteredProperties, searchCriteria);
        return filteredProperties;
    }

    public List<Property> createPropertyDtosFromMatchingProperties(List<ConsolidatedPropertyView> properties) {
        List<Property> propertyDtos = new ArrayList<>();
        long start = System.currentTimeMillis();

        List<Integer> dummyPropertyIds;
        List<Integer> propertyIds = properties.stream().map(ConsolidatedPropertyView::getPropertyId).collect(Collectors.toList());
        if (StringUtils.equalsIgnoreCase("Hilton", PacmanWorkContextHelper.getClientCode())) {
            dummyPropertyIds = virtualPropertyMappingService.findDummyPropertyIds();
        } else {
            dummyPropertyIds = Collections.emptyList();
        }

        Map<Integer, Date> systemDates = dateService.getPropertyCaughtUpDateMap(
                propertyIds.stream().filter(propertyId -> !dummyPropertyIds.contains(propertyId))
                        .collect(Collectors.toList()));

        for (ConsolidatedPropertyView consolidatedPropertyView : properties) {
            if (!dummyPropertyIds.contains(consolidatedPropertyView.getPropertyId())) {
                PacmanWorkContextHelper.setPropertyId(consolidatedPropertyView.getPropertyId());
                PacmanWorkContextHelper.setPropertyCode(consolidatedPropertyView.getPropertyCode());
                propertyDtos.add(createDto(consolidatedPropertyView, systemDates.get(consolidatedPropertyView.getPropertyId())));
            }
        }

        LOGGER.info("Total time for creating property dtos took " + (System.currentTimeMillis() - start) + " ms");
        return propertyDtos;
    }

    @SuppressWarnings("unchecked")
    private List<ConsolidatedPropertyView> findByPropertyCode(List<String> propertyCodes) {
        List<ConsolidatedPropertyView> matchingProperties = new ArrayList<>();
        if (propertyCodes.size() > 1) {
            matchingProperties = globalCrudService.findByNamedQuery(ConsolidatedPropertyView.BY_CLIENT_ID_AND_PROPERTY_CODES,
                    QueryParameter.with(ConsolidatedPropertyView.PARAM_CLIENT_ID, getClientId()).
                            and(ConsolidatedPropertyView.PARAM_PROP_CODES, propertyCodes).parameters());
        } else {
            ConsolidatedPropertyView matchingProperty = globalCrudService.findByNamedQuerySingleResult(
                    ConsolidatedPropertyView.BY_CLIENT_ID_AND_PROPERTY_CODE,
                    QueryParameter.with(ConsolidatedPropertyView.PARAM_CLIENT_ID, getClientId()).
                            and(ConsolidatedPropertyView.PARAM_PROP_CODE, propertyCodes.get(0)).parameters());
            if (matchingProperty != null) {
                matchingProperties.add(matchingProperty);
            }
        }
        return matchingProperties;
    }

    private Predicate getDatePredicate(CriteriaBuilder cb, Expression<Date> expression, DateRange dateRange) {
        try {
            DateParameter startDateParameter = dateRange.getStartDate();
            DateParameter endDateParameter = dateRange.getEndDate();
            if (startDateParameter != null) {
                if (endDateParameter != null) {
                    // if both...between dates but make sure start date is beginning of day and end date is end of day
                    LocalDateTime endOfDay = new LocalDateTime(endDateParameter.getTime()).millisOfDay().withMaximumValue();
                    return cb.between(expression, startDateParameter.getTime(), endOfDay.toDate());
                } else {
                    // if just start date...where after date
                    return cb.greaterThanOrEqualTo(expression, startDateParameter.getTime());
                }
            } else {
                // if just end date...where before date but make sure end date is end of day
                LocalDateTime endOfDay = new LocalDateTime(endDateParameter.getTime()).millisOfDay().withMaximumValue();
                return cb.lessThanOrEqualTo(expression, endOfDay.toDate());
            }
        } catch (IllegalFieldValueException e) {
            //the flex screen passes in buggy date data that crashes.  handle the exception and just show everything within 200 years if there's an error
            //todo: remove this try/catch block once installation status flex screen is gone
            LOGGER.error(e.getMessage(), e);
            return cb.between(expression, new LocalDate().minusYears(100).toDate(), new LocalDate().plusYears(100).toDate());
        }
    }

    private List<ConsolidatedPropertyView> findByCriteria(PropertySearchCriteria criteria) {
        List<ConsolidatedPropertyView> properties = new ArrayList<>();

        EntityManager em = globalCrudService.getEntityManager();
        CriteriaBuilder criteriaBuilder = em.getCriteriaBuilder();

        CriteriaQuery<ConsolidatedPropertyView> criteriaQuery = criteriaBuilder.createQuery(ConsolidatedPropertyView.class);
        Root<ConsolidatedPropertyView> property = criteriaQuery.from(ConsolidatedPropertyView.class);

        List<Predicate> criteriaList = new ArrayList<>();

        Expression<Integer> clientId = property.get("clientId");
        criteriaList.add(criteriaBuilder.equal(clientId, getClientId()));

        if (criteria.getHandoffDueDateRange() != null && !criteria.getHandoffDueDateRange().isEmpty()) {
            Expression<Date> setupCompletionDate = property.get("setupCompletionDate");
            criteriaList.add(getDatePredicate(criteriaBuilder, setupCompletionDate, criteria.getHandoffDueDateRange()));
        }

        if (criteria.getScheduledTwoWayDateRange() != null && !criteria.getScheduledTwoWayDateRange().isEmpty()) {
            Expression<Date> scheduledTwoWayDate = property.get("scheduledTwoWayDate");
            criteriaList.add(getDatePredicate(criteriaBuilder, scheduledTwoWayDate, criteria.getScheduledTwoWayDateRange()));
        }

        if (criteria.getActualTwoWayDateRange() != null && !criteria.getActualTwoWayDateRange().isEmpty()) {
            Expression<Date> actualTwoWayDate = property.get("twoWayDate");
            criteriaList.add(getDatePredicate(criteriaBuilder, actualTwoWayDate, criteria.getActualTwoWayDateRange()));
        }

        if (criteria.getStages() != null && !criteria.getStages().isEmpty()) {
            Expression<String> stage = property.get("stage");
            criteriaList.add(criteria.getStages().size() == 1 ? criteriaBuilder.equal(stage, criteria.getStages().get(0)) : stage.in(criteria.getStages()));
        }

        criteriaQuery.select(property).where(criteriaBuilder.and(criteriaList.toArray(new Predicate[0])));

        TypedQuery<ConsolidatedPropertyView> query = em.createQuery(criteriaQuery);
        List<ConsolidatedPropertyView> results = query.getResultList();
        if (results != null) {
            properties.addAll(results);
        }
        return properties;
    }

    private Property createDto(ConfigurationFileRecord record) {
        String propertyCode = record.getPropertyCode().trim();
        Property property = new Property();
        property.setCode(propertyCode);
        String[] segments = record.getRecord().split("[|]");
        property.setName(segments[PROPERTY_NAME_SEGMENT]);
        if (segments.length > WEB_RATE_ALIAS_SEGMENT) {
            property.setPropertyTimeZone(segments[PROPERTY_TIME_ZONE_SEGMENT].trim());
            property.setYieldCurrency(segments[YIELD_CURRENCY_SEGMENT].trim());
            property.setCrsTimeZone(segments[CRS_TIME_ZONE_SEGMENT].trim());
            property.setWebRateAlias(segments[WEB_RATE_ALIAS_SEGMENT].trim());
        }
        property.setEligibleForConfigure(false);
        return property;
    }

    private Property createDto(ConsolidatedPropertyView consolidatedPropertyView, Date systemDate) {
        long start = System.currentTimeMillis();
        Property property = new Property();
        String propertyCode = consolidatedPropertyView.getPropertyCode();
        property.setId(consolidatedPropertyView.getPropertyId());
        property.setCode(propertyCode);
        property.setName(consolidatedPropertyView.getPropertyName());
        property.setStage(consolidatedPropertyView.getStage() != null ? consolidatedPropertyView.getStage().getCode() : null);

        property.setSrpAttributionExtractDate(DateParameter.fromDate(consolidatedPropertyView.getSrpAttributionExtractDate()));
        property.setSrpAttributionSubmittedDate(DateParameter.fromDate(consolidatedPropertyView.getSrpAttributionSubmittedDate()));
        property.setSetupCompletionDate(DateParameter.fromDate(consolidatedPropertyView.getSetupCompletionDate()));

        property.setScheduledTwoWayDate(DateParameter.fromDate(consolidatedPropertyView.getScheduledTwoWayDate()));
        property.setActualTwoWayDate(DateParameter.fromDate(consolidatedPropertyView.getTwoWayDate()));
        property.setStageChangedToCatchupDate(DateParameter.fromDate(consolidatedPropertyView.getCatchupDate()));
        property.setStageChangedToPopulationDate(DateParameter.fromDate(consolidatedPropertyView.getPopulationDate()));
        property.setStageChangedToOneWayDate(DateParameter.fromDate(consolidatedPropertyView.getOneWayDate()));

        property.setExternalSystem(consolidatedPropertyView.getExternalSystem());
        property.setPropertyTimeZone(consolidatedPropertyView.getPropertyTimeZone());
        property.setWebRateAlias(consolidatedPropertyView.getWebRateAlias());

        property.setConfigFileName(consolidatedPropertyView.getConfigFileName());
        property.setConfigFileLoadDate(DateParameter.fromDate(consolidatedPropertyView.getConfigFileLoadDate()));
        property.setOutOfOrderRecordsHaveBeenLoaded(consolidatedPropertyView.isOutOfOrderRecordsLoaded());
        property.setPendingOORecordCount(consolidatedPropertyView.getPendingOORecordCount());
        property.setLdb(consolidatedPropertyView.isLdbEnabled());
        property.setVirtualProperty(consolidatedPropertyView.isVirtualProperty());
        property.setVirtualPropertyDisplayCode(consolidatedPropertyView.getVirtualPropertyDisplayCode());
        property.setUpsId(consolidatedPropertyView.getUpsId());

        com.ideas.tetris.platform.services.daoandentities.entity.Property disconnectedProperty = consolidatedPropertyView.toProperty();
        ExtractDetails extractDetails = extractDetailsService.getExtractDetails(disconnectedProperty);
        WebRateExtractDetails webRateExtractDetails = extractDetailsService.getWebRateExtractDetails(disconnectedProperty);
        if (extractDetails != null) {
            property.setHistoricalExtractDate(extractDetails.getHistoricalExtractDate());
            property.setMissingDates(extractDetails.getMissingDates());
            property.setNumberOfIncomingExtracts(extractDetails.getNumberOfIncomingExtracts());
            property.setNumberOfArchivedExtracts(extractDetails.getNumberOfArchivedExtracts());
            property.setFirstIncomingExtractDate(extractDetails.getFirstIncomingExtractDate());
            property.setLastIncomingExtractDate(extractDetails.getLastIncomingExtractDate());
        }
        if (webRateExtractDetails != null) {
            property.setNumberOfIncomingWebRateExtracts(webRateExtractDetails.getNumberOfIncomingExtracts());
            property.setFirstIncomingWebRateExtractDate(webRateExtractDetails.getFirstIncomingExtractDate());
            property.setLastIncomingWebRateExtractDate(webRateExtractDetails.getLastIncomingExtractDate());
        }

        property.setNumberOfArchivedWebRateExtracts(getProcessedWebrateExtractCount(property.getId()).intValue());

        property.setSystemDate(DateParameter.fromDate(systemDate));

        property.setCatchupData(getMostRecentCatchup(consolidatedPropertyView));
        property.verifyCatchupEligibility(extractDetails, webRateExtractDetails);

        property.setConfigurationComplete(consolidatedPropertyView.isConfigurationComplete());
        property.verifyConfigureEligibility(extractDetails, webRateExtractDetails);

        LOGGER.info("populateProperty() for property " + property.getCode() + " elapsed time = " + (System.currentTimeMillis() - start) + " msecs");
        return property;
    }

    private Integer getClientId() {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        return workContext.getClientId();
    }

    private CatchupData getMostRecentCatchup(ConsolidatedPropertyView view) {
        CatchupData mostRecentCatchup = new CatchupData();
        mostRecentCatchup.setDetailId(view.getCatchupDetailId());
        mostRecentCatchup.setProcessEndDate(view.getCatchupEndDate());
        mostRecentCatchup.setProcessStartDate(view.getCatchupStartDate());
        mostRecentCatchup.setUser(view.getCatchupInitiatedBy());
        if (view.getCatchupStartDate() != null) {
            mostRecentCatchup.setStatus(view.getCatchupEndDate() == null ? CatchupStatus.IN_PROGRESS : CatchupStatus.COMPLETE);
        }
        return mostRecentCatchup;
    }

    public Long getProcessedWebrateExtractCount(int propertyId) {
        List<Long> result = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId,
                FileMetadata.COUNT_BY_PROPERTY_RECORD_TYPE_AND_STATUS,
                QueryParameter.with(FileMetadata.PARAM_PROPERTY_ID, propertyId)
                        .and(FileMetadata.PARAM_RECORD_TYPE_ID, WEBRATE_RECORD_TYPE_ID)
                        .and(FileMetadata.PARAM_PROCESS_STATUS, SUCCESSFUL).parameters());
        return result.get(0);
    }
}
