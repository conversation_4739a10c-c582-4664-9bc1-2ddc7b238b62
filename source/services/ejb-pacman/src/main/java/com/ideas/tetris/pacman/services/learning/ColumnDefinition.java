package com.ideas.tetris.pacman.services.learning;

import com.ideas.tetris.platform.common.crudservice.RowMapper;

public class ColumnDefinition implements <PERSON>Mapper<ColumnDefinition> {
    private String schemaName;
    private String tableName;
    private String columnName;

    public String getSchemaName() {
        return schemaName;
    }

    public void setSchemaName(String schemaName) {
        this.schemaName = schemaName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    @Override
    public ColumnDefinition mapRow(Object[] row) {
        ColumnDefinition columnDefinition = new ColumnDefinition();
        columnDefinition.setSchemaName((String) row[0]);
        columnDefinition.setTableName((String) row[1]);
        columnDefinition.setColumnName((String) row[2]);
        return columnDefinition;
    }
}

