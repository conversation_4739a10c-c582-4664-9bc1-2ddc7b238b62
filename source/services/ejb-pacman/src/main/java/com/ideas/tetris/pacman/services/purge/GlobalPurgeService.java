package com.ideas.tetris.pacman.services.purge;

import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class GlobalPurgeService {
    private static final Logger LOGGER = Logger.getLogger(GlobalPurgeService.class);

    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService crudService;
    @Autowired
    JobServiceLocal jobService;

    public int purgeTable(String sql, Map<String, Object> parameters, boolean failOnException) {
        int totalRowsDeleted = 0;
        long start = System.currentTimeMillis();

        try {
            totalRowsDeleted += crudService.executeUpdateByNativeQuery(sql, parameters);

            long elapsed = System.currentTimeMillis() - start;
            LOGGER.info("Deleted " + totalRowsDeleted + " rows for: [" + sql + "] in " + elapsed + "ms");
        } catch (Exception e) {
            if (failOnException) {
                throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error occurred during purge operation", e);
            } else {
                LOGGER.warn("Purge operation failed, but ignored", e);
            }
        }

        return totalRowsDeleted;
    }

    public void startPurgeJob() {
        HashMap<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.DATE, DateUtil.formatDate(DateUtil.getCurrentDate(),
                DateUtil.DATE_TIME_MILLIS_FORMAT));
        jobService.startJob(JobName.PurgeOldGlobalDataJob, parameters);
    }
}
