package com.ideas.tetris.pacman.services.minlos;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Component
@Transactional
public class MinlosRecommendationService extends AbstractMinlosRecommendationService {

    public void createMinlosDecisions() {
        super.createMinlosDecisions();
    }

    @Override
	public
    Date getOptimizationWindowEndDate() {
        return dateService.getDecisionUploadWindowEndDate();
    }
}
