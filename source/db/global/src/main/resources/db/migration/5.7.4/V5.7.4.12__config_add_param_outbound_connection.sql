use Global;
IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Group]
	WHERE
		Group_Name      = 'DefaultVendor'
		AND Category_Id =
		(
			SELECT
				Category_Id
			FROM
				[dbo].[Config_Parameter_Category]
			WHERE
				category_Name = 'Outbound Connection Details'
		)
)
INSERT INTO [dbo].[Config_Parameter_Group]
	([Category_Id]
		,[Group_Name]
		,[Description]
		,[CreateDate]
		,[ModIFiedDate]
	)
	VALUES
	(
	(
		SELECT
			Category_Id
		FROM
			[dbo].[Config_Parameter_Category]
		WHERE
			category_Name = 'Outbound Connection Details'
	)
	,'DefaultVendor'
	,'Connection Details group'
	,getdate()
	,getdate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.DailyBAR.alternateURL'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.DailyBAR.alternateURL'
		,'An alternate url to which to send DailyBAR decisions'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.DailyBAR.alternateURL'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[FixedValue]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.DailyBAR.alternateURL'
	)
	,'pacman'
	, NULL
	, NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.LRVatRoomType.alternateURL'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.LRVatRoomType.alternateURL'
		,'An alternate url to which to send LRVatRoomType decisions'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.LRVatRoomType.alternateURL'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.LRVatRoomType.alternateURL'
	)
	,'pacman'
	,NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.LRVatRoomClass.alternateURL'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.LRVatRoomClass.alternateURL'
		,'An alternate url to which to send LRVatRoomClass decisions'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.LRVatRoomClass.alternateURL'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.LRVatRoomClass.alternateURL'
	)
	,'pacman'
	,NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.BarByLOSatRoomType.alternateURL'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.BarByLOSatRoomType.alternateURL'
		,'An alternate url to which to send BarByLOSatRoomType decisions'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.BarByLOSatRoomType.alternateURL'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.BarByLOSatRoomType.alternateURL'
	)
	,'pacman'
	,NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.BarByLOSatRoomClass.alternateURL'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.BarByLOSatRoomClass.alternateURL'
		,'An alternate url to which to send BarByLOSatRoomClass decisions'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.BarByLOSatRoomClass.alternateURL'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.BarByLOSatRoomClass.alternateURL'
	)
	,'pacman'
	,NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.RoomTypeOverbooking.alternateURL'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.RoomTypeOverbooking.alternateURL'
		,'An alternate url to which to send RoomTypeOverbooking decisions'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.RoomTypeOverbooking.alternateURL'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.RoomTypeOverbooking.alternateURL'
	)
	,'pacman'
	,NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.HotelOverbooking.alternateURL'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.HotelOverbooking.alternateURL'
		,'An alternate url to which to send HotelOverbooking decisions'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.HotelOverbooking.alternateURL'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.HotelOverbooking.alternateURL'
	)
	,'pacman'
	,NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.Fplos.alternateURL'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.Fplos.alternateURL'
		,'An alternate url to which to send Fplos decisions'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.Fplos.alternateURL'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.Fplos.alternateURL'
	)
	,'pacman'
	,NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;;
IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.customAvailAction'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.customAvailAction'
		,'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.customAvailAction'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[FixedValue]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.customAvailAction'
	)
	,'pacman'
	,NULL
	,NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.customRateAction'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.customRateAction'
		,'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.customRateAction'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[FixedValue]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.customRateAction'
	)
	,'pacman'
	,NULL
	,NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.password'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.password'
		,'integration password'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.password'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[FixedValue]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.password'
	)
	,'pacman'
	,NULL
	,NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.replyTo'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.replyTo'
		,'The url of the local callback server'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.replyTo'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[FixedValue]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.replyTo'
	)
	,'pacman'
	,NULL
	,NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.url'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.url'
		,'Integration URL'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.url'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[FixedValue]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.url'
	)
	,'pacman'
	,NULL
	,NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.useSoap2'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.useSoap2'
		,'Set to true to use SOAP 1.2 protocol'
		, (
			SELECT
				Config_Parameter_Predefined_Value_Type_ID
			FROM
				[dbo].[Config_Parameter_Predefined_Value_Type]
			WHERE
				code ='boolean'
		)
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'boolean'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.useSoap2'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[FixedValue]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.useSoap2'
	)
	,'pacman'
	,NULL
	,(
		SELECT
			[Config_Parameter_Predefined_Value_ID]
		FROM
			[dbo].[Config_Parameter_Predefined_Value]
		WHERE
			value = 'false'
	)
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter]
	WHERE
		Name = 'pacman.integration.username'
)
INSERT INTO [dbo].[Config_Parameter]
	([Name]
		,[Description]
		,[Config_Parameter_Predefined_Value_Type_ID]
		,[CreateDate]
		,[ModIFiedDate]
		,[Config_Parameter_Type_ID]
		,[Group_ID]
	)
	VALUES
	('pacman.integration.username'
		,'Integration username'
		, NULL
		,getdate()
		,getdate()
		,(
			SELECT
				TOP 1 param_type_id
			FROM
				[dbo].[Config_Parameter_Type]
			WHERE
				Param_Type = 'Char'
		)
		,(
			SELECT
				Group_ID
			FROM
				[dbo].[Config_Parameter_Group]
			WHERE
				Group_Name      = 'DefaultVendor'
				AND Category_ID =
				(
					SELECT
						Category_Id
					FROM
						Config_Parameter_Category
					WHERE
						Category_Name = 'Outbound Connection Details'
				)
		)
	)
;

IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Value]
	WHERE
		[Config_Parameter_ID] =
		(
			SELECT
				TOP 1 [Config_Parameter_ID]
			FROM
				[dbo].[Config_Parameter]
			WHERE
				Name = 'pacman.integration.username'
		)
		AND context = 'pacman'
)
INSERT INTO [dbo].[Config_Parameter_Value]
	([Config_Parameter_ID]
		,[Context]
		,[FixedValue]
		,[Config_Parameter_Predefined_Value_ID]
		,[Created_By_User_ID]
		,[Created_DTTM]
		,[Last_Updated_By_User_ID]
		,[Last_Updated_DTTM]
	)
	VALUES
	(
	(
		SELECT
			TOP 1 [Config_Parameter_ID]
		FROM
			[dbo].[Config_Parameter]
		WHERE
			Name = 'pacman.integration.username'
	)
	,'pacman'
	,NULL
	,NULL
	,(
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	, (
		SELECT
			TOP 1 [User_ID]
		FROM
			[dbo].[Users]
		WHERE
			email_address = '<EMAIL>'
	)
	,getDate()
	)
;
