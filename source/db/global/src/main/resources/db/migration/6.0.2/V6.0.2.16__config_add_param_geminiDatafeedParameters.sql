use Global;
IF NOT EXISTS
(
	SELECT
		1
	FROM
		[dbo].[Config_Parameter_Group]
	WHERE
		Group_Name      = 'Gemini DataFeed'
		AND category_id =
		(
			SELECT
				category_id
			FROM
				[dbo].[Config_Parameter_Category]
			WHERE
				category_Name = 'Outbound Data'
		)
)
INSERT INTO [dbo].[Config_Parameter_Group]
	([Category_Id]
		,[Group_Name]
		,[Description]
		,[CreateDate]
		,[ModifiedDate]
	)
	VALUES
	(
	(
		SELECT
			category_id
		FROM
			[dbo].[Config_Parameter_Category]
		WHERE
			category_Name = 'Outbound Data'
	)
	,'Gemini DataFeed'
	,'Outbound Gemini Datafeed group'
	,getdate()
	,getdate()
	)
;


if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.host')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.GeminiDF.ftp.host',
 'This parameter sets the host name for gemini datafeed ftp location.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'Gemini DataFeed' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 '')
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.host') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.host'),
 'pacman',
 '',
 null, getdate(),
 getdate())
 
END;


if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.port')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.GeminiDF.ftp.port',
 'This parameter sets the host port number for gemini datafeed ftp location.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'Gemini DataFeed' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 '')
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.port') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.port'),
 'pacman',
 '',
 null, getdate(),
 getdate())
 
END;


if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.username')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.GeminiDF.ftp.username',
 'This parameter sets the username to access gemini ftp host.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'Gemini DataFeed' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 '')
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.username') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.username'),
 'pacman',
 '',
 null, getdate(),
 getdate())
 
END;


if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.password')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.GeminiDF.ftp.password',
 ' This parameter sets the Password to access gemini ftp host.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'Gemini DataFeed' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 '')
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.password') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.password'),
 'pacman',
 '',
 null, getdate(),
 getdate())
 
END;


if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.sftp.certificateFileName')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.GeminiDF.sftp.certificateFileName',
 ' This parameter sets the certificate file name for gemini datafeed ftp host.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'Gemini DataFeed' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 '')
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.sftp.certificateFileName') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.sftp.certificateFileName'),
 'pacman',
 '',
 null, getdate(),
 getdate())
 
END;


if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.sftp.privateKeyPassPhrase')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.GeminiDF.sftp.privateKeyPassPhrase',
 ' This parameter sets the privateKeyPassPhrase for gemini datafeed ftp host access.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'Gemini DataFeed' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 '')
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.sftp.privateKeyPassPhrase') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.sftp.privateKeyPassPhrase'),
 'pacman',
 '',
 null, getdate(),
 getdate())
 
END;


if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.sftp.timeout.seconds')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.GeminiDF.sftp.timeout.seconds',
 ' This parameter sets the SFTP connection time out used by gemini datafeed at NGI side.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'Gemini DataFeed' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 '0')
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.sftp.timeout.seconds') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.sftp.timeout.seconds'),
 'pacman',
 '',
 null, getdate(),
 getdate())
 
END;


if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.remoteDirectory')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.GeminiDF.ftp.remoteDirectory',
 ' This parameter sets the SFTP directory location for gemini datafeeds.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'Gemini DataFeed' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 '')
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.remoteDirectory') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.ftp.remoteDirectory'),
 'pacman',
 '',
 null, getdate(),
 getdate())
 
END;


if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.minStage')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.GeminiDF.minStage',
 ' Starting Stage of property from where gemini datafeed file generation should be enabled.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'Gemini DataFeed' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 '')
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.minStage') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.GeminiDF.minStage'),
 'pacman',
 '',
 null, getdate(),
 getdate())
 
END;


if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.gemini.datafeed.bucket')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.gemini.datafeed.bucket',
 'This toggle is added to allocate client/property with set of gemini datafeed buckets.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'Gemini DataFeed' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 'coreGemini')
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.gemini.datafeed.bucket') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.gemini.datafeed.bucket'),
 'pacman',
 '',
 null, getdate(),
 getdate())
 
END;

