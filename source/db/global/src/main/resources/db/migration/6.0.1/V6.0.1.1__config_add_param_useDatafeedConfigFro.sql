if not exists (select 1 from [dbo].[Config_Parameter] where name = 'useDatafeedConfigFromDB')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'useDatafeedConfigFromDB',
 'Provide functionality to use Datafeed configuration from DB',
 (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'Pre-production' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Feature Toggles')  ) ,
 'null',
 'false')
 
INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'useDatafeedConfigFromDB'),
 (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
 'Use Pre-defined Data Feed Endpoint Configuration ',
 getdate())
 
INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'useDatafeedConfigFromDB'),
 (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
 'Use Data Feed Endpoint Configuration from DB',
 getdate())
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'useDatafeedConfigFromDB') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'useDatafeedConfigFromDB'),
 'pacman',
 null,
 (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'false'), getdate(),
 getdate())
 
END;
