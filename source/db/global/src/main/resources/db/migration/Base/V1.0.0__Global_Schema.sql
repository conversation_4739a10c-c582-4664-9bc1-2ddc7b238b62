SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE FUNCTION [dbo].[getFqnParameters]()
RETURNS @resultTable TABLE 
(
    -- Columns returned by the function
    Param_ID int PRIMARY KEY NOT NULL, 
    Fqn nvarchar(250) NULL
)
AS 
-- Returns the first name, last name, job title, and contact type for the specified contact.
BEGIN
declare @mycounter int 
set @mycounter=0 
	DECLARE @TempNote TABLE  (Param_Node_ID INT, Param_ID int, param_name nvarchar(250)) 
	DECLARE @TempPath TABLE  (fully_qualified_parameter_name nvarchar(250) ) 

	insert into @TempNote select Param_Node_ID, param_id, param_name from Parameter 
	--where Param_Name=@parameterName 
	
declare @concatString nvarchar(250) 
declare @currentNodeID int 
declare @currentParamID int 
declare @parentNodeID int 

	while (select count(*) from @TempNote)>0
	begin
		set @concatString=(select  top(1)Param_Name  from @TempNote)
		set @parentNodeID=0 
		set @mycounter=@mycounter+1 
		set @currentNodeID = (select  top(1)Param_Node_ID  from @TempNote) 
		set @currentParamID = (select  top(1)Param_ID  from @TempNote) 
		set @concatString=(select Node_Name from Parameter_Node where Param_Node_ID=@currentNodeID)+'.'+@concatString 
		
		while(@parentNodeID is not null)
		begin
			set @parentNodeID=(select Parent_Param_Node_ID from Parameter_Node where Param_Node_ID=@currentNodeID)
			if(@parentNodeID is NULL)
				break 
			set @concatString=(select Node_Name from Parameter_Node where Param_Node_ID=@parentNodeID)+'.'+@concatString 
			set @currentNodeID=@parentNodeID 
		end 
		delete top(1) from @TempNote
        INSERT into @resultTable
        SELECT @currentParamID, @concatString
		--insert into @TempPath select @currentParamID, @concatString 
	end 
    RETURN
END
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE FUNCTION [dbo].[getFqnParameterValueNodes]()
RETURNS @resultTable TABLE 
(
    -- Columns returned by the function
    Param_Val_Node_ID int PRIMARY KEY NOT NULL, 
    Fqn nvarchar(250) NULL
)
AS 
-- Returns the first name, last name, job title, and contact type for the specified contact.
BEGIN

	DECLARE @TempNote TABLE (Param_Val_Node_ID INT, Node_Name nvarchar(250)) 
	DECLARE @TempPath TABLE (fully_qualified_property_name nvarchar(250) PRIMARY KEY) 

	insert into @TempNote 
	select Param_Val_Node_ID, Node_Name from Parameter_Value_Node 
	--where Node_Name=@propertyName
	
declare @concatString nvarchar(250) 
declare @currentNodeID int 
declare @originalNodeID int 
declare @parentNodeID int 

	while (select count(*) from @TempNote)>0
	begin
		set @concatString=(select  top(1)Node_Name  from @TempNote)
		set @parentNodeID=0 
		set @currentNodeID = (select  top(1)Param_Val_Node_ID  from @TempNote) 
		set @originalNodeID = @currentNodeID 
				
		while(@parentNodeID is not null)
		begin
			set @parentNodeID=(select Parent_Param_Val_Node_ID from Parameter_Value_Node where Param_Val_Node_ID=@currentNodeID)
			if(@parentNodeID is NULL)
				break 
			set @concatString=(select Node_Name from Parameter_Value_Node where Param_Val_Node_ID=@parentNodeID)+'.'+@concatString 
			set @currentNodeID=@parentNodeID 
		end 
		delete top(1) from @TempNote
		
        INSERT into @resultTable
        SELECT @originalNodeID, @concatString
		
--		insert into @TempPath select @concatString	 
	end 
    RETURN
END
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE function [dbo].[getParameterID] (@parameterName nvarchar(250))
RETURNS INT
AS
begin 

declare @start_index int
declare @end_index int
declare @parentNodeId int
declare @nodeId int
declare @parameterId int
declare @param nvarchar(250)
set @start_index=1
set @parameterId=0
set @parentNodeId=0

	while (select CHARINDEX('.',@parameterName,@start_index)) > 0
	begin
		set @end_index=(select CHARINDEX('.',@parameterName,@start_index))	
		set @param = (select SUBSTRING(@parameterName,@start_index,@end_index-@start_index) )

		if (@start_index = 1)		
			begin
				set @nodeId = (select Param_Node_ID from Parameter_Node 
				where node_name=@param and Parent_Param_Node_ID is null		)
			end
		else
			begin
				set @nodeId = (select Param_Node_ID from Parameter_Node 
				where node_name=@param and Parent_Param_Node_ID=@parentNodeId) 
			end
		
		set @parentNodeId=@nodeId 
		set @start_index=@end_index+1 
		
	end
	set @param = (select SUBSTRING(@parameterName,@start_index,9999) ) 
	if(@parentNodeId=0)
		set @parameterId=(select Param_ID from Parameter where Param_Name=@param and Param_Node_ID is null)	
	else
		set @parameterId=(select Param_ID from Parameter where Param_Name=@param and Param_Node_ID=@parentNodeId)	
	RETURN @parameterId 

end 
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE function [dbo].[getParameterID2] (@parameterName nvarchar(250))
RETURNS INT
AS
BEGIN
	declare @parameterId int 
	
	set @parameterId=(select Config_Parameter_ID from Config_Parameter where Name=@parameterName)	
	RETURN @parameterId 

END
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE function [dbo].[getParameterNodeValueID] (@node nvarchar(250))
RETURNS INT
AS
begin 

declare @start_index int 
declare @end_index int 
declare @parentNodeId int 
declare @nodeId int 
declare @parameterId int 
declare @param nvarchar(250) 
set @start_index=1 
set @parameterId=0 
set @parentNodeId=0 

	while (select CHARINDEX('.',@node,@start_index)) > 0
	begin
		set @end_index=(select CHARINDEX('.',@node,@start_index)) 	
		set @param = (select SUBSTRING(@node,@start_index,@end_index-@start_index) ) 

		if (@start_index = 1)		
			begin
				set @nodeId = (select Param_Val_Node_ID from Parameter_Value_Node where Node_Name=@param
and Parent_Param_Val_Node_ID is null) 
			end
		else
			begin
				set @nodeId = (select Param_Val_Node_ID from Parameter_Value_Node where Node_Name=@param
and Parent_Param_Val_Node_ID=@parentNodeId) 
			end
		
		set @parentNodeId=@nodeId 
		set @start_index=@end_index+1 
		
	end
	set @param = (select SUBSTRING(@node,@start_index,9999) ) 
	if(@parentNodeId=0)
		begin
			set @parameterId=(select Param_Val_Node_ID from Parameter_Value_Node where Node_Name=@param
and Parent_Param_Val_Node_ID is null) 
		end
	else
		begin
			set @parameterId=(select Param_Val_Node_ID from Parameter_Value_Node where Node_Name=@param
and Parent_Param_Val_Node_ID=@parentNodeId)	 
		end
	
	RETURN @parameterId 

end 
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
 

CREATE function [dbo].[getParameterValue] (@parameter nvarchar(250),@node nvarchar(250), @hierarchy int)
RETURNS nvarchar(250)
AS
begin 

declare @parameterId int 
declare @paramValNodeID int 
declare @value nvarchar(250) 

declare @start_index int 
declare @end_index int 
declare @parentNodeId int 
declare @nodeId int 

set @start_index=1 
set @parameterId=0 
set @parentNodeId=0 


	set @parameterId=(select dbo.getParameterID(@parameter)) 


	set @paramValNodeID=(select dbo.getParameterNodeValueID(@node)) 
	set @value=(select value from Parameter_Value where Param_ID=@parameterId and Param_Val_Node_ID=@paramValNodeID)
	
	if(@hierarchy = 1)
	begin
	
		
		if(@value is null)
		begin
			set @end_index=(select CHARINDEX('.',@node,0)) 	
			set @end_index=(select CHARINDEX('.',@node,@end_index+1)) 	
			
			set @paramValNodeID=(select dbo.getParameterNodeValueID(SUBSTRING(@node,0,@end_index))) 
			set @value=(select value from Parameter_Value where Param_ID=@parameterId and Param_Val_Node_ID=@paramValNodeID)
	
			if(@value is null)
				begin
					set @end_index=(select CHARINDEX('.',@node,0)) 	
					set @paramValNodeID=(select dbo.getParameterNodeValueID(SUBSTRING(@node,0,@end_index))) 
					set @value=(select value from Parameter_Value where Param_ID=@parameterId and Param_Val_Node_ID=@paramValNodeID)
				end
		end
	end

	RETURN @value 

end 
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE function [dbo].[getParameterValue2] (@parameter nvarchar(250),@context nvarchar(250))
RETURNS nvarchar(250)
AS
begin 

declare @parameterId int 
declare @predefinedValueId int 
declare @value nvarchar(250) 

	set @parameterId=(select dbo.getParameterID2(@parameter)) 

	set @predefinedValueId=(select [Config_Parameter_Predefined_Value_ID] from dbo.Config_Parameter_Value where Config_Parameter_ID=@parameterId and Context = @context)

	if(@predefinedValueId is null)
		BEGIN
			set @value=(select FixedValue from dbo.Config_Parameter_Value where Config_Parameter_ID=@parameterId and Context = @context)
		
			if(@value is null)
			begin
				/* try higher scope bar.foo.123 -> bar.foo ->bar*/
				if (CHARINDEX('.', @context) = 0)
					BEGIN
					set @context = (select left(@context, len(@context) - charindex('.', reverse(@context))))
					if (len(@context) > 0)
						BEGIN
							set @value=(select dbo.getParameterValue2(@parameter, @context))
						END
					END
			end
		END
	else
		BEGIN
			set @value=(select Value from [dbo].[Config_Parameter_Predefined_Value] where [Config_Parameter_Predefined_Value_ID]=@predefinedValueId)
		END
	
	RETURN @value 

end
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE FUNCTION [dbo].[Split](@String nvarchar(max),@Delimiter char(1))       
returns @temptable TABLE (id int IDENTITY,items nvarchar(max))       
as       
begin       
    declare @idx int       
    declare @slice nvarchar(max)       
      
    select @idx = 1       
        if len(@String)<1 or @String is null  return       
      
    while @idx!= 0       
    begin       
        set @idx = charindex(@Delimiter,@String)       
        if @idx!=0       
            set @slice = left(@String,@idx - 1)       
        else       
            set @slice = @String       
          
        if(len(@slice)>0)  
            insert into @temptable(Items) values(@slice)       
  
        set @String = right(@String,len(@String) - @idx)       
        if len(@String) = 0 break       
    end   
return       
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE function [dbo].[ufn_calculate_new_offset_for_bde_received_overdue]
(
	@client_code nvarchar(50),
	@property_code nvarchar(50),
	@startdate Date,
	@enddate Date
)
RETURNS nvarchar(50)
AS
BEGIN 

declare @std decimal
declare @mean decimal
declare @offset decimal
declare @lower decimal
declare @higher decimal

select @std = STDEVP(DATEDIFF(MINUTE, CAST( Received_DTTM AS DATE ), Received_DTTM )),  
@mean = AVG(DATEDIFF(MINUTE, CAST( Received_DTTM AS DATE ), Received_DTTM))
from Input_Processing ip
inner join Property_Daily_Processing pdp
on ip.Property_Daily_Processing_ID = pdp.Property_Daily_Processing_ID
where ip.Input_Type = 'BDE' and pdp.Client_Code = @client_code and pdp.Property_Code = @property_code
and Processing_Date between @startdate and @enddate

select @lower = @mean - (2 * @std)
select @higher = @mean + (2 * @std)

select @offset = AVG( DATEDIFF(MINUTE, CAST( Received_DTTM AS DATE ), Received_DTTM ))
from Input_Processing ip
inner join Property_Daily_Processing pdp
on ip.Property_Daily_Processing_ID = pdp.Property_Daily_Processing_ID
where ip.Input_Type = 'BDE' and pdp.Client_Code = @client_code and pdp.Property_Code = @property_code
and Processing_Date between @startdate and @enddate 
and DATEDIFF(MINUTE, CAST( Received_DTTM AS DATE ), Received_DTTM) between @lower and @higher
RETURN @offset

END

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************

Function Name: ufn_get_absolute_dates_from_rolling_dates

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@start_date --> occupancy_start_date ('2011-07-01')
	@end_date --> occupancy_end_date ('2011-07-31')
	@accom_class_id --> for which we want to get pricing pace report
	@Webrate_Competitors_ID --> id associated with a webrate competitor
Ouput Parameter : NA

Execution: this is just an example
	Example 1: pricing pace Report
	-----------------------------------------
	select * from ufn_get_absolute_dates_from_rolling_dates ('2011-11-01' ,'2011-11-29',69)
	
	
Purpose: The purpose of this function is to get pricing pace report 
		 
Author: Atul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
06/18/2012		Atul				Shendye					Initial Version
***************************************************************************************/

create function [dbo].[ufn_get_absolute_dates_from_rolling_dates]

(
	@rolling_date nvarchar(40),
	@caughtupdate date
)		
returns  @absolute_date table
(	
	absolute_date date
)
as
begin
	declare @abs_date date 
	declare @dateOffset nvarchar(10)
	declare @lyDOWAdjustedCaughtupDate date 

	set @lyDOWAdjustedCaughtupDate = (select DateAdd(WEEK,-52,@caughtupdate))
	set @rolling_date = REPLACE(@rolling_date,' ','') -- removing any spaces if there
	
	if(PATINDEX('%today%',@rolling_date)>0)
	begin
		set @dateOffset = REPLACE(@rolling_date,'today','0')
		if(PATINDEX('%0+%',@dateOffset)>0)
		begin
			set @dateOffset = REPLACE(@dateOffset,'0+','')
			set @abs_date = (select DATEADD (day , cast(@dateOffset as int) , @caughtupdate ))
		end
		
		else if(PATINDEX('%0-%',@dateOffset)>0)
		begin
			set @dateOffset = REPLACE(@dateOffset,'0-','')
			set @abs_date = (select DATEADD (day , -cast(@dateOffset as int) , @caughtupdate ))
		end
		
		else
		begin
			set @abs_date = (select DATEADD (day , cast(@dateOffset as int) , @caughtupdate ))
		end
		
	end
	if(PATINDEX('LAST_UPDATED%',@rolling_date)>0)
	begin
		set @dateOffset = REPLACE(@rolling_date,'LAST_UPDATED','0')
		if(PATINDEX('%0-%',@dateOffset)>0)
		begin
			set @dateOffset = REPLACE(@dateOffset,'0-','')
		end
		set @dateOffset = @dateOffset+1
		set @abs_date = (select DATEADD (day , -cast(@dateOffset as int) , @caughtupdate ))
	end
	
	/*** LY_THIS_DAY implementation***/	
	
	if(PATINDEX('%LY_THIS_DAY%',@rolling_date)>0)
	begin
		set @dateOffset = REPLACE(@rolling_date,'LY_THIS_DAY','0')
		if(PATINDEX('%0+%',@dateOffset)>0)
		begin
			set @dateOffset = REPLACE(@dateOffset,'0+','')
			set @abs_date = (select DATEADD (day , cast(@dateOffset as int) , @lyDOWAdjustedCaughtupDate ))
		end
		
		else if(PATINDEX('%0-%',@dateOffset)>0)
		begin
			set @dateOffset = REPLACE(@dateOffset,'0-','')
			set @abs_date = (select DATEADD (day , -cast(@dateOffset as int) , @lyDOWAdjustedCaughtupDate ))
		end
		
		else
		begin
			set @abs_date = (select DATEADD (day , cast(@dateOffset as int) , @lyDOWAdjustedCaughtupDate ))
		end		
	end
	
	/*** LY_LAST_UPDATED implementation***/
	if(PATINDEX('%LY_LAST_UPDATED%',@rolling_date)>0)
	begin
		set @dateOffset = REPLACE(@rolling_date,'LY_LAST_UPDATED','0')
		if(PATINDEX('%0-%',@dateOffset)>0)
		begin
			set @dateOffset = REPLACE(@dateOffset,'0-','')
		end
		set @dateOffset = @dateOffset+1
		set @abs_date = (select DATEADD (day , -cast(@dateOffset as int) , @lyDOWAdjustedCaughtupDate ))
	end
	
	--Last year start of month
	if(PATINDEX('LY_START_OF_MONTH%', @rolling_date) > 0)
	begin
		set @dateOffset = REPLACE(@rolling_date, 'LY_START_OF_MONTH', '0')
		if(PATINDEX('%0-%', @dateOffset) > 0)							 
		begin
			set @dateOffset = REPLACE(@dateOffset, '0-', '')     
			set @dateOffset = 0-@dateOffset
		end
		else if(PATINDEX('%0+%', @dateOffset) > 0)
		begin
			set @dateOffset = REPLACE(@dateOffset, '0+', '')
		end
		set @abs_date = (select DATEADD(dd, -(DAY(DATEADD(mm, cast(@dateOffset as int), @caughtupdate))-1), DATEADD(yy, -1, DATEADD(mm, cast(@dateOffset as int), @caughtupdate))))
	end

	--Last year end of month
	if(PATINDEX('LY_END_OF_MONTH%', @rolling_date) > 0)
	begin
		set @dateOffset = REPLACE(@rolling_date, 'LY_END_OF_MONTH', '0')
		if(PATINDEX('%0-%', @dateOffset) > 0)
		begin
			set @dateOffset = REPLACE(@dateOffset, '0-', '')
			set @dateOffset = 0-@dateOffset
		end
		else if(PATINDEX('%0+%', @dateOffset) > 0)
		begin
			set @dateOffset = REPLACE(@dateOffset, '0+', '')
		end
		set @dateOffset = @dateOffset+1
		set @abs_date = (select DATEADD(dd, -(DAY(DATEADD(mm,cast(@dateOffset as int),@caughtupdate))), DATEADD(yy, -1, DATEADD(mm, cast(@dateOffset as int), @caughtupdate))))
	end
	
	if(PATINDEX('START_OF_MONTH%', @rolling_date) > 0)
	begin
		set @dateOffset = REPLACE(@rolling_date, 'START_OF_MONTH', '0')
		if(PATINDEX('%0-%', @dateOffset) > 0)
		begin
			set @dateOffset = REPLACE(@dateOffset, '0-', '')
			set @dateOffset = 0-@dateOffset
		end
		else if(PATINDEX('%0+%', @dateOffset) > 0)
		begin
			set @dateOffset = REPLACE(@dateOffset, '0+', '')
		end
		set @abs_date = (select DATEADD(dd,-(DAY(DATEADD(mm, cast(@dateOffset as int), @caughtupdate))-1),DATEADD(mm, cast(@dateOffset as int), @caughtupdate)))
	end

	if(PATINDEX('END_OF_MONTH%', @rolling_date) > 0)
	begin
		set @dateOffset = REPLACE(@rolling_date, 'END_OF_MONTH', '0')
		if(PATINDEX('%0-%', @dateOffset) > 0)
		begin
			set @dateOffset = REPLACE(@dateOffset, '0-', '')
			set @dateOffset = 0-@dateOffset
		end
		else if(PATINDEX('%0+%', @dateOffset) > 0)
		begin
			set @dateOffset = REPLACE(@dateOffset, '0+', '')
		end
		set @dateOffset = @dateOffset+1
		set @abs_date = (select DATEADD(dd,-(DAY(DATEADD(mm,cast(@dateOffset as int),@caughtupdate))),DATEADD(mm,cast(@dateOffset as int),@caughtupdate)))
	end

	if(PATINDEX('LAST_OPTIMIZATION',@rolling_date)>0)
		begin
		set @dateOffset = 1
		set @abs_date = (select DATEADD (day , -cast(@dateOffset as int) , @caughtupdate ))
	end


	insert into @absolute_date values(@abs_date)
		
	return
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************
Function Name: ufn_get_authgrp_property_role_details_for_user

Input Parameters : 
	@subMcat --> subMcat for which you need data
	
Ouput Parameter : NA

Execution: this is just an example
	Example : 
	------------------------------------------------
	
	
	Declare @usersID int;	
	Declare @client_code nvarchar(10);	
	
	set @usersID  = 6;
	set @client_code = 'bstn'	

	
	select * from ufn_get_authgrp_property_role_details_for_user(@usersID,@client_code)
	
Purpose: The purpose of this function is to get users base on the filter selection
		e.g Individual property access, authorization group, role.

	 
Author: Vinay

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
04/16/2014		Vinay				Patil					Initial Version
***************************************************************************************/

create function [dbo].[ufn_get_authgrp_property_role_details_for_user]
(
	@userID int, @client_code nvarchar(50)
)		
returns @user_report_details_table table
(	
	--USER_ID int,
	Auth_Grp_Name nvarchar(100),
	Auth_Grp_Role nvarchar(100),
	Auth_Grp_Property_Name nvarchar(100),
	Individual_Property nvarchar(100),
	Individual_Property_Role nvarchar(100),
	last_Login_Time datetime
)
as
begin

	Declare @status int
	Declare @isExtranal int
	Declare @client_id int
	
	
	set @client_id = (select client_id from Client where Client_Code = @client_code)	
	
	-- get all selected property ids
	
	
	declare @User_Auth_Grp_With_Property table
	(
		user_ID int,
		Auth_Grp_Name nvarchar(100),
		Auth_Grp_Role nvarchar(100),
		Property_ID int,
		Auth_Grp_Property_Name nvarchar(100)
	)		
	
	insert into @User_Auth_Grp_With_Property
		select user_id, Auth_Group_Name, Role_ID, d.Property_ID, d.Property_Name from 
			User_Auth_Group_Role a 
			inner join Auth_Group b 
			on a.Auth_Group_ID = b.Auth_Group_ID
			inner join Auth_Group_Property c
			on b.Auth_Group_ID = c.Auth_Group_ID
			inner join Property d
			on c.Property_ID = d.Property_ID			
			where user_id = @userID
	
	declare @last_access_per_property table
	(
		property_id int,
		last_access datetime
	)	
	
	insert into @last_access_per_property
		select a.property_id, MAX(Start_DTTM) last_access from System_Usage a
		inner join 
		@User_Auth_Grp_With_Property b on b.User_ID = a.User_ID AND a.Type_ID = 1 and a.property_id = b.Property_ID
		group by a.property_id
		
		
	insert into @user_report_details_table
		select Auth_Grp_Name,Auth_Grp_Role, 
			Auth_Grp_Property_Name,null,null,last_access
		from 
			@User_Auth_Grp_With_Property a
			inner join 
			@last_access_per_property b
			on a.Property_ID = b.property_id
			
	
	insert into @user_report_details_table
		select null,null,null,Property_Name,Role_ID,last_access from 
		(
			select distinct b.Property_Name,b.Role_ID, MAX(Start_DTTM) last_access 
			from System_Usage a
			inner join
			( 
				select c.Property_ID,c.Role_ID,c.User_ID,d.Property_Name from 
				dbo.User_Individual_Property_Role c
				inner join 
				dbo.Property d  on c.Property_ID = d.Property_ID and d.Client_ID = 2 and c.User_ID = @userID
			) b on a.Property_ID = b.Property_ID
			group by b.Property_Name,b.Role_ID
		) as a 
		
	return
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/*************************************************************************************
Function Name: ufn_get_filter_selection

Input Parameters : 
	@property_id int,
	@user_id int,
	@isRollingDate int,
	@StartDate date,
	@EndDate date,
	@param_AnalysisStartDate date,
	@param_AnalysisEndDate date,
	@param_AnalysisAsOfDate date,
	@param_ComparisonStartDate date,
	@param_ComparisonEndDate date,
	@param_ComparisonAsOfDate date,
	@rolling_start_date nvarchar(50),
	@rolling_end_date nvarchar(50),
	@rolling_analysis_start_date nvarchar(50),
	@rolling_analysis_end_date nvarchar(50),		
	@rolling_analysis_business_dt nvarchar(50),	
	@rolling_comparision_start_date nvarchar(50),
	@rolling_comparision_end_date nvarchar(50),
	@rolling_comparision_business_dt nvarchar(50)
	
Ouput Parameter : NA

Return :
	@listTable table.

Execution: this is just an example
	Example : 
	------------------------------------------------
	select * from ufn_get_filter_selection(14,1,'TODAY','LAST_UPDATED-1','','','','','','')
	
Purpose: The purpose of this function is to convert varchar(max) string with delimeter 
			into int.

Note :- This fucntion is present in both pacman per property db and global db
	 
Author: Vinay

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
2013-06-27		Vinay				Patil					Initial Version
***************************************************************************************/
CREATE FUNCTION [dbo].[ufn_get_filter_selection]
(
	@property_id int,
	@user_id int,
	@currancy varchar(10),
	@isRollingDate int,
	@StartDate date,
	@EndDate date,
	@param_AnalysisStartDate date,
	@param_AnalysisEndDate date,
	@param_AnalysisAsOfDate date,
	@param_ComparisonStartDate date,
	@param_ComparisonEndDate date,
	@param_ComparisonAsOfDate date,
	@rolling_start_date nvarchar(50),
	@rolling_end_date nvarchar(50),
	@rolling_analysis_start_date nvarchar(50),
	@rolling_analysis_end_date nvarchar(50),		
	@rolling_analysis_business_dt nvarchar(50),	
	@rolling_comparision_start_date nvarchar(50),
	@rolling_comparision_end_date nvarchar(50),
	@rolling_comparision_business_dt nvarchar(50)	
)
RETURNS @listTable table
(
	property_name nvarchar(100),
	created_by varchar(100),
	genration_date varchar(20),
	start_date date,
	end_date date,
	analysis_start_date date,
	analysis_end_date date,		
	analysis_business_dt date,	
	comparision_start_date date,
	comparision_end_date date,
	comparision_business_dt date,
	param_BaseCurrency varchar(10)
)
AS
BEGIN
     
     -- property name
    declare @propertyName nvarchar(100)
    select @propertyName = Property_Name from Property where  Property_ID=@property_id
    
    declare @userName varchar(100)
    select @userName= USER_NAME from Users where USER_ID = @user_id
    
    declare @base_Currancy varchar(10)
    if(@currancy <> '')
	   set @base_Currancy = @currancy
	 else
		set @base_Currancy = 'USD'
     
    declare @start_date date
	declare @end_date date
	declare @analysis_start_date date
	declare @analysis_end_date date
	declare @analysis_business_dt date
	declare @comparision_start_date date
	declare @comparision_end_date date
	declare @comparision_business_dt date
    
		
     if(@isRollingDate=0)
		 begin			
				if(@StartDate<> '')
					set @start_date = @StartDate

				if(@EndDate<> '')
					set @end_date = @EndDate

				if(@param_AnalysisEndDate<> '')
					set @analysis_start_date = @param_AnalysisStartDate


				if(@param_AnalysisEndDate<> '')
					set @analysis_end_date = @param_AnalysisEndDate


				if(@param_AnalysisAsOfDate<> '')
					set @analysis_business_dt = @param_AnalysisAsOfDate


				if(@param_ComparisonStartDate<> '')
					set @comparision_start_date = @param_ComparisonStartDate


				if(@param_ComparisonEndDate<> '')
					set @comparision_end_date = @param_ComparisonEndDate
					
				if(@param_ComparisonAsOfDate<> '')
					set @comparision_business_dt = @param_ComparisonAsOfDate
				
			end
		else 
			begin
				declare @caughtupdate date 
				set @caughtupdate = (select cast(GETDATE() as DATE)) --> caughtup date is NA for Global, so using getdate
				
				if(@rolling_start_date<> '')
					set @start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_start_date ,@caughtupdate))

				if(@rolling_end_date<> '')
					set @end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_end_date ,@caughtupdate))


				if(@rolling_analysis_start_date<> '')
					set @analysis_start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_analysis_start_date ,@caughtupdate))


				if(@rolling_analysis_end_date<> '')
					set @analysis_end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_analysis_end_date ,@caughtupdate))


				if(@rolling_analysis_business_dt<> '')
					set @analysis_business_dt = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_analysis_business_dt ,@caughtupdate))


				if(@rolling_comparision_start_date<> '')
					set @comparision_start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_comparision_start_date ,@caughtupdate))


				if(@rolling_comparision_end_date<> '')
					set @comparision_end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_comparision_end_date ,@caughtupdate))
					
				if(@rolling_comparision_business_dt<> '')
					set @comparision_business_dt = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_comparision_business_dt ,@caughtupdate))
		
		end
		
		insert into @listTable
			select @propertyName,ISNULL(@userName,'-'),CONVERT(VARCHAR(20), GetDate(),120),@start_date,
				@end_date,@analysis_start_date,@analysis_end_date,@analysis_business_dt,@comparision_start_date,
				@comparision_end_date,@comparision_business_dt,ISNULL(@base_Currancy,'-')

		
   RETURN
END

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE FUNCTION [dbo].[ufn_get_filter_selection_with_account_id]
(
	@property_id int,
	@user_id int,
	@currancy varchar(10),
	@isRollingDate int,
	@StartDate date,
	@EndDate date,
	@param_AnalysisStartDate date,
	@param_AnalysisEndDate date,
	@param_AnalysisAsOfDate date,
	@param_ComparisonStartDate date,
	@param_ComparisonEndDate date,
	@param_ComparisonAsOfDate date,
	@rolling_start_date nvarchar(50),
	@rolling_end_date nvarchar(50),
	@rolling_analysis_start_date nvarchar(50),
	@rolling_analysis_end_date nvarchar(50),		
	@rolling_analysis_business_dt nvarchar(50),	
	@rolling_comparision_start_date nvarchar(50),
	@rolling_comparision_end_date nvarchar(50),
	@rolling_comparision_business_dt nvarchar(50)	
)
RETURNS @listTable table
(
	property_name varchar(100),
	created_by varchar(100),
	genration_date varchar(20),
	start_date date,
	end_date date,
	analysis_start_date date,
	analysis_end_date date,		
	analysis_business_dt date,	
	comparision_start_date date,
	comparision_end_date date,
	comparision_business_dt date,
	param_BaseCurrency varchar(10),
	account_id varchar(9)
)
AS
BEGIN
     	
		insert into @listTable
	select 
			*,
			(SELECT ISNULL( SFDC_Account_Number,'---')FROM property where Property_ID = @property_id) as account_id  
  from dbo.ufn_get_filter_selection
	(
		@property_id ,
		@user_id ,
		@currancy ,
		@isRollingDate,
		@StartDate,
		@EndDate ,
		@param_AnalysisStartDate ,
		@param_AnalysisEndDate ,
		@param_AnalysisAsOfDate ,
		@param_ComparisonStartDate,
		@param_ComparisonEndDate ,
		@param_ComparisonAsOfDate ,
		@rolling_start_date ,
		@rolling_end_date ,
		@rolling_analysis_start_date ,
		@rolling_analysis_end_date ,
		@rolling_analysis_business_dt ,
		@rolling_comparision_start_date ,
		@rolling_comparision_end_date ,
		@rolling_comparision_business_dt 
	)

		
   RETURN
END

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


create function [dbo].[ufn_get_inbound_client_property_mappings]
(
	@externalSystemHavingSubSystem nvarchar(max)
	
)		
returns  @inbound_client_property_mappings table
(	
	clientCode nvarchar(max),
	propertyCode nvarchar(max),
	propertyId int,
	clientId int,
	clientName nvarchar(max),
	propertyName nvarchar(max),
	realContext nvarchar(max),
	primaryInbound nvarchar(max)
)
as 
begin
	insert into @inbound_client_property_mappings
	select 
	clientCode,propertyCode,propertyId,clientId,clientName,propertyName,property.realContext, case when e.externalSystem <> @externalSystemHavingSubSystem then e.externalSystem
		else s.subSystem
	end as primaryInbound
	 from (
		select pacman.realContext, 
		case when property.value is not null then property.value	 
			 when client.value is not null then client.value
			 else pacman.Value
		end as externalSystem
		from ( 
			select pacman.context,pacman.realContext,pacmanLevelValue.Value from (
				select 'pacman' as context, 'pacman.'+Client_Code+'.'+Property_Code as realContext from property inner join client on property.Client_ID=client.Client_ID
				where
				property.Status_ID=1 and Client.Status_ID=1
			) as pacman left join (
				select Context,Config_Parameter_Predefined_Value.Value 
				from Config_Parameter inner join Config_Parameter_Value on Config_Parameter.Config_Parameter_ID=Config_Parameter_Value.Config_Parameter_ID
				inner join Config_Parameter_Predefined_Value on Config_Parameter_Value.Config_Parameter_Predefined_Value_ID=Config_Parameter_Predefined_Value.Config_Parameter_Predefined_Value_ID
				where
				Config_Parameter.Name='pacman.core.property.externalSystem'
			) as pacmanLevelValue on pacman.context=pacmanLevelValue.Context
		) as pacman left join (
			select client.context,client.realContext,clientLevelValue.Value from (
				select 'pacman.'+Client_Code as context, 'pacman.'+Client_Code+'.'+Property_Code as realContext from property inner join client on property.Client_ID=client.Client_ID
				where
				property.Status_ID=1 and Client.Status_ID=1
			) as client left join (
				select Context,Config_Parameter_Predefined_Value.Value 
				from Config_Parameter inner join Config_Parameter_Value on Config_Parameter.Config_Parameter_ID=Config_Parameter_Value.Config_Parameter_ID
				inner join Config_Parameter_Predefined_Value on Config_Parameter_Value.Config_Parameter_Predefined_Value_ID=Config_Parameter_Predefined_Value.Config_Parameter_Predefined_Value_ID
				where
				Config_Parameter.Name='pacman.core.property.externalSystem'
			) as clientLevelValue on client.context=clientLevelValue.Context
		) as client on pacman.realContext=client.realContext left join (
			select property.context,property.realContext,propertyLevelValue.Value  from (
				select 'pacman.'+Client_Code+'.'+Property_Code as context,'pacman.'+Client_Code+'.'+Property_Code as realContext from property inner join client on property.Client_ID=client.Client_ID
				where
				property.Status_ID=1 and Client.Status_ID=1
			) as property inner join (
				select Context,Config_Parameter_Predefined_Value.Value 
				from Config_Parameter inner join Config_Parameter_Value on Config_Parameter.Config_Parameter_ID=Config_Parameter_Value.Config_Parameter_ID
				inner join Config_Parameter_Predefined_Value on Config_Parameter_Value.Config_Parameter_Predefined_Value_ID=Config_Parameter_Predefined_Value.Config_Parameter_Predefined_Value_ID
				where
				Config_Parameter.Name='pacman.core.property.externalSystem'
			) as propertyLevelValue on property.context=propertyLevelValue.Context
		) as property on client.realContext=property.realContext
	) as e left join (
		select pacman.realContext, 
		case when property.value is not null then property.value	 
			 when client.value is not null then client.value
			 else pacman.Value
		end as subSystem
		from ( 
			select pacman.context,pacman.realContext,pacmanLevelValue.Value from (
				select 'pacman' as context, 'pacman.'+Client_Code+'.'+Property_Code as realContext from property inner join client on property.Client_ID=client.Client_ID
				where
				property.Status_ID=1 and Client.Status_ID=1
			) as pacman left join (
				select Context,Config_Parameter_Predefined_Value.Value 
				from Config_Parameter inner join Config_Parameter_Value on Config_Parameter.Config_Parameter_ID=Config_Parameter_Value.Config_Parameter_ID
				inner join Config_Parameter_Predefined_Value on Config_Parameter_Value.Config_Parameter_Predefined_Value_ID=Config_Parameter_Predefined_Value.Config_Parameter_Predefined_Value_ID
				where
				Config_Parameter.Name='pacman.core.property.externalSystem.subSystem'
			) as pacmanLevelValue on pacman.context=pacmanLevelValue.Context
		) as pacman left join (
			select client.context,client.realContext,clientLevelValue.Value from (
				select 'pacman.'+Client_Code as context, 'pacman.'+Client_Code+'.'+Property_Code as realContext from property inner join client on property.Client_ID=client.Client_ID
				where
				property.Status_ID=1 and Client.Status_ID=1
			) as client left join (
				select Context,Config_Parameter_Predefined_Value.Value 
				from Config_Parameter inner join Config_Parameter_Value on Config_Parameter.Config_Parameter_ID=Config_Parameter_Value.Config_Parameter_ID
				inner join Config_Parameter_Predefined_Value on Config_Parameter_Value.Config_Parameter_Predefined_Value_ID=Config_Parameter_Predefined_Value.Config_Parameter_Predefined_Value_ID
				where
				Config_Parameter.Name='pacman.core.property.externalSystem.subSystem'
			) as clientLevelValue on client.context=clientLevelValue.Context
		) as client on pacman.realContext=client.realContext left join (
			select property.context,property.realContext,propertyLevelValue.Value  from (
				select 'pacman.'+Client_Code+'.'+Property_Code as context,'pacman.'+Client_Code+'.'+Property_Code as realContext from property inner join client on property.Client_ID=client.Client_ID
				where
				property.Status_ID=1 and Client.Status_ID=1
			) as property inner join (
				select Context,Config_Parameter_Predefined_Value.Value 
				from Config_Parameter inner join Config_Parameter_Value on Config_Parameter.Config_Parameter_ID=Config_Parameter_Value.Config_Parameter_ID
				inner join Config_Parameter_Predefined_Value on Config_Parameter_Value.Config_Parameter_Predefined_Value_ID=Config_Parameter_Predefined_Value.Config_Parameter_Predefined_Value_ID
				where
				Config_Parameter.Name='pacman.core.property.externalSystem.subSystem'
			) as propertyLevelValue on property.context=propertyLevelValue.Context
		) as property on client.realContext=property.realContext
	) as s on e.realContext=s.realContext inner join (
				select client_code as clientCode,property_code as propertyCode,Property_ID as propertyId,
				client.Client_ID as clientId,client_name as clientName,property_name as propertyName,'pacman.'+Client_Code+'.'+Property_Code as realContext from property inner join client on property.Client_ID=client.Client_ID
				where
				property.Status_ID=1 and Client.Status_ID=1 and Client.Client_Code <> 'Dummy Client' 
	) as property on e.realContext=property.realContext
	return
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************
Store Procedure Name: dbo.ufn_get_parameter_node_id

Input Parameters : @node -> node name for which the param_node_id is being returned (e.g.'platform.eventhandler.pacman')
				   
					
Ouput Parameter : param_node_id for a given node.

Execution: select dbo.ufn_get_parameter_node_id ('platform.eventhandler.pacman') --> this is just an example

Purpose: The purpose of this function is to get a param_node_id for a given node.
		 
Author: Manohar Sunkum

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
10/27/2011	Manohar				Sunkum					Initial version for adding 
***************************************************************************************/

create function [dbo].[ufn_get_parameter_node_id] (@node nvarchar(250))
RETURNS INT
AS
begin 
	declare @start_index int 
	declare @end_index int 
	declare @parentNodeId int 
	declare @nodeId int 
	declare @parameterId int 
	declare @param nvarchar(250) 
	set @start_index=1 
	set @parameterId=0 
	set @parentNodeId=0 

		while (select CHARINDEX('.',@node,@start_index)) > 0
		begin
			set @end_index=(select CHARINDEX('.',@node,@start_index)) 	
			set @param = (select SUBSTRING(@node,@start_index,@end_index-@start_index) ) 

			if (@start_index = 1)		
				begin
					set @nodeId = (select Param_Node_ID from Parameter_Node where Node_Name=@param and Parent_Param_Node_ID is null) 
				end
			else
				begin
					set @nodeId = (select Param_Node_ID from Parameter_Node where Node_Name=@param and Parent_Param_Node_ID=@parentNodeId) 
				end
			
			set @parentNodeId=@nodeId 
			set @start_index=@end_index+1 
			
		end
		set @param = (select SUBSTRING(@node,@start_index,9999) ) 
		if(@parentNodeId=0)
			begin
				set @parameterId=(select Param_Node_ID from Parameter_Node where Node_Name=@param and Parent_Param_Node_ID is null) 
			end
		else
			begin
				set @parameterId=(select Param_Node_ID from Parameter_Node where Node_Name=@param and Parent_Param_Node_ID=@parentNodeId)	 
			end	
		RETURN @parameterId 
end 

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



/*************************************************************************************
Function Name: ufn_get_submcat_mcat_fg_mapping

Input Parameters : 
	@subMcat --> subMcat for which you need data
	
Ouput Parameter : NA

Execution: this is just an example
	Example : 
	------------------------------------------------
	select * from ufn_get_submcat_mcat_fg_mapping('1G,2A,2D,2E','DISC_QNL,DISC_QNL')
	
Purpose: The purpose of this function is to get mapping between SubMCAT and MCAT.

	 
Author: Atul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
07/20/2012		Atul				Shendye					Initial Version
02/12/2014		Shivani				Kaul					Updated
04/16/2018      Shirishkumar        Bari                    Changes related to split function
***************************************************************************************/

create function [dbo].[ufn_get_submcat_mcat_fg_mapping]
(
	@srp nvarchar(max),@subMcat nvarchar(max),@fg nvarchar(max)
	
)		
returns  @ms_mcat_fg_mapping table
(	
	srp nvarchar(50),
	subMcat nvarchar(50),
	mcat nvarchar(50),
	fg nvarchar(50)
)
as
begin
       declare @temp_srp table
       (
              id int ,
              srp varchar(50)
       )
       
       declare @temp_subMcat table
       (
              id int ,
              subMcat varchar(50)
       )
       declare @temp_fg table
       (
              id int ,
              fg varchar(50)
       )
       
       declare @temp_srp_SubMcat_fg table
       (
              srp varchar(50),
              subMcat varchar(50),
              fg varchar(50)
       )
       
       insert into @temp_srp
              select * from split(@srp,',') order by id
              
       insert into @temp_subMcat
              select * from split(@subMcat,',')  order by id
       
       insert into @temp_fg
              select * from split(@fg,',')  order by id

       insert into @temp_srp_SubMcat_fg
              select srp,subMcat,fg from @temp_srp a 
              inner join @temp_subMcat b 
              on  a.id = b.id
              inner join @temp_fg f
              on a.id = f.id
              order by a.id
              
       DECLARE CursorName CURSOR FAST_FORWARD
              for select srp,subMcat,fg
                     from @temp_srp_SubMcat_fg
                     
       OPEN CursorName
              FETCH NEXT FROM CursorName
              INTO @srp,@subMcat,@fg            
       
       WHILE @@FETCH_STATUS = 0
       BEGIN
              insert into @ms_mcat_fg_mapping 
              select @srp srp,@subMcat subMcat,
              mcat = 
              case 
                     when Client_Business_Group_Name is NULL then @subMcat
              else 
                     Client_Business_Group_Name
              end
              , @fg fg
              from
              (
                     select @subMcat as Mkt_Seg_Code
              ) subMcat
              left join
              (
                     select Mkt_Seg_Code,Client_Business_Group_Name from 
                     (
                           select Client_Business_Group_ID,Client_Business_Group_Name 
                           from Client_Business_Group
                           where Client_Business_View_ID = 
                           (
                                  select Client_Business_View_ID from Client_Business_View 
                                  where Client_Business_View_Name = 'MCAT_Mappings' and Status_ID=2
                           )
                     ) cbg inner join 
                     Client_Business_Group_Mkt_Seg_Code cbgmc on cbg.Client_Business_Group_ID=cbgmc.Client_Business_Group_ID
                     where Mkt_Seg_Code=@subMcat
              ) ms_mcat 
              on subMcat.Mkt_Seg_Code=ms_mcat.Mkt_Seg_Code
              
       FETCH NEXT FROM CursorName
              INTO @srp,@subMcat ,@fg    
       END
       CLOSE CursorName
       DEALLOCATE CursorName 
       return
end
 


GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/*************************************************************************************
Function Name: ufn_get_submcat_mcat_mapping

Input Parameters : 
	@subMcat --> subMcat for which you need data
	
Ouput Parameter : NA

Execution: this is just an example
	Example : 
	------------------------------------------------
	select * from ufn_get_submcat_mcat_mapping ('1G,2A,2D,2E','DISC_QNL,DISC_QNL')
	
Purpose: The purpose of this function is to get mapping between SubMCAT and MCAT.

	 
Author: Atul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
07/20/2012		Atul				Shendye					Initial Version
04/16/2018      Shirishkumar        Bari                    Changes related to split function
***************************************************************************************/

create function [dbo].[ufn_get_submcat_mcat_mapping]
(
	@srp nvarchar(max),@subMcat nvarchar(max)
	
)		
returns  @ms_mcat_mapping table
(	
	srp nvarchar(50),
	subMcat nvarchar(50),
	mcat nvarchar(50)
)
as

begin
	declare @temp_srp table
	(
		id int ,
		srp varchar(50)
	)
	
	declare @temp_subMcat table
	(
		id int ,
		subMcat varchar(50)
	)
	
	declare @temp_srp_SubMcat table
	(
		srp varchar(50),
		subMcat varchar(50)
	)
	
	insert into @temp_srp
		select * from split(@srp,',') order by id
		
	insert into @temp_subMcat
		select * from split(@subMcat,',') order by id

	insert into @temp_srp_SubMcat
		select srp,subMcat from @temp_srp a inner join @temp_subMcat b 
		on  a.id = b.id
		
	DECLARE CursorName CURSOR FAST_FORWARD
		for select srp,subMcat 
			from @temp_srp_SubMcat
			
	OPEN CursorName
		FETCH NEXT FROM CursorName
		INTO @srp,@subMcat		
	
	WHILE @@FETCH_STATUS = 0
	BEGIN
		insert into @ms_mcat_mapping 
		select @srp srp,@subMcat subMcat,
		mcat = 
		case 
			when Client_Business_Group_Name is NULL then @subMcat
		else 
			Client_Business_Group_Name
		end
		from
		(
			select @subMcat as Mkt_Seg_Code
		) subMcat
		left join
		(
			select Mkt_Seg_Code,Client_Business_Group_Name from 
			(
				select Client_Business_Group_ID,Client_Business_Group_Name 
				from Client_Business_Group
				where Client_Business_View_ID = 
				(
					select Client_Business_View_ID from Client_Business_View 
					where Client_Business_View_Name = 'MCAT_Mappings' and Status_ID=2
				)
			) cbg inner join 
			Client_Business_Group_Mkt_Seg_Code cbgmc on cbg.Client_Business_Group_ID=cbgmc.Client_Business_Group_ID
			where Mkt_Seg_Code=@subMcat
		) ms_mcat 
		on subMcat.Mkt_Seg_Code=ms_mcat.Mkt_Seg_Code
		
       FETCH NEXT FROM CursorName
		INTO @srp,@subMcat		
	END
	CLOSE CursorName
	DEALLOCATE CursorName 
	return
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************
Function Name: ufn_get_users_for_userreport

Input Parameters : 	
	
Ouput Parameter : 
	USER_ID int,
	USER_NAME nvarchar(100),
	Last_Name nvarchar(100),
	First_Name nvarchar(100),
	Email_Address nvarchar(300),
	Status_ID int,
	last_Login_Time datetime

Execution: this is just an example
	Example : 
	------------------------------------------------
	
	Declare @propertyIDs nvarchar(max); 
	Declare @authGrpIDs nvarchar(max);
	Declare @roleIDs nvarchar(max);
	Declare @userIDs nvarchar(max);
	Declare @propertiesAllSelected int;
	Declare @authGroupAllSelected int;
	Declare @rolesAllSelected int;
	Declare @usersAllSelected int;	
	Declare @isActive int;
	Declare @isUserInternal int;	
	Declare @client_code nvarchar(10);	

	--set @propertyIDs = ''
	--set @authGrpIDs = ''
	--set @roleIDs = '96488fd4-27bf-42ea-903c-c8343825d298'
	--set @userIDs = '11403,5,6,7'
	set @isActive = -1
	set @isUserInternal = 0 
	set @propertiesAllSelected  = -1;
	set @authGroupAllSelected  = 1;
	set @rolesAllSelected  = 1;
	set @usersAllSelected  = 1;
	set @client_code = 'ideas'	
	
	--select * from ufn_get_users_for_userreport('1G,2A,2D,2E','DISC_QNL,DISC_QNL')
	
	select * from ufn_get_users_for_userreport(@propertyIDs,@authGrpIDs,@roleIDs,@userIDs,
		@propertiesAllSelected,@authGroupAllSelected,@rolesAllSelected,
		@usersAllSelected,@isActive,@isUserInternal,@client_code)
	
Purpose: The purpose of this function is to get users base on the filter selection
		e.g Individual property access, authorization group, role.

	 
Author: Vinay

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
04/16/2014		Vinay				Patil					Initial Version
***************************************************************************************/

create function [dbo].[ufn_get_users_for_userreport]
(
	@propertyIDs nvarchar(max), 
	@authGrpIDs nvarchar(max),
	@roleIDs nvarchar(max),
	@userIDs nvarchar(max),
	@propertiesAllSelected int,
	@authGroupAllSelected int,
	@rolesAllSelected int,
	@usersAllSelected int,	
	@isActive int,
	@isUserInternal int,
	@client_code nvarchar(10)
)		
returns  @user_report_table table
(	
	USER_ID int,
	USER_NAME nvarchar(100),
	Last_Name nvarchar(100),
	First_Name nvarchar(100),
	Email_Address nvarchar(300),
	Status_ID int,
	last_Login_Time datetime
)
as
begin

	Declare @status int
	Declare @isExtranal int
	Declare @client_id int
	
	/*
		internal = 1 (internal user)
		if logged in user is internal show all external and internal users 
		if logged in user is external user show only external users 
		@isUserInternal =1 and @isExtranal = 0 will give you all users 
		@isUserInternal = 0 and @isExtranal = 0 give you onlu external users
	*/
	
	if(@isActive = 1 )
		set @status = 1
	else if (@isActive = 2)
		set @status = 2
	else
		begin
			set @status = 1
			set @isActive = 2
		end	
	
	/*
		internal = 1 (internal user)
		if logged in user is internal show all external and internal users 
		if logged in user is external user show only external users 
		@isUserInternal =1 and @isExtranal = 0 will give you all users 
		@isUserInternal = 0 and @isExtranal = 0 give you onlu external users
	*/
	
	if(@isUserInternal = 1 )
		set @isExtranal = 0
	else if (@isUserInternal = 0)
		set @isExtranal = 0
	
	set @client_id = (select client_id from Client where Client_Code = @client_code)	
	
	-- get all selected property ids
	Declare @propertyIDTable table
	(
		propertyID int
	)

	if (@propertiesAllSelected<>-1 and (@propertyIDs is not null or len(@propertyIDs)>1))
		insert into @propertyIDTable
			select * from ufn_varchar_max_to_int (@propertyIDs,',')
	else if (@propertiesAllSelected=-1)
		insert into @propertyIDTable
			select Property_ID from property where Client_ID=2 and  Status_ID = 1  
	
	-- get all selected authorization group ids	
	Declare @AuthGroupIDTable table
	(
		authGrpID int
	)
	
	if (@authGroupAllSelected<>-1 and (@authGrpIDs is not null or len(@authGrpIDs)>1))
		insert into @AuthGroupIDTable
			select * from ufn_varchar_max_to_int (@authGrpIDs,',')
	else if (@authGroupAllSelected=-1)
		insert into @AuthGroupIDTable
			select Auth_Group_ID  from Auth_Group where Client_ID = @client_id and status_id =1 
			
	-- get all selected role ids
	Declare @RoleIDTable table
	(
		roleID varchar(100)
	)

	insert into @RoleIDTable
		select items from split(@roleIDs,',')
		
	if (@rolesAllSelected<>-1 and (@roleIDs is not null or len(@roleIDs)>1))
		insert into @RoleIDTable
			select items from split(@roleIDs,',')
	else if (@authGroupAllSelected=-1)
		insert into @RoleIDTable
			select Auth_Group_ID  from Auth_Group where Client_ID = @client_id and status_id =1 
		
	-- get all selected user ids
	Declare @UserIDTable table
	(
		userID int
	)
				
	if (@usersAllSelected<>-1 and (@userIDs is not null or len(@userIDs)>1))
		insert into @UserIDTable
				select * from ufn_varchar_max_to_int (@userIDs,',')
	else if (@usersAllSelected=-1)
		insert into @UserIDTable
			select USER_ID  from users where client_code='bstn' and status_id =1 and Internal=0
			
		/*select 
		a.USER_ID,USER_NAME,Last_Name,First_Name,
		Email_Address,Status_ID, outerTable.last_Login_Time*/
		insert into @user_report_table
		select 
			a.USER_ID,USER_NAME,Last_Name,First_Name,
			Email_Address,Status_ID, outerTable.last_Login_Time		
		from 
			Users a 
		INNER JOIN 	
	-- get all users base on property code selection
		(
			select innerUserTable.user_ID, MAX(Start_DTTM) as last_Login_Time
			FROM
				(
					select USER_ID
						from 
						@propertyIDTable a
						inner join 
						User_Individual_Property_Role b
						on a.propertyID = b.property_ID 
					UNION		
						
					-- get all users based on the authorization gruop selection	
						select USER_ID
						from 
						@AuthGroupIDTable a
						inner join 
						User_Auth_Group_Role  b
							on a.authGrpID = b.Auth_Group_ID	
						inner join 
							Auth_Group_Property c
							on b.Auth_Group_ID = c.Auth_Group_ID

					UNION		
				/** to get users for roles**/		
						select b.USER_ID
						from 
							@RoleIDTable a
						 inner join 
							User_Individual_Property_Role b
							on a.roleID = b.Role_ID
					UNION		
						select c.User_ID 
						from 
							@RoleIDTable a
						 inner join
							User_Auth_Group_Role c
							on a.roleID = c.Role_ID
					UNION		
					-- get all user ids based on user selection	
						select 
						USER_ID
						from 
							Users a 
								inner join 
							@UserIDTable b  
								on a.User_ID = b.userID
				) as innerUserTable
				LEFT OUTER JOIN System_Usage c on innerUserTable.User_ID = c.User_ID AND c.Type_ID = 1
				group by innerUserTable.user_ID
			)as outerTable on a.User_ID=outerTable.User_ID 
				and a.Status_ID in (@isActive,@status) 
				and a.Internal in (@isUserInternal, @isExtranal)
	return
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************
Function Name: ufn_varchar_max_to_int

Input Parameters : 
	@list --> string with delimeter i.e '40100,40101,40102'
	@delim --> delimeter i.e ", or | or + "
	
Ouput Parameter : NA

Return :
	@listTable table with column value datatype INT.

Execution: this is just an example
	Example : 
	------------------------------------------------
	select * from ufn_varchar_max_to_int ('40100,40101,40102',',')
	
Purpose: The purpose of this function is to convert varchar(max) string with delimeter 
			into int.

	 
Author: vPatil

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
07/05/2013		Vinay				Patil					Initial Version
***************************************************************************************/

CREATE FUNCTION [dbo].[ufn_varchar_max_to_int](@list as varchar(max), @delim as varchar(10))
RETURNS @listTable table(
   Value INT
   )
AS
BEGIN
     --Declare helper to identify the position of the delim
     DECLARE @DelimPosition INT
     
     --Prime the loop, with an initial check for the delim
     SET @DelimPosition = CHARINDEX(@delim, @list)

     --Loop through, until we no longer find the delimiter
     WHILE @DelimPosition > 0
     BEGIN
         --Add the item to the table
         INSERT INTO @listTable(Value)
             VALUES(CAST(RTRIM(LEFT(@list, @DelimPosition - 1)) AS INT))
     
         --Remove the entry from the List
         SET @list = right(@list, len(@list) - @DelimPosition)

         --Perform position comparison
         SET @DelimPosition = CHARINDEX(@delim, @list)
     END

     --If we still have an entry, add it to the list
     IF len(@list) > 0
         insert into @listTable(Value)
         values(CAST(RTRIM(@list) AS INT))

   RETURN
END

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE FUNCTION [dbo].[varcharToInt](@list as varchar(8000), @delim as varchar(10))
RETURNS @listTable table(
   Value INT
   )
AS
BEGIN
     --Declare helper to identify the position of the delim
     DECLARE @DelimPosition INT
     
     --Prime the loop, with an initial check for the delim
     SET @DelimPosition = CHARINDEX(@delim, @list)

     --Loop through, until we no longer find the delimiter
     WHILE @DelimPosition > 0
     BEGIN
         --Add the item to the table
         INSERT INTO @listTable(Value)
             VALUES(CAST(RTRIM(LEFT(@list, @DelimPosition - 1)) AS INT))
     
         --Remove the entry from the List
         SET @list = right(@list, len(@list) - @DelimPosition)

         --Perform position comparison
         SET @DelimPosition = CHARINDEX(@delim, @list)
     END

     --If we still have an entry, add it to the list
     IF len(@list) > 0
         insert into @listTable(Value)
         values(CAST(RTRIM(@list) AS INT))

   RETURN
END

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SAS_File_Loc_History](
	[SAS_File_Loc_History_ID] [int] IDENTITY(1,1) NOT NULL,
	[SAS_File_Loc_ID] [int] NOT NULL,
	[Property_ID] [int] NOT NULL,
	[SAS_Server_Name] [nvarchar](50) NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_SAS_File_Loc_History] PRIMARY KEY CLUSTERED 
(
	[SAS_File_Loc_History_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DBLoc_History](
	[DBLoc_History_ID] [int] IDENTITY(1,1) NOT NULL,
	[DBLoc_ID] [int] NOT NULL,
	[DBName] [nvarchar](150) NOT NULL,
	[Server_Name] [nvarchar](150) NULL,
	[Server_Inst] [nvarchar](150) NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_DBLoc_History_ID] PRIMARY KEY CLUSTERED 
(
	[DBLoc_History_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE VIEW [dbo].[VW_Property_DBLoc_SASFileLoc_History]
AS
WITH DBLocHistCTE AS
(
SELECT DB.DBLoc_History_ID
      ,DB.DBName
      ,DB.Server_Name
      ,DB.Server_Inst
      ,CONVERT(VARCHAR(10),DB.CreateDate,121) AS CreateDate
	  ,ROW_NUMBER() OVER (PARTITION BY DB.DBName ORDER BY DB.CreateDate, DB.DBLoc_History_ID) AS RowNum
	  ,COUNT(1) OVER (PARTITION BY DB.DBName) AS MaxRowNum
  FROM dbo.DBLoc_History DB
		  JOIN (SELECT DBName, CONVERT(VARCHAR(10),CreateDate,121) as CreateDate, MAX(DBLoc_History_ID) DBLoc_History_ID FROM dbo.DBLoc_History
		  GROUP BY DBName, CONVERT(VARCHAR(10),CreateDate,121)
		) as HistID ON DB.DBLoc_History_ID = HistID.DBLoc_History_ID
  )
, SASLocHistCTE AS
(
SELECT SAS.SAS_File_Loc_History_ID
      ,SAS.Property_ID
      ,SAS.SAS_Server_Name
      ,CONVERT(VARCHAR(10),SAS.CreateDate,121) AS CreateDate
	  ,ROW_NUMBER() OVER (PARTITION BY SAS.Property_ID ORDER BY SAS.CreateDate, SAS.SAS_File_Loc_History_ID) AS RowNum
	  ,COUNT(1) OVER (PARTITION BY SAS.Property_ID) AS MaxRowNum
  FROM dbo.SAS_File_Loc_History	SAS
		  JOIN (SELECT Property_ID, CONVERT(VARCHAR(10),CreateDate,121) as CreateDate, MAX(SAS_File_Loc_History_ID) SAS_File_Loc_History_ID FROM dbo.SAS_File_Loc_History
		  GROUP BY Property_ID, CONVERT(VARCHAR(10),CreateDate,121)
		) as HistID ON SAS.SAS_File_Loc_History_ID = HistID.SAS_File_Loc_History_ID
  )

SELECT ISNULL(DB.DBName, RIGHT('000000'+ CONVERT(VARCHAR, SAS.Property_ID),len(DB.DBName))) as Property
	, DB.Server_Name
	, SAS.SAS_Server_Name
	, CASE WHEN DB.BeginDate IS NULL THEN SAS.BeginDate
		WHEN SAS.BeginDate IS NULL THEN DB.BeginDate
		WHEN SAS.BeginDate > DB.BeginDate THEN SAS.BeginDate
		ELSE DB.BeginDate
		END as BeginDate
	, CASE WHEN DB.EndDate IS NULL THEN SAS.EndDate
		WHEN SAS.EndDate IS NULL THEN DB.EndDate
		WHEN SAS.EndDate < DB.EndDate THEN SAS.EndDate
		ELSE DB.EndDate
		END as EndDate
		FROM
		 ( SELECT DB1.DBName, DB1.Server_Name, DB1.CreateDate as BeginDate, ISNULL(DB2.CreateDate,'9999-12-31') as EndDate
		  FROM DBLocHistCTE db1 LEFT JOIN DBLocHistCTE db2 ON db2.DBName = db1.DBName AND DB2.RowNum = DB1.RowNum + 1 ) as DB
		FULL JOIN
		 ( SELECT SAS1.Property_ID, SAS1.SAS_Server_Name, SAS1.CreateDate as BeginDate, ISNULL(SAS2.CreateDate,'9999-12-31') as EndDate
		  FROM SASLocHistCTE SAS1 LEFT JOIN SASLocHistCTE SAS2 ON SAS2.Property_ID = SAS1.Property_ID AND SAS2.RowNum = SAS1.RowNum + 1 ) AS SAS
		 ON DB.DBName = RIGHT('000000'+ CONVERT(VARCHAR, SAS.Property_ID),len(DB.DBName))
		 AND (
		 (SAS.BeginDate >= DB.BeginDate and SAS.BeginDate < DB.EndDate)
		 OR (DB.BeginDate >= SAS.BeginDate and DB.BeginDate < SAS.EndDate)
		 )
UNION  -- getting the endpoints; cases where rows for dbloc or sasloc exists at the end or the beginning without matching rows for the other
SELECT ISNULL(DB.DBName, RIGHT('000000'+ CONVERT(VARCHAR, SAS.Property_ID),len(DB.DBName))) as Property
	, CASE WHEN DB.RowNum = 1 and SAS.RowNum = 1 THEN
			CASE WHEN SAS.BeginDate < DB.BeginDate THEN NULL
				ELSE DB.Server_Name
			END
		   WHEN DB.RowNum = DB.MaxRowNum and SAS.RowNum = SAS.MaxRowNum THEN
			CASE WHEN SAS.EndDate < DB.EndDate THEN DB.Server_Name
				ELSE NULL
			END
	  END 	as Server_Name
	, CASE WHEN DB.RowNum = 1 and SAS.RowNum = 1 THEN
			CASE WHEN SAS.BeginDate < DB.BeginDate THEN SAS.SAS_Server_Name
				ELSE NULL
			END
		   WHEN DB.RowNum = DB.MaxRowNum and SAS.RowNum = SAS.MaxRowNum THEN
			CASE WHEN SAS.EndDate < DB.EndDate THEN NULL
				ELSE SAS.SAS_Server_Name
			END
	  END 	as SAS_Server_Name
	, CASE WHEN DB.RowNum = 1 and SAS.RowNum = 1 THEN
			CASE WHEN SAS.BeginDate < DB.BeginDate THEN SAS.BeginDate
				ELSE DB.BeginDate
			END
		   WHEN DB.RowNum = DB.MaxRowNum and SAS.RowNum = SAS.MaxRowNum THEN
			CASE WHEN SAS.EndDate < DB.EndDate THEN SAS.EndDate
				ELSE DB.EndDate
			END
	  END 	as BeginDate
	, CASE WHEN DB.RowNum = 1 and SAS.RowNum = 1 THEN
			CASE WHEN SAS.BeginDate < DB.BeginDate THEN DB.BeginDate
				ELSE SAS.BeginDate
			END
		   WHEN DB.RowNum = DB.MaxRowNum and SAS.RowNum = SAS.MaxRowNum THEN
			CASE WHEN SAS.EndDate < DB.EndDate THEN DB.EndDate
				ELSE SAS.EndDate
			END
	  END 	as EndDate
		FROM
		 ( SELECT DB1.DBName, DB1.Server_Name, DB1.CreateDate as BeginDate, ISNULL(DB2.CreateDate,'9999-12-31') as EndDate, db1.RowNum, db1.MaxRowNum
		  FROM DBLocHistCTE db1 LEFT JOIN DBLocHistCTE db2 ON db2.DBName = db1.DBName AND DB2.RowNum = DB1.RowNum + 1 ) as DB
		 JOIN
		 ( SELECT SAS1.Property_ID, SAS1.SAS_Server_Name, SAS1.CreateDate as BeginDate, ISNULL(SAS2.CreateDate,'9999-12-31') as EndDate, SAS1.RowNum, SAS1.MaxRowNum
		  FROM SASLocHistCTE SAS1 LEFT JOIN SASLocHistCTE SAS2 ON SAS2.Property_ID = SAS1.Property_ID AND SAS2.RowNum = SAS1.RowNum + 1 ) AS SAS
		 ON DB.DBName = RIGHT('000000'+ CONVERT(VARCHAR, SAS.Property_ID),len(DB.DBName))
		 AND (DB.RowNum = 1 and SAS.RowNum = 1 AND NOT DB.BeginDate = SAS.BeginDate)
		  OR (DB.RowNum = DB.MaxRowNum and SAS.RowNum = SAS.MaxRowNum AND NOT DB.EndDate = SAS.EndDate)
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Client](
	[Client_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_Code] [nvarchar](50) NOT NULL,
	[Client_Name] [nvarchar](150) NOT NULL,
	[Status_ID] [int] NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_Client] PRIMARY KEY CLUSTERED 
(
	[Client_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Property](
	[Property_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[Property_Code] [nvarchar](50) NOT NULL,
	[Property_Name] [nvarchar](150) NOT NULL,
	[Status_ID] [int] NOT NULL,
	[Created_DTTM] [smalldatetime] NOT NULL,
	[DBLoc_ID] [int] NULL,
	[Last_Purged_Date] [datetime] NOT NULL,
	[Stage] [varchar](100) NULL,
	[Created_By_User_ID] [bigint] NOT NULL,
	[Last_Updated_By_User_ID] [bigint] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Force_Full_Decisions] [bit] NULL,
	[Read_Only_Override] [bit] NULL,
	[SFDC_Account_Number] [varchar](9) NULL,
	[MonitorProcess_Next_Run_Date] [date] NULL,
	[Reservation_Data_Version] [int] NULL,
	[Questionnaire_Status] [nvarchar](20) NOT NULL,
	[Client_Property_Code] [nvarchar](50) NULL,
	[Last_SAS_Purged_Date] [datetime] NOT NULL,
 CONSTRAINT [PK_Property] PRIMARY KEY CLUSTERED 
(
	[Property_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Config_Parameter_Predefined_Value](
	[Config_Parameter_Predefined_Value_ID] [int] IDENTITY(1,1) NOT NULL,
	[Config_Parameter_Predefined_Value_Type_ID] [int] NOT NULL,
	[Value] [nvarchar](250) NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
	[ModifiedDate] [smalldatetime] NOT NULL,
	[Display_Name] [nchar](100) NULL,
 CONSTRAINT [PK_Config_Parameter_Predefined_Values] PRIMARY KEY CLUSTERED 
(
	[Config_Parameter_Predefined_Value_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [Unique_Config_Parameter_Predefined_Value_Parameter_Type] UNIQUE NONCLUSTERED 
(
	[Config_Parameter_Predefined_Value_Type_ID] ASC,
	[Value] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Config_Parameter](
	[Config_Parameter_ID] [int] IDENTITY(1,1) NOT NULL,
	[Name] [nvarchar](100) NOT NULL,
	[Description] [nvarchar](2000) NULL,
	[Config_Parameter_Predefined_Value_Type_ID] [int] NULL,
	[CreateDate] [smalldatetime] NOT NULL,
	[ModifiedDate] [smalldatetime] NOT NULL,
	[Config_Parameter_Type_ID] [int] NOT NULL,
	[Group_ID] [int] NOT NULL,
	[Value_Constraints] [nvarchar](512) NULL,
	[Default_Value] [nvarchar](250) NULL,
 CONSTRAINT [PK_Config_Parameter_1] PRIMARY KEY CLUSTERED 
(
	[Config_Parameter_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [Unique_Config_Parameter_Name] UNIQUE NONCLUSTERED 
(
	[Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Config_Parameter_Value](
	[Config_Parameter_Value_ID] [int] IDENTITY(1,1) NOT NULL,
	[Config_Parameter_ID] [int] NOT NULL,
	[Context] [nvarchar](100) NOT NULL,
	[FixedValue] [nvarchar](700) NULL,
	[Config_Parameter_Predefined_Value_ID] [int] NULL,
	[Created_By_User_ID] [bigint] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Last_Updated_By_User_ID] [bigint] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_Config_Parameter_Value] PRIMARY KEY CLUSTERED 
(
	[Config_Parameter_Value_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [Unique_Config_Parameter_Value_Context_Parameter_ID] UNIQUE NONCLUSTERED 
(
	[Config_Parameter_ID] ASC,
	[Context] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/*************************************************************************************
Function Name: ufn_get_client_property_integration
***************************************************************************************/
CREATE FUNCTION [dbo].[ufn_get_client_property_integration]()
RETURNS TABLE
AS
RETURN
SELECT
	Client_ID,
	Client_Name,
	Client_Code,
	Property_ID,
	Property_Code,
	Property_Name,
	Stage,
	External_System,
	External_System_Sub_System,
	Suspended_BDE_And_CDP,
	Time_To_Check_Audit
FROM (
	SELECT
		c.Client_ID,
		c.Client_Name,
		c.Client_Code,
		p.Property_ID,
		p.Property_Code,
		p.Property_Name,
		p.Stage,
		ISNULL(clientPropertyConfigParams.External_System, ISNULL(clientConfigParams.External_System, globalConfigParams.External_System)) External_System,
		ISNULL(clientPropertyConfigParams.External_System_Sub_System, ISNULL(clientConfigParams.External_System_Sub_System, globalConfigParams.External_System_Sub_System)) External_System_Sub_System,
		CASE WHEN ISNULL(clientPropertyConfigParams.Suspended_BDE_And_CDP, ISNULL(clientConfigParams.Suspended_BDE_And_CDP, globalConfigParams.Suspended_BDE_And_CDP)) = 'true'
			THEN 1 ELSE 0
		END Suspended_BDE_And_CDP,
		ISNULL(clientPropertyConfigParams.Time_To_Check_Audit, ISNULL(clientConfigParams.Time_To_Check_Audit, globalConfigParams.Time_To_Check_Audit)) Time_To_Check_Audit
	FROM [dbo].[Client] c INNER JOIN [dbo].[Property] p ON c.Client_ID = p.Client_ID
	LEFT JOIN (
		SELECT
			Client_Code,
			Property_Code,
			externalSystem AS External_System,
			subSystem AS External_System_Sub_System,
			suspendBdeAndCdp AS Suspended_BDE_And_CDP,
			timeToCheckAuditDate AS Time_To_Check_Audit
		FROM (
			SELECT
				SUBSTRING(Client_And_Property, 0, CHARINDEX('.',Client_And_Property)) Client_Code,
				SUBSTRING(Client_And_Property, CHARINDEX('.',Client_And_Property) +1, LEN(Client_And_Property)) Property_Code,
				reverse(left(reverse(Name), charindex('.', reverse(Name)) -1)) Trimmed_Name,
				Value
			FROM (
					-- FixedValue config parameters
					SELECT cp.Name, REPLACE(cpv.Context, 'pacman.', '') Client_And_Property, cpv.FixedValue as Value
					FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID
					WHERE cp.Name in (
						'pacman.integration.timeToCheckAuditDate'
					)
					AND CHARINDEX('.',cpv.Context) > 0
					UNION ALL
					-- Pre-defined Value config parameters
					SELECT cp.Name, REPLACE(cpv.Context, 'pacman.', '') Client_And_Property, cppv.Value
					FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID INNER JOIN dbo.Config_Parameter_Predefined_Value AS cppv WITH(NOLOCK) ON cpv.Config_Parameter_Predefined_Value_ID = cppv.Config_Parameter_Predefined_Value_ID
					WHERE cp.Name in (
						'pacman.core.property.externalSystem',
						'pacman.core.property.externalSystem.subSystem',
						'pacman.integration.suspendBdeAndCdp'
					)
					AND CHARINDEX('.',cpv.Context) > 0
			) allConfigParams
		) configParamNamesTrimmed
		PIVOT (
			max(Value) for Trimmed_Name IN (externalSystem, subSystem, suspendBdeAndCdp, timeToCheckAuditDate)
		) AS configParams
	) clientPropertyConfigParams ON c.Client_Code = clientPropertyConfigParams.Client_Code AND p.Property_Code = clientPropertyConfigParams.Property_Code
	LEFT JOIN (
		SELECT
			Client_Code,
			externalSystem AS External_System,
			subSystem AS External_System_Sub_System,
			suspendBdeAndCdp AS Suspended_BDE_And_CDP,
			timeToCheckAuditDate AS Time_To_Check_Audit
		FROM (
			SELECT
				Client_Code,
				reverse(left(reverse(Name), charindex('.', reverse(Name)) -1)) Trimmed_Name,
				Value
			FROM (
					-- FixedValue config parameters
					SELECT cp.Name, REPLACE(cpv.Context, 'pacman.', '') Client_Code, cpv.FixedValue as Value
					FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID
					WHERE cp.Name in (
						'pacman.integration.timeToCheckAuditDate'
					)
					AND CHARINDEX('.',cpv.Context) > 0 AND CHARINDEX('.',REPLACE(cpv.Context, 'pacman.', '')) = 0
					UNION ALL
					-- Pre-defined Value config parameters
					SELECT cp.Name, REPLACE(cpv.Context, 'pacman.', '') Client_Code, cppv.Value
					FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID INNER JOIN dbo.Config_Parameter_Predefined_Value AS cppv WITH(NOLOCK) ON cpv.Config_Parameter_Predefined_Value_ID = cppv.Config_Parameter_Predefined_Value_ID
					WHERE cp.Name in (
						'pacman.core.property.externalSystem',
						'pacman.core.property.externalSystem.subSystem',
						'pacman.integration.suspendBdeAndCdp'
					)
					AND CHARINDEX('.',cpv.Context) > 0 AND CHARINDEX('.',REPLACE(cpv.Context, 'pacman.', '')) = 0
			) allConfigParams
		) configParamNamesTrimmed
		PIVOT (
			max(Value) for Trimmed_Name IN (externalSystem, subSystem, suspendBdeAndCdp,timeToCheckAuditDate)
		) AS configParams
	) clientConfigParams ON c.Client_Code = clientConfigParams.Client_Code
	LEFT JOIN (
		SELECT
			externalSystem AS External_System,
			subSystem AS External_System_Sub_System,
			suspendBdeAndCdp AS Suspended_BDE_And_CDP,
			timeToCheckAuditDate AS Time_To_Check_Audit
		FROM (
			SELECT
				reverse(left(reverse(Name), charindex('.', reverse(Name)) -1)) Trimmed_Name,
				Value
			FROM (
					-- FixedValue config parameters
					SELECT cp.Name, cpv.FixedValue as Value
					FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID
					WHERE cp.Name in (
						'pacman.integration.timeToCheckAuditDate'
					) AND cpv.Context='pacman'
					UNION ALL
					SELECT cp.Name, cppv.Value
					FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID INNER JOIN dbo.Config_Parameter_Predefined_Value AS cppv WITH(NOLOCK) ON cpv.Config_Parameter_Predefined_Value_ID = cppv.Config_Parameter_Predefined_Value_ID
					WHERE cp.Name in (
						'pacman.core.property.externalSystem',
						'pacman.core.property.externalSystem.subSystem',
						'pacman.integration.suspendBdeAndCdp'
					) and cpv.Context='pacman'
			) allConfigParams
		) configParamNamesTrimmed
		PIVOT (
			max(Value) for Trimmed_Name IN (externalSystem, subSystem, suspendBdeAndCdp,timeToCheckAuditDate)
		) AS configParams
	) globalConfigParams ON globalConfigParams.Suspended_BDE_And_CDP IS NOT NULL OR globalConfigParams.Time_To_Check_Audit IS NOT NULL
) properties

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Property_Daily_Processing](
	[Property_Daily_Processing_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_Code] [nvarchar](50) NOT NULL,
	[Property_Code] [nvarchar](50) NOT NULL,
	[Processing_Date] [date] NOT NULL,
	[Stage] [nvarchar](50) NOT NULL,
	[Property_Time_Zone] [nvarchar](100) NOT NULL,
	[Expected_CDP_Count] [int] NOT NULL,
	[Completed_CDP_Count] [int] NOT NULL,
	[Status] [nvarchar](50) NOT NULL,
	[Completed_RSS_Count] [int] NOT NULL,
	[Property_Name] [nvarchar](150) NOT NULL,
	[Client_Name] [nvarchar](150) NOT NULL,
	[Completed_Function_Space_Count] [int] NOT NULL,
	[Completed_Scheduled_Reports_Count] [int] NOT NULL,
 CONSTRAINT [PK_Property_Daily_Processing] PRIMARY KEY CLUSTERED 
(
	[Property_Daily_Processing_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Input_Processing](
	[Input_Processing_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_Daily_Processing_ID] [int] NOT NULL,
	[Input_Type] [nvarchar](50) NOT NULL,
	[Input_ID] [nvarchar](250) NOT NULL,
	[Received_DTTM] [datetime] NULL,
	[Completed_DTTM] [datetime] NULL,
	[SLA_Violation] [bit] NULL,
	[Status] [nvarchar](50) NULL,
	[Overdue_DTTM] [datetime] NULL,
	[CDP_Schedule_ID] [int] NULL,
	[Prepared_DTTM] [datetime] NULL,
	[Started_DTTM] [datetime] NULL,
	[Decisions_Generated_DTTM] [datetime] NULL,
	[Created_DTTM] [datetime] NULL,
	[Created_by_User_ID] [bigint] NULL,
	[Last_Updated_DTTM] [datetime] NULL,
	[Last_Updated_by_User_ID] [bigint] NULL,
	[Data_Collection_Started_DTTM] [datetime] NULL,
	[Summary_Started_DTTM] [datetime] NULL,
	[Summary_Completed_DTTM] [datetime] NULL,
	[Extract_Status] [nvarchar](50) NULL,
	[Extract_Started_DTTM] [datetime] NULL,
	[Extract_Completed_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Input_Processing] PRIMARY KEY CLUSTERED 
(
	[Input_Processing_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE VIEW [dbo].[BDE_View] AS
SELECT
  pdp.Client_Code,
  pdp.Property_Code,
  ip.Received_DTTM as BDE_Initiated_Date, 
  ip.Decisions_Generated_DTTM as Decisions_Created_Date, 
  ip.Input_ID as Input_Identifier,
  pdp.Property_Daily_Processing_ID,
  pdp.Stage,
  pdp.Property_Time_Zone,
  Duration = CASE
    WHEN ip.Decisions_Generated_DTTM IS NULL THEN NULL
    WHEN ip.Decisions_Generated_DTTM IS NOT NULL THEN DATEDIFF(s, ip.Received_DTTM, ip.Decisions_Generated_DTTM)
  END
FROM
  Property_Daily_Processing AS pdp WITH(NOLOCK)
    JOIN Input_Processing AS ip WITH(NOLOCK)
      ON pdp.Property_Daily_Processing_ID = ip.Property_Daily_Processing_ID AND ip.input_type = 'BDE'

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Configuration_File_Record](
	[Configuration_File_Record_ID] [int] IDENTITY(1,1) NOT NULL,
	[Configuration_File_ID] [int] NOT NULL,
	[Record_Type] [nvarchar](50) NOT NULL,
	[Record_Status] [nvarchar](50) NOT NULL,
	[Record] [nvarchar](1000) NOT NULL,
	[Failure_Reason] [nvarchar](1000) NULL,
	[CreateDate] [datetime] NOT NULL,
	[Property_Code] [nvarchar](50) NULL,
 CONSTRAINT [PK_Configuration_File_Record] PRIMARY KEY CLUSTERED 
(
	[Configuration_File_Record_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Configuration_File](
	[Configuration_File_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[File_Name] [nvarchar](255) NOT NULL,
	[File_Status] [nvarchar](50) NOT NULL,
	[CreateDate] [datetime] NOT NULL,
	[Snapshot_Date] [datetime] NULL,
	[Prepared_Date] [datetime] NULL,
	[Number_Of_Properties] [int] NULL,
	[Failure_Reason] [nvarchar](1000) NULL,
 CONSTRAINT [PK_Configuration_File] PRIMARY KEY CLUSTERED 
(
	[Configuration_File_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Property_Stage_Changes](
	[Property_Stage_Change_ID] [INT] IDENTITY(1,1) NOT NULL,
	[Property_ID] [INT] NOT NULL,
	[New_Stage] [VARCHAR](100) NOT NULL,
	[Initiated_By] [VARCHAR](100) NOT NULL,
	[Stage_Changed_Date_Time][DATETIME] NOT NULL,
	[Notes] [VARCHAR](500),
	[Decisions] [VARCHAR](500),
	[SRP_FPLOS_Total_Level] [bit],
	[Scheduled_Two_Way_Date] [DATETIME],
	CONSTRAINT [Property_Stage_Changes_Property_ID] FOREIGN KEY ([Property_Id]) REFERENCES [dbo].[Property]([Property_Id]),
	CONSTRAINT [PK_Property_Stage_Changes] PRIMARY KEY CLUSTERED
		([Property_Stage_Change_ID] ASC ) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
)
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Property_Rollout_Dates](
	[Property_ID] [INT] NOT NULL,
	[First_Extract_Name] [VARCHAR](100),
	[Last_Extract_Name] [VARCHAR](100),
	[Catchup_Initiated_By] [VARCHAR](100),
	[Catchup_Start_Date] [DATETIME],
	[Catchup_End_Date][DATETIME],
	CONSTRAINT [PK_Property_Rollout_Dates] PRIMARY KEY CLUSTERED
		([Property_ID] ASC ) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
)
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[Vw_Consolidated_Property_View]
AS
SELECT
    c.Client_ID,
    c.Client_Name,
    c.Client_Code,
    p.Property_ID,
    p.Property_Code,
    p.Property_Name,
    p.Stage,
    CONVERT(datetime, clientPropertyConfigParams.SRP_Attribution_Extract_Date, 120) AS SRP_Attribution_Extract_Date,
    CONVERT(datetime, clientPropertyConfigParams.SRP_Attribution_Submitted_Date, 120) AS SRP_Attribution_Submitted_Date,
    DATEADD(day, 35, clientPropertyConfigParams.Setup_Completion_Date) AS Setup_Completion_Date,
    CONVERT(datetime, clientPropertyConfigParams.Scheduled_Two_Way_Date, 120) AS Scheduled_Two_Way_Date,
    CONVERT(tinyint, CASE WHEN (UPPER(clientPropertyConfigParams.Configuration_Complete) = 'TRUE') THEN 1 ELSE 0 END) as Configuration_Complete,
    CONVERT(tinyint, CASE WHEN (UPPER(clientPropertyConfigParams.LDB_Enabled) = 'TRUE') THEN 1 ELSE 0 END) as LDB_Enabled,
    clientPropertyConfigParams.External_System,
    clientPropertyConfigParams.Property_Time_Zone,
    clientPropertyConfigParams.Web_Rate_Alias,
    configurationFile.File_Name AS Config_File_Name,
    configurationFile.CreateDate AS Config_File_Load_Date,
    propertyStageChanges.Two_Way_Date,
    propertyStageChanges.One_Way_Date,
    propertyStageChanges.Population_Date,
    propertyStageChanges.Catchup_Date,
    propertyCatchupChanges.Catchup_Start_Date,
    propertyCatchupChanges.Catchup_End_Date,
    CatchupInitiatedBy.Catchup_Detail_ID,
    CatchupInitiatedBy.Catchup_Initiated_By,
    ISNULL(configurationFileOOO.Pending_OO_Records,0) AS Pending_OO_Records,
    CASE WHEN ISNULL(configurationFileOOO.Out_Of_Order_Records_Loaded,0) > 0 THEN 1 ELSE 0 END as Out_Of_Order_Records_Loaded
FROM [dbo].[Client] c WITH (NOLOCK) INNER JOIN [dbo].[Property] p WITH (NOLOCK) ON c.Client_ID = p.Client_ID
    LEFT JOIN (
    SELECT
    Client_Code,
    Property_Code,
    scheduledExtractDate AS SRP_Attribution_Extract_Date,
    srpAttributionSubmittedDate AS SRP_Attribution_Submitted_Date,
    srpAttributionSubmittedDate AS Setup_Completion_Date,
    scheduledTwoWayDate AS Scheduled_Two_Way_Date,
    configurationComplete AS Configuration_Complete,
    LimitedDataBuildEnabled AS LDB_Enabled,
    externalSystem AS External_System,
    propertyTimeZone AS Property_Time_Zone,
    webRateAlias AS Web_Rate_Alias
    FROM (
    SELECT
    SUBSTRING(Client_And_Property, 0, CHARINDEX('.',Client_And_Property)) Client_Code,
    SUBSTRING(Client_And_Property, CHARINDEX('.',Client_And_Property) +1, LEN(Client_And_Property)) Property_Code,
    reverse(left(reverse(Name), charindex('.', reverse(Name)) -1)) Trimmed_Name,
    Value
    FROM (
    -- FixedValue config parameters
    SELECT cp.Name, REPLACE(cpv.Context, 'pacman.', '') Client_And_Property, cpv.FixedValue as Value
    FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID
    WHERE cp.Name in (
    'pacman.core.property.scheduledExtractDate',
    'pacman.core.property.srpAttributionSubmittedDate',
    'pacman.core.property.scheduledTwoWayDate',
    'pacman.bar.webRateAlias'
    )
    UNION ALL
    -- Pre-defined Value config parameters
    SELECT cp.Name, REPLACE(cpv.Context, 'pacman.', '') Client_And_Property, cppv.Value
    FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID INNER JOIN dbo.Config_Parameter_Predefined_Value AS cppv WITH(NOLOCK) ON cpv.Config_Parameter_Predefined_Value_ID = cppv.Config_Parameter_Predefined_Value_ID
    WHERE cp.Name in (
    'pacman.core.property.configurationComplete',
    'pacman.core.LimitedDataBuildEnabled',
    'pacman.core.property.externalSystem',
    'pacman.core.propertyTimeZone'
    )
    AND CHARINDEX('.',cpv.Context) > 0
    ) allConfigParams
    ) configParamNamesTrimmed
    PIVOT (
    max(Value) for Trimmed_Name IN (scheduledExtractDate, srpAttributionSubmittedDate, scheduledTwoWayDate, configurationComplete, LimitedDataBuildEnabled, externalSystem, propertyTimeZone, webRateAlias)
    ) AS configParams
    ) clientPropertyConfigParams ON c.Client_Code = clientPropertyConfigParams.Client_Code AND p.Property_Code = clientPropertyConfigParams.Property_Code
    LEFT JOIN (
    SELECT
    cf.Client_ID,
    cfr.Property_Code,
    cf.File_Name,
    MAX(cfr.CreateDate) AS CreateDate
    FROM dbo.Configuration_File AS cf WITH(NOLOCK) INNER JOIN dbo.Configuration_File_Record AS cfr WITH(NOLOCK) ON cf.Configuration_File_ID = cfr.Configuration_File_ID
    GROUP BY cf.Client_ID, cfr.Property_Code, cf.File_Name
    ) configurationFile ON c.Client_ID = configurationFile.Client_ID AND configurationFile.Property_Code = P.Property_Code
    LEFT JOIN (
    SELECT
    Property_ID,
    TwoWay as Two_Way_Date,
    OneWay as One_Way_Date,
    Population as Population_Date,
    CatchUp as Catchup_Date
    FROM (
    SELECT Property_ID, New_Stage, max(Stage_Changed_Date_Time) as Stage_Change_Date
    FROM Property_Stage_Changes WITH(NOLOCK)
    GROUP BY Property_ID, New_Stage
    ) stageChanges
    PIVOT (
    MAX(Stage_Change_Date) for New_Stage IN (TwoWay, OneWay, Population, CatchUp)
    ) AS stageChangesFinal
    ) propertyStageChanges ON p.Property_ID = propertyStageChanges.Property_ID
    LEFT JOIN (
    SELECT Property_ID, Catchup_Start_Date,
    CASE WHEN Catchup_End_Date > Catchup_Start_Date THEN Catchup_End_Date ELSE NULL END as Catchup_End_Date
    FROM Property_Rollout_Dates WITH(NOLOCK)
    ) propertyCatchupChanges ON p.Property_ID = propertyCatchupChanges.Property_ID
    LEFT JOIN (
    SELECT PRD.Property_ID, NULL AS Catchup_Detail_ID, Catchup_Initiated_By
    FROM Property_Rollout_Dates PRD WITH(NOLOCK)
    ) AS CatchupInitiatedBy ON P.Property_ID = CatchupInitiatedBy.Property_ID
    LEFT JOIN (
    SELECT
    propertyOOO.Client_ID,
    propertyOOO.Property_Code,
    ISNULL(PENDING,0) AS Pending_OO_Records,
    ISNULL(PASSED,0) + ISNULL(WARNING,0) + ISNULL(FAILED,0) + ISNULL(PENDING,0) AS Out_Of_Order_Records_Loaded
    FROM (
    SELECT
    cf.Client_ID,
    cfr.Property_Code,
    cfr.Record_Status,
    COUNT(*) AS Record_Count
    FROM [dbo].[Configuration_File] cf WITH(NOLOCK) INNER JOIN [dbo].[Configuration_File_Record] cfr WITH(NOLOCK) ON cf.Configuration_File_ID = cfr.Configuration_File_ID
    WHERE cfr.Record_Type = 'OO'
    GROUP BY cf.Client_ID, cfr.Property_Code, Record_Status
    ) propertyOOOByStatus
    PIVOT (
    MAX(Record_Count) for Record_Status IN (PASSED,WARNING,FAILED,PENDING)
    ) propertyOOO
    ) configurationFileOOO ON c.Client_ID = configurationFileOOO.Client_ID AND p.Property_Code = configurationFileOOO.Property_Code

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Agent_Property](
	[Agent_Property_ID] [int] IDENTITY(1,1) NOT NULL,
	[Remote_Agent_ID] [int] NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Current_Business_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Agent_Property] PRIMARY KEY CLUSTERED 
(
	[Agent_Property_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Announcement_Client](
	[Announcement_id] [int] NOT NULL,
	[Client_id] [int] NOT NULL,
 CONSTRAINT [PK_Announcement_Client] PRIMARY KEY CLUSTERED 
(
	[Announcement_id] ASC,
	[Client_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Announcement_User](
	[Announcement_id] [int] NOT NULL,
	[User_id] [int] NOT NULL,
 CONSTRAINT [PK_Announcement_User] PRIMARY KEY CLUSTERED 
(
	[Announcement_id] ASC,
	[User_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Attribute_Display_Type](
	[Attribute_Display_Type_ID] [int] IDENTITY(1,1) NOT NULL,
	[Display_Type_Name] [nvarchar](16) NOT NULL,
	[Display_Type_Description] [nvarchar](150) NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_Attribute_Display_Type] PRIMARY KEY CLUSTERED 
(
	[Attribute_Display_Type_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Auth_Group](
	[Auth_Group_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[Auth_Group_Name] [nvarchar](50) NOT NULL,
	[Auth_Group_Description] [nvarchar](150) NULL,
	[Rule_ID] [int] NULL,
	[Status_ID] [int] NOT NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_Auth_Group] PRIMARY KEY CLUSTERED 
(
	[Auth_Group_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Auth_Group_Property](
	[Auth_Group_Property_ID] [int] IDENTITY(1,1) NOT NULL,
	[Auth_Group_ID] [int] NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Status_ID] [int] NULL,
	[CreateDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_Auth_Group_Property] PRIMARY KEY CLUSTERED 
(
	[Auth_Group_Property_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Client_Attribute](
	[Client_Attribute_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[Client_Attribute_Name] [nvarchar](16) NOT NULL,
	[Client_Attribute_Description] [nvarchar](150) NULL,
	[User_Length_Entry] [int] NOT NULL,
	[Attribute_Display_Length] [int] NULL,
	[Attribute_Display_Type_ID] [int] NOT NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_Client_Attribute] PRIMARY KEY CLUSTERED 
(
	[Client_Attribute_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [Unique_Client_ID_Attribute_Name] UNIQUE NONCLUSTERED 
(
	[Client_ID] ASC,
	[Client_Attribute_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Client_Attribute_Value](
	[Client_Attribute_Value_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_Attribute_ID] [int] NOT NULL,
	[Client_Attribute_Default] [int] NOT NULL,
	[Client_Attribute_Value] [nvarchar](150) NOT NULL,
	[Status_ID] [int] NOT NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_Client_Attribute_Value] PRIMARY KEY CLUSTERED 
(
	[Client_Attribute_Value_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Client_Business_Group](
	[Client_Business_Group_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_Business_Group_Name] [nvarchar](100) NOT NULL,
	[Client_Business_Group_Description] [nvarchar](250) NULL,
	[Client_Business_View_ID] [int] NOT NULL,
	[Ranking] [int] NOT NULL,
	[Status_ID] [int] NOT NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_Client_Business_Group] PRIMARY KEY CLUSTERED 
(
	[Client_Business_Group_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Client_Business_Group_Mkt_Seg_Code](
	[Client_Business_Group_Mkt_Seg_Code_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_Business_Group_ID] [int] NOT NULL,
	[Mkt_Seg_Code] [nvarchar](50) NOT NULL,
	[CreateDate_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_Client_Business_Group_Mkt_Seg_Code] PRIMARY KEY CLUSTERED 
(
	[Client_Business_Group_Mkt_Seg_Code_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Client_Business_View](
	[Client_Business_View_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[Client_Business_View_Name] [nvarchar](100) NOT NULL,
	[Client_Business_View_Description] [nvarchar](250) NULL,
	[Status_ID] [int] NOT NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Default_View] [int] NOT NULL,
 CONSTRAINT [PK_Client_Business_View] PRIMARY KEY CLUSTERED 
(
	[Client_Business_View_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Client_Certificate_Details](
	[Client_Certificate_Details_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_Code] [nvarchar](50) NOT NULL,
	[Certificate_Domain_Name] [nvarchar](1000) NOT NULL,
	[Certificate_User_Email_Id] [nvarchar](1000) NULL,
 CONSTRAINT [PK_Client_Certificate_Details] PRIMARY KEY CLUSTERED 
(
	[Client_Certificate_Details_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [Client_Certificate_Details_Unique_Client_Code] UNIQUE NONCLUSTERED 
(
	[Client_Code] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Client_Property_Attribute_Pairing](
	[Client_Property_Attribute_Pairing_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Client_Attribute_Value_ID] [int] NOT NULL,
	[Status_ID] [int] NOT NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_Client_Property_Attribute_Pairing] PRIMARY KEY CLUSTERED 
(
	[Client_Property_Attribute_Pairing_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Client_User](
	[Client_User_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[User_ID] [int] NOT NULL,
	[CreateDate_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Client_User] PRIMARY KEY CLUSTERED 
(
	[Client_User_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Condition_Type](
	[Condition_Type_ID] [int] IDENTITY(1,1) NOT NULL,
	[Condition_Type_Name] [nvarchar](50) NOT NULL,
	[Condition_Type_Description] [nvarchar](150) NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_Condition_Type] PRIMARY KEY CLUSTERED 
(
	[Condition_Type_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Config_Parameter_Category](
	[Category_ID] [int] IDENTITY(1,1) NOT NULL,
	[Category_Name] [nvarchar](50) NOT NULL,
	[Description] [nvarchar](50) NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
	[ModifiedDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_Param_Categories] PRIMARY KEY CLUSTERED 
(
	[Category_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [Unique_Config_Parameter_Category_Name] UNIQUE NONCLUSTERED 
(
	[Category_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Config_Parameter_Change_Reason](
	[Config_Parameter_Value_ID] [int] NOT NULL,
	[REV] [int] NOT NULL,
	[Reason] [nvarchar](250) NULL,
PRIMARY KEY CLUSTERED 
(
	[Config_Parameter_Value_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Config_Parameter_Group](
	[Group_ID] [int] IDENTITY(1,1) NOT NULL,
	[Category_ID] [int] NOT NULL,
	[Group_Name] [nvarchar](50) NOT NULL,
	[Description] [nvarchar](50) NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
	[ModifiedDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_Param_Groups] PRIMARY KEY CLUSTERED 
(
	[Group_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Config_Parameter_Predefined_Value_Type](
	[Config_Parameter_Predefined_Value_Type_ID] [int] IDENTITY(1,1) NOT NULL,
	[code] [nvarchar](250) NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
	[ModifiedDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_Config_Parameter_Predefined_Value_Type] PRIMARY KEY CLUSTERED 
(
	[Config_Parameter_Predefined_Value_Type_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Config_Parameter_Type](
	[Param_Type_ID] [int] IDENTITY(1,1) NOT NULL,
	[Param_Type] [nvarchar](50) NOT NULL,
	[Description] [nvarchar](50) NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
	[ModifiedDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_Param_Types] PRIMARY KEY CLUSTERED 
(
	[Param_Type_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Config_Parameter_Value_AUD](
	[Config_Parameter_Value_ID] [int] NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[Config_Parameter_ID] [int] NULL,
	[Context] [nvarchar](100) NULL,
	[FixedValue] [nvarchar](700) NULL,
	[Config_Parameter_Predefined_Value_ID] [int] NULL,
	[Created_DTTM] [datetime] NULL,
	[Created_By_User_Id] [bigint] NULL,
	[Last_Updated_DTTM] [datetime] NULL,
	[Last_Updated_By_User_Id] [bigint] NULL,
PRIMARY KEY CLUSTERED 
(
	[Config_Parameter_Value_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Config_Parameter_Value_Meaning](
	[Config_Parameter_Value_Meaning_ID] [int] IDENTITY(1,1) NOT NULL,
	[Config_Param_ID] [int] NOT NULL,
	[Config_Parameter_Predefined_Value_ID] [int] NOT NULL,
	[Meaning] [nvarchar](512) NOT NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_Config_Parameter_Value_Meaning] PRIMARY KEY CLUSTERED 
(
	[Config_Parameter_Value_Meaning_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Conjunction_Type](
	[Conjunction_Type_ID] [int] IDENTITY(1,1) NOT NULL,
	[Conjunction_Name] [nvarchar](50) NOT NULL,
	[CreateDate_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_Conjunction_Type] PRIMARY KEY CLUSTERED 
(
	[Conjunction_Type_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Corp_Business_Group](
	[Corp_Business_Group_ID] [int] IDENTITY(1,1) NOT NULL,
	[Corp_Business_Group_Name] [nvarchar](100) NOT NULL,
	[Corp_Business_Group_Description] [nvarchar](250) NULL,
	[Corp_Business_View_ID] [int] NOT NULL,
	[Ranking] [numeric](18, 0) NOT NULL,
	[Status_ID] [int] NOT NULL,
	[Created_by_User_ID] [bigint] NOT NULL,
	[Last_Updated_by_User_ID] [bigint] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_Corp_Business_Group] PRIMARY KEY CLUSTERED 
(
	[Corp_Business_Group_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [IX_Corp_Business_Group_Name] UNIQUE NONCLUSTERED 
(
	[Corp_Business_Group_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Corp_Business_Group_Mkt_Seg_Code](
	[Corp_Business_Group_Mkt_Seg_Code_ID] [int] IDENTITY(1,1) NOT NULL,
	[Corp_Business_Group_ID] [int] NOT NULL,
	[Mkt_Seg_Code] [nvarchar](50) NOT NULL,
	[CreateDate_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_Corp_Business_Group_Mkt_Seg_Code] PRIMARY KEY CLUSTERED 
(
	[Corp_Business_Group_Mkt_Seg_Code_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Corp_Business_View](
	[Corp_Business_View_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[Corp_Business_View_Name] [nvarchar](100) NOT NULL,
	[Corp_Business_View_Description] [nvarchar](250) NULL,
	[Status_ID] [int] NOT NULL,
	[Created_by_User_ID] [bigint] NOT NULL,
	[Last_Updated_by_User_ID] [bigint] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Default_View] [int] NOT NULL,
 CONSTRAINT [PK_Corp_Business_View] PRIMARY KEY CLUSTERED 
(
	[Corp_Business_View_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [IX_Corp_Business_View_Name] UNIQUE NONCLUSTERED 
(
	[Corp_Business_View_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DBLoc](
	[DBLoc_ID] [int] IDENTITY(1,1) NOT NULL,
	[DBName] [nvarchar](150) NOT NULL,
	[Server_Name] [nvarchar](150) NOT NULL,
	[Server_Inst] [nvarchar](150) NULL,
	[Port_Number] [int] NULL,
	[JNDI_Name] [nvarchar](150) NULL,
	[Status_ID] [int] NOT NULL,
	[DBType_ID] [int] NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
	[JNDI_Name_For_Reports] [nvarchar](150) NULL,
 CONSTRAINT [PK_DBLoc] PRIMARY KEY CLUSTERED 
(
	[DBLoc_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[dbmaintain_scripts](
	[file_name] [varchar](150) NULL,
	[file_last_modified_at] [bigint] NULL,
	[checksum] [varchar](50) NULL,
	[executed_at] [varchar](20) NULL,
	[succeeded] [bigint] NULL
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DBType](
	[DBType_ID] [int] IDENTITY(1,1) NOT NULL,
	[DBType_Name] [nvarchar](50) NOT NULL,
	[Status_ID] [int] NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_DBType] PRIMARY KEY CLUSTERED 
(
	[DBType_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Decision_Delivery](
	[Decision_Delivery_ID] [int] IDENTITY(1,1) NOT NULL,
	[Input_Processing_ID] [int] NOT NULL,
	[Decision_ID] [nvarchar](250) NOT NULL,
	[Uploaded_DTTM] [datetime] NULL,
	[Destination_ID] [nvarchar](250) NOT NULL,
	[Result] [nvarchar](250) NOT NULL,
	[Overdue_DTTM] [datetime] NULL,
	[Started_DTTM] [datetime] NULL,
	[Ack_Received_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Decision_Delivery] PRIMARY KEY CLUSTERED 
(
	[Decision_Delivery_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Decision_Delivery_By_Type](
	[Decision_Delivery_By_Type_ID] [int] IDENTITY(1,1) NOT NULL,
	[Decision_Delivery_ID] [int] NOT NULL,
	[Decision_Type] [nvarchar](250) NOT NULL,
	[Status] [nvarchar](250) NULL,
	[Started_DTTM] [datetime] NULL,
	[Delivered_DTTM] [datetime] NULL,
	[Acknowledged_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Decision_Delivery_By_Type] PRIMARY KEY CLUSTERED 
(
	[Decision_Delivery_By_Type_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Dialog_Request](
	[Dialog_Request_ID] [int] IDENTITY(1,1) NOT NULL,
	[Dialog_Session_ID] [int] NOT NULL,
	[Request_Date] [datetime] NOT NULL,
	[Property_ID] [int] NULL,
	[Intent] [nvarchar](100) NOT NULL,
	[Parameters] [nvarchar](2048) NULL,
	[Response] [nvarchar](2048) NOT NULL,
 CONSTRAINT [PK_Dialog_Request] PRIMARY KEY CLUSTERED 
(
	[Dialog_Request_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Dialog_Session](
	[Dialog_Session_ID] [int] IDENTITY(1,1) NOT NULL,
	[Start_Date] [datetime] NOT NULL,
	[End_Date] [datetime] NULL,
	[Client_ID] [int] NULL,
	[User_ID] [int] NOT NULL,
 CONSTRAINT [PK_Dialog_Session] PRIMARY KEY CLUSTERED 
(
	[Dialog_Session_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FAQ](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Module_id] [int] NOT NULL,
	[Question] [nvarchar](max) NOT NULL,
	[Answer] [nvarchar](max) NOT NULL,
	[Steps] [nvarchar](max) NULL,
	[Created_By_User_ID] [int] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Last_Updated_By_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_FAQ] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Force_Full_Decisions](
	[ForceFullDecisions_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Outbound_Name] [nvarchar](150) NOT NULL,
	[IsForceFullDecision] [int] NOT NULL,
	[Created_by_User_ID] [int] NULL,
	[Created_DTTM] [datetime] NULL,
	[Last_Updated_by_User_ID] [int] NULL,
	[Last_Updated_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Force_Full_Decisions] PRIMARY KEY CLUSTERED 
(
	[ForceFullDecisions_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Force_Full_Decisions_AUD](
	[ForceFullDecisions_ID] [int] NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[Client_ID] [int] NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Outbound_Name] [nvarchar](150) NOT NULL,
	[IsForceFullDecision] [int] NOT NULL,
	[Created_by_User_ID] [int] NULL,
	[Created_DTTM] [datetime] NULL,
	[Last_Updated_by_User_ID] [int] NULL,
	[Last_Updated_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Force_Full_Decisions_AUD] PRIMARY KEY CLUSTERED 
(
	[ForceFullDecisions_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Grp_Evl_Multi](
	[Grp_Evl_Multi_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[Property_Group_ID] [int] NOT NULL,
	[Group_Name] [nvarchar](100) NOT NULL,
	[Materialization_Status] [int] NOT NULL,
	[Evaluation_Date] [datetime] NOT NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_Grp_Evl_Multi] PRIMARY KEY CLUSTERED 
(
	[Grp_Evl_Multi_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Grp_Evl_Multi_AUD](
	[Grp_Evl_Multi_ID] [int] NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[Client_ID] [int] NULL,
	[Property_Group_ID] [int] NULL,
	[Group_Name] [nvarchar](100) NULL,
	[Materialization_Status] [int] NULL,
	[Evaluation_Date] [datetime] NULL,
	[Created_by_User_ID] [int] NULL,
	[Created_DTTM] [datetime] NULL,
	[Last_Updated_by_User_ID] [int] NULL,
	[Last_Updated_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Grp_Evl_Multi_AUD] PRIMARY KEY CLUSTERED 
(
	[Grp_Evl_Multi_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Grp_Evl_Multi_Property](
	[Grp_Evl_Multi_Property_ID] [int] IDENTITY(1,1) NOT NULL,
	[Grp_Evl_Multi_ID] [int] NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_Grp_Evl_Multi_Property] PRIMARY KEY CLUSTERED 
(
	[Grp_Evl_Multi_Property_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Grp_Evl_Multi_Property_AUD](
	[Grp_Evl_Multi_Property_ID] [int] NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[Grp_Evl_Multi_ID] [int] NULL,
	[Property_ID] [int] NULL,
	[Created_by_User_ID] [int] NULL,
	[Created_DTTM] [datetime] NULL,
	[Last_Updated_by_User_ID] [int] NULL,
	[Last_Updated_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Grp_Evl_Multi_Property_AUD] PRIMARY KEY CLUSTERED 
(
	[Grp_Evl_Multi_Property_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Input_Processing_Job](
	[Input_Processing_Job_ID] [int] IDENTITY(1,1) NOT NULL,
	[Input_Processing_ID] [int] NOT NULL,
	[Job_Instance_ID] [bigint] NOT NULL,
 CONSTRAINT [PK_Input_Processing_Job] PRIMARY KEY CLUSTERED 
(
	[Input_Processing_Job_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[INPUT_PROCESSING_NOTE](
	[INPUT_PROCESSING_NOTE_ID] [bigint] IDENTITY(1,1) NOT NULL,
	[INPUT_PROCESSING_ID] [int] NOT NULL,
	[CREATION_DATE] [datetime] NOT NULL,
	[TEXT] [nvarchar](2000) NOT NULL,
	[AUTHOR_NAME] [nvarchar](200) NOT NULL,
	[TYPE] [nvarchar](100) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[INPUT_PROCESSING_NOTE_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Internal_Alert](
	[Internal_Alert_ID] [int] IDENTITY(1,1) NOT NULL,
	[Internal_Alert_Type] [nvarchar](100) NOT NULL,
	[Internal_Alert_Status] [nvarchar](20) NOT NULL,
	[Created_Date] [datetime] NOT NULL,
	[Notified_Date] [datetime] NULL,
	[Resolved_Date] [datetime] NULL,
	[Details] [text] NOT NULL,
	[Property_ID] [int] NULL,
	[Client_ID] [int] NULL,
	[Stage] [nvarchar](50) NULL,
 CONSTRAINT [PK_Internal_Alert] PRIMARY KEY CLUSTERED 
(
	[Internal_Alert_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Internal_Alert_Subscription](
	[Internal_Alert_Subscription_ID] [int] IDENTITY(1,1) NOT NULL,
	[User_ID] [int] NOT NULL,
	[Internal_Alert_Type] [nvarchar](100) NOT NULL,
 CONSTRAINT [PK_Internal_Alert_Subscription] PRIMARY KEY CLUSTERED 
(
	[Internal_Alert_Subscription_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [UQ_Internal_Alert_Subscription] UNIQUE NONCLUSTERED 
(
	[User_ID] ASC,
	[Internal_Alert_Type] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LDB_Generic_Booking_Curve](
	[LDB_Generic_Booking_Curve_ID] [int] IDENTITY(1,1) NOT NULL,
	[Label] [nvarchar](100) NOT NULL,
	[LDB_Hotel_Profile_ID] [int] NOT NULL,
	[Business_Type_ID] [int] NOT NULL,
	[Booking_Window] [nvarchar](10) NOT NULL,
 CONSTRAINT [PK_LDB_Generic_Booking_Curve] PRIMARY KEY CLUSTERED 
(
	[LDB_Generic_Booking_Curve_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [UQ_LDB_Generic_Booking_Curve] UNIQUE NONCLUSTERED 
(
	[Label] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LDB_Generic_Booking_Curve_Point](
	[LDB_Generic_Booking_Curve_Point_ID] [int] IDENTITY(1,1) NOT NULL,
	[LDB_Generic_Booking_Curve_ID] [int] NOT NULL,
	[Days_To_Arrival] [int] NOT NULL,
	[Data] [numeric](6, 5) NOT NULL,
 CONSTRAINT [PK_LDB_Generic_Booking_Curve_Point] PRIMARY KEY CLUSTERED 
(
	[LDB_Generic_Booking_Curve_Point_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [UQ_LDB_Generic_Booking_Curve_Point] UNIQUE NONCLUSTERED 
(
	[LDB_Generic_Booking_Curve_ID] ASC,
	[Days_To_Arrival] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LDB_Generic_LOS_Distribution](
	[LDB_Generic_LOS_Distribution_ID] [int] IDENTITY(1,1) NOT NULL,
	[Label] [nvarchar](100) NOT NULL,
	[Min_Average_LOS] [numeric](8, 5) NOT NULL,
	[Max_Average_LOS] [numeric](8, 5) NOT NULL,
	[LOS_1_Probability] [numeric](6, 5) NOT NULL,
	[LOS_2_Probability] [numeric](6, 5) NOT NULL,
	[LOS_3_Probability] [numeric](6, 5) NOT NULL,
	[LOS_4_Probability] [numeric](6, 5) NOT NULL,
	[LOS_5_Probability] [numeric](6, 5) NOT NULL,
	[LOS_6_Probability] [numeric](6, 5) NOT NULL,
	[LOS_7_Probability] [numeric](6, 5) NOT NULL,
	[LOS_8_Probability] [numeric](6, 5) NOT NULL,
	[LOS_14_Probability] [numeric](6, 5) NOT NULL,
	[LOS_21_Probability] [numeric](6, 5) NOT NULL,
	[LOS_28_Probability] [numeric](6, 5) NOT NULL,
	[LOS_35_Probability] [numeric](6, 5) NOT NULL,
	[LOS_63_Probability] [numeric](6, 5) NOT NULL,
 CONSTRAINT [PK_LDB_Generic_LOS_Distribution] PRIMARY KEY CLUSTERED 
(
	[LDB_Generic_LOS_Distribution_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LDB_Hotel_Profile](
	[LDB_Hotel_Profile_ID] [int] IDENTITY(1,1) NOT NULL,
	[Label] [nvarchar](100) NOT NULL,
 CONSTRAINT [PK_LDB_Hotel_Profile] PRIMARY KEY CLUSTERED 
(
	[LDB_Hotel_Profile_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [UQ_LDB_Hotel_Profile] UNIQUE NONCLUSTERED 
(
	[Label] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Learning_Client_Property_Mapping](
	[Learning_Client_Property_Mapping_ID] [bigint] IDENTITY(1,1) NOT NULL,
	[Client_Code] [nvarchar](50) NOT NULL,
	[Property_Code] [nvarchar](50) NULL,
	[Caughtup_Year] [int] NULL,
 CONSTRAINT [PK_Learning_Client_Property_Mapping] PRIMARY KEY CLUSTERED 
(
	[Learning_Client_Property_Mapping_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Learning_Client_User](
	[Learning_Client_User_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_User_ID] [int] NOT NULL,
	[Status] [nvarchar](50) NOT NULL,
	[Last_Allocated_DTTM] [datetime] NULL,
	[Last_Updated_DTTM] [datetime] NULL,
	[Created_Date] [date] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[Learning_Client_User_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Login_Attempts](
	[Login_Attempts_ID] [bigint] IDENTITY(1,1) NOT NULL,
	[User_ID] [int] NOT NULL,
	[Failed_Attempts] [tinyint] NOT NULL,
	[Created_by_User_ID] [bigint] NOT NULL,
	[CreateDate] [datetime] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Last_Updated_by_User_ID] [bigint] NOT NULL,
	[Version] [numeric](18, 0) NOT NULL,
 CONSTRAINT [PK_Login_Attempts] PRIMARY KEY CLUSTERED 
(
	[Login_Attempts_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Module](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Name] [int] NOT NULL,
	[Description] [int] NOT NULL,
 CONSTRAINT [PK_Module] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Monitoring_Dashboard_Email_Status](
	[Monitoring_Dashboard_Email_Status_ID] [bigint] IDENTITY(1,1) NOT NULL,
	[Issue_ID] [int] NOT NULL,
	[Client_Code] [nvarchar](100) NOT NULL,
	[Property_Code] [nvarchar](200) NOT NULL,
	[Property_Name] [nvarchar](500) NOT NULL,
	[SFDC_Account_Number] [nvarchar](50) NOT NULL,
	[Email_Subject] [nvarchar](500) NOT NULL,
	[Email_Content] [nvarchar](max) NOT NULL,
	[Template_Name] [nvarchar](500) NULL,
	[Processing_Date] [datetime] NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Created_By_User_Id] [bigint] NOT NULL,
	[Last_Updated_DTTM] [datetime] NULL,
	[Last_Updated_By_User_Id] [bigint] NULL,
PRIMARY KEY CLUSTERED 
(
	[Monitoring_Dashboard_Email_Status_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Performance_Metrics_Stat](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[KeyName] [nvarchar](600) NOT NULL,
	[MethodName] [nvarchar](600) NULL,
	[ClassName] [nvarchar](600) NULL,
	[MinVal] [numeric](18, 0) NULL,
	[MaxVal] [numeric](18, 0) NULL,
	[AvgVal] [numeric](18, 0) NULL,
	[StdVal] [numeric](18, 3) NULL,
	[TotalVal] [numeric](18, 0) NULL,
	[SumOfSquares] [numeric](18, 0) NULL,
	[TotalNumberOfExecution] [numeric](18, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[KeyName] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Performance_Metrics_Summary](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Method_Name] [nvarchar](600) NULL,
	[Class_Name] [nvarchar](600) NULL,
	[Start_Date] [datetime] NULL,
	[End_Date] [datetime] NULL,
	[Execution_Time] [numeric](18, 3) NULL
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PMS_Migration_Config](
	[PMS_Migration_Config_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Old_System] [varchar](50) NOT NULL,
	[New_System] [varchar](50) NOT NULL,
	[Old_Sys_Date] [date] NOT NULL,
	[New_Sys_Date] [date] NOT NULL,
	[Is_Room_Type_Changed] [tinyint] NOT NULL,
	[Is_Mkt_Seg_Changed] [tinyint] NOT NULL,
	[Is_Rate_Code_Changed] [tinyint] NOT NULL,
	[Migration_State] [varchar](100) NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Original_Stage] [varchar](50) NULL,
	[Is_Backup_Created] [tinyint] NOT NULL,
 CONSTRAINT [PK_PMS_Migration_Config] PRIMARY KEY CLUSTERED 
(
	[PMS_Migration_Config_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [UNQ_Configs] UNIQUE NONCLUSTERED 
(
	[Client_ID] ASC,
	[Property_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PMS_Migration_Config_AUD](
	[PMS_Migration_Config_ID] [int] NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[Client_ID] [int] NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Old_System] [varchar](50) NOT NULL,
	[New_System] [varchar](50) NOT NULL,
	[Old_Sys_Date] [date] NOT NULL,
	[New_Sys_Date] [date] NOT NULL,
	[Is_Room_Type_Changed] [tinyint] NOT NULL,
	[Is_Mkt_Seg_Changed] [tinyint] NOT NULL,
	[Is_Rate_Code_Changed] [tinyint] NOT NULL,
	[Migration_State] [varchar](100) NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Original_Stage] [varchar](50) NULL,
	[Is_Backup_Created] [tinyint] NOT NULL,
 CONSTRAINT [PK_PMS_Migration_Config_AUD] PRIMARY KEY CLUSTERED 
(
	[PMS_Migration_Config_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Property_AUD](
	[Property_ID] [int] NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[Property_Name] [nvarchar](100) NULL,
	[Stage] [nvarchar](100) NULL,
	[Created_DTTM] [smalldatetime] NULL,
	[Created_By_User_Id] [bigint] NULL,
	[Last_Updated_DTTM] [datetime] NULL,
	[Last_Updated_By_User_Id] [bigint] NULL,
	[Force_Full_Decisions] [bit] NULL,
	[SFDC_Account_Number] [varchar](9) NULL,
	[Client_ID] [int] NULL,
	[Property_Code] [nvarchar](50) NULL,
	[Reservation_Data_Version] [int] NULL,
	[Questionnaire_Status] [nvarchar](20) NULL,
	[Client_Property_Code] [nvarchar](50) NULL,
	[Last_Purged_Date] [datetime] NULL,
	[Last_SAS_Purged_Date] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
	[Property_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Property_Group](
	[Property_Group_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[Property_Group_Name] [nvarchar](50) NOT NULL,
	[Property_Group_Description] [nvarchar](150) NULL,
	[Rule_ID] [int] NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[CreateDate] [datetime] NOT NULL,
	[User_ID] [int] NOT NULL,
	[Default_Group] [int] NOT NULL,
 CONSTRAINT [PK_Property_Group] PRIMARY KEY CLUSTERED 
(
	[Property_Group_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Property_Property_Group](
	[Property_Property_Group_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Property_Group_ID] [int] NOT NULL,
	[Status_ID] [int] NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_Property_Property_Group] PRIMARY KEY CLUSTERED 
(
	[Property_Property_Group_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [UC_Property_Property_Group] UNIQUE NONCLUSTERED 
(
	[Property_Group_ID] ASC,
	[Property_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Regulator_Control_Record](
	[Constraint_ID] [int] IDENTITY(1,1) NOT NULL,
	[Constraint_Name] [nvarchar](50) NOT NULL,
	[Version] [bigint] NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_Regulator_Constraint] PRIMARY KEY CLUSTERED 
(
	[Constraint_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Regulator_Request](
	[Request_ID] [int] IDENTITY(1,1) NOT NULL,
	[Request_Constraint] [nvarchar](50) NULL,
	[Service_Name] [nvarchar](100) NOT NULL,
	[CreateDate] [datetime] NOT NULL,
	[ModifiedDate] [datetime] NULL,
	[Status_ID] [int] NOT NULL,
	[Priority] [int] NOT NULL,
	[Ignore_In_Throttler] [bit] NOT NULL,
	[SAS_Server_Name] [nvarchar](50) NULL,
	[DB_Server_Name] [nvarchar](50) NULL,
	[App_Server_Name] [nvarchar](50) NULL,
	[Job_Instance_ID] [bigint] NULL,
	[Property_ID] [int] NULL,
	[Event_Date] [datetime] NULL,
 CONSTRAINT [PK_RegulatorRequest] PRIMARY KEY CLUSTERED 
(
	[Request_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Regulator_Status](
	[Status_ID] [int] IDENTITY(1,1) NOT NULL,
	[Status_Description] [nvarchar](50) NOT NULL,
	[Status_Code] [nvarchar](50) NOT NULL,
 CONSTRAINT [PK_Regulator_Status] PRIMARY KEY CLUSTERED 
(
	[Status_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Related_Config_Parameter](
	[Config_Param_ID] [int] NOT NULL,
	[Related_Config_Param_ID] [int] NOT NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_Related_Config_Parameter] PRIMARY KEY CLUSTERED 
(
	[Config_Param_ID] ASC,
	[Related_Config_Param_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Remote_Agent](
	[Remote_Agent_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[Agent_Alive_DTTM] [datetime] NULL,
	[Updater_Alive_DTTM] [datetime] NULL,
	[Agent_Version] [nvarchar](50) NULL,
	[Agent_Type] [nvarchar](20) NOT NULL,
	[Suspended] [bit] NOT NULL,
	[Machine_Name] [nvarchar](64) NULL,
	[OS_Version] [nvarchar](64) NULL,
	[Reported_Agent_Poll_Int] [int] NULL,
	[Reported_Agent_Property_Poll_Int] [int] NULL,
	[Reported_Agent_Property_Threads] [int] NULL,
 CONSTRAINT [PK_Remote_Agent] PRIMARY KEY CLUSTERED 
(
	[Remote_Agent_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Remote_Agent_Update](
	[Remote_Agent_Update_ID] [int] IDENTITY(1,1) NOT NULL,
	[Build_Number] [nvarchar](50) NULL,
	[Initiated_By] [nvarchar](150) NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Completed_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Remote_Agent_Update] PRIMARY KEY CLUSTERED 
(
	[Remote_Agent_Update_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Remote_Task](
	[Remote_Task_ID] [int] IDENTITY(1,1) NOT NULL,
	[Remote_Agent_ID] [int] NOT NULL,
	[Task_Class] [nvarchar](20) NOT NULL,
	[Property_ID] [int] NULL,
	[Task_Type] [nvarchar](20) NOT NULL,
	[Task_Status] [nvarchar](20) NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Completed_DTTM] [datetime] NULL,
	[Results] [nvarchar](512) NULL,
	[Scheduled_DTTM] [datetime] NULL,
	[Notes] [text] NULL,
 CONSTRAINT [PK_Remote_Task] PRIMARY KEY CLUSTERED 
(
	[Remote_Task_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ROA_Report](
	[ROA_Report_ID] [int] NOT NULL,
	[ROA_Report_Category_ID] [int] NOT NULL,
	[Name] [nvarchar](100) NOT NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_ROA_Report] PRIMARY KEY CLUSTERED 
(
	[ROA_Report_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ROA_Report_Category](
	[ROA_Report_Category_ID] [int] NOT NULL,
	[Name] [nvarchar](100) NOT NULL,
	[Displayable] [bit] NOT NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_ROA_Report_Category] PRIMARY KEY CLUSTERED 
(
	[ROA_Report_Category_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ROA_Report_Request](
	[ROA_Report_Request_ID] [int] IDENTITY(1,1) NOT NULL,
	[ROA_Report_Category_ID] [int] NULL,
	[ROA_Report_ID] [int] NULL,
	[Property_ID] [int] NOT NULL,
	[Report_Requested_For_User_ID] [int] NOT NULL,
	[CSV_Output] [bit] NOT NULL,
	[PDF_Output] [bit] NOT NULL,
	[ROA_Report_Status_ID] [int] NOT NULL,
	[Status_Message] [nvarchar](100) NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Decision_ID] [int] NOT NULL,
 CONSTRAINT [PK_ROA_Report_Request] PRIMARY KEY CLUSTERED 
(
	[ROA_Report_Request_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ROA_Report_Request_Status](
	[ROA_Report_Request_Status_ID] [int] NOT NULL,
	[Code] [nvarchar](10) NOT NULL,
	[Name] [nvarchar](10) NOT NULL,
 CONSTRAINT [PK_ROA_Report_Request_Status] PRIMARY KEY CLUSTERED 
(
	[ROA_Report_Request_Status_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Roles](
	[Role_ID] [int] IDENTITY(1,1) NOT NULL,
	[Role_Name] [varchar](100) NOT NULL,
	[Description] [varchar](1000) NULL,
	[CorporateUser] [bit] NOT NULL,
	[InternalUser] [bit] NOT NULL,
	[Permissions] [ntext] NULL,
	[Client_Code] [nvarchar](50) NULL,
	[View_Announcements] [bit] NOT NULL,
	[Created_By_User_ID] [bigint] NULL,
	[Created_DTTM] [datetime] NULL,
	[Last_Updated_By_User_ID] [bigint] NULL,
	[Last_Updated_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Roles] PRIMARY KEY CLUSTERED 
(
	[Role_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Roles_AUD](
	[Role_ID] [int] NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[Role_Name] [varchar](100) NOT NULL,
	[Description] [varchar](1000) NULL,
	[CorporateUser] [bit] NOT NULL,
	[InternalUser] [bit] NOT NULL,
	[Permissions] [ntext] NULL,
	[Client_Code] [nvarchar](50) NOT NULL,
	[View_Announcements] [bit] NOT NULL,
	[Created_By_User_ID] [bigint] NULL,
	[Created_DTTM] [datetime] NULL,
	[Last_Updated_By_User_ID] [bigint] NULL,
	[Last_Updated_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Roles_AUD] PRIMARY KEY CLUSTERED 
(
	[Role_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Rule_Attribute_Value](
	[Rule_Attribute_Value_ID] [int] IDENTITY(1,1) NOT NULL,
	[Rule_ID] [int] NOT NULL,
	[Client_Attribute_Value_ID] [int] NOT NULL,
	[Condition_Type_ID] [int] NOT NULL,
	[Ranking] [int] NOT NULL,
	[Conjuction_Type_ID] [int] NOT NULL,
	[CreateDate_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_Rule_Attribute_Value] PRIMARY KEY CLUSTERED 
(
	[Rule_Attribute_Value_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Rules](
	[Rule_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[CreateDate_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_Rules] PRIMARY KEY CLUSTERED 
(
	[Rule_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Sales_Users](
	[Sales_Users_ID] [bigint] NOT NULL,
	[First_Name] [nvarchar](50) NOT NULL,
	[Last_Name] [nvarchar](50) NOT NULL,
	[Email_Address] [nvarchar](100) NOT NULL,
	[Active] [int] NOT NULL
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SAS_File_Loc](
	[SAS_File_Loc_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NOT NULL,
	[SAS_File_Location_Path] [nvarchar](200) NOT NULL,
	[Status_ID] [int] NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
	[SAS_Server_Name] [nvarchar](50) NOT NULL,
	[SAS_Ratchet_File_Location_Path] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_SAS_File_Loc] PRIMARY KEY CLUSTERED 
(
	[SAS_File_Loc_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Scheduled_Report](
	[Scheduled_Report_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[Property_ID] [int] NULL,
	[Owner_ID] [int] NOT NULL,
	[Report_Type] [nvarchar](100) NOT NULL,
	[Report_Criteria] [nvarchar](max) NULL,
	[Status_ID] [int] NOT NULL,
	[Name] [nvarchar](150) NOT NULL,
	[Description] [nvarchar](250) NULL,
	[Scheduled_Time] [time](7) NOT NULL,
	[Next_Run_Time] [datetime] NULL,
	[Last_Run_Time] [datetime] NULL,
	[Frequency] [int] NOT NULL,
	[Frequency_Type] [nvarchar](50) NOT NULL,
	[Language] [nvarchar](50) NOT NULL,
	[Output_Type] [nvarchar](50) NOT NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Date_Format] [nvarchar](100) NOT NULL,
 CONSTRAINT [PK_Scheduled_Report] PRIMARY KEY CLUSTERED 
(
	[Scheduled_Report_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Scheduled_Report_AUD](
	[Scheduled_Report_ID] [int] NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[Owner_ID] [int] NULL,
	[Client_ID] [int] NULL,
	[Property_ID] [int] NULL,
	[Report_Type] [nvarchar](100) NULL,
	[Report_Criteria] [text] NULL,
	[Status_ID] [int] NULL,
	[Name] [nvarchar](150) NULL,
	[Description] [nvarchar](250) NULL,
	[Scheduled_Time] [time](7) NULL,
	[Next_Run_Time] [datetime] NULL,
	[Last_Run_Time] [datetime] NULL,
	[Frequency] [int] NULL,
	[Frequency_Type] [nvarchar](50) NULL,
	[Language] [nvarchar](50) NULL,
	[Output_Type] [nvarchar](50) NULL,
	[Created_by_User_ID] [int] NULL,
	[Created_DTTM] [datetime] NULL,
	[Last_Updated_by_User_ID] [int] NULL,
	[Last_Updated_DTTM] [datetime] NULL,
	[Date_Format] [nvarchar](100) NOT NULL,
 CONSTRAINT [PK_Scheduled_Report_AUD] PRIMARY KEY CLUSTERED 
(
	[Scheduled_Report_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Scheduled_Report_Recipient](
	[Scheduled_Report_Recipient_ID] [int] IDENTITY(1,1) NOT NULL,
	[Scheduled_Report_ID] [int] NOT NULL,
	[User_ID] [int] NOT NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_Scheduled_Report_Recipient] PRIMARY KEY CLUSTERED 
(
	[Scheduled_Report_Recipient_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Scheduled_Report_Recipient_AUD](
	[Scheduled_Report_Recipient_ID] [int] NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[Scheduled_Report_ID] [int] NULL,
	[User_ID] [int] NULL,
	[Created_by_User_ID] [int] NULL,
	[Created_DTTM] [datetime] NULL,
	[Last_Updated_by_User_ID] [int] NULL,
	[Last_Updated_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Scheduled_Report_Recipient_AUD] PRIMARY KEY CLUSTERED 
(
	[Scheduled_Report_Recipient_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Sfdc_Email_Template](
	[Sfdc_Email_Template_ID] [int] IDENTITY(1,1) NOT NULL,
	[Template_Category] [nvarchar](200) NOT NULL,
	[Template_Name] [nvarchar](450) NOT NULL,
	[Template_Content] [nvarchar](max) NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Created_By_User_Id] [bigint] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Last_Updated_By_User_Id] [bigint] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[Sfdc_Email_Template_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
UNIQUE NONCLUSTERED 
(
	[Template_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Status](
	[Status_ID] [int] IDENTITY(1,1) NOT NULL,
	[Status_Name] [nvarchar](100) NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_Status] PRIMARY KEY CLUSTERED 
(
	[Status_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[System_Announcement](
	[System_Announcement_ID] [int] IDENTITY(1,1) NOT NULL,
	[Title] [nvarchar](100) NOT NULL,
	[Message] [nvarchar](max) NULL,
	[Status] [nvarchar](50) NOT NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Modification_Date] [datetime] NULL,
	[Modified_By] [int] NULL,
	[CreateDate] [datetime] NOT NULL,
	[Start_Date] [date] NULL,
	[End_Date] [date] NULL,
 CONSTRAINT [PK_System_Announcement] PRIMARY KEY CLUSTERED 
(
	[System_Announcement_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[System_Usage](
	[System_Usage_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NULL,
	[Property_Group_ID] [int] NULL,
	[User_ID] [int] NOT NULL,
	[Page_ID] [int] NULL,
	[Type_ID] [int] NOT NULL,
	[Details] [nvarchar](255) NULL,
	[Start_DTTM] [datetime] NOT NULL,
	[End_DTTM] [datetime] NULL,
 CONSTRAINT [System_Usage_ID] PRIMARY KEY CLUSTERED 
(
	[System_Usage_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[System_Usage_Page](
	[System_Usage_Page_ID] [int] IDENTITY(1,1) NOT NULL,
	[Name] [nvarchar](100) NOT NULL,
	[Description] [nvarchar](100) NOT NULL,
	[Create_DTTM] [datetime] NOT NULL,
	[Page_Code] [nvarchar](150) NOT NULL,
 CONSTRAINT [System_Usage_Page_ID] PRIMARY KEY CLUSTERED 
(
	[System_Usage_Page_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[System_Usage_Type](
	[System_Usage_Type_ID] [int] IDENTITY(1,1) NOT NULL,
	[Name] [nvarchar](50) NOT NULL,
	[Description] [nvarchar](100) NOT NULL,
	[Create_DTTM] [datetime] NOT NULL,
 CONSTRAINT [System_Usage_Type_ID] PRIMARY KEY CLUSTERED 
(
	[System_Usage_Type_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Task_Parameter](
	[Task_Parameter_ID] [int] IDENTITY(1,1) NOT NULL,
	[Remote_Task_ID] [int] NOT NULL,
	[Parameter_Type] [nvarchar](20) NOT NULL,
	[Value] [nvarchar](256) NULL,
 CONSTRAINT [PK_Task_Parameter] PRIMARY KEY CLUSTERED 
(
	[Task_Parameter_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TetrisRevisionEntity](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[timestamp] [numeric](19, 0) NOT NULL,
	[userID] [int] NULL,
	[userName] [varchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Two_Factor_Authentication](
	[Authentication_ID] [int] IDENTITY(1,1) NOT NULL,
	[User_ID] [int] NOT NULL,
	[Token] [nvarchar](150) NOT NULL,
	[Created_DTTM] [datetime] NULL,
 CONSTRAINT [PK_Two_Factor_Authentication] PRIMARY KEY CLUSTERED 
(
	[Authentication_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[User_Auth_Group_Role](
	[User_Auth_Group_Role_ID] [int] IDENTITY(1,1) NOT NULL,
	[User_ID] [int] NOT NULL,
	[Auth_Group_ID] [int] NOT NULL,
	[Role_ID] [nvarchar](100) NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_User_Auth_Group_Role] PRIMARY KEY CLUSTERED 
(
	[User_Auth_Group_Role_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[User_Auth_Group_Role_AUD](
	[User_Auth_Group_Role_ID] [int] NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[User_ID] [int] NOT NULL,
	[Auth_Group_ID] [int] NOT NULL,
	[Role_ID] [nvarchar](100) NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_User_Auth_Group_Role_AUD] PRIMARY KEY CLUSTERED 
(
	[User_Auth_Group_Role_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[User_Feedback](
	[Feedback_ID] [int] IDENTITY(1,1) NOT NULL,
	[Client_ID] [int] NOT NULL,
	[Property_ID] [int] NOT NULL,
	[User_ID] [int] NOT NULL,
	[Module] [nvarchar](20) NOT NULL,
	[Type] [nvarchar](20) NULL,
	[create_date] [datetime] NOT NULL,
	[Rating] [int] NULL,
	[Description] [nvarchar](250) NULL,
 CONSTRAINT [PK_User_Feedback] PRIMARY KEY CLUSTERED 
(
	[Feedback_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[User_Individual_Property_Role](
	[User_Individual_Property_Role_ID] [int] IDENTITY(1,1) NOT NULL,
	[User_ID] [int] NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Role_ID] [nvarchar](100) NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_User_Individual_Property_Role] PRIMARY KEY CLUSTERED 
(
	[User_Individual_Property_Role_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[User_Individual_Property_Role_AUD](
	[User_Individual_Property_Role_ID] [int] NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[User_ID] [int] NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Role_ID] [nvarchar](100) NOT NULL,
	[CreateDate] [smalldatetime] NOT NULL,
 CONSTRAINT [PK_User_Individual_Property_Role_AUD] PRIMARY KEY CLUSTERED 
(
	[User_Individual_Property_Role_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Users](
	[User_ID] [int] IDENTITY(1,1) NOT NULL,
	[User_Name] [nvarchar](50) NOT NULL,
	[Screen_Name] [nvarchar](512) NOT NULL,
	[Email_Address] [nvarchar](150) NOT NULL,
	[Status_ID] [int] NOT NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Client_Code] [nvarchar](50) NOT NULL,
	[Internal] [bit] NOT NULL,
	[First_Name] [nvarchar](50) NULL,
	[Last_Name] [nvarchar](50) NULL,
	[Corporate] [bit] NOT NULL,
	[Salesforce_Access] [bit] NOT NULL,
	[REMAINING_FAILED_ATTEMPTS_ALLOWED] [int] NOT NULL,
	[Last_Password_Change_DTTM] [datetime] NULL,
	[Password_Never_Expire] [bit] NOT NULL,
	[Password_History] [nvarchar](1024) NULL,
	[User_Language] [varchar](100) NULL,
	[Password] [nvarchar](150) NOT NULL,
	[Salt] [nvarchar](150) NULL,
	[User_Preferences] [ntext] NULL,
	[Salesforce_Portal_Id] [nvarchar](50) NULL,
	[Salesforce_Organization_Id] [nvarchar](50) NULL,
	[Sun_fm_saml2_NameId_Info] [text] NULL,
	[Sun_fm_saml2_NameId_Infokey] [text] NULL,
	[Learning_Access] [bit] NOT NULL,
 CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED 
(
	[User_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Users_AUD](
	[User_ID] [int] NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[User_Name] [nvarchar](50) NOT NULL,
	[Screen_Name] [nvarchar](512) NOT NULL,
	[Email_Address] [nvarchar](150) NOT NULL,
	[Status_ID] [int] NOT NULL,
	[Created_by_User_ID] [int] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Last_Updated_by_User_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Client_Code] [nvarchar](50) NOT NULL,
	[Internal] [bit] NOT NULL,
	[First_Name] [nvarchar](50) NULL,
	[Last_Name] [nvarchar](50) NULL,
	[Corporate] [bit] NOT NULL,
	[Salesforce_Access] [bit] NOT NULL,
	[REMAINING_FAILED_ATTEMPTS_ALLOWED] [int] NOT NULL,
	[Last_Password_Change_DTTM] [datetime] NULL,
	[Password_Never_Expire] [bit] NOT NULL,
	[Password_History] [nvarchar](1024) NULL,
	[User_Language] [varchar](100) NULL,
	[Password] [nvarchar](150) NOT NULL,
	[Salt] [nvarchar](150) NULL,
	[User_Preferences] [ntext] NULL,
	[Salesforce_Portal_Id] [nvarchar](50) NULL,
	[Salesforce_Organization_Id] [nvarchar](50) NULL,
	[Sun_fm_saml2_NameId_Info] [text] NULL,
	[Sun_fm_saml2_NameId_Infokey] [text] NULL,
	[Learning_Access] [bit] NOT NULL,
 CONSTRAINT [PK_Users_AUD] PRIMARY KEY CLUSTERED 
(
	[User_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Client_Code] ON [dbo].[Client]
(
	[Client_Code] ASC
)
WHERE ([Status_Id]=(1))
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [NC_CVRD_Client_Business_Group] ON [dbo].[Client_Business_Group]
(
	[Client_Business_View_ID] ASC
)
INCLUDE ( 	[Client_Business_Group_ID],
	[Client_Business_Group_Name]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [NC_CVRD_cbg_msg_code] ON [dbo].[Client_Business_Group_Mkt_Seg_Code]
(
	[Client_Business_Group_ID] ASC,
	[Mkt_Seg_Code] ASC
)
INCLUDE ( 	[Client_Business_Group_Mkt_Seg_Code_ID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [NC_CVRD_Client_Business_View] ON [dbo].[Client_Business_View]
(
	[Client_Business_View_Name] ASC
)
INCLUDE ( 	[Client_Business_View_ID],
	[Default_View]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Client_Business_View_Name] ON [dbo].[Client_Business_View]
(
	[Client_ID] ASC,
	[Client_Business_View_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [IDX_Client_User_Client_ID_User_ID] ON [dbo].[Client_User]
(
	[Client_ID] ASC,
	[User_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [IDX_Client_User_User_ID] ON [dbo].[Client_User]
(
	[User_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Parameter_Type] ON [dbo].[Config_Parameter_Type]
(
	[Param_Type] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [NonUnique_Context] ON [dbo].[Config_Parameter_Value]
(
	[Context] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Config_Parameter_Value_AUD_Context_Last_Updated_DTTM_Predefined_ID] ON [dbo].[Config_Parameter_Value_AUD]
(
	[Context] ASC,
	[Last_Updated_DTTM] ASC,
	[Config_Parameter_Predefined_Value_ID] ASC
)
INCLUDE ( 	[Config_Parameter_ID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Config_Parameter_Value_AUD_Context_Parameter_ID] ON [dbo].[Config_Parameter_Value_AUD]
(
	[Context] ASC,
	[Config_Parameter_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [IDX_Config_Parameter_Value_AUD_REV] ON [dbo].[Config_Parameter_Value_AUD]
(
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Config_File_Record_Prop_Code_Rec_Type] ON [dbo].[Configuration_File_Record]
(
	[Property_Code] ASC,
	[Record_Type] ASC
)
INCLUDE ( 	[Configuration_File_Record_ID],
	[Configuration_File_ID],
	[Record_Status],
	[Record],
	[Failure_Reason],
	[CreateDate]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [NonUnique_DBType_ID_Incl_DBName] ON [dbo].[DBLoc]
(
	[DBType_ID] ASC
)
INCLUDE ( 	[DBName]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [UQ_DBLoc] ON [dbo].[DBLoc]
(
	[DBName] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [unique_Dbtype_Name] ON [dbo].[DBType]
(
	[DBType_Name] ASC,
	[Status_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [UQ_DBType] ON [dbo].[DBType]
(
	[DBType_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Input_Processing_ID] ON [dbo].[Decision_Delivery]
(
	[Input_Processing_ID] ASC
)
INCLUDE ( 	[Decision_Delivery_ID],
	[Decision_ID],
	[Uploaded_DTTM],
	[Destination_ID],
	[Overdue_DTTM],
	[Started_DTTM],
	[Ack_Received_DTTM],
	[Result]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [NonUnique_Decision_ID_Destination] ON [dbo].[Decision_Delivery]
(
	[Decision_ID] ASC,
	[Destination_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Decision_Delivery_ID] ON [dbo].[Decision_Delivery_By_Type]
(
	[Decision_Delivery_ID] ASC
)
INCLUDE ( 	[Decision_Delivery_By_Type_ID],
	[Decision_Type],
	[Status],
	[Started_DTTM],
	[Delivered_DTTM],
	[Acknowledged_DTTM]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Force_Full_Decisions] ON [dbo].[Force_Full_Decisions]
(
	[Client_ID] ASC,
	[Property_ID] ASC,
	[Outbound_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Input_Processing_Input_Type_Last_Updated_DTTM] ON [dbo].[Input_Processing]
(
	[Input_Type] ASC,
	[Last_Updated_DTTM] ASC
)
INCLUDE ( 	[Property_Daily_Processing_ID],
	[Received_DTTM],
	[Completed_DTTM],
	[Status],
	[Started_DTTM],
	[Decisions_Generated_DTTM]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Input_Processing_Input_Type_Received_DTTM] ON [dbo].[Input_Processing]
(
	[Input_Type] ASC,
	[Received_DTTM] ASC
)
INCLUDE ( 	[Property_Daily_Processing_ID],
	[Input_ID],
	[Decisions_Generated_DTTM]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Input_Processing_InputType_Status_Extract_Completed_DTTM] ON [dbo].[Input_Processing]
(
	[Input_Type] ASC,
	[Status] ASC,
	[Extract_Completed_DTTM] ASC
)
INCLUDE ( 	[Property_Daily_Processing_ID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Input_Processing_Property_Daily_Processing_ID_Status_Input_Type] ON [dbo].[Input_Processing]
(
	[Property_Daily_Processing_ID] ASC,
	[Status] ASC,
	[Input_Type] ASC
)
INCLUDE ( 	[Input_Processing_ID],
	[Input_ID],
	[Received_DTTM],
	[Completed_DTTM],
	[SLA_Violation],
	[Overdue_DTTM],
	[CDP_Schedule_ID],
	[Prepared_DTTM],
	[Started_DTTM],
	[Decisions_Generated_DTTM],
	[Created_DTTM],
	[Created_by_User_ID],
	[Last_Updated_DTTM],
	[Last_Updated_by_User_ID],
	[Data_Collection_Started_DTTM],
	[Summary_Started_DTTM],
	[Summary_Completed_DTTM],
	[Extract_Status],
	[Extract_Started_DTTM],
	[Extract_Completed_DTTM]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [NonUnique_Date_SLA] ON [dbo].[Input_Processing]
(
	[Received_DTTM] ASC,
	[SLA_Violation] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [NonUnique_InputId] ON [dbo].[Input_Processing]
(
	[Input_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [NonUnique_Overdue] ON [dbo].[Input_Processing]
(
	[Status] ASC,
	[Overdue_DTTM] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [IDX_Input_Processing_Job_ID] ON [dbo].[Input_Processing_Job]
(
	[Input_Processing_ID] ASC
)
INCLUDE ( 	[Input_Processing_Job_ID],
	[Job_Instance_ID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [IDX_Input_Processing_Note_Input_Processing_ID] ON [dbo].[INPUT_PROCESSING_NOTE]
(
	[INPUT_PROCESSING_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [IDX_Monitoring_Dashboard_Email_Status_Issue_ID] ON [dbo].[Monitoring_Dashboard_Email_Status]
(
	[Issue_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Property_Client_ID] ON [dbo].[Property]
(
	[Client_ID] ASC
)
INCLUDE ( 	[Property_ID],
	[Property_Code],
	[Property_Name],
	[Status_ID],
	[Created_DTTM],
	[DBLoc_ID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [unique_Property_Code] ON [dbo].[Property]
(
	[Property_Code] ASC,
	[Status_ID] ASC,
	[Client_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Property] ON [dbo].[Property]
(
	[Property_Code] ASC,
	[Client_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Property_Daily_Processing_Client_Date_Stage] ON [dbo].[Property_Daily_Processing]
(
	[Client_Code] ASC,
	[Processing_Date] ASC,
	[Stage] ASC
)
INCLUDE ( 	[Property_Code],
	[Property_Name]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Property_Daily_Processing_Date_Stage] ON [dbo].[Property_Daily_Processing]
(
	[Processing_Date] ASC,
	[Stage] ASC
)
INCLUDE ( 	[Client_Code]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Property_Daily_Processing_Property_And_Date] ON [dbo].[Property_Daily_Processing]
(
	[Client_Code] ASC,
	[Property_Code] ASC,
	[Processing_Date] ASC
)
INCLUDE ( 	[Property_Daily_Processing_ID],
	[Stage],
	[Property_Time_Zone],
	[Expected_CDP_Count],
	[Completed_CDP_Count],
	[Status]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Regulator_Constraint] ON [dbo].[Regulator_Control_Record]
(
	[Constraint_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Regulator_Request_App_Server_Status_And_Ignore_In_Throttler] ON [dbo].[Regulator_Request]
(
	[App_Server_Name] ASC,
	[Status_ID] ASC,
	[Ignore_In_Throttler] ASC
)
INCLUDE ( 	[Request_ID],
	[Request_Constraint],
	[Service_Name],
	[CreateDate],
	[ModifiedDate],
	[Priority],
	[SAS_Server_Name],
	[DB_Server_Name],
	[Job_Instance_ID],
	[Property_ID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Regulator_Request_Constraint_And_Status] ON [dbo].[Regulator_Request]
(
	[Request_Constraint] ASC,
	[Status_ID] ASC
)
INCLUDE ( 	[Request_ID],
	[Service_Name],
	[CreateDate],
	[ModifiedDate],
	[Priority],
	[Ignore_In_Throttler],
	[SAS_Server_Name],
	[DB_Server_Name],
	[App_Server_Name],
	[Job_Instance_ID],
	[Property_ID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Regulator_Request_DB_Server_Status_And_Ignore_In_Throttler] ON [dbo].[Regulator_Request]
(
	[DB_Server_Name] ASC,
	[Status_ID] ASC,
	[Ignore_In_Throttler] ASC
)
INCLUDE ( 	[Request_ID],
	[Request_Constraint],
	[Service_Name],
	[CreateDate],
	[ModifiedDate],
	[Priority],
	[SAS_Server_Name],
	[App_Server_Name],
	[Job_Instance_ID],
	[Property_ID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Regulator_Request_Job_Instance_ID] ON [dbo].[Regulator_Request]
(
	[Job_Instance_ID] ASC
)
INCLUDE ( 	[Request_ID],
	[Status_ID],
	[Request_Constraint],
	[Service_Name],
	[CreateDate],
	[ModifiedDate],
	[Priority],
	[Ignore_In_Throttler],
	[SAS_Server_Name],
	[DB_Server_Name],
	[App_Server_Name],
	[Property_ID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [IDX_Regulator_Request_ModifiedDate_Status] ON [dbo].[Regulator_Request]
(
	[ModifiedDate] ASC,
	[Status_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Regulator_Request_Property_ID] ON [dbo].[Regulator_Request]
(
	[Property_ID] ASC
)
INCLUDE ( 	[Request_ID],
	[Status_ID],
	[Request_Constraint],
	[Service_Name],
	[CreateDate],
	[ModifiedDate],
	[Priority],
	[Ignore_In_Throttler],
	[SAS_Server_Name],
	[DB_Server_Name],
	[App_Server_Name],
	[Job_Instance_ID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Regulator_Request_SAS_Server_Status_And_Ignore_In_Throttler] ON [dbo].[Regulator_Request]
(
	[SAS_Server_Name] ASC,
	[Status_ID] ASC,
	[Ignore_In_Throttler] ASC
)
INCLUDE ( 	[Request_ID],
	[Request_Constraint],
	[Service_Name],
	[CreateDate],
	[ModifiedDate],
	[Priority],
	[DB_Server_Name],
	[App_Server_Name],
	[Job_Instance_ID],
	[Property_ID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Regulator_Request_Service_Status_And_Ignore_In_Throttler] ON [dbo].[Regulator_Request]
(
	[Service_Name] ASC,
	[Status_ID] ASC,
	[Ignore_In_Throttler] ASC
)
INCLUDE ( 	[Request_ID],
	[Request_Constraint],
	[CreateDate],
	[ModifiedDate],
	[Priority],
	[SAS_Server_Name],
	[DB_Server_Name],
	[App_Server_Name],
	[Job_Instance_ID],
	[Property_ID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Regulator_Status] ON [dbo].[Regulator_Status]
(
	[Status_Code] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Agent_and_Status] ON [dbo].[Remote_Task]
(
	[Remote_Agent_ID] ASC,
	[Task_Status] ASC
)
INCLUDE ( 	[Remote_Task_ID],
	[Task_Class],
	[Property_ID],
	[Task_Type],
	[Created_DTTM],
	[Completed_DTTM],
	[Results],
	[Scheduled_DTTM]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Property_Task_Type_Status_Agent] ON [dbo].[Remote_Task]
(
	[Property_ID] ASC,
	[Task_Class] ASC,
	[Task_Type] ASC,
	[Task_Status] ASC
)
INCLUDE ( 	[Remote_Agent_ID]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [UNIQUE_Roles] ON [dbo].[Roles]
(
	[Client_Code] ASC,
	[Role_Name] ASC
)
INCLUDE ( 	[Role_ID],
	[Description],
	[CorporateUser],
	[InternalUser],
	[View_Announcements]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [UQ_SAS_File_Loc] ON [dbo].[SAS_File_Loc]
(
	[Property_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Scheduled_Report_Active_AND_Next_Run_Time] ON [dbo].[Scheduled_Report]
(
	[Status_ID] ASC,
	[Next_Run_Time] ASC
)
INCLUDE ( 	[Scheduled_Report_ID],
	[Client_ID],
	[Property_ID],
	[Owner_ID],
	[Report_Type],
	[Name],
	[Description],
	[Scheduled_Time],
	[Last_Run_Time],
	[Frequency],
	[Frequency_Type],
	[Language],
	[Output_Type],
	[Created_by_User_ID],
	[Created_DTTM],
	[Last_Updated_by_User_ID],
	[Last_Updated_DTTM]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [unique_Status_Name] ON [dbo].[Status]
(
	[Status_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_System_Usage_Details_Type_ID_End_DTTM] ON [dbo].[System_Usage]
(
	[Details] ASC,
	[Type_ID] ASC,
	[End_DTTM] ASC
)
INCLUDE ( 	[System_Usage_ID],
	[Property_ID],
	[Property_Group_ID],
	[User_ID],
	[Page_ID],
	[Start_DTTM]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [IDX_System_Usage_TypeID_StartDTTM_EndDTTM] ON [dbo].[System_Usage]
(
	[Type_ID] ASC,
	[Start_DTTM] ASC,
	[End_DTTM] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [System_Usage_UserID_TypeID_EndDTTM] ON [dbo].[System_Usage]
(
	[User_ID] ASC,
	[Type_ID] ASC,
	[End_DTTM] ASC
)
INCLUDE ( 	[Start_DTTM]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Remote_Task_ID] ON [dbo].[Task_Parameter]
(
	[Remote_Task_ID] ASC
)
INCLUDE ( 	[Task_Parameter_ID],
	[Parameter_Type],
	[Value]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Two_Factor_Authentication] ON [dbo].[Two_Factor_Authentication]
(
	[User_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_User_Auth_Group_Role_ON_Auth_Group_Id_User_ID] ON [dbo].[User_Auth_Group_Role]
(
	[Auth_Group_ID] ASC,
	[User_ID] ASC
)
INCLUDE ( 	[User_Auth_Group_Role_ID],
	[Role_ID],
	[CreateDate]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [UNIQUE_User_Auth_Group_Role_ON_User_ID] ON [dbo].[User_Auth_Group_Role]
(
	[User_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [IDX_User_Individual_Property_Role_ON_Property_Id_User_Id] ON [dbo].[User_Individual_Property_Role]
(
	[Property_ID] ASC,
	[User_ID] ASC
)
INCLUDE ( 	[User_Individual_Property_Role_ID],
	[Role_ID],
	[CreateDate]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [NonUnique_User_ID] ON [dbo].[User_Individual_Property_Role]
(
	[User_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Global_User_Client_Code_Internal] ON [dbo].[Users]
(
	[Client_Code] ASC,
	[Internal] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [IDX_Global_User_ON_Email_Address_Client_Code_Internal_Status_ID_User_Name] ON [dbo].[Users]
(
	[Email_Address] ASC,
	[Client_Code] ASC,
	[Internal] ASC,
	[Status_ID] ASC,
	[User_Name] ASC
)
INCLUDE ( 	[User_ID],
	[Screen_Name],
	[Created_by_User_ID],
	[Created_DTTM],
	[Last_Updated_by_User_ID],
	[Last_Updated_DTTM],
	[First_Name],
	[Last_Name],
	[Corporate],
	[Salesforce_Access],
	[REMAINING_FAILED_ATTEMPTS_ALLOWED],
	[Last_Password_Change_DTTM],
	[Password_Never_Expire],
	[Password_History],
	[User_Language],
	[Password],
	[Salt],
	[Salesforce_Portal_Id],
	[Salesforce_Organization_Id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE NONCLUSTERED INDEX [NonUnique_User_Name] ON [dbo].[Users]
(
	[User_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
CREATE UNIQUE NONCLUSTERED INDEX [UNIQUE_User_ClientCode_EmailAddress] ON [dbo].[Users]
(
	[Email_Address] ASC
)
INCLUDE ( 	[Client_Code],
	[User_ID],
	[User_Name],
	[Screen_Name],
	[Status_ID],
	[Internal],
	[Created_by_User_ID],
	[Created_DTTM],
	[Last_Updated_by_User_ID],
	[Last_Updated_DTTM],
	[First_Name],
	[Last_Name],
	[Corporate],
	[Salesforce_Access],
	[REMAINING_FAILED_ATTEMPTS_ALLOWED],
	[Last_Password_Change_DTTM],
	[Password_Never_Expire],
	[Password_History],
	[User_Language],
	[Password],
	[Salt],
	[Salesforce_Portal_Id],
	[Salesforce_Organization_Id]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
ALTER TABLE [dbo].[Attribute_Display_Type] ADD  CONSTRAINT [DF_Display_Type_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Auth_Group] ADD  CONSTRAINT [DF_Auth_Group_Status_ID]  DEFAULT ((1)) FOR [Status_ID]
GO
ALTER TABLE [dbo].[Auth_Group] ADD  CONSTRAINT [DF_Auth_Group_Created_DTTM]  DEFAULT (getdate()) FOR [Created_DTTM]
GO
ALTER TABLE [dbo].[Auth_Group_Property] ADD  CONSTRAINT [DF_Auth_Group_Property_Status_ID]  DEFAULT ((1)) FOR [Status_ID]
GO
ALTER TABLE [dbo].[Auth_Group_Property] ADD  CONSTRAINT [DF_Auth_Group_Property_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Client] ADD  CONSTRAINT [DF_Corp_StatusID]  DEFAULT ((1)) FOR [Status_ID]
GO
ALTER TABLE [dbo].[Client] ADD  CONSTRAINT [DF_Corp_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Client_Attribute] ADD  CONSTRAINT [DF_Client_Attribute_User_Length_Entry]  DEFAULT ((0)) FOR [User_Length_Entry]
GO
ALTER TABLE [dbo].[Client_Attribute] ADD  CONSTRAINT [DF_Client_Attribute_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Client_Attribute_Value] ADD  CONSTRAINT [DF_Client_Attribute_Value_Client_Attribute_Default]  DEFAULT ((0)) FOR [Client_Attribute_Default]
GO
ALTER TABLE [dbo].[Client_Attribute_Value] ADD  CONSTRAINT [DF_Client_Attribute_Value_Status_ID]  DEFAULT ((1)) FOR [Status_ID]
GO
ALTER TABLE [dbo].[Client_Attribute_Value] ADD  CONSTRAINT [DF_Client_Attribute_Value_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Client_Business_Group] ADD  CONSTRAINT [DF_Client_Business_Group_Ranking]  DEFAULT ((0)) FOR [Ranking]
GO
ALTER TABLE [dbo].[Client_Business_Group] ADD  CONSTRAINT [DF_Client_Business_Group_Status_ID]  DEFAULT ((1)) FOR [Status_ID]
GO
ALTER TABLE [dbo].[Client_Business_Group] ADD  CONSTRAINT [DF_Client_Business_Group_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
GO
ALTER TABLE [dbo].[Client_Business_Group] ADD  CONSTRAINT [DF_Client_Business_Group_Created_DTTM]  DEFAULT (getdate()) FOR [Created_DTTM]
GO
ALTER TABLE [dbo].[Client_Business_Group_Mkt_Seg_Code] ADD  CONSTRAINT [DF_Client_Business_Group_Mkt_Seg_Code_CreateDate_DTTM]  DEFAULT (getdate()) FOR [CreateDate_DTTM]
GO
ALTER TABLE [dbo].[Client_Business_View] ADD  CONSTRAINT [DF_Client_Business_View_Status_ID]  DEFAULT ((1)) FOR [Status_ID]
GO
ALTER TABLE [dbo].[Client_Business_View] ADD  CONSTRAINT [DF_Client_Business_View_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
GO
ALTER TABLE [dbo].[Client_Business_View] ADD  CONSTRAINT [DF_Client_Business_View_Created_DTTM]  DEFAULT (getdate()) FOR [Created_DTTM]
GO
ALTER TABLE [dbo].[Client_Business_View] ADD  CONSTRAINT [DF__Client_Bu__Defau__42A7873D]  DEFAULT ((0)) FOR [Default_View]
GO
ALTER TABLE [dbo].[Client_Property_Attribute_Pairing] ADD  CONSTRAINT [DF_Client_Property_Attribute_Pairing_Status_ID]  DEFAULT ((1)) FOR [Status_ID]
GO
ALTER TABLE [dbo].[Client_Property_Attribute_Pairing] ADD  CONSTRAINT [DF_Client_Property_Attribute_Pairing_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Client_User] ADD  CONSTRAINT [DF_Client_User_CreateDate_DTTM]  DEFAULT (getdate()) FOR [CreateDate_DTTM]
GO
ALTER TABLE [dbo].[Condition_Type] ADD  CONSTRAINT [DF_Condition_Type_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Config_Parameter] ADD  CONSTRAINT [DF_Config_Parameter2_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Config_Parameter] ADD  DEFAULT ((1)) FOR [Config_Parameter_Type_ID]
GO
ALTER TABLE [dbo].[Config_Parameter] ADD  CONSTRAINT [DF_Config_Parameter_Group_ID]  DEFAULT ((1)) FOR [Group_ID]
GO
ALTER TABLE [dbo].[Config_Parameter_Category] ADD  CONSTRAINT [DF_Param_Categories_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Config_Parameter_Category] ADD  CONSTRAINT [DF_Parameter_Categories_ModifiedDate]  DEFAULT (getdate()) FOR [ModifiedDate]
GO
ALTER TABLE [dbo].[Config_Parameter_Group] ADD  CONSTRAINT [DF_Param_Groups_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Config_Parameter_Group] ADD  CONSTRAINT [DF_Parameter_Groups_ModifiedDate]  DEFAULT (getdate()) FOR [ModifiedDate]
GO
ALTER TABLE [dbo].[Config_Parameter_Predefined_Value] ADD  CONSTRAINT [DF_Config_Parameter_Predefined_Value_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Config_Parameter_Predefined_Value] ADD  DEFAULT (NULL) FOR [Display_Name]
GO
ALTER TABLE [dbo].[Config_Parameter_Predefined_Value_Type] ADD  CONSTRAINT [DF_Config_Parameter_Predefined_Value_Type_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Config_Parameter_Type] ADD  CONSTRAINT [DF_Param_Types_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Config_Parameter_Type] ADD  CONSTRAINT [DF_Parameter_Type_ModifiedDate]  DEFAULT (getdate()) FOR [ModifiedDate]
GO
ALTER TABLE [dbo].[Config_Parameter_Value] ADD  DEFAULT ((1)) FOR [Created_By_User_ID]
GO
ALTER TABLE [dbo].[Config_Parameter_Value] ADD  DEFAULT (getdate()) FOR [Created_DTTM]
GO
ALTER TABLE [dbo].[Config_Parameter_Value] ADD  DEFAULT ((1)) FOR [Last_Updated_By_User_ID]
GO
ALTER TABLE [dbo].[Config_Parameter_Value] ADD  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
GO
ALTER TABLE [dbo].[Config_Parameter_Value_Meaning] ADD  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Conjunction_Type] ADD  CONSTRAINT [DF_Conjunction_Type_CreateDate_DTTM]  DEFAULT (getdate()) FOR [CreateDate_DTTM]
GO
ALTER TABLE [dbo].[Corp_Business_View] ADD  CONSTRAINT [DF_Corp_Business_View_Status_ID]  DEFAULT ((1)) FOR [Status_ID]
GO
ALTER TABLE [dbo].[Corp_Business_View] ADD  CONSTRAINT [DF_Corp_Business_View_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
GO
ALTER TABLE [dbo].[DBLoc] ADD  CONSTRAINT [DF_DatabaseLocation_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[DBLoc_History] ADD  CONSTRAINT [DF_DBLoc_History_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[DBType] ADD  CONSTRAINT [DF_DBTypeID_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[FAQ] ADD  CONSTRAINT [DF_FAQ_Created_DTTM]  DEFAULT (getdate()) FOR [Created_DTTM]
GO
ALTER TABLE [dbo].[FAQ] ADD  CONSTRAINT [DF_FAQ_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
GO
ALTER TABLE [dbo].[Input_Processing] ADD  CONSTRAINT [DF_Constraint]  DEFAULT ('NOT_RECEIVED') FOR [Extract_Status]
GO
ALTER TABLE [dbo].[Login_Attempts] ADD  CONSTRAINT [DF_Login_Attempts_Fails]  DEFAULT ((0)) FOR [Failed_Attempts]
GO
ALTER TABLE [dbo].[Login_Attempts] ADD  CONSTRAINT [DF_Login_Attempts_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Login_Attempts] ADD  CONSTRAINT [DF_Login_Attempts_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
GO
ALTER TABLE [dbo].[Login_Attempts] ADD  CONSTRAINT [DF_Login_Attempts_Version]  DEFAULT ((0)) FOR [Version]
GO
ALTER TABLE [dbo].[PMS_Migration_Config] ADD  DEFAULT ((0)) FOR [Is_Backup_Created]
GO
ALTER TABLE [dbo].[PMS_Migration_Config_AUD] ADD  DEFAULT ((0)) FOR [Is_Backup_Created]
GO
ALTER TABLE [dbo].[Property] ADD  CONSTRAINT [DF_Property_StatusID]  DEFAULT ((1)) FOR [Status_ID]
GO
ALTER TABLE [dbo].[Property] ADD  CONSTRAINT [DF_Property_CreateDate]  DEFAULT (getdate()) FOR [Created_DTTM]
GO
ALTER TABLE [dbo].[Property] ADD  DEFAULT ((0)) FOR [DBLoc_ID]
GO
ALTER TABLE [dbo].[Property] ADD  CONSTRAINT [DF_Property_Last_Purged_Date]  DEFAULT (N'1970-01-01') FOR [Last_Purged_Date]
GO
ALTER TABLE [dbo].[Property] ADD  CONSTRAINT [DF_Property_Created_By_User_ID]  DEFAULT ((1)) FOR [Created_By_User_ID]
GO
ALTER TABLE [dbo].[Property] ADD  CONSTRAINT [DF_Property_Last_Updated_By_User_ID]  DEFAULT ((1)) FOR [Last_Updated_By_User_ID]
GO
ALTER TABLE [dbo].[Property] ADD  CONSTRAINT [DF_Property_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
GO
ALTER TABLE [dbo].[Property] ADD  DEFAULT ((0)) FOR [Read_Only_Override]
GO
ALTER TABLE [dbo].[Property] ADD  CONSTRAINT [DEFAULT_VALUE]  DEFAULT (CONVERT([date],getdate())) FOR [MonitorProcess_Next_Run_Date]
GO
ALTER TABLE [dbo].[Property] ADD  DEFAULT ((0)) FOR [Reservation_Data_Version]
GO
ALTER TABLE [dbo].[Property] ADD  DEFAULT ('COMPLETED') FOR [Questionnaire_Status]
GO
ALTER TABLE [dbo].[Property] ADD  CONSTRAINT [DF_Property_Last_SAS_Purged_Date]  DEFAULT (N'1970-01-01') FOR [Last_SAS_Purged_Date]
GO
ALTER TABLE [dbo].[Property_AUD] ADD  DEFAULT (NULL) FOR [Last_Purged_Date]
GO
ALTER TABLE [dbo].[Property_AUD] ADD  DEFAULT (NULL) FOR [Last_SAS_Purged_Date]
GO
ALTER TABLE [dbo].[Property_Daily_Processing] ADD  DEFAULT ((0)) FOR [Completed_RSS_Count]
GO
ALTER TABLE [dbo].[Property_Daily_Processing] ADD  DEFAULT ('') FOR [Property_Name]
GO
ALTER TABLE [dbo].[Property_Daily_Processing] ADD  DEFAULT ('DEFAULT_CLIENT') FOR [Client_Name]
GO
ALTER TABLE [dbo].[Property_Daily_Processing] ADD  DEFAULT ((0)) FOR [Completed_Function_Space_Count]
GO
ALTER TABLE [dbo].[Property_Daily_Processing] ADD  DEFAULT ((0)) FOR [Completed_Scheduled_Reports_Count]
GO
ALTER TABLE [dbo].[Property_Group] ADD  CONSTRAINT [DF_Property_Group_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Property_Group] ADD  CONSTRAINT [DF__Property___User___036C17C8]  DEFAULT ((1)) FOR [User_ID]
GO
ALTER TABLE [dbo].[Property_Group] ADD  CONSTRAINT [DF__Property___Defau__04603C01]  DEFAULT ((0)) FOR [Default_Group]
GO
ALTER TABLE [dbo].[Property_Property_Group] ADD  CONSTRAINT [DF_Property_Property_Group_Status_ID]  DEFAULT ((1)) FOR [Status_ID]
GO
ALTER TABLE [dbo].[Property_Property_Group] ADD  CONSTRAINT [DF_Property_Property_Group_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Regulator_Control_Record] ADD  CONSTRAINT [DF_Regulator_Constraint_Create_Date]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Regulator_Request] ADD  CONSTRAINT [DF_RegulatorRequest_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Regulator_Request] ADD  DEFAULT ((0)) FOR [Ignore_In_Throttler]
GO
ALTER TABLE [dbo].[Related_Config_Parameter] ADD  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Remote_Agent] ADD  DEFAULT ('OPMS') FOR [Agent_Type]
GO
ALTER TABLE [dbo].[Remote_Agent] ADD  DEFAULT ((0)) FOR [Suspended]
GO
ALTER TABLE [dbo].[ROA_Report] ADD  DEFAULT ((1)) FOR [Created_by_User_ID]
GO
ALTER TABLE [dbo].[ROA_Report] ADD  DEFAULT (getdate()) FOR [Created_DTTM]
GO
ALTER TABLE [dbo].[ROA_Report] ADD  DEFAULT ((1)) FOR [Last_Updated_by_User_ID]
GO
ALTER TABLE [dbo].[ROA_Report] ADD  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
GO
ALTER TABLE [dbo].[ROA_Report_Category] ADD  DEFAULT ((1)) FOR [Created_by_User_ID]
GO
ALTER TABLE [dbo].[ROA_Report_Category] ADD  DEFAULT (getdate()) FOR [Created_DTTM]
GO
ALTER TABLE [dbo].[ROA_Report_Category] ADD  DEFAULT ((1)) FOR [Last_Updated_by_User_ID]
GO
ALTER TABLE [dbo].[ROA_Report_Category] ADD  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
GO
ALTER TABLE [dbo].[ROA_Report_Request] ADD  DEFAULT ((1)) FOR [Created_by_User_ID]
GO
ALTER TABLE [dbo].[ROA_Report_Request] ADD  DEFAULT (getdate()) FOR [Created_DTTM]
GO
ALTER TABLE [dbo].[ROA_Report_Request] ADD  DEFAULT ((1)) FOR [Last_Updated_by_User_ID]
GO
ALTER TABLE [dbo].[ROA_Report_Request] ADD  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
GO
ALTER TABLE [dbo].[ROA_Report_Request] ADD  DEFAULT ((-1)) FOR [Decision_ID]
GO
ALTER TABLE [dbo].[Roles] ADD  DEFAULT ((0)) FOR [CorporateUser]
GO
ALTER TABLE [dbo].[Roles] ADD  DEFAULT ((0)) FOR [InternalUser]
GO
ALTER TABLE [dbo].[Roles] ADD  CONSTRAINT [DF_ViewAnnoucements]  DEFAULT ((1)) FOR [View_Announcements]
GO
ALTER TABLE [dbo].[Roles] ADD  CONSTRAINT [DF_Roles_Created_By_User_ID]  DEFAULT (NULL) FOR [Created_By_User_ID]
GO
ALTER TABLE [dbo].[Roles] ADD  CONSTRAINT [DF_Roles_Created_DTTM]  DEFAULT (NULL) FOR [Created_DTTM]
GO
ALTER TABLE [dbo].[Roles] ADD  CONSTRAINT [DF_Roles_Last_Updated_By_User_ID]  DEFAULT (NULL) FOR [Last_Updated_By_User_ID]
GO
ALTER TABLE [dbo].[Roles] ADD  CONSTRAINT [DF_Roles_Last_Updated_DTTM]  DEFAULT (NULL) FOR [Last_Updated_DTTM]
GO
ALTER TABLE [dbo].[Roles_AUD] ADD  DEFAULT ((0)) FOR [CorporateUser]
GO
ALTER TABLE [dbo].[Roles_AUD] ADD  DEFAULT ((0)) FOR [InternalUser]
GO
ALTER TABLE [dbo].[Roles_AUD] ADD  DEFAULT ((1)) FOR [View_Announcements]
GO
ALTER TABLE [dbo].[Rule_Attribute_Value] ADD  CONSTRAINT [DF_Rule_Attribute_Value_Ranking]  DEFAULT ((0)) FOR [Ranking]
GO
ALTER TABLE [dbo].[Rule_Attribute_Value] ADD  CONSTRAINT [DF_Rule_Attribute_Value_CreateDate_DTTM]  DEFAULT (getdate()) FOR [CreateDate_DTTM]
GO
ALTER TABLE [dbo].[Rules] ADD  CONSTRAINT [DF_Rule_CreateDate_DTTM]  DEFAULT (getdate()) FOR [CreateDate_DTTM]
GO
ALTER TABLE [dbo].[SAS_File_Loc] ADD  CONSTRAINT [DF_SAS_File_Loc_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[SAS_File_Loc] ADD  CONSTRAINT [DF_SAS_File_Loc_SAS_Server_Name]  DEFAULT (N'localhost') FOR [SAS_Server_Name]
GO
ALTER TABLE [dbo].[SAS_File_Loc] ADD  CONSTRAINT [DF_SAS_Ratchet_File_Location_Path]  DEFAULT (N'sas\data\ratchet\') FOR [SAS_Ratchet_File_Location_Path]
GO
ALTER TABLE [dbo].[SAS_File_Loc_History] ADD  CONSTRAINT [DF_SAS_File_Loc_History_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Status] ADD  CONSTRAINT [DF_Status_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[System_Announcement] ADD  CONSTRAINT [DF_System_Announcement_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[User_Auth_Group_Role] ADD  CONSTRAINT [DF_User_Auth_Group_Role_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[User_Auth_Group_Role_AUD] ADD  CONSTRAINT [DF_User_Auth_Group_Role_AUD_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[User_Feedback] ADD  DEFAULT (getdate()) FOR [create_date]
GO
ALTER TABLE [dbo].[User_Individual_Property_Role] ADD  CONSTRAINT [DF_User_Individual_Property_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[User_Individual_Property_Role_AUD] ADD  CONSTRAINT [DF_User_Individual_Property_Role_AUD_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
GO
ALTER TABLE [dbo].[Users] ADD  CONSTRAINT [DF_User_Created_DTTM]  DEFAULT (getdate()) FOR [Created_DTTM]
GO
ALTER TABLE [dbo].[Users] ADD  CONSTRAINT [DF_Users_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
GO
ALTER TABLE [dbo].[Users] ADD  DEFAULT ((0)) FOR [Salesforce_Access]
GO
ALTER TABLE [dbo].[Users] ADD  DEFAULT ((5)) FOR [REMAINING_FAILED_ATTEMPTS_ALLOWED]
GO
ALTER TABLE [dbo].[Users] ADD  DEFAULT (getdate()) FOR [Last_Password_Change_DTTM]
GO
ALTER TABLE [dbo].[Users] ADD  DEFAULT ((0)) FOR [Password_Never_Expire]
GO
ALTER TABLE [dbo].[Users] ADD  DEFAULT ('DUMMY') FOR [Password]
GO
ALTER TABLE [dbo].[Users] ADD  DEFAULT ((0)) FOR [Learning_Access]
GO
ALTER TABLE [dbo].[Users_AUD] ADD  DEFAULT ((0)) FOR [Learning_Access]
GO
ALTER TABLE [dbo].[Agent_Property]  WITH CHECK ADD  CONSTRAINT [Property_ID_FK] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])
GO
ALTER TABLE [dbo].[Agent_Property] CHECK CONSTRAINT [Property_ID_FK]
GO
ALTER TABLE [dbo].[Agent_Property]  WITH CHECK ADD  CONSTRAINT [Remote_Agent_ID_FK] FOREIGN KEY([Remote_Agent_ID])
REFERENCES [dbo].[Remote_Agent] ([Remote_Agent_ID])
GO
ALTER TABLE [dbo].[Agent_Property] CHECK CONSTRAINT [Remote_Agent_ID_FK]
GO
ALTER TABLE [dbo].[Announcement_Client]  WITH CHECK ADD  CONSTRAINT [FK_Announcement_Client_Announcement_id] FOREIGN KEY([Announcement_id])
REFERENCES [dbo].[System_Announcement] ([System_Announcement_ID])
GO
ALTER TABLE [dbo].[Announcement_Client] CHECK CONSTRAINT [FK_Announcement_Client_Announcement_id]
GO
ALTER TABLE [dbo].[Announcement_Client]  WITH CHECK ADD  CONSTRAINT [FK_Announcement_Client_Client_id] FOREIGN KEY([Client_id])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[Announcement_Client] CHECK CONSTRAINT [FK_Announcement_Client_Client_id]
GO
ALTER TABLE [dbo].[Announcement_User]  WITH CHECK ADD  CONSTRAINT [FK_Announcement_User_Announcement_id] FOREIGN KEY([Announcement_id])
REFERENCES [dbo].[System_Announcement] ([System_Announcement_ID])
GO
ALTER TABLE [dbo].[Announcement_User] CHECK CONSTRAINT [FK_Announcement_User_Announcement_id]
GO
ALTER TABLE [dbo].[Announcement_User]  WITH CHECK ADD  CONSTRAINT [FK_Announcement_User_User_id] FOREIGN KEY([User_id])
REFERENCES [dbo].[Users] ([User_ID])
GO
ALTER TABLE [dbo].[Announcement_User] CHECK CONSTRAINT [FK_Announcement_User_User_id]
GO
ALTER TABLE [dbo].[Auth_Group]  WITH CHECK ADD  CONSTRAINT [FK_Auth_Group_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[Auth_Group] CHECK CONSTRAINT [FK_Auth_Group_Status]
GO
ALTER TABLE [dbo].[Auth_Group_Property]  WITH CHECK ADD  CONSTRAINT [FK_Auth_Group_Property_Auth_Group] FOREIGN KEY([Auth_Group_ID])
REFERENCES [dbo].[Auth_Group] ([Auth_Group_ID])
GO
ALTER TABLE [dbo].[Auth_Group_Property] CHECK CONSTRAINT [FK_Auth_Group_Property_Auth_Group]
GO
ALTER TABLE [dbo].[Auth_Group_Property]  WITH CHECK ADD  CONSTRAINT [FK_Auth_Group_Property_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])
GO
ALTER TABLE [dbo].[Auth_Group_Property] CHECK CONSTRAINT [FK_Auth_Group_Property_Property]
GO
ALTER TABLE [dbo].[Auth_Group_Property]  WITH CHECK ADD  CONSTRAINT [FK_Auth_Group_Property_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[Auth_Group_Property] CHECK CONSTRAINT [FK_Auth_Group_Property_Status]
GO
ALTER TABLE [dbo].[Client]  WITH CHECK ADD  CONSTRAINT [FK_Client_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[Client] CHECK CONSTRAINT [FK_Client_Status]
GO
ALTER TABLE [dbo].[Client_Attribute]  WITH CHECK ADD  CONSTRAINT [FK_Client_Attribute_Attribute_Display_Type] FOREIGN KEY([Attribute_Display_Type_ID])
REFERENCES [dbo].[Attribute_Display_Type] ([Attribute_Display_Type_ID])
GO
ALTER TABLE [dbo].[Client_Attribute] CHECK CONSTRAINT [FK_Client_Attribute_Attribute_Display_Type]
GO
ALTER TABLE [dbo].[Client_Attribute]  WITH CHECK ADD  CONSTRAINT [FK_CLIENT_ATTRIBUTE_CLIENT] FOREIGN KEY([Client_ID])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[Client_Attribute] CHECK CONSTRAINT [FK_CLIENT_ATTRIBUTE_CLIENT]
GO
ALTER TABLE [dbo].[Client_Attribute_Value]  WITH CHECK ADD  CONSTRAINT [FK_Client_Attribute_Value_Client_Attribute] FOREIGN KEY([Client_Attribute_ID])
REFERENCES [dbo].[Client_Attribute] ([Client_Attribute_ID])
GO
ALTER TABLE [dbo].[Client_Attribute_Value] CHECK CONSTRAINT [FK_Client_Attribute_Value_Client_Attribute]
GO
ALTER TABLE [dbo].[Client_Attribute_Value]  WITH CHECK ADD  CONSTRAINT [FK_Client_Attribute_Value_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[Client_Attribute_Value] CHECK CONSTRAINT [FK_Client_Attribute_Value_Status]
GO
ALTER TABLE [dbo].[Client_Business_Group]  WITH CHECK ADD  CONSTRAINT [FK_Client_Business_Group_Cient_Business_View] FOREIGN KEY([Client_Business_View_ID])
REFERENCES [dbo].[Client_Business_View] ([Client_Business_View_ID])
GO
ALTER TABLE [dbo].[Client_Business_Group] CHECK CONSTRAINT [FK_Client_Business_Group_Cient_Business_View]
GO
ALTER TABLE [dbo].[Client_Business_Group]  WITH CHECK ADD  CONSTRAINT [FK_Client_Business_Group_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[Client_Business_Group] CHECK CONSTRAINT [FK_Client_Business_Group_Status]
GO
ALTER TABLE [dbo].[Client_Business_Group_Mkt_Seg_Code]  WITH CHECK ADD  CONSTRAINT [FK_Client_Business_Group_Mkt_Seg_Code_Client_Business_Group] FOREIGN KEY([Client_Business_Group_ID])
REFERENCES [dbo].[Client_Business_Group] ([Client_Business_Group_ID])
GO
ALTER TABLE [dbo].[Client_Business_Group_Mkt_Seg_Code] CHECK CONSTRAINT [FK_Client_Business_Group_Mkt_Seg_Code_Client_Business_Group]
GO
ALTER TABLE [dbo].[Client_Business_View]  WITH CHECK ADD  CONSTRAINT [FK_CLIENT_BUSINESS_VIEW_CLIENT] FOREIGN KEY([Client_ID])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[Client_Business_View] CHECK CONSTRAINT [FK_CLIENT_BUSINESS_VIEW_CLIENT]
GO
ALTER TABLE [dbo].[Client_Business_View]  WITH CHECK ADD  CONSTRAINT [FK_Client_Business_View_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[Client_Business_View] CHECK CONSTRAINT [FK_Client_Business_View_Status]
GO
ALTER TABLE [dbo].[Client_Property_Attribute_Pairing]  WITH CHECK ADD  CONSTRAINT [FK_Client_Property_Attribute_Pairing_Client_Attribute_Value] FOREIGN KEY([Client_Attribute_Value_ID])
REFERENCES [dbo].[Client_Attribute_Value] ([Client_Attribute_Value_ID])
GO
ALTER TABLE [dbo].[Client_Property_Attribute_Pairing] CHECK CONSTRAINT [FK_Client_Property_Attribute_Pairing_Client_Attribute_Value]
GO
ALTER TABLE [dbo].[Client_Property_Attribute_Pairing]  WITH CHECK ADD  CONSTRAINT [FK_Client_Property_Attribute_Pairing_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])
GO
ALTER TABLE [dbo].[Client_Property_Attribute_Pairing] CHECK CONSTRAINT [FK_Client_Property_Attribute_Pairing_Property]
GO
ALTER TABLE [dbo].[Client_Property_Attribute_Pairing]  WITH CHECK ADD  CONSTRAINT [FK_Client_Property_Attribute_Pairing_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[Client_Property_Attribute_Pairing] CHECK CONSTRAINT [FK_Client_Property_Attribute_Pairing_Status]
GO
ALTER TABLE [dbo].[Client_User]  WITH CHECK ADD  CONSTRAINT [FK_Client_User_Client] FOREIGN KEY([Client_ID])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[Client_User] CHECK CONSTRAINT [FK_Client_User_Client]
GO
ALTER TABLE [dbo].[Client_User]  WITH CHECK ADD  CONSTRAINT [FK_Client_User_Users] FOREIGN KEY([User_ID])
REFERENCES [dbo].[Users] ([User_ID])
GO
ALTER TABLE [dbo].[Client_User] CHECK CONSTRAINT [FK_Client_User_Users]
GO
ALTER TABLE [dbo].[Config_Parameter]  WITH CHECK ADD  CONSTRAINT [FK_Config_Parameter_Group] FOREIGN KEY([Group_ID])
REFERENCES [dbo].[Config_Parameter_Group] ([Group_ID])
GO
ALTER TABLE [dbo].[Config_Parameter] CHECK CONSTRAINT [FK_Config_Parameter_Group]
GO
ALTER TABLE [dbo].[Config_Parameter]  WITH CHECK ADD  CONSTRAINT [FK_Config_Parameter_Parameter_Type] FOREIGN KEY([Config_Parameter_Type_ID])
REFERENCES [dbo].[Config_Parameter_Type] ([Param_Type_ID])
GO
ALTER TABLE [dbo].[Config_Parameter] CHECK CONSTRAINT [FK_Config_Parameter_Parameter_Type]
GO
ALTER TABLE [dbo].[Config_Parameter]  WITH CHECK ADD  CONSTRAINT [FK_Config_Parameter_Predefined_Value_Type] FOREIGN KEY([Config_Parameter_Predefined_Value_Type_ID])
REFERENCES [dbo].[Config_Parameter_Predefined_Value_Type] ([Config_Parameter_Predefined_Value_Type_ID])
GO
ALTER TABLE [dbo].[Config_Parameter] CHECK CONSTRAINT [FK_Config_Parameter_Predefined_Value_Type]
GO
ALTER TABLE [dbo].[Config_Parameter_Group]  WITH CHECK ADD  CONSTRAINT [FK_Config_Parameter_Category] FOREIGN KEY([Category_ID])
REFERENCES [dbo].[Config_Parameter_Category] ([Category_ID])
GO
ALTER TABLE [dbo].[Config_Parameter_Group] CHECK CONSTRAINT [FK_Config_Parameter_Category]
GO
ALTER TABLE [dbo].[Config_Parameter_Predefined_Value]  WITH CHECK ADD  CONSTRAINT [FK_Config_Parameter_Predefined_Value_Value_Type] FOREIGN KEY([Config_Parameter_Predefined_Value_Type_ID])
REFERENCES [dbo].[Config_Parameter_Predefined_Value_Type] ([Config_Parameter_Predefined_Value_Type_ID])
GO
ALTER TABLE [dbo].[Config_Parameter_Predefined_Value] CHECK CONSTRAINT [FK_Config_Parameter_Predefined_Value_Value_Type]
GO
ALTER TABLE [dbo].[Config_Parameter_Value]  WITH CHECK ADD  CONSTRAINT [FK_Config_Parameter_Value_Config_Parameter] FOREIGN KEY([Config_Parameter_ID])
REFERENCES [dbo].[Config_Parameter] ([Config_Parameter_ID])
GO
ALTER TABLE [dbo].[Config_Parameter_Value] CHECK CONSTRAINT [FK_Config_Parameter_Value_Config_Parameter]
GO
ALTER TABLE [dbo].[Config_Parameter_Value]  WITH CHECK ADD  CONSTRAINT [FK_Config_Parameter_Value_Config_Parameter_Predefined_Value] FOREIGN KEY([Config_Parameter_Predefined_Value_ID])
REFERENCES [dbo].[Config_Parameter_Predefined_Value] ([Config_Parameter_Predefined_Value_ID])
GO
ALTER TABLE [dbo].[Config_Parameter_Value] CHECK CONSTRAINT [FK_Config_Parameter_Value_Config_Parameter_Predefined_Value]
GO
ALTER TABLE [dbo].[Config_Parameter_Value_AUD]  WITH CHECK ADD  CONSTRAINT [FK_Config_Parameter_Value_AUD_Tetris_Revision_Entity] FOREIGN KEY([REV])
REFERENCES [dbo].[TetrisRevisionEntity] ([id])
GO
ALTER TABLE [dbo].[Config_Parameter_Value_AUD] CHECK CONSTRAINT [FK_Config_Parameter_Value_AUD_Tetris_Revision_Entity]
GO
ALTER TABLE [dbo].[Config_Parameter_Value_Meaning]  WITH CHECK ADD  CONSTRAINT [FK_Config_Parameter_Predefined_Value_ID_1] FOREIGN KEY([Config_Parameter_Predefined_Value_ID])
REFERENCES [dbo].[Config_Parameter_Predefined_Value] ([Config_Parameter_Predefined_Value_ID])
GO
ALTER TABLE [dbo].[Config_Parameter_Value_Meaning] CHECK CONSTRAINT [FK_Config_Parameter_Predefined_Value_ID_1]
GO
ALTER TABLE [dbo].[Config_Parameter_Value_Meaning]  WITH CHECK ADD  CONSTRAINT [FK_Config_Parameter_Value_Meaning_Config_Parameter_1] FOREIGN KEY([Config_Param_ID])
REFERENCES [dbo].[Config_Parameter] ([Config_Parameter_ID])
GO
ALTER TABLE [dbo].[Config_Parameter_Value_Meaning] CHECK CONSTRAINT [FK_Config_Parameter_Value_Meaning_Config_Parameter_1]
GO
ALTER TABLE [dbo].[Corp_Business_Group]  WITH CHECK ADD  CONSTRAINT [FK_Corp_Business_Group_Cient_Business_View] FOREIGN KEY([Corp_Business_View_ID])
REFERENCES [dbo].[Corp_Business_View] ([Corp_Business_View_ID])
GO
ALTER TABLE [dbo].[Corp_Business_Group] CHECK CONSTRAINT [FK_Corp_Business_Group_Cient_Business_View]
GO
ALTER TABLE [dbo].[Corp_Business_Group]  WITH CHECK ADD  CONSTRAINT [FK_Corp_Business_Group_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[Corp_Business_Group] CHECK CONSTRAINT [FK_Corp_Business_Group_Status]
GO
ALTER TABLE [dbo].[Corp_Business_Group_Mkt_Seg_Code]  WITH CHECK ADD  CONSTRAINT [FK_Corp_Business_Group_Mkt_Seg_Code_Corp_Business_Group] FOREIGN KEY([Corp_Business_Group_ID])
REFERENCES [dbo].[Corp_Business_Group] ([Corp_Business_Group_ID])
GO
ALTER TABLE [dbo].[Corp_Business_Group_Mkt_Seg_Code] CHECK CONSTRAINT [FK_Corp_Business_Group_Mkt_Seg_Code_Corp_Business_Group]
GO
ALTER TABLE [dbo].[Corp_Business_View]  WITH CHECK ADD  CONSTRAINT [FK_Corp_BUSINESS_VIEW_CLIENT] FOREIGN KEY([Client_ID])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[Corp_Business_View] CHECK CONSTRAINT [FK_Corp_BUSINESS_VIEW_CLIENT]
GO
ALTER TABLE [dbo].[Corp_Business_View]  WITH CHECK ADD  CONSTRAINT [FK_Corp_Business_View_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[Corp_Business_View] CHECK CONSTRAINT [FK_Corp_Business_View_Status]
GO
ALTER TABLE [dbo].[DBLoc]  WITH CHECK ADD  CONSTRAINT [FK_DBLoc_DBType] FOREIGN KEY([DBType_ID])
REFERENCES [dbo].[DBType] ([DBType_ID])
GO
ALTER TABLE [dbo].[DBLoc] CHECK CONSTRAINT [FK_DBLoc_DBType]
GO
ALTER TABLE [dbo].[DBLoc]  WITH CHECK ADD  CONSTRAINT [FK_DBLoc_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[DBLoc] CHECK CONSTRAINT [FK_DBLoc_Status]
GO
ALTER TABLE [dbo].[DBType]  WITH CHECK ADD  CONSTRAINT [FK_DBType_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[DBType] CHECK CONSTRAINT [FK_DBType_Status]
GO
ALTER TABLE [dbo].[Decision_Delivery]  WITH CHECK ADD  CONSTRAINT [Input_Processing_ID_FK] FOREIGN KEY([Input_Processing_ID])
REFERENCES [dbo].[Input_Processing] ([Input_Processing_ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[Decision_Delivery] CHECK CONSTRAINT [Input_Processing_ID_FK]
GO
ALTER TABLE [dbo].[Decision_Delivery_By_Type]  WITH CHECK ADD  CONSTRAINT [Decision_Delivery_ID_FK] FOREIGN KEY([Decision_Delivery_ID])
REFERENCES [dbo].[Decision_Delivery] ([Decision_Delivery_ID])
GO
ALTER TABLE [dbo].[Decision_Delivery_By_Type] CHECK CONSTRAINT [Decision_Delivery_ID_FK]
GO
ALTER TABLE [dbo].[Dialog_Request]  WITH CHECK ADD  CONSTRAINT [Dialog_Session_ID_FK] FOREIGN KEY([Dialog_Session_ID])
REFERENCES [dbo].[Dialog_Session] ([Dialog_Session_ID])
GO
ALTER TABLE [dbo].[Dialog_Request] CHECK CONSTRAINT [Dialog_Session_ID_FK]
GO
ALTER TABLE [dbo].[FAQ]  WITH CHECK ADD  CONSTRAINT [FK_Module_ID] FOREIGN KEY([Module_id])
REFERENCES [dbo].[Module] ([Id])
GO
ALTER TABLE [dbo].[FAQ] CHECK CONSTRAINT [FK_Module_ID]
GO
ALTER TABLE [dbo].[Force_Full_Decisions]  WITH CHECK ADD  CONSTRAINT [FK_Force_Full_Decisions_Client] FOREIGN KEY([Client_ID])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[Force_Full_Decisions] CHECK CONSTRAINT [FK_Force_Full_Decisions_Client]
GO
ALTER TABLE [dbo].[Force_Full_Decisions]  WITH CHECK ADD  CONSTRAINT [FK_Force_Full_Decisions_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])
GO
ALTER TABLE [dbo].[Force_Full_Decisions] CHECK CONSTRAINT [FK_Force_Full_Decisions_Property]
GO
ALTER TABLE [dbo].[Force_Full_Decisions_AUD]  WITH CHECK ADD  CONSTRAINT [FK_Force_Full_Decisions_AUD_Tetris_Revision_Entity] FOREIGN KEY([REV])
REFERENCES [dbo].[TetrisRevisionEntity] ([id])
GO
ALTER TABLE [dbo].[Force_Full_Decisions_AUD] CHECK CONSTRAINT [FK_Force_Full_Decisions_AUD_Tetris_Revision_Entity]
GO
ALTER TABLE [dbo].[Grp_Evl_Multi]  WITH CHECK ADD  CONSTRAINT [FK_Grp_Evl_Multi_Client] FOREIGN KEY([Client_ID])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[Grp_Evl_Multi] CHECK CONSTRAINT [FK_Grp_Evl_Multi_Client]
GO
ALTER TABLE [dbo].[Grp_Evl_Multi]  WITH CHECK ADD  CONSTRAINT [FK_Grp_Evl_Multi_Property_Group] FOREIGN KEY([Property_Group_ID])
REFERENCES [dbo].[Property_Group] ([Property_Group_ID])
GO
ALTER TABLE [dbo].[Grp_Evl_Multi] CHECK CONSTRAINT [FK_Grp_Evl_Multi_Property_Group]
GO
ALTER TABLE [dbo].[Grp_Evl_Multi_AUD]  WITH CHECK ADD  CONSTRAINT [FK_Grp_Evl_Multi_AUD_Tetris_Revision_Entity] FOREIGN KEY([REV])
REFERENCES [dbo].[TetrisRevisionEntity] ([id])
GO
ALTER TABLE [dbo].[Grp_Evl_Multi_AUD] CHECK CONSTRAINT [FK_Grp_Evl_Multi_AUD_Tetris_Revision_Entity]
GO
ALTER TABLE [dbo].[Grp_Evl_Multi_Property]  WITH CHECK ADD  CONSTRAINT [FK_Grp_Evl_Multi_Property_Grp_Evl_Multi] FOREIGN KEY([Grp_Evl_Multi_ID])
REFERENCES [dbo].[Grp_Evl_Multi] ([Grp_Evl_Multi_ID])
GO
ALTER TABLE [dbo].[Grp_Evl_Multi_Property] CHECK CONSTRAINT [FK_Grp_Evl_Multi_Property_Grp_Evl_Multi]
GO
ALTER TABLE [dbo].[Grp_Evl_Multi_Property]  WITH CHECK ADD  CONSTRAINT [FK_Grp_Evl_Multi_Property_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])
GO
ALTER TABLE [dbo].[Grp_Evl_Multi_Property] CHECK CONSTRAINT [FK_Grp_Evl_Multi_Property_Property]
GO
ALTER TABLE [dbo].[Grp_Evl_Multi_Property_AUD]  WITH CHECK ADD  CONSTRAINT [FK_Grp_Evl_Multi_Property_AUD_Tetris_Revision_Entity] FOREIGN KEY([REV])
REFERENCES [dbo].[TetrisRevisionEntity] ([id])
GO
ALTER TABLE [dbo].[Grp_Evl_Multi_Property_AUD] CHECK CONSTRAINT [FK_Grp_Evl_Multi_Property_AUD_Tetris_Revision_Entity]
GO
ALTER TABLE [dbo].[Input_Processing]  WITH CHECK ADD  CONSTRAINT [Property_Daily_Processing_ID_FK] FOREIGN KEY([Property_Daily_Processing_ID])
REFERENCES [dbo].[Property_Daily_Processing] ([Property_Daily_Processing_ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[Input_Processing] CHECK CONSTRAINT [Property_Daily_Processing_ID_FK]
GO
ALTER TABLE [dbo].[Input_Processing_Job]  WITH CHECK ADD  CONSTRAINT [Input_Processing_ID_Job_FK] FOREIGN KEY([Input_Processing_ID])
REFERENCES [dbo].[Input_Processing] ([Input_Processing_ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[Input_Processing_Job] CHECK CONSTRAINT [Input_Processing_ID_Job_FK]
GO
ALTER TABLE [dbo].[INPUT_PROCESSING_NOTE]  WITH CHECK ADD  CONSTRAINT [Input_Processing_ID_Note_FK] FOREIGN KEY([INPUT_PROCESSING_ID])
REFERENCES [dbo].[Input_Processing] ([Input_Processing_ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[INPUT_PROCESSING_NOTE] CHECK CONSTRAINT [Input_Processing_ID_Note_FK]
GO
ALTER TABLE [dbo].[LDB_Generic_Booking_Curve]  WITH CHECK ADD  CONSTRAINT [LDB_Hotel_Profile_ID_FK] FOREIGN KEY([LDB_Hotel_Profile_ID])
REFERENCES [dbo].[LDB_Hotel_Profile] ([LDB_Hotel_Profile_ID])
GO
ALTER TABLE [dbo].[LDB_Generic_Booking_Curve] CHECK CONSTRAINT [LDB_Hotel_Profile_ID_FK]
GO
ALTER TABLE [dbo].[LDB_Generic_Booking_Curve_Point]  WITH CHECK ADD  CONSTRAINT [LDB_Generic_Booking_Curve_ID_FK] FOREIGN KEY([LDB_Generic_Booking_Curve_ID])
REFERENCES [dbo].[LDB_Generic_Booking_Curve] ([LDB_Generic_Booking_Curve_ID])
GO
ALTER TABLE [dbo].[LDB_Generic_Booking_Curve_Point] CHECK CONSTRAINT [LDB_Generic_Booking_Curve_ID_FK]
GO
ALTER TABLE [dbo].[Login_Attempts]  WITH CHECK ADD  CONSTRAINT [FK_Login_User] FOREIGN KEY([User_ID])
REFERENCES [dbo].[Users] ([User_ID])
GO
ALTER TABLE [dbo].[Login_Attempts] CHECK CONSTRAINT [FK_Login_User]
GO
ALTER TABLE [dbo].[PMS_Migration_Config]  WITH CHECK ADD  CONSTRAINT [FK_PMS_Migration_Config_Client] FOREIGN KEY([Client_ID])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[PMS_Migration_Config] CHECK CONSTRAINT [FK_PMS_Migration_Config_Client]
GO
ALTER TABLE [dbo].[PMS_Migration_Config]  WITH CHECK ADD  CONSTRAINT [FK_PMS_Migration_Config_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])
GO
ALTER TABLE [dbo].[PMS_Migration_Config] CHECK CONSTRAINT [FK_PMS_Migration_Config_Property]
GO
ALTER TABLE [dbo].[PMS_Migration_Config_AUD]  WITH CHECK ADD  CONSTRAINT [FK_PMS_Migration_Config_AUD_Tetris_Revision_Entity] FOREIGN KEY([REV])
REFERENCES [dbo].[TetrisRevisionEntity] ([id])
GO
ALTER TABLE [dbo].[PMS_Migration_Config_AUD] CHECK CONSTRAINT [FK_PMS_Migration_Config_AUD_Tetris_Revision_Entity]
GO
ALTER TABLE [dbo].[Property]  WITH CHECK ADD  CONSTRAINT [FK_Property_Client] FOREIGN KEY([Client_ID])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[Property] CHECK CONSTRAINT [FK_Property_Client]
GO
ALTER TABLE [dbo].[Property]  WITH CHECK ADD  CONSTRAINT [FK_Property_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[Property] CHECK CONSTRAINT [FK_Property_Status]
GO
ALTER TABLE [dbo].[Property_AUD]  WITH CHECK ADD  CONSTRAINT [FK_Property_AUD_Tetris_Revision_Entity] FOREIGN KEY([REV])
REFERENCES [dbo].[TetrisRevisionEntity] ([id])
GO
ALTER TABLE [dbo].[Property_AUD] CHECK CONSTRAINT [FK_Property_AUD_Tetris_Revision_Entity]
GO
ALTER TABLE [dbo].[Property_Group]  WITH CHECK ADD  CONSTRAINT [FK_PROPERTY_GROUP_AND_USERS] FOREIGN KEY([User_ID])
REFERENCES [dbo].[Users] ([User_ID])
GO
ALTER TABLE [dbo].[Property_Group] CHECK CONSTRAINT [FK_PROPERTY_GROUP_AND_USERS]
GO
ALTER TABLE [dbo].[Property_Group]  WITH CHECK ADD  CONSTRAINT [FK_PROPERTY_GROUP_CLIENT] FOREIGN KEY([Client_ID])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[Property_Group] CHECK CONSTRAINT [FK_PROPERTY_GROUP_CLIENT]
GO
ALTER TABLE [dbo].[Property_Group]  WITH CHECK ADD  CONSTRAINT [FK_Property_Group_Rules] FOREIGN KEY([Rule_ID])
REFERENCES [dbo].[Rules] ([Rule_ID])
GO
ALTER TABLE [dbo].[Property_Group] CHECK CONSTRAINT [FK_Property_Group_Rules]
GO
ALTER TABLE [dbo].[Property_Property_Group]  WITH CHECK ADD  CONSTRAINT [FK_Property_Property_Group_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])
GO
ALTER TABLE [dbo].[Property_Property_Group] CHECK CONSTRAINT [FK_Property_Property_Group_Property]
GO
ALTER TABLE [dbo].[Property_Property_Group]  WITH CHECK ADD  CONSTRAINT [FK_Property_Property_Group_Property_Group] FOREIGN KEY([Property_Group_ID])
REFERENCES [dbo].[Property_Group] ([Property_Group_ID])
GO
ALTER TABLE [dbo].[Property_Property_Group] CHECK CONSTRAINT [FK_Property_Property_Group_Property_Group]
GO
ALTER TABLE [dbo].[Property_Property_Group]  WITH CHECK ADD  CONSTRAINT [FK_Property_Property_Group_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[Property_Property_Group] CHECK CONSTRAINT [FK_Property_Property_Group_Status]
GO
ALTER TABLE [dbo].[Regulator_Request]  WITH CHECK ADD  CONSTRAINT [FK_RegulatorRequest_Regulator_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Regulator_Status] ([Status_ID])
GO
ALTER TABLE [dbo].[Regulator_Request] CHECK CONSTRAINT [FK_RegulatorRequest_Regulator_Status]
GO
ALTER TABLE [dbo].[Related_Config_Parameter]  WITH CHECK ADD  CONSTRAINT [FK_Related_Config_Parameter_Config_Parameter_1] FOREIGN KEY([Config_Param_ID])
REFERENCES [dbo].[Config_Parameter] ([Config_Parameter_ID])
GO
ALTER TABLE [dbo].[Related_Config_Parameter] CHECK CONSTRAINT [FK_Related_Config_Parameter_Config_Parameter_1]
GO
ALTER TABLE [dbo].[Related_Config_Parameter]  WITH CHECK ADD  CONSTRAINT [FK_Related_Config_Parameter_Related_Config_Parameter_2] FOREIGN KEY([Related_Config_Param_ID])
REFERENCES [dbo].[Config_Parameter] ([Config_Parameter_ID])
GO
ALTER TABLE [dbo].[Related_Config_Parameter] CHECK CONSTRAINT [FK_Related_Config_Parameter_Related_Config_Parameter_2]
GO
ALTER TABLE [dbo].[Remote_Agent]  WITH CHECK ADD  CONSTRAINT [Client_ID_FK] FOREIGN KEY([Client_ID])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[Remote_Agent] CHECK CONSTRAINT [Client_ID_FK]
GO
ALTER TABLE [dbo].[Remote_Task]  WITH CHECK ADD  CONSTRAINT [RT_Remote_Agent_ID_FK] FOREIGN KEY([Remote_Agent_ID])
REFERENCES [dbo].[Remote_Agent] ([Remote_Agent_ID])
GO
ALTER TABLE [dbo].[Remote_Task] CHECK CONSTRAINT [RT_Remote_Agent_ID_FK]
GO
ALTER TABLE [dbo].[ROA_Report]  WITH CHECK ADD FOREIGN KEY([ROA_Report_Category_ID])
REFERENCES [dbo].[ROA_Report_Category] ([ROA_Report_Category_ID])
GO
ALTER TABLE [dbo].[Rule_Attribute_Value]  WITH CHECK ADD  CONSTRAINT [FK_Rule_Attribute_Value_Client_Attribute_Value] FOREIGN KEY([Client_Attribute_Value_ID])
REFERENCES [dbo].[Client_Attribute_Value] ([Client_Attribute_Value_ID])
GO
ALTER TABLE [dbo].[Rule_Attribute_Value] CHECK CONSTRAINT [FK_Rule_Attribute_Value_Client_Attribute_Value]
GO
ALTER TABLE [dbo].[Rule_Attribute_Value]  WITH CHECK ADD  CONSTRAINT [FK_Rule_Attribute_Value_Condition_Type] FOREIGN KEY([Condition_Type_ID])
REFERENCES [dbo].[Condition_Type] ([Condition_Type_ID])
GO
ALTER TABLE [dbo].[Rule_Attribute_Value] CHECK CONSTRAINT [FK_Rule_Attribute_Value_Condition_Type]
GO
ALTER TABLE [dbo].[Rule_Attribute_Value]  WITH CHECK ADD  CONSTRAINT [FK_Rule_Attribute_Value_Conjunction_Type] FOREIGN KEY([Conjuction_Type_ID])
REFERENCES [dbo].[Conjunction_Type] ([Conjunction_Type_ID])
GO
ALTER TABLE [dbo].[Rule_Attribute_Value] CHECK CONSTRAINT [FK_Rule_Attribute_Value_Conjunction_Type]
GO
ALTER TABLE [dbo].[Rule_Attribute_Value]  WITH CHECK ADD  CONSTRAINT [FK_Rule_Attribute_Value_Rules] FOREIGN KEY([Rule_ID])
REFERENCES [dbo].[Rules] ([Rule_ID])
GO
ALTER TABLE [dbo].[Rule_Attribute_Value] CHECK CONSTRAINT [FK_Rule_Attribute_Value_Rules]
GO
ALTER TABLE [dbo].[Rules]  WITH CHECK ADD  CONSTRAINT [FK_RULES_CLIENT] FOREIGN KEY([Client_ID])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[Rules] CHECK CONSTRAINT [FK_RULES_CLIENT]
GO
ALTER TABLE [dbo].[SAS_File_Loc]  WITH CHECK ADD  CONSTRAINT [FK_SAS_File_Loc_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])
GO
ALTER TABLE [dbo].[SAS_File_Loc] CHECK CONSTRAINT [FK_SAS_File_Loc_Property]
GO
ALTER TABLE [dbo].[SAS_File_Loc]  WITH CHECK ADD  CONSTRAINT [FK_SAS_File_Loc_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[SAS_File_Loc] CHECK CONSTRAINT [FK_SAS_File_Loc_Status]
GO
ALTER TABLE [dbo].[Scheduled_Report]  WITH CHECK ADD  CONSTRAINT [FK_Scheduled_Report_Client] FOREIGN KEY([Client_ID])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[Scheduled_Report] CHECK CONSTRAINT [FK_Scheduled_Report_Client]
GO
ALTER TABLE [dbo].[Scheduled_Report]  WITH CHECK ADD  CONSTRAINT [FK_Scheduled_Report_Owner] FOREIGN KEY([Owner_ID])
REFERENCES [dbo].[Users] ([User_ID])
GO
ALTER TABLE [dbo].[Scheduled_Report] CHECK CONSTRAINT [FK_Scheduled_Report_Owner]
GO
ALTER TABLE [dbo].[Scheduled_Report]  WITH CHECK ADD  CONSTRAINT [FK_Scheduled_Report_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])
GO
ALTER TABLE [dbo].[Scheduled_Report] CHECK CONSTRAINT [FK_Scheduled_Report_Property]
GO
ALTER TABLE [dbo].[Scheduled_Report_AUD]  WITH CHECK ADD  CONSTRAINT [FK_Scheduled_Report_AUD_Tetris_Revision_Entity] FOREIGN KEY([REV])
REFERENCES [dbo].[TetrisRevisionEntity] ([id])
GO
ALTER TABLE [dbo].[Scheduled_Report_AUD] CHECK CONSTRAINT [FK_Scheduled_Report_AUD_Tetris_Revision_Entity]
GO
ALTER TABLE [dbo].[Scheduled_Report_Recipient]  WITH CHECK ADD  CONSTRAINT [FK_Scheduled_Report_Recipient_Scheduled_Report] FOREIGN KEY([Scheduled_Report_ID])
REFERENCES [dbo].[Scheduled_Report] ([Scheduled_Report_ID])
GO
ALTER TABLE [dbo].[Scheduled_Report_Recipient] CHECK CONSTRAINT [FK_Scheduled_Report_Recipient_Scheduled_Report]
GO
ALTER TABLE [dbo].[Scheduled_Report_Recipient]  WITH CHECK ADD  CONSTRAINT [FK_Scheduled_Report_Recipient_User] FOREIGN KEY([User_ID])
REFERENCES [dbo].[Users] ([User_ID])
GO
ALTER TABLE [dbo].[Scheduled_Report_Recipient] CHECK CONSTRAINT [FK_Scheduled_Report_Recipient_User]
GO
ALTER TABLE [dbo].[Scheduled_Report_Recipient_AUD]  WITH CHECK ADD  CONSTRAINT [FK_Scheduled_Report_Recipient_AUD_Tetris_Revision_Entity] FOREIGN KEY([REV])
REFERENCES [dbo].[TetrisRevisionEntity] ([id])
GO
ALTER TABLE [dbo].[Scheduled_Report_Recipient_AUD] CHECK CONSTRAINT [FK_Scheduled_Report_Recipient_AUD_Tetris_Revision_Entity]
GO
ALTER TABLE [dbo].[System_Announcement]  WITH CHECK ADD  CONSTRAINT [FK_System_Announcement_Modified_Users] FOREIGN KEY([Modified_By])
REFERENCES [dbo].[Users] ([User_ID])
GO
ALTER TABLE [dbo].[System_Announcement] CHECK CONSTRAINT [FK_System_Announcement_Modified_Users]
GO
ALTER TABLE [dbo].[System_Announcement]  WITH CHECK ADD  CONSTRAINT [FK_System_Announcement_Users] FOREIGN KEY([Created_by_User_ID])
REFERENCES [dbo].[Users] ([User_ID])
GO
ALTER TABLE [dbo].[System_Announcement] CHECK CONSTRAINT [FK_System_Announcement_Users]
GO
ALTER TABLE [dbo].[System_Usage]  WITH CHECK ADD  CONSTRAINT [FK_System_Usage_Page] FOREIGN KEY([Page_ID])
REFERENCES [dbo].[System_Usage_Page] ([System_Usage_Page_ID])
GO
ALTER TABLE [dbo].[System_Usage] CHECK CONSTRAINT [FK_System_Usage_Page]
GO
ALTER TABLE [dbo].[System_Usage]  WITH CHECK ADD  CONSTRAINT [FK_System_Usage_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])
GO
ALTER TABLE [dbo].[System_Usage] CHECK CONSTRAINT [FK_System_Usage_Property]
GO
ALTER TABLE [dbo].[System_Usage]  WITH CHECK ADD  CONSTRAINT [FK_System_Usage_Property_Group] FOREIGN KEY([Property_Group_ID])
REFERENCES [dbo].[Property_Group] ([Property_Group_ID])
GO
ALTER TABLE [dbo].[System_Usage] CHECK CONSTRAINT [FK_System_Usage_Property_Group]
GO
ALTER TABLE [dbo].[System_Usage]  WITH CHECK ADD  CONSTRAINT [FK_System_Usage_Type] FOREIGN KEY([Type_ID])
REFERENCES [dbo].[System_Usage_Type] ([System_Usage_Type_ID])
GO
ALTER TABLE [dbo].[System_Usage] CHECK CONSTRAINT [FK_System_Usage_Type]
GO
ALTER TABLE [dbo].[System_Usage]  WITH CHECK ADD  CONSTRAINT [FK_System_Usage_User] FOREIGN KEY([User_ID])
REFERENCES [dbo].[Users] ([User_ID])
GO
ALTER TABLE [dbo].[System_Usage] CHECK CONSTRAINT [FK_System_Usage_User]
GO
ALTER TABLE [dbo].[Task_Parameter]  WITH CHECK ADD  CONSTRAINT [Remote_Task_ID_FK] FOREIGN KEY([Remote_Task_ID])
REFERENCES [dbo].[Remote_Task] ([Remote_Task_ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[Task_Parameter] CHECK CONSTRAINT [Remote_Task_ID_FK]
GO
ALTER TABLE [dbo].[Two_Factor_Authentication]  WITH CHECK ADD  CONSTRAINT [FK_Two_Factor_Authentication_User] FOREIGN KEY([User_ID])
REFERENCES [dbo].[Users] ([User_ID])
GO
ALTER TABLE [dbo].[Two_Factor_Authentication] CHECK CONSTRAINT [FK_Two_Factor_Authentication_User]
GO
ALTER TABLE [dbo].[User_Auth_Group_Role_AUD]  WITH CHECK ADD  CONSTRAINT [FK_User_Auth_Group_Role_AUD_Tetris_Revision_Entity] FOREIGN KEY([REV])
REFERENCES [dbo].[TetrisRevisionEntity] ([id])
GO
ALTER TABLE [dbo].[User_Auth_Group_Role_AUD] CHECK CONSTRAINT [FK_User_Auth_Group_Role_AUD_Tetris_Revision_Entity]
GO
ALTER TABLE [dbo].[User_Feedback]  WITH NOCHECK ADD  CONSTRAINT [FK_User_Feedback_Client] FOREIGN KEY([Client_ID])
REFERENCES [dbo].[Client] ([Client_ID])
GO
ALTER TABLE [dbo].[User_Feedback] CHECK CONSTRAINT [FK_User_Feedback_Client]
GO
ALTER TABLE [dbo].[User_Feedback]  WITH NOCHECK ADD  CONSTRAINT [FK_User_Feedback_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])
GO
ALTER TABLE [dbo].[User_Feedback] CHECK CONSTRAINT [FK_User_Feedback_Property]
GO
ALTER TABLE [dbo].[User_Feedback]  WITH NOCHECK ADD  CONSTRAINT [FK_User_Feedback_User] FOREIGN KEY([User_ID])
REFERENCES [dbo].[Users] ([User_ID])
GO
ALTER TABLE [dbo].[User_Feedback] CHECK CONSTRAINT [FK_User_Feedback_User]
GO
ALTER TABLE [dbo].[User_Individual_Property_Role_AUD]  WITH CHECK ADD  CONSTRAINT [FK_User_Individual_Property_Role_AUD_Tetris_Revision_Entity] FOREIGN KEY([REV])
REFERENCES [dbo].[TetrisRevisionEntity] ([id])
GO
ALTER TABLE [dbo].[User_Individual_Property_Role_AUD] CHECK CONSTRAINT [FK_User_Individual_Property_Role_AUD_Tetris_Revision_Entity]
GO
ALTER TABLE [dbo].[Users]  WITH CHECK ADD  CONSTRAINT [FK_Users_Status] FOREIGN KEY([Status_ID])
REFERENCES [dbo].[Status] ([Status_ID])
GO
ALTER TABLE [dbo].[Users] CHECK CONSTRAINT [FK_Users_Status]
GO
ALTER TABLE [dbo].[Users_AUD]  WITH CHECK ADD  CONSTRAINT [FK_Users_AUD_Tetris_Revision_Entity] FOREIGN KEY([REV])
REFERENCES [dbo].[TetrisRevisionEntity] ([id])
GO
ALTER TABLE [dbo].[Users_AUD] CHECK CONSTRAINT [FK_Users_AUD_Tetris_Revision_Entity]
GO
ALTER TABLE [dbo].[System_Announcement]  WITH CHECK ADD  CONSTRAINT [System_Announcement_Status] CHECK  (([Status]='ACTIVE' OR [Status]='INACTIVE'))
GO
ALTER TABLE [dbo].[System_Announcement] CHECK CONSTRAINT [System_Announcement_Status]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


create procedure [dbo].[getAnalyticsPropertyConfigMacro] @PropertyId int ,
@attributeCursor cursor varying output
AS
begin
    DECLARE @pacmanLevel nvarchar(6)='pacman' , @clientLevel nvarchar(56), @propertyLevel nvarchar(106)
		DECLARE  @ClientCodeValue nvarchar(50), @PropertyCodeValue nvarchar(50)
		DECLARE @clientid int
		SELECT @PropertyCodeValue= Property_Code , @Clientid=client_id from Property where Property_id=@PropertyId
		SELECT @ClientCodeValue = Client_Code from Client where client_id = @Clientid
		SET @clientLevel = @pacmanLevel+'.'+@ClientCodeValue
		SET @propertyLevel = @pacmanLevel+'.'+@ClientCodeValue+'.'+@PropertyCodeValue

		set nocount on
      begin
        set @attributeCursor=CURSOR forward_only static for
        SELECT
      Property_ID,
      CASE
        WHEN Name LIKE 'pacman.analytics.ipconfig.%' THEN STUFF(Name, 1, LEN('pacman.analytics.ipconfig.'),'')
        WHEN Name LIKE 'pacman.feature.%' THEN STUFF(Name, 1, LEN('pacman.feature.'),'')
        ELSE Name
      END AS Name,
      CASE
        WHEN CONTEXT = @pacmanLevel THEN 'Global'
        WHEN CONTEXT = @clientLevel THEN 'Client'
        ELSE 'Property'
      END AS LEVEL,
      CASE
        WHEN Name LIKE 'pacman.feature.%' AND Param_Type_ID = 2 AND UPPER(Value) = 'TRUE' THEN '1'
        WHEN Name LIKE 'pacman.feature.%' AND Param_Type_ID = 2 AND UPPER(Value) = 'FALSE' THEN '0'
        WHEN FixedValue IS NULL OR FixedValue = '' THEN VALUE
        ELSE FixedValue
      END AS Param_value,
      Param_Type_ID,
      Param_Type
    FROM (
      SELECT
        Name,
        Context,
        FixedValue,
        cppv.Value,
        cpt.Param_Type_ID,
        cpt.Param_Type,
        RANK() OVER (PARTITION BY CPV.Config_Parameter_ID, Name ORDER BY CPV.Config_Parameter_ID, name, len(context) DESC) AS POS
      FROM [dbo].[Config_Parameter_Value] cpv with (nolock)
      INNER JOIN [dbo].[Config_Parameter] cp with (nolock) ON cpv.Config_Parameter_ID = cp.Config_Parameter_ID
      INNER join [dbo].[Config_Parameter_Type] cpt with (nolock) ON cp.Config_Parameter_Type_ID = cpt.Param_Type_ID
      INNER JOIN [dbo].[Config_Parameter_Group] cpg with (nolock) ON CP.group_ID = cpg.group_ID
      INNER JOIN [dbo].[Config_Parameter_Category] cpc with (nolock) ON cpg.Category_ID = CPC.Category_ID
      LEFT JOIN [dbo].[Config_Parameter_Predefined_Value] cppv with (nolock) ON cppv.Config_Parameter_Predefined_Value_ID = cpv.Config_Parameter_Predefined_Value_ID
      WHERE CPC.Category_Name = 'Analytics'
      AND context IN (@pacmanLevel, @clientLevel, @propertyLevel)
    ) allLevels
    CROSS JOIN CLIENT c with (nolock)
    CROSS JOIN PROPERTY p with (nolock)
    WHERE allLevels.POS = 1
    AND c.Client_ID = p.Client_ID
    AND p.Property_ID = @PropertyId
  END
	open @attributeCursor

END

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[getAnalyticsPropertyConfigValue] @PropertyId int
AS
BEGIN
	DECLARE @pacmanLevel nvarchar(6)='pacman', @clientLevel nvarchar(56), @propertyLevel nvarchar(106)
	DECLARE  @ClientCodeValue nvarchar(50), @PropertyCodeValue nvarchar(50)
	DECLARE @clientid int
	SELECT @PropertyCodeValue= Property_Code, @Clientid=Client_ID FROM Property WHERE Property_ID = @PropertyId
	SELECT @ClientCodeValue = Client_Code FROM Client WHERE Client_ID = @Clientid
	SET @clientLevel = @pacmanLevel + '.' + @ClientCodeValue
	SET @propertyLevel = @pacmanLevel + '.' + @ClientCodeValue + '.' + @PropertyCodeValue

	SELECT
		Property_ID,
		CASE
			WHEN Name LIKE 'pacman.analytics.ipconfig.%' THEN STUFF(Name, 1, LEN('pacman.analytics.ipconfig.'),'')
			WHEN Name LIKE 'pacman.feature.%' THEN STUFF(Name, 1, LEN('pacman.feature.'),'')
			ELSE Name
		END AS Name,
		CASE
			WHEN CONTEXT = @pacmanLevel THEN 'Global'
			WHEN CONTEXT = @clientLevel THEN 'Client'
			ELSE 'Property'
		END AS LEVEL,
		CASE
	    WHEN Name LIKE 'pacman.feature.%' AND Param_Type_ID = 2 AND UPPER(Value) = 'TRUE' THEN '1'
			WHEN Name LIKE 'pacman.feature.%' AND Param_Type_ID = 2 AND UPPER(Value) = 'FALSE' THEN '0'
			WHEN FixedValue IS NULL OR FixedValue = '' THEN VALUE
			ELSE FixedValue
		END AS Param_value,
		Param_Type_ID,
		Param_Type
	FROM (
		SELECT
			Name,
			Context,
			FixedValue,
			cppv.Value,
			cpt.Param_Type_ID,
			cpt.Param_Type,
			RANK() OVER (PARTITION BY CPV.Config_Parameter_ID, Name ORDER BY CPV.Config_Parameter_ID, name, len(context) DESC) AS POS
		FROM [dbo].[Config_Parameter_Value] cpv with (nolock)
		INNER JOIN [dbo].[Config_Parameter] cp with (nolock) ON cpv.Config_Parameter_ID = cp.Config_Parameter_ID
		INNER join [dbo].[Config_Parameter_Type] cpt with (nolock) ON cp.Config_Parameter_Type_ID = cpt.Param_Type_ID
		INNER JOIN [dbo].[Config_Parameter_Group] cpg with (nolock) ON CP.group_ID = cpg.group_ID
		INNER JOIN [dbo].[Config_Parameter_Category] cpc with (nolock) ON cpg.Category_ID = CPC.Category_ID
		LEFT JOIN [dbo].[Config_Parameter_Predefined_Value] cppv with (nolock) ON cppv.Config_Parameter_Predefined_Value_ID = cpv.Config_Parameter_Predefined_Value_ID
		WHERE CPC.Category_Name = 'Analytics'
		AND context IN (@pacmanLevel, @clientLevel, @propertyLevel)
	) allLevels
	CROSS JOIN CLIENT c with (nolock)
	CROSS JOIN PROPERTY p with (nolock)
	WHERE allLevels.POS = 1
	AND c.Client_ID = p.Client_ID
	AND p.Property_ID = @PropertyId
END

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE procedure [dbo].[getFullyQualifiedParameterName] (@parameterName nvarchar(250))
AS
begin 
declare @mycounter int 
set @mycounter=0 
	CREATE TABLE #TempNote (Param_Node_ID INT) 
	CREATE TABLE #TempPath (fully_qualified_parameter_name nvarchar(250) ) 

	insert into #TempNote select Param_Node_ID from Parameter where Param_Name=@parameterName 
	
declare @concatString nvarchar(250) 
declare @currentNodeID int 
declare @parentNodeID int 

	while (select count(*) from #TempNote)>0
	begin
		set @concatString=@parameterName 
		set @parentNodeID=0 
		set @mycounter=@mycounter+1 
		set @currentNodeID = (select  top(1)Param_Node_ID  from #TempNote) 
		set @concatString=(select Node_Name from Parameter_Node where Param_Node_ID=@currentNodeID)+'.'+@concatString 
		
		while(@parentNodeID is not null)
		begin
			set @parentNodeID=(select Parent_Param_Node_ID from Parameter_Node where Param_Node_ID=@currentNodeID)
			if(@parentNodeID is NULL)
				break 
			set @concatString=(select Node_Name from Parameter_Node where Param_Node_ID=@parentNodeID)+'.'+@concatString 
			set @currentNodeID=@parentNodeID 
		end 
		delete top(1) from #TempNote
		insert into #TempPath select @concatString	 
	end 
	
	select * from #TempPath
end 
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE procedure [dbo].[getFullyQualifiedPropertyName](@propertyName nvarchar(250))
AS
begin 
	CREATE TABLE #TempNote (Param_Node_ID INT) 
	CREATE TABLE #TempPath (fully_qualified_property_name nvarchar(250) PRIMARY KEY) 

	insert into #TempNote 
	select Param_Val_Node_ID from Parameter_Value_Node where Node_Name=@propertyName
	
declare @concatString nvarchar(250) 
declare @currentNodeID int 
declare @parentNodeID int 

	while (select count(*) from #TempNote)>0
	begin
		set @concatString=@propertyName 
		set @parentNodeID=0 
		set @currentNodeID = (select  top(1)Param_Node_ID  from #TempNote) 
				
		while(@parentNodeID is not null)
		begin
			set @parentNodeID=(select Parent_Param_Val_Node_ID from Parameter_Value_Node where Param_Val_Node_ID=@currentNodeID)
			if(@parentNodeID is NULL)
				break 
			set @concatString=(select Node_Name from Parameter_Value_Node where Param_Val_Node_ID=@parentNodeID)+'.'+@concatString 
			set @currentNodeID=@parentNodeID 
		end 
		delete top(1) from #TempNote
		insert into #TempPath select @concatString	 
	end 
	
	select * from #TempPath
end 
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


create procedure [dbo].[updateParameterValue] @parameter nvarchar(250),@node nvarchar(250), @value nvarchar(250)
AS
begin 

DECLARE @existingValue nvarchar(250) 
DECLARE @paramValNodeID INT 
DECLARE @paramID INT 
DECLARE @err_message nvarchar(250) 
DECLARE @predefinedValueId int 
DECLARE @predefinedValueTypeId int 

set @existingValue = (dbo.getParameterValue2(@parameter,@node))

if(@existingValue = @value)
	return 

/*  look for predefined match */
set @paramID=dbo.getParameterID2(@parameter) 
set @predefinedValueTypeId=(SELECT [Config_Parameter_Predefined_Value_Type_ID] FROM [dbo].[Config_Parameter] where Config_Parameter_ID = @paramID )
if (@predefinedValueTypeId is not null)
	BEGIN
	/* is prederinfed, try to find match */
	set @predefinedValueId = (SELECT [Config_Parameter_Predefined_Value_ID] FROM [dbo].[Config_Parameter_Predefined_Value] WHERE Value = @value AND Config_Parameter_Predefined_Value_Type_ID = @predefinedValueTypeId)
	if (@predefinedValueId is null)
			 begin
			 	  SET @err_message = 'Invalid Value: Value '+@value+' not part of predefined(2) value list.' 
			 	  RAISERROR (@err_message, 11,1) 
			 	  return 
			 end		
	set @value = null
	END
if (@existingValue is null)
	begin
		INSERT INTO dbo.Config_Parameter_Value ([Config_Parameter_ID], [Context], [FixedValue], [Config_Parameter_Predefined_Value_ID], [Created_DTTM], [Last_Updated_DTTM])
		VALUES (@paramID, @node, @value, @predefinedValueId, getdate(), getdate())
	end
else
	begin
		update dbo.Config_Parameter_Value set FixedValue=@value, [Config_Parameter_Predefined_Value_ID] = @predefinedValueId where [Config_Parameter_ID]=@paramID and Context=@node
	end

end
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE procedure [dbo].[updatePropertySpecificConfigurationParameters] (@propertyNode nvarchar(250), @isMinMaxAllowed nvarchar(250), @isAvailableForArrival nvarchar(250), @crsTimeZone nvarchar(250), @propertyTimeZone nvarchar(250),@webRateAlias nvarchar(250))
AS
begin 
	exec updateParameterValue @parameter='pacman.integration.ratchet.crsTimeZone', @node=@propertyNode, @value=@crsTimeZone     
	exec updateParameterValue @parameter='pacman.integration.ratchet.pastDays', @node=@propertyNode, @value='720'          
	exec updateParameterValue @parameter='pacman.core.propertyTimeZone', @node=@propertyNode, @value=@propertyTimeZone    
	exec updateParameterValue @parameter='pacman.bar.AllowMinMaxLOSOverride',@node=@propertyNode,@value=@isMinMaxAllowed
	exec updateParameterValue @parameter='pacman.bar.AllowAvailableForArrival',@node=@propertyNode,@value=@isAvailableForArrival
	exec updateParameterValue @parameter='pacman.bar.webRateAlias',@node=@propertyNode,@value=@webRateAlias
	exec updateParameterValue @parameter='pacman.integration.ratchet.futureDays', @node=@propertyNode, @value='365'   
	exec updateParameterValue @parameter='pacman.integration.ratchet.processRoomRates',@node=@propertyNode,@value='TRUE'
	exec updateParameterValue @parameter='pacman.integration.ratchet.processRmsRates',@node=@propertyNode,@value='TRUE'
	exec updateParameterValue @parameter='pacman.integration.ratchet.yieldCurrencyCode',@node=@propertyNode,@value='USD'
	exec updateParameterValue @parameter='pacman.integration.ratchet.newRmCostRaw',@node=@propertyNode,@value='TRUE'
	exec updateParameterValue @parameter='pacman.integration.ratchet.useMktOccupancyRates',@node=@propertyNode,@value='FALSE'
	exec updateParameterValue @parameter='pacman.integration.ratchet.applyTax',@node=@propertyNode,@value='FALSE'
	exec updateParameterValue @parameter='pacman.integration.ratchet.useSrpIdSubstitution',@node=@propertyNode,@value='TRUE'

end
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


create procedure [dbo].[usp_add_client_and_property] 
(
	 @dbname nvarchar(50) --> used for holding the database name
	,@dbservername nvarchar(150) --> used for holding the database host name
	,@dbserverinstance nvarchar(150) --> used for holding the database instance name
	,@dbport int --> used for holding the database port number
	,@root_node nvarchar(50) --> root node ('pacman')
	,@client_name nvarchar(50) --> name of the new client being added
	,@property_code nvarchar(50) --> property code associated with the client
	,@property_name nvarchar(150) --> description of the property
	,@property_id int --> used for setting property_ids manually
	,@sas_server_name nvarchar(150) -- used for setting SAS_File_Loc entry
	,@decision_file_path nvarchar(500) --> used for setting the value of global config parameter pacman.integration.ratchet.decisionFilePath
)
as
begin
	-- the following values are used for debug purpose only
	--set @dbname = 'Pacman_Ideas_SandBox'
	--set @root_node = 'pacman'
	--set @client_name = 'SandBox'
	--set @property_code = 'SandBox_Property'
	--set @property_name = 'SandBox property used for demo and testing'
	--set @property_id = 10016
	
	-- local variables
	declare @dbloc_id int --> used for extracting the dbloc entry just added
	declare @client_id int --> used for extracting client_id just got added

	-- extract 'pacman' parameter_value_node
	declare @pacman_node int
	declare @client_node int
	declare @param_id int
	declare @context nvarchar(250)

	IF NOT EXISTS (SELECT 1 FROM [dbo].[Client] where [Client_Name] = @client_name)
	BEGIN

	-- step 1 --> add dbloc entry
	IF NOT EXISTS (SELECT 1 from [dbo].[DBLoc] where dbname = @dbname)
	INSERT INTO [dbo].[DBLoc]
	(
		[DBName]
	   ,[Server_Name]
	   ,[Server_Inst]
	   ,[Port_Number]
	   ,[JNDI_Name]
	   ,[Status_ID]
	   ,[DBType_ID]
	   ,[CreateDate]
	   ,[JNDI_Name_For_Reports] 
	)
	values
	(
		@dbname,
		@dbservername,
		@dbserverinstance,
		@dbport,
		'java:/PacmanSandBoxTS',
		1,
		2,
		getdate(),
		'java:/PacmanSandBoxTS'	
	)

	select @dbloc_id = dbloc_id from dbloc where dbname = @dbname

    set @context = @root_node+ '.' + @client_name 
    
	-- step 2 --> setup the client
	insert into [dbo].[client] ( [client_code], [client_name], [status_id], [createdate]) 
		values (  @client_name, @client_name, 1, getdate() )

	-- extract the client_id
	select @client_id = client_id from dbo.client where client_code = @client_name

	-- step 3 --> select @pacman_node
	select @pacman_node = dbo.getparameternodevalueid(@root_node) --> new client node is defined under this root node.

	-- step 4 --> create the client node
	insert into [dbo].[parameter_value_node] ( [node_name], [parent_param_val_node_id], [createdate]) 
		values (  @client_name, @pacman_node, getdate() )
		
	select @client_node = dbo.getparameternodevalueid(@root_node + '.' + @client_name) --> use the root_node and client_node combinations

	-- step 5 --> add parameter values at the client node	
	select @param_id = dbo.getparameterid('pacman.integration.ratchet.applytax') --> 141
	insert into [dbo].[parameter_value] ([param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values ( @client_node, @param_id, null, N'false', getdate(), getdate() )

	select @param_id = dbo.getparameterid2('pacman.integration.ratchet.applytax')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, null, 1001, @context)
      
	select @param_id = dbo.getparameterid('pacman.integration.ratchet.crstimezone') --> 3
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, 1, null, getdate(), getdate() )

	select @param_id = dbo.getparameterid2('pacman.integration.ratchet.crstimezone')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, null, 113, @context)
      
	select @param_id = dbo.getparameterid('pacman.integration.ratchet.futuredays') --> 1
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, null, N'365', getdate(), getdate() )
		
	select @param_id = dbo.getparameterid2('pacman.integration.ratchet.futuredays')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, N'365', null, @context)
      
	select @param_id = dbo.getparameterid('pacman.integration.ratchet.newrmcostraw') --> 126
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, null, N'true', getdate(), getdate() )
		
	select @param_id = dbo.getparameterid2('pacman.integration.ratchet.newrmcostraw')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, null, 1000, @context)
      
	select @param_id = dbo.getparameterid('pacman.integration.ratchet.pastdays') --> 2
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, null, N'45', getdate(), getdate() )
		
	select @param_id = dbo.getparameterid2('pacman.integration.ratchet.pastdays')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, N'45', null, @context)
      
	select @param_id = dbo.getparameterid('pacman.integration.ratchet.processrmsrates') -->6
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, null, N'true', getdate(), getdate() )
		
	select @param_id = dbo.getparameterid2('pacman.integration.ratchet.processrmsrates')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, null, 1000, @context)
      
	select @param_id = dbo.getparameterid('pacman.integration.ratchet.processroomrates') --> 5
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, null, N'true', getdate(), getdate() )

	select @param_id = dbo.getparameterid2('pacman.integration.ratchet.processroomrates')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, null, 1000, @context)
      
	--insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
	--	values ( 261, @client_node, 4, null, n'america/phoenix', getdate(), getdate() )
	select @param_id = dbo.getparameterid('pacman.integration.ratchet.usemktoccupancyrates') -->137
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, null, N'false', getdate(), getdate() )
		
	select @param_id = dbo.getparameterid2('pacman.integration.ratchet.usemktoccupancyrates')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, null, 1001, @context)
      
	select @param_id = dbo.getparameterid('pacman.integration.ratchet.yieldcurrencycode') --> 7
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, null, N'usd', getdate(), getdate() )

	select @param_id = dbo.getparameterid2('pacman.integration.ratchet.yieldcurrencycode')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, N'usd', null, @context)
      
	select @param_id = dbo.getparameterid('pacman.integration.ratchet.useSrpIdSubstitution') 
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, null, N'true', getdate(), getdate() )

	select @param_id = dbo.getparameterid2('pacman.integration.ratchet.useSrpIdSubstitution')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, null, 1000, @context)
      
	select @param_id = dbo.getparameterid('pacman.integration.ratchet.defaultMarketSegment') 
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, null, N'BAR', getdate(), getdate() )
		
	select @param_id = dbo.getparameterid2('pacman.integration.ratchet.defaultMarketSegment')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, N'BAR', null, @context)
      
	select @param_id = dbo.getparameterid('pacman.integration.ratchet.qualifiedfplos') 
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, 69, null, getdate(), getdate() )

	select @param_id = dbo.getparameterid2('pacman.integration.ratchet.qualifiedfplos')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, null, 69, @context)
      
	select @param_id = dbo.getparameterid('pacman.integration.receivingsystems') 
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, 19, null, getdate(), getdate() )

	select @param_id = dbo.getparameterid2('pacman.integration.receivingsystems')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, null, 69, @context)
      
	select @param_id = dbo.getparameterid('pacman.integration.ratchet.decisionfilepath') 
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, null, @decision_file_path, getdate(), getdate() )
		
	select @param_id = dbo.getparameterid2('pacman.integration.ratchet.decisionfilepath')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, @decision_file_path, null, @context)
      
	select @param_id = dbo.getparameterid('pacman.population.catchupmode') --> 125
	insert into [dbo].[parameter_value] ( [param_val_node_id], [param_id], [predefined_value_id], [value], [createdate], [modifieddate]) 
		values (  @client_node, @param_id, null, N'true',getdate(), getdate() )

	select @param_id = dbo.getparameterid2('pacman.population.catchupmode')
    insert into [dbo].[Config_Parameter_Value] 
	(Config_Parameter_ID, FixedValue, Config_Parameter_Predefined_Value_ID, Context)
      VALUES (@param_id, null, 1000, @context)
      
	END

	IF NOT EXISTS (SELECT 1 FROM [dbo].[Property] where [Property_ID] = @property_id)
	BEGIN
	-- extract the client_id
	select @client_id = client_id from dbo.client where client_code = @client_name

	-- step 6 --> add a property for the client
	-- we will have to hardcode the property id for specific reasons.	
	set identity_insert [dbo].[property] on

	insert into [dbo].[property] ([property_id], [client_id], [property_code], [property_name], [status_id], [Created_DTTM], [dbloc_id]) 
		values ( @property_id, @client_id, @property_code, @property_name, 1, getdate(), @dbloc_id )

	set identity_insert [dbo].[property] off

	-- step 7 --> add sas file location for the property
	insert into [dbo].[sas_file_loc] ( [property_id], [sas_file_location_path], [status_id], [createdate], [sas_server_name]) 
		values (  @property_id, 'sas_data\properties\' + cast(@property_id as varchar(5)), 1, getdate(), @sas_server_name )
	END
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************
Store Procedure Name: usp_add_parameter

Input Parameters :	@param_name --> name of the parameter (e.g. 'processName')
					@param_description --> parameter description (e.g. 'Name of the Business Process')
					@param_level --> parameter level (e.g. 0)
					@param_type --> patameter type (e.g. 1)
					@param_node --> parameter node this parameter is getting added to  (e.g.'platform.eventhandler.pacman.DecisionFileUploadProcess')
					
					Note: Please make sure the parameter_node exists. If it doesn't please add the node using 'usp_add_parameter_node' procedure.
					
Ouput Parameter : NA

Execution: exec dbo.usp_add_parameter  --> this is just an example
			'processName',
			'Name of the Business Process',
			0,
			1,
			'platform.eventhandler.pacman.DecisionFileUploadProcess'
			 	   
Purpose: The purpose of this procedure is to add parameters to a given node (NODE MUST EXIST)
		 
Author: Manohar Sunkum

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
10/27/2011	Manohar				Sunkum					Initial version for adding 
***************************************************************************************/

create procedure [dbo].[usp_add_parameter] 
(
	@param_name nvarchar(50),
	@param_description nvarchar(125),
	@param_level int,
	@param_type int,
	@param_node nvarchar(250),
	@predefinedValueTypeId int
)
as
begin
	declare @param_node_id int
	declare @fqName nvarchar(125)
	
	set @fqName = @param_node + '.' + @param_name 
	
    INSERT INTO [dbo].[Config_Parameter]
               ([Name]
               ,[Description]
               ,[Config_Parameter_Predefined_Value_Type_ID]
               ,[Config_Parameter_Type_ID]
               ,[CreateDate]
               ,[ModifiedDate])
         VALUES
               (@fqName, @param_description, @predefinedValueTypeId, @param_type, getdate(), getdate())		
end
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************
Store Procedure Name: usp_add_parameter_node

Input Parameters : @parent_node -> parent node (e.g.'platform.eventhandler.pacman')
				   @child_node  -> child node (e.g.'DecisionFileUploadProcess') 
					
Ouput Parameter : NA

Execution: exec dbo.usp_add_parameter_node 'platform.eventhandler.pacman','DecisionFileUploadProcess' --> this is just an example
		   exec dbo.usp_add_parameter_node 'parent_only','' --> Adding a parent without a child

Purpose: The purpose of this utility is to add :
		 a) Parent node where there is no child
		 b) Child node to a particular parent
		 
Author: Manohar Sunkum

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
10/27/2011	Manohar				Sunkum					Initial version for adding 
***************************************************************************************/

create procedure [dbo].[usp_add_parameter_node] 
(
	@parent_node nvarchar(250),
	@child_node nvarchar(100)
)
as
begin
	declare @parent_node_id int 

	
	if @child_node = '' -- adding only the parent node
		begin
			insert into dbo.parameter_node ( node_name, parent_param_node_id, createdate) 
			values ( @parent_node,NULL, getdate())
		end
	else
		begin
			-- extract the parent_param_node_id associated with this child
			select @parent_node_id = dbo.ufn_get_parameter_node_id(@parent_node)
			
			-- insert the child node to the corresponding parent_param_node_id extracted above
			insert into dbo.parameter_node ( node_name, parent_param_node_id, createdate) 
			values ( @child_node,@parent_node_id, getdate())
		end
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************
Store Procedure Name: usp_add_parameter_node_value

Input Parameters : @parent_node -> parent node (e.g.'platform.eventhandler.IDG')
				   @child_node  -> child node (e.g.'MORGANS') 
					
Ouput Parameter : NA

Execution: exec dbo.usp_add_parameter_node_value 'platform.eventhandler.IDG','MORGANS' --> this is just an example
		   exec dbo.usp_add_parameter_node_value 'parent_only','' --> Adding a parent without a child

Purpose: The purpose of this utility is to add :
		 a) Parent node value where there is no child
		 b) Child node value to a particular parent node value
		 
Author: Manohar Sunkum

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
10/27/2011	Manohar				Sunkum					Initial version for adding 
***************************************************************************************/

create procedure [dbo].[usp_add_parameter_node_value] 
(
	@parent_node_value nvarchar(250),
	@child_node_value nvarchar(100)
)
as
begin
	declare @parent_node_id int 

	
	if @child_node_value = '' -- adding only the parent node
		begin
			insert into dbo.parameter_value_node ( node_name, parent_param_val_node_id, createdate) 
			values ( @parent_node_value,NULL, getdate())
		end
	else
		begin
			-- extract the parent_param_node_id associated with this child
			select @parent_node_id = dbo.getParameterNodeValueID(@parent_node_value)
			
			-- insert the child node to the corresponding parent_param_node_id extracted above
			insert into dbo.parameter_value_node ( node_name, parent_param_val_node_id, createdate) 
			values ( @child_node_value,@parent_node_id, getdate())
		end
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/*************************************************************************************
Store Procedure Name: usp_add_parameter_value

Input Parameters :	@param_node -> parameter_node (e.g.'platform.eventhandler.pacman.DecisionFileUploadProcess'),
					@parameter -> parameter name (e.g. 'processName')
					@param_value_node -> parameter node value  (e.g.'platform.eventhandler.IDG')
					@parameter_value -> parameter value(e.g. 'UploadDecisionsProcess') 
					@predefined_value -> Refers to any predefined value if exists...otherwise NULL
					
Ouput Parameter : NA

Execution: exec dbo.usp_add_parameter_value  --> this is just an example
			,@parameter = 'platform.eventHandler.pacman.DecisionFileUploadProcess.processName' 
							--> This includes both	parameter_node and the parameter.						
			,@param_value_node = 'platform.eventHandler.IDG'
			,@parameter_value = 'UploadDecisionsProcess'
			,@predefined_value = NULL
			
Purpose: The purpose of this utility is to add parameter values at specified nodes.
		
		 
Author: Manohar Sunkum

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
10/27/2011	Manohar				Sunkum					Initial version for adding 
***************************************************************************************/
create procedure [dbo].[usp_add_parameter_value] 
(
	@parameter nvarchar(250),--> absolute node name & parameter
	@param_value_node nvarchar(250),
	@parameter_value nvarchar(50),
	@predefined_value int
)
as
begin
	declare @parameter_id int

	select @parameter_id = dbo.getParameterID2(@parameter)
	
	if (@parameter_id is not NULL)
		BEGIN
			INSERT INTO dbo.Config_Parameter_Value ([Config_Parameter_ID], [Context], [FixedValue], [Config_Parameter_Predefined_Value_ID], [Created_DTTM], [Last_Updated_DTTM])
			VALUES (@parameter_id, @param_value_node, @parameter_value, @predefined_value, getdate(), getdate())
		END
	else
		print 'No Matching Parameter Found'
		
end
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


create procedure [dbo].[usp_add_property] 
(
		@client_code nvarchar(100),
		@property_code nvarchar(100),
		@property_name nvarchar(300)
)
as

begin
		declare @parent_param_val_node_id int
		declare @client_id int
		
		select @client_id = client_id from client where client_code = @client_code
		select @parent_param_val_node_id = param_val_node_id from dbo.Parameter_Value_Node where node_name = 'Hilton'
		select @property_code = @property_code 
		select @property_name = @property_name 

		-- add a property
		INSERT INTO [dbo].[Property] ([Client_ID], [Property_Code], [Property_Name], [Status_ID], [Created_DTTM]) 
			VALUES (  @client_id, @property_code, @property_name, 1, getdate() )

		--add parameter_value_node for a given property
		INSERT INTO [dbo].[Parameter_Value_Node] ( [Node_Name], [Parent_Param_Val_Node_ID], [CreateDate]) 
			VALUES (  @property_code, @parent_param_val_node_id, getdate() )

		--- add the parameter_value
		INSERT INTO [dbo].[Parameter_Value] ( [Param_Val_Node_ID], [Param_ID], [Predefined_Value_Id], [Value], [CreateDate], [ModifiedDate]) 
			VALUES (  (select max(Param_Val_Node_ID) from [dbo].[Parameter_Value_Node]) , 1, NULL, N'365', '2011-06-09 08:02:00.000', '2011-06-09 08:01:31.553' )
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/*************************************************************************************
Store Procedure Name: usp_add_update_parameter_value

Input Parameters :	@param_node -> parameter_node (e.g.'platform.eventhandler.pacman.DecisionFileUploadProcess'),
					@parameter -> parameter name (e.g. 'processName')
					@param_value_node -> parameter node value  (e.g.'platform.eventhandler.IDG')
					@parameter_value -> parameter value(e.g. 'UploadDecisionsProcess') 
					@predefined_value -> Refers to any predefined value if exists...otherwise NULL
					
Ouput Parameter : NA
Execution: exec dbo.usp_add_update_parameter_value  
			@parameter 'pacman.integration.isCrsAckRequired'
			@param_value_node ,'pacman'
			@parameter_value ,'false'
			@predefined_code ,'boolean';
					
Purpose: The purpose of this utility is to add if not present / update when present parameter values at specified nodes.
Author: Sandeep Ghiya

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
09/11/2014	Sandeep				Ghiya					Initial version for adding 
***************************************************************************************/
create procedure [dbo].[usp_add_update_parameter_value] 
(
	@parameter nvarchar(250),--> absolute node name & parameter
	@param_value_node nvarchar(250),
	@parameter_value nvarchar(50),
	@predefined_code nvarchar(50)
)
as
begin
	declare @parameter_id int

	select @parameter_id = dbo.getParameterID2(@parameter)
	
	if (@parameter_id is not NULL)
	BEGIN
	 if EXISTS(select 1 from [dbo].[Config_Parameter_Value] where 
		[Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name =@parameter)
		and context = @param_value_node)
	  update dbo.Config_Parameter_Value set FixedValue =@parameter_value,Config_Parameter_Predefined_Value_ID=(select top 1 Config_Parameter_Predefined_Value_ID from [dbo].[Config_Parameter_Predefined_Value] where 
				[Config_Parameter_Predefined_Value_Type_ID] = 
				(select top 1 [Config_Parameter_Predefined_Value_Type_ID] 
				 from [dbo].[Config_Parameter_Predefined_Value_Type] 
				 where code = @predefined_code
				) and value = @parameter_value) where Config_Parameter_ID = @parameter_id and context = @param_value_node
	ELSE
		INSERT INTO dbo.Config_Parameter_Value ([Config_Parameter_ID], [Context], [FixedValue], [Config_Parameter_Predefined_Value_ID], [Created_DTTM], [Last_Updated_DTTM])
			VALUES (@parameter_id, @param_value_node, @parameter_value, (select top 1 Config_Parameter_Predefined_Value_ID from [dbo].[Config_Parameter_Predefined_Value] where 
				[Config_Parameter_Predefined_Value_Type_ID] = 
				(select top 1 [Config_Parameter_Predefined_Value_Type_ID] 
				 from [dbo].[Config_Parameter_Predefined_Value_Type] 
				 where code = @predefined_code
				) and value = @parameter_value), getdate(), getdate())
		END
	else
		print 'No Matching Parameter Found'
		
end
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[usp_create_all_permission_role] 
	@role_name nvarchar(max), @client_code nvarchar(50), @CorporateUser int, @internaluser int, @viewannouncement int
AS
BEGIN
if(@client_code = 'SandBox' or @client_code = 'bstn')
BEGIN
INSERT [dbo].[Roles] ([Role_Name], [Description], [CorporateUser], [InternalUser], [Permissions], [Client_Code], [View_Announcements], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM]) VALUES (@role_name, @role_name, @CorporateUser, @internaluser, N'pageCode=information-manager&access=readWrite&functions={alerts:readWrite,alerts-decision-outlier:readWrite,systemHealth:readOnly,exceptions:readWrite,notifications:readWrite,notificationsConfiguration:readWrite}
pageCode=investigator&access=readWrite
pageCode=dashboards&access=readWrite
pageCode=at-a-glance&access=readWrite
pageCode=business-analysis&access=readWrite
pageCode=business-insights&access=readWrite
pageCode=demand-360&access=readWrite
pageCode=reputation-management&access=readWrite
pageCode=reports&access=readWrite
pageCode=arrival-bylos-report&access=readWrite
pageCode=arrival-by-los-remaining-demand-report&access=readWrite
pageCode=booking-pace-report&access=readWrite
pageCode=booking-situation-report&access=readWrite
pageCode=comparative-booking-pace-report&access=readWrite
pageCode=data-extraction-report&access=readWrite
pageCode=forecast-validation-report&access=readWrite
pageCode=input-override-report&access=readWrite
pageCode=inventory-history-report&access=readWrite
pageCode=mcat-mapping-report&access=readWrite
pageCode=operations-report&access=readWrite
pageCode=output-override-report&access=readWrite
pageCode=performance-comparison-report&access=readWrite
pageCode=pick-up-change-and-differential-control-report&access=readWrite
pageCode=pricing-override-history-report&access=readWrite
pageCode=pricing-pace-report&access=readWrite
pageCode=pricing-report&access=readWrite
pageCode=rate-plan-report&access=readWrite
pageCode=property-attribute-assignment-report&access=readWrite
pageCode=rate-plan-production-report&access=readWrite
pageCode=restriction-level-report&access=readWrite
pageCode=schedule-reports&access=readWrite
pageCode=special-events-report&access=readWrite
pageCode=sla-report&access=readWrite
pageCode=user-activity-log-report&access=readWrite
pageCode=forecasts&access=readWrite
pageCode=demand-and-wash-management&access=readWrite&functions={singleDayDemandOverrideOccupancyDate:readWrite,singleDayDemandOverrideArrivallos:readWrite,multiDayDemandOverride:readWrite,demandAndWashMultiDayOverrideRemoval:readWrite,demandAndWashWashOverride:readWrite,demandAndWashWhatIf:readWrite}
pageCode=group-wash&access=readWrite
pageCode=special-events-management&access=readWrite&functions={specialEventsAddNewDelete:readWrite,createNewCategory:readWrite}
pageCode=decisions&access=readWrite
pageCode=cp-pricing-management&access=readWrite&functions={continuousPricingManagementWhatIf:readWrite,continuousPricingManagementManualUpload:readWrite}
pageCode=extended-stay-rate-management&access=readWrite
pageCode=group-pricing-evaluation&access=readWrite
pageCode=overbooking-management&access=readWrite&functions={singleDayOverrideAllRtpropertyValue:readWrite,singleDayOverrideAllRtpropertyLimit:readWrite,singleDayOverrideRemovalAllRtpropertyValueAndLimit:readWrite,singleDayOverrideByRtLimit:readWrite,singleDayOverrideRemovalByRtLimit:readWrite,multiDayOverride:readWrite,overbookingManagementMultiDayOverrideRemoval:readWrite,costOfWalkByRt:readWrite,costOfWalkByRtRemoval:readWrite,overbookingManagementWashOverride:readWrite,overbookingManagementWhatIf:readWrite,ceilingDefaultSettings:readWrite}
pageCode=price-strategy-configuration&access=readWrite&functions={defaultSettingsMinlosMaxlosAvailableForArrivalUserOverrideOnly:readWrite,onArrivalSettings:readWrite,userOverrideOnlySettings:readWrite,closeBarRateSettings:readWrite}
pageCode=pricing&access=readWrite&functions={pricingWhatIf:readWrite,pricingManualUpload:readWrite}
pageCode=pricing-management&access=readWrite&functions={applyeditSingleDayBarOverrideUserAndFloor:readWrite,applyeditSingleDayBarOverrideFloorOnly:readWrite,applyeditMultiDayBarOverrideUserAndFloor:readWrite,applyeditMultiDayBarOverrideFloorOnly:readWrite,removeSingleDayBarOverrideUserAndFloor:readWrite,removeMultiDayBarOverrideUserAndFloor:readWrite,pricingManagementWhatIf:readWrite,pricingManagementManualUpload:readWrite}
pageCode=configure&access=readWrite
pageCode=agile-rates&access=readWrite
pageCode=client-budget&access=readWrite&functions={budgetConfigution:readWrite,budgetDataEntry:readWrite}
pageCode=corporate-business-views&access=readWrite&functions={business-view-corporate-view:readWrite,business-view-property-view:readWrite,business-view-inventory-group:readWrite}
pageCode=channel-costs&access=readWrite
pageCode=component-rooms&access=readWrite
pageCode=decision-configuration&access=readWrite&functions={force-full-decisions:readWrite}
pageCode=es-rate-configuration&access=readWrite
pageCode=fiscal-calendar&access=readWrite
pageCode=group-pricing-configuration&access=readWrite&functions={groupPricingConfigurationGeneralConfiguration:readWrite,groupPricingConfigurationRateConfiguration:readWrite,groupPricingConfigurationConferenceBanquet:readWrite,groupPricingConfigurationAncillary:readWrite,groupPricingMinProfitPercentage:readWrite}
pageCode=group-status-code&access=readWrite
pageCode=client-questionnaire&access=readWrite
pageCode=lra-restrictions&access=readWrite
pageCode=client-limited-data-build&access=readWrite
pageCode=market-segments&access=readWrite&functions={attributeSettings:readWrite,proposedForecastGroups_approveReject:readWrite,createForecastGroups:readWrite,removeForecastGroupsMapping:readWrite,moveMSBetweenFG:readWrite,propertyBusinessViewsManagement:readWrite}
pageCode=mass-bar-upload&access=readWrite
pageCode=mass-restriction-upload&access=readWrite
pageCode=out-of-order-component-rooms&access=readWrite
pageCode=pricing-configuration&access=readWrite&functions={pricingConfigurationGroupCeilingAndFloor:readWrite,pricingConfigurationTransientCeilingAndFloor:readWrite}
pageCode=property-attribute-assignments&access=readWrite
pageCode=property-attributes&access=readWrite
pageCode=property-groups&access=readWrite
pageCode=property-specific-configuration&access=readWrite&functions={propertySpecificConfigPropertyInformation:readOnly,propertySpecificConfigOptimizationSettings:readOnly,propertySpecificConfigCloseLV0Settings:readOnly}
pageCode=rate-plan-configuration&access=readWrite&functions={rate-plan-setup:readWrite,ratePlanDefaultSettingsMinlosMaxlosAvailableForArrivalUserOverrideOnly:readWrite,ratePlanOnArrivalSettings:readWrite,ratePlanUserOverrideOnlySettings:readWrite,ratePlanCloseBarRateSettings:readWrite}
pageCode=rate-shopping-configuration&access=readWrite&functions={roomClassMappingCompetetiveRoomTypeToRoomClassMapping:readWrite,rateShoppingDataChannelDisplay:readWrite,competitiveMarketReferenceChannelDefaultChannelSetting:readWrite,competitiveMarketReferenceChannelOverrideDefaultChannel:readWrite,propertySpecificConfigCompetitorDisplaySettings:readWrite,rateShoppingDataCompetitorDisplayAndConfiguration:readWrite,roomClassMappingCompetetiveMarketPositionConstraint:readWrite,competitorDataFilterIgnoreCompetitorSettings:readWrite,rate-adjustment:readWrite,rateShoppingScheduleConfigureRateShoppingSchedule:readWrite,reputationCompetitorMappingToWebrateCompetitor:readWrite,vendor-change-mapping:readWrite}
pageCode=qualified-rate-plan-configuration&access=readWrite
pageCode=daily-bar-config&access=readWrite
pageCode=rooms-configuration&access=readWrite&functions={roomsConfiguration:readWrite,costOfWalkSettings:readWrite}
pageCode=vendor-integration-mapping&access=readWrite
pageCode=servicing-cost-by-los-configuration&access=readWrite
pageCode=authorizations&access=readWrite
pageCode=user-management&access=readWrite
pageCode=function-space&access=readWrite
pageCode=function-space-configuration&access=readWrite&functions={functionSpaceConfigurationGeneralConfiguration:readWrite,functionSpaceConfigurationRateConfiguration:readWrite,functionSpaceConfigurationConferenceBanquet:readWrite,functionSpaceConfigurationAncillary:readWrite,functionSpaceConfigurationDayParts:readWrite,functionSpaceConfigurationFunctionRooms:readWrite,functionSpaceConfigurationForecastLevels:readWrite,functionSpaceConfigurationEventTypes:readWrite,functionSpaceConfigurationMarketSegment:readWrite,functionSpaceConfigurationGuestRoomType:readWrite,functionSpaceConfigurationMinProfitPercentage:readWrite}
pageCode=function-space-demand-calendar&access=readWrite
pageCode=function-space-evaluation&access=readWrite
pageCode=function-space-forecast-review&access=readWrite
pageCode=function-space-performance-trends&access=readWrite
pageCode=support&access=readWrite
pageCode=installation-status&access=readWrite
pageCode=reset-property&access=readWrite
pageCode=nsight&access=readWrite
pageCode=gemini&access=readWrite
pageCode=gemini-dashboard&access=readWrite
pageCode=multiproperty-booking-pace-report&access=readWrite
pageCode=multiproperty-rate-plan-report&access=readWrite',@client_code, @viewannouncement, 11403, CAST(N'2018-11-21T16:48:52.047' AS DateTime), 11403, CAST(N'2018-11-21T16:48:52.047' AS DateTime))
END
END

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/*************************************************************************************
Store Procedure Name: usp_create_seed_global_users

Ouput Parameter : NA
	 	   
Purpose: Creates seed users for sandbox client. This can be used to create seed users for other clients as well. 
Currently this supports to create seed user with only one role for all properties this needs to be modified to support 
property-role as parameter.
		 
Author: Archana Mundaye

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
07/12/2013  	Archana				Mundaye					Seed users
***************************************************************************************/
CREATE procedure [dbo].[usp_create_seed_global_users] 
(
	  @ClientID int
	 ,@UserID bigint
	 ,@UserName nvarchar(50)
	 ,@UserEmail nvarchar(150)
	 ,@clientCode nvarchar(50)
	 ,@internalUser smallint
	 ,@firstName nvarchar(150)
	 ,@lastName nvarchar(150)
	 ,@roleID nvarchar(200)
	 ,@allProperties int
	 ,@roleRO nvarchar(200)
	 ,@roleRW nvarchar(200)
)
as
begin
	set identity_insert [dbo].[Users] on
	
	delete from User_Individual_Property_Role where USER_ID = @UserID
	delete from User_Auth_Group_Role where USER_ID = @UserID
	delete from Client_User where USER_ID = @UserID
	delete from System_Usage where USER_ID = @UserID
	delete from Users where USER_ID = @UserID
			
	insert into [dbo].[Users]
		([User_Id],[User_Name],[Screen_Name],[Email_Address],[Status_ID],[Created_by_User_ID],[Created_DTTM],
		[Last_Updated_by_User_ID],[Last_Updated_DTTM],[client_code],[internal],[first_name],[last_name],[Corporate]	)
	values
		(@UserID,@UserName,@UserEmail,@UserEmail,1,1,GETDATE(),1,GETDATE(),@clientCode,
		@internalUser, @firstName, @lastName, 1)

-- Add Client_user references - for current client only
	begin
			insert into [dbo].[Client_User]
				([Client_ID],[User_ID],[CreateDate_DTTM])
			values
				(@ClientID,@UserID,GETDATE())
	end 

	begin
            if(@allProperties = 1)
                  BEGIN
						insert into [dbo].User_Auth_Group_Role ([user_ID], [Auth_Group_ID], [Role_ID])
                        values (@UserID, -666, @roleID)
                  END
            else
				  BEGIN
						insert into [User_Individual_Property_Role] ([User_ID], [Property_ID],[Role_ID])
							values (@UserID, 010014,@roleRW)
							
						insert into [User_Individual_Property_Role] ([User_ID], [Property_ID],[Role_ID])
							values (@UserID, 010021,@roleRO)							
				  END                   
    end
END



GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE procedure [dbo].[usp_Delete_TableDataByBatch]
(
	@DryRun TINYINT = 0,
  @BatchSize INT = 5000,
	@SchemaName NVARCHAR(128) = 'dbo',
	@TableName NVARCHAR(128) = NULL,
  @WhereClause VARCHAR(1000) = NULL,
  @UseRowLockHint TINYINT = 0
)
AS
BEGIN
	DECLARE
		@Status NVARCHAR(MAX) = 'COMPLETE'
		,@ProcessingStartTime DATETIME
		,@ProcessingEndTime DATETIME
		,@MaxBatchSize INT = 5000
		,@MaxBatchesToProcess INT = 100
		,@MaxTotalExecutionTimeInMinutes INT = 3
		,@DetailedErrorMessage VARCHAR(5000)
		,@SqlFromPartReplacementToken NVARCHAR(MAX) = ' FROM [%SCHEMA_REPLACE_TOKEN%].[%TABLE_REPLACE_TOKEN%]'
		,@SqlWherePartReplacementToken NVARCHAR(MAX) = ' %WHERE_REPLACE_TOKEN%'
		,@SqlFromPart NVARCHAR(MAX)
		,@SqlWherePart NVARCHAR(MAX)
		,@TableExistsValidationSQL NVARCHAR(MAX)
		,@TableExistsCount INT
		,@TotalRowCount INT = 0
		,@SqlValidCount INT
		,@SelectSqlStatement NVARCHAR(MAX)
		,@ExecutableSelectSqlStatement NVARCHAR(MAX)
		,@DeleteSqlStatement NVARCHAR(MAX)
		,@TotalRowsToDelete INT
		,@RowsDeleted INT
		,@TotalRowsDeleted INT = 0
		,@CurrentBatch INT
		,@TotalBatchesProcessed INT = 0
		,@TotalCalculatedBatches INT = 0

		SET @ProcessingStartTime = GETDATE()

	PRINT 'Processing Started...'

	-- Validate @SchemaName parameter
	IF (@SchemaName IS NULL) OR (LTRIM(RTRIM(LEN(@SchemaName))) < 1)
    BEGIN
        RAISERROR('Parameter @SchemaName cannot be null or empty.',1,1)
        RETURN
    END

	-- Validate @TableName parameter
    IF (@TableName IS NULL) OR (LTRIM(RTRIM(LEN(@TableName))) < 1)
    BEGIN
        RAISERROR('Parameter @TableName cannot be null or empty.',1,1)
        RETURN
    END

    -- Validate @WhereClause parameter
    IF (@WhereClause IS NULL) OR (LTRIM(RTRIM(LEN(@WhereClause))) < 1)
    BEGIN
        RAISERROR('Parameter @WhereClause cannot be null or empty.',1,1)
        RETURN
    END

	-- Perform validation on parameters and update them to comply if needed
	IF (@BatchSize IS NULL) OR (@BatchSize > @MaxBatchSize)
		SET @BatchSize = @MaxBatchSize

	IF (@UseRowLockHint IS NULL)
		SET @UseRowLockHint = 0

	IF (CHARINDEX('WHERE', @WhereClause) = 0)
		SET @WhereClause = N'WHERE ' + @WhereClause


	-- Validate that schema and table exist
	SET @TableExistsValidationSQL =
		N'SELECT @TableExistsCount = COUNT(*) FROM [INFORMATION_SCHEMA].[TABLES] ' +
		N'WHERE TABLE_CATALOG = '''' AND TABLE_SCHEMA = ''' + @SchemaName + N''' AND TABLE_NAME = ''' +@TableName + N''''

  BEGIN TRY
		EXEC sp_executesql @TableExistsValidationSQL, N'@TableExistsCount INT OUTPUT', @TableExistsCount OUTPUT
	END TRY
	BEGIN CATCH
		-- The SQL was invalid, either the
		SET @DetailedErrorMessage = 'Schema or table does not exist. Check INFORMATION_SCHEMA.TABLES. Query to verify: ' + @TableExistsValidationSQL
		SELECT
			'Invalid Database Objects' AS Error
			,@DetailedErrorMessage AS DetailedErrorMessage
			,@TableExistsValidationSQL AS SqlValidationStatementUsed
			,ERROR_PROCEDURE() AS ErrorProcedure
			,ERROR_MESSAGE() AS ErorException
		PRINT 'Error! @Database, @Schema or @Table are invalid objects. Check that they are valid objects.'
		RETURN
	END CATCH

  -- Build the FROM part of SQL Statement
	SET @SqlFromPart = @SqlFromPartReplacementToken
	SET @SqlFromPart = REPLACE(@SqlFromPart, '%SCHEMA_REPLACE_TOKEN%', @SchemaName)
	SET @SqlFromPart = REPLACE(@SqlFromPart, '%TABLE_REPLACE_TOKEN%', @TableName)

	-- Build the WHERE part of SQL Statement
	SET @SqlWherePart = @SqlWherePartReplacementToken
	SET @SqlWherePart = REPLACE(@SqlWherePart, '%WHERE_REPLACE_TOKEN%', @WhereClause)

	-- Build the "SELECT COUNT(*)" Dynamic SQL Validation Query
	SET @ExecutableSelectSqlStatement = N'SELECT COUNT(*)' + N'' + @SqlFromPart + N'' + @SqlWherePart
	SET @SelectSqlStatement = N'SELECT @TotalRowCount = COUNT(*)' + N'' + @SqlFromPart + N'' + @SqlWherePart

	-- Validate the SQL passed in. This will be done by performing a simple SELECT COUNT(*) + "passed in SQL"
	BEGIN TRY
		EXEC sp_executesql @SelectSqlStatement, N'@TotalRowCount INT OUTPUT', @TotalRowCount OUTPUT
	END TRY
	BEGIN CATCH
		-- The SQL was invalid, either the
		SET @DetailedErrorMessage = 'Invalid @WhereClause. Check the syntax for unterminated quotes or invalid columns'
		SELECT
			'Invalid SQL Syntax' AS Error
			,@DetailedErrorMessage AS DetailedErrorMessage
			,@SelectSqlStatement AS SqlValidationStatementUsed
			,ERROR_PROCEDURE() AS ErrorProcedure
			,ERROR_MESSAGE() AS ErrorException
		PRINT 'Error! @WhereClause failed dynamic SQL validation. Check the @WhereClause parameter for syntax errors.'
		RETURN
	END CATCH

  	-- Build the "DELETE TOP" Dynamic SQL Query
	SET @DeleteSqlStatement = N'DELETE TOP (' + CONVERT(VARCHAR(10), @BatchSize) + N')' + @SqlFromPart

	-- Add ROWLOCK hint if passed in
	IF (@UseRowLockHint = 1)
		SET @DeleteSqlStatement = @DeleteSqlStatement + N' WITH (ROWLOCK)'

	SET @DeleteSqlStatement = @DeleteSqlStatement + @SqlWherePart

	-- Create Dry Run output if parameter set ot true
	IF (@DryRun = 1)
	BEGIN
		PRINT 'Dry run results'

		IF (@TotalRowCount > 0 AND @TotalRowCount < @BatchSize)
			--PRINT('Setting @TotalCalculatedBatches = 1')
			SET @TotalCalculatedBatches = 1
		ELSE
			--PRINT('Setting @TotalCalculatedBatches = CEILING(@TotalRowCount/@BatchSize)')
			SET @TotalCalculatedBatches = CEILING(@TotalRowCount/@BatchSize)

		SELECT
			@DryRun AS DryRun
			,@UseRowLockHint AS UseRowLockHint
			,@BatchSize AS "BatchSize"
			,@MaxBatchSize AS MaxBatchSizeAllowed
			,@ExecutableSelectSqlStatement AS SelectValidationStatement
			,@DeleteSqlStatement AS DeleteStatement
			,@TotalRowCount AS TotalRowCount
			,@TotalCalculatedBatches AS TotalBatchesToProcess
		RETURN
	END

	-- Perform Batch Deletes
	SET XACT_ABORT ON
	SET NOCOUNT ON

	PRINT 'Starting Batch Delete...'

	BEGIN TRY
		SET @RowsDeleted = 1
		WHILE (@RowsDeleted > 0)
		BEGIN
			BEGIN TRANSACTION
				PRINT 'Deleting batch...'

				EXEC sp_executesql @DeleteSqlStatement
				SET @RowsDeleted = @@ROWCOUNT

				PRINT '@RowsDeleted: ' + CAST(@RowsDeleted as NVARCHAR(MAX))

				SET @TotalRowsDeleted = @TotalRowsDeleted + @RowsDeleted

				IF (@RowsDeleted > 0)
					SET @TotalBatchesProcessed = @TotalBatchesProcessed + 1
			COMMIT TRANSACTION
		END
	END TRY
	BEGIN CATCH
		SET @Status = 'INCOMPLETE'
		PRINT 'Error occurred'
		IF (XACT_STATE()) = -1
		BEGIN
			SET @DetailedErrorMessage = N'The transaction is in an uncommittable state. Rolling back transaction for batch: ' + CAST(@TotalBatchesProcessed AS NVARCHAR(MAX))
			PRINT N'The transaction is in an uncommittable state. Rolling back transaction.'
			ROLLBACK TRANSACTION
		END

		IF (XACT_STATE()) = 1
		BEGIN
			SET @DetailedErrorMessage = N'The transaction is committable. Committing transaction for batch: ' + CAST(@TotalBatchesProcessed AS NVARCHAR(MAX)) + N'. However there may not have been additional batches committed.'
			PRINT N'The transaction is committable. Committing transaction.'
			COMMIT TRANSACTION
		END
	END CATCH

  -- End processing timer
	SET @ProcessingEndTime = GETDATE()

	-- Return Final Processing Stats
	SELECT
		@Status as Status
		,@DetailedErrorMessage as DetailedErrorMessage
		,@ProcessingStartTime AS ProcessingStartTime
		,@ProcessingEndTime AS ProcessingEndTime
		,@DryRun AS DryRun
		,@BatchSize AS "BatchSize"
		,@MaxBatchSize AS MaxBatchSizeAllowed
		,@SchemaName AS SchemaName
		,@TableName AS TableName
		,@WhereClause AS WhereClause
		,@UseRowLockHint AS UseRowLockHint
		,@DeleteSqlStatement AS DeleteSqlStatement
		,@TotalRowCount AS TotalRowCount
		,@TotalRowsDeleted AS TotalRowsDeleted
		,@TotalBatchesProcessed AS TotalBatchesProcessed

	PRINT 'Processing Completed'


END
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/**
Author: Chaitanya

Release Update:
Release_Dt		First_Name				Release Comments
----------		----------------		-----------------
2019-01-21		Chaitanya Deshmukh		Initial Version
2019-01-30		Chaitanya Deshmukh		Added Extract_Started_DTTM, Extract_Completed_DTTM, Property Id columns
2019-02-11		Rohan Mule    			Modified stored procedure to get processing completed DTTM
2019-02-18		Chaitanya Deshmukh    	Modified stored procedure to get Failed Decision Delivery Job and external system code
2019-02-18		Chaitanya Deshmukh    	Modified to include Abandoned decision delivery jobs, previously it was only Failed
2019-04-25		Sonam Rasal				Added 4 columns to gather user action required and extract processing data for given duration
**/
CREATE PROCEDURE [dbo].[usp_get_client_processing_dashboard_data] (
	@clientCode VARCHAR(50),
	@fromDate DATE,
	@toDate DATE,
	@cutoffTime TIME
)
AS
BEGIN
DECLARE @DataReceivedCutoffTime VARCHAR(20)
SET @DataReceivedCutoffTime = '04:00:00.00'

SELECT CASE
			WHEN Extract_Status != 'COMPLETE' THEN 'DATA_EXTRACT_NOT_RECEIVED'
			WHEN Client_Interaction_Required IS NOT NULL THEN 'USER_ACTION_REQUIRED'
			WHEN All_Problems_Count - Failed_Decision_Delivery_Jobs_Count > 0
				AND Processing_Status != 'COMPLETED' THEN 'PROCESSING_FAILED'
			WHEN Failed_Decision_Delivery_Jobs_Count > 0
				AND Processing_Status != 'COMPLETED' THEN 'DECISION_UPLOAD_FAILED'
			WHEN pdpStatus = 'BDE_DECISIONS_IN_PROGRESS'
				AND Processing_Status = 'IN_PROGRESS'	THEN 'DECISIONS_UPLOAD_IN_PROGRESS'
			ELSE Processing_Status
			END AS Activity_Status,
			Summary_Status,
			Property_Daily_Processing_ID,
			Input_Processing_ID_fromChild,
			Client_code,
			Property_Code,
			Property_Name,
			Property_ID,
			Stage,
			Property_Time_Zone,
			Processing_Date,
			Processing_Status,
			Extract_Status,
			Extract_Started_DTTM,
			Extract_Completed_DTTM,
			Processing_Duration,
			Received_DTTM AS Processing_Received_DTTM,
			Started_DTTM AS Processing_Started_DTTM,
			Decision_Completed_DTTM,
			Completed_DTTM AS Processing_Completed_DTTM,
			Completed_Duration,
			All_Problems_Count - Failed_Decision_Delivery_Jobs_Count AS Input_Processing_Problems_Count,
			Failed_Decision_Delivery_Jobs_Count,
			Client_Interaction_Required,
			CASE
			WHEN Failed_Decision_Delivery_Jobs_Count > 0 THEN
			(
					SELECT LEFT(JOB_NAME, LEN(JOB_NAME) - 1)
			  	FROM (
							SELECT JOB_NAME + '#' + jep.STRING_VAL + ','
							FROM Job.dbo.JOB_STATE js WITH (NOLOCK)
							JOIN Job.dbo.JOB_EXECUTION_PARAMS jep WITH (NOLOCK) ON js.JOB_EXECUTION_ID = jep.JOB_EXECUTION_ID
								AND (js.JOB_NAME LIKE '%DecisionDelivery%' OR js.JOB_NAME LIKE '%UploadDecisions%')
								AND js.EXECUTION_STATUS IN ('ABANDONED', 'FAILED')
								AND jep.KEY_NAME = 'externalSystemCode'
								AND js.Job_Instance_ID IN (
									SELECT ipj.Job_Instance_ID
									FROM GLOBAL.dbo.Input_Processing_Job ipj WITH (NOLOCK)JOIN GLOBAL.dbo.Input_Processing ip WITH (NOLOCK)
									ON ipj.Input_Processing_ID = ip.Input_Processing_ID
									AND ip.Input_Processing_ID = Input_Processing_ID_fromChild
									AND ip.STATUS = 'IN_PROGRESS'
									)
							ORDER BY STEP_START_TIME DESC
							FOR XML PATH('')
							) jn (JOB_NAME)
						)
			ELSE '-'
			END AS Failed_Decision_Delivery_Job_Names,
			pdpStatus,
			CASE
			WHEN client_interaction_required_trend IS NULL
				THEN 0
			ELSE 1
			END AS user_action_required_trend,
			CASE
			WHEN Summary_Status = 'DATA_EXTRACT_NOT_RECEIVED'
				THEN 1
			ELSE 0
			END AS data_extract_not_received_trend,
			CASE
			WHEN count_of_failed_decision_delivery_jobs_trend > 0
				THEN 1
			ELSE 0
			END AS decision_upload_failed_trend,
			CASE
			WHEN count_of_all_problems_trend - count_of_failed_decision_delivery_jobs_trend > 0
				THEN 1
			ELSE 0
			END AS processing_failed_trend
	FROM (
		SELECT pdp.Property_Daily_Processing_ID,
		 CASE
				WHEN Extract_Status = 'COMPLETE'
					AND ip.STATUS = 'NOT_RECEIVED'
					THEN 'PRE_BDE_SCHEDULED_TIME'
				WHEN Extract_Status = 'COMPLETE'
					AND ip.STATUS = 'IN_PROGRESS'
					THEN 'IN_PROGRESS'
				WHEN ip.STATUS = 'COMPLETED'
					AND Extract_Completed_DTTM IS NOT NULL
					AND Extract_Completed_DTTM < Dateadd(millisecond, Datediff(millisecond, 0, @DataReceivedCutoffTime), CAST(pdp.Processing_Date AS DATETIME))
					AND ip.Completed_DTTM > Dateadd(millisecond, Datediff(millisecond, 0, @cutoffTime), CAST(pdp.Processing_Date AS DATETIME))
					THEN 'DATA_RECEIVED_BEFORE_4AM_AND_COMPLETED_AFTER_CUTOFF_TIME'
				WHEN ip.STATUS = 'COMPLETED'
					AND Extract_Completed_DTTM IS NOT NULL
					AND Extract_Completed_DTTM > Dateadd(millisecond, Datediff(millisecond, 0, @DataReceivedCutoffTime), CAST(pdp.Processing_Date AS DATETIME))
					AND ip.Completed_DTTM > Dateadd(millisecond, Datediff(millisecond, 0, @cutoffTime), CAST(pdp.Processing_Date AS DATETIME))
					THEN 'DATA_RECEIVED_AFTER_4AM_AND_COMPLETED_AFTER_CUTOFF_TIME'
				WHEN ip.STATUS = 'COMPLETED'
					AND Extract_Completed_DTTM IS NOT NULL
					AND Extract_Completed_DTTM > Dateadd(millisecond, Datediff(millisecond, 0, @DataReceivedCutoffTime), CAST(pdp.Processing_Date AS DATETIME))
					AND ip.Completed_DTTM < Dateadd(millisecond, Datediff(millisecond, 0, @cutoffTime), CAST(pdp.Processing_Date AS DATETIME))
					THEN 'DATA_RECEIVED_AFTER_4AM_AND_COMPLETED_BEFORE_CUTOFF_TIME'
				WHEN ip.STATUS = 'COMPLETED'
					AND Extract_Completed_DTTM IS NOT NULL
					AND Extract_Completed_DTTM < Dateadd(millisecond, Datediff(millisecond, 0, @DataReceivedCutoffTime), CAST(pdp.Processing_Date AS DATETIME))
					AND ip.Completed_DTTM < Dateadd(millisecond, Datediff(millisecond, 0, @cutoffTime), CAST(pdp.Processing_Date AS DATETIME))
					THEN 'DATA_RECEIVED_BEFORE_4AM_AND_COMPLETED_BEFORE_CUTOFF_TIME'
				WHEN ip.STATUS = 'FAILED'
					OR ip.STATUS = 'SKIPPED' THEN 'IN_PROGRESS'
				ELSE 'DATA_EXTRACT_NOT_RECEIVED'
				END AS Summary_Status,
				ip.Input_Processing_ID AS Input_Processing_ID_fromChild,
				ip.STATUS AS Processing_Status,
				Extract_Status,
				pdp.STATUS AS pdpStatus,
				pdp.Client_Code,
				pdp.Property_Code,
				p.Property_Name,
				p.Property_ID,
				p.Stage,
				pdp.Property_Time_Zone,
				pdp.Processing_Date,
				Extract_Started_DTTM,
				Extract_Completed_DTTM,
				CASE
				WHEN ip.STATUS = 'COMPLETED' THEN ip.Completed_DTTM
				ELSE NULL
				END AS Decision_Completed_DTTM,
				CASE
				WHEN Received_DTTM IS NOT NULL
					AND Decisions_Generated_DTTM IS NOT NULL THEN CONVERT(TIME, Decisions_Generated_DTTM - Received_DTTM)
				ELSE NULL
				END AS Processing_Duration,
				Received_DTTM,
				Started_DTTM,
				Decisions_Generated_DTTM,
				Completed_DTTM,
				CASE
				WHEN Received_DTTM IS NOT NULL
					AND Completed_DTTM IS NOT NULL
					THEN CONVERT(TIME, Completed_DTTM - Received_DTTM)
				ELSE NULL
				END AS Completed_Duration,
				ISNULL (
				 (SELECT MAX(p.Problem_ID)
					FROM Job.dbo.Problem p WITH (NOLOCK)
					WHERE p.IS_ACTIVE = 'True'
						AND p.Description LIKE '%USER_ACTION_REQUIRED_EXCEPTION%'
						AND p.Job_Instance_ID IN
						 (SELECT ipj.Job_Instance_ID
							FROM GLOBAL.dbo.Input_Processing_Job ipj
							WITH (NOLOCK)
							WHERE ipj.Input_Processing_ID = ip.Input_Processing_ID
								AND ip.STATUS = 'IN_PROGRESS')	), NULL) AS Client_Interaction_Required,
					(SELECT count(p.Problem_ID)
				FROM Job.dbo.Problem p WITH (NOLOCK)
				WHERE p.IS_ACTIVE = 'True'
					AND p.Job_Instance_ID IN (
						SELECT ipj.Job_Instance_ID
						FROM GLOBAL.dbo.Input_Processing_Job ipj
						WITH (NOLOCK)
						WHERE ipj.Input_Processing_ID = ip.Input_Processing_ID
							AND ip.STATUS = 'IN_PROGRESS'	) ) All_Problems_Count,
				 (SELECT count(js.JOB_INSTANCE_ID)
				FROM Job.dbo.JOB_STATE js WITH (NOLOCK)
				WHERE (	js.EXECUTION_STATUS = 'ABANDONED' OR js.EXECUTION_STATUS = 'FAILED')
					AND js.Job_Instance_ID IN
					(	SELECT ipj.Job_Instance_ID
						FROM GLOBAL.dbo.Input_Processing_Job ipj
						WITH (NOLOCK)
						WHERE ipj.Input_Processing_ID = ip.Input_Processing_ID )
					AND ( js.JOB_NAME LIKE '%DecisionDelivery%'	OR js.JOB_NAME LIKE '%UploadDecisions%' )
					AND ip.STATUS = 'IN_PROGRESS' ) Failed_Decision_Delivery_Jobs_Count,
				 isnull((
					SELECT max(p.Problem_ID)
					FROM Job.dbo.PROBLEM p WITH (NOLOCK)
					WHERE p.DESCRIPTION LIKE '%USER_ACTION_REQUIRED_EXCEPTION%'
						AND p.JOB_INSTANCE_ID IN (
							SELECT ipj.Job_Instance_ID
							FROM GLOBAL.dbo.Input_Processing_Job ipj
							WITH (NOLOCK)
							WHERE ipj.Input_Processing_ID = ip.Input_Processing_ID
								AND ip.STATUS = 'COMPLETED'
							)), NULL) AS client_interaction_required_trend,
					 (
						SELECT count(js.JOB_INSTANCE_ID)
						FROM Job.dbo.JOB_STATE js WITH (NOLOCK)
						inner join Job.dbo.PROBLEM p WITH (NOLOCK) on p.JOB_INSTANCE_ID = js.JOB_INSTANCE_ID
						where js.Job_Instance_ID IN (
							SELECT ipj.Job_Instance_ID
							FROM GLOBAL.dbo.Input_Processing_Job ipj WITH (NOLOCK)
							WHERE ipj.Input_Processing_ID = ip.Input_Processing_ID AND ip.STATUS = 'COMPLETED'
						)
						and (
						js.JOB_NAME LIKE '%DecisionDelivery%'
						OR js.JOB_NAME LIKE '%UploadDecisions%'
						)
				) count_of_failed_decision_delivery_jobs_trend,
				 (
				SELECT count(p.Problem_ID)
				FROM Job.dbo.Problem p WITH (NOLOCK)
				WHERE p.Job_Instance_ID IN (
						SELECT ipj.Job_Instance_ID
						FROM GLOBAL.dbo.Input_Processing_Job ipj
						WITH (NOLOCK)
						WHERE ipj.Input_Processing_ID = ip.Input_Processing_ID
							AND ip.STATUS = 'COMPLETED'
						)
				) count_of_all_problems_trend
		FROM GLOBAL.dbo.Property_Daily_Processing pdp
		WITH (NOLOCK)
		INNER JOIN GLOBAL.dbo.Input_Processing ip
		WITH (NOLOCK) ON pdp.property_daily_processing_id = ip.property_daily_processing_id
		INNER JOIN GLOBAL.dbo.Client c
		WITH (NOLOCK) ON c.Client_Code = pdp.Client_Code
		INNER JOIN GLOBAL.dbo.Property p ON p.Property_Code = pdp.Property_Code
			AND p.Client_ID = c.Client_ID
		WHERE ip.Input_Type = 'BDE'
			AND p.Stage IN ('TWO_WAY', 'ONE_WAY', 'POPULATION')
			AND ip.Last_Updated_DTTM IS NOT NULL
			AND pdp.Client_Code = @clientCode
			AND Processing_Date BETWEEN @fromDate AND @toDate) propertyStatistics
END
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_Running_and_Throttled_At_Intervals]
	@ProdEnv INT = 1,
	@ProcessingDate DATETIME = NULL
AS
BEGIN
	SET @ProcessingDate = ISNULL(@ProcessingDate, GETDATE())

	IF OBJECT_ID('tempdb..#DateTime_Intervals') IS NOT NULL DROP TABLE #DateTime_Intervals
	CREATE TABLE #DateTime_Intervals (timeInterval datetime)

	DECLARE @StartDate datetime = CONVERT(date, @ProcessingDate-1)
	DECLARE @EndDate datetime = CONVERT(date, @ProcessingDate+ 1)

	DECLARE @dt datetime
	SET @dt = @StartDate

	WHILE @dt < @EndDate
	BEGIN
		INSERT INTO #DateTime_Intervals VALUES (@dt)
		SET @dt = DATEADD(MINUTE, 15, @dt)
	END

	IF @ProdEnv = 1
		SELECT
			runningJobs.Time_Interval,
			Running_ProcessCRSFileBDE,
			Throttled_ProcessCRSFileBDE,
			Running_ProcessCRSFileCDP,
			Throttled_ProcessCRSFileCDP,
			Running_PostProcessingJobs,
			Throttled_PostProcessingJobs,
			Running_mn5pg3xdbsw102,
			Throttled_mn5pg3xdbsw102,
			Running_mn5pg3xdbsw103,
			Throttled_mn5pg3xdbsw103,
			Running_mn5pg3xdbsw104,
			Throttled_mn5pg3xdbsw104,
			Running_mn5pg3xdbsw105,
			Throttled_mn5pg3xdbsw105,
			Running_mn5pg3xdbsw106,
			Throttled_mn5pg3xdbsw106,
			Running_mn5pg3xdbsw107,
			Throttled_mn5pg3xdbsw107,
			Running_mn5pg3xdbsw108,
			Throttled_mn5pg3xdbsw108,
			Running_mn5pg3xdbsw109,
			Throttled_mn5pg3xdbsw109,
			Running_mn5pg3xdbsw110,
			Throttled_mn5pg3xdbsw110,
			Running_mn5pg3xdbsw111,
			Throttled_mn5pg3xdbsw111,
			Running_mn5pg3xdbsw112,
			Throttled_mn5pg3xdbsw112,
			Running_mn5pg3xdbsw113,
			Throttled_mn5pg3xdbsw113,
			Running_mn5pg3xdbsw114,
			Throttled_mn5pg3xdbsw114,
			Running_mn5pg3xdbsw115,
			Throttled_mn5pg3xdbsw115,
			Running_mn5pg3xdbsw116,
			Throttled_mn5pg3xdbsw116,
			Running_mn5pg3xdbsw117,
			Throttled_mn5pg3xdbsw117,
			Running_mn5pg3xdbsw118,
			Throttled_mn5pg3xdbsw118,
			Running_mn5pg3xdbsw119,
			Throttled_mn5pg3xdbsw119,
			Running_mn5pg3xdbsw120,
			Throttled_mn5pg3xdbsw120,
			Running_mn5pg3xdbsw121,
			Throttled_mn5pg3xdbsw121,
			Running_mn5pg3xdbsw122,
			Throttled_mn5pg3xdbsw122,
			Running_mn5pg3xdbsw123,
			Throttled_mn5pg3xdbsw123,
			Running_mn5pg3xdbsw124,
			Throttled_mn5pg3xdbsw124,
			Running_mn5pg3xsasw004,
			Throttled_mn5pg3xsasw004,
			Running_mn5pg3xsasw005,
			Throttled_mn5pg3xsasw005,
			Running_mn5pg3xsasw006,
			Throttled_mn5pg3xsasw006,
			Running_mn5pg3xsasw007,
			Throttled_mn5pg3xsasw007,
			Running_mn5pg3xsasw008,
			Throttled_mn5pg3xsasw008,
			Running_mn5pg3xsasw009,
			Throttled_mn5pg3xsasw009,
			Running_mn5pg3xsasw010,
			Throttled_mn5pg3xsasw010,
			Running_mn5pg3xsasw011,
			Throttled_mn5pg3xsasw011,
			Running_mn5pg3xsasw012,
			Throttled_mn5pg3xsasw012,
			Running_mn5pg3xsasw013,
			Throttled_mn5pg3xsasw013,
			Running_mn5pg3xsasw014,
			Throttled_mn5pg3xsasw014,
			Running_mn5pg3xsasw119,
			Throttled_mn5pg3xsasw119,
			Running_mn5pg3xsasw120,
			Throttled_mn5pg3xsasw120,
			Running_mn5pg3xsasw121,
			Throttled_mn5pg3xsasw121,
			Running_mn5pg3xsasw122,
			Throttled_mn5pg3xsasw122,
			Running_mn5pg3xsasw123,
			Throttled_mn5pg3xsasw123,
			Running_mn5pg3xsasw124,
			Throttled_mn5pg3xsasw124
		FROM (
			SELECT
				Time_Interval,
				ISNULL(ProcessCRSFileBDE,0) AS Running_ProcessCRSFileBDE,
				ISNULL(ProcessCRSFileCDP,0) AS Running_ProcessCRSFileCDP
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					pdp.JOB_NAME,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_BDE_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						js.JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT max(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						DB_Server_Name,
						SAS_Server_Name
					FROM Property_Daily_Processing pdp INNER JOIN Input_Processing ip ON pdp.property_daily_processing_id = ip.property_daily_processing_id
					INNER JOIN Input_Processing_Job ipj ON ip.input_processing_id = ipj.input_processing_id
					INNER JOIN job.dbo.Job_State js WITH (ROWLOCK) ON js.job_instance_id = ipj.job_instance_id AND js.JOB_NAME in ('ProcessCRSFileBDE','ProcessCRSFileCDP')
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE ip.Input_Type IN ('BDE','CDP')
					AND Processing_Date = CONVERT(date, @ProcessingDate)
				) pdp ON pdp.Processing_Start_Time <= dti.timeInterval AND dti.timeInterval < Job_End_Time
				GROUP BY dti.timeInterval, pdp.Job_Name
			) timeIntervalRunningJobs
			PIVOT (
				SUM(Number_Of_Running_BDE_Jobs) for JOB_NAME IN (ProcessCRSFileBDE,ProcessCRSFileCDP)
			) AS timeIntervalPivotedByRunningJobs
		) runningJobs LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL(ProcessCRSFileBDE,0) AS Throttled_ProcessCRSFileBDE,
				ISNULL(ProcessCRSFileCDP,0) AS Throttled_ProcessCRSFileCDP
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					pdp.JOB_NAME,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_BDE_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						js.JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT max(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						DB_Server_Name,
						SAS_Server_Name
					FROM Property_Daily_Processing pdp INNER JOIN Input_Processing ip ON pdp.property_daily_processing_id = ip.property_daily_processing_id
					INNER JOIN Input_Processing_Job ipj ON ip.input_processing_id = ipj.input_processing_id
					INNER JOIN job.dbo.Job_State js WITH (ROWLOCK) ON js.job_instance_id = ipj.job_instance_id AND js.JOB_NAME in ('ProcessCRSFileBDE','ProcessCRSFileCDP')
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE ip.Input_Type IN ('BDE','CDP')
					AND Processing_Date = CONVERT(date, @ProcessingDate)
				) pdp ON pdp.Job_Start_Time <= dti.timeInterval AND dti.timeInterval < Processing_Start_Time
				GROUP BY dti.timeInterval, pdp.Job_Name
			) timeIntervalThrottledJobs
			PIVOT (
				SUM(Number_Of_Running_BDE_Jobs) for JOB_NAME IN (ProcessCRSFileBDE,ProcessCRSFileCDP)
			) AS timeIntervalPivotedByThrottledJobs
		) throttledJobs ON runningJobs.Time_Interval = throttledJobs.Time_Interval
		LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL(PostProcessing,0) AS Running_PostProcessingJobs
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					JOB_NAME,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_Post_Processing_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						'PostProcessing' as JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT MAX(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						jiwc.DB_Server_Name,
						jiwc.SAS_Server_Name
					FROM job.dbo.Job_State js
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE js.JOB_NAME in ('BDEPostProcessingJob','BDEPostProcessingIndexRebuildJob')
					AND js.START_TIME between CONVERT(date, @ProcessingDate-1) and CONVERT(date, @ProcessingDate+1)
				) pdp ON pdp.Processing_Start_Time <= dti.timeInterval AND dti.timeInterval < Job_End_Time
				GROUP BY dti.timeInterval, JOB_NAME
			) timeIntervalRunningJobs
			PIVOT (
				SUM(Number_Of_Running_Post_Processing_Jobs) for JOB_NAME IN (PostProcessing)
			) AS timeIntervalPivotedByRunningJobs
		) postProcessingRunningJobs ON runningJobs.Time_Interval = postProcessingRunningJobs.Time_Interval
		LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL(PostProcessing,0) AS Throttled_PostProcessingJobs
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					pdp.JOB_NAME,
					ISNULL(SUM(Jobs), 0) as Number_Of_Throttled_PropertyPostProcessingJobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
				SELECT
					'PostProcessing' as JOB_NAME,
					js.START_TIME Job_Start_Time,
					(SELECT MAX(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
					js.END_TIME Job_End_Time,
					1 Jobs,
					DB_Server_Name,
					SAS_Server_Name
					FROM job.dbo.Job_State js
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE js.JOB_NAME in ('BDEPostProcessingJob','BDEPostProcessingIndexRebuildJob')
					AND js.START_TIME between CONVERT(date, @ProcessingDate-1) and CONVERT(date, @ProcessingDate+1)
			) pdp ON pdp.Job_Start_Time <= dti.timeInterval AND dti.timeInterval < Processing_Start_Time
			GROUP BY dti.timeInterval, pdp.Job_Name
		) timeIntervalThrottledJobs
		PIVOT (
			SUM(Number_Of_Throttled_PropertyPostProcessingJobs) for JOB_NAME IN (PostProcessing)
		) AS timeIntervalPivotedByThrottledJobs
		) postProcessingThrottledJobs ON runningJobs.Time_Interval = postProcessingThrottledJobs.Time_Interval
		LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL([mn5pg3xdbsw102.ideasprod.int],0) as Running_mn5pg3xdbsw102,
				ISNULL([mn5pg3xdbsw103.ideasprod.int],0) as Running_mn5pg3xdbsw103,
				ISNULL([mn5pg3xdbsw104.ideasprod.int],0) as Running_mn5pg3xdbsw104,
				ISNULL([mn5pg3xdbsw105.ideasprod.int],0) as Running_mn5pg3xdbsw105,
				ISNULL([mn5pg3xdbsw106.ideasprod.int],0) as Running_mn5pg3xdbsw106,
				ISNULL([mn5pg3xdbsw107.ideasprod.int],0) as Running_mn5pg3xdbsw107,
				ISNULL([mn5pg3xdbsw108.ideasprod.int],0) as Running_mn5pg3xdbsw108,
				ISNULL([mn5pg3xdbsw109.ideasprod.int],0) as Running_mn5pg3xdbsw109,
				ISNULL([mn5pg3xdbsw110.ideasprod.int],0) as Running_mn5pg3xdbsw110,
				ISNULL([mn5pg3xdbsw111.ideasprod.int],0) as Running_mn5pg3xdbsw111,
				ISNULL([mn5pg3xdbsw112.ideasprod.int],0) as Running_mn5pg3xdbsw112,
				ISNULL([mn5pg3xdbsw113.ideasprod.int],0) as Running_mn5pg3xdbsw113,
				ISNULL([mn5pg3xdbsw114.ideasprod.int],0) as Running_mn5pg3xdbsw114,
				ISNULL([mn5pg3xdbsw115.ideasprod.int],0) as Running_mn5pg3xdbsw115,
				ISNULL([mn5pg3xdbsw116.ideasprod.int],0) as Running_mn5pg3xdbsw116,
				ISNULL([mn5pg3xdbsw117.ideasprod.int],0) as Running_mn5pg3xdbsw117,
				ISNULL([mn5pg3xdbsw118.ideasprod.int],0) as Running_mn5pg3xdbsw118,
				ISNULL([mn5pg3xdbsw119.ideasprod.int],0) as Running_mn5pg3xdbsw119,
				ISNULL([mn5pg3xdbsw120.ideasprod.int],0) as Running_mn5pg3xdbsw120,
				ISNULL([mn5pg3xdbsw121.ideasprod.int],0) as Running_mn5pg3xdbsw121,
				ISNULL([mn5pg3xdbsw122.ideasprod.int],0) as Running_mn5pg3xdbsw122,
				ISNULL([mn5pg3xdbsw123.ideasprod.int],0) as Running_mn5pg3xdbsw123,
				ISNULL([mn5pg3xdbsw124.ideasprod.int],0) as Running_mn5pg3xdbsw124
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					DB_Server_Name,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_BDE_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT MAX(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						jiwc.DB_Server_Name,
						jiwc.SAS_Server_Name
					FROM job.dbo.Job_State js
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE js.JOB_NAME in ('ProcessCRSFile','ProcessCRSFileBDE','ProcessCRSFileCDP','OperaDataLoad','OperaCdpDataLoad','NGIDeferredDeliveryJob','NGICdpDeferredDeliveryJob','BDEPostProcessingJob','BDEPostProcessingIndexRebuildJob','PropertyPostProcessingJob','CrsCatchup','OperaCatchupJob','NGICatchupJob')
					AND js.START_TIME between CONVERT(date, @ProcessingDate-1) and CONVERT(date, @ProcessingDate+1)
				) pdp ON pdp.Processing_Start_Time <= dti.timeInterval AND dti.timeInterval < Job_End_Time
				GROUP BY dti.timeInterval, pdp.DB_Server_Name
			) timeIntervalRunningJobsByDBServer
			PIVOT (
				SUM(Number_Of_Running_BDE_Jobs) for DB_Server_Name IN ([mn5pg3xdbsw102.ideasprod.int], [mn5pg3xdbsw103.ideasprod.int], [mn5pg3xdbsw104.ideasprod.int], [mn5pg3xdbsw105.ideasprod.int], [mn5pg3xdbsw106.ideasprod.int], [mn5pg3xdbsw107.ideasprod.int],[mn5pg3xdbsw108.ideasprod.int], [mn5pg3xdbsw109.ideasprod.int], [mn5pg3xdbsw110.ideasprod.int], [mn5pg3xdbsw111.ideasprod.int], [mn5pg3xdbsw112.ideasprod.int], [mn5pg3xdbsw113.ideasprod.int], [mn5pg3xdbsw114.ideasprod.int], [mn5pg3xdbsw115.ideasprod.int], [mn5pg3xdbsw116.ideasprod.int], [mn5pg3xdbsw117.ideasprod.int], [mn5pg3xdbsw118.ideasprod.int], [mn5pg3xdbsw119.ideasprod.int], [mn5pg3xdbsw120.ideasprod.int], [mn5pg3xdbsw121.ideasprod.int], [mn5pg3xdbsw122.ideasprod.int], [mn5pg3xdbsw123.ideasprod.int], [mn5pg3xdbsw124.ideasprod.int])
			) AS timeIntervalPivotedByRunningJobsByDBServer
		) runningJobsByDBServer ON runningJobs.Time_Interval = runningJobsByDBServer.Time_Interval
		LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL([mn5pg3xdbsw102.ideasprod.int],0) as Throttled_mn5pg3xdbsw102,
				ISNULL([mn5pg3xdbsw103.ideasprod.int],0) as Throttled_mn5pg3xdbsw103,
				ISNULL([mn5pg3xdbsw104.ideasprod.int],0) as Throttled_mn5pg3xdbsw104,
				ISNULL([mn5pg3xdbsw105.ideasprod.int],0) as Throttled_mn5pg3xdbsw105,
				ISNULL([mn5pg3xdbsw106.ideasprod.int],0) as Throttled_mn5pg3xdbsw106,
				ISNULL([mn5pg3xdbsw107.ideasprod.int],0) as Throttled_mn5pg3xdbsw107,
				ISNULL([mn5pg3xdbsw108.ideasprod.int],0) as Throttled_mn5pg3xdbsw108,
				ISNULL([mn5pg3xdbsw109.ideasprod.int],0) as Throttled_mn5pg3xdbsw109,
				ISNULL([mn5pg3xdbsw110.ideasprod.int],0) as Throttled_mn5pg3xdbsw110,
				ISNULL([mn5pg3xdbsw111.ideasprod.int],0) as Throttled_mn5pg3xdbsw111,
				ISNULL([mn5pg3xdbsw112.ideasprod.int],0) as Throttled_mn5pg3xdbsw112,
				ISNULL([mn5pg3xdbsw113.ideasprod.int],0) as Throttled_mn5pg3xdbsw113,
				ISNULL([mn5pg3xdbsw114.ideasprod.int],0) as Throttled_mn5pg3xdbsw114,
				ISNULL([mn5pg3xdbsw115.ideasprod.int],0) as Throttled_mn5pg3xdbsw115,
				ISNULL([mn5pg3xdbsw116.ideasprod.int],0) as Throttled_mn5pg3xdbsw116,
				ISNULL([mn5pg3xdbsw117.ideasprod.int],0) as Throttled_mn5pg3xdbsw117,
				ISNULL([mn5pg3xdbsw118.ideasprod.int],0) as Throttled_mn5pg3xdbsw118,
				ISNULL([mn5pg3xdbsw119.ideasprod.int],0) as Throttled_mn5pg3xdbsw119,
				ISNULL([mn5pg3xdbsw120.ideasprod.int],0) as Throttled_mn5pg3xdbsw120,
				ISNULL([mn5pg3xdbsw121.ideasprod.int],0) as Throttled_mn5pg3xdbsw121,
				ISNULL([mn5pg3xdbsw122.ideasprod.int],0) as Throttled_mn5pg3xdbsw122,
				ISNULL([mn5pg3xdbsw123.ideasprod.int],0) as Throttled_mn5pg3xdbsw123,
				ISNULL([mn5pg3xdbsw124.ideasprod.int],0) as Throttled_mn5pg3xdbsw124
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					DB_Server_Name,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_BDE_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						js.JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT max(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						DB_Server_Name,
						SAS_Server_Name
					FROM job.dbo.Job_State js
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE js.JOB_NAME in ('ProcessCRSFile','ProcessCRSFileBDE','ProcessCRSFileCDP','OperaDataLoad','OperaCdpDataLoad','NGIDeferredDeliveryJob','NGICdpDeferredDeliveryJob','BDEPostProcessingJob','BDEPostProcessingIndexRebuildJob','PropertyPostProcessingJob','CrsCatchup','OperaCatchupJob','NGICatchupJob')
					AND js.START_TIME between CONVERT(date, @ProcessingDate-1) and CONVERT(date, @ProcessingDate+1)
				) pdp ON pdp.Job_Start_Time <= dti.timeInterval AND dti.timeInterval < Processing_Start_Time
				GROUP BY dti.timeInterval, pdp.DB_Server_Name
			) timeIntervalThrottledJobsByDBServer
			PIVOT (
				SUM(Number_Of_Running_BDE_Jobs) for DB_Server_Name IN ([mn5pg3xdbsw102.ideasprod.int], [mn5pg3xdbsw103.ideasprod.int], [mn5pg3xdbsw104.ideasprod.int], [mn5pg3xdbsw105.ideasprod.int], [mn5pg3xdbsw106.ideasprod.int], [mn5pg3xdbsw107.ideasprod.int],[mn5pg3xdbsw108.ideasprod.int], [mn5pg3xdbsw109.ideasprod.int], [mn5pg3xdbsw110.ideasprod.int], [mn5pg3xdbsw111.ideasprod.int], [mn5pg3xdbsw112.ideasprod.int], [mn5pg3xdbsw113.ideasprod.int], [mn5pg3xdbsw114.ideasprod.int], [mn5pg3xdbsw115.ideasprod.int], [mn5pg3xdbsw116.ideasprod.int], [mn5pg3xdbsw117.ideasprod.int], [mn5pg3xdbsw118.ideasprod.int], [mn5pg3xdbsw119.ideasprod.int], [mn5pg3xdbsw120.ideasprod.int], [mn5pg3xdbsw121.ideasprod.int], [mn5pg3xdbsw122.ideasprod.int], [mn5pg3xdbsw123.ideasprod.int], [mn5pg3xdbsw124.ideasprod.int])
			) AS timeIntervalPivotedByThrottledJobsByDBServer
		) throttledJobsByDBServer ON runningJobs.Time_Interval = throttledJobsByDBServer.Time_Interval
		LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL([mn5pg3xsasw004.ideasprod.int],0) as Running_mn5pg3xsasw004,
				ISNULL([mn5pg3xsasw005.ideasprod.int],0) as Running_mn5pg3xsasw005,
				ISNULL([mn5pg3xsasw006.ideasprod.int],0) as Running_mn5pg3xsasw006,
				ISNULL([mn5pg3xsasw007.ideasprod.int],0) as Running_mn5pg3xsasw007,
				ISNULL([mn5pg3xsasw008.ideasprod.int],0) as Running_mn5pg3xsasw008,
				ISNULL([mn5pg3xsasw009.ideasprod.int],0) as Running_mn5pg3xsasw009,
				ISNULL([mn5pg3xsasw010.ideasprod.int],0) as Running_mn5pg3xsasw010,
				ISNULL([mn5pg3xsasw011.ideasprod.int],0) as Running_mn5pg3xsasw011,
				ISNULL([mn5pg3xsasw012.ideasprod.int],0) as Running_mn5pg3xsasw012,
				ISNULL([mn5pg3xsasw013.ideasprod.int],0) as Running_mn5pg3xsasw013,
				ISNULL([mn5pg3xsasw014.ideasprod.int],0) as Running_mn5pg3xsasw014,
				ISNULL([mn5pg3xsasw119.ideasprod.int],0) as Running_mn5pg3xsasw119,
				ISNULL([mn5pg3xsasw120.ideasprod.int],0) as Running_mn5pg3xsasw120,
				ISNULL([mn5pg3xsasw121.ideasprod.int],0) as Running_mn5pg3xsasw121,
				ISNULL([mn5pg3xsasw122.ideasprod.int],0) as Running_mn5pg3xsasw122,
				ISNULL([mn5pg3xsasw123.ideasprod.int],0) as Running_mn5pg3xsasw123,
				ISNULL([mn5pg3xsasw124.ideasprod.int],0) as Running_mn5pg3xsasw124
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					SAS_Server_Name,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_BDE_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						js.JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT max(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						DB_Server_Name,
						SAS_Server_Name
					FROM job.dbo.Job_State js
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE js.JOB_NAME in ('ProcessCRSFile','ProcessCRSFileBDE','ProcessCRSFileCDP','OperaDataLoad','OperaCdpDataLoad','NGIDeferredDeliveryJob','NGICdpDeferredDeliveryJob','BDEPostProcessingJob','BDEPostProcessingIndexRebuildJob','PropertyPostProcessingJob','CrsCatchup','OperaCatchupJob','NGICatchupJob')
					AND js.START_TIME between CONVERT(date, @ProcessingDate-1) and CONVERT(date, @ProcessingDate+1)
				) pdp ON pdp.Processing_Start_Time <= dti.timeInterval AND dti.timeInterval < Job_End_Time
				GROUP BY dti.timeInterval, pdp.SAS_Server_Name
			) timeIntervalRunningJobsBySASServer
			PIVOT (
				SUM(Number_Of_Running_BDE_Jobs) for SAS_Server_Name IN ([mn5pg3xsasw004.ideasprod.int], [mn5pg3xsasw005.ideasprod.int], [mn5pg3xsasw006.ideasprod.int], [mn5pg3xsasw007.ideasprod.int], [mn5pg3xsasw008.ideasprod.int], [mn5pg3xsasw009.ideasprod.int],[mn5pg3xsasw010.ideasprod.int], [mn5pg3xsasw011.ideasprod.int], [mn5pg3xsasw012.ideasprod.int], [mn5pg3xsasw013.ideasprod.int], [mn5pg3xsasw014.ideasprod.int], [mn5pg3xsasw119.ideasprod.int], [mn5pg3xsasw120.ideasprod.int], [mn5pg3xsasw121.ideasprod.int], [mn5pg3xsasw122.ideasprod.int], [mn5pg3xsasw123.ideasprod.int], [mn5pg3xsasw124.ideasprod.int])
			) AS timeIntervalPivotedByRunningJobsBySASServer
		) runningJobsBySASServer ON runningJobs.Time_Interval = runningJobsBySASServer.Time_Interval
		LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL([mn5pg3xsasw004.ideasprod.int],0) as Throttled_mn5pg3xsasw004,
				ISNULL([mn5pg3xsasw005.ideasprod.int],0) as Throttled_mn5pg3xsasw005,
				ISNULL([mn5pg3xsasw006.ideasprod.int],0) as Throttled_mn5pg3xsasw006,
				ISNULL([mn5pg3xsasw007.ideasprod.int],0) as Throttled_mn5pg3xsasw007,
				ISNULL([mn5pg3xsasw008.ideasprod.int],0) as Throttled_mn5pg3xsasw008,
				ISNULL([mn5pg3xsasw009.ideasprod.int],0) as Throttled_mn5pg3xsasw009,
				ISNULL([mn5pg3xsasw010.ideasprod.int],0) as Throttled_mn5pg3xsasw010,
				ISNULL([mn5pg3xsasw011.ideasprod.int],0) as Throttled_mn5pg3xsasw011,
				ISNULL([mn5pg3xsasw012.ideasprod.int],0) as Throttled_mn5pg3xsasw012,
				ISNULL([mn5pg3xsasw013.ideasprod.int],0) as Throttled_mn5pg3xsasw013,
				ISNULL([mn5pg3xsasw014.ideasprod.int],0) as Throttled_mn5pg3xsasw014,
				ISNULL([mn5pg3xsasw119.ideasprod.int],0) as Throttled_mn5pg3xsasw119,
				ISNULL([mn5pg3xsasw120.ideasprod.int],0) as Throttled_mn5pg3xsasw120,
				ISNULL([mn5pg3xsasw121.ideasprod.int],0) as Throttled_mn5pg3xsasw121,
				ISNULL([mn5pg3xsasw122.ideasprod.int],0) as Throttled_mn5pg3xsasw122,
				ISNULL([mn5pg3xsasw123.ideasprod.int],0) as Throttled_mn5pg3xsasw123,
				ISNULL([mn5pg3xsasw124.ideasprod.int],0) as Throttled_mn5pg3xsasw124
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					SAS_Server_Name,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_BDE_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						js.JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT max(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						DB_Server_Name,
						SAS_Server_Name
					FROM job.dbo.Job_State js
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE js.JOB_NAME in ('ProcessCRSFile','ProcessCRSFileBDE','ProcessCRSFileCDP','OperaDataLoad','OperaCdpDataLoad','NGIDeferredDeliveryJob','NGICdpDeferredDeliveryJob','BDEPostProcessingJob','BDEPostProcessingIndexRebuildJob','PropertyPostProcessingJob','CrsCatchup','OperaCatchupJob','NGICatchupJob')
					AND js.START_TIME between CONVERT(date, @ProcessingDate-1) and CONVERT(date, @ProcessingDate+1)
				) pdp ON pdp.Job_Start_Time <= dti.timeInterval AND dti.timeInterval < Processing_Start_Time
				GROUP BY dti.timeInterval, pdp.SAS_Server_Name
			) timeIntervalThrottledJobsBySASServer
			PIVOT (
				SUM(Number_Of_Running_BDE_Jobs) for SAS_Server_Name IN ([mn5pg3xsasw004.ideasprod.int], [mn5pg3xsasw005.ideasprod.int], [mn5pg3xsasw006.ideasprod.int], [mn5pg3xsasw007.ideasprod.int], [mn5pg3xsasw008.ideasprod.int], [mn5pg3xsasw009.ideasprod.int],[mn5pg3xsasw010.ideasprod.int], [mn5pg3xsasw011.ideasprod.int], [mn5pg3xsasw012.ideasprod.int], [mn5pg3xsasw013.ideasprod.int], [mn5pg3xsasw014.ideasprod.int], [mn5pg3xsasw119.ideasprod.int], [mn5pg3xsasw120.ideasprod.int], [mn5pg3xsasw121.ideasprod.int], [mn5pg3xsasw122.ideasprod.int], [mn5pg3xsasw123.ideasprod.int], [mn5pg3xsasw124.ideasprod.int])
			) AS timeIntervalPivotedByThrottledJobsBySASServer
		) throttledJobsBySASServer ON runningJobs.Time_Interval = throttledJobsBySASServer.Time_Interval
		ORDER BY runningJobs.Time_Interval
	ELSE IF @ProdEnv = 2
		SELECT
			runningJobs.Time_Interval,
			Running_ProcessCRSFileBDE,
			Throttled_ProcessCRSFileBDE,
			Running_OperaDataLoad,
			Throttled_OperaDataLoad,
			Running_NGIDeferredDeliveryJob,
			Throttled_NGIDeferredDeliveryJob,
			Running_ProcessCRSFileCDP,
			Throttled_ProcessCRSFileCDP,
			Running_OperaCdpDataLoad,
			Throttled_OperaCdpDataLoad,
			Running_NGICdpDeferredDeliveryJob,
			Throttled_NGICdpDeferredDeliveryJob,
			Running_PostProcessingJobs,
			Throttled_PostProcessingJobs,
			Running_mn5pg3xdbsw202,
			Throttled_mn5pg3xdbsw202,
			Running_mn5pg3xdbsw203,
			Throttled_mn5pg3xdbsw203,
			Running_mn5pg3xdbsw204,
			Throttled_mn5pg3xdbsw204,
			Running_mn5pg3xdbsw205,
			Throttled_mn5pg3xdbsw205,
			Running_mn5pg3xdbsw206,
			Throttled_mn5pg3xdbsw206,
			Running_mn5pg3xdbsw207,
			Throttled_mn5pg3xdbsw207,
			Running_mn5pg3xdbsw208,
			Throttled_mn5pg3xdbsw208,
			Running_mn5pg3xdbsw209,
			Throttled_mn5pg3xdbsw209,
			Running_mn5pg3xdbsw210,
			Throttled_mn5pg3xdbsw210,
			Running_mn5pg3xdbsw211,
			Throttled_mn5pg3xdbsw211,
			Running_mn5pg3xdbsw212,
			Throttled_mn5pg3xdbsw212,
			Running_mn5pg3xdbsw213,
			Throttled_mn5pg3xdbsw213,
			Running_mn5pg3xdbsw214,
			Throttled_mn5pg3xdbsw214,
			Running_mn5pg3xdbsw215,
			Throttled_mn5pg3xdbsw215,
			Running_mn5pg3xdbsw216,
			Throttled_mn5pg3xdbsw216,
			Running_mn5pg3xdbsw217,
			Throttled_mn5pg3xdbsw217,
			Running_mn5pg3xdbsw218,
			Throttled_mn5pg3xdbsw218,
			Running_mn5pg3xsasw050,
			Throttled_mn5pg3xsasw050,
			Running_mn5pg3xsasw051,
			Throttled_mn5pg3xsasw051,
			Running_mn5pg3xsasw052,
			Throttled_mn5pg3xsasw052,
			Running_mn5pg3xsasw053,
			Throttled_mn5pg3xsasw053,
			Running_mn5pg3xsasw054,
			Throttled_mn5pg3xsasw054,
			Running_mn5pg3xsasw055,
			Throttled_mn5pg3xsasw055,
			Running_mn5pg3xsasw056,
			Throttled_mn5pg3xsasw056,
			Running_mn5pg3xsasw057,
			Throttled_mn5pg3xsasw057,
			Running_mn5pg3xsasw058,
			Throttled_mn5pg3xsasw058,
			Running_mn5pg3xsasw059,
			Throttled_mn5pg3xsasw059,
			Running_mn5pg3xsasw060,
			Throttled_mn5pg3xsasw060,
			Running_mn5pg3xsasw215,
			Throttled_mn5pg3xsasw215,
			Running_mn5pg3xsasw216,
			Throttled_mn5pg3xsasw216,
			Running_mn5pg3xsasw217,
			Throttled_mn5pg3xsasw217,
			Running_mn5pg3xsasw218,
			Throttled_mn5pg3xsasw218
		FROM (
			SELECT
				Time_Interval,
				ISNULL(ProcessCRSFileBDE,0) AS Running_ProcessCRSFileBDE,
				ISNULL(OperaDataLoad,0) AS Running_OperaDataLoad,
				ISNULL(NGIDeferredDeliveryJob,0) AS Running_NGIDeferredDeliveryJob,
				ISNULL(ProcessCRSFileCDP,0) AS Running_ProcessCRSFileCDP,
				ISNULL(OperaCdpDataLoad,0) AS Running_OperaCdpDataLoad,
				ISNULL(NGICdpDeferredDeliveryJob,0) AS Running_NGICdpDeferredDeliveryJob
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					pdp.JOB_NAME,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						js.JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT MAX(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						DB_Server_Name,
						SAS_Server_Name
					FROM Property_Daily_Processing pdp INNER JOIN Input_Processing ip ON pdp.property_daily_processing_id = ip.property_daily_processing_id
					INNER JOIN Input_Processing_Job ipj ON ip.input_processing_id = ipj.input_processing_id
					INNER JOIN job.dbo.Job_State js WITH (ROWLOCK) ON js.job_instance_id = ipj.job_instance_id AND js.JOB_NAME in ('ProcessCRSFileBDE','ProcessCRSFileCDP','OperaDataLoad','OperaCdpDataLoad','NGIDeferredDeliveryJob','NGICdpDeferredDeliveryJob')
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE ip.Input_Type IN ('BDE','CDP')
					AND Processing_Date = CONVERT(date, @ProcessingDate)
				) pdp ON pdp.Processing_Start_Time <= dti.timeInterval AND dti.timeInterval < Job_End_Time
				GROUP BY dti.timeInterval, pdp.Job_Name
			) timeIntervalRunningJobs
			PIVOT (
				SUM(Number_Of_Running_Jobs) for JOB_NAME IN (ProcessCRSFileBDE,OperaDataLoad,NGIDeferredDeliveryJob,ProcessCRSFileCDP,OperaCdpDataLoad,NGICdpDeferredDeliveryJob)
			) AS timeIntervalPivotedByRunningJobs
		) runningJobs LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL(ProcessCRSFileBDE,0) AS Throttled_ProcessCRSFileBDE,
				ISNULL(OperaDataLoad,0) AS Throttled_OperaDataLoad,
				ISNULL(NGIDeferredDeliveryJob,0) AS Throttled_NGIDeferredDeliveryJob,
				ISNULL(ProcessCRSFileCDP,0) AS Throttled_ProcessCRSFileCDP,
				ISNULL(OperaCdpDataLoad,0) AS Throttled_OperaCdpDataLoad,
				ISNULL(NGICdpDeferredDeliveryJob,0) AS Throttled_NGICdpDeferredDeliveryJob
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					pdp.JOB_NAME,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						js.JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT MAX(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						DB_Server_Name,
						SAS_Server_Name
					FROM Property_Daily_Processing pdp INNER JOIN Input_Processing ip ON pdp.property_daily_processing_id = ip.property_daily_processing_id
					INNER JOIN Input_Processing_Job ipj ON ip.input_processing_id = ipj.input_processing_id
					INNER JOIN job.dbo.Job_State js WITH (ROWLOCK) ON js.job_instance_id = ipj.job_instance_id AND js.JOB_NAME in ('ProcessCRSFileBDE','ProcessCRSFileCDP','OperaDataLoad','OperaCdpDataLoad','NGIDeferredDeliveryJob','NGICdpDeferredDeliveryJob')
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE ip.Input_Type IN ('BDE','CDP')
					AND Processing_Date = CONVERT(date, @ProcessingDate)
				) pdp ON pdp.Job_Start_Time <= dti.timeInterval AND dti.timeInterval < Processing_Start_Time
				GROUP BY dti.timeInterval, pdp.Job_Name
			) timeIntervalThrottledJobs
			PIVOT (
				SUM(Number_Of_Running_Jobs) for JOB_NAME IN (ProcessCRSFileBDE,OperaDataLoad,NGIDeferredDeliveryJob,ProcessCRSFileCDP,OperaCdpDataLoad,NGICdpDeferredDeliveryJob)
			) AS timeIntervalPivotedByThrottledJobs
		) throttledJobs ON runningJobs.Time_Interval = throttledJobs.Time_Interval
		 LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL(PostProcessing,0) AS Running_PostProcessingJobs
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					JOB_NAME,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_Post_Processing_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						'PostProcessing' as JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT MAX(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						jiwc.DB_Server_Name,
						jiwc.SAS_Server_Name
					FROM job.dbo.Job_State js
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE js.JOB_NAME in ('BDEPostProcessingJob','BDEPostProcessingIndexRebuildJob')
					AND js.START_TIME between CONVERT(date, @ProcessingDate-1) and CONVERT(date, @ProcessingDate+1)
				) pdp ON pdp.Processing_Start_Time <= dti.timeInterval AND dti.timeInterval < Job_End_Time
				GROUP BY dti.timeInterval, JOB_NAME
			) timeIntervalRunningJobs
			PIVOT (
				SUM(Number_Of_Running_Post_Processing_Jobs) for JOB_NAME IN (PostProcessing)
			) AS timeIntervalPivotedByRunningJobs
		) postProcessingRunningJobs ON runningJobs.Time_Interval = postProcessingRunningJobs.Time_Interval
		LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL(PostProcessing,0) AS Throttled_PostProcessingJobs
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					pdp.JOB_NAME,
					ISNULL(SUM(Jobs), 0) as Number_Of_Throttled_PropertyPostProcessingJobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						'PostProcessing' as JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT MAX(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						DB_Server_Name,
						SAS_Server_Name
					FROM job.dbo.Job_State js
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE js.JOB_NAME in ('BDEPostProcessingJob','BDEPostProcessingIndexRebuildJob')
					AND js.START_TIME between CONVERT(date, @ProcessingDate-1) and CONVERT(date, @ProcessingDate+1)
				) pdp ON pdp.Job_Start_Time <= dti.timeInterval AND dti.timeInterval < Processing_Start_Time
				GROUP BY dti.timeInterval, pdp.Job_Name
			) timeIntervalThrottledJobs
			PIVOT (
				SUM(Number_Of_Throttled_PropertyPostProcessingJobs) for JOB_NAME IN (PostProcessing)
			) AS timeIntervalPivotedByThrottledJobs
		) postProcessingThrottledJobs ON runningJobs.Time_Interval = postProcessingThrottledJobs.Time_Interval
		LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL([mn5pg3xdbsw202.ideasprod.int],0) as Running_mn5pg3xdbsw202,
				ISNULL([mn5pg3xdbsw203.ideasprod.int],0) as Running_mn5pg3xdbsw203,
				ISNULL([mn5pg3xdbsw204.ideasprod.int],0) as Running_mn5pg3xdbsw204,
				ISNULL([mn5pg3xdbsw205.ideasprod.int],0) as Running_mn5pg3xdbsw205,
				ISNULL([mn5pg3xdbsw206.ideasprod.int],0) as Running_mn5pg3xdbsw206,
				ISNULL([mn5pg3xdbsw207.ideasprod.int],0) as Running_mn5pg3xdbsw207,
				ISNULL([mn5pg3xdbsw208.ideasprod.int],0) as Running_mn5pg3xdbsw208,
				ISNULL([mn5pg3xdbsw209.ideasprod.int],0) as Running_mn5pg3xdbsw209,
				ISNULL([mn5pg3xdbsw210.ideasprod.int],0) as Running_mn5pg3xdbsw210,
				ISNULL([mn5pg3xdbsw211.ideasprod.int],0) as Running_mn5pg3xdbsw211,
				ISNULL([mn5pg3xdbsw212.ideasprod.int],0) as Running_mn5pg3xdbsw212,
				ISNULL([mn5pg3xdbsw213.ideasprod.int],0) as Running_mn5pg3xdbsw213,
				ISNULL([mn5pg3xdbsw214.ideasprod.int],0) as Running_mn5pg3xdbsw214,
				ISNULL([mn5pg3xdbsw215.ideasprod.int],0) as Running_mn5pg3xdbsw215,
				ISNULL([mn5pg3xdbsw216.ideasprod.int],0) as Running_mn5pg3xdbsw216,
				ISNULL([mn5pg3xdbsw217.ideasprod.int],0) as Running_mn5pg3xdbsw217,
				ISNULL([mn5pg3xdbsw218.ideasprod.int],0) as Running_mn5pg3xdbsw218
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					DB_Server_Name,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT MAX(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						jiwc.DB_Server_Name,
						jiwc.SAS_Server_Name
					FROM job.dbo.Job_State js
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE js.JOB_NAME in ('ProcessCRSFile','ProcessCRSFileBDE','ProcessCRSFileCDP','OperaDataLoad','OperaCdpDataLoad','NGIDeferredDeliveryJob','NGICdpDeferredDeliveryJob','BDEPostProcessingJob','BDEPostProcessingIndexRebuildJob','PropertyPostProcessingJob','CrsCatchup','OperaCatchupJob','NGICatchupJob')
					AND js.START_TIME between CONVERT(date, @ProcessingDate-1) and CONVERT(date, @ProcessingDate+1)
				) pdp ON pdp.Processing_Start_Time <= dti.timeInterval AND dti.timeInterval < Job_End_Time
				GROUP BY dti.timeInterval, pdp.DB_Server_Name
			) timeIntervalRunningJobsByDBServer
			PIVOT (
				SUM(Number_Of_Running_Jobs) for DB_Server_Name IN ([mn5pg3xdbsw202.ideasprod.int], [mn5pg3xdbsw203.ideasprod.int], [mn5pg3xdbsw204.ideasprod.int], [mn5pg3xdbsw205.ideasprod.int], [mn5pg3xdbsw206.ideasprod.int], [mn5pg3xdbsw207.ideasprod.int],[mn5pg3xdbsw208.ideasprod.int], [mn5pg3xdbsw209.ideasprod.int], [mn5pg3xdbsw210.ideasprod.int], [mn5pg3xdbsw211.ideasprod.int], [mn5pg3xdbsw212.ideasprod.int], [mn5pg3xdbsw213.ideasprod.int], [mn5pg3xdbsw214.ideasprod.int], [mn5pg3xdbsw215.ideasprod.int], [mn5pg3xdbsw216.ideasprod.int], [mn5pg3xdbsw217.ideasprod.int], [mn5pg3xdbsw218.ideasprod.int])
			) AS timeIntervalPivotedByRunningJobsByDBServer
		) runningJobsByDBServer ON runningJobs.Time_Interval = runningJobsByDBServer.Time_Interval
		LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL([mn5pg3xdbsw202.ideasprod.int],0) as Throttled_mn5pg3xdbsw202,
				ISNULL([mn5pg3xdbsw203.ideasprod.int],0) as Throttled_mn5pg3xdbsw203,
				ISNULL([mn5pg3xdbsw204.ideasprod.int],0) as Throttled_mn5pg3xdbsw204,
				ISNULL([mn5pg3xdbsw205.ideasprod.int],0) as Throttled_mn5pg3xdbsw205,
				ISNULL([mn5pg3xdbsw206.ideasprod.int],0) as Throttled_mn5pg3xdbsw206,
				ISNULL([mn5pg3xdbsw207.ideasprod.int],0) as Throttled_mn5pg3xdbsw207,
				ISNULL([mn5pg3xdbsw208.ideasprod.int],0) as Throttled_mn5pg3xdbsw208,
				ISNULL([mn5pg3xdbsw209.ideasprod.int],0) as Throttled_mn5pg3xdbsw209,
				ISNULL([mn5pg3xdbsw210.ideasprod.int],0) as Throttled_mn5pg3xdbsw210,
				ISNULL([mn5pg3xdbsw211.ideasprod.int],0) as Throttled_mn5pg3xdbsw211,
				ISNULL([mn5pg3xdbsw212.ideasprod.int],0) as Throttled_mn5pg3xdbsw212,
				ISNULL([mn5pg3xdbsw213.ideasprod.int],0) as Throttled_mn5pg3xdbsw213,
				ISNULL([mn5pg3xdbsw214.ideasprod.int],0) as Throttled_mn5pg3xdbsw214,
				ISNULL([mn5pg3xdbsw215.ideasprod.int],0) as Throttled_mn5pg3xdbsw215,
				ISNULL([mn5pg3xdbsw216.ideasprod.int],0) as Throttled_mn5pg3xdbsw216,
				ISNULL([mn5pg3xdbsw217.ideasprod.int],0) as Throttled_mn5pg3xdbsw217,
				ISNULL([mn5pg3xdbsw218.ideasprod.int],0) as Throttled_mn5pg3xdbsw218
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					DB_Server_Name,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT MAX(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						DB_Server_Name,
						SAS_Server_Name
					FROM job.dbo.Job_State js
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE js.JOB_NAME in ('ProcessCRSFile','ProcessCRSFileBDE','ProcessCRSFileCDP','OperaDataLoad','OperaCdpDataLoad','NGIDeferredDeliveryJob','NGICdpDeferredDeliveryJob','BDEPostProcessingJob','BDEPostProcessingIndexRebuildJob','PropertyPostProcessingJob','CrsCatchup','OperaCatchupJob','NGICatchupJob')
					AND js.START_TIME between CONVERT(date, @ProcessingDate-1) and CONVERT(date, @ProcessingDate+1)
				) pdp ON pdp.Job_Start_Time <= dti.timeInterval AND dti.timeInterval < Processing_Start_Time
				GROUP BY dti.timeInterval, pdp.DB_Server_Name
			) timeIntervalThrottledJobsByDBServer
			PIVOT (
				SUM(Number_Of_Running_Jobs) for DB_Server_Name IN ([mn5pg3xdbsw202.ideasprod.int], [mn5pg3xdbsw203.ideasprod.int], [mn5pg3xdbsw204.ideasprod.int], [mn5pg3xdbsw205.ideasprod.int], [mn5pg3xdbsw206.ideasprod.int], [mn5pg3xdbsw207.ideasprod.int],[mn5pg3xdbsw208.ideasprod.int], [mn5pg3xdbsw209.ideasprod.int], [mn5pg3xdbsw210.ideasprod.int], [mn5pg3xdbsw211.ideasprod.int], [mn5pg3xdbsw212.ideasprod.int], [mn5pg3xdbsw213.ideasprod.int], [mn5pg3xdbsw214.ideasprod.int], [mn5pg3xdbsw215.ideasprod.int], [mn5pg3xdbsw216.ideasprod.int], [mn5pg3xdbsw217.ideasprod.int], [mn5pg3xdbsw218.ideasprod.int])
			) AS timeIntervalPivotedByThrottledJobsByDBServer
		) throttledJobsByDBServer ON runningJobs.Time_Interval = throttledJobsByDBServer.Time_Interval
		LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL([mn5pg3xsasw050.ideasprod.int],0) as Running_mn5pg3xsasw050,
				ISNULL([mn5pg3xsasw051.ideasprod.int],0) as Running_mn5pg3xsasw051,
				ISNULL([mn5pg3xsasw052.ideasprod.int],0) as Running_mn5pg3xsasw052,
				ISNULL([mn5pg3xsasw053.ideasprod.int],0) as Running_mn5pg3xsasw053,
				ISNULL([mn5pg3xsasw054.ideasprod.int],0) as Running_mn5pg3xsasw054,
				ISNULL([mn5pg3xsasw055.ideasprod.int],0) as Running_mn5pg3xsasw055,
				ISNULL([mn5pg3xsasw056.ideasprod.int],0) as Running_mn5pg3xsasw056,
				ISNULL([mn5pg3xsasw057.ideasprod.int],0) as Running_mn5pg3xsasw057,
				ISNULL([mn5pg3xsasw058.ideasprod.int],0) as Running_mn5pg3xsasw058,
				ISNULL([mn5pg3xsasw059.ideasprod.int],0) as Running_mn5pg3xsasw059,
				ISNULL([mn5pg3xsasw060.ideasprod.int],0) as Running_mn5pg3xsasw060,
				ISNULL([mn5pg3xsasw215.ideasprod.int],0) as Running_mn5pg3xsasw215,
				ISNULL([mn5pg3xsasw216.ideasprod.int],0) as Running_mn5pg3xsasw216,
				ISNULL([mn5pg3xsasw217.ideasprod.int],0) as Running_mn5pg3xsasw217,
				ISNULL([mn5pg3xsasw218.ideasprod.int],0) as Running_mn5pg3xsasw218
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					SAS_Server_Name,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT MAX(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						DB_Server_Name,
						SAS_Server_Name
					FROM job.dbo.Job_State js
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE js.JOB_NAME in ('ProcessCRSFile','ProcessCRSFileBDE','ProcessCRSFileCDP','OperaDataLoad','OperaCdpDataLoad','NGIDeferredDeliveryJob','NGICdpDeferredDeliveryJob','CrsCatchup','OperaCatchupJob','NGICatchupJob')
					AND js.START_TIME between CONVERT(date, @ProcessingDate-1) and CONVERT(date, @ProcessingDate+1)
				) pdp ON pdp.Processing_Start_Time <= dti.timeInterval AND dti.timeInterval < Job_End_Time
				GROUP BY dti.timeInterval, pdp.SAS_Server_Name
			) timeIntervalRunningJobsBySASServer
			PIVOT (
				SUM(Number_Of_Running_Jobs) for SAS_Server_Name IN ([mn5pg3xsasw050.ideasprod.int], [mn5pg3xsasw051.ideasprod.int], [mn5pg3xsasw052.ideasprod.int], [mn5pg3xsasw053.ideasprod.int], [mn5pg3xsasw054.ideasprod.int], [mn5pg3xsasw055.ideasprod.int], [mn5pg3xsasw056.ideasprod.int], [mn5pg3xsasw057.ideasprod.int], [mn5pg3xsasw058.ideasprod.int], [mn5pg3xsasw059.ideasprod.int], [mn5pg3xsasw060.ideasprod.int], [mn5pg3xsasw215.ideasprod.int], [mn5pg3xsasw216.ideasprod.int], [mn5pg3xsasw217.ideasprod.int], [mn5pg3xsasw218.ideasprod.int])
			) AS timeIntervalPivotedByRunningJobsByDBServer
		) runningJobsBySASServer ON runningJobs.Time_Interval = runningJobsBySASServer.Time_Interval
		LEFT JOIN (
			SELECT
				Time_Interval,
				ISNULL([mn5pg3xsasw050.ideasprod.int],0) as Throttled_mn5pg3xsasw050,
				ISNULL([mn5pg3xsasw051.ideasprod.int],0) as Throttled_mn5pg3xsasw051,
				ISNULL([mn5pg3xsasw052.ideasprod.int],0) as Throttled_mn5pg3xsasw052,
				ISNULL([mn5pg3xsasw053.ideasprod.int],0) as Throttled_mn5pg3xsasw053,
				ISNULL([mn5pg3xsasw054.ideasprod.int],0) as Throttled_mn5pg3xsasw054,
				ISNULL([mn5pg3xsasw055.ideasprod.int],0) as Throttled_mn5pg3xsasw055,
				ISNULL([mn5pg3xsasw056.ideasprod.int],0) as Throttled_mn5pg3xsasw056,
				ISNULL([mn5pg3xsasw057.ideasprod.int],0) as Throttled_mn5pg3xsasw057,
				ISNULL([mn5pg3xsasw058.ideasprod.int],0) as Throttled_mn5pg3xsasw058,
				ISNULL([mn5pg3xsasw059.ideasprod.int],0) as Throttled_mn5pg3xsasw059,
				ISNULL([mn5pg3xsasw060.ideasprod.int],0) as Throttled_mn5pg3xsasw060,
				ISNULL([mn5pg3xsasw215.ideasprod.int],0) as Throttled_mn5pg3xsasw215,
				ISNULL([mn5pg3xsasw216.ideasprod.int],0) as Throttled_mn5pg3xsasw216,
				ISNULL([mn5pg3xsasw217.ideasprod.int],0) as Throttled_mn5pg3xsasw217,
				ISNULL([mn5pg3xsasw218.ideasprod.int],0) as Throttled_mn5pg3xsasw218
			FROM (
				SELECT
					dti.timeInterval as Time_Interval,
					SAS_Server_Name,
					ISNULL(SUM(Jobs), 0) as Number_Of_Running_Jobs
				FROM #DateTime_Intervals dti
				LEFT JOIN (
					SELECT
						JOB_NAME,
						js.START_TIME Job_Start_Time,
						(SELECT MAX(END_TIME) FROM job.dbo.Step_Execution se  WITH (ROWLOCK) WHERE se.Job_Execution_ID IN (SELECT Job_Execution_ID FROM job.dbo.Job_Execution je  WITH (ROWLOCK) WHERE je.JOB_INSTANCE_ID = js.job_instance_id AND UPPER(STEP_NAME) like '%ACQUIREREGULATOR%')) AS Processing_Start_Time,
						js.END_TIME Job_End_Time,
						1 Jobs,
						DB_Server_Name,
						SAS_Server_Name
					FROM job.dbo.Job_State js
					INNER JOIN job.dbo.Job_Instance_Work_Context jiwc ON js.job_instance_id = jiwc.job_instance_id
					WHERE js.JOB_NAME in ('ProcessCRSFile','ProcessCRSFileBDE','ProcessCRSFileCDP','OperaDataLoad','OperaCdpDataLoad','NGIDeferredDeliveryJob','NGICdpDeferredDeliveryJob','CrsCatchup','OperaCatchupJob','NGICatchupJob')
					AND js.START_TIME between CONVERT(date, @ProcessingDate-1) and CONVERT(date, @ProcessingDate+1)
				) pdp ON pdp.Job_Start_Time <= dti.timeInterval AND dti.timeInterval < Processing_Start_Time
				GROUP BY dti.timeInterval, pdp.SAS_Server_Name
			) timeIntervalThrottledJobsBySASServer
			PIVOT (
				SUM(Number_Of_Running_Jobs) for SAS_Server_Name IN ([mn5pg3xsasw050.ideasprod.int], [mn5pg3xsasw051.ideasprod.int], [mn5pg3xsasw052.ideasprod.int], [mn5pg3xsasw053.ideasprod.int], [mn5pg3xsasw054.ideasprod.int], [mn5pg3xsasw055.ideasprod.int], [mn5pg3xsasw056.ideasprod.int], [mn5pg3xsasw057.ideasprod.int], [mn5pg3xsasw058.ideasprod.int], [mn5pg3xsasw059.ideasprod.int], [mn5pg3xsasw060.ideasprod.int], [mn5pg3xsasw215.ideasprod.int], [mn5pg3xsasw216.ideasprod.int], [mn5pg3xsasw217.ideasprod.int], [mn5pg3xsasw218.ideasprod.int])
			) AS timeIntervalPivotedByThrottledJobsBySASServer
		) throttledJobsBySASServer ON runningJobs.Time_Interval = throttledJobsBySASServer.Time_Interval
		ORDER BY runningJobs.Time_Interval
RETURN
END

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************

Procedure Name: usp_system_activity_report

Input Parameters : 
	@user_id --> user ids for which we need system activity
	@start_date --> activity_start_date ('2011-07-01')
	@end_date --> activity_end_date ('2011-07-31')
	
Ouput Parameter : NA

Execution: this is just an example
	Example :
	------------------------------------------------------
	exec dbo.usp_system_activity_report '11403,20227','2011-08-12','2011-10-25' 
	
	
Purpose: The purpose of this procedure is to report Activity log report for selected list of users

Assumptions : Please make sure to pass user_id as a string ('11403,20227').
		 
Author: Atul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
10/15/2011	Atul				Shendye					Initial Version
10/15/2011	Vinay				Patil					Version 01
***************************************************************************************/
create procedure [dbo].[usp_system_activity_report]
(
		@user_id varchar(max),
		@start_date date,
		@end_date date,
		@loggedInUser int,
		@propertyId int,
		@isRollingDate int,
		@rolling_start_date nvarchar(50),
		@rolling_end_date nvarchar(50)	
) 
as

begin
		SET NOCOUNT ON
		declare @today date 
		set @today = (select cast(GETDATE() as DATE))

		if(@isRollingDate=1)
		begin
			set @start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_start_date ,@today))
			set @end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_end_date ,@today))
		end

		--get user is internal or external and client_code
		declare @client_code varchar(10)
		declare @isInternalUser int 
		
		set @client_code = (select client_code from client where Client_ID in (select Client_ID from Property where Property_ID =  @propertyID))
		set @isInternalUser = (Select Internal From users  where USER_ID = @loggedInUser)
		
		-- create table variable for userId
		declare @temp_UserIdTable table
		(
			userID int
		)		
	
		if(@user_id='-1')
		begin
			if(@isInternalUser=1)
				begin
					insert into @temp_UserIdTable
						select user_id from users 
						where client_code in ('ideas',@client_code) and Status_ID = 1 
				end
			else
				begin
					insert into @temp_UserIdTable
						select user_id from users 
						where client_code in (@client_code) and Status_ID = 1
				end
		end
		else 
			begin
				insert into @temp_UserIdTable
					SELECT Value FROM ufn_varchar_max_to_int(@user_id,',')		
			end		
		
		--- extract report metrics
		
		select isnull(USER_NAME,'-') as USER_NAME,isnull(Property_Name,'-') as Property_Name ,
		isnull(Property_Group_Name,'-') as Property_Group_Name,
		DASHBOARD_visits,DASHBOARD_mins,DASHBOARD_average,lastUsed,
		DEMAND_AND_WASH_visits,DEMAND_AND_WASH_mins,DEMAND_AND_WASH_average,
		OVERBOOKING_visits,OVERBOOKING_mins,OVERBOOKING_average,
		PRICING_visits,PRICING_mins,PRICING_average,
		INFORMATION_MANAGER_visits,INFORMATION_MANAGER_mins,INFORMATION_MANAGER_average,
		BUSINESS_ANALYSIS_DASHBOARD_visits,BUSINESS_ANALYSIS_DASHBOARD_mins,BUSINESS_ANALYSIS_DASHBOARD_average,
		0 as SPECIAL_EVENTS_No_Of_visits,0 as SPECIAL_EVENTS_System_Usage,0 as SPECIAL_EVENTS_AvgDuration,
        0 as PRICE_STRATEGY_No_Of_visits,0 as PRICE_STRATEGY_System_Usage,0 as PRICE_STRATEGY_AvgDuration,
        0 as PROPERTY_ATTRIBUTES_No_Of_visits,0 as PROPERTY_ATTRIBUTES_System_Usage,0 as PROPERTY_ATTRIBUTES_AvgDuration,
       	0 as PROPERTY_ATTRIBUTE_ASSIGNMENTS_No_Of_visits,0 as PROPERTY_ATTRIBUTE_ASSIGNMENTS_System_Usage,
             0 as PROPERTY_ATTRIBUTE_ASSIGNMENTS_AvgDuration,
             0 as PROPERTY_GROUPS_No_Of_visits,
             0 as PROPERTY_GROUPS_System_Usage,
             0 as PROPERTY_GROUPS_AvgDuration,
             0 as CORPORATE_BUSINESS_VIEWS_No_Of_visits,
             0 as CORPORATE_BUSINESS_VIEWS_System_Usage,
             0 as CORPORATE_BUSINESS_VIEWS_AvgDuration,
             0 as ROOM_CLASS_CONFIGURATION_No_Of_visits,
             0 as ROOM_CLASS_CONFIGURATION_System_Usage,
             0 as ROOM_CLASS_CONFIGURATION_AvgDuration,
             0 as RATE_SHOPPING_CONFIGURATION_No_Of_visits,
             0 as RATE_SHOPPING_CONFIGURATION_System_Usage,
             0 as RATE_SHOPPING_CONFIGURATION_AvgDuration,
             0 as CLIENTS_No_Of_visits,
             0 as CLIENTS_System_Usage,
             0 as CLIENTS_AvgDuration,
             0 as PROPERTIES_No_Of_visits,
             0 as PROPERTIES_System_Usage,
             0 as PROPERTIES_AvgDuration,
             0 as MARKET_SEGMENTS_No_Of_visits,
             0 as MARKET_SEGMENTS_System_Usage,
             0 as MARKET_SEGMENTS_AvgDuration,
             0 as OVERBOOKING_CONFIGURATION_No_Of_visits,
             0 as OVERBOOKING_CONFIGURATION_System_Usage,
             0 as OVERBOOKING_CONFIGURATION_AvgDuration,
             0 as AUTHORIZATION_GROUPS_No_Of_visits,
             0 as AUTHORIZATION_GROUPS_System_Usage,
             0 as AUTHORIZATION_GROUPS_AvgDuration,
             0 as PROPERTY_AUTHORIZATION_No_Of_visits,
             0 as PROPERTY_AUTHORIZATION_System_Usage,
             0 as PROPERTY_AUTHORIZATION_AvgDuration,
			 GROUP_PRICING_EVALUATION_visits,
             GROUP_PRICING_EVALUATION_mins,
             GROUP_PRICING_EVALUATION_average,
			 GROUP_PRICING_EVALUATION_generate_count,
			 Email_Address,
			 Property_Code
		from 
		(
			select Property_Id,Property_Group_ID,USER_ID,
				isnull(MAX(DASHBOARD_visits),0) DASHBOARD_visits,
				isnull(MAX(DASHBOARD_mins),0) DASHBOARD_mins,
				isnull(MAX(DASHBOARD_average),0) DASHBOARD_average,
				lastUsed =
					case cast(isnull(MAX(lastUsed),'1971-01-01') as date) 
					when '1971-01-01' then '---'
					else CAST ( cast(isnull(MAX(lastUsed),'1971-01-01')as date) as varchar)
					end,
					
				isnull(MAX(DEMAND_AND_WASH_visits), case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) DEMAND_AND_WASH_visits,
				isnull(MAX(DEMAND_AND_WASH_mins),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end  ) DEMAND_AND_WASH_mins,
				isnull(MAX(DEMAND_AND_WASH_average),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) DEMAND_AND_WASH_average,
				
				isnull(MAX(OVERBOOKING_visits),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) OVERBOOKING_visits,
				isnull(MAX(OVERBOOKING_mins),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end  ) OVERBOOKING_mins,
				isnull(MAX(OVERBOOKING_average),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end  ) OVERBOOKING_average,
	
				isnull(MAX(PRICING_visits),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) PRICING_visits,
				isnull(MAX(PRICING_mins),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) PRICING_mins,
				isnull(MAX(PRICING_average),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) PRICING_average,
				
				isnull(MAX(INFORMATION_MANAGER_visits),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) INFORMATION_MANAGER_visits,
				isnull(MAX(INFORMATION_MANAGER_mins),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) INFORMATION_MANAGER_mins,
				isnull(MAX(INFORMATION_MANAGER_average),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) INFORMATION_MANAGER_average,
				
				isnull(MAX(BUSINESS_ANALYSIS_DASHBOARD_visits),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) BUSINESS_ANALYSIS_DASHBOARD_visits,
				isnull(MAX(BUSINESS_ANALYSIS_DASHBOARD_mins),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) BUSINESS_ANALYSIS_DASHBOARD_mins,
				isnull(MAX(BUSINESS_ANALYSIS_DASHBOARD_average),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) BUSINESS_ANALYSIS_DASHBOARD_average,
				
				isnull(MAX(GROUP_PRICING_EVALUATION_visits),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) GROUP_PRICING_EVALUATION_visits,
				isnull(MAX(GROUP_PRICING_EVALUATION_mins),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) GROUP_PRICING_EVALUATION_mins,
				isnull(MAX(GROUP_PRICING_EVALUATION_average),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) GROUP_PRICING_EVALUATION_average,
				isnull(MAX(GROUP_PRICING_EVALUATION_generate_count),case isnull(Property_Group_ID,-1) when  -1 then  0 else -1 end ) GROUP_PRICING_EVALUATION_generate_count
		
			from
			(
				select 
					Property_Id,Property_Group_ID,USER_ID,
					DASHBOARD_visits,DASHBOARD_mins,DASHBOARD_average,
					DEMAND_AND_WASH_visits,DEMAND_AND_WASH_mins,DEMAND_AND_WASH_average,
					OVERBOOKING_visits,OVERBOOKING_mins,OVERBOOKING_average,
					PRICING_visits,PRICING_mins,PRICING_average,
					INFORMATION_MANAGER_visits,INFORMATION_MANAGER_mins,INFORMATION_MANAGER_average,
					BUSINESS_ANALYSIS_DASHBOARD_visits,BUSINESS_ANALYSIS_DASHBOARD_mins,BUSINESS_ANALYSIS_DASHBOARD_average,
					GROUP_PRICING_EVALUATION_visits,GROUP_PRICING_EVALUATION_mins,GROUP_PRICING_EVALUATION_average,GROUP_PRICING_EVALUATION_generate_count,
					max(max_date) as lastUsed
				from
				(
					SELECT
							Property_Id,Property_Group_ID,USER_ID,
							(case name when  'DASHBOARD' then  visits  end ) as DASHBOARD_visits,
							(case name when  'DASHBOARD' then  mins  end ) as DASHBOARD_mins,
							(case name when  'DASHBOARD' then  average  end ) as DASHBOARD_average,
							(case name when  'DASHBOARD' then  lastUsed  end ) as DASHBOARD_lastUsed,
							(case name when  'DEMAND_AND_WASH' then  visits  end ) as DEMAND_AND_WASH_visits,
							(case name when  'DEMAND_AND_WASH' then  mins  end ) as DEMAND_AND_WASH_mins,
							(case name when  'DEMAND_AND_WASH' then  average  end ) as DEMAND_AND_WASH_average,
							(case name when  'DEMAND_AND_WASH' then  lastUsed  end ) as DEMAND_AND_WASH_lastUsed,
							(case name when  'OVERBOOKING' then  visits  end ) as OVERBOOKING_visits,
							(case name when  'OVERBOOKING' then  mins  end ) as OVERBOOKING_mins,
							(case name when  'OVERBOOKING' then  average  end ) as OVERBOOKING_average,
							(case name when  'OVERBOOKING' then  lastUsed  end ) as OVERBOOKING_lastUsed,
							(case name when  'PRICING' then  visits  end ) as PRICING_visits,
							(case name when  'PRICING' then  mins  end ) as PRICING_mins,
							(case name when  'PRICING' then  average  end ) as PRICING_average,
							(case name when  'PRICING' then  lastUsed  end ) as PRICING_lastUsed,
							(case name when  'INFORMATION_MANAGER' then  visits  end ) as INFORMATION_MANAGER_visits,
							(case name when  'INFORMATION_MANAGER' then  mins  end ) as INFORMATION_MANAGER_mins,
							(case name when  'INFORMATION_MANAGER' then  average  end ) as INFORMATION_MANAGER_average,
							(case name when  'INFORMATION_MANAGER' then  lastUsed  end ) as INFORMATION_MANAGER_lastUsed,
							(case name when  'BUSINESS_ANALYSIS_DASHBOARD' then  visits  end ) as BUSINESS_ANALYSIS_DASHBOARD_visits,
							(case name when  'BUSINESS_ANALYSIS_DASHBOARD' then  mins  end ) as BUSINESS_ANALYSIS_DASHBOARD_mins,
							(case name when  'BUSINESS_ANALYSIS_DASHBOARD' then  average  end ) as BUSINESS_ANALYSIS_DASHBOARD_average,
							(case name when  'BUSINESS_ANALYSIS_DASHBOARD' then  lastUsed  end ) as BUSINESS_ANALYSIS_DASHBOARD_lastUsed,
							(case name when  'GROUP_PRICING_EVALUATION' then  visits  end ) as GROUP_PRICING_EVALUATION_visits,
							(case name when  'GROUP_PRICING_EVALUATION' then  mins  end ) as GROUP_PRICING_EVALUATION_mins,
							(case name when  'GROUP_PRICING_EVALUATION' then  average  end ) as GROUP_PRICING_EVALUATION_average,
							(case name when  'GROUP_PRICING_EVALUATION' then  lastUsed  end ) as GROUP_PRICING_EVALUATION_lastUsed,
							(case name when  'GROUP_PRICING_EVALUATION' then  generate_count  end ) as GROUP_PRICING_EVALUATION_generate_count
							 FROM 
							 (
								select Property_Id,Property_Group_ID,USER_ID,Name,count(*) visits, 
									sum(mins) mins, AVG(mins) average, MAX(End_DTTM) as lastUsed,
									(select COUNT(*) from System_Usage su inner join System_Usage_Page sup on su.Page_ID=sup.System_Usage_Page_ID and sup.Name=data2.Name where su.Property_ID=data2.Property_Id and su.USER_ID = data2.USER_ID and Start_DTTM >= @start_date and Start_DTTM < DATEADD(day,1,@end_date) and Type_ID = 8) as generate_count 
								from 
								(
									select Property_Id,Property_Group_ID,USER_ID,Name,
											Start_DTTM,End_DTTM,DATEDIFF ( mi, Start_DTTM, End_DTTM ) as mins
									from 
									(
										select * from 
										(
											select * from System_Usage 
												where  
												Start_DTTM >= @start_date and End_DTTM <= DATEADD(day,1,@end_date) 
												and USER_ID in (SELECT userID FROM @temp_UserIdTable) 
												and TYPE_ID = 2
										) SU
										inner join 
										(
											SELECT System_Usage_Page_ID,Name 
											  FROM System_Usage_Page where 
											  Name in ('DASHBOARD','DEMAND_AND_WASH','OVERBOOKING','PRICING',
														'INFORMATION_MANAGER','BUSINESS_ANALYSIS_DASHBOARD','GROUP_PRICING_EVALUATION')
										) SUP on SU.Page_ID = SUP.System_Usage_Page_ID
									)data 
								) data2
								group by Property_Id,Property_Group_ID,USER_ID,Name
							) A
						) SUB 
							  UNPIVOT 
						  (
						  max_date
						  for ndate in (
										DASHBOARD_lastUsed,PRICING_lastUsed,
										OVERBOOKING_lastUsed,DEMAND_AND_WASH_lastUsed,
										INFORMATION_MANAGER_lastUsed,
										BUSINESS_ANALYSIS_DASHBOARD_lastUsed,
										GROUP_PRICING_EVALUATION_lastUsed
										)
						  ) as unpvt
						  group by Property_Id,Property_Group_ID,USER_ID,
								DASHBOARD_visits,DASHBOARD_mins,DASHBOARD_average,
								DEMAND_AND_WASH_visits,DEMAND_AND_WASH_mins,DEMAND_AND_WASH_average,
								OVERBOOKING_visits,OVERBOOKING_mins,OVERBOOKING_average,
								PRICING_visits,PRICING_mins,PRICING_average,
								INFORMATION_MANAGER_visits,INFORMATION_MANAGER_mins,INFORMATION_MANAGER_average,
								BUSINESS_ANALYSIS_DASHBOARD_visits,BUSINESS_ANALYSIS_DASHBOARD_mins,BUSINESS_ANALYSIS_DASHBOARD_average,
								GROUP_PRICING_EVALUATION_visits,GROUP_PRICING_EVALUATION_mins,GROUP_PRICING_EVALUATION_average,GROUP_PRICING_EVALUATION_generate_count
					) as B group by Property_Id,Property_Group_ID,USER_ID
		) as C 
		inner join
			Users U on C.User_ID=U.User_ID
		left join
			Property P on C.Property_ID=P.Property_ID
		left join Property_Group PG on C.Property_Group_ID=PG.Property_Group_ID		
		order by USER_NAME
		
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************

Procedure Name: usp_system_activity_report_by_roles_property_users

Input Parameters :
	@user_id --> user ids for which we need system activity
	@start_date --> activity_start_date ('2011-07-01')
	@end_date --> activity_end_date ('2011-07-31')

Ouput Parameter : NA

Execution: this is just an example
	Example :
	------------------------------------------------------
	exec dbo.usp_system_activity_report_by_roles_property_users '11403,20227','2011-08-12','2011-10-25'


Purpose: The purpose of this procedure is to report Activity log report for selected list of users

Assumptions : Please make sure to pass user_id as a string ('11403,20227').

Author: Rahul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
17/12/2017	Rahul				Chavan					Initial Version
04/16/2018      Shirishkumar        Bari                    Changes related to split function
***************************************************************************************/

CREATE procedure [dbo].[usp_system_activity_report_by_roles_property_users]
(
		@user_id varchar(max),
		@propertyOrGroupIDs varchar(max),
		@roleIDs varchar(max),
		@start_date date,
		@end_date date,
		@loggedInUser int,
		@propertyId int,
		@isPropertyGroup int,
		@isRollingDate int,
		@rolling_start_date nvarchar(50),
		@rolling_end_date nvarchar(50)
)
as

begin
		SET NOCOUNT ON
		declare @today date

		set @today = (select cast(GETDATE() as DATE))

		if(@isRollingDate=1)
		begin
			set @start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_start_date ,@today))
			set @end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_end_date ,@today))
		end



	declare @tempSystemUsage table(
	[System_Usage_ID] [int] NOT NULL,
	[Property_ID] [int] NULL,
	[Property_Group_ID] [int] NULL,
	[User_ID] [int] NOT NULL,
	[Page_ID] [int] NULL,
	[Type_ID] [int] NOT NULL,
	[Details] [nvarchar](255) NULL,
	[Start_DTTM] [datetime] NOT NULL,
	[End_DTTM] [datetime] NULL
)

		-- User Table for Filter
			declare @usersTable table
		(
			[User_ID] [int] NOT NULL,
			[Client_Code] [nvarchar](50) NOT NULL,
			primary key ([User_ID])
		)


		--get user is internal or external and client_code
		declare @client_code varchar(10)
		Declare @client_id int
		declare @isInternalUser int
		Declare @isProperty int = 0

		set @client_code = (select client_code from client where Client_ID in (select Client_ID from Property where Property_ID =  @propertyID))
		set @isInternalUser = (Select Internal From users  where USER_ID = @loggedInUser)
		set @client_id = (select client_id from Client where Client_Code = @client_code)
		-- create table variable for userId
		declare @temp_UserIdTable table
		(
			userID int
		)

			if(@isInternalUser=1)
			begin
				insert into @usersTable
					select [User_ID],[Client_Code]
					from Users
					where
					Client_Code in (@client_code,'ideas')
					and Status_ID = 1
					and Internal in (@isInternalUser, 0)
			end
		else
			begin
				insert into @usersTable
					select [User_ID],[Client_Code]
					from Users
					where
					Client_Code in (@client_code)
					and Status_ID = 1
					and Internal in (0)
			end
			------

		-- get all selected role ids

		IF OBJECT_ID('tempdb..#tempTable_ForRoleFilter') IS NOT NULL
		BEGIN
			DROP TABLE #tempTable_ForRoleFilter
		END
		CREATE TABLE #tempTable_ForRoleFilter
		(
			USER_ID int
		)
		Declare @isRole int  = 0
		Declare @RoleIDTable table
		(
			roleID varchar(100)
		)

		if (len(@roleIDs)>0 and @roleIDs<>'-1')
			begin
				insert into @RoleIDTable select items from split(@roleIDs,',')

				set @isRole = 1
			end
		else if (@roleIDs='-1'and @isInternalUser<>1 )
			begin

			INSERT INTO #tempTable_ForRoleFilter
				select distinct a.USER_ID from
					User_Auth_Group_Role a
					inner join
					@usersTable b  on a.User_ID = b.User_ID and b.Client_Code = @client_code
				union
				select distinct USER_ID from
					dbo.User_Individual_Property_Role c
					inner join
					Property d  on c.Property_ID = d.Property_ID and d.Client_ID = @client_id

				set @isRole = 1
		end

		ELSE
		BEGIN
		    INSERT INTO #tempTable_ForRoleFilter
				select distinct a.USER_ID from
					User_Auth_Group_Role a
					inner join
					@usersTable b  on a.User_ID = b.User_ID and b.Client_Code in(@client_code,'ideas')
				union
				select distinct USER_ID from
					dbo.User_Individual_Property_Role c
					inner join
					Property d  on c.Property_ID = d.Property_ID and d.Client_ID = @client_id

				set @isRole = 1
		end


		if(@isRole=1)
		begin
			if(@roleIDs<>'-1')
			begin
				insert into #tempTable_ForRoleFilter
					select b.USER_ID from @RoleIDTable a
						inner join User_Individual_Property_Role b on a.roleID = b.Role_ID
					UNION
					select c.User_ID from @RoleIDTable a
						inner join User_Auth_Group_Role c on a.roleID = c.Role_ID
			end
		end

			------------------
			-- get all selected property ids
		Declare @propertyIDTable table
		(
			propertyID int
		)

if (len(@propertyOrGroupIDs)>0 and @propertyOrGroupIDs<>'-1')
		begin
			insert into @propertyIDTable
				select * from ufn_varchar_max_to_int (@propertyOrGroupIDs,',')

		end
		else if(@isPropertyGroup<>'1')
			BEGIN
			  insert into @propertyIDTable
				select p.Property_ID from
				property as p where p.Client_ID=@client_id and Status_ID =1

			END

		else
		BEGIN
			  insert into @propertyIDTable
				select p.Property_Group_ID from
				Property_Group as p where p.Client_ID=@client_id

		END



		---------------

		if(@user_id='-1')
		begin
			if(@isInternalUser=1)
				begin
					insert into @temp_UserIdTable
						select user_id from users
						where client_code in ('ideas',@client_code) and Status_ID = 1
				end
			else
				begin
					insert into @temp_UserIdTable
						select user_id from users
						where client_code in (@client_code) and Status_ID = 1
				end
		end
		else
			begin
				insert into @temp_UserIdTable
					SELECT Value FROM ufn_varchar_max_to_int(@user_id,',')
			end

		--- extract report metrics

		if(@isPropertyGroup = '1')
		begin
		insert into @propertyIDTable values(null)
		insert into @tempSystemUsage select * from System_Usage
		where Property_Group_ID in (select * from @propertyIDTable)
		or Property_ID in (Select ppg.Property_ID from Property_Property_Group as ppg inner join @propertyIDTable as pt on pt.propertyID = ppg.Property_Group_ID)
		end
		else
		begin
		insert into @tempSystemUsage select * from System_Usage
		where Property_ID in (select * from @propertyIDTable)
		end
		-----------------


		select isnull(USER_NAME,'-') as USER_NAME,isnull(Property_Name,'-') as Property_Name ,
		isnull(Property_Group_Name,'-') as Property_Group_Name,
		DASHBOARD_visits,DASHBOARD_mins,DASHBOARD_average,lastUsed,
		DEMAND_AND_WASH_visits,DEMAND_AND_WASH_mins,DEMAND_AND_WASH_average,
		OVERBOOKING_visits,OVERBOOKING_mins,OVERBOOKING_average,
		PRICING_visits,PRICING_mins,PRICING_average,
		INFORMATION_MANAGER_visits,INFORMATION_MANAGER_mins,INFORMATION_MANAGER_average,
		BUSINESS_ANALYSIS_DASHBOARD_visits,BUSINESS_ANALYSIS_DASHBOARD_mins,BUSINESS_ANALYSIS_DASHBOARD_average,
		0 as SPECIAL_EVENTS_No_Of_visits,0 as SPECIAL_EVENTS_System_Usage,0 as SPECIAL_EVENTS_AvgDuration,
        0 as PRICE_STRATEGY_No_Of_visits,0 as PRICE_STRATEGY_System_Usage,0 as PRICE_STRATEGY_AvgDuration,
        0 as PROPERTY_ATTRIBUTES_No_Of_visits,0 as PROPERTY_ATTRIBUTES_System_Usage,0 as PROPERTY_ATTRIBUTES_AvgDuration,
       	0 as PROPERTY_ATTRIBUTE_ASSIGNMENTS_No_Of_visits,0 as PROPERTY_ATTRIBUTE_ASSIGNMENTS_System_Usage,
             0 as PROPERTY_ATTRIBUTE_ASSIGNMENTS_AvgDuration,
             0 as PROPERTY_GROUPS_No_Of_visits,
             0 as PROPERTY_GROUPS_System_Usage,
             0 as PROPERTY_GROUPS_AvgDuration,
             0 as CORPORATE_BUSINESS_VIEWS_No_Of_visits,
             0 as CORPORATE_BUSINESS_VIEWS_System_Usage,
             0 as CORPORATE_BUSINESS_VIEWS_AvgDuration,
             0 as ROOM_CLASS_CONFIGURATION_No_Of_visits,
             0 as ROOM_CLASS_CONFIGURATION_System_Usage,
             0 as ROOM_CLASS_CONFIGURATION_AvgDuration,
             0 as RATE_SHOPPING_CONFIGURATION_No_Of_visits,
             0 as RATE_SHOPPING_CONFIGURATION_System_Usage,
             0 as RATE_SHOPPING_CONFIGURATION_AvgDuration,
             0 as CLIENTS_No_Of_visits,
             0 as CLIENTS_System_Usage,
             0 as CLIENTS_AvgDuration,
             0 as PROPERTIES_No_Of_visits,
             0 as PROPERTIES_System_Usage,
             0 as PROPERTIES_AvgDuration,
             0 as MARKET_SEGMENTS_No_Of_visits,
             0 as MARKET_SEGMENTS_System_Usage,
             0 as MARKET_SEGMENTS_AvgDuration,
             0 as OVERBOOKING_CONFIGURATION_No_Of_visits,
             0 as OVERBOOKING_CONFIGURATION_System_Usage,
             0 as OVERBOOKING_CONFIGURATION_AvgDuration,
             0 as AUTHORIZATION_GROUPS_No_Of_visits,
             0 as AUTHORIZATION_GROUPS_System_Usage,
             0 as AUTHORIZATION_GROUPS_AvgDuration,
             0 as PROPERTY_AUTHORIZATION_No_Of_visits,
             0 as PROPERTY_AUTHORIZATION_System_Usage,
             0 as PROPERTY_AUTHORIZATION_AvgDuration,
			 GROUP_PRICING_EVALUATION_visits,
             GROUP_PRICING_EVALUATION_mins,
             GROUP_PRICING_EVALUATION_average,
			 GROUP_PRICING_EVALUATION_generate_count,
			 Email_Address,
			 Property_Code
		from
		(
			select Property_Id,Property_Group_ID,USER_ID,
				isnull(MAX(DASHBOARD_visits),0) DASHBOARD_visits,
				isnull(MAX(DASHBOARD_mins),0) DASHBOARD_mins,
				isnull(MAX(DASHBOARD_average),0) DASHBOARD_average,
				lastUsed =
					case cast(isnull(MAX(lastUsed),'1971-01-01') as date)
					when '1971-01-01' then '---'
					else CAST ( cast(isnull(MAX(lastUsed),'1971-01-01')as date) as varchar)
					end,

				isnull(MAX(DEMAND_AND_WASH_visits), 0 ) DEMAND_AND_WASH_visits,
				isnull(MAX(DEMAND_AND_WASH_mins),0  ) DEMAND_AND_WASH_mins,
				isnull(MAX(DEMAND_AND_WASH_average),0 ) DEMAND_AND_WASH_average,

				isnull(MAX(OVERBOOKING_visits),0) OVERBOOKING_visits,
				isnull(MAX(OVERBOOKING_mins), 0) OVERBOOKING_mins,
				isnull(MAX(OVERBOOKING_average),0 ) OVERBOOKING_average,

				isnull(MAX(PRICING_visits), 0) PRICING_visits,
				isnull(MAX(PRICING_mins),0) PRICING_mins,
				isnull(MAX(PRICING_average), 0) PRICING_average,

				isnull(MAX(INFORMATION_MANAGER_visits), 0) INFORMATION_MANAGER_visits,
				isnull(MAX(INFORMATION_MANAGER_mins), 0) INFORMATION_MANAGER_mins,
				isnull(MAX(INFORMATION_MANAGER_average),0) INFORMATION_MANAGER_average,

				isnull(MAX(BUSINESS_ANALYSIS_DASHBOARD_visits), 0) BUSINESS_ANALYSIS_DASHBOARD_visits,
				isnull(MAX(BUSINESS_ANALYSIS_DASHBOARD_mins),0) BUSINESS_ANALYSIS_DASHBOARD_mins,
				isnull(MAX(BUSINESS_ANALYSIS_DASHBOARD_average),0) BUSINESS_ANALYSIS_DASHBOARD_average,

				isnull(MAX(GROUP_PRICING_EVALUATION_visits),0) GROUP_PRICING_EVALUATION_visits,
				isnull(MAX(GROUP_PRICING_EVALUATION_mins),0) GROUP_PRICING_EVALUATION_mins,
				isnull(MAX(GROUP_PRICING_EVALUATION_average),0) GROUP_PRICING_EVALUATION_average,
				isnull(MAX(GROUP_PRICING_EVALUATION_generate_count),0) GROUP_PRICING_EVALUATION_generate_count

			from
			(
				select
					Property_Id,Property_Group_ID,USER_ID,
					DASHBOARD_visits,DASHBOARD_mins,DASHBOARD_average,
					DEMAND_AND_WASH_visits,DEMAND_AND_WASH_mins,DEMAND_AND_WASH_average,
					OVERBOOKING_visits,OVERBOOKING_mins,OVERBOOKING_average,
					PRICING_visits,PRICING_mins,PRICING_average,
					INFORMATION_MANAGER_visits,INFORMATION_MANAGER_mins,INFORMATION_MANAGER_average,
					BUSINESS_ANALYSIS_DASHBOARD_visits,BUSINESS_ANALYSIS_DASHBOARD_mins,BUSINESS_ANALYSIS_DASHBOARD_average,
					GROUP_PRICING_EVALUATION_visits,GROUP_PRICING_EVALUATION_mins,GROUP_PRICING_EVALUATION_average,GROUP_PRICING_EVALUATION_generate_count,
					max(max_date) as lastUsed
				from
				(
					SELECT
							Property_Id,Property_Group_ID,USER_ID,
							(case name when  'DASHBOARD' then  visits  end ) as DASHBOARD_visits,
							(case name when  'DASHBOARD' then  mins  end ) as DASHBOARD_mins,
							(case name when  'DASHBOARD' then  average  end ) as DASHBOARD_average,
							(case name when  'DASHBOARD' then  lastUsed  end ) as DASHBOARD_lastUsed,
							(case name when  'DEMAND_AND_WASH' then  visits  end ) as DEMAND_AND_WASH_visits,
							(case name when  'DEMAND_AND_WASH' then  mins  end ) as DEMAND_AND_WASH_mins,
							(case name when  'DEMAND_AND_WASH' then  average  end ) as DEMAND_AND_WASH_average,
							(case name when  'DEMAND_AND_WASH' then  lastUsed  end ) as DEMAND_AND_WASH_lastUsed,
							(case name when  'OVERBOOKING' then  visits  end ) as OVERBOOKING_visits,
							(case name when  'OVERBOOKING' then  mins  end ) as OVERBOOKING_mins,
							(case name when  'OVERBOOKING' then  average  end ) as OVERBOOKING_average,
							(case name when  'OVERBOOKING' then  lastUsed  end ) as OVERBOOKING_lastUsed,
							(case name when  'PRICING' then  visits  end ) as PRICING_visits,
							(case name when  'PRICING' then  mins  end ) as PRICING_mins,
							(case name when  'PRICING' then  average  end ) as PRICING_average,
							(case name when  'PRICING' then  lastUsed  end ) as PRICING_lastUsed,
							(case name when  'INFORMATION_MANAGER' then  visits  end ) as INFORMATION_MANAGER_visits,
							(case name when  'INFORMATION_MANAGER' then  mins  end ) as INFORMATION_MANAGER_mins,
							(case name when  'INFORMATION_MANAGER' then  average  end ) as INFORMATION_MANAGER_average,
							(case name when  'INFORMATION_MANAGER' then  lastUsed  end ) as INFORMATION_MANAGER_lastUsed,
							(case name when  'BUSINESS_ANALYSIS_DASHBOARD' then  visits  end ) as BUSINESS_ANALYSIS_DASHBOARD_visits,
							(case name when  'BUSINESS_ANALYSIS_DASHBOARD' then  mins  end ) as BUSINESS_ANALYSIS_DASHBOARD_mins,
							(case name when  'BUSINESS_ANALYSIS_DASHBOARD' then  average  end ) as BUSINESS_ANALYSIS_DASHBOARD_average,
							(case name when  'BUSINESS_ANALYSIS_DASHBOARD' then  lastUsed  end ) as BUSINESS_ANALYSIS_DASHBOARD_lastUsed,
							(case name when  'GROUP_PRICING_EVALUATION' then  visits  end ) as GROUP_PRICING_EVALUATION_visits,
							(case name when  'GROUP_PRICING_EVALUATION' then  mins  end ) as GROUP_PRICING_EVALUATION_mins,
							(case name when  'GROUP_PRICING_EVALUATION' then  average  end ) as GROUP_PRICING_EVALUATION_average,
							(case name when  'GROUP_PRICING_EVALUATION' then  lastUsed  end ) as GROUP_PRICING_EVALUATION_lastUsed,
							(case name when  'GROUP_PRICING_EVALUATION' then  generate_count  end ) as GROUP_PRICING_EVALUATION_generate_count
							 FROM
							 (
								select Property_Id,Property_Group_ID,USER_ID,Name,count(*) visits,
									sum(mins) mins, AVG(mins) average, MAX(End_DTTM) as lastUsed,
									(select COUNT(*) from System_Usage su inner join System_Usage_Page sup on su.Page_ID=sup.System_Usage_Page_ID and sup.Name=data2.Name where su.Property_ID=data2.Property_Id and su.USER_ID = data2.USER_ID and Start_DTTM >= @start_date and Start_DTTM < DATEADD(day,1,@end_date) and Type_ID = 8) as generate_count
								from
								(
									select Property_Id,Property_Group_ID,USER_ID,Name,
											Start_DTTM,End_DTTM,DATEDIFF ( mi, Start_DTTM, End_DTTM ) as mins
									from
									(
                    select * from
										( select * from @tempSystemUsage
										  where  USER_ID in (SELECT userID FROM @temp_UserIdTable) and TYPE_ID = 2
		                  and Start_DTTM >= @start_date and End_DTTM <= DATEADD(day,1,@end_date)
										) as SU
										inner join
										(
											SELECT System_Usage_Page_ID,Name
											  FROM System_Usage_Page where
											  Name in ('DASHBOARD','DEMAND_AND_WASH','OVERBOOKING','PRICING',
														'INFORMATION_MANAGER','BUSINESS_ANALYSIS_DASHBOARD','GROUP_PRICING_EVALUATION')
										) SUP on SU.Page_ID = SUP.System_Usage_Page_ID

									)data
								) data2
								group by Property_Id,Property_Group_ID,USER_ID,Name
							) A
						) SUB
							  UNPIVOT
						  (
						  max_date
						  for ndate in (
										DASHBOARD_lastUsed,PRICING_lastUsed,
										OVERBOOKING_lastUsed,DEMAND_AND_WASH_lastUsed,
										INFORMATION_MANAGER_lastUsed,
										BUSINESS_ANALYSIS_DASHBOARD_lastUsed,
										GROUP_PRICING_EVALUATION_lastUsed
										)
						  ) as unpvt
						  group by Property_Id,Property_Group_ID,USER_ID,
								DASHBOARD_visits,DASHBOARD_mins,DASHBOARD_average,
								DEMAND_AND_WASH_visits,DEMAND_AND_WASH_mins,DEMAND_AND_WASH_average,
								OVERBOOKING_visits,OVERBOOKING_mins,OVERBOOKING_average,
								PRICING_visits,PRICING_mins,PRICING_average,
								INFORMATION_MANAGER_visits,INFORMATION_MANAGER_mins,INFORMATION_MANAGER_average,
								BUSINESS_ANALYSIS_DASHBOARD_visits,BUSINESS_ANALYSIS_DASHBOARD_mins,BUSINESS_ANALYSIS_DASHBOARD_average,
								GROUP_PRICING_EVALUATION_visits,GROUP_PRICING_EVALUATION_mins,GROUP_PRICING_EVALUATION_average,GROUP_PRICING_EVALUATION_generate_count
					) as B group by Property_Id,Property_Group_ID,USER_ID
		) as C
		inner join
			Users U on C.User_ID=U.User_ID
		inner Join
			#tempTable_ForRoleFilter as rf on rf.USER_ID = u.User_ID
		left join
			Property P on C.Property_ID=P.Property_ID
		left join Property_Group PG on C.Property_Group_ID=PG.Property_Group_ID
		order by USER_NAME

end
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_user_authgrp_property_role_details] 
	@userID nvarchar(20), @client_code nvarchar(50),@isInternal int
AS
BEGIN
	SET NOCOUNT ON
	Declare @client_id int	
	set @client_id = (select client_id from Client where Client_Code = @client_code) 
	
	declare @user_report_details_table table
	(	
		USER_ID int,
		Auth_Grp_Name nvarchar(100),
		Auth_Grp_Role nvarchar(100),
		Auth_Grp_Property_Code nvarchar(50),
		Auth_Grp_Property_Name nvarchar(150),
		Individual_Property nvarchar(100),
		Individual_Property_Role nvarchar(100),
		last_Login_Time datetime
	)
	
	declare @authGrpTable table
	(
		Auth_Group_ID int,
		Client_ID int,
		Auth_Group_Name nvarchar(100)
		primary key (auth_group_id)
	)
		
	declare @authGroupPropertyTable table
	(
		Auth_Group_ID int,
		Property_ID int,
		Status_ID int
		primary key (auth_group_id,Property_ID)
	)
	
	insert into @authGrpTable
		select Auth_Group_ID, Client_ID, Auth_Group_Name from Auth_Group
		UNION
		select -666, -1, 'All Properties' --WHERE @isInternal = 1

	insert into @authGroupPropertyTable
		select Auth_Group_ID, Property_ID, Status_ID from Auth_Group_Property
		UNION
		select -666, Property_ID, '1' from Property WHERE Client_ID = @client_id and Status_ID = 1 --AND @isInternal = 1
	
	
	declare @User_Auth_Grp_With_Property table
	(
		user_ID int,
		Auth_Grp_Name nvarchar(100),
		Auth_Grp_Role nvarchar(100),
		Property_ID int,
		Auth_Grp_Property_Code nvarchar(50),		
		Auth_Grp_Property_Name nvarchar(150)
	)		
	
	insert into @User_Auth_Grp_With_Property
		select user_id, Auth_Group_Name, Role_ID, e.Property_ID, e.Property_Code,e.Property_Name from 
			User_Auth_Group_Role a 			
			inner join @authGrpTable c 
			on a.Auth_Group_ID = c.Auth_Group_ID
			inner join @authGroupPropertyTable d
			on c.Auth_Group_ID = d.Auth_Group_ID
			inner join Property e
			on d.Property_ID = e.Property_ID
			where a.user_id = @userID
	
	declare @last_access_per_property table
	(
		property_id int,
		last_access datetime
	)	
	
	insert into @last_access_per_property
		select a.property_id, MAX(Start_DTTM) last_access 
			from @User_Auth_Grp_With_Property a
			left join 
			System_Usage b on a.User_ID = b.User_ID AND b.Type_ID in (1,2) and a.property_id = b.Property_ID
			group by a.property_id
		
		
	insert into @user_report_details_table
		select a.user_ID,Auth_Grp_Name,Auth_Grp_Role,Auth_Grp_Property_Code,Auth_Grp_Property_Name,null,null,last_access
		from 
			@User_Auth_Grp_With_Property a
			inner join 
			@last_access_per_property b
			on a.Property_ID = b.property_id
		union
		select user_ID,null,null,null,Property_Code,Property_Name,Role_ID,last_access from 
		(
			select distinct b.user_ID, b.Property_Code,b.Property_Name,Role_ID, MAX(Start_DTTM) last_access 
			from System_Usage a
				right join
				( 
					select c.Property_ID,c.Role_ID,c.User_ID,e.Property_Code,e.Property_Name from 
					dbo.User_Individual_Property_Role c
					inner join 
					dbo.Property e  on c.Property_ID = e.Property_ID and e.Client_ID = @client_id and c.User_ID=@userID
				) b on a.Property_ID = b.Property_ID and a.User_ID = b.User_ID and a.Type_ID in (1,2)
			group by b.Property_Code,b.Property_Name,b.Role_ID,b.user_ID
		) as indPropDetailsTable 
		
		select * from @user_report_details_table order by user_ID,Individual_Property		
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_user_authgrp_property_role_information] 
	@userIDs nvarchar(max), @client_code nvarchar(50),@isInternal int
AS
BEGIN
	SET NOCOUNT ON
	Declare @client_id int	
	set @client_id = (select client_id from Client where Client_Code = @client_code) 
	
	Declare @userIDTable table
	(
		userID int
		primary key(userID)
	)
	
	if (len(@userIDs)>0)
	begin
		insert into @userIDTable
		select * from ufn_varchar_max_to_int (@userIDs,',')
	end
	-- get all selected property ids	
	
	declare @authGrpTable table
	(
		Auth_Group_ID int,
		Client_ID int,
		Auth_Group_Name nvarchar(100)
		primary key (auth_group_id)
	)	
	
	insert into @authGrpTable
		select Auth_Group_ID, Client_ID, Auth_Group_Name from Auth_Group
		UNION
		select -666, -1, 'All Properties' --WHERE @isInternal = 1	
		
	IF OBJECT_ID('tempdb..#user_report_details_table') IS NOT NULL
	BEGIN	
		insert into #user_report_details_table
		select a.user_ID,Auth_Group_Name,Role_ID,null,null,null
		from 
			User_Auth_Group_Role a 
			inner join @authGrpTable c 
			on a.Auth_Group_ID = c.Auth_Group_ID
			inner join @userIDTable b
			on a.user_ID = b.userID
		union
		select user_ID,null,null,Property_Code,Property_Name,Role_ID from 
		(	
			select c.Property_ID,c.Role_ID,c.User_ID,e.Property_Code,e.Property_Name from 
			dbo.User_Individual_Property_Role c
			inner join 
			@userIDTable d
			on c.user_ID = d.userID
			inner join 
			dbo.Property e  on c.Property_ID = e.Property_ID and e.Client_ID = @client_id
		) as indPropDetailsTable
	END	
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_users_details_filterby_individualproperty_authgrp_role] 
	@propertyIDs nvarchar(max), 
	@authGrpIDs nvarchar(max),
	@roleIDs nvarchar(max),
	@userIDs nvarchar(max),
	@isActive int,
	@isUserInternal int,
	@client_code nvarchar(10),
	@loggedInUserId int
AS
BEGIN
	SET NOCOUNT ON
	
	IF OBJECT_ID('tempdb..#user_report_table') IS NOT NULL
	BEGIN
		DROP TABLE #user_report_table
	END
	
	Create table #user_report_table
	(	
		USER_ID int,
		USER_NAME nvarchar(100),
		Last_Name nvarchar(100),
		First_Name nvarchar(100),
		Status_ID int,
		Email_Address nvarchar(300),
		Created_DTTM datetime,
		last_Login_Time datetime
	)	
	
	exec dbo.usp_users_filterby_individualproperty_authgrp_role @propertyIDs,@authGrpIDs,@roleIDs,@userIDs,
			@isActive,@isUserInternal,@client_code,'true', @loggedInUserId	
			
	DECLARE @UserlistStr NVARCHAR(MAX)
	SELECT @UserlistStr = COALESCE(@UserlistStr+',' ,'') + cast(USER_ID as varchar(10))
		FROM #user_report_table		
	
	IF OBJECT_ID('tempdb..#user_report_details_table') IS NOT NULL
	BEGIN
		DROP TABLE #user_report_details_table
	END
	
	Create table #user_report_details_table 
	(	
		USER_ID int,
		Auth_Grp_Name nvarchar(100),
		Auth_Grp_Role nvarchar(100),
		Individual_Property nvarchar(100),
		Individual_Property_Name nvarchar(150),
		Individual_Property_Role nvarchar(100)
	)
	
	Declare @Auth_Grp_Details_table table
	(	
		USER_ID int,
		Auth_Grp_Name nvarchar(100),
		Auth_Grp_Role nvarchar(100)
	)
	
	Declare @Ind_Property_Details_table table
	(	
		USER_ID int,
		Individual_Property nvarchar(100),
		Individual_Property_Name nvarchar(150),
		Individual_Property_Role nvarchar(100)
		primary key (USER_ID,Individual_Property)
	)

	exec dbo.usp_user_authgrp_property_role_information @UserlistStr,@client_code,@isUserInternal
	
	insert into @Auth_Grp_Details_table 
		select user_ID,Auth_Grp_Name,Auth_Grp_Role
		from #user_report_details_table
		where Auth_Grp_Name is not null
		
	
	insert into @Ind_Property_Details_table 
		select user_ID,Individual_Property,Individual_Property_Name,Individual_Property_Role
		from #user_report_details_table
		where 
			Individual_Property is not null
	
	
	select First_Name,Last_Name,Status_ID,Email_Address,created_DTTM,
		a.last_Login_Time,Auth_Grp_Name, Auth_Grp_Role,
		b.Individual_Property,b.Individual_Property_Name,b.Individual_Property_Role  
		from 
		#user_report_table a
		left outer join
		(
			select user_ID,Auth_Grp_Name,Auth_Grp_Role,null as Individual_Property,null as Individual_Property_Name,
					null as Individual_Property_Role
			from @Auth_Grp_Details_table
			union 
			select user_ID,null,null,Individual_Property,Individual_Property_Name,Individual_Property_Role
			from @Ind_Property_Details_table
		) b	on 
		a.USER_ID = b.USER_ID
		order by a.USER_ID,Individual_Property
end

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_users_filterby_individualproperty_authgrp_role] 
	@propertyIDs nvarchar(max), 
	@authGrpIDs nvarchar(max),
	@roleIDs nvarchar(max),
	@userIDs nvarchar(max),
	@isActive int,
	@isUserInternal int,
	@client_code nvarchar(10),
	@useExitingTempTable nvarchar(5),
	@loggedInUserId int
AS
BEGIN
	SET NOCOUNT ON

		if(@useExitingTempTable<>'true')
		begin		
			IF OBJECT_ID('tempdb..#user_report_table') IS NOT NULL
			BEGIN
				DROP TABLE #user_report_table
			END
			Create table #user_report_table
			(	
				USER_ID int,
				USER_NAME nvarchar(100),
				Last_Name nvarchar(100),
				First_Name nvarchar(100),
				Status_ID int,
				Email_Address nvarchar(300),
				Created_DTTM datetime,
				last_Login_Time datetime
				primary key (USER_ID)
			)
		end
		
		Declare @status int
		Declare @isExtranal int
		Declare @client_id int
		
		Declare @isProperty int = 0
		Declare @isAuthGrp int = 0
		Declare @isRole int  = 0
		Declare @isUsers int = 0
		
		declare @isAuthGroupWithAllProperties int
		set @isAuthGroupWithAllProperties = (select COUNT(User_Auth_Group_Role_ID) from User_Auth_Group_Role where USER_ID = @loggedInUserId and Auth_Group_ID = -666)
		
		IF OBJECT_ID('tempdb..#tempTable_ForPropertyFilter') IS NOT NULL
		BEGIN
			DROP TABLE #tempTable_ForPropertyFilter
		END
				
		CREATE TABLE #tempTable_ForPropertyFilter
		(	
			USER_ID int
		) 
		IF OBJECT_ID('tempdb..#tempTable_ForAuthGroupFilter') IS NOT NULL
		BEGIN
			DROP TABLE #tempTable_ForAuthGroupFilter
		END
		
		CREATE TABLE #tempTable_ForAuthGroupFilter
		(	
			USER_ID int
		)
		IF OBJECT_ID('tempdb..#tempTable_ForRoleFilter') IS NOT NULL
		BEGIN
			DROP TABLE #tempTable_ForRoleFilter
		END
		CREATE TABLE #tempTable_ForRoleFilter
		(	
			USER_ID int
		) 
		IF OBJECT_ID('tempdb..#tempTable_ForUsersFilter') IS NOT NULL
		BEGIN
			DROP TABLE #tempTable_ForUsersFilter
		END
		CREATE TABLE #tempTable_ForUsersFilter
		(	
			USER_ID int
		) 
		--IF OBJECT_ID('tempdb..#tempTable_ForUsersBasedOnAllFilters') IS NOT NULL
		--BEGIN
		--	DROP TABLE #tempTable_ForUsersBasedOnAllFilters
		--END	
		--CREATE TABLE #tempTable_ForUsersBasedOnAllFilters
		--(	
		--	USER_ID int
		--)
		
		declare @tempTable_ForUsersBasedOnAllFilters table
		(	
			USER_ID int
		)

		DECLARE @finalSQL nvarchar(max)
				
		--internal = 1 (internal user)
		--if logged in user is internal show all external and internal users 
		--if logged in user is external user show only external users 
		--@isUserInternal =1 and @isExtranal = 0 will give you all users 
		--@isUserInternal = 0 and @isExtranal = 0 give you onlu external users
		
		
		if(@isActive = 1 )
			set @status = 1
		else if (@isActive = 2)
			set @status = 2
		else
			begin
				set @status = 1
				set @isActive = 2
			end	
		
		
		--internal = 1 (internal user)
		--if logged in user is internal show all external and internal users 
		--if logged in user is external user show only external users 
		--@isUserInternal =1 and @isExtranal = 0 will give you all users 
		--@isUserInternal = 0 and @isExtranal = 0 give you onlu external users
	
		
		if(@isUserInternal = 1 )
			set @isExtranal = 0
		else if (@isUserInternal = 0)
			set @isExtranal = 0
		
		set @client_id = (select client_id from Client where Client_Code = @client_code)
		
		declare @usersTable table
		(
			[User_ID] [int] NOT NULL,
			[User_Name] [nvarchar](50) NOT NULL,
			[Email_Address] [nvarchar](150) NOT NULL,
			[Status_ID] [int] NOT NULL,
			[Created_by_User_ID] [int] NOT NULL,
			[Created_DTTM] [datetime] NOT NULL,
			[Client_Code] [nvarchar](50) NOT NULL,
			[Internal] [int] NOT NULL,
			[First_Name] [nvarchar](50) NULL,
			[Last_Name] [nvarchar](50) NULL
			primary key ([User_ID])
		)
		
		if(@isUserInternal=1)
			begin
				insert into @usersTable
					select [User_ID],[User_Name],[Email_Address],
						[Status_ID],[Created_by_User_ID],[Created_DTTM],
						[Client_Code],[Internal],[First_Name],[Last_Name]
					from Users
					where 
					Client_Code in (@client_code,'ideas')
					and Status_ID in (@isActive,@status) 
					and Internal in (@isUserInternal, @isExtranal) 
			end
		else
			begin
				insert into @usersTable
					select [User_ID],[User_Name],[Email_Address],
						[Status_ID],[Created_by_User_ID],[Created_DTTM],
						[Client_Code],[Internal],[First_Name],[Last_Name]
					from Users
					where 
					Client_Code in (@client_code)
					and Status_ID in (@isActive,@status) 
					and Internal in (@isExtranal) 
			end
		
		-- get all selected property ids
		Declare @propertyIDTable table
		(
			propertyID int
		)

		if (len(@propertyIDs)>0 and @propertyIDs<>'-1')
		begin
			insert into @propertyIDTable
				select * from ufn_varchar_max_to_int (@propertyIDs,',')
				
			set @isProperty = 1
		end
		else if (@propertyIDs = '-1')
		begin				
			IF @isAuthGroupWithAllProperties > 0
			BEGIN
				insert into @propertyIDTable					
					select p.Property_ID from Property as p where @isAuthGroupWithAllProperties > 0 and p.Client_ID=@client_id and Status_ID =1
			END
			ELSE
			BEGIN
				insert into @propertyIDTable
				select p.Property_ID from 
				property as p inner join User_Individual_Property_Role as up 
				on p.Property_ID = up.Property_ID 
				where p.Client_ID=@client_id and p.Status_ID = 1 and up.User_ID = @loggedInUserId
				UNION
				select p.Property_ID from 
				property as p inner join Auth_Group_Property as agp 
				on agp.Property_ID = p.Property_ID
				inner join User_Auth_Group_Role as uap 
				on agp.Auth_Group_ID = uap.Auth_Group_ID and uap.User_ID = @loggedInUserId 
			END
			set @isProperty = 1
		end
		
		if(@isProperty=1)
		begin 
			insert into #tempTable_ForPropertyFilter
				select distinct USER_ID
					from 
					@propertyIDTable a
					inner join (
						select up.Property_ID, up.User_ID from User_Individual_Property_Role as up 
						union
						select agp.Property_ID, uap.User_ID from Auth_Group_Property as agp 
						inner join User_Auth_Group_Role as uap
						on agp.Auth_Group_ID = uap.Auth_Group_ID
					) as allProp
					on a.propertyID = allProp.Property_ID
				union
				select uap.User_ID from User_Auth_Group_Role as uap inner join @usersTable as u 
					on uap.User_ID = u.User_ID and Auth_Group_ID = -666
				
				SET @finalSQL = ' SELECT a.USER_ID FROM #tempTable_ForPropertyFilter a'
		end
						
		-- get all selected authorization group ids	
		Declare @AuthGroupIDTable table
		(
			authGrpID int
		)
		
		if (len(@authGrpIDs)>0 and @authGrpIDs<>'-1')
			begin
			insert into @AuthGroupIDTable
				select * from ufn_varchar_max_to_int (@authGrpIDs,',')
			
			set @isAuthGrp = 1
			end
		else if (@authGrpIDs='-1')
			begin
			insert into @AuthGroupIDTable
				select Auth_Group_ID  from Auth_Group where Client_ID = @client_id and status_id =1 
				UNION
				select -666
				
				set @isAuthGrp = 1
		end
		
		if(@isAuthGrp=1)
		begin 
			insert into #tempTable_ForAuthGroupFilter
			select distinct USER_ID from @AuthGroupIDTable a
			inner join 
			User_Auth_Group_Role  b
				on a.authGrpID = b.Auth_Group_ID
				
			IF(LEN(@finalSQL)>0)
				SET @finalSQL += ' INNER JOIN #tempTable_ForAuthGroupFilter b ON a.USER_ID = b.USER_ID'
			else
				SET @finalSQL = ' SELECT a.USER_ID FROM #tempTable_ForAuthGroupFilter a'

		end 	
			
		-- get all selected role ids
		Declare @RoleIDTable table
		(
			roleID varchar(100)
		)
			
		if (len(@roleIDs)>0 and @roleIDs<>'-1')
			begin
				insert into @RoleIDTable select items from split(@roleIDs,',')
				
				set @isRole = 1
			end
		else if (@roleIDs='-1')
			begin
				
			INSERT INTO #tempTable_ForRoleFilter
				select distinct a.USER_ID from 
					User_Auth_Group_Role a 
					inner join 
					@usersTable b  on a.User_ID = b.User_ID and b.Client_Code = @client_code
				union 
				select distinct USER_ID from 
					dbo.User_Individual_Property_Role c
					inner join 
					Property d  on c.Property_ID = d.Property_ID and d.Client_ID = @client_id
										
				set @isRole = 1
		end
		
		if(@isRole=1)
		begin
			if(@roleIDs<>'-1')
			begin
				insert into #tempTable_ForRoleFilter
					select b.USER_ID from @RoleIDTable a
						inner join User_Individual_Property_Role b on a.roleID = b.Role_ID
					UNION		
					select c.User_ID from @RoleIDTable a
						inner join User_Auth_Group_Role c on a.roleID = c.Role_ID
			end
			 			
			IF(LEN(@finalSQL)>0)
				SET @finalSQL += ' INNER JOIN #tempTable_ForRoleFilter c ON a.USER_ID = c.USER_ID'
			else
				SET @finalSQL = ' SELECT a.USER_ID FROM #tempTable_ForRoleFilter a'

		end		
			
		-- get all selected user ids
		Declare @UserIDTable table
		(
			userID int
		)
					
		if (len(@userIDs)>0 and @userIDs<>'-1')
			begin
				insert into @UserIDTable
					select * from ufn_varchar_max_to_int (@userIDs,',')
				
				set @isUsers = 1
			end
		else if (@userIDs='-1')
			begin				
				insert into @UserIDTable
					select USER_ID  from @usersTable
				set @isUsers = 1
			end
		
		if(@isUsers=1)
		begin 
			
			if(@userIDs='-1')
			begin
				insert INTO #tempTable_ForUsersFilter
						select distinct USER_ID
						from @usersTable
			end
			else
				begin
					insert INTO #tempTable_ForUsersFilter
						select distinct USER_ID
						from @usersTable a inner join @UserIDTable b
							 on a.User_ID = b.userID
				end
			
			IF(LEN(@finalSQL)>0)
				SET @finalSQL += ' INNER JOIN #tempTable_ForUsersFilter d ON a.USER_ID = d.USER_ID'
			else
				SET @finalSQL = ' SELECT a.USER_ID FROM #tempTable_ForUsersFilter a'

		end
		--print @finalSQL
		INSERT INTO @tempTable_ForUsersBasedOnAllFilters
		exec (@finalSQL)
		
		declare @tempTable_ForLastLogin table
		(	
			USER_ID int,
			last_login_Time DATETIME 
			primary key (USER_ID)
		)
		
		insert into @tempTable_ForLastLogin
			select user_ID,MAX(Start_DTTM) from System_Usage  
			where Type_ID = 1
			group by user_id 
			
		
		insert into #user_report_table
		select a.USER_ID,USER_NAME,Last_Name,First_Name,Status_ID,
			Email_Address,created_DTTM,outerTable.last_Login_Time	
		from 
			@usersTable a 
		INNER JOIN
		(
			 select innerUserTable.user_ID,  last_Login_Time
			 FROM @tempTable_ForUsersBasedOnAllFilters as innerUserTable
			 LEFT OUTER JOIN @tempTable_ForLastLogin lastLoginData on innerUserTable.User_ID = lastLoginData.User_ID
		 )as outerTable on a.User_ID=outerTable.User_ID 
				and a.Status_ID in (@isActive,@status) 
				and a.Internal in (@isUserInternal, @isExtranal)
				
		--select USER_ID from #tempTable_ForUsersBasedOnAllFilters
		if(@useExitingTempTable<>'true')
			SELECT * FROM #user_report_table order by Last_Name 
		
		IF OBJECT_ID('tempdb..#tempTable_ForPropertyFilter') IS NOT NULL
		BEGIN
			DROP TABLE #tempTable_ForPropertyFilter
		END
		IF OBJECT_ID('tempdb..#tempTable_ForAuthGroupFilter') IS NOT NULL
		BEGIN
			DROP TABLE #tempTable_ForAuthGroupFilter
		END
		IF OBJECT_ID('tempdb..#tempTable_ForRoleFilter') IS NOT NULL
		BEGIN
			DROP TABLE #tempTable_ForRoleFilter
		END
		IF OBJECT_ID('tempdb..#tempTable_ForUsersFilter') IS NOT NULL
		BEGIN
			DROP TABLE #tempTable_ForUsersFilter
		END
		IF OBJECT_ID('tempdb..#tempTable_ForUsersBasedOnAllFilters') IS NOT NULL
		BEGIN
			DROP TABLE #tempTable_ForUsersBasedOnAllFilters
		END	
end