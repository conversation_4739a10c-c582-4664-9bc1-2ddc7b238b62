DROP VIEW [dbo].[Vw_Client_Property]
GO

CREATE VIEW [dbo].[Vw_Client_Property]
AS
SELECT
	Client_ID,
	Client_Name,
	Client_Code,
	Property_ID,
	Property_Code,
	Property_Name,
	Stage,
	External_System,
	External_System_Sub_System,
	Suspended_BDE_And_CDP,
	Time_To_Check_Audit,
	Time_Zone
FROM (
	SELECT
		c.Client_ID,
		c.Client_Name,
		c.Client_Code,
		p.Property_ID,
		p.Property_Code,
		p.Property_Name,
		p.Stage,
		ISNULL(clientPropertyConfigParams.External_System, ISNULL(clientConfigParams.External_System, globalConfigParams.External_System)) External_System,
		ISNULL(clientPropertyConfigParams.External_System_Sub_System, ISNULL(clientConfigParams.External_System_Sub_System, globalConfigParams.External_System_Sub_System)) External_System_Sub_System,
		CASE WHEN ISNULL(clientPropertyConfigParams.Suspended_BDE_And_CDP, ISNULL(clientConfigParams.Suspended_BDE_And_CDP, globalConfigParams.Suspended_BDE_And_CDP)) = 'true'
			THEN 1 ELSE 0
		END Suspended_BDE_And_CDP,
		ISNULL(clientPropertyConfigParams.Time_To_Check_Audit, ISNULL(clientConfigParams.Time_To_Check_Audit, globalConfigParams.Time_To_Check_Audit)) Time_To_Check_Audit,
		clientPropertyConfigParams.Time_Zone Time_Zone
	FROM [dbo].[Client] c INNER JOIN [dbo].[Property] p ON c.Client_ID = p.Client_ID
	LEFT JOIN (
		SELECT
			Client_Code,
			Property_Code,
			externalSystem AS External_System,
			subSystem AS External_System_Sub_System,
			suspendBdeAndCdp AS Suspended_BDE_And_CDP,
			timeToCheckAuditDate AS Time_To_Check_Audit,
			propertyTimeZone AS Time_Zone
		FROM (
			SELECT
				SUBSTRING(Client_And_Property, 0, CHARINDEX('.',Client_And_Property)) Client_Code,
				SUBSTRING(Client_And_Property, CHARINDEX('.',Client_And_Property) +1, LEN(Client_And_Property)) Property_Code,
				reverse(left(reverse(Name), charindex('.', reverse(Name)) -1)) Trimmed_Name,
				Value
			FROM (
					-- FixedValue config parameters
					SELECT cp.Name, REPLACE(cpv.Context, 'pacman.', '') Client_And_Property, cpv.FixedValue as Value
					FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID
					WHERE cp.Name in (
						'pacman.integration.timeToCheckAuditDate'
					)
					AND CHARINDEX('.',cpv.Context) > 0
					UNION ALL
					-- Pre-defined Value config parameters
					SELECT cp.Name, REPLACE(cpv.Context, 'pacman.', '') Client_And_Property, cppv.Value
					FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID INNER JOIN dbo.Config_Parameter_Predefined_Value AS cppv WITH(NOLOCK) ON cpv.Config_Parameter_Predefined_Value_ID = cppv.Config_Parameter_Predefined_Value_ID
					WHERE cp.Name in (
						'pacman.core.property.externalSystem',
						'pacman.core.property.externalSystem.subSystem',
						'pacman.integration.suspendBdeAndCdp',
						'pacman.core.propertyTimeZone'
					)
					AND CHARINDEX('.',cpv.Context) > 0
			) allConfigParams
		) configParamNamesTrimmed
		PIVOT (
			max(Value) for Trimmed_Name IN (externalSystem, subSystem, suspendBdeAndCdp, timeToCheckAuditDate, propertyTimeZone)
		) AS configParams
	) clientPropertyConfigParams ON c.Client_Code = clientPropertyConfigParams.Client_Code AND p.Property_Code = clientPropertyConfigParams.Property_Code
	LEFT JOIN (
		SELECT
			Client_Code,
			externalSystem AS External_System,
			subSystem AS External_System_Sub_System,
			suspendBdeAndCdp AS Suspended_BDE_And_CDP,
			timeToCheckAuditDate AS Time_To_Check_Audit
		FROM (
			SELECT
				Client_Code,
				reverse(left(reverse(Name), charindex('.', reverse(Name)) -1)) Trimmed_Name,
				Value
			FROM (
					-- FixedValue config parameters
					SELECT cp.Name, REPLACE(cpv.Context, 'pacman.', '') Client_Code, cpv.FixedValue as Value
					FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID
					WHERE cp.Name in (
						'pacman.integration.timeToCheckAuditDate'
					)
					AND CHARINDEX('.',cpv.Context) > 0 AND CHARINDEX('.',REPLACE(cpv.Context, 'pacman.', '')) = 0
					UNION ALL
					-- Pre-defined Value config parameters
					SELECT cp.Name, REPLACE(cpv.Context, 'pacman.', '') Client_Code, cppv.Value
					FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID INNER JOIN dbo.Config_Parameter_Predefined_Value AS cppv WITH(NOLOCK) ON cpv.Config_Parameter_Predefined_Value_ID = cppv.Config_Parameter_Predefined_Value_ID
					WHERE cp.Name in (
						'pacman.core.property.externalSystem',
						'pacman.core.property.externalSystem.subSystem',
						'pacman.integration.suspendBdeAndCdp'
					)
					AND CHARINDEX('.',cpv.Context) > 0 AND CHARINDEX('.',REPLACE(cpv.Context, 'pacman.', '')) = 0
			) allConfigParams
		) configParamNamesTrimmed
		PIVOT (
			max(Value) for Trimmed_Name IN (externalSystem, subSystem, suspendBdeAndCdp,timeToCheckAuditDate)
		) AS configParams
	) clientConfigParams ON c.Client_Code = clientConfigParams.Client_Code
	LEFT JOIN (
		SELECT
			externalSystem AS External_System,
			subSystem AS External_System_Sub_System,
			suspendBdeAndCdp AS Suspended_BDE_And_CDP,
			timeToCheckAuditDate AS Time_To_Check_Audit
		FROM (
			SELECT
				reverse(left(reverse(Name), charindex('.', reverse(Name)) -1)) Trimmed_Name,
				Value
			FROM (
					-- FixedValue config parameters
					SELECT cp.Name, cpv.FixedValue as Value
					FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID
					WHERE cp.Name in (
						'pacman.integration.timeToCheckAuditDate'
					) AND cpv.Context='pacman'
    				-- Pre-defined Value config parameters
					UNION ALL
					SELECT cp.Name, cppv.Value
					FROM dbo.Config_Parameter cp WITH(NOLOCK) INNER JOIN dbo.Config_Parameter_Value cpv WITH(NOLOCK) ON cp.Config_Parameter_ID = cpv.Config_Parameter_ID INNER JOIN dbo.Config_Parameter_Predefined_Value AS cppv WITH(NOLOCK) ON cpv.Config_Parameter_Predefined_Value_ID = cppv.Config_Parameter_Predefined_Value_ID
					WHERE cp.Name in (
						'pacman.core.property.externalSystem',
						'pacman.core.property.externalSystem.subSystem',
						'pacman.integration.suspendBdeAndCdp'
					) and cpv.Context='pacman'
			) allConfigParams
		) configParamNamesTrimmed
		PIVOT (
			max(Value) for Trimmed_Name IN (externalSystem, subSystem, suspendBdeAndCdp,timeToCheckAuditDate)
		) AS configParams
	) globalConfigParams ON globalConfigParams.Suspended_BDE_And_CDP IS NOT NULL OR globalConfigParams.Time_To_Check_Audit IS NOT NULL
) properties;
GO
