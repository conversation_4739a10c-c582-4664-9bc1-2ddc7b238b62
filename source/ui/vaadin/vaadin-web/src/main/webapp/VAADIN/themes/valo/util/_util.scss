/**
 * Ouput selectors and properties to vertically center elements inside their parent.
 * 
 * @param {string} $to-align (()) - The selector to match the elements which you wish to align vertically. The targeted elements should be inline or inline-block elements.
 * @param {string} $align (middle) - The vertical-align value, e.g. top, middle, bottom
 * @param {string} $pseudo-element (after) - Which pseudo element to use for the vertical align guide
 * 
 * @group util
 */
@mixin valo-vertical-align-guide ($to-align: (), $align: middle, $pseudo-element: after) {
  &:#{$pseudo-element} {
    content: "";
    display: inline-block;
    width: 0;
    height: 100%;
    vertical-align: middle;
  }

  @if length($to-align) > 0 {
    @each $selector in $to-align {
      & > #{$selector} {
        vertical-align: $align;
      }
    }
  }
}


/**
 * Indicate that an element is clickable/tappable
 * 
 * @group util
 */
@mixin valo-tappable {
  -webkit-tap-highlight-color: rgba(0,0,0,0);
  -webkit-touch-callout: none;
  cursor: pointer;
 }


/**
 * Output Vaadin Responsive extension specific width-range declaration.
 * 
 * @param {size} $min (0) - The lower bound for the width-range
 * @param {size} $max (null) - The upper bound for the width-range
 * 
 * @group util
 * 
 * @example scss
 * .v-ui {
 *   font-size: 18px;
 *   
 *   // Make the font-size smaller for 481px-768px wide UIs
 *   @include width-range(481px, 768px) {
 *     font-size: 16px;
 *   }
 *   
 *   // Make it even smaller for UIs narrower than 480px
 *   @include width-range($max: 480px) {
 *     font-size: 14px;
 *   }
 * }
 */
@mixin width-range($min: 0, $max: null) {
  &[width-range~="#{$min}-#{$max}"] {
    @content;
  }
}

/**
 * Output Vaadin Responsive extension specific height-range declaration.
 * 
 * @param {size} $min (0) - The lower bound for the height-range
 * @param {size} $max (null) - The upper bound for the height-range
 * 
 * @group util
 * 
 * @example scss
 * .v-csslayout {
 *   color: red;
 *   
 *   // Make the text color blue when the layout height is between 100px-300px
 *   @include height-range(100px, 300px) {
 *     color: blue;
 *   }
 * }
 */
@mixin height-range($min: 0, $max: null) {
  &[height-range~="#{$min}-#{$max}"] {
    @content;
  }
}
