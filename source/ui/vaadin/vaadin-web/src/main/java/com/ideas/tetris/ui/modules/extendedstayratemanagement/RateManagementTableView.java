package com.ideas.tetris.ui.modules.extendedstayratemanagement;

import com.ideas.tetris.pacman.services.extendedstay.ratemanagement.ExtendedStayOverrideStatus;
import com.ideas.tetris.pacman.services.extendedstay.ratemanagement.dto.ExtendedStayRateMappingData;
import com.ideas.tetris.pacman.services.extendedstay.ratemanagement.dto.ProductLevelDTO;
import com.ideas.tetris.pacman.services.extendedstay.ratemanagement.service.SpecialEventOccupancyDetails;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.ui.common.TetrisComponentFactory;
import com.ideas.tetris.ui.common.TetrisUi;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisLabel;
import com.ideas.tetris.ui.common.component.button.TetrisButton;
import com.ideas.tetris.ui.common.component.button.TetrisLinkButton;
import com.ideas.tetris.ui.common.component.fontawesome.TetrisFontAwesome;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareColumnComponentProvider;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareComponentColumnGenerator;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareTable;
import com.ideas.tetris.ui.common.component.textfield.TetrisBigDecimalField;
import com.ideas.tetris.ui.common.component.window.TetrisWindow;
import com.ideas.tetris.ui.common.data.util.converter.StringToBigDecimalConverter;
import com.ideas.tetris.ui.common.util.ChangeAware;
import com.ideas.tetris.ui.common.util.DateFormatUtil;
import com.ideas.tetris.ui.common.util.TetrisTheme;
import com.vaadin.server.ExternalResource;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.Button;
import com.vaadin.ui.Component;
import com.vaadin.ui.CssLayout;
import com.vaadin.ui.Link;
import com.vaadin.v7.data.validator.RangeValidator;
import com.vaadin.v7.shared.ui.label.ContentMode;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Label;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DATE_FORMAT_DD_MMM_YYYY;
import static com.ideas.tetris.ui.common.util.UiUtils.getText;

public class RateManagementTableView extends VerticalLayout {
    private final VerticalLayout verticalLayout;
    private RateManagementPresenter presenter;
    private TetrisBeanItemContainer<ExtendedStayRateMappingDataWrapper> container;
    private transient TetrisComponentFactory componentFactory;
    private String dowColId;
    private String dateColId;
    private RateManagementTable table;
    private List<String> columns;
    private List<String> recommendedColumns;
    private String compColId;

    public RateManagementTableView(RateManagementPresenter presenter, TetrisComponentFactory componentFactory) {
        this.presenter = presenter;
        this.componentFactory = componentFactory;

        setSizeFull();
        setSpacing(true);

        verticalLayout = new VerticalLayout();
        verticalLayout.setSizeFull();

        createTable();
        addComponent(verticalLayout);
        CssLayout legend = addLegend();
        HorizontalLayout footerLayout = new HorizontalLayout(legend);
        footerLayout.setWidth(100, Unit.PERCENTAGE);
        footerLayout.setExpandRatio(legend, 7);
        addComponent(footerLayout);
        setExpandRatio(verticalLayout, 1);
    }

    public List<ExtendedStayRateMappingDataWrapper> getTableItems() {
        return container.getItemsExcludingNoDataFoundItem();
    }

    public void setInvalidItem(ExtendedStayRateMappingDataWrapper extendedStayRateMappingDataWrapper) {
        table.setInvalidItem(extendedStayRateMappingDataWrapper);
    }

    private CssLayout addLegend() {
        CssLayout cssLayout = new CssLayout();
        cssLayout.setWidth(100, Unit.PERCENTAGE);
        cssLayout.addStyleName("extendedStay-legend");
        Label pending = new Label(" " + getText("price.change.delivered"));
        pending.addStyleName("pending");
        cssLayout.addComponent(pending);
        return cssLayout;
    }

    private void createTable() {
        container = new TetrisBeanItemContainer<ExtendedStayRateMappingDataWrapper>(ExtendedStayRateMappingDataWrapper.class);
        columns = new ArrayList<String>();
        recommendedColumns = new ArrayList<>();
        table = new RateManagementTable();
        table.setSizeFull();


        container.addNestedContainerBean("extendedStayRateMappingData");
        container.addNestedContainerBean("extendedStayRateMappingData.hotelLevelDTO");
        container.addNestedContainerBean("extendedStayRateMappingData.productslevelDtos");

        dowColId = "dow";
        table.setColumnAlignment(dowColId, Table.Align.LEFT);
        dateColId = "extendedStayRateMappingData.date";

        table.addGeneratedColumn(dateColId, newDateAndDailyStatusColumn());
        table.setColumnAlignment(dateColId, Table.Align.CENTER);

        table.setColumnHeader(dowColId, getText("three.level.day.of.week"));
        table.setColumnWidth(dowColId, 68);
        table.setColumnHeader(dateColId, getText("date"));
        table.setColumnAlignment(dateColId, Table.Align.CENTER);

        columns.add(dowColId);
        columns.add(dateColId);
        table.setColumnWidth(dateColId, 110);

        addColumns();
    }

    private TetrisBigDecimalField newOverrideField(BigDecimal override, boolean isInPast) {
        TetrisBigDecimalField field = new TetrisBigDecimalField();
        field.setFormatString(StringToBigDecimalConverter.DEFAULT_FORMAT_TWO_DECIMALS);
        field.setDisableReadOnlyComponentSwapping(true);
        field.setWidth(100, Unit.PERCENTAGE);
        field.setRequired(false);
        field.addStyleName(TetrisTheme.TEXT_RIGHT_ALIGNED);
        field.setMaxLength(15);
        field.setValue(override == null ? "" : override.toString());
        field.setReadOnly(isInPast);
        field.setEnabled(!isInPast);

        RangeValidator<BigDecimal> bigDecimalRangeValidator = new RangeValidator<BigDecimal>(getText("groupPricing.confAndBanquet.range.validator"),
                BigDecimal.class,
                BigDecimal.ZERO, null);
        bigDecimalRangeValidator.setMinValueIncluded(false);
        bigDecimalRangeValidator.setMaxValueIncluded(false);
        field.addValidator(bigDecimalRangeValidator);
        return field;
    }

    private Table.ColumnGenerator newDateAndDailyStatusColumn() {
        return new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table source, Object itemId, Object columnId) {
                ExtendedStayRateMappingDataWrapper bean = container.getItem(itemId).getBean();
                final ExtendedStayRateMappingData extendedStayRateMappingData = bean.getExtendedStayRateMappingData();
                HorizontalLayout dateLayout = new HorizontalLayout();
                if (extendedStayRateMappingData != null) {
                    LocalDate occupancyDate = extendedStayRateMappingData.getDate();
                    com.vaadin.ui.Label dateLabel = componentFactory.createLabelV8("occupancyDate", "");
                    dateLabel.setValue(StringUtils.rightPad(DateUtil.formatDate(occupancyDate.toDate(), DATE_FORMAT_DD_MMM_YYYY), 1));
                    dateLabel.setContentMode(com.vaadin.shared.ui.ContentMode.HTML);
                    dateLayout.addComponent(dateLabel);
                    dateLayout.setComponentAlignment(dateLabel, Alignment.BOTTOM_LEFT);
                    dateLayout.setWidth(105, Unit.PIXELS);
                    if (extendedStayRateMappingData.getIsSpecialEvent()) {

                        TetrisButton popup = new TetrisButton();
                        popup.setPopupContentProvider(new TetrisButton.TetrisPopupContentProvider() {
                            @Override
                            public Component getContent(TetrisButton parentButton) {
                                return createSpecialEventContent(extendedStayRateMappingData.getDate());
                            }
                        });

                        popup.setIcon(TetrisFontAwesome.SPECIAL_EVENTS_STAR);
                        popup.setStyleName(TetrisLinkButton.LINK_STYLE, true);
                        popup.addStyleName(TetrisTheme.FONT_YELLOW);
                        dateLayout.addComponent(popup);
                        dateLayout.setComponentAlignment(popup, Alignment.MIDDLE_RIGHT);
                    }
                    return dateLayout;
                }
                return dateLayout;
            }
        };
    }


    private Component createSpecialEventContent(LocalDate occupancyDate) {
        VerticalLayout layout = new VerticalLayout();
        layout.setSpacing(true);
        layout.setSizeUndefined();

        Label title = new Label(getText("common.specialEventsForDate", occupancyDate.toString(DateFormatUtil.getDateFormatString(), TetrisUi.getCurrent().getLocale())));
        title.addStyleName(TetrisTheme.BOLD);
        title.setSizeUndefined();
        layout.addComponent(title);
        layout.setComponentAlignment(title, Alignment.MIDDLE_CENTER);

        List<SpecialEventOccupancyDetails> specialEvents = presenter.getSpecialEventOccupancyDateSummaries(occupancyDate);

        for (SpecialEventOccupancyDetails specialEvent : specialEvents) {
            Link name = new Link(specialEvent.getName(), new ExternalResource(presenter.getSpecialEventsManagementUrl(occupancyDate, specialEvent.getId())));
            name.setTargetName("_blank");
            name.addStyleName(TetrisTheme.BOLD);
            Label dateRange = new Label(getText("common.date.range", specialEvent.getStartDate().toString(new StringBuilder("EEE").append(" ").append(DateFormatUtil.getDateFormatString()).toString(), TetrisUi.getCurrent().getLocale()), specialEvent.getEndDate().toString(new StringBuilder("EEE").append(" ").append(DateFormatUtil.getDateFormatString()).toString(), TetrisUi.getCurrent().getLocale())));
            Label impactForecast = new Label(getText("impactForecast") + " : ".concat(specialEvent.isImpactForecast() ? "Y" : "N"));
            VerticalLayout row = new VerticalLayout(name, dateRange, impactForecast);
            layout.addComponent(row);
        }
        return layout;
    }


    private Component generateLabel(Number value) {
        TetrisLabel label = null;
        if (value == null) {
            label = new TetrisLabel("--");
        } else {
            label = new TetrisLabel(value);
        }
        label.setSizeUndefined();

        return label;
    }

    private Component generatePendingLabel(Number value, Number previousValue) {
        TetrisLabel label = null;
        VerticalLayout cssLayout = new VerticalLayout();
        if (value == null) {
            label = new TetrisLabel(value);
        } else {

            label = new TetrisLabel(value, ContentMode.HTML);
            label.setStyleName(TetrisTheme.FONT_RED);
            label.setDescription(getText("was") + ": " + previousValue);

        }
        label.setSizeUndefined();
        cssLayout.setSizeFull();
        cssLayout.addComponent(label);
        cssLayout.setComponentAlignment(label, Alignment.MIDDLE_RIGHT);

        return cssLayout;
    }

    private TetrisWindow openCompetitorDetailsWindow(LocalDate date) {
        final TetrisWindow window = new TetrisWindow();
        window.setContent(new CompetitorDetailsPopupViewTable(date, presenter, window));
        window.setCaption(getText("competitorDetails"));
        window.center();
        window.setWidth(90, Unit.PERCENTAGE);
        window.setHeight(65, Unit.PERCENTAGE);
        return window;

    }


    public void setResults(List<ExtendedStayRateMappingDataWrapper> results, boolean hasChanges) {
        container.removeAllItems();
        container.addAll(results);
        table.setHasChanges(hasChanges);
    }

    public void setHasChanges(Boolean hasChanges) {
        table.setHasChanges(hasChanges);
    }

    private void addColumns() {
        String oooColumnID = "";
        String roomsOnBooksID = "";
        String ofColId = "";
        String lrvColId = "";
        String los1BarColId = "";
        String availableTosellID = "";

        oooColumnID = "extendedStayRateMappingData.hotelLevelDTO.outOfOrder";
        columns.add(oooColumnID);
        table.setColumnHeader(oooColumnID, getText("three.level.out.of.order"));
        table.setColumnAlignment(oooColumnID, Table.Align.CENTER);
        table.setColumnWidth(oooColumnID, 65);


        roomsOnBooksID = "extendedStayRateMappingData.hotelLevelDTO.roomsOnBooks";
        table.setColumnHeader(roomsOnBooksID, getText("three.level.occupancy.on.books"));
        columns.add(roomsOnBooksID);
        table.setColumnAlignment(roomsOnBooksID, Table.Align.CENTER);
        table.setColumnWidth(roomsOnBooksID, 100);


        ofColId = "extendedStayRateMappingData.hotelLevelDTO.occupancyForecastPercent";
        table.setColumnHeader(ofColId, getText("common.occ.fcst") + " %");
        table.setColumnAlignment(ofColId, Table.Align.CENTER);

        columns.add(ofColId);
        table.setColumnWidth(ofColId, 55);

        availableTosellID = "extendedStayRateMappingData.hotelLevelDTO.availableToSell";
        table.setColumnHeader(availableTosellID, getText("available.to.sell.label"));
        table.setColumnAlignment(availableTosellID, Table.Align.CENTER);
        table.setColumnWidth(availableTosellID, 125);

        columns.add(availableTosellID);


        lrvColId = "extendedStayRateMappingData.hotelLevelDTO.lrv";
        table.setColumnHeader(lrvColId, getText("los.1.lrv.label"));
        table.setColumnAlignment(lrvColId, Table.Align.CENTER);

        columns.add(lrvColId);


        los1BarColId = "extendedStayRateMappingData.hotelLevelDTO.bar";
        table.setColumnHeader(los1BarColId, getText("los.1.bar.label"));
        table.setColumnAlignment(los1BarColId, Table.Align.CENTER);

        columns.add(los1BarColId);

        compColId = "compCol";
        table.addGeneratedColumn(compColId, newCompColumn());
        table.setColumnHeader(compColId, getText("competitor") + "\n" + "(" + getText("daily") + ")");
        table.setColumnAlignment(compColId, Table.Align.CENTER);
        table.setColumnWidth(compColId, 85);
        columns.add(compColId);

        String overrideColId = "";
        String currentBarColID = "";
        String recommendedColID = "";
        String competitorColId = "";


        Set<Product> extendedStayProducts = presenter.getExtendedStayProducts();
        for (Product product : extendedStayProducts) {
            final String code = product.getCode();
            overrideColId = code.toLowerCase() + getText("groupWash.overrideStatus");
            table.setColumnHeader(overrideColId, getText("two.level.price.change") + "\n (" + code + ")");
            table.addGeneratedColumn(overrideColId, new TetrisChangeAwareComponentColumnGenerator() {
                @Override
                public ChangeAware generateComponent(Table source, Object itemId, Object columnId) {
                    ExtendedStayRateMappingDataWrapper bean = container.getItem(itemId).getBean();
                    ExtendedStayRateMappingData extendedStayRateMappingData = bean.getExtendedStayRateMappingData();
                    BigDecimal override = BigDecimal.ZERO;
                    boolean isInPast = false;
                    if (extendedStayRateMappingData != null) {
                        List<ProductLevelDTO> productLevelDTOList = extendedStayRateMappingData.getProductslevelDtos();
                        if (productLevelDTOList != null) {

                            for (ProductLevelDTO productLevelDTO : productLevelDTOList) {
                                if (productLevelDTO.getProductCode().equals(code)) {
                                    BigDecimal override1 = productLevelDTO.getOverride();
                                    String status = productLevelDTO.getStatus();
                                    if (ExtendedStayOverrideStatus.ACKNOWLEDGEMENT_PENDING.name().equals(status) && !presenter.getIsCopied().get()) {
                                        override1 = null;
                                    }
                                    override = override1 == null ? override1 : override1.setScale(2, RoundingMode.HALF_UP);
                                    if (productLevelDTO.getOccupancyDate().toDate().before(presenter.getCaughtUpDate().toDate())) {
                                        isInPast = true;
                                    }
                                    break;
                                }
                            }

                        }
                    }

                    TetrisBigDecimalField rateField = newOverrideField(override, isInPast);
                    return rateField;
                }
            });
            table.setColumnAlignment(overrideColId, Table.Align.CENTER);
            table.setColumnWidth(overrideColId, 98);

            recommendedColID = code.toLowerCase() + "recommended";
            table.setColumnHeader(recommendedColID, getText("esa.rateManagement.grid.columnname.recommended") + "\n(" + code + ")");
            table.setColumnWidth(recommendedColID, 100);
            table.addGeneratedColumn(recommendedColID, new Table.ColumnGenerator() {
                @Override
                public Object generateCell(Table source, Object itemId, Object columnId) {
                    ExtendedStayRateMappingDataWrapper bean = container.getItem(itemId).getBean();
                    ExtendedStayRateMappingData extendedStayRateMappingData = bean.getExtendedStayRateMappingData();
                    if (extendedStayRateMappingData != null) {
                        List<ProductLevelDTO> productLevelDTOList = extendedStayRateMappingData.getProductslevelDtos();
                        if (productLevelDTOList != null) {

                            for (ProductLevelDTO productLevelDTO : productLevelDTOList) {
                                if (productLevelDTO.getProductCode().equals(code)) {
                                    BigDecimal recommendedPrice = productLevelDTO.getRecommended();
                                    return recommendedPrice == null ? "--" : recommendedPrice.setScale(2, RoundingMode.HALF_UP);
                                }
                            }
                        }
                    }
                    return "--";
                }
            });
            table.setColumnAlignment(recommendedColID, Table.Align.CENTER);

            currentBarColID = code.toLowerCase() + "currentBar";
            table.addGeneratedColumn(currentBarColID, new Table.ColumnGenerator() {
                @Override
                public Object generateCell(Table source, Object itemId, Object columnId) {
                    ExtendedStayRateMappingDataWrapper bean = container.getItem(itemId).getBean();
                    ExtendedStayRateMappingData extendedStayRateMappingData = bean.getExtendedStayRateMappingData();
                    if (extendedStayRateMappingData != null) {
                        List<ProductLevelDTO> productLevelDTOList = extendedStayRateMappingData.getProductslevelDtos();
                        if (productLevelDTOList != null) {

                            for (ProductLevelDTO productLevelDTO : productLevelDTOList) {
                                if (productLevelDTO.getProductCode().equals(code)) {
                                    BigDecimal currentBar = productLevelDTO.getCurrentBar();
                                    if (productLevelDTO.getOverride() != null && ExtendedStayOverrideStatus.ACKNOWLEDGEMENT_PENDING.toString().equals(productLevelDTO.getStatus())) {
                                        BigDecimal override = productLevelDTO.getOverride();
                                        return override == null ? generatePendingLabel(override, null) : generatePendingLabel(override.setScale(2, RoundingMode.HALF_UP),
                                                currentBar == null ? null : currentBar.setScale(2, RoundingMode.HALF_UP));
                                    } else {

                                        return currentBar == null ? generateLabel(currentBar) : generateLabel(currentBar.setScale(2, RoundingMode.HALF_UP));
                                    }
                                }
                            }
                        }
                    }
                    return "--";
                }
            });
            table.setColumnAlignment(currentBarColID, Table.Align.CENTER);

            table.setColumnHeader(currentBarColID, getText("common.current") + " " + getText("BAR") + "\n (" + code + ")");
            table.setColumnWidth(currentBarColID, 95);


            competitorColId = code.toLowerCase() + "competitor";
            table.setColumnHeader(competitorColId, getText("competitor") + "\n (" + code + ")");


            table.addGeneratedColumn(competitorColId, new Table.ColumnGenerator() {
                @Override
                public Object generateCell(Table source, Object itemId, Object columnId) {
                    ExtendedStayRateMappingDataWrapper bean = container.getItem(itemId).getBean();
                    ExtendedStayRateMappingData extendedStayRateMappingData = bean.getExtendedStayRateMappingData();
                    if (extendedStayRateMappingData != null) {
                        List<ProductLevelDTO> productLevelDTOList = extendedStayRateMappingData.getProductslevelDtos();
                        if (productLevelDTOList != null) {

                            for (ProductLevelDTO productLevelDTO : productLevelDTOList) {
                                if (productLevelDTO.getProductCode().equals(code)) {
                                    BigDecimal competitor = productLevelDTO.getCompetitor();
                                    return competitor == null ? "--" : competitor.setScale(2, RoundingMode.HALF_UP);
                                }
                            }

                        }
                    }
                    return "--";
                }
            });
            table.setColumnAlignment(competitorColId, Table.Align.CENTER);

            table.setColumnWidth(competitorColId, 90);
            columns.add(currentBarColID);
            if (presenter.getPropertyConfigParamService().isExtendedStayProductPricingRecommendationEnabled()) {
                recommendedColumns.add(recommendedColID);
                columns.add(recommendedColID);
            }
            columns.add(overrideColId);
            columns.add(competitorColId);

        }

        table.setTwoHeaderRow();
        verticalLayout.addComponent(table);
        verticalLayout.setExpandRatio(table, 1);
        table.setContainerDataSource(container);
        table.addTableValueChangeListener(new TetrisChangeAwareTable.TableValueChangeListener() {
            @Override
            public void valueChange() {
                table.setHasChanges(true);
            }
        });
        verticalLayout.setDefaultComponentAlignment(Alignment.MIDDLE_CENTER);
    }

    public void refreshColumns(boolean pricingRecommendationEnabled) {
        if (pricingRecommendationEnabled) {
            table.setVisibleColumns(columns.toArray());
        } else {
            List<String> allColumns = new ArrayList(columns);
            allColumns.removeAll(recommendedColumns);
            table.setVisibleColumns(allColumns.toArray());
        }
    }

    private Table.ColumnGenerator newCompColumn() {
        return new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table source, Object itemId, Object columnId) {
                ExtendedStayRateMappingDataWrapper bean = container.getItem(itemId).getBean();
                final ExtendedStayRateMappingData extendedStayRateMappingData = bean.getExtendedStayRateMappingData();
                if (extendedStayRateMappingData != null) {
                    String competitor = extendedStayRateMappingData.getHotelLevelDTO().getCompetitor();
                    final Set<String> competitorNames = extendedStayRateMappingData.getHotelLevelDTO().getCompetitorNames();
                    if (competitor != null) {
                        final TetrisLinkButton popup = new TetrisLinkButton(competitor);
                        popup.addClickListener(
                                new Button.ClickListener() {
                                    @Override
                                    public void buttonClick(Button.ClickEvent clickEvent) {
                                        TetrisWindow window = openCompetitorDetailsWindow(extendedStayRateMappingData.getDate());
                                        window.show();
                                    }
                                }
                        );
                        StringBuilder builder = new StringBuilder().append(getText("competitor") + ":");
                        for (String competitorName : competitorNames) {
                            builder.append(competitorName).append(",");
                        }
                        String finalString = StringUtils.removeEnd(builder.toString(), ",");
                        popup.setDescription(finalString);
                        VerticalLayout layout = new VerticalLayout();
                        layout.addStyleName("padding-added");
                        layout.addComponent(popup);
                        layout.setComponentAlignment(popup, Alignment.MIDDLE_RIGHT);

                        return layout;
                    }
                }
                return "--";
            }
        };
    }

    public boolean tableHasChanges() {
        return table.hasChanges();
    }

    private static class RateManagementTable extends TetrisChangeAwareTable implements ChangeAware {

        private boolean hasChanges;
        private transient TetrisChangeAwareColumnComponentProvider invalidItem;

        @Override
        public boolean hasChanges() {
            return hasChanges;
        }

        public void setHasChanges(boolean hasChanges) {
            this.hasChanges = hasChanges;
        }

        @Override
        public TetrisChangeAwareColumnComponentProvider getFirstInvalidItem() {
            return invalidItem;
        }

        public void setInvalidItem(TetrisChangeAwareColumnComponentProvider componentProvider) {
            this.invalidItem = componentProvider;
        }

    }

}