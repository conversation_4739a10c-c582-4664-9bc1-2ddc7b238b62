package com.ideas.tetris.ui.modules.overbookingmanagement;


import com.google.common.collect.Sets;
import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.HeatMapService;
import com.ideas.tetris.pacman.services.demandoverride.dto.WashOverride;
import com.ideas.tetris.pacman.services.groupwash.ForecastGroupForWashOverrideService;
import com.ideas.tetris.pacman.services.heatmap.entity.HeatMapRangeAndColors;
import com.ideas.tetris.pacman.services.heatmap.entity.HeatMapRangeAndColorsConfig;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroupDetails;
import com.ideas.tetris.pacman.services.inventorygroup.service.InventoryGroupService;
import com.ideas.tetris.pacman.services.overbooking.dto.OverbookingOverrideAccomTypeLevelSummary;
import com.ideas.tetris.pacman.services.overbooking.dto.OverbookingOverridePropertyLevelSummary;
import com.ideas.tetris.pacman.services.overbooking.dto.OverbookingOverrideSummary;
import com.ideas.tetris.pacman.services.overbooking.dto.OverbookingSaveOverrideDetails;
import com.ideas.tetris.pacman.services.overbooking.entity.DecisionCOWValueOVR;
import com.ideas.tetris.pacman.services.overbooking.entity.DecisionOvrbkAccomOVR;
import com.ideas.tetris.pacman.services.overbooking.entity.DecisionOvrbkPropertyOVR;
import com.ideas.tetris.pacman.services.overbooking.entity.OVROverbookingType;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingAccomLevelView;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingPropertyLevel;
import com.ideas.tetris.pacman.services.overbooking.service.OverbookingOverrideService;
import com.ideas.tetris.pacman.services.overbooking.service.OverbookingService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.sasoptimization.dto.SimplifiedWhatIfResult;
import com.ideas.tetris.pacman.services.sasoptimization.service.SimplifiedWhatIfService;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.UserGlobalDBService;
import com.ideas.tetris.pacman.services.userpreferences.UserPreferences;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.license.LicenseService;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import com.ideas.tetris.ui.common.cdi.TetrisUIThread;
import com.ideas.tetris.ui.common.component.window.TetrisConfirmationWindow;
import com.ideas.tetris.ui.common.util.HeatMapUtil;
import com.ideas.tetris.ui.common.util.InventoryGroupUtil;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.ataglance.InventoryGroupDto;
import com.ideas.tetris.ui.modules.overbookingmanagement.multidayoverrides.view.MultidayOvrTableBean;
import com.ideas.tetris.ui.modules.overbookingmanagement.multidayoverrides.view.OverbookingManagementMultidayFilter;
import com.ideas.tetris.ui.modules.overbookingmanagement.views.OverbookingManagementCalendar;
import com.ideas.tetris.ui.modules.overbookingmanagement.views.OverbookingManagementView;
import com.ideas.tetris.ui.modules.overbookingmanagement.views.RoomTypeComboBox;
import com.ideas.tetris.ui.modules.reports.util.URLParamUtil;
import com.ideas.tetris.ui.modules.washoverride.WashOverridesDto;
import com.ideas.tetris.ui.modules.washoverride.roomtype.RoomTypeOverrideTableDto;
import com.vaadin.server.Sizeable;
import com.vaadin.ui.Button;
import com.vaadin.v7.shared.ui.label.ContentMode;
import com.vaadin.v7.ui.Label;
import org.apache.commons.collections.MapUtils;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.FEATURE_LICENSING_ENABLED;
import static com.ideas.tetris.ui.modules.overbookingmanagement.OverbookingManagementUtil.getNoCeilingOVROverbookingType;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;
import org.springframework.beans.factory.annotation.Autowired;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class OverbookingManagementPresenter extends TetrisPresenter<OverbookingManagementView, Void> {

    public static final String EXCEPTION_WHILE_LOADING_WHAT_IF_DATA = "Exception while loading what if data.";
    public static final String FAILED_LOADING_WHAT_IF = "failedLoadingWhatIf";

    public enum ShowOverrides {OVERBOOKING_VALUE, OVERBOOKING_CEILING, WASH, COST_OF_WALK}

    private HeatMapUtil.Mode currentHeatMapMode = HeatMapUtil.Mode.OVERBOOKING;

    private Date heatMapStartDate, heatMapEndDate;

    //there is a special case where we wouldn't show save success message if user tries to save wash overrides without permission
    private boolean showSaveSuccessMessage = true;

    private List<LocalDate> urlDates = new ArrayList<>();

    private Map<Date, BigDecimal> heatMapData = new HashMap<>();

    private Map<Date, OverbookingOverridePropertyLevelSummary> leftCalendarData, rightCalendarData = new HashMap<>();

    private String noCeilingOVROverbookingTypeName;
    private boolean enablePhysicalCapacityConsideration;
    private boolean isAdvancedPriceRankingEnabled;
    private boolean isROHEnabled;
    private boolean isRoomTypeRecodingUIEnabled;
    private boolean isEnableCustomizedHeatMap;
    private boolean isContinuousPricingEnabled;
    private boolean isBarByLOS;
    private boolean isWhatIfEnabled;
    private boolean isOvbkOptimizationEnabled;
    private int maxLOS;
    private List<InventoryGroupDetails> inventoryGroupDetails;
    private List<InventoryGroupDto> inventoryGroups;
    private InventoryGroupDto lastSelectedInventoryGroup;



    private Map<Integer, Map<Integer, Integer>> ceilingDefaultDataMap = new HashMap<>();
    private List<AccomType> baseRoomTypeList = new ArrayList<>();
    private List<AccomType> accomTypes = new ArrayList<>();

    public static final Integer PROPERTY_ID = -1;

    @Autowired
    AccommodationService accommodationService;

    @Autowired
    OverbookingOverrideService overbookingOverrideService;

    @Autowired
	private HeatMapService heatMapService;

    @Autowired
	private SimplifiedWhatIfService simplifiedWhatIfService;

    @Autowired
	private ForecastGroupForWashOverrideService forecastGroupForWashOverrideService;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
	private OverbookingService overbookingService;

    @Autowired
	private InventoryGroupService inventoryGroupService;

    @Autowired
	private PricingConfigurationService pricingConfigurationService;
    @Autowired
	private LicenseService licenseService;
    @Autowired
    UserGlobalDBService userGlobalDBService;

    private UserPreferences userPreferences;

    public static final String MAX_LOS = "pacman.bar.maxLOS";

    @Override
    public void onViewInit() {
        userPreferences = getUserPreferences();
        logger.debug("OverbookingManagementPresenter.onViewInit()");
    }

    @Override
    public void onViewOpened(Void aVoid) {
        logger.debug("OverbookingManagementPresenter.onViewOpened()");
        init();
    }

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        setLastSelectedInventoryGroup();
        logger.debug("OverbookingManagementPresenter.onWorkContextChange()");
        setToggles();
        init();
        checkLastSelectedInventoryGroup();
        setceilingDefaultWarningMessageVisibility();
    }

    private void init() {
        logger.debug("OverbookingManagementPresenter.init()");
        setupAccomTypes();
        checkTimeframeURLParam();
        view.updateShowWhatIf(isWhatIfEnabled);

        LocalDate date = (urlDates.size() == 1) ? urlDates.get(0) : this.getSystemDateAsLocalDate();
        updateAllDates(date);

        view.setCalendarLoadingIconVisibility(true);
        TetrisUIThread tetrisUIThread = new TetrisUIThread() {
            @Override
            public void callServices() {
                OverbookingManagementPresenter.this.callCalendarServices();
            }

            @Override
            public void updateUI() {
                OverbookingManagementPresenter.this.updateCalendarUI();
            }
        };
        tetrisUIThread.run();

        baseRoomTypeList = pricingConfigurationService
                .getPricingAccomClasses()
                .stream()
                .map(PricingAccomClass::getAccomType)
                .collect(Collectors.toList());

        view.setUnsavedChangesWindowShowing(false);

        doHeatMap(currentHeatMapMode, heatMapStartDate, heatMapEndDate);

        if (urlDates.size() == 1 && view.getLeftCalendar().hasDataForDate(urlDates.get(0).toDate())) {
            view.showOverrideDetailsWindow(urlDates.get(0),false);
        }

        ceilingDefaultDataMap = new HashMap<>();
        loadAndStoreAllCeilingDataToMap();
        fetchAndStoreCeilingDataToMap(OverbookingService.DEFAULT_PROPERTY_SELECTION_ID);

        logger.debug("OverbookingManagementPresenter.init()---end");
        view.setInventoryGroupComboBox();
    }

    private void setupAccomTypes() {
        accomTypes = getAccomTypes();
    }

    public void setToggles() {
        noCeilingOVROverbookingTypeName = UiUtils.getText("report.noLimit");
        enablePhysicalCapacityConsideration = configParamsService.isEnablePhysicalCapacityConsideration();
        isAdvancedPriceRankingEnabled = configParamsService.getParameterValue(FeatureTogglesConfigParamName.IS_ADVANCED_PRICE_RANKING_ENABLED);
        isROHEnabled = configParamsService.getBooleanParameterValue(IPConfigParamName.ROH_ROHENABLED.value());
        isRoomTypeRecodingUIEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED.value());
        isEnableCustomizedHeatMap = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CUSTOMIZED_HEAT_MAP);
        maxLOS = Integer.parseInt(configParamsService.getParameterValue(MAX_LOS));
        isContinuousPricingEnabled = configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value());
        String barDecision = configParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        isOvbkOptimizationEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_OVERBOOKING_SCREEN_OPTIMIZATION);
        isBarByLOS = Constants.BAR_DECISION_VALUE_LOS.equals(barDecision);
        isWhatIfEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.WHAT_IF_ENABLED.value());
    }

    public void setceilingDefaultWarningMessageVisibility() {
        int roomTypeId = view.getSelectedRoomTypeId();
        ceilingDefaultDataMap.remove(roomTypeId);
        fetchAndStoreCeilingDataToMap(roomTypeId);

        boolean isCeilingDefaultValuePresent = ceilingDefaultDataMap.containsKey(roomTypeId) && !ceilingDefaultDataMap.get(roomTypeId).isEmpty();
        view.setCeilingDefaultMessageLayoutVisibility(isCeilingDefaultValuePresent);
    }

    public List<AccomType> getAccomTypesForDropdowns() {
        if(isOvbkOptimizationEnabled && !accomTypes.isEmpty()){
            return accomTypes;
        }
        return getAccomTypes();
    }

    private List<AccomType> getAccomTypes() {
        if (isRoomTypeRecodingUIEnabled()) {
            return isROHEnabled() ? accommodationService.getROHAccomTypesWithValidCapacityAndDisplayStatus() :
                    getActiveAccomTypesWithValidCapacity();
        } else {
            return isROHEnabled() ? accommodationService.getROHAccomTypesValidCapacity() :
                    accommodationService.getAllActiveAccomTypesWithCapacity();
        }
    }

    private List<AccomType> getActiveAccomTypesWithValidCapacity() {
        return isOvbkOptimizationEnabled() ?
                accommodationService.getActiveAccomTypesWithValidCapacityAndDisplayStatusOptimisedFetch():
                accommodationService.getActiveAccomTypesWithValidCapacityAndDisplayStatus();
    }

    private boolean isOvbkOptimizationEnabled() {
        return isOvbkOptimizationEnabled;
    }

    public List<AccomType> getAccomTypesForDropdownsByInventoryGroup(int inventoryGroupId) {
        if (inventoryGroupId == -1) {
            return getAccomTypesForDropdowns();
        } else {
            List<InventoryGroupDetails> selectedInventoryGroupDetails = inventoryGroupDetails.stream().filter(inventoryGroupDetail -> inventoryGroupDetail.getInventoryGroup().getId() == inventoryGroupId).collect(Collectors.toList());
            List<AccomType> selectedAccomTypes = selectedInventoryGroupDetails.stream().map(InventoryGroupDetails::getAccomClass).map(AccomClass::getAccomTypes).flatMap(Collection::stream).collect(Collectors.toList());
            return getAccomTypesForDropdowns().stream().filter(accomType -> selectedAccomTypes.contains(accomType)).collect(Collectors.toList());
        }
    }

    public void doHeatMap(HeatMapUtil.Mode heatMapMode, Date startDate, Date endDate) {
        view.setHeatMapLoadingIconVisibility(true);
        TetrisUIThread tetrisUIThread = new TetrisUIThread() {
            @Override
            public void callServices() {
                OverbookingManagementPresenter.this.callHeatMapServices(heatMapMode, startDate, endDate);
            }

            @Override
            public void updateUI() {
                OverbookingManagementPresenter.this.updateHeatMapUI();
            }
        };
        tetrisUIThread.run();
    }

    private void callHeatMapServices(HeatMapUtil.Mode currentHeatMapMode, Date startDate, Date endDate) {
        updateHeatMapData(currentHeatMapMode, startDate, endDate);
    }

    private void updateHeatMapUI() {
        view.updateHeatMap(currentHeatMapMode, heatMapData, heatMapStartDate);
        view.setHeatMapLoadingIconVisibility(false);
    }

    private void callCalendarServices() {
        leftCalendarData = getOverbookingOverrideSummaryDataPropertyLevel(view.getLeftCalendar().getStartDate(), view.getLeftCalendar().getEndDate());
        rightCalendarData = getOverbookingOverrideSummaryDataPropertyLevel(view.getRightCalendar().getStartDate(), view.getRightCalendar().getEndDate());
    }

    private void updateCalendarUI() {
        view.getLeftCalendar().setCalendarDataPropertyLevel(leftCalendarData);
        view.getRightCalendar().setCalendarDataPropertyLevel(rightCalendarData);
        view.setCalendarLoadingIconVisibility(false);
    }

    private void checkTimeframeURLParam() {
        urlDates.clear();
        if (containsTimeFrame(getUrlParameters())) {
            String[] dates = getTimeFrameParamValue(getUrlParameters());
            try {
                LocalDate start = extractStartDateFromTimeFrame(dates[0]);
                LocalDate end = extractEndDateFromTimeFrame(dates[0]);
                urlDates.add(start);
                if (!start.equals(end)) {
                    urlDates.add(end);
                }
            } catch (IllegalArgumentException iae) {
                logger.error("Illegal formatting in timeframe parameter in URL", iae);
            }
        }
    }

    protected boolean containsTimeFrame(Map<String, String[]> urlParameters) {
        return URLParamUtil.containsTimeFrame(urlParameters);
    }

    protected String[] getTimeFrameParamValue(Map<String, String[]> urlParameters) {
        return URLParamUtil.getTimeFrameParamValue(urlParameters);
    }

    protected LocalDate extractEndDateFromTimeFrame(String date) {
        return JavaLocalDateUtils.toJodaLocalDate(URLParamUtil.extractEndDateFromTimeFrame(date));
    }

    protected LocalDate extractStartDateFromTimeFrame(String date) {
        return JavaLocalDateUtils.toJodaLocalDate(URLParamUtil.extractStartDateFromTimeFrame(date));
    }

    private void updateAllDates(LocalDate date) {
        heatMapStartDate = date.dayOfMonth().withMinimumValue().toDate();
        heatMapEndDate = date.plusMonths(6).dayOfMonth().withMinimumValue().toDate();
        view.setMonthSelectorDates(date, date.plusMonths(1));
        view.getLeftCalendar().setDate(date);
        view.getRightCalendar().setDate(date.plusMonths(1));
    }

    public void requestPendingEventRefresh() {
        view.refreshPendingEvents();
    }

    public Map<Date, OverbookingOverridePropertyLevelSummary> getOverbookingOverrideSummaryDataPropertyLevel(Date startDate, Date endDate) {
        logger.debug("OverbookingManagementPresenter.getOverbookingOverrideSummaryDataPropertyLevel(startDate:" + startDate.toString() + " endDate:" + endDate.toString() + ")");
        Map<Date, OverbookingOverridePropertyLevelSummary> overbookingOverrideSummaryData = overbookingOverrideService.getOverbookingOverrideSummary(startDate, endDate, isEnablePhysicalCapacityConsideration(), isAdvancedPriceRankingEnabled());
        if (overbookingOverrideSummaryData != null) {
            logger.debug("OverbookingManagementPresenter.overbookingOverrideSummaryData size:" + overbookingOverrideSummaryData.size());
            logger.debug("OverbookingManagementPresenter.overbookingOverrideSummaryData isEmpty:" + overbookingOverrideSummaryData.isEmpty());
        }
        return overbookingOverrideSummaryData;
    }

    public Map<Date, OverbookingOverrideAccomTypeLevelSummary> getOverbookingOverrideSummaryDataAccomType(
            Integer accomTypeId, Date startDate, Date endDate,
            List<OverbookingAccomLevelView> overbookingAccomLevelViews, List<OverbookingPropertyLevel> overbookingPropertyLevelList,
            List<DecisionOvrbkAccomOVR> decisionOvrbkAccomOVRS, List<DecisionCOWValueOVR> decisionCOWValueOVRS) {
        logger.debug("OverbookingManagementPresenter.getOverbookingOverrideSummaryDataAccomType(startDate:" + startDate.toString() + " endDate:" + endDate.toString() + ")");
        if (overbookingAccomLevelViews == null || overbookingPropertyLevelList == null || decisionOvrbkAccomOVRS == null || decisionCOWValueOVRS == null) {
            return overbookingOverrideService.getOverbookingOverrideSummaryData(accomTypeId, startDate, endDate, isEnablePhysicalCapacityConsideration(), isAdvancedPriceRankingEnabled());
        }
        return overbookingOverrideService.getOverbookingOverrideSummaryDataWithProvidedOverbookingAccomLevelViewsAndOverbookingPropertyLevel(
                accomTypeId, startDate, endDate, overbookingAccomLevelViews, overbookingPropertyLevelList,
                decisionOvrbkAccomOVRS, decisionCOWValueOVRS, isEnablePhysicalCapacityConsideration(), isAdvancedPriceRankingEnabled());
    }

    public List<DecisionOvrbkAccomOVR> getDecisionOvrbkAccomOVRS(Date date) {
        List<AccomType> accomTypes = getAccomTypesForDropdowns()
                .stream()
                .sorted(Comparator.comparing(AccomType::getAccomClassViewOrder))
                .collect(Collectors.toList());
        List<Integer> accomTypeIds = accomTypes.stream().map(AccomType::getId).collect(toList());
        List<DecisionOvrbkAccomOVR> decisionOvrbkAccomOVRS = overbookingOverrideService.getDecisionOvrbkAccomOVRS(accomTypeIds, date, date);
        return decisionOvrbkAccomOVRS;
    }

    public HeatMapUtil.Mode getCurrentHeatMapMode() {
        return currentHeatMapMode;
    }

    private void updateHeatMapData(HeatMapUtil.Mode mode, Date startDate, Date endDate) {
        currentHeatMapMode = mode;
        heatMapStartDate = startDate;
        heatMapEndDate = endDate;

        if (currentHeatMapMode == HeatMapUtil.Mode.OCCUPANCY_FORECAST) {
            logger.debug("OverbookingManagementPresenter.requestOccupancyForecastHeatMap()");
            heatMapData = heatMapService.getOccupancyForecastHeatMapData(startDate, endDate);
        } else if (currentHeatMapMode == HeatMapUtil.Mode.PEAK_DEMAND) {
            logger.debug("OverbookingManagementPresenter.requestPeakDemandHeatMap()");
            heatMapData = heatMapService.getPeakDemandHeatMapData(startDate, endDate);
        } else if (currentHeatMapMode == HeatMapUtil.Mode.OVERBOOKING) {
            logger.debug("OverbookingManagementPresenter.requestOverbookingHeatMap()");
            heatMapData = heatMapService.getOverbookingHeatMapData(startDate, endDate, isOvbkOptimizationEnabled(), uiContext.getSystemCaughtUpDate());
        }
    }

    public boolean hasPendingOverrides() {
        return view.getLeftCalendar().hasPendingOverrides() || view.getRightCalendar().hasPendingOverrides();
    }

    public void updateUnsavedChangesWindowShowing() {
        view.setUnsavedChangesWindowShowing(hasPendingOverrides());
    }

    public void checkPermissions() {
        if ((view.getLeftCalendar().hasPendingWashOverrides() || view.getRightCalendar().hasPendingWashOverrides())
                && !UiUtils.getTetrisUi().isWriteAuthorized(Sets.newHashSet(TetrisPermissionKey.FUNCTION_OVERBOOKING_MANAGEMENT_WASH_OVERRIDE))) {
            TetrisConfirmationWindow confirmationWindow = new TetrisConfirmationWindow(UiUtils.getText("saveOverrideWarningTitle"));
            confirmationWindow.setYesLabel(UiUtils.getText("OK"));
            confirmationWindow.setNoLabel(UiUtils.getText("cancel"));
            Label label = new Label(UiUtils.getText("permOverrideMessageSome") + getText("saveOverrideWashWarning"), ContentMode.HTML);
            label.setWidth(280, Sizeable.Unit.PIXELS);
            confirmationWindow.setInnerContent(label);

            confirmationWindow.setYesClickListener((Button.ClickListener) event -> {
                view.getLeftCalendar().clearPendingWashOverrides();
                view.getRightCalendar().clearPendingWashOverrides();
                showSaveSuccessMessage = view.getLeftCalendar().hasPendingOverrides();
                save();
            });
            confirmationWindow.show();
        } else {
            save();
        }
    }

    private void save() {
        //only need to save the pending overrides from one calendar because each calendar contains all pending overrides
        overbookingOverrideService.saveOverbookingOverrides(createSaveOverrideDetails(view.getLeftCalendar()));

        view.getDetailsView().clearUnsavedOverrides();
        view.refreshCalendars();
        updateUnsavedChangesWindowShowing();
        if (showSaveSuccessMessage) {
            showSaveSuccessMessage();
        }
    }

    private OverbookingSaveOverrideDetails createSaveOverrideDetails(OverbookingManagementCalendar calendar) {
        Map<Date, List<OverbookingOverrideSummary>> allPendingOverrides = calendar.getAllPendingOverrides();

        OverbookingSaveOverrideDetails saveOverrideDetails = new OverbookingSaveOverrideDetails();

        Set<DecisionOvrbkPropertyOVR> decisionOvrbkPropertyOVRs = new HashSet<>();
        Set<DecisionOvrbkAccomOVR> decisionOvrbkAccomOVRs = new HashSet<>();
        Set<DecisionCOWValueOVR> decisionCOWValueOVRs = new HashSet<>();
        allPendingOverrides.forEach((date, overbookingOverrideSummaryList) -> {
            overbookingOverrideSummaryList.forEach(overbookingOverrideSummary -> {
                if (overbookingOverrideSummary.getPropertyOverride() != null) {
                    decisionOvrbkPropertyOVRs.add(overbookingOverrideSummary.getPropertyOverride());
                }
                if (overbookingOverrideSummary.getAccomOverride() != null) {
                    decisionOvrbkAccomOVRs.add(overbookingOverrideSummary.getAccomOverride());
                }
                if (overbookingOverrideSummary.getCostofWalkOverride() != null) {
                    decisionCOWValueOVRs.add(overbookingOverrideSummary.getCostofWalkOverride());
                }
            });
        });
        saveOverrideDetails.setPropertyOverbookingOverride(decisionOvrbkPropertyOVRs);
        saveOverrideDetails.setAccomOverbookingOverride(decisionOvrbkAccomOVRs);
        saveOverrideDetails.setCostofWalkOverride(decisionCOWValueOVRs);

        Set<WashOverride> washOverridesToBeSaved = new HashSet<>();
        calendar.getPendingWashOverrides().forEach((date, pendingWashOverrideList) -> {
            pendingWashOverrideList.forEach(washOverridesToBeSaved::add);
        });
        saveOverrideDetails.setWashOverride(washOverridesToBeSaved);

        return saveOverrideDetails;
    }

    public boolean isDisplayOccupancyForecastInWhatIfEnabled() {
        return simplifiedWhatIfService.isDisplayOccupancyForecastInWhatIfToggleEnabled();
    }

    private Map<Date, String> getDateToPendingTagHtmlMap() {
        Map<Date, String> dateToPendingTagHtmlMap = new HashMap<>();
        dateToPendingTagHtmlMap.putAll(view.getLeftCalendar().getDateToPendingTagHTMLMap());
        dateToPendingTagHtmlMap.putAll(view.getRightCalendar().getDateToPendingTagHTMLMap());
        return dateToPendingTagHtmlMap;
    }

    public SimplifiedWhatIfResult simplifiedAnalyzeChanges() {
        SimplifiedWhatIfResult simplifiedWhatIfResult = null;
        try {
            Map<Date, String> dateOverRideIconHtmlMap = getDateToPendingTagHtmlMap();
            OverbookingSaveOverrideDetails saveDetails = createSaveOverrideDetails(view.getLeftCalendar());
            simplifiedWhatIfResult = simplifiedWhatIfService.analyzeChanges(saveDetails);
            simplifiedWhatIfResult.getDateData().stream().forEach(dateData ->
                    dateData.setOverrideIconsHtml(dateOverRideIconHtmlMap.get(dateData.getDate())));
        } catch (TetrisException e) {
            logger.error(EXCEPTION_WHILE_LOADING_WHAT_IF_DATA, e);
            if (e.getErrorCode() == ErrorCode.DENIED_BY_REGULATOR_SERVICE) {
                showWarning(getText("deniedByWhatIfRegulatorService"));
            } else {
                showError(getText(FAILED_LOADING_WHAT_IF));
            }
        } catch (Exception e) {
            logger.error(EXCEPTION_WHILE_LOADING_WHAT_IF_DATA, e);
            showError(getText(FAILED_LOADING_WHAT_IF));
        }
        return simplifiedWhatIfResult;
    }

    public LocalDate getWhatIfOverrideDate(boolean isStartDate) {
        LocalDate localDate;
        List<Date> dateKeys = new ArrayList<>();
        dateKeys.add(view.getLeftCalendar().getPendingOverrideDate(isStartDate));
        dateKeys.add(view.getRightCalendar().getPendingOverrideDate(isStartDate));
        Collections.sort(dateKeys);

        if (isStartDate) {
            localDate = new LocalDate(dateKeys.get(0));
        } else {
            localDate = new LocalDate(dateKeys.get(dateKeys.size() - 1));
        }
        return localDate;
    }

    public void applySingleDayReverts(WashOverridesDto washOverridesDTO) {
        applyOverbookingReverts(washOverridesDTO);
        applyCowReverts(washOverridesDTO);

        removeFullyRevertedOverrides(washOverridesDTO);
        washOverridesDTO.getRevertedRoomTypeOverrideTableDtos().clear();
    }

    private void applyOverbookingReverts(WashOverridesDto washOverridesDto) {
        washOverridesDto.getRevertedRoomTypeOverrideTableDtos()
                .stream()
                .filter(RoomTypeOverrideTableDto::isOverbookingReverted)
                .forEach(
                        revertedDto -> {
                            OverbookingOverrideSummary summary = getPendingOverbookingOverrideSummary(washOverridesDto, revertedDto);
                            if (revertedDto.isAccom()) {
                                summary.setAccomOverride(revertedDto.getBean().getAccomOverride());
                            } else {
                                summary.setPropertyOverride(revertedDto.getBean().getPropertyOverride());
                            }
                        }
                );
    }

    private void applyCowReverts(WashOverridesDto washOverridesDto) {
        washOverridesDto.getRevertedRoomTypeOverrideTableDtos()
                .stream()
                .filter(RoomTypeOverrideTableDto::isCowReverted)
                .forEach(
                        revertedDto -> {
                            OverbookingOverrideSummary summary = getPendingOverbookingOverrideSummary(washOverridesDto, revertedDto);
                            summary.setCostofWalkOverride(revertedDto.getBean().getCostofWalkOverride());
                        }
                );
    }

    private void removeFullyRevertedOverrides(WashOverridesDto washOverridesDto) {
        washOverridesDto.getRevertedRoomTypeOverrideTableDtos().forEach(
                revertedDto -> {
                    if (revertedDto.isSameAsSavedValues()) {
                        removePendingOverbookingOverrideSummary(washOverridesDto, revertedDto);
                    }
                }
        );
    }

    private void removePendingOverbookingOverrideSummary(WashOverridesDto washOverridesDto, RoomTypeOverrideTableDto roomTypeOverrideTableDto) {
        Date selectedDate = washOverridesDto.getSelectedDate().toDate();
        Integer accomTypeId = roomTypeOverrideTableDto.getAccomTypeId();
        Map<Integer, OverbookingOverrideSummary> summariesForSelectedDate = washOverridesDto.getPendingOverbookingOverrideSummaryData().get(selectedDate);
        if (summariesForSelectedDate != null) {
            summariesForSelectedDate.remove(accomTypeId);
            view.getLeftCalendar().getPendingOverbookingOverrideSummaryData().get(selectedDate).remove(accomTypeId);
            view.getRightCalendar().getPendingOverbookingOverrideSummaryData().get(selectedDate).remove(accomTypeId);
        }
    }

    private OverbookingOverrideSummary getPendingOverbookingOverrideSummary(WashOverridesDto washOverridesDto, RoomTypeOverrideTableDto roomTypeOverrideTableDto) {
        Date selectedDate = washOverridesDto.getSelectedDate().toDate();
        Integer accomTypeId = roomTypeOverrideTableDto.getAccomTypeId();

        OverbookingOverrideSummary pendingAccomOverbookingOverrideSummary = (washOverridesDto.getPendingOverbookingOverrideSummaryData().get(selectedDate) != null
                && washOverridesDto.getPendingOverbookingOverrideSummaryData().get(selectedDate).get(accomTypeId) != null) ?
                washOverridesDto.getPendingOverbookingOverrideSummaryData().get(selectedDate).get(accomTypeId) : null;
        return pendingAccomOverbookingOverrideSummary;
    }

    public void applySingleDayOverride(WashOverridesDto dto) {
        List<WashOverride> washOverrides = createWashOverrideList(dto);
        if (!washOverrides.isEmpty()) {
            view.getLeftCalendar().getPendingWashOverrides().put(dto.getSelectedDate().toDate(), washOverrides);
            view.getRightCalendar().getPendingWashOverrides().put(dto.getSelectedDate().toDate(), washOverrides);
            //if list is empty we want to remove any pending wash overrides for this day
        } else {
            view.getLeftCalendar().getPendingWashOverrides().remove(dto.getSelectedDate().toDate());
            view.getRightCalendar().getPendingWashOverrides().remove(dto.getSelectedDate().toDate());
        }

        Map<Integer, OverbookingOverrideSummary> singleDayPendingOverridesMap = createSingleDayPendingOverridesMap(dto);
        Map<Integer, OverbookingOverrideSummary> existingSingleDayPendingOverridesMap = view.getLeftCalendar().getPendingOverbookingOverrideSummaryData().get(dto.getSelectedDate().toDate());
        Map<Integer, OverbookingOverrideSummary> mergedMap = mergeSingleDayMaps(singleDayPendingOverridesMap, existingSingleDayPendingOverridesMap);
        view.getLeftCalendar().getPendingOverbookingOverrideSummaryData().put(dto.getSelectedDate().toDate(), mergedMap);
        view.getRightCalendar().getPendingOverbookingOverrideSummaryData().put(dto.getSelectedDate().toDate(), mergedMap);

        requestPendingEventRefresh();
        updateUnsavedChangesWindowShowing();
    }

    private List<WashOverride> createWashOverrideList(WashOverridesDto dto) {
        List<WashOverride> washOverrides = new ArrayList<>();

        dto.getForecastGroupUnsavedOverrideDtos().stream().filter(forecastGroupDto -> forecastGroupDto.getDate().equals(dto.getSelectedDate())).forEach(forecastGroupDto -> {
            WashOverride washOverride = new WashOverride();

            washOverride.setForecastGroupId(forecastGroupDto.getForecastGroupId());
            washOverride.setSystemWash(forecastGroupDto.getSystemWashPercent());
            washOverride.setId(forecastGroupDto.getOverrideId());
            washOverride.setOccupancyDate(forecastGroupDto.getDate().toDate());
            washOverride.setValue(forecastGroupDto.getUserWashPercent());

            Date expirationDate = (forecastGroupDto.getExpirationDate() == null) ? null : forecastGroupDto.getExpirationDate().toDate();
            washOverride.setExpirationDate(expirationDate);

            //if the user hit the remove button for this override check and see if the override exists in the database already and if so mark for removal
            if (forecastGroupDto.isMarkedForRemoval()) {
                if (washOverride.getId() != null) {
                    washOverride.setActive(false);
                } else {
                    //this corresponds to user adding a pending override and then going back and removing it before saving
                    washOverride = null;
                }
            }

            washOverrides.add(washOverride);
        });
        return washOverrides.stream().filter(Objects::nonNull).collect(toList());
    }

    private Map<Integer, OverbookingOverrideSummary> createSingleDayPendingOverridesMap(WashOverridesDto dto) {
        Map<Integer, OverbookingOverrideSummary> pendingOverridesMap = new HashMap<>();
        dto.getUnsavedRoomTypeOverrideTableDtos().forEach(unsavedRoomTypeDto -> {
            OverbookingOverrideSummary overbookingOverrideSummary = new OverbookingOverrideSummary();
            if (unsavedRoomTypeDto.getAccomTypeId() == RoomTypeComboBox.PROPERTY_ID) {
                DecisionOvrbkPropertyOVR decisionOvrbkPropertyOVR = new DecisionOvrbkPropertyOVR();
                decisionOvrbkPropertyOVR.setIsDeleted(0);
                decisionOvrbkPropertyOVR.setOccupancyDate(dto.getSelectedDate().toDate());

                OVROverbookingType ovrOverbookingType;
                Integer overbookingOverrideValue;
                if (unsavedRoomTypeDto.isNoCeilingOverride()) {
                    ovrOverbookingType = new OVROverbookingType();
                    ovrOverbookingType.setId(OVROverbookingType.OVR_OVERBOOKING_TYPE_CEILING_ID);
                    overbookingOverrideValue = Constants.OVERBOOKING_NO_LIMIT;
                } else {
                    ovrOverbookingType = unsavedRoomTypeDto.getOverbookingOverrideType();
                    overbookingOverrideValue = unsavedRoomTypeDto.getOverbookingOverrideValueField().intValue();
                }
                decisionOvrbkPropertyOVR.setOvrOverbookingType(ovrOverbookingType);
                decisionOvrbkPropertyOVR.setOverbookingOVR(overbookingOverrideValue);
                //also set the dto to the same type as above since we compare it to saved values later
                unsavedRoomTypeDto.setOverbookingOverrideType(ovrOverbookingType);

                //there is an existing override which means we are performing an update
                if (unsavedRoomTypeDto.getBean().getPropertyOverride() != null) {
                    if (unsavedRoomTypeDto.isOverbookingMarkedForDeletion()) {
                        decisionOvrbkPropertyOVR = unsavedRoomTypeDto.getBean().getPropertyOverride();
                        decisionOvrbkPropertyOVR.setIsDeleted(Constants.DELETED_ENTITY_INDICATOR);
                    } else {
                        decisionOvrbkPropertyOVR.setId(unsavedRoomTypeDto.getBean().getPropertyOverride().getId());
                    }
                }

                if (unsavedRoomTypeDto.overbookingValuesDifferentFromSavedValues()) {
                    overbookingOverrideSummary.setPropertyOverride(decisionOvrbkPropertyOVR);
                    //the user added a pending override but then reverted it so we need to clear that override
                } else {
                    view.getLeftCalendar().getPendingOverbookingOverrideSummaryData().get(dto.getSelectedDate().toDate()).remove(unsavedRoomTypeDto.getAccomTypeId());
                    view.getRightCalendar().getPendingOverbookingOverrideSummaryData().get(dto.getSelectedDate().toDate()).remove(unsavedRoomTypeDto.getAccomTypeId());
                }
                //construct overrides for the accom type level
            } else {
                if (unsavedRoomTypeDto.shouldConstructAccomOverride()) {
                    DecisionOvrbkAccomOVR decisionOvrbkAccomOVR = new DecisionOvrbkAccomOVR();
                    decisionOvrbkAccomOVR.setIsDeleted(0);
                    decisionOvrbkAccomOVR.setOccupancyDate(dto.getSelectedDate().toDate());
                    decisionOvrbkAccomOVR.setAccomTypeId(unsavedRoomTypeDto.getAccomTypeId());

                    OVROverbookingType ovrOverbookingType = new OVROverbookingType();
                    ovrOverbookingType.setId(OVROverbookingType.OVR_OVERBOOKING_TYPE_CEILING_ID);
                    Integer overbookingOverrideValue;
                    if (unsavedRoomTypeDto.getOverbookingAllowed()
                            && unsavedRoomTypeDto.isNoCeilingOverride()) {
                        overbookingOverrideValue = Constants.OVERBOOKING_NO_LIMIT;
                    } else {
                        overbookingOverrideValue = unsavedRoomTypeDto.getOverbookingOverrideValueField().intValue();
                    }
                    decisionOvrbkAccomOVR.setOvrOverbookingType(ovrOverbookingType);
                    decisionOvrbkAccomOVR.setOverbookingOVR(overbookingOverrideValue);
                    //also set the dto to the same type as above since we compare it to saved values later
                    unsavedRoomTypeDto.setOverbookingOverrideType(ovrOverbookingType);
                    unsavedRoomTypeDto.setOverbookingOverrideValueField(BigDecimal.valueOf(overbookingOverrideValue));

                    //there is an existing override which means we are performing an update
                    if (unsavedRoomTypeDto.getBean().getAccomOverride() != null) {
                        if (unsavedRoomTypeDto.isOverbookingMarkedForDeletion()) {
                            decisionOvrbkAccomOVR = unsavedRoomTypeDto.getBean().getAccomOverride();
                            decisionOvrbkAccomOVR.setIsDeleted(Constants.DELETED_ENTITY_INDICATOR);
                        } else {
                            decisionOvrbkAccomOVR.setId(unsavedRoomTypeDto.getBean().getAccomOverride().getId());
                        }
                    }

                    if (unsavedRoomTypeDto.overbookingValuesDifferentFromSavedValues()) {
                        overbookingOverrideSummary.setAccomOverride(decisionOvrbkAccomOVR);
                        //the user added a pending override but then reverted it so we need to clear that override
                    } else {
                        if (view.getLeftCalendar().getPendingOverbookingOverrideSummaryData().get(dto.getSelectedDate().toDate()).get(unsavedRoomTypeDto.getAccomTypeId()).getCostofWalkOverride() != null) {
                            view.getLeftCalendar().getPendingOverbookingOverrideSummaryData().get(dto.getSelectedDate().toDate()).get(unsavedRoomTypeDto.getAccomTypeId()).setAccomOverride(null);
                            view.getRightCalendar().getPendingOverbookingOverrideSummaryData().get(dto.getSelectedDate().toDate()).get(unsavedRoomTypeDto.getAccomTypeId()).setAccomOverride(null);
                        } else {
                            view.getLeftCalendar().getPendingOverbookingOverrideSummaryData().get(dto.getSelectedDate().toDate()).remove(unsavedRoomTypeDto.getAccomTypeId());
                            view.getRightCalendar().getPendingOverbookingOverrideSummaryData().get(dto.getSelectedDate().toDate()).remove(unsavedRoomTypeDto.getAccomTypeId());
                        }
                    }
                }

                if (unsavedRoomTypeDto.shouldConstructCOWOverride()) {
                    DecisionCOWValueOVR decisionCOWValueOVR = new DecisionCOWValueOVR();
                    decisionCOWValueOVR.setOccupancyDate(dto.getSelectedDate().toDate());
                    decisionCOWValueOVR.setIsDeleted(0);
                    decisionCOWValueOVR.setAccomTypeId(unsavedRoomTypeDto.getAccomTypeId());
                    BigDecimal costOfWalkValueOVR = unsavedRoomTypeDto.getCostOfWalkOverrideValueField();
                    decisionCOWValueOVR.setCostOfWalkValueOVR(costOfWalkValueOVR);
                    //there is an existing override which means we are performing an update
                    if (unsavedRoomTypeDto.getBean().getCostofWalkOverride() != null) {
                        if (unsavedRoomTypeDto.isCowMarkedForDeletion()) {
                            decisionCOWValueOVR = unsavedRoomTypeDto.getBean().getCostofWalkOverride();
                            decisionCOWValueOVR.setIsDeleted(Constants.DELETED_ENTITY_INDICATOR);
                        } else {
                            decisionCOWValueOVR.setId(unsavedRoomTypeDto.getBean().getCostofWalkOverride().getId());
                        }
                    }
                    if (unsavedRoomTypeDto.costOfWalkValuesDifferentFromSavedValues()) {
                        overbookingOverrideSummary.setCostofWalkOverride(decisionCOWValueOVR);
                        //the user added a pending override but then reverted it so we need to clear that override
                    } else {
                        if (view.getLeftCalendar().getPendingOverbookingOverrideSummaryData().get(dto.getSelectedDate().toDate()).get(unsavedRoomTypeDto.getAccomTypeId()).getAccomOverride() != null) {
                            view.getLeftCalendar().getPendingOverbookingOverrideSummaryData().get(dto.getSelectedDate().toDate()).get(unsavedRoomTypeDto.getAccomTypeId()).setCostofWalkOverride(null);
                            view.getRightCalendar().getPendingOverbookingOverrideSummaryData().get(dto.getSelectedDate().toDate()).get(unsavedRoomTypeDto.getAccomTypeId()).setCostofWalkOverride(null);
                        } else {
                            view.getLeftCalendar().getPendingOverbookingOverrideSummaryData().get(dto.getSelectedDate().toDate()).remove(unsavedRoomTypeDto.getAccomTypeId());
                            view.getRightCalendar().getPendingOverbookingOverrideSummaryData().get(dto.getSelectedDate().toDate()).remove(unsavedRoomTypeDto.getAccomTypeId());
                        }
                    }
                }
                overbookingOverrideSummary.setOverbookingOriginallyAllowed(unsavedRoomTypeDto.getOverbookingOriginallyAllowed());
            }
            if (overbookingOverrideSummary.containsOverride()) {
                pendingOverridesMap.put(unsavedRoomTypeDto.getAccomTypeId(), overbookingOverrideSummary);
            }
        });
        return pendingOverridesMap;
    }

    /*
    This merge method is called when the user enters a single day pending override for a given day and there is already a pending override for that day.
    We need to intelligently merge the data from each map in order to preserve all the data.
     */
    private Map<Integer, OverbookingOverrideSummary> mergeSingleDayMaps(Map<Integer, OverbookingOverrideSummary> newPendingOverridesMap, Map<Integer, OverbookingOverrideSummary> existingPendingOverridesMap) {
        Map<Integer, OverbookingOverrideSummary> mergedMap = new HashMap<>();
        if (existingPendingOverridesMap != null) {
            mergedMap.putAll(existingPendingOverridesMap);
        }
        newPendingOverridesMap.forEach((accomTypeId, overbookingOverrideSummary) -> {
            if (accomTypeId == RoomTypeComboBox.PROPERTY_ID) {
                mergedMap.put(accomTypeId, overbookingOverrideSummary);
            } else {
                //we have a new accom level override and we must merge it with the existing override without losing any data
                if (existingPendingOverridesMap != null && existingPendingOverridesMap.containsKey(accomTypeId)) {
                    OverbookingOverrideSummary overbookingOverrideSummaryMerged = existingPendingOverridesMap.get(accomTypeId);
                    if (overbookingOverrideSummary.getAccomOverride() != null) {
                        overbookingOverrideSummaryMerged.setAccomOverride(overbookingOverrideSummary.getAccomOverride());
                    }
                    if (overbookingOverrideSummary.getCostofWalkOverride() != null) {
                        overbookingOverrideSummaryMerged.setCostofWalkOverride(overbookingOverrideSummary.getCostofWalkOverride());
                    }
                    mergedMap.put(accomTypeId, overbookingOverrideSummaryMerged);
                } else {
                    mergedMap.put(accomTypeId, overbookingOverrideSummary);
                }
            }
        });
        return mergedMap;
    }

    public void cancel() {
        view.getLeftCalendar().clearPendingOverrideLists();
        view.getRightCalendar().clearPendingOverrideLists();
        view.getDetailsView().clearUnsavedOverrides();
        updateUnsavedChangesWindowShowing();
        view.refreshPendingEvents();
    }

    public Map<String, List<String>> getOrderedRatePlans() {
        if (!isContinuousPricingEnabled()) {
            Map<String, List<String>> accomClassRatePlanMap = new HashMap<>();
            heatMapService.getOrderedRatePlanByAccomClass().forEach((accomClass, ratePlans) -> accomClassRatePlanMap.put(accomClass.getName(), ratePlans));
            return accomClassRatePlanMap;
        }

        return null;
    }

    //NEW MULTIDAY OVERBOOKING CODE
    public List<MultidayOvrTableBean> getMultidayOverrideTableBeans(OverbookingManagementMultidayFilter filterBean) {
        List<MultidayOvrTableBean> tableBeans = new ArrayList<>();

        HashMap<Integer, OVROverbookingType> ovrTypeMap = new HashMap<>();
        ovrTypeMap.put(OVROverbookingType.OVR_OVERBOOKING_TYPE_NO_CEILING_ID, getNoCeilingOVROverbookingType(noCeilingOVROverbookingTypeName));
        ovrTypeMap.put(OVROverbookingType.OVR_OVERBOOKING_TYPE_CEILING_ID, overbookingOverrideService.getOVROverbookingTypeById(OVROverbookingType.OVR_OVERBOOKING_TYPE_CEILING_ID));
        ovrTypeMap.put(OVROverbookingType.OVR_OVERBOOKING_TYPE_VALUE_ID, overbookingOverrideService.getOVROverbookingTypeById(OVROverbookingType.OVR_OVERBOOKING_TYPE_VALUE_ID));

        Set<Integer> daysOfWeek = new HashSet<>();
        filterBean.getDayOfWeek().forEach(dayOfWeek -> daysOfWeek.add(dayOfWeek.toInt()));

        Map<Date, OverbookingOverridePropertyLevelSummary> propertyOvbkData = getOverbookingOverrideSummaryDataPropertyLevel(
                filterBean.getStartDate().toDate(), filterBean.getEndDate().toDate());
        List<OverbookingOverridePropertyLevelSummary> propertyValueOverrides = propertyOvbkData.values().stream().filter(summary ->
                summary.getPropertyOverride() != null && daysOfWeek.contains(new LocalDate(summary.getOccupancyDate()).getDayOfWeek())).collect(Collectors.toList());

        MultidayOvrTableBean propertyBean = new MultidayOvrTableBean();
        propertyBean.setOverbookingTypeMap(ovrTypeMap);
        propertyBean.setAccomTypeId(RoomTypeComboBox.PROPERTY_ID);
        propertyBean.setPropertyLevelOverrides(propertyValueOverrides);
        propertyBean.setRoomType(getText("common.property"));
        propertyBean.setRoomClass(getText("NA"));
        propertyBean.setOverbookingOverrideType(propertyBean.determineOVROverbookingType());
        tableBeans.add(propertyBean);

        List<AccomType> accomTypes = getAccomTypesForDropdowns()
                .stream()
                .sorted(Comparator.comparing(AccomType::getAccomClassViewOrder))
                .collect(Collectors.toList());
        List<Integer> accomTypeIds = accomTypes.stream().map(AccomType::getId).collect(toList());
        List<OverbookingAccomLevelView> overbookingAccomLevelViews = overbookingOverrideService.executeQueryAccomTypeListLevel(accomTypeIds, filterBean.getStartDate().toDate(), filterBean.getEndDate().toDate());
        List<OverbookingPropertyLevel> overbookingPropertyLevelList = overbookingOverrideService.executeQueryPropertyLevel(
                filterBean.getStartDate().toDate(), filterBean.getEndDate().toDate());

        List<DecisionOvrbkAccomOVR> decisionOvrbkAccomOVRS = overbookingOverrideService.getDecisionOvrbkAccomOVRS(accomTypeIds, filterBean.getStartDate().toDate(), filterBean.getEndDate().toDate());
        List<DecisionCOWValueOVR> decisionCOWValueOVRS = overbookingOverrideService.getDecisionCOWValueOVRS(accomTypeIds, filterBean.getStartDate().toDate(), filterBean.getEndDate().toDate());

        accomTypes.forEach(accomType -> {
            MultidayOvrTableBean tableBean = new MultidayOvrTableBean();
            tableBean.setOverbookingTypeMap(ovrTypeMap);
            Map<Date, OverbookingOverrideAccomTypeLevelSummary> accomOverrideData = getOverbookingOverrideSummaryDataAccomType(
                    accomType.getId(), filterBean.getStartDate().toDate(), filterBean.getEndDate().toDate(),
                    overbookingAccomLevelViews, overbookingPropertyLevelList, decisionOvrbkAccomOVRS, decisionCOWValueOVRS);

            // Filter our room types with no data
            if (MapUtils.isNotEmpty(accomOverrideData)) {
                List<OverbookingOverrideAccomTypeLevelSummary> accomOverrides = accomOverrideData.values().stream().filter(summary ->
                        summary.getAccomOverride() != null && summary.getAccomOverride().getOvrOverbookingType().getId().equals(OVROverbookingType.OVR_OVERBOOKING_TYPE_CEILING_ID) &&
                                daysOfWeek.contains(new LocalDate(summary.getOccupancyDate()).getDayOfWeek())).collect(Collectors.toList());
                tableBean.setAccomLevelOverrides(accomOverrides);
                tableBean.setRoomType(accomType.getName());
                tableBean.setRoomClass(accomType.getAccomClass().getName());
                tableBean.setAccomTypeId(accomType.getId());
                tableBean.setOverbookingOverrideType(tableBean.determineOVROverbookingType());
                tableBean.setComponentRoomType(accomType.isComponentRoom());

                List<DecisionCOWValueOVR> costOfWalkOverrides = accomOverrideData.values().stream().filter(summary -> summary.getCostofWalkOverride() != null &&
                                daysOfWeek.contains(new LocalDate(summary.getOccupancyDate()).getDayOfWeek()))
                        .map(OverbookingOverrideAccomTypeLevelSummary::getCostofWalkOverride).collect(Collectors.toList());
                tableBean.setCostOfWalkOverrides(costOfWalkOverrides);
                tableBean.setROHRoomType(accomType.getRohType().equals(1));

                tableBeans.add(tableBean);
            }
        });

        return tableBeans;
    }

    private void addInRemoveOverbookingOverride(OverbookingOverrideSummary pendingOverride, LocalDate date, Integer accomTypeId, Map<Date, OverbookingOverrideAccomTypeLevelSummary> accomOverbookingSummaries) {
        if (accomTypeId.equals(RoomTypeComboBox.PROPERTY_ID)) {
            OverbookingOverrideSummary existingOverride = view.getLeftCalendar().containsDate(date) ? view.getLeftCalendar().getOverbookingOverridePropertyLevelSummaryData().get(date.toDate()) :
                    view.getRightCalendar().getOverbookingOverridePropertyLevelSummaryData().get(date.toDate());
            if (existingOverride != null && existingOverride.getPropertyOverride() != null) {
                pendingOverride.setPropertyOverride(existingOverride.getPropertyOverride());
                pendingOverride.getPropertyOverride().setIsDeleted(Constants.DELETED_ENTITY_INDICATOR);
            }
        } else {
            OverbookingOverrideSummary existingOverride = accomOverbookingSummaries.get(date.toDate());
            if (existingOverride != null && existingOverride.getAccomOverride() != null &&
                    existingOverride.getAccomOverride().getOvrOverbookingType().getId().equals(OVROverbookingType.OVR_OVERBOOKING_TYPE_CEILING_ID)) {
                pendingOverride.setAccomOverride(existingOverride.getAccomOverride());
                pendingOverride.getAccomOverride().setIsDeleted(Constants.DELETED_ENTITY_INDICATOR);
            }
        }
    }

    private void addInOverbookingOverride(OverbookingOverrideSummary pendingOverride, LocalDate date, MultidayOvrTableBean row) {
        if (row.getAccomTypeId().equals(RoomTypeComboBox.PROPERTY_ID)) {
            DecisionOvrbkPropertyOVR decisionOvrbkPropertyOVR = new DecisionOvrbkPropertyOVR();
            decisionOvrbkPropertyOVR.setOccupancyDate(date.toDate());

            OVROverbookingType ceilingOvrOverbookingType = new OVROverbookingType();
            ceilingOvrOverbookingType.setId(OVROverbookingType.OVR_OVERBOOKING_TYPE_CEILING_ID);
            OVROverbookingType valueOvrOverbookingType = new OVROverbookingType();
            valueOvrOverbookingType.setId(OVROverbookingType.OVR_OVERBOOKING_TYPE_VALUE_ID);
            decisionOvrbkPropertyOVR.setOvrOverbookingType(row.getOverbookingOverrideType().getId().equals(OVROverbookingType.OVR_OVERBOOKING_TYPE_VALUE_ID) ?
                    valueOvrOverbookingType : ceilingOvrOverbookingType);

            decisionOvrbkPropertyOVR.setOverbookingOVR(row.getOverbookingOverrideType().getId().equals(OVROverbookingType.OVR_OVERBOOKING_TYPE_NO_CEILING_ID) ?
                    Constants.OVERBOOKING_NO_LIMIT : row.getOverbookingOverrideValue().intValue());
            decisionOvrbkPropertyOVR.setIsDeleted(Constants.NON_DELETED_ENTITY_INDICATOR);

            pendingOverride.setPropertyOverride(decisionOvrbkPropertyOVR);
        } else {
            DecisionOvrbkAccomOVR decisionOvrbkAccomOVR = new DecisionOvrbkAccomOVR();
            decisionOvrbkAccomOVR.setAccomTypeId(row.getAccomTypeId());
            decisionOvrbkAccomOVR.setOccupancyDate(date.toDate());

            OVROverbookingType ceilingOvrOverbookingType = new OVROverbookingType();
            ceilingOvrOverbookingType.setId(OVROverbookingType.OVR_OVERBOOKING_TYPE_CEILING_ID);
            decisionOvrbkAccomOVR.setOvrOverbookingType(ceilingOvrOverbookingType);

            decisionOvrbkAccomOVR.setOverbookingOVR(row.getOverbookingOverrideType().getId().equals(OVROverbookingType.OVR_OVERBOOKING_TYPE_NO_CEILING_ID) ?
                    Constants.OVERBOOKING_NO_LIMIT : row.getOverbookingOverrideValue().intValue());
            decisionOvrbkAccomOVR.setIsDeleted(Constants.NON_DELETED_ENTITY_INDICATOR);

            pendingOverride.setAccomOverride(decisionOvrbkAccomOVR);
        }
    }

    private void addInRemoveCostOfWalkOverride(OverbookingOverrideSummary pendingOverride, LocalDate date, Map<Date, OverbookingOverrideAccomTypeLevelSummary> accomOverbookingSummaries) {
        OverbookingOverrideSummary existingOverride = accomOverbookingSummaries.get(date.toDate());
        if (existingOverride != null && existingOverride.getCostofWalkOverride() != null) {
            pendingOverride.setCostofWalkOverride(existingOverride.getCostofWalkOverride());
            pendingOverride.getCostofWalkOverride().setIsDeleted(Constants.DELETED_ENTITY_INDICATOR);
        }
    }

    private void addInCostOfWalkOverride(OverbookingOverrideSummary pendingOverride, LocalDate date, MultidayOvrTableBean row) {
        DecisionCOWValueOVR decisionCOWValueOVR = new DecisionCOWValueOVR();
        decisionCOWValueOVR.setAccomTypeId(row.getAccomTypeId());
        decisionCOWValueOVR.setOccupancyDate(date.toDate());
        decisionCOWValueOVR.setCostOfWalkValueOVR(row.getCostOfWalkOverrideValue());
        decisionCOWValueOVR.setIsDeleted(Constants.NON_DELETED_ENTITY_INDICATOR);
        pendingOverride.setCostofWalkOverride(decisionCOWValueOVR);
    }

    /*
    An assumption for this method is that there are no pending overrides, meaning that user can only perform multiday overrides if there are no pending overrides.
    Also, there is no updating of overrides in the multiday scenario.  User can only create new overrides or delete existing overrides.
     */
    public void applyMultidayOverrides(List<MultidayOvrTableBean> multidayBeans, OverbookingManagementMultidayFilter filterBean) {
        Map<Date, Map<Integer, OverbookingOverrideSummary>> pendingOverridesMap = new HashMap<>();

        Set<Integer> daysOfWeek = new HashSet<>();
        filterBean.getDayOfWeek().forEach(dayOfWeek -> daysOfWeek.add(dayOfWeek.toInt()));

        List<Integer> accomTypeIds = multidayBeans.stream().map(MultidayOvrTableBean::getAccomTypeId).collect(toList());
        List<OverbookingAccomLevelView> overbookingAccomLevelViews = overbookingOverrideService.executeQueryAccomTypeListLevel(accomTypeIds, filterBean.getStartDate().toDate(), filterBean.getEndDate().toDate());
        List<OverbookingPropertyLevel> overbookingPropertyLevelList = overbookingOverrideService.executeQueryPropertyLevel(
                filterBean.getStartDate().toDate(), filterBean.getEndDate().toDate());
        List<DecisionOvrbkAccomOVR> decisionOvrbkAccomOVRS = overbookingOverrideService.getDecisionOvrbkAccomOVRS(accomTypeIds, filterBean.getStartDate().toDate(), filterBean.getEndDate().toDate());
        List<DecisionCOWValueOVR> decisionCOWValueOVRS = overbookingOverrideService.getDecisionCOWValueOVRS(accomTypeIds, filterBean.getStartDate().toDate(), filterBean.getEndDate().toDate());

        multidayBeans.forEach(row -> {
            Map<Date, OverbookingOverrideAccomTypeLevelSummary> accomOverbookingSummaries = !row.getAccomTypeId().equals(RoomTypeComboBox.PROPERTY_ID) ?
                    getOverbookingOverrideSummaryDataAccomType(row.getAccomTypeId(), filterBean.getStartDate().toDate(), filterBean.getEndDate().toDate(),
                            overbookingAccomLevelViews, overbookingPropertyLevelList, decisionOvrbkAccomOVRS, decisionCOWValueOVRS) : new HashMap<>();
            LocalDate currDate = filterBean.getStartDate();
            while (!currDate.isAfter(filterBean.getEndDate())) {
                if (daysOfWeek.contains(currDate.getDayOfWeek())) {
                    OverbookingOverrideSummary pendingOverride = new OverbookingOverrideSummary();
                    if (row.overbookingFieldsHaveChanges()) {
                        if (row.isOverbookingOverrideMarkedForRemoval()) {
                            addInRemoveOverbookingOverride(pendingOverride, currDate, row.getAccomTypeId(), accomOverbookingSummaries);
                        } else {
                            addInOverbookingOverride(pendingOverride, currDate, row);
                        }
                        pendingOverride.setOverbookingOriginallyAllowed(true);
                    }
                    if (row.costOfWalkFieldsHaveChanges()) {
                        if (row.isCostOfWalkOverrideMarkedForRemoval()) {
                            addInRemoveCostOfWalkOverride(pendingOverride, currDate, accomOverbookingSummaries);
                        } else {
                            addInCostOfWalkOverride(pendingOverride, currDate, row);
                        }
                    }

                    boolean shouldAddToMap = (row.getAccomTypeId().equals(RoomTypeComboBox.PROPERTY_ID) && pendingOverride.getPropertyOverride() != null) ||
                            (!row.getAccomTypeId().equals(RoomTypeComboBox.PROPERTY_ID) && (pendingOverride.getAccomOverride() != null || pendingOverride.getCostofWalkOverride() != null));
                    if (shouldAddToMap) {
                        if (pendingOverridesMap.containsKey(currDate.toDate())) {
                            pendingOverridesMap.get(currDate.toDate()).put(row.getAccomTypeId(), pendingOverride);
                        } else {
                            Map<Integer, OverbookingOverrideSummary> accomTypeToOverrideMap = new HashMap<>();
                            accomTypeToOverrideMap.put(row.getAccomTypeId(), pendingOverride);
                            pendingOverridesMap.put(currDate.toDate(), accomTypeToOverrideMap);
                        }
                    }
                }
                currDate = currDate.plusDays(1);
            }
        });

        view.getLeftCalendar().setPendingOverbookingOverrideSummaryData(pendingOverridesMap);
        view.getRightCalendar().setPendingOverbookingOverrideSummaryData(pendingOverridesMap);
        requestPendingEventRefresh();
        updateUnsavedChangesWindowShowing();
    }

    public void updateCalendarStyles(Set<OverbookingManagementPresenter.ShowOverrides> overrideFilterSelection) {
        view.getLeftCalendar().updateStyles(overrideFilterSelection);
        view.getRightCalendar().updateStyles(overrideFilterSelection);
    }

    @Override
    public boolean hasChanges() {
        return hasPendingOverrides();
    }

    public Map<HeatMapRangeAndColorsConfig, List<HeatMapRangeAndColors>> getHeatMapConfigWithRangeAndColors(Date startDate,
                                                                                                            Date endDate) {
        return heatMapService.getHeatMapConfigWithRangeAndColors(startDate, endDate, isFiscalCalendarEnabled());
    }

    public boolean isFiscalCalendarEnabled() {
        return UiUtils.isFiscalCalendarEnabled();
    }

    public boolean isDisplayProfitMetricsEnabled() {
        return simplifiedWhatIfService.isDisplayProfitMetricsEnabled();
    }

    public void fetchAndStoreCeilingDataToMap(int roomTypeId) {
        if (ceilingDefaultDataMap.containsKey(roomTypeId)) {
            return;
        }
        final Map<Integer, Integer> roomTypeCeilingDefaultDataMap = overbookingService.getCeilingDefaultDataMap(roomTypeId);
        ceilingDefaultDataMap.put(roomTypeId, roomTypeCeilingDefaultDataMap);
    }

    public void loadAndStoreAllCeilingDataToMap() {
        ceilingDefaultDataMap = overbookingService.loadAllCeilingDefaultDataMap(accomTypes);
    }

    public Map<Integer, Map<Integer, Integer>> getCeilingDefaultDataMap() {
        return ceilingDefaultDataMap;
    }

    public boolean isEnablePhysicalCapacityConsideration() {
        return enablePhysicalCapacityConsideration;
    }

    public boolean isPropertyLevelSelected() {
        return view.getLeftCalendar().getAccomTypeId().equals(PROPERTY_ID);
    }

    public List<InventoryGroupDto> getInventoryGroups() {
        inventoryGroupDetails = inventoryGroupService.getInventoryGroupDetails();
        inventoryGroups = InventoryGroupUtil.getInventoryGroups(userPreferences, inventoryGroupService.getInventoryGroups());
        return inventoryGroups;
    }

    public boolean isInventoryGroupSelectionShown() {
        return !inventoryGroupService.getInventoryGroups().isEmpty();
    }

    public void setLastSelectedInventoryGroup() {
        lastSelectedInventoryGroup = view.getLastSelectedInventoryGroup();
    }

    public void checkLastSelectedInventoryGroup() {
        if (lastSelectedInventoryGroup != null) {
            Optional<InventoryGroupDto> matchingInventoryGroup = inventoryGroups.stream().filter(ig -> ig.getName().equals(lastSelectedInventoryGroup.getName())).findFirst();
            if (matchingInventoryGroup.isPresent()) {
                view.setInventoryGroupSelection(matchingInventoryGroup.get());
            }
        }
    }

    public void clearInventoryGroups() {
        if (inventoryGroups != null) {
            inventoryGroups.clear();
        }
    }

    @ForTesting
    public void setEnablePhysicalCapacityConsideration(boolean enablePhysicalCapacityConsideration) {
        this.enablePhysicalCapacityConsideration = enablePhysicalCapacityConsideration;
    }

    public boolean isAdvancedPriceRankingEnabled() {
        return isAdvancedPriceRankingEnabled;
    }

    @ForTesting
    public void setAdvancedPriceRankingEnabled(boolean advancedPriceRankingEnabled) {
        isAdvancedPriceRankingEnabled = advancedPriceRankingEnabled;
    }

    public boolean isROHEnabled() {
        return isROHEnabled;
    }

    @ForTesting
    public void setROHEnabled(boolean ROHEnabled) {
        isROHEnabled = ROHEnabled;
    }

    @ForTesting
    public void setOvbkOptimizationEnabled(boolean isEnabled){
        isOvbkOptimizationEnabled = isEnabled;
    }

    public boolean isRoomTypeRecodingUIEnabled() {
        return isRoomTypeRecodingUIEnabled;
    }

    @ForTesting
    public void setRoomTypeRecodingUIEnabled(boolean roomTypeRecodingUIEnabled) {
        isRoomTypeRecodingUIEnabled = roomTypeRecodingUIEnabled;
    }

    public boolean isEnableCustomizedHeatMap() {
        return isEnableCustomizedHeatMap;
    }

    @ForTesting
    public void setEnableCustomizedHeatMap(boolean enableCustomizedHeatMap) {
        isEnableCustomizedHeatMap = enableCustomizedHeatMap;
    }

    public boolean isContinuousPricingEnabled() {
        return isContinuousPricingEnabled;
    }

    @ForTesting
    public void setContinuousPricingEnabled(boolean continuousPricingEnabled) {
        isContinuousPricingEnabled = continuousPricingEnabled;
    }

    public boolean isBarByLOS() {
        return isBarByLOS;
    }

    @ForTesting
    public void setBarByLOS(boolean barByLOS) {
        isBarByLOS = barByLOS;
    }

    public int getMaxLOS() {
        return maxLOS;
    }

    @ForTesting
    public void setMaxLOS(int maxLOS) {
        this.maxLOS = maxLOS;
    }

    @ForTesting
    public void setBaseRoomTypeList(List<AccomType> accomTypes) {
        baseRoomTypeList = accomTypes;
    }

    public boolean isBaseRoomType(AccomType accomType) {
        return baseRoomTypeList.contains(accomType);
    }


    public boolean isLicenseFeatureValueConfigured(String cacheLicenseKey) {
        return licenseService.isLicenseFeatureValueConfigured(PacmanWorkContextHelper.getPropertyId(), cacheLicenseKey);
    }

    public boolean isLicenseFeatureEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FEATURE_LICENSING_ENABLED);
    }

    public boolean isInternalUser() {
        String userId = PacmanWorkContextHelper.getUserId();
        GlobalUser globalUser = userGlobalDBService.getGlobalUserById(Integer.parseInt(userId));
        return nonNull(globalUser) && globalUser.isInternal();
    }

}
