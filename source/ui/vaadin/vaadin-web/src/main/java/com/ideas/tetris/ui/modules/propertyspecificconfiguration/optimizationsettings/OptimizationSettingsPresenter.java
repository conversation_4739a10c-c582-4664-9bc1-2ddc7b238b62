package com.ideas.tetris.ui.modules.propertyspecificconfiguration.optimizationsettings;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.lrvdroprestriction.entity.LrvDropRestrictionConfiguration;
import com.ideas.tetris.pacman.services.lrvdroprestriction.service.LrvDropRestrictionService;
import com.ideas.tetris.pacman.services.optimizationsettings.OptimizationSettingsEnum;
import com.ideas.tetris.pacman.services.optimizationsettings.PriceDropOccupancyType;
import com.ideas.tetris.pacman.services.optimizationsettings.PriceDropRestrictionConfig;
import com.ideas.tetris.pacman.services.optimizationsettings.PriceDropRestrictionConfigAudit;
import com.ideas.tetris.pacman.services.pricedroprestriction.PriceDropRestrictionService;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttribute;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum;
import com.ideas.tetris.pacman.services.roa.attribute.service.ROAPropertyAttributeService;
import com.ideas.tetris.pacman.services.roa.dto.PropertyAttributeRevision;
import com.ideas.tetris.pacman.util.SeasonService;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import com.ideas.tetris.ui.common.component.SeasonsFilterBar;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.common.util.models.SingleValueModel;
import com.ideas.tetris.ui.modules.propertyspecificconfiguration.optimizationsettings.lrvdroprestriction.LrvDropConfigTracker;
import com.ideas.tetris.ui.modules.propertyspecificconfiguration.optimizationsettings.lrvdroprestriction.LrvDropRestrictionDto;
import com.ideas.tetris.ui.modules.propertyspecificconfiguration.optimizationsettings.lrvdroprestriction.LrvRoomClassRemainingCapacity;
import com.ideas.tetris.ui.modules.reports.usermodified.Status;
import org.fest.util.VisibleForTesting;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum.LRV_DROP_MIN_DTA;
import static com.ideas.tetris.ui.modules.propertyspecificconfiguration.optimizationsettings.PriceDropRestrictionSeasonUiWrapper.defaultSeason;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import org.springframework.beans.factory.annotation.Autowired;
import com.ideas.tetris.spring.SpringAutowired;

@SpringAutowired
public class OptimizationSettingsPresenter extends TetrisPresenter<OptimizationSettingsView, Void> {

    public static final String PRICE_DROP_MIN_REV_GAIN = "PRICE_DROP_MIN_REV_GAIN";
    public static final String PRICE_DROP_MAX_VALUE = "PRICE_DROP_MAX_VALUE";
    public static final String PRICE_DROP_MIN_DTA = "PRICE_DROP_MIN_DTA";
    private List<PriceDropRestrictionSeasonUiWrapper> priceDropRestrictionSeasons;
    private static final String INPUT_WARNING_MESSGE = "pleaseEnterValidValues";
    private static final String LRV_PROPERTY_ATTRIBUTE = "enable.lrv.drop.restrictions";

    @Autowired
    SeasonService seasonService;

    private final HashMap<Integer, String> attributeNamePrefixMap = new HashMap<>() {
        {
            put(0, UiUtils.getText("added.capacity.for"));
            put(1, UiUtils.getText("updated.capacity.for"));
            put(2, UiUtils.getText("deleted.capacity.for"));
        }
    };

    public void logErrors(String message, Exception exception) {
        logger.error(message, exception);
    }

    public String getPermissionKeyForSelectedPage() {
        return TetrisPermissionKey.FUNCTION_PROPERTY_SPECIFIC_CONFIG_OPTIMIZATION_SETTINGS;
    }

    public void deleteSeason(PriceDropRestrictionSeasonUiWrapper wrapper) {
        PriceDropRestrictionConfig entity = getPriceDropRestrictionConfig(wrapper.getPriceDropRestrictionDowDto().getPriceDropRestrictionConfigWrapperList());
        deletePriceDropRestrictionConfigs(List.of(entity));
    }

    public PriceDropRestrictionSeasonUiWrapper getDefaultSeason() {
        return defaultSeason();
    }

    public void applyNewSeason(PriceDropRestrictionSeasonUiWrapper season) {
        saveValidSeasonValuesToTheDatabase(season.getPriceDropRestrictionDowDto().getPriceDropRestrictionConfigWrapperList(), season.getStartDate(), season.getEndDate());
    }

    private List<PriceDropRestrictionSeasonUiWrapper> getOverlappingSeasons(PriceDropRestrictionSeasonUiWrapper newSeason) {
        return seasonService.getOverlappingSeasons(priceDropRestrictionSeasons, newSeason);
    }

    public void applyOverlappingSeasons(PriceDropRestrictionSeasonUiWrapper season) {
        List<PriceDropRestrictionSeasonUiWrapper> seasonsToDelete = getOverlappingSeasons(season);
        seasonsToDelete.forEach(this::deleteSeason);
        List<PriceDropRestrictionSeasonUiWrapper> updatedSeasons = seasonService.applySplit(priceDropRestrictionSeasons, season, getSystemDateAsLocalDate());
        updatedSeasons.forEach(this::deleteOutOfRangeDOWData);
        updatedSeasons.forEach(this::applyNewSeason);
    }

    public void closeWindowWithSuccessMessage(PriceDropRestrictionSeasonWindow window) {
        showSaveSuccessMessage();
        init();
        window.close();
    }

    public boolean checkOverlappingSeasons(PriceDropRestrictionSeasonUiWrapper newSeason) {
        return seasonService.willSplitOccur(priceDropRestrictionSeasons, newSeason);
    }

    public void initDecoupledLrvDropRestrictionSettings() {
        view.initLrvDropRestriction(getDecoupledLrvDropRestrictionConfigurations());
    }

    public void initCoupledLrvDropRestrictionSettings() {
        view.initLrvDropRestriction(getLrvDropRestrictionConfigurations());
    }

    enum PRICE_DROP_RESTRICTION {
        PRICE_DROP_MIN_REV_GAIN {
            @Override
            String getKey() {
                return "revenue.threshold.for.price";
            }
        },
        PRICE_DROP_MAX_VALUE {
            @Override
            String getKey() {
                return "maximum.price.drop";
            }
        },
        PRICE_DROP_MIN_DTA {
            @Override
            String getKey() {
                return "daysToArrival";
            }
        };

        abstract String getKey();
    }

    OptimizationSettingsUIWrapper uiWrapper;

    @Autowired
    ROAPropertyAttributeService roaPropertyAttributeService;

    @Autowired
    LrvDropRestrictionService lrvDropRestrictionService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    PriceDropRestrictionService priceDropRestrictionService;

    private static final String PACMAN_FEATURE_DISPLAY_CANCEL_REBOOK_PERCENTAGE_OPTION = "pacman.feature.DisplayCancelRebookPercentageOption";
    boolean cancelRebookAllowed;
    boolean isOptimisationSettingsTabEnabled;
    boolean enablePriceDropRestriction;
    boolean enableLrvDropRestriction;
    boolean enablePriceDropRestrictionDow;
    boolean decoupleLrvDropRestrictionEnabled;
    boolean hideOptimizationMethodOption;
    String optDynamicPrice;
    private boolean defaultConfigPresentInDB = true;
    private boolean isPriceDropProtectionDeleted = false;

    public static final List<String> PRICE_DROP_ATTRIBUTE_NAMES = new ArrayList<String>() {
        {
            add(PRICE_DROP_MIN_DTA);
            add(PRICE_DROP_MIN_REV_GAIN);
            add(PRICE_DROP_MAX_VALUE);
        }
    };

    @Override
    public void onViewOpened(Void aVoid) {
        init();
    }

    @Override
    public void onWorkContextChange(WorkContextType workContextType) {
        refreshPage();
        init();
    }

    public void init() {
        PriceDropRestrictionDowLayout priceDropRestrictionDowLayout = view.priceDropRestrictionDowLayout;
        priceDropRestrictionDowLayout.closeSeasonWindow();
        priceDropRestrictionDowLayout.setSeasonTableHasChanges(false);
        cancelRebookAllowed = Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(PACMAN_FEATURE_DISPLAY_CANCEL_REBOOK_PERCENTAGE_OPTION));
        isOptimisationSettingsTabEnabled = Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.DISPLAY_OPTIMIZATION_SETTINGS_TAB.value()));
        hideOptimizationMethodOption = Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.HIDE_OPTIMIZATION_METHOD_IN_SETTINGS_TAB.value()));
        enablePriceDropRestriction = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE);
        enableLrvDropRestriction = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_LRV_DROP_RESTRICTION_FEATURE);
        enablePriceDropRestrictionDow = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE_DOW_AND_SEASONS);
        decoupleLrvDropRestrictionEnabled = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DECOUPLE_LRV_DROP_RESTRICTION_ENABLED);

        view.showCancelRebookPercentage(cancelRebookAllowed);
        view.showOptimizationMethodOption(isOptimisationSettingsTabEnabled && !hideOptimizationMethodOption);
        view.showPriceDropRestrictionOption(enablePriceDropRestriction && !enablePriceDropRestrictionDow);
        view.showPriceDropRestrictionDOW(enablePriceDropRestriction && enablePriceDropRestrictionDow);

        if (!isDecoupleLrvDropRestrictionEnabled()) {
            view.showLrvDropRestrictionOption(enablePriceDropRestriction && enableLrvDropRestriction);
        } else {
            view.showLrvDropRestrictionOption(enableLrvDropRestriction);
        }

        priceDropRestrictionDowLayout.addPresenter(this);
        priceDropRestrictionDowLayout.initialiseFilterCheckboxes();
        priceDropRestrictionDowLayout.resetSearchFields();

        initData();
    }

    private void initData() {
        initOptimizationOptionData();
        if (cancelRebookAllowed) {
            populateCancelRebookPercentage();
        }
        view.initForm(uiWrapper);
        if (enablePriceDropRestriction && !enablePriceDropRestrictionDow) {
            view.initPriceDropRestriction(getPriceDropRestrictionDto());
        }
        if (enablePriceDropRestriction && enablePriceDropRestrictionDow) {
            initializePriceDropByDOWAndSeasons();
        }
        if (enableLrvDropRestriction && decoupleLrvDropRestrictionEnabled) {
            initDecoupledLrvDropRestrictionSettings();
        } else if (enableLrvDropRestriction) {
            initCoupledLrvDropRestrictionSettings();
        }
    }

    private void initializePriceDropByDOWAndSeasons() {
        view.initPriceDropRestrictionConfigs(getDropRestrictionTableWrapperData(), defaultConfigPresentInDB);
        priceDropRestrictionSeasons = getSeasons();
        view.priceDropRestrictionDowLayout.updateSeasons(getSeasonsOnEventChange(SeasonsFilterBar.SeasonsFilterBarType.PAST, false));
    }

    private PriceDropRestrictionDowDto getDropRestrictionTableWrapperData() {
        PriceDropRestrictionDowDto dto = new PriceDropRestrictionDowDto();
        List<PriceDropRestrictionConfigWrapper> priceDropRestrictionTableWrapperData = this.getPriceDropRestrictionTableWrapperData();
        dto.setPriceDropRestrictionConfigWrapperList(priceDropRestrictionTableWrapperData);
        dto.setEnablePriceDropRestriction(priceDropRestrictionTableWrapperData.stream().anyMatch(PriceDropRestrictionConfigWrapper::isEnablePriceDropCheckboxTicked));
        return dto;
    }

    public void reset() {
        initializePriceDropByDOWAndSeasons();
    }

    private LrvDropRestrictionDto getLrvDropRestrictionConfigurations() {
        List<LrvDropRestrictionConfiguration> configurations = lrvDropRestrictionService.getConfiguration();
        LrvDropRestrictionDto lrvDropRestriction = new LrvDropRestrictionDto();
        lrvDropRestriction.setEnableLrvDropRestriction(shouldCheckLrvDropRestriction(configurations));
        lrvDropRestriction.setRoomClassCapacities(getRoomClassMinCapacities(configurations));
        return lrvDropRestriction;
    }

    private LrvDropRestrictionDto getDecoupledLrvDropRestrictionConfigurations() {
        LrvDropRestrictionDto lrvDropRestrictionDto = new LrvDropRestrictionDto();
        List<LrvDropRestrictionConfiguration> configurations = lrvDropRestrictionService.getConfiguration();
        Optional<String> lrvDropMinDta = Optional.ofNullable(roaPropertyAttributeService.getOverridenAttributeValueForAttributeName(LRV_DROP_MIN_DTA.getAttributeName()));
        lrvDropRestrictionDto.setEnableLrvDropRestriction(lrvDropMinDta.isPresent() && shouldCheckLrvDropRestriction(configurations));

        lrvDropRestrictionDto.setRoomClassCapacities(getRoomClassMinCapacities(configurations));
        lrvDropRestrictionDto.setDecoupleLrvDropRestrictionEnabled(true);
        lrvDropMinDta.ifPresent(lrv -> lrvDropRestrictionDto.setDaysToArrival(Integer.valueOf(lrv)));

        return lrvDropRestrictionDto;
    }

    private List<LrvRoomClassRemainingCapacity> getRoomClassMinCapacities(List<LrvDropRestrictionConfiguration> configurations) {
        List<LrvRoomClassRemainingCapacity> roomClassCapacities = new ArrayList<>();
        configurations.forEach(config -> roomClassCapacities.add(createLrvRoomClassRemainingCapacity(config)));
        return roomClassCapacities;
    }

    public List<PriceDropRestrictionSeasonUiWrapper> getSeasonsOnEventChange(SeasonsFilterBar.SeasonsFilterBarType type, boolean isSelected) {
        return priceDropRestrictionSeasons.stream()
                .peek(priceDropRestrictionSeasonUiWrapper -> updateVisibility(priceDropRestrictionSeasonUiWrapper, type, isSelected))
                .filter(PriceDropRestrictionSeasonUiWrapper::isVisible)
                .collect(Collectors.toList());
    }

    private void updateVisibility(PriceDropRestrictionSeasonUiWrapper priceDropRestrictionSeasonUiWrapper, SeasonsFilterBar.SeasonsFilterBarType type, boolean isSelected) {
        if (isType(priceDropRestrictionSeasonUiWrapper, type)) {
            priceDropRestrictionSeasonUiWrapper.setVisible(isSelected);
        }
    }

    private boolean isType(PriceDropRestrictionSeasonUiWrapper priceDropRestrictionSeasonUiWrapper, SeasonsFilterBar.SeasonsFilterBarType type) {
        LocalDate systemDateAsLocalDate = getSystemDateAsLocalDate();
        switch (type) {
            case PAST:
                return getEndDate(priceDropRestrictionSeasonUiWrapper).compareTo(systemDateAsLocalDate) < 0;
            case FUTURE:
                return getStartDate(priceDropRestrictionSeasonUiWrapper).compareTo(systemDateAsLocalDate) > 0;
            case PRESENT:
                return getStartDate(priceDropRestrictionSeasonUiWrapper).compareTo(systemDateAsLocalDate) <= 0
                        && getEndDate(priceDropRestrictionSeasonUiWrapper).compareTo(systemDateAsLocalDate) >= 0;
        }
        return false;
    }

    private LocalDate getStartDate(PriceDropRestrictionSeasonUiWrapper row) {
        return row.getStartDate();
    }

    private LocalDate getEndDate(PriceDropRestrictionSeasonUiWrapper row) {
        return row.getEndDate();
    }

    protected List<PriceDropRestrictionSeasonUiWrapper> getSeasons() {
        List<PriceDropRestrictionConfig> priceDropRestrictionConfig = priceDropRestrictionService.findAllSeasons();
        return priceDropRestrictionConfig.stream()
                .map(this::createSeasonConfigWrapper)
                .collect(Collectors.toList());
    }

    private PriceDropRestrictionSeasonUiWrapper createSeasonConfigWrapper(PriceDropRestrictionConfig config) {
        PriceDropRestrictionSeasonUiWrapper wrapper = new PriceDropRestrictionSeasonUiWrapper();
        wrapper.setStartDate(LocalDate.fromDateFields(config.getStartDate()));
        wrapper.setEndDate(LocalDate.fromDateFields(config.getEndDate()));
        PriceDropRestrictionDowDto priceDropRestrictionDowDto = new PriceDropRestrictionDowDto();
        priceDropRestrictionDowDto.setPriceDropRestrictionConfigWrapperList(getPriceDropRestrictionConfigWrappers(config));
        wrapper.setPriceDropRestrictionDowDto(priceDropRestrictionDowDto);
        return wrapper;
    }

    private boolean shouldCheckLrvDropRestriction(List<LrvDropRestrictionConfiguration> configurations) {
        return isNotEmpty(configurations) &&
                configurations.stream().anyMatch(configuration -> null != configuration.getMinRemainingCapacity());
    }

    private LrvRoomClassRemainingCapacity createLrvRoomClassRemainingCapacity(LrvDropRestrictionConfiguration config) {
        LrvRoomClassRemainingCapacity roomClassCap = new LrvRoomClassRemainingCapacity();
        roomClassCap.setAccomClassId(config.getAccomClassId());
        roomClassCap.setAccomClassName(config.getAccomClassName());
        roomClassCap.setRemainingCapacity(SingleValueModel.createSingleValueModel(config.getMinRemainingCapacity()));
        roomClassCap.setMaxAllowedRoomCapacity(config.getMaxAllowedRoomCapacity());
        return roomClassCap;
    }

    private PriceDropRestrictionDto getPriceDropRestrictionDto() {
        PriceDropRestrictionDto priceDropRestrictionDto = new PriceDropRestrictionDto();
        priceDropRestrictionDto.setEnableLrvDropRestriction(enableLrvDropRestriction);
        Map<String, String> attributeValueWithName =
                roaPropertyAttributeService.getAttributeValueWithNameByAttributeNames(PRICE_DROP_ATTRIBUTE_NAMES);
        if (!attributeValueWithName.isEmpty()) {
            priceDropRestrictionDto.setEnablePriceDropRestriction(true);
            if (null != attributeValueWithName.get(PRICE_DROP_MIN_DTA)) {
                priceDropRestrictionDto.setDaysToArrival(Integer.valueOf(attributeValueWithName.get(PRICE_DROP_MIN_DTA)));
            }
            if (null != attributeValueWithName.get(PRICE_DROP_MAX_VALUE)) {
                priceDropRestrictionDto.setMaximumPriceDrop(new BigDecimal(attributeValueWithName.get(PRICE_DROP_MAX_VALUE)));
            }
            if (null != attributeValueWithName.get(PRICE_DROP_MIN_REV_GAIN)) {
                priceDropRestrictionDto.setRevenueThresholdForPrice(new BigDecimal(attributeValueWithName.get(PRICE_DROP_MIN_REV_GAIN)));
            }
        }
        priceDropRestrictionDto.setDecoupleLrvDropRestriction(isDecoupleLrvDropRestrictionEnabled());
        return priceDropRestrictionDto;
    }

    private void initOptimizationOptionData() {
        populateOptimizationOption();
        populateOptimizationChangeHistory();
    }

    private void populateCancelRebookPercentage() {
        PropertyAttribute cancelRebookPercentage = roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT);
        String cancelRebookPercentageValue = roaPropertyAttributeService.getAttributeValue(cancelRebookPercentage.getId());
        cancelRebookPercentage.setValue(cancelRebookPercentageValue);
        uiWrapper.setCancelRebookPercentage(cancelRebookPercentage);
    }

    private void populateOptimizationChangeHistory() {
        List<PropertyAttributeRevisionUIWrapper> propertyAttributeRevisionUIWrappers = new ArrayList<PropertyAttributeRevisionUIWrapper>();

        PropertyAttribute propertyAttribute = roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.OPT_DYNAMIC_PRICE);
        propertyAttributeRevisionUIWrappers.addAll(getPropertyAttributeRevisionUIWrappers(PropertyAttributeEnum.OPT_DYNAMIC_PRICE.getAttributeName(), roaPropertyAttributeService.fetchAttributeRevisions(propertyAttribute.getId())));

        propertyAttribute = roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT);
        propertyAttributeRevisionUIWrappers.addAll(getPropertyAttributeRevisionUIWrappers(PropertyAttributeEnum.CANCEL_REBOOK_PCT.getAttributeName(), roaPropertyAttributeService.fetchAttributeRevisions(propertyAttribute.getId())));

        propertyAttributeRevisionUIWrappers.addAll(getPriceDropRestrictionHistory());
        propertyAttributeRevisionUIWrappers.addAll(getLrvDropRestrictionHistory());

        sortPropertyAttributeRevisionUIWrappers(propertyAttributeRevisionUIWrappers);
        uiWrapper.setPropertyAttributeRevisionUIWrapper(propertyAttributeRevisionUIWrappers);
    }


    protected List<PropertyAttributeRevisionUIWrapper> getPriceDropRestrictionHistory() {
        List<PropertyAttributeRevisionUIWrapper> propertyAttributeRevisionUIWrappers = new ArrayList<>();
        List<PropertyAttribute> propertyAttributes = roaPropertyAttributeService.fetchPropertyAttributesByName(
                Arrays.asList(PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN.toString(),
                        PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE.toString(),
                        PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA.toString())
        );
        if (isNotEmpty(propertyAttributes)) {
            List<Integer> propertyAttributeIds = propertyAttributes.stream().map(propertyAttr -> propertyAttr.getId()).collect(Collectors.toList());
            List<PropertyAttributeRevision> priceDropPropertyAttributeRevisions = roaPropertyAttributeService.fetchAttributeRevisionsForMultipleAttributes(propertyAttributeIds);

            if (isNotEmpty(priceDropPropertyAttributeRevisions)) {
                final String propertyAttributeName = "enable.price.drop.restrictions";
                convertPropertyAttributeRevisionsToUIWrappers(propertyAttributeRevisionUIWrappers, propertyAttributeName, priceDropPropertyAttributeRevisions);
            }
        }
        return propertyAttributeRevisionUIWrappers;
    }

    private List<PropertyAttributeRevisionUIWrapper> getLrvDropRestrictionHistory() {
        List<PropertyAttributeRevisionUIWrapper> propertyAttributeRevisionUIWrappers = new ArrayList<>();
        List<PropertyAttributeRevision> lrvDropRestrictionRevisions = lrvDropRestrictionService.fetchAllLrvDropRestrictionConfigurationRevisions();
        if (isNotEmpty(lrvDropRestrictionRevisions)) {
            convertPropertyAttributeRevisionsToUIWrappers(propertyAttributeRevisionUIWrappers, LRV_PROPERTY_ATTRIBUTE, lrvDropRestrictionRevisions);
        }
        return propertyAttributeRevisionUIWrappers;
    }

    private List<PropertyAttributeRevisionUIWrapper> getDecoupledLrvDropRestrictionHistory() {
        List<PropertyAttributeRevision> lrvDropRestrictionRevisions = new ArrayList<>(lrvDropRestrictionService.fetchAllLrvDropRestrictionConfigurationRevisions());
        Optional<PropertyAttribute> propertyAttributeDta = Optional.ofNullable(roaPropertyAttributeService.getPropertyAttribute(LRV_DROP_MIN_DTA));

        propertyAttributeDta.ifPresent(propertyAttribute -> {
            List<PropertyAttributeRevision> lrvMinDta = roaPropertyAttributeService.fetchAttributeRevisionsForMultipleAttributes(Collections.singletonList(propertyAttribute.getId()));
            if (isNotEmpty(lrvMinDta)) {
                lrvDropRestrictionRevisions.addAll(lrvMinDta);
            }
        });

        if (isNotEmpty(lrvDropRestrictionRevisions)) {
            return convertPropertyAttributeRevisionsToUIWrappersForDecoupleLrv(lrvDropRestrictionRevisions);
        }

        return new ArrayList<>();
    }

    private List<PropertyAttributeRevisionUIWrapper> convertPropertyAttributeRevisionsToUIWrappersForDecoupleLrv(List<PropertyAttributeRevision> lrvDropRestrictionsAttributeRevisions) {
        List<PropertyAttributeRevision> sortedPropertyAttributeRevisions = getPropertyAttributeRevisions(lrvDropRestrictionsAttributeRevisions);
        List<PropertyAttributeRevisionUIWrapper> propertyAttributeRevisionUIWrappers = new ArrayList<>();
        LrvDropConfigTracker lrvDropConfigTracker = new LrvDropConfigTracker();
        for (PropertyAttributeRevision propertyAttribute : sortedPropertyAttributeRevisions) {
            getLrvPropertyAttributeRevisionUiWrapperEntry(propertyAttribute).ifPresent(propertyAttributeRevisionUIWrappers::add);
            addLrvDropRestrictionEntry(lrvDropConfigTracker, propertyAttribute).ifPresent(propertyAttributeRevisionUIWrappers::add);
        }
        return propertyAttributeRevisionUIWrappers;
    }

    private List<PropertyAttributeRevision> getPropertyAttributeRevisions(List<PropertyAttributeRevision> lrvDropRestrictionsAttributeRevisions) {
        return lrvDropRestrictionsAttributeRevisions.stream()
                .sorted(Comparator.comparing(PropertyAttributeRevision::getLastUpdatedDateTime))
                .collect(Collectors.toList());
    }

    private Optional<PropertyAttributeRevisionUIWrapper> addLrvDropRestrictionEntry(LrvDropConfigTracker lrvConfigTracker, PropertyAttributeRevision propertyAttribute) {
        if (propertyAttribute.getRevType() == 0 && !lrvConfigTracker.allLrvConfigsAdded()) {
            lrvConfigTracker.updateLrvConfigAddition(isLrvMinDta(propertyAttribute));
            if (lrvConfigTracker.allLrvConfigsAdded()) {
                return Optional.of(createPropertyAttributeRestrictionEntry(propertyAttribute, LRV_PROPERTY_ATTRIBUTE, "True"));
            }
        }
        if (propertyAttribute.getRevType() == 2 && lrvConfigTracker.allLrvConfigsAdded()) {
            lrvConfigTracker.resetLrvDropConfigTracker();
            return Optional.of(createPropertyAttributeRestrictionEntry(propertyAttribute, LRV_PROPERTY_ATTRIBUTE, "False"));
        }
        return Optional.empty();
    }

    private Optional<PropertyAttributeRevisionUIWrapper> getLrvPropertyAttributeRevisionUiWrapperEntry(PropertyAttributeRevision propertyAttribute) {
        return Optional.ofNullable((propertyAttribute.getRevType() == 0 ||
                (propertyAttribute.getRevType() == 1 && propertyAttribute.getStatusId() != 2) ?
                convertLrvPropertyRevisionsToUiWrapper(propertyAttribute) : null));
    }

    private boolean isLrvMinDta(PropertyAttributeRevision propertyAttribute) {
        return LRV_DROP_MIN_DTA.getAttributeName().equals(propertyAttribute.getAttributeName());
    }

    private PropertyAttributeRevisionUIWrapper convertLrvPropertyRevisionsToUiWrapper(PropertyAttributeRevision propertyRevision) {
        return isLrvMinDta(propertyRevision) ?
                new PropertyAttributeRevisionUIWrapper(propertyRevision, getText("daysToArrival")) :
                new PropertyAttributeRevisionUIWrapper(propertyRevision, getKeyForAttributeName(propertyRevision));
    }

    private void convertPropertyAttributeRevisionsToUIWrappers(List<PropertyAttributeRevisionUIWrapper> propertyAttributeRevisionUIWrappers, String propertyAttributeName, List<PropertyAttributeRevision> dropRestrictionsAttributeRevisions) {
        List<PropertyAttributeRevision> sortedPropertyAttributeRevisions = dropRestrictionsAttributeRevisions.stream()
                .sorted(Comparator.comparing(PropertyAttributeRevision::getLastUpdatedDateTime))
                .collect(Collectors.toList());

        propertyAttributeRevisionUIWrappers.add(createEarliestEntry(sortedPropertyAttributeRevisions, propertyAttributeName));

        boolean isPreviousActive = true;
        for (PropertyAttributeRevision propertyRevision : sortedPropertyAttributeRevisions) {
            if (isPreviousActive && (propertyRevision.getRevType().equals(1) || propertyRevision.getRevType().equals(2)) && propertyRevision.getStatusId().equals(Status.INACTIVE.getId())) {
                PropertyAttributeRevisionUIWrapper disablePriceDropRestrictionEntry = createPropertyAttributeRestrictionEntry(propertyRevision, propertyAttributeName, "False");
                propertyAttributeRevisionUIWrappers.add(disablePriceDropRestrictionEntry);
                isPreviousActive = false;
            } else if (!isPreviousActive && propertyRevision.getStatusId().equals(Status.ACTIVE.getId())) {
                PropertyAttributeRevisionUIWrapper enablePriceDropRestrictionEntry = createPropertyAttributeRestrictionEntry(propertyRevision, propertyAttributeName, "True");
                propertyAttributeRevisionUIWrappers.add(enablePriceDropRestrictionEntry);
                isPreviousActive = true;
            }

            if (propertyRevision.getStatusId().equals(Status.ACTIVE.getId())) {
                PropertyAttributeRevisionUIWrapper wrapper = new PropertyAttributeRevisionUIWrapper(propertyRevision, getKeyForAttributeName(propertyRevision));
                propertyAttributeRevisionUIWrappers.add(wrapper);
                isPreviousActive = true;
            }
        }
    }

    private PropertyAttributeRevisionUIWrapper createEarliestEntry(List<PropertyAttributeRevision> propertyAttributeRevisions, String propertyAttributeName) {
        PropertyAttributeRevision propertyAttributeRevision = propertyAttributeRevisions.get(0);
        return createPropertyAttributeRestrictionEntry(propertyAttributeRevision, propertyAttributeName, "True");
    }

    private PropertyAttributeRevisionUIWrapper createPropertyAttributeRestrictionEntry(PropertyAttributeRevision propertyAttributeRevision, String propertyAttributeName, String value) {
        PropertyAttributeRevision propertyAttributeRev = new PropertyAttributeRevision();
        propertyAttributeRev.setLastUpdatedDateTime(propertyAttributeRevision.getLastUpdatedDateTime().minusMillis(1));
        propertyAttributeRev.setUserName(propertyAttributeRevision.getUserName());
        propertyAttributeRev.setStatusId(propertyAttributeRevision.getStatusId());
        propertyAttributeRev.setRevType(propertyAttributeRevision.getRevType().intValue());
        return new PropertyAttributeRevisionUIWrapper(propertyAttributeRev,
                getText(propertyAttributeName), value);
    }

    private String getKeyForAttributeName(PropertyAttributeRevision attributeRevision) {
        return isKeyPresent(attributeRevision.getAttributeName()) ?
                getText(PRICE_DROP_RESTRICTION.valueOf(attributeRevision.getAttributeName()).getKey()) :
                getPrefix(attributeRevision.getRevType()) + attributeRevision.getAttributeName();
    }

    private String getPrefix(Integer revType) {
        return attributeNamePrefixMap.getOrDefault(revType, EMPTY) + " ";
    }

    private boolean isKeyPresent(String attributeName) {
        HashSet<String> values = new HashSet<>();
        for (PRICE_DROP_RESTRICTION enumValue : PRICE_DROP_RESTRICTION.values()) {
            values.add(enumValue.name());
        }
        return values.contains(attributeName);
    }


    private List<PropertyAttributeRevisionUIWrapper> sortPropertyAttributeRevisionUIWrappers
            (List<PropertyAttributeRevisionUIWrapper> propertyAttributeRevisionUIWrappers) {
        Collections.sort(propertyAttributeRevisionUIWrappers, new Comparator<PropertyAttributeRevisionUIWrapper>() {
            @Override
            public int compare(PropertyAttributeRevisionUIWrapper propertyAttributeRevisionUIWrapper1, PropertyAttributeRevisionUIWrapper propertyAttributeRevisionUIWrapper2) {
                if (propertyAttributeRevisionUIWrapper1.getPropertyAttributeRevision().getLastUpdatedDateTime() != null &&
                        propertyAttributeRevisionUIWrapper2.getPropertyAttributeRevision().getLastUpdatedDateTime() != null) {
                    return propertyAttributeRevisionUIWrapper2.getPropertyAttributeRevision().getLastUpdatedDateTime()
                            .compareTo(propertyAttributeRevisionUIWrapper1.getPropertyAttributeRevision().getLastUpdatedDateTime());
                } else {
                    return 0;
                }
            }
        });
        return propertyAttributeRevisionUIWrappers;
    }

    private List<PropertyAttributeRevisionUIWrapper> getPropertyAttributeRevisionUIWrappers(String propertyAttributeName, List<PropertyAttributeRevision> propertyAttributeRevisions) {
        List<PropertyAttributeRevisionUIWrapper> propertyAttributeRevisionUIWrappers = new ArrayList<PropertyAttributeRevisionUIWrapper>();
        if (propertyAttributeRevisions == null) {
            return propertyAttributeRevisionUIWrappers;
        }
        for (PropertyAttributeRevision propertyAttributeRevision : propertyAttributeRevisions) {
            PropertyAttributeRevisionUIWrapper propertyAttributeRevisionUIWrapper = new PropertyAttributeRevisionUIWrapper(propertyAttributeRevision, propertyAttributeName);
            propertyAttributeRevisionUIWrappers.add(propertyAttributeRevisionUIWrapper);
        }
        return propertyAttributeRevisionUIWrappers;
    }

    private void populateOptimizationOption() {
        uiWrapper = new OptimizationSettingsUIWrapper();
        OptimizationMethodOption option = new OptimizationMethodOption();
        PropertyAttribute propertyAttribute = roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.OPT_DYNAMIC_PRICE);
        optDynamicPrice = roaPropertyAttributeService.getAttributeValue(propertyAttribute.getId());
        option.setId(Integer.valueOf(optDynamicPrice));
        uiWrapper.setOptimizationMethodOption(option);
    }

    public void save() {
        PropertyAttribute propertyAttribute = roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.OPT_DYNAMIC_PRICE);
        String optimizationMethodOption = String.valueOf(uiWrapper.getOptimizationMethodOption().getId());
        if (optimizationMethodHasChanged(optimizationMethodOption)) {
            roaPropertyAttributeService.save(propertyAttribute.getId(), optimizationMethodOption);
        }
        if (cancelRebookAllowed && cancelRebookPercentageHasChanged()) {
            String cancelRebookPercentageValue = uiWrapper.getCancelRebookPercentage().getValue();
            if (cancelRebookPercentageValue == null || "".equalsIgnoreCase(cancelRebookPercentageValue)) {
                view.showValidationMessage(getText("enterCancelRebook"));
                return;
            }
            PropertyAttribute cancelRebookPctPropertyAttribute = roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT);
            roaPropertyAttributeService.save(cancelRebookPctPropertyAttribute.getId(), cancelRebookPercentageValue);
        }

        savePriceDropSettings();

        if (view.priceDropRestrictionDowLayoutHasChanges()) {
            if (!view.isPriceDropRestrictionDowLayoutValid()) {
                view.showValidationMessage(getText(INPUT_WARNING_MESSGE));
                return;
            }

            saveOrDeleteRecord(view.priceDropRestrictionDowLayout.defaultPriceDropConfigTable.getData(), view.priceDropRestrictionDowLayout.getEnablePriceDropRestrictionCheckBox().getValue());
            initializePriceDropByDOWAndSeasons();
        }

        saveLrvDropSettings();

        view.showSaveSuccessMessage();
        initData();
    }

    private void savePriceDropSettings() {
        if (decoupleLrvDropRestrictionEnabled) {
            saveDecoupledPriceDropRestriction();
        } else {
            saveCoupledPriceDropRestriction();
        }
    }

    private void saveLrvDropSettings() {
        if (decoupleLrvDropRestrictionEnabled) {
            saveDecoupledLrvDropRestriction();
        } else if (view.lrvDropRestrictionLayoutHasChanges() || isPriceDropProtectionDeleted) {
            saveCoupledLrvDropRestriction();
        }
    }

    private void saveCoupledPriceDropRestriction() {
        if (view.priceDropRestrictionLayoutHasChanges()) {
            if (validateLayout(view.isPriceDropRestrictionLayoutValid())) return;

            PriceDropRestrictionDto priceDropRestrictionDto = view.getPriceDropRestrictionDto();
            HashMap<String, String> attributesWithValue = new HashMap<>();
            setPriceDropRestrictionAttributeValues(priceDropRestrictionDto, attributesWithValue);
            if (priceDropRestrictionDto.isEnablePriceDropRestriction()) {
                roaPropertyAttributeService.saveMultiplePropertyAttributes(attributesWithValue);
            } else {
                roaPropertyAttributeService.deletePropertyAttribute(attributesWithValue.keySet().stream().collect(Collectors.toList()));
                isPriceDropProtectionDeleted = true;
            }
        }
    }

    private boolean validateLayout(boolean isLayoutValid) {
        if (!isLayoutValid) {
            view.showValidationMessage(getText(INPUT_WARNING_MESSGE));
            return true;
        }
        return false;
    }

    private void saveDecoupledPriceDropRestriction() {
        if (view.priceDropRestrictionLayoutHasChanges()) {
            if (validatePriceDropLayoutIfPriceDropEnabled()) return;

            PriceDropRestrictionDto priceDropRestrictionDto = view.getPriceDropRestrictionDto();
            HashMap<String, String> attributesWithValue = new HashMap<>();

            if (priceDropRestrictionDto.isEnablePriceDropRestriction()) {
                setPriceDropRestrictionAttributeValues(priceDropRestrictionDto, attributesWithValue);
                roaPropertyAttributeService.saveMultiplePropertyAttributes(attributesWithValue);
            } else if (isPriceDropRestrictionDtoAttributesNull(priceDropRestrictionDto)) {
                setPriceDropRestrictionAttributeValues(priceDropRestrictionDto, attributesWithValue);
                roaPropertyAttributeService.deletePropertyAttribute(new ArrayList<>(attributesWithValue.keySet()));
            }
        }
    }

    private void setPriceDropRestrictionAttributeValues(PriceDropRestrictionDto priceDropRestrictionDto, HashMap<String, String> attributesWithValue) {
        attributesWithValue.put(PRICE_DROP_MIN_DTA, priceDropRestrictionDto.getDaysToArrival().toString());
        attributesWithValue.put(PRICE_DROP_MIN_REV_GAIN, priceDropRestrictionDto.getRevenueThresholdForPrice().toString());
        attributesWithValue.put(PRICE_DROP_MAX_VALUE, priceDropRestrictionDto.getMaximumPriceDrop().toString());
    }

    private boolean isPriceDropRestrictionDtoAttributesNull(PriceDropRestrictionDto priceDropRestrictionDto) {
        return priceDropRestrictionDto.getDaysToArrival() != null && priceDropRestrictionDto.getRevenueThresholdForPrice() != null &&
                priceDropRestrictionDto.getMaximumPriceDrop() != null;
    }

    private boolean validatePriceDropLayoutIfPriceDropEnabled() {
        if (view.isPriceRestrictionCheckBoxEnabled() && !view.isPriceDropRestrictionLayoutValid()) {
            view.showValidationMessage(getText(INPUT_WARNING_MESSGE));
            return true;
        }
        return false;
    }

    private void saveDecoupledLrvDropRestriction() {
        if (view.lrvDropRestrictionLayoutHasChanges()) {
            if (validateLrvDropLayoutIfLrvEnabled()) return;

            if (view.isLrvRestrictionCheckBoxEnabled()) {
                saveLrvRoomClassWithCapacityConfigs();
                if (view.lrvMinDtaHasChanges()) {
                    roaPropertyAttributeService.saveOverrideByAttributeName(LRV_DROP_MIN_DTA.getAttributeName(),
                            view.getLrvDropRestrictionLayoutBean().getDaysToArrival().toString());
                }
            } else {
                roaPropertyAttributeService.deletePropertyAttribute(Collections.singletonList(LRV_DROP_MIN_DTA.getAttributeName()));
                lrvDropRestrictionService.deleteConfiguration();
            }
        }
    }

    private boolean validateLrvDropLayoutIfLrvEnabled() {
        if (!view.isLrvDropRestrictionLayoutValid() && view.isLrvRestrictionCheckBoxEnabled()) {
            view.showValidationMessage(getText(INPUT_WARNING_MESSGE));
            return true;
        }
        return false;
    }

    private void saveCoupledLrvDropRestriction() {
        if (validateLayout(view.isLrvDropRestrictionLayoutValid())) return;

        if (view.isLrvRestrictionCheckBoxEnabled() && !isPriceDropProtectionDeleted) {
            saveLrvRoomClassWithCapacityConfigs();
        } else {
            lrvDropRestrictionService.deleteConfiguration();
        }
    }

    private void saveLrvRoomClassWithCapacityConfigs() {
        view.lrvDropRestrictionLayoutCommit();
        LrvDropRestrictionDto dto = view.getLrvDropRestrictionLayoutBean();
        Map<Integer, Integer> rcWithCapacity = new HashMap<>();
        dto.getRoomClassCapacities().forEach(rcCapacity ->
                rcWithCapacity.put(rcCapacity.getAccomClassId(), rcCapacity.getRemainingCapacity().getValue()));
        lrvDropRestrictionService.saveConfiguration(rcWithCapacity);
    }

    private boolean cancelRebookPercentageHasChanged() {
        PropertyAttribute cancelRebookPercentage = roaPropertyAttributeService.getPropertyAttribute(PropertyAttributeEnum.CANCEL_REBOOK_PCT);
        String cancelRebookPercentageValue = roaPropertyAttributeService.getAttributeValue(cancelRebookPercentage.getId());
        return !cancelRebookPercentageValue.equals(uiWrapper.getCancelRebookPercentage().getValue());
    }

    private boolean optimizationMethodHasChanged(String optimizationMethodOption) {
        return !optDynamicPrice.equals(optimizationMethodOption);
    }

    public TimeZone getPropertyTimeZone() {
        return uiContext.getTimeZone();
    }

    private List<PriceDropRestrictionConfigWrapper> getPriceDropRestrictionTableWrapperData() {
        PriceDropRestrictionConfig priceDropRestrictionConfig = this.getDefaultPriceDropRestrictionConfig();
        return getPriceDropRestrictionConfigWrappers(priceDropRestrictionConfig);
    }

    private List<PriceDropRestrictionConfigWrapper> getPriceDropRestrictionConfigWrappers(PriceDropRestrictionConfig priceDropRestrictionConfig) {
        List<PriceDropRestrictionConfigWrapper> priceDropRestrictionConfigWrappers = new ArrayList<>();

        priceDropRestrictionConfigWrappers.add(new PriceDropRestrictionConfigWrapper(priceDropRestrictionConfig, getText("price.drop.enable.restrictions"), PriceDropOccupancyType.ENABLE_RESTRICTIONS, null, defaultConfigPresentInDB));
        priceDropRestrictionConfigWrappers.add(new PriceDropRestrictionConfigWrapper(priceDropRestrictionConfig, getText("common.days.to.arrival"), PriceDropOccupancyType.DAYS_TO_ARRIVAL, null, defaultConfigPresentInDB));
        priceDropRestrictionConfigWrappers.add(new PriceDropRestrictionConfigWrapper(priceDropRestrictionConfig, getText("price.drop.revenue.threshold.for.price"), PriceDropOccupancyType.REVENUE_THRESHOLD_FOR_PRICE, priceDropRestrictionConfig.getPrcRevThresholdMethodType(), defaultConfigPresentInDB));
        priceDropRestrictionConfigWrappers.add(new PriceDropRestrictionConfigWrapper(priceDropRestrictionConfig, getText("maximum.price.drop"), PriceDropOccupancyType.MAXIMUM_PRICE_DROP, priceDropRestrictionConfig.getMaxPrcDrpMethodType(), defaultConfigPresentInDB));

        return priceDropRestrictionConfigWrappers;
    }

    private PriceDropRestrictionConfig getDefaultPriceDropRestrictionConfig() {
        PriceDropRestrictionConfig priceDropRestrictionConfig = priceDropRestrictionService.findDefault();
        if (priceDropRestrictionConfig == null) {
            defaultConfigPresentInDB = false;
            priceDropRestrictionConfig = new PriceDropRestrictionConfig(OffsetMethod.FIXED_OFFSET);
        } else {
            defaultConfigPresentInDB = true;
        }
        return priceDropRestrictionConfig;
    }

    public boolean saveOrDeleteRecord(List<PriceDropRestrictionConfigWrapper> priceDropRestrictionConfigWrappers, boolean isPriceDropRestrictionsEnabled) {
        if (isPriceDropRestrictionsEnabled) {
            return saveValidValuesToTheDatabase(priceDropRestrictionConfigWrappers);
        }
        return deleteAllDefaultAndSeasonConfigs();
    }

    private boolean deleteAllDefaultAndSeasonConfigs() {
        List<PriceDropRestrictionConfig> defaultAndSeasonConfigs = priceDropRestrictionService.findAll();
        deletePriceDropRestrictionConfigs(defaultAndSeasonConfigs);
        return true;
    }

    private void deletePriceDropRestrictionConfigs(List<PriceDropRestrictionConfig> defaultAndSeasonConfigs) {
        priceDropRestrictionService.delete(defaultAndSeasonConfigs);
        List<Integer> prcDrpConfigIdsToDelete = defaultAndSeasonConfigs.stream().map(PriceDropRestrictionConfig::getId).collect(Collectors.toList());
        priceDropRestrictionService.updatePriceDropRestrictionAUD(prcDrpConfigIdsToDelete);
    }

    private boolean saveValidValuesToTheDatabase(List<PriceDropRestrictionConfigWrapper> priceDropRestrictionConfigWrappers) {
        PriceDropRestrictionConfig itemToSave = this.getPriceDropRestrictionConfig(priceDropRestrictionConfigWrappers);
        PriceDropRestrictionConfig existingConfig = this.getDefaultPriceDropRestrictionConfig();

        if (!itemToSave.equals(existingConfig)) {
            priceDropRestrictionService.save(itemToSave);
            defaultConfigPresentInDB = true;
            return true;
        }
        return false;
    }

    @VisibleForTesting
    protected void deleteOutOfRangeDOWData(PriceDropRestrictionSeasonUiWrapper season) {
        if (season != null && season.getStartDate() != null && season.getEndDate() != null) {
            List<DayOfWeek> outOfRangeDayOfWeeks = LocalDateUtils.getOutOfRangeDayOfWeeks(season.getStartDate(), season.getEndDate());
            season.getPriceDropRestrictionDowDto().getPriceDropRestrictionConfigWrapperList().forEach(wrapper -> applyDisabledDays(wrapper, outOfRangeDayOfWeeks));
        }
    }

    private void applyDisabledDays(PriceDropRestrictionConfigWrapper wrapper, List<DayOfWeek> outOfRangeDayOfWeeks) {
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.SUNDAY)) {
            wrapper.setSunday(null);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.MONDAY)) {
            wrapper.setMonday(null);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.TUESDAY)) {
            wrapper.setTuesday(null);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.WEDNESDAY)) {
            wrapper.setWednesday(null);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.THURSDAY)) {
            wrapper.setThursday(null);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.FRIDAY)) {
            wrapper.setFriday(null);
        }
        if (outOfRangeDayOfWeeks.contains(DayOfWeek.SATURDAY)) {
            wrapper.setSaturday(null);
        }
    }

    private void saveValidSeasonValuesToTheDatabase(List<PriceDropRestrictionConfigWrapper> priceDropRestrictionConfigWrappers, LocalDate startDate, LocalDate endDate) {
        PriceDropRestrictionConfig itemToSave = this.getPriceDropRestrictionConfig(priceDropRestrictionConfigWrappers);
        itemToSave.setStartDate(startDate.toDate());
        itemToSave.setEndDate(endDate.toDate());
        priceDropRestrictionService.save(itemToSave);
    }

    public PriceDropRestrictionConfig getPriceDropRestrictionConfig(List<PriceDropRestrictionConfigWrapper> wrappers) {
        return wrappers.stream().findAny().get().getPriceDropRestrictionConfig();
    }

    public boolean isPriceDropRestrictionFeatureDOWAndSeasonsEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE_DOW_AND_SEASONS);
    }

    public List<OptimizationSettingExcelDto> addOptimizationSettingsHistoryToExcel(OptimizationSettingsEnum optimizationSetting) {
        List<OptimizationSettingExcelDto> excelDtoList = new ArrayList<>();
        switch (optimizationSetting) {
            case PRICE_DROP_DEFAULT:
                excelDtoList = getPriceDropDOWHistory(OptimizationSettingsEnum.PRICE_DROP_DEFAULT);
                break;
            case PRICE_DROP_SEASON:
                excelDtoList = getPriceDropDOWHistory(OptimizationSettingsEnum.PRICE_DROP_SEASON);
                break;
            case PRICE_DROP_OLD:
                excelDtoList = getOldPriceDropHistory();
                break;
            case CANCEL_REBOOK:
                excelDtoList = getPropertyAttributeRevisionHistory(PropertyAttributeEnum.CANCEL_REBOOK_PCT);
                break;
            case OPTIMIZATION_METHOD:
                excelDtoList = getPropertyAttributeRevisionHistory(PropertyAttributeEnum.OPT_DYNAMIC_PRICE);
                break;
            case LRV_DROP_RESTRICTION:
                excelDtoList = getLrvDropHistory();
                break;
        }
        return excelDtoList;
    }

    private List<OptimizationSettingExcelDto> getOldPriceDropHistory() {
        List<OptimizationSettingExcelDto> excelDtoList = new ArrayList<>();
        convertPropertyAttributeRevisionUiWrapperToOptimizationSettingExcelDto(excelDtoList, this.getPriceDropRestrictionHistory());
        return excelDtoList;
    }

    private List<OptimizationSettingExcelDto> getLrvDropHistory() {
        List<OptimizationSettingExcelDto> excelDtoList = new ArrayList<>();
        List<PropertyAttributeRevisionUIWrapper> lrvDropRestrictionHistory = getDecoupledLrvDropRestrictionHistory();
        convertPropertyAttributeRevisionUiWrapperToOptimizationSettingExcelDto(excelDtoList, lrvDropRestrictionHistory);
        return sortLrvDropExcelDtoInDescendingOrderBasedOnUpdatedDate(excelDtoList);
    }

    private void convertPropertyAttributeRevisionUiWrapperToOptimizationSettingExcelDto(List<OptimizationSettingExcelDto> excelDtoList, List<PropertyAttributeRevisionUIWrapper> propertyAttributeRevisionUIWrappers) {
        propertyAttributeRevisionUIWrappers.forEach(wrapper -> excelDtoList.add(OptimizationSettingExcelDto.builder().setting(wrapper.getPropertyAttributeName()).value(wrapper.getValue())
                .updatedBy(wrapper.getPropertyAttributeRevision().getUserName()).updatedOn(DateUtil.convertJavaUtilDateToLocalDateTime(wrapper.getPropertyAttributeRevision().getLastUpdatedDateTime().toDate())).build()));
    }

    private List<OptimizationSettingExcelDto> sortLrvDropExcelDtoInDescendingOrderBasedOnUpdatedDate(List<OptimizationSettingExcelDto> excelDtoList) {
        return excelDtoList.stream()
                .sorted(Comparator.comparing(OptimizationSettingExcelDto::getUpdatedOn).reversed())
                .collect(Collectors.toList());
    }

    private List<OptimizationSettingExcelDto> getPropertyAttributeRevisionHistory(PropertyAttributeEnum propertyAttributeEnum) {
        List<OptimizationSettingExcelDto> excelDtoList = new ArrayList<>();
        PropertyAttribute propertyAttribute = roaPropertyAttributeService.getPropertyAttribute(propertyAttributeEnum);
        List<PropertyAttributeRevision> propertyAttributeRevisions = roaPropertyAttributeService.fetchAttributeRevisions(propertyAttribute.getId());
        populateExcelDTOForPropertyAttributeHistory(propertyAttributeRevisions, excelDtoList, propertyAttributeEnum);
        return excelDtoList;
    }

    private void populateExcelDTOForPropertyAttributeHistory(List<PropertyAttributeRevision> propertyAttributeRevisions, List<OptimizationSettingExcelDto> excelDtoList, PropertyAttributeEnum propertyAttributeEnum) {
        if (propertyAttributeRevisions != null) {
            propertyAttributeRevisions.forEach(attributeRevision -> excelDtoList.add(OptimizationSettingExcelDto.builder().setting(propertyAttributeEnum.getAttributeName()).value(getValueOfPropertyAttributeSetting(attributeRevision.getValue(), propertyAttributeEnum))
                    .updatedBy(attributeRevision.getUserName()).updatedOn(DateUtil.convertJavaUtilDateToLocalDateTime(attributeRevision.getLastUpdatedDateTime().toDate())).build()));
        }
    }

    private String getValueOfPropertyAttributeSetting(BigDecimal value, PropertyAttributeEnum propertyAttributeEnum) {
        return PropertyAttributeEnum.OPT_DYNAMIC_PRICE.equals(propertyAttributeEnum) ? (BigDecimal.ONE.equals(value) ? getText("Enable") : getText("Disable")) : value.toString();
    }

    private List<OptimizationSettingExcelDto> getPriceDropDOWHistory(OptimizationSettingsEnum optimizationSetting) {
        List<OptimizationSettingExcelDto> excelDtoList = new ArrayList<>();
        List<PriceDropRestrictionConfigAudit> priceDropRestrictionConfigAudits = new ArrayList<>();
        if (OptimizationSettingsEnum.PRICE_DROP_DEFAULT.equals(optimizationSetting)) {
            priceDropRestrictionConfigAudits = priceDropRestrictionService.getPriceDropRestrictionConfigHistory(OptimizationSettingsEnum.PRICE_DROP_DEFAULT);
        } else {
            priceDropRestrictionConfigAudits = priceDropRestrictionService.getPriceDropRestrictionConfigHistory(OptimizationSettingsEnum.PRICE_DROP_SEASON);
        }
        populateExcelDTOForPriceDropHistory(priceDropRestrictionConfigAudits, excelDtoList, optimizationSetting);
        return excelDtoList;
    }

    private void populateExcelDTOForPriceDropHistory(List<PriceDropRestrictionConfigAudit> priceDropRestrictionConfigAudits, List<OptimizationSettingExcelDto> excelDtoList, OptimizationSettingsEnum optimizationSetting) {
        priceDropRestrictionConfigAudits.forEach(audit -> {
            List<OptimizationSettingExcelDto> optimizationSettingExcelDtos = new ArrayList<>();
            if (audit.getRevType() == 2) {
                OptimizationSettingExcelDto excelDtoForEnableDisableSetting = (OptimizationSettingsEnum.PRICE_DROP_DEFAULT.equals(optimizationSetting)) ?
                        OptimizationSettingExcelDto.builder().setting(getText("price.drop.enable.restrictions")).sunday("FALSE").monday("FALSE").tuesday("FALSE").wednesday("FALSE").thursday("FALSE").friday("FALSE").saturday("FALSE").updatedBy(audit.getUsername()).updatedOn(audit.getLastUpdatedDate()).build()
                        : OptimizationSettingExcelDto.builder().setting(getText("delete.price.drop.setting.excel.row")).startDate(audit.getStartDate()).endDate(audit.getEndDate()).updatedBy(audit.getUsername()).updatedOn(audit.getLastUpdatedDate()).build();
                optimizationSettingExcelDtos.add(excelDtoForEnableDisableSetting);
            } else {
                optimizationSettingExcelDtos.add(OptimizationSettingExcelDto.builder().setting(getText("daysToArrival")).startDate(audit.getStartDate()).endDate(audit.getEndDate()).sunday(getValue(audit.getSundayDTA())).monday(getValue(audit.getMondayDTA())).tuesday(getValue(audit.getTuesdayDTA())).wednesday(getValue(audit.getWednesdayDTA())).thursday(getValue(audit.getThursdayDTA())).friday(getValue(audit.getFridayDTA())).saturday(getValue(audit.getSaturdayDTA())).updatedBy(audit.getUsername()).updatedOn(audit.getLastUpdatedDate()).build());
                optimizationSettingExcelDtos.add(OptimizationSettingExcelDto.builder().setting(getText("price.drop.revenue.threshold.for.price")).startDate(audit.getStartDate()).endDate(audit.getEndDate()).offsetMethod(audit.getPrcRevThresholdMethodType().name()).sunday(getValue(audit.getSundayPrcRevThreshold())).monday(getValue(audit.getMondayPrcRevThreshold())).tuesday(getValue(audit.getTuesdayPrcRevThreshold())).wednesday(getValue(audit.getWednesdayPrcRevThreshold())).thursday(getValue(audit.getThursdayPrcRevThreshold())).friday(getValue(audit.getFridayPrcRevThreshold())).saturday(getValue(audit.getSaturdayPrcRevThreshold())).updatedBy(audit.getUsername()).updatedOn(audit.getLastUpdatedDate()).build());
                optimizationSettingExcelDtos.add(OptimizationSettingExcelDto.builder().setting(getText("maximum.price.drop")).startDate(audit.getStartDate()).endDate(audit.getEndDate()).offsetMethod(audit.getMaxPrcDrpMethodType().name()).sunday(getValue(audit.getSundayMaxPrcDrp())).monday(getValue(audit.getMondayMaxPrcDrp())).tuesday(getValue(audit.getTuesdayMaxPrcDrp())).wednesday(getValue(audit.getWednesdayMaxPrcDrp())).thursday(getValue(audit.getThursdayMaxPrcDrp())).friday(getValue(audit.getFridayMaxPrcDrp())).saturday(getValue(audit.getSaturdayMaxPrcDrp())).updatedBy(audit.getUsername()).updatedOn(audit.getLastUpdatedDate()).build());

                OptimizationSettingExcelDto excelDtoForEnableDisableSetting = OptimizationSettingExcelDto.builder().setting(getText("price.drop.enable.restrictions")).startDate(audit.getStartDate()).endDate(audit.getEndDate()).sunday(getEnableRestrictionsValue(audit.getSundayDTA(), audit.getSundayPrcRevThreshold(), audit.getSundayMaxPrcDrp())).monday(getEnableRestrictionsValue(audit.getMondayDTA(), audit.getMondayPrcRevThreshold(), audit.getMondayMaxPrcDrp())).tuesday(getEnableRestrictionsValue(audit.getTuesdayDTA(), audit.getTuesdayPrcRevThreshold(), audit.getTuesdayMaxPrcDrp())).wednesday(getEnableRestrictionsValue(audit.getWednesdayDTA(), audit.getWednesdayPrcRevThreshold(), audit.getWednesdayMaxPrcDrp())).thursday(getEnableRestrictionsValue(audit.getThursdayDTA(), audit.getThursdayPrcRevThreshold(), audit.getThursdayMaxPrcDrp())).friday(getEnableRestrictionsValue(audit.getFridayDTA(), audit.getFridayPrcRevThreshold(), audit.getFridayMaxPrcDrp())).saturday(getEnableRestrictionsValue(audit.getSaturdayDTA(), audit.getSaturdayPrcRevThreshold(), audit.getSaturdayMaxPrcDrp())).updatedBy(audit.getUsername()).updatedOn(audit.getLastUpdatedDate()).build();
                optimizationSettingExcelDtos.add(0, excelDtoForEnableDisableSetting);
            }
            excelDtoList.addAll(optimizationSettingExcelDtos);
        });
    }

    public boolean isDefaultConfigPresentInDB() {
        return defaultConfigPresentInDB;
    }

    public boolean isCancelRebookAllowed() {
        return cancelRebookAllowed;
    }

    public boolean isDisplayOptimizationMethodOption() {
        return isOptimisationSettingsTabEnabled && !hideOptimizationMethodOption;
    }

    public boolean isOldPriceDropSettingOldThan(int noOfDays) {
        List<PropertyAttribute> propertyAttributes = roaPropertyAttributeService.fetchPropertyAttributesByName(
                Arrays.asList(PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_REV_GAIN.toString(),
                        PRICE_DROP_RESTRICTION.PRICE_DROP_MAX_VALUE.toString(),
                        PRICE_DROP_RESTRICTION.PRICE_DROP_MIN_DTA.toString())
        );
        List<Integer> propertyAttributeIds = propertyAttributes.stream().map(PropertyAttribute::getId).collect(Collectors.toList());
        LocalDateTime latestLastUpdatedDTTM = roaPropertyAttributeService.getLastestUpdatedDateForMultipleAttributes(propertyAttributeIds);
        return (latestLastUpdatedDTTM != null) ? latestLastUpdatedDTTM.plusDays(noOfDays).isAfter(LocalDateTime.now()) : false;
    }

    private String getValue(BigDecimal value) {
        return value != null ? String.valueOf(value) : null;
    }

    private String getEnableRestrictionsValue(BigDecimal dta, BigDecimal prcRevThreshold, BigDecimal maxPrcDrp) {
        boolean allNull = Stream.of(dta, prcRevThreshold, maxPrcDrp).allMatch(Objects::isNull);
        return allNull ? "FALSE" : "TRUE";
    }

    @VisibleForTesting
    protected void setPriceDropRestrictionSeasons(List<PriceDropRestrictionSeasonUiWrapper> priceDropRestrictionSeasons) {
        this.priceDropRestrictionSeasons = priceDropRestrictionSeasons;
    }

    public boolean isDecoupleLrvDropRestrictionEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DECOUPLE_LRV_DROP_RESTRICTION_ENABLED);
    }

    public boolean isLrvDropRestrictionEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_LRV_DROP_RESTRICTION_FEATURE);
    }
}
