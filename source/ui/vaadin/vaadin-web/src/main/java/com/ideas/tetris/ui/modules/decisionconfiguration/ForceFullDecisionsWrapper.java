package com.ideas.tetris.ui.modules.decisionconfiguration;

import com.ideas.tetris.pacman.services.forcefulldecisions.ForceFullDecisionsUpdateReasons;
import com.ideas.tetris.pacman.services.property.PropertyAuditDto;
import com.ideas.tetris.platform.services.daoandentities.entity.PropertyAudit;

import java.time.LocalDateTime;

public class ForceFullDecisionsWrapper {

    private String propertyCode;
    private String modifiedBy;
    private Object modifiedDate;
    private ForceFullDecisionsOutboundWrapper fullDecisionsOutboundWrapper;
    private PropertyAuditDto property;
    private Object outboundName;

    public PropertyAuditDto getProperty() {
        return property;
    }

    public ForceFullDecisionsOutboundWrapper getFullDecisionsOutboundWrapper() {
        return fullDecisionsOutboundWrapper;
    }

    public boolean getForceFullDecisions() {
        return property != null ? property.getForceFullDecisions() : fullDecisionsSetAtOutbounds();
    }

    private boolean fullDecisionsSetAtOutbounds() {
        return (null != fullDecisionsOutboundWrapper && null != fullDecisionsOutboundWrapper.getForceFullDecisions().getREVTYPE()) ?
                fullDecisionsOutboundWrapper.getForceFullDecisions().getREVTYPE().intValue() == PropertyAudit.ADDED_ACTION ? true : false :
                (null != fullDecisionsOutboundWrapper) ? Integer.valueOf(ForceFullDecisionsUpdateReasons.FFD_SET.getReasonID()).equals(fullDecisionsOutboundWrapper.getForceFullDecisions().getIsForceFullDecision()) : false;
    }

    public String getAction() {
        return action;
    }

    private String action;

    public ForceFullDecisionsWrapper() {
    }

    public void setPropertyAuditDto(PropertyAuditDto property) {
        this.property = property;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getPropertyName() {
        return property != null ? property.getPropertyName() : null != fullDecisionsOutboundWrapper.getForceFullDecisions()
                ? fullDecisionsOutboundWrapper.getForceFullDecisions().getProperty().getName() : "";
    }

    public String getPropertyCode() {
        return property != null ? property.getPropertyCode() : null != fullDecisionsOutboundWrapper.getForceFullDecisions() ?
                fullDecisionsOutboundWrapper.getForceFullDecisions().getProperty().getCode() : "";
    }

    public String getModifiedBy() {
        return property != null ? property.getModifiedBy() : null != fullDecisionsOutboundWrapper ? fullDecisionsOutboundWrapper.getModifiedBy() : "";
    }

    public LocalDateTime getModifiedDate() {
        return property != null ? property.getModifiedDate() :
                null != fullDecisionsOutboundWrapper ? fullDecisionsOutboundWrapper.getForceFullDecisions().getLastUpdatedDate()
                        : LocalDateTime.now();
    }


    public void setFullDecisionsOutboundWrapper(ForceFullDecisionsOutboundWrapper fullDecisionsOutboundWrapper) {
        this.fullDecisionsOutboundWrapper = fullDecisionsOutboundWrapper;
    }

    public String getOutboundName() {
        return null != fullDecisionsOutboundWrapper ? fullDecisionsOutboundWrapper.getForceFullDecisions().getOutboundName() : "";
    }
}

