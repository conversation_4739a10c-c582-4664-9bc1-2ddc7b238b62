package com.ideas.tetris.ui.modules.rateplanconfiguration.ratedetails;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.ui.common.component.TetrisBeanItemContainer;
import com.ideas.tetris.ui.common.component.TetrisSaveCancelButtonBarAware;
import com.ideas.tetris.ui.common.component.TetrisSaveCancelEnum;
import com.ideas.tetris.ui.common.component.label.TetrisSpacer;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareComponentColumnGenerator;
import com.ideas.tetris.ui.common.component.table.TetrisChangeAwareTable;
import com.ideas.tetris.ui.common.component.textfield.AcceleratorTextFieldListener;
import com.ideas.tetris.ui.common.component.textfield.TetrisBigDecimalField;
import com.ideas.tetris.ui.common.data.util.converter.StringToBigDecimalConverter;
import com.ideas.tetris.ui.common.util.ChangeAware;
import com.ideas.tetris.ui.common.util.TetrisTheme;
import com.ideas.tetris.ui.common.util.UiUtils;
import com.ideas.tetris.ui.modules.rateplanconfiguration.dto.RoomClassLevelRatesStat;
import com.ideas.tetris.ui.modules.rateplanconfiguration.uiwrappers.RDRoomClassUiWrapper;
import com.ideas.tetris.ui.modules.rateplanconfiguration.uiwrappers.RDSeasonDetailsUIWrapper;
import com.ideas.tetris.ui.modules.rateplanconfiguration.uiwrappers.RDSeasonUiWrapper;
import com.ideas.tetris.ui.modules.rateplanconfiguration.uiwrappers.SeasonDetailDtoWrapper;
import com.vaadin.ui.Alignment;
import com.vaadin.ui.Component;
import com.vaadin.v7.data.Property;
import com.vaadin.v7.data.Validator;
import com.vaadin.v7.data.validator.RangeValidator;
import com.vaadin.v7.ui.Field;
import com.vaadin.v7.ui.HorizontalLayout;
import com.vaadin.v7.ui.Label;
import com.vaadin.v7.ui.Table;
import com.vaadin.v7.ui.VerticalLayout;
import org.apache.log4j.Logger;
import org.joda.time.Days;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class RateDetailsRoomClassLayout extends VerticalLayout implements ChangeAware, TetrisSaveCancelButtonBarAware {

    private static final Logger LOGGER = Logger.getLogger(RateDetailsRoomClassLayout.class);
    private LocalDate systemDate;
    private LinkedHashMap<Object, TetrisChangeAwareTable> tables = new LinkedHashMap<Object, TetrisChangeAwareTable>();
    private RDSeasonUiWrapper season;
    private HorizontalLayout roomClassHeader;
    private List<TetrisChangeAwareTable> roomClassTables = new ArrayList<>();
    private Component ratesTable;
    private TetrisBeanItemContainer<RDSeasonDetailsUIWrapper> beanItemContainer;
    private boolean isSeasonInPast;
    private static final BigDecimal MAX_INPUT = new BigDecimal(100000000f);
    private Map<Integer, RoomClassLevelRatesStat> roomClassRatesStatsMap = new HashMap<>();
    private Map<TetrisBigDecimalField, Integer> roomClassWiseFieldMap = new HashMap<>();
    private static Map<String, Integer> dayOfWeekMap = new HashMap<String, Integer>(7);
    private boolean isAdvancedPriceRankingEnabled;
    private boolean hideDiscontinuedAccomTypes;

    private String sundayRate;
    private String mondayRate;
    private String tuesdayRate;
    private String wednesdayRate;
    private String thursdayRate;
    private String fridayRate;
    private String saturdayRate;

    public RateDetailsRoomClassLayout(RDSeasonUiWrapper season, boolean isSeasonInPast, LocalDate systemDate,
                                      boolean isAdvancedPriceRankingEnabled, boolean hideDiscontinuedAccomTypes) {
        setMargin(true);
        setSpacing(true);
        initializeDayOfWeekMap();
        this.season = season;
        this.systemDate = systemDate;
        this.isAdvancedPriceRankingEnabled = isAdvancedPriceRankingEnabled;
        this.hideDiscontinuedAccomTypes = hideDiscontinuedAccomTypes;
        for (RDRoomClassUiWrapper roomClassUiWrapper : season.getRoomClassUiWrapperList()) {
            beanItemContainer = new TetrisBeanItemContainer<RDSeasonDetailsUIWrapper>(RDSeasonDetailsUIWrapper.class);
            populateRatesTableData(roomClassUiWrapper.getSeasonDetails());
            Component roomClassComponent = addRoomClass(roomClassUiWrapper);
            addComponent(roomClassComponent);
            setComponentAlignment(roomClassComponent, Alignment.TOP_LEFT);
            this.isSeasonInPast = isSeasonInPast;
        }
        calculateMinRatePerRoomClass();
        setId("rateDetailsModifications");
    }

    private void initializeDayOfWeekMap() {
        dayOfWeekMap.put("seasonDetail.monday", 1);
        dayOfWeekMap.put("seasonDetail.tuesday", 2);
        dayOfWeekMap.put("seasonDetail.wednesday", 3);
        dayOfWeekMap.put("seasonDetail.thursday", 4);
        dayOfWeekMap.put("seasonDetail.friday", 5);
        dayOfWeekMap.put("seasonDetail.saturday", 6);
        dayOfWeekMap.put("seasonDetail.sunday", 7);
    }

    public RateDetailsRoomClassLayout() {
        initializeDayOfWeekMap();
    }

    private Component addRoomClass(RDRoomClassUiWrapper roomClassUiWrapper) {
        VerticalLayout layout = new VerticalLayout();
        roomClassHeader = addRoomClassHeader(roomClassUiWrapper.getRoomClass());
        layout.addComponent(roomClassHeader);
        ratesTable = addRatesTable(roomClassUiWrapper);
        layout.addComponent(ratesTable);
        layout.setExpandRatio(ratesTable, 1);
        return layout;
    }

    private HorizontalLayout addRoomClassHeader(RDRoomClassUiWrapper.RoomClass roomClass) {
        Label roomClassOrderLabel = new Label(getText("rateDetails.order") + " " + roomClass.getOrder());
        Label roomClassNameLabel = new Label(" " + getText("roomClassLabel") + " " + roomClass.getName());
        HorizontalLayout horizontalLayout = new HorizontalLayout(roomClassOrderLabel, new TetrisSpacer(10, Unit.PIXELS), roomClassNameLabel);
        horizontalLayout.setMargin(true);
        horizontalLayout.setWidth(100, Unit.PERCENTAGE);
        horizontalLayout.addStyleName(TetrisTheme.BOLD);
        horizontalLayout.addStyleName("table-component-header");
        horizontalLayout.setExpandRatio(roomClassOrderLabel, 0.1F);
        horizontalLayout.setExpandRatio(roomClassNameLabel, 1);
        return horizontalLayout;
    }

    private Component addRatesTable(RDRoomClassUiWrapper roomClass) {
        TetrisChangeAwareTable table = new TetrisChangeAwareTable();
        table.setWidth(100, Unit.PERCENTAGE);
        table.setAutoRowHeightEnabled(true);
        tables.put(table, table);

        beanItemContainer.addNestedContainerBean("seasonDetailDtoWrapper");
        table.setContainerDataSource(beanItemContainer);
        table.setImmediate(true);
        table.setSortEnabled(false);

        List<RDSeasonDetailsUIWrapper> filteredSeasonDetails = getFilteredSeasonDetails(roomClass);

        int numberOfRowsToShow = filteredSeasonDetails.size();
        table.setPageLength(numberOfRowsToShow);

        ArrayList<String> visibleColumns = new ArrayList<String>();
        String[] columnHeaders = new String[8];

        setColumns(visibleColumns, columnHeaders, table, roomClass.getRoomClass().getOrder());
        beanItemContainer.removeAllItems();
        beanItemContainer.addAll(filteredSeasonDetails);
        roomClassTables.add(table);
        return table;
    }

    @VisibleForTesting
    List<RDSeasonDetailsUIWrapper> getFilteredSeasonDetails(RDRoomClassUiWrapper roomClass) {
        List<RDSeasonDetailsUIWrapper> filteredSeasonDetails = roomClass.getSeasonDetails();
        if (hideDiscontinuedAccomTypes) {
            filteredSeasonDetails = roomClass.getSeasonDetails().stream()
                    .filter(seasonWrapper -> seasonWrapper.getSeasonDetailDtoWrapper().getSeasonDetail().isAccomTypeDisplayActive())
                    .collect(Collectors.toList());
        }
        return filteredSeasonDetails;
    }

    private void setColumns(ArrayList<String> visibleColumns, String[] columnHeaders, final TetrisChangeAwareTable table, Integer roomClassOrder) {

        final String acceleratorId = "acceleratorId";
        sundayRate = "seasonDetailDtoWrapper.sunday";
        mondayRate = "seasonDetailDtoWrapper.monday";
        tuesdayRate = "seasonDetailDtoWrapper.tuesday";
        wednesdayRate = "seasonDetailDtoWrapper.wednesday";
        thursdayRate = "seasonDetailDtoWrapper.thursday";
        fridayRate = "seasonDetailDtoWrapper.friday";
        saturdayRate = "seasonDetailDtoWrapper.saturday";

        table.addGeneratedColumn(acceleratorId, new Table.ColumnGenerator() {
            @Override
            public Object generateCell(Table source, Object itemId, Object columnId) {
                RDSeasonDetailsUIWrapper seasonDetail = (RDSeasonDetailsUIWrapper) itemId;
                HorizontalLayout acceleratorLayout = seasonDetail.getAcceleratorLayout();
                if (acceleratorLayout == null) {
                    acceleratorLayout = new HorizontalLayout();
                    acceleratorLayout.setWidth(100, Unit.PERCENTAGE);
                    acceleratorLayout.setSpacing(true);
                    Label roomTypeLabel = new Label(seasonDetail.getSeasonDetailDtoWrapper().getSeasonDetail().getAccomTypeName()
                            + (seasonDetail.getSeasonDetailDtoWrapper().getSeasonDetail().isAccomTypeDisplayActive() ? "" : "(Ø)"));
                    roomTypeLabel.setDescription(seasonDetail.getSeasonDetailDtoWrapper().getSeasonDetail().getAccomTypeName());
                    acceleratorLayout.addComponent(roomTypeLabel);

                    TetrisBigDecimalField acceleratorInput = new TetrisBigDecimalField();
                    addValidatorForAccelerator(acceleratorInput);
                    acceleratorInput.addStyleName(TetrisTheme.TEXT_RIGHT_ALIGNED);
                    acceleratorInput.setMaxLength(15);
                    acceleratorInput.setFormatString(StringToBigDecimalConverter.DEFAULT_FORMAT_TWO_DECIMALS);
                    acceleratorInput.setRequired(false);
                    acceleratorInput.setWidth(60, Unit.PIXELS);
                    acceleratorInput.setReadOnly(isSeasonInPast || !seasonDetail.getSeasonDetailDtoWrapper().getSeasonDetail().isAccomTypeActive());
                    seasonDetail.setAcceleratorInput(acceleratorInput);
                    acceleratorLayout.addComponent(acceleratorInput);

                    acceleratorLayout.setComponentAlignment(roomTypeLabel, Alignment.MIDDLE_LEFT);
                    acceleratorLayout.setComponentAlignment(acceleratorInput, Alignment.MIDDLE_RIGHT);
                    seasonDetail.setAcceleratorLayout(acceleratorLayout);
                }

                return acceleratorLayout;
            }
        });
        visibleColumns.add(acceleratorId);
        columnHeaders[0] = "";

        table.setColumnWidth(acceleratorId, 150);

        createGeneratedColumn(sundayRate, table, roomClassOrder, DayOfWeek.SUNDAY);
        visibleColumns.add(sundayRate);
        columnHeaders[1] = getText("sunday");

        createGeneratedColumn(mondayRate, table, roomClassOrder, DayOfWeek.MONDAY);
        visibleColumns.add(mondayRate);
        columnHeaders[2] = getText("monday");

        createGeneratedColumn(tuesdayRate, table, roomClassOrder, DayOfWeek.TUESDAY);
        visibleColumns.add(tuesdayRate);
        columnHeaders[3] = getText("tuesday");

        createGeneratedColumn(wednesdayRate, table, roomClassOrder, DayOfWeek.WEDNESDAY);
        visibleColumns.add(wednesdayRate);
        columnHeaders[4] = getText("wednesday");

        createGeneratedColumn(thursdayRate, table, roomClassOrder, DayOfWeek.THURSDAY);
        visibleColumns.add(thursdayRate);
        columnHeaders[5] = getText("thursday");

        createGeneratedColumn(fridayRate, table, roomClassOrder, DayOfWeek.FRIDAY);
        visibleColumns.add(fridayRate);
        columnHeaders[6] = getText("friday");

        createGeneratedColumn(saturdayRate, table, roomClassOrder, DayOfWeek.SATURDAY);
        visibleColumns.add(saturdayRate);
        columnHeaders[7] = getText("saturday");

        table.addListener(new TetrisChangeAwareTable.TetrisRowGenerationCompleteListener() {
            @Override
            public void onRowGenerationComplete(TetrisChangeAwareTable.TetrisRowGenerationCompleteEvent event) {
                LinkedHashMap<DayOfWeek, TetrisBigDecimalField> rateFieldByDayOfWeekMap = new LinkedHashMap<DayOfWeek, TetrisBigDecimalField>();
                RDSeasonDetailsUIWrapper bean = (RDSeasonDetailsUIWrapper) event.getBean();
                TetrisBigDecimalField sunday = (TetrisBigDecimalField) bean.getComponent(sundayRate);
                rateFieldByDayOfWeekMap.put(DayOfWeek.SUNDAY, sunday);
                addDayFieldChangeListener(DayOfWeek.SUNDAY, rateFieldByDayOfWeekMap.get(DayOfWeek.SUNDAY), rateFieldByDayOfWeekMap);
                TetrisBigDecimalField monday = (TetrisBigDecimalField) bean.getComponent(mondayRate);
                rateFieldByDayOfWeekMap.put(DayOfWeek.MONDAY, monday);
                addDayFieldChangeListener(DayOfWeek.MONDAY, rateFieldByDayOfWeekMap.get(DayOfWeek.MONDAY), rateFieldByDayOfWeekMap);
                TetrisBigDecimalField tuesday = (TetrisBigDecimalField) bean.getComponent(tuesdayRate);
                rateFieldByDayOfWeekMap.put(DayOfWeek.TUESDAY, tuesday);
                addDayFieldChangeListener(DayOfWeek.TUESDAY, rateFieldByDayOfWeekMap.get(DayOfWeek.TUESDAY), rateFieldByDayOfWeekMap);
                TetrisBigDecimalField wednesday = (TetrisBigDecimalField) bean.getComponent(wednesdayRate);
                rateFieldByDayOfWeekMap.put(DayOfWeek.WEDNESDAY, wednesday);
                addDayFieldChangeListener(DayOfWeek.WEDNESDAY, rateFieldByDayOfWeekMap.get(DayOfWeek.WEDNESDAY), rateFieldByDayOfWeekMap);
                TetrisBigDecimalField thursday = (TetrisBigDecimalField) bean.getComponent(thursdayRate);
                rateFieldByDayOfWeekMap.put(DayOfWeek.THURSDAY, thursday);
                addDayFieldChangeListener(DayOfWeek.THURSDAY, rateFieldByDayOfWeekMap.get(DayOfWeek.THURSDAY), rateFieldByDayOfWeekMap);
                TetrisBigDecimalField friday = (TetrisBigDecimalField) bean.getComponent(fridayRate);
                rateFieldByDayOfWeekMap.put(DayOfWeek.FRIDAY, friday);
                addDayFieldChangeListener(DayOfWeek.FRIDAY, rateFieldByDayOfWeekMap.get(DayOfWeek.FRIDAY), rateFieldByDayOfWeekMap);
                TetrisBigDecimalField saturday = (TetrisBigDecimalField) bean.getComponent(saturdayRate);
                rateFieldByDayOfWeekMap.put(DayOfWeek.SATURDAY, saturday);
                addDayFieldChangeListener(DayOfWeek.SATURDAY, rateFieldByDayOfWeekMap.get(DayOfWeek.SATURDAY), rateFieldByDayOfWeekMap);

                AcceleratorTextFieldListener acceleratorTextFieldListener = bean.getAcceleratorTextFieldListener();
                acceleratorTextFieldListener.addTextField(sunday);
                acceleratorTextFieldListener.addTextField(monday);
                acceleratorTextFieldListener.addTextField(tuesday);
                acceleratorTextFieldListener.addTextField(wednesday);
                acceleratorTextFieldListener.addTextField(thursday);
                acceleratorTextFieldListener.addTextField(friday);
                acceleratorTextFieldListener.addTextField(saturday);
                if (null != season.getSeason().getStartDate() && null != season.getSeason().getEndDate()) {
                    List<DayOfWeek> outOfRangeDayOfWeeks = getOutOfRangeDayOfWeeks(season.getSeason().getStartDate(), season.getSeason().getEndDate());
                    disableFieldsForSeasonDetail(outOfRangeDayOfWeeks, bean);
                }
            }
        });
        table.setVisibleColumns(new Object[]{acceleratorId, sundayRate, mondayRate, tuesdayRate, wednesdayRate, thursdayRate, fridayRate, saturdayRate});
        table.setColumnHeaders(columnHeaders);
        table.setNumericAlignmentColumns(sundayRate, mondayRate, tuesdayRate, wednesdayRate, thursdayRate, fridayRate, saturdayRate);
        table.setColumnExpandRatio(sundayRate, 12);
        table.setColumnExpandRatio(mondayRate, 12);
        table.setColumnExpandRatio(tuesdayRate, 12);
        table.setColumnExpandRatio(wednesdayRate, 12);
        table.setColumnExpandRatio(thursdayRate, 12);
        table.setColumnExpandRatio(fridayRate, 12);
        table.setColumnExpandRatio(saturdayRate, 12);
        table.setColumnExpandRatio(sundayRate, 12);
    }

    private void createGeneratedColumn(final String rateColumn, Table table, final Integer roomClassOrder, final DayOfWeek dow) {
        table.addGeneratedColumn(rateColumn, new TetrisChangeAwareComponentColumnGenerator() {
            @Override
            public ChangeAware generateComponent(Table source, Object itemId, Object columnId) {
                RDSeasonDetailsUIWrapper detailUiWrapper = (RDSeasonDetailsUIWrapper) itemId;
                TetrisBigDecimalField rateField = createRateField(getRateValueForTheDay(detailUiWrapper.getSeasonDetailDtoWrapper(), rateColumn), dow);
                roomClassWiseFieldMap.put(rateField, roomClassOrder);
                return rateField;
            }
        });
    }

    private TetrisBigDecimalField createRateField(BigDecimal rateValue, DayOfWeek dow) {
        TetrisBigDecimalField field = new TetrisBigDecimalField();
        field.setFormatString(StringToBigDecimalConverter.DEFAULT_FORMAT_TWO_DECIMALS);
        field.setDisableReadOnlyComponentSwapping(true);
        field.setWidth(100, Unit.PERCENTAGE);
        field.setRequired(false);
        addValidator(field, dow);
        field.addStyleName(TetrisTheme.TEXT_RIGHT_ALIGNED);
        field.setMaxLength(15);
        field.setVisibleValidationOnLoad(isInValidRateFor(rateValue));

        return field;
    }

    private boolean isInValidRateFor(BigDecimal rateValue) {
        return (rateValue != null) && (rateValue.compareTo(BigDecimal.ZERO) == -1 || rateValue.compareTo(BigDecimal.ZERO) == 0);
    }

    private void addDayFieldChangeListener(final DayOfWeek dow, TetrisBigDecimalField dayField, final LinkedHashMap<DayOfWeek, TetrisBigDecimalField> rateFieldByDayOfWeekMap) {
        dayField.addValueChangeListener(new Property.ValueChangeListener() {
            @Override
            public void valueChange(Property.ValueChangeEvent event) {
                triggerValidationAndErrorRepaintOfRateFields(dow, (TetrisBigDecimalField) event.getProperty(), rateFieldByDayOfWeekMap);
                calculateMinRatePerRoomClass();
            }
        });
    }

    public void triggerValidationAndErrorRepaintOfRateFields(DayOfWeek dow, TetrisBigDecimalField valueChangedField, LinkedHashMap<DayOfWeek, TetrisBigDecimalField> rateFieldByDayOfWeekMap) {
        rateFieldByDayOfWeekMap.put(dow, valueChangedField);
        //check for all empty
        if (Strings.isNullOrEmpty(valueChangedField.getValue())) {
            boolean atleastOneFieldHasValue = false;
            for (TetrisBigDecimalField field : rateFieldByDayOfWeekMap.values()) {
                if (field.isEnabled() && !Strings.isNullOrEmpty(field.getValue())) {
                    atleastOneFieldHasValue = true;
                    break;
                }
            }
            if (!atleastOneFieldHasValue) {
                for (TetrisBigDecimalField field : rateFieldByDayOfWeekMap.values()) {
                    field.setRequired(false);
                }
            } else {
                for (TetrisBigDecimalField field : rateFieldByDayOfWeekMap.values()) {
                    field.setRequired(field.isEnabled());
                }
            }
        } else {
            for (TetrisBigDecimalField field : rateFieldByDayOfWeekMap.values()) {
                field.setRequired(field.isEnabled());
            }
        }
    }

    public void disableOutOfRangeValues() {
        //re-enable all then disable out of range values
        if (null != season.getSeason().getStartDate() && null != season.getSeason().getEndDate()) {
            List<DayOfWeek> outOfRangeDayOfWeeks = getOutOfRangeDayOfWeeks(season.getSeason().getStartDate(), season.getSeason().getEndDate());
            List<RDRoomClassUiWrapper> roomClassWrappers = season.getRoomClassUiWrapperList();
            for (RDRoomClassUiWrapper roomClassWrapper : roomClassWrappers) {
                List<RDSeasonDetailsUIWrapper> filteredSeasonDetails = getFilteredSeasonDetails(roomClassWrapper);
                for (RDSeasonDetailsUIWrapper seasonDetail : filteredSeasonDetails) {
                    disableFieldsForSeasonDetail(outOfRangeDayOfWeeks, seasonDetail);
                }
            }
        }
    }

    private List<DayOfWeek> getOutOfRangeDayOfWeeks(LocalDate seasonStartDate, LocalDate seasonEndDate) {
        List<DayOfWeek> outOfRangeDayOfWeeks;
        if (seasonEndDate.isBefore(systemDate)) {
            outOfRangeDayOfWeeks = LocalDateUtils.getOutOfRangeDayOfWeeks(seasonStartDate, seasonEndDate);
        } else if (getDifferenceBetweenDays(systemDate, seasonStartDate) < 0) {
            outOfRangeDayOfWeeks = LocalDateUtils.getOutOfRangeDayOfWeeks(systemDate, seasonEndDate);
        } else {
            outOfRangeDayOfWeeks = LocalDateUtils.getOutOfRangeDayOfWeeks(seasonStartDate, seasonEndDate);
        }
        return outOfRangeDayOfWeeks;
    }

    private void disableFieldsForSeasonDetail(List<DayOfWeek> outOfRangeDayOfWeeks, RDSeasonDetailsUIWrapper seasonDetail) {
        boolean isRoomTypeActive = seasonDetail.getSeasonDetailDtoWrapper().getSeasonDetail().isAccomTypeActive();

        TetrisBigDecimalField sunday = (TetrisBigDecimalField) seasonDetail.getComponent(sundayRate);
        setValueAndEnablementOfDOW(sunday, outOfRangeDayOfWeeks, DayOfWeek.SUNDAY, isRoomTypeActive);

        TetrisBigDecimalField monday = (TetrisBigDecimalField) seasonDetail.getComponent(mondayRate);
        setValueAndEnablementOfDOW(monday, outOfRangeDayOfWeeks, DayOfWeek.MONDAY, isRoomTypeActive);

        TetrisBigDecimalField tuesday = (TetrisBigDecimalField) seasonDetail.getComponent(tuesdayRate);
        setValueAndEnablementOfDOW(tuesday, outOfRangeDayOfWeeks, DayOfWeek.TUESDAY, isRoomTypeActive);

        TetrisBigDecimalField wednesday = (TetrisBigDecimalField) seasonDetail.getComponent(wednesdayRate);
        setValueAndEnablementOfDOW(wednesday, outOfRangeDayOfWeeks, DayOfWeek.WEDNESDAY, isRoomTypeActive);

        TetrisBigDecimalField thursday = (TetrisBigDecimalField) seasonDetail.getComponent(thursdayRate);
        setValueAndEnablementOfDOW(thursday, outOfRangeDayOfWeeks, DayOfWeek.THURSDAY, isRoomTypeActive);

        TetrisBigDecimalField friday = (TetrisBigDecimalField) seasonDetail.getComponent(fridayRate);
        setValueAndEnablementOfDOW(friday, outOfRangeDayOfWeeks, DayOfWeek.FRIDAY, isRoomTypeActive);

        TetrisBigDecimalField saturday = (TetrisBigDecimalField) seasonDetail.getComponent(saturdayRate);
        setValueAndEnablementOfDOW(saturday, outOfRangeDayOfWeeks, DayOfWeek.SATURDAY, isRoomTypeActive);

    }

    private void setValueAndEnablementOfDOW(TetrisBigDecimalField field, List<DayOfWeek> outOfRangeDayOfWeeks, DayOfWeek dow, boolean isAccomTypeActive) {
        enableDisableDOWCell(field, outOfRangeDayOfWeeks, dow, isAccomTypeActive);
        setValuesForDOWCell(field);
    }

    private void enableDisableDOWCell(TetrisBigDecimalField field, List<DayOfWeek> outOfRangeDayOfWeeks, DayOfWeek dow, boolean isAccomTypeActive) {
        if (outOfRangeDayOfWeeks.contains(dow) || isSeasonInPast || !isAccomTypeActive || season.getSeason().isDeleted()) {
            field.setEnabled(false);
            field.setRequired(false);
        } else {
            field.setEnabled(true);
        }
    }

    private void setValuesForDOWCell(TetrisBigDecimalField field) {
        if ((season.getRateHeaderUiWrapper().getRateHeader() != null || !field.isEnabled()) && ("-1.00".equals(field.getValue()) || "-1,00".equals(field.getValue()))) {
            field.setValue("");
        }
    }

    @VisibleForTesting
    void setHideDiscontinuedAccomTypes(boolean hideDiscontinuedAccomTypes) {
        this.hideDiscontinuedAccomTypes = hideDiscontinuedAccomTypes;
    }

    public void populateRatesTableData(List<RDSeasonDetailsUIWrapper> seasonDetailUiWrapperList) {
        beanItemContainer.removeAllItems();
        beanItemContainer.addAll(seasonDetailUiWrapperList);
    }

    private BigDecimal getRateValueForTheDay(SeasonDetailDtoWrapper seasonDetail, String rateColumn) {
        if ("seasonDetailDtoWrapper.sunday".equals(rateColumn)) {
            return seasonDetail.getSunday();
        } else if ("seasonDetailDtoWrapper.monday".equals(rateColumn)) {
            return seasonDetail.getMonday();
        } else if ("seasonDetailDtoWrapper.tuesday".equals(rateColumn)) {
            return seasonDetail.getTuesday();
        } else if ("seasonDetailDtoWrapper.wednesday".equals(rateColumn)) {
            return seasonDetail.getWednesday();
        } else if ("seasonDetailDtoWrapper.thursday".equals(rateColumn)) {
            return seasonDetail.getThursday();
        } else if ("seasonDetailDtoWrapper.friday".equals(rateColumn)) {
            return seasonDetail.getFriday();
        } else if ("seasonDetailDtoWrapper.saturday".equals(rateColumn)) {
            return seasonDetail.getSaturday();
        }
        return null;
    }

    private int getDifferenceBetweenDays(LocalDate seasonStartDate, LocalDate seasonEndDate) {
        return Days.daysBetween(seasonStartDate, seasonEndDate).getDays();
    }

    @Override
    public boolean hasChanges() {
        for (Map.Entry<Object, TetrisChangeAwareTable> entry : tables.entrySet()) {
            TetrisChangeAwareTable table = entry.getValue();
            if (table.hasChanges()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isValid() {
        return false;
    }

    protected String getText(String propertyKey, Object... params) {
        return UiUtils.getText(propertyKey, params);
    }


    @Override
    public TetrisSaveCancelEnum onSaveButtonClick() {
        for (Map.Entry<Object, TetrisChangeAwareTable> entry : tables.entrySet()) {
            TetrisChangeAwareTable table = entry.getValue();
            if (!table.isValid()) {
                return TetrisSaveCancelEnum.INVALID_CHANGES;
            }
        }
        if (!hasChanges()) {
            return TetrisSaveCancelEnum.NO_CHANGES;
        }
        if (validateAllFieldsForRatesAsPerRoomClassOrder()) {
            return TetrisSaveCancelEnum.INVALID_CHANGES;
        }
        return TetrisSaveCancelEnum.HAS_VALID_CHANGES;
    }

    private boolean validateAllFieldsForRatesAsPerRoomClassOrder() {
        boolean invalidValuesFlag = false;
        for (TetrisBigDecimalField tetrisBigDecimalField : roomClassWiseFieldMap.keySet()) {
            try {
                tetrisBigDecimalField.validate();
            } catch (Exception e) {
                LOGGER.info(e);
                tetrisBigDecimalField.setRequired(true);
                invalidValuesFlag = true;
            }
        }
        return invalidValuesFlag;
    }

    @Override
    public void onCancelButtonClick() {
        //This behaviour is not required
    }

    @Override
    public void onInvalidButtonClick() {
        //This behaviour is not required
    }

    private void addValidator(Field field, DayOfWeek dow) {
        addValidatorForAccelerator(field);
        field.addValidator(new PositiveRateValueValidator());
        field.addValidator(new RoomClassValuesByOrderValidator(field, dow));
    }

    private void addValidatorForAccelerator(Field field) {
        RangeValidator<BigDecimal> bigDecimalRangeValidator = new RangeValidator<BigDecimal>(getText("groupPricing.confAndBanquet.range.validator"),
                BigDecimal.class,
                BigDecimal.ZERO, MAX_INPUT);

        bigDecimalRangeValidator.setMinValueIncluded(false);
        bigDecimalRangeValidator.setMaxValueIncluded(false);
        field.addValidator(bigDecimalRangeValidator);
    }

    private class PositiveRateValueValidator implements Validator {

        @Override
        public void validate(Object value) throws InvalidValueException {
            if (null == value) {
                return;
            }
            BigDecimal rateValue = new BigDecimal(value.toString());
            if (rateValue.doubleValue() <= 0) {
                throw new InvalidValueException(getText("rateDetails.message.validation.negativeRateValues"));
            }

        }
    }

    protected void calculateMinRatePerRoomClass() {
        Map<Integer, RoomClassLevelRatesStat> allRoomClassRatesStats = new HashMap<>();
        List<DayOfWeek> outOfRangeDOWs = new ArrayList<>();
        if (season.getSeason().getStartDate() != null && season.getSeason().getEndDate() != null) {
            outOfRangeDOWs = getOutOfRangeDayOfWeeks(season.getSeason().getStartDate(), season.getSeason().getEndDate());
        }
        for (RDRoomClassUiWrapper roomClassUiWrapper : season.getRoomClassUiWrapperList()) {
            RoomClassLevelRatesStat roomClassRatesStats = new RoomClassLevelRatesStat();
            roomClassRatesStats.setRoomClassUiWrapper(roomClassUiWrapper);
            roomClassRatesStats.findReferenceRatesInTheClass(outOfRangeDOWs);
            allRoomClassRatesStats.put(roomClassRatesStats.getRoomClassUiWrapper().getRoomClass().getOrder(), roomClassRatesStats);
        }
        this.roomClassRatesStatsMap = allRoomClassRatesStats;
    }

    private class RoomClassValuesByOrderValidator implements Validator {
        private Field field;
        private DayOfWeek dow;

        RoomClassValuesByOrderValidator(Field field, DayOfWeek dow) {
            this.field = field;
            this.dow = dow;
        }

        @Override
        public void validate(Object value) throws InvalidValueException {
            if (null == value || !field.isEnabled()) {
                return;
            }
            BigDecimal rateValue = new BigDecimal(value.toString());
            if (!isAdvancedPriceRankingEnabled && !isRatesEnteredAreInOrder(rateValue)) {
                throw new InvalidValueException(getText("validation.Message.InvalidRatesAsPerRoomOrder"));
            }
        }

        public boolean isRatesEnteredAreInOrder(BigDecimal value) {
            List<RoomClassLevelRatesStat> higherOrderRoomClasses = findHigherOrderRoomClasses();
            for (int orderIndex = 0; orderIndex < higherOrderRoomClasses.size(); orderIndex++) {
                if (value.compareTo(new BigDecimal(0)) == 1 && higherOrderRoomClasses.get(orderIndex).getMaxRate(dow).compareTo(value) == 1) {
                    return false;
                }
            }
            return true;
        }

        private List<RoomClassLevelRatesStat> findHigherOrderRoomClasses() {
            List<RoomClassLevelRatesStat> higherOrderRoomClassStats = new ArrayList<>();
            Integer fieldRoomClassOrder = roomClassWiseFieldMap.get(field);
            for (Integer classOrder : roomClassRatesStatsMap.keySet()) {
                if (classOrder < fieldRoomClassOrder) {
                    higherOrderRoomClassStats.add(roomClassRatesStatsMap.get(classOrder));
                }
            }
            return higherOrderRoomClassStats;
        }


    }

}
