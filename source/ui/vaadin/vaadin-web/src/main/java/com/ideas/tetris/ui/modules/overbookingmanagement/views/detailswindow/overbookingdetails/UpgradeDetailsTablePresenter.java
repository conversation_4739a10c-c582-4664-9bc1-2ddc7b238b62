package com.ideas.tetris.ui.modules.overbookingmanagement.views.detailswindow.overbookingdetails;

import com.ideas.tetris.ui.common.cdi.TetrisPresenter;
import com.ideas.tetris.ui.modules.overbookingmanagement.views.detailswindow.overbookingdetails.dto.BookedVersusStayedRoomClassMappingDTO;
import com.ideas.tetris.ui.modules.overbookingmanagement.views.detailswindow.overbookingdetails.dto.OverBookingDetailsDTO;
import com.ideas.tetris.ui.modules.overbookingmanagement.views.detailswindow.overbookingdetails.dto.RoomClassUpgradePercentagesDetailsDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class UpgradeDetailsTablePresenter extends TetrisPresenter<UpgradeDetailsTableView, OverBookingDetailsDTO> {

    @Autowired
    OverbookingReasonsDetailsService overbookingReasonsDetailsService;

    public void filterData(LocalDate startDate, LocalDate endDate) {
        List<BookedVersusStayedRoomClassMappingDTO> bookedVersusStayedRoomClassMappingDTOS = getBookedVsStayedData(startDate, endDate);
        List<RoomClassUpgradePercentagesDetailsDTO> roomClassUpgradePercentagesDetailsDTOS = getRoomClassUpgradePercentageData(startDate, endDate);

        view.updateTable(bookedVersusStayedRoomClassMappingDTOS, roomClassUpgradePercentagesDetailsDTOS);
    }

    public List<BookedVersusStayedRoomClassMappingDTO> getBookedVsStayedData(LocalDate startDate, LocalDate endDate) {
        return overbookingReasonsDetailsService.getBookedVersusStayedRoomClassMappingDetails(startDate, endDate);
    }

    public List<RoomClassUpgradePercentagesDetailsDTO> getRoomClassUpgradePercentageData(LocalDate startDate, LocalDate endDate) {
        return overbookingReasonsDetailsService.getRoomClassUpgradePercentagesDetails(startDate, endDate);
    }
}
